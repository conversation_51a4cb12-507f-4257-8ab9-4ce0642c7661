{"version": 3, "file": "Updates.js", "names": ["_BuildProperties", "data", "require", "_Manifest", "_Resources", "_Strings", "_androidPlugins", "_withPlugins", "_Updates", "_warnings", "Config", "exports", "withUpdates", "config", "with<PERSON><PERSON><PERSON>", "withUpdatesManifest", "withRuntimeVersionResource", "withUpdatesNativeDebugGradleProps", "createBuildGradlePropsConfigPlugin", "propName", "propValueGetter", "updates", "useNativeDebug", "undefined", "withAndroidManifest", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfigAsync", "createStringsXmlPlugin", "applyRuntimeVersionFromConfigAsync", "stringsJSON", "applyRuntimeVersionFromConfigForProjectRootAsync", "runtimeVersion", "getRuntimeVersionNullableAsync", "setStringItem", "buildResourceItem", "name", "value", "removeStringItem", "androidManifest", "mainApplication", "getMainApplicationOrThrow", "addMetaDataItemToMainApplication", "ENABLED", "String", "getUpdatesEnabled", "checkOnLaunch", "getUpdatesCheckOnLaunch", "CHECK_ON_LAUNCH", "timeout", "getUpdatesTimeout", "LAUNCH_WAIT_MS", "useEmbeddedUpdate", "getUpdatesUseEmbeddedUpdate", "removeMetaDataItemFromMainApplication", "UPDATES_HAS_EMBEDDED_UPDATE", "addWarningAndroid", "updateUrl", "getUpdateUrl", "UPDATE_URL", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadataStringified", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeadersStringified", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "disableAntiBrickingMeasures", "getDisableAntiBrickingMeasures", "DISABLE_ANTI_BRICKING_MEASURES", "setVersionsConfigAsync", "findMetaDataItem", "RUNTIME_VERSION", "Error"], "sources": ["../../src/android/Updates.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createBuildGradlePropsConfigPlugin } from './BuildProperties';\nimport {\n  addMetaDataItemToMainApplication,\n  AndroidManifest,\n  findMetaDataItem,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} from './Manifest';\nimport { buildResourceItem, ResourceXML } from './Resources';\nimport * as Resources from './Resources';\nimport { removeStringItem, setStringItem } from './Strings';\nimport { ConfigPlugin, ExportedConfigWithProps } from '../Plugin.types';\nimport { createStringsXmlPlugin, withAndroidManifest } from '../plugins/android-plugins';\nimport { withPlugins } from '../plugins/withPlugins';\nimport {\n  ExpoConfigUpdates,\n  getDisableAntiBrickingMeasures,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullableAsync,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadataStringified,\n  getUpdatesRequestHeadersStringified,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdateUrl,\n  getUpdatesUseEmbeddedUpdate,\n} from '../utils/Updates';\nimport { addWarningAndroid } from '../utils/warnings';\n\nexport enum Config {\n  ENABLED = 'expo.modules.updates.ENABLED',\n  CHECK_ON_LAUNCH = 'expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH',\n  LAUNCH_WAIT_MS = 'expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS',\n  RUNTIME_VERSION = 'expo.modules.updates.EXPO_RUNTIME_VERSION',\n  UPDATE_URL = 'expo.modules.updates.EXPO_UPDATE_URL',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'expo.modules.updates.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY',\n  UPDATES_HAS_EMBEDDED_UPDATE = 'expo.modules.updates.HAS_EMBEDDED_UPDATE',\n  CODE_SIGNING_CERTIFICATE = 'expo.modules.updates.CODE_SIGNING_CERTIFICATE',\n  CODE_SIGNING_METADATA = 'expo.modules.updates.CODE_SIGNING_METADATA',\n  DISABLE_ANTI_BRICKING_MEASURES = 'expo.modules.updates.DISABLE_ANTI_BRICKING_MEASURES',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin = (config) => {\n  return withPlugins(config, [\n    withUpdatesManifest,\n    withRuntimeVersionResource,\n    withUpdatesNativeDebugGradleProps,\n  ]);\n};\n\n/**\n * A config-plugin to update `android/gradle.properties` from the `updates.useNativeDebug` in expo config\n */\nconst withUpdatesNativeDebugGradleProps = createBuildGradlePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'EX_UPDATES_NATIVE_DEBUG',\n      propValueGetter: (config) => (config?.updates?.useNativeDebug === true ? 'true' : undefined),\n    },\n  ],\n  'withUpdatesNativeDebugGradleProps'\n);\n\nconst withUpdatesManifest: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, async (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = await setUpdatesConfigAsync(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nconst withRuntimeVersionResource = createStringsXmlPlugin(\n  applyRuntimeVersionFromConfigAsync,\n  'withRuntimeVersionResource'\n);\n\nexport async function applyRuntimeVersionFromConfigAsync(\n  config: ExportedConfigWithProps<Resources.ResourceXML>,\n  stringsJSON: ResourceXML\n): Promise<ResourceXML> {\n  const projectRoot = config.modRequest.projectRoot;\n  return await applyRuntimeVersionFromConfigForProjectRootAsync(projectRoot, config, stringsJSON);\n}\n\nexport async function applyRuntimeVersionFromConfigForProjectRootAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  stringsJSON: ResourceXML\n): Promise<ResourceXML> {\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'android');\n  if (runtimeVersion) {\n    return setStringItem(\n      [buildResourceItem({ name: 'expo_runtime_version', value: runtimeVersion })],\n      stringsJSON\n    );\n  }\n  return removeStringItem('expo_runtime_version', stringsJSON);\n}\n\nexport async function setUpdatesConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  androidManifest: AndroidManifest,\n  expoUpdatesPackageVersion?: string | null\n): Promise<AndroidManifest> {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  addMetaDataItemToMainApplication(\n    mainApplication,\n    Config.ENABLED,\n    String(getUpdatesEnabled(config))\n  );\n  const checkOnLaunch = getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion);\n  addMetaDataItemToMainApplication(mainApplication, Config.CHECK_ON_LAUNCH, checkOnLaunch);\n\n  const timeout = getUpdatesTimeout(config);\n  addMetaDataItemToMainApplication(mainApplication, Config.LAUNCH_WAIT_MS, String(timeout));\n\n  const useEmbeddedUpdate = getUpdatesUseEmbeddedUpdate(config);\n  if (useEmbeddedUpdate) {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.UPDATES_HAS_EMBEDDED_UPDATE);\n  } else {\n    // TODO: is there a better place for this validation?\n    if (timeout === 0 && checkOnLaunch !== 'ALWAYS') {\n      addWarningAndroid(\n        'updates.useEmbeddedUpdate',\n        `updates.checkOnLaunch should be set to \"ON_LOAD\" and updates.fallbackToCacheTimeout should be set to a non-zero value when updates.useEmbeddedUpdate is set to false. This is because an update must be fetched on the initial launch, when no embedded update is available.`\n      );\n    }\n    addMetaDataItemToMainApplication(mainApplication, Config.UPDATES_HAS_EMBEDDED_UPDATE, 'false');\n  }\n\n  const updateUrl = getUpdateUrl(config);\n  if (updateUrl) {\n    addMetaDataItemToMainApplication(mainApplication, Config.UPDATE_URL, updateUrl);\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.UPDATE_URL);\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_CERTIFICATE,\n      codeSigningCertificate\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_CERTIFICATE);\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadataStringified(config);\n  if (codeSigningMetadata) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_METADATA,\n      codeSigningMetadata\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_METADATA);\n  }\n\n  const requestHeaders = getUpdatesRequestHeadersStringified(config);\n  if (requestHeaders) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY,\n      requestHeaders\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY\n    );\n  }\n\n  const disableAntiBrickingMeasures = getDisableAntiBrickingMeasures(config);\n  if (disableAntiBrickingMeasures) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.DISABLE_ANTI_BRICKING_MEASURES,\n      'true'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.DISABLE_ANTI_BRICKING_MEASURES);\n  }\n\n  return await setVersionsConfigAsync(projectRoot, config, androidManifest);\n}\n\nexport async function setVersionsConfigAsync(\n  projectRoot: string,\n  config: Pick<ExpoConfigUpdates, 'sdkVersion' | 'runtimeVersion'>,\n  androidManifest: AndroidManifest\n): Promise<AndroidManifest> {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'android');\n  if (!runtimeVersion && findMetaDataItem(mainApplication, Config.RUNTIME_VERSION) > -1) {\n    throw new Error(\n      'A runtime version is set in your AndroidManifest.xml, but is missing from your Expo app config (app.json/app.config.js). Either set runtimeVersion in your Expo app config or remove expo.modules.updates.EXPO_RUNTIME_VERSION from your AndroidManifest.xml.'\n    );\n  }\n  if (runtimeVersion) {\n    removeMetaDataItemFromMainApplication(mainApplication, 'expo.modules.updates.EXPO_SDK_VERSION');\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.RUNTIME_VERSION,\n      '@string/expo_runtime_version'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.RUNTIME_VERSION);\n    removeMetaDataItemFromMainApplication(mainApplication, 'expo.modules.updates.EXPO_SDK_VERSION');\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;;;AAEA,SAAAA,iBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,gBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,gBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,eAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,SAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAcA,SAAAQ,UAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsD,IAE1CS,MAAM,GAAAC,OAAA,CAAAD,MAAA,0BAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAAA,OAANA,MAAM;AAAA,OAalB;AACA;AAEO,MAAME,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,0BAAW,EAACD,MAAM,EAAE,CACzBE,mBAAmB,EACnBC,0BAA0B,EAC1BC,iCAAiC,CAClC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAN,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAGA,MAAMK,iCAAiC,GAAG,IAAAC,qDAAkC,EAC1E,CACE;EACEC,QAAQ,EAAE,yBAAyB;EACnCC,eAAe,EAAGP,MAAM,IAAMA,MAAM,EAAEQ,OAAO,EAAEC,cAAc,KAAK,IAAI,GAAG,MAAM,GAAGC;AACpF,CAAC,CACF,EACD,mCACF,CAAC;AAED,MAAMR,mBAAiC,GAAIF,MAAM,IAAK;EACpD,OAAO,IAAAW,qCAAmB,EAACX,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnD,MAAMY,WAAW,GAAGZ,MAAM,CAACa,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EZ,MAAM,CAACgB,UAAU,GAAG,MAAMC,qBAAqB,CAC7CL,WAAW,EACXZ,MAAM,EACNA,MAAM,CAACgB,UAAU,EACjBF,yBACF,CAAC;IACD,OAAOd,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMG,0BAA0B,GAAG,IAAAe,wCAAsB,EACvDC,kCAAkC,EAClC,4BACF,CAAC;AAEM,eAAeA,kCAAkCA,CACtDnB,MAAsD,EACtDoB,WAAwB,EACF;EACtB,MAAMR,WAAW,GAAGZ,MAAM,CAACa,UAAU,CAACD,WAAW;EACjD,OAAO,MAAMS,gDAAgD,CAACT,WAAW,EAAEZ,MAAM,EAAEoB,WAAW,CAAC;AACjG;AAEO,eAAeC,gDAAgDA,CACpET,WAAmB,EACnBZ,MAAyB,EACzBoB,WAAwB,EACF;EACtB,MAAME,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAACX,WAAW,EAAEZ,MAAM,EAAE,SAAS,CAAC;EAC3F,IAAIsB,cAAc,EAAE;IAClB,OAAO,IAAAE,wBAAa,EAClB,CAAC,IAAAC,8BAAiB,EAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,KAAK,EAAEL;IAAe,CAAC,CAAC,CAAC,EAC5EF,WACF,CAAC;EACH;EACA,OAAO,IAAAQ,2BAAgB,EAAC,sBAAsB,EAAER,WAAW,CAAC;AAC9D;AAEO,eAAeH,qBAAqBA,CACzCL,WAAmB,EACnBZ,MAAyB,EACzB6B,eAAgC,EAChCf,yBAAyC,EACf;EAC1B,MAAMgB,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,IAAAG,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAACoC,OAAO,EACdC,MAAM,CAAC,IAAAC,4BAAiB,EAACnC,MAAM,CAAC,CAClC,CAAC;EACD,MAAMoC,aAAa,GAAG,IAAAC,kCAAuB,EAACrC,MAAM,EAAEc,yBAAyB,CAAC;EAChF,IAAAkB,4CAAgC,EAACF,eAAe,EAAEjC,MAAM,CAACyC,eAAe,EAAEF,aAAa,CAAC;EAExF,MAAMG,OAAO,GAAG,IAAAC,4BAAiB,EAACxC,MAAM,CAAC;EACzC,IAAAgC,4CAAgC,EAACF,eAAe,EAAEjC,MAAM,CAAC4C,cAAc,EAAEP,MAAM,CAACK,OAAO,CAAC,CAAC;EAEzF,MAAMG,iBAAiB,GAAG,IAAAC,sCAA2B,EAAC3C,MAAM,CAAC;EAC7D,IAAI0C,iBAAiB,EAAE;IACrB,IAAAE,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAACgD,2BAA2B,CAAC;EAC5F,CAAC,MAAM;IACL;IACA,IAAIN,OAAO,KAAK,CAAC,IAAIH,aAAa,KAAK,QAAQ,EAAE;MAC/C,IAAAU,6BAAiB,EACf,2BAA2B,EAC3B,8QACF,CAAC;IACH;IACA,IAAAd,4CAAgC,EAACF,eAAe,EAAEjC,MAAM,CAACgD,2BAA2B,EAAE,OAAO,CAAC;EAChG;EAEA,MAAME,SAAS,GAAG,IAAAC,uBAAY,EAAChD,MAAM,CAAC;EACtC,IAAI+C,SAAS,EAAE;IACb,IAAAf,4CAAgC,EAACF,eAAe,EAAEjC,MAAM,CAACoD,UAAU,EAAEF,SAAS,CAAC;EACjF,CAAC,MAAM;IACL,IAAAH,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAACoD,UAAU,CAAC;EAC3E;EAEA,MAAMC,sBAAsB,GAAG,IAAAC,2CAAgC,EAACvC,WAAW,EAAEZ,MAAM,CAAC;EACpF,IAAIkD,sBAAsB,EAAE;IAC1B,IAAAlB,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAACuD,wBAAwB,EAC/BF,sBACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAN,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAACuD,wBAAwB,CAAC;EACzF;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,mDAAwC,EAACtD,MAAM,CAAC;EAC5E,IAAIqD,mBAAmB,EAAE;IACvB,IAAArB,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAAC0D,qBAAqB,EAC5BF,mBACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAT,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAAC0D,qBAAqB,CAAC;EACtF;EAEA,MAAMC,cAAc,GAAG,IAAAC,8CAAmC,EAACzD,MAAM,CAAC;EAClE,IAAIwD,cAAc,EAAE;IAClB,IAAAxB,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAAC6D,yCAAyC,EAChDF,cACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAZ,iDAAqC,EACnCd,eAAe,EACfjC,MAAM,CAAC6D,yCACT,CAAC;EACH;EAEA,MAAMC,2BAA2B,GAAG,IAAAC,yCAA8B,EAAC5D,MAAM,CAAC;EAC1E,IAAI2D,2BAA2B,EAAE;IAC/B,IAAA3B,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAACgE,8BAA8B,EACrC,MACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAjB,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAACgE,8BAA8B,CAAC;EAC/F;EAEA,OAAO,MAAMC,sBAAsB,CAAClD,WAAW,EAAEZ,MAAM,EAAE6B,eAAe,CAAC;AAC3E;AAEO,eAAeiC,sBAAsBA,CAC1ClD,WAAmB,EACnBZ,MAAgE,EAChE6B,eAAgC,EACN;EAC1B,MAAMC,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,MAAMP,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAACX,WAAW,EAAEZ,MAAM,EAAE,SAAS,CAAC;EAC3F,IAAI,CAACsB,cAAc,IAAI,IAAAyC,4BAAgB,EAACjC,eAAe,EAAEjC,MAAM,CAACmE,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IACrF,MAAM,IAAIC,KAAK,CACb,+PACF,CAAC;EACH;EACA,IAAI3C,cAAc,EAAE;IAClB,IAAAsB,iDAAqC,EAACd,eAAe,EAAE,uCAAuC,CAAC;IAC/F,IAAAE,4CAAgC,EAC9BF,eAAe,EACfjC,MAAM,CAACmE,eAAe,EACtB,8BACF,CAAC;EACH,CAAC,MAAM;IACL,IAAApB,iDAAqC,EAACd,eAAe,EAAEjC,MAAM,CAACmE,eAAe,CAAC;IAC9E,IAAApB,iDAAqC,EAACd,eAAe,EAAE,uCAAuC,CAAC;EACjG;EAEA,OAAOD,eAAe;AACxB", "ignoreList": []}