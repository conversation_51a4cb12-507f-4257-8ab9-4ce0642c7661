{"version": 3, "names": ["checkCppVersion", "jsVersion", "getValueUnpackerCode", "isF<PERSON><PERSON>", "getShadowNodeWrapperFromRef", "ReanimatedModule", "ReanimatedError", "assertSingleReanimatedInstance", "global", "_REANIMATED_VERSION_JS", "undefined", "NativeReanimated", "constructor", "__DEV__", "__reanimatedModuleProxy", "valueUnpackerCode", "installTurboModule", "InnerNativeModule", "makeShareableClone", "value", "shouldPersistRemote", "nativeStateSource", "scheduleOnUI", "shareable", "executeOnUIRuntimeSync", "createWorkletRuntime", "name", "initializer", "scheduleOnRuntime", "workletRuntime", "shareableWorklet", "registerSensor", "sensorType", "interval", "iosReferenceFrame", "handler", "unregisterSensor", "sensorId", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "unregisterEventHandler", "id", "getViewProp", "viewTag", "propName", "component", "callback", "shadowNodeWrapper", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "enableLayoutAnimations", "flag", "configureProps", "uiProps", "nativeProps", "subscribeForKeyboardEvents", "isStatusBarTranslucent", "isNavigationBarTranslucent", "unsubscribeFromKeyboardEvents", "listenerId"], "sourceRoot": "../../../src", "sources": ["NativeReanimated/NativeReanimated.ts"], "mappings": "AAAA,YAAY;;AAOZ,SAASA,eAAe,QAAQ,yCAAsC;AACtE,SAASC,SAAS,QAAQ,mCAAgC;AAE1D,SAASC,oBAAoB,QAAQ,qBAAkB;AACvD,SAASC,QAAQ,QAAQ,uBAAoB;AAE7C,SAASC,2BAA2B,QAAQ,gBAAgB;AAE5D,OAAOC,gBAAgB,MAAM,oCAAiC;AAC9D,SAASC,eAAe,QAAQ,cAAW;;AAE3C;;AAiDA,SAASC,8BAA8BA,CAAA,EAAG;EACxC,IACEC,MAAM,CAACC,sBAAsB,KAAKC,SAAS,IAC3CF,MAAM,CAACC,sBAAsB,KAAKR,SAAS,EAC3C;IACA,MAAM,IAAIK,eAAe,CACvB;AACN,iKAAiKE,MAAM,CAACC,sBAAsB,cAAcR,SAAS,GACjN,CAAC;EACH;AACF;AAEA,OAAO,MAAMU,gBAAgB,CAAC;EAG5BC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAIC,OAAO,EAAE;MACXN,8BAA8B,CAAC,CAAC;IAClC;IACAC,MAAM,CAACC,sBAAsB,GAAGR,SAAS;IACzC,IAAIO,MAAM,CAACM,uBAAuB,KAAKJ,SAAS,EAAE;MAChD,MAAMK,iBAAiB,GAAGb,oBAAoB,CAAC,CAAC;MAChDG,gBAAgB,EAAEW,kBAAkB,CAACD,iBAAiB,CAAC;IACzD;IACA,IAAIP,MAAM,CAACM,uBAAuB,KAAKJ,SAAS,EAAE;MAChD,MAAM,IAAIJ,eAAe,CACvB;AACR,6JACM,CAAC;IACH;IACA,IAAIO,OAAO,EAAE;MACXb,eAAe,CAAC,CAAC;IACnB;IACA,IAAI,CAACiB,iBAAiB,GAAGT,MAAM,CAACM,uBAAuB;EACzD;EAEAI,kBAAkBA,CAChBC,KAAQ,EACRC,mBAA4B,EAC5BC,iBAA0B,EAC1B;IACA,OAAO,IAAI,CAACJ,iBAAiB,CAACC,kBAAkB,CAC9CC,KAAK,EACLC,mBAAmB,EACnBC,iBACF,CAAC;EACH;EAEAC,YAAYA,CAAIC,SAA0B,EAAE;IAC1C,OAAO,IAAI,CAACN,iBAAiB,CAACK,YAAY,CAACC,SAAS,CAAC;EACvD;EAEAC,sBAAsBA,CAAOD,SAA0B,EAAK;IAC1D,OAAO,IAAI,CAACN,iBAAiB,CAACO,sBAAsB,CAACD,SAAS,CAAC;EACjE;EAEAE,oBAAoBA,CAACC,IAAY,EAAEC,WAAqC,EAAE;IACxE,OAAO,IAAI,CAACV,iBAAiB,CAACQ,oBAAoB,CAACC,IAAI,EAAEC,WAAW,CAAC;EACvE;EAEAC,iBAAiBA,CACfC,cAA8B,EAC9BC,gBAAiC,EACjC;IACA,OAAO,IAAI,CAACb,iBAAiB,CAACW,iBAAiB,CAC7CC,cAAc,EACdC,gBACF,CAAC;EACH;EAEAC,cAAcA,CACZC,UAAkB,EAClBC,QAAgB,EAChBC,iBAAyB,EACzBC,OAA8D,EAC9D;IACA,OAAO,IAAI,CAAClB,iBAAiB,CAACc,cAAc,CAC1CC,UAAU,EACVC,QAAQ,EACRC,iBAAiB,EACjBC,OACF,CAAC;EACH;EAEAC,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,OAAO,IAAI,CAACpB,iBAAiB,CAACmB,gBAAgB,CAACC,QAAQ,CAAC;EAC1D;EAEAC,oBAAoBA,CAClBC,YAA6B,EAC7BC,SAAiB,EACjBC,eAAuB,EACvB;IACA,OAAO,IAAI,CAACxB,iBAAiB,CAACqB,oBAAoB,CAChDC,YAAY,EACZC,SAAS,EACTC,eACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,EAAU,EAAE;IACjC,OAAO,IAAI,CAAC1B,iBAAiB,CAACyB,sBAAsB,CAACC,EAAE,CAAC;EAC1D;EAEAC,WAAWA,CACTC,OAAe,EACfC,QAAgB,EAChBC,SAAsC;EAAE;EACxCC,QAA8B,EAC9B;IACA,IAAIC,iBAAiB;IACrB,IAAI9C,QAAQ,CAAC,CAAC,EAAE;MACd8C,iBAAiB,GAAG7C,2BAA2B,CAC7C2C,SACF,CAAC;MACD,OAAO,IAAI,CAAC9B,iBAAiB,CAAC2B,WAAW,CACvCK,iBAAiB,EACjBH,QAAQ,EACRE,QACF,CAAC;IACH;IAEA,OAAO,IAAI,CAAC/B,iBAAiB,CAAC2B,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEE,QAAQ,CAAC;EACxE;EAEAE,6BAA6BA,CAC3BC,qBAAiD,EACjD;IACA,IAAI,CAAClC,iBAAiB,CAACiC,6BAA6B,CAACC,qBAAqB,CAAC;EAC7E;EAEAC,6BAA6BA,CAACP,OAAe,EAAEQ,aAAsB,EAAE;IACrE,IAAI,CAACpC,iBAAiB,CAACmC,6BAA6B,CAClDP,OAAO,EACPQ,aACF,CAAC;EACH;EAEAC,sBAAsBA,CAACC,IAAa,EAAE;IACpC,IAAI,CAACtC,iBAAiB,CAACqC,sBAAsB,CAACC,IAAI,CAAC;EACrD;EAEAC,cAAcA,CAACC,OAAiB,EAAEC,WAAqB,EAAE;IACvD,IAAI,CAACzC,iBAAiB,CAACuC,cAAc,CAACC,OAAO,EAAEC,WAAW,CAAC;EAC7D;EAEAC,0BAA0BA,CACxBxB,OAA6B,EAC7ByB,sBAA+B,EAC/BC,0BAAmC,EACnC;IACA,OAAO,IAAI,CAAC5C,iBAAiB,CAAC0C,0BAA0B,CACtDxB,OAAO,EACPyB,sBAAsB,EACtBC,0BACF,CAAC;EACH;EAEAC,6BAA6BA,CAACC,UAAkB,EAAE;IAChD,IAAI,CAAC9C,iBAAiB,CAAC6C,6BAA6B,CAACC,UAAU,CAAC;EAClE;AACF", "ignoreList": []}