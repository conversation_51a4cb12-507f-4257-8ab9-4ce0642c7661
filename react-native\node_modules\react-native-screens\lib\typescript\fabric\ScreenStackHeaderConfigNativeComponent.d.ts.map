{"version": 3, "file": "ScreenStackHeaderConfigNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../src/fabric/ScreenStackHeaderConfigNativeComponent.ts"], "names": [], "mappings": ";AAGA,OAAO,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,KAAK,EACV,KAAK,EACL,WAAW,EACX,kBAAkB,EACnB,MAAM,2CAA2C,CAAC;AAEnD,KAAK,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC;AAGnC,KAAK,eAAe,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEpC,KAAK,eAAe,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAEpC,KAAK,qBAAqB,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AAE/D,KAAK,UAAU,GACX,MAAM,GACN,YAAY,GACZ,OAAO,GACP,MAAM,GACN,SAAS,GACT,WAAW,GACX,yBAAyB,GACzB,oBAAoB,GACpB,gBAAgB,GAChB,qBAAqB,GACrB,sBAAsB,GACtB,8BAA8B,GAC9B,yBAAyB,GACzB,qBAAqB,GACrB,0BAA0B,GAC1B,2BAA2B,GAC3B,6BAA6B,GAC7B,wBAAwB,GACxB,oBAAoB,GACpB,yBAAyB,GACzB,0BAA0B,CAAC;AAE/B,MAAM,WAAW,WAAY,SAAQ,SAAS;IAC5C,UAAU,CAAC,EAAE,kBAAkB,CAAC,eAAe,CAAC,CAAC;IACjD,UAAU,CAAC,EAAE,kBAAkB,CAAC,eAAe,CAAC,CAAC;IACjD,eAAe,CAAC,EAAE,UAAU,CAAC;IAC7B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,iBAAiB,CAAC,EAAE,KAAK,CAAC;IAC1B,gBAAgB,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChD,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,SAAS,CAAC,EAAE,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC9C,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,kBAAkB,CAAC,EAAE,KAAK,CAAC;IAC3B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,yBAAyB,CAAC,EAAE,UAAU,CAAC;IACvC,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,eAAe,CAAC,EAAE,UAAU,CAAC;IAC7B,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,aAAa,CAAC,EAAE,KAAK,CAAC;IACtB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,qBAAqB,CAAC,EAAE,WAAW,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;IACtE,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,UAAU,CAAC,EAAE,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAE7C,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;;AAED,wBAKE"}