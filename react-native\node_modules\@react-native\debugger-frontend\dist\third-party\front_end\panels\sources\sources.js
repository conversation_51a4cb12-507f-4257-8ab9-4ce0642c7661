import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as i from"../../core/common/common.js";import*as o from"../../core/sdk/sdk.js";import*as n from"../../third_party/codemirror.next/codemirror.next.js";import*as r from"../../ui/components/icon_button/icon_button.js";import*as s from"../../ui/components/text_editor/text_editor.js";import*as a from"../../ui/visual_logging/visual_logging.js";import*as c from"../../core/host/host.js";import*as l from"../../core/platform/platform.js";import{assertNotNullOrUndefined as d}from"../../core/platform/platform.js";import*as u from"../../models/bindings/bindings.js";import*as h from"../../models/persistence/persistence.js";import*as p from"../../models/source_map_scopes/source_map_scopes.js";import*as g from"../../models/text_utils/text_utils.js";import*as m from"../../ui/legacy/components/source_frame/source_frame.js";import*as b from"../coverage/coverage.js";import*as f from"../../models/workspace/workspace.js";import*as S from"../../ui/legacy/components/color_picker/color_picker.js";import*as v from"../../ui/legacy/components/inline_editor/inline_editor.js";import*as C from"../../models/breakpoints/breakpoints.js";import*as w from"../../models/formatter/formatter.js";import*as I from"../../ui/components/buttons/buttons.js";import*as y from"../../ui/legacy/components/object_ui/object_ui.js";import*as x from"./components/components.js";import*as k from"../../core/root/root.js";import*as T from"../../models/extensions/extensions.js";import*as E from"../snippets/snippets.js";import*as L from"../search/search.js";import*as M from"../../ui/legacy/components/quick_open/quick_open.js";import*as P from"../../models/issues_manager/issues_manager.js";import*as F from"../../ui/components/adorners/adorners.js";import*as D from"../../ui/components/issue_counter/issue_counter.js";import*as N from"../../ui/legacy/components/utils/utils.js";import{PanelUtils as A}from"../utils/utils.js";const U=new CSSStyleSheet;U.replaceSync(':host{padding:10px}.widget{align-items:center}label{white-space:nowrap}input[type="text"].add-source-map{box-shadow:0 0 0 1px var(--box-shadow-outline-color);font-size:inherit;margin:0 8px 0 5px}\n/*# sourceURL=dialog.css */\n');const B={sourceMapUrl:"Source map URL: ",debugInfoUrl:"DWARF symbols URL: ",add:"Add"},R=e.i18n.registerUIStrings("panels/sources/AddSourceMapURLDialog.ts",B),j=e.i18n.getLocalizedString.bind(void 0,R);class V extends t.Widget.HBox{input;dialog;callback;constructor(e,i,o){super(!0),this.contentElement.createChild("label").textContent=e,this.input=t.UIUtils.createInput("add-source-map","text","url"),this.input.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.contentElement.appendChild(this.input);const n=t.UIUtils.createTextButton(j(B.add),this.apply.bind(this),{jslogContext:"add"});this.contentElement.appendChild(n),this.dialog=new t.Dialog.Dialog(i),this.dialog.setSizeBehavior("MeasureContent"),this.dialog.setDefaultFocusedElement(this.input),this.callback=o}static createAddSourceMapURLDialog(e){return new V(j(B.sourceMapUrl),"add-source-map-url",e)}static createAddDWARFSymbolsURLDialog(e){return new V(j(B.debugInfoUrl),"add-debug-info-url",e)}show(){super.show(this.dialog.contentElement),this.dialog.show()}done(e){this.dialog.hide(),this.callback(e)}apply(){this.done(this.input.value)}onKeyDown(e){"Enter"===e.key&&(e.consume(!0),this.apply())}wasShown(){super.wasShown(),this.registerCSSFiles([U])}}var W=Object.freeze({__proto__:null,AddDebugInfoURLDialog:V});const O=new CSSStyleSheet;O.replaceSync(":host{z-index:30;padding:4px;background-color:var(--sys-color-surface3);border-radius:7px;border:2px solid var(--sys-color-divider);width:90%;pointer-events:auto}:host(.sources-edit-breakpoint-dialog){border-radius:0;z-index:30;background-color:var(--sys-color-surface3);width:555px;pointer-events:auto;margin-left:-1px;padding:0 10px 10px 5px;border:1px solid var(--sys-color-divider)}:host-context(.sources-edit-breakpoint-dialog) .condition-editor{background-color:var(--sys-color-cdt-base-container);margin:0 6px 20px 3px}:host-context(.sources-edit-breakpoint-dialog) .source-frame-breakpoint-toolbar{font-family:sans-serif;font-size:12px}:host-context(.sources-edit-breakpoint-dialog) .link,\n.devtools-link{font-family:sans-serif;font-size:12px;margin:0 3px}:host-context(.sources-edit-breakpoint-dialog) devtools-icon.link-icon{vertical-align:sub;margin-right:0.5ch;color:var(--icon-link);width:16px;height:16px}:host-context(.sources-edit-breakpoint-dialog) .link-wrapper{display:inline-flex}:host-context(.sources-edit-breakpoint-dialog) .dialog-header{display:flex;justify-content:space-between;align-items:center}:host-context(.sources-edit-breakpoint-dialog) .dialog-header > devtools-icon:hover{color:var(--icon-default-hover)}\n/*# sourceURL=breakpointEditDialog.css */\n");const{Direction:H}=s.TextEditorHistory,_={breakpointType:"Breakpoint type",breakpoint:"Breakpoint",closeDialog:"Close edit dialog and save changes",conditionalBreakpoint:"Conditional breakpoint",logpoint:"Logpoint",expressionToCheckBeforePausingEg:"Expression to check before pausing, e.g. x > 5",pauseOnlyWhenTheConditionIsTrue:"Pause only when the condition is true",learnMoreOnBreakpointTypes:"Learn more: Breakpoint Types",logMessageEgXIsX:"Log message, e.g. `'x is', x`",logAMessageToConsoleDoNotBreak:"Log a message to Console, do not break"},z=e.i18n.registerUIStrings("panels/sources/BreakpointEditDialog.ts",_),q=e.i18n.getLocalizedString.bind(void 0,z);class $ extends t.Widget.Widget{onFinish;finished;editor;typeSelector;placeholderCompartment;#e;#t;constructor(e,o,c,l){super(!0);const d=[n.javascript.javascriptLanguage,s.Config.baseConfiguration(o||""),s.Config.closeBrackets.instance(),s.Config.autocompletion.instance(),n.EditorView.lineWrapping,s.Config.showCompletionHint,s.Config.conservativeCompletion,n.javascript.javascriptLanguage.data.of({autocomplete:e=>this.#t.historyCompletions(e)}),n.autocompletion(),s.JavaScript.argumentHints()];this.onFinish=l,this.finished=!1,this.element.tabIndex=-1,this.element.classList.add("sources-edit-breakpoint-dialog"),this.element.setAttribute("jslog",`${a.dialog("edit-breakpoint")}`);const u=this.contentElement.createChild("div","dialog-header"),h=new t.Toolbar.Toolbar("source-frame-breakpoint-toolbar",u);h.appendText(`Line ${e+1}:`),this.typeSelector=new t.Toolbar.ToolbarComboBox(this.onTypeChanged.bind(this),q(_.breakpointType),void 0,"type"),this.typeSelector.createOption(q(_.breakpoint),"REGULAR_BREAKPOINT");const p=this.typeSelector.createOption(q(_.conditionalBreakpoint),"CONDITIONAL_BREAKPOINT"),g=this.typeSelector.createOption(q(_.logpoint),"LOGPOINT");this.typeSelector.select(c?g:p),h.appendToolbarItem(this.typeSelector);const m=o||"",b=e=>(s.JavaScript.isExpressionComplete(e.state.doc.toString()).then((t=>{t?this.finishEditing(!0,this.editor.state.doc.toString()):n.insertNewlineAndIndent(e)})),!0),f=[{key:"ArrowUp",run:()=>this.#t.moveHistory(-1)},{key:"ArrowDown",run:()=>this.#t.moveHistory(1)},{mac:"Ctrl-p",run:()=>this.#t.moveHistory(-1,!0)},{mac:"Ctrl-n",run:()=>this.#t.moveHistory(1,!0)},{key:"Mod-Enter",run:b},{key:"Enter",run:b},{key:"Shift-Enter",run:n.insertNewlineAndIndent},{key:"Escape",run:()=>(this.finishEditing(!1,""),!0)}];this.placeholderCompartment=new n.Compartment;const S=this.contentElement.appendChild(document.createElement("div"));S.classList.add("condition-editor"),S.setAttribute("jslog",`${a.textField().track({change:!0})}`),this.editor=new s.TextEditor.TextEditor(n.EditorState.create({doc:m,selection:{anchor:0,head:m.length},extensions:[this.placeholderCompartment.of(this.getPlaceholder()),n.keymap.of(f),d]})),S.appendChild(this.editor);const v=new r.Icon.Icon;v.name="cross",v.title=q(_.closeDialog),v.setAttribute("jslog",`${a.close().track({click:!0})}`),v.onclick=()=>this.finishEditing(!0,this.editor.state.doc.toString()),u.appendChild(v),this.#e=new s.AutocompleteHistory.AutocompleteHistory(i.Settings.Settings.instance().createLocalSetting("breakpoint-condition-history",[])),this.#t=new s.TextEditorHistory.TextEditorHistory(this.editor,this.#e);const C=this.contentElement.appendChild(document.createElement("div"));C.classList.add("link-wrapper");const w=t.Fragment.html`<x-link class="link devtools-link" tabindex="0" href="https://goo.gle/devtools-loc"
                                          jslog="${a.link("learn-more")}">${q(_.learnMoreOnBreakpointTypes)}</x-link>`,I=new r.Icon.Icon;I.name="open-externally",I.classList.add("link-icon"),w.prepend(I),C.appendChild(w),this.updateTooltip()}saveAndFinish(){this.finishEditing(!0,this.editor.state.doc.toString())}focusEditor(){this.editor.editor.focus()}onTypeChanged(){"REGULAR_BREAKPOINT"!==this.breakpointType?(this.focusEditor(),this.editor.dispatch({effects:this.placeholderCompartment.reconfigure(this.getPlaceholder())}),this.updateTooltip()):this.finishEditing(!0,"")}get breakpointType(){const e=this.typeSelector.selectedOption();return e?e.value:null}getPlaceholder(){const e=this.breakpointType;return"CONDITIONAL_BREAKPOINT"===e?n.placeholder(q(_.expressionToCheckBeforePausingEg)):"LOGPOINT"===e?n.placeholder(q(_.logMessageEgXIsX)):[]}updateTooltip(){const e=this.breakpointType;"CONDITIONAL_BREAKPOINT"===e?t.Tooltip.Tooltip.install(this.typeSelector.element,q(_.pauseOnlyWhenTheConditionIsTrue)):"LOGPOINT"===e&&t.Tooltip.Tooltip.install(this.typeSelector.element,q(_.logAMessageToConsoleDoNotBreak))}finishEditing(e,t){if(this.finished)return;this.finished=!0,this.editor.remove(),this.#e.pushHistoryItem(t);const i="LOGPOINT"===this.breakpointType;this.onFinish({committed:e,condition:t,isLogpoint:i})}wasShown(){super.wasShown(),this.registerCSSFiles([O])}get editorForTest(){return this.editor}}var G=Object.freeze({__proto__:null,BreakpointEditDialog:$});const K=new CSSStyleSheet;K.replaceSync('.call-frame-warnings-message{text-align:center;font-style:italic;padding:4px;color:var(--sys-color-on-surface-yellow);background-color:var(--sys-color-surface-yellow)}.ignore-listed-message{padding:1px}.ignore-listed-message-label{color:var(--sys-color-token-subtle);align-items:center;display:flex}.show-more-message > .link{margin-left:5px}.show-more-message{text-align:center;font-style:italic;padding:4px;border-top:1px solid var(--sys-color-divider)}.call-frame-item{padding:3px 8px 3px 20px;position:relative;min-height:18px;line-height:15px;display:flex;flex-wrap:wrap}.call-frame-title-text{text-overflow:ellipsis;overflow:hidden}.async-header + .call-frame-item{border-top:0}.call-frame-item:not(.async-header){border-top:1px solid var(--sys-color-divider)}.call-frame-item-title,\n.call-frame-location{display:flex;white-space:nowrap}.async-header .call-frame-item-title{font-weight:bold;color:var(--sys-color-on-surface);background-color:var(--sys-color-cdt-base-container);margin-left:-5px;padding:0 5px;z-index:1}.call-frame-item:focus-visible,\n.call-frame-item.async-header:focus-visible .call-frame-item-title{background-color:var(--sys-color-tonal-container)}.ignore-listed-checkbox:focus-visible{outline-width:unset}.call-frame-item:not(.async-header):hover{background-color:var(--sys-color-state-hover-on-subtle)}.call-frame-location{color:var(--sys-color-token-subtle);margin-left:auto;padding:0 10px}.async-header::before{content:" ";width:100%;border-top:1px solid var(--sys-color-divider);margin-top:8px;position:absolute;left:0}.ignore-listed-call-frame{opacity:60%;font-style:italic}.selected-call-frame-icon{display:none;position:absolute;top:3px;left:4px}.call-frame-item.selected .selected-call-frame-icon{display:block}.call-frame-warning-icon{display:block;position:absolute;top:3px;right:4px}@media (forced-colors: active){.call-frame-item:focus-visible,\n  .call-frame-item:not(.async-header):hover{forced-color-adjust:none;background-color:Highlight}.call-frame-item:focus-visible *,\n  .call-frame-item:not(.async-header):hover *{color:HighlightText}}\n/*# sourceURL=callStackSidebarPane.css */\n');const J={callStack:"Call Stack",notPaused:"Not paused",onIgnoreList:"on ignore list",showIgnorelistedFrames:"Show ignore-listed frames",showMore:"Show more",copyStackTrace:"Copy stack trace",callFrameWarnings:"Some call frames have warnings",debugFileNotFound:'Failed to load debug file "{PH1}".',restartFrame:"Restart frame"},X=e.i18n.registerUIStrings("panels/sources/CallStackSidebarPane.ts",J),Y=e.i18n.getLocalizedString.bind(void 0,X);let Q;class Z extends t.View.SimpleView{ignoreListMessageElement;ignoreListCheckboxElement;notPausedMessageElement;callFrameWarningsElement;items;list;showMoreMessageElement;showIgnoreListed;locationPool;updateThrottler;maxAsyncStackChainDepth;updateItemThrottler;scheduledForUpdateItems;muteActivateItem;lastDebuggerModel=null;constructor(){super(Y(J.callStack),!0,"sources.callstack"),this.contentElement.setAttribute("jslog",`${a.section("sources.callstack")}`),({element:this.ignoreListMessageElement,checkbox:this.ignoreListCheckboxElement}=this.createIgnoreListMessageElementAndCheckbox()),this.contentElement.appendChild(this.ignoreListMessageElement),this.notPausedMessageElement=this.contentElement.createChild("div","gray-info-message"),this.notPausedMessageElement.textContent=Y(J.notPaused),this.notPausedMessageElement.tabIndex=-1,this.callFrameWarningsElement=this.contentElement.createChild("div","call-frame-warnings-message");const e=new r.Icon.Icon;e.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},e.classList.add("call-frame-warning-icon"),this.callFrameWarningsElement.appendChild(e),this.callFrameWarningsElement.appendChild(document.createTextNode(Y(J.callFrameWarnings))),this.callFrameWarningsElement.tabIndex=-1,this.items=new t.ListModel.ListModel,this.list=new t.ListControl.ListControl(this.items,this,t.ListControl.ListMode.NonViewport),this.contentElement.appendChild(this.list.element),this.list.element.addEventListener("contextmenu",this.onContextMenu.bind(this),!1),self.onInvokeElement(this.list.element,(e=>{const t=this.list.itemForNode(e.target);t&&(this.activateItem(t),e.consume(!0))})),this.showMoreMessageElement=this.createShowMoreMessageElement(),this.showMoreMessageElement.classList.add("hidden"),this.contentElement.appendChild(this.showMoreMessageElement),this.showIgnoreListed=!1,this.locationPool=new u.LiveLocation.LiveLocationPool,this.updateThrottler=new i.Throttler.Throttler(100),this.maxAsyncStackChainDepth=te,this.update(),this.updateItemThrottler=new i.Throttler.Throttler(100),this.scheduledForUpdateItems=new Set,o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.DebugInfoAttached,this.debugInfoAttached,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return Q&&!t||(Q=new Z),Q}flavorChanged(e){this.showIgnoreListed=!1,this.ignoreListCheckboxElement.checked=!1,this.maxAsyncStackChainDepth=te,this.update()}debugInfoAttached(){this.update()}setSourceMapSubscription(e){this.lastDebuggerModel!==e&&(this.lastDebuggerModel&&this.lastDebuggerModel.sourceMapManager().removeEventListener(o.SourceMapManager.Events.SourceMapAttached,this.debugInfoAttached,this),this.lastDebuggerModel=e,this.lastDebuggerModel&&this.lastDebuggerModel.sourceMapManager().addEventListener(o.SourceMapManager.Events.SourceMapAttached,this.debugInfoAttached,this))}update(){this.updateThrottler.schedule((()=>this.doUpdate()))}async doUpdate(){this.locationPool.disposeAll(),this.callFrameWarningsElement.classList.add("hidden");const e=t.Context.Context.instance().flavor(o.DebuggerModel.DebuggerPausedDetails);if(this.setSourceMapSubscription(e?.debuggerModel??null),!e)return this.notPausedMessageElement.classList.remove("hidden"),this.ignoreListMessageElement.classList.add("hidden"),this.showMoreMessageElement.classList.add("hidden"),this.items.replaceAll([]),void t.Context.Context.instance().setFlavor(o.DebuggerModel.CallFrame,null);this.notPausedMessageElement.classList.add("hidden");const i=[],n=new Set;for(const t of e.callFrames){const e=ie.createForDebuggerCallFrame(t,this.locationPool,this.refreshItem.bind(this));i.push(e),t.missingDebugInfoDetails&&n.add(t.missingDebugInfoDetails.details)}const r=await Promise.all(i);n.size&&(this.callFrameWarningsElement.classList.remove("hidden"),t.Tooltip.Tooltip.install(this.callFrameWarningsElement,Array.from(n).join("\n")));let s=e.debuggerModel,a=e.asyncStackTraceId,c=e.asyncStackTrace,l=e.callFrames;for(let{maxAsyncStackChainDepth:e}=this;e>0;--e){if(!c){if(!a)break;if(a.debuggerId){const e=await o.DebuggerModel.DebuggerModel.modelForDebuggerId(a.debuggerId);if(!e)break;s=e}if(c=await s.fetchAsyncStackTrace(a),!c)break}const e=t.UIUtils.asyncStackTraceLabel(c.description,l);r.push(...await ie.createItemsForAsyncStack(e,s,c.callFrames,this.locationPool,this.refreshItem.bind(this))),l=c.callFrames,a=c.parentId,c=c.parent}this.showMoreMessageElement.classList.toggle("hidden",!c),this.items.replaceAll(r);for(const e of this.items)this.refreshItem(e);if(this.maxAsyncStackChainDepth===te){this.list.selectNextItem(!0,!1);const e=this.list.selectedItem();e&&this.activateItem(e)}this.updatedForTest()}updatedForTest(){}refreshItem(e){this.scheduledForUpdateItems.add(e),this.updateItemThrottler.schedule((async()=>{const e=Array.from(this.scheduledForUpdateItems);if(this.scheduledForUpdateItems.clear(),this.muteActivateItem=!0,!this.showIgnoreListed&&this.items.every((e=>e.isIgnoreListed))){this.showIgnoreListed=!0;for(let e=0;e<this.items.length;++e)this.list.refreshItemByIndex(e);this.ignoreListMessageElement.classList.toggle("hidden",!0)}else{this.showIgnoreListed=this.ignoreListCheckboxElement.checked;const t=new Set(e);let i=!1;for(let e=0;e<this.items.length;++e){const o=this.items.at(e);t.has(o)&&this.list.refreshItemByIndex(e),i=i||o.isIgnoreListed}this.ignoreListMessageElement.classList.toggle("hidden",!i)}delete this.muteActivateItem}))}createElementForItem(e){const n=document.createElement("div");n.classList.add("call-frame-item");const s=n.createChild("div","call-frame-item-title").createChild("div","call-frame-title-text");if(s.textContent=e.title,e.isAsyncHeader)n.classList.add("async-header");else{t.Tooltip.Tooltip.install(s,e.title);const i=n.createChild("div","call-frame-location");i.textContent=l.StringUtilities.trimMiddle(e.linkText,30),t.Tooltip.Tooltip.install(i,e.linkText),n.classList.toggle("ignore-listed-call-frame",e.isIgnoreListed),e.isIgnoreListed&&t.ARIAUtils.setDescription(n,Y(J.onIgnoreList)),e.frame||t.ARIAUtils.setDisabled(n,!0)}const a=e.frame,c=a===t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);n.classList.toggle("selected",c),t.ARIAUtils.setSelected(n,c),n.classList.toggle("hidden",!this.showIgnoreListed&&e.isIgnoreListed);const d=new r.Icon.Icon;if(d.data={iconName:"large-arrow-right-filled",color:"var(--icon-arrow-main-thread)",width:"14px",height:"14px"},d.classList.add("selected-call-frame-icon"),n.appendChild(d),n.tabIndex=e===this.list.selectedItem()?0:-1,a&&a.missingDebugInfoDetails){const e=new r.Icon.Icon;e.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},e.classList.add("call-frame-warning-icon");const o=a.missingDebugInfoDetails.resources.map((e=>Y(J.debugFileNotFound,{PH1:i.ParsedURL.ParsedURL.extractName(e.resourceUrl)})));t.Tooltip.Tooltip.install(e,[a.missingDebugInfoDetails.details,...o].join("\n")),n.appendChild(e)}return n}heightForItem(e){return console.assert(!1),0}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,o){i&&(i.tabIndex=-1),o&&(this.setDefaultFocusedElement(o),o.tabIndex=0,this.hasFocus()&&o.focus())}updateSelectedItemARIA(e,t){return!0}createIgnoreListMessageElementAndCheckbox(){const e=document.createElement("div");e.classList.add("ignore-listed-message");const t=e.createChild("label");t.classList.add("ignore-listed-message-label");const i=t.createChild("input");i.tabIndex=0,i.type="checkbox",i.classList.add("ignore-listed-checkbox"),t.append(Y(J.showIgnorelistedFrames));return i.addEventListener("click",(()=>{this.showIgnoreListed=i.checked;for(const e of this.items)this.refreshItem(e)})),{element:e,checkbox:i}}createShowMoreMessageElement(){const e=document.createElement("div");e.classList.add("show-more-message"),e.createChild("span");const t=e.createChild("span","link");return t.textContent=Y(J.showMore),t.addEventListener("click",(()=>{this.maxAsyncStackChainDepth+=te,this.update()}),!1),e}onContextMenu(e){const i=this.list.itemForNode(e.target);if(!i)return;const o=new t.ContextMenu.ContextMenu(e),n=i.frame;n&&o.defaultSection().appendItem(Y(J.restartFrame),(()=>{c.userMetrics.actionTaken(c.UserMetrics.Action.StackFrameRestarted),n.restart()}),{disabled:!n.canBeRestarted,jslogContext:"restart-frame"}),o.defaultSection().appendItem(Y(J.copyStackTrace),this.copyStackTrace.bind(this),{jslogContext:"copy-stack-trace"}),i.uiLocation&&this.appendIgnoreListURLContextMenuItems(o,i.uiLocation.uiSourceCode),o.show()}onClick(e){const t=this.list.itemForNode(e.target);t&&this.activateItem(t)}activateItem(e){const n=e.uiLocation;if(this.muteActivateItem||!n)return;this.list.selectItem(e);const r=e.frame,s=this.activeCallFrameItem();r&&s!==e?(r.debuggerModel.setSelectedCallFrame(r),t.Context.Context.instance().setFlavor(o.DebuggerModel.CallFrame,r),s&&this.refreshItem(s),this.refreshItem(e)):i.Revealer.reveal(n)}activeCallFrameItem(){const e=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);return e&&this.items.find((t=>t.frame===e))||null}appendIgnoreListURLContextMenuItems(e,t){const i=h.Persistence.PersistenceImpl.instance().binding(t);i&&(t=i.network);const o=e.section("ignoreList");if(!(o.items.length>0))for(const{text:e,callback:i,jslogContext:n}of u.IgnoreListManager.IgnoreListManager.instance().getIgnoreListURLContextMenuItems(t))o.appendItem(e,i,{jslogContext:n})}selectNextCallFrameOnStack(){const e=this.activeCallFrameItem();for(let t=e?this.items.indexOf(e)+1:0;t<this.items.length;t++){const e=this.items.at(t);if(e.frame){this.activateItem(e);break}}}selectPreviousCallFrameOnStack(){const e=this.activeCallFrameItem();for(let t=e?this.items.indexOf(e)-1:this.items.length-1;t>=0;t--){const e=this.items.at(t);if(e.frame){this.activateItem(e);break}}}copyStackTrace(){const e=[];for(const t of this.items){let i=t.title;t.uiLocation&&(i+=" ("+t.uiLocation.linkText(!0)+")"),e.push(i)}c.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.join("\n"))}wasShown(){super.wasShown(),this.registerCSSFiles([K])}}const ee=Symbol("element"),te=32;class ie{isIgnoreListed;title;linkText;uiLocation;isAsyncHeader;updateDelegate;frame;static async createForDebuggerCallFrame(e,i,o){const n=e.functionName,r=new ie(t.UIUtils.beautifyFunctionName(n),o,e);return await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().createCallFrameLiveLocation(e.location(),r.update.bind(r),i),p.NamesResolver.resolveDebuggerFrameFunctionName(e).then((e=>{e&&e!==n&&(r.title=e,r.updateDelegate(r))})),r}static async createItemsForAsyncStack(e,i,o,n,r){const s=new WeakMap,a=new ie(e,r);s.set(a,new Set),a.isAsyncHeader=!0;const c=[],l=[];for(const e of o){const o=new ie(t.UIUtils.beautifyFunctionName(e.functionName),d),r=i.createRawLocationByScriptId(e.scriptId,e.lineNumber,e.columnNumber);l.push(u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().createCallFrameLiveLocation(r,o.update.bind(o),n)),c.push(o)}return await Promise.all(l),r(a),[a,...c];function d(e){r(e);let t=!1;const i=s.get(a);i&&(e.isIgnoreListed?(i.delete(e),t=0===i.size):(t=0===i.size,i.add(e)),a.isIgnoreListed=0===i.size),t&&r(a)}}constructor(e,t,i){this.isIgnoreListed=!1,this.title=e,this.linkText="",this.uiLocation=null,this.isAsyncHeader=!1,this.updateDelegate=t,this.frame=i}async update(e){const t=await e.uiLocation();this.isIgnoreListed=await e.isIgnoreListed(),this.linkText=t?t.linkText():"",this.uiLocation=t,this.updateDelegate(this)}}var oe=Object.freeze({__proto__:null,CallStackSidebarPane:Z,elementSymbol:ee,defaultMaxAsyncStackChainDepth:te,ActionDelegate:class{handleAction(e,t){switch(t){case"debugger.next-call-frame":return Z.instance().selectNextCallFrameOnStack(),!0;case"debugger.previous-call-frame":return Z.instance().selectPreviousCallFrameOnStack(),!0}return!1}},Item:ie});const ne={beforeBidderWorkletBiddingStart:"Bidder Bidding Phase Start",beforeBidderWorkletReportingStart:"Bidder Reporting Phase Start",beforeSellerWorkletScoringStart:"Seller Scoring Phase Start",beforeSellerWorkletReportingStart:"Seller Reporting Phase Start",setTimeoutOrIntervalFired:"{PH1} fired",scriptFirstStatement:"Script First Statement",scriptBlockedByContentSecurity:"Script Blocked by Content Security Policy",requestAnimationFrame:"Request Animation Frame",cancelAnimationFrame:"Cancel Animation Frame",animationFrameFired:"Animation Frame Fired",webglErrorFired:"WebGL Error Fired",webglWarningFired:"WebGL Warning Fired",setInnerhtml:"Set `innerHTML`",createCanvasContext:"Create canvas context",createAudiocontext:"Create `AudioContext`",closeAudiocontext:"Close `AudioContext`",resumeAudiocontext:"Resume `AudioContext`",suspendAudiocontext:"Suspend `AudioContext`",sinkViolations:"Sink Violations",policyViolations:"Policy Violations"},re=e.i18n.registerUIStrings("panels/sources/CategorizedBreakpointL10n.ts",ne),se=e.i18n.getLazilyComputedLocalizedString.bind(void 0,re);function ae(t){return(de.get(t)??e.i18n.lockedLazyString(t))()}const ce={beforeBidderWorkletBiddingStart:se(ne.beforeBidderWorkletBiddingStart),beforeBidderWorkletReportingStart:se(ne.beforeBidderWorkletReportingStart),beforeSellerWorkletScoringStart:se(ne.beforeSellerWorkletScoringStart),beforeSellerWorkletReportingStart:se(ne.beforeSellerWorkletReportingStart),setTimeout:e.i18n.lockedLazyString("setTimeout"),clearTimeout:e.i18n.lockedLazyString("clearTimeout"),"setTimeout.callback":se(ne.setTimeoutOrIntervalFired,{PH1:"setTimeout"}),setInterval:e.i18n.lockedLazyString("setInterval"),clearInterval:e.i18n.lockedLazyString("clearInterval"),"setInterval.callback":se(ne.setTimeoutOrIntervalFired,{PH1:"setInterval"}),scriptFirstStatement:se(ne.scriptFirstStatement),scriptBlockedByCSP:se(ne.scriptBlockedByContentSecurity),sharedStorageWorkletScriptFirstStatement:se(ne.scriptFirstStatement),requestAnimationFrame:se(ne.requestAnimationFrame),cancelAnimationFrame:se(ne.cancelAnimationFrame),"requestAnimationFrame.callback":se(ne.animationFrameFired),webglErrorFired:se(ne.webglErrorFired),webglWarningFired:se(ne.webglWarningFired),"Element.setInnerHTML":se(ne.setInnerhtml),canvasContextCreated:se(ne.createCanvasContext),"Geolocation.getCurrentPosition":e.i18n.lockedLazyString("getCurrentPosition"),"Geolocation.watchPosition":e.i18n.lockedLazyString("watchPosition"),"Notification.requestPermission":e.i18n.lockedLazyString("requestPermission"),"DOMWindow.close":e.i18n.lockedLazyString("window.close"),"Document.write":e.i18n.lockedLazyString("document.write"),audioContextCreated:se(ne.createAudiocontext),audioContextClosed:se(ne.closeAudiocontext),audioContextResumed:se(ne.resumeAudiocontext),audioContextSuspended:se(ne.suspendAudiocontext)},le={"trustedtype-policy-violation":se(ne.policyViolations),"trustedtype-sink-violation":se(ne.sinkViolations)},de=new Map([...Object.entries(ce),...Object.entries(le)]);var ue=Object.freeze({__proto__:null,getLocalizedBreakpointName:ae});class he{uiSourceCode;constructor(e,t){this.uiSourceCode=e}static accepts(e){return!1}willHide(){}rightToolbarItems(){return[]}leftToolbarItems(){return[]}populateLineGutterContextMenu(e,t){}populateTextAreaContextMenu(e,t,i){}decorationChanged(e,t){}editorExtension(){return[]}editorInitialized(e){}dispose(){}}var pe=Object.freeze({__proto__:null,Plugin:he});const ge={clickToShowCoveragePanel:"Click to show Coverage Panel",showDetails:"Show Details",coverageS:"Coverage: {PH1}",coverageNa:"Coverage: n/a"},me=e.i18n.registerUIStrings("panels/sources/CoveragePlugin.ts",ge),be=e.i18n.getLocalizedString.bind(void 0,me);class fe extends he{originalSourceCode;infoInToolbar;model;coverage;#i;constructor(e,i){super(e),this.originalSourceCode=this.uiSourceCode,this.#i=i,this.infoInToolbar=new t.Toolbar.ToolbarButton(be(ge.clickToShowCoveragePanel),void 0,void 0,"debugger.show-coverage"),this.infoInToolbar.setSecondary(),this.infoInToolbar.addEventListener("Click",(()=>{t.ViewManager.ViewManager.instance().showView("coverage")}));const n=o.TargetManager.TargetManager.instance().primaryPageTarget();n&&(this.model=n.model(b.CoverageModel.CoverageModel),this.model&&(this.model.addEventListener(b.CoverageModel.Events.CoverageReset,this.handleReset,this),this.coverage=this.model.getCoverageForUrl(this.originalSourceCode.url()),this.coverage&&this.coverage.addEventListener(b.CoverageModel.URLCoverageInfo.Events.SizesChanged,this.handleCoverageSizesChanged,this))),this.updateStats()}dispose(){this.coverage&&this.coverage.removeEventListener(b.CoverageModel.URLCoverageInfo.Events.SizesChanged,this.handleCoverageSizesChanged,this),this.model&&this.model.removeEventListener(b.CoverageModel.Events.CoverageReset,this.handleReset,this)}static accepts(e){return e.contentType().isDocumentOrScriptOrStyleSheet()}handleReset(){this.coverage=null,this.updateStats()}handleCoverageSizesChanged(){this.updateStats()}updateStats(){if(this.coverage){this.infoInToolbar.setTitle(be(ge.showDetails));const t=new Intl.NumberFormat(e.DevToolsLocale.DevToolsLocale.instance().locale,{style:"percent",maximumFractionDigits:1});this.infoInToolbar.setText(be(ge.coverageS,{PH1:t.format(this.coverage.usedPercentage())}))}else this.infoInToolbar.setTitle(be(ge.clickToShowCoveragePanel)),this.infoInToolbar.setText(be(ge.coverageNa))}rightToolbarItems(){return[this.infoInToolbar]}editorExtension(){return ye.of([])}getCoverageManager(){return this.uiSourceCode.getDecorationData("coverage")}editorInitialized(e){this.getCoverageManager()&&this.startDecoUpdate(e)}decorationChanged(e,t){"coverage"===e&&this.startDecoUpdate(t)}startDecoUpdate(e){const i=this.getCoverageManager();(i?i.usageByLine(this.uiSourceCode,this.#o(e)):Promise.resolve([])).then((i=>{const o=Boolean(e.state.field(Ie,!1));var r;i.length?o?e.dispatch({effects:we.of(i)}):e.dispatch({effects:ye.reconfigure([Ie.init((e=>Ce(i,e))),(r=this.uiSourceCode.url(),n.gutter({markers:e=>e.state.field(Ie),domEventHandlers:{click:()=>(t.ViewManager.ViewManager.instance().showView("coverage").then((()=>{const e=t.ViewManager.ViewManager.instance().view("coverage");return e&&e.widget()})).then((e=>{const t=r.match(/(.*):formatted$/),i=t&&t[1]||r;e.selectCoverageItemByUrl(i)})),!0)},class:"cm-coverageGutter"})),xe])}):o&&e.dispatch({effects:ye.reconfigure([])})}))}#o(e){const t=[];for(let i=1;i<=e.state.doc.lines;++i){const o=e.state.doc.line(i),{lineNumber:n,columnNumber:r}=this.#i.editorLocationToUILocation(i-1,0),{lineNumber:s,columnNumber:a}=this.#i.editorLocationToUILocation(i-1,o.length);t.push(new g.TextRange.TextRange(n,r,s,a))}return t}}const Se=new class extends n.GutterMarker{elementClass="cm-coverageUsed"},ve=new class extends n.GutterMarker{elementClass="cm-coverageUnused"};function Ce(e,t){const i=new n.RangeSetBuilder;for(let o=0;o<e.length;o++){const n=e[o];if(void 0!==n&&o<t.doc.lines){const e=t.doc.line(o+1).from;i.add(e,e,n?Se:ve)}}return i.finish()}const we=n.StateEffect.define(),Ie=n.StateField.define({create:()=>n.RangeSet.empty,update:(e,t)=>t.effects.reduce(((e,i)=>i.is(we)?Ce(i.value,t.state):e),e.map(t.changes))});const ye=new n.Compartment,xe=n.EditorView.baseTheme({".cm-coverageGutter":{width:"5px",marginLeft:"3px"},".cm-coverageUnused":{backgroundColor:"var(--app-color-coverage-unused)"},".cm-coverageUsed":{backgroundColor:"var(--app-color-coverage-used)"}});var ke=Object.freeze({__proto__:null,CoveragePlugin:fe});const Te={openColorPicker:"Open color picker.",openCubicBezierEditor:"Open cubic bezier editor.",addSourceMap:"Add source map…"},Ee=e.i18n.registerUIStrings("panels/sources/CSSPlugin.ts",Te),Le=e.i18n.getLocalizedString.bind(void 0,Ee),Me=new Set(["ColorLiteral","NumberLiteral","StringLiteral","Comment","Important"]);async function Pe(e,t,i){const r=n.syntaxTree(e.state).resolveInner(e.pos,-1);if("ClassName"===r.name){d(i);const e=function(e,t){const i=t.getStyleSheetIdsForURL(e);if(0===i.length)throw new Error("Can't find style sheet ID for current URL");return i[0]}(t.url(),i),o=await i.getClassNames(e);return{from:r.from,options:o.map((e=>({type:"constant",label:e})))}}const s=function(e,t){if(Me.has(e.name))return null;for(let i=e;i&&"StyleSheet"!==i.name&&"Styles"!==i.name&&"CallExpression"!==i.name;i=i.parent)if("Declaration"===i.name){const e=i.getChild("PropertyName"),o=i.getChild(":");return e&&o&&o.to<=t?e:null}return null}(r,e.pos);if(s){const t=o.CSSMetadata.cssMetadata().getPropertyValues(e.state.sliceDoc(s.from,s.to));return{from:"ValueName"===r.name?r.from:e.pos,options:t.map((e=>({type:"constant",label:e}))),validFor:/^[\w\P{ASCII}\-]+$/u}}return null}class Fe extends n.WidgetType{#n;#r;#s;constructor(e,t,i){super(),this.#r=e,this.#n=t,this.#s=i}eq(e){return this.#r.equal(e.#r)&&this.#n===e.#n&&this.#s===e.#s}toDOM(e){const t=new v.ColorSwatch.ColorSwatch(Le(Te.openColorPicker));t.renderColor(this.#r);const i=t.createChild("span");return i.textContent=this.#n,i.setAttribute("hidden","true"),t.addEventListener(v.ColorSwatch.ColorChangedEvent.eventName,(i=>{const o=i.data.color.getAuthoredText()??i.data.color.asString();e.dispatch({changes:{from:this.#s,to:this.#s+this.#n.length,insert:o}}),this.#n=o,this.#r=t.getColor()})),t.addEventListener(v.ColorSwatch.ClickEvent.eventName,(i=>{i.consume(!0),e.dispatch({effects:Ne.of({type:0,pos:e.posAtDOM(t),text:this.#n,swatch:t,color:this.#r})})})),t}ignoreEvent(){return!0}}class De extends n.WidgetType{curve;text;constructor(e,t){super(),this.curve=e,this.text=t}eq(e){return this.curve.asCSSText()===e.curve.asCSSText()&&this.text===e.text}toDOM(e){const i=v.Swatches.BezierSwatch.create();return i.setBezierText(this.text),t.Tooltip.Tooltip.install(i.iconElement(),Le(Te.openCubicBezierEditor)),i.iconElement().addEventListener("click",(t=>{t.consume(!0),e.dispatch({effects:Ne.of({type:1,pos:e.posAtDOM(i),text:this.text,swatch:i,curve:this.curve})})}),!1),i.hideText(!0),i}ignoreEvent(){return!0}}const Ne=n.StateEffect.define(),Ae=n.Annotation.define(),Ue=n.StateField.define({create:()=>null,update(e,t){!t.docChanged&&!t.selection||t.annotation(Ae)||(e=null);for(const i of t.effects)i.is(Ne)&&(e=i.value);return e},provide:e=>n.showTooltip.from(e,(e=>e&&function(e){return{pos:e.pos,arrow:!0,create(t){let i,o,n=e.text;if(0===e.type){const n=new S.Spectrum.Spectrum;o=e=>{n.addEventListener("ColorChanged",e)},n.addEventListener("SizeChanged",(()=>t.requestMeasure())),n.setColor(e.color),i=n,c.userMetrics.colorPickerOpenedFrom(0)}else{const t=new v.BezierEditor.BezierEditor(e.curve);i=t,o=e=>{t.addEventListener("BezierChanged",e)}}const r=document.createElement("div");return r.className="cm-tooltip-swatchEdit",i.markAsRoot(),i.show(r),i.showWidget(),i.element.addEventListener("keydown",(o=>{"Escape"===o.key&&(o.consume(),t.dispatch({effects:Ne.of(null),changes:n===e.text?void 0:{from:e.pos,to:e.pos+n.length,insert:e.text}}),i.hideWidget(),t.focus())})),i.element.addEventListener("focusout",(e=>{e.relatedTarget&&!i.element.contains(e.relatedTarget)&&(t.dispatch({effects:Ne.of(null)}),i.hideWidget())}),!1),i.element.addEventListener("mousedown",(e=>e.consume())),{dom:r,resize:!1,offset:{x:-8,y:0},mount:()=>{i.focus(),i.wasShown(),o((i=>{t.dispatch({changes:{from:e.pos,to:e.pos+n.length,insert:i.data},annotations:Ae.of(!0)}),n=i.data}))}}}}}(e)))});function Be(e,o,r){const s=new n.RangeSetBuilder;return function(e,o,r,s,a){let c=e.doc.lineAt(o);function l(t,i){return t>=c.to&&(c=e.doc.lineAt(t)),c.text.slice(t-c.from,i-c.from)}const d=n.ensureSyntaxTree(e,r,100);d&&d.iterate({from:o,to:r,enter:o=>{let n;if("ValueName"===o.name||"ColorLiteral"===o.name?n=l(o.from,o.to):"Callee"===o.name&&/^(?:(?:rgba?|hsla?|hwba?|lch|oklch|lab|oklab|color)|cubic-bezier)$/.test(l(o.from,o.to))&&(n=e.sliceDoc(o.from,o.node.parent.to)),n){const e=i.Color.parse(n);if(e)s(o.from,e,n);else{const e=t.Geometry.CubicBezier.parse(n);e&&a(o.from,e,n)}}}})}(e,o,r,((e,t,i)=>{s.add(e,e,n.Decoration.widget({widget:new Fe(t,i,e)}))}),((e,t,i)=>{s.add(e,e,n.Decoration.widget({widget:new De(t,i)}))})),s.finish()}const Re=n.ViewPlugin.fromClass(class{decorations;constructor(e){this.decorations=Be(e.state,e.viewport.from,e.viewport.to)}update(e){(e.viewportChanged||e.docChanged)&&(this.decorations=Be(e.state,e.view.viewport.from,e.view.viewport.to))}},{decorations:e=>e.decorations});function je(e){if("Unit"===e.name&&(e=e.parent),"NumberLiteral"===e.name){const t=e.lastChild;return{from:e.from,to:t&&"Unit"===t.name?t.from:e.to}}return null}function Ve(e,t){const{head:i}=e.state.selection.main,o=n.syntaxTree(e.state).resolveInner(i,-1),r=je(o)||je(o.resolve(i,1));if(!r)return!1;const s=Number(e.state.sliceDoc(r.from,r.to));return!isNaN(s)&&(e.dispatch({changes:{from:r.from,to:r.to,insert:String(s+t)},scrollIntoView:!0,userEvent:"insert.modifyUnit"}),!0)}function We(){let e=null;const i=t.ShortcutRegistry.ShortcutRegistry.instance().getShortcutListener({"sources.increment-css":()=>Promise.resolve(Ve(e,1)),"sources.increment-css-by-ten":()=>Promise.resolve(Ve(e,10)),"sources.decrement-css":()=>Promise.resolve(Ve(e,-1)),"sources.decrement-css-by-ten":()=>Promise.resolve(Ve(e,-10))});return n.EditorView.domEventHandlers({keydown:(t,o)=>{const n=e;return e=o,i(t),e=n,t.defaultPrevented}})}class Oe extends he{#a;constructor(e,t){super(e,t),o.TargetManager.TargetManager.instance().observeModels(o.CSSModel.CSSModel,this)}static accepts(e){return e.contentType().hasStyleSheets()}modelAdded(e){e.target()===o.TargetManager.TargetManager.instance().primaryPageTarget()&&(this.#a=e)}modelRemoved(e){this.#a===e&&(this.#a=void 0)}editorExtension(){return[We(),this.#c(),[Re,Ue]]}#c(){const{cssCompletionSource:e}=n.css,t=this.uiSourceCode,i=this.#a;return n.autocompletion({override:[async o=>await Pe(o,t,i)||e(o)]})}populateTextAreaContextMenu(e){const t=this.#a,i=this.uiSourceCode.url();if(this.uiSourceCode.project().type()===f.Workspace.projectTypes.Network&&t&&!u.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(i)){const o=Le(Te.addSourceMap);e.debugSection().appendItem(o,(()=>function(e,t){V.createAddSourceMapURLDialog((i=>{u.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().modelToInfo.get(e)?.addSourceMap(t,i)})).show()}(t,i)),{jslogContext:"add-source-map"})}}}var He=Object.freeze({__proto__:null,cssBindings:We,CSSPlugin:Oe});const _e=new CSSStyleSheet;_e.replaceSync(".paused-status{margin:6px;padding:4px 10px;border-radius:10px;background-color:var(--sys-color-yellow-container);color:var(--sys-color-on-yellow-container)}.paused-status.error-reason{background-color:var(--sys-color-surface-error);color:var(--sys-color-on-surface-error)}.status-main{padding-left:18px;position:relative}.status-sub:not(:empty){padding-left:15px;padding-top:5px;overflow:hidden;text-overflow:ellipsis}.paused-status.error-reason .status-sub{color:var(--sys-color-error);line-height:11px;max-height:27px;user-select:text}devtools-icon{position:absolute;left:-1px;top:-1px}\n/*# sourceURL=debuggerPausedMessage.css */\n");const ze={pausedOnS:"Paused on {PH1}",childSAdded:"Child {PH1} added",descendantSAdded:"Descendant {PH1} added",descendantSRemoved:"Descendant {PH1} removed",pausedOnEventListener:"Paused on event listener",pausedOnXhrOrFetch:"Paused on XHR or fetch",pausedOnException:"Paused on exception",pausedOnPromiseRejection:"Paused on `promise` rejection",pausedOnAssertion:"Paused on assertion",pausedOnDebuggedFunction:"Paused on debugged function",pausedBeforePotentialOutofmemory:"Paused before potential out-of-memory crash",pausedOnCspViolation:"Paused on CSP violation",trustedTypeSinkViolation:"`Trusted Type` Sink Violation",trustedTypePolicyViolation:"`Trusted Type` Policy Violation",pausedOnBreakpoint:"Paused on breakpoint",debuggerPaused:"Debugger paused",subtreeModifications:"subtree modifications",attributeModifications:"attribute modifications",nodeRemoval:"node removal",webglErrorFiredS:"WebGL Error Fired ({PH1})",scriptBlockedDueToContent:"Script blocked due to Content Security Policy directive: {PH1}"},qe=e.i18n.registerUIStrings("panels/sources/DebuggerPausedMessage.ts",ze),$e=e.i18n.getLocalizedString.bind(void 0,qe),Ge=e.i18n.getLazilyComputedLocalizedString.bind(void 0,qe);class Ke{elementInternal;contentElement;constructor(){this.elementInternal=document.createElement("div"),this.elementInternal.classList.add("paused-message"),this.elementInternal.classList.add("flex-none"),this.elementInternal.setAttribute("jslog",`${a.dialog("debugger-paused")}`);const e=t.UIUtils.createShadowRootWithCoreStyles(this.elementInternal,{cssFile:[_e],delegatesFocus:void 0});this.contentElement=e.createChild("div"),t.ARIAUtils.markAsPoliteLiveRegion(this.elementInternal,!1)}element(){return this.elementInternal}static descriptionWithoutStack(e){const t=/^\s+at\s/m.exec(e);return t?e.substring(0,t.index-1):e.substring(0,e.lastIndexOf("\n"))}static async createDOMBreakpointHitMessage(t){const n=document.createElement("span"),s=t.debuggerModel.target().model(o.DOMDebuggerModel.DOMDebuggerModel);if(!t.auxData||!s)return n;const a=s.resolveDOMBreakpointData(t.auxData);if(!a)return n;const c=n.createChild("div","status-main"),l=new r.Icon.Icon;l.data={iconName:"info",color:"var(--sys-color-on-yellow-container)",width:"16px",height:"16px"},c.appendChild(l);const d=Je.get(a.type);c.appendChild(document.createTextNode($e(ze.pausedOnS,{PH1:d?d():String(null)})));const u=n.createChild("div","status-sub monospace"),h=await i.Linkifier.Linkifier.linkify(a.node);if(u.appendChild(h),a.targetNode){const t=await i.Linkifier.Linkifier.linkify(a.targetNode);let o;o=a.insertion?a.targetNode===a.node?e.i18n.getFormatLocalizedString(qe,ze.childSAdded,{PH1:t}):e.i18n.getFormatLocalizedString(qe,ze.descendantSAdded,{PH1:t}):e.i18n.getFormatLocalizedString(qe,ze.descendantSRemoved,{PH1:t}),u.appendChild(document.createElement("br")),u.appendChild(o)}return n}static#l(e){if(!e)return"";const{eventName:t,webglErrorName:i,directiveText:n,targetName:r}=e;if("instrumentation:webglErrorFired"===t&&i){const e=i.replace(/^.*(0x[0-9a-f]+).*$/i,"$1");return $e(ze.webglErrorFiredS,{PH1:e})}if("instrumentation:scriptBlockedByCSP"===t&&n)return $e(ze.scriptBlockedDueToContent,{PH1:n});let s=o.EventBreakpointsModel.EventBreakpointsManager.instance().resolveEventListenerBreakpoint(e);return s?ae(s.name):(s=o.DOMDebuggerModel.DOMDebuggerManager.instance().resolveEventListenerBreakpoint(e),s&&r?r+"."+s.name:s?.name??"")}async render(e,i,o){if(this.contentElement.removeChildren(),this.contentElement.hidden=!e,!e)return;const n=this.contentElement.createChild("div","paused-status"),s="exception"===e.reason||"promiseRejection"===e.reason||"assert"===e.reason||"OOM"===e.reason;let a;if("DOM"===e.reason)a=await Ke.createDOMBreakpointHitMessage(e);else if("EventListener"===e.reason){const t=Ke.#l(e.auxData);a=c($e(ze.pausedOnEventListener),t)}else if("XHR"===e.reason){const t=e.auxData;a=c($e(ze.pausedOnXhrOrFetch),t.url||"")}else if("exception"===e.reason){const t=e.auxData,i=t.description||t.value||"",o=Ke.descriptionWithoutStack(i);a=c($e(ze.pausedOnException),o,i)}else if("promiseRejection"===e.reason){const t=e.auxData,i=t.description||t.value||"",o=Ke.descriptionWithoutStack(i);a=c($e(ze.pausedOnPromiseRejection),o,i)}else if("assert"===e.reason)a=c($e(ze.pausedOnAssertion));else if("debugCommand"===e.reason)a=c($e(ze.pausedOnDebuggedFunction));else if("OOM"===e.reason)a=c($e(ze.pausedBeforePotentialOutofmemory));else if("CSPViolation"===e.reason&&e.auxData&&e.auxData.violationType){const t=e.auxData.violationType;"trustedtype-sink-violation"===t?a=c($e(ze.pausedOnCspViolation),$e(ze.trustedTypeSinkViolation)):"trustedtype-policy-violation"===t&&(a=c($e(ze.pausedOnCspViolation),$e(ze.trustedTypePolicyViolation)))}else if(e.callFrames.length){const t=await i.rawLocationToUILocation(e.callFrames[0].location()),n=t?o.findBreakpoint(t):null;a=c($e(n?ze.pausedOnBreakpoint:ze.debuggerPaused))}else console.warn("ScriptsPanel paused, but callFrames.length is zero.");function c(e,i,o){const n=document.createElement("span"),a=n.createChild("div","status-main"),c=new r.Icon.Icon;if(c.data={iconName:s?"cross-circle-filled":"info",color:s?"var(--icon-error)":"var(--sys-color-on-yellow-container)",width:"16px",height:"16px"},a.appendChild(c),a.appendChild(document.createTextNode(e)),i){const e=n.createChild("div","status-sub monospace");e.textContent=i,t.Tooltip.Tooltip.install(e,o||i)}return n}n.classList.toggle("error-reason",s),a&&n.appendChild(a)}}const Je=new Map([["subtree-modified",Ge(ze.subtreeModifications)],["attribute-modified",Ge(ze.attributeModifications)],["node-removed",Ge(ze.nodeRemoval)]]);var Xe=Object.freeze({__proto__:null,DebuggerPausedMessage:Ke,BreakpointTypeNouns:Je});const Ye=new CSSStyleSheet;Ye.replaceSync(":host{overflow-y:auto}.icon,\n.icon-basic,\n.icon-badge{margin:-3px -5px}.navigator-fs-tree-item:not(.has-mapped-files):not(.selected) > :not(.selection),\n.navigator-fs-folder-tree-item:not(.has-mapped-files):not(.selected) > :not(.selection){filter:grayscale(50%);opacity:50%}.is-ignore-listed{opacity:50%}.tree-outline li{min-height:20px}.tree-outline li:hover:not(.selected) .selection{display:block;background-color:var(--sys-color-state-hover-on-subtle)}.navigator-fs-folder-tree-item devtools-icon{color:var(--icon-folder-workspace)}.navigator-fs-tree-item devtools-icon{color:var(--icon-file-authored)}.navigator-nw-folder-tree-item devtools-icon{color:var(--icon-folder-deployed)}.navigator-sm-script-tree-item devtools-icon,\n.navigator-script-tree-item devtools-icon,\n.navigator-snippet-tree-item devtools-icon{color:var(--icon-file-script)}.navigator-file-tree-item devtools-icon.dot::before{width:7px;height:7px;top:12px;left:11px}.navigator-file-tree-item:hover:not(.force-white-icons) devtools-icon.dot::before{outline-color:var(--icon-gap-hover)}.navigator-file-tree-item.selected:not(.force-white-icons) devtools-icon.dot::before{outline-color:var(--icon-gap-inactive)}.navigator-file-tree-item.selected.force-white-icons devtools-icon.dot::before{outline-color:var(--icon-gap-focus-selected)}.navigator-sm-stylesheet-tree-item devtools-icon,\n.navigator-stylesheet-tree-item devtools-icon{color:var(--icon-file-styles)}.navigator-image-tree-item devtools-icon,\n.navigator-font-tree-item devtools-icon{color:var(--icon-file-image)}.navigator-nw-folder-tree-item.is-from-source-map devtools-icon{color:var(--icon-folder-authored)}.tree-outline:not(:has(.navigator-deployed-tree-item)) .navigator-sm-folder-tree-item .tree-element-title,\n.tree-outline:not(:has(.navigator-deployed-tree-item)) .navigator-sm-script-tree-item .tree-element-title,\n.tree-outline:not(:has(.navigator-deployed-tree-item)) .navigator-sm-stylesheet-tree-item .tree-element-title{font-style:italic}@media (forced-colors: active){.tree-outline li .leading-icons devtools-icon{color:ButtonText}.tree-outline li:hover:not(.selected) .selection{forced-color-adjust:none;background-color:Highlight}.tree-outline:not(.hide-selection-when-blurred) li.parent:hover:not(.selected)::before{background-color:HighlightText}.tree-outline:not(.hide-selection-when-blurred) li:hover:not(.selected) devtools-icon,\n  .tree-outline li:not(.selected):hover .tree-element-title{forced-color-adjust:none;color:HighlightText}.navigator-fs-tree-item:not(.has-mapped-files):not(.selected) > :not(.selection),\n  .navigator-fs-folder-tree-item:not(.has-mapped-files):not(.selected) > :not(.selection),\n  .is-ignore-listed{filter:none;opacity:100%}}\n/*# sourceURL=navigatorTree.css */\n");const Qe=new CSSStyleSheet;Qe.replaceSync(".navigator-toolbar{border-bottom:1px solid var(--sys-color-divider);padding-left:8px}\n/*# sourceURL=navigatorView.css */\n");class Ze{searchId;searchResultCandidates;searchResultCallback;searchFinishedCallback;searchConfig;constructor(){this.searchId=0,this.searchResultCandidates=[],this.searchResultCallback=null,this.searchFinishedCallback=null,this.searchConfig=null}static filesComparator(e,t){if(e.isDirty()&&!t.isDirty())return-1;if(!e.isDirty()&&t.isDirty())return 1;const i=e.project().type()===f.Workspace.projectTypes.FileSystem&&!h.Persistence.PersistenceImpl.instance().binding(e);if(i!==(t.project().type()===f.Workspace.projectTypes.FileSystem&&!h.Persistence.PersistenceImpl.instance().binding(t)))return i?1:-1;const o=e.url(),n=t.url();return o&&!n?-1:!o&&n?1:l.StringUtilities.naturalOrderComparator(e.fullDisplayName(),t.fullDisplayName())}static urlComparator(e,t){return l.StringUtilities.naturalOrderComparator(e.url(),t.url())}performIndexing(e){this.stopSearch();const t=this.projects(),o=new i.Progress.CompositeProgress(e);for(let e=0;e<t.length;++e){const i=t[e],n=o.createSubProgress([...i.uiSourceCodes()].length);i.indexContent(n)}}projects(){const e=i.Settings.Settings.instance().moduleSetting("search-in-anonymous-and-content-scripts").get();return f.Workspace.WorkspaceImpl.instance().projects().filter((t=>t.type()!==f.Workspace.projectTypes.Service&&(!(!e&&t.isServiceProject()&&t.type()!==f.Workspace.projectTypes.Formatter)&&!(!e&&t.type()===f.Workspace.projectTypes.ContentScripts))))}performSearch(e,t,o,n){this.stopSearch(),this.searchResultCandidates=[],this.searchResultCallback=o,this.searchFinishedCallback=n,this.searchConfig=e;const r=[],s=new i.Progress.CompositeProgress(t),a=s.createSubProgress(),c=new i.Progress.CompositeProgress(s.createSubProgress());for(const t of this.projects()){const i=[...t.uiSourceCodes()].length,o=c.createSubProgress(i),n=this.projectFilesMatchingFileQuery(t,e),s=t.findFilesMatchingSearchRequest(e,n,o).then(this.processMatchingFilesForProject.bind(this,this.searchId,t,e,n));r.push(s)}Promise.all(r).then(this.processMatchingFiles.bind(this,this.searchId,a,this.searchFinishedCallback.bind(this,!0)))}projectFilesMatchingFileQuery(e,t,i){const o=[];for(const n of e.uiSourceCodes()){if(!n.contentType().isTextType())continue;if(u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(n))continue;const e=h.Persistence.PersistenceImpl.instance().binding(n);e&&e.network===n||(i&&!n.isDirty()||t.filePathMatchesFileQuery(n.fullDisplayName())&&o.push(n))}return o.sort(Ze.urlComparator),o}processMatchingFilesForProject(e,t,i,o,n){if(e!==this.searchId&&this.searchFinishedCallback)return void this.searchFinishedCallback(!1);let r=[...n.keys()];r.sort(Ze.urlComparator),r=l.ArrayUtilities.intersectOrdered(r,o,Ze.urlComparator);const s=this.projectFilesMatchingFileQuery(t,i,!0);r=l.ArrayUtilities.mergeOrdered(r,s,Ze.urlComparator);const a=[];for(const e of r){const t=u.DefaultScriptMapping.DefaultScriptMapping.scriptForUISourceCode(e);t&&!t.isAnonymousScript()||a.push(e)}a.sort(Ze.filesComparator),this.searchResultCandidates=l.ArrayUtilities.mergeOrdered(this.searchResultCandidates,a,Ze.filesComparator)}processMatchingFiles(e,t,i){if(e!==this.searchId&&this.searchFinishedCallback)return void this.searchFinishedCallback(!1);const o=this.searchResultCandidates;if(!o.length)return t.done(),void i();t.setTotalWork(o.length);let n=0;let r=0;for(let e=0;e<20&&e<o.length;++e)a.call(this);function s(e){e.isDirty()?c.call(this,e,e.workingCopy()):e.requestContent().then((t=>{c.call(this,e,t.content||"")}))}function a(){if(n>=o.length)return r?void 0:(t.done(),void i());++r;const e=o[n++];window.setTimeout(s.bind(this,e),0)}function c(e,i){t.incrementWorked(1);let o=[];const n=this.searchConfig,s=n.queries();if(null!==i){for(let e=0;e<s.length;++e){const t=g.TextUtils.performSearchInContent(i,s[e],!n.ignoreCase(),n.isRegex());o=l.ArrayUtilities.mergeOrdered(o,t,g.ContentProvider.SearchMatch.comparator)}n.queries().length||(o=[new g.ContentProvider.SearchMatch(0,new g.Text.Text(i).lineAt(0),0,0)])}if(o&&this.searchResultCallback){const t=new et(e,o);this.searchResultCallback(t)}--r,a.call(this)}}stopSearch(){++this.searchId}}class et{uiSourceCode;searchMatches;constructor(e,t){this.uiSourceCode=e,this.searchMatches=t}label(){return this.uiSourceCode.displayName()}description(){return this.uiSourceCode.fullDisplayName()}matchesCount(){return this.searchMatches.length}matchLineContent(e){return this.searchMatches[e].lineContent}matchRevealable(e){const{lineNumber:t,columnNumber:i,matchLength:o}=this.searchMatches[e],n=new g.TextRange.TextRange(t,i,t,i+o);return new f.UISourceCode.UILocationRange(this.uiSourceCode,n)}matchLabel(e){return String(this.searchMatches[e].lineNumber+1)}matchColumn(e){return this.searchMatches[e].columnNumber}matchLength(e){return this.searchMatches[e].matchLength}}var tt=Object.freeze({__proto__:null,SourcesSearchScope:Ze,FileBasedSearchResult:et});class it{query;constructor(e){this.query=e}}class ot extends L.SearchView.SearchView{constructor(){super("sources",new i.Throttler.Throttler(200))}createScope(){return new Ze}}var nt=Object.freeze({__proto__:null,SearchSources:it,SearchSourcesView:ot,ActionDelegate:class{handleAction(e,o){if("sources.search"===o){const e=t.InspectorView.InspectorView.instance().element.window().getSelection(),o=e?e.toString().replace(/\r?\n.*/,""):"";return i.Revealer.reveal(new it(o)),!0}return!1}},Revealer:class{async reveal({query:e},i){const o=t.ViewManager.ViewManager.instance();await o.showView("sources.search-sources-tab",!0,i);const n=o.materializedWidget("sources.search-sources-tab");n instanceof ot&&n.toggle(e)}}});const rt={searchInFolder:"Search in folder",searchInAllFiles:"Search in all files",noDomain:"(no domain)",authored:"Authored",authoredTooltip:"Contains original sources",deployed:"Deployed",deployedTooltip:"Contains final sources the browser sees",areYouSureYouWantToExcludeThis:"Are you sure you want to exclude this folder?",areYouSureYouWantToDeleteThis:"Are you sure you want to delete this file?",rename:"Rename…",makeACopy:"Make a copy…",delete:"Delete",remove:"Remove",areYouSureYouWantToDeleteFolder:"Are you sure you want to delete this folder and its contents?",actionCannotBeUndone:"This action cannot be undone.",openFolder:"Open folder",newFile:"New file",excludeFolder:"Exclude from workspace",removeFolderFromWorkspace:"Remove from workspace",areYouSureYouWantToRemoveThis:"Remove ‘{PH1}’ from Workspace?",workspaceStopSyncing:"This will stop syncing changes from DevTools to your sources.",sFromSourceMap:"{PH1} (from source map)",sIgnoreListed:"{PH1} (ignore listed)"},st=e.i18n.registerUIStrings("panels/sources/NavigatorView.ts",rt),at=e.i18n.getLocalizedString.bind(void 0,st),ct={Authored:"authored",Deployed:"deployed",Domain:"domain",File:"file",FileSystem:"fs",FileSystemFolder:"fs-folder",Frame:"frame",NetworkFolder:"nw-folder",Root:"root",Worker:"worker"},lt=new Map([[ct.Root,1],[ct.Authored,1],[ct.Deployed,5],[ct.Domain,10],[ct.FileSystemFolder,1],[ct.NetworkFolder,1],[ct.File,10],[ct.Frame,70],[ct.Worker,90],[ct.FileSystem,100]]);class dt extends t.Widget.VBox{placeholder;scriptsTree;uiSourceCodeNodes;subfolderNodes;rootNode;frameNodes;authoredNode;deployedNode;navigatorGroupByFolderSetting;navigatorGroupByAuthoredExperiment;workspaceInternal;groupByFrame;groupByAuthored;groupByDomain;groupByFolder;constructor(e,n){super(!0),this.placeholder=null,this.scriptsTree=new t.TreeOutline.TreeOutlineInShadow,this.scriptsTree.setComparator(dt.treeElementsCompare),this.scriptsTree.setFocusable(!1),this.contentElement.setAttribute("jslog",`${a.pane(e).track({resize:!0})}`),this.contentElement.appendChild(this.scriptsTree.element),this.setDefaultFocusedElement(this.scriptsTree.element),this.uiSourceCodeNodes=new l.MapUtilities.Multimap,this.subfolderNodes=new Map,this.rootNode=new mt(this),this.rootNode.populate(),this.frameNodes=new Map,this.contentElement.addEventListener("contextmenu",this.handleContextMenu.bind(this),!1),t.ShortcutRegistry.ShortcutRegistry.instance().addShortcutListener(this.contentElement,{"sources.rename":this.renameShortcut.bind(this)}),this.navigatorGroupByFolderSetting=i.Settings.Settings.instance().moduleSetting("navigator-group-by-folder"),this.navigatorGroupByFolderSetting.addChangeListener(this.groupingChanged.bind(this)),n&&(this.navigatorGroupByAuthoredExperiment="authored-deployed-grouping"),u.IgnoreListManager.IgnoreListManager.instance().addChangeListener(this.ignoreListChanged.bind(this)),this.initGrouping(),h.Persistence.PersistenceImpl.instance().addEventListener(h.Persistence.Events.BindingCreated,this.onBindingChanged,this),h.Persistence.PersistenceImpl.instance().addEventListener(h.Persistence.Events.BindingRemoved,this.onBindingChanged,this),h.NetworkPersistenceManager.NetworkPersistenceManager.instance().addEventListener("RequestsForHeaderOverridesFileChanged",this.#d,this),o.TargetManager.TargetManager.instance().addEventListener("NameChanged",this.targetNameChanged,this),o.TargetManager.TargetManager.instance().observeTargets(this),this.resetWorkspace(f.Workspace.WorkspaceImpl.instance()),this.workspaceInternal.uiSourceCodes().forEach(this.addUISourceCode.bind(this)),u.NetworkProject.NetworkProjectManager.instance().addEventListener("FrameAttributionAdded",this.frameAttributionAdded,this),u.NetworkProject.NetworkProjectManager.instance().addEventListener("FrameAttributionRemoved",this.frameAttributionRemoved,this)}static treeElementOrder(e){if(ut.has(e))return 0;const t=e;let i=lt.get(t.nodeType)||0;if(t.uiSourceCode){const e=t.uiSourceCode.contentType();e.isDocument()?i+=3:e.isScript()?i+=5:e.isStyleSheet()?i+=10:i+=15}return i}static appendSearchItem(e,t){const o=at(t?rt.searchInFolder:rt.searchInAllFiles),n=new it(t&&`file:${t}`);e.viewSection().appendItem(o,(()=>i.Revealer.reveal(n)),{jslogContext:t?"search-in-folder":"search-in-all-files"})}static treeElementsCompare(e,t){const i=dt.treeElementOrder(e),o=dt.treeElementOrder(t);return i>o?1:i<o?-1:l.StringUtilities.naturalOrderComparator(e.titleAsText(),t.titleAsText())}setPlaceholder(e){function i(){const t=this.scriptsTree.firstChild();t?e.hideWidget():e.showWidget(),this.scriptsTree.element.classList.toggle("hidden",!t)}console.assert(!this.placeholder,"A placeholder widget was already set"),this.placeholder=e,e.show(this.contentElement,this.contentElement.firstChild),i.call(this),this.scriptsTree.addEventListener(t.TreeOutline.Events.ElementAttached,i.bind(this)),this.scriptsTree.addEventListener(t.TreeOutline.Events.ElementsDetached,i.bind(this))}onBindingChanged(e){const t=e.data;let o=!1;const n=this.uiSourceCodeNodes.get(t.network);for(const e of n)e.updateTitle(),o||=e.uiSourceCode().contentType().isFromSourceMap();const r=this.uiSourceCodeNodes.get(t.fileSystem);for(const e of r)e.updateTitle(),o||=e.uiSourceCode().contentType().isFromSourceMap();const s=h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.relativePath(t.fileSystem);let a=l.DevToolsPath.EmptyEncodedPathString;for(let e=0;e<s.length-1;++e){a=i.ParsedURL.ParsedURL.concatenate(a,s[e]);const n=this.folderNodeId(t.fileSystem.project(),null,null,t.fileSystem.origin(),o,a),r=this.subfolderNodes.get(n);r&&r.updateTitle(),a=i.ParsedURL.ParsedURL.concatenate(a,"/")}const c=this.rootOrDeployedNode().child(t.fileSystem.project().id());c&&c.updateTitle()}#d(e){const t=e.data,i=this.uiSourceCodeNodes.get(t);for(const e of i)e.updateTitle()}focus(){this.scriptsTree.focus()}appendChild(e,t){this.scriptsTree.setFocusable(!0),e.appendChild(t)}removeChild(e,t){e.removeChild(t),0===this.scriptsTree.rootElement().childCount()&&this.scriptsTree.setFocusable(!1)}resetWorkspace(e){this.workspaceInternal&&(this.workspaceInternal.removeEventListener(f.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAddedCallback,this),this.workspaceInternal.removeEventListener(f.Workspace.Events.UISourceCodeRemoved,this.uiSourceCodeRemovedCallback,this),this.workspaceInternal.removeEventListener(f.Workspace.Events.ProjectAdded,this.projectAddedCallback,this),this.workspaceInternal.removeEventListener(f.Workspace.Events.ProjectRemoved,this.projectRemovedCallback,this)),this.workspaceInternal=e,this.workspaceInternal.addEventListener(f.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAddedCallback,this),this.workspaceInternal.addEventListener(f.Workspace.Events.UISourceCodeRemoved,this.uiSourceCodeRemovedCallback,this),this.workspaceInternal.addEventListener(f.Workspace.Events.ProjectAdded,this.projectAddedCallback,this),this.workspaceInternal.addEventListener(f.Workspace.Events.ProjectRemoved,this.projectRemovedCallback,this),this.workspaceInternal.projects().forEach(this.projectAdded.bind(this)),this.computeUniqueFileSystemProjectNames()}projectAddedCallback(e){const t=e.data;this.projectAdded(t),t.type()===f.Workspace.projectTypes.FileSystem&&this.computeUniqueFileSystemProjectNames()}projectRemovedCallback(e){const t=e.data;this.removeProject(t),t.type()===f.Workspace.projectTypes.FileSystem&&this.computeUniqueFileSystemProjectNames()}workspace(){return this.workspaceInternal}acceptProject(e){return!e.isServiceProject()}frameAttributionAdded(e){const{uiSourceCode:t}=e.data;if(!this.acceptsUISourceCode(t))return;const i=e.data.frame;this.addUISourceCodeNode(t,i)}frameAttributionRemoved(e){const{uiSourceCode:t}=e.data;if(!this.acceptsUISourceCode(t))return;const i=e.data.frame,o=Array.from(this.uiSourceCodeNodes.get(t)).find((e=>e.frame()===i));o&&this.removeUISourceCodeNode(o)}acceptsUISourceCode(e){return this.acceptProject(e.project())}addUISourceCode(e){if(k.Runtime.experiments.isEnabled("just-my-code")&&u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(e))return;if(!this.acceptsUISourceCode(e))return;if(e.isFetchXHR())return;const t=u.NetworkProject.NetworkProject.framesForUISourceCode(e);if(t.length)for(const i of t)this.addUISourceCodeNode(e,i);else this.addUISourceCodeNode(e,null);this.uiSourceCodeAdded(e)}addUISourceCodeNode(e,t){const o=e.contentType().isFromSourceMap();let n;n=e.project().type()===f.Workspace.projectTypes.FileSystem?h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.relativePath(e).slice(0,-1):i.ParsedURL.ParsedURL.extractPath(e.url()).split("/").slice(1,-1);const r=e.project(),s=u.NetworkProject.NetworkProject.targetForUISourceCode(e),a=this.folderNode(e,r,s,t,e.origin(),n,o),c=new bt(this,e,t),l=a.child(c.id);l&&l instanceof bt?this.uiSourceCodeNodes.set(e,l):(a.appendChild(c),this.uiSourceCodeNodes.set(e,c),c.updateTitleBubbleUp()),this.selectDefaultTreeNode()}uiSourceCodeAdded(e){}uiSourceCodeAddedCallback(e){const t=e.data;this.addUISourceCode(t)}uiSourceCodeRemovedCallback(e){this.removeUISourceCodes([e.data])}tryAddProject(e){this.projectAdded(e);for(const t of e.uiSourceCodes())this.addUISourceCode(t)}projectAdded(e){const t=this.rootOrDeployedNode();!this.acceptProject(e)||e.type()!==f.Workspace.projectTypes.FileSystem||E.ScriptSnippetFileSystem.isSnippetsProject(e)||t.child(e.id())||(t.appendChild(new St(this,e,e.id(),ct.FileSystem,e.displayName())),this.selectDefaultTreeNode())}selectDefaultTreeNode(){const e=this.rootNode.children();e.length&&!this.scriptsTree.selectedTreeElement&&e[0].treeNode().select(!0,!1)}computeUniqueFileSystemProjectNames(){const e=this.workspaceInternal.projectsForType(f.Workspace.projectTypes.FileSystem);if(!e.length)return;const t=i.Trie.Trie.newArrayTrie(),o=[];for(const i of e){const e=i.fileSystemPath().split("/").reverse();o.push(e),t.add(e)}const n=this.rootOrDeployedNode();for(let r=0;r<e.length;++r){const s=o[r],a=e[r];t.remove(s);const c=t.longestPrefix(s,!1);t.add(s);const l=s.slice(0,c.length+1),d=i.ParsedURL.ParsedURL.encodedPathToRawPathString(l.reverse().join("/")),u=n.child(a.id());u&&u.setTitle(d)}}removeProject(e){if(this.removeUISourceCodes(e.uiSourceCodes()),e.type()!==f.Workspace.projectTypes.FileSystem)return;const t=this.rootNode.child(e.id());t&&this.rootNode.removeChild(t)}folderNodeId(e,t,i,o,n,r){const s=e.type()===f.Workspace.projectTypes.FileSystem?e.id():"";let a=!t||this.groupByAuthored&&n?"":t.id(),c=this.groupByFrame&&i?i.id:"";return this.groupByAuthored&&(n?(a="Authored",c=""):a="Deployed:"+a),a+":"+s+":"+c+":"+o+":"+r}folderNode(e,t,o,n,r,s,a){if(E.ScriptSnippetFileSystem.isSnippetsUISourceCode(e))return this.rootNode;if(o&&!this.groupByFolder&&!a)return this.domainNode(e,t,o,n,r);const c=i.ParsedURL.ParsedURL.join(s,"/"),l=this.folderNodeId(t,o,n,r,a,c);let d=this.subfolderNodes.get(l);if(d)return d;if(!s.length)return o?this.domainNode(e,t,o,n,r):this.rootOrDeployedNode().child(t.id());const u=this.folderNode(e,t,o,n,r,s.slice(0,-1),a);let h=ct.NetworkFolder;t.type()===f.Workspace.projectTypes.FileSystem&&(h=ct.FileSystemFolder);const p=i.ParsedURL.ParsedURL.encodedPathToRawPathString(s[s.length-1]);return d=new ft(this,t,l,h,c,p,r),this.subfolderNodes.set(l,d),u.appendChild(d),d}domainNode(e,t,o,n,r){const s=e.contentType().isFromSourceMap(),a=this.frameNode(t,o,n,s);if(!this.groupByDomain)return a;let c=a.child(r);return c||(c=new St(this,t,r,ct.Domain,this.computeProjectDisplayName(o,r)),n&&r===i.ParsedURL.ParsedURL.extractOrigin(n.url)&&ut.add(c.treeNode()),a.appendChild(c),s&&this.groupByAuthored&&c.treeNode().expand(),c)}frameNode(e,t,i,n){if(!this.groupByFrame||!i||this.groupByAuthored&&n)return this.targetNode(e,t,n);let r=this.frameNodes.get(i);if(r)return r;r=new St(this,e,t.id()+":"+i.id,ct.Frame,i.displayName()),r.setHoverCallback((function(e){if(e){const e=t.model(o.OverlayModel.OverlayModel);e&&i&&e.highlightFrame(i.id)}else o.OverlayModel.OverlayModel.hideDOMNodeHighlight()})),this.frameNodes.set(i,r);const s=i.parentFrame();return this.frameNode(e,s?s.resourceTreeModel().target():t,s,n).appendChild(r),s||(ut.add(r.treeNode()),r.treeNode().expand()),r}targetNode(e,t,i){if(this.groupByAuthored&&i)return this.authoredNode||(this.authoredNode=new St(this,null,"group:Authored",ct.Authored,at(rt.authored),at(rt.authoredTooltip)),this.rootNode.appendChild(this.authoredNode),this.authoredNode.treeNode().expand()),this.authoredNode;const n=this.rootOrDeployedNode();if(t===o.TargetManager.TargetManager.instance().scopeTarget())return n;let r=n.child("target:"+t.id());return r||(r=new St(this,e,"target:"+t.id(),t.type()===o.Target.Type.Frame?ct.Frame:ct.Worker,t.name()),n.appendChild(r)),r}rootOrDeployedNode(){return this.groupByAuthored?(this.deployedNode||(this.deployedNode=new St(this,null,"group:Deployed",ct.Deployed,at(rt.deployed),at(rt.deployedTooltip)),this.rootNode.appendChild(this.deployedNode)),this.deployedNode):this.rootNode}computeProjectDisplayName(e,t){const n=e.model(o.RuntimeModel.RuntimeModel),r=n?n.executionContexts():[];for(const e of r)if(e.name&&e.origin&&t.startsWith(e.origin))return e.name;if(!t)return at(rt.noDomain);const s=new i.ParsedURL.ParsedURL(t);return(s.isValid?s.host+(s.port?":"+s.port:""):"")||t}revealUISourceCode(e,i){const o=this.uiSourceCodeNodes.get(e);if(0===o.size)return null;const n=o.values().next().value;if(!n)return null;if(this.scriptsTree.selectedTreeElement){if(t.UIUtils.isBeingEdited(this.scriptsTree.selectedTreeElement.treeOutline?.element))return null;this.scriptsTree.selectedTreeElement.deselect()}return n.reveal(i),n}sourceSelected(e,t){i.Revealer.reveal(e,!t)}#u(e){const t=this.scriptsTree.selectedTreeElement,i=t&&t.node;let o=e;for(;o;){if(o===i)return!0;if(o=o.parent,!(e instanceof St||e instanceof ht))break}return!1}removeUISourceCodes(e){const t=[];for(const i of e){const e=this.uiSourceCodeNodes.get(i);for(const i of e)this.#u(i)?t.push(i):this.removeUISourceCodeNode(i)}t.forEach(this.removeUISourceCodeNode.bind(this))}removeUISourceCodeNode(e){const t=e.uiSourceCode();this.uiSourceCodeNodes.delete(t,e);const i=t.project(),o=u.NetworkProject.NetworkProject.targetForUISourceCode(t);let n=e.frame(),r=e.parent;if(!r)return;r.removeChild(e);let s=r;for(;s&&(r=s.parent,r)&&(r!==this.rootNode&&r!==this.deployedNode||i.type()!==f.Workspace.projectTypes.FileSystem)&&(s instanceof St||s instanceof ft);){if(!s.isEmpty()){s.updateTitleBubbleUp();break}if(s.type===ct.Frame)this.discardFrame(n,Boolean(this.groupByAuthored)&&t.contentType().isFromSourceMap()),n=n.parentFrame();else{const e=this.folderNodeId(i,o,n,t.origin(),t.contentType().isFromSourceMap(),s instanceof ft&&s.folderPath||l.DevToolsPath.EmptyEncodedPathString);this.subfolderNodes.delete(e),r.removeChild(s)}s===this.authoredNode?this.authoredNode=void 0:s===this.deployedNode&&(this.deployedNode=void 0),s=r}}reset(e){for(const e of this.uiSourceCodeNodes.valuesArray())e.dispose();this.scriptsTree.removeChildren(),this.scriptsTree.setFocusable(!1),this.uiSourceCodeNodes.clear(),this.subfolderNodes.clear(),this.frameNodes.clear(),this.rootNode.reset(),this.authoredNode=void 0,this.deployedNode=void 0,e||this.resetWorkspace(f.Workspace.WorkspaceImpl.instance())}handleContextMenu(e){}async renameShortcut(){const e=this.scriptsTree.selectedTreeElement,t=e&&e.node;return!!(t&&t.uiSourceCode()&&t.uiSourceCode().canRename())&&(this.rename(t,!1),!0)}handleContextMenuCreate(e,t,o){if(o){const e=h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.relativePath(o);e.pop(),t=i.ParsedURL.ParsedURL.join(e,"/")}this.create(e,t,o)}handleContextMenuRename(e){this.rename(e,!1)}async handleContextMenuExclude(e,i){await t.UIUtils.ConfirmDialog.show(at(rt.areYouSureYouWantToExcludeThis),void 0,{jslogContext:"exclude-folder-confirmation"})&&(t.UIUtils.startBatchUpdate(),e.excludeFolder(h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.completeURL(e,i)),t.UIUtils.endBatchUpdate())}async handleContextMenuDelete(e){await t.UIUtils.ConfirmDialog.show(at(rt.areYouSureYouWantToDeleteThis),void 0,{jslogContext:"delete-file-confirmation"})&&e.project().deleteFile(e)}handleFileContextMenu(e,i){const o=i.uiSourceCode(),n=new t.ContextMenu.ContextMenu(e);n.appendApplicableItems(o);const r=o.project();r.type()===f.Workspace.projectTypes.FileSystem&&(n.editSection().appendItem(at(rt.rename),this.handleContextMenuRename.bind(this,i),{jslogContext:"rename"}),n.editSection().appendItem(at(rt.makeACopy),this.handleContextMenuCreate.bind(this,r,l.DevToolsPath.EmptyEncodedPathString,o),{jslogContext:"make-a-copy"}),n.editSection().appendItem(at(rt.delete),this.handleContextMenuDelete.bind(this,o),{jslogContext:"delete"})),n.show()}async handleDeleteFolder(e){const i=`${at(rt.areYouSureYouWantToDeleteFolder)}\n${at(rt.actionCannotBeUndone)}`;if(await t.UIUtils.ConfirmDialog.show(i,void 0,{jslogContext:"delete-folder-confirmation"})){c.userMetrics.actionTaken(c.UserMetrics.Action.OverrideTabDeleteFolderContextMenu);const t=this.findTopNonMergedNode(e);await this.removeUISourceCodeFromProject(t),await this.deleteDirectoryRecursively(t)}}async removeUISourceCodeFromProject(e){e.children().forEach((async e=>{await this.removeUISourceCodeFromProject(e)})),e instanceof bt&&e.uiSourceCode().project().removeUISourceCode(e.uiSourceCode().url())}async deleteDirectoryRecursively(e){e instanceof ft&&await(h.NetworkPersistenceManager.NetworkPersistenceManager.instance().project()?.deleteDirectoryRecursively(e.folderPath))}findTopNonMergedNode(e){return e.isMerged&&e.parent instanceof ft?this.findTopNonMergedNode(e.parent):e}handleFolderContextMenu(e,o){const n=o.folderPath||l.DevToolsPath.EmptyEncodedPathString,r=o.project||null,s=new t.ContextMenu.ContextMenu(e);if(dt.appendSearchItem(s,n),r){if(r.type()===f.Workspace.projectTypes.FileSystem){const e=i.ParsedURL.ParsedURL.urlToRawPathString(h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.completeURL(r,n),c.Platform.isWin());s.revealSection().appendItem(at(rt.openFolder),(()=>c.InspectorFrontendHost.InspectorFrontendHostInstance.showItemInFolder(e)),{jslogContext:"open-folder"}),r.canCreateFile()&&s.defaultSection().appendItem(at(rt.newFile),(()=>{this.handleContextMenuCreate(r,n,void 0)}),{jslogContext:"new-file"})}else if(o.origin&&o.folderPath){const e=i.ParsedURL.ParsedURL.concatenate(o.origin,"/",o.folderPath),t={isContentScript:o.recursiveProperties.exclusivelyContentScripts||!1,isKnownThirdParty:o.recursiveProperties.exclusivelyThirdParty||!1,isCurrentlyIgnoreListed:o.recursiveProperties.exclusivelyIgnored||!1};for(const{text:i,callback:o,jslogContext:n}of u.IgnoreListManager.IgnoreListManager.instance().getIgnoreListFolderContextMenuItems(e,t))s.defaultSection().appendItem(i,o,{jslogContext:n})}if(r.canExcludeFolder(n)&&s.defaultSection().appendItem(at(rt.excludeFolder),this.handleContextMenuExclude.bind(this,r,n),{jslogContext:"exclude-folder"}),r.type()===f.Workspace.projectTypes.FileSystem){"overrides"===r.fileSystem().type()?o instanceof St||s.defaultSection().appendItem(at(rt.delete),this.handleDeleteFolder.bind(this,o),{jslogContext:"delete"}):o instanceof St&&s.defaultSection().appendItem(at(rt.removeFolderFromWorkspace),(async()=>{const e=`${at(rt.areYouSureYouWantToRemoveThis,{PH1:o.title})}\n${at(rt.workspaceStopSyncing)}`;await t.UIUtils.ConfirmDialog.show(e,void 0,{okButtonLabel:at(rt.remove),jslogContext:"remove-folder-from-workspace-confirmation"})&&r.remove()}),{jslogContext:"remove-folder-from-workspace"})}s.show()}}rename(e,t){const i=e.uiSourceCode();e.rename(function(o){if(!t)return;o?e.treeElement&&e.treeElement.listItemElement.hasFocus()&&this.sourceSelected(i,!0):i.remove()}.bind(this))}async create(e,t,i){let o="";i&&(o=(await i.requestContent()).content||"");const n=await e.createFile(t,null,o);if(!n)return;this.sourceSelected(n,!1);const r=this.revealUISourceCode(n,!0);r&&this.rename(r,!0)}groupingChanged(){this.reset(!0),this.initGrouping(),this.resetWorkspace(f.Workspace.WorkspaceImpl.instance()),this.workspaceInternal.uiSourceCodes().forEach(this.addUISourceCode.bind(this))}ignoreListChanged(){k.Runtime.experiments.isEnabled("just-my-code")?this.groupingChanged():this.rootNode.updateTitleRecursive()}initGrouping(){this.groupByFrame=!0,this.groupByDomain=this.navigatorGroupByFolderSetting.get(),this.groupByFolder=this.groupByDomain,this.navigatorGroupByAuthoredExperiment?this.groupByAuthored=k.Runtime.experiments.isEnabled(this.navigatorGroupByAuthoredExperiment):this.groupByAuthored=!1}resetForTest(){this.reset(),this.workspaceInternal.uiSourceCodes().forEach(this.addUISourceCode.bind(this))}discardFrame(e,t){if(t)return;const i=this.frameNodes.get(e);if(i){i.parent&&i.parent.removeChild(i),this.frameNodes.delete(e);for(const i of e.childFrames)this.discardFrame(i,t)}}targetAdded(e){}targetRemoved(e){const t=this.rootOrDeployedNode(),i=t.child("target:"+e.id());i&&t.removeChild(i)}targetNameChanged(e){const t=e.data,i=this.rootOrDeployedNode().child("target:"+t.id());i&&i.setTitle(t.name())}wasShown(){super.wasShown(),this.scriptsTree.registerCSSFiles([Ye]),this.registerCSSFiles([Qe])}}const ut=new WeakSet;class ht extends t.TreeOutline.TreeElement{nodeType;navigatorView;hoverCallback;node;hovered;isIgnoreListed;isFromSourceMap;constructor(e,i,o,n){super("",!0,ht.#h(i)),this.listItemElement.classList.add("navigator-"+i+"-tree-item","navigator-folder-tree-item"),t.ARIAUtils.setLabel(this.listItemElement,`${o}, ${i}`),this.nodeType=i,this.title=o,this.tooltip=o,this.navigatorView=e,this.hoverCallback=n,this.isFromSourceMap=!1;let s="folder";i===ct.Domain?s="cloud":i===ct.Frame?s="frame":i===ct.Worker?s="gears":i===ct.Authored?s="code":i===ct.Deployed&&(s="deployed");const a=r.Icon.create(s);this.setLeadingIcons([a])}async onpopulate(){this.node.populate()}onattach(){this.collapse(),this.node.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1),this.listItemElement.addEventListener("mousemove",this.mouseMove.bind(this),!1),this.listItemElement.addEventListener("mouseleave",this.mouseLeave.bind(this),!1)}setIgnoreListed(e){this.isIgnoreListed!==e&&(this.isIgnoreListed=e,this.listItemElement.classList.toggle("is-ignore-listed",e),this.updateTooltip())}setFromSourceMap(e){this.isFromSourceMap=e,this.listItemElement.classList.toggle("is-from-source-map",e)}setNode(e){this.node=e,this.updateTooltip(),t.ARIAUtils.setLabel(this.listItemElement,`${this.title}, ${this.nodeType}`)}updateTooltip(){if(this.node.tooltip)this.tooltip=this.node.tooltip;else{const e=[];let t=this.node;for(;t&&!t.isRoot()&&t.type===this.node.type;)e.push(t.title),t=t.parent;e.reverse();let i=e.join("/");this.isIgnoreListed&&(i=at(rt.sIgnoreListed,{PH1:i})),this.tooltip=i}}handleContextMenuEvent(e){this.node&&(this.select(),this.navigatorView.handleFolderContextMenu(e,this.node))}mouseMove(e){!this.hovered&&this.hoverCallback&&(this.hovered=!0,this.hoverCallback(!0))}mouseLeave(e){this.hoverCallback&&(this.hovered=!1,this.hoverCallback(!1))}static#h(e){switch(e){case ct.Domain:return"domain";case ct.Frame:return"frame";case ct.Worker:return"worker";case ct.Authored:return"authored";case ct.Deployed:return"deployed"}return"folder"}}class pt extends t.TreeOutline.TreeElement{nodeType;node;navigatorView;uiSourceCodeInternal;constructor(e,o,n,r){super("",!1,o.contentType().name()),this.nodeType=ct.File,this.node=r,this.title=n,this.listItemElement.classList.add("navigator-"+o.contentType().name()+"-tree-item","navigator-file-tree-item"),this.tooltip=o.url(),t.ARIAUtils.setLabel(this.listItemElement,`${o.name()}, ${this.nodeType}`),i.EventTarget.fireEvent("source-tree-file-added",o.fullDisplayName()),this.navigatorView=e,this.uiSourceCodeInternal=o,this.updateIcon(),this.titleElement.setAttribute("jslog",`${a.value("title").track({change:!0})}`)}updateIcon(){const e=h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal),i=h.NetworkPersistenceManager.NetworkPersistenceManager.instance();let o="document",n=[];if(e){E.ScriptSnippetFileSystem.isSnippetsUISourceCode(e.fileSystem)&&(o="snippet");n=i.project()===e.fileSystem.project()?["dot","purple"]:["dot","green"]}else i.isActiveHeaderOverrides(this.uiSourceCode)?n=["dot","purple"]:E.ScriptSnippetFileSystem.isSnippetsUISourceCode(this.uiSourceCodeInternal)&&(o="snippet");const s=r.Icon.create(o,n.join(" "));e&&t.Tooltip.Tooltip.install(s,h.PersistenceUtils.PersistenceUtils.tooltipForUISourceCode(this.uiSourceCodeInternal)),this.setLeadingIcons([s])}updateAccessibleName(){t.ARIAUtils.setLabel(this.listItemElement,`${this.uiSourceCodeInternal.name()}, ${this.nodeType}`)}get uiSourceCode(){return this.uiSourceCodeInternal}onattach(){this.listItemElement.draggable=!0,this.listItemElement.addEventListener("click",this.onclick.bind(this),!1),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1),this.listItemElement.addEventListener("dragstart",this.ondragstart.bind(this),!1)}shouldRenameOnMouseDown(){if(!this.uiSourceCodeInternal.canRename())return!1;if(!this.treeOutline)return!1;return this===this.treeOutline.selectedTreeElement&&this.treeOutline.element.hasFocus()&&!t.UIUtils.isBeingEdited(this.treeOutline.element)}selectOnMouseDown(e){1===e.which&&this.shouldRenameOnMouseDown()?window.setTimeout(function(){this.shouldRenameOnMouseDown()&&this.navigatorView.rename(this.node,!1)}.bind(this),300):super.selectOnMouseDown(e)}ondragstart(e){e.dataTransfer&&(e.dataTransfer.setData("text/plain",this.uiSourceCodeInternal.url()),e.dataTransfer.effectAllowed="copy")}onspace(){return this.navigatorView.sourceSelected(this.uiSourceCode,!0),!0}onclick(e){this.navigatorView.sourceSelected(this.uiSourceCode,!1)}ondblclick(e){const t=1===e.button;return this.navigatorView.sourceSelected(this.uiSourceCode,!t),!1}onenter(){return this.navigatorView.sourceSelected(this.uiSourceCode,!0),!0}ondelete(){return!0}handleContextMenuEvent(e){this.select(),this.navigatorView.handleFileContextMenu(e,this.node)}}class gt{id;navigatorView;type;childrenInternal;populated;isMerged;parent;title;tooltip;recursiveProperties;constructor(e,t,i,o){this.id=t,this.navigatorView=e,this.type=i,this.childrenInternal=new Map,this.tooltip=o,this.populated=!1,this.isMerged=!1,this.recursiveProperties={exclusivelySourceMapped:null,exclusivelyIgnored:null,exclusivelyContentScripts:null,exclusivelyThirdParty:null}}treeNode(){throw"Not implemented"}dispose(){}updateTitle(){}updateTitleRecursive(){for(const e of this.children())e.updateTitleRecursive();this.updateTitle()}updateTitleBubbleUp(){this.updateTitle(),this.parent&&this.parent.updateTitleBubbleUp()}isRoot(){return!1}hasChildren(){return!0}onattach(){}setTitle(e){throw"Not implemented"}populate(){this.isPopulated()||(this.parent&&this.parent.populate(),this.populated=!0,this.wasPopulated())}wasPopulated(){const e=this.children();for(let t=0;t<e.length;++t)this.navigatorView.appendChild(this.treeNode(),e[t].treeNode())}didAddChild(e){this.isPopulated()&&this.navigatorView.appendChild(this.treeNode(),e.treeNode())}willRemoveChild(e){this.isPopulated()&&this.navigatorView.removeChild(this.treeNode(),e.treeNode())}isPopulated(){return this.populated}isEmpty(){return!this.childrenInternal.size}children(){return[...this.childrenInternal.values()]}child(e){return this.childrenInternal.get(e)||null}appendChild(e){this.childrenInternal.set(e.id,e),e.parent=this,this.didAddChild(e)}removeChild(e){this.willRemoveChild(e),this.childrenInternal.delete(e.id),e.parent=null,e.dispose()}reset(){this.childrenInternal.clear()}}class mt extends gt{constructor(e){super(e,"",ct.Root)}isRoot(){return!0}treeNode(){return this.navigatorView.scriptsTree.rootElement()}}class bt extends gt{uiSourceCodeInternal;treeElement;eventListeners;frameInternal;constructor(e,t,i){super(e,"UISourceCode:"+t.canononicalScriptId(),ct.File),this.uiSourceCodeInternal=t,this.treeElement=null,this.eventListeners=[],this.frameInternal=i,this.recursiveProperties.exclusivelySourceMapped=t.contentType().isFromSourceMap(),t.contentType().isScript()&&(this.recursiveProperties.exclusivelyThirdParty=t.isKnownThirdParty(),this.recursiveProperties.exclusivelyContentScripts=t.project().type()===f.Workspace.projectTypes.ContentScripts)}frame(){return this.frameInternal}uiSourceCode(){return this.uiSourceCodeInternal}treeNode(){if(this.treeElement)return this.treeElement;this.treeElement=new pt(this.navigatorView,this.uiSourceCodeInternal,"",this),this.updateTitle();const e=this.updateTitle.bind(this,void 0);return this.eventListeners=[this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.TitleChanged,e),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.WorkingCopyChanged,e),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.WorkingCopyCommitted,e)],this.treeElement}updateTitle(e){const t=u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(this.uiSourceCodeInternal);if((this.uiSourceCodeInternal.contentType().isScript()||t)&&(this.recursiveProperties.exclusivelyIgnored=t),!this.treeElement)return;let i=this.uiSourceCodeInternal.displayName();!e&&this.uiSourceCodeInternal.isDirty()&&(i="*"+i),this.treeElement.title=i,this.treeElement.updateIcon(),this.treeElement.listItemElement.classList.toggle("is-ignore-listed",t);let o=this.uiSourceCodeInternal.url();this.uiSourceCodeInternal.contentType().isFromSourceMap()&&(o=at(rt.sFromSourceMap,{PH1:this.uiSourceCodeInternal.displayName()})),t&&(o=at(rt.sIgnoreListed,{PH1:o})),this.treeElement.tooltip=o,this.treeElement.updateAccessibleName(),this.parent?.childrenInternal.delete(this.id),this.id="UISourceCode:"+this.uiSourceCodeInternal.canononicalScriptId(),this.parent?.childrenInternal.set(this.id,this)}hasChildren(){return!1}dispose(){i.EventTarget.removeEventListeners(this.eventListeners)}reveal(e){this.parent&&(this.parent.populate(),this.parent.treeNode().expand()),this.treeElement&&(this.treeElement.reveal(!0),e&&this.treeElement.select(!0))}rename(e){if(!this.treeElement)return;if(this.treeElement.listItemElement.focus(),!this.treeElement.treeOutline)return;const i=this.treeElement.treeOutline.element;function o(o){if(!o)return t.UIUtils.markBeingEdited(i,!1),this.updateTitle(),void this.rename(e);if(this.treeElement){const{parent:e}=this.treeElement;e&&(e.removeChild(this.treeElement),e.appendChild(this.treeElement),this.treeElement.select())}n.call(this,!0)}function n(o){t.UIUtils.markBeingEdited(i,!1),this.updateTitle(),e&&e(o)}t.UIUtils.markBeingEdited(i,!0),this.updateTitle(!0),this.treeElement.startEditingTitle(new t.InplaceEditor.Config(function(e,t,i){if(t!==i)return this.treeElement&&(this.treeElement.title=t),void this.uiSourceCodeInternal.rename(t).then(o.bind(this));n.call(this,!0)}.bind(this),n.bind(this,!1)))}}class ft extends gt{project;folderPath;origin;title;treeElement;constructor(e,t,i,o,n,r,s){super(e,i,o),this.project=t,this.folderPath=n,this.title=r,this.origin=s}treeNode(){return this.treeElement||(this.treeElement=this.createTreeElement(this.title,this),this.updateTitle()),this.treeElement}updateTitle(){let e;for(e in this.recursiveProperties){let t=null;for(const i of this.children()){if(!1===i.recursiveProperties[e]){t=!1;break}i.recursiveProperties[e]&&(t=!0)}this.recursiveProperties[e]=t}if(!this.treeElement)return;if(this.treeElement.setFromSourceMap(this.recursiveProperties.exclusivelySourceMapped||!1),this.treeElement.setIgnoreListed(this.recursiveProperties.exclusivelyIgnored||!1),!this.project||this.project.type()!==f.Workspace.projectTypes.FileSystem)return;const t=i.ParsedURL.ParsedURL.concatenate(h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemPath(this.project.id()),"/",this.folderPath),o=h.Persistence.PersistenceImpl.instance().filePathHasBindings(t);this.treeElement.listItemElement.classList.toggle("has-mapped-files",o)}createTreeElement(e,t){const i=new ht(this.navigatorView,this.type,e);return i.setNode(t),i}wasPopulated(){this.treeElement&&this.treeElement.node===this&&this.addChildrenRecursive()}addChildrenRecursive(){const e=this.children();for(let t=0;t<e.length;++t){const i=e[t];this.didAddChild(i),i instanceof ft&&i.addChildrenRecursive()}}shouldMerge(e){return this.type!==ct.Domain&&e instanceof ft}didAddChild(e){if(!this.treeElement)return;let t,i=this.children();if(1===i.length&&this.shouldMerge(e))return e.isMerged=!0,this.treeElement.title=this.treeElement.title+"/"+e.title,e.treeElement=this.treeElement,e.updateTitle(),void this.treeElement.setNode(e);if(2===i.length&&(t=i[0]!==e?i[0]:i[1]),t&&t.isMerged){t.isMerged=!1;const e=[];e.push(this);let o=this;for(;o&&o.isMerged;)o=o.parent,o&&e.push(o);e.reverse();const n=e.map((e=>e.title)).join("/"),r=[];o=t;do{r.push(o),i=o.children(),o=1===i.length?i[0]:null}while(o&&o.isMerged);if(!this.isPopulated()){this.treeElement.title=n,this.treeElement.setNode(this);for(let e=0;e<r.length;++e)r[e].treeElement=null,r[e].isMerged=!1;return void this.updateTitle()}const s=this.treeElement,a=this.createTreeElement(n,this);for(let t=0;t<e.length;++t)e[t].treeElement=a,e[t].updateTitle();s.parent&&this.navigatorView.appendChild(s.parent,a),s.setNode(r[r.length-1]),s.title=r.map((e=>e.title)).join("/"),s.parent&&this.navigatorView.removeChild(s.parent,s),this.navigatorView.appendChild(this.treeElement,s),s.expanded&&a.expand(),this.updateTitle()}this.isPopulated()&&this.navigatorView.appendChild(this.treeElement,e.treeNode())}willRemoveChild(e){const t=e;!t.isMerged&&this.isPopulated()&&this.treeElement&&t.treeElement&&this.navigatorView.removeChild(this.treeElement,t.treeElement)}}class St extends gt{project;title;hoverCallback;treeElement;constructor(e,t,i,o,n,r){super(e,i,o,r),this.project=t,this.title=n,this.populate()}setHoverCallback(e){this.hoverCallback=e}treeNode(){return this.treeElement||(this.treeElement=new ht(this.navigatorView,this.type,this.title,this.hoverCallback),this.treeElement.setNode(this)),this.treeElement}onattach(){this.updateTitle()}updateTitle(){if(!this.treeElement||!this.project||this.project.type()!==f.Workspace.projectTypes.FileSystem)return;const e=h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemPath(this.project.id()),t=this.treeElement.listItemElement.classList.contains("has-mapped-files"),i=h.Persistence.PersistenceImpl.instance().filePathHasBindings(e);t!==i&&(this.treeElement.listItemElement.classList.toggle("has-mapped-files",i),this.treeElement.childrenListElement.hasFocus()||(i?this.treeElement.expand():this.treeElement.collapse()))}setTitle(e){this.title=e,this.treeElement&&(this.treeElement.title=this.title)}}var vt=Object.freeze({__proto__:null,Types:ct,NavigatorView:dt,NavigatorFolderTreeElement:ht,NavigatorSourceTreeElement:pt,NavigatorTreeNode:gt,NavigatorRootTreeNode:mt,NavigatorUISourceCodeTreeNode:bt,NavigatorFolderTreeNode:ft,NavigatorGroupTreeNode:St});const Ct=new CSSStyleSheet;Ct.replaceSync('.paused-message{align-self:center;width:fit-content}.scripts-debug-toolbar{position:absolute;top:0;width:100%;background-color:var(--app-color-toolbar-background);border-bottom:1px solid var(--sys-color-divider);overflow:hidden;z-index:1}.scripts-debug-toolbar-drawer{flex:0 0 52px;transition:margin-top 0.1s ease-in-out;margin-top:-26px;padding-top:25px;background-color:var(--sys-color-cdt-base-container);overflow:hidden;white-space:nowrap}.scripts-debug-toolbar-drawer.expanded{margin-top:0}.scripts-debug-toolbar-drawer > [is="dt-checkbox"]{display:none;padding-left:3px;height:28px}.scripts-debug-toolbar-drawer.expanded > [is="dt-checkbox"]{display:flex}.cursor-auto{cursor:auto}.navigator-tabbed-pane{background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=sourcesPanel.css */\n');class wt{sourcesView;entries=[];current=-1;revealing=!1;constructor(e){this.sourcesView=e}trackSourceFrameCursorJumps(e){e.addEventListener("EditorUpdate",(t=>this.onEditorUpdate(t.data,e)))}onEditorUpdate(e,t){e.docChanged&&this.mapEntriesFor(t.uiSourceCode(),e.changes);const i=e.startState.selection.main,o=e.state.selection.main;!this.revealing&&i.anchor!==o.anchor&&e.transactions.some((e=>Boolean(e.isUserEvent("select.pointer")||e.isUserEvent("select.reveal")||e.isUserEvent("select.search"))))&&(this.updateCurrentState(t.uiSourceCode(),i.head),this.entries.length>this.current+1&&(this.entries.length=this.current+1),this.entries.push(new It(t.uiSourceCode(),o.head)),this.current++,this.entries.length>20&&(this.entries.shift(),this.current--))}updateCurrentState(e,t){if(!this.revealing){const i=this.current>=0?this.entries[this.current]:null;i?.matches(e)&&(i.position=t)}}mapEntriesFor(e,t){for(const i of this.entries)i.matches(e)&&(i.position=t.mapPos(i.position))}reveal(e){const t=f.Workspace.WorkspaceImpl.instance().uiSourceCode(e.projectId,e.url);t&&(this.revealing=!0,this.sourcesView.showSourceLocation(t,e.position,!1,!0),this.revealing=!1)}rollback(){this.current>0&&(this.current--,this.reveal(this.entries[this.current]))}rollover(){this.current<this.entries.length-1&&(this.current++,this.reveal(this.entries[this.current]))}removeHistoryForSourceCode(e){for(let t=this.entries.length-1;t>=0;t--)this.entries[t].matches(e)&&(this.entries.splice(t,1),this.current>=t&&this.current--)}}class It{projectId;url;position;constructor(e,t){this.projectId=e.project().id(),this.url=e.url(),this.position=t}matches(e){return this.url===e.url()&&this.projectId===e.project().id()}}var yt=Object.freeze({__proto__:null,HistoryDepth:20,EditingLocationHistoryManager:wt});const xt=new CSSStyleSheet;xt.replaceSync("#sources-panel-sources-view{--override-highlight-animation-10pc-background-color:rgb(158 54 153);--override-highlight-animation-10pc-foreground-color:rgb(255 255 255);flex:auto;position:relative}#sources-panel-sources-view .sources-toolbar{display:flex;flex:0 0 27px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);overflow:hidden;z-index:0}.sources-toolbar .toolbar{cursor:default}.source-frame-debugger-script{--override-debugger-background-tint:rgb(255 255 194/50%);background-color:var(--override-debugger-background-tint)}.theme-with-dark-background .source-frame-debugger-script{--override-debugger-background-tint:rgb(61 61 0/50%)}\n/*# sourceURL=sourcesView.css */\n");const kt=["application/javascript","application/json","application/manifest+json","text/css","text/html","text/javascript"],Tt={ms:"ms",mb:"MB",kb:"kB"},Et=e.i18n.registerUIStrings("panels/sources/ProfilePlugin.ts",Tt),Lt=e.i18n.getLocalizedString.bind(void 0,Et);class Mt extends n.GutterMarker{value;constructor(e){super(),this.value=e}eq(e){return this.value===e.value}toDOM(){const e=document.createElement("div");e.className="cm-profileMarker";let t=this.value;const i=l.NumberUtilities.clamp(Math.log10(1+.002*t)/5,.02,1);let o,n;e.style.backgroundColor=`hsla(217, 100%, 70%, ${i.toFixed(3)})`,t/=1e3,t>=1e3?(o=Lt(Tt.mb),t/=1e3,n=t>=20?0:1):(o=Lt(Tt.kb),n=0),e.textContent=t.toFixed(n);const r=e.appendChild(document.createElement("span"));return r.className="cm-units",r.textContent=o,e}}class Pt extends n.GutterMarker{value;constructor(e){super(),this.value=e}eq(e){return this.value===e.value}toDOM(){const e=document.createElement("div");e.className="cm-profileMarker";const t=l.NumberUtilities.clamp(Math.log10(1+10*this.value)/5,.02,1);e.textContent=this.value.toFixed(1),e.style.backgroundColor=`hsla(44, 100%, 50%, ${t.toFixed(3)})`;const i=document.createElement("span");return i.className="cm-units",i.textContent=Lt(Tt.ms),e.appendChild(i),e}}function Ft(e,t,i){const o="performance"===i?Pt:Mt,r=[];for(const[i,n]of e)if(i<=t.doc.lines){const{from:e}=t.doc.line(i);r.push(new o(n).range(e))}return n.RangeSet.of(r,!0)}const Dt=e=>class extends he{updateEffect=n.StateEffect.define();field;gutter;compartment=new n.Compartment;constructor(t){super(t),this.field=n.StateField.define({create:()=>n.RangeSet.empty,update:(t,i)=>i.effects.reduce(((t,o)=>o.is(this.updateEffect)?Ft(o.value,i.state,e):t),t.map(i.changes))}),this.gutter=n.gutter({markers:e=>e.state.field(this.field),class:`cm-${e}Gutter`})}static accepts(e){return e.contentType().hasScripts()}getLineMap(){return this.uiSourceCode.getDecorationData(e)}editorExtension(){const t=this.getLineMap();return this.compartment.of(t?[this.field.init((i=>Ft(t,i,e))),this.gutter,Nt]:[])}decorationChanged(e,t){const i=Boolean(t.state.field(this.field,!1)),o=this.getLineMap();o?i?t.dispatch({effects:this.updateEffect.of(o)}):t.dispatch({effects:this.compartment.reconfigure([this.field.init((t=>Ft(o,t,e))),this.gutter,Nt])}):i&&t.dispatch({effects:this.compartment.reconfigure([])})}},Nt=n.EditorView.baseTheme({".cm-performanceGutter":{width:"60px",backgroundColor:"var(--sys-color-cdt-base-container)",marginLeft:"3px"},".cm-memoryGutter":{width:"48px",backgroundColor:"var(--sys-color-cdt-base-container)",marginLeft:"3px"},".cm-profileMarker":{textAlign:"right",paddingRight:"3px"},".cm-profileMarker .cm-units":{color:"var(--sys-color-token-subtle)",fontSize:"75%",marginLeft:"3px"}}),At=Dt("memory"),Ut=Dt("performance"),Bt={fromS:"(From {PH1})",sourceMappedFromS:"(Source mapped from {PH1})"},Rt=e.i18n.registerUIStrings("panels/sources/ResourceOriginPlugin.ts",Bt),jt=e.i18n.getLocalizedString.bind(void 0,Rt);class Vt extends he{static accepts(e){const t=e.contentType();return t.hasScripts()||t.isFromSourceMap()}rightToolbarItems(){const i=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();if(this.uiSourceCode.contentType().isFromSourceMap()){const o=[];for(const e of i.scriptsForUISourceCode(this.uiSourceCode)){const t=i.uiSourceCodeForScript(e);if(!t)continue;const n=t.url(),r=u.ResourceUtils.displayNameForURL(n),s=jt(Bt.sourceMappedFromS,{PH1:r});o.push(N.Linkifier.Linkifier.linkifyRevealable(t,r,n,s,void 0,"original-script-location"))}for(const e of u.SASSSourceMapping.SASSSourceMapping.uiSourceOrigin(this.uiSourceCode))o.push(N.Linkifier.Linkifier.linkifyURL(e));if(0===o.length)return[];const n=document.createElement("span");return o.forEach(((e,t)=>{t>0&&n.append(", "),n.append(e)})),[new t.Toolbar.ToolbarItem(e.i18n.getFormatLocalizedString(Rt,Bt.fromS,{PH1:n}))]}for(const o of i.scriptsForUISourceCode(this.uiSourceCode))if(o.originStackTrace){const i=Wt.linkifyStackTraceTopFrame(o.debuggerModel.target(),o.originStackTrace);return[new t.Toolbar.ToolbarItem(e.i18n.getFormatLocalizedString(Rt,Bt.fromS,{PH1:i}))]}return[]}}const Wt=new N.Linkifier.Linkifier;var Ot=Object.freeze({__proto__:null,ResourceOriginPlugin:Vt,linkifier:Wt});const Ht={enter:"⌘+Enter",ctrlenter:"Ctrl+Enter"},_t=e.i18n.registerUIStrings("panels/sources/SnippetsPlugin.ts",Ht),zt=e.i18n.getLocalizedString.bind(void 0,_t);class qt extends he{static accepts(e){return E.ScriptSnippetFileSystem.isSnippetsUISourceCode(e)}rightToolbarItems(){const e=t.Toolbar.Toolbar.createActionButtonForId("debugger.run-snippet");return e.setText(c.Platform.isMac()?zt(Ht.enter):zt(Ht.ctrlenter)),[e]}editorExtension(){return s.JavaScript.completion()}}var $t=Object.freeze({__proto__:null,SnippetsPlugin:qt});class Gt extends(i.ObjectWrapper.eventMixin(m.SourceFrame.SourceFrameImpl)){uiSourceCodeInternal;muteSourceCodeEvents;persistenceBinding;uiSourceCodeEventListeners;messageAndDecorationListeners;boundOnBindingChanged;plugins=[];errorPopoverHelper;openInExternalEditorToolbarButton;#p=!1;constructor(e){if(super((()=>this.workingCopy())),this.uiSourceCodeInternal=e,this.muteSourceCodeEvents=!1,this.persistenceBinding=h.Persistence.PersistenceImpl.instance().binding(e),this.uiSourceCodeEventListeners=[],this.messageAndDecorationListeners=[],this.canOpenInExternalEditor()){this.openInExternalEditorToolbarButton=new t.Toolbar.ToolbarButton("Open in editor",void 0,"Open in editor");const e=globalThis.reactNativeOpenInEditorButtonImage;if("string"==typeof e&&""!==e){const t=new F.Adorner.Adorner;t.classList.add("open-in-external-editor-adorner"),t.style.setProperty("background-image",e),this.openInExternalEditorToolbarButton.element.classList.add("toolbar-has-glyph","open-in-external-editor-button"),this.openInExternalEditorToolbarButton.setGlyphOrAdorner(t)}else this.openInExternalEditorToolbarButton.setGlyph("open-externally");this.openInExternalEditorToolbarButton.addEventListener("Click",(()=>{const e={url:this.uiSourceCode().url()},t=this.textEditor.state,i=t.doc.lineAt(t.selection.main.head),{lineNumber:o}=this.editorLocationToUILocation(i.number);e.lineNumber=o,fetch("/open-stack-frame",{method:"POST",body:JSON.stringify(e)}).catch((e=>console.error(e)))}))}this.boundOnBindingChanged=this.onBindingChanged.bind(this),i.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled").addChangeListener(this.onNetworkPersistenceChanged,this),this.errorPopoverHelper=new t.PopoverHelper.PopoverHelper(this.textEditor.editor.contentDOM,this.getErrorPopoverContent.bind(this),"sources.error"),this.errorPopoverHelper.setHasPadding(!0),this.errorPopoverHelper.setTimeout(100,100),this.initializeUISourceCode()}canOpenInExternalEditor(){return!!globalThis.enableReactNativeOpenInExternalEditor&&(this.uiSourceCode().url().startsWith("http")??!1)}async workingCopy(){return this.uiSourceCodeInternal.isDirty()?{content:this.uiSourceCodeInternal.workingCopy(),isEncoded:!1}:this.uiSourceCodeInternal.requestContent()}editorConfiguration(e){return[super.editorConfiguration(e),(t=this.allMessages(),[ni.init((e=>oi.create(Zt.create(t),e.doc))),si]),Xt.of(this.plugins.map((e=>e.editorExtension())))];var t}onFocus(){super.onFocus(),t.Context.Context.instance().setFlavor(Gt,this)}onBlur(){super.onBlur(),t.Context.Context.instance().setFlavor(Gt,null)}installMessageAndDecorationListeners(){if(this.persistenceBinding){const e=this.persistenceBinding.network,t=this.persistenceBinding.fileSystem;this.messageAndDecorationListeners=[e.addEventListener(f.UISourceCode.Events.MessageAdded,this.onMessageAdded,this),e.addEventListener(f.UISourceCode.Events.MessageRemoved,this.onMessageRemoved,this),e.addEventListener(f.UISourceCode.Events.DecorationChanged,this.onDecorationChanged,this),t.addEventListener(f.UISourceCode.Events.MessageAdded,this.onMessageAdded,this),t.addEventListener(f.UISourceCode.Events.MessageRemoved,this.onMessageRemoved,this)]}else this.messageAndDecorationListeners=[this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.MessageAdded,this.onMessageAdded,this),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.MessageRemoved,this.onMessageRemoved,this),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.DecorationChanged,this.onDecorationChanged,this)]}uiSourceCode(){return this.uiSourceCodeInternal}setUISourceCode(e){const t=e.contentLoaded()?Promise.resolve():e.requestContent(),i=this.uiSourceCodeInternal;t.then((async()=>{this.uiSourceCodeInternal===i&&(this.unloadUISourceCode(),this.uiSourceCodeInternal=e,e.workingCopy()!==this.textEditor.state.doc.toString()?await this.setDeferredContent(Promise.resolve(e.workingCopyContent())):this.reloadPlugins(),this.initializeUISourceCode())}),console.error)}unloadUISourceCode(){i.EventTarget.removeEventListeners(this.messageAndDecorationListeners),i.EventTarget.removeEventListeners(this.uiSourceCodeEventListeners),this.uiSourceCodeInternal.removeWorkingCopyGetter(),h.Persistence.PersistenceImpl.instance().unsubscribeFromBindingEvent(this.uiSourceCodeInternal,this.boundOnBindingChanged)}initializeUISourceCode(){this.uiSourceCodeEventListeners=[this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.WorkingCopyChanged,this.onWorkingCopyChanged,this),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.WorkingCopyCommitted,this.onWorkingCopyCommitted,this),this.uiSourceCodeInternal.addEventListener(f.UISourceCode.Events.TitleChanged,this.onTitleChanged,this)],h.Persistence.PersistenceImpl.instance().subscribeForBindingEvent(this.uiSourceCodeInternal,this.boundOnBindingChanged),this.installMessageAndDecorationListeners(),this.updateStyle();const e=kt.includes(this.contentType)&&!this.uiSourceCodeInternal.project().canSetFileContent()&&null===h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal),t=!this.uiSourceCodeInternal.contentType().isFromSourceMap();this.setCanPrettyPrint(e,t)}wasShown(){super.wasShown(),this.setEditable(this.canEditSourceInternal())}willHide(){for(const e of this.plugins)e.willHide();super.willHide(),t.Context.Context.instance().setFlavor(Gt,null),this.uiSourceCodeInternal.removeWorkingCopyGetter()}getContentType(){const e=h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal),t=e?e.network.mimeType():this.uiSourceCodeInternal.mimeType();return i.ResourceType.ResourceType.simplifyContentType(t)}canEditSourceInternal(){return!this.hasLoadError()&&(!this.uiSourceCodeInternal.editDisabled()&&("application/wasm"!==this.uiSourceCodeInternal.mimeType()&&(!!h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal)||(!!this.uiSourceCodeInternal.project().canSetFileContent()||!this.uiSourceCodeInternal.project().isServiceProject()&&(!(this.uiSourceCodeInternal.project().type()!==f.Workspace.projectTypes.Network||!h.NetworkPersistenceManager.NetworkPersistenceManager.instance().active())||(!this.pretty||!this.uiSourceCodeInternal.contentType().hasScripts())&&this.uiSourceCodeInternal.contentType()!==i.ResourceType.resourceTypes.Document)))))}onNetworkPersistenceChanged(){this.setEditable(this.canEditSourceInternal())}commitEditing(){this.uiSourceCodeInternal.isDirty()&&(this.muteSourceCodeEvents=!0,this.uiSourceCodeInternal.commitWorkingCopy(),this.muteSourceCodeEvents=!1)}async setContent(e){this.disposePlugins(),this.loadPlugins(),await super.setContent(e);for(const e of this.plugins)e.editorInitialized(this.textEditor);this.#g(),i.EventTarget.fireEvent("source-file-loaded",this.uiSourceCodeInternal.displayName(!0))}createMessage(e){const{lineNumber:t,columnNumber:i}=this.uiLocationToEditorLocation(e.lineNumber(),e.columnNumber());return new Yt(e,t,i)}allMessages(){return(null!==this.persistenceBinding?[...this.persistenceBinding.network.messages(),...this.persistenceBinding.fileSystem.messages()]:[...this.uiSourceCodeInternal.messages()]).map((e=>this.createMessage(e)))}onTextChanged(){const e=this.pretty;super.onTextChanged(),this.errorPopoverHelper.hidePopover(),Wi.instance().updateLastModificationTime(),this.muteSourceCodeEvents=!0,this.isClean()?this.uiSourceCodeInternal.resetWorkingCopy():this.uiSourceCodeInternal.setWorkingCopyGetter((()=>this.textEditor.state.sliceDoc())),this.muteSourceCodeEvents=!1,e!==this.pretty&&(this.updateStyle(),this.reloadPlugins())}onWorkingCopyChanged(){this.muteSourceCodeEvents||this.maybeSetContent(this.uiSourceCodeInternal.workingCopyContent())}onWorkingCopyCommitted(){this.muteSourceCodeEvents||this.maybeSetContent(this.uiSourceCode().workingCopyContent()),this.contentCommitted(),this.updateStyle()}reloadPlugins(){this.disposePlugins(),this.loadPlugins();const e=this.textEditor;e.dispatch({effects:Xt.reconfigure(this.plugins.map((e=>e.editorExtension())))});for(const t of this.plugins)t.editorInitialized(e)}onTitleChanged(){this.updateLanguageMode("").then((()=>this.reloadPlugins()),console.error)}loadPlugins(){const e=h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal),t=e?e.network:this.uiSourceCodeInternal;for(const e of[Oe,Qi,qt,Vt,fe,At,Ut])e.accepts(t)&&this.plugins.push(new e(t,this));this.dispatchEventToListeners("ToolbarItemsChanged")}disposePlugins(){for(const e of this.plugins)e.dispose();this.plugins=[]}onBindingChanged(){const e=h.Persistence.PersistenceImpl.instance().binding(this.uiSourceCodeInternal);e!==this.persistenceBinding&&(this.unloadUISourceCode(),this.persistenceBinding=e,this.initializeUISourceCode(),this.reloadMessages(),this.reloadPlugins())}reloadMessages(){const e=this.allMessages(),{editor:t}=this.textEditor;t.dispatch({effects:ei.of(Zt.create(e))})}updateStyle(){this.setEditable(this.canEditSourceInternal())}maybeSetContent(e){this.textEditor.state.doc.toString()!==e.content&&this.setDeferredContent(Promise.resolve(e))}populateTextAreaContextMenu(e,t,i){super.populateTextAreaContextMenu(e,t,i),e.appendApplicableItems(this.uiSourceCodeInternal);const o=this.editorLocationToUILocation(t,i);e.appendApplicableItems(new f.UISourceCode.UILocation(this.uiSourceCodeInternal,o.lineNumber,o.columnNumber));for(const o of this.plugins)o.populateTextAreaContextMenu(e,t,i)}populateLineGutterContextMenu(e,t){super.populateLineGutterContextMenu(e,t);for(const i of this.plugins)i.populateLineGutterContextMenu(e,t)}dispose(){this.errorPopoverHelper.dispose(),this.disposePlugins(),this.unloadUISourceCode(),this.textEditor.editor.destroy(),this.detach(),i.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled").removeChangeListener(this.onNetworkPersistenceChanged,this)}onMessageAdded(e){const{editor:t}=this.textEditor,i=t.state.field(ni,!1);if(i){const o=this.createMessage(e.data);t.dispatch({effects:ei.of(i.messages.add(o))})}}onMessageRemoved(e){const{editor:t}=this.textEditor,i=t.state.field(ni,!1);if(i){const o=this.createMessage(e.data);t.dispatch({effects:ei.of(i.messages.remove(o))})}}onDecorationChanged(e){for(const t of this.plugins)t.decorationChanged(e.data,this.textEditor)}async toolbarItems(){const e=await super.toolbarItems(),i=[];for(const t of this.plugins)e.push(...t.leftToolbarItems()),i.push(...t.rightToolbarItems());return i.length?(this.openInExternalEditorToolbarButton&&e.push(this.openInExternalEditorToolbarButton),[...e,new t.Toolbar.ToolbarSeparator(!0),...i]):e}getErrorPopoverContent(e){const t=e,i=e.target,o=i.enclosingNodeOrSelfWithClass("cm-messageIcon-error")||i.enclosingNodeOrSelfWithClass("cm-messageIcon-issue");if(!o)return null;const n=this.textEditor.state.field(ni,!1);if(!n||0===n.messages.rows.length)return null;const{editor:r}=this.textEditor,s=r.posAtCoords(t);if(null===s)return null;const a=r.state.doc.lineAt(s);if(s!==a.to)return null;const c=n.messages.rows.find((e=>e[0].lineNumber()===a.number-1));if(!c)return null;const l=o.classList.contains("cm-messageIcon-issue"),d=c.filter((e=>"Issue"===e.level()===l));if(!d.length)return null;const u=o?o.boxInWindow():new AnchorBox(t.clientX,t.clientY,1,1),h=function(e){const t=[];for(let i=0;i<e.length;i++){t[i]=0;for(let o=0;o<=i;o++)if(e[o].isEqual(e[i])){t[o]++;break}}return t}(d),p=document.createElement("div");p.classList.add("text-editor-messages-description-container");for(let e=0;e<d.length;e++)h[e]&&p.appendChild(ri(d[e],h[e]));return{box:u,hide(){},show:async e=>(e.contentElement.append(p),!0)}}#g(){if(this.#p)return;this.#p=!0;const e=i.ResourceType.ResourceType.mimeFromURL(this.uiSourceCodeInternal.url()),t=i.ResourceType.ResourceType.mediaTypeForMetrics(e??"",this.uiSourceCodeInternal.contentType().isFromSourceMap(),g.TextUtils.isMinified(this.uiSourceCodeInternal.content()));c.userMetrics.sourcesPanelFileOpened(t)}}function Kt(e){return"Error"===e?{color:"var(--icon-error)",width:"16px",height:"14px",iconName:"cross-circle-filled"}:"Warning"===e?{color:"var(--icon-warning)",width:"18px",height:"14px",iconName:"warning-filled"}:"Issue"===e?{color:"var(--icon-warning)",width:"17px",height:"14px",iconName:"issue-exclamation-filled"}:{color:"var(--icon-error)",width:"16px",height:"14px",iconName:"cross-circle-filled"}}function Jt(e,t){const i={Issue:2,Warning:3,Error:4};return i[e.level()]-i[t.level()]}const Xt=new n.Compartment;class Yt{origin;#m;#b;constructor(e,t,i){this.origin=e,this.#m=t,this.#b=i}level(){return this.origin.level()}text(){return this.origin.text()}clickHandler(){return this.origin.clickHandler()}lineNumber(){return this.#m}columnNumber(){return this.#b}isEqual(e){return this.origin.isEqual(e.origin)}}function Qt(e,t){const i=t.lineNumber();let o=0;for(;o<e.length;o++){const n=e[o][0].lineNumber()-i;if(0===n)return e[o]=e[o].concat(t),e;if(n>0)break}return e.splice(o,0,[t]),e}class Zt{rows;constructor(e){this.rows=e}static create(e){const t=[];for(const i of e)Qt(t,i);return new Zt(t)}remove(e){const t=this.rows.slice();return function(e,t){for(let i=0;i<e.length;i++)if(e[i][0].lineNumber()===t.lineNumber()){const o=e[i].filter((e=>!e.isEqual(t)));o.length?e[i]=o:e.splice(i,1);break}}(t,e),new Zt(t)}add(e){return new Zt(Qt(this.rows.slice(),e))}}const ei=n.StateEffect.define(),ti=n.Decoration.mark({class:"cm-waveUnderline"});class ii extends n.WidgetType{messages;constructor(e){super(),this.messages=e}eq(e){return e.messages===this.messages}toDOM(){const e=document.createElement("span");e.classList.add("cm-messageIcon");const t=this.messages.filter((e=>"Issue"!==e.level()));if(t.length){const i=t.sort(Jt)[t.length-1],o=e.appendChild(new r.Icon.Icon);o.data=Kt(i.level()),o.classList.add("cm-messageIcon-error")}const i=this.messages.find((e=>"Issue"===e.level()));if(i){const t=e.appendChild(new r.Icon.Icon);t.data=Kt("Issue"),t.classList.add("cm-messageIcon-issue"),t.addEventListener("click",(()=>(i.clickHandler()||Math.min)()))}return e}ignoreEvents(){return!0}}class oi{messages;decorations;constructor(e,t){this.messages=e,this.decorations=t}static create(e,t){const i=new n.RangeSetBuilder;for(const o of e.rows){const e=t.line(Math.min(t.lines,o[0].lineNumber()+1)),r=o.reduce(((e,t)=>Math.min(e,t.columnNumber()||0)),e.length);r<e.length&&i.add(e.from+r,e.to,ti),i.add(e.to,e.to,n.Decoration.widget({side:1,widget:new ii(o)}))}return new oi(e,i.finish())}apply(e){let t=this;e.docChanged&&(t=new oi(this.messages,this.decorations.map(e.changes)));for(const i of e.effects)i.is(ei)&&(t=oi.create(i.value,e.state.doc));return t}}const ni=n.StateField.define({create:e=>oi.create(new Zt([]),e.doc),update:(e,t)=>e.apply(t),provide:e=>n.Prec.lowest(n.EditorView.decorations.from(e,(e=>e.decorations)))});function ri(e,t){const i=document.createElement("div");if(i.classList.add("text-editor-row-message"),i.style.display="flex",i.style.alignItems="center",i.style.gap="4px",1===t){const t=i.appendChild(new r.Icon.Icon);t.data=function(e){return e.origin instanceof P.SourceFrameIssuesManager.IssueMessage?{...D.IssueCounter.getIssueKindIconData(e.origin.getIssueKind()),width:"12px",height:"12px"}:Kt(e.level())}(e),t.classList.add("text-editor-row-message-icon"),t.addEventListener("click",(()=>(e.clickHandler()||Math.min)()))}else{const o=document.createElement("span",{is:"dt-small-bubble"});o.textContent=String(t),o.classList.add("text-editor-row-message-repeat-count"),o.style.flexShrink="0",i.appendChild(o),o.type=function(e){switch(e){case"Error":return"error";case"Warning":case"Issue":return"warning"}}(e.level())}const o=i.createChild("div");for(const t of e.text().split("\n"))o.createChild("div").textContent=t;return i}const si=n.EditorView.baseTheme({".cm-tooltip-message":{padding:"4px"},".cm-waveUnderline":{backgroundImage:"var(--image-file-errorWave)",backgroundRepeat:"repeat-x",backgroundPosition:"bottom",paddingBottom:"1px"},".cm-messageIcon":{cursor:"pointer","& > *":{verticalAlign:"text-bottom",marginLeft:"2px"}},".cm-messageIcon-issue, .cm-messageIcon-error":{marginTop:"-1px",marginBottom:"-1px"}});var ai=Object.freeze({__proto__:null,UISourceCodeFrame:Gt});const ci={areYouSureYouWantToCloseUnsaved:"Are you sure you want to close unsaved file: {PH1}?",unableToLoadThisContent:"Unable to load this content.",changesToThisFileWereNotSavedTo:"Changes to this file were not saved to file system."},li=e.i18n.registerUIStrings("panels/sources/TabbedEditorContainer.ts",ci),di=e.i18n.getLocalizedString.bind(void 0,li);let ui=0;class hi extends i.ObjectWrapper.ObjectWrapper{delegate;tabbedPane;tabIds;files;previouslyViewedFilesSetting;history;uriToUISourceCode;idToUISourceCode;currentFileInternal;currentView;scrollTimer;reentrantShow;constructor(e,i,o,n){super(),this.delegate=e,this.tabbedPane=new t.TabbedPane.TabbedPane,this.tabbedPane.setPlaceholderElement(o,n),this.tabbedPane.setTabDelegate(new fi(this)),this.tabbedPane.setCloseableTabs(!0),this.tabbedPane.setAllowTabReorder(!0,!0),this.tabbedPane.addEventListener(t.TabbedPane.Events.TabClosed,this.tabClosed,this),this.tabbedPane.addEventListener(t.TabbedPane.Events.TabSelected,this.tabSelected,this),this.tabbedPane.headerElement().setAttribute("jslog",`${a.toolbar("top").track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`),h.Persistence.PersistenceImpl.instance().addEventListener(h.Persistence.Events.BindingCreated,this.onBindingCreated,this),h.Persistence.PersistenceImpl.instance().addEventListener(h.Persistence.Events.BindingRemoved,this.onBindingRemoved,this),h.NetworkPersistenceManager.NetworkPersistenceManager.instance().addEventListener("RequestsForHeaderOverridesFileChanged",this.#d,this),this.tabIds=new Map,this.files=new Map,this.previouslyViewedFilesSetting=i,this.history=bi.fromObject(this.previouslyViewedFilesSetting.get()),this.uriToUISourceCode=new Map,this.idToUISourceCode=new Map,this.reentrantShow=!1}onBindingCreated(e){const t=e.data;this.updateFileTitle(t.fileSystem);const i=this.tabIds.get(t.network);let o=this.tabIds.get(t.fileSystem);const n=this.currentFileInternal===t.network,r=gi(t.network),s=this.history.selectionRange(r),a=this.history.scrollLineNumber(r);if(this.history.remove(r),i){if(!o){const e=this.tabbedPane.tabView(i),n=this.tabbedPane.tabIndex(i);if(e instanceof Gt)this.delegate.recycleUISourceCodeFrame(e,t.fileSystem),o=this.appendFileTab(t.fileSystem,!1,n,e);else{o=this.appendFileTab(t.fileSystem,!1,n);const e=this.tabbedPane.tabView(o);this.restoreEditorProperties(e,s,a)}}this.closeTabs([i],!0),n&&this.tabbedPane.selectTab(o,!1),this.updateHistory()}}#d(e){this.updateFileTitle(e.data)}onBindingRemoved(e){const t=e.data;this.updateFileTitle(t.fileSystem)}get view(){return this.tabbedPane}get visibleView(){return this.tabbedPane.visibleView}fileViews(){return this.tabbedPane.tabViews()}leftToolbar(){return this.tabbedPane.leftToolbar()}rightToolbar(){return this.tabbedPane.rightToolbar()}show(e){this.tabbedPane.show(e)}showFile(e){const o=h.Persistence.PersistenceImpl.instance().binding(e);e=o?o.fileSystem:e;const n=t.Context.Context.instance().flavor(Ii);n?.currentSourceFrame()?.contentSet&&this.currentFileInternal===e&&n?.currentUISourceCode()===e?i.EventTarget.fireEvent("source-file-loaded",e.displayName(!0)):this.innerShowFile(e,!0)}closeFile(e){const t=this.tabIds.get(e);t&&this.closeTabs([t])}closeAllFiles(){this.closeTabs(this.tabbedPane.tabIds())}historyUISourceCodes(){const e=[];for(const{url:t,resourceType:i}of this.history.keys()){const o=this.uriToUISourceCode.get(t);void 0!==o&&o.contentType()===i&&e.push(o)}return e}selectNextTab(){this.tabbedPane.selectNextTab()}selectPrevTab(){this.tabbedPane.selectPrevTab()}addViewListeners(){this.currentView&&this.currentView instanceof m.SourceFrame.SourceFrameImpl&&(this.currentView.addEventListener("EditorUpdate",this.onEditorUpdate,this),this.currentView.addEventListener("EditorScroll",this.onScrollChanged,this))}removeViewListeners(){this.currentView&&this.currentView instanceof m.SourceFrame.SourceFrameImpl&&(this.currentView.removeEventListener("EditorUpdate",this.onEditorUpdate,this),this.currentView.removeEventListener("EditorScroll",this.onScrollChanged,this))}onScrollChanged(){if(this.currentView instanceof m.SourceFrame.SourceFrameImpl&&(this.scrollTimer&&clearTimeout(this.scrollTimer),this.scrollTimer=window.setTimeout((()=>this.previouslyViewedFilesSetting.set(this.history.toObject())),100),this.currentFileInternal)){const{editor:e}=this.currentView.textEditor,t=e.lineBlockAtHeight(e.scrollDOM.getBoundingClientRect().top-e.documentTop),i=e.state.doc.lineAt(t.from).number-1;this.history.updateScrollLineNumber(gi(this.currentFileInternal),i)}}onEditorUpdate({data:e}){if(e.docChanged||e.selectionSet){const{main:t}=e.state.selection,i=e.state.doc.lineAt(t.from),o=e.state.doc.lineAt(t.to),n=new g.TextRange.TextRange(i.number-1,t.from-i.from,o.number-1,t.to-o.from);this.currentFileInternal&&this.history.updateSelectionRange(gi(this.currentFileInternal),n),this.previouslyViewedFilesSetting.set(this.history.toObject()),this.currentFileInternal&&T.ExtensionServer.ExtensionServer.instance().sourceSelectionChanged(this.currentFileInternal.url(),n)}}innerShowFile(e,t){if(this.reentrantShow)return;const i=this.canonicalUISourceCode(e),o=h.Persistence.PersistenceImpl.instance().binding(e);if(e=o?o.fileSystem:e,this.currentFileInternal===e)return;this.removeViewListeners(),this.currentFileInternal=e;try{this.reentrantShow=!0;const e=this.tabIds.get(i)||this.appendFileTab(i,t);this.tabbedPane.selectTab(e,t)}finally{this.reentrantShow=!1}t&&this.editorSelectedByUserAction();const n=this.currentView;this.currentView=this.visibleView,this.addViewListeners(),this.currentView instanceof Gt&&this.currentView.uiSourceCode()!==e&&(this.delegate.recycleUISourceCodeFrame(this.currentView,e),e.project().type()!==f.Workspace.projectTypes.FileSystem&&e.disableEdit());const r={currentFile:this.currentFileInternal,currentView:this.currentView,previousView:n,userGesture:t};this.dispatchEventToListeners("EditorSelected",r)}titleForFile(e){let t=l.StringUtilities.trimMiddle(e.displayName(!0),30);return e.isDirty()&&(t+="*"),t}maybeCloseTab(e,t){const i=this.files.get(e);if(!i)return!1;return!(i.isDirty()&&i.project().canSetFileContent()&&!confirm(di(ci.areYouSureYouWantToCloseUnsaved,{PH1:i.name()})))&&(i.resetWorkingCopy(),t&&this.tabbedPane.selectTab(t,!0),this.tabbedPane.closeTab(e,!0),!0)}closeTabs(e,t){const i=[],o=[];for(let n=0;n<e.length;++n){const r=e[n],s=this.files.get(r);s&&(!t&&s.isDirty()?i.push(r):o.push(r))}i.length&&this.tabbedPane.selectTab(i[0],!0),this.tabbedPane.closeTabs(o,!0);for(let e=0;e<i.length;++e){const t=e+1<i.length?i[e+1]:null;if(!this.maybeCloseTab(i[e],t))break}}onContextMenu(e,t){const i=this.files.get(e);i&&t.appendApplicableItems(i)}canonicalUISourceCode(e){const t=this.idToUISourceCode.get(e.canononicalScriptId());return t||(this.idToUISourceCode.set(e.canononicalScriptId(),e),this.uriToUISourceCode.set(e.url(),e),e)}addUISourceCode(e){const t=this.canonicalUISourceCode(e),i=t!==e,o=h.Persistence.PersistenceImpl.instance().binding(t);if(e=o?o.fileSystem:t,i&&e.project().type()!==f.Workspace.projectTypes.FileSystem&&e.disableEdit(),this.currentFileInternal?.canononicalScriptId()===e.canononicalScriptId())return;const n=this.history.index(gi(e));if(-1===n)return;if(this.tabIds.has(e)||this.appendFileTab(e,!1),!n)return void this.innerShowFile(e,!1);if(!this.currentFileInternal)return;const r=E.ScriptSnippetFileSystem.isSnippetsUISourceCode(this.currentFileInternal),s=E.ScriptSnippetFileSystem.isSnippetsUISourceCode(e);this.history.index(gi(this.currentFileInternal))&&r&&!s&&this.innerShowFile(e,!1)}removeUISourceCode(e){this.removeUISourceCodes([e])}removeUISourceCodes(e){const t=[];for(const i of e){const e=this.tabIds.get(i);e&&t.push(e),this.uriToUISourceCode.get(i.url())===i&&this.uriToUISourceCode.delete(i.url()),this.idToUISourceCode.get(i.canononicalScriptId())===i&&this.idToUISourceCode.delete(i.canononicalScriptId())}this.tabbedPane.closeTabs(t)}editorClosedByUserAction(e){this.history.remove(gi(e)),this.updateHistory()}editorSelectedByUserAction(){this.updateHistory()}updateHistory(){const e=[];for(const t of this.tabbedPane.lastOpenedTabIds(pi)){const i=this.files.get(t);void 0!==i&&e.push(gi(i))}this.history.update(e),this.previouslyViewedFilesSetting.set(this.history.toObject())}tooltipForFile(e){return(e=h.Persistence.PersistenceImpl.instance().network(e)||e).url()}appendFileTab(e,t,i,o){const n=o||this.delegate.viewForFile(e),r=this.titleForFile(e),s=this.tooltipForFile(e),a=this.generateTabId();if(this.tabIds.set(e,a),this.files.set(a,e),!o){const t=this.history.selectionRange(gi(e)),i=this.history.scrollLineNumber(gi(e));this.restoreEditorProperties(n,t,i)}return this.tabbedPane.appendTab(a,r,n,s,t,void 0,void 0,i),this.updateFileTitle(e),this.addUISourceCodeListeners(e),e.loadError()?this.addLoadErrorIcon(a):e.contentLoaded()||e.requestContent().then((t=>{e.loadError()&&this.addLoadErrorIcon(a)})),a}addLoadErrorIcon(e){const i=new r.Icon.Icon;i.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},t.Tooltip.Tooltip.install(i,di(ci.unableToLoadThisContent)),this.tabbedPane.tabView(e)&&this.tabbedPane.setTabIcon(e,i)}restoreEditorProperties(e,t,i){const o=e instanceof m.SourceFrame.SourceFrameImpl?e:null;o&&(t&&o.setSelection(t),"number"==typeof i&&o.scrollToLine(i))}tabClosed(e){const{tabId:t,isUserGesture:i}=e.data,o=this.files.get(t);this.currentFileInternal&&this.currentFileInternal.canononicalScriptId()===o?.canononicalScriptId()&&(this.removeViewListeners(),this.currentView=null,this.currentFileInternal=null),o&&this.tabIds.delete(o),this.files.delete(t),o&&(this.removeUISourceCodeListeners(o),this.dispatchEventToListeners("EditorClosed",o),i&&this.editorClosedByUserAction(o))}tabSelected(e){const{tabId:t,isUserGesture:i}=e.data,o=this.files.get(t);o&&this.innerShowFile(o,i)}addUISourceCodeListeners(e){e.addEventListener(f.UISourceCode.Events.TitleChanged,this.uiSourceCodeTitleChanged,this),e.addEventListener(f.UISourceCode.Events.WorkingCopyChanged,this.uiSourceCodeWorkingCopyChanged,this),e.addEventListener(f.UISourceCode.Events.WorkingCopyCommitted,this.uiSourceCodeWorkingCopyCommitted,this)}removeUISourceCodeListeners(e){e.removeEventListener(f.UISourceCode.Events.TitleChanged,this.uiSourceCodeTitleChanged,this),e.removeEventListener(f.UISourceCode.Events.WorkingCopyChanged,this.uiSourceCodeWorkingCopyChanged,this),e.removeEventListener(f.UISourceCode.Events.WorkingCopyCommitted,this.uiSourceCodeWorkingCopyCommitted,this)}updateFileTitle(e){const i=this.tabIds.get(e);if(i){const o=this.titleForFile(e),n=this.tooltipForFile(e);this.tabbedPane.changeTabTitle(i,o,n);let s=null;e.loadError()?(s=new r.Icon.Icon,s.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},t.Tooltip.Tooltip.install(s,di(ci.unableToLoadThisContent))):h.Persistence.PersistenceImpl.instance().hasUnsavedCommittedChanges(e)?(s=new r.Icon.Icon,s.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},t.Tooltip.Tooltip.install(s,di(ci.changesToThisFileWereNotSavedTo))):s=h.PersistenceUtils.PersistenceUtils.iconForUISourceCode(e),this.tabbedPane.setTabIcon(i,s)}}uiSourceCodeTitleChanged(e){const t=e.data;this.updateFileTitle(t),this.updateHistory();for(const[e,i]of this.uriToUISourceCode)i===t&&e!==i.url()&&this.uriToUISourceCode.delete(e);for(const[e,i]of this.idToUISourceCode)i===t&&e!==i.canononicalScriptId()&&this.idToUISourceCode.delete(e);this.canonicalUISourceCode(t)}uiSourceCodeWorkingCopyChanged(e){const t=e.data;this.updateFileTitle(t)}uiSourceCodeWorkingCopyCommitted(e){const t=e.data.uiSourceCode;this.updateFileTitle(t)}generateTabId(){return"tab-"+ui++}currentFile(){return this.currentFileInternal||null}}const pi=30;function gi(e){return{url:e.url(),resourceType:e.contentType()}}class mi{url;resourceType;selectionRange;scrollLineNumber;constructor(e,t,i,o){this.url=e,this.resourceType=t,this.selectionRange=i,this.scrollLineNumber=o}static fromObject(e){const t=i.ResourceType.ResourceType.fromName(e.resourceTypeName);if(null===t)throw new TypeError(`Invalid resource type name "${e.resourceTypeName}"`);const o=e.selectionRange?g.TextRange.TextRange.fromObject(e.selectionRange):void 0;return new mi(e.url,t,o,e.scrollLineNumber)}toObject(){return this.url.length>=4096?null:{url:this.url,resourceTypeName:this.resourceType.name(),selectionRange:this.selectionRange,scrollLineNumber:this.scrollLineNumber}}}class bi{items;constructor(e){this.items=e}static fromObject(e){const t=[];for(const i of e)try{t.push(mi.fromObject(i))}catch{}return new bi(t)}index({url:e,resourceType:t}){return this.items.findIndex((i=>i.url===e&&i.resourceType===t))}selectionRange(e){const t=this.index(e);if(-1!==t)return this.items[t].selectionRange}updateSelectionRange(e,t){if(!t)return;const i=this.index(e);-1!==i&&(this.items[i].selectionRange=t)}scrollLineNumber(e){const t=this.index(e);if(-1!==t)return this.items[t].scrollLineNumber}updateScrollLineNumber(e,t){const i=this.index(e);-1!==i&&(this.items[i].scrollLineNumber=t)}update(e){for(let t=e.length-1;t>=0;--t){const i=this.index(e[t]);let o;-1!==i?(o=this.items[i],this.items.splice(i,1)):o=new mi(e[t].url,e[t].resourceType),this.items.unshift(o)}}remove(e){const t=this.index(e);-1!==t&&this.items.splice(t,1)}toObject(){const e=[];for(const t of this.items){const i=t.toObject();if(i&&e.push(i),e.length===pi)break}return e}keys(){return this.items}}class fi{editorContainer;constructor(e){this.editorContainer=e}closeTabs(e,t){this.editorContainer.closeTabs(t)}onContextMenu(e,t){this.editorContainer.onContextMenu(e,t)}}var Si=Object.freeze({__proto__:null,TabbedEditorContainer:hi,HistoryItem:mi,History:bi,EditorContainerTabDelegate:fi});const vi={openFile:"Open file",runCommand:"Run command",workspaceDropInAFolderToSyncSources:"To sync edits to the workspace, drop a folder with your sources here or",selectFolder:"Select folder",sourceViewActions:"Source View Actions"},Ci=e.i18n.registerUIStrings("panels/sources/SourcesView.ts",vi),wi=e.i18n.getLocalizedString.bind(void 0,Ci);class Ii extends(i.ObjectWrapper.eventMixin(t.Widget.VBox)){selectedIndex;searchableViewInternal;sourceViewByUISourceCode;editorContainer;historyManager;toolbarContainerElementInternal;scriptViewToolbar;bottomToolbarInternal;toolbarChangedListener;focusedPlaceholderElement;searchView;searchConfig;constructor(){super(),this.element.id="sources-panel-sources-view",this.element.setAttribute("jslog",`${a.pane("editor").track({keydown:"Escape"})}`),this.setMinimumAndPreferredSizes(88,52,150,100),this.selectedIndex=0;const e=f.Workspace.WorkspaceImpl.instance();this.searchableViewInternal=new t.SearchableView.SearchableView(this,this,"sources-view-search-config"),this.searchableViewInternal.setMinimalSearchQuerySize(0),this.searchableViewInternal.show(this.element),this.sourceViewByUISourceCode=new Map,this.editorContainer=new hi(this,i.Settings.Settings.instance().createLocalSetting("previously-viewed-files",[]),this.placeholderElement(),this.focusedPlaceholderElement),this.editorContainer.show(this.searchableViewInternal.element),this.editorContainer.addEventListener("EditorSelected",this.editorSelected,this),this.editorContainer.addEventListener("EditorClosed",this.editorClosed,this),this.historyManager=new wt(this),this.toolbarContainerElementInternal=this.element.createChild("div","sources-toolbar"),this.toolbarContainerElementInternal.setAttribute("jslog",`${a.toolbar("bottom")}`),this.scriptViewToolbar=new t.Toolbar.Toolbar("",this.toolbarContainerElementInternal),this.scriptViewToolbar.element.style.flex="auto",this.bottomToolbarInternal=new t.Toolbar.Toolbar("",this.toolbarContainerElementInternal),this.toolbarChangedListener=null,t.UIUtils.startBatchUpdate(),e.uiSourceCodes().forEach(this.addUISourceCode.bind(this)),t.UIUtils.endBatchUpdate(),e.addEventListener(f.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAdded,this),e.addEventListener(f.Workspace.Events.UISourceCodeRemoved,this.uiSourceCodeRemoved,this),e.addEventListener(f.Workspace.Events.ProjectRemoved,this.projectRemoved.bind(this),this),o.TargetManager.TargetManager.instance().addScopeChangeListener(this.#f.bind(this)),window.opener||window.addEventListener("beforeunload",(function(e){if(e.returnValue)return;const o=[],n=f.Workspace.WorkspaceImpl.instance().projectsForType(f.Workspace.projectTypes.FileSystem);for(const e of n)for(const t of e.uiSourceCodes())t.isDirty()&&o.push(t);if(o.length){e.returnValue=!0,t.ViewManager.ViewManager.instance().showView("sources");for(const e of o)i.Revealer.reveal(e)}}),!0)}placeholderElement(){const e=[{actionId:"quick-open.show",description:wi(vi.openFile)},{actionId:"quick-open.show-command-menu",description:wi(vi.runCommand)},{actionId:"sources.add-folder-to-workspace",condition:k.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER,description:wi(vi.workspaceDropInAFolderToSyncSources),isWorkspace:!0}],i=document.createElement("div");t.ARIAUtils.markAsList(i),t.ARIAUtils.setLabel(i,wi(vi.sourceViewActions));for(const o of e){const{condition:e}=o;if(void 0!==e&&!k.Runtime.Runtime.isDescriptorEnabled({experiment:void 0,condition:()=>Boolean(k.Runtime.Runtime.queryParam(e))}))continue;const n=t.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction(o.actionId),r=i.createChild("div","tabbed-pane-placeholder-row");if(t.ARIAUtils.markAsListitem(r),n){r.createChild("span").textContent=n;const e=r.createChild("button");e.textContent=o.description;const i=t.ActionRegistry.ActionRegistry.instance().getAction(o.actionId);e.addEventListener("click",(()=>i.execute()))}if(o.isWorkspace){const e=r.createChild("span","workspace");e.textContent=o.description;const t=e.createChild("button");t.textContent=wi(vi.selectFolder),t.addEventListener("click",this.addFileSystemClicked.bind(this))}}return k.Runtime.Runtime.isDescriptorEnabled({experiment:void 0,condition:k.Runtime.conditions.notSourcesHideAddFolder})&&i.appendChild(t.XLink.XLink.create("https://developer.chrome.com/docs/devtools/workspaces/","Learn more about Workspaces")),i}async addFileSystemClicked(){await h.IsolatedFileSystemManager.IsolatedFileSystemManager.instance().addFileSystem()&&(c.userMetrics.actionTaken(c.UserMetrics.Action.WorkspaceSelectFolder),t.ViewManager.ViewManager.instance().showView("navigator-files"))}static defaultUISourceCodeScores(){const e=new Map,i=t.Context.Context.instance().flavor(Ii);if(i){const t=i.editorContainer.historyUISourceCodes();for(let i=1;i<t.length;++i)e.set(t[i],t.length-i)}return e}leftToolbar(){return this.editorContainer.leftToolbar()}rightToolbar(){return this.editorContainer.rightToolbar()}bottomToolbar(){return this.bottomToolbarInternal}wasShown(){super.wasShown(),this.registerCSSFiles([xt]),t.Context.Context.instance().setFlavor(Ii,this)}willHide(){t.Context.Context.instance().setFlavor(Ii,null),super.willHide()}toolbarContainerElement(){return this.toolbarContainerElementInternal}searchableView(){return this.searchableViewInternal}visibleView(){return this.editorContainer.visibleView}currentSourceFrame(){const e=this.visibleView();return e instanceof Gt?e:null}currentUISourceCode(){return this.editorContainer.currentFile()}onCloseEditorTab(){const e=this.editorContainer.currentFile();return!!e&&(this.editorContainer.closeFile(e),!0)}onJumpToPreviousLocation(){this.historyManager.rollback()}onJumpToNextLocation(){this.historyManager.rollover()}#f(){const e=f.Workspace.WorkspaceImpl.instance();for(const t of e.uiSourceCodes()){if(t.project().type()!==f.Workspace.projectTypes.Network)continue;const e=u.NetworkProject.NetworkProject.targetForUISourceCode(t);o.TargetManager.TargetManager.instance().isInScope(e)?this.addUISourceCode(t):this.removeUISourceCodes([t])}}uiSourceCodeAdded(e){const t=e.data;this.addUISourceCode(t)}addUISourceCode(e){const t=e.project();if(!t.isServiceProject()){switch(t.type()){case f.Workspace.projectTypes.FileSystem:if("overrides"===h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemType(t))return;break;case f.Workspace.projectTypes.Network:{const t=u.NetworkProject.NetworkProject.targetForUISourceCode(e);if(!o.TargetManager.TargetManager.instance().isInScope(t))return}}this.editorContainer.addUISourceCode(e)}}uiSourceCodeRemoved(e){const t=e.data;this.removeUISourceCodes([t])}removeUISourceCodes(e){this.editorContainer.removeUISourceCodes(e);for(let t=0;t<e.length;++t)this.removeSourceFrame(e[t]),this.historyManager.removeHistoryForSourceCode(e[t])}projectRemoved(e){const t=e.data.uiSourceCodes();this.removeUISourceCodes([...t])}updateScriptViewToolbarItems(){const e=this.visibleView();e instanceof t.View.SimpleView&&e.toolbarItems().then((e=>{this.scriptViewToolbar.removeToolbarItems();for(const e of ki())this.scriptViewToolbar.appendToolbarItem(e.getOrCreateButton(this));e.map((e=>this.scriptViewToolbar.appendToolbarItem(e)))}))}showSourceLocation(e,t,i,o){const n=this.currentSourceFrame();n&&this.historyManager.updateCurrentState(n.uiSourceCode(),n.textEditor.state.selection.main.head),this.editorContainer.showFile(e);const r=this.currentSourceFrame();r&&t&&r.revealPosition(t,!o);const s=this.visibleView();!i&&s&&s.focus()}createSourceView(e){let t;const o=e.contentType();return o===i.ResourceType.resourceTypes.Image?t=new m.ImageView.ImageView(e.mimeType(),e):o===i.ResourceType.resourceTypes.Font?t=new m.FontView.FontView(e.mimeType(),e):e.name()===Ei?t=new x.HeadersView.HeadersView(e):(t=new Gt(e),this.historyManager.trackSourceFrameCursorJumps(t)),e.addEventListener(f.UISourceCode.Events.TitleChanged,this.#S,this),this.sourceViewByUISourceCode.set(e,t),t}#v(e){return e instanceof m.ImageView.ImageView?"ImageView":e instanceof m.FontView.FontView?"FontView":e instanceof x.HeadersView.HeadersView?"HeadersView":"SourceView"}#C(e){if(e.name()===Ei)return"HeadersView";switch(e.contentType()){case i.ResourceType.resourceTypes.Image:return"ImageView";case i.ResourceType.resourceTypes.Font:return"FontView";default:return"SourceView"}}#S(e){const t=e.data,i=this.sourceViewByUISourceCode.get(t);i&&this.#v(i)!==this.#C(t)&&(this.removeUISourceCodes([t]),this.showSourceLocation(t))}getSourceView(e){return this.sourceViewByUISourceCode.get(e)}getOrCreateSourceView(e){return this.sourceViewByUISourceCode.get(e)||this.createSourceView(e)}recycleUISourceCodeFrame(e,t){e.uiSourceCode().removeEventListener(f.UISourceCode.Events.TitleChanged,this.#S,this),this.sourceViewByUISourceCode.delete(e.uiSourceCode()),e.setUISourceCode(t),this.sourceViewByUISourceCode.set(t,e),t.addEventListener(f.UISourceCode.Events.TitleChanged,this.#S,this)}viewForFile(e){return this.getOrCreateSourceView(e)}removeSourceFrame(e){const t=this.sourceViewByUISourceCode.get(e);this.sourceViewByUISourceCode.delete(e),t&&t instanceof Gt&&t.dispose(),e.removeEventListener(f.UISourceCode.Events.TitleChanged,this.#S,this)}editorClosed(e){const t=e.data;this.historyManager.removeHistoryForSourceCode(t);let i=!1;this.editorContainer.currentFile()||(i=!0),this.removeToolbarChangedListener(),this.updateScriptViewToolbarItems(),this.searchableViewInternal.resetSearch();const o={uiSourceCode:t,wasSelected:i};this.dispatchEventToListeners("EditorClosed",o)}editorSelected(e){const t=e.data.previousView instanceof Gt?e.data.previousView:null;t&&t.setSearchableView(null);const i=e.data.currentView instanceof Gt?e.data.currentView:null;i&&i.setSearchableView(this.searchableViewInternal),this.searchableViewInternal.setReplaceable(Boolean(i?.canEditSource())),this.searchableViewInternal.refreshSearch(),this.updateToolbarChangedListener(),this.updateScriptViewToolbarItems();const o=this.editorContainer.currentFile();o&&this.dispatchEventToListeners("EditorSelected",o)}removeToolbarChangedListener(){this.toolbarChangedListener&&i.EventTarget.removeEventListeners([this.toolbarChangedListener]),this.toolbarChangedListener=null}updateToolbarChangedListener(){this.removeToolbarChangedListener();const e=this.currentSourceFrame();e&&(this.toolbarChangedListener=e.addEventListener("ToolbarItemsChanged",this.updateScriptViewToolbarItems,this))}onSearchCanceled(){this.searchView&&this.searchView.onSearchCanceled(),delete this.searchView,delete this.searchConfig}performSearch(e,t,i){const o=this.currentSourceFrame();o&&(this.searchView=o,this.searchConfig=e,this.searchView.performSearch(this.searchConfig,t,i))}jumpToNextSearchResult(){this.searchView&&(this.searchConfig&&this.searchView!==this.currentSourceFrame()?this.performSearch(this.searchConfig,!0):this.searchView.jumpToNextSearchResult())}jumpToPreviousSearchResult(){if(this.searchView)return this.searchConfig&&this.searchView!==this.currentSourceFrame()?(this.performSearch(this.searchConfig,!0),void(this.searchView&&this.searchView.jumpToLastSearchResult())):void this.searchView.jumpToPreviousSearchResult()}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}replaceSelectionWith(e,t){const i=this.currentSourceFrame();i?i.replaceSelectionWith(e,t):console.assert(Boolean(i))}replaceAllWith(e,t){const i=this.currentSourceFrame();i?i.replaceAllWith(e,t):console.assert(Boolean(i))}showOutlineQuickOpen(){M.QuickOpen.QuickOpenImpl.show("@")}showGoToLineQuickOpen(){this.editorContainer.currentFile()&&M.QuickOpen.QuickOpenImpl.show(":")}save(){this.saveSourceFrame(this.currentSourceFrame())}saveAll(){this.editorContainer.fileViews().forEach(this.saveSourceFrame.bind(this))}saveSourceFrame(e){if(!(e instanceof Gt))return;e.commitEditing()}toggleBreakpointsActiveState(e){this.editorContainer.view.element.classList.toggle("breakpoints-deactivated",!e)}}const yi=[];function xi(e){yi.push(e)}function ki(){return yi.map((e=>e()))}class Ti{static nextFile(e){function t(e){const t=e.lastIndexOf(".");return e.substr(0,-1!==t?t:e.length).toLowerCase()}const o=[],n=e.parentURL(),r=e.name(),s=t(r);for(const i of e.project().uiSourceCodes())n===i.parentURL()&&t(i.name())===s&&o.push(i.name());o.sort(l.StringUtilities.naturalOrderComparator);const a=l.NumberUtilities.mod(o.indexOf(r)+1,o.length),c=i.ParsedURL.ParsedURL.concatenate(n?i.ParsedURL.ParsedURL.concatenate(n,"/"):"",o[a]),d=e.project().uiSourceCodeForURL(c);return d!==e?d:null}handleAction(e,t){const i=e.flavor(Ii);if(!i)return!1;const o=i.currentUISourceCode();if(!o)return!1;const n=Ti.nextFile(o);return!!n&&(i.showSourceLocation(n),!0)}}const Ei=".headers";var Li=Object.freeze({__proto__:null,SourcesView:Ii,registerEditorAction:xi,getRegisteredEditorActions:ki,SwitchFileActionDelegate:Ti,ActionDelegate:class{handleAction(e,t){const i=e.flavor(Ii);if(!i)return!1;switch(t){case"sources.close-all":return i.editorContainer.closeAllFiles(),!0;case"sources.jump-to-previous-location":return i.onJumpToPreviousLocation(),!0;case"sources.jump-to-next-location":return i.onJumpToNextLocation(),!0;case"sources.next-editor-tab":return i.editorContainer.selectNextTab(),!0;case"sources.previous-editor-tab":return i.editorContainer.selectPrevTab(),!0;case"sources.close-editor-tab":return i.onCloseEditorTab();case"sources.go-to-line":return i.showGoToLineQuickOpen(),!0;case"sources.go-to-member":return i.showOutlineQuickOpen(),!0;case"sources.save":return i.save(),!0;case"sources.save-all":return i.saveAll(),!0}return!1}}});const Mi=new CSSStyleSheet;Mi.replaceSync(".thread-item{padding:3px 8px 3px 20px;position:relative;min-height:18px;line-height:15px;display:flex;flex-wrap:wrap}.thread-item + .thread-item{border-top:1px solid var(--sys-color-divider)}.thread-item:hover{background-color:var(--sys-color-state-hover-on-subtle)}.thread-item:focus-visible{background-color:var(--sys-color-tonal-container)}.thread-item-title,\n.thread-item-paused-state{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.thread-item-paused-state{color:var(--sys-color-state-disabled);margin-left:auto;padding:0 10px}.selected-thread-icon{display:none;position:absolute;top:3px;left:4px}.thread-item.selected .selected-thread-icon{display:block}@media (forced-colors: active){.thread-item:hover,\n  .thread-item:focus-visible{forced-color-adjust:none;background-color:Highlight}.thread-item:hover > div,\n  .thread-item:focus-visible > div{color:HighlightText}}\n/*# sourceURL=threadsSidebarPane.css */\n");const Pi={paused:"paused"},Fi=e.i18n.registerUIStrings("panels/sources/ThreadsSidebarPane.ts",Pi),Di=e.i18n.getLocalizedString.bind(void 0,Fi);class Ni extends t.Widget.VBox{items;list;selectedModel;constructor(){super(!0),this.contentElement.setAttribute("jslog",`${a.section("sources.threads")}`),this.items=new t.ListModel.ListModel,this.list=new t.ListControl.ListControl(this.items,this,t.ListControl.ListMode.NonViewport);const e=t.Context.Context.instance().flavor(o.Target.Target);this.selectedModel=null!==e?e.model(o.DebuggerModel.DebuggerModel):null,this.contentElement.appendChild(this.list.element),t.Context.Context.instance().addFlavorChangeListener(o.Target.Target,this.targetFlavorChanged,this),o.TargetManager.TargetManager.instance().observeModels(o.DebuggerModel.DebuggerModel,this)}static shouldBeShown(){return o.TargetManager.TargetManager.instance().models(o.DebuggerModel.DebuggerModel).length>=2}createElementForItem(e){const i=document.createElement("div");i.classList.add("thread-item");const n=i.createChild("div","thread-item-title"),s=i.createChild("div","thread-item-paused-state"),a=new r.Icon.Icon;a.data={iconName:"large-arrow-right-filled",color:"var(--icon-arrow-main-thread)",width:"14px",height:"14px"},a.classList.add("selected-thread-icon"),i.appendChild(a),i.tabIndex=-1,self.onInvokeElement(i,(i=>{t.Context.Context.instance().setFlavor(o.Target.Target,e.target()),i.consume(!0)}));const c=t.Context.Context.instance().flavor(o.Target.Target)===e.target();function l(){const t=e.runtimeModel().defaultExecutionContext();n.textContent=t&&t.label()?t.label():e.target().name()}function d(){s.textContent=e.isPaused()?Di(Pi.paused):""}return i.classList.toggle("selected",c),t.ARIAUtils.setSelected(i,c),e.addEventListener(o.DebuggerModel.Events.DebuggerPaused,d),e.addEventListener(o.DebuggerModel.Events.DebuggerResumed,d),e.runtimeModel().addEventListener(o.RuntimeModel.Events.ExecutionContextChanged,l),o.TargetManager.TargetManager.instance().addEventListener("NameChanged",(function(t){t.data===e.target()&&l()})),d(),l(),i}heightForItem(e){return console.assert(!1),0}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,o){i&&(i.tabIndex=-1);const n=o;n&&(this.setDefaultFocusedElement(n),n.tabIndex=0,this.hasFocus()&&n.focus())}updateSelectedItemARIA(e,t){return!1}modelAdded(e){this.items.insert(this.items.length,e);t.Context.Context.instance().flavor(o.Target.Target)===e.target()&&this.list.selectItem(e)}modelRemoved(e){this.items.remove(this.items.indexOf(e))}targetFlavorChanged({data:e}){const t=this.hasFocus(),i=e.model(o.DebuggerModel.DebuggerModel);this.list.selectItem(i),i&&this.list.refreshItem(i),null!==this.selectedModel&&this.list.refreshItem(this.selectedModel),this.selectedModel=i,t&&this.focus()}wasShown(){super.wasShown(),this.registerCSSFiles([Mi])}}var Ai=Object.freeze({__proto__:null,ThreadsSidebarPane:Ni});const Ui={dropWorkspaceFolderHere:"Drop workspace folder here",moreOptions:"More options",showNavigator:"Show navigator",hideNavigator:"Hide navigator",navigatorShown:"Navigator sidebar shown",navigatorHidden:"Navigator sidebar hidden",debuggerShown:"Debugger sidebar shown",debuggerHidden:"Debugger sidebar hidden",showDebugger:"Show debugger",hideDebugger:"Hide debugger",groupByFolder:"Group by folder",groupByAuthored:"Group by Authored/Deployed",hideIgnoreListed:"Hide ignore-listed sources",resumeWithAllPausesBlockedForMs:"Resume with all pauses blocked for 500 ms",terminateCurrentJavascriptCall:"Terminate current JavaScript call",pauseOnCaughtExceptions:"Pause on caught exceptions",revealInSidebar:"Reveal in navigator sidebar",continueToHere:"Continue to here",storeAsGlobalVariable:"Store as global variable",copyS:"Copy {PH1}",copyStringContents:"Copy string contents",copyStringAsJSLiteral:"Copy string as JavaScript literal",copyStringAsJSONLiteral:"Copy string as JSON literal",showFunctionDefinition:"Show function definition",openInSourcesPanel:"Open in Sources panel"},Bi=e.i18n.registerUIStrings("panels/sources/SourcesPanel.ts",Ui),Ri=e.i18n.getLocalizedString.bind(void 0,Bi),ji=new Set(["number","boolean","bigint","undefined"]);let Vi;class Wi extends t.Panel.Panel{workspace;togglePauseAction;stepOverAction;stepIntoAction;stepOutAction;stepAction;toggleBreakpointsActiveAction;debugToolbar;debugToolbarDrawer;debuggerPausedMessage;overlayLoggables;splitWidget;editorView;navigatorTabbedLocation;sourcesViewInternal;toggleNavigatorSidebarButton;toggleDebuggerSidebarButton;threadsSidebarPane;watchSidebarPane;callstackPane;liveLocationPool;lastModificationTime;pausedInternal;switchToPausedTargetTimeout;ignoreExecutionLineEvents;executionLineLocation;pauseOnExceptionButton;sidebarPaneStack;tabbedLocationHeader;extensionSidebarPanesContainer;sidebarPaneView;constructor(){super("sources"),k.Runtime.Runtime.isDescriptorEnabled({experiment:void 0,condition:k.Runtime.conditions.notSourcesHideAddFolder})&&new t.DropTarget.DropTarget(this.element,[t.DropTarget.Type.Folder],Ri(Ui.dropWorkspaceFolderHere),this.handleDrop.bind(this)),this.workspace=f.Workspace.WorkspaceImpl.instance(),this.togglePauseAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.toggle-pause"),this.stepOverAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.step-over"),this.stepIntoAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.step-into"),this.stepOutAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.step-out"),this.stepAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.step"),this.toggleBreakpointsActiveAction=t.ActionRegistry.ActionRegistry.instance().getAction("debugger.toggle-breakpoints-active"),this.debugToolbar=this.createDebugToolbar(),this.debugToolbarDrawer=this.createDebugToolbarDrawer(),this.debuggerPausedMessage=new Ke;this.splitWidget=new t.SplitWidget.SplitWidget(!0,!0,"sources-panel-split-view-state",225),this.splitWidget.enableShowModeSaving(),this.splitWidget.show(this.element);this.editorView=new t.SplitWidget.SplitWidget(!0,!1,"sources-panel-navigator-split-view-state",225),this.editorView.enableShowModeSaving(),this.splitWidget.setMainWidget(this.editorView),this.navigatorTabbedLocation=t.ViewManager.ViewManager.instance().createTabbedLocation(this.revealNavigatorSidebar.bind(this),"navigator-view",!0,!0);const e=this.navigatorTabbedLocation.tabbedPane();e.setMinimumSize(100,25),e.element.classList.add("navigator-tabbed-pane"),e.headerElement().setAttribute("jslog",`${a.toolbar("navigator").track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`);const n=new t.Toolbar.ToolbarMenuButton(this.populateNavigatorMenu.bind(this),!0,!0,"more-options");if(n.setGlyph("dots-vertical"),n.setTitle(Ri(Ui.moreOptions)),e.rightToolbar().appendToolbarItem(n),e.addEventListener(t.TabbedPane.Events.TabSelected,(({data:{tabId:e}})=>c.userMetrics.sourcesSidebarTabShown(e))),t.ViewManager.ViewManager.instance().hasViewsForLocation("run-view-sidebar")){const i=new t.SplitWidget.SplitWidget(!1,!0,"source-panel-navigator-sidebar-split-view-state");i.setMainWidget(e);const o=t.ViewManager.ViewManager.instance().createTabbedLocation(this.revealNavigatorSidebar.bind(this),"run-view-sidebar").tabbedPane();i.setSidebarWidget(o),i.installResizer(o.headerElement()),this.editorView.setSidebarWidget(i)}else this.editorView.setSidebarWidget(e);this.sourcesViewInternal=new Ii,this.sourcesViewInternal.addEventListener("EditorSelected",this.editorSelected.bind(this)),this.toggleNavigatorSidebarButton=this.editorView.createShowHideSidebarButton(Ri(Ui.showNavigator),Ri(Ui.hideNavigator),Ri(Ui.navigatorShown),Ri(Ui.navigatorHidden),"navigator"),this.toggleDebuggerSidebarButton=this.splitWidget.createShowHideSidebarButton(Ri(Ui.showDebugger),Ri(Ui.hideDebugger),Ri(Ui.debuggerShown),Ri(Ui.debuggerHidden),"debugger"),this.editorView.setMainWidget(this.sourcesViewInternal),this.threadsSidebarPane=null,this.watchSidebarPane=t.ViewManager.ViewManager.instance().view("sources.watch"),this.callstackPane=Z.instance(),i.Settings.Settings.instance().moduleSetting("sidebar-position").addChangeListener(this.updateSidebarPosition.bind(this)),this.updateSidebarPosition(),this.updateDebuggerButtonsAndStatus(),this.liveLocationPool=new u.LiveLocation.LiveLocationPool,this.setTarget(t.Context.Context.instance().flavor(o.Target.Target)),i.Settings.Settings.instance().moduleSetting("breakpoints-active").addChangeListener(this.breakpointsActiveStateChanged,this),t.Context.Context.instance().addFlavorChangeListener(o.Target.Target,this.onCurrentTargetChanged,this),t.Context.Context.instance().addFlavorChangeListener(o.DebuggerModel.CallFrame,this.callFrameChanged,this),o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.DebuggerWasEnabled,this.debuggerWasEnabled,this),o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.DebuggerPaused,this.debuggerPaused,this),o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.DebugInfoAttached,this.debugInfoAttached,this),o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.DebuggerResumed,(e=>this.debuggerResumed(e.data))),o.TargetManager.TargetManager.instance().addModelListener(o.DebuggerModel.DebuggerModel,o.DebuggerModel.Events.GlobalObjectCleared,(e=>this.debuggerResumed(e.data))),T.ExtensionServer.ExtensionServer.instance().addEventListener("SidebarPaneAdded",this.extensionSidebarPaneAdded,this),o.TargetManager.TargetManager.instance().observeTargets(this),this.lastModificationTime=-1/0}static instance(e={forceNew:null}){const{forceNew:t}=e;return Vi&&!t||(Vi=new Wi),Vi}static updateResizerAndSidebarButtons(e){e.sourcesViewInternal.leftToolbar().removeToolbarItems(),e.sourcesViewInternal.rightToolbar().removeToolbarItems(),e.sourcesViewInternal.bottomToolbar().removeToolbarItems();const i=t.Context.Context.instance().flavor(zi)&&!t.InspectorView.InspectorView.instance().isDrawerMinimized();e.splitWidget.isVertical()||i?e.splitWidget.uninstallResizer(e.sourcesViewInternal.toolbarContainerElement()):e.splitWidget.installResizer(e.sourcesViewInternal.toolbarContainerElement()),i||(e.sourcesViewInternal.leftToolbar().appendToolbarItem(e.toggleNavigatorSidebarButton),e.splitWidget.isVertical()?e.sourcesViewInternal.rightToolbar().appendToolbarItem(e.toggleDebuggerSidebarButton):e.sourcesViewInternal.bottomToolbar().appendToolbarItem(e.toggleDebuggerSidebarButton))}targetAdded(e){this.showThreadsIfNeeded()}targetRemoved(e){}showThreadsIfNeeded(){Ni.shouldBeShown()&&!this.threadsSidebarPane&&(this.threadsSidebarPane=t.ViewManager.ViewManager.instance().view("sources.threads"),this.sidebarPaneStack&&this.threadsSidebarPane&&this.sidebarPaneStack.appendView(this.threadsSidebarPane,this.splitWidget.isVertical()?this.watchSidebarPane:this.callstackPane))}setTarget(e){if(!e)return;const t=e.model(o.DebuggerModel.DebuggerModel);t&&(t.isPaused()?this.showDebuggerPausedDetails(t.debuggerPausedDetails()):(this.pausedInternal=!1,this.clearInterface(),this.toggleDebuggerSidebarButton.setEnabled(!0)))}onCurrentTargetChanged({data:e}){this.setTarget(e)}paused(){return this.pausedInternal||!1}wasShown(){t.Context.Context.instance().setFlavor(Wi,this),this.registerCSSFiles([Ct]),super.wasShown(),t.Context.Context.instance().flavor(zi)&&(t.InspectorView.InspectorView.instance().setDrawerMinimized(!0),Wi.updateResizerAndSidebarButtons(this)),this.editorView.setMainWidget(this.sourcesViewInternal)}willHide(){super.willHide(),t.Context.Context.instance().setFlavor(Wi,null);const e=t.Context.Context.instance().flavor(zi);e&&(e.showViewInWrapper(),t.InspectorView.InspectorView.instance().setDrawerMinimized(!1),Wi.updateResizerAndSidebarButtons(this))}resolveLocation(e){return"sources.sidebar-top"===e||"sources.sidebar-bottom"===e||"sources.sidebar-tabs"===e?this.sidebarPaneStack||null:this.navigatorTabbedLocation}ensureSourcesViewVisible(){return!!t.Context.Context.instance().flavor(zi)||!!t.InspectorView.InspectorView.instance().canSelectPanel("sources")&&(t.ViewManager.ViewManager.instance().showView("sources"),!0)}onResize(){"auto"===i.Settings.Settings.instance().moduleSetting("sidebar-position").get()&&this.element.window().requestAnimationFrame(this.updateSidebarPosition.bind(this))}searchableView(){return this.sourcesViewInternal.searchableView()}toggleNavigatorSidebar(){this.editorView.toggleSidebar()}toggleDebuggerSidebar(){this.splitWidget.toggleSidebar()}debuggerPaused(e){const n=e.data,r=n.debuggerPausedDetails();!this.pausedInternal&&i.Settings.Settings.instance().moduleSetting("auto-focus-on-debugger-paused-enabled").get()&&this.setAsCurrentPanel(),t.Context.Context.instance().flavor(o.Target.Target)===n.target()?this.showDebuggerPausedDetails(r):this.pausedInternal||t.Context.Context.instance().setFlavor(o.Target.Target,n.target())}debugInfoAttached(e){const{debuggerModel:i}=e.data;if(!i.isPaused())return;const n=i.debuggerPausedDetails();n&&t.Context.Context.instance().flavor(o.Target.Target)===i.target()&&this.showDebuggerPausedDetails(n)}showDebuggerPausedDetails(e){this.pausedInternal=!0,this.updateDebuggerButtonsAndStatus(),t.Context.Context.instance().setFlavor(o.DebuggerModel.DebuggerPausedDetails,e),this.toggleDebuggerSidebarButton.setEnabled(!1),this.revealDebuggerSidebar(),window.focus(),c.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront();t.Context.Context.instance().flavor(o.Target.Target)?.model(o.OverlayModel.OverlayModel)&&!i.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").get()&&!this.overlayLoggables&&(this.overlayLoggables={debuggerPausedMessage:{},resumeButton:{},stepOverButton:{}},a.registerLoggable(this.overlayLoggables.debuggerPausedMessage,`${a.dialog("debugger-paused")}`,null),a.registerLoggable(this.overlayLoggables.resumeButton,`${a.action("debugger.toggle-pause")}`,this.overlayLoggables.debuggerPausedMessage),a.registerLoggable(this.overlayLoggables.stepOverButton,`${a.action("debugger.step-over")}`,this.overlayLoggables.debuggerPausedMessage))}maybeLogOverlayAction(){if(!this.overlayLoggables)return;const e=!document.hasFocus();window.setTimeout((()=>{if(this.overlayLoggables){if(e){const e=t.Context.Context.instance().flavor(o.DebuggerModel.DebuggerPausedDetails);a.logClick(this.pausedInternal&&"step"===e?.reason?this.overlayLoggables.stepOverButton:this.overlayLoggables.resumeButton,new MouseEvent("click"))}this.pausedInternal||(a.logResize(this.overlayLoggables.debuggerPausedMessage,new DOMRect(0,0,0,0)),this.overlayLoggables=void 0)}}),500)}debuggerResumed(e){this.maybeLogOverlayAction();const i=e.target();t.Context.Context.instance().flavor(o.Target.Target)===i&&(this.pausedInternal=!1,this.clearInterface(),this.toggleDebuggerSidebarButton.setEnabled(!0),this.switchToPausedTargetTimeout=window.setTimeout(this.switchToPausedTarget.bind(this,e),500))}debuggerWasEnabled(e){const i=e.data;t.Context.Context.instance().flavor(o.Target.Target)===i.target()&&this.updateDebuggerButtonsAndStatus()}get visibleView(){return this.sourcesViewInternal.visibleView()}showUISourceCode(e,i,o){if(o){if(!this.isShowing()&&!t.Context.Context.instance().flavor(zi))return}else this.showEditor();this.sourcesViewInternal.showSourceLocation(e,i,o)}showEditor(){t.Context.Context.instance().flavor(zi)||this.setAsCurrentPanel()}showUILocation(e,t){const{uiSourceCode:i,lineNumber:o,columnNumber:n}=e;this.showUISourceCode(i,{lineNumber:o,columnNumber:n},t)}async revealInNavigator(e,i){const o=t.ViewManager.ViewManager.instance();for(const t of o.viewsForLocation("navigator-view")){const o=await t.widget();if(o instanceof dt&&o.acceptProject(e.project())){o.revealUISourceCode(e,!0),this.navigatorTabbedLocation.tabbedPane().selectTab(t.viewId(),!0),i||(this.editorView.showBoth(!0),o.focus());break}}}addExperimentMenuItem(e,t,o){e.appendCheckboxItem(o,(function(){const e=k.Runtime.experiments.isEnabled(t);k.Runtime.experiments.setEnabled(t,!e),c.userMetrics.experimentChanged(t,e);const o=i.Settings.Settings.instance().moduleSetting("navigator-group-by-folder");o.set(o.get())}),{checked:k.Runtime.experiments.isEnabled(t),additionalElement:r.Icon.create("experiment"),jslogContext:l.StringUtilities.toKebabCase(t)})}populateNavigatorMenu(e){const t=i.Settings.Settings.instance().moduleSetting("navigator-group-by-folder");e.appendItemsAtLocation("navigatorMenu"),e.viewSection().appendCheckboxItem(Ri(Ui.groupByFolder),(()=>t.set(!t.get())),{checked:t.get(),jslogContext:t.name}),this.addExperimentMenuItem(e.viewSection(),"authored-deployed-grouping",Ri(Ui.groupByAuthored)),this.addExperimentMenuItem(e.viewSection(),"just-my-code",Ri(Ui.hideIgnoreListed))}setIgnoreExecutionLineEvents(e){this.ignoreExecutionLineEvents=e}updateLastModificationTime(){this.lastModificationTime=window.performance.now()}async executionLineChanged(e){const t=await e.uiLocation();e.isDisposed()||t&&(window.performance.now()-this.lastModificationTime<Oi||this.sourcesViewInternal.showSourceLocation(t.uiSourceCode,t,void 0,!0))}lastModificationTimeoutPassedForTest(){Oi=Number.MIN_VALUE}updateLastModificationTimeForTest(){Oi=Number.MAX_VALUE}async callFrameChanged(){const e=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);e&&(this.executionLineLocation&&this.executionLineLocation.dispose(),this.executionLineLocation=await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().createCallFrameLiveLocation(e.location(),this.executionLineChanged.bind(this),this.liveLocationPool))}async updateDebuggerButtonsAndStatus(){const e=t.Context.Context.instance().flavor(o.Target.Target),i=e?e.model(o.DebuggerModel.DebuggerModel):null;i?this.pausedInternal?(this.togglePauseAction.setToggled(!0),this.togglePauseAction.setEnabled(!0),this.stepOverAction.setEnabled(!0),this.stepIntoAction.setEnabled(!0),this.stepOutAction.setEnabled(!0),this.stepAction.setEnabled(!0)):(this.togglePauseAction.setToggled(!1),this.togglePauseAction.setEnabled(!i.isPausing()),this.stepOverAction.setEnabled(!1),this.stepIntoAction.setEnabled(!1),this.stepOutAction.setEnabled(!1),this.stepAction.setEnabled(!1)):(this.togglePauseAction.setEnabled(!1),this.stepOverAction.setEnabled(!1),this.stepIntoAction.setEnabled(!1),this.stepOutAction.setEnabled(!1),this.stepAction.setEnabled(!1));const n=i?i.debuggerPausedDetails():null;await this.debuggerPausedMessage.render(n,u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),C.BreakpointManager.BreakpointManager.instance()),n&&this.updateDebuggerButtonsAndStatusForTest()}updateDebuggerButtonsAndStatusForTest(){}clearInterface(){this.updateDebuggerButtonsAndStatus(),t.Context.Context.instance().setFlavor(o.DebuggerModel.DebuggerPausedDetails,null),this.switchToPausedTargetTimeout&&clearTimeout(this.switchToPausedTargetTimeout),this.liveLocationPool.disposeAll()}switchToPausedTarget(e){if(delete this.switchToPausedTargetTimeout,!this.pausedInternal&&!e.isPaused())for(const e of o.TargetManager.TargetManager.instance().models(o.DebuggerModel.DebuggerModel))if(e.isPaused()){t.Context.Context.instance().setFlavor(o.Target.Target,e.target());break}}runSnippet(){const e=this.sourcesViewInternal.currentUISourceCode();e&&E.ScriptSnippetFileSystem.evaluateScriptSnippet(e)}editorSelected(e){const t=e.data;this.editorView.mainWidget()&&i.Settings.Settings.instance().moduleSetting("auto-reveal-in-navigator").get()&&this.revealInNavigator(t,!0)}togglePause(){const e=t.Context.Context.instance().flavor(o.Target.Target);if(!e)return!0;const i=e.model(o.DebuggerModel.DebuggerModel);return!i||(this.pausedInternal?(this.pausedInternal=!1,i.resume()):i.pause(),this.clearInterface(),!0)}prepareToResume(){if(!this.pausedInternal)return null;this.pausedInternal=!1,this.clearInterface();const e=t.Context.Context.instance().flavor(o.Target.Target);return e?e.model(o.DebuggerModel.DebuggerModel):null}longResume(){const e=this.prepareToResume();e&&(e.skipAllPausesUntilReloadOrTimeout(500),e.resume())}terminateExecution(){const e=this.prepareToResume();e&&(e.runtimeModel().terminateExecution(),e.resume())}stepOver(){const e=this.prepareToResume();return e&&e.stepOver(),!0}stepInto(){const e=this.prepareToResume();return e&&e.stepInto(),!0}stepIntoAsync(){const e=this.prepareToResume();return e&&e.scheduleStepIntoAsync(),!0}stepOut(){const e=this.prepareToResume();return e&&e.stepOut(),!0}async continueToLocation(e){const i=t.Context.Context.instance().flavor(o.RuntimeModel.ExecutionContext);if(!i)return;const n=(await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiLocationToRawLocations(e.uiSourceCode,e.lineNumber,0)).find((e=>e.debuggerModel===i.debuggerModel));n&&this.prepareToResume()&&n.continueToLocation()}toggleBreakpointsActive(){i.Settings.Settings.instance().moduleSetting("breakpoints-active").set(!i.Settings.Settings.instance().moduleSetting("breakpoints-active").get())}breakpointsActiveStateChanged(){const e=i.Settings.Settings.instance().moduleSetting("breakpoints-active").get();this.toggleBreakpointsActiveAction.setToggled(!e),this.sourcesViewInternal.toggleBreakpointsActiveState(e)}createDebugToolbar(){const e=new t.Toolbar.Toolbar("scripts-debug-toolbar");e.element.setAttribute("jslog",`${a.toolbar("debug").track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`);const i=new t.Toolbar.ToolbarButton(Ri(Ui.resumeWithAllPausesBlockedForMs),"play");i.addEventListener("Click",this.longResume,this);const o=new t.Toolbar.ToolbarButton(Ri(Ui.terminateCurrentJavascriptCall),"stop");return o.addEventListener("Click",this.terminateExecution,this),e.appendToolbarItem(t.Toolbar.Toolbar.createLongPressActionButton(this.togglePauseAction,[o,i],[])),e.appendToolbarItem(t.Toolbar.Toolbar.createActionButton(this.stepOverAction)),e.appendToolbarItem(t.Toolbar.Toolbar.createActionButton(this.stepIntoAction)),e.appendToolbarItem(t.Toolbar.Toolbar.createActionButton(this.stepOutAction)),e.appendToolbarItem(t.Toolbar.Toolbar.createActionButton(this.stepAction)),e.appendSeparator(),e.appendToolbarItem(t.Toolbar.Toolbar.createActionButton(this.toggleBreakpointsActiveAction)),e}createDebugToolbarDrawer(){const e=document.createElement("div");e.classList.add("scripts-debug-toolbar-drawer");const o=Ri(Ui.pauseOnCaughtExceptions),n=i.Settings.Settings.instance().moduleSetting("pause-on-caught-exception");return e.appendChild(t.SettingsUI.createSettingCheckbox(o,n,!0)),e}appendApplicableItems(e,t,i){i instanceof f.UISourceCode.UISourceCode?this.appendUISourceCodeItems(e,t,i):i instanceof Gt?this.appendUISourceCodeFrameItems(t,i):i instanceof f.UISourceCode.UILocation?this.appendUILocationItems(t,i):i instanceof o.RemoteObject.RemoteObject?this.appendRemoteObjectItems(t,i):this.appendNetworkRequestItems(t,i)}appendUISourceCodeItems(e,t,i){if(!e.target)return;const o=e.target;i.project().isServiceProject()||o.isSelfOrDescendant(this.navigatorTabbedLocation.widget().element)||k.Runtime.experiments.isEnabled("just-my-code")&&u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(i)||t.revealSection().appendItem(Ri(Ui.revealInSidebar),this.revealInNavigator.bind(this,i),{jslogContext:"sources.reveal-in-navigator-sidebar"}),i.contentType().hasScripts()&&u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptsForUISourceCode(i).every((e=>e.isJavaScript()))&&this.callstackPane.appendIgnoreListURLContextMenuItems(t,i)}appendUISourceCodeFrameItems(e,t){t.uiSourceCode().contentType().isFromSourceMap()||t.textEditor.state.selection.main.empty||e.debugSection().appendAction("debugger.evaluate-selection")}appendUILocationItems(e,i){const n=i.uiSourceCode;if(!u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptsForUISourceCode(n).every((e=>e.isJavaScript())))return;if(n.contentType().hasScripts()){const r=t.Context.Context.instance().flavor(o.Target.Target),s=r?r.model(o.DebuggerModel.DebuggerModel):null;s&&s.isPaused()&&e.debugSection().appendItem(Ri(Ui.continueToHere),this.continueToLocation.bind(this,i),{jslogContext:"continue-to-here"}),this.callstackPane.appendIgnoreListURLContextMenuItems(e,n)}}appendRemoteObjectItems(e,n){const r=i.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),s=t.Context.Context.instance().flavor(o.RuntimeModel.ExecutionContext);const a="wasm"===n.type?n.subtype:"node"===n.subtype?"outerHTML":n.type;e.debugSection().appendItem(Ri(Ui.storeAsGlobalVariable),(()=>s?.target().model(o.ConsoleModel.ConsoleModel)?.saveToTempVariable(s,n)),{jslogContext:"store-as-global-variable"});const d=e.clipboardSection(),u=c.InspectorFrontendHost.InspectorFrontendHostInstance;if("string"===n.type)d.appendItem(Ri(Ui.copyStringContents),(()=>{u.copyText(n.value)}),{jslogContext:"copy-string-contents"}),d.appendItem(Ri(Ui.copyStringAsJSLiteral),(()=>{u.copyText(l.StringUtilities.formatAsJSLiteral(n.value))}),{jslogContext:"copy-string-as-js-literal"}),d.appendItem(Ri(Ui.copyStringAsJSONLiteral),(()=>{u.copyText(JSON.stringify(n.value))}),{jslogContext:"copy-string-as-json-literal"});else if(ji.has(n.type))d.appendItem(Ri(Ui.copyS,{PH1:String(a)}),(()=>{u.copyText(n.description)}),{jslogContext:"copy-primitive"});else if("object"===n.type){const e=async()=>{const e=await n.callFunctionJSON(h,[{value:{subtype:n.subtype,indent:r}}]);u.copyText(e)};d.appendItem(Ri(Ui.copyS,{PH1:String(a)}),e,{jslogContext:"copy-object"})}else"function"===n.type&&e.debugSection().appendItem(Ri(Ui.showFunctionDefinition),this.showFunctionDefinition.bind(this,n),{jslogContext:"show-function-definition"});function h(e){const t=e.subtype,i=e.indent;if("map"!==t){if("set"!==t){if("node"===t)return this instanceof Element?this.outerHTML:void 0;if(t&&void 0===this)return String(t);try{return JSON.stringify(this,null,i)}catch(e){return String(this)}}else if(this instanceof Set){const e=Array.from(this.values());return`new Set(${0===e.length?"":JSON.stringify(e,null,i)})`}}else if(this instanceof Map){const e=Array.from(this.entries());return`new Map(${0===e.length?"":JSON.stringify(e,null,i)})`}}}appendNetworkRequestItems(e,t){const i=this.workspace.uiSourceCodeForURL(t.url());if(!i)return;const o=Ri(Ui.openInSourcesPanel),n=this.showUILocation.bind(this,i.uiLocation(0,0));e.revealSection().appendItem(o,n,{jslogContext:"reveal-in-sources"})}showFunctionDefinition(e){o.RemoteObject.RemoteFunction.objectAsFunction(e).targetFunction().then((e=>e.debuggerModel().functionDetailsPromise(e).then(this.didGetFunctionDetails.bind(this))))}async didGetFunctionDetails(e){if(!e||!e.location)return;const t=await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().rawLocationToUILocation(e.location);t&&this.showUILocation(t)}revealNavigatorSidebar(){this.setAsCurrentPanel(),this.editorView.showBoth(!0)}revealDebuggerSidebar(){i.Settings.Settings.instance().moduleSetting("auto-focus-on-debugger-paused-enabled").get()&&(this.setAsCurrentPanel(),this.splitWidget.showBoth(!0))}updateSidebarPosition(){let e;const o=i.Settings.Settings.instance().moduleSetting("sidebar-position").get();if(e="right"!==o&&("bottom"===o||t.InspectorView.InspectorView.instance().element.offsetWidth<680),this.sidebarPaneView&&e===!this.splitWidget.isVertical())return;if(this.sidebarPaneView&&this.sidebarPaneView.shouldHideOnDetach())return;this.sidebarPaneView&&this.sidebarPaneView.detach(),this.splitWidget.setVertical(!e),this.splitWidget.element.classList.toggle("sources-split-view-vertical",e),Wi.updateResizerAndSidebarButtons(this);const n=new t.Widget.VBox;n.element.appendChild(this.debugToolbar.element),n.element.appendChild(this.debugToolbarDrawer),n.setMinimumAndPreferredSizes(Hi,25,Hi,100),this.sidebarPaneStack=t.ViewManager.ViewManager.instance().createStackLocation(this.revealDebuggerSidebar.bind(this),void 0,"debug"),this.sidebarPaneStack.widget().element.classList.add("overflow-auto"),this.sidebarPaneStack.widget().show(n.element),this.sidebarPaneStack.widget().element.appendChild(this.debuggerPausedMessage.element()),this.sidebarPaneStack.appendApplicableItems("sources.sidebar-top"),this.threadsSidebarPane&&this.sidebarPaneStack.appendView(this.threadsSidebarPane);const r=t.ViewManager.ViewManager.instance().view("sources.js-breakpoints"),s=t.ViewManager.ViewManager.instance().view("sources.scope-chain");if(this.tabbedLocationHeader&&(this.splitWidget.uninstallResizer(this.tabbedLocationHeader),this.tabbedLocationHeader=null),e){const e=new t.SplitWidget.SplitWidget(!0,!0,"sources-panel-debugger-sidebar-split-view-state",.5);e.setMainWidget(n),this.sidebarPaneStack.showView(r),this.sidebarPaneStack.showView(this.callstackPane);const i=t.ViewManager.ViewManager.instance().createTabbedLocation(this.revealDebuggerSidebar.bind(this));e.setSidebarWidget(i.tabbedPane()),this.tabbedLocationHeader=i.tabbedPane().headerElement(),this.splitWidget.installResizer(this.tabbedLocationHeader),this.splitWidget.installResizer(this.debugToolbar.gripElementForResize()),i.appendView(s),i.appendView(this.watchSidebarPane),i.appendApplicableItems("sources.sidebar-tabs"),this.extensionSidebarPanesContainer=i,this.sidebarPaneView=e}else this.sidebarPaneStack.appendView(this.watchSidebarPane),this.sidebarPaneStack.showView(r),this.sidebarPaneStack.showView(s),this.sidebarPaneStack.showView(this.callstackPane),this.extensionSidebarPanesContainer=this.sidebarPaneStack,this.sidebarPaneView=n,this.splitWidget.uninstallResizer(this.debugToolbar.gripElementForResize());this.sidebarPaneStack.appendApplicableItems("sources.sidebar-bottom");const a=T.ExtensionServer.ExtensionServer.instance().sidebarPanes();for(let e=0;e<a.length;++e)this.addExtensionSidebarPane(a[e]);this.splitWidget.setSidebarWidget(this.sidebarPaneView)}setAsCurrentPanel(){return t.ViewManager.ViewManager.instance().showView("sources")}extensionSidebarPaneAdded(e){this.addExtensionSidebarPane(e.data)}addExtensionSidebarPane(e){e.panelName()===this.name&&this.extensionSidebarPanesContainer.appendView(e)}sourcesView(){return this.sourcesViewInternal}handleDrop(e){const i=e.items;if(!i.length)return;const o=i[0].webkitGetAsEntry();o&&o.isDirectory&&(c.InspectorFrontendHost.InspectorFrontendHostInstance.upgradeDraggedFileSystemPermissions(o.filesystem),c.userMetrics.actionTaken(c.UserMetrics.Action.WorkspaceDropFolder),t.ViewManager.ViewManager.instance().showView("navigator-files"))}}let Oi=200;const Hi=215;class _i{static#w;static instance(e={forceNew:!1}){return _i.#w&&!e.forceNew||(_i.#w=new _i),_i.#w}async reveal(e,t){const{uiSourceCode:i,range:{start:o,end:n}}=e;Wi.instance().showUISourceCode(i,{from:o,to:n},t)}}class zi extends t.Widget.VBox{view;constructor(){super(),this.element.classList.add("sources-view-wrapper"),this.element.setAttribute("jslog",`${a.panel("sources.quick").track({resize:!0})}`),this.view=Wi.instance().sourcesView()}wasShown(){t.Context.Context.instance().setFlavor(zi,this),super.wasShown(),Wi.instance().isShowing()?t.InspectorView.InspectorView.instance().setDrawerMinimized(!0):this.showViewInWrapper(),Wi.updateResizerAndSidebarButtons(Wi.instance())}willHide(){t.InspectorView.InspectorView.instance().setDrawerMinimized(!1),queueMicrotask((()=>{Wi.updateResizerAndSidebarButtons(Wi.instance())})),super.willHide(),t.Context.Context.instance().setFlavor(zi,null)}showViewInWrapper(){this.view.show(this.element)}}var qi=Object.freeze({__proto__:null,SourcesPanel:Wi,get lastModificationTimeout(){return Oi},minToolbarWidth:Hi,UILocationRevealer:class{async reveal(e,t){Wi.instance().showUILocation(e,t)}},UILocationRangeRevealer:_i,DebuggerLocationRevealer:class{async reveal(e,t){const i=await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().rawLocationToUILocation(e);i&&Wi.instance().showUILocation(i,t)}},UISourceCodeRevealer:class{async reveal(e,t){Wi.instance().showUISourceCode(e,void 0,t)}},DebuggerPausedDetailsRevealer:class{async reveal(e){if(i.Settings.Settings.instance().moduleSetting("auto-focus-on-debugger-paused-enabled").get())return Wi.instance().setAsCurrentPanel()}},RevealingActionDelegate:class{handleAction(e,o){const n=Wi.instance();if(!n.ensureSourcesViewVisible())return!1;if("debugger.toggle-pause"===o){return e.flavor(t.ShortcutRegistry.ForwardedShortcut)&&!i.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").get()||n.togglePause(),!0}return!1}},ActionDelegate:class{handleAction(e,t){const i=Wi.instance();switch(t){case"debugger.step-over":return i.stepOver(),!0;case"debugger.step-into":return i.stepIntoAsync(),!0;case"debugger.step":return i.stepInto(),!0;case"debugger.step-out":return i.stepOut(),!0;case"debugger.run-snippet":return i.runSnippet(),!0;case"debugger.toggle-breakpoints-active":return i.toggleBreakpointsActive(),!0;case"debugger.evaluate-selection":{const t=e.flavor(Gt);if(t){const{state:i}=t.textEditor;let n=i.sliceDoc(i.selection.main.from,i.selection.main.to);const r=e.flavor(o.RuntimeModel.ExecutionContext),s=r?.target().model(o.ConsoleModel.ConsoleModel);if(r&&s){const e=s.addCommandMessage(r,n);n=y.JavaScriptREPL.JavaScriptREPL.wrapObjectLiteral(n),s.evaluateCommandInConsole(r,e,n,!0)}}return!0}case"sources.reveal-in-navigator-sidebar":{const e=i.sourcesView().currentUISourceCode();return null!==e&&(i.revealInNavigator(e),!0)}case"sources.toggle-navigator-sidebar":return i.toggleNavigatorSidebar(),!0;case"sources.toggle-debugger-sidebar":return i.toggleDebuggerSidebar(),!0}return!1}},QuickSourceView:zi});const{EMPTY_BREAKPOINT_CONDITION:$i,NEVER_PAUSE_HERE_CONDITION:Gi}=C.BreakpointManager,Ki={thisScriptIsOnTheDebuggersIgnore:"This script is on the debugger's ignore list",removeFromIgnoreList:"Remove from ignore list",configure:"Configure",addBreakpoint:"Add breakpoint",addConditionalBreakpoint:"Add conditional breakpoint…",addLogpoint:"Add logpoint…",neverPauseHere:"Never pause here",removeBreakpoint:"{n, plural, =1 {Remove breakpoint} other {Remove all breakpoints in line}}",editBreakpoint:"Edit breakpoint…",disableBreakpoint:"{n, plural, =1 {Disable breakpoint} other {Disable all breakpoints in line}}",enableBreakpoint:"{n, plural, =1 {Enable breakpoint} other {Enable all breakpoints in line}}",addSourceMap:"Add source map…",addWasmDebugInfo:"Add DWARF debug info…",sourceMapLoaded:"Source map loaded.",associatedFilesAreAvailable:"Associated files are available via file tree or {PH1}.",associatedFilesShouldBeAdded:"Associated files should be added to the file tree. You can debug these resolved source files as regular JavaScript files.",theDebuggerWillSkipStepping:"The debugger will skip stepping through this script, and will not stop on exceptions.",sourceMapSkipped:"Source map skipped for this file.",sourceMapFailed:"Source map failed to load.",debuggingPowerReduced:"DevTools can't show authored sources, but you can debug the deployed code.",reloadForSourceMap:"To enable again, make sure the file isn't on the ignore list and reload.",errorLoading:"Error loading url {PH1}: {PH2}",debugFileNotFound:'Failed to load debug file "{PH1}".',debugInfoNotFound:"Failed to load any debug info for {PH1}.",showRequest:"Show request",openDeveloperResources:"Opens the request in the Developer resource panel"},Ji=e.i18n.registerUIStrings("panels/sources/DebuggerPlugin.ts",Ki),Xi=e.i18n.getLocalizedString.bind(void 0,Ji),Yi=new Map;class Qi extends he{transformer;editor=void 0;executionLocation=null;controlDown=!1;controlTimeout=void 0;sourceMapInfobar=null;scriptsPanel;breakpointManager;popoverHelper=null;scriptFileForDebuggerModel;breakpoints=[];continueToLocations=null;liveLocationPool;muted;initializedMuted;ignoreListInfobar;refreshBreakpointsTimeout=void 0;activeBreakpointDialog=null;#I=void 0;#y=!1;missingDebugInfoBar=null;#x=!1;loader;ignoreListCallback;constructor(e,i){super(e),this.transformer=i,Yi.set(e,this),this.scriptsPanel=Wi.instance(),this.breakpointManager=C.BreakpointManager.BreakpointManager.instance(),this.breakpointManager.addEventListener(C.BreakpointManager.Events.BreakpointAdded,this.breakpointChange,this),this.breakpointManager.addEventListener(C.BreakpointManager.Events.BreakpointRemoved,this.breakpointChange,this),this.uiSourceCode.addEventListener(f.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.addEventListener(f.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this),this.scriptFileForDebuggerModel=new Map,this.loader=o.PageResourceLoader.PageResourceLoader.instance(),this.loader.addEventListener("Update",this.showSourceMapInfobarIfNeeded.bind(this),this),this.ignoreListCallback=this.showIgnoreListInfobarIfNeeded.bind(this),u.IgnoreListManager.IgnoreListManager.instance().addChangeListener(this.ignoreListCallback),t.Context.Context.instance().addFlavorChangeListener(o.DebuggerModel.CallFrame,this.callFrameChanged,this),this.liveLocationPool=new u.LiveLocation.LiveLocationPool,this.updateScriptFiles(),this.muted=this.uiSourceCode.isDirty(),this.initializedMuted=this.muted,this.ignoreListInfobar=null,this.showIgnoreListInfobarIfNeeded();for(const e of this.scriptFileForDebuggerModel.values())e.checkMapping()}editorExtension(){const e=this.shortcutHandlers();return[n.EditorView.updateListener.of((e=>this.onEditorUpdate(e))),n.EditorView.domEventHandlers({keydown:t=>!!this.onKeyDown(t)||(e(t),t.defaultPrevented),keyup:e=>this.onKeyUp(e),mousemove:e=>this.onMouseMove(e),mousedown:e=>this.onMouseDown(e),focusout:e=>this.onBlur(e),wheel:e=>this.onWheel(e)}),n.lineNumbers({domEventHandlers:{click:(e,t,i)=>this.handleGutterClick(e.state.doc.lineAt(t.from),i)}}),to,ro,s.ExecutionPositionHighlighter.positionHighlighter("cm-executionLine","cm-executionToken"),n.Prec.lowest(po.field),bo,So.field,n.Prec.lowest(To.field),Eo,this.uiSourceCode.project().type()===f.Workspace.projectTypes.Debugger?n.EditorView.editorAttributes.of({class:"source-frame-debugger-script"}):[]]}shortcutHandlers(){const e=e=>e.state.doc.lineAt(e.state.selection.main.head);return t.ShortcutRegistry.ShortcutRegistry.instance().getShortcutListener({"debugger.toggle-breakpoint":async()=>!(this.muted||!this.editor)&&(await this.toggleBreakpoint(e(this.editor),!1),!0),"debugger.toggle-breakpoint-enabled":async()=>!(this.muted||!this.editor)&&(await this.toggleBreakpoint(e(this.editor),!0),!0),"debugger.breakpoint-input-window":async()=>{if(this.muted||!this.editor)return!1;const t=e(this.editor);return this.#k(t),!0}})}#k(e,t){if(this.muted)return;this.activeBreakpointDialog&&this.activeBreakpointDialog.finishEditing(!1,"");const i=this.breakpoints.find((t=>t.position>=e.from&&t.position<=e.to))?.breakpoint||null;void 0===t&&null!==i&&(t=i.isLogpoint()),this.editBreakpointCondition({line:e,breakpoint:i,location:null,isLogpoint:t})}editorInitialized(e){this.editor=e,async function(e,t,i){const o=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),n=await o.getMappedLines(i);if(!n)return[];const r=[];for(let i=0;i<e.doc.lines;i++){const{lineNumber:o}=t.editorLocationToUILocation(i,0);n.has(o)||r.push(e.doc.line(i+1).from)}return r}(e.state,this.transformer,this.uiSourceCode).then((t=>{t.length&&e.dispatch({effects:m.SourceFrame.addNonBreakableLines.of(t)})}),console.error),this.ignoreListInfobar&&this.attachInfobar(this.ignoreListInfobar),this.missingDebugInfoBar&&this.attachInfobar(this.missingDebugInfoBar),this.sourceMapInfobar&&this.attachInfobar(this.sourceMapInfobar),this.muted||this.refreshBreakpoints(),this.callFrameChanged(),this.popoverHelper?.dispose(),this.popoverHelper=new t.PopoverHelper.PopoverHelper(e,this.getPopoverRequest.bind(this),"sources.object-properties"),this.popoverHelper.setDisableOnClick(!0),this.popoverHelper.setTimeout(250,250),this.popoverHelper.setHasPadding(!0)}static accepts(e){return e.contentType().hasScripts()}showIgnoreListInfobarIfNeeded(){const e=this.uiSourceCode;if(!e.contentType().hasScripts())return;if(!u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(e))return void this.hideIgnoreListInfobar();this.ignoreListInfobar&&this.ignoreListInfobar.dispose();const i=new t.Infobar.Infobar("warning",Xi(Ki.thisScriptIsOnTheDebuggersIgnore),[{text:Xi(Ki.removeFromIgnoreList),highlight:!1,delegate:function(){u.IgnoreListManager.IgnoreListManager.instance().unIgnoreListUISourceCode(e)},dismiss:!0,jslogContext:"remove-from-ignore-list"},{text:Xi(Ki.configure),highlight:!1,delegate:t.ViewManager.ViewManager.instance().showView.bind(t.ViewManager.ViewManager.instance(),"blackbox"),dismiss:!1,jslogContext:"configure"}],void 0,void 0,"script-on-ignore-list");this.ignoreListInfobar=i,i.setCloseCallback((()=>this.removeInfobar(this.ignoreListInfobar))),i.createDetailsRowMessage(Xi(Ki.theDebuggerWillSkipStepping)),this.attachInfobar(this.ignoreListInfobar)}attachInfobar(e){this.editor&&this.editor.dispatch({effects:Zi.of(e)})}removeInfobar(e){this.editor&&e&&this.editor.dispatch({effects:eo.of(e)})}hideIgnoreListInfobar(){this.ignoreListInfobar&&(this.ignoreListInfobar.dispose(),this.ignoreListInfobar=null)}willHide(){this.popoverHelper?.hidePopover()}editBreakpointLocation({breakpoint:e,uiLocation:t}){const{lineNumber:i}=this.transformer.uiLocationToEditorLocation(t.lineNumber,t.columnNumber),o=this.editor?.state.doc.line(i+1);o&&this.editBreakpointCondition({line:o,breakpoint:e,location:null,isLogpoint:e.isLogpoint()})}populateLineGutterContextMenu(e,t){const i=new f.UISourceCode.UILocation(this.uiSourceCode,t,0);if(this.scriptsPanel.appendUILocationItems(e,i),this.muted||!this.editor)return;const o=this.editor.state.doc.line(t+1),n=this.lineBreakpoints(o),r=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().supportsConditionalBreakpoints(this.uiSourceCode);if(n.length){const t=Xi(Ki.removeBreakpoint,{n:n.length});e.debugSection().appendItem(t,(()=>n.forEach((e=>{c.userMetrics.actionTaken(c.UserMetrics.Action.BreakpointRemovedFromGutterContextMenu),e.remove(!1)}))),{jslogContext:"remove-breakpoint"}),1===n.length&&r&&e.debugSection().appendItem(Xi(Ki.editBreakpoint),(()=>{this.editBreakpointCondition({line:o,breakpoint:n[0],location:null})}),{jslogContext:"edit-breakpoint"});if(n.some((e=>e.enabled()))){const t=Xi(Ki.disableBreakpoint,{n:n.length});e.debugSection().appendItem(t,(()=>n.forEach((e=>e.setEnabled(!1)))),{jslogContext:"enable-breakpoint"})}if(n.some((e=>!e.enabled()))){const t=Xi(Ki.enableBreakpoint,{n:n.length});e.debugSection().appendItem(t,(()=>n.forEach((e=>e.setEnabled(!0)))),{jslogContext:"disable-breakpoint"})}}else this.editor&&m.SourceFrame.isBreakableLine(this.editor.state,o)&&(e.debugSection().appendItem(Xi(Ki.addBreakpoint),this.createNewBreakpoint.bind(this,o,$i,!0,!1),{jslogContext:"add-breakpoint"}),r&&(e.debugSection().appendItem(Xi(Ki.addConditionalBreakpoint),(()=>{this.editBreakpointCondition({line:o,breakpoint:null,location:null,isLogpoint:!1})}),{jslogContext:"add-cnd-breakpoint"}),e.debugSection().appendItem(Xi(Ki.addLogpoint),(()=>{this.editBreakpointCondition({line:o,breakpoint:null,location:null,isLogpoint:!0})}),{jslogContext:"add-logpoint"}),e.debugSection().appendItem(Xi(Ki.neverPauseHere),this.createNewBreakpoint.bind(this,o,Gi,!0,!1),{jslogContext:"never-pause-here"})))}populateTextAreaContextMenu(e){function t(e,t){t&&e.addSourceMapURL(t)}function o(e,t){t&&e.addDebugInfoURL(t)}if(this.uiSourceCode.project().type()===f.Workspace.projectTypes.Network&&i.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").get()&&!u.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(this.uiSourceCode.url())&&this.scriptFileForDebuggerModel.size){const i=this.scriptFileForDebuggerModel.values().next().value,n=Xi(Ki.addSourceMap);e.debugSection().appendItem(n,function(e){V.createAddSourceMapURLDialog(t.bind(null,e)).show()}.bind(null,i),{jslogContext:"add-source-map"}),i.script?.isWasm()&&!u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().pluginManager.hasPluginForScript(i.script)&&e.debugSection().appendItem(Xi(Ki.addWasmDebugInfo),function(e){V.createAddDWARFSymbolsURLDialog(o.bind(null,e)).show()}.bind(null,i),{jslogContext:"add-wasm-debug-info"})}}workingCopyChanged(){this.scriptFileForDebuggerModel.size||this.setMuted(this.uiSourceCode.isDirty())}workingCopyCommitted(){this.scriptsPanel.updateLastModificationTime(),this.scriptFileForDebuggerModel.size||this.setMuted(!1)}didMergeToVM(){this.consistentScripts()&&this.setMuted(!1)}didDivergeFromVM(){this.setMuted(!0)}setMuted(e){this.initializedMuted||e!==this.muted&&(this.muted=e,e?this.editor&&this.editor.dispatch({effects:oo.of(null)}):this.restoreBreakpointsAfterEditing())}consistentScripts(){for(const e of this.scriptFileForDebuggerModel.values())if(e.hasDivergedFromVM()||e.isMergingToVM())return!1;return!0}isVariableIdentifier(e){return"VariableName"===e||"VariableDefinition"===e}isIdentifier(e){return"VariableName"===e||"VariableDefinition"===e||"PropertyName"===e||"PropertyDefinition"===e}getPopoverRequest(e){if(t.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e))return null;const i=t.Context.Context.instance().flavor(o.Target.Target),r=i?i.model(o.DebuggerModel.DebuggerModel):null,{editor:s}=this;if(!r||!r.isPaused()||!s)return null;const a=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);if(!a)return null;let c=s.editor.posAtCoords(e);if(!c)return null;const l=s.editor.coordsAtPos(c);if(!l||e.clientY<l.top||e.clientY>l.bottom||e.clientX<l.left-30||e.clientX>l.right+30)return null;e.clientX<l.left&&c>s.state.doc.lineAt(c).from&&(c-=1);const d=yo(s.state,this.uiSourceCode.mimeType(),c);if(!d)return null;const u=s.state.doc.lineAt(d.from);if(d.to>u.to)return null;const h=s.editor.coordsAtPos(d.from),g=s.editor.coordsAtPos(d.to);if(!h||!g)return null;const m=new AnchorBox(h.left,h.top-2,g.right-h.left,g.bottom-h.top),b=s.state.sliceDoc(d.from,d.to);let f=null;return{box:m,show:async e=>{let i="";if(a.script.isJavaScript()){const e=await p.NamesResolver.allVariablesInCallFrame(a);try{i=await w.FormatterWorkerPool.formatterWorkerPool().javaScriptSubstitute(b,e)}catch{}}const c=d.containsSideEffects,l=await a.evaluate({expression:i||b,objectGroup:"popover",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1,throwOnSideEffect:c,timeout:void 0,disableBreaks:void 0,replMode:void 0,allowUnsafeEvalBlockedByCSP:void 0});if(!l||"error"in l||!l.object||"object"===l.object.type&&"error"===l.object.subtype)return!1;f=await y.ObjectPopoverHelper.ObjectPopoverHelper.buildObjectPopover(l.object,e);const u=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);if(!f||a!==u)return r.runtimeModel().releaseObjectGroup("popover"),f&&f.dispose(),!1;const h=n.Decoration.set(ko.range(d.from,d.to));return s.dispatch({effects:To.update.of(h)}),!0},hide:()=>{f&&f.dispose(),r.runtimeModel().releaseObjectGroup("popover"),s.dispatch({effects:To.update.of(n.Decoration.none)})}}}onEditorUpdate(e){if(!e.changes.empty)for(const t of this.breakpoints)t.position=e.changes.mapPos(t.position)}onWheel(e){this.executionLocation&&t.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&e.preventDefault()}onKeyDown(e){const i=t.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e);return i||this.setControlDown(!1),e.key===l.KeyboardUtilities.ESCAPE_KEY&&this.popoverHelper&&this.popoverHelper.isPopoverVisible()?(this.popoverHelper.hidePopover(),e.consume(),!0):(i&&this.executionLocation&&this.setControlDown(!0),!1)}onMouseMove(e){this.executionLocation&&this.controlDown&&t.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&(this.continueToLocations||this.showContinueToLocations())}onMouseDown(e){if(!this.executionLocation||!t.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e))return;if(!this.continueToLocations||!this.editor)return;e.consume();const i=this.editor.editor.posAtCoords(e);if(null!==i)for(const{from:e,to:t,click:o}of this.continueToLocations)if(e<=i&&t>=i){o();break}}onBlur(e){this.setControlDown(!1)}onKeyUp(e){this.setControlDown(!1)}setControlDown(e){e!==this.controlDown&&(this.controlDown=e,clearTimeout(this.controlTimeout),this.controlTimeout=void 0,e&&this.executionLocation?this.controlTimeout=window.setTimeout((()=>{this.executionLocation&&this.controlDown&&this.showContinueToLocations()}),150):this.clearContinueToLocations())}editBreakpointCondition(e){const{line:t,breakpoint:i,location:o,isLogpoint:r}=e;if(i?.isRemoved)return;this.#y=!1;if(this.#I&&function(e,t){if(e.line.number!==t.line.number)return!1;if(e.line.from!==t.line.from)return!1;if(e.line.text!==t.line.text)return!1;if(e.breakpoint!==t.breakpoint)return!1;if(e.location!==t.location)return!1;return e.isLogpoint===t.isLogpoint}(this.#I,e))return;this.activeBreakpointDialog&&this.activeBreakpointDialog.saveAndFinish();const s=this.editor,a=i?i.condition():"",c=i?.isLogpoint()??Boolean(r),l=document.createElement("div"),d=new n.Compartment,u=new $(t.number-1,a,c,(async e=>{this.activeBreakpointDialog=null,this.#I=void 0,u.detach(),s.dispatch({effects:d.reconfigure([])}),e.committed?(x.BreakpointsView.BreakpointsSidebarController.instance().breakpointEditFinished(i,a!==e.condition),i?i.setCondition(e.condition,e.isLogpoint):o?await this.setBreakpoint(o.lineNumber,o.columnNumber,e.condition,!0,e.isLogpoint):await this.createNewBreakpoint(t,e.condition,!0,e.isLogpoint)):x.BreakpointsView.BreakpointsSidebarController.instance().breakpointEditFinished(i,!1)}));s.dispatch({effects:n.StateEffect.appendConfig.of(d.of(n.EditorView.decorations.of(n.Decoration.set([n.Decoration.widget({block:!0,widget:new class extends n.WidgetType{toDOM(){return l}},side:1}).range(t.to)]))))}),u.element.addEventListener("blur",(async e=>{(!e.relatedTarget||e.relatedTarget&&!e.relatedTarget.isSelfOrDescendant(u.element))&&(this.#y=!0,setTimeout((()=>{this.activeBreakpointDialog===u&&(this.#y?(u.saveAndFinish(),this.#y=!1):u.focusEditor())}),200))}),!0),u.markAsExternallyManaged(),u.show(l),u.focusEditor(),this.activeBreakpointDialog=u,this.#I=e}async updateValueDecorations(){if(!this.editor)return;const e=this.executionLocation?await this.computeValueDecorations():null;this.editor&&(e||this.editor.state.field(So.field).size)&&this.editor.dispatch({effects:So.update.of(e||n.Decoration.none)})}async#T(e,t){const i=e&&await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().rawLocationToUILocation(e);if(!i||i.uiSourceCode.url()!==t)return null;const o=this.editor?.toOffset(this.transformer.uiLocationToEditorLocation(i.lineNumber,i.columnNumber));return o??null}async computeValueDecorations(){if(!this.editor)return null;if(!i.Settings.Settings.instance().moduleSetting("inline-variable-values").get())return null;if(!t.Context.Context.instance().flavor(o.RuntimeModel.ExecutionContext))return null;const e=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);if(!e)return null;const r=this.uiSourceCode.url(),s=this.#T(e.functionLocation(),r),a=this.#T(e.location(),r),[c,l]=await Promise.all([s,a]);if(!c||!l||!this.editor)return null;if(c>=l||l-c>1e4)return null;for(;n.syntaxParserRunning(this.editor.editor);){if(await new Promise((e=>window.requestIdleCallback(e))),!this.editor)return null;n.ensureSyntaxTree(this.editor.state,l,16)}const d=Co(this.editor.state,c,l,l);if(0===d.length)return null;const u=await wo(e,(e=>this.#T(e,r)));if(!this.editor||0===u.length)return null;const h=Io(u,d);if(!h||!this.editor)return null;const p=[];for(const[e,t]of h){const i=h.get(e-1);let o=i?Array.from(t).filter((e=>i.get(e[0])!==e[1])):Array.from(t);o.length&&(o.length>10&&(o=o.slice(0,10)),p.push(n.Decoration.widget({widget:new fo(o),side:1}).range(this.editor.state.doc.line(e+1).to)))}return n.Decoration.set(p,!0)}async showContinueToLocations(){this.popoverHelper?.hidePopover();if(!t.Context.Context.instance().flavor(o.RuntimeModel.ExecutionContext)||!this.editor)return;const e=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);if(!e)return;const i=e.functionLocation()||e.location(),r=e.debuggerModel,{state:s}=this.editor,a=await r.getPossibleBreakpoints(i,null,!0);this.continueToLocations=[];let c=-1;for(const e of a.reverse()){const t=this.transformer.uiLocationToEditorLocation(e.lineNumber,e.columnNumber);if(c===t.lineNumber&&"call"!==e.type||t.lineNumber>=s.doc.lines)continue;const i=s.doc.line(t.lineNumber+1),o=Math.min(i.to,i.from+t.columnNumber);let r=n.syntaxTree(s).resolveInner(o,1);if(r.firstChild||r.from<i.from||r.to>i.to)continue;if("."===r.name){const e=r.resolve(r.to,1);if(e.firstChild||e.from<i.from||e.to>i.to)continue;r=e}const a=r.name,l="this"===a||"return"===a||"new"===a||"break"===a||"continue"===a;if(!l&&!this.isIdentifier(a))continue;this.continueToLocations.push({from:r.from,to:r.to,async:!1,click:()=>e.continueToLocation()}),"call"===e.type&&(c=t.lineNumber);const d=l?"":i.text.slice(r.from-i.from,r.to-i.from);let u=null;if("then"===d&&"MemberExpression"===r.parent?.name?u=r.parent.parent:"setTimeout"!==d&&"setInterval"!==d&&"postMessage"!==d||(u=r.parent),"new"===a){const e=r.parent?.getChild("Expression");e&&"VariableName"===e.name&&"Worker"===s.sliceDoc(e.from,e.to)&&(u=r.parent)}if(u&&("CallExpression"===u.name||"NewExpression"===u.name)&&"call"===e.type){const t=u.getChild("ArgList")?.firstChild?.nextSibling;let i;if("VariableName"===t?.name?i=t:"ArrowFunction"!==t?.name&&"FunctionExpression"!==t?.name||(i=t.firstChild,"async"===i?.name&&(i=i.nextSibling)),i){const t=this.executionLocation&&e.lineNumber===this.executionLocation.lineNumber&&e.columnNumber===this.executionLocation.columnNumber;this.continueToLocations.push({from:i.from,to:i.to,async:!0,click:()=>this.asyncStepIn(e,Boolean(t))})}}}const l=n.Decoration.set(this.continueToLocations.map((e=>(e.async?ho:uo).range(e.from,e.to))),!0);this.editor.dispatch({effects:po.update.of(l)})}clearContinueToLocations(){this.editor&&this.editor.state.field(po.field).size&&this.editor.dispatch({effects:po.update.of(n.Decoration.none)})}asyncStepIn(e,t){function i(){e.debuggerModel.scheduleStepIntoAsync()}t?i():e.continueToLocation(i)}fetchBreakpoints(){if(!this.editor)return[];const{editor:e}=this;return this.breakpointManager.breakpointLocationsForUISourceCode(this.uiSourceCode).map((({uiLocation:t,breakpoint:i})=>{const o=this.transformer.uiLocationToEditorLocation(t.lineNumber,t.columnNumber);return{position:e.toOffset(o),breakpoint:i}}))}lineBreakpoints(e){return this.breakpoints.filter((t=>t.position>=e.from&&t.position<=e.to)).map((e=>e.breakpoint))}async linePossibleBreakpoints(e){const t=this.transformer.editorLocationToUILocation(e.number-1,0),i=this.transformer.editorLocationToUILocation(e.number-1,Math.min(e.length,2500)),o=new g.TextRange.TextRange(t.lineNumber,t.columnNumber||0,i.lineNumber,i.columnNumber||0);return await this.breakpointManager.possibleBreakpoints(this.uiSourceCode,o)}async computeBreakpointDecoration(e,t){const i=[],o=[],r=new Map,s=new Map,a=[],c=new Set,l=(e,t,i)=>{let o=s.get(e);o||(o=[],s.set(e,o)),o.push({breakpoint:i,column:t})};for(const{position:i,breakpoint:o}of t){const t=e.doc.lineAt(i);let n=r.get(t.from);n||(n=[],r.set(t.from,n)),o.enabled()&&n.every((e=>!e.enabled()))&&a.push(this.linePossibleBreakpoints(t).then((e=>d(t,e)))),n.push(o),o.enabled()&&(c.add(i),l(t.from,i-t.from,o))}for(const[e,t]of r){const i=t.sort(co)[0];let n="cm-breakpoint";i.enabled()||(n+=" cm-breakpoint-disabled"),i.bound()||(n+=" cm-breakpoint-unbound"),i.isLogpoint()?n+=" cm-breakpoint-logpoint":i.condition()&&(n+=" cm-breakpoint-conditional"),o.push(new ao(n,e).range(e))}const d=(e,t)=>{for(const i of t){const t=this.transformer.uiLocationToEditorLocation(i.lineNumber,i.columnNumber);if(t.lineNumber!==e.number-1)continue;const o=Math.min(e.to,e.from+t.columnNumber);c.has(o)||l(e.from,t.columnNumber,null)}};await Promise.all(a);for(const[e,t]of s)if(t.length>1)for(const{column:o,breakpoint:r}of t){const t=new so(r,this);i.push(n.Decoration.widget({widget:t,side:-1}).range(e+o))}return{content:n.Decoration.set(i,!0),gutter:n.RangeSet.of(o,!0)}}async restoreBreakpointsAfterEditing(){const{breakpoints:e}=this,t=this.editor;this.breakpoints=[],await Promise.all(e.map((async e=>{const{breakpoint:i,position:o}=e,n=i.condition(),r=i.enabled(),s=i.isLogpoint();await i.remove(!1);const a=t.toLineColumn(o),c=this.transformer.editorLocationToUILocation(a.lineNumber,a.columnNumber);await this.setBreakpoint(c.lineNumber,c.columnNumber,n,r,s)})))}async refreshBreakpoints(){if(this.editor){this.breakpoints=this.fetchBreakpoints();const e=this.breakpoints,t=await this.computeBreakpointDecoration(this.editor.state,e);this.editor&&this.breakpoints===e&&(t.gutter.size||this.editor.state.field(ro,!1)?.gutter.size)&&this.editor.dispatch({effects:io.of(t)})}}breakpointChange(e){const{uiLocation:t}=e.data;if(t.uiSourceCode===this.uiSourceCode&&!this.muted){for(const e of this.scriptFileForDebuggerModel.values())if(e.isDivergingFromVM()||e.isMergingToVM())return;window.clearTimeout(this.refreshBreakpointsTimeout),this.refreshBreakpointsTimeout=window.setTimeout((()=>this.refreshBreakpoints()),50)}}onInlineBreakpointMarkerClick(e,t){if(e.consume(!0),t)e.shiftKey?t.setEnabled(!t.enabled()):t.remove(!1);else if(this.editor){const t=this.editor.editor.posAtDOM(e.target),i=this.editor.state.doc.lineAt(t),o=this.transformer.editorLocationToUILocation(i.number-1,t-i.from);this.setBreakpoint(o.lineNumber,o.columnNumber,$i,!0,!1)}}onInlineBreakpointMarkerContextMenu(e,i){e.consume(!0);const o=this.editor,n=o.editor.posAtDOM(e.target),r=o.state.doc.lineAt(n);if(!m.SourceFrame.isBreakableLine(o.state,r)||!u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().supportsConditionalBreakpoints(this.uiSourceCode))return;const s=new t.ContextMenu.ContextMenu(e);if(i)s.debugSection().appendItem(Xi(Ki.editBreakpoint),(()=>{this.editBreakpointCondition({line:r,breakpoint:i,location:null})}),{jslogContext:"edit-breakpoint"});else{const e=this.transformer.editorLocationToUILocation(r.number-1,n-r.from);s.debugSection().appendItem(Xi(Ki.addConditionalBreakpoint),(()=>{this.editBreakpointCondition({line:r,breakpoint:null,location:e,isLogpoint:!1})}),{jslogContext:"add-cnd-breakpoint"}),s.debugSection().appendItem(Xi(Ki.addLogpoint),(()=>{this.editBreakpointCondition({line:r,breakpoint:null,location:e,isLogpoint:!0})}),{jslogContext:"add-logpoint"}),s.debugSection().appendItem(Xi(Ki.neverPauseHere),(()=>this.setBreakpoint(e.lineNumber,e.columnNumber,Gi,!0,!1)),{jslogContext:"never-pause-here"})}s.show()}updateScriptFiles(){for(const e of o.TargetManager.TargetManager.instance().models(o.DebuggerModel.DebuggerModel)){u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptFile(this.uiSourceCode,e)&&this.updateScriptFile(e)}this.showSourceMapInfobarIfNeeded()}updateScriptFile(e){const t=this.scriptFileForDebuggerModel.get(e),i=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptFile(this.uiSourceCode,e);this.scriptFileForDebuggerModel.delete(e),t&&(t.removeEventListener("DidMergeToVM",this.didMergeToVM,this),t.removeEventListener("DidDivergeFromVM",this.didDivergeFromVM,this),this.muted&&!this.uiSourceCode.isDirty()&&this.consistentScripts()&&this.setMuted(!1)),i&&(this.scriptFileForDebuggerModel.set(e,i),i.addEventListener("DidMergeToVM",this.didMergeToVM,this),i.addEventListener("DidDivergeFromVM",this.didDivergeFromVM,this),i.checkMapping(),i.missingSymbolFiles().then((e=>{if(e){const t=Xi(Ki.debugInfoNotFound,{PH1:i.uiSourceCode.url()});this.updateMissingDebugInfoInfobar({resources:e,details:t})}else this.updateMissingDebugInfoInfobar(null)})))}updateMissingDebugInfoInfobar(e){if(!this.missingDebugInfoBar){if(null===e)return this.removeInfobar(this.missingDebugInfoBar),void(this.missingDebugInfoBar=null);if(this.missingDebugInfoBar=t.Infobar.Infobar.create("error",e.details,[],void 0,"missing-debug-info"),this.missingDebugInfoBar){for(const n of e.resources){const e=this.missingDebugInfoBar?.createDetailsRowMessage(Xi(Ki.debugFileNotFound,{PH1:i.ParsedURL.ParsedURL.extractName(n.resourceUrl)}));if(e){const r=o.PageResourceLoader.PageResourceLoader.makeExtensionKey(n.resourceUrl,n.initiator);if(o.PageResourceLoader.PageResourceLoader.instance().getResourcesLoaded().get(r)){const n=t.UIUtils.createTextButton(Xi(Ki.showRequest),(()=>{i.Revealer.reveal(new o.PageResourceLoader.ResourceKey(r))}),{jslogContext:"show-request",variant:"text"});n.style.setProperty("margin-left","10px"),n.title=Xi(Ki.openDeveloperResources),e.appendChild(n)}e.classList.add("infobar-selectable")}}this.missingDebugInfoBar.setCloseCallback((()=>{this.removeInfobar(this.missingDebugInfoBar),this.missingDebugInfoBar=null})),this.attachInfobar(this.missingDebugInfoBar)}}}scriptHasSourceMap(){for(const e of o.TargetManager.TargetManager.instance().models(o.DebuggerModel.DebuggerModel)){const t=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().scriptFile(this.uiSourceCode,e);if(t&&t.hasSourceMapURL())return!0}return!1}getSourceMapResource(){const e=this.loader.getResourcesLoaded();for(const[t,n]of this.scriptFileForDebuggerModel.entries()){const r=n.script?.sourceMapURL;if(r){const s=o.SourceMapManager.SourceMapManager.resolveRelativeSourceURL(t.target(),n.script.sourceURL),a=i.ParsedURL.ParsedURL.completeURL(s,r);if(a){const t=e.get(o.PageResourceLoader.PageResourceLoader.makeKey(a,n.script.createPageResourceLoadInitiator()));if(t)return t}}}return null}showSourceMapInfobarIfNeeded(){if(this.sourceMapInfobar)return;if(!i.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").get())return;if(!this.scriptHasSourceMap())return;const e=this.getSourceMapResource();if(!e||null!==e.success){if(e)if(e.success){if(this.sourceMapInfobar=t.Infobar.Infobar.create("info",Xi(Ki.sourceMapLoaded),[],i.Settings.Settings.instance().createSetting("source-map-infobar-disabled",!1),"source-map-loaded"),!this.sourceMapInfobar)return;this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.associatedFilesShouldBeAdded)),this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.associatedFilesAreAvailable,{PH1:String(t.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("quick-open.show"))}))}else{if(this.sourceMapInfobar=t.Infobar.Infobar.create("warning",Xi(Ki.sourceMapFailed),[],void 0,"source-map-failed"),!this.sourceMapInfobar)return;this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.debuggingPowerReduced)),e.errorMessage&&this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.errorLoading,{PH1:l.StringUtilities.trimMiddle(e.url,t.UIUtils.MaxLengthForDisplayedURLs),PH2:e.errorMessage}))}else{if(this.sourceMapInfobar=t.Infobar.Infobar.create("info",Xi(Ki.sourceMapSkipped),[],i.Settings.Settings.instance().createSetting("source-map-skipped-infobar-disabled",!1),"source-map-skipped"),!this.sourceMapInfobar)return;this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.debuggingPowerReduced)),this.sourceMapInfobar.createDetailsRowMessage(Xi(Ki.reloadForSourceMap))}this.sourceMapInfobar.setCloseCallback((()=>{this.removeInfobar(this.sourceMapInfobar),this.sourceMapInfobar=null})),this.attachInfobar(this.sourceMapInfobar)}}handleGutterClick(e,t){return!this.muted&&0===t.button&&!t.altKey&&(t.metaKey||t.ctrlKey?(this.#k(e,t.shiftKey),!0):(this.toggleBreakpoint(e,t.shiftKey),!0))}async toggleBreakpoint(e,t){if(this.muted)return;this.activeBreakpointDialog&&this.activeBreakpointDialog.finishEditing(!1,"");const i=this.lineBreakpoints(e);if(!i.length)return void await this.createNewBreakpoint(e,$i,!0,!1);const o=i.some((e=>!e.enabled()));for(const e of i)t?e.setEnabled(o):(c.userMetrics.actionTaken(c.UserMetrics.Action.BreakpointRemovedFromGutterToggle),e.remove(!1))}async defaultBreakpointLocation(e){if(this.executionLocation){if(this.transformer.uiLocationToEditorLocation(this.executionLocation.lineNumber,this.executionLocation.columnNumber).lineNumber===e.number-1){const t=await this.linePossibleBreakpoints(e);for(const e of t)if(0===e.compareTo(this.executionLocation))return this.executionLocation}}return this.transformer.editorLocationToUILocation(e.number-1)}async createNewBreakpoint(e,t,i,o){if(!this.editor||!m.SourceFrame.isBreakableLine(this.editor.state,e))return;c.userMetrics.actionTaken(c.UserMetrics.Action.ScriptsBreakpointSet),this.#E();const n=await this.defaultBreakpointLocation(e);await this.setBreakpoint(n.lineNumber,n.columnNumber,t,i,o)}async setBreakpoint(e,t,o,n,r){i.Settings.Settings.instance().moduleSetting("breakpoints-active").set(!0);const s=await this.breakpointManager.setBreakpoint(this.uiSourceCode,e,t,o,n,r,"USER_ACTION");return this.breakpointWasSetForTest(e,t,o,n),s}breakpointWasSetForTest(e,t,i,o){}async callFrameChanged(){this.liveLocationPool.disposeAll();const e=t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame);e?await u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().createCallFrameLiveLocation(e.location(),(async t=>{const i=await t.uiLocation();i&&i.uiSourceCode.canononicalScriptId()===this.uiSourceCode.canononicalScriptId()?(this.setExecutionLocation(i),this.updateMissingDebugInfoInfobar(e.missingDebugInfoDetails),this.#E()):this.setExecutionLocation(null)}),this.liveLocationPool):this.setExecutionLocation(null)}setExecutionLocation(e){if(this.executionLocation!==e&&this.editor)if(this.executionLocation=e,e){const t=this.transformer.uiLocationToEditorLocation(e.lineNumber,e.columnNumber),i=s.Position.toOffset(this.editor.state.doc,t);this.editor.dispatch({effects:[s.ExecutionPositionHighlighter.setHighlightedPosition.of(i)]}),this.updateValueDecorations(),this.controlDown&&this.showContinueToLocations()}else this.editor.dispatch({effects:[po.update.of(n.Decoration.none),So.update.of(n.Decoration.none),s.ExecutionPositionHighlighter.clearHighlightedPosition.of()]})}dispose(){this.hideIgnoreListInfobar(),this.sourceMapInfobar&&this.sourceMapInfobar.dispose();for(const e of this.scriptFileForDebuggerModel.values())e.removeEventListener("DidMergeToVM",this.didMergeToVM,this),e.removeEventListener("DidDivergeFromVM",this.didDivergeFromVM,this);this.scriptFileForDebuggerModel.clear(),this.popoverHelper?.hidePopover(),this.popoverHelper?.dispose(),this.setExecutionLocation(null),this.breakpointManager.removeEventListener(C.BreakpointManager.Events.BreakpointAdded,this.breakpointChange,this),this.breakpointManager.removeEventListener(C.BreakpointManager.Events.BreakpointRemoved,this.breakpointChange,this),this.uiSourceCode.removeEventListener(f.UISourceCode.Events.WorkingCopyChanged,this.workingCopyChanged,this),this.uiSourceCode.removeEventListener(f.UISourceCode.Events.WorkingCopyCommitted,this.workingCopyCommitted,this),u.IgnoreListManager.IgnoreListManager.instance().removeChangeListener(this.ignoreListCallback),Yi.delete(this.uiSourceCode),super.dispose(),window.clearTimeout(this.refreshBreakpointsTimeout),this.editor=void 0,t.Context.Context.instance().removeFlavorChangeListener(o.DebuggerModel.CallFrame,this.callFrameChanged,this),this.liveLocationPool.disposeAll()}#E(){if(this.#x)return;this.#x=!0;const e=i.ResourceType.ResourceType.mimeFromURL(this.uiSourceCode.url()),t=i.ResourceType.ResourceType.mediaTypeForMetrics(e??"",this.uiSourceCode.contentType().isFromSourceMap(),g.TextUtils.isMinified(this.uiSourceCode.content()));c.userMetrics.sourcesPanelFileDebugged(t)}}const Zi=n.StateEffect.define(),eo=n.StateEffect.define(),to=n.StateField.define({create:()=>[],update(e,t){for(const i of t.effects)i.is(Zi)?e=e.concat(i.value):i.is(eo)&&(e=e.filter((e=>e!==i.value)));return e},provide:e=>n.showPanel.computeN([e],(t=>t.field(e).map((e=>()=>({dom:e.element})))))});const io=n.StateEffect.define(),oo=n.StateEffect.define();function no(e,t){const i=[];return e.between(0,t.length,((e,t,o)=>{let n=o.elementClass;/cm-breakpoint-disabled/.test(n)||(n+=" cm-breakpoint-disabled"),i.push(new ao(n,e).range(e))})),n.RangeSet.of(i,!1)}const ro=n.StateField.define({create:()=>({content:n.RangeSet.empty,gutter:n.RangeSet.empty}),update(e,t){t.changes.empty||(e={content:e.content.map(t.changes),gutter:e.gutter.map(t.changes)});for(const i of t.effects)i.is(io)?e=i.value:i.is(oo)&&(e={content:n.RangeSet.empty,gutter:no(e.gutter,t.state.doc)});return e},provide:e=>[n.EditorView.decorations.from(e,(e=>e.content)),n.lineNumberMarkers.from(e,(e=>e.gutter))]});class so extends n.WidgetType{breakpoint;parent;class;constructor(e,t){super(),this.breakpoint=e,this.parent=t,this.class="cm-inlineBreakpoint",e?.isLogpoint()?this.class+=" cm-inlineBreakpoint-logpoint":e?.condition()&&(this.class+=" cm-inlineBreakpoint-conditional"),e?.enabled()||(this.class+=" cm-inlineBreakpoint-disabled")}eq(e){return e.class===this.class&&e.breakpoint===this.breakpoint}toDOM(){const e=document.createElement("span");return e.className=this.class,e.setAttribute("jslog",`${a.breakpointMarker().track({click:!0})}`),e.addEventListener("click",(e=>{this.parent.onInlineBreakpointMarkerClick(e,this.breakpoint),e.consume()})),e.addEventListener("contextmenu",(e=>{this.parent.onInlineBreakpointMarkerContextMenu(e,this.breakpoint),e.consume()})),e}ignoreEvent(){return!0}}class ao extends n.GutterMarker{elementClass;#L;constructor(e,t){super(),this.elementClass=e,this.#L=t}eq(e){return e.elementClass===this.elementClass}toDOM(e){const t=document.createElement("div");t.setAttribute("jslog",`${a.breakpointMarker().track({click:!0})}`);const i=e.state.doc.lineAt(this.#L).number,o=e.state.facet(m.SourceFrame.LINE_NUMBER_FORMATTER);return t.textContent=o(i,e.state),t}}function co(e,t){return e.enabled()!==t.enabled()?e.enabled()?-1:1:e.bound()!==t.bound()?e.bound()?-1:1:Boolean(e.condition())!==Boolean(t.condition())?Boolean(e.condition())?-1:1:0}function lo(){const e=n.StateEffect.define(),t=n.StateField.define({create:()=>n.Decoration.none,update:(t,i)=>i.effects.reduce(((t,i)=>i.is(e)?i.value:t),t.map(i.changes)),provide:e=>n.EditorView.decorations.from(e)});return{update:e,field:t}}const uo=n.Decoration.mark({class:"cm-continueToLocation"}),ho=n.Decoration.mark({class:"cm-continueToLocation cm-continueToLocation-async"}),po=lo(),go={},mo={class:"cm-hasContinueMarkers"},bo=n.EditorView.contentAttributes.compute([po.field],(e=>e.field(po.field).size?mo:go));class fo extends n.WidgetType{pairs;constructor(e){super(),this.pairs=e}eq(e){return this.pairs.length===e.pairs.length&&this.pairs.every(((t,i)=>t[0]===e.pairs[i][0]&&t[1]===e.pairs[i][1]))}toDOM(){const e=new y.RemoteObjectPreviewFormatter.RemoteObjectPreviewFormatter,i=document.createElement("div");i.classList.add("cm-variableValues");let o=!0;for(const[n,r]of this.pairs){o?o=!1:t.UIUtils.createTextChild(i,", ");const s=i.createChild("span");t.UIUtils.createTextChild(s,n+" = ");const a=r.preview?r.preview.properties.length:0,c=r.preview&&r.preview.entries?r.preview.entries.length:0;if(r.preview&&a+c<10)e.appendObjectPreview(s,r.preview,!1);else{const e=y.ObjectPropertiesSection.ObjectPropertiesSection.createPropertyValue(r,!1,!1);s.appendChild(e.element)}}return i}}const So=lo();class vo{blockList=new Set;variables=[]}function Co(e,t,i,o){const r=e.doc.lineAt(t);t=Math.min(r.to,t),i=e.doc.lineAt(i).from;const s=n.syntaxTree(e);function a(e){return("Block"===(t=e.name)||"ForSpec"===t)&&(e.to<o||o<e.from);var t}const c=[];let l=r;const d=[];let u=null;function h(){return d.length?d[d.length-1].variables:c}return s.iterate({from:t,to:i,enter:i=>{if(i.from<t)return;if("let"===(o=i.name)||"const"===o)return void(u=i.node.nextSibling);var o;if(a(i))return void d.push(new vo);const n=function(e){return"VariableName"===e||"VariableDefinition"===e}(i.name)&&e.sliceDoc(i.from,i.to);n&&(u&&function(e){return"VariableDefinition"===e}(i.name)&&d.length>0?d[d.length-1].blockList.add(n):(i.from>l.to&&(l=e.doc.lineAt(i.from)),h().push({line:l.number-1,from:i.from,id:n})))},leave:e=>{if(u===e.node)u=null;else if(a(e)){const e=d.pop(),t=h();for(const i of e?.variables??[])e?.blockList.has(i.id)||t.push(i)}}}),c}async function wo(e,t){const i=[];for(const o of e.scopeChain()){const e=await t(o.range()?.start??null);if(!e)break;const n=await t(o.range()?.end??null);if(!n)break;const{properties:r}=await p.NamesResolver.resolveScopeInObject(o).getAllProperties(!1,!1);if(!r||r.length>500)break;const s=new Map(r.map((e=>[e.name,e.value])));if(i.push({scopeStart:e,scopeEnd:n,variableMap:s}),"local"===o.type())break}return i}function Io(e,t){const i=new Map;for(const{line:n,from:r,id:s}of t){const t=o(s,r,e);if(!t)continue;let a=i.get(n);a||(a=new Map,i.set(n,a)),a.set(s,t)}return i;function o(e,t,i){for(const o of i){if(t<o.scopeStart||t>=o.scopeEnd)continue;const i=o.variableMap.get(e);if(i)return i}return null}}function yo(e,t,i){const{main:o}=e.selection;if(!o.empty)return i<o.from||o.to<i?null:{from:o.from,to:o.to,containsSideEffects:!1};const r=n.ensureSyntaxTree(e,i,5e3);if(!r)return null;const s=r.resolveInner(i,1);if(s.firstChild)return null;switch(t){case"application/wasm":{if("Identifier"!==s.name)return null;const t=["block","loop","if","else","end","br","br_if","br_table"];for(let i=s.parent;i;i=i.parent)if("App"===i.name){const o=i.firstChild,n="Keyword"===o?.name&&e.sliceDoc(o.from,o.to);if(n&&t.includes(n))return null}return{from:s.from,to:s.to,containsSideEffects:!1}}case"text/html":case"text/javascript":case"text/jsx":case"text/typescript":case"text/typescript-jsx":{let t=s;for(;t&&"this"!==t.name&&"VariableDefinition"!==t.name&&"VariableName"!==t.name&&"MemberExpression"!==t.name&&("PropertyName"!==t.name||"PatternProperty"!==t.parent?.name||":"===t.nextSibling?.name)&&("PropertyDefinition"!==t.name||"Property"!==t.parent?.name||":"===t.nextSibling?.name);)t=t.parent;return t?{from:t.from,to:t.to,containsSideEffects:xo(e.doc,t)}:null}default:return s.to-s.from>50||/[^\w_\-$]/.test(e.sliceDoc(s.from,s.to))?null:{from:s.from,to:s.to,containsSideEffects:!1}}}function xo(e,t){let i=!1;return t.toTree().iterate({enter(o){switch(o.name){case"AssignmentExpression":case"CallExpression":return i=!0,!1;case"ArithOp":{const n=e.sliceString(t.from+o.from,t.from+o.to);if("++"===n||"--"===n)return i=!0,!1;break}}return!0}}),i}const ko=n.Decoration.mark({class:"cm-evaluatedExpression"}),To=lo(),Eo=n.EditorView.baseTheme({".cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement":{"&:hover, &.cm-breakpoint":{borderStyle:"solid",borderWidth:"1px 4px 1px 1px",marginRight:"-4px",paddingLeft:"8px",lineHeight:"calc(1.2em - 2px)",position:"relative"},"&:hover":{WebkitBorderImage:Lo("#ebeced","#ebeced")},"&.cm-breakpoint":{color:"#fff",WebkitBorderImage:Lo("#4285f4","#1a73e8")},"&.cm-breakpoint-conditional":{WebkitBorderImage:Lo("#f29900","#e37400"),"&::before":{content:'"?"',position:"absolute",top:0,left:"1px"}},"&.cm-breakpoint-logpoint":{WebkitBorderImage:Lo("#f439a0","#d01884"),"&::before":{content:'"‥"',position:"absolute",top:"-3px",left:"1px"}}},"&dark .cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement":{"&:hover":{WebkitBorderImage:Lo("#3c4043","#3c4043")},"&.cm-breakpoint":{WebkitBorderImage:Lo("#5186EC","#1a73e8")},"&.cm-breakpoint-conditional":{WebkitBorderImage:Lo("#e9a33a","#e37400")},"&.cm-breakpoint-logpoint":{WebkitBorderImage:Lo("#E54D9B","#d01884")}},":host-context(.breakpoints-deactivated) & .cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement.cm-breakpoint, .cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement.cm-breakpoint-disabled":{color:"#1a73e8",WebkitBorderImage:Lo("#d9e7fd","#1a73e8"),"&.cm-breakpoint-conditional":{color:"#e37400",WebkitBorderImage:Lo("#fcebcc","#e37400")},"&.cm-breakpoint-logpoint":{color:"#d01884",WebkitBorderImage:Lo("#fdd7ec","#f439a0")}},":host-context(.breakpoints-deactivated) &dark .cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement.cm-breakpoint, &dark .cm-gutters .cm-gutter.cm-lineNumbers .cm-gutterElement.cm-breakpoint-disabled":{WebkitBorderImage:Lo("#2a384e","#1a73e8"),"&.cm-breakpoint-conditional":{WebkitBorderImage:Lo("#4d3c1d","#e37400")},"&.cm-breakpoint-logpoint":{WebkitBorderImage:Lo("#4e283d","#f439a0")}},".cm-inlineBreakpoint":{cursor:"pointer",position:"relative",top:"1px",content:Mo("#4285F4","#1A73E8"),height:"10px","&.cm-inlineBreakpoint-conditional":{content:Po("#F29900","#E37400")},"&.cm-inlineBreakpoint-logpoint":{content:Fo("#F439A0","#D01884")}},"&dark .cm-inlineBreakpoint":{content:Mo("#5186EC","#1A73E8"),"&.cm-inlineBreakpoint-conditional":{content:Po("#e9a33a","#E37400")},"&.cm-inlineBreakpoint-logpoint":{content:Fo("#E54D9B","#D01884")}},":host-context(.breakpoints-deactivated) & .cm-inlineBreakpoint, .cm-inlineBreakpoint-disabled":{content:Mo("#4285F4","#1A73E8","0.2"),"&.cm-inlineBreakpoint-conditional":{content:Po("#F9AB00","#E37400","0.2")},"&.cm-inlineBreakpoint-logpoint":{content:Fo("#F439A0","#D01884","0.2")}},".cm-executionLine":{backgroundColor:"var(--sys-color-yellow-container)",outline:"1px solid var(--sys-color-yellow-outline)",".cm-hasContinueMarkers &":{backgroundColor:"transparent"},"&.cm-highlightedLine":{animation:"cm-fading-highlight-execution 2s 0s"},"&.cm-line::selection, &.cm-line ::selection":{backgroundColor:"var(--sys-color-tonal-container) !important"}},".cm-executionToken":{backgroundColor:"var(--sys-color-state-focus-select)"},"@keyframes cm-fading-highlight-execution":{from:{backgroundColor:"var(--sys-color-tonal-container)"},to:{backgroundColor:"var(--sys-color-yellow-container)"}},".cm-continueToLocation":{cursor:"pointer",backgroundColor:"var(--color-continue-to-location)","&:hover":{backgroundColor:"var(--color-continue-to-location-hover)",border:"1px solid var(--color-continue-to-location-hover-border)",margin:"0 -1px"},"&.cm-continueToLocation-async":{backgroundColor:"var(--color-continue-to-location-async)","&:hover":{backgroundColor:"var(--color-continue-to-location-async-hover)",border:"1px solid var(--color-continue-to-location-async-hover-border)",margin:"0 -1px"}}},".cm-evaluatedExpression":{backgroundColor:"var(--color-evaluated-expression)",border:"1px solid var(--color-evaluated-expression-border)",margin:"0 -1px"},".cm-variableValues":{display:"inline",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"1000px",opacity:"80%",backgroundColor:"var(--color-variable-values)",marginLeft:"10px",padding:"0 5px",userSelect:"text",".cm-executionLine &":{backgroundColor:"transparent",opacity:"50%"}}});function Lo(e,t){return`url('data:image/svg+xml,<svg height="11" width="26" xmlns="http://www.w3.org/2000/svg"><path d="M22.8.5l2.7 5-2.7 5H.5V.5z" fill="${encodeURIComponent(e)}" stroke="${encodeURIComponent(t)}"/></svg>') 1 3 1 1`}function Mo(e,t,i="1"){return`url('data:image/svg+xml,<svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.5 0.5H5.80139C6.29382 0.5 6.7549 0.741701 7.03503 1.14669L10.392 6L7.03503 10.8533C6.7549 11.2583 6.29382 11.5 5.80139 11.5H0.5V0.5Z" fill="${encodeURIComponent(e)}" stroke="${encodeURIComponent(t)}" fill-opacity="${encodeURIComponent(i)}"/></svg>')`}function Po(e,t,i="1"){return`url('data:image/svg+xml,<svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.5 0.5H5.80139C6.29382 0.5 6.75489 0.741701 7.03503 1.14669L10.392 6L7.03503 10.8533C6.75489 11.2583 6.29382 11.5 5.80138 11.5H0.5V0.5Z" fill="${encodeURIComponent(e)}" fill-opacity="${encodeURIComponent(i)}" stroke="${encodeURIComponent(t)}"/><path d="M3.51074 7.75635H4.68408V9H3.51074V7.75635ZM4.68408 7.23779H3.51074V6.56104C3.51074 6.271 3.55615 6.02344 3.64697 5.81836C3.73779 5.61328 3.90039 5.39648 4.13477 5.16797L4.53027 4.77686C4.71484 4.59814 4.83936 4.4502 4.90381 4.33301C4.97119 4.21582 5.00488 4.09424 5.00488 3.96826C5.00488 3.77197 4.9375 3.62402 4.80273 3.52441C4.66797 3.4248 4.46582 3.375 4.19629 3.375C3.9502 3.375 3.69238 3.42773 3.42285 3.5332C3.15625 3.63574 2.88232 3.78955 2.60107 3.99463V2.81689C2.88818 2.65283 3.17822 2.52979 3.47119 2.44775C3.76709 2.36279 4.06299 2.32031 4.35889 2.32031C4.95068 2.32031 5.41504 2.45801 5.75195 2.7334C6.08887 3.00879 6.25732 3.38818 6.25732 3.87158C6.25732 4.09424 6.20752 4.30225 6.10791 4.49561C6.0083 4.68604 5.8208 4.91602 5.54541 5.18555L5.15869 5.56348C4.95947 5.75684 4.83203 5.91504 4.77637 6.03809C4.7207 6.16113 4.69287 6.31201 4.69287 6.49072C4.69287 6.51709 4.69141 6.54785 4.68848 6.58301C4.68848 6.61816 4.68701 6.65625 4.68408 6.69727V7.23779Z" fill="white"/></svg>')`}function Fo(e,t,i="1"){return`url('data:image/svg+xml,<svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.5 0.5H5.80139C6.29382 0.5 6.7549 0.741701 7.03503 1.14669L10.392 6L7.03503 10.8533C6.7549 11.2583 6.29382 11.5 5.80139 11.5H0.5V0.5Z" fill="${encodeURIComponent(e)}" stroke="${encodeURIComponent(t)}" fill-opacity="${encodeURIComponent(i)}"/><circle cx="3" cy="6" r="1" fill="white"/><circle cx="7" cy="6" r="1" fill="white"/></svg>')`}var Do=Object.freeze({__proto__:null,DebuggerPlugin:Qi,BreakpointLocationRevealer:class{async reveal(e,t){const{uiLocation:i}=e;Wi.instance().showUILocation(i,t);const o=Yi.get(i.uiSourceCode);o?o.editBreakpointLocation(e):x.BreakpointsView.BreakpointsSidebarController.instance().breakpointEditFinished(e.breakpoint,!1)}},getVariableNamesByLine:Co,computeScopeMappings:wo,getVariableValuesByLine:Io,computePopoverHighlightRange:yo});class No{query;queryUpperCase;score;sequence;dataUpperCase;fileNameIndex;constructor(e){this.query=e,this.queryUpperCase=e.toUpperCase(),this.score=new Int32Array(2e3),this.sequence=new Int32Array(2e3),this.dataUpperCase="",this.fileNameIndex=0}calculateScore(e,t){if(!e||!this.query)return 0;const i=this.query.length,o=e.length;(!this.score||this.score.length<i*o)&&(this.score=new Int32Array(i*o*2),this.sequence=new Int32Array(i*o*2));const n=this.score,r=this.sequence;this.dataUpperCase=e.toUpperCase(),this.fileNameIndex=e.lastIndexOf("/");for(let t=0;t<i;++t)for(let i=0;i<o;++i){const s=0===i?0:n[t*o+i-1],a=0===t||0===i?0:n[(t-1)*o+i-1],c=0===t||0===i?0:r[(t-1)*o+i-1],l=this.match(this.query,e,t,i,c);l&&a+l>=s?(r[t*o+i]=c+1,n[t*o+i]=a+l):(r[t*o+i]=0,n[t*o+i]=s)}t&&this.restoreMatchIndexes(r,i,o,t);return 256*n[i*o-1]+(256-e.length)}testWordStart(e,t){if(0===t)return!0;const i=e.charAt(t-1);return"_"===i||"-"===i||"/"===i||"."===i||" "===i||e[t-1]!==this.dataUpperCase[t-1]&&e[t]===this.dataUpperCase[t]}restoreMatchIndexes(e,t,i,o){let n=t-1,r=i-1;for(;n>=0&&r>=0;)if(0===e[n*i+r])--r;else o.push(r),--n,--r;o.reverse()}singleCharScore(e,t,i,o){const n=this.testWordStart(t,o),r=o>this.fileNameIndex;let s=10;return(0===o||"/"===t[o-1])&&(s+=4),n&&(s+=2),e[i]===t[o]&&e[i]===this.queryUpperCase[i]&&(s+=6),r&&(s+=4),o===this.fileNameIndex+1&&0===i&&(s+=5),r&&n&&(s+=3),s}sequenceCharScore(e,t,i,o,n){let r=10;return o>this.fileNameIndex&&(r+=4),(0===o||"/"===t[o-1])&&(r+=5),r+=4*n,r}match(e,t,i,o,n){return this.queryUpperCase[i]!==this.dataUpperCase[o]?0:n?this.sequenceCharScore(e,t,i,o-n,n):this.singleCharScore(e,t,i,o)}}var Ao=Object.freeze({__proto__:null,FilePathScoreFunction:No});const Uo={noFilesFound:"No files found",sIgnoreListed:"{PH1} (ignore listed)"},Bo=e.i18n.registerUIStrings("panels/sources/FilteredUISourceCodeListProvider.ts",Uo),Ro=e.i18n.getLocalizedString.bind(void 0,Bo);class jo extends M.FilteredListWidget.Provider{queryLineNumberAndColumnNumber;defaultScores;scorer;uiSourceCodes;uiSourceCodeIds;query;constructor(e){super(e),this.queryLineNumberAndColumnNumber="",this.defaultScores=null,this.scorer=new No(""),this.uiSourceCodes=[],this.uiSourceCodeIds=new Set}projectRemoved(e){const t=e.data;this.populate(t),this.refresh()}populate(e){this.uiSourceCodes=[],this.uiSourceCodeIds.clear();for(const t of f.Workspace.WorkspaceImpl.instance().projects())if(t!==e&&this.filterProject(t))for(const e of t.uiSourceCodes())this.filterUISourceCode(e)&&(this.uiSourceCodes.push(e),this.uiSourceCodeIds.add(e.canononicalScriptId()))}filterUISourceCode(e){if(this.uiSourceCodeIds.has(e.canononicalScriptId()))return!1;if(k.Runtime.experiments.isEnabled("just-my-code")&&u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(e))return!1;if(e.isFetchXHR())return!1;const t=h.Persistence.PersistenceImpl.instance().binding(e);return!t||t.fileSystem===e}uiSourceCodeSelected(e,t,i){}filterProject(e){return!0}itemCount(){return this.uiSourceCodes.length}itemContentTypeAt(e){return this.uiSourceCodes[e].contentType()}itemKeyAt(e){return this.uiSourceCodes[e].url()}setDefaultScores(e){this.defaultScores=e}itemScoreAt(e,t){const i=this.uiSourceCodes[e],o=this.defaultScores&&this.defaultScores.get(i)||0;if(!t||t.length<2)return o;this.query!==t&&(this.query=t,this.scorer=new No(t));let n=10;i.project().type()!==f.Workspace.projectTypes.FileSystem||h.Persistence.PersistenceImpl.instance().binding(i)||(n=5);let r=0;i.contentType().isFromSourceMap()&&!i.isKnownThirdParty()&&(r=100),i.contentType().isScript()&&(u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(i)||(r+=50));const s=i.fullDisplayName();return o+n*(r+this.scorer.calculateScore(s,null))}renderItem(e,i,o,n){i=this.rewriteQuery(i);const r=this.uiSourceCodes[e],s=r.fullDisplayName(),a=[];new No(i).calculateScore(s,a);const c=s.lastIndexOf("/");let l=s;u.IgnoreListManager.IgnoreListManager.instance().isUserOrSourceMapIgnoreListedUISourceCode(r)&&(o.parentElement?.classList.add("is-ignore-listed"),l=Ro(Uo.sIgnoreListed,{PH1:l})),o.textContent=r.displayName()+(this.queryLineNumberAndColumnNumber||""),this.renderSubtitleElement(n,s.substring(0,c+1)),t.Tooltip.Tooltip.install(n,l);const d=[];for(let e=0;e<a.length;++e)d.push({offset:a[e],length:1});if(a[0]>c){for(let e=0;e<d.length;++e)d[e].offset-=c+1;t.UIUtils.highlightRangesWithStyleClass(o,d,"highlight")}else t.UIUtils.highlightRangesWithStyleClass(n,d,"highlight")}renderSubtitleElement(e,i){e.removeChildren();let o=i.lastIndexOf("/");i.length>43&&(o=i.length-43);e.createChild("div","first-part").textContent=i.substring(0,o);e.createChild("div","second-part").textContent=i.substring(o),t.Tooltip.Tooltip.install(e,i)}selectItem(e,t){const i=t.trim().match(/^([^:]*)(:\d+)?(:\d+)?$/);if(!i)return;let o,n;i[2]&&(o=parseInt(i[2].substr(1),10)-1),i[3]&&(n=parseInt(i[3].substr(1),10)-1);const r=null!==e?this.uiSourceCodes[e]:null;this.uiSourceCodeSelected(r,o,n)}rewriteQuery(e){if(!(e=e?e.trim():"")||":"===e)return"";const t=e.match(/^([^:]+)((?::[^:]*){0,2})$/);return this.queryLineNumberAndColumnNumber=t?t[2]:"",t?t[1]:e}uiSourceCodeAdded(e){const t=e.data;this.filterUISourceCode(t)&&this.filterProject(t.project())&&(this.uiSourceCodes.push(t),this.uiSourceCodeIds.add(t.canononicalScriptId()),this.refresh())}notFoundText(){return Ro(Uo.noFilesFound)}attach(){f.Workspace.WorkspaceImpl.instance().addEventListener(f.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAdded,this),f.Workspace.WorkspaceImpl.instance().addEventListener(f.Workspace.Events.ProjectRemoved,this.projectRemoved,this),this.populate()}detach(){f.Workspace.WorkspaceImpl.instance().removeEventListener(f.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAdded,this),f.Workspace.WorkspaceImpl.instance().removeEventListener(f.Workspace.Events.ProjectRemoved,this.projectRemoved,this),this.queryLineNumberAndColumnNumber="",this.defaultScores=null}}var Vo=Object.freeze({__proto__:null,FilteredUISourceCodeListProvider:jo});const Wo={noFileSelected:"No file selected.",noResultsFound:"No results found",typeANumberToGoToThatLine:"Type a number to go to that line.",currentPositionXsTypeAnOffset:"Current position: 0x{PH1}. Type an offset between 0x{PH2} and 0x{PH3} to navigate to.",currentLineSTypeALineNumber:"Current line: {PH1}. Type a line number between 1 and {PH2} to navigate to.",goToOffsetXs:"Go to offset 0x{PH1}.",goToLineSAndColumnS:"Go to line {PH1} and column {PH2}.",goToLineS:"Go to line {PH1}."},Oo=e.i18n.registerUIStrings("panels/sources/GoToLineQuickOpen.ts",Wo),Ho=e.i18n.getLocalizedString.bind(void 0,Oo);class _o extends M.FilteredListWidget.Provider{#M=[];constructor(){super("source-line")}selectItem(e,t){const i=this.currentSourceFrame();if(!i)return;const o=this.parsePosition(t);o&&i.revealPosition({lineNumber:o.line-1,columnNumber:o.column-1})}itemCount(){return this.#M.length}renderItem(e,i,o,n){t.UIUtils.createTextChild(o,this.#M[e])}rewriteQuery(e){return""}queryChanged(e){this.#M=[];const t=this.parsePosition(e),i=this.currentSourceFrame();if(t)i&&i.wasmDisassembly?this.#M.push(Ho(Wo.goToOffsetXs,{PH1:(t.column-1).toString(16)})):t.column&&t.column>1?this.#M.push(Ho(Wo.goToLineSAndColumnS,{PH1:t.line,PH2:t.column})):i&&t.line>i.textEditor.state.doc.lines||this.#M.push(Ho(Wo.goToLineS,{PH1:t.line}));else{if(!i)return void this.#M.push(Ho(Wo.typeANumberToGoToThatLine));const e=i.textEditor.state,t=i.wasmDisassembly,o=i.editorLocationToUILocation(e.doc.lineAt(e.selection.main.head).number-1).lineNumber;if(t){const e=t.lineNumberToBytecodeOffset(t.lineNumbers-1),i=e.toString(16).length,n=t.lineNumberToBytecodeOffset(o);return void this.#M.push(Ho(Wo.currentPositionXsTypeAnOffset,{PH1:n.toString(16).padStart(i,"0"),PH2:"0".padStart(i,"0"),PH3:e.toString(16)}))}const n=i.editorLocationToUILocation(e.doc.lines-1).lineNumber+1;this.#M.push(Ho(Wo.currentLineSTypeALineNumber,{PH1:o+1,PH2:n}))}}notFoundText(e){return this.currentSourceFrame()?Ho(Wo.noResultsFound):Ho(Wo.noFileSelected)}parsePosition(e){const t=this.currentSourceFrame();if(t&&t.wasmDisassembly){const t=e.match(/0x([0-9a-fA-F]+)/);if(!t||!t[0]||t[0].length!==e.length)return null;return{line:0,column:parseInt(t[0],16)+1}}const i=e.match(/([0-9]+)(\:[0-9]*)?/);if(!i||!i[0]||i[0].length!==e.length)return null;const o=parseInt(i[1],10);let n=0;return i[2]&&(n=parseInt(i[2].substring(1),10)),{line:Math.max(0|o,1),column:Math.max(0|n,1)}}currentSourceFrame(){const e=t.Context.Context.instance().flavor(Ii);return e?e.currentSourceFrame():null}}var zo=Object.freeze({__proto__:null,GoToLineQuickOpen:_o});const qo={formatS:"Format {PH1}",format:"Format"},$o=e.i18n.registerUIStrings("panels/sources/InplaceFormatterEditorAction.ts",qo),Go=e.i18n.getLocalizedString.bind(void 0,$o);let Ko;class Jo{button;sourcesView;uiSourceCodeTitleChangedEvent=null;constructor(){}static instance(e={forceNew:null}){const{forceNew:t}=e;return Ko&&!t||(Ko=new Jo),Ko}editorSelected(e){const t=e.data;this.updateButton(t)}editorClosed(e){const{wasSelected:t}=e.data;t&&this.updateButton(null)}updateButton(e){this.uiSourceCodeTitleChangedEvent&&i.EventTarget.removeEventListeners([this.uiSourceCodeTitleChangedEvent]),this.uiSourceCodeTitleChangedEvent=e?e.addEventListener(f.UISourceCode.Events.TitleChanged,(e=>this.updateButton(e.data)),this):null;const t=this.isFormattable(e);this.button.element.classList.toggle("hidden",!t),e&&t&&this.button.setTitle(Go(qo.formatS,{PH1:e.name()}))}getOrCreateButton(e){return this.button||(this.sourcesView=e,this.sourcesView.addEventListener("EditorSelected",this.editorSelected.bind(this)),this.sourcesView.addEventListener("EditorClosed",this.editorClosed.bind(this)),this.button=new t.Toolbar.ToolbarButton(Go(qo.format),"brackets"),this.button.addEventListener("Click",this.formatSourceInPlace,this),this.updateButton(e.currentUISourceCode())),this.button}isFormattable(e){return!!e&&(!!e.project().canSetFileContent()||null!==h.Persistence.PersistenceImpl.instance().binding(e))}formatSourceInPlace(){const e=this.sourcesView.currentSourceFrame();if(!e)return;const t=e.uiSourceCode();this.isFormattable(t)&&(t.isDirty()?this.contentLoaded(t,e,t.workingCopy()):t.requestContent().then((i=>{this.contentLoaded(t,e,i.content||"")})))}async contentLoaded(e,t,i){const{formattedContent:o,formattedMapping:n}=await w.ScriptFormatter.format(e.contentType(),t.contentType,i);if(e.workingCopy()===o)return;const r=t.textEditor.toLineColumn(t.textEditor.state.selection.main.head),[s,a]=n.originalToFormatted(r.lineNumber,r.columnNumber);e.setWorkingCopy(o),this.sourcesView.showSourceLocation(e,{lineNumber:s,columnNumber:a})}}xi(Jo.instance);var Xo=Object.freeze({__proto__:null,InplaceFormatterEditorAction:Jo});var Yo=Object.freeze({__proto__:null,OpenFileQuickOpen:class extends jo{constructor(){super("source-file")}attach(){this.setDefaultScores(Ii.defaultUISourceCodeScores()),super.attach()}uiSourceCodeSelected(e,t,o){c.userMetrics.actionTaken(c.UserMetrics.Action.SelectFileFromFilePicker),e&&("number"==typeof t?i.Revealer.reveal(e.uiLocation(t,o)):i.Revealer.reveal(e))}filterProject(e){return!e.isServiceProject()}renderItem(e,t,i,o){super.renderItem(e,t,i,o);const n=new r.Icon.Icon,s=A.iconDataForResourceType(this.itemContentTypeAt(e));n.data={...s,width:"20px",height:"20px"},i.parentElement?.parentElement?.insertBefore(n,i.parentElement)}renderAsTwoRows(){return!0}}});const Qo={noFileSelected:"No file selected.",openAJavascriptOrCssFileToSee:"Open a JavaScript or CSS file to see symbols",noResultsFound:"No results found"},Zo=e.i18n.registerUIStrings("panels/sources/OutlineQuickOpen.ts",Qo),en=e.i18n.getLocalizedString.bind(void 0,Zo);function tn(e){function t(t){t=Math.max(0,Math.min(t,e.doc.length));const i=e.doc.lineAt(t);return{lineNumber:i.number-1,columnNumber:t-i.from}}function i(){for(;"ParamList"!==r.name&&r.nextSibling(););let t="";if("ParamList"===r.name&&r.firstChild())do{switch(r.name){case"ArrayPattern":t+="[‥]";break;case"ObjectPattern":t+="{‥}";break;case"VariableDefinition":t+=e.sliceDoc(r.from,r.to);break;case"Spread":t+="...";break;case",":t+=", "}}while(r.nextSibling());return`(${t})`}const o=[],r=n.syntaxTree(e).cursor();do{switch(r.name){case"RuleSet":for(r.firstChild();;r.nextSibling()){const i=e.sliceDoc(r.from,r.to),{lineNumber:n,columnNumber:s}=t(r.from);if(o.push({title:i,lineNumber:n,columnNumber:s}),r.nextSibling(),","!==r.name)break}break;case"FunctionDeclaration":case"MethodDeclaration":{let n="";r.firstChild();do{switch(r.name){case"abstract":case"async":case"get":case"set":case"static":n=`${n}${r.name} `;break;case"Star":n+="*";break;case"PropertyDefinition":case"PrivatePropertyDefinition":case"VariableDefinition":{const s=n+e.sliceDoc(r.from,r.to),{lineNumber:a,columnNumber:c}=t(r.from),l=i();o.push({title:s,subtitle:l,lineNumber:a,columnNumber:c});break}}}while(r.nextSibling());break}case"Property":{let n="";r.firstChild();do{if("async"===r.name||"get"===r.name||"set"===r.name)n=`${n}${r.name} `;else{if("Star"!==r.name){if("PropertyDefinition"===r.name){let s=e.sliceDoc(r.from,r.to);const{lineNumber:a,columnNumber:c}=t(r.from);for(;r.nextSibling();){if("ClassExpression"===r.name){s=`class ${s}`,o.push({title:s,lineNumber:a,columnNumber:c});break}if("ArrowFunction"!==r.name&&"FunctionExpression"!==r.name||r.firstChild(),"async"===r.name)n=`async ${n}`;else if("Star"===r.name)n+="*";else if("ParamList"===r.name){s=n+s;const e=i();o.push({title:s,subtitle:e,lineNumber:a,columnNumber:c});break}}break}break}n+="*"}}while(r.nextSibling());break}case"PropertyName":case"VariableDefinition":if(r.matchContext(["ClassDeclaration"])){const i="class "+e.sliceDoc(r.from,r.to),{lineNumber:n,columnNumber:s}=t(r.from);o.push({title:i,lineNumber:n,columnNumber:s})}else if(r.matchContext(["AssignmentExpression","MemberExpression"])||r.matchContext(["VariableDeclaration"])){let n=e.sliceDoc(r.from,r.to);const{lineNumber:s,columnNumber:a}=t(r.from);for(;"Equals"!==r.name&&r.next(););if(!r.nextSibling())break;if("ArrowFunction"===r.name||"FunctionExpression"===r.name){r.firstChild();let e="";for(;"ParamList"!==r.name&&("async"===r.name?e=`async ${e}`:"Star"===r.name&&(e+="*"),r.nextSibling()););n=e+n;const t=i();o.push({title:n,subtitle:t,lineNumber:s,columnNumber:a})}else"ClassExpression"===r.name&&(n=`class ${n}`,o.push({title:n,lineNumber:s,columnNumber:a}))}break;case"App":if(r.firstChild()&&r.nextSibling()&&"module"===e.sliceDoc(r.from,r.to)){if(r.nextSibling()&&"Identifier"===r.name){const i=e.sliceDoc(r.from,r.to),{lineNumber:n,columnNumber:s}=t(r.from);o.push({title:i,lineNumber:n,columnNumber:s})}do{if("App"===r.name&&r.firstChild()){if(r.nextSibling()&&"func"===e.sliceDoc(r.from,r.to)&&r.nextSibling()&&"Identifier"===r.name){const i=e.sliceDoc(r.from,r.to),{lineNumber:n,columnNumber:s}=t(r.from),a=[];for(;r.nextSibling();)"App"===r.name&&r.firstChild()&&(r.nextSibling()&&"param"===e.sliceDoc(r.from,r.to)&&(r.nextSibling()&&"Identifier"===r.name?a.push(e.sliceDoc(r.from,r.to)):a.push(`$${a.length}`)),r.parent());const c=`(${a.join(", ")})`;o.push({title:i,subtitle:c,lineNumber:n,columnNumber:s})}r.parent()}}while(r.nextSibling())}break;case"FieldIdentifier":case"Identifier":if(r.matchContext(["FunctionDeclarator"])){const i=e.sliceDoc(r.from,r.to),{lineNumber:n,columnNumber:s}=t(r.from);o.push({title:i,lineNumber:n,columnNumber:s})}break;case"TypeIdentifier":if(r.matchContext(["ClassSpecifier"])){const i=`class ${e.sliceDoc(r.from,r.to)}`,{lineNumber:n,columnNumber:s}=t(r.from);o.push({title:i,lineNumber:n,columnNumber:s})}else if(r.matchContext(["StructSpecifier"])){const i=`struct ${e.sliceDoc(r.from,r.to)}`,{lineNumber:n,columnNumber:s}=t(r.from);o.push({title:i,lineNumber:n,columnNumber:s})}}}while(r.next());return o}class on extends M.FilteredListWidget.Provider{items=[];active=!1;constructor(){super("source-symbol")}attach(){const e=this.currentSourceFrame();e?(this.active=!0,this.items=tn(e.textEditor.state).map((({title:t,subtitle:i,lineNumber:o,columnNumber:n})=>(({lineNumber:o,columnNumber:n}=e.editorLocationToUILocation(o,n)),{title:t,subtitle:i,lineNumber:o,columnNumber:n})))):(this.active=!1,this.items=[])}detach(){this.active=!1,this.items=[]}itemCount(){return this.items.length}itemKeyAt(e){const t=this.items[e];return t.title+(t.subtitle?t.subtitle:"")}itemScoreAt(e,t){const i=this.items[e];return t.split("(")[0].toLowerCase()===i.title.toLowerCase()?1/(1+i.lineNumber):-i.lineNumber-1}renderItem(e,t,i,o){const n=this.items[e];i.textContent=n.title+(n.subtitle?n.subtitle:""),M.FilteredListWidget.FilteredListWidget.highlightRanges(i,t);const r=this.currentSourceFrame();if(!r)return;const s=i.parentElement?.parentElement?.createChild("span","tag");if(!s)return;const a=r.wasmDisassembly;if(a){const e=a.lineNumberToBytecodeOffset(a.lineNumbers-1).toString(16).length;s.textContent=`:0x${n.columnNumber.toString(16).padStart(e,"0")}`}else s.textContent=`:${n.lineNumber+1}`}selectItem(e,t){if(null===e)return;const i=this.currentSourceFrame();if(!i)return;const o=this.items[e];i.revealPosition({lineNumber:o.lineNumber,columnNumber:o.columnNumber},!0)}currentSourceFrame(){const e=t.Context.Context.instance().flavor(Ii);return e&&e.currentSourceFrame()}notFoundText(){return this.currentSourceFrame()?this.active?en(Qo.noResultsFound):en(Qo.openAJavascriptOrCssFileToSee):en(Qo.noFileSelected)}}var nn=Object.freeze({__proto__:null,outline:tn,OutlineQuickOpen:on});const rn=new CSSStyleSheet;rn.replaceSync(".scope-chain-sidebar-pane-section-header{flex:auto}.scope-chain-sidebar-pane-section-icon{float:left;margin-right:5px}.scope-chain-sidebar-pane-section-subtitle{float:right;margin-left:5px;max-width:55%;text-overflow:ellipsis;overflow:hidden}.scope-chain-sidebar-pane-section-title{font-weight:normal;word-wrap:break-word;white-space:normal}.scope-chain-sidebar-pane-section{padding:2px 4px;flex:none}\n/*# sourceURL=scopeChainSidebarPane.css */\n");const sn={loading:"Loading...",notPaused:"Not paused",noVariables:"No variables",closureS:"Closure ({PH1})",closure:"Closure"},an=e.i18n.registerUIStrings("panels/sources/ScopeChainSidebarPane.ts",sn),cn=e.i18n.getLocalizedString.bind(void 0,an);let ln;class dn extends t.Widget.VBox{treeOutline;expandController;linkifier;infoElement;#P=null;constructor(){super(!0),this.contentElement.setAttribute("jslog",`${a.section("sources.scope-chain")}`),this.treeOutline=new y.ObjectPropertiesSection.ObjectPropertiesSectionsTreeOutline,this.treeOutline.hideOverflow(),this.treeOutline.setShowSelectionOnKeyboardFocus(!0),this.expandController=new y.ObjectPropertiesSection.ObjectPropertiesSectionsTreeExpandController(this.treeOutline),this.linkifier=new N.Linkifier.Linkifier,this.infoElement=document.createElement("div"),this.infoElement.className="gray-info-message",this.infoElement.tabIndex=-1,this.flavorChanged(t.Context.Context.instance().flavor(o.DebuggerModel.CallFrame))}static instance(){return ln||(ln=new dn),ln}flavorChanged(e){this.#P?.dispose(),this.#P=null,this.linkifier.reset(),this.contentElement.removeChildren(),this.contentElement.appendChild(this.infoElement),e?(this.infoElement.textContent=cn(sn.loading),this.#P=new p.ScopeChainModel.ScopeChainModel(e),this.#P.addEventListener("ScopeChainUpdated",(e=>this.buildScopeTreeOutline(e.data)),this)):this.infoElement.textContent=cn(sn.notPaused)}focus(){this.hasFocus()||t.Context.Context.instance().flavor(o.DebuggerModel.DebuggerPausedDetails)&&this.treeOutline.forceSelect()}buildScopeTreeOutline(e){const{scopeChain:t}=e;this.treeOutline.removeChildren(),this.contentElement.removeChildren(),this.contentElement.appendChild(this.treeOutline.element);let i=!1;for(const[e,o]of t.entries()){"local"===o.type()&&(i=!0);const t=this.createScopeSectionTreeElement(o);"global"===o.type()?t.collapse():i&&"local"!==o.type()||t.expand(),this.treeOutline.appendChild(t),0===e&&t.select(!0)}this.sidebarPaneUpdatedForTest()}createScopeSectionTreeElement(e){let i=null;"local"!==e.type()&&"closure"!==e.type()||(i=cn(sn.noVariables));let o=e.typeName();if("closure"===e.type()){const i=e.name();o=i?cn(sn.closureS,{PH1:t.UIUtils.beautifyFunctionName(i)}):cn(sn.closure)}let n=e.description();o&&o!==n||(n=null);const r=e.icon(),s=document.createElement("div");if(s.classList.add("scope-chain-sidebar-pane-section-header"),s.classList.add("tree-element-title"),r){const e=document.createElement("img");e.classList.add("scope-chain-sidebar-pane-section-icon"),e.src=r,s.appendChild(e)}s.createChild("div","scope-chain-sidebar-pane-section-subtitle").textContent=n,s.createChild("div","scope-chain-sidebar-pane-section-title").textContent=o;const a=new y.ObjectPropertiesSection.RootElement(e.object(),this.linkifier,i,0,e.extraProperties());return a.title=s,a.listItemElement.classList.add("scope-chain-sidebar-pane-section"),a.listItemElement.setAttribute("aria-label",o),this.expandController.watchSection(o+(n?":"+n:""),a),a}sidebarPaneUpdatedForTest(){}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([rn]),this.registerCSSFiles([rn])}}var un=Object.freeze({__proto__:null,ScopeChainSidebarPane:dn});const hn=new CSSStyleSheet;hn.replaceSync(".border-container{border-bottom:1px solid var(--sys-color-divider);flex-shrink:0}\n/*# sourceURL=sourcesNavigator.css */\n");const pn={explainWorkspace:"Set up workspace to sync edits directly to the sources you develop",explainLocalOverrides:"Override network requests and web content locally to mock remote resources",learnMore:"Learn more",clearConfiguration:"Clear configuration",selectFolderForOverrides:"Select folder for overrides",explainContentScripts:"View content scripts served by extensions",explainSnippets:"Save the JavaScript code you run often to run it again anytime",newSnippet:"New snippet",createNewSnippet:"Create new snippet",run:"Run",rename:"Rename…",remove:"Remove",saveAs:"Save as..."},gn=e.i18n.registerUIStrings("panels/sources/SourcesNavigator.ts",pn),mn=e.i18n.getLocalizedString.bind(void 0,gn);let bn,fn;class Sn extends dt{constructor(){super("navigator-network",!0),o.TargetManager.TargetManager.instance().addEventListener("InspectedURLChanged",this.inspectedURLChanged,this),c.userMetrics.panelLoaded("sources","DevTools.Launch.Sources"),o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this))}wasShown(){this.registerCSSFiles([hn]),super.wasShown()}static instance(e={forceNew:null}){const{forceNew:t}=e;return bn&&!t||(bn=new Sn),bn}acceptProject(e){return e.type()===f.Workspace.projectTypes.Network&&o.TargetManager.TargetManager.instance().isInScope(u.NetworkProject.NetworkProject.getTargetForProject(e))}onScopeChange(){for(const e of f.Workspace.WorkspaceImpl.instance().projects())this.acceptProject(e)?this.tryAddProject(e):this.removeProject(e)}inspectedURLChanged(e){const t=o.TargetManager.TargetManager.instance().scopeTarget();if(e.data!==t)return;const i=t&&t.inspectedURL();if(i)for(const e of this.workspace().uiSourceCodes())this.acceptProject(e.project())&&e.url()===i&&this.revealUISourceCode(e,!0)}uiSourceCodeAdded(e){const t=o.TargetManager.TargetManager.instance().scopeTarget(),i=t&&t.inspectedURL();i&&e.url()===i&&this.revealUISourceCode(e,!0)}}class vn extends dt{toolbar;constructor(){super("navigator-overrides");const e=new t.EmptyWidget.EmptyWidget("");this.setPlaceholder(e),e.appendParagraph().appendChild(t.Fragment.html`
  <div>${mn(pn.explainLocalOverrides)}</div><br />
  ${t.XLink.XLink.create("https://goo.gle/devtools-overrides",mn(pn.learnMore),void 0,void 0,"learn-more")}
  `),this.toolbar=new t.Toolbar.Toolbar("navigator-toolbar"),this.contentElement.insertBefore(this.toolbar.element,this.contentElement.firstChild),h.NetworkPersistenceManager.NetworkPersistenceManager.instance().addEventListener("ProjectChanged",this.updateProjectAndUI,this),this.workspace().addEventListener(f.Workspace.Events.ProjectAdded,this.onProjectAddOrRemoved,this),this.workspace().addEventListener(f.Workspace.Events.ProjectRemoved,this.onProjectAddOrRemoved,this),this.updateProjectAndUI()}static instance(e={forceNew:null}){const{forceNew:t}=e;return fn&&!t||(fn=new vn),fn}onProjectAddOrRemoved(e){const t=e.data;t&&t.type()===f.Workspace.projectTypes.FileSystem&&"overrides"!==h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemType(t)||this.updateUI()}updateProjectAndUI(){this.reset();const e=h.NetworkPersistenceManager.NetworkPersistenceManager.instance().project();e&&this.tryAddProject(e),this.updateUI()}updateUI(){this.toolbar.removeToolbarItems();const e=h.NetworkPersistenceManager.NetworkPersistenceManager.instance().project();if(e){const o=new t.Toolbar.ToolbarSettingCheckbox(i.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled"));this.toolbar.appendToolbarItem(o),this.toolbar.appendToolbarItem(new t.Toolbar.ToolbarSeparator(!0));const n=new t.Toolbar.ToolbarButton(mn(pn.clearConfiguration),"clear");return n.addEventListener("Click",(()=>{i.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled").set(!1),e.remove()})),void this.toolbar.appendToolbarItem(n)}const o=mn(pn.selectFolderForOverrides),n=new t.Toolbar.ToolbarButton(o,"plus",o);n.addEventListener("Click",(e=>{this.setupNewWorkspace()}),this),this.toolbar.appendToolbarItem(n)}async setupNewWorkspace(){await h.IsolatedFileSystemManager.IsolatedFileSystemManager.instance().addFileSystem("overrides")&&i.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled").set(!0)}sourceSelected(e,t){c.userMetrics.actionTaken(c.UserMetrics.Action.OverridesSourceSelected),super.sourceSelected(e,t)}acceptProject(e){return e===h.NetworkPersistenceManager.NetworkPersistenceManager.instance().project()}}var Cn=Object.freeze({__proto__:null,NetworkNavigatorView:Sn,FilesNavigatorView:class extends dt{constructor(){super("navigator-files");const e=new t.EmptyWidget.EmptyWidget("");this.setPlaceholder(e),e.appendParagraph().appendChild(t.Fragment.html`
  <div>${mn(pn.explainWorkspace)}</div><br />
  ${t.XLink.XLink.create("https://goo.gle/devtools-workspace",mn(pn.learnMore),void 0,void 0,"learn-more")}
  `);const i=new t.Toolbar.Toolbar("navigator-toolbar");i.appendItemsAtLocation("files-navigator-toolbar").then((()=>{i.empty()||this.contentElement.insertBefore(i.element,this.contentElement.firstChild)}))}sourceSelected(e,t){c.userMetrics.actionTaken(c.UserMetrics.Action.WorkspaceSourceSelected),super.sourceSelected(e,t)}acceptProject(e){return e.type()===f.Workspace.projectTypes.FileSystem&&"overrides"!==h.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding.fileSystemType(e)&&!E.ScriptSnippetFileSystem.isSnippetsProject(e)}handleContextMenu(e){const i=new t.ContextMenu.ContextMenu(e);i.defaultSection().appendAction("sources.add-folder-to-workspace",void 0,!0),i.show()}},OverridesNavigatorView:vn,ContentScriptsNavigatorView:class extends dt{constructor(){super("navigator-content-scripts");const e=new t.EmptyWidget.EmptyWidget("");this.setPlaceholder(e),e.appendParagraph().appendChild(t.Fragment.html`
  <div>${mn(pn.explainContentScripts)}</div><br />
  ${t.XLink.XLink.create("https://developer.chrome.com/extensions/content_scripts",mn(pn.learnMore),void 0,void 0,"learn-more")}
  `)}acceptProject(e){return e.type()===f.Workspace.projectTypes.ContentScripts}},SnippetsNavigatorView:class extends dt{constructor(){super("navigator-snippets");const e=new t.EmptyWidget.EmptyWidget("");this.setPlaceholder(e),e.appendParagraph().appendChild(t.Fragment.html`
  <div>${mn(pn.explainSnippets)}</div><br />
  ${t.XLink.XLink.create("https://goo.gle/devtools-snippets",mn(pn.learnMore),void 0,void 0,"learn-more")}
  `);const i=new t.Toolbar.Toolbar("navigator-toolbar"),o=new t.Toolbar.ToolbarButton(mn(pn.newSnippet),"plus",mn(pn.newSnippet),"sources.new-snippet");o.addEventListener("Click",(e=>{this.create(E.ScriptSnippetFileSystem.findSnippetsProject(),"")})),i.appendToolbarItem(o),this.contentElement.insertBefore(i.element,this.contentElement.firstChild)}acceptProject(e){return E.ScriptSnippetFileSystem.isSnippetsProject(e)}handleContextMenu(e){const i=new t.ContextMenu.ContextMenu(e);i.headerSection().appendItem(mn(pn.createNewSnippet),(()=>this.create(E.ScriptSnippetFileSystem.findSnippetsProject(),"")),{jslogContext:"create-new-snippet"}),i.show()}handleFileContextMenu(e,i){const o=i.uiSourceCode(),n=new t.ContextMenu.ContextMenu(e);n.headerSection().appendItem(mn(pn.run),(()=>E.ScriptSnippetFileSystem.evaluateScriptSnippet(o)),{jslogContext:"run"}),n.editSection().appendItem(mn(pn.rename),(()=>this.rename(i,!1)),{jslogContext:"rename"}),n.editSection().appendItem(mn(pn.remove),(()=>o.project().deleteFile(o)),{jslogContext:"remove"}),n.saveSection().appendItem(mn(pn.saveAs),this.handleSaveAs.bind(this,o),{jslogContext:"save-as"}),n.show()}async handleSaveAs(e){e.commitWorkingCopy();const{content:t}=await e.requestContent();await f.FileManager.FileManager.instance().save(this.addJSExtension(e.url()),t||"",!0,!1),f.FileManager.FileManager.instance().close(e.url())}addJSExtension(e){return i.ParsedURL.ParsedURL.concatenate(e,".js")}},ActionDelegate:class{handleAction(e,t){switch(t){case"sources.create-snippet":return E.ScriptSnippetFileSystem.findSnippetsProject().createFile(l.DevToolsPath.EmptyEncodedPathString,null,"").then((e=>i.Revealer.reveal(e))),!0;case"sources.add-folder-to-workspace":return h.IsolatedFileSystemManager.IsolatedFileSystemManager.instance().addFileSystem(),!0}return!1}}});const wn=new CSSStyleSheet;wn.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");const In=new CSSStyleSheet;In.replaceSync(".watch-expression-delete-button{position:absolute;opacity:0%;right:0;.watch-expression-title:hover &{opacity:100%}.watch-expression-title:focus-within &{opacity:100%}}:host-context(.theme-with-dark-background) .watch-expression-delete-button{filter:brightness(1.5)}.watch-expressions{min-height:26px}.watch-expression-title{white-space:nowrap;line-height:20px;display:flex}.watch-expression-title:hover,\n.watch-expression-title:focus-within{padding-right:26px}.watch-expression-object-header .watch-expression-title{margin-left:1px}.watch-expression{position:relative;flex:auto;min-height:20px}.watch-expression .name{color:var(--sys-color-purple);white-space:nowrap;text-overflow:ellipsis;overflow:hidden;flex-shrink:1000000;min-width:2em}.watch-expression-error{color:var(--sys-color-error)}.watch-expressions-separator{flex-shrink:0;flex-grow:0}.watch-expression .value{white-space:nowrap;display:inline;overflow:hidden;padding-left:4px;text-overflow:ellipsis;flex-shrink:1}.watch-expression .text-prompt{text-overflow:clip;overflow:hidden;white-space:nowrap;padding-left:4px;min-height:18px;line-height:18px;user-select:text}.watch-expression-text-prompt-proxy{margin:2px 12px 2px -4px;padding-bottom:3px}.watch-expression-header{flex:auto;margin-left:-16px;padding-left:15px}li.watch-expression-tree-item{padding-left:4px}li.watch-expression-tree-item.selected{background:var(--sys-color-neutral-container)}li.watch-expression-tree-item.selected:focus{background:var(--sys-color-tonal-container)}li.watch-expression-tree-item.selected:focus-within:focus-visible{background:var(--sys-color-tonal-container)}.watch-expression-header:focus-visible{background:var(--sys-color-tonal-container)}li.watch-expression-editing::before{background-color:transparent}@media (forced-colors: active){.watch-expression-title:hover .watch-expression-delete-button,\n  .watch-expressions .dimmed{opacity:100%}li.watch-expression-tree-item *{forced-color-adjust:none;color:ButtonText}li.watch-expression-tree-item:hover{forced-color-adjust:none;background-color:Highlight}li.watch-expression-tree-item:hover *{color:HighlightText}li.watch-expression-tree-item:hover .watch-expression-delete-button{background-color:HighlightText}}\n/*# sourceURL=watchExpressionsSidebarPane.css */\n");const yn={addWatchExpression:"Add watch expression",refreshWatchExpressions:"Refresh watch expressions",noWatchExpressions:"No watch expressions",deleteAllWatchExpressions:"Delete all watch expressions",addPropertyPathToWatch:"Add property path to watch",deleteWatchExpression:"Delete watch expression",notAvailable:"<not available>",copyValue:"Copy value"},xn=e.i18n.registerUIStrings("panels/sources/WatchExpressionsSidebarPane.ts",yn),kn=e.i18n.getLocalizedString.bind(void 0,xn);let Tn;class En extends t.ThrottledWidget.ThrottledWidget{watchExpressions;emptyElement;watchExpressionsSetting;addButton;refreshButton;treeOutline;expandController;linkifier;constructor(){super(!0),this.watchExpressions=[],this.watchExpressionsSetting=i.Settings.Settings.instance().createLocalSetting("watch-expressions",[]),this.addButton=new t.Toolbar.ToolbarButton(kn(yn.addWatchExpression),"plus",void 0,"add-watch-expression"),this.addButton.addEventListener("Click",(e=>{this.addButtonClicked()})),this.refreshButton=new t.Toolbar.ToolbarButton(kn(yn.refreshWatchExpressions),"refresh",void 0,"refresh-watch-expressions"),this.refreshButton.addEventListener("Click",this.update,this),this.contentElement.classList.add("watch-expressions"),this.contentElement.setAttribute("jslog",`${a.section("sources.watch")}`),this.contentElement.addEventListener("contextmenu",this.contextMenu.bind(this),!1),this.treeOutline=new y.ObjectPropertiesSection.ObjectPropertiesSectionsTreeOutline,this.treeOutline.hideOverflow(),this.treeOutline.setShowSelectionOnKeyboardFocus(!0),this.expandController=new y.ObjectPropertiesSection.ObjectPropertiesSectionsTreeExpandController(this.treeOutline),t.Context.Context.instance().addFlavorChangeListener(o.RuntimeModel.ExecutionContext,this.update,this),t.Context.Context.instance().addFlavorChangeListener(o.DebuggerModel.CallFrame,this.update,this),this.linkifier=new N.Linkifier.Linkifier,this.update()}static instance(){return Tn||(Tn=new En),Tn}toolbarItems(){return[this.addButton,this.refreshButton]}focus(){this.hasFocus()||this.watchExpressions.length>0&&this.treeOutline.forceSelect()}hasExpressions(){return Boolean(this.watchExpressionsSetting.get().length)}saveExpressions(){const e=[];for(let t=0;t<this.watchExpressions.length;t++){const i=this.watchExpressions[t].expression();i&&e.push(i)}this.watchExpressionsSetting.set(e)}async addButtonClicked(){await t.ViewManager.ViewManager.instance().showView("sources.watch"),this.emptyElement.classList.add("hidden"),this.createWatchExpression(null).startEditing()}async doUpdate(){this.linkifier.reset(),this.contentElement.removeChildren(),this.treeOutline.removeChildren(),this.watchExpressions=[],this.emptyElement=this.contentElement.createChild("div","gray-info-message"),this.emptyElement.textContent=kn(yn.noWatchExpressions),this.emptyElement.tabIndex=-1;const e=this.watchExpressionsSetting.get();e.length&&this.emptyElement.classList.add("hidden");for(let t=0;t<e.length;++t){const i=e[t];i&&this.createWatchExpression(i)}}createWatchExpression(e){this.contentElement.appendChild(this.treeOutline.element);const i=new Ln(e,this.expandController,this.linkifier);return t.ARIAUtils.setLabel(this.contentElement,kn(yn.addWatchExpression)),i.addEventListener("ExpressionUpdated",this.watchExpressionUpdated,this),this.treeOutline.appendChild(i.treeElement()),this.watchExpressions.push(i),i}watchExpressionUpdated({data:e}){e.expression()||(l.ArrayUtilities.removeElement(this.watchExpressions,e),this.treeOutline.removeChild(e.treeElement()),this.emptyElement.classList.toggle("hidden",Boolean(this.watchExpressions.length)),0===this.watchExpressions.length&&this.treeOutline.element.remove()),this.saveExpressions()}contextMenu(e){const i=new t.ContextMenu.ContextMenu(e);this.populateContextMenu(i,e),i.show()}populateContextMenu(e,t){let i=!1;for(const e of this.watchExpressions)i=i||e.isEditing();i||e.debugSection().appendItem(kn(yn.addWatchExpression),this.addButtonClicked.bind(this),{jslogContext:"add-watch-expression"}),this.watchExpressions.length>1&&e.debugSection().appendItem(kn(yn.deleteAllWatchExpressions),this.deleteAllButtonClicked.bind(this),{jslogContext:"delete-all-watch-expressions"});const o=this.treeOutline.treeElementFromEvent(t);if(!o)return;const n=this.watchExpressions.find((e=>o.hasAncestorOrSelf(e.treeElement())));n&&n.populateContextMenu(e,t)}deleteAllButtonClicked(){this.watchExpressions=[],this.saveExpressions(),this.update()}async focusAndAddExpressionToWatch(e){await t.ViewManager.ViewManager.instance().showView("sources.watch"),this.createWatchExpression(e),this.saveExpressions(),this.update()}handleAction(e,i){const o=t.Context.Context.instance().flavor(Gt);if(!o)return!1;const{state:n}=o.textEditor,r=n.sliceDoc(n.selection.main.from,n.selection.main.to);return this.focusAndAddExpressionToWatch(r),!0}appendApplicableItems(e,t,i){i instanceof y.ObjectPropertiesSection.ObjectPropertyTreeElement?i.property.synthetic||t.debugSection().appendItem(kn(yn.addPropertyPathToWatch),(()=>this.focusAndAddExpressionToWatch(i.path())),{jslogContext:"add-property-path-to-watch"}):i.textEditor.state.selection.main.empty||t.debugSection().appendAction("sources.add-to-watch")}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([In]),this.registerCSSFiles([In,wn])}}class Ln extends i.ObjectWrapper.ObjectWrapper{treeElementInternal;nameElement;valueElement;expressionInternal;expandController;element;editing;linkifier;textPrompt;result;preventClickTimeout;constructor(e,t,i){super(),this.expressionInternal=e,this.expandController=t,this.element=document.createElement("div"),this.element.classList.add("watch-expression"),this.element.classList.add("monospace"),this.editing=!1,this.linkifier=i,this.createWatchExpression(),this.update()}treeElement(){return this.treeElementInternal}expression(){return this.expressionInternal}async#F(e,t){const i=e.debuggerModel.selectedCallFrame();if(i&&i.script.isJavaScript()){const e=await p.NamesResolver.allVariablesInCallFrame(i);try{t=await w.FormatterWorkerPool.formatterWorkerPool().javaScriptSubstitute(t,e)}catch{}}return e.evaluate({expression:t,objectGroup:Ln.watchObjectGroupId,includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1},!1,!1)}update(){const e=t.Context.Context.instance().flavor(o.RuntimeModel.ExecutionContext);e&&this.expressionInternal?this.#F(e,this.expressionInternal).then((e=>{"object"in e?this.createWatchExpression(e.object,e.exceptionDetails):this.createWatchExpression()})):this.createWatchExpression()}startEditing(){this.editing=!0,this.treeElementInternal.setDisableSelectFocus(!0),this.element.removeChildren();const e=this.element.createChild("div");e.textContent=this.nameElement.textContent,this.textPrompt=new y.ObjectPropertiesSection.ObjectPropertyPrompt,this.textPrompt.renderAsBlock();const t=this.textPrompt.attachAndStartEditing(e,this.finishEditing.bind(this));this.treeElementInternal.listItemElement.classList.add("watch-expression-editing"),this.treeElementInternal.collapse(),t.classList.add("watch-expression-text-prompt-proxy"),t.addEventListener("keydown",this.promptKeyDown.bind(this),!1);const i=this.element.getComponentSelection();i&&i.selectAllChildren(e)}isEditing(){return Boolean(this.editing)}finishEditing(e,t){if(e&&e.consume(t),this.editing=!1,this.treeElementInternal.setDisableSelectFocus(!1),this.treeElementInternal.listItemElement.classList.remove("watch-expression-editing"),this.textPrompt){this.textPrompt.detach();const e=t?this.expressionInternal:this.textPrompt.text();this.textPrompt=void 0,this.element.removeChildren(),this.updateExpression(e)}}dblClickOnWatchExpression(e){e.consume(),this.isEditing()||this.startEditing()}updateExpression(e){this.expressionInternal&&this.expandController.stopWatchSectionsWithId(this.expressionInternal),this.expressionInternal=e,this.update(),this.dispatchEventToListeners("ExpressionUpdated",this)}deleteWatchExpression(e){e.consume(!0),this.updateExpression(null)}createWatchExpression(e,t){this.result=e||null,this.element.removeChildren();const i=this.treeElementInternal;if(this.createWatchExpressionTreeElement(e,t),i&&i.parent){const e=i.parent,t=e.indexOfChild(i);e.removeChild(i),e.insertChild(this.treeElementInternal,t)}this.treeElementInternal.select()}createWatchExpressionHeader(e,i){const o=this.element.createChild("div","watch-expression-header"),n=new I.Button.Button;n.variant="icon",n.iconName="bin",n.className="watch-expression-delete-button",n.jslogContext="delete-watch-expression",n.size="SMALL",t.Tooltip.Tooltip.install(n,kn(yn.deleteWatchExpression)),n.addEventListener("click",this.deleteWatchExpression.bind(this),!1),n.addEventListener("keydown",(e=>{"Enter"===e.key&&this.deleteWatchExpression(e)}));const r=o.createChild("div","watch-expression-title tree-element-title");if(r.appendChild(n),this.nameElement=y.ObjectPropertiesSection.ObjectPropertiesSection.createNameElement(this.expressionInternal),t.Tooltip.Tooltip.install(this.nameElement,this.expressionInternal),Boolean(i)||!e)this.valueElement=document.createElement("span"),this.valueElement.classList.add("watch-expression-error"),this.valueElement.classList.add("value"),r.classList.add("dimmed"),this.valueElement.textContent=kn(yn.notAvailable),void 0!==i&&void 0!==i.exception&&void 0!==i.exception.description&&t.Tooltip.Tooltip.install(this.valueElement,i.exception.description);else{const t=y.ObjectPropertiesSection.ObjectPropertiesSection.createPropertyValueWithCustomSupport(e,Boolean(i),!1,r,this.linkifier);this.valueElement=t.element}const s=document.createElement("span");return s.classList.add("watch-expressions-separator"),s.textContent=": ",r.append(this.nameElement,s,this.valueElement),o}createWatchExpressionTreeElement(e,i){const o=this.createWatchExpressionHeader(e,i);!i&&e&&e.hasChildren&&!e.customPreview()?(o.classList.add("watch-expression-object-header"),this.treeElementInternal=new y.ObjectPropertiesSection.RootElement(e,this.linkifier),this.expandController.watchSection(this.expressionInternal,this.treeElementInternal),this.treeElementInternal.toggleOnClick=!1,this.treeElementInternal.listItemElement.addEventListener("click",this.onSectionClick.bind(this),!1),this.treeElementInternal.listItemElement.addEventListener("dblclick",this.dblClickOnWatchExpression.bind(this))):(o.addEventListener("dblclick",this.dblClickOnWatchExpression.bind(this)),this.treeElementInternal=new t.TreeOutline.TreeElement),this.treeElementInternal.title=this.element,this.treeElementInternal.listItemElement.classList.add("watch-expression-tree-item"),this.treeElementInternal.listItemElement.addEventListener("keydown",(e=>{"Enter"!==e.key||this.isEditing()?"Delete"!==e.key||this.isEditing()||this.deleteWatchExpression(e):(this.startEditing(),e.consume(!0))}))}onSectionClick(e){e.consume(!0);1===e.detail?this.preventClickTimeout=window.setTimeout(function(){if(!this.treeElementInternal)return;this.treeElementInternal.expanded?this.treeElementInternal.collapse():this.editing||this.treeElementInternal.expand()}.bind(this),333):void 0!==this.preventClickTimeout&&(window.clearTimeout(this.preventClickTimeout),this.preventClickTimeout=void 0)}promptKeyDown(e){const t=l.KeyboardUtilities.isEscKey(e);("Enter"===e.key||t)&&this.finishEditing(e,t)}populateContextMenu(e,i){this.isEditing()||e.editSection().appendItem(kn(yn.deleteWatchExpression),this.updateExpression.bind(this,null),{jslogContext:"delete-watch-expression"}),this.isEditing()||!this.result||"number"!==this.result.type&&"string"!==this.result.type||e.clipboardSection().appendItem(kn(yn.copyValue),this.copyValueButtonClicked.bind(this),{jslogContext:"copy-watch-expression-value"});const o=t.UIUtils.deepElementFromEvent(i);o&&this.valueElement.isSelfOrAncestor(o)&&this.result&&e.appendApplicableItems(this.result)}copyValueButtonClicked(){c.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.valueElement.textContent)}static watchObjectGroupId="watch-group"}var Mn=Object.freeze({__proto__:null,WatchExpressionsSidebarPane:En,WatchExpression:Ln});export{W as AddSourceMapURLDialog,G as BreakpointEditDialog,He as CSSPlugin,oe as CallStackSidebarPane,ue as CategorizedBreakpointL10n,ke as CoveragePlugin,Xe as DebuggerPausedMessage,Do as DebuggerPlugin,yt as EditingLocationHistoryManager,Ao as FilePathScoreFunction,Vo as FilteredUISourceCodeListProvider,zo as GoToLineQuickOpen,Xo as InplaceFormatterEditorAction,vt as NavigatorView,Yo as OpenFileQuickOpen,nn as OutlineQuickOpen,pe as Plugin,Ot as ResourceOriginPlugin,un as ScopeChainSidebarPane,nt as SearchSourcesView,$t as SnippetsPlugin,Cn as SourcesNavigator,qi as SourcesPanel,tt as SourcesSearchScope,Li as SourcesView,Si as TabbedEditorContainer,Ai as ThreadsSidebarPane,ai as UISourceCodeFrame,Mn as WatchExpressionsSidebarPane};
