import*as e from"../../third_party/i18n/i18n.js";import*as t from"../root/root.js";import*as o from"../platform/platform.js";let n=null;class r{locale;lookupClosestDevToolsLocale;constructor(e){this.lookupClosestDevToolsLocale=e.lookupClosestDevToolsLocale,"browserLanguage"===e.settingLanguage?this.locale=e.navigatorLanguage||"en-US":this.locale=e.settingLanguage,this.locale=this.lookupClosestDevToolsLocale(this.locale)}static instance(e={create:!1}){if(!n&&!e.create)throw new Error("No LanguageSelector instance exists yet.");return e.create&&(n=new r(e.data)),n}static removeInstance(){n=null}forceFallbackLocale(){this.locale="en-US"}languageIsSupportedByDevTools(e){return s(e,this.lookupClosestDevToolsLocale(e))}}function s(e,t){const o=new Intl.Locale(e),n=new Intl.Locale(t);return o.language===n.language}var i=Object.freeze({__proto__:null,DevToolsLocale:r,localeLanguagesMatch:s});const a="@HOST@/remote/serve_file/@VERSION@/core/i18n/locales/@LOCALE@.json",l="./locales/@LOCALE@.json",c=new e.I18n.I18n(["af","am","ar","as","az","be","bg","bn","bs","ca","cs","cy","da","de","el","en-GB","es-419","es","et","eu","fa","fi","fil","fr-CA","fr","gl","gu","he","hi","hr","hu","hy","id","is","it","ja","ka","kk","km","kn","ko","ky","lo","lt","lv","mk","ml","mn","mr","ms","my","ne","nl","no","or","pa","pl","pt-PT","pt","ro","ru","si","sk","sl","sq","sr-Latn","sr","sv","sw","ta","te","th","tr","uk","ur","uz","vi","zh-HK","zh-TW","zu","en-US","zh"],"en-US"),u=new Set(["en-US","zh"]);function g(e,t,o={}){return e.getLocalizedStringSetFor(r.instance().locale).getLocalizedString(t,o)}function f(e,t){return c.registerFileStrings(e,t)}var m=Object.freeze({__proto__:null,lookupClosestSupportedDevToolsLocale:function(e){return c.lookupClosestSupportedLocale(e)},getAllSupportedDevToolsLocales:function(){return[...c.supportedLocales]},fetchAndRegisterLocaleData:async function(e,o=self.location.toString()){const n=fetch(function(e,o){const n=t.Runtime.getRemoteBase(o);if(n&&n.version&&!u.has(e))return a.replace("@HOST@","devtools://devtools").replace("@VERSION@",n.version).replace("@LOCALE@",e);const r=l.replace("@LOCALE@",e);return new URL(r,import.meta.url).toString()}(e,o)).then((e=>e.json())),r=new Promise(((e,t)=>window.setTimeout((()=>t(new Error("timed out fetching locale"))),5e3))),s=await Promise.race([r,n]);c.registerLocaleData(e,s)},hasLocaleDataForTest:function(e){return c.hasLocaleDataForTest(e)},resetLocaleDataForTest:function(){c.resetLocaleDataForTest()},getLazilyComputedLocalizedString:function(e,t,o={}){return()=>g(e,t,o)},getLocalizedString:g,registerUIStrings:f,getFormatLocalizedString:function(e,t,o){const n=e.getLocalizedStringSetFor(r.instance().locale).getMessageFormatterFor(t),s=document.createElement("span");for(const e of n.getAst())if(1===e.type){const t=o[e.value];t&&s.append(t)}else"value"in e&&s.append(String(e.value));return s},serializeUIString:function(e,t={}){const o={string:e,values:t};return JSON.stringify(o)},deserializeUIString:function(e){return e?JSON.parse(e):{string:"",values:{}}},lockedString:function(e){return e},lockedLazyString:function(e){return()=>e},getLocalizedLanguageRegion:function(e,t){const o=new Intl.Locale(e),{language:n,baseName:r}=o,s=n===new Intl.Locale(t.locale).language?"en":r,i=new Intl.DisplayNames([t.locale],{type:"language"}).of(n),a=new Intl.DisplayNames([s],{type:"language"}).of(n);let l="",c="";if(o.region){l=` (${new Intl.DisplayNames([t.locale],{type:"region",style:"short"}).of(o.region)})`,c=` (${new Intl.DisplayNames([s],{type:"region",style:"short"}).of(o.region)})`}return`${i}${l} - ${a}${c}`}});const d={fmms:"{PH1} μs",fms:"{PH1} ms",fs:"{PH1} s",fmin:"{PH1} min",fhrs:"{PH1} hrs",fdays:"{PH1} days"},p=f("core/i18n/time-utilities.ts",d),L=g.bind(void 0,p);const S=function(e,t){if(!isFinite(e))return"-";if(0===e)return"0";if(t&&e<.1)return L(d.fmms,{PH1:(1e3*e).toFixed(0)});if(t&&e<1e3)return L(d.fms,{PH1:e.toFixed(2)});if(e<1e3)return L(d.fms,{PH1:e.toFixed(0)});const o=e/1e3;if(o<60)return L(d.fs,{PH1:o.toFixed(2)});const n=o/60;if(n<60)return L(d.fmin,{PH1:n.toFixed(1)});const r=n/60;if(r<24)return L(d.fhrs,{PH1:r.toFixed(1)});return L(d.fdays,{PH1:(r/24).toFixed(1)})};var h=Object.freeze({__proto__:null,preciseMillisToString:function(e,t){return t=t||0,L(d.fms,{PH1:e.toFixed(t)})},formatMicroSecondsTime:function(e){return S(o.Timing.microSecondsToMilliSeconds(e),!0)},formatMicroSecondsAsSeconds:function(e){const t=o.Timing.microSecondsToMilliSeconds(e),n=o.Timing.milliSecondsToSeconds(t);return L(d.fs,{PH1:n.toFixed(2)})},millisToString:S,secondsToString:function(e,t){return isFinite(e)?S(1e3*e,t):"-"}});export{i as DevToolsLocale,h as TimeUtilities,m as i18n};
