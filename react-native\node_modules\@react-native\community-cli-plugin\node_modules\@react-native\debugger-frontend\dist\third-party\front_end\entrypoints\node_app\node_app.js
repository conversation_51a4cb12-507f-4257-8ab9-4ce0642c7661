import"../shell/shell.js";import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";import*as o from"../../core/root/root.js";import*as i from"../main/main.js";import*as s from"../../core/host/host.js";import*as r from"../../ui/legacy/components/utils/utils.js";import*as a from"../../core/sdk/sdk.js";const c={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},d=t.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",c),l=t.i18n.getLazilyComputedLocalizedString.bind(void 0,d);let g;async function h(){return g||(g=await import("../../panels/mobile_throttling/mobile_throttling.js")),g}n.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:l(c.throttling),commandPrompt:l(c.showThrottling),order:35,loadView:async()=>new((await h()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:l(c.goOffline),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:l(c.enableSlowGThrottling),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:l(c.enableFastGThrottling),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:l(c.goOnline),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const p={profiler:"Profiler",showProfiler:"Show Profiler",startStopRecording:"Start/stop recording",showRecentTimelineSessions:"Show recent timeline sessions",record:"Record",stop:"Stop",startProfilingAndReloadPage:"Start profiling and reload page"},w=t.i18n.registerUIStrings("panels/js_profiler/js_profiler-meta.ts",p),m=t.i18n.getLazilyComputedLocalizedString.bind(void 0,w);let f,v;async function u(){return v||(v=await import("../../panels/profiler/profiler.js")),v}async function y(){return f||(f=await import("../../panels/timeline/timeline.js")),f}function C(e){return void 0===f?[]:e(f)}n.ViewManager.registerViewExtension({location:"panel",id:"js-profiler",title:m(p.profiler),commandPrompt:m(p.showProfiler),order:65,persistence:"permanent",experiment:"js-profiler-temporarily-enable",loadView:async()=>(await u()).ProfilesPanel.JSProfilerPanel.instance()}),n.ActionRegistration.registerActionExtension({actionId:"profiler.js-toggle-recording",category:"JAVASCRIPT_PROFILER",title:m(p.startStopRecording),iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===v?[]:(e=>[e.ProfilesPanel.JSProfilerPanel])(v),loadActionDelegate:async()=>(await u()).ProfilesPanel.JSProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await y()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:m(p.showRecentTimelineSessions),contextTypes:()=>C((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>C((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await y()).TimelinePanel.ActionDelegate),options:[{value:!0,title:m(p.record)},{value:!1,title:m(p.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>C((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:m(p.startProfilingAndReloadPage),loadActionDelegate:async()=>new((await y()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]});const k={main:"Main",nodejsS:"Node.js: {PH1}"},T=t.i18n.registerUIStrings("entrypoints/node_app/NodeMain.ts",k),x=t.i18n.getLocalizedString.bind(void 0,T);let I;class D{static instance(e={forceNew:null}){const{forceNew:t}=e;return I&&!t||(I=new D),I}async run(){s.userMetrics.actionTaken(s.UserMetrics.Action.ConnectToNodeJSFromFrontend),a.Connections.initMainConnection((async()=>{a.TargetManager.TargetManager.instance().createTarget("main",x(k.main),a.Target.Type.Browser,null).setInspectedURL("Node.js")}),r.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost)}}class E extends a.SDKModel.SDKModel{#e;#t;#n;#o;#i;constructor(e){super(e),this.#e=e.targetManager(),this.#t=e,this.#n=e.targetAgent(),this.#o=new Map,this.#i=new Map,e.registerTargetDispatcher(this),this.#n.invoke_setDiscoverTargets({discover:!0}),s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#s,this),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!0)}#s({data:e}){const t=[];for(const n of e.networkDiscoveryConfig){const e=n.split(":"),o=parseInt(e[1],10);e[0]&&o&&t.push({host:e[0],port:o})}this.#n.invoke_setRemoteLocations({locations:t})}dispose(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#s,this);for(const e of this.#o.keys())this.detachedFromTarget({sessionId:e})}targetCreated({targetInfo:e}){"node"!==e.type||e.attached||this.#n.invoke_attachToTarget({targetId:e.targetId,flatten:!1})}targetInfoChanged(e){}targetDestroyed(e){}attachedToTarget({sessionId:e,targetInfo:t}){const n=x(k.nodejsS,{PH1:t.url}),o=new S(this.#n,e);this.#i.set(e,o);const i=this.#e.createTarget(t.targetId,n,a.Target.Type.Node,this.#t,void 0,void 0,o);this.#o.set(e,i),i.runtimeAgent().invoke_runIfWaitingForDebugger()}detachedFromTarget({sessionId:e}){const t=this.#o.get(e);t&&t.dispose("target terminated"),this.#o.delete(e),this.#i.delete(e)}receivedMessageFromTarget({sessionId:e,message:t}){const n=this.#i.get(e),o=n?n.onMessage:null;o&&o.call(null,t)}targetCrashed(e){}}class S{#n;#r;onMessage;#a;constructor(e,t){this.#n=e,this.#r=t,this.onMessage=null,this.#a=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#a=e}sendRawMessage(e){this.#n.invoke_sendMessageToTarget({message:e,sessionId:this.#r})}async disconnect(){this.#a&&this.#a.call(null,"force disconnect"),this.#a=null,this.onMessage=null,await this.#n.invoke_detachFromTarget({sessionId:this.#r})}}a.SDKModel.SDKModel.register(E,{capabilities:32,autostart:!0});const A=new CSSStyleSheet;A.replaceSync(".add-network-target-button{margin:10px 25px;align-self:center}.network-discovery-list{flex:none;max-width:600px;max-height:202px;margin:20px 0 5px}.network-discovery-list-empty{flex:auto;height:30px;display:flex;align-items:center;justify-content:center}.network-discovery-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.network-discovery-value{flex:3 1 0}.list-item .network-discovery-value{white-space:nowrap;text-overflow:ellipsis;user-select:none;color:var(--sys-color-on-surface);overflow:hidden}.network-discovery-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px;align-items:center}.network-discovery-edit-row input{width:100%;text-align:inherit}.network-discovery-footer{margin:0;overflow:hidden;max-width:500px;padding:3px}.network-discovery-footer > *{white-space:pre-wrap}.node-panel{align-items:center;justify-content:flex-start;overflow-y:auto}.network-discovery-view{min-width:400px;text-align:left}:host-context(.node-frontend) .network-discovery-list-empty{height:40px}:host-context(.node-frontend) .network-discovery-list-item{padding:3px 15px;height:40px}.node-panel-center{max-width:600px;padding-top:50px;text-align:center}.node-panel-logo{width:400px;margin-bottom:50px}:host-context(.node-frontend) .network-discovery-edit-row input{height:30px;padding-left:5px}:host-context(.node-frontend) .network-discovery-edit-row{margin:6px 9px}\n/*# sourceURL=nodeConnectionsPanel.css */\n");const P={nodejsDebuggingGuide:"Node.js debugging guide",specifyNetworkEndpointAnd:"Specify network endpoint and DevTools will connect to it automatically. Read {PH1} to learn more.",noConnectionsSpecified:"No connections specified",addConnection:"Add connection",networkAddressEgLocalhost:"Network address (e.g. localhost:9229)"},b=t.i18n.registerUIStrings("entrypoints/node_app/NodeConnectionsPanel.ts",P),M=t.i18n.getLocalizedString.bind(void 0,b);class R extends n.Panel.Panel{#c;#d;constructor(){super("node-connection"),this.contentElement.classList.add("node-panel");const e=this.contentElement.createChild("div","node-panel-center");e.createChild("img","node-panel-logo").src="https://nodejs.org/static/images/logos/nodejs-new-pantone-black.svg",s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#s,this),this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!0),this.#d=new F((e=>{this.#c.networkDiscoveryConfig=e,s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesDiscoveryConfig(this.#c)})),this.#d.show(e)}#s({data:e}){this.#c=e,this.#d.discoveryConfigChanged(this.#c.networkDiscoveryConfig)}wasShown(){super.wasShown(),this.registerCSSFiles([A])}}class F extends n.Widget.VBox{#l;#g;#h;#p;constructor(e){super(),this.#l=e,this.element.classList.add("network-discovery-view");const o=this.element.createChild("div","network-discovery-footer"),i=n.XLink.XLink.create("https://nodejs.org/en/docs/inspector/",M(P.nodejsDebuggingGuide),void 0,void 0,"node-js-debugging");o.appendChild(t.i18n.getFormatLocalizedString(b,P.specifyNetworkEndpointAnd,{PH1:i})),this.#g=new n.ListWidget.ListWidget(this),this.#g.element.classList.add("network-discovery-list");const s=document.createElement("div");s.classList.add("network-discovery-list-empty"),s.textContent=M(P.noConnectionsSpecified),this.#g.setEmptyPlaceholder(s),this.#g.show(this.element),this.#h=null;const r=n.UIUtils.createTextButton(M(P.addConnection),this.#w.bind(this),{className:"add-network-target-button",primary:!0});this.element.appendChild(r),this.#p=[],this.element.classList.add("node-frontend")}#m(){const e=this.#p.map((e=>e.address));this.#l.call(null,e)}#w(){this.#g.addNewItem(this.#p.length,{address:"",port:""})}discoveryConfigChanged(e){this.#p=[],this.#g.clear();for(const t of e){const e={address:t,port:""};this.#p.push(e),this.#g.appendItem(e,!0)}}renderItem(e,t){const n=document.createElement("div");return n.classList.add("network-discovery-list-item"),n.createChild("div","network-discovery-value network-discovery-address").textContent=e.address,n}removeItemRequested(e,t){this.#p.splice(t,1),this.#g.removeItem(t),this.#m()}commitEdit(e,t,n){e.address=t.control("address").value.trim(),n&&this.#p.push(e),this.#m()}beginEdit(e){const t=this.#f();return t.control("address").value=e.address,t}#f(){if(this.#h)return this.#h;const e=new n.ListWidget.Editor;this.#h=e;const t=e.contentElement().createChild("div","network-discovery-edit-row"),o=e.createInput("address","text",M(P.networkAddressEgLocalhost),(function(e,t,n){const o=n.value.trim().match(/^([a-zA-Z0-9\.\-_]+):(\d+)$/);if(!o)return{valid:!1,errorMessage:void 0};return{valid:parseInt(o[2],10)<=65535,errorMessage:void 0}}));return t.createChild("div","network-discovery-value network-discovery-address").appendChild(o),e}wasShown(){super.wasShown(),this.#g.registerCSSFiles([A])}}const N={connection:"Connection",node:"node",showConnection:"Show Connection",networkTitle:"Node",showNode:"Show Node"},j=t.i18n.registerUIStrings("entrypoints/node_app/node_app.ts",N),L=t.i18n.getLazilyComputedLocalizedString.bind(void 0,j);let H;n.ViewManager.registerViewExtension({location:"panel",id:"node-connection",title:L(N.connection),commandPrompt:L(N.showConnection),order:0,loadView:async()=>new R,tags:[L(N.node)]}),n.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:L(N.networkTitle),commandPrompt:L(N.showNode),order:2,persistence:"permanent",loadView:async()=>(await async function(){return H||(H=await import("../../panels/sources/sources.js")),H}()).SourcesNavigator.NetworkNavigatorView.instance()}),self.runtime=o.Runtime.Runtime.instance({forceNew:!0}),e.Runnable.registerEarlyInitializationRunnable(D.instance),new i.MainImpl.MainImpl;
