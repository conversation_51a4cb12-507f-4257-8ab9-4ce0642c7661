{"version": 3, "sources": ["GestureButtons.tsx"], "names": ["RawButton", "GestureHandlerButton", "shouldCancelWhenOutside", "shouldActivateOnStart", "InnerBaseButton", "React", "Component", "constructor", "props", "nativeEvent", "state", "oldState", "pointerInside", "active", "State", "ACTIVE", "lastActive", "onActiveStateChange", "longPressDetected", "CANCELLED", "onPress", "Platform", "OS", "BEGAN", "onLongPress", "longPressTimeout", "setTimeout", "delayLongPress", "undefined", "clearTimeout", "END", "FAILED", "e", "onHandlerStateChange", "handleEvent", "onGestureEvent", "render", "rippleColor", "style", "rest", "innerRef", "cursor", "BaseButton", "forwardRef", "ref", "AnimatedBaseButton", "Animated", "createAnimatedComponent", "btnStyles", "StyleSheet", "create", "underlay", "position", "left", "right", "bottom", "top", "InnerRectButton", "opacity", "setValue", "activeOpacity", "Value", "children", "resolvedStyle", "flatten", "backgroundColor", "underlayColor", "borderRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "RectButton", "InnerBorderlessButton", "borderless", "BorderlessButton"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA;;AAEA;;AACA;;AACA;;;;;;;;;;;;AAgBO,MAAMA,SAAS,GAAG,kCAAoBC,6BAApB,EAA0C;AACjEC,EAAAA,uBAAuB,EAAE,KADwC;AAEjEC,EAAAA,qBAAqB,EAAE;AAF0C,CAA1C,CAAlB;;;AAKP,MAAMC,eAAN,SAA8BC,KAAK,CAACC,SAApC,CAAsE;AASpEC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA;;AAAA;;AAAA,yCAMd,CAAC;AACrBC,MAAAA;AADqB,KAAD,KAE0C;AAC9D,YAAM;AAAEC,QAAAA,KAAF;AAASC,QAAAA,QAAT;AAAmBC,QAAAA;AAAnB,UAAqCH,WAA3C;AACA,YAAMI,MAAM,GAAGD,aAAa,IAAIF,KAAK,KAAKI,aAAMC,MAAhD;;AAEA,UAAIF,MAAM,KAAK,KAAKG,UAAhB,IAA8B,KAAKR,KAAL,CAAWS,mBAA7C,EAAkE;AAChE,aAAKT,KAAL,CAAWS,mBAAX,CAA+BJ,MAA/B;AACD;;AAED,UACE,CAAC,KAAKK,iBAAN,IACAP,QAAQ,KAAKG,aAAMC,MADnB,IAEAL,KAAK,KAAKI,aAAMK,SAFhB,IAGA,KAAKH,UAHL,IAIA,KAAKR,KAAL,CAAWY,OALb,EAME;AACA,aAAKZ,KAAL,CAAWY,OAAX,CAAmBR,aAAnB;AACD;;AAED,UACE,CAAC,KAAKI,UAAN,IACA;AACAN,MAAAA,KAAK,MAAMW,sBAASC,EAAT,KAAgB,SAAhB,GAA4BR,aAAMC,MAAlC,GAA2CD,aAAMS,KAAvD,CAFL,IAGAX,aAJF,EAKE;AACA,aAAKM,iBAAL,GAAyB,KAAzB;;AACA,YAAI,KAAKV,KAAL,CAAWgB,WAAf,EAA4B;AAC1B,eAAKC,gBAAL,GAAwBC,UAAU,CAChC,KAAKF,WAD2B,EAEhC,KAAKhB,KAAL,CAAWmB,cAFqB,CAAlC;AAID;AACF,OAbD,MAaO,KACL;AACAjB,MAAAA,KAAK,KAAKI,aAAMC,MAAhB,IACA,CAACH,aADD,IAEA,KAAKa,gBAAL,KAA0BG,SAJrB,EAKL;AACAC,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD,OARM,MAQA,KACL;AACA,WAAKH,gBAAL,KAA0BG,SAA1B,KACClB,KAAK,KAAKI,aAAMgB,GAAhB,IACCpB,KAAK,KAAKI,aAAMK,SADjB,IAECT,KAAK,KAAKI,aAAMiB,MAHlB,CAFK,EAML;AACAF,QAAAA,YAAY,CAAC,KAAKJ,gBAAN,CAAZ;AACA,aAAKA,gBAAL,GAAwBG,SAAxB;AACD;;AAED,WAAKZ,UAAL,GAAkBH,MAAlB;AACD,KA3DmC;;AAAA,yCA6Dd,MAAM;AAAA;;AAC1B,WAAKK,iBAAL,GAAyB,IAAzB;AACA,mDAAKV,KAAL,EAAWgB,WAAX;AACD,KAhEmC;;AAAA,kDAuElCQ,CAD6B,IAE1B;AAAA;;AACH,oDAAKxB,KAAL,EAAWyB,oBAAX,mGAAkCD,CAAlC;AACA,WAAKE,WAAL,CAAiBF,CAAjB;AACD,KA3EmC;;AAAA,4CA8ElCA,CADuB,IAEpB;AAAA;;AACH,oDAAKxB,KAAL,EAAW2B,cAAX,mGAA4BH,CAA5B;AACA,WAAKE,WAAL,CACEF,CADF,EAFG,CAIA;AACJ,KApFmC;;AAElC,SAAKhB,UAAL,GAAkB,KAAlB;AACA,SAAKE,iBAAL,GAAyB,KAAzB;AACD;;AAkFDkB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEC,MAAAA,WAAF;AAAeC,MAAAA,KAAf;AAAsB,SAAGC;AAAzB,QAAkC,KAAK/B,KAA7C;AAEA,wBACE,oBAAC,SAAD;AACE,MAAA,GAAG,EAAE,KAAKA,KAAL,CAAWgC,QADlB;AAEE,MAAA,WAAW,EAAE,+BAAaH,WAAb,CAFf;AAGE,MAAA,KAAK,EAAE,CAACC,KAAD,EAAQjB,sBAASC,EAAT,KAAgB,KAAhB,IAAyB;AAAEmB,QAAAA,MAAM,EAAEb;AAAV,OAAjC;AAHT,OAIMW,IAJN;AAKE,MAAA,cAAc,EAAE,KAAKJ,cALvB;AAME,MAAA,oBAAoB,EAAE,KAAKF;AAN7B,OADF;AAUD;;AA5GmE;;gBAAhE7B,e,kBACkB;AACpBuB,EAAAA,cAAc,EAAE;AADI,C;;AA8GjB,MAAMe,UAAU,gBAAGrC,KAAK,CAACsC,UAAN,CAGxB,CAACnC,KAAD,EAAQoC,GAAR,kBAAgB,oBAAC,eAAD;AAAiB,EAAA,QAAQ,EAAEA;AAA3B,GAAoCpC,KAApC,EAHQ,CAAnB;;;AAKP,MAAMqC,kBAAkB,GAAGC,sBAASC,uBAAT,CAAiCL,UAAjC,CAA3B;;AAEA,MAAMM,SAAS,GAAGC,wBAAWC,MAAX,CAAkB;AAClCC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,QAAQ,EAAE,UADF;AAERC,IAAAA,IAAI,EAAE,CAFE;AAGRC,IAAAA,KAAK,EAAE,CAHC;AAIRC,IAAAA,MAAM,EAAE,CAJA;AAKRC,IAAAA,GAAG,EAAE;AALG;AADwB,CAAlB,CAAlB;;AAUA,MAAMC,eAAN,SAA8BpD,KAAK,CAACC,SAApC,CAAsE;AAQpEC,EAAAA,WAAW,CAACC,KAAD,EAAyB;AAClC,UAAMA,KAAN;;AADkC;;AAAA,iDAKLK,MAAD,IAAqB;AAAA;;AACjD,UAAIQ,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAKoC,OAAL,CAAaC,QAAb,CAAsB9C,MAAM,GAAG,KAAKL,KAAL,CAAWoD,aAAd,GAA+B,CAA3D;AACD;;AAED,oDAAKpD,KAAL,EAAWS,mBAAX,mGAAiCJ,MAAjC;AACD,KAXmC;;AAElC,SAAK6C,OAAL,GAAe,IAAIZ,sBAASe,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDzB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAE0B,MAAAA,QAAF;AAAYxB,MAAAA,KAAZ;AAAmB,SAAGC;AAAtB,QAA+B,KAAK/B,KAA1C;;AAEA,UAAMuD,aAAa,GAAGd,wBAAWe,OAAX,CAAmB1B,KAAnB,aAAmBA,KAAnB,cAAmBA,KAAnB,GAA4B,EAA5B,CAAtB;;AAEA,wBACE,oBAAC,UAAD,eACMC,IADN;AAEE,MAAA,GAAG,EAAE,KAAK/B,KAAL,CAAWgC,QAFlB;AAGE,MAAA,KAAK,EAAEuB,aAHT;AAIE,MAAA,mBAAmB,EAAE,KAAK9C;AAJ5B,qBAKE,oBAAC,qBAAD,CAAU,IAAV;AACE,MAAA,KAAK,EAAE,CACL+B,SAAS,CAACG,QADL,EAEL;AACEO,QAAAA,OAAO,EAAE,KAAKA,OADhB;AAEEO,QAAAA,eAAe,EAAE,KAAKzD,KAAL,CAAW0D,aAF9B;AAGEC,QAAAA,YAAY,EAAEJ,aAAa,CAACI,YAH9B;AAIEC,QAAAA,mBAAmB,EAAEL,aAAa,CAACK,mBAJrC;AAKEC,QAAAA,oBAAoB,EAAEN,aAAa,CAACM,oBALtC;AAMEC,QAAAA,sBAAsB,EAAEP,aAAa,CAACO,sBANxC;AAOEC,QAAAA,uBAAuB,EAAER,aAAa,CAACQ;AAPzC,OAFK;AADT,MALF,EAmBGT,QAnBH,CADF;AAuBD;;AAjDmE;;gBAAhEL,e,kBACkB;AACpBG,EAAAA,aAAa,EAAE,KADK;AAEpBM,EAAAA,aAAa,EAAE;AAFK,C;;AAmDjB,MAAMM,UAAU,gBAAGnE,KAAK,CAACsC,UAAN,CAGxB,CAACnC,KAAD,EAAQoC,GAAR,kBAAgB,oBAAC,eAAD;AAAiB,EAAA,QAAQ,EAAEA;AAA3B,GAAoCpC,KAApC,EAHQ,CAAnB;;;AAKP,MAAMiE,qBAAN,SAAoCpE,KAAK,CAACC,SAA1C,CAAkF;AAQhFC,EAAAA,WAAW,CAACC,KAAD,EAA+B;AACxC,UAAMA,KAAN;;AADwC;;AAAA,iDAKXK,MAAD,IAAqB;AAAA;;AACjD,UAAIQ,sBAASC,EAAT,KAAgB,SAApB,EAA+B;AAC7B,aAAKoC,OAAL,CAAaC,QAAb,CAAsB9C,MAAM,GAAG,KAAKL,KAAL,CAAWoD,aAAd,GAA+B,CAA3D;AACD;;AAED,qDAAKpD,KAAL,EAAWS,mBAAX,qGAAiCJ,MAAjC;AACD,KAXyC;;AAExC,SAAK6C,OAAL,GAAe,IAAIZ,sBAASe,KAAb,CAAmB,CAAnB,CAAf;AACD;;AAUDzB,EAAAA,MAAM,GAAG;AACP,UAAM;AAAE0B,MAAAA,QAAF;AAAYxB,MAAAA,KAAZ;AAAmBE,MAAAA,QAAnB;AAA6B,SAAGD;AAAhC,QAAyC,KAAK/B,KAApD;AAEA,wBACE,oBAAC,kBAAD,eACM+B,IADN;AAEE;AACA;AACA,MAAA,QAAQ,EAAEC,QAJZ;AAKE,MAAA,mBAAmB,EAAE,KAAKvB,mBAL5B;AAME,MAAA,KAAK,EAAE,CAACqB,KAAD,EAAQjB,sBAASC,EAAT,KAAgB,KAAhB,IAAyB;AAAEoC,QAAAA,OAAO,EAAE,KAAKA;AAAhB,OAAjC;AANT,QAOGI,QAPH,CADF;AAWD;;AAnC+E;;gBAA5EW,qB,kBACkB;AACpBb,EAAAA,aAAa,EAAE,GADK;AAEpBc,EAAAA,UAAU,EAAE;AAFQ,C;;AAqCjB,MAAMC,gBAAgB,gBAAGtE,KAAK,CAACsC,UAAN,CAG9B,CAACnC,KAAD,EAAQoC,GAAR,kBAAgB,oBAAC,qBAAD;AAAuB,EAAA,QAAQ,EAAEA;AAAjC,GAA0CpC,KAA1C,EAHc,CAAzB", "sourcesContent": ["import * as React from 'react';\nimport { Animated, Platform, processColor, StyleSheet } from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\nimport GestureHandlerButton from './GestureHandlerButton';\nimport { State } from '../State';\n\nimport {\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport type { NativeViewGestureHandlerPayload } from '../handlers/GestureHandlerEventPayload';\nimport type {\n  BaseButtonWithRefProps,\n  BaseButtonProps,\n  RectButtonWithRefProps,\n  RectButtonProps,\n  BorderlessButtonWithRefProps,\n  BorderlessButtonProps,\n} from './GestureButtonsProps';\n\nexport const RawButton = createNativeWrapper(GestureHandlerButton, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: false,\n});\n\nclass InnerBaseButton extends React.Component<BaseButtonWithRefProps> {\n  static defaultProps = {\n    delayLongPress: 600,\n  };\n\n  private lastActive: boolean;\n  private longPressTimeout: ReturnType<typeof setTimeout> | undefined;\n  private longPressDetected: boolean;\n\n  constructor(props: BaseButtonProps) {\n    super(props);\n    this.lastActive = false;\n    this.longPressDetected = false;\n  }\n\n  private handleEvent = ({\n    nativeEvent,\n  }: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>) => {\n    const { state, oldState, pointerInside } = nativeEvent;\n    const active = pointerInside && state === State.ACTIVE;\n\n    if (active !== this.lastActive && this.props.onActiveStateChange) {\n      this.props.onActiveStateChange(active);\n    }\n\n    if (\n      !this.longPressDetected &&\n      oldState === State.ACTIVE &&\n      state !== State.CANCELLED &&\n      this.lastActive &&\n      this.props.onPress\n    ) {\n      this.props.onPress(pointerInside);\n    }\n\n    if (\n      !this.lastActive &&\n      // NativeViewGestureHandler sends different events based on platform\n      state === (Platform.OS !== 'android' ? State.ACTIVE : State.BEGAN) &&\n      pointerInside\n    ) {\n      this.longPressDetected = false;\n      if (this.props.onLongPress) {\n        this.longPressTimeout = setTimeout(\n          this.onLongPress,\n          this.props.delayLongPress\n        );\n      }\n    } else if (\n      // Cancel longpress timeout if it's set and the finger moved out of the view\n      state === State.ACTIVE &&\n      !pointerInside &&\n      this.longPressTimeout !== undefined\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    } else if (\n      // Cancel longpress timeout if it's set and the gesture has finished\n      this.longPressTimeout !== undefined &&\n      (state === State.END ||\n        state === State.CANCELLED ||\n        state === State.FAILED)\n    ) {\n      clearTimeout(this.longPressTimeout);\n      this.longPressTimeout = undefined;\n    }\n\n    this.lastActive = active;\n  };\n\n  private onLongPress = () => {\n    this.longPressDetected = true;\n    this.props.onLongPress?.();\n  };\n\n  // Normally, the parent would execute it's handler first, then forward the\n  // event to listeners. However, here our handler is virtually only forwarding\n  // events to listeners, so we reverse the order to keep the proper order of\n  // the callbacks (from \"raw\" ones to \"processed\").\n  private onHandlerStateChange = (\n    e: HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onHandlerStateChange?.(e);\n    this.handleEvent(e);\n  };\n\n  private onGestureEvent = (\n    e: GestureEvent<NativeViewGestureHandlerPayload>\n  ) => {\n    this.props.onGestureEvent?.(e);\n    this.handleEvent(\n      e as HandlerStateChangeEvent<NativeViewGestureHandlerPayload>\n    ); // TODO: maybe it is not correct\n  };\n\n  render() {\n    const { rippleColor, style, ...rest } = this.props;\n\n    return (\n      <RawButton\n        ref={this.props.innerRef}\n        rippleColor={processColor(rippleColor)}\n        style={[style, Platform.OS === 'ios' && { cursor: undefined }]}\n        {...rest}\n        onGestureEvent={this.onGestureEvent}\n        onHandlerStateChange={this.onHandlerStateChange}\n      />\n    );\n  }\n}\n\nexport const BaseButton = React.forwardRef<\n  any,\n  Omit<BaseButtonProps, 'innerRef'>\n>((props, ref) => <InnerBaseButton innerRef={ref} {...props} />);\n\nconst AnimatedBaseButton = Animated.createAnimatedComponent(BaseButton);\n\nconst btnStyles = StyleSheet.create({\n  underlay: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    bottom: 0,\n    top: 0,\n  },\n});\n\nclass InnerRectButton extends React.Component<RectButtonWithRefProps> {\n  static defaultProps = {\n    activeOpacity: 0.105,\n    underlayColor: 'black',\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: RectButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(0);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 0);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, ...rest } = this.props;\n\n    const resolvedStyle = StyleSheet.flatten(style ?? {});\n\n    return (\n      <BaseButton\n        {...rest}\n        ref={this.props.innerRef}\n        style={resolvedStyle}\n        onActiveStateChange={this.onActiveStateChange}>\n        <Animated.View\n          style={[\n            btnStyles.underlay,\n            {\n              opacity: this.opacity,\n              backgroundColor: this.props.underlayColor,\n              borderRadius: resolvedStyle.borderRadius,\n              borderTopLeftRadius: resolvedStyle.borderTopLeftRadius,\n              borderTopRightRadius: resolvedStyle.borderTopRightRadius,\n              borderBottomLeftRadius: resolvedStyle.borderBottomLeftRadius,\n              borderBottomRightRadius: resolvedStyle.borderBottomRightRadius,\n            },\n          ]}\n        />\n        {children}\n      </BaseButton>\n    );\n  }\n}\n\nexport const RectButton = React.forwardRef<\n  any,\n  Omit<RectButtonProps, 'innerRef'>\n>((props, ref) => <InnerRectButton innerRef={ref} {...props} />);\n\nclass InnerBorderlessButton extends React.Component<BorderlessButtonWithRefProps> {\n  static defaultProps = {\n    activeOpacity: 0.3,\n    borderless: true,\n  };\n\n  private opacity: Animated.Value;\n\n  constructor(props: BorderlessButtonProps) {\n    super(props);\n    this.opacity = new Animated.Value(1);\n  }\n\n  private onActiveStateChange = (active: boolean) => {\n    if (Platform.OS !== 'android') {\n      this.opacity.setValue(active ? this.props.activeOpacity! : 1);\n    }\n\n    this.props.onActiveStateChange?.(active);\n  };\n\n  render() {\n    const { children, style, innerRef, ...rest } = this.props;\n\n    return (\n      <AnimatedBaseButton\n        {...rest}\n        // @ts-ignore We don't want `innerRef` to be accessible from public API.\n        // However in this case we need to set it indirectly on `BaseButton`, hence we use ts-ignore\n        innerRef={innerRef}\n        onActiveStateChange={this.onActiveStateChange}\n        style={[style, Platform.OS === 'ios' && { opacity: this.opacity }]}>\n        {children}\n      </AnimatedBaseButton>\n    );\n  }\n}\n\nexport const BorderlessButton = React.forwardRef<\n  any,\n  Omit<BorderlessButtonProps, 'innerRef'>\n>((props, ref) => <InnerBorderlessButton innerRef={ref} {...props} />);\n\nexport { default as PureNativeButton } from './GestureHandlerButton';\n"]}