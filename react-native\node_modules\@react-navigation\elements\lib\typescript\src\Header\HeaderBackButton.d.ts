import type { HeaderBackButtonProps } from '../types';
export default function HeaderBackButton({ disabled, allowFontScaling, backImage, label, labelStyle, labelVisible, onLabelLayout, onPress, pressColor, pressOpacity, screenLayout, tintColor: customTintColor, titleLayout, truncatedLabel, accessibilityLabel, testID, style, }: HeaderBackButtonProps): JSX.Element;
//# sourceMappingURL=HeaderBackButton.d.ts.map