{"version": 3, "names": ["formatToList", "items", "map", "key", "join", "validatePathConfig", "config", "root", "validKeys", "push", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "filter", "includes", "length", "Error", "screens", "entries", "for<PERSON>ach", "_", "value"], "sourceRoot": "../../src", "sources": ["validatePathConfig.tsx"], "mappings": ";;;;;;AAAA,MAAMA,YAAY,GAAIC,KAAe,IACnCA,KAAK,CAACC,GAAG,CAAEC,GAAG,IAAM,KAAIA,GAAI,EAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AAE5B,SAASC,kBAAkB,CAACC,MAAW,EAAe;EAAA,IAAbC,IAAI,uEAAG,IAAI;EACjE,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC;EAEjD,IAAI,CAACD,IAAI,EAAE;IACTC,SAAS,CAACC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC;EACvD;EAEA,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,CAC3CV,GAAG,IAAK,CAACK,SAAS,CAACM,QAAQ,CAACX,GAAG,CAAC,CAClC;EAED,IAAIO,WAAW,CAACK,MAAM,EAAE;IACtB,MAAM,IAAIC,KAAK,CACZ,mDAAkDhB,YAAY,CAC7DU,WAAW,CACX,qHAAoHV,YAAY,CAChIQ,SAAS,CACT,wHAAuH,CAC1H;EACH;EAEA,IAAIF,MAAM,CAACW,OAAO,EAAE;IAClBN,MAAM,CAACO,OAAO,CAACZ,MAAM,CAACW,OAAO,CAAC,CAACE,OAAO,CAAC,QAAgB;MAAA,IAAf,CAACC,CAAC,EAAEC,KAAK,CAAC;MAChD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BhB,kBAAkB,CAACgB,KAAK,EAAE,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;EACJ;AACF"}