{"version": 3, "sources": ["../../../src/utils/ora.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport oraReal, { Ora } from 'ora';\n\n// import * as Log from '../log';\nimport { env } from './env';\nimport { isInteractive } from './interactive';\n\nconst logReal = console.log;\nconst infoReal = console.info;\nconst warnReal = console.warn;\nconst errorReal = console.error;\n\nconst runningSpinners: oraReal.Ora[] = [];\n\nexport function getAllSpinners() {\n  return runningSpinners;\n}\n\n/**\n * A custom ora spinner that sends the stream to stdout in CI, non-TTY, or expo's non-interactive flag instead of stderr (the default).\n *\n * @param options\n * @returns\n */\nexport function ora(options?: oraReal.Options | string): oraReal.Ora {\n  const inputOptions = typeof options === 'string' ? { text: options } : options || {};\n  const disabled = !isInteractive() || env.EXPO_DEBUG;\n  const spinner = oraReal({\n    // Ensure our non-interactive mode emulates CI mode.\n    isEnabled: !disabled,\n    // In non-interactive mode, send the stream to stdout so it prevents looking like an error.\n    stream: disabled ? process.stdout : process.stderr,\n    ...inputOptions,\n  });\n\n  const oraStart = spinner.start.bind(spinner);\n  const oraStop = spinner.stop.bind(spinner);\n  const oraStopAndPersist = spinner.stopAndPersist.bind(spinner);\n\n  const logWrap = (method: any, args: any[]): void => {\n    oraStop();\n    method(...args);\n    spinner.start();\n  };\n\n  const wrapNativeLogs = (): void => {\n    console.log = (...args: any) => logWrap(logReal, args);\n    console.info = (...args: any) => logWrap(infoReal, args);\n    console.warn = (...args: any) => logWrap(warnReal, args);\n    console.error = (...args: any) => logWrap(errorReal, args);\n\n    runningSpinners.push(spinner);\n  };\n\n  const resetNativeLogs = (): void => {\n    console.log = logReal;\n    console.info = infoReal;\n    console.warn = warnReal;\n    console.error = errorReal;\n\n    const index = runningSpinners.indexOf(spinner);\n    if (index >= 0) {\n      runningSpinners.splice(index, 1);\n    }\n  };\n\n  spinner.start = (text): Ora => {\n    wrapNativeLogs();\n    return oraStart(text);\n  };\n\n  spinner.stopAndPersist = (options): Ora => {\n    const result = oraStopAndPersist(options);\n    resetNativeLogs();\n    return result;\n  };\n\n  spinner.stop = (): Ora => {\n    const result = oraStop();\n    resetNativeLogs();\n    return result;\n  };\n\n  // Always make the central logging module aware of the current spinner\n  // Log.setSpinner(spinner);\n\n  return spinner;\n}\n\n/**\n * Create a unified section spinner.\n *\n * @param title\n * @returns\n */\nexport function logNewSection(title: string) {\n  const spinner = ora(chalk.bold(title));\n  // Prevent the spinner from clashing with debug logs\n  spinner.start();\n  return spinner;\n}\n"], "names": ["getAllSpinners", "logNewSection", "ora", "logReal", "console", "log", "infoReal", "info", "warnReal", "warn", "errorReal", "error", "runningSpinners", "options", "inputOptions", "text", "disabled", "isInteractive", "env", "EXPO_DEBUG", "spinner", "oraReal", "isEnabled", "stream", "process", "stdout", "stderr", "oraStart", "start", "bind", "oraStop", "stop", "oraStopAndPersist", "stopAndPersist", "logWrap", "method", "args", "wrapNativeLogs", "push", "resetNativeLogs", "index", "indexOf", "splice", "result", "title", "chalk", "bold"], "mappings": ";;;;;;;;;;;IAcgBA,cAAc;eAAdA;;IAiFAC,aAAa;eAAbA;;IAvEAC,GAAG;eAAHA;;;;gEAxBE;;;;;;;gEACW;;;;;;qBAGT;6BACU;;;;;;AAE9B,MAAMC,UAAUC,QAAQC,GAAG;AAC3B,MAAMC,WAAWF,QAAQG,IAAI;AAC7B,MAAMC,WAAWJ,QAAQK,IAAI;AAC7B,MAAMC,YAAYN,QAAQO,KAAK;AAE/B,MAAMC,kBAAiC,EAAE;AAElC,SAASZ;IACd,OAAOY;AACT;AAQO,SAASV,IAAIW,OAAkC;IACpD,MAAMC,eAAe,OAAOD,YAAY,WAAW;QAAEE,MAAMF;IAAQ,IAAIA,WAAW,CAAC;IACnF,MAAMG,WAAW,CAACC,IAAAA,0BAAa,OAAMC,QAAG,CAACC,UAAU;IACnD,MAAMC,UAAUC,IAAAA,cAAO,EAAC;QACtB,oDAAoD;QACpDC,WAAW,CAACN;QACZ,2FAA2F;QAC3FO,QAAQP,WAAWQ,QAAQC,MAAM,GAAGD,QAAQE,MAAM;QAClD,GAAGZ,YAAY;IACjB;IAEA,MAAMa,WAAWP,QAAQQ,KAAK,CAACC,IAAI,CAACT;IACpC,MAAMU,UAAUV,QAAQW,IAAI,CAACF,IAAI,CAACT;IAClC,MAAMY,oBAAoBZ,QAAQa,cAAc,CAACJ,IAAI,CAACT;IAEtD,MAAMc,UAAU,CAACC,QAAaC;QAC5BN;QACAK,UAAUC;QACVhB,QAAQQ,KAAK;IACf;IAEA,MAAMS,iBAAiB;QACrBjC,QAAQC,GAAG,GAAG,CAAC,GAAG+B,OAAcF,QAAQ/B,SAASiC;QACjDhC,QAAQG,IAAI,GAAG,CAAC,GAAG6B,OAAcF,QAAQ5B,UAAU8B;QACnDhC,QAAQK,IAAI,GAAG,CAAC,GAAG2B,OAAcF,QAAQ1B,UAAU4B;QACnDhC,QAAQO,KAAK,GAAG,CAAC,GAAGyB,OAAcF,QAAQxB,WAAW0B;QAErDxB,gBAAgB0B,IAAI,CAAClB;IACvB;IAEA,MAAMmB,kBAAkB;QACtBnC,QAAQC,GAAG,GAAGF;QACdC,QAAQG,IAAI,GAAGD;QACfF,QAAQK,IAAI,GAAGD;QACfJ,QAAQO,KAAK,GAAGD;QAEhB,MAAM8B,QAAQ5B,gBAAgB6B,OAAO,CAACrB;QACtC,IAAIoB,SAAS,GAAG;YACd5B,gBAAgB8B,MAAM,CAACF,OAAO;QAChC;IACF;IAEApB,QAAQQ,KAAK,GAAG,CAACb;QACfsB;QACA,OAAOV,SAASZ;IAClB;IAEAK,QAAQa,cAAc,GAAG,CAACpB;QACxB,MAAM8B,SAASX,kBAAkBnB;QACjC0B;QACA,OAAOI;IACT;IAEAvB,QAAQW,IAAI,GAAG;QACb,MAAMY,SAASb;QACfS;QACA,OAAOI;IACT;IAEA,sEAAsE;IACtE,2BAA2B;IAE3B,OAAOvB;AACT;AAQO,SAASnB,cAAc2C,KAAa;IACzC,MAAMxB,UAAUlB,IAAI2C,gBAAK,CAACC,IAAI,CAACF;IAC/B,oDAAoD;IACpDxB,QAAQQ,KAAK;IACb,OAAOR;AACT"}