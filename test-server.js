const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4001;

// Middleware
app.use(cors());
app.use(express.json());

// Route de test simple
app.get('/', (req, res) => {
  console.log('📥 Requête GET /');
  res.json({
    message: 'Serveur de test fonctionnel',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route de test pour les clients (données statiques)
app.get('/api/clients', (req, res) => {
  console.log('📥 Requête GET /api/clients');
  
  const testClients = [
    {
      idclient: 1,
      nom: '<PERSON><PERSON>',
      prenom: '<PERSON>',
      adresse: '123 Rue de la Paix',
      ville: 'Tunis',
      tel: '71234567',
      email: '<EMAIL>',
      secteur_nom: 'Centre-Ville',
      ids: 1
    },
    {
      idclient: 2,
      nom: '<PERSON>',
      prenom: '<PERSON>',
      adresse: '456 Avenue Habib Bourguiba',
      ville: 'Tu<PERSON>',
      tel: '71345678',
      email: '<EMAIL>',
      secteur_nom: 'Manouba',
      ids: 2
    },
    {
      idclient: 3,
      nom: 'Benali',
      prenom: 'Ahmed',
      adresse: '789 Rue de Carthage',
      ville: 'Ariana',
      tel: '71456789',
      email: '<EMAIL>',
      secteur_nom: 'Ariana',
      ids: 3
    },
    {
      idclient: 4,
      nom: 'Trabelsi',
      prenom: 'Fatima',
      adresse: '321 Rue Ibn Khaldoun',
      ville: 'Ben Arous',
      tel: '71567890',
      email: '<EMAIL>',
      secteur_nom: 'Ben Arous',
      ids: 4
    },
    {
      idclient: 5,
      nom: 'Sassi',
      prenom: 'Mohamed',
      adresse: '654 Avenue de la République',
      ville: 'Nabeul',
      tel: '71678901',
      email: '<EMAIL>',
      secteur_nom: 'Nabeul',
      ids: 5
    }
  ];

  res.json({
    success: true,
    count: testClients.length,
    data: testClients
  });
});

// Route pour récupérer les secteurs (données statiques)
app.get('/api/secteurs', (req, res) => {
  console.log('📥 Requête GET /api/secteurs');

  const testSecteurs = [
    { ids: 1, nom: 'Centre-Ville' },
    { ids: 2, nom: 'Manouba' },
    { ids: 3, nom: 'Ariana' },
    { ids: 4, nom: 'Ben Arous' },
    { ids: 5, nom: 'Nabeul' }
  ];

  res.json({
    success: true,
    data: testSecteurs,
    count: testSecteurs.length
  });
});

// Route pour récupérer un secteur spécifique par ID
app.get('/api/secteurs/:id', (req, res) => {
  const { id } = req.params;
  console.log(`📥 Requête GET /api/secteurs/${id}`);

  const testSecteurs = [
    { ids: 1, nom: 'Centre-Ville' },
    { ids: 2, nom: 'Manouba' },
    { ids: 3, nom: 'Ariana' },
    { ids: 4, nom: 'Ben Arous' },
    { ids: 5, nom: 'Nabeul' }
  ];

  const secteur = testSecteurs.find(s => s.ids === parseInt(id));

  if (secteur) {
    res.json({
      success: true,
      data: secteur
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'Secteur non trouvé'
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', (req, res) => {
  const { id } = req.params;
  console.log(`📥 Requête GET /api/clients/${id}/contracts`);

  // Données de test pour les contrats
  const testContracts = [
    {
      idcontract: 1,
      codeqr: 'QR001',
      datecontract: '2024-01-15',
      idclient: 1,
      marquecompteur: 'Sensus',
      numseriecompteur: 'SN123456',
      posx: '10.1658', // Longitude (Tunis)
      posy: '36.8065'  // Latitude (Tunis)
    },
    {
      idcontract: 2,
      codeqr: 'QR002',
      datecontract: '2024-02-20',
      idclient: 2,
      marquecompteur: 'Itron',
      numseriecompteur: 'IT789012',
      posx: '10.1658',
      posy: '36.8065'
    },
    {
      idcontract: 3,
      codeqr: 'QR003',
      datecontract: '2024-03-10',
      idclient: 3,
      marquecompteur: 'Elster',
      numseriecompteur: 'EL345678',
      posx: '10.1950', // Ariana
      posy: '36.8625'
    },
    {
      idcontract: 4,
      codeqr: 'QR004',
      datecontract: '2024-04-05',
      idclient: 4,
      marquecompteur: 'Sensus',
      numseriecompteur: 'SN901234',
      posx: '10.2200', // Ben Arous
      posy: '36.7500'
    },
    {
      idcontract: 5,
      codeqr: 'QR005',
      datecontract: '2024-05-12',
      idclient: 5,
      marquecompteur: 'Itron',
      numseriecompteur: 'IT567890',
      posx: '10.7350', // Nabeul
      posy: '36.4560'
    }
  ];

  const clientContracts = testContracts.filter(contract => contract.idclient === parseInt(id));

  res.json({
    success: true,
    count: clientContracts.length,
    data: clientContracts,
    client_id: parseInt(id)
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test démarré sur le port ${PORT}`);
  console.log(`📡 Test: http://localhost:${PORT}`);
  console.log(`📋 Clients: http://localhost:${PORT}/api/clients`);
  console.log(`✅ Prêt !`);
});
