{"version": 3, "file": "ExpoKeepAwake.web.js", "sourceRoot": "", "sources": ["../src/ExpoKeepAwake.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAA0B,MAAM,mBAAmB,CAAC;AAEjF,OAAO,EAAE,mBAAmB,EAAqB,MAAM,mBAAmB,CAAC;AAE3E,MAAM,WAAW,GAAqC,EAAE,CAAC;AAkBzD,sGAAsG;AACtG,eAAe;IACb,KAAK,CAAC,gBAAgB;QACpB,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC5B,OAAO,UAAU,IAAI,SAAS,CAAC;QACjC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,CAAC,QAAQ,CAAC,GAAW;QACxB,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5D,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;IAC9B,CAAC;IACD,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QACD,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7B,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,UAAU,CAClB,4BAA4B,EAC5B,0BAA0B,GAAG,wBAAwB,CACtD,CAAC;QACJ,CAAC;IACH,CAAC;IACD,iBAAiB,CAAC,GAAW,EAAE,QAA2B;QACxD,MAAM,aAAa,GAAG,GAAG,EAAE;YACzB,QAAQ,CAAC,EAAE,KAAK,EAAE,mBAAmB,CAAC,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC;QACF,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,kBAAkB,IAAI,QAAQ,EAAE,CAAC;gBACnC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;YACrC,CAAC;QACH,CAAC;QACD,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,QAAQ,CAAC,mBAAmB,EAAE,CAAC;wBACjC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBACzD,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;oBAC5B,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC", "sourcesContent": ["import { CodedError, Platform, type EventSubscription } from 'expo-modules-core';\n\nimport { KeepAwakeEventState, KeepAwakeListener } from './KeepAwake.types';\n\nconst wakeLockMap: Record<string, WakeLockSentinel> = {};\n\ntype WakeLockSentinel = {\n  onrelease: null | ((event: any) => void);\n  released: boolean;\n  type: 'screen';\n  release?: Function;\n\n  addEventListener?: (event: string, listener: (event: any) => void) => void;\n  removeEventListener?: (event: string, listener: (event: any) => void) => void;\n};\n\ndeclare const navigator: {\n  wakeLock: {\n    request(type: 'screen'): Promise<WakeLockSentinel>;\n  };\n};\n\n/** Wraps the webWakeLock API https://developer.mozilla.org/en-US/docs/Web/API/Screen_Wake_Lock_API */\nexport default {\n  async isAvailableAsync() {\n    if (Platform.isDOMAvailable) {\n      return 'wakeLock' in navigator;\n    }\n    return false;\n  },\n  async activate(tag: string) {\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n    const wakeLock = await navigator.wakeLock.request('screen');\n    wakeLockMap[tag] = wakeLock;\n  },\n  async deactivate(tag: string) {\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n    if (wakeLockMap[tag]) {\n      wakeLockMap[tag].release?.();\n      delete wakeLockMap[tag];\n    } else {\n      throw new CodedError(\n        'ERR_KEEP_AWAKE_TAG_INVALID',\n        `The wake lock with tag ${tag} has not activated yet`\n      );\n    }\n  },\n  addListenerForTag(tag: string, listener: KeepAwakeListener): EventSubscription {\n    const eventListener = () => {\n      listener({ state: KeepAwakeEventState.RELEASE });\n    };\n    const sentinel = wakeLockMap[tag];\n    if (sentinel) {\n      if ('addEventListener' in sentinel) {\n        sentinel.addEventListener?.('release', eventListener);\n      } else {\n        sentinel.onrelease = eventListener;\n      }\n    }\n    return {\n      remove: () => {\n        const sentinel = wakeLockMap[tag];\n        if (sentinel) {\n          if (sentinel.removeEventListener) {\n            sentinel.removeEventListener('release', eventListener);\n          } else {\n            sentinel.onrelease = null;\n          }\n        }\n      },\n    };\n  },\n};\n"]}