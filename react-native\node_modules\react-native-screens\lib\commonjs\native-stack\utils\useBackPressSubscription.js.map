{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "obj", "__esModule", "default", "useBackPressSubscription", "_ref", "onBackPress", "isDisabled", "isActive", "setIsActive", "React", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "arguments", "length", "undefined", "current", "remove", "createSubscription", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useBackPressSubscription.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAoE,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAcpE;AACA;AACA;AACA;AACO,SAASG,wBAAwBA,CAAAC,IAAA,EAGL;EAAA,IAHM;IACvCC,WAAW;IACXC;EACI,CAAC,GAAAF,IAAA;EACL,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMC,YAAY,GAAGF,cAAK,CAACG,MAAM,CAAsC,CAAC;EAExE,MAAMC,iBAAiB,GAAGJ,cAAK,CAACK,WAAW,CAAC,YAA4B;IAAA,IAA3BC,eAAe,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACjEL,YAAY,CAACQ,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC9BT,YAAY,CAACQ,OAAO,GAAGD,SAAS;IAChC,IAAIH,eAAe,EAAEP,WAAW,CAAC,KAAK,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,kBAAkB,GAAGZ,cAAK,CAACK,WAAW,CAAC,MAAM;IACjD,IAAI,CAACR,UAAU,EAAE;MACfK,YAAY,CAACQ,OAAO,EAAEC,MAAM,CAAC,CAAC;MAC9BT,YAAY,CAACQ,OAAO,GAAGG,wBAAW,CAACC,gBAAgB,CACjD,mBAAmB,EACnBlB,WACF,CAAC;MACDG,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACF,UAAU,EAAED,WAAW,CAAC,CAAC;EAE7B,MAAMmB,cAAc,GAAGf,cAAK,CAACK,WAAW,CAAC,MAAM;IAC7C,IAAIP,QAAQ,EAAE;MACZc,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEd,QAAQ,CAAC,CAAC;EAElC,MAAMkB,cAAc,GAAGhB,cAAK,CAACK,WAAW,CAAC,MAAM;IAC7CD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvBJ,cAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIpB,UAAU,EAAE;MACdO,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,UAAU,EAAEO,iBAAiB,CAAC,CAAC;EAEnC,OAAO;IACLW,cAAc;IACdC,cAAc;IACdJ,kBAAkB;IAClBR;EACF,CAAC;AACH"}