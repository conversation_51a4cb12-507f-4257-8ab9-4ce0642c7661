{"version": 3, "names": ["Platform", "View", "React", "isNativePlatformSupported", "screensEnabled", "ScreenContainerNativeComponent", "ScreenNavigationContainerNativeComponent", "ScreenContainer", "props", "enabled", "hasTwoStates", "rest", "ScreenNavigationContainer", "OS", "createElement"], "sourceRoot": "../../../src", "sources": ["components/ScreenContainer.tsx"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,yBAAyB,EAAEC,cAAc,QAAQ,SAAS;;AAEnE;AACA,OAAOC,8BAA8B,MAAM,0CAA0C;AACrF,OAAOC,wCAAwC,MAAM,oDAAoD;AAEzG,SAASC,eAAeA,CAACC,KAA2B,EAAE;EACpD,MAAM;IAAEC,OAAO,GAAGL,cAAc,CAAC,CAAC;IAAEM,YAAY;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EAEnE,IAAIC,OAAO,IAAIN,yBAAyB,EAAE;IACxC,IAAIO,YAAY,EAAE;MAChB,MAAME,yBAAyB,GAC7BZ,QAAQ,CAACa,EAAE,KAAK,KAAK,GACjBP,wCAAwC,GACxCD,8BAA8B;MACpC,oBAAOH,KAAA,CAAAY,aAAA,CAACF,yBAAyB,EAAKD,IAAO,CAAC;IAChD;IACA,oBAAOT,KAAA,CAAAY,aAAA,CAACT,8BAA8B,EAAKM,IAAO,CAAC;EACrD;EACA,oBAAOT,KAAA,CAAAY,aAAA,CAACb,IAAI,EAAKU,IAAO,CAAC;AAC3B;AAEA,eAAeJ,eAAe"}