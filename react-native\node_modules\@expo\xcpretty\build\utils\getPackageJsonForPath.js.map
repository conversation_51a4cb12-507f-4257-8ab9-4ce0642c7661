{"version": 3, "file": "getPackageJsonForPath.js", "sourceRoot": "", "sources": ["../../src/utils/getPackageJsonForPath.ts"], "names": [], "mappings": ";;;;;AAEA,sDAMC;AARD,sDAA6B;AAE7B,SAAgB,qBAAqB,CAAC,QAAgB;IACpD,MAAM,WAAW,GAAG,iBAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;IACnE,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import findUp from 'find-up';\n\nexport function getPackageJsonForPath(filePath: string): any {\n  const packageJson = findUp.sync('package.json', { cwd: filePath });\n  if (packageJson) {\n    return require(packageJson);\n  }\n  return null;\n}\n"]}