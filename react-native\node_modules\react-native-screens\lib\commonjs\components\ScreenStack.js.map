{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_contexts", "_core", "_DelayedFreeze", "_warnOnce", "_ScreenStackNativeComponent", "obj", "__esModule", "_extends", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "isF<PERSON><PERSON>", "global", "assertGHProvider", "ScreenGestureDetector", "goBackGesture", "isGestureDetectorProviderNotDetected", "name", "undefined", "warnOnce", "assertCustomScreenTransitionsProps", "screensRefs", "currentScreenId", "isGestureDetectorNotConfiguredProperly", "ScreenStack", "props", "passedScreenRefs", "transitionAnimation", "screenEdgeGesture", "onFinishTransitioning", "children", "rest", "React", "useRef", "current", "ref", "size", "Children", "count", "useContext", "GHContext", "gestureDetectorBridge", "stackUseEffectCallback", "_stackRef", "childrenWithFreeze", "map", "child", "index", "descriptor", "descriptors", "isFreezeEnabled", "options", "freezeOnBlur", "freezeEnabled", "freezePreviousScreen", "createElement", "freeze", "useEffect", "RNSScreensRefContext", "Provider", "_default"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAQA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AAGA,IAAAK,2BAAA,GAAAN,sBAAA,CAAAC,OAAA;AAE8C,SAAAD,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAT,OAAA,EAAAS,GAAA;AAAA,SAAAE,SAAA,IAAAA,QAAA,GAAAf,MAAA,CAAAgB,MAAA,GAAAhB,MAAA,CAAAgB,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAtB,MAAA,CAAAwB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAH,QAAA,CAAAY,KAAA,OAAAP,SAAA,KAH9C;AAKA,SAASQ,QAAQA,CAAA,EAAG;EAClB,OAAO,uBAAuB,IAAIC,MAAM;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CACvBC,qBAEsB,EACtBC,aAAwC,KACrC;EACH,MAAMC,oCAAoC,GACxCF,qBAAqB,CAACG,IAAI,KAAK,WAAW,IAAIF,aAAa,KAAKG,SAAS;EAE3E,IAAAC,iBAAQ,EACNH,oCAAoC,EACpC,8IACF,CAAC;AACH,CAAC;AAED,MAAMI,kCAAkC,GAAGA,CACzCC,WAA4C,EAC5CC,eAAoD,EACpDP,aAAgD,KAC7C;EACH,MAAMQ,sCAAsC,GAC1CR,aAAa,KAAKG,SAAS,IAC3BG,WAAW,KAAK,IAAI,IACpBC,eAAe,KAAKJ,SAAS;EAE/B,IAAAC,iBAAQ,EACNI,sCAAsC,EACtC,kFACF,CAAC;AACH,CAAC;AAED,SAASC,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IACJV,aAAa;IACbM,WAAW,EAAEK,gBAAgB;IAAE;IAC/BJ,eAAe;IACfK,mBAAmB;IACnBC,iBAAiB;IACjBC,qBAAqB;IACrBC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGN,KAAK;EAET,MAAMJ,WAAW,GAAGW,cAAK,CAACC,MAAM,CAC9BP,gBAAgB,EAAEQ,OAAO,IAAI,CAAC,CAChC,CAAC;EACD,MAAMC,GAAG,GAAGH,cAAK,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMG,IAAI,GAAGJ,cAAK,CAACK,QAAQ,CAACC,KAAK,CAACR,QAAQ,CAAC;EAC3C,MAAMhB,qBAAqB,GAAGkB,cAAK,CAACO,UAAU,CAACC,mBAAS,CAAC;EACzD,MAAMC,qBAAqB,GAAGT,cAAK,CAACC,MAAM,CAAwB;IAChES,sBAAsB,EAAEC,SAAS,IAAI;MACnC;IAAA;EAEJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGZ,cAAK,CAACK,QAAQ,CAACQ,GAAG,CAACf,QAAQ,EAAE,CAACgB,KAAK,EAAEC,KAAK,KAAK;IACxE;IACA,MAAM;MAAEtB,KAAK;MAAEnB;IAAI,CAAC,GAAGwC,KAAK;IAC5B,MAAME,UAAU,GAAGvB,KAAK,EAAEuB,UAAU,IAAIvB,KAAK,EAAEwB,WAAW,GAAG3C,GAAG,CAAC;IACjE,MAAM4C,eAAe,GACnBF,UAAU,EAAEG,OAAO,EAAEC,YAAY,IAAI,IAAAC,mBAAa,EAAC,CAAC;;IAEtD;IACA;IACA,MAAMC,oBAAoB,GAAG3C,QAAQ,CAAC,CAAC,GACnCyB,IAAI,GAAGW,KAAK,GAAG,CAAC,GAChBX,IAAI,GAAGW,KAAK,GAAG,CAAC;IAEpB,oBACE3D,MAAA,CAAAD,OAAA,CAAAoE,aAAA,CAAC9D,cAAA,CAAAN,OAAa;MAACqE,MAAM,EAAEN,eAAe,IAAII;IAAqB,GAC5DR,KACY,CAAC;EAEpB,CAAC,CAAC;EAEFd,cAAK,CAACyB,SAAS,CAAC,MAAM;IACpBhB,qBAAqB,CAACP,OAAO,CAACQ,sBAAsB,CAACP,GAAG,CAAC;EAC3D,CAAC,CAAC;EAEFtB,gBAAgB,CAACC,qBAAqB,EAAEC,aAAa,CAAC;EAEtDK,kCAAkC,CAChCC,WAAW,EACXC,eAAe,EACfP,aACF,CAAC;EAED,oBACE3B,MAAA,CAAAD,OAAA,CAAAoE,aAAA,CAAChE,SAAA,CAAAmE,oBAAoB,CAACC,QAAQ;IAACzE,KAAK,EAAEmC;EAAY,gBAChDjC,MAAA,CAAAD,OAAA,CAAAoE,aAAA,CAACzC,qBAAqB;IACpB2B,qBAAqB,EAAEA,qBAAsB;IAC7C1B,aAAa,EAAEA,aAAc;IAC7BY,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAiB,IAAI,KAAM;IAC9CP,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA;EAAgB,gBACjClC,MAAA,CAAAD,OAAA,CAAAoE,aAAA,CAAC5D,2BAAA,CAAAR,OAA0B,EAAAW,QAAA,KACrBiC,IAAI;IACR;AACV;AACA;AACA;AACA;IACUF,qBAAqB,EACnBA,qBACD;IACDM,GAAG,EAAEA;EAAI,IACRS,kBACyB,CACP,CACM,CAAC;AAEpC;AAAC,IAAAgB,QAAA,GAAA3E,OAAA,CAAAE,OAAA,GAEcqC,WAAW"}