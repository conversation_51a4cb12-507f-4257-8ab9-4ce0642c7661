{"version": 3, "sources": ["../../../src/prebuild/validateTemplatePlatforms.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { directoryExistsSync } from '../utils/dir';\n\nexport function validateTemplatePlatforms({\n  templateDirectory,\n  platforms,\n}: {\n  templateDirectory: string;\n  platforms: ModPlatform[];\n}) {\n  const existingPlatforms: ModPlatform[] = [];\n\n  for (const platform of platforms) {\n    if (directoryExistsSync(path.join(templateDirectory, platform))) {\n      existingPlatforms.push(platform);\n    } else {\n      Log.warn(\n        chalk`⚠️  Skipping platform ${platform}. Use a template that contains native files for ${platform} (./${platform}).`\n      );\n    }\n  }\n\n  return existingPlatforms;\n}\n"], "names": ["validateTemplatePlatforms", "templateDirectory", "platforms", "existingPlatforms", "platform", "directoryExistsSync", "path", "join", "push", "Log", "warn", "chalk"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;;gEANE;;;;;;;gEACD;;;;;;6DAEI;qBACe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,SAASA,0BAA0B,EACxCC,iBAAiB,EACjBC,SAAS,EAIV;IACC,MAAMC,oBAAmC,EAAE;IAE3C,KAAK,MAAMC,YAAYF,UAAW;QAChC,IAAIG,IAAAA,wBAAmB,EAACC,eAAI,CAACC,IAAI,CAACN,mBAAmBG,YAAY;YAC/DD,kBAAkBK,IAAI,CAACJ;QACzB,OAAO;YACLK,KAAIC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,sBAAsB,EAAEP,SAAS,gDAAgD,EAAEA,SAAS,IAAI,EAAEA,SAAS,EAAE,CAAC;QAExH;IACF;IAEA,OAAOD;AACT"}