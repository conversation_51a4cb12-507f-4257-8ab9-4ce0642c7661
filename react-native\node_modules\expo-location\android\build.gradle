apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '18.0.10'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.location"
  defaultConfig {
    versionCode 29
    versionName "18.0.10"
  }
}

dependencies {
  api 'com.google.android.gms:play-services-location:21.0.1'
  implementation project(":${project.name}\$io.nlopez.smartlocation-jetified-aar")
}
