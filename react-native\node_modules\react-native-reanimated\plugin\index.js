"use strict";var et=Object.create;var me=Object.defineProperty;var tt=Object.getOwnPropertyDescriptor;var nt=Object.getOwnPropertyNames;var rt=Object.getPrototypeOf,it=Object.prototype.hasOwnProperty;var b=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var ot=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of nt(t))!it.call(e,i)&&i!==n&&me(e,i,{get:()=>t[i],enumerable:!(r=tt(t,i))||r.enumerable});return e};var R=(e,t,n)=>(n=e!=null?et(rt(e)):{},ot(t||!e||!e.__esModule?me(n,"default",{value:e,enumerable:!0}):n,e));var S=b(y=>{"use strict";Object.defineProperty(y,"__esModule",{value:!0});y.workletClassFactorySuffix=y.isWorkletizableObjectNode=y.isWorkletizableObjectPath=y.isWorkletizableFunctionNode=y.isWorkletizableFunctionPath=y.WorkletizableObject=y.WorkletizableFunction=void 0;var B=require("@babel/types");y.WorkletizableFunction="FunctionDeclaration|FunctionExpression|ArrowFunctionExpression|ObjectMethod";y.WorkletizableObject="ObjectExpression";function st(e){return e.isFunctionDeclaration()||e.isFunctionExpression()||e.isArrowFunctionExpression()||e.isObjectMethod()}y.isWorkletizableFunctionPath=st;function at(e){return(0,B.isFunctionDeclaration)(e)||(0,B.isFunctionExpression)(e)||(0,B.isArrowFunctionExpression)(e)||(0,B.isObjectMethod)(e)}y.isWorkletizableFunctionNode=at;function lt(e){return e.isObjectExpression()}y.isWorkletizableObjectPath=lt;function ct(e){return(0,B.isObjectExpression)(e)}y.isWorkletizableObjectNode=ct;y.workletClassFactorySuffix="__classFactory"});var M=b(A=>{"use strict";Object.defineProperty(A,"__esModule",{value:!0});A.replaceWithFactoryCall=A.isRelease=void 0;var G=require("@babel/types");function ut(){var e,t;let n=/(prod|release|stag[ei])/i;return!!(!((e=process.env.BABEL_ENV)===null||e===void 0)&&e.match(n)||!((t=process.env.NODE_ENV)===null||t===void 0)&&t.match(n))}A.isRelease=ut;function dt(e,t,n){if(!t||!ft(e))e.replaceWith(n);else{let r=(0,G.variableDeclaration)("const",[(0,G.variableDeclarator)((0,G.identifier)(t),n)]);e.replaceWith(r)}}A.replaceWithFactoryCall=dt;function ft(e){return(0,G.isScopable)(e.parent)||(0,G.isExportNamedDeclaration)(e.parent)}});var ae=b(g=>{"use strict";Object.defineProperty(g,"__esModule",{value:!0});g.addCustomGlobals=g.initializeGlobals=g.globals=g.defaultGlobals=g.initializeState=void 0;var pt=["globalThis","Infinity","NaN","undefined","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape","Object","Function","Boolean","Symbol","Error","AggregateError","EvalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError","InternalError","Number","BigInt","Math","Date","String","RegExp","Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","BigInt64Array","BigUint64Array","Float32Array","Float64Array","Map","Set","WeakMap","WeakSet","ArrayBuffer","SharedArrayBuffer","DataView","Atomics","JSON","WeakRef","FinalizationRegistry","Iterator","AsyncIterator","Promise","GeneratorFunction","AsyncGeneratorFunction","Generator","AsyncGenerator","AsyncFunction","Reflect","Proxy","Intl","null","this","global","window","globalThis","console","performance","queueMicrotask","requestAnimationFrame","setImmediate","arguments","HermesInternal","_WORKLET","ReanimatedError","__reanimatedLoggerConfig"],bt=["_IS_FABRIC","_log","_toString","_scheduleHostFunctionOnJS","_scheduleRemoteFunctionOnJS","_scheduleOnRuntime","_makeShareableClone","_updatePropsPaper","_updatePropsFabric","_removeFromPropsRegistry","_measurePaper","_measureFabric","_scrollToPaper","_dispatchCommandPaper","_dispatchCommandFabric","_setGestureState","_notifyAboutProgress","_notifyAboutEnd","_runOnUIQueue","_getAnimationTimestamp"];function mt(e){e.workletNumber=1,e.classesToWorkletize=[],ye(),ke(e)}g.initializeState=mt;g.defaultGlobals=new Set(pt.concat(bt));function ye(){g.globals=new Set(g.defaultGlobals)}g.initializeGlobals=ye;function ke(e){e.opts&&Array.isArray(e.opts.globals)&&e.opts.globals.forEach(t=>{g.globals.add(t)})}g.addCustomGlobals=ke});var V=b(P=>{"use strict";var yt=P&&P.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};Object.defineProperty(P,"__esModule",{value:!0});P.workletTransformSync=void 0;var kt=require("@babel/core");function gt(e,t){let{extraPlugins:n=[],extraPresets:r=[]}=t,i=yt(t,["extraPlugins","extraPresets"]);return(0,kt.transformSync)(e,Object.assign(Object.assign({},i),{plugins:[...Ot,...n],presets:[..._t,...r]}))}P.workletTransformSync=gt;var _t=[require.resolve("@babel/preset-typescript")],Ot=[]});var Oe=b(v=>{"use strict";var vt=v&&v.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),ht=v&&v.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),ge=v&&v.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&vt(t,e,n);return ht(t,e),t},Et=v&&v.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(v,"__esModule",{value:!0});v.buildWorkletString=void 0;var _e=require("@babel/core"),St=Et(require("@babel/generator")),c=require("@babel/types"),H=require("assert"),It=ge(require("convert-source-map")),Wt=ge(require("fs")),xt=S(),wt=M(),Ft=V(),Ct="mock source map";function jt(e,t,n,r,i){Dt(e,r);let s=e.program.body.find(_=>(0,c.isFunctionDeclaration)(_))||e.program.body.find(_=>(0,c.isExpressionStatement)(_))||void 0;(0,H.strict)(s,"[Reanimated] `draftExpression` is undefined.");let a=(0,c.isFunctionDeclaration)(s)?s:s.expression;(0,H.strict)("params"in a,"'params' property is undefined in 'expression'"),(0,H.strict)((0,c.isBlockStatement)(a.body),"[Reanimated] `expression.body` is not a `BlockStatement`");let u=new Set;(0,_e.traverse)(e,{NewExpression(_){if(!(0,c.isIdentifier)(_.node.callee))return;let x=_.node.callee.name;if(!n.some(Z=>Z.name===x)||u.has(x))return;let D=n.findIndex(Z=>Z.name===x);n.splice(D,1);let ie=x+xt.workletClassFactorySuffix;n.push((0,c.identifier)(ie)),(0,c.assertBlockStatement)(a.body),a.body.body.unshift((0,c.variableDeclaration)("const",[(0,c.variableDeclarator)((0,c.identifier)(x),(0,c.callExpression)((0,c.identifier)(ie),[]))])),u.add(x)}});let p=(0,c.functionExpression)((0,c.identifier)(r),a.params,a.body,a.generator,a.async),d=(0,St.default)(p).code;(0,H.strict)(i,"[Reanimated] `inputMap` is undefined.");let O=!((0,wt.isRelease)()||t.opts.disableSourceMaps);if(O){i.sourcesContent=[];for(let _ of i.sources)i.sourcesContent.push(Wt.readFileSync(_).toString("utf-8"))}let j=(0,Ft.workletTransformSync)(d,{filename:t.file.opts.filename,extraPlugins:[Pt(n)],compact:!0,sourceMaps:O,inputSourceMap:i,ast:!1,babelrc:!1,configFile:!1,comments:!1});(0,H.strict)(j,"[Reanimated] `transformed` is null.");let C;return O&&(Rt()?C=Ct:(C=It.fromObject(j.map).toObject(),delete C.sourcesContent)),[j.code,JSON.stringify(C)]}v.buildWorkletString=jt;function Dt(e,t){(0,_e.traverse)(e,{FunctionExpression(n){if(!n.node.id){n.stop();return}let r=n.node.id.name;n.scope.rename(r,t)}})}function Rt(){return process.env.REANIMATED_JEST_SHOULD_MOCK_SOURCE_MAP==="1"}function At(e,t,n){t.length===0||!(0,c.isProgram)(e.parent)||(0,c.isExpression)(e.node.body)||e.node.body.body.unshift(n)}function Mt(e){var t;(0,c.isProgram)(e.parent)&&!(0,c.isArrowFunctionExpression)(e.node)&&!(0,c.isObjectMethod)(e.node)&&e.node.id&&e.scope.parent&&((t=e.scope.parent.bindings[e.node.id.name])===null||t===void 0?void 0:t.references)>0&&e.node.body.body.unshift((0,c.variableDeclaration)("const",[(0,c.variableDeclarator)((0,c.identifier)(e.node.id.name),(0,c.memberExpression)((0,c.thisExpression)(),(0,c.identifier)("_recur")))]))}function Pt(e){let t=(0,c.variableDeclaration)("const",[(0,c.variableDeclarator)((0,c.objectPattern)(e.map(n=>(0,c.objectProperty)((0,c.identifier)(n.name),(0,c.identifier)(n.name),!1,!0))),(0,c.memberExpression)((0,c.thisExpression)(),(0,c.identifier)("__closure")))]);return{visitor:{"FunctionDeclaration|FunctionExpression|ArrowFunctionExpression|ObjectMethod":n=>{At(n,e,t),Mt(n)}}}}});var Ee=b(q=>{"use strict";var qt=q&&q.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(q,"__esModule",{value:!0});q.makeWorkletFactory=void 0;var zt=require("@babel/core"),Lt=qt(require("@babel/generator")),o=require("@babel/types"),w=require("assert"),he=require("path"),Tt=ae(),ve=S(),le=M(),Nt=Oe(),Ut=V(),Bt=require("../package.json").version,Gt="x.y.z";function Ht(e,t){Zt(e),(0,w.strict)(t.file.opts.filename,"[Reanimated] `state.file.opts.filename` is undefined.");let n=(0,Lt.default)(e.node,{sourceMaps:!0,sourceFileName:t.file.opts.filename});n.code="("+(e.isObjectMethod()?"function ":"")+n.code+`
)`;let r=(0,Ut.workletTransformSync)(n.code,{extraPlugins:Yt,filename:t.file.opts.filename,ast:!0,babelrc:!1,configFile:!1,inputSourceMap:n.map});(0,w.strict)(r,"[Reanimated] `transformed` is undefined."),(0,w.strict)(r.ast,"[Reanimated] `transformed.ast` is undefined.");let i=$t(r.ast,e),s=(0,o.cloneNode)(e.node),a=(0,o.isBlockStatement)(s.body)?(0,o.functionExpression)(null,s.params,s.body,s.generator,s.async):s,{workletName:u,reactName:p}=Xt(e,t),[d,O]=(0,Nt.buildWorkletString)(r.ast,t,i,u,r.map);(0,w.strict)(d,"[Reanimated] `funString` is undefined.");let j=Jt(d),C=1;i.length>0&&(C-=i.length+2);let _=e.parentPath.isProgram()?e:e.findParent(k=>{var oe,se;return(se=(oe=k.parentPath)===null||oe===void 0?void 0:oe.isProgram())!==null&&se!==void 0?se:!1});(0,w.strict)(_,"[Reanimated] `pathForStringDefinitions` is null."),(0,w.strict)(_.parentPath,"[Reanimated] `pathForStringDefinitions.parentPath` is null.");let x=_.parentPath.scope.generateUidIdentifier(`worklet_${j}_init_data`),D=(0,o.objectExpression)([(0,o.objectProperty)((0,o.identifier)("code"),(0,o.stringLiteral)(d))]);if(!(0,le.isRelease)()){let k=t.file.opts.filename;t.opts.relativeSourceLocation&&(k=(0,he.relative)(t.cwd,k),O=O==null?void 0:O.replace(t.file.opts.filename,k)),D.properties.push((0,o.objectProperty)((0,o.identifier)("location"),(0,o.stringLiteral)(k)))}O&&D.properties.push((0,o.objectProperty)((0,o.identifier)("sourceMap"),(0,o.stringLiteral)(O))),!(0,le.isRelease)()&&D.properties.push((0,o.objectProperty)((0,o.identifier)("version"),(0,o.stringLiteral)(Vt()?Gt:Bt)));let be=!t.opts.omitNativeOnlyData;be&&_.insertBefore((0,o.variableDeclaration)("const",[(0,o.variableDeclarator)(x,D)])),(0,w.strict)(!(0,o.isFunctionDeclaration)(a),"[Reanimated] `funExpression` is a `FunctionDeclaration`."),(0,w.strict)(!(0,o.isObjectMethod)(a),"[Reanimated] `funExpression` is an `ObjectMethod`.");let U=[(0,o.variableDeclaration)("const",[(0,o.variableDeclarator)((0,o.identifier)(p),a)]),(0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(p),(0,o.identifier)("__closure"),!1),(0,o.objectExpression)(i.map(k=>k.name.endsWith(ve.workletClassFactorySuffix)?(0,o.objectProperty)((0,o.identifier)(k.name),(0,o.memberExpression)((0,o.identifier)(k.name.slice(0,k.name.length-ve.workletClassFactorySuffix.length)),(0,o.identifier)(k.name))):(0,o.objectProperty)((0,o.identifier)(k.name),k,!1,!0))))),(0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(p),(0,o.identifier)("__workletHash"),!1),(0,o.numericLiteral)(j)))];return be&&U.push((0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(p),(0,o.identifier)("__initData"),!1),x))),(0,le.isRelease)()||(U.unshift((0,o.variableDeclaration)("const",[(0,o.variableDeclarator)((0,o.identifier)("_e"),(0,o.arrayExpression)([(0,o.newExpression)((0,o.memberExpression)((0,o.identifier)("global"),(0,o.identifier)("Error")),[]),(0,o.numericLiteral)(C),(0,o.numericLiteral)(-27)]))])),U.push((0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(p),(0,o.identifier)("__stackDetails"),!1),(0,o.identifier)("_e"))))),U.push((0,o.returnStatement)((0,o.identifier)(p))),(0,o.functionExpression)(void 0,[],(0,o.blockStatement)(U))}q.makeWorkletFactory=Ht;function Zt(e){e.traverse({DirectiveLiteral(t){t.node.value==="worklet"&&t.getFunctionParent()===e&&t.parentPath.remove()}})}function Vt(){return process.env.REANIMATED_JEST_SHOULD_MOCK_VERSION==="1"}function Jt(e){let t=e.length,n=5381,r=52711;for(;t--;){let i=e.charCodeAt(t);n=n*33^i,r=r*33^i}return(n>>>0)*4096+(r>>>0)}function Xt(e,t){let n="unknownFile";if(t.file.opts.filename){let a=t.file.opts.filename;n=(0,he.basename)(a);let u=a.split("/"),p=u.indexOf("node_modules");p!==-1&&(n=`${u[p+1]}_${n}`)}let r=`${n}${t.workletNumber++}`,i="";(0,o.isObjectMethod)(e.node)&&(0,o.isIdentifier)(e.node.key)?i=e.node.key.name:((0,o.isFunctionDeclaration)(e.node)||(0,o.isFunctionExpression)(e.node))&&(0,o.isIdentifier)(e.node.id)&&(i=e.node.id.name);let s=i?(0,o.toIdentifier)(`${i}_${r}`):(0,o.toIdentifier)(r);return i=i||(0,o.toIdentifier)(r),{workletName:s,reactName:i}}function $t(e,t){let n=new Map,r=new Map;return(0,zt.traverse)(e,{Identifier(i){if(!i.isReferencedIdentifier())return;let s=i.node.name;if(Tt.globals.has(s)||"id"in t.node&&t.node.id&&t.node.id.name===s)return;let a=i.parent;if((0,o.isMemberExpression)(a)&&a.property===i.node&&!a.computed||(0,o.isObjectProperty)(a)&&(0,o.isObjectExpression)(i.parentPath.parent)&&i.node!==a.value)return;let u=i.scope;for(;u!=null;){if(u.bindings[s]!=null)return;u=u.parent}n.set(s,i.node),r.set(s,!1)}}),t.traverse({Identifier(i){if(!i.isReferencedIdentifier())return;let s=n.get(i.node.name);!s||r.get(i.node.name)||(s.loc=i.node.loc,r.set(i.node.name,!0))}}),Array.from(n.values())}var Yt=[require.resolve("@babel/plugin-transform-shorthand-properties"),require.resolve("@babel/plugin-transform-arrow-functions"),require.resolve("@babel/plugin-transform-optional-chaining"),require.resolve("@babel/plugin-transform-nullish-coalescing-operator"),[require.resolve("@babel/plugin-transform-template-literals"),{loose:!0}]]});var Se=b(J=>{"use strict";Object.defineProperty(J,"__esModule",{value:!0});J.makeWorkletFactoryCall=void 0;var Kt=require("@babel/types"),Qt=Ee();function en(e,t){let n=(0,Qt.makeWorkletFactory)(e,t),r=(0,Kt.callExpression)(n,[]);return tn(e,r),r}J.makeWorkletFactoryCall=en;function tn(e,t){let n=e.node.loc;n&&(t.callee.loc={filename:n.filename,identifierName:n.identifierName,start:n.start,end:n.start})}});var X=b(F=>{"use strict";Object.defineProperty(F,"__esModule",{value:!0});F.substituteObjectMethodWithObjectProperty=F.processWorklet=F.processIfWithWorkletDirective=void 0;var ce=require("@babel/types"),nn=S(),rn=M(),on=Se();function Ie(e,t){return!(0,ce.isBlockStatement)(e.node.body)||!sn(e.node.body.directives)?!1:(We(e,t),!0)}F.processIfWithWorkletDirective=Ie;function We(e,t){t.opts.processNestedWorklets&&e.traverse({[nn.WorkletizableFunction](r,i){Ie(r,i)}},t);let n=(0,on.makeWorkletFactoryCall)(e,t);an(e,n)}F.processWorklet=We;function sn(e){return e.some(t=>(0,ce.isDirectiveLiteral)(t.value)&&t.value.value==="worklet")}function an(e,t){var n;if(e.isObjectMethod())xe(e,t);else{let r="id"in e.node?(n=e.node.id)===null||n===void 0?void 0:n.name:void 0;(0,rn.replaceWithFactoryCall)(e,r,t)}}function xe(e,t){let n=(0,ce.objectProperty)(e.node.key,t);e.replaceWith(n)}F.substituteObjectMethodWithObjectProperty=xe});var Fe=b($=>{"use strict";Object.defineProperty($,"__esModule",{value:!0});$.isGestureHandlerEventCallback=void 0;var I=require("@babel/types"),ln=new Set(["Tap","Pan","Pinch","Rotation","Fling","LongPress","ForceTouch","Native","Manual","Race","Simultaneous","Exclusive","Hover"]),cn=new Set(["onBegin","onStart","onEnd","onFinalize","onUpdate","onChange","onTouchesDown","onTouchesMove","onTouchesUp","onTouchesCancelled"]);function un(e){return(0,I.isCallExpression)(e.parent)&&(0,I.isExpression)(e.parent.callee)&&dn(e.parent.callee)}$.isGestureHandlerEventCallback=un;function dn(e){return(0,I.isMemberExpression)(e)&&(0,I.isIdentifier)(e.property)&&cn.has(e.property.name)&&we(e.object)}function we(e){return!!(fn(e)||(0,I.isCallExpression)(e)&&(0,I.isMemberExpression)(e.callee)&&we(e.callee.object))}function fn(e){return(0,I.isCallExpression)(e)&&(0,I.isMemberExpression)(e.callee)&&(0,I.isIdentifier)(e.callee.object)&&e.callee.object.name==="Gesture"&&(0,I.isIdentifier)(e.callee.property)&&ln.has(e.callee.property.name)}});var De=b(Y=>{"use strict";Object.defineProperty(Y,"__esModule",{value:!0});Y.isLayoutAnimationCallback=void 0;var W=require("@babel/types"),pn=new Set(["BounceIn","BounceInDown","BounceInLeft","BounceInRight","BounceInUp","BounceOut","BounceOutDown","BounceOutLeft","BounceOutRight","BounceOutUp","FadeIn","FadeInDown","FadeInLeft","FadeInRight","FadeInUp","FadeOut","FadeOutDown","FadeOutLeft","FadeOutRight","FadeOutUp","FlipInEasyX","FlipInEasyY","FlipInXDown","FlipInXUp","FlipInYLeft","FlipInYRight","FlipOutEasyX","FlipOutEasyY","FlipOutXDown","FlipOutXUp","FlipOutYLeft","FlipOutYRight","LightSpeedInLeft","LightSpeedInRight","LightSpeedOutLeft","LightSpeedOutRight","PinwheelIn","PinwheelOut","RollInLeft","RollInRight","RollOutLeft","RollOutRight","RotateInDownLeft","RotateInDownRight","RotateInUpLeft","RotateInUpRight","RotateOutDownLeft","RotateOutDownRight","RotateOutUpLeft","RotateOutUpRight","SlideInDown","SlideInLeft","SlideInRight","SlideInUp","SlideOutDown","SlideOutLeft","SlideOutRight","SlideOutUp","StretchInX","StretchInY","StretchOutX","StretchOutY","ZoomIn","ZoomInDown","ZoomInEasyDown","ZoomInEasyUp","ZoomInLeft","ZoomInRight","ZoomInRotate","ZoomInUp","ZoomOut","ZoomOutDown","ZoomOutEasyDown","ZoomOutEasyUp","ZoomOutLeft","ZoomOutRight","ZoomOutRotate","ZoomOutUp"]),bn=new Set(["Layout","LinearTransition","SequencedTransition","FadingTransition","JumpingTransition","CurvedTransition","EntryExitTransition"]),Ce=new Set([...pn,...bn]),mn=new Set(["build","duration","delay","getDuration","randomDelay","getDelay","getDelayFunction"]),yn=new Set(["easing","rotate","springify","damping","mass","stiffness","overshootClamping","restDisplacementThreshold","restSpeedThreshold","withInitialValues","getAnimationAndConfig"]),kn=new Set(["easingX","easingY","easingWidth","easingHeight","entering","exiting","reverse"]),gn=new Set([...mn,...yn,...kn]),_n=new Set(["withCallback"]);function On(e){return(0,W.isCallExpression)(e.parent)&&(0,W.isExpression)(e.parent.callee)&&vn(e.parent.callee)}Y.isLayoutAnimationCallback=On;function vn(e){return(0,W.isMemberExpression)(e)&&(0,W.isIdentifier)(e.property)&&_n.has(e.property.name)&&je(e.object)}function je(e){return(0,W.isIdentifier)(e)&&Ce.has(e.name)?!0:!!((0,W.isNewExpression)(e)&&(0,W.isIdentifier)(e.callee)&&Ce.has(e.callee.name)||(0,W.isCallExpression)(e)&&(0,W.isMemberExpression)(e.callee)&&(0,W.isIdentifier)(e.callee.property)&&gn.has(e.callee.property.name)&&je(e.callee.object))}});var Re=b(K=>{"use strict";Object.defineProperty(K,"__esModule",{value:!0});K.findReferencedWorklet=void 0;var z=S();function hn(e,t,n){let r=e.node.name,s=e.scope.getBinding(r);return s?t&&s.path.isFunctionDeclaration()?s.path:s.constant?En(s,t,n):Sn(s,t,n):void 0}K.findReferencedWorklet=hn;function En(e,t,n){let r=e.path;if(!r.isVariableDeclarator())return;let i=r.get("init");if(t&&(0,z.isWorkletizableFunctionPath)(i)||n&&(0,z.isWorkletizableObjectPath)(i))return i}function Sn(e,t,n){let r=e.constantViolations.reverse().find(s=>s.isAssignmentExpression()&&(t&&(0,z.isWorkletizableFunctionPath)(s.get("right"))||n&&(0,z.isWorkletizableObjectPath)(s.get("right"))));if(!r||!r.isAssignmentExpression())return;let i=r.get("right");if(t&&(0,z.isWorkletizableFunctionPath)(i)||n&&(0,z.isWorkletizableObjectPath)(i))return i}});var Me=b(Q=>{"use strict";Object.defineProperty(Q,"__esModule",{value:!0});Q.processWorkletizableObject=void 0;var In=S(),Ae=X();function Wn(e,t){let n=e.get("properties");for(let r of n)if(r.isObjectMethod())(0,Ae.processWorklet)(r,t);else if(r.isObjectProperty()){let i=r.get("value");(0,In.isWorkletizableFunctionPath)(i)&&(0,Ae.processWorklet)(i,t)}else throw new Error(`[Reanimated] '${r.type}' as to-be workletized argument is not supported for object hooks.`)}Q.processWorkletizableObject=Wn});var Le=b(L=>{"use strict";Object.defineProperty(L,"__esModule",{value:!0});L.processCalleesAutoworkletizableCallbacks=L.processIfAutoworkletizableCallback=void 0;var xn=require("@babel/types"),ee=S(),ze=X(),wn=Fe(),Fn=De(),Cn=Re(),jn=Me(),Pe=new Set(["useAnimatedGestureHandler","useAnimatedScrollHandler"]),qe=new Set(["useFrameCallback","useAnimatedStyle","useAnimatedProps","createAnimatedPropAdapter","useDerivedValue","useAnimatedScrollHandler","useAnimatedReaction","useWorkletCallback","withTiming","withSpring","withDecay","withRepeat","runOnUI","executeOnUIRuntimeSync"]),Dn=new Map([["useAnimatedGestureHandler",[0]],["useFrameCallback",[0]],["useAnimatedStyle",[0]],["useAnimatedProps",[0]],["createAnimatedPropAdapter",[0]],["useDerivedValue",[0]],["useAnimatedScrollHandler",[0]],["useAnimatedReaction",[0,1]],["useWorkletCallback",[0]],["withTiming",[2]],["withSpring",[2]],["withDecay",[1]],["withRepeat",[3]],["runOnUI",[0]],["executeOnUIRuntimeSync",[0]]]);function Rn(e,t){return(0,wn.isGestureHandlerEventCallback)(e)||(0,Fn.isLayoutAnimationCallback)(e)?((0,ze.processWorklet)(e,t),!0):!1}L.processIfAutoworkletizableCallback=Rn;function An(e,t){let n=(0,xn.isSequenceExpression)(e.node.callee)?e.node.callee.expressions[e.node.callee.expressions.length-1]:e.node.callee,r="name"in n?n.name:"property"in n&&"name"in n.property?n.property.name:void 0;if(r!==void 0&&(qe.has(r)||Pe.has(r))){let i=qe.has(r),s=Pe.has(r),a=Dn.get(r),u=e.get("arguments").filter((p,d)=>a.includes(d));Mn(u,t,i,s)}}L.processCalleesAutoworkletizableCallbacks=An;function Mn(e,t,n,r){e.forEach(i=>{let s=Pn(i,n,r);s&&((0,ee.isWorkletizableFunctionPath)(s)?(0,ze.processWorklet)(s,t):(0,ee.isWorkletizableObjectPath)(s)&&(0,jn.processWorkletizableObject)(s,t))})}function Pn(e,t,n){if(t&&(0,ee.isWorkletizableFunctionPath)(e)||n&&(0,ee.isWorkletizableObjectPath)(e))return e;if(e.isReferencedIdentifier()&&e.isIdentifier())return(0,Cn.findReferencedWorklet)(e,t,n)}});var Be=b(T=>{"use strict";var Te=T&&T.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(T,"__esModule",{value:!0});T.processIfWorkletClass=void 0;var qn=Te(require("@babel/generator")),zn=Te(require("@babel/traverse")),l=require("@babel/types"),te=require("assert"),Ln=S(),Tn=M(),Nn=V(),Ne="__workletClass";function Un(e,t){return er(e,t)?(Xn(e.node.body),Bn(e,t),!0):!1}T.processIfWorkletClass=Un;function Bn(e,t){(0,te.strict)(e.node.id);let n=e.node.id.name,r=Gn(e.node,t);$n(r),Hn(r.program.body),Zn(r.program.body,n),r.program.body.push((0,l.returnStatement)((0,l.identifier)(n)));let i=(0,l.functionExpression)(null,[],(0,l.blockStatement)([...r.program.body])),s=(0,l.callExpression)(i,[]);(0,Tn.replaceWithFactoryCall)(e,n,s)}function Gn(e,t){let n=(0,qn.default)(e).code,r=(0,Nn.workletTransformSync)(n,{extraPlugins:["@babel/plugin-transform-class-properties","@babel/plugin-transform-classes","@babel/plugin-transform-unicode-regex"],filename:t.file.opts.filename,ast:!0,babelrc:!1,configFile:!1});return(0,te.strict)(r&&r.ast),r.ast}function Hn(e){e.forEach(t=>{if((0,l.isFunctionDeclaration)(t)){let n=(0,l.directive)((0,l.directiveLiteral)("worklet"));t.body.directives.push(n)}})}function Zn(e,t){let n=t+Ln.workletClassFactorySuffix,r=Vn(e,t),s=e[r].declarations[0].init,a=(0,l.functionDeclaration)((0,l.identifier)(n),[],(0,l.blockStatement)([(0,l.variableDeclaration)("const",[(0,l.variableDeclarator)((0,l.identifier)(t),s)]),(0,l.expressionStatement)((0,l.assignmentExpression)("=",(0,l.memberExpression)((0,l.identifier)(t),(0,l.identifier)(n)),(0,l.identifier)(n))),(0,l.returnStatement)((0,l.identifier)(t))],[(0,l.directive)((0,l.directiveLiteral)("worklet"))])),u=(0,l.variableDeclaration)("const",[(0,l.variableDeclarator)((0,l.identifier)(t),(0,l.callExpression)((0,l.identifier)(n),[]))]);e.splice(r,1,a,u)}function Vn(e,t){let n=e.findIndex(r=>(0,l.isVariableDeclaration)(r)&&r.declarations.some(i=>(0,l.isIdentifier)(i.id)&&i.id.name===t));return(0,te.strict)(n>=0),n}function Jn(e){return e.body.some(t=>(0,l.isClassProperty)(t)&&(0,l.isIdentifier)(t.key)&&t.key.name===Ne)}function Xn(e){e.body=e.body.filter(t=>!(0,l.isClassProperty)(t)||!(0,l.isIdentifier)(t.key)||t.key.name!==Ne)}function $n(e){let t=Yn(e),n=Kn(t),r=t.map(u=>u.index),i=n.map(u=>u.index),s=e.program.body,a=[...s];for(let u=0;u<t.length;u++){let p=i[u],d=r[u],O=a[p];s[d]=O}}function Yn(e){let t=[];return(0,zn.default)(e,{Program:{enter:n=>{n.get("body").forEach((i,s)=>{var a;let u=i.getBindingIdentifiers();if(!i.isFunctionDeclaration()||!(!((a=i.node.id)===null||a===void 0)&&a.name))return;let p={name:i.node.id.name,index:s,dependencies:new Set};t.push(p),i.traverse({Identifier(d){Qn(d,u,i)&&p.dependencies.add(d.node.name)}})})}}}),t}function Kn(e){let t=[],n=new Set;for(let r of e)Ue(r,e,t,n);return t}function Ue(e,t,n,r){if(r.has(e.name))throw new Error("Cycle detected. This should never happen.");if(!n.find(i=>i.name===e.name)){r.add(e.name);for(let i of e.dependencies)if(!n.find(s=>s.name===i)){let s=t.find(a=>a.name===i);(0,te.strict)(s),Ue(s,t,n,r)}n.push(e),r.delete(e.name)}}function Qn(e,t,n){return e.isReferencedIdentifier()&&!(e.node.name in t)&&!n.scope.hasOwnBinding(e.node.name)&&n.scope.hasReference(e.node.name)}function er(e,t){var n;let r=(n=e.node.id)===null||n===void 0?void 0:n.name,i=e.node;if(!r)return!1;let s=Jn(i.body),a=t.classesToWorkletize.some(d=>d.node===i),u=e.parentPath.isProgram()&&t.classesToWorkletize.some(d=>d.name===r);return t.classesToWorkletize=t.classesToWorkletize.filter(d=>d.node!==i&&d.name!==r),s||a||u}});var ue=b(E=>{"use strict";Object.defineProperty(E,"__esModule",{value:!0});E.isContextObject=E.processIfWorkletContextObject=E.contextObjectMarker=void 0;var h=require("@babel/types");E.contextObjectMarker="__workletContextObject";function tr(e,t){return Ge(e.node)?(rr(e.node),nr(e.node),!0):!1}E.processIfWorkletContextObject=tr;function Ge(e){return e.properties.some(t=>(0,h.isObjectProperty)(t)&&(0,h.isIdentifier)(t.key)&&t.key.name===E.contextObjectMarker)}E.isContextObject=Ge;function nr(e){let t=(0,h.functionExpression)(null,[],(0,h.blockStatement)([(0,h.returnStatement)((0,h.cloneNode)(e))],[(0,h.directive)((0,h.directiveLiteral)("worklet"))]));e.properties.push((0,h.objectProperty)((0,h.identifier)(`${E.contextObjectMarker}Factory`),t))}function rr(e){e.properties=e.properties.filter(t=>!((0,h.isObjectProperty)(t)&&(0,h.isIdentifier)(t.key)&&t.key.name===E.contextObjectMarker))}});var Xe=b(N=>{"use strict";Object.defineProperty(N,"__esModule",{value:!0});N.isImplicitContextObject=N.processIfWorkletFile=void 0;var m=require("@babel/types"),He=S(),Ze=ue();function ir(e,t){return e.node.directives.some(n=>n.value.value==="worklet")?(e.node.directives=e.node.directives.filter(n=>n.value.value!=="worklet"),or(e,t),!0):!1}N.processIfWorkletFile=ir;function or(e,t){let n=e.get("body");pr(e.node),n.forEach(r=>{let i=sr(r);de(i,t)})}function sr(e){return e.isExportNamedDeclaration()||e.isExportDefaultDeclaration()?e.get("declaration"):e}function de(e,t){var n;(0,He.isWorkletizableFunctionPath)(e)?(e.isArrowFunctionExpression()&&cr(e.node),Ve(e.node.body)):(0,He.isWorkletizableObjectPath)(e)?Je(e)?ur(e.node):lr(e,t):e.isVariableDeclaration()?ar(e,t):e.isClassDeclaration()&&(fr(e.node.body),!((n=e.node.id)===null||n===void 0)&&n.name&&t.classesToWorkletize.push({node:e.node,name:e.node.id.name}))}function ar(e,t){e.get("declarations").forEach(r=>{let i=r.get("init");i.isExpression()&&de(i,t)})}function lr(e,t){e.get("properties").forEach(r=>{if(r.isObjectMethod())Ve(r.node.body);else if(r.isObjectProperty()){let i=r.get("value");de(i,t)}})}function cr(e){(0,m.isBlockStatement)(e.body)||(e.body=(0,m.blockStatement)([(0,m.returnStatement)(e.body)]))}function Ve(e){e.directives.some(t=>t.value.value==="worklet")||e.directives.push((0,m.directive)((0,m.directiveLiteral)("worklet")))}function ur(e){e.properties.some(t=>(0,m.isObjectProperty)(t)&&(0,m.isIdentifier)(t.key)&&t.key.name===Ze.contextObjectMarker)||e.properties.push((0,m.objectProperty)((0,m.identifier)(`${Ze.contextObjectMarker}`),(0,m.booleanLiteral)(!0)))}function Je(e){return e.get("properties").some(n=>n.isObjectMethod()?dr(n):!1)}N.isImplicitContextObject=Je;function dr(e){let t=!1;return e.traverse({ThisExpression(n){t=!0,n.stop()}}),t}function fr(e){e.body.push((0,m.classProperty)((0,m.identifier)("__workletClass"),(0,m.booleanLiteral)(!0)))}function pr(e){let t=e.body,n=t.length,r=0;for(;r<n;){let i=t[r];if(!br(i)){r++;continue}let s=t.splice(r,1);t.push(...s),n--}}function br(e){return(0,m.isExpressionStatement)(e)&&(0,m.isAssignmentExpression)(e.expression)&&(0,m.isMemberExpression)(e.expression.left)&&(0,m.isIdentifier)(e.expression.left.object)&&e.expression.left.object.name==="exports"}});var $e=b(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.processInlineStylesWarning=void 0;var f=require("@babel/types"),mr=M(),fe=require("assert");function yr(e){return(0,f.callExpression)((0,f.arrowFunctionExpression)([],(0,f.blockStatement)([(0,f.expressionStatement)((0,f.callExpression)((0,f.memberExpression)((0,f.identifier)("console"),(0,f.identifier)("warn")),[(0,f.callExpression)((0,f.memberExpression)((0,f.callExpression)((0,f.identifier)("require"),[(0,f.stringLiteral)("react-native-reanimated")]),(0,f.identifier)("getUseOfValueInStyleWarning")),[])])),(0,f.returnStatement)(e.node)])),[])}function kr(e){e.isMemberExpression()&&(0,f.isIdentifier)(e.node.property)&&e.node.property.name==="value"&&e.replaceWith(yr(e))}function gr(e){if((0,f.isArrayExpression)(e.node)){let t=e.get("elements");(0,fe.strict)(Array.isArray(t),"[Reanimated] `elements` should be an array.");for(let n of t)n.isObjectExpression()&&pe(n)}}function pe(e){let t=e.get("properties");for(let n of t)if(n.isObjectProperty()){let r=n.get("value");(0,f.isIdentifier)(n.node.key)&&n.node.key.name==="transform"?gr(r):kr(r)}}function _r(e,t){if((0,mr.isRelease)()||t.opts.disableInlineStylesWarning||e.node.name.name!=="style"||!(0,f.isJSXExpressionContainer)(e.node.value))return;let n=e.get("value").get("expression");if((0,fe.strict)(!Array.isArray(n),"[Reanimated] `expression` should not be an array."),n.isArrayExpression()){let r=n.get("elements");(0,fe.strict)(Array.isArray(r),"[Reanimated] `elements` should be an array.");for(let i of r)i.isObjectExpression()&&pe(i)}else n.isObjectExpression()&&pe(n)}ne.processInlineStylesWarning=_r});var Ke=b(re=>{"use strict";Object.defineProperty(re,"__esModule",{value:!0});re.substituteWebCallExpression=void 0;var Ye=require("@babel/types");function Or(e){let t=e.node.callee;if((0,Ye.isIdentifier)(t)){let n=t.name;(n==="isWeb"||n==="shouldBeUseWeb")&&e.replaceWith((0,Ye.booleanLiteral)(!0))}}re.substituteWebCallExpression=Or});Object.defineProperty(exports,"__esModule",{value:!0});var Qe=Le(),vr=Be(),hr=ue(),Er=Xe(),Sr=ae(),Ir=$e(),Wr=S(),xr=Ke(),wr=X();module.exports=function(){function e(t){try{t()}catch(n){throw new Error(`[Reanimated] Babel plugin exception: ${n}`)}}return{name:"reanimated",pre(){e(()=>{(0,Sr.initializeState)(this)})},visitor:{CallExpression:{enter(t,n){e(()=>{(0,Qe.processCalleesAutoworkletizableCallbacks)(t,n),n.opts.substituteWebPlatformChecks&&(0,xr.substituteWebCallExpression)(t)})}},[Wr.WorkletizableFunction]:{enter(t,n){e(()=>{(0,wr.processIfWithWorkletDirective)(t,n)||(0,Qe.processIfAutoworkletizableCallback)(t,n)})}},ObjectExpression:{enter(t,n){e(()=>{(0,hr.processIfWorkletContextObject)(t,n)})}},ClassDeclaration:{enter(t,n){e(()=>{(0,vr.processIfWorkletClass)(t,n)})}},Program:{enter(t,n){e(()=>{(0,Er.processIfWorkletFile)(t,n)})}},JSXAttribute:{enter(t,n){e(()=>(0,Ir.processInlineStylesWarning)(t,n))}}}}};
//# sourceMappingURL=index.js.map
