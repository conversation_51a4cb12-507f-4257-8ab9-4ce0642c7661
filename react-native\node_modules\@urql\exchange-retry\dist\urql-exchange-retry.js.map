{"version": 3, "file": "urql-exchange-retry.js", "sources": ["../src/retryExchange.ts"], "sourcesContent": null, "names": ["retryExchange", "options", "retryIf", "retryWith", "MIN_DELAY", "initialDelayMs", "MAX_DELAY", "max<PERSON>elay<PERSON>", "MAX_ATTEMPTS", "maxNumberAttempts", "RANDOM_DELAY", "randomDelay", "forward", "dispatchDebug", "operations$", "source", "retry$", "next", "nextRetryOperation", "makeSubject", "retryWithBackoff$", "mergeMap", "operation", "retry", "context", "count", "delay", "retryCount", "delayAmount", "backoffFactor", "Math", "random", "min", "teardown$", "filter", "op", "kind", "key", "process", "env", "NODE_ENV", "type", "message", "data", "undefined", "takeUntil", "debounce", "fromValue", "makeOperation", "res", "error", "networkError", "maxNumberAttemptsExceeded", "merge"], "mappings": ";;;;;AAcA;;AAwEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAMA,aAAa,GAAGA,CAACC,OAA6B,GAAG,EAAE,KAAe;EAC7E,IAAM;IAAEC,OAAO;AAAEC,IAAAA,SAAAA;AAAU,GAAC,GAAGF,OAAO,CAAA;AACtC,EAAA,IAAMG,SAAS,GAAGH,OAAO,CAACI,cAAc,IAAI,IAAI,CAAA;AAChD,EAAA,IAAMC,SAAS,GAAGL,OAAO,CAACM,UAAU,IAAI,MAAM,CAAA;AAC9C,EAAA,IAAMC,YAAY,GAAGP,OAAO,CAACQ,iBAAiB,IAAI,CAAC,CAAA;AACnD,EAAA,IAAMC,YAAY,GAChBT,OAAO,CAACU,WAAW,IAAI,IAAI,GAAG,CAAC,CAACV,OAAO,CAACU,WAAW,GAAG,IAAI,CAAA;AAE5D,EAAA,OAAO,CAAC;IAAEC,OAAO;AAAEC,IAAAA,aAAAA;GAAe,KAChCC,WAAW,IAAI;IACb,IAAM;AAAEC,MAAAA,MAAM,EAAEC,MAAM;AAAEC,MAAAA,IAAI,EAAEC,kBAAAA;KAAoB,GAChDC,iBAAW,EAAa,CAAA;AAE1B,IAAA,IAAMC,iBAAiB,GAErBC,cAAQ,CAAEC,SAAoB,IAAK;AACjC,MAAA,IAAMC,KAAiB,GAAGD,SAAS,CAACE,OAAO,CAACD,KAAK,IAAI;AACnDE,QAAAA,KAAK,EAAE,CAAC;AACRC,QAAAA,KAAK,EAAE,IAAA;OACR,CAAA;AAED,MAAA,IAAMC,UAAU,GAAG,EAAEJ,KAAK,CAACE,KAAK,CAAA;AAChC,MAAA,IAAIG,WAAW,GAAGL,KAAK,CAACG,KAAK,IAAItB,SAAS,CAAA;MAE1C,IAAMyB,aAAa,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,CAAA;AACzC,MAAA,IAAIrB,YAAY,EAAE;AAChB;AACA;AACA,QAAA,IAAIkB,WAAW,GAAGC,aAAa,GAAGvB,SAAS,EAAE;AAC3CsB,UAAAA,WAAW,IAAIC,aAAa,CAAA;AAC9B,SAAC,MAAM;AACLD,UAAAA,WAAW,GAAGtB,SAAS,CAAA;AACzB,SAAA;AACF,OAAC,MAAM;AACL;QACAsB,WAAW,GAAGE,IAAI,CAACE,GAAG,CAACL,UAAU,GAAGvB,SAAS,EAAEE,SAAS,CAAC,CAAA;AAC3D,OAAA;;AAEA;MACAiB,KAAK,CAACG,KAAK,GAAGE,WAAW,CAAA;;AAEzB;AACA;AACA;AACA,MAAA,IAAMK,SAAS,GAEbC,YAAM,CAACC,EAAE,IAAI;AACX,QAAA,OACE,CAACA,EAAE,CAACC,IAAI,KAAK,OAAO,IAAID,EAAE,CAACC,IAAI,KAAK,UAAU,KAC9CD,EAAE,CAACE,GAAG,KAAKf,SAAS,CAACe,GAAG,CAAA;OAE3B,CAAC,CANFvB,WAAW,CAOZ,CAAA;AAEDwB,MAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAA3B,aAAa,CAAC;AACZ4B,QAAAA,IAAI,EAAE,cAAc;AACpBC,QAAAA,OAAO,EAAE,CAAA,yDAAA,EAA4Df,UAAU,CAAA,GAAA,EAAMnB,YAAY,CAAG,CAAA,CAAA;QACpGc,SAAS;AACTqB,QAAAA,IAAI,EAAE;UACJhB,UAAU;AACVC,UAAAA,WAAAA;SACD;AAAA,QAAA,QAAA,EAAA,eAAA;OACF,CAAC,GAAAgB,SAAA,CAAA;;AAEF;AACA,MAAA;AAQE;AACAC,QAAAA,eAAS,CAACZ,SAAS,CAAC,CAFpBa,cAAQ,CAAC,MAAMlB,WAAW,CAAC,CAN3BmB,eAAS,CACPC,kBAAa,CAAC1B,SAAS,CAACc,IAAI,EAAEd,SAAS,EAAE;UACvC,GAAGA,SAAS,CAACE,OAAO;AACpBD,UAAAA,KAAAA;AACF,SAAC,CACH,CAAC,CAAA,CAAA;AAAA,QAAA;KAKJ,CAAC,CA9DFP,MAAM,CA+DP,CAAA;IAED,OAGEkB,YAAM,CAACe,GAAG,IAAI;MACZ,IAAM1B,KAAK,GAAG0B,GAAG,CAAC3B,SAAS,CAACE,OAAO,CAACD,KAA+B,CAAA;AACnE;AACA;AACA,MAAA,IACE,CAAC0B,GAAG,CAACC,KAAK,KACThD,OAAO,GACJ,CAACA,OAAO,CAAC+C,GAAG,CAACC,KAAK,EAAED,GAAG,CAAC3B,SAAS,CAAC,GAClC,CAACnB,SAAS,IAAI,CAAC8C,GAAG,CAACC,KAAK,CAACC,YAAY,CAAC,EAC1C;AACA;AACA,QAAA,IAAI5B,KAAK,EAAE;UACTA,KAAK,CAACE,KAAK,GAAG,CAAC,CAAA;UACfF,KAAK,CAACG,KAAK,GAAG,IAAI,CAAA;AACpB,SAAA;AACA,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAM0B,yBAAyB,GAC7B,CAAE7B,KAAK,IAAIA,KAAK,CAACE,KAAK,IAAK,CAAC,KAAKjB,YAAY,GAAG,CAAC,CAAA;MACnD,IAAI,CAAC4C,yBAAyB,EAAE;AAC9B,QAAA,IAAM9B,UAAS,GAAGnB,SAAS,GACvBA,SAAS,CAAC8C,GAAG,CAACC,KAAK,EAAED,GAAG,CAAC3B,SAAS,CAAC,GACnC2B,GAAG,CAAC3B,SAAS,CAAA;AACjB,QAAA,IAAI,CAACA,UAAS,EAAE,OAAO,IAAI,CAAA;;AAE3B;AACA;QACAJ,kBAAkB,CAACI,UAAS,CAAC,CAAA;AAC7B,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AAEAgB,MAAAA,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAA,YAAA,GAAA3B,aAAa,CAAC;AACZ4B,QAAAA,IAAI,EAAE,gBAAgB;AACtBC,QAAAA,OAAO,EACL,mFAAmF;QACrFpB,SAAS,EAAE2B,GAAG,CAAC3B,SAAS;AAAA,QAAA,QAAA,EAAA,eAAA;OACzB,CAAC,GAAAsB,SAAA,CAAA;AAEF,MAAA,OAAO,IAAI,CAAA;KACZ,CAAC,CAzCFhC,OAAO,CADPyC,WAAK,CAAC,CAACvC,WAAW,EAAEM,iBAAiB,CAAC,CAAC,CAAA,CAAA,CAAA;GA4C1C,CAAA;AACL;;;;"}