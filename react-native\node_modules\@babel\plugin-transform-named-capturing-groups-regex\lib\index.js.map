{"version": 3, "names": ["_helperCreateRegexpFeaturesPlugin", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "api", "options", "runtime", "undefined", "Error", "createRegExpFeaturePlugin", "name", "feature"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\nimport { createRegExpFeaturePlugin } from \"@babel/helper-create-regexp-features-plugin\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  runtime?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  const { runtime } = options;\n  if (runtime !== undefined && typeof runtime !== \"boolean\") {\n    throw new Error(\"The 'runtime' option must be boolean\");\n  }\n\n  return createRegExpFeaturePlugin({\n    name: \"transform-named-capturing-groups-regex\",\n    feature: \"namedCaptureGroups\",\n    options: { runtime },\n  });\n});\n"], "mappings": ";;;;;;AACA,IAAAA,iCAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChD,MAAM;IAAEC;EAAQ,CAAC,GAAGD,OAAO;EAC3B,IAAIC,OAAO,KAAKC,SAAS,IAAI,OAAOD,OAAO,KAAK,SAAS,EAAE;IACzD,MAAM,IAAIE,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAEA,OAAO,IAAAC,2DAAyB,EAAC;IAC/BC,IAAI,EAAE,wCAAwC;IAC9CC,OAAO,EAAE,oBAAoB;IAC7BN,OAAO,EAAE;MAAEC;IAAQ;EACrB,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}