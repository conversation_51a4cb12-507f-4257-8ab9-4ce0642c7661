// Test simple de l'API
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/clients',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const jsonData = JSON.parse(data);
      console.log('\n📊 Réponse API:');
      console.log('Success:', jsonData.success);
      console.log('Count:', jsonData.count);
      console.log('Data type:', typeof jsonData.data);
      console.log('Data is array:', Array.isArray(jsonData.data));
      
      if (jsonData.data && jsonData.data.length > 0) {
        console.log('\n👤 Premier client:');
        console.log(JSON.stringify(jsonData.data[0], null, 2));
      } else {
        console.log('\n❌ Aucune donnée client');
        console.log('Structure complète:', JSON.stringify(jsonData, null, 2));
      }
    } catch (error) {
      console.error('❌ Erreur parsing JSON:', error.message);
      console.log('Raw data:', data);
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Erreur requête: ${e.message}`);
});

req.end();
