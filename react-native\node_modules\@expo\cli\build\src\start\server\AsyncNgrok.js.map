{"version": 3, "sources": ["../../../../src/start/server/AsyncNgrok.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport crypto from 'crypto';\nimport * as path from 'path';\nimport slugify from 'slugify';\n\nimport { getSettingsDirectory } from '../../api/user/UserSettings';\nimport { getActorDisplayName, getUserAsync } from '../../api/user/user';\nimport * as Log from '../../log';\nimport { delayAsync, resolveWithTimeout } from '../../utils/delay';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { isNgrokClientError, NgrokInstance, NgrokResolver } from '../doctor/ngrok/NgrokResolver';\nimport { hasAdbReverseAsync, startAdbReverseAsync } from '../platforms/android/adbReverse';\nimport { ProjectSettings } from '../project/settings';\n\nconst debug = require('debug')('expo:start:server:ngrok') as typeof console.log;\n\nconst NGROK_CONFIG = {\n  authToken: '5W1bR67GNbWcXqmxZzBG1_56GezNeaX6sSRvn8npeQ8',\n  domain: 'exp.direct',\n};\n\nconst TUNNEL_TIMEOUT = 10 * 1000;\n\nexport class AsyncNgrok {\n  /** Resolves the best instance of ngrok, exposed for testing. */\n  resolver: NgrokResolver;\n\n  /** Info about the currently running instance of ngrok. */\n  private serverUrl: string | null = null;\n\n  constructor(\n    private projectRoot: string,\n    private port: number\n  ) {\n    this.resolver = new NgrokResolver(projectRoot);\n  }\n\n  public getActiveUrl(): string | null {\n    return this.serverUrl;\n  }\n\n  /** Exposed for testing. */\n  async _getIdentifyingUrlSegmentsAsync(): Promise<string[]> {\n    const user = await getUserAsync();\n    if (user?.__typename === 'Robot') {\n      throw new CommandError('NGROK_ROBOT', 'Cannot use ngrok with a robot user.');\n    }\n    const username = getActorDisplayName(user);\n\n    return [\n      // NOTE: https://github.com/expo/expo/pull/16556#discussion_r822944286\n      await this.getProjectRandomnessAsync(),\n      // Strip out periods from the username to avoid subdomain issues with SSL certificates.\n      slugify(username, { remove: /\\./ }),\n      // Use the port to distinguish between multiple tunnels (webpack, metro).\n      String(this.port),\n    ];\n  }\n\n  /** Exposed for testing. */\n  async _getProjectHostnameAsync(): Promise<string> {\n    return `${(await this._getIdentifyingUrlSegmentsAsync()).join('-')}.${NGROK_CONFIG.domain}`;\n  }\n\n  /** Exposed for testing. */\n  async _getProjectSubdomainAsync(): Promise<string> {\n    return (await this._getIdentifyingUrlSegmentsAsync()).join('-');\n  }\n\n  /** Start ngrok on the given port for the project. */\n  async startAsync({ timeout }: { timeout?: number } = {}): Promise<void> {\n    // Ensure the instance is loaded first, this can linger so we should run it before the timeout.\n    await this.resolver.resolveAsync({\n      // For now, prefer global install since the package has native code (harder to install) and doesn't change very often.\n      prefersGlobalInstall: true,\n    });\n\n    // NOTE(EvanBacon): If the user doesn't have ADB installed,\n    // then skip attempting to reverse the port.\n    if (hasAdbReverseAsync()) {\n      // Ensure ADB reverse is running.\n      if (!(await startAdbReverseAsync([this.port]))) {\n        // TODO: Better error message.\n        throw new CommandError(\n          'NGROK_ADB',\n          `Cannot start tunnel URL because \\`adb reverse\\` failed for the connected Android device(s).`\n        );\n      }\n    }\n\n    this.serverUrl = await this._connectToNgrokAsync({ timeout });\n\n    debug('Tunnel URL:', this.serverUrl);\n    Log.log('Tunnel ready.');\n  }\n\n  /** Stop the ngrok process if it's running. */\n  public async stopAsync(): Promise<void> {\n    debug('Stopping Tunnel');\n\n    await this.resolver.get()?.kill?.();\n    this.serverUrl = null;\n  }\n\n  /** Exposed for testing. */\n  async _connectToNgrokAsync(\n    options: { timeout?: number } = {},\n    attempts: number = 0\n  ): Promise<string> {\n    // Attempt to stop any hanging processes, this increases the chances of a successful connection.\n    await this.stopAsync();\n\n    // Get the instance quietly or assert otherwise.\n    const instance = await this.resolver.resolveAsync({\n      shouldPrompt: false,\n      autoInstall: false,\n    });\n\n    // TODO(Bacon): Consider dropping the timeout functionality:\n    // https://github.com/expo/expo/pull/16556#discussion_r822307373\n    const results = await resolveWithTimeout(\n      () => this.connectToNgrokInternalAsync(instance, attempts),\n      {\n        timeout: options.timeout ?? TUNNEL_TIMEOUT,\n        errorMessage: 'ngrok tunnel took too long to connect.',\n      }\n    );\n    if (typeof results === 'string') {\n      return results;\n    }\n\n    // Wait 100ms and then try again\n    await delayAsync(100);\n\n    return this._connectToNgrokAsync(options, attempts + 1);\n  }\n\n  private async _getConnectionPropsAsync(): Promise<{ hostname?: string; subdomain?: string }> {\n    const userDefinedSubdomain = env.EXPO_TUNNEL_SUBDOMAIN;\n    if (userDefinedSubdomain) {\n      const subdomain =\n        typeof userDefinedSubdomain === 'string'\n          ? userDefinedSubdomain\n          : await this._getProjectSubdomainAsync();\n      debug('Subdomain:', subdomain);\n      return { subdomain };\n    } else {\n      const hostname = await this._getProjectHostnameAsync();\n      debug('Hostname:', hostname);\n      return { hostname };\n    }\n  }\n\n  private async connectToNgrokInternalAsync(\n    instance: NgrokInstance,\n    attempts: number = 0\n  ): Promise<string | false> {\n    try {\n      // Global config path.\n      const configPath = path.join(getSettingsDirectory(), 'ngrok.yml');\n      debug('Global config path:', configPath);\n      const urlProps = await this._getConnectionPropsAsync();\n\n      const url = await instance.connect({\n        ...urlProps,\n        authtoken: NGROK_CONFIG.authToken,\n        configPath,\n        onStatusChange(status) {\n          if (status === 'closed') {\n            Log.error(\n              chalk.red(\n                'Tunnel connection has been closed. This is often related to intermittent connection problems with the Ngrok servers. Restart the dev server to try connecting to Ngrok again.'\n              ) + chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n            );\n          } else if (status === 'connected') {\n            Log.log('Tunnel connected.');\n          }\n        },\n        port: this.port,\n      });\n      return url;\n    } catch (error: any) {\n      const assertNgrok = () => {\n        if (isNgrokClientError(error)) {\n          throw new CommandError(\n            'NGROK_CONNECT',\n            [\n              error.body.msg,\n              error.body.details?.err,\n              chalk.gray('Check the Ngrok status page for outages: https://status.ngrok.com/'),\n            ]\n              .filter(Boolean)\n              .join('\\n\\n')\n          );\n        }\n        throw new CommandError(\n          'NGROK_CONNECT',\n          error.toString() +\n            chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n        );\n      };\n\n      // Attempt to connect 3 times\n      if (attempts >= 2) {\n        assertNgrok();\n      }\n\n      // Attempt to fix the issue\n      if (isNgrokClientError(error) && error.body.error_code === 103) {\n        // Assert early if a custom subdomain is used since it cannot\n        // be changed and retried. If the tunnel subdomain is a boolean\n        // then we can reset the randomness and try again.\n        if (typeof env.EXPO_TUNNEL_SUBDOMAIN === 'string') {\n          assertNgrok();\n        }\n        // Change randomness to avoid conflict if killing ngrok doesn't help\n        await this._resetProjectRandomnessAsync();\n      }\n\n      return false;\n    }\n  }\n\n  private async getProjectRandomnessAsync() {\n    const { urlRandomness: randomness } = await ProjectSettings.readAsync(this.projectRoot);\n    if (randomness && /^[A-Za-z0-9]/.test(randomness)) {\n      return randomness;\n    }\n    return await this._resetProjectRandomnessAsync();\n  }\n\n  async _resetProjectRandomnessAsync() {\n    let randomness: string;\n    do {\n      randomness = crypto.randomBytes(5).toString('base64url');\n    } while (randomness.startsWith('_')); // _ is an invalid character for a hostname\n\n    await ProjectSettings.setAsync(this.projectRoot, { urlRandomness: randomness });\n    debug('Resetting project randomness:', randomness);\n    return randomness;\n  }\n}\n"], "names": ["AsyncNgrok", "debug", "require", "NGROK_CONFIG", "authToken", "domain", "TUNNEL_TIMEOUT", "constructor", "projectRoot", "port", "serverUrl", "resolver", "NgrokResolver", "getActiveUrl", "_getIdentifyingUrlSegmentsAsync", "user", "getUserAsync", "__typename", "CommandError", "username", "getActorDisplayName", "getProjectRandomnessAsync", "slugify", "remove", "String", "_getProjectHostnameAsync", "join", "_getProjectSubdomainAsync", "startAsync", "timeout", "resolveAsync", "prefersGlobalInstall", "hasAdbReverseAsync", "startAdbReverseAsync", "_connectToNgrokAsync", "Log", "log", "stopAsync", "get", "kill", "options", "attempts", "instance", "should<PERSON>rompt", "autoInstall", "results", "resolveWithTimeout", "connectToNgrokInternalAsync", "errorMessage", "delayAsync", "_getConnectionPropsAsync", "userDefinedSubdomain", "env", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "hostname", "config<PERSON><PERSON>", "path", "getSettingsDirectory", "urlProps", "url", "connect", "authtoken", "onStatusChange", "status", "error", "chalk", "red", "gray", "assertNgrok", "isNgrokClientError", "body", "msg", "details", "err", "filter", "Boolean", "toString", "error_code", "_resetProjectRandomnessAsync", "url<PERSON><PERSON><PERSON><PERSON>", "randomness", "ProjectSettings", "readAsync", "test", "crypto", "randomBytes", "startsWith", "setAsync"], "mappings": ";;;;+BAwBaA;;;eAAAA;;;;gEAxBK;;;;;;;gEACC;;;;;;;iEACG;;;;;;;gEACF;;;;;;8BAEiB;sBACa;6DAC7B;uBAC0B;qBAC3B;wBACS;+BACoC;4BACR;0BACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,eAAe;IACnBC,WAAW;IACXC,QAAQ;AACV;AAEA,MAAMC,iBAAiB,KAAK;AAErB,MAAMN;IAOXO,YACE,AAAQC,WAAmB,EAC3B,AAAQC,IAAY,CACpB;aAFQD,cAAAA;aACAC,OAAAA;aAJFC,YAA2B;QAMjC,IAAI,CAACC,QAAQ,GAAG,IAAIC,4BAAa,CAACJ;IACpC;IAEOK,eAA8B;QACnC,OAAO,IAAI,CAACH,SAAS;IACvB;IAEA,yBAAyB,GACzB,MAAMI,kCAAqD;QACzD,MAAMC,OAAO,MAAMC,IAAAA,kBAAY;QAC/B,IAAID,CAAAA,wBAAAA,KAAME,UAAU,MAAK,SAAS;YAChC,MAAM,IAAIC,oBAAY,CAAC,eAAe;QACxC;QACA,MAAMC,WAAWC,IAAAA,yBAAmB,EAACL;QAErC,OAAO;YACL,sEAAsE;YACtE,MAAM,IAAI,CAACM,yBAAyB;YACpC,uFAAuF;YACvFC,IAAAA,kBAAO,EAACH,UAAU;gBAAEI,QAAQ;YAAK;YACjC,yEAAyE;YACzEC,OAAO,IAAI,CAACf,IAAI;SACjB;IACH;IAEA,yBAAyB,GACzB,MAAMgB,2BAA4C;QAChD,OAAO,GAAG,AAAC,CAAA,MAAM,IAAI,CAACX,+BAA+B,EAAC,EAAGY,IAAI,CAAC,KAAK,CAAC,EAAEvB,aAAaE,MAAM,EAAE;IAC7F;IAEA,yBAAyB,GACzB,MAAMsB,4BAA6C;QACjD,OAAO,AAAC,CAAA,MAAM,IAAI,CAACb,+BAA+B,EAAC,EAAGY,IAAI,CAAC;IAC7D;IAEA,mDAAmD,GACnD,MAAME,WAAW,EAAEC,OAAO,EAAwB,GAAG,CAAC,CAAC,EAAiB;QACtE,+FAA+F;QAC/F,MAAM,IAAI,CAAClB,QAAQ,CAACmB,YAAY,CAAC;YAC/B,sHAAsH;YACtHC,sBAAsB;QACxB;QAEA,2DAA2D;QAC3D,4CAA4C;QAC5C,IAAIC,IAAAA,8BAAkB,KAAI;YACxB,iCAAiC;YACjC,IAAI,CAAE,MAAMC,IAAAA,gCAAoB,EAAC;gBAAC,IAAI,CAACxB,IAAI;aAAC,GAAI;gBAC9C,8BAA8B;gBAC9B,MAAM,IAAIS,oBAAY,CACpB,aACA,CAAC,2FAA2F,CAAC;YAEjG;QACF;QAEA,IAAI,CAACR,SAAS,GAAG,MAAM,IAAI,CAACwB,oBAAoB,CAAC;YAAEL;QAAQ;QAE3D5B,MAAM,eAAe,IAAI,CAACS,SAAS;QACnCyB,KAAIC,GAAG,CAAC;IACV;IAEA,4CAA4C,GAC5C,MAAaC,YAA2B;YAGhC,yBAAA;QAFNpC,MAAM;QAEN,QAAM,qBAAA,IAAI,CAACU,QAAQ,CAAC2B,GAAG,wBAAjB,0BAAA,mBAAqBC,IAAI,qBAAzB,6BAAA;QACN,IAAI,CAAC7B,SAAS,GAAG;IACnB;IAEA,yBAAyB,GACzB,MAAMwB,qBACJM,UAAgC,CAAC,CAAC,EAClCC,WAAmB,CAAC,EACH;QACjB,gGAAgG;QAChG,MAAM,IAAI,CAACJ,SAAS;QAEpB,gDAAgD;QAChD,MAAMK,WAAW,MAAM,IAAI,CAAC/B,QAAQ,CAACmB,YAAY,CAAC;YAChDa,cAAc;YACdC,aAAa;QACf;QAEA,4DAA4D;QAC5D,gEAAgE;QAChE,MAAMC,UAAU,MAAMC,IAAAA,yBAAkB,EACtC,IAAM,IAAI,CAACC,2BAA2B,CAACL,UAAUD,WACjD;YACEZ,SAASW,QAAQX,OAAO,IAAIvB;YAC5B0C,cAAc;QAChB;QAEF,IAAI,OAAOH,YAAY,UAAU;YAC/B,OAAOA;QACT;QAEA,gCAAgC;QAChC,MAAMI,IAAAA,iBAAU,EAAC;QAEjB,OAAO,IAAI,CAACf,oBAAoB,CAACM,SAASC,WAAW;IACvD;IAEA,MAAcS,2BAA+E;QAC3F,MAAMC,uBAAuBC,QAAG,CAACC,qBAAqB;QACtD,IAAIF,sBAAsB;YACxB,MAAMG,YACJ,OAAOH,yBAAyB,WAC5BA,uBACA,MAAM,IAAI,CAACxB,yBAAyB;YAC1C1B,MAAM,cAAcqD;YACpB,OAAO;gBAAEA;YAAU;QACrB,OAAO;YACL,MAAMC,WAAW,MAAM,IAAI,CAAC9B,wBAAwB;YACpDxB,MAAM,aAAasD;YACnB,OAAO;gBAAEA;YAAS;QACpB;IACF;IAEA,MAAcR,4BACZL,QAAuB,EACvBD,WAAmB,CAAC,EACK;QACzB,IAAI;YACF,sBAAsB;YACtB,MAAMe,aAAaC,QAAK/B,IAAI,CAACgC,IAAAA,kCAAoB,KAAI;YACrDzD,MAAM,uBAAuBuD;YAC7B,MAAMG,WAAW,MAAM,IAAI,CAACT,wBAAwB;YAEpD,MAAMU,MAAM,MAAMlB,SAASmB,OAAO,CAAC;gBACjC,GAAGF,QAAQ;gBACXG,WAAW3D,aAAaC,SAAS;gBACjCoD;gBACAO,gBAAeC,MAAM;oBACnB,IAAIA,WAAW,UAAU;wBACvB7B,KAAI8B,KAAK,CACPC,gBAAK,CAACC,GAAG,CACP,mLACED,gBAAK,CAACE,IAAI,CAAC;oBAEnB,OAAO,IAAIJ,WAAW,aAAa;wBACjC7B,KAAIC,GAAG,CAAC;oBACV;gBACF;gBACA3B,MAAM,IAAI,CAACA,IAAI;YACjB;YACA,OAAOmD;QACT,EAAE,OAAOK,OAAY;YACnB,MAAMI,cAAc;gBAClB,IAAIC,IAAAA,iCAAkB,EAACL,QAAQ;wBAKzBA;oBAJJ,MAAM,IAAI/C,oBAAY,CACpB,iBACA;wBACE+C,MAAMM,IAAI,CAACC,GAAG;yBACdP,sBAAAA,MAAMM,IAAI,CAACE,OAAO,qBAAlBR,oBAAoBS,GAAG;wBACvBR,gBAAK,CAACE,IAAI,CAAC;qBACZ,CACEO,MAAM,CAACC,SACPlD,IAAI,CAAC;gBAEZ;gBACA,MAAM,IAAIR,oBAAY,CACpB,iBACA+C,MAAMY,QAAQ,KACZX,gBAAK,CAACE,IAAI,CAAC;YAEjB;YAEA,6BAA6B;YAC7B,IAAI3B,YAAY,GAAG;gBACjB4B;YACF;YAEA,2BAA2B;YAC3B,IAAIC,IAAAA,iCAAkB,EAACL,UAAUA,MAAMM,IAAI,CAACO,UAAU,KAAK,KAAK;gBAC9D,6DAA6D;gBAC7D,+DAA+D;gBAC/D,kDAAkD;gBAClD,IAAI,OAAO1B,QAAG,CAACC,qBAAqB,KAAK,UAAU;oBACjDgB;gBACF;gBACA,oEAAoE;gBACpE,MAAM,IAAI,CAACU,4BAA4B;YACzC;YAEA,OAAO;QACT;IACF;IAEA,MAAc1D,4BAA4B;QACxC,MAAM,EAAE2D,eAAeC,UAAU,EAAE,GAAG,MAAMC,yBAAe,CAACC,SAAS,CAAC,IAAI,CAAC3E,WAAW;QACtF,IAAIyE,cAAc,eAAeG,IAAI,CAACH,aAAa;YACjD,OAAOA;QACT;QACA,OAAO,MAAM,IAAI,CAACF,4BAA4B;IAChD;IAEA,MAAMA,+BAA+B;QACnC,IAAIE;QACJ,GAAG;YACDA,aAAaI,iBAAM,CAACC,WAAW,CAAC,GAAGT,QAAQ,CAAC;QAC9C,QAASI,WAAWM,UAAU,CAAC,MAAM,CAAC,2CAA2C;QAEjF,MAAML,yBAAe,CAACM,QAAQ,CAAC,IAAI,CAAChF,WAAW,EAAE;YAAEwE,eAAeC;QAAW;QAC7EhF,MAAM,iCAAiCgF;QACvC,OAAOA;IACT;AACF"}