# @react-native/gradle-plugin

[![Version][version-badge]][package]

A Gradle Plugin used to support development of React Native applications for Android.

## Installation

```
yarn add @react-native/gradle-plugin
```

*Note: We're using `yarn` to install deps. Feel free to change commands to use `npm` 3+ and `npx` if you like*

[version-badge]: https://img.shields.io/npm/v/@react-native/gradle-plugin?style=flat-square
[package]: https://www.npmjs.com/package/@react-native/gradle-plugin

## Testing

To run the tests in this package, run the following commands from the React Native root folder:

1. `yarn` to install the dependencies. You just need to run this once
2. `./gradlew -p packages/gradle-plugin test`.
