{"version": 3, "sources": ["Vector.ts"], "names": ["Vector", "constructor", "x", "y", "_magnitude", "Math", "hypot", "isMagnitudeSufficient", "MINIMAL_RECOGNIZABLE_MAGNITUDE", "unitX", "unitY", "fromDirection", "direction", "DirectionToVectorMappings", "get", "fromVelocity", "tracker", "pointerId", "velocity", "getVelocity", "magnitude", "computeSimilarity", "vector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "Map", "Directions", "LEFT", "RIGHT", "UP", "DOWN", "DiagonalDirections", "UP_RIGHT", "DOWN_RIGHT", "UP_LEFT", "DOWN_LEFT"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAGe,MAAMA,MAAN,CAAa;AAO1BC,EAAAA,WAAW,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAChC,SAAKD,CAAL,GAASA,CAAT;AACA,SAAKC,CAAL,GAASA,CAAT;AAEA,SAAKC,UAAL,GAAkBC,IAAI,CAACC,KAAL,CAAW,KAAKJ,CAAhB,EAAmB,KAAKC,CAAxB,CAAlB;AACA,UAAMI,qBAAqB,GACzB,KAAKH,UAAL,GAAkBI,yCADpB;AAGA,SAAKC,KAAL,GAAaF,qBAAqB,GAAG,KAAKL,CAAL,GAAS,KAAKE,UAAjB,GAA8B,CAAhE;AACA,SAAKM,KAAL,GAAaH,qBAAqB,GAAG,KAAKJ,CAAL,GAAS,KAAKC,UAAjB,GAA8B,CAAhE;AACD;;AAEmB,SAAbO,aAAa,CAACC,SAAD,EAAqD;AAAA;;AACvE,oCAAOC,yBAAyB,CAACC,GAA1B,CAA8BF,SAA9B,CAAP,yEAAmD,IAAIZ,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAnD;AACD;;AAEkB,SAAZe,YAAY,CAACC,OAAD,EAA0BC,SAA1B,EAA6C;AAC9D,UAAMC,QAAQ,GAAGF,OAAO,CAACG,WAAR,CAAoBF,SAApB,CAAjB;AACA,WAAO,IAAIjB,MAAJ,CAAWkB,QAAQ,CAAChB,CAApB,EAAuBgB,QAAQ,CAACf,CAAhC,CAAP;AACD;;AAEY,MAATiB,SAAS,GAAG;AACd,WAAO,KAAKhB,UAAZ;AACD;;AAEDiB,EAAAA,iBAAiB,CAACC,MAAD,EAAiB;AAChC,WAAO,KAAKb,KAAL,GAAaa,MAAM,CAACb,KAApB,GAA4B,KAAKC,KAAL,GAAaY,MAAM,CAACZ,KAAvD;AACD;;AAEDa,EAAAA,SAAS,CAACD,MAAD,EAAiBE,SAAjB,EAAoC;AAC3C,WAAO,KAAKH,iBAAL,CAAuBC,MAAvB,IAAiCE,SAAxC;AACD;;AAtCyB;;;AAyC5B,MAAMX,yBAAyB,GAAG,IAAIY,GAAJ,CAGhC,CACA,CAACC,uBAAWC,IAAZ,EAAkB,IAAI3B,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAf,CAAlB,CADA,EAEA,CAAC0B,uBAAWE,KAAZ,EAAmB,IAAI5B,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAnB,CAFA,EAGA,CAAC0B,uBAAWG,EAAZ,EAAgB,IAAI7B,MAAJ,CAAW,CAAX,EAAc,CAAC,CAAf,CAAhB,CAHA,EAIA,CAAC0B,uBAAWI,IAAZ,EAAkB,IAAI9B,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAlB,CAJA,EAMA,CAAC+B,+BAAmBC,QAApB,EAA8B,IAAIhC,MAAJ,CAAW,CAAX,EAAc,CAAC,CAAf,CAA9B,CANA,EAOA,CAAC+B,+BAAmBE,UAApB,EAAgC,IAAIjC,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAhC,CAPA,EAQA,CAAC+B,+BAAmBG,OAApB,EAA6B,IAAIlC,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAC,CAAhB,CAA7B,CARA,EASA,CAAC+B,+BAAmBI,SAApB,EAA+B,IAAInC,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAf,CAA/B,CATA,CAHgC,CAAlC", "sourcesContent": ["import { DiagonalDirections, Directions } from '../../Directions';\nimport { MINIMAL_RECOGNIZABLE_MAGNITUDE } from '../constants';\nimport PointerTracker from './PointerTracker';\n\nexport default class Vector {\n  private readonly x;\n  private readonly y;\n  private readonly unitX;\n  private readonly unitY;\n  private readonly _magnitude;\n\n  constructor(x: number, y: number) {\n    this.x = x;\n    this.y = y;\n\n    this._magnitude = Math.hypot(this.x, this.y);\n    const isMagnitudeSufficient =\n      this._magnitude > MINIMAL_RECOGNIZABLE_MAGNITUDE;\n\n    this.unitX = isMagnitudeSufficient ? this.x / this._magnitude : 0;\n    this.unitY = isMagnitudeSufficient ? this.y / this._magnitude : 0;\n  }\n\n  static fromDirection(direction: Directions | DiagonalDirections): Vector {\n    return DirectionToVectorMappings.get(direction) ?? new Vector(0, 0);\n  }\n\n  static fromVelocity(tracker: PointerTracker, pointerId: number) {\n    const velocity = tracker.getVelocity(pointerId);\n    return new Vector(velocity.x, velocity.y);\n  }\n\n  get magnitude() {\n    return this._magnitude;\n  }\n\n  computeSimilarity(vector: Vector) {\n    return this.unitX * vector.unitX + this.unitY * vector.unitY;\n  }\n\n  isSimilar(vector: Vector, threshold: number) {\n    return this.computeSimilarity(vector) > threshold;\n  }\n}\n\nconst DirectionToVectorMappings = new Map<\n  Directions | DiagonalDirections,\n  Vector\n>([\n  [Directions.LEFT, new Vector(-1, 0)],\n  [Directions.RIGHT, new Vector(1, 0)],\n  [Directions.UP, new Vector(0, -1)],\n  [Directions.DOWN, new Vector(0, 1)],\n\n  [DiagonalDirections.UP_RIGHT, new Vector(1, -1)],\n  [DiagonalDirections.DOWN_RIGHT, new Vector(1, 1)],\n  [DiagonalDirections.UP_LEFT, new Vector(-1, -1)],\n  [DiagonalDirections.DOWN_LEFT, new Vector(-1, 1)],\n]);\n"]}