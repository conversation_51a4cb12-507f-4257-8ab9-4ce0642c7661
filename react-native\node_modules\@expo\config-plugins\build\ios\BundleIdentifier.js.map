{"version": 3, "file": "BundleIdentifier.js", "names": ["_plist", "data", "_interopRequireDefault", "require", "_assert", "_fs", "_xcode", "_Paths", "_Target", "_Xcodeproj", "_string", "_iosPlugins", "e", "__esModule", "default", "withBundleIdentifier", "config", "bundleIdentifier", "withXcodeProject", "bundleId", "ios", "assert", "modResults", "updateBundleIdentifierForPbxprojObject", "exports", "getBundleIdentifier", "setBundleIdentifier", "infoPlist", "CFBundleIdentifier", "getBundleIdentifierFromPbxproj", "projectRoot", "targetName", "buildConfiguration", "pbxproj<PERSON><PERSON>", "getPBXProjectPath", "project", "xcode", "parseSync", "xcBuildConfiguration", "getXCBuildConfigurationFromPbxproj", "getProductBundleIdentifierFromBuildConfiguration", "bundleIdentifierRaw", "buildSettings", "PRODUCT_BUNDLE_IDENTIFIER", "trimQuotes", "resolveXcodeBuildSetting", "setting", "updateBundleIdentifierForPbxproj", "updateProductName", "fs", "writeFileSync", "writeSync", "nativeTarget", "findFirstNativeTarget", "getBuildConfigurationsForListId", "buildConfigurationList", "for<PERSON>ach", "item", "productName", "split", "pop", "includes", "PRODUCT_NAME", "setBundleIdentifierForPbxproj", "pbxproj<PERSON><PERSON><PERSON>", "getAllPBXProjectPaths", "defaultBundleId", "resetAllPlistBundleIdentifiers", "infoPlistPaths", "getAllInfoPlistPaths", "plist<PERSON><PERSON>", "resetPlistBundleIdentifier", "rawPlist", "readFileSync", "plistObject", "plist", "parse", "format", "pretty", "indent", "xml", "build"], "sources": ["../../src/ios/BundleIdentifier.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport plist, { PlistObject } from '@expo/plist';\nimport assert from 'assert';\nimport fs from 'fs';\nimport xcode, { XCBuildConfiguration } from 'xcode';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { getAllInfoPlistPaths, getAllPBXProjectPaths, getPBXProjectPath } from './Paths';\nimport { findFirstNativeTarget, getXCBuildConfigurationFromPbxproj } from './Target';\nimport { ConfigPlugin, XcodeProject } from '../Plugin.types';\nimport {\n  ConfigurationSectionEntry,\n  getBuildConfigurationsForListId,\n  resolveXcodeBuildSetting,\n} from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\nimport { withXcodeProject } from '../plugins/ios-plugins';\n\nexport const withBundleIdentifier: ConfigPlugin<{ bundleIdentifier?: string }> = (\n  config,\n  { bundleIdentifier }\n) => {\n  return withXcodeProject(config, async (config) => {\n    const bundleId = bundleIdentifier ?? config.ios?.bundleIdentifier;\n    // Should never happen.\n    assert(\n      bundleId,\n      '`bundleIdentifier` must be defined in the app config (`ios.bundleIdentifier`) or passed to the plugin `withBundleIdentifier`.'\n    );\n\n    config.modResults = updateBundleIdentifierForPbxprojObject(config.modResults, bundleId, false);\n\n    return config;\n  });\n};\n\nfunction getBundleIdentifier(config: Pick<ExpoConfig, 'ios'>): string | null {\n  return config.ios?.bundleIdentifier ?? null;\n}\n\n/**\n * In Turtle v1 we set the bundleIdentifier directly on Info.plist rather\n * than in pbxproj\n */\nfunction setBundleIdentifier(config: ExpoConfig, infoPlist: InfoPlist): InfoPlist {\n  const bundleIdentifier = getBundleIdentifier(config);\n\n  if (!bundleIdentifier) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleIdentifier: bundleIdentifier,\n  };\n}\n\n/**\n * Gets the bundle identifier defined in the Xcode project found in the project directory.\n *\n * A bundle identifier is stored as a value in XCBuildConfiguration entry.\n * Those entries exist for every pair (build target, build configuration).\n * Unless target name is passed, the first target defined in the pbxproj is used\n * (to keep compatibility with the inaccurate legacy implementation of this function).\n * The build configuration is usually 'Release' or 'Debug'. However, it could be any arbitrary string.\n * Defaults to 'Release'.\n *\n * @param {string} projectRoot Path to project root containing the ios directory\n * @param {string} targetName Target name\n * @param {string} buildConfiguration Build configuration. Defaults to 'Release'.\n * @returns {string | null} bundle identifier of the Xcode project or null if the project is not configured\n */\nfunction getBundleIdentifierFromPbxproj(\n  projectRoot: string,\n  {\n    targetName,\n    buildConfiguration = 'Release',\n  }: { targetName?: string; buildConfiguration?: string } = {}\n): string | null {\n  let pbxprojPath: string;\n  try {\n    pbxprojPath = getPBXProjectPath(projectRoot);\n  } catch {\n    return null;\n  }\n  const project = xcode.project(pbxprojPath);\n  project.parseSync();\n\n  const xcBuildConfiguration = getXCBuildConfigurationFromPbxproj(project, {\n    targetName,\n    buildConfiguration,\n  });\n  if (!xcBuildConfiguration) {\n    return null;\n  }\n  return getProductBundleIdentifierFromBuildConfiguration(xcBuildConfiguration);\n}\n\nfunction getProductBundleIdentifierFromBuildConfiguration(\n  xcBuildConfiguration: XCBuildConfiguration\n): string | null {\n  const bundleIdentifierRaw = xcBuildConfiguration.buildSettings.PRODUCT_BUNDLE_IDENTIFIER;\n  if (bundleIdentifierRaw) {\n    const bundleIdentifier = trimQuotes(bundleIdentifierRaw);\n    return resolveXcodeBuildSetting(\n      bundleIdentifier,\n      (setting) => xcBuildConfiguration.buildSettings[setting] as string | undefined\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * Updates the bundle identifier for a given pbxproj\n *\n * @param {string} pbxprojPath Path to pbxproj file\n * @param {string} bundleIdentifier Bundle identifier to set in the pbxproj\n * @param {boolean} [updateProductName=true]  Whether to update PRODUCT_NAME\n */\nfunction updateBundleIdentifierForPbxproj(\n  pbxprojPath: string,\n  bundleIdentifier: string,\n  updateProductName: boolean = true\n): void {\n  const project = xcode.project(pbxprojPath);\n  project.parseSync();\n  fs.writeFileSync(\n    pbxprojPath,\n    updateBundleIdentifierForPbxprojObject(project, bundleIdentifier, updateProductName).writeSync()\n  );\n}\n\n/**\n * Updates the bundle identifier for a given pbxproj\n *\n * @param {string} project pbxproj file\n * @param {string} bundleIdentifier Bundle identifier to set in the pbxproj\n * @param {boolean} [updateProductName=true]  Whether to update PRODUCT_NAME\n */\nfunction updateBundleIdentifierForPbxprojObject(\n  project: XcodeProject,\n  bundleIdentifier: string,\n  updateProductName: boolean = true\n) {\n  const [, nativeTarget] = findFirstNativeTarget(project);\n\n  getBuildConfigurationsForListId(project, nativeTarget.buildConfigurationList).forEach(\n    ([, item]: ConfigurationSectionEntry) => {\n      if (item.buildSettings.PRODUCT_BUNDLE_IDENTIFIER === bundleIdentifier) {\n        return;\n      }\n\n      item.buildSettings.PRODUCT_BUNDLE_IDENTIFIER = `\"${bundleIdentifier}\"`;\n\n      if (updateProductName) {\n        const productName = bundleIdentifier.split('.').pop();\n        if (!productName?.includes('$')) {\n          item.buildSettings.PRODUCT_NAME = productName;\n        }\n      }\n    }\n  );\n  return project;\n}\n\n/**\n * Updates the bundle identifier for pbx projects inside the ios directory of the given project root\n *\n * @param {string} projectRoot Path to project root containing the ios directory\n * @param {string} bundleIdentifier Desired bundle identifier\n * @param {boolean} [updateProductName=true]  Whether to update PRODUCT_NAME\n */\nfunction setBundleIdentifierForPbxproj(\n  projectRoot: string,\n  bundleIdentifier: string,\n  updateProductName: boolean = true\n): void {\n  // Get all pbx projects in the ${projectRoot}/ios directory\n  let pbxprojPaths: string[] = [];\n  try {\n    pbxprojPaths = getAllPBXProjectPaths(projectRoot);\n  } catch {}\n\n  for (const pbxprojPath of pbxprojPaths) {\n    updateBundleIdentifierForPbxproj(pbxprojPath, bundleIdentifier, updateProductName);\n  }\n}\n\n/**\n * Reset bundle identifier field in Info.plist to use PRODUCT_BUNDLE_IDENTIFIER, as recommended by Apple.\n */\n\nconst defaultBundleId = '$(PRODUCT_BUNDLE_IDENTIFIER)';\n\nfunction resetAllPlistBundleIdentifiers(projectRoot: string): void {\n  const infoPlistPaths = getAllInfoPlistPaths(projectRoot);\n\n  for (const plistPath of infoPlistPaths) {\n    resetPlistBundleIdentifier(plistPath);\n  }\n}\n\nfunction resetPlistBundleIdentifier(plistPath: string): void {\n  const rawPlist = fs.readFileSync(plistPath, 'utf8');\n  const plistObject = plist.parse(rawPlist) as PlistObject;\n\n  if (plistObject.CFBundleIdentifier) {\n    if (plistObject.CFBundleIdentifier === defaultBundleId) return;\n\n    // attempt to match default Info.plist format\n    const format = { pretty: true, indent: `\\t` };\n\n    const xml = plist.build(\n      {\n        ...plistObject,\n        CFBundleIdentifier: defaultBundleId,\n      },\n      format\n    );\n\n    if (xml !== rawPlist) {\n      fs.writeFileSync(plistPath, xml);\n    }\n  }\n}\n\nexport {\n  getBundleIdentifier,\n  setBundleIdentifier,\n  getBundleIdentifierFromPbxproj,\n  updateBundleIdentifierForPbxproj,\n  setBundleIdentifierForPbxproj,\n  resetAllPlistBundleIdentifiers,\n  resetPlistBundleIdentifier,\n};\n"], "mappings": ";;;;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,OAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAM,OAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,MAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAQ,WAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,UAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,SAAAS,QAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,OAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,YAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,WAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,SAAAC,uBAAAU,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEnD,MAAMG,oBAAiE,GAAGA,CAC/EC,MAAM,EACN;EAAEC;AAAiB,CAAC,KACjB;EACH,OAAO,IAAAC,8BAAgB,EAACF,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChD,MAAMG,QAAQ,GAAGF,gBAAgB,IAAID,MAAM,CAACI,GAAG,EAAEH,gBAAgB;IACjE;IACA,IAAAI,iBAAM,EACJF,QAAQ,EACR,+HACF,CAAC;IAEDH,MAAM,CAACM,UAAU,GAAGC,sCAAsC,CAACP,MAAM,CAACM,UAAU,EAAEH,QAAQ,EAAE,KAAK,CAAC;IAE9F,OAAOH,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACQ,OAAA,CAAAT,oBAAA,GAAAA,oBAAA;AAEF,SAASU,mBAAmBA,CAACT,MAA+B,EAAiB;EAC3E,OAAOA,MAAM,CAACI,GAAG,EAAEH,gBAAgB,IAAI,IAAI;AAC7C;;AAEA;AACA;AACA;AACA;AACA,SAASS,mBAAmBA,CAACV,MAAkB,EAAEW,SAAoB,EAAa;EAChF,MAAMV,gBAAgB,GAAGQ,mBAAmB,CAACT,MAAM,CAAC;EAEpD,IAAI,CAACC,gBAAgB,EAAE;IACrB,OAAOU,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZC,kBAAkB,EAAEX;EACtB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,8BAA8BA,CACrCC,WAAmB,EACnB;EACEC,UAAU;EACVC,kBAAkB,GAAG;AAC+B,CAAC,GAAG,CAAC,CAAC,EAC7C;EACf,IAAIC,WAAmB;EACvB,IAAI;IACFA,WAAW,GAAG,IAAAC,0BAAiB,EAACJ,WAAW,CAAC;EAC9C,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;EACA,MAAMK,OAAO,GAAGC,gBAAK,CAACD,OAAO,CAACF,WAAW,CAAC;EAC1CE,OAAO,CAACE,SAAS,CAAC,CAAC;EAEnB,MAAMC,oBAAoB,GAAG,IAAAC,4CAAkC,EAACJ,OAAO,EAAE;IACvEJ,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI,CAACM,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAOE,gDAAgD,CAACF,oBAAoB,CAAC;AAC/E;AAEA,SAASE,gDAAgDA,CACvDF,oBAA0C,EAC3B;EACf,MAAMG,mBAAmB,GAAGH,oBAAoB,CAACI,aAAa,CAACC,yBAAyB;EACxF,IAAIF,mBAAmB,EAAE;IACvB,MAAMxB,gBAAgB,GAAG,IAAA2B,oBAAU,EAACH,mBAAmB,CAAC;IACxD,OAAO,IAAAI,qCAAwB,EAC7B5B,gBAAgB,EACf6B,OAAO,IAAKR,oBAAoB,CAACI,aAAa,CAACI,OAAO,CACzD,CAAC;EACH,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgCA,CACvCd,WAAmB,EACnBhB,gBAAwB,EACxB+B,iBAA0B,GAAG,IAAI,EAC3B;EACN,MAAMb,OAAO,GAAGC,gBAAK,CAACD,OAAO,CAACF,WAAW,CAAC;EAC1CE,OAAO,CAACE,SAAS,CAAC,CAAC;EACnBY,aAAE,CAACC,aAAa,CACdjB,WAAW,EACXV,sCAAsC,CAACY,OAAO,EAAElB,gBAAgB,EAAE+B,iBAAiB,CAAC,CAACG,SAAS,CAAC,CACjG,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5B,sCAAsCA,CAC7CY,OAAqB,EACrBlB,gBAAwB,EACxB+B,iBAA0B,GAAG,IAAI,EACjC;EACA,MAAM,GAAGI,YAAY,CAAC,GAAG,IAAAC,+BAAqB,EAAClB,OAAO,CAAC;EAEvD,IAAAmB,4CAA+B,EAACnB,OAAO,EAAEiB,YAAY,CAACG,sBAAsB,CAAC,CAACC,OAAO,CACnF,CAAC,GAAGC,IAAI,CAA4B,KAAK;IACvC,IAAIA,IAAI,CAACf,aAAa,CAACC,yBAAyB,KAAK1B,gBAAgB,EAAE;MACrE;IACF;IAEAwC,IAAI,CAACf,aAAa,CAACC,yBAAyB,GAAG,IAAI1B,gBAAgB,GAAG;IAEtE,IAAI+B,iBAAiB,EAAE;MACrB,MAAMU,WAAW,GAAGzC,gBAAgB,CAAC0C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACrD,IAAI,CAACF,WAAW,EAAEG,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC/BJ,IAAI,CAACf,aAAa,CAACoB,YAAY,GAAGJ,WAAW;MAC/C;IACF;EACF,CACF,CAAC;EACD,OAAOvB,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,6BAA6BA,CACpCjC,WAAmB,EACnBb,gBAAwB,EACxB+B,iBAA0B,GAAG,IAAI,EAC3B;EACN;EACA,IAAIgB,YAAsB,GAAG,EAAE;EAC/B,IAAI;IACFA,YAAY,GAAG,IAAAC,8BAAqB,EAACnC,WAAW,CAAC;EACnD,CAAC,CAAC,MAAM,CAAC;EAET,KAAK,MAAMG,WAAW,IAAI+B,YAAY,EAAE;IACtCjB,gCAAgC,CAACd,WAAW,EAAEhB,gBAAgB,EAAE+B,iBAAiB,CAAC;EACpF;AACF;;AAEA;AACA;AACA;;AAEA,MAAMkB,eAAe,GAAG,8BAA8B;AAEtD,SAASC,8BAA8BA,CAACrC,WAAmB,EAAQ;EACjE,MAAMsC,cAAc,GAAG,IAAAC,6BAAoB,EAACvC,WAAW,CAAC;EAExD,KAAK,MAAMwC,SAAS,IAAIF,cAAc,EAAE;IACtCG,0BAA0B,CAACD,SAAS,CAAC;EACvC;AACF;AAEA,SAASC,0BAA0BA,CAACD,SAAiB,EAAQ;EAC3D,MAAME,QAAQ,GAAGvB,aAAE,CAACwB,YAAY,CAACH,SAAS,EAAE,MAAM,CAAC;EACnD,MAAMI,WAAW,GAAGC,gBAAK,CAACC,KAAK,CAACJ,QAAQ,CAAgB;EAExD,IAAIE,WAAW,CAAC9C,kBAAkB,EAAE;IAClC,IAAI8C,WAAW,CAAC9C,kBAAkB,KAAKsC,eAAe,EAAE;;IAExD;IACA,MAAMW,MAAM,GAAG;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC;IAE7C,MAAMC,GAAG,GAAGL,gBAAK,CAACM,KAAK,CACrB;MACE,GAAGP,WAAW;MACd9C,kBAAkB,EAAEsC;IACtB,CAAC,EACDW,MACF,CAAC;IAED,IAAIG,GAAG,KAAKR,QAAQ,EAAE;MACpBvB,aAAE,CAACC,aAAa,CAACoB,SAAS,EAAEU,GAAG,CAAC;IAClC;EACF;AACF", "ignoreList": []}