const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

async function testDatabase() {
  console.log('🧪 Test de connexion à la base de données PostgreSQL...\n');

  try {
    // Test de connexion
    console.log('1. Test de connexion...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie à PostgreSQL');
    
    // Test de la base de données
    console.log('\n2. Vérification de la base de données...');
    const dbResult = await client.query('SELECT current_database()');
    console.log(`✅ Base de données connectée: ${dbResult.rows[0].current_database}`);
    
    // Test des tables
    console.log('\n3. Vérification des tables...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log(`✅ ${tablesResult.rows.length} table(s) trouvée(s):`);
    tablesResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.table_name}`);
    });
    
    // Test de la table Client
    console.log('\n4. Test de la table Client...');
    const clientsResult = await client.query('SELECT COUNT(*) as count FROM client');
    console.log(`✅ ${clientsResult.rows[0].count} client(s) dans la table Client`);
    
    // Test d'un échantillon de clients
    if (parseInt(clientsResult.rows[0].count) > 0) {
      console.log('\n5. Échantillon de clients:');
      const sampleResult = await client.query('SELECT nom, prenom FROM client LIMIT 5');
      sampleResult.rows.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.nom} ${client.prenom}`);
      });
    }
    
    client.release();
    console.log('\n🎉 Tous les tests sont réussis !');
    
  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    console.error('Détails:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solutions possibles:');
      console.log('   1. Vérifiez que PostgreSQL est démarré');
      console.log('   2. Vérifiez le port (5432 par défaut)');
      console.log('   3. Vérifiez les paramètres de connexion');
    } else if (error.code === '3D000') {
      console.log('\n💡 La base de données "Facutration" n\'existe pas');
      console.log('   Créez-la avec: CREATE DATABASE "Facutration";');
    } else if (error.code === '28P01') {
      console.log('\n💡 Erreur d\'authentification');
      console.log('   Vérifiez le nom d\'utilisateur et le mot de passe');
    }
  } finally {
    await pool.end();
  }
}

testDatabase();
