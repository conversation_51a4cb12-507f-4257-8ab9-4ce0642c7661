{"version": 3, "names": ["React", "Children", "Component", "createContext", "useEffect", "useRef", "setShouldAnimateExitingForTag", "findNodeHandle", "SkipEnteringContext", "SkipEntering", "props", "skipV<PERSON>ue<PERSON>ef", "shouldSkip", "current", "createElement", "Provider", "value", "children", "LayoutAnimationConfig", "getMaybeWrappedChildren", "count", "skipExiting", "map", "child", "setShouldAnimateExiting", "tag", "componentWillUnmount", "undefined", "render", "skipEntering"], "sourceRoot": "../../../src", "sources": ["component/LayoutAnimationConfig.tsx"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,SAAS,EACTC,MAAM,QACD,OAAO;AAEd,SAASC,6BAA6B,QAAQ,YAAS;AACvD,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,OAAO,MAAMC,mBAAmB,gBAC9BL,aAAa,CAAyC,IAAI,CAAC;;AAE7D;AACA;;AAOA,SAASM,YAAYA,CAACC,KAAmD,EAAE;EACzE,MAAMC,YAAY,GAAGN,MAAM,CAACK,KAAK,CAACE,UAAU,CAAC;EAE7CR,SAAS,CAAC,MAAM;IACdO,YAAY,CAACE,OAAO,GAAG,KAAK;EAC9B,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;EAElB,oBACEX,KAAA,CAAAc,aAAA,CAACN,mBAAmB,CAACO,QAAQ;IAACC,KAAK,EAAEL;EAAa,GAC/CD,KAAK,CAACO,QACqB,CAAC;AAEnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,SAAShB,SAAS,CAA6B;EAC/EiB,uBAAuBA,CAAA,EAAG;IACxB,OAAOlB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACV,KAAK,CAACO,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACP,KAAK,CAACW,WAAW,GACpEpB,QAAQ,CAACqB,GAAG,CAAC,IAAI,CAACZ,KAAK,CAACO,QAAQ,EAAGM,KAAK,iBACtCvB,KAAA,CAAAc,aAAA,CAACI,qBAAqB;MAACG,WAAW;IAAA,GAAEE,KAA6B,CAClE,CAAC,GACF,IAAI,CAACb,KAAK,CAACO,QAAQ;EACzB;EAEAO,uBAAuBA,CAAA,EAAG;IACxB,IAAIvB,QAAQ,CAACmB,KAAK,CAAC,IAAI,CAACV,KAAK,CAACO,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7C,MAAMQ,GAAG,GAAGlB,cAAc,CAAC,IAAI,CAAC;MAChC,IAAIkB,GAAG,EAAE;QACPnB,6BAA6B,CAACmB,GAAG,EAAE,CAAC,IAAI,CAACf,KAAK,CAACW,WAAW,CAAC;MAC7D;IACF;EACF;EAEAK,oBAAoBA,CAAA,EAAS;IAC3B,IAAI,IAAI,CAAChB,KAAK,CAACW,WAAW,KAAKM,SAAS,EAAE;MACxC,IAAI,CAACH,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEAI,MAAMA,CAAA,EAAc;IAClB,MAAMX,QAAQ,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAE/C,IAAI,IAAI,CAACT,KAAK,CAACmB,YAAY,KAAKF,SAAS,EAAE;MACzC,OAAOV,QAAQ;IACjB;IAEA,oBACEjB,KAAA,CAAAc,aAAA,CAACL,YAAY;MAACG,UAAU,EAAE,IAAI,CAACF,KAAK,CAACmB;IAAa,GAC/CZ,QACW,CAAC;EAEnB;AACF", "ignoreList": []}