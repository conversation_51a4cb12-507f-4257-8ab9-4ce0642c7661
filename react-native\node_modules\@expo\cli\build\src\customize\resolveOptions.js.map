{"version": 3, "sources": ["../../../src/customize/resolveOptions.ts"], "sourcesContent": ["import { assertUnexpectedObjectKeys, parseVariadicArguments } from '../utils/variadic';\n\nexport type Options = object;\n\nexport async function resolveArgsAsync(\n  argv: string[]\n): Promise<{ variadic: string[]; options: Options; extras: string[] }> {\n  const { variadic, extras, flags } = parseVariadicArguments(argv);\n\n  assertUnexpectedObjectKeys([], flags);\n\n  return {\n    // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']\n    variadic,\n    options: {},\n    extras,\n  };\n}\n"], "names": ["resolveArgsAsync", "argv", "variadic", "extras", "flags", "parseVariadicArguments", "assertUnexpectedObjectKeys", "options"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;0BAJ6C;AAI5D,eAAeA,iBACpBC,IAAc;IAEd,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGC,IAAAA,gCAAsB,EAACJ;IAE3DK,IAAAA,oCAA0B,EAAC,EAAE,EAAEF;IAE/B,OAAO;QACL,uFAAuF;QACvFF;QACAK,SAAS,CAAC;QACVJ;IACF;AACF"}