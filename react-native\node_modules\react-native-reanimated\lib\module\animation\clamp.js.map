{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "recognizePrefixSuffix", "logger", "withClamp", "config", "_animationToClamp", "animationToClamp", "strippedMin", "min", "undefined", "strippedValue", "strippedMax", "max", "clampOnFrame", "animation", "now", "finished", "onFrame", "current", "warn", "prefix", "suffix", "newValue", "onStart", "value", "previousAnimation", "animationBeforeClamped", "callback", "isHigherOrder", "reduceMotion"], "sourceRoot": "../../../src", "sources": ["animation/clamp.ts"], "mappings": "AAAA,YAAY;;AACZ,SACEA,eAAe,EACfC,2BAA2B,EAC3BC,qBAAqB,QAChB,WAAQ;AASf,SAASC,MAAM,QAAQ,oBAAW;AAUlC,OAAO,MAAMC,SAAS,GAAG,SAAAA,CACvBC,MAAyD,EACzDC,iBAAkE,EACvC;EAC3B,SAAS;;EACT,OAAON,eAAe,CACpBM,iBAAiB,EACjB,MAAsB;IACpB,SAAS;;IACT,MAAMC,gBAAgB,GACpB,OAAOD,iBAAiB,KAAK,UAAU,GACnCA,iBAAiB,CAAC,CAAC,GACnBA,iBAAiB;IAEvB,MAAME,WAAW,GACfH,MAAM,CAACI,GAAG,KAAKC,SAAS,GACpBA,SAAS,GACTR,qBAAqB,CAACG,MAAM,CAACI,GAAG,CAAC,CAACE,aAAa;IAErD,MAAMC,WAAW,GACfP,MAAM,CAACQ,GAAG,KAAKH,SAAS,GACpBA,SAAS,GACTR,qBAAqB,CAACG,MAAM,CAACQ,GAAG,CAAC,CAACF,aAAa;IAErD,SAASG,YAAYA,CACnBC,SAAyB,EACzBC,GAAc,EACL;MACT,MAAMC,QAAQ,GAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,EAAES,GAAG,CAAC;MAEhE,IAAIT,gBAAgB,CAACY,OAAO,KAAKT,SAAS,EAAE;QAC1CP,MAAM,CAACiB,IAAI,CACT,mFACF,CAAC;QACD,OAAO,IAAI;MACb,CAAC,MAAM;QACL,MAAM;UAAEC,MAAM;UAAEV,aAAa;UAAEW;QAAO,CAAC,GAAGpB,qBAAqB,CAC7DK,gBAAgB,CAACY,OACnB,CAAC;QAED,IAAII,QAAQ;QAEZ,IAAIX,WAAW,KAAKF,SAAS,IAAIE,WAAW,GAAGD,aAAa,EAAE;UAC5DY,QAAQ,GAAGX,WAAW;QACxB,CAAC,MAAM,IAAIJ,WAAW,KAAKE,SAAS,IAAIF,WAAW,GAAGG,aAAa,EAAE;UACnEY,QAAQ,GAAGf,WAAW;QACxB,CAAC,MAAM;UACLe,QAAQ,GAAGZ,aAAa;QAC1B;QAEAI,SAAS,CAACI,OAAO,GACf,OAAOZ,gBAAgB,CAACY,OAAO,KAAK,QAAQ,GACxCI,QAAQ,GACR,GAAGF,MAAM,KAAKX,SAAS,GAAG,EAAE,GAAGW,MAAM,GAAGE,QAAQ,GAC9CD,MAAM,KAAKZ,SAAS,GAAG,EAAE,GAAGY,MAAM,EAClC;MACV;MAEA,OAAOL,QAAQ;IACjB;IAEA,SAASO,OAAOA,CACdT,SAAyB,EACzBU,KAAsB,EACtBT,GAAc,EACdU,iBAAwC,EAClC;MACNX,SAAS,CAACI,OAAO,GAAGM,KAAK;MACzBV,SAAS,CAACW,iBAAiB,GAAGnB,gBAAgB;MAC9C,MAAMoB,sBAAsB,GAAGD,iBAAiB,EAAEA,iBAAiB;MACnE,IACErB,MAAM,CAACQ,GAAG,KAAKH,SAAS,IACxBL,MAAM,CAACI,GAAG,KAAKC,SAAS,IACxBL,MAAM,CAACQ,GAAG,GAAGR,MAAM,CAACI,GAAG,EACvB;QACAN,MAAM,CAACiB,IAAI,CACT,sEACF,CAAC;MACH;MAEAb,gBAAgB,CAACiB,OAAO,CACtBjB,gBAAgB;MAChB;AACV;AACA;AACA;MACUoB,sBAAsB,EAAER,OAAO,IAAIM,KAAK,EACxCT,GAAG,EACHW,sBACF,CAAC;IACH;IAEA,MAAMC,QAAQ,GAAIX,QAAkB,IAAW;MAC7C,IAAIV,gBAAgB,CAACqB,QAAQ,EAAE;QAC7BrB,gBAAgB,CAACqB,QAAQ,CAACX,QAAQ,CAAC;MACrC;IACF,CAAC;IAED,OAAO;MACLY,aAAa,EAAE,IAAI;MACnBX,OAAO,EAAEJ,YAAY;MACrBU,OAAO;MACPL,OAAO,EAAEZ,gBAAgB,CAACY,OAAQ;MAClCS,QAAQ;MACRF,iBAAiB,EAAE,IAAI;MACvBI,YAAY,EAAE7B,2BAA2B,CAACI,MAAM,CAACyB,YAAY;IAC/D,CAAC;EACH,CACF,CAAC;AACH,CAAkB", "ignoreList": []}