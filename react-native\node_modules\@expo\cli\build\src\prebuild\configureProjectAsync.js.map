{"version": 3, "sources": ["../../../src/prebuild/configureProjectAsync.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { compileModsAsync, ModPlatform } from '@expo/config-plugins';\nimport { getPrebuildConfigAsync } from '@expo/prebuild-config';\n\nimport { logConfig } from '../config/configAsync';\nimport * as Log from '../log';\nimport { env } from '../utils/env';\nimport {\n  getOrPromptForBundleIdentifierAsync,\n  getOrPromptForPackageAsync,\n} from '../utils/getOrPromptApplicationId';\n\nexport async function configureProjectAsync(\n  projectRoot: string,\n  {\n    platforms,\n    exp,\n    templateChecksum,\n  }: {\n    platforms: ModPlatform[];\n    exp?: ExpoConfig;\n    templateChecksum?: string;\n  }\n): Promise<ExpoConfig> {\n  let bundleIdentifier: string | undefined;\n  if (platforms.includes('ios')) {\n    // Check bundle ID before reading the config because it may mutate the config if the user is prompted to define it.\n    bundleIdentifier = await getOrPromptForBundleIdentifierAsync(projectRoot, exp);\n  }\n  let packageName: string | undefined;\n  if (platforms.includes('android')) {\n    // Check package before reading the config because it may mutate the config if the user is prompted to define it.\n    packageName = await getOrPromptForPackageAsync(projectRoot, exp);\n  }\n\n  let { exp: config } = await getPrebuildConfigAsync(projectRoot, {\n    platforms,\n    packageName,\n    bundleIdentifier,\n  });\n\n  if (templateChecksum) {\n    // Prepare template checksum for the patch mods\n    config._internal = config._internal ?? {};\n    config._internal.templateChecksum = templateChecksum;\n  }\n\n  // compile all plugins and mods\n  config = await compileModsAsync(config, {\n    projectRoot,\n    platforms,\n    assertMissingModProviders: false,\n  });\n\n  if (env.EXPO_DEBUG) {\n    Log.log();\n    Log.log('Evaluated config:');\n    logConfig(config);\n    Log.log();\n  }\n\n  return config;\n}\n"], "names": ["configureProjectAsync", "projectRoot", "platforms", "exp", "templateChecksum", "bundleIdentifier", "includes", "getOrPromptForBundleIdentifierAsync", "packageName", "getOrPromptForPackageAsync", "config", "getPrebuildConfigAsync", "_internal", "compileModsAsync", "assertMissingModProviders", "env", "EXPO_DEBUG", "Log", "log", "logConfig"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;;yBAXwB;;;;;;;yBACP;;;;;;6BAEb;6DACL;qBACD;0CAIb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,eAAeA,sBACpBC,WAAmB,EACnB,EACEC,SAAS,EACTC,GAAG,EACHC,gBAAgB,EAKjB;IAED,IAAIC;IACJ,IAAIH,UAAUI,QAAQ,CAAC,QAAQ;QAC7B,mHAAmH;QACnHD,mBAAmB,MAAME,IAAAA,6DAAmC,EAACN,aAAaE;IAC5E;IACA,IAAIK;IACJ,IAAIN,UAAUI,QAAQ,CAAC,YAAY;QACjC,iHAAiH;QACjHE,cAAc,MAAMC,IAAAA,oDAA0B,EAACR,aAAaE;IAC9D;IAEA,IAAI,EAAEA,KAAKO,MAAM,EAAE,GAAG,MAAMC,IAAAA,wCAAsB,EAACV,aAAa;QAC9DC;QACAM;QACAH;IACF;IAEA,IAAID,kBAAkB;QACpB,+CAA+C;QAC/CM,OAAOE,SAAS,GAAGF,OAAOE,SAAS,IAAI,CAAC;QACxCF,OAAOE,SAAS,CAACR,gBAAgB,GAAGA;IACtC;IAEA,+BAA+B;IAC/BM,SAAS,MAAMG,IAAAA,iCAAgB,EAACH,QAAQ;QACtCT;QACAC;QACAY,2BAA2B;IAC7B;IAEA,IAAIC,QAAG,CAACC,UAAU,EAAE;QAClBC,KAAIC,GAAG;QACPD,KAAIC,GAAG,CAAC;QACRC,IAAAA,sBAAS,EAACT;QACVO,KAAIC,GAAG;IACT;IAEA,OAAOR;AACT"}