{"version": 3, "names": ["dispatchCommand", "measure", "scrollTo", "setGestureState", "setNativeProps", "getRelativeCoords"], "sourceRoot": "../../../src", "sources": ["platformFunctions/index.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,wBAAqB", "ignoreList": []}