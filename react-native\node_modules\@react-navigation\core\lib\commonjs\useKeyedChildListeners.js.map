{"version": 3, "names": ["useKeyedChildListeners", "current", "keyedListeners", "React", "useRef", "Object", "assign", "create", "getState", "beforeRemove", "addKeyedListener", "useCallback", "type", "key", "listener", "undefined"], "sourceRoot": "../../src", "sources": ["useKeyedChildListeners.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAI/B;AACA;AACA;AACe,SAASA,sBAAsB,GAAG;EAC/C,MAAM;IAAEC,OAAO,EAAEC;EAAe,CAAC,GAAGC,KAAK,CAACC,MAAM,CAM9CC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC,EAAE;IACjCC,QAAQ,EAAE,CAAC,CAAC;IACZC,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,CACH;EAED,MAAMC,gBAAgB,GAAGP,KAAK,CAACQ,WAAW,CACxC,CACEC,IAAO,EACPC,GAAW,EACXC,QAA6B,KAC1B;IACH;IACAZ,cAAc,CAACU,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGC,QAAQ;IAEpC,OAAO,MAAM;MACX;MACAZ,cAAc,CAACU,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGE,SAAS;IACvC,CAAC;EACH,CAAC,EACD,CAACb,cAAc,CAAC,CACjB;EAED,OAAO;IACLA,cAAc;IACdQ;EACF,CAAC;AACH"}