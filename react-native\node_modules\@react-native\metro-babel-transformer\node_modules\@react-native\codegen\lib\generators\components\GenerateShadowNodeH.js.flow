/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

'use strict';

import type {SchemaType} from '../../CodegenSchema';

const {IncludeTemplate} = require('./CppHelpers');

// File path -> contents
type FilesOutput = Map<string, string>;

const FileTemplate = ({
  componentClasses,
  headerPrefix,
}: {
  componentClasses: string,
  headerPrefix: string,
}) => `
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * ${'@'}generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

${IncludeTemplate({headerPrefix, file: 'EventEmitters.h'})}
${IncludeTemplate({headerPrefix, file: 'Props.h'})}
${IncludeTemplate({headerPrefix, file: 'States.h'})}
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

${componentClasses}

} // namespace facebook::react
`;

const ComponentTemplate = ({
  className,
  eventEmitter,
}: {
  className: string,
  eventEmitter: string,
}) =>
  `
JSI_EXPORT extern const char ${className}ComponentName[];

/*
 * \`ShadowNode\` for <${className}> component.
 */
using ${className}ShadowNode = ConcreteViewShadowNode<
    ${className}ComponentName,
    ${className}Props${eventEmitter},
    ${className}State>;
`.trim();

module.exports = {
  generate(
    libraryName: string,
    schema: SchemaType,
    packageName?: string,
    assumeNonnull: boolean = false,
    headerPrefix?: string,
  ): FilesOutput {
    const fileName = 'ShadowNodes.h';

    const moduleResults = Object.keys(schema.modules)
      .map(moduleName => {
        const module = schema.modules[moduleName];
        if (module.type !== 'Component') {
          return;
        }

        const {components} = module;
        // No components in this module
        if (components == null) {
          return null;
        }

        return Object.keys(components)
          .map(componentName => {
            const component = components[componentName];
            if (component.interfaceOnly === true) {
              return;
            }

            const eventEmitter = `,\n    ${componentName}EventEmitter`;

            const replacedTemplate = ComponentTemplate({
              className: componentName,
              eventEmitter,
            });

            return replacedTemplate;
          })
          .join('\n\n');
      })
      .filter(Boolean)
      .join('\n\n');

    const replacedTemplate = FileTemplate({
      componentClasses: moduleResults,
      headerPrefix: headerPrefix ?? '',
    });

    return new Map([[fileName, replacedTemplate]]);
  },
};
