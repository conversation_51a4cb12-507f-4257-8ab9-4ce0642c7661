{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_utils", "_reactNative", "_SearchBarNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "_extends", "assign", "bind", "target", "arguments", "length", "source", "key", "apply", "NativeSearchBar", "SearchBarNativeComponent", "NativeSearchBarCommands", "SearchBarNativeCommands", "SearchBar", "props", "ref", "searchBarRef", "React", "useRef", "useImperativeHandle", "blur", "_callMethodWithRef", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "useCallback", "method", "current", "console", "warn", "isSearchBarAvailableForCurrentPlatform", "View", "createElement", "onSearchFocus", "onFocus", "onSearchBlur", "onBlur", "onSearchButtonPress", "onCancelButtonPress", "onChangeText", "_default", "forwardRef"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAGA,IAAAG,yBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAM4C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAT,OAAA,EAAAS,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAE,GAAA,CAAAL,CAAA,UAAAG,CAAA,CAAAG,GAAA,CAAAN,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAtB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAuB,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,IAAAxB,MAAA,CAAAyB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAW,CAAA,SAAAI,CAAA,GAAAN,CAAA,GAAAtB,MAAA,CAAAuB,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAT,GAAA,IAAAS,CAAA,CAAAC,GAAA,IAAA7B,MAAA,CAAAC,cAAA,CAAAmB,CAAA,EAAAI,CAAA,EAAAI,CAAA,IAAAR,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAhB,OAAA,GAAAS,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAO,CAAA,GAAAA,CAAA;AAAA,SAAAd,uBAAAwB,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAb,UAAA,GAAAa,GAAA,KAAA1B,OAAA,EAAA0B,GAAA;AAAA,SAAAC,SAAA,IAAAA,QAAA,GAAA/B,MAAA,CAAAgC,MAAA,GAAAhC,MAAA,CAAAgC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAO,SAAA,CAAAC,MAAA,EAAAR,CAAA,UAAAS,MAAA,GAAAF,SAAA,CAAAP,CAAA,YAAAU,GAAA,IAAAD,MAAA,QAAArC,MAAA,CAAAyB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAU,MAAA,EAAAC,GAAA,KAAAJ,MAAA,CAAAI,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAJ,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAJ,SAAA,KAP5C;AAUA,MAAMK,eAG0B,GAC9BC,iCACuB;AACzB,MAAMC,uBAA8C,GAClDC,kCAAgD;AAalD,SAASC,SAASA,CAACC,KAAqB,EAAEC,GAAiC,EAAE;EAC3E,MAAMC,YAAY,GAAGC,cAAK,CAACC,MAAM,CAA2B,IAAI,CAAC;EAEjED,cAAK,CAACE,mBAAmB,CAACJ,GAAG,EAAE,OAAO;IACpCK,IAAI,EAAEA,CAAA,KAAM;MACVC,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACS,IAAI,CAACL,GAAG,CAAC,CAAC;IAC9D,CAAC;IACDO,KAAK,EAAEA,CAAA,KAAM;MACXD,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACW,KAAK,CAACP,GAAG,CAAC,CAAC;IAC/D,CAAC;IACDQ,kBAAkB,EAAGC,IAAa,IAAK;MACrCH,kBAAkB,CAACN,GAAG,IACpBJ,uBAAuB,CAACY,kBAAkB,CAACR,GAAG,EAAES,IAAI,CACtD,CAAC;IACH,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfJ,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACc,SAAS,CAACV,GAAG,CAAC,CAAC;IACnE,CAAC;IACDW,OAAO,EAAGC,IAAY,IAAK;MACzBN,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACe,OAAO,CAACX,GAAG,EAAEY,IAAI,CAAC,CAAC;IACvE,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAClBP,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACiB,YAAY,CAACb,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMM,kBAAkB,GAAGJ,cAAK,CAACY,WAAW,CACzCC,MAAwC,IAAK;IAC5C,MAAMf,GAAG,GAAGC,YAAY,CAACe,OAAO;IAChC,IAAIhB,GAAG,EAAE;MACPe,MAAM,CAACf,GAAG,CAAC;IACb,CAAC,MAAM;MACLiB,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF,CAAC,EACD,CAACjB,YAAY,CACf,CAAC;EAED,IAAI,CAACkB,6CAAsC,EAAE;IAC3CF,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD,OAAOE,iBAAI;EACb;EAEA,oBACE7D,MAAA,CAAAD,OAAA,CAAA+D,aAAA,CAAC3B,eAAe,EAAAT,QAAA;IACde,GAAG,EAAEC;EAAa,GACdF,KAAK;IACTuB,aAAa,EAAEvB,KAAK,CAACwB,OAA8C;IACnEC,YAAY,EAAEzB,KAAK,CAAC0B,MAA6C;IACjEC,mBAAmB,EACjB3B,KAAK,CAAC2B,mBACP;IACDC,mBAAmB,EACjB5B,KAAK,CAAC4B,mBACP;IACDC,YAAY,EAAE7B,KAAK,CAAC6B;EAAoD,EACzE,CAAC;AAEN;AAAC,IAAAC,QAAA,GAAAzE,OAAA,CAAAE,OAAA,gBAEc4C,cAAK,CAAC4B,UAAU,CAAoChC,SAAS,CAAC"}