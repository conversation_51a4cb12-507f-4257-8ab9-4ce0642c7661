// Script de test pour vérifier l'API clients
const fetch = require('node-fetch');

async function testClientsAPI() {
  console.log('🧪 Test de l\'API clients...\n');

  try {
    const response = await fetch('http://localhost:5000/api/clients');
    
    console.log('📡 Status:', response.status);
    console.log('📡 Status Text:', response.statusText);
    
    const data = await response.json();
    
    console.log('\n📊 Réponse de l\'API:');
    console.log('✅ Success:', data.success);
    console.log('📊 Count:', data.count);
    console.log('📋 Data type:', typeof data.data);
    console.log('📋 Data is array:', Array.isArray(data.data));
    
    if (data.data && data.data.length > 0) {
      console.log('\n👤 Premier client:');
      console.log(JSON.stringify(data.data[0], null, 2));
      
      console.log('\n📋 Liste des clients:');
      data.data.forEach((client, index) => {
        console.log(`${index + 1}. ${client.nom} ${client.prenom} (ID: ${client.idClient})`);
      });
    } else {
      console.log('\n❌ Aucune donnée client trouvée');
      console.log('📋 Structure complète de la réponse:');
      console.log(JSON.stringify(data, null, 2));
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

testClientsAPI();
