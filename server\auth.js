const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route de connexion
router.post('/login', async (req, res) => {
  console.log('📥 Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche de l'utilisateur dans la base de données
    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur 
      WHERE email = $1 AND motdepass = $2
    `;

    console.log('🔍 Recherche utilisateur avec email:', email);
    const result = await pool.query(query, [email, motDepass]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé ou mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('✅ Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Déterminer la redirection selon le rôle
    let redirectTo = '/dashboard';
    if (user.role === 'Tech') {
      redirectTo = '/technician-dashboard';
    } else if (user.role === 'Admin') {
      redirectTo = '/admin-dashboard';
    }

    // Réponse de succès
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      redirectTo: redirectTo
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// Route pour obtenir les informations d'un utilisateur
router.get('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/user/${id}`);

    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur 
      WHERE idtech = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log(`✅ Utilisateur ${id} récupéré`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Utilisateur trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour mettre à jour le profil utilisateur
router.put('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête PUT /api/user/${id}:`, req.body);
    const { nom, prenom, adresse, tel, email } = req.body;

    const query = `
      UPDATE utilisateur 
      SET nom = $1, prenom = $2, adresse = $3, tel = $4, email = $5
      WHERE idtech = $6
      RETURNING idtech, nom, prenom, adresse, tel, email, role
    `;

    const values = [nom, prenom, adresse, tel, email, id];
    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log('✅ Profil utilisateur mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Profil mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du profil',
      error: error.message
    });
  }
});

// Route pour changer le mot de passe
router.put('/api/user/:id/password', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête PUT /api/user/${id}/password`);
    const { currentPassword, newPassword } = req.body;

    // Validation des champs requis
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Mot de passe actuel et nouveau mot de passe requis'
      });
    }

    // Vérifier le mot de passe actuel
    const checkQuery = `
      SELECT idtech FROM utilisateur 
      WHERE idtech = $1 AND motdepass = $2
    `;

    const checkResult = await pool.query(checkQuery, [id, currentPassword]);

    if (checkResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Mot de passe actuel incorrect'
      });
    }

    // Mettre à jour le mot de passe
    const updateQuery = `
      UPDATE utilisateur 
      SET motdepass = $1
      WHERE idtech = $2
      RETURNING idtech, nom, prenom, email, role
    `;

    const updateResult = await pool.query(updateQuery, [newPassword, id]);

    console.log('✅ Mot de passe mis à jour pour l\'utilisateur:', updateResult.rows[0]);
    res.json({
      success: true,
      data: updateResult.rows[0],
      message: 'Mot de passe mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du mot de passe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du mot de passe',
      error: error.message
    });
  }
});

// Route pour récupérer tous les utilisateurs (pour les admins)
router.get('/api/users', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/users');

    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur 
      ORDER BY nom, prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} utilisateurs récupérés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} utilisateur(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
});

// Route pour créer un nouvel utilisateur (pour les admins)
router.post('/api/users', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/users:', req.body);
    const { nom, prenom, adresse, tel, email, motdepass, role } = req.body;

    // Validation des champs requis
    if (!nom || !prenom || !email || !motdepass || !role) {
      return res.status(400).json({
        success: false,
        message: 'Les champs nom, prénom, email, mot de passe et rôle sont requis'
      });
    }

    // Validation du rôle
    if (!['Admin', 'Tech'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Le rôle doit être "Admin" ou "Tech"'
      });
    }

    // Vérifier si l'email existe déjà
    const checkQuery = 'SELECT idtech FROM utilisateur WHERE email = $1';
    const checkResult = await pool.query(checkQuery, [email]);

    if (checkResult.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Un utilisateur avec cet email existe déjà'
      });
    }

    const query = `
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING idtech, nom, prenom, adresse, tel, email, role
    `;

    const values = [nom, prenom, adresse, tel, email, motdepass, role];
    const result = await pool.query(query, values);

    console.log('✅ Nouvel utilisateur créé:', result.rows[0]);
    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Utilisateur créé avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'utilisateur',
      error: error.message
    });
  }
});

module.exports = router;
