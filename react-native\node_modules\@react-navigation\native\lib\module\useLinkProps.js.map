{"version": 3, "names": ["getPathFromState", "NavigationContainerRefContext", "NavigationHelpersContext", "React", "Platform", "LinkingContext", "useLinkTo", "getStateFromParams", "params", "state", "screen", "routes", "name", "undefined", "useLinkProps", "to", "action", "root", "useContext", "navigation", "options", "linkTo", "onPress", "e", "<PERSON><PERSON><PERSON><PERSON>", "OS", "defaultPrevented", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "button", "includes", "currentTarget", "target", "preventDefault", "dispatch", "Error", "getPathFromStateHelper", "href", "config", "accessibilityRole"], "sourceRoot": "../../src", "sources": ["useLinkProps.tsx"], "mappings": "AAAA,SACEA,gBAAgB,EAEhBC,6BAA6B,EAC7BC,wBAAwB,QAGnB,wBAAwB;AAE/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAAgCC,QAAQ,QAAQ,cAAc;AAE9D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,SAAS,MAAc,aAAa;AAO3C,MAAMC,kBAAkB,GACtBC,MAAyE,IACT;EAChE,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEC,KAAK,EAAE;IACjB,OAAOD,MAAM,CAACC,KAAK;EACrB;EAEA,IAAID,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEE,MAAM,EAAE;IAClB,OAAO;MACLC,MAAM,EAAE,CACN;QACEC,IAAI,EAAEJ,MAAM,CAACE,MAAM;QACnBF,MAAM,EAAEA,MAAM,CAACA,MAAM;QACrB;QACAC,KAAK,EAAED,MAAM,CAACE,MAAM,GAChBH,kBAAkB,CAChBC,MAAM,CAACA,MAAM,CAGd,GACDK;MACN,CAAC;IAEL,CAAC;EACH;EAEA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAY,OAEA;EAAA,IAAlC;IAAEC,EAAE;IAAEC;EAAyB,CAAC;EAChC,MAAMC,IAAI,GAAGd,KAAK,CAACe,UAAU,CAACjB,6BAA6B,CAAC;EAC5D,MAAMkB,UAAU,GAAGhB,KAAK,CAACe,UAAU,CAAChB,wBAAwB,CAAC;EAC7D,MAAM;IAAEkB;EAAQ,CAAC,GAAGjB,KAAK,CAACe,UAAU,CAACb,cAAc,CAAC;EACpD,MAAMgB,MAAM,GAAGf,SAAS,EAAa;EAErC,MAAMgB,OAAO,GACXC,CAA2E,IACxE;IAAA;IACH,IAAIC,YAAY,GAAG,KAAK;IAExB,IAAIpB,QAAQ,CAACqB,EAAE,KAAK,KAAK,IAAI,CAACF,CAAC,EAAE;MAC/BC,YAAY,GAAGD,CAAC,GAAG,CAACA,CAAC,CAACG,gBAAgB,GAAG,IAAI;IAC/C,CAAC,MAAM,IACL,CAACH,CAAC,CAACG,gBAAgB;IAAI;IACvB;IACA,EAAEH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,MAAM,IAAIL,CAAC,CAACM,OAAO,IAAIN,CAAC,CAACO,QAAQ,CAAC;IAAI;IACvD;IACCP,CAAC,CAACQ,MAAM,IAAI,IAAI,IAAIR,CAAC,CAACQ,MAAM,KAAK,CAAC,CAAC;IAAI;IACxC;IACA,CAAClB,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAACmB,QAAQ,qBAACT,CAAC,CAACU,aAAa,qDAAf,iBAAiBC,MAAM,CAAC,CAAC;IAAA,EAChE;MACAX,CAAC,CAACY,cAAc,EAAE;MAClBX,YAAY,GAAG,IAAI;IACrB;IAEA,IAAIA,YAAY,EAAE;MAChB,IAAIR,MAAM,EAAE;QACV,IAAIG,UAAU,EAAE;UACdA,UAAU,CAACiB,QAAQ,CAACpB,MAAM,CAAC;QAC7B,CAAC,MAAM,IAAIC,IAAI,EAAE;UACfA,IAAI,CAACmB,QAAQ,CAACpB,MAAM,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAIqB,KAAK,CACb,kFAAkF,CACnF;QACH;MACF,CAAC,MAAM;QACLhB,MAAM,CAACN,EAAE,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAMuB,sBAAsB,GAAG,CAAAlB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpB,gBAAgB,KAAIA,gBAAgB;EAE5E,MAAMuC,IAAI,GACR,OAAOxB,EAAE,KAAK,QAAQ,GAClBA,EAAE,GACFuB,sBAAsB,CACpB;IACE3B,MAAM,EAAE,CACN;MACEC,IAAI,EAAEG,EAAE,CAACL,MAAM;MACf;MACAF,MAAM,EAAEO,EAAE,CAACP,MAAM;MACjB;MACAC,KAAK,EAAEF,kBAAkB,CAACQ,EAAE,CAACP,MAAM;IACrC,CAAC;EAEL,CAAC,EACDY,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB,MAAM,CAChB;EAEP,OAAO;IACLD,IAAI;IACJE,iBAAiB,EAAE,MAAe;IAClCnB;EACF,CAAC;AACH"}