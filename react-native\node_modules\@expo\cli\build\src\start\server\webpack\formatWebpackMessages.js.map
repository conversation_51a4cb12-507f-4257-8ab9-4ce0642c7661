{"version": 3, "sources": ["../../../../../src/start/server/webpack/formatWebpackMessages.ts"], "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/b172b5e/packages/react-dev-utils/ModuleNotFoundPlugin.js\n * But with Node LTS support.\n */\n\nimport type { Stats } from 'webpack';\n\nconst friendlySyntaxErrorLabel = 'Syntax error:';\n\nfunction isLikelyASyntaxError(message: string): boolean {\n  return message.indexOf(friendlySyntaxErrorLabel) !== -1;\n}\n\n// Cleans up webpack error messages.\nfunction formatMessage(message: string | { message: string } | { message: string }[]) {\n  let lines: string[] = [];\n\n  if (typeof message === 'string') {\n    lines = message.split('\\n');\n  } else if ('message' in message) {\n    lines = message['message'].split('\\n');\n  } else if (Array.isArray(message)) {\n    message.forEach((message) => {\n      if ('message' in message) {\n        lines = message['message'].split('\\n');\n      }\n    });\n  }\n\n  // Strip webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter((line) => !/Module [A-z ]+\\(from/.test(line));\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map((line) => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n    if (!parsingError) {\n      return line;\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError;\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`;\n  });\n\n  message = lines.join('\\n');\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  );\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  );\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  );\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  );\n  lines = message.split('\\n');\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1);\n  }\n  // Clean up file name\n  lines[0] = lines[0].replace(/^(.*) \\d+:\\d+-\\d+$/, '$1');\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].indexOf('Module not found: ') === 0) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n    ];\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n    lines[1] = 'To import Sass files, you first need to install sass.\\n';\n    lines[1] += 'Run `npm install sass` or `yarn add sass` inside your workspace.';\n  }\n\n  message = lines.join('\\n');\n  // Internal stacks are generally useless so we strip them... with the\n  // exception of stacks containing `webpack:` because they're normally\n  // from user code generated by webpack. For more information see\n  // https://github.com/facebook/create-react-app/pull/1050\n  message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, ''); // at ... ...:x:y\n  message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, ''); // at <anonymous>\n  lines = message.split('\\n');\n\n  // Remove duplicated newlines\n  lines = lines.filter(\n    (line, index, arr) => index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  );\n\n  // Reassemble the message\n  message = lines.join('\\n');\n  return message.trim();\n}\n\nexport function formatWebpackMessages(json?: Stats.ToJsonOutput) {\n  const formattedErrors = json?.errors?.map(formatMessage);\n  const formattedWarnings = json?.warnings?.map(formatMessage);\n  const result = { errors: formattedErrors, warnings: formattedWarnings };\n  if (result.errors?.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError);\n  }\n  return result;\n}\n"], "names": ["formatWebpackMessages", "friendlySyntaxErrorLabel", "isLikelyASyntaxError", "message", "indexOf", "formatMessage", "lines", "split", "Array", "isArray", "for<PERSON>ach", "filter", "line", "test", "map", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "join", "replace", "length", "trim", "splice", "match", "index", "arr", "json", "result", "formattedErrors", "errors", "formattedWarnings", "warnings", "some"], "mappings": "AAAA;;;;;;;;;CASC;;;;+BAwGeA;;;eAAAA;;;AApGhB,MAAMC,2BAA2B;AAEjC,SAASC,qBAAqBC,OAAe;IAC3C,OAAOA,QAAQC,OAAO,CAACH,8BAA8B,CAAC;AACxD;AAEA,oCAAoC;AACpC,SAASI,cAAcF,OAA6D;IAClF,IAAIG,QAAkB,EAAE;IAExB,IAAI,OAAOH,YAAY,UAAU;QAC/BG,QAAQH,QAAQI,KAAK,CAAC;IACxB,OAAO,IAAI,aAAaJ,SAAS;QAC/BG,QAAQH,OAAO,CAAC,UAAU,CAACI,KAAK,CAAC;IACnC,OAAO,IAAIC,MAAMC,OAAO,CAACN,UAAU;QACjCA,QAAQO,OAAO,CAAC,CAACP;YACf,IAAI,aAAaA,SAAS;gBACxBG,QAAQH,OAAO,CAAC,UAAU,CAACI,KAAK,CAAC;YACnC;QACF;IACF;IAEA,kDAAkD;IAClD,oEAAoE;IACpED,QAAQA,MAAMK,MAAM,CAAC,CAACC,OAAS,CAAC,uBAAuBC,IAAI,CAACD;IAE5D,4CAA4C;IAC5C,2CAA2C;IAC3CN,QAAQA,MAAMQ,GAAG,CAAC,CAACF;QACjB,MAAMG,eAAe,gDAAgDC,IAAI,CAACJ;QAC1E,IAAI,CAACG,cAAc;YACjB,OAAOH;QACT;QACA,MAAM,GAAGK,WAAWC,aAAaC,aAAa,GAAGJ;QACjD,OAAO,GAAGd,yBAAyB,CAAC,EAAEkB,aAAa,EAAE,EAAEF,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAC;IACpF;IAEAf,UAAUG,MAAMc,IAAI,CAAC;IACrB,+CAA+C;IAC/CjB,UAAUA,QAAQkB,OAAO,CACvB,4CACA,GAAGpB,yBAAyB,aAAa,CAAC;IAE5C,yBAAyB;IACzBE,UAAUA,QAAQkB,OAAO,CACvB,mDACA,CAAC,uDAAuD,CAAC;IAE3DlB,UAAUA,QAAQkB,OAAO,CACvB,6EACA,CAAC,kFAAkF,CAAC;IAEtFlB,UAAUA,QAAQkB,OAAO,CACvB,2EACA,CAAC,0EAA0E,CAAC;IAE9Ef,QAAQH,QAAQI,KAAK,CAAC;IAEtB,yBAAyB;IACzB,IAAID,MAAMgB,MAAM,GAAG,KAAKhB,KAAK,CAAC,EAAE,CAACiB,IAAI,OAAO,IAAI;QAC9CjB,MAAMkB,MAAM,CAAC,GAAG;IAClB;IACA,qBAAqB;IACrBlB,KAAK,CAAC,EAAE,GAAGA,KAAK,CAAC,EAAE,CAACe,OAAO,CAAC,sBAAsB;IAElD,wEAAwE;IACxE,IAAIf,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACF,OAAO,CAAC,0BAA0B,GAAG;QAC5DE,QAAQ;YACNA,KAAK,CAAC,EAAE;YACRA,KAAK,CAAC,EAAE,CACLe,OAAO,CAAC,WAAW,IACnBA,OAAO,CAAC,uCAAuC;SACnD;IACH;IAEA,sEAAsE;IACtE,IAAIf,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACmB,KAAK,CAAC,6BAA6B;QAC1DnB,KAAK,CAAC,EAAE,GAAG;QACXA,KAAK,CAAC,EAAE,IAAI;IACd;IAEAH,UAAUG,MAAMc,IAAI,CAAC;IACrB,qEAAqE;IACrE,qEAAqE;IACrE,gEAAgE;IAChE,yDAAyD;IACzDjB,UAAUA,QAAQkB,OAAO,CAAC,kDAAkD,KAAK,iBAAiB;IAClGlB,UAAUA,QAAQkB,OAAO,CAAC,+BAA+B,KAAK,iBAAiB;IAC/Ef,QAAQH,QAAQI,KAAK,CAAC;IAEtB,6BAA6B;IAC7BD,QAAQA,MAAMK,MAAM,CAClB,CAACC,MAAMc,OAAOC,MAAQD,UAAU,KAAKd,KAAKW,IAAI,OAAO,MAAMX,KAAKW,IAAI,OAAOI,GAAG,CAACD,QAAQ,EAAE,CAACH,IAAI;IAGhG,yBAAyB;IACzBpB,UAAUG,MAAMc,IAAI,CAAC;IACrB,OAAOjB,QAAQoB,IAAI;AACrB;AAEO,SAASvB,sBAAsB4B,IAAyB;QACrCA,cACEA,gBAEtBC;IAHJ,MAAMC,kBAAkBF,yBAAAA,eAAAA,KAAMG,MAAM,qBAAZH,aAAcd,GAAG,CAACT;IAC1C,MAAM2B,oBAAoBJ,yBAAAA,iBAAAA,KAAMK,QAAQ,qBAAdL,eAAgBd,GAAG,CAACT;IAC9C,MAAMwB,SAAS;QAAEE,QAAQD;QAAiBG,UAAUD;IAAkB;IACtE,KAAIH,iBAAAA,OAAOE,MAAM,qBAAbF,eAAeK,IAAI,CAAChC,uBAAuB;QAC7C,kDAAkD;QAClD2B,OAAOE,MAAM,GAAGF,OAAOE,MAAM,CAACpB,MAAM,CAACT;IACvC;IACA,OAAO2B;AACT"}