{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactFreeze", "obj", "__esModule", "default", "DelayedFreeze", "_ref", "freeze", "children", "freezeState", "setFreezeState", "React", "useState", "useEffect", "id", "setImmediate", "clearImmediate", "createElement", "Freeze", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/helpers/DelayedFreeze.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAsC,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAOtC;AACA;AACA,SAASG,aAAaA,CAAAC,IAAA,EAA2C;EAAA,IAA1C;IAAEC,MAAM;IAAEC;EAA6B,CAAC,GAAAF,IAAA;EAC7D;EACA,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;EAE3DD,cAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM;MAC5BL,cAAc,CAACH,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,OAAO,MAAM;MACXS,cAAc,CAACF,EAAE,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;EAEZ,oBAAOT,MAAA,CAAAM,OAAA,CAAAa,aAAA,CAAChB,YAAA,CAAAiB,MAAM;IAACX,MAAM,EAAEA,MAAM,GAAGE,WAAW,GAAG;EAAM,GAAED,QAAiB,CAAC;AAC1E;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAhB,OAAA,GAEcC,aAAa"}