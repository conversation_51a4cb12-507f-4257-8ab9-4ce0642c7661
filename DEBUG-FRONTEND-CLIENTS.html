<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Frontend - Clients</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #f0fdf4; color: #059669; border: 1px solid #10b981; }
        .error { background-color: #fef2f2; color: #dc2626; border: 1px solid #dc2626; }
        .info { background-color: #f0f8ff; color: #3b82f6; border: 1px solid #3b82f6; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Frontend - Chargement des Clients</h1>
    
    <div class="debug-container">
        <h2>🧪 Tests de Connectivité</h2>
        <button onclick="testServerConnection()">Test Serveur</button>
        <button onclick="testClientsAPI()">Test API Clients</button>
        <button onclick="testContractsAPI()">Test API Contrats</button>
        <button onclick="simulateReactFetch()">Simuler React Fetch</button>
        <div id="connectionStatus"></div>
    </div>
    
    <div class="debug-container">
        <h2>📊 Résultats des Tests</h2>
        <div id="testResults"></div>
    </div>
    
    <div class="debug-container">
        <h2>📝 Journal de Debug</h2>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status info"><span class="loading"></span> ${message}</div>`;
        }
        
        async function testServerConnection() {
            log('🔄 Test de connexion au serveur...');
            showLoading('connectionStatus', 'Test de connexion au serveur...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Serveur accessible');
                    showStatus('connectionStatus', 'success', `✅ Serveur accessible - ${data.message}`);
                } else {
                    log(`❌ Erreur serveur: ${response.status}`);
                    showStatus('connectionStatus', 'error', `❌ Erreur serveur: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Erreur de connexion: ${error.message}`);
                showStatus('connectionStatus', 'error', `❌ Erreur de connexion: ${error.message}`);
            }
        }
        
        async function testClientsAPI() {
            log('🔄 Test de l\'API clients...');
            showLoading('testResults', 'Test de l\'API clients...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ API clients OK - ${data.count} clients reçus`);
                    showStatus('testResults', 'success', 
                        `✅ API clients OK - ${data.count} clients reçus<br>` +
                        `Clients: ${data.data.map(c => `${c.nom} ${c.prenom}`).join(', ')}`
                    );
                } else {
                    log(`❌ Erreur API clients: ${data.message || 'Erreur inconnue'}`);
                    showStatus('testResults', 'error', `❌ Erreur API clients: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                log(`❌ Erreur lors du test API clients: ${error.message}`);
                showStatus('testResults', 'error', `❌ Erreur lors du test API clients: ${error.message}`);
            }
        }
        
        async function testContractsAPI() {
            log('🔄 Test de l\'API contrats...');
            showLoading('testResults', 'Test de l\'API contrats...');
            
            try {
                // Test avec client ID 1 (devrait avoir 1 contrat)
                const response = await fetch(`${API_BASE_URL}/api/clients/1/contracts`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ API contrats OK - ${data.count} contrat(s) pour le client 1`);
                    showStatus('testResults', 'success', 
                        `✅ API contrats OK - ${data.count} contrat(s) pour le client 1<br>` +
                        `Contrats: ${data.data.map(c => c.codeqr).join(', ')}`
                    );
                } else {
                    log(`❌ Erreur API contrats: ${data.message || 'Erreur inconnue'}`);
                    showStatus('testResults', 'error', `❌ Erreur API contrats: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                log(`❌ Erreur lors du test API contrats: ${error.message}`);
                showStatus('testResults', 'error', `❌ Erreur lors du test API contrats: ${error.message}`);
            }
        }
        
        async function simulateReactFetch() {
            log('🔄 Simulation du fetch React...');
            showLoading('testResults', 'Simulation du fetch React...');
            
            try {
                // Reproduire exactement le code de listes_clients.js
                const response = await fetch(`${API_BASE_URL}/api/clients`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                const data = await response.json();
                log('📊 Données reçues:', JSON.stringify(data, null, 2));

                if (data.success && data.data) {
                    log(`✅ ${data.data.length} clients chargés depuis la base "Facutration"`);
                    showStatus('testResults', 'success', 
                        `✅ Simulation React réussie - ${data.data.length} clients chargés<br>` +
                        `Structure des données correcte: ${JSON.stringify(data.data[0], null, 2)}`
                    );
                } else {
                    log('⚠️ Aucun client trouvé dans la réponse');
                    showStatus('testResults', 'error', '⚠️ Aucun client trouvé dans la réponse');
                }
            } catch (error) {
                log(`❌ Erreur lors de la simulation React: ${error.message}`);
                showStatus('testResults', 'error', `❌ Erreur lors de la simulation React: ${error.message}`);
            }
        }
        
        // Tests automatiques au chargement
        window.onload = function() {
            log('🚀 Debug Frontend initialisé');
            log('💡 Cliquez sur les boutons pour tester chaque composant');
            
            // Test automatique de base
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        };
    </script>
</body>
</html>
