import*as e from"../../core/i18n/i18n.js";import*as i from"../../ui/legacy/legacy.js";const t={security:"Security",showSecurity:"Show Security"},r=e.i18n.registerUIStrings("panels/security/security-meta.ts",t),s=e.i18n.getLazilyComputedLocalizedString.bind(void 0,r);let c;i.ViewManager.registerViewExtension({location:"panel",id:"security",title:s(t.security),commandPrompt:s(t.showSecurity),order:80,persistence:"closeable",loadView:async()=>(await async function(){return c||(c=await import("./security.js")),c}()).SecurityPanel.SecurityPanel.instance()});
