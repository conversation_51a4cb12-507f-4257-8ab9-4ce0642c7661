{"version": 3, "sources": ["mocks.tsx"], "names": ["React", "TouchableHighlight", "TouchableNativeFeedback", "TouchableOpacity", "TouchableWithoutFeedback", "ScrollView", "FlatList", "Switch", "TextInput", "DrawerLayoutAndroid", "View", "State", "Directions", "NOOP", "PanGestureHandler", "attachGestureHandler", "createGestureHandler", "dropGestureHandler", "updateGestureHandler", "flushOperations", "install", "NativeViewGestureHandler", "TapGestureHandler", "ForceTouchGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "RawButton", "enabled", "rest", "BaseButton", "RectButton", "BorderlessButton"], "mappings": ";;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SACEC,kBADF,EAEEC,uBAFF,EAGEC,gBAHF,EAIEC,wBAJF,EAKEC,UALF,EAMEC,QANF,EAOEC,MAPF,EAQEC,SARF,EASEC,mBATF,EAUEC,IAVF,QAWO,cAXP;AAYA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,UAAT,QAA2B,cAA3B;;AAEA,MAAMC,IAAI,GAAG,MAAM,CACjB;AACD,CAFD;;AAGA,MAAMC,iBAAiB,GAAGJ,IAA1B;AACA,MAAMK,oBAAoB,GAAGF,IAA7B;AACA,MAAMG,oBAAoB,GAAGH,IAA7B;AACA,MAAMI,kBAAkB,GAAGJ,IAA3B;AACA,MAAMK,oBAAoB,GAAGL,IAA7B;AACA,MAAMM,eAAe,GAAGN,IAAxB;AACA,MAAMO,OAAO,GAAGP,IAAhB;AACA,MAAMQ,wBAAwB,GAAGX,IAAjC;AACA,MAAMY,iBAAiB,GAAGZ,IAA1B;AACA,MAAMa,wBAAwB,GAAGb,IAAjC;AACA,MAAMc,uBAAuB,GAAGd,IAAhC;AACA,MAAMe,mBAAmB,GAAGf,IAA5B;AACA,MAAMgB,sBAAsB,GAAGhB,IAA/B;AACA,MAAMiB,mBAAmB,GAAGjB,IAA5B;;AACA,MAAMkB,SAAS,GAAG,CAAC;AAAEC,EAAAA,OAAF;AAAW,KAAGC;AAAd,CAAD,kBAChB,oBAAC,uBAAD;AAAyB,EAAA,QAAQ,EAAE,CAACD;AAApC,GAAiDC,IAAjD,gBACE,oBAAC,IAAD,OADF,CADF;;AAKA,MAAMC,UAAU,GAAGH,SAAnB;AACA,MAAMI,UAAU,GAAGJ,SAAnB;AACA,MAAMK,gBAAgB,GAAG/B,uBAAzB;AAEA,eAAe;AACbD,EAAAA,kBADa;AAEbC,EAAAA,uBAFa;AAGbC,EAAAA,gBAHa;AAIbC,EAAAA,wBAJa;AAKbC,EAAAA,UALa;AAMbC,EAAAA,QANa;AAObC,EAAAA,MAPa;AAQbC,EAAAA,SARa;AASbC,EAAAA,mBATa;AAUbY,EAAAA,wBAVa;AAWbC,EAAAA,iBAXa;AAYbC,EAAAA,wBAZa;AAabC,EAAAA,uBAba;AAcbC,EAAAA,mBAda;AAebC,EAAAA,sBAfa;AAgBbC,EAAAA,mBAhBa;AAiBbC,EAAAA,SAjBa;AAkBbG,EAAAA,UAlBa;AAmBbC,EAAAA,UAnBa;AAoBbC,EAAAA,gBApBa;AAqBbnB,EAAAA,iBArBa;AAsBbC,EAAAA,oBAtBa;AAuBbC,EAAAA,oBAvBa;AAwBbC,EAAAA,kBAxBa;AAyBbC,EAAAA,oBAzBa;AA0BbC,EAAAA,eA1Ba;AA2BbC,EAAAA,OA3Ba;AA4Bb;AACAR,EAAAA,UA7Ba;AA8BbD,EAAAA;AA9Ba,CAAf", "sourcesContent": ["import React from 'react';\nimport {\n  TouchableHighlight,\n  TouchableNativeFeedback,\n  TouchableOpacity,\n  TouchableWithoutFeedback,\n  ScrollView,\n  FlatList,\n  Switch,\n  TextInput,\n  DrawerLayoutAndroid,\n  View,\n} from 'react-native';\nimport { State } from './State';\nimport { Directions } from './Directions';\n\nconst NOOP = () => {\n  // Do nothing\n};\nconst PanGestureHandler = View;\nconst attachGestureHandler = NOOP;\nconst createGestureHandler = NOOP;\nconst dropGestureHandler = NOOP;\nconst updateGestureHandler = NOOP;\nconst flushOperations = NOOP;\nconst install = NOOP;\nconst NativeViewGestureHandler = View;\nconst TapGestureHandler = View;\nconst ForceTouchGestureHandler = View;\nconst LongPressGestureHandler = View;\nconst PinchGestureHandler = View;\nconst RotationGestureHandler = View;\nconst FlingGestureHandler = View;\nconst RawButton = ({ enabled, ...rest }: any) => (\n  <TouchableNativeFeedback disabled={!enabled} {...rest}>\n    <View />\n  </TouchableNativeFeedback>\n);\nconst BaseButton = RawButton;\nconst RectButton = RawButton;\nconst BorderlessButton = TouchableNativeFeedback;\n\nexport default {\n  TouchableHighlight,\n  TouchableNativeFeedback,\n  TouchableOpacity,\n  TouchableWithoutFeedback,\n  ScrollView,\n  FlatList,\n  Switch,\n  TextInput,\n  DrawerLayoutAndroid,\n  NativeViewGestureHandler,\n  TapGestureHandler,\n  ForceTouchGestureHandler,\n  LongPressGestureHandler,\n  PinchGestureHandler,\n  RotationGestureHandler,\n  FlingGestureHandler,\n  RawButton,\n  BaseButton,\n  RectButton,\n  BorderlessButton,\n  PanGestureHandler,\n  attachGestureHandler,\n  createGestureHandler,\n  dropGestureHandler,\n  updateGestureHandler,\n  flushOperations,\n  install,\n  // Probably can be removed\n  Directions,\n  State,\n} as const;\n"]}