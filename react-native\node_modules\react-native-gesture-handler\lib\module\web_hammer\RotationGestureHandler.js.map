{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["Hammer", "DEG_RAD", "IndiscreteGestureHandler", "RotationGestureHandler", "name", "NativeGestureClass", "Rotate", "transformNativeEvent", "rotation", "velocity", "center", "initialRotation", "anchorX", "x", "anchorY", "y"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,gBAAnB;AAEA,SAASC,OAAT,QAAwB,aAAxB;AAEA,OAAOC,wBAAP,MAAqC,4BAArC;;AAEA,MAAMC,sBAAN,SAAqCD,wBAArC,CAA8D;AACpD,MAAJE,IAAI,GAAG;AACT,WAAO,QAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOL,MAAM,CAACM,MAAd;AACD;;AAEDC,EAAAA,oBAAoB,CAAC;AAAEC,IAAAA,QAAF;AAAYC,IAAAA,QAAZ;AAAsBC,IAAAA;AAAtB,GAAD,EAAiD;AAAA;;AACnE,WAAO;AACLF,MAAAA,QAAQ,EAAE,CAACA,QAAQ,6BAAI,KAAKG,eAAT,yEAA4B,CAA5B,CAAT,IAA2CV,OADhD;AAELW,MAAAA,OAAO,EAAEF,MAAM,CAACG,CAFX;AAGLC,MAAAA,OAAO,EAAEJ,MAAM,CAACK,CAHX;AAILN,MAAAA;AAJK,KAAP;AAMD;;AAhB2D;;AAkB9D,eAAeN,sBAAf", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport { DEG_RAD } from './constants';\nimport { HammerInputExt } from './GestureHandler';\nimport IndiscreteGestureHandler from './IndiscreteGestureHandler';\n\nclass RotationGestureHandler extends IndiscreteGestureHandler {\n  get name() {\n    return 'rotate';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Rotate;\n  }\n\n  transformNativeEvent({ rotation, velocity, center }: HammerInputExt) {\n    return {\n      rotation: (rotation - (this.initialRotation ?? 0)) * DEG_RAD,\n      anchorX: center.x,\n      anchorY: center.y,\n      velocity,\n    };\n  }\n}\nexport default RotationGestureHandler;\n"]}