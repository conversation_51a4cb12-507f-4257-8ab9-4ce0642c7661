{"version": 3, "sources": ["GestureHandler.ts"], "names": ["State", "TouchEventType", "EventTypes", "GestureHandlerOrchestrator", "InteractionManager", "PointerTracker", "MouseB<PERSON>on", "PointerType", "Gesture<PERSON>andler", "constructor", "delegate", "UNDETERMINED", "enabled", "MOUSE", "newState", "oldState", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "resultEvent", "transformEventData", "lastSentState", "invokeNullableMethod", "currentState", "ACTIVE", "nativeEvent", "undefined", "init", "viewRef", "attachEventManager", "manager", "setOnPointerDown", "onPointerDown", "bind", "setOnPointerAdd", "onPointerAdd", "setOnPointerUp", "onPointerUp", "setOnPointerRemove", "onPointerRemove", "setOnPointerMove", "onPointerMove", "setOnPointerEnter", "onPointerEnter", "setOnPointerLeave", "onPointerLeave", "setOnPointerCancel", "onPointerCancel", "setOnPointerOutOfBounds", "onPointerOutOfBounds", "setOnPointerMoveOver", "onPointerMoveOver", "setOnPointerMoveOut", "onPointerMoveOut", "registerListeners", "onCancel", "onReset", "resetProgress", "reset", "tracker", "resetTracker", "moveToState", "sendIfDisabled", "getTrackedPointersCount", "config", "needsPointerData", "isFinished", "cancelTouches", "getInstance", "onHandlerStateChange", "onStateChange", "_newState", "_oldState", "begin", "checkHitSlop", "BEGAN", "fail", "onFail", "FAILED", "cancel", "CANCELLED", "activate", "force", "manualActivation", "onActivate", "end", "onEnd", "END", "isAwaiting", "awaiting", "setAwaiting", "value", "isActive", "active", "setActive", "getShouldResetProgress", "shouldResetProgress", "setShouldResetProgress", "getActivationIndex", "activationIndex", "setActivationIndex", "shouldWaitForHandlerFailure", "handler", "shouldRequireToWaitForFailure", "shouldRequireHandlerToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "shouldHandlerBeCancelledBy", "event", "recordHandlerIfNotPresent", "pointerType", "TOUCH", "cancelMouseAndPenGestures", "tryToSendTouchEvent", "tryToSendMoveEvent", "shouldCancelWhenOutside", "_event", "out", "sendEvent", "sendTouchEvent", "touchEvent", "transformTouchEvent", "numberOfPointers", "state", "pointerInside", "isPointerInBounds", "getAbsoluteCoordsAverage", "transformNativeEvent", "handlerTag", "target", "timeStamp", "Date", "now", "rect", "measure<PERSON>iew", "all", "changed", "trackerData", "getData", "size", "has", "pointerId", "for<PERSON>ach", "element", "key", "id", "getMappedTouchEventId", "push", "x", "abosoluteCoords", "pageX", "y", "pageY", "absoluteX", "absoluteY", "eventType", "CANCEL", "DOWN", "ADDITIONAL_POINTER_DOWN", "UP", "ADDITIONAL_POINTER_UP", "MOVE", "numberOfTouches", "length", "changedTouches", "allTouches", "cancelEvent", "lastCoords", "lastRelativeCoords", "getRelativeCoordsAverage", "updateGestureConfig", "props", "onEnabledChange", "setShouldCancelWhenOutside", "validateHitSlops", "removeHandlerFromOrchestrator", "checkCustomActivationCriteria", "criterias", "indexOf", "hasCustomActivationCriteria", "hitSlop", "left", "right", "width", "Error", "height", "top", "bottom", "horizontal", "vertical", "getLastAbsoluteCoords", "offsetX", "offsetY", "isButtonInConfig", "mouseButton", "LEFT", "resetConfig", "onDestroy", "destroy", "getTag", "setTag", "tag", "getConfig", "getDelegate", "getTracker", "getTrackedPointersID", "getState", "isEnabled", "shouldCancel", "getShouldCancelWhenOutside", "getPointerType", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "Array", "isArray", "index", "entries", "nativeValue", "setValue"], "mappings": ";;AAAA;AACA,SAASA,KAAT,QAAsB,aAAtB;AACA,SAOEC,cAPF,EAQEC,UARF,QASO,eATP;AAWA,OAAOC,0BAAP,MAAuC,qCAAvC;AACA,OAAOC,kBAAP,MAA+B,6BAA/B;AACA,OAAOC,cAAP,MAA+C,yBAA/C;AAEA,SAASC,WAAT,QAA4B,qCAA5B;AACA,SAASC,WAAT,QAA4B,mBAA5B;AAGA,eAAe,MAAeC,cAAf,CAAyD;AAetE;AASOC,EAAAA,WAAW,CAChBC,QADgB,EAEhB;AAAA,2CAzBoC,IAyBpC;;AAAA,0CAxB8BV,KAAK,CAACW,YAwBpC;;AAAA,qDAtBkC,KAsBlC;;AAAA,yDArBsC,KAqBtC;;AAAA,qCApBkB,KAoBlB;;AAAA;;AAAA;;AAAA;;AAAA,oCAfyB;AAAEC,MAAAA,OAAO,EAAE;AAAX,KAezB;;AAAA,qCAbkC,IAAIP,cAAJ,EAalC;;AAAA,6CAV0B,CAU1B;;AAAA,sCATmB,KASnB;;AAAA,oCARiB,KAQjB;;AAAA,iDAP8B,KAO9B;;AAAA,yCANmCE,WAAW,CAACM,KAM/C;;AAAA;;AAAA,uCA2UiB,CAACC,QAAD,EAAkBC,QAAlB,KAA4C;AAC7D,YAAM;AAAEC,QAAAA,qBAAF;AAAyBC,QAAAA;AAAzB,UACJ,KAAKC,QAAL,CAAcC,OADhB;AAGA,YAAMC,WAAwB,GAAG,KAAKC,kBAAL,CAC/BP,QAD+B,EAE/BC,QAF+B,CAAjC,CAJ6D,CAS7D;AACA;AACA;AACA;;AAEA,UAAI,KAAKO,aAAL,KAAuBR,QAA3B,EAAqC;AACnC,aAAKQ,aAAL,GAAqBR,QAArB;AACAS,QAAAA,oBAAoB,CAACN,2BAAD,EAA8BG,WAA9B,CAApB;AACD;;AACD,UAAI,KAAKI,YAAL,KAAsBxB,KAAK,CAACyB,MAAhC,EAAwC;AACtCL,QAAAA,WAAW,CAACM,WAAZ,CAAwBX,QAAxB,GAAmCY,SAAnC;AACAJ,QAAAA,oBAAoB,CAACP,qBAAD,EAAwBI,WAAxB,CAApB;AACD;AACF,KAjWC;;AACA,SAAKV,QAAL,GAAgBA,QAAhB;AACD,GA5BqE,CA8BtE;AACA;AACA;;;AAEUkB,EAAAA,IAAI,CAACC,OAAD,EAAkBX,QAAlB,EAAsD;AAClE,SAAKA,QAAL,GAAgBA,QAAhB;AACA,SAAKW,OAAL,GAAeA,OAAf;AAEA,SAAKL,YAAL,GAAoBxB,KAAK,CAACW,YAA1B;AAEA,SAAKD,QAAL,CAAckB,IAAd,CAAmBC,OAAnB,EAA4B,IAA5B;AACD;;AAEMC,EAAAA,kBAAkB,CAACC,OAAD,EAAuC;AAC9DA,IAAAA,OAAO,CAACC,gBAAR,CAAyB,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACI,eAAR,CAAwB,KAAKC,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAAxB;AACAH,IAAAA,OAAO,CAACM,cAAR,CAAuB,KAAKC,WAAL,CAAiBJ,IAAjB,CAAsB,IAAtB,CAAvB;AACAH,IAAAA,OAAO,CAACQ,kBAAR,CAA2B,KAAKC,eAAL,CAAqBN,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACU,gBAAR,CAAyB,KAAKC,aAAL,CAAmBR,IAAnB,CAAwB,IAAxB,CAAzB;AACAH,IAAAA,OAAO,CAACY,iBAAR,CAA0B,KAAKC,cAAL,CAAoBV,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACc,iBAAR,CAA0B,KAAKC,cAAL,CAAoBZ,IAApB,CAAyB,IAAzB,CAA1B;AACAH,IAAAA,OAAO,CAACgB,kBAAR,CAA2B,KAAKC,eAAL,CAAqBd,IAArB,CAA0B,IAA1B,CAA3B;AACAH,IAAAA,OAAO,CAACkB,uBAAR,CAAgC,KAAKC,oBAAL,CAA0BhB,IAA1B,CAA+B,IAA/B,CAAhC;AACAH,IAAAA,OAAO,CAACoB,oBAAR,CAA6B,KAAKC,iBAAL,CAAuBlB,IAAvB,CAA4B,IAA5B,CAA7B;AACAH,IAAAA,OAAO,CAACsB,mBAAR,CAA4B,KAAKC,gBAAL,CAAsBpB,IAAtB,CAA2B,IAA3B,CAA5B;AAEAH,IAAAA,OAAO,CAACwB,iBAAR;AACD,GAzDqE,CA2DtE;AACA;AACA;;;AAEUC,EAAAA,QAAQ,GAAS,CAAE;;AACnBC,EAAAA,OAAO,GAAS,CAAE;;AAClBC,EAAAA,aAAa,GAAS,CAAE;;AAE3BC,EAAAA,KAAK,GAAS;AACnB,SAAKC,OAAL,CAAaC,YAAb;AACA,SAAKJ,OAAL;AACA,SAAKC,aAAL;AACA,SAAKhD,QAAL,CAAciD,KAAd;AACA,SAAKnC,YAAL,GAAoBxB,KAAK,CAACW,YAA1B;AACD,GAzEqE,CA2EtE;AACA;AACA;;;AAEOmD,EAAAA,WAAW,CAAChD,QAAD,EAAkBiD,cAAlB,EAA4C;AAC5D,QAAI,KAAKvC,YAAL,KAAsBV,QAA1B,EAAoC;AAClC;AACD;;AAED,UAAMC,QAAQ,GAAG,KAAKS,YAAtB;AACA,SAAKA,YAAL,GAAoBV,QAApB;;AAEA,QACE,KAAK8C,OAAL,CAAaI,uBAAb,KAAyC,CAAzC,IACA,KAAKC,MAAL,CAAYC,gBADZ,IAEA,KAAKC,UAAL,EAHF,EAIE;AACA,WAAKC,aAAL;AACD;;AAEDjE,IAAAA,0BAA0B,CAACkE,WAA3B,GAAyCC,oBAAzC,CACE,IADF,EAEExD,QAFF,EAGEC,QAHF,EAIEgD,cAJF;AAOA,SAAKQ,aAAL,CAAmBzD,QAAnB,EAA6BC,QAA7B;;AAEA,QAAI,CAAC,KAAKH,OAAN,IAAiB,KAAKuD,UAAL,EAArB,EAAwC;AACtC,WAAK3C,YAAL,GAAoBxB,KAAK,CAACW,YAA1B;AACD;AACF;;AAES4D,EAAAA,aAAa,CAACC,SAAD,EAAmBC,SAAnB,EAA2C,CAAE;;AAE7DC,EAAAA,KAAK,GAAS;AACnB,QAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;AACxB;AACD;;AAED,QAAI,KAAKnD,YAAL,KAAsBxB,KAAK,CAACW,YAAhC,EAA8C;AAC5C,WAAKmD,WAAL,CAAiB9D,KAAK,CAAC4E,KAAvB;AACD;AACF;AAED;AACF;AACA;;;AACSC,EAAAA,IAAI,CAACd,cAAD,EAAiC;AAC1C,QACE,KAAKvC,YAAL,KAAsBxB,KAAK,CAACyB,MAA5B,IACA,KAAKD,YAAL,KAAsBxB,KAAK,CAAC4E,KAF9B,EAGE;AACA;AACA;AACA,WAAKlE,QAAL,CAAcoE,MAAd;AAEA,WAAKhB,WAAL,CAAiB9D,KAAK,CAAC+E,MAAvB,EAA+BhB,cAA/B;AACD;;AAED,SAAKL,aAAL;AACD;AAED;AACF;AACA;;;AACSsB,EAAAA,MAAM,CAACjB,cAAD,EAAiC;AAC5C,QACE,KAAKvC,YAAL,KAAsBxB,KAAK,CAACyB,MAA5B,IACA,KAAKD,YAAL,KAAsBxB,KAAK,CAACW,YAD5B,IAEA,KAAKa,YAAL,KAAsBxB,KAAK,CAAC4E,KAH9B,EAIE;AACA,WAAKpB,QAAL,GADA,CAGA;;AACA,WAAK9C,QAAL,CAAc8C,QAAd;AAEA,WAAKM,WAAL,CAAiB9D,KAAK,CAACiF,SAAvB,EAAkClB,cAAlC;AACD;AACF;;AAEMmB,EAAAA,QAAQ,CAACC,KAAK,GAAG,KAAT,EAAgB;AAC7B,QACE,CAAC,KAAKlB,MAAL,CAAYmB,gBAAZ,KAAiC,IAAjC,IAAyCD,KAA1C,MACC,KAAK3D,YAAL,KAAsBxB,KAAK,CAACW,YAA5B,IACC,KAAKa,YAAL,KAAsBxB,KAAK,CAAC4E,KAF9B,CADF,EAIE;AACA,WAAKlE,QAAL,CAAc2E,UAAd;AACA,WAAKvB,WAAL,CAAiB9D,KAAK,CAACyB,MAAvB;AACD;AACF;;AAEM6D,EAAAA,GAAG,GAAG;AACX,QACE,KAAK9D,YAAL,KAAsBxB,KAAK,CAAC4E,KAA5B,IACA,KAAKpD,YAAL,KAAsBxB,KAAK,CAACyB,MAF9B,EAGE;AACA;AACA,WAAKf,QAAL,CAAc6E,KAAd;AAEA,WAAKzB,WAAL,CAAiB9D,KAAK,CAACwF,GAAvB;AACD;;AAED,SAAK9B,aAAL;AACD,GApLqE,CAsLtE;AACA;AACA;;;AAEO+B,EAAAA,UAAU,GAAY;AAC3B,WAAO,KAAKC,QAAZ;AACD;;AACMC,EAAAA,WAAW,CAACC,KAAD,EAAuB;AACvC,SAAKF,QAAL,GAAgBE,KAAhB;AACD;;AAEMC,EAAAA,QAAQ,GAAY;AACzB,WAAO,KAAKC,MAAZ;AACD;;AACMC,EAAAA,SAAS,CAACH,KAAD,EAAuB;AACrC,SAAKE,MAAL,GAAcF,KAAd;AACD;;AAEMI,EAAAA,sBAAsB,GAAY;AACvC,WAAO,KAAKC,mBAAZ;AACD;;AACMC,EAAAA,sBAAsB,CAACN,KAAD,EAAuB;AAClD,SAAKK,mBAAL,GAA2BL,KAA3B;AACD;;AAEMO,EAAAA,kBAAkB,GAAW;AAClC,WAAO,KAAKC,eAAZ;AACD;;AACMC,EAAAA,kBAAkB,CAACT,KAAD,EAAsB;AAC7C,SAAKQ,eAAL,GAAuBR,KAAvB;AACD;;AAEMU,EAAAA,2BAA2B,CAACC,OAAD,EAAoC;AACpE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOnG,kBAAkB,CAACiE,WAAnB,GAAiCiC,2BAAjC,CACL,IADK,EAELC,OAFK,CAAP;AAID;;AAEMC,EAAAA,6BAA6B,CAACD,OAAD,EAAoC;AACtE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOnG,kBAAkB,CAACiE,WAAnB,GAAiCoC,oCAAjC,CACL,IADK,EAELF,OAFK,CAAP;AAID;;AAEMG,EAAAA,6BAA6B,CAACH,OAAD,EAAoC;AACtE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,IAAP;AACD;;AAED,WAAOnG,kBAAkB,CAACiE,WAAnB,GAAiCqC,6BAAjC,CACL,IADK,EAELH,OAFK,CAAP;AAID;;AAEMI,EAAAA,wBAAwB,CAACJ,OAAD,EAAoC;AACjE,QAAIA,OAAO,KAAK,IAAhB,EAAsB;AACpB,aAAO,KAAP;AACD;;AAED,WAAOnG,kBAAkB,CAACiE,WAAnB,GAAiCuC,0BAAjC,CACL,IADK,EAELL,OAFK,CAAP;AAID,GAhQqE,CAkQtE;AACA;AACA;;;AAEUtE,EAAAA,aAAa,CAAC4E,KAAD,EAA4B;AACjD1G,IAAAA,0BAA0B,CAACkE,WAA3B,GAAyCyC,yBAAzC,CAAmE,IAAnE;AACA,SAAKC,WAAL,GAAmBF,KAAK,CAACE,WAAzB;;AAEA,QAAI,KAAKA,WAAL,KAAqBxG,WAAW,CAACyG,KAArC,EAA4C;AAC1C7G,MAAAA,0BAA0B,CAACkE,WAA3B,GAAyC4C,yBAAzC,CAAmE,IAAnE;AACD,KANgD,CAQjD;;AACD,GA/QqE,CAgRtE;;;AACU7E,EAAAA,YAAY,CAACyE,KAAD,EAA4B;AAChD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACSvE,EAAAA,WAAW,CAACuE,KAAD,EAA4B;AAC/C,SAAKK,mBAAL,CAAyBL,KAAzB;AACD,GAtRqE,CAuRtE;;;AACUrE,EAAAA,eAAe,CAACqE,KAAD,EAA4B;AACnD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACSnE,EAAAA,aAAa,CAACmE,KAAD,EAA4B;AACjD,SAAKM,kBAAL,CAAwB,KAAxB,EAA+BN,KAA/B;AACD;;AACS/D,EAAAA,cAAc,CAAC+D,KAAD,EAA4B;AAClD,QAAI,KAAKO,uBAAT,EAAkC;AAChC,cAAQ,KAAK5F,YAAb;AACE,aAAKxB,KAAK,CAACyB,MAAX;AACE,eAAKuD,MAAL;AACA;;AACF,aAAKhF,KAAK,CAAC4E,KAAX;AACE,eAAKC,IAAL;AACA;AANJ;;AAQA;AACD;;AAED,SAAKqC,mBAAL,CAAyBL,KAAzB;AACD;;AACSjE,EAAAA,cAAc,CAACiE,KAAD,EAA4B;AAClD,SAAKK,mBAAL,CAAyBL,KAAzB;AACD;;AACS7D,EAAAA,eAAe,CAAC6D,KAAD,EAA4B;AACnD,SAAKK,mBAAL,CAAyBL,KAAzB;AAEA,SAAK7B,MAAL;AACA,SAAKrB,KAAL;AACD;;AACST,EAAAA,oBAAoB,CAAC2D,KAAD,EAA4B;AACxD,SAAKM,kBAAL,CAAwB,IAAxB,EAA8BN,KAA9B;AACD;;AACSzD,EAAAA,iBAAiB,CAACiE,MAAD,EAA6B,CACtD;AACD;;AACS/D,EAAAA,gBAAgB,CAAC+D,MAAD,EAA6B,CACrD;AACD;;AACOF,EAAAA,kBAAkB,CAACG,GAAD,EAAeT,KAAf,EAA0C;AAClE,QAAKS,GAAG,IAAI,KAAKF,uBAAb,IAAyC,CAAC,KAAKxG,OAAnD,EAA4D;AAC1D;AACD;;AAED,QAAI,KAAKkF,MAAT,EAAiB;AACf,WAAKyB,SAAL,CAAe,KAAK/F,YAApB,EAAkC,KAAKA,YAAvC;AACD;;AAED,SAAK0F,mBAAL,CAAyBL,KAAzB;AACD;;AAESK,EAAAA,mBAAmB,CAACL,KAAD,EAA4B;AACvD,QAAI,KAAK5C,MAAL,CAAYC,gBAAhB,EAAkC;AAChC,WAAKsD,cAAL,CAAoBX,KAApB;AACD;AACF;;AAEMW,EAAAA,cAAc,CAACX,KAAD,EAA4B;AAC/C,QAAI,CAAC,KAAKjG,OAAV,EAAmB;AACjB;AACD;;AAED,UAAM;AAAEI,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGA,UAAMsG,UAAwC,GAC5C,KAAKC,mBAAL,CAAyBb,KAAzB,CADF;;AAGA,QAAIY,UAAJ,EAAgB;AACdlG,MAAAA,oBAAoB,CAACP,qBAAD,EAAwByG,UAAxB,CAApB;AACD;AACF,GA/VqE,CAiWtE;AACA;AACA;;;AA0BQpG,EAAAA,kBAAkB,CAACP,QAAD,EAAkBC,QAAlB,EAAgD;AACxE,WAAO;AACLW,MAAAA,WAAW,EAAE;AACXiG,QAAAA,gBAAgB,EAAE,KAAK/D,OAAL,CAAaI,uBAAb,EADP;AAEX4D,QAAAA,KAAK,EAAE9G,QAFI;AAGX+G,QAAAA,aAAa,EAAE,KAAKnH,QAAL,CAAcoH,iBAAd,CACb,KAAKlE,OAAL,CAAamE,wBAAb,EADa,CAHJ;AAMX,WAAG,KAAKC,oBAAL,EANQ;AAOXC,QAAAA,UAAU,EAAE,KAAKA,UAPN;AAQXC,QAAAA,MAAM,EAAE,KAAKrG,OARF;AASXd,QAAAA,QAAQ,EAAED,QAAQ,KAAKC,QAAb,GAAwBA,QAAxB,GAAmCY,SATlC;AAUXoF,QAAAA,WAAW,EAAE,KAAKA;AAVP,OADR;AAaLoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAbN,KAAP;AAeD;;AAEOX,EAAAA,mBAAmB,CACzBb,KADyB,EAEK;AAC9B,UAAMyB,IAAI,GAAG,KAAK5H,QAAL,CAAc6H,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAK9E,OAAL,CAAa+E,OAAb,EAApB,CAN8B,CAQ9B;AACA;AACA;AACA;;AACA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAArB,IAA0B,CAACF,WAAW,CAACG,GAAZ,CAAgBhC,KAAK,CAACiC,SAAtB,CAA/B,EAAiE;AAC/D;AACD;;AAEDJ,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAKtF,OAAL,CAAauF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAF7B;AAGPC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAH7B;AAIPC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJ5B;AAKPM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AAL5B,OAAT;AAOD,KAVD,EAhB8B,CA4B9B;AACA;;AACA,QAAI3C,KAAK,CAAC+C,SAAN,KAAoB1J,UAAU,CAAC2J,MAAnC,EAA2C;AACzCpB,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAE,KAAKtF,OAAL,CAAauF,qBAAb,CAAmCtC,KAAK,CAACiC,SAAzC,CADO;AAEXO,QAAAA,CAAC,EAAExC,KAAK,CAACwC,CAAN,GAAUf,IAAI,CAACiB,KAFP;AAGXC,QAAAA,CAAC,EAAE3C,KAAK,CAAC2C,CAAN,GAAUlB,IAAI,CAACmB,KAHP;AAIXC,QAAAA,SAAS,EAAE7C,KAAK,CAACwC,CAJN;AAKXM,QAAAA,SAAS,EAAE9C,KAAK,CAAC2C;AALN,OAAb;AAOD,KARD,MAQO;AACLd,MAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,cAAMC,EAAU,GAAG,KAAKtF,OAAL,CAAauF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAR,QAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,UAAAA,EAAE,EAAEA,EADO;AAEXG,UAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAFzB;AAGXC,UAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAHzB;AAIXC,UAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJxB;AAKXM,UAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AALxB,SAAb;AAOD,OAVD;AAWD;;AAED,QAAII,SAAyB,GAAG3J,cAAc,CAACU,YAA/C;;AAEA,YAAQkG,KAAK,CAAC+C,SAAd;AACE,WAAK1J,UAAU,CAAC4J,IAAhB;AACA,WAAK5J,UAAU,CAAC6J,uBAAhB;AACEH,QAAAA,SAAS,GAAG3J,cAAc,CAAC6J,IAA3B;AACA;;AACF,WAAK5J,UAAU,CAAC8J,EAAhB;AACA,WAAK9J,UAAU,CAAC+J,qBAAhB;AACEL,QAAAA,SAAS,GAAG3J,cAAc,CAAC+J,EAA3B;AACA;;AACF,WAAK9J,UAAU,CAACgK,IAAhB;AACEN,QAAAA,SAAS,GAAG3J,cAAc,CAACiK,IAA3B;AACA;;AACF,WAAKhK,UAAU,CAAC2J,MAAhB;AACED,QAAAA,SAAS,GAAG3J,cAAc,CAACgF,SAA3B;AACA;AAdJ,KAtD8B,CAuE9B;AACA;AACA;;;AACA,QAAIkF,eAAuB,GAAG3B,GAAG,CAAC4B,MAAlC;;AAEA,QACEvD,KAAK,CAAC+C,SAAN,KAAoB1J,UAAU,CAAC8J,EAA/B,IACAnD,KAAK,CAAC+C,SAAN,KAAoB1J,UAAU,CAAC+J,qBAFjC,EAGE;AACA,QAAEE,eAAF;AACD;;AAED,WAAO;AACLzI,MAAAA,WAAW,EAAE;AACXuG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXL,QAAAA,KAAK,EAAE,KAAKpG,YAFD;AAGXoI,QAAAA,SAAS,EAAEA,SAHA;AAIXS,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX2B,QAAAA,eAAe,EAAEA,eANN;AAOXpD,QAAAA,WAAW,EAAE,KAAKA;AAPP,OADR;AAULoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAVN,KAAP;AAYD;;AAEOjE,EAAAA,aAAa,GAAS;AAC5B,UAAMkE,IAAI,GAAG,KAAK5H,QAAL,CAAc6H,WAAd,EAAb;AAEA,UAAMC,GAAkB,GAAG,EAA3B;AACA,UAAMC,OAAsB,GAAG,EAA/B;AAEA,UAAMC,WAAW,GAAG,KAAK9E,OAAL,CAAa+E,OAAb,EAApB;;AAEA,QAAID,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;AAC1B;AACD;;AAEDF,IAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,OAAD,EAA0BC,GAA1B,KAAgD;AAClE,YAAMC,EAAU,GAAG,KAAKtF,OAAL,CAAauF,qBAAb,CAAmCF,GAAnC,CAAnB;AAEAT,MAAAA,GAAG,CAACY,IAAJ,CAAS;AACPF,QAAAA,EAAE,EAAEA,EADG;AAEPG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAF7B;AAGPC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAH7B;AAIPC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJ5B;AAKPM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AAL5B,OAAT;AAQAf,MAAAA,OAAO,CAACW,IAAR,CAAa;AACXF,QAAAA,EAAE,EAAEA,EADO;AAEXG,QAAAA,CAAC,EAAEL,OAAO,CAACM,eAAR,CAAwBD,CAAxB,GAA4Bf,IAAI,CAACiB,KAFzB;AAGXC,QAAAA,CAAC,EAAER,OAAO,CAACM,eAAR,CAAwBE,CAAxB,GAA4BlB,IAAI,CAACmB,KAHzB;AAIXC,QAAAA,SAAS,EAAEV,OAAO,CAACM,eAAR,CAAwBD,CAJxB;AAKXM,QAAAA,SAAS,EAAEX,OAAO,CAACM,eAAR,CAAwBE;AALxB,OAAb;AAOD,KAlBD;AAoBA,UAAMe,WAA6B,GAAG;AACpC7I,MAAAA,WAAW,EAAE;AACXuG,QAAAA,UAAU,EAAE,KAAKA,UADN;AAEXL,QAAAA,KAAK,EAAE,KAAKpG,YAFD;AAGXoI,QAAAA,SAAS,EAAE3J,cAAc,CAACgF,SAHf;AAIXoF,QAAAA,cAAc,EAAE5B,OAJL;AAKX6B,QAAAA,UAAU,EAAE9B,GALD;AAMX2B,QAAAA,eAAe,EAAE3B,GAAG,CAAC4B,MANV;AAOXrD,QAAAA,WAAW,EAAE,KAAKA;AAPP,OADuB;AAUpCoB,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAVyB,KAAtC;AAaA,UAAM;AAAErH,MAAAA;AAAF,QAAsC,KAAKE,QAAL,CACzCC,OADH;AAGAI,IAAAA,oBAAoB,CAACP,qBAAD,EAAwBuJ,WAAxB,CAApB;AACD;;AAESvC,EAAAA,oBAAoB,GAA4B;AACxD;AACA,UAAMwC,UAAU,GAAG,KAAK5G,OAAL,CAAamE,wBAAb,EAAnB;AACA,UAAM0C,kBAAkB,GAAG,KAAK7G,OAAL,CAAa8G,wBAAb,EAA3B;AAEA,WAAO;AACLrB,MAAAA,CAAC,EAAEoB,kBAAkB,CAACpB,CADjB;AAELG,MAAAA,CAAC,EAAEiB,kBAAkB,CAACjB,CAFjB;AAGLE,MAAAA,SAAS,EAAEc,UAAU,CAACnB,CAHjB;AAILM,MAAAA,SAAS,EAAEa,UAAU,CAAChB;AAJjB,KAAP;AAMD,GAhjBqE,CAkjBtE;AACA;AACA;;;AAEOmB,EAAAA,mBAAmB,CAAC;AAAE/J,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGgK;AAArB,GAAD,EAA6C;AACrE,SAAK3G,MAAL,GAAc;AAAErD,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGgK;AAAvB,KAAd;AACA,SAAKhK,OAAL,GAAeA,OAAf;AAEA,SAAKF,QAAL,CAAcmK,eAAd,CAA8BjK,OAA9B;;AAEA,QAAI,KAAKqD,MAAL,CAAYmD,uBAAZ,KAAwCzF,SAA5C,EAAuD;AACrD,WAAKmJ,0BAAL,CAAgC,KAAK7G,MAAL,CAAYmD,uBAA5C;AACD;;AAED,SAAK2D,gBAAL;;AAEA,QAAI,KAAKnK,OAAT,EAAkB;AAChB;AACD;;AAED,YAAQ,KAAKY,YAAb;AACE,WAAKxB,KAAK,CAACyB,MAAX;AACE,aAAKoD,IAAL,CAAU,IAAV;AACA;;AACF,WAAK7E,KAAK,CAACW,YAAX;AACER,QAAAA,0BAA0B,CAACkE,WAA3B,GAAyC2G,6BAAzC,CACE,IADF;AAGA;;AACF;AACE,aAAKhG,MAAL,CAAY,IAAZ;AACA;AAXJ;AAaD;;AAESiG,EAAAA,6BAA6B,CAACC,SAAD,EAA4B;AACjE,SAAK,MAAMjC,GAAX,IAAkB,KAAKhF,MAAvB,EAA+B;AAC7B,UAAIiH,SAAS,CAACC,OAAV,CAAkBlC,GAAlB,KAA0B,CAA9B,EAAiC;AAC/B,aAAKmC,2BAAL,GAAmC,IAAnC;AACD;AACF;AACF;;AAEOL,EAAAA,gBAAgB,GAAS;AAC/B,QAAI,CAAC,KAAK9G,MAAL,CAAYoH,OAAjB,EAA0B;AACxB;AACD;;AAED,QACE,KAAKpH,MAAL,CAAYoH,OAAZ,CAAoBC,IAApB,KAA6B3J,SAA7B,IACA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBE,KAApB,KAA8B5J,SAD9B,IAEA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBG,KAApB,KAA8B7J,SAHhC,EAIE;AACA,YAAM,IAAI8J,KAAJ,CACJ,qEADI,CAAN;AAGD;;AAED,QACE,KAAKxH,MAAL,CAAYoH,OAAZ,CAAoBG,KAApB,KAA8B7J,SAA9B,IACA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBC,IAApB,KAA6B3J,SAD7B,IAEA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBE,KAApB,KAA8B5J,SAHhC,EAIE;AACA,YAAM,IAAI8J,KAAJ,CACJ,8EADI,CAAN;AAGD;;AAED,QACE,KAAKxH,MAAL,CAAYoH,OAAZ,CAAoBK,MAApB,KAA+B/J,SAA/B,IACA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBM,GAApB,KAA4BhK,SAD5B,IAEA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBO,MAApB,KAA+BjK,SAHjC,EAIE;AACA,YAAM,IAAI8J,KAAJ,CACJ,sEADI,CAAN;AAGD;;AAED,QACE,KAAKxH,MAAL,CAAYoH,OAAZ,CAAoBK,MAApB,KAA+B/J,SAA/B,IACA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBM,GAApB,KAA4BhK,SAD5B,IAEA,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBO,MAApB,KAA+BjK,SAHjC,EAIE;AACA,YAAM,IAAI8J,KAAJ,CACJ,+EADI,CAAN;AAGD;AACF;;AAEO9G,EAAAA,YAAY,GAAY;AAC9B,QAAI,CAAC,KAAKV,MAAL,CAAYoH,OAAjB,EAA0B;AACxB,aAAO,IAAP;AACD;;AAED,UAAM;AAAEG,MAAAA,KAAF;AAASE,MAAAA;AAAT,QAAoB,KAAKhL,QAAL,CAAc6H,WAAd,EAA1B;AAEA,QAAI+C,IAAI,GAAG,CAAX;AACA,QAAIK,GAAG,GAAG,CAAV;AACA,QAAIJ,KAAa,GAAGC,KAApB;AACA,QAAII,MAAc,GAAGF,MAArB;;AAEA,QAAI,KAAKzH,MAAL,CAAYoH,OAAZ,CAAoBQ,UAApB,KAAmClK,SAAvC,EAAkD;AAChD2J,MAAAA,IAAI,IAAI,KAAKrH,MAAL,CAAYoH,OAAZ,CAAoBQ,UAA5B;AACAN,MAAAA,KAAK,IAAI,KAAKtH,MAAL,CAAYoH,OAAZ,CAAoBQ,UAA7B;AACD;;AAED,QAAI,KAAK5H,MAAL,CAAYoH,OAAZ,CAAoBS,QAApB,KAAiCnK,SAArC,EAAgD;AAC9CgK,MAAAA,GAAG,IAAI,KAAK1H,MAAL,CAAYoH,OAAZ,CAAoBS,QAA3B;AACAF,MAAAA,MAAM,IAAI,KAAK3H,MAAL,CAAYoH,OAAZ,CAAoBS,QAA9B;AACD;;AAED,QAAI,KAAK7H,MAAL,CAAYoH,OAAZ,CAAoBC,IAApB,KAA6B3J,SAAjC,EAA4C;AAC1C2J,MAAAA,IAAI,GAAG,CAAC,KAAKrH,MAAL,CAAYoH,OAAZ,CAAoBC,IAA5B;AACD;;AAED,QAAI,KAAKrH,MAAL,CAAYoH,OAAZ,CAAoBE,KAApB,KAA8B5J,SAAlC,EAA6C;AAC3C4J,MAAAA,KAAK,GAAGC,KAAK,GAAG,KAAKvH,MAAL,CAAYoH,OAAZ,CAAoBE,KAApC;AACD;;AAED,QAAI,KAAKtH,MAAL,CAAYoH,OAAZ,CAAoBM,GAApB,KAA4BhK,SAAhC,EAA2C;AACzCgK,MAAAA,GAAG,GAAG,CAAC,KAAK1H,MAAL,CAAYoH,OAAZ,CAAoBM,GAA3B;AACD;;AAED,QAAI,KAAK1H,MAAL,CAAYoH,OAAZ,CAAoBO,MAApB,KAA+BjK,SAAnC,EAA8C;AAC5CiK,MAAAA,MAAM,GAAGJ,KAAK,GAAG,KAAKvH,MAAL,CAAYoH,OAAZ,CAAoBO,MAArC;AACD;;AACD,QAAI,KAAK3H,MAAL,CAAYoH,OAAZ,CAAoBG,KAApB,KAA8B7J,SAAlC,EAA6C;AAC3C,UAAI,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBC,IAApB,KAA6B3J,SAAjC,EAA4C;AAC1C4J,QAAAA,KAAK,GAAGD,IAAI,GAAG,KAAKrH,MAAL,CAAYoH,OAAZ,CAAoBG,KAAnC;AACD,OAFD,MAEO,IAAI,KAAKvH,MAAL,CAAYoH,OAAZ,CAAoBE,KAApB,KAA8B5J,SAAlC,EAA6C;AAClD2J,QAAAA,IAAI,GAAGC,KAAK,GAAG,KAAKtH,MAAL,CAAYoH,OAAZ,CAAoBG,KAAnC;AACD;AACF;;AAED,QAAI,KAAKvH,MAAL,CAAYoH,OAAZ,CAAoBK,MAApB,KAA+B/J,SAAnC,EAA8C;AAC5C,UAAI,KAAKsC,MAAL,CAAYoH,OAAZ,CAAoBM,GAApB,KAA4BhK,SAAhC,EAA2C;AACzCiK,QAAAA,MAAM,GAAGD,GAAG,GAAG,KAAK1H,MAAL,CAAYoH,OAAZ,CAAoBK,MAAnC;AACD,OAFD,MAEO,IAAI,KAAKzH,MAAL,CAAYoH,OAAZ,CAAoBO,MAApB,KAA+BjK,SAAnC,EAA8C;AACnDgK,QAAAA,GAAG,GAAGC,MAAM,GAAG,KAAK3H,MAAL,CAAYoH,OAAZ,CAAoBK,MAAnC;AACD;AACF;;AAED,UAAMpD,IAAI,GAAG,KAAK5H,QAAL,CAAc6H,WAAd,EAAb;AACA,UAAM;AAAEc,MAAAA,CAAF;AAAKG,MAAAA;AAAL,QAAW,KAAK5F,OAAL,CAAamI,qBAAb,EAAjB;AACA,UAAMC,OAAe,GAAG3C,CAAC,GAAGf,IAAI,CAACiB,KAAjC;AACA,UAAM0C,OAAe,GAAGzC,CAAC,GAAGlB,IAAI,CAACmB,KAAjC;AAEA,WACEuC,OAAO,IAAIV,IAAX,IAAmBU,OAAO,IAAIT,KAA9B,IAAuCU,OAAO,IAAIN,GAAlD,IAAyDM,OAAO,IAAIL,MADtE;AAGD;;AAEMM,EAAAA,gBAAgB,CAACC,WAAD,EAAuC;AAC5D,WACE,CAACA,WAAD,IACC,CAAC,KAAKlI,MAAL,CAAYkI,WAAb,IAA4BA,WAAW,KAAK7L,WAAW,CAAC8L,IADzD,IAEC,KAAKnI,MAAL,CAAYkI,WAAZ,IAA2BA,WAAW,GAAG,KAAKlI,MAAL,CAAYkI,WAHxD;AAKD;;AAESE,EAAAA,WAAW,GAAS,CAAE;;AAEzBC,EAAAA,SAAS,GAAS;AACvB,SAAK5L,QAAL,CAAc6L,OAAd,CAAsB,KAAKtI,MAA3B;AACD,GAttBqE,CAwtBtE;AACA;AACA;;;AAEOuI,EAAAA,MAAM,GAAW;AACtB,WAAO,KAAKvE,UAAZ;AACD;;AAEMwE,EAAAA,MAAM,CAACC,GAAD,EAAoB;AAC/B,SAAKzE,UAAL,GAAkByE,GAAlB;AACD;;AAEMC,EAAAA,SAAS,GAAG;AACjB,WAAO,KAAK1I,MAAZ;AACD;;AAEM2I,EAAAA,WAAW,GAAqD;AACrE,WAAO,KAAKlM,QAAZ;AACD;;AAEMmM,EAAAA,UAAU,GAAmB;AAClC,WAAO,KAAKjJ,OAAZ;AACD;;AAEMkJ,EAAAA,oBAAoB,GAAa;AACtC,WAAO,KAAKlJ,OAAL,CAAakJ,oBAAb,EAAP;AACD;;AAEMC,EAAAA,QAAQ,GAAU;AACvB,WAAO,KAAKvL,YAAZ;AACD;;AAEMwL,EAAAA,SAAS,GAAY;AAC1B,WAAO,KAAKpM,OAAZ;AACD;;AAEOuD,EAAAA,UAAU,GAAY;AAC5B,WACE,KAAK3C,YAAL,KAAsBxB,KAAK,CAACwF,GAA5B,IACA,KAAKhE,YAAL,KAAsBxB,KAAK,CAAC+E,MAD5B,IAEA,KAAKvD,YAAL,KAAsBxB,KAAK,CAACiF,SAH9B;AAKD;;AAES6F,EAAAA,0BAA0B,CAACmC,YAAD,EAAwB;AAC1D,SAAK7F,uBAAL,GAA+B6F,YAA/B;AACD;;AAESC,EAAAA,0BAA0B,GAAY;AAC9C,WAAO,KAAK9F,uBAAZ;AACD;;AAEM+F,EAAAA,cAAc,GAAgB;AACnC,WAAO,KAAKpG,WAAZ;AACD;;AA9wBqE;;AAixBxE,SAASxF,oBAAT,CACE6L,MADF,EAKEvG,KALF,EAMQ;AACN,MAAI,CAACuG,MAAL,EAAa;AACX;AACD;;AAED,MAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChCA,IAAAA,MAAM,CAACvG,KAAD,CAAN;AACA;AACD;;AAED,MAAI,kBAAkBuG,MAAlB,IAA4B,OAAOA,MAAM,CAACC,YAAd,KAA+B,UAA/D,EAA2E;AACzE,UAAM9G,OAAO,GAAG6G,MAAM,CAACC,YAAP,EAAhB;;AACA9L,IAAAA,oBAAoB,CAACgF,OAAD,EAAUM,KAAV,CAApB;AACA;AACD;;AAED,MAAI,EAAE,kBAAkBuG,MAApB,CAAJ,EAAiC;AAC/B;AACD;;AAED,QAAM;AAAEE,IAAAA;AAAF,MAA0CF,MAAM,CAACG,YAAvD;;AACA,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcH,UAAd,CAAL,EAAgC;AAC9B;AACD;;AAED,OAAK,MAAM,CAACI,KAAD,EAAQ,CAACzE,GAAD,EAAMrD,KAAN,CAAR,CAAX,IAAoC0H,UAAU,CAACK,OAAX,EAApC,EAA0D;AACxD,QAAI,EAAE1E,GAAG,IAAIpC,KAAK,CAACnF,WAAf,CAAJ,EAAiC;AAC/B;AACD,KAHuD,CAKxD;;;AACA,UAAMkM,WAAW,GAAG/G,KAAK,CAACnF,WAAN,CAAkBuH,GAAlB,CAApB,CANwD,CAQxD;;AACA,QAAIrD,KAAJ,aAAIA,KAAJ,eAAIA,KAAK,CAAEiI,QAAX,EAAqB;AACnB;AACA;AACAjI,MAAAA,KAAK,CAACiI,QAAN,CAAeD,WAAf;AACD,KAJD,MAIO;AACL;AACAR,MAAAA,MAAM,CAACG,YAAP,CAAoBD,UAApB,CAA+BI,KAA/B,IAAwC,CAACzE,GAAD,EAAM2E,WAAN,CAAxC;AACD;AACF;;AAED;AACD", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\nimport { State } from '../../State';\nimport {\n  Config,\n  AdaptedEvent,\n  PropsRef,\n  ResultEvent,\n  PointerData,\n  ResultTouchEvent,\n  TouchEventType,\n  EventTypes,\n} from '../interfaces';\nimport EventManager from '../tools/EventManager';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport InteractionManager from '../tools/InteractionManager';\nimport PointerTracker, { TrackerElement } from '../tools/PointerTracker';\nimport IGestureHandler from './IGestureHandler';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport { PointerType } from '../../PointerType';\nimport { GestureHandlerDelegate } from '../tools/GestureHandlerDelegate';\n\nexport default abstract class GestureHandler implements IGestureHandler {\n  private lastSentState: State | null = null;\n  protected currentState: State = State.UNDETERMINED;\n\n  protected shouldCancelWhenOutside = false;\n  protected hasCustomActivationCriteria = false;\n  protected enabled = false;\n\n  private viewRef!: number;\n  private propsRef!: React.RefObject<unknown>;\n  private handlerTag!: number;\n  protected config: Config = { enabled: false };\n\n  protected tracker: PointerTracker = new PointerTracker();\n\n  // Orchestrator properties\n  protected activationIndex = 0;\n  protected awaiting = false;\n  protected active = false;\n  protected shouldResetProgress = false;\n  protected pointerType: PointerType = PointerType.MOUSE;\n\n  protected delegate: GestureHandlerDelegate<unknown, IGestureHandler>;\n\n  public constructor(\n    delegate: GestureHandlerDelegate<unknown, IGestureHandler>\n  ) {\n    this.delegate = delegate;\n  }\n\n  //\n  // Initializing handler\n  //\n\n  protected init(viewRef: number, propsRef: React.RefObject<unknown>) {\n    this.propsRef = propsRef;\n    this.viewRef = viewRef;\n\n    this.currentState = State.UNDETERMINED;\n\n    this.delegate.init(viewRef, this);\n  }\n\n  public attachEventManager(manager: EventManager<unknown>): void {\n    manager.setOnPointerDown(this.onPointerDown.bind(this));\n    manager.setOnPointerAdd(this.onPointerAdd.bind(this));\n    manager.setOnPointerUp(this.onPointerUp.bind(this));\n    manager.setOnPointerRemove(this.onPointerRemove.bind(this));\n    manager.setOnPointerMove(this.onPointerMove.bind(this));\n    manager.setOnPointerEnter(this.onPointerEnter.bind(this));\n    manager.setOnPointerLeave(this.onPointerLeave.bind(this));\n    manager.setOnPointerCancel(this.onPointerCancel.bind(this));\n    manager.setOnPointerOutOfBounds(this.onPointerOutOfBounds.bind(this));\n    manager.setOnPointerMoveOver(this.onPointerMoveOver.bind(this));\n    manager.setOnPointerMoveOut(this.onPointerMoveOut.bind(this));\n\n    manager.registerListeners();\n  }\n\n  //\n  // Resetting handler\n  //\n\n  protected onCancel(): void {}\n  protected onReset(): void {}\n  protected resetProgress(): void {}\n\n  public reset(): void {\n    this.tracker.resetTracker();\n    this.onReset();\n    this.resetProgress();\n    this.delegate.reset();\n    this.currentState = State.UNDETERMINED;\n  }\n\n  //\n  // State logic\n  //\n\n  public moveToState(newState: State, sendIfDisabled?: boolean) {\n    if (this.currentState === newState) {\n      return;\n    }\n\n    const oldState = this.currentState;\n    this.currentState = newState;\n\n    if (\n      this.tracker.getTrackedPointersCount() > 0 &&\n      this.config.needsPointerData &&\n      this.isFinished()\n    ) {\n      this.cancelTouches();\n    }\n\n    GestureHandlerOrchestrator.getInstance().onHandlerStateChange(\n      this,\n      newState,\n      oldState,\n      sendIfDisabled\n    );\n\n    this.onStateChange(newState, oldState);\n\n    if (!this.enabled && this.isFinished()) {\n      this.currentState = State.UNDETERMINED;\n    }\n  }\n\n  protected onStateChange(_newState: State, _oldState: State): void {}\n\n  public begin(): void {\n    if (!this.checkHitSlop()) {\n      return;\n    }\n\n    if (this.currentState === State.UNDETERMINED) {\n      this.moveToState(State.BEGAN);\n    }\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send fail event\n   */\n  public fail(sendIfDisabled?: boolean): void {\n    if (\n      this.currentState === State.ACTIVE ||\n      this.currentState === State.BEGAN\n    ) {\n      // Here the order of calling the delegate and moveToState is important.\n      // At this point we can use currentState as previuos state, because immediately after changing cursor we call moveToState method.\n      this.delegate.onFail();\n\n      this.moveToState(State.FAILED, sendIfDisabled);\n    }\n\n    this.resetProgress();\n  }\n\n  /**\n   * @param {boolean} sendIfDisabled - Used when handler becomes disabled. With this flag orchestrator will be forced to send cancel event\n   */\n  public cancel(sendIfDisabled?: boolean): void {\n    if (\n      this.currentState === State.ACTIVE ||\n      this.currentState === State.UNDETERMINED ||\n      this.currentState === State.BEGAN\n    ) {\n      this.onCancel();\n\n      // Same as above - order matters\n      this.delegate.onCancel();\n\n      this.moveToState(State.CANCELLED, sendIfDisabled);\n    }\n  }\n\n  public activate(force = false) {\n    if (\n      (this.config.manualActivation !== true || force) &&\n      (this.currentState === State.UNDETERMINED ||\n        this.currentState === State.BEGAN)\n    ) {\n      this.delegate.onActivate();\n      this.moveToState(State.ACTIVE);\n    }\n  }\n\n  public end() {\n    if (\n      this.currentState === State.BEGAN ||\n      this.currentState === State.ACTIVE\n    ) {\n      // Same as above - order matters\n      this.delegate.onEnd();\n\n      this.moveToState(State.END);\n    }\n\n    this.resetProgress();\n  }\n\n  //\n  // Methods for orchestrator\n  //\n\n  public isAwaiting(): boolean {\n    return this.awaiting;\n  }\n  public setAwaiting(value: boolean): void {\n    this.awaiting = value;\n  }\n\n  public isActive(): boolean {\n    return this.active;\n  }\n  public setActive(value: boolean): void {\n    this.active = value;\n  }\n\n  public getShouldResetProgress(): boolean {\n    return this.shouldResetProgress;\n  }\n  public setShouldResetProgress(value: boolean): void {\n    this.shouldResetProgress = value;\n  }\n\n  public getActivationIndex(): number {\n    return this.activationIndex;\n  }\n  public setActivationIndex(value: number): void {\n    this.activationIndex = value;\n  }\n\n  public shouldWaitForHandlerFailure(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldWaitForHandlerFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRequireToWaitForFailure(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldRequireHandlerToWaitForFailure(\n      this,\n      handler\n    );\n  }\n\n  public shouldRecognizeSimultaneously(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return true;\n    }\n\n    return InteractionManager.getInstance().shouldRecognizeSimultaneously(\n      this,\n      handler\n    );\n  }\n\n  public shouldBeCancelledByOther(handler: IGestureHandler): boolean {\n    if (handler === this) {\n      return false;\n    }\n\n    return InteractionManager.getInstance().shouldHandlerBeCancelledBy(\n      this,\n      handler\n    );\n  }\n\n  //\n  // Event actions\n  //\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.getInstance().recordHandlerIfNotPresent(this);\n    this.pointerType = event.pointerType;\n\n    if (this.pointerType === PointerType.TOUCH) {\n      GestureHandlerOrchestrator.getInstance().cancelMouseAndPenGestures(this);\n    }\n\n    // TODO: Bring back touch events along with introducing `handleDown` method that will handle handler specific stuff\n  }\n  // Adding another pointer to existing ones\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerUp(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  // Removing pointer, when there is more than one pointers\n  protected onPointerRemove(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(false, event);\n  }\n  protected onPointerLeave(event: AdaptedEvent): void {\n    if (this.shouldCancelWhenOutside) {\n      switch (this.currentState) {\n        case State.ACTIVE:\n          this.cancel();\n          break;\n        case State.BEGAN:\n          this.fail();\n          break;\n      }\n      return;\n    }\n\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerEnter(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n  }\n  protected onPointerCancel(event: AdaptedEvent): void {\n    this.tryToSendTouchEvent(event);\n\n    this.cancel();\n    this.reset();\n  }\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    this.tryToSendMoveEvent(true, event);\n  }\n  protected onPointerMoveOver(_event: AdaptedEvent): void {\n    // Used only by hover gesture handler atm\n  }\n  protected onPointerMoveOut(_event: AdaptedEvent): void {\n    // Used only by hover gesture handler atm\n  }\n  private tryToSendMoveEvent(out: boolean, event: AdaptedEvent): void {\n    if ((out && this.shouldCancelWhenOutside) || !this.enabled) {\n      return;\n    }\n\n    if (this.active) {\n      this.sendEvent(this.currentState, this.currentState);\n    }\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected tryToSendTouchEvent(event: AdaptedEvent): void {\n    if (this.config.needsPointerData) {\n      this.sendTouchEvent(event);\n    }\n  }\n\n  public sendTouchEvent(event: AdaptedEvent): void {\n    if (!this.enabled) {\n      return;\n    }\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    const touchEvent: ResultTouchEvent | undefined =\n      this.transformTouchEvent(event);\n\n    if (touchEvent) {\n      invokeNullableMethod(onGestureHandlerEvent, touchEvent);\n    }\n  }\n\n  //\n  // Events Sending\n  //\n\n  public sendEvent = (newState: State, oldState: State): void => {\n    const { onGestureHandlerEvent, onGestureHandlerStateChange }: PropsRef =\n      this.propsRef.current as PropsRef;\n\n    const resultEvent: ResultEvent = this.transformEventData(\n      newState,\n      oldState\n    );\n\n    // In the new API oldState field has to be undefined, unless we send event state changed\n    // Here the order is flipped to avoid workarounds such as making backup of the state and setting it to undefined first, then changing it back\n    // Flipping order with setting oldState to undefined solves issue, when events were being sent twice instead of once\n    // However, this may cause trouble in the future (but for now we don't know that)\n\n    if (this.lastSentState !== newState) {\n      this.lastSentState = newState;\n      invokeNullableMethod(onGestureHandlerStateChange, resultEvent);\n    }\n    if (this.currentState === State.ACTIVE) {\n      resultEvent.nativeEvent.oldState = undefined;\n      invokeNullableMethod(onGestureHandlerEvent, resultEvent);\n    }\n  };\n\n  private transformEventData(newState: State, oldState: State): ResultEvent {\n    return {\n      nativeEvent: {\n        numberOfPointers: this.tracker.getTrackedPointersCount(),\n        state: newState,\n        pointerInside: this.delegate.isPointerInBounds(\n          this.tracker.getAbsoluteCoordsAverage()\n        ),\n        ...this.transformNativeEvent(),\n        handlerTag: this.handlerTag,\n        target: this.viewRef,\n        oldState: newState !== oldState ? oldState : undefined,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private transformTouchEvent(\n    event: AdaptedEvent\n  ): ResultTouchEvent | undefined {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.getData();\n\n    // This if handles edge case where all pointers have been cancelled\n    // When pointercancel is triggered, reset method is called. This means that tracker will be reset after first pointer being cancelled\n    // The problem is, that handler will receive another pointercancel event from the rest of the pointers\n    // To avoid crashing, we don't send event if tracker tracks no pointers, i.e. has been reset\n    if (trackerData.size === 0 || !trackerData.has(event.pointerId)) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n    });\n\n    // Each pointer sends its own event, so we want changed touches to contain only the pointer that has changed.\n    // However, if the event is cancel, we want to cancel all pointers to avoid crashes\n    if (event.eventType !== EventTypes.CANCEL) {\n      changed.push({\n        id: this.tracker.getMappedTouchEventId(event.pointerId),\n        x: event.x - rect.pageX,\n        y: event.y - rect.pageY,\n        absoluteX: event.x,\n        absoluteY: event.y,\n      });\n    } else {\n      trackerData.forEach((element: TrackerElement, key: number): void => {\n        const id: number = this.tracker.getMappedTouchEventId(key);\n\n        changed.push({\n          id: id,\n          x: element.abosoluteCoords.x - rect.pageX,\n          y: element.abosoluteCoords.y - rect.pageY,\n          absoluteX: element.abosoluteCoords.x,\n          absoluteY: element.abosoluteCoords.y,\n        });\n      });\n    }\n\n    let eventType: TouchEventType = TouchEventType.UNDETERMINED;\n\n    switch (event.eventType) {\n      case EventTypes.DOWN:\n      case EventTypes.ADDITIONAL_POINTER_DOWN:\n        eventType = TouchEventType.DOWN;\n        break;\n      case EventTypes.UP:\n      case EventTypes.ADDITIONAL_POINTER_UP:\n        eventType = TouchEventType.UP;\n        break;\n      case EventTypes.MOVE:\n        eventType = TouchEventType.MOVE;\n        break;\n      case EventTypes.CANCEL:\n        eventType = TouchEventType.CANCELLED;\n        break;\n    }\n\n    // Here, when we receive up event, we want to decrease number of touches\n    // That's because we want handler to send information that there's one pointer less\n    // However, we still want this pointer to be present in allTouches array, so that its data can be accessed\n    let numberOfTouches: number = all.length;\n\n    if (\n      event.eventType === EventTypes.UP ||\n      event.eventType === EventTypes.ADDITIONAL_POINTER_UP\n    ) {\n      --numberOfTouches;\n    }\n\n    return {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.currentState,\n        eventType: eventType,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: numberOfTouches,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  private cancelTouches(): void {\n    const rect = this.delegate.measureView();\n\n    const all: PointerData[] = [];\n    const changed: PointerData[] = [];\n\n    const trackerData = this.tracker.getData();\n\n    if (trackerData.size === 0) {\n      return;\n    }\n\n    trackerData.forEach((element: TrackerElement, key: number): void => {\n      const id: number = this.tracker.getMappedTouchEventId(key);\n\n      all.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n\n      changed.push({\n        id: id,\n        x: element.abosoluteCoords.x - rect.pageX,\n        y: element.abosoluteCoords.y - rect.pageY,\n        absoluteX: element.abosoluteCoords.x,\n        absoluteY: element.abosoluteCoords.y,\n      });\n    });\n\n    const cancelEvent: ResultTouchEvent = {\n      nativeEvent: {\n        handlerTag: this.handlerTag,\n        state: this.currentState,\n        eventType: TouchEventType.CANCELLED,\n        changedTouches: changed,\n        allTouches: all,\n        numberOfTouches: all.length,\n        pointerType: this.pointerType,\n      },\n      timeStamp: Date.now(),\n    };\n\n    const { onGestureHandlerEvent }: PropsRef = this.propsRef\n      .current as PropsRef;\n\n    invokeNullableMethod(onGestureHandlerEvent, cancelEvent);\n  }\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    // Those properties are shared by most handlers and if not this method will be overriden\n    const lastCoords = this.tracker.getAbsoluteCoordsAverage();\n    const lastRelativeCoords = this.tracker.getRelativeCoordsAverage();\n\n    return {\n      x: lastRelativeCoords.x,\n      y: lastRelativeCoords.y,\n      absoluteX: lastCoords.x,\n      absoluteY: lastCoords.y,\n    };\n  }\n\n  //\n  // Handling config\n  //\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    this.config = { enabled: enabled, ...props };\n    this.enabled = enabled;\n\n    this.delegate.onEnabledChange(enabled);\n\n    if (this.config.shouldCancelWhenOutside !== undefined) {\n      this.setShouldCancelWhenOutside(this.config.shouldCancelWhenOutside);\n    }\n\n    this.validateHitSlops();\n\n    if (this.enabled) {\n      return;\n    }\n\n    switch (this.currentState) {\n      case State.ACTIVE:\n        this.fail(true);\n        break;\n      case State.UNDETERMINED:\n        GestureHandlerOrchestrator.getInstance().removeHandlerFromOrchestrator(\n          this\n        );\n        break;\n      default:\n        this.cancel(true);\n        break;\n    }\n  }\n\n  protected checkCustomActivationCriteria(criterias: string[]): void {\n    for (const key in this.config) {\n      if (criterias.indexOf(key) >= 0) {\n        this.hasCustomActivationCriteria = true;\n      }\n    }\n  }\n\n  private validateHitSlops(): void {\n    if (!this.config.hitSlop) {\n      return;\n    }\n\n    if (\n      this.config.hitSlop.left !== undefined &&\n      this.config.hitSlop.right !== undefined &&\n      this.config.hitSlop.width !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define left, right and width at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.width !== undefined &&\n      this.config.hitSlop.left === undefined &&\n      this.config.hitSlop.right === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When width is defined, either left or right has to be defined'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top !== undefined &&\n      this.config.hitSlop.bottom !== undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: Cannot define top, bottom and height at the same time'\n      );\n    }\n\n    if (\n      this.config.hitSlop.height !== undefined &&\n      this.config.hitSlop.top === undefined &&\n      this.config.hitSlop.bottom === undefined\n    ) {\n      throw new Error(\n        'HitSlop Error: When height is defined, either top or bottom has to be defined'\n      );\n    }\n  }\n\n  private checkHitSlop(): boolean {\n    if (!this.config.hitSlop) {\n      return true;\n    }\n\n    const { width, height } = this.delegate.measureView();\n\n    let left = 0;\n    let top = 0;\n    let right: number = width;\n    let bottom: number = height;\n\n    if (this.config.hitSlop.horizontal !== undefined) {\n      left -= this.config.hitSlop.horizontal;\n      right += this.config.hitSlop.horizontal;\n    }\n\n    if (this.config.hitSlop.vertical !== undefined) {\n      top -= this.config.hitSlop.vertical;\n      bottom += this.config.hitSlop.vertical;\n    }\n\n    if (this.config.hitSlop.left !== undefined) {\n      left = -this.config.hitSlop.left;\n    }\n\n    if (this.config.hitSlop.right !== undefined) {\n      right = width + this.config.hitSlop.right;\n    }\n\n    if (this.config.hitSlop.top !== undefined) {\n      top = -this.config.hitSlop.top;\n    }\n\n    if (this.config.hitSlop.bottom !== undefined) {\n      bottom = width + this.config.hitSlop.bottom;\n    }\n    if (this.config.hitSlop.width !== undefined) {\n      if (this.config.hitSlop.left !== undefined) {\n        right = left + this.config.hitSlop.width;\n      } else if (this.config.hitSlop.right !== undefined) {\n        left = right - this.config.hitSlop.width;\n      }\n    }\n\n    if (this.config.hitSlop.height !== undefined) {\n      if (this.config.hitSlop.top !== undefined) {\n        bottom = top + this.config.hitSlop.height;\n      } else if (this.config.hitSlop.bottom !== undefined) {\n        top = bottom - this.config.hitSlop.height;\n      }\n    }\n\n    const rect = this.delegate.measureView();\n    const { x, y } = this.tracker.getLastAbsoluteCoords();\n    const offsetX: number = x - rect.pageX;\n    const offsetY: number = y - rect.pageY;\n\n    return (\n      offsetX >= left && offsetX <= right && offsetY >= top && offsetY <= bottom\n    );\n  }\n\n  public isButtonInConfig(mouseButton: MouseButton | undefined) {\n    return (\n      !mouseButton ||\n      (!this.config.mouseButton && mouseButton === MouseButton.LEFT) ||\n      (this.config.mouseButton && mouseButton & this.config.mouseButton)\n    );\n  }\n\n  protected resetConfig(): void {}\n\n  public onDestroy(): void {\n    this.delegate.destroy(this.config);\n  }\n\n  //\n  // Getters and setters\n  //\n\n  public getTag(): number {\n    return this.handlerTag;\n  }\n\n  public setTag(tag: number): void {\n    this.handlerTag = tag;\n  }\n\n  public getConfig() {\n    return this.config;\n  }\n\n  public getDelegate(): GestureHandlerDelegate<unknown, IGestureHandler> {\n    return this.delegate;\n  }\n\n  public getTracker(): PointerTracker {\n    return this.tracker;\n  }\n\n  public getTrackedPointersID(): number[] {\n    return this.tracker.getTrackedPointersID();\n  }\n\n  public getState(): State {\n    return this.currentState;\n  }\n\n  public isEnabled(): boolean {\n    return this.enabled;\n  }\n\n  private isFinished(): boolean {\n    return (\n      this.currentState === State.END ||\n      this.currentState === State.FAILED ||\n      this.currentState === State.CANCELLED\n    );\n  }\n\n  protected setShouldCancelWhenOutside(shouldCancel: boolean) {\n    this.shouldCancelWhenOutside = shouldCancel;\n  }\n\n  protected getShouldCancelWhenOutside(): boolean {\n    return this.shouldCancelWhenOutside;\n  }\n\n  public getPointerType(): PointerType {\n    return this.pointerType;\n  }\n}\n\nfunction invokeNullableMethod(\n  method:\n    | ((event: ResultEvent | ResultTouchEvent) => void)\n    | { __getHandler: () => (event: ResultEvent | ResultTouchEvent) => void }\n    | { __nodeConfig: { argMapping: unknown[] } },\n  event: ResultEvent | ResultTouchEvent\n): void {\n  if (!method) {\n    return;\n  }\n\n  if (typeof method === 'function') {\n    method(event);\n    return;\n  }\n\n  if ('__getHandler' in method && typeof method.__getHandler === 'function') {\n    const handler = method.__getHandler();\n    invokeNullableMethod(handler, event);\n    return;\n  }\n\n  if (!('__nodeConfig' in method)) {\n    return;\n  }\n\n  const { argMapping }: { argMapping: unknown } = method.__nodeConfig;\n  if (!Array.isArray(argMapping)) {\n    return;\n  }\n\n  for (const [index, [key, value]] of argMapping.entries()) {\n    if (!(key in event.nativeEvent)) {\n      continue;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    const nativeValue = event.nativeEvent[key];\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (value?.setValue) {\n      // Reanimated API\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call\n      value.setValue(nativeValue);\n    } else {\n      // RN Animated API\n      method.__nodeConfig.argMapping[index] = [key, nativeValue];\n    }\n  }\n\n  return;\n}\n"]}