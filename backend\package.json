{"name": "aquatrack-scanner-api", "version": "1.0.0", "description": "API Backend pour le scanner QR d'AquaTrack", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["scanner", "qr-code", "aquatrack", "postgresql", "api"], "author": "AquaTrack Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "pg": "^8.11.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}