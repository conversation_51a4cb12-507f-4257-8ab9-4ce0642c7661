import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/components/data_grid/data_grid.js";import*as s from"../../ui/legacy/components/source_frame/source_frame.js";import*as i from"../../ui/legacy/legacy.js";import*as a from"../../ui/visual_logging/visual_logging.js";import*as r from"../../core/sdk/sdk.js";import*as n from"../../core/common/common.js";import*as o from"../../core/host/host.js";import*as l from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as d from"../../ui/legacy/theme_support/theme_support.js";import*as h from"../../core/platform/platform.js";import*as c from"../../ui/components/icon_button/icon_button.js";const m=new CSSStyleSheet;m.replaceSync(".no-border-top-datagrid > .data-grid{border-top:0}.event-display-table-contents-table-container > .widget > .data-grid{height:100%}.data-grid .event-display-table-basic-text-table-entry{line-height:26px}.event-display-table-contents-json-wrapper > .json-view{overflow:visible}\n/*# sourceURL=eventDisplayTable.css */\n");const p={timestamp:"Timestamp",eventName:"Event name",value:"Value",eventDisplay:"Event display"},u=e.i18n.registerUIStrings("panels/media/EventDisplayTable.ts",p),g=e.i18n.getLocalizedString.bind(void 0,u);class v extends t.DataGrid.DataGridNode{expandableElement;constructor(e){super(e,!1),this.expandableElement=null}createCell(e){const t=this.createTD(e),a=this.data[e];if("value"===e){const e=t.createChild("div","event-display-table-contents-json-wrapper");this.expandableElement=new s.JSONView.JSONView(new s.JSONView.ParsedJSON(a,"",""),!0),this.expandableElement.markAsRoot(),this.expandableElement.show(e)}else t.classList.add("event-display-table-basic-text-table-entry"),i.UIUtils.createTextChild(t,a);return t}}class y extends i.Widget.VBox{dataGrid;firstEventTime;constructor(){super(),this.element.setAttribute("jslog",`${a.pane("events")}`),this.contentElement.classList.add("event-display-table-contents-table-container"),this.dataGrid=this.createDataGrid([{id:"display-timestamp",title:g(p.timestamp),weight:1,sortable:!1},{id:"event",title:g(p.eventName),weight:2,sortable:!1},{id:"value",title:g(p.value),weight:7,sortable:!1}]),this.firstEventTime=0,this.dataGrid.setStriped(!0),this.dataGrid.asWidget().show(this.contentElement)}createDataGrid(e){const s=[];for(const t of e)s.push(y.convertToGridDescriptor(t));const i=new t.DataGrid.DataGridImpl({displayName:g(p.eventDisplay),columns:s,deleteCallback:void 0,editCallback:void 0,refreshCallback:void 0});return i.asWidget().contentElement.classList.add("no-border-top-datagrid"),i}onEvent(e){0===this.firstEventTime&&"number"==typeof e.timestamp&&(this.firstEventTime=e.timestamp);const t=(e=this.subtractFirstEventTime(e)).value;try{const s=JSON.parse(t);e.event=s.event,delete s.event,e.value=s;const i=new v(e),a=this.dataGrid.scrollContainer,r=a.scrollTop===a.scrollHeight-a.offsetHeight;this.dataGrid.rootNode().appendChild(i),r&&(a.scrollTop=a.scrollHeight)}catch(e){}}subtractFirstEventTime(e){return"number"==typeof e.timestamp&&(e.displayTimestamp=(e.timestamp-this.firstEventTime).toFixed(3)),e}static convertToGridDescriptor(e){return{id:e.id,title:e.title,sortable:e.sortable,weight:e.weight||0,sort:t.DataGrid.Order.Ascending}}wasShown(){super.wasShown(),this.registerCSSFiles([m])}}var b=Object.freeze({__proto__:null,EventNode:v,PlayerEventsView:y});class f extends r.SDKModel.SDKModel{enabled;agent;constructor(e){super(e),this.enabled=!1,this.agent=e.mediaAgent(),e.registerMediaDispatcher(this)}async resumeModel(){if(!this.enabled)return Promise.resolve();await this.agent.invoke_enable()}ensureEnabled(){this.agent.invoke_enable(),this.enabled=!0}playerPropertiesChanged(e){this.dispatchEventToListeners("PlayerPropertiesChanged",e)}playerEventsAdded(e){this.dispatchEventToListeners("PlayerEventsAdded",e)}playerMessagesLogged(e){this.dispatchEventToListeners("PlayerMessagesLogged",e)}playerErrorsRaised(e){this.dispatchEventToListeners("PlayerErrorsRaised",e)}playersCreated({players:e}){this.dispatchEventToListeners("PlayersCreated",e)}}r.SDKModel.SDKModel.register(f,{capabilities:262144,autostart:!1});var w=Object.freeze({__proto__:null,MediaModel:f});function E(e,t){const s=Math.pow(10,3-t),i=Math.pow(10,Math.max(0,t));return Math.round(e/s)/i+" s"}class T{minInternal;maxInternal;lowInternal;highInternal;maxRange;minRange;constructor(e,t,s,i){this.minInternal=e,this.maxInternal=t,this.lowInternal=this.minInternal,this.highInternal=this.maxInternal,this.maxRange=s,this.minRange=i}get low(){return this.lowInternal}get high(){return this.highInternal}get min(){return this.minInternal}get max(){return this.maxInternal}get range(){return this.highInternal-this.lowInternal}reassertBounds(){let e=!0;for(;e;){if(e=!1,this.range<this.minRange){e=!0;const t=(this.minRange-this.range)/2;this.highInternal+=t,this.lowInternal-=t}this.lowInternal<this.minInternal&&(e=!0,this.lowInternal=this.minInternal),this.highInternal>this.maxInternal&&(e=!0,this.highInternal=this.maxInternal)}}zoomOut(e,t){const s=this.highInternal-this.lowInternal,i=s*Math.pow(1.1,e)-s,a=i*t,r=i-a;this.lowInternal-=a,this.highInternal+=r,this.reassertBounds()}zoomIn(e,t){const s=this.highInternal-this.lowInternal;if(this.range<=this.minRange)return;const i=s-s/Math.pow(1.1,e),a=i*t,r=i-a;this.lowInternal+=a,this.highInternal-=r,this.reassertBounds()}addMax(e){const t=this.highInternal-this.lowInternal,s=this.highInternal===this.maxInternal,i=this.lowInternal===this.minInternal||t>=this.maxRange;this.maxInternal+=e,s&&i&&(this.highInternal=this.maxInternal),this.reassertBounds()}pushMaxAtLeastTo(e){return this.maxInternal<e&&(this.addMax(e-this.maxInternal),!0)}}var x=Object.freeze({__proto__:null,formatMillisecondsToSeconds:E,Bounds:T});const P="11px "+o.Platform.fontFamily();function C(){return d.ThemeSupport.instance().getComputedValue("--sys-color-on-surface")}const S=["#ffba08","#faa307","#f48c06","#e85d04","#dc2f02","#d00000","#9d0208"],k=["#7400b8","#6930c3","#5e60ce","#5390d9","#4ea8de","#48bfe3","#56cfe1","#64dfdf"];function I(e){const t=n.Color.parse(e)?.as("hsl");return t&&t.l<.5?"#eee":"#444"}class M{timelineData;setLive;setComplete;updateMaxTime;selfIndex;liveInternal;title;colorInternal;fontColorInternal;hoverData;constructor(e,t,s={color:void 0,duration:void 0,hoverData:{},level:0,name:"",startTime:0}){this.timelineData=e,this.setLive=t.setLive,this.setComplete=t.setComplete,this.updateMaxTime=t.updateMaxTime,this.selfIndex=this.timelineData.entryLevels.length,this.liveInternal=!1;const i=void 0===s.duration?0:s.duration;this.timelineData.entryLevels.push(s.level||0),this.timelineData.entryStartTimes.push(s.startTime||0),this.timelineData.entryTotalTimes.push(i),-1===i&&(this.endTime=-1),this.title=s.name||"",this.colorInternal=s.color||S[0],this.fontColorInternal=I(this.colorInternal),this.hoverData=s.hoverData||{}}decorate(e){e.createChild("span").textContent=`Name: ${this.title}`,e.createChild("br");const t=E(this.startTime,2);if(this.liveInternal)e.createChild("span").textContent=`Duration: ${t} - LIVE!`;else if(isNaN(this.duration))e.createChild("span").textContent=`Time: ${t}`;else{const s=E(this.duration+this.startTime,2);e.createChild("span").textContent=`Duration: ${t} - ${s}`}}set endTime(e){if(-1===e)this.timelineData.entryTotalTimes[this.selfIndex]=this.setLive(this.selfIndex),this.liveInternal=!0;else{this.liveInternal=!1;const t=e-this.timelineData.entryStartTimes[this.selfIndex];this.timelineData.entryTotalTimes[this.selfIndex]=t,this.setComplete(this.selfIndex),this.updateMaxTime(e)}}get id(){return this.selfIndex}set level(e){this.timelineData.entryLevels[this.selfIndex]=e}set color(e){this.colorInternal=e,this.fontColorInternal=I(this.colorInternal)}get color(){return this.colorInternal}get fontColor(){return this.fontColorInternal}get startTime(){return this.timelineData.entryStartTimes[this.selfIndex]}get duration(){return this.timelineData.entryTotalTimes[this.selfIndex]}get live(){return this.liveInternal}}class D extends i.Widget.VBox{intervalTimer;lastTimestamp;canTickInternal;ticking;isShown;bounds;dataProvider;delegate;chartGroupExpansionSetting;chart;stoppedPermanently;constructor(){super(),this.intervalTimer=0,this.lastTimestamp=0,this.canTickInternal=!0,this.ticking=!1,this.isShown=!1,this.bounds=new T(0,1e3,3e4,1e3),this.dataProvider=new F(this.bounds,this.updateMaxTime.bind(this)),this.delegate=new L,this.chartGroupExpansionSetting=n.Settings.Settings.instance().createSetting("media-flame-chart-group-expansion",{}),this.chart=new l.FlameChart.FlameChart(this.dataProvider,this.delegate,this.chartGroupExpansionSetting),this.chart.disableRangeSelection(),this.chart.bindCanvasEvent("wheel",(e=>{this.onScroll(e)})),this.chart.show(this.contentElement)}addMarker(e){e.duration=NaN,this.startEvent(e)}startEvent(e){void 0===e.duration&&(e.duration=-1);const t=e.startTime||0,s=this.dataProvider.startEvent(e);return this.updateMaxTime(t),s}addGroup(e,t){this.dataProvider.addGroup(e,t)}updateMaxTime(e){this.bounds.pushMaxAtLeastTo(e)&&this.updateRender()}onScroll(e){const t=Math.round(e.deltaY/50),s=e.offsetX/e.srcElement.clientWidth;t>0?this.bounds.zoomOut(t,s):this.bounds.zoomIn(-t,s),this.updateRender()}willHide(){this.isShown=!1,this.ticking&&this.stop()}wasShown(){this.isShown=!0,this.canTickInternal&&!this.ticking&&this.start()}set canTick(e){this.canTickInternal=e,this.ticking&&!e&&this.stop(),!this.ticking&&this.isShown&&e&&this.start()}start(){0===this.lastTimestamp&&(this.lastTimestamp=Date.now()),0!==this.intervalTimer||this.stoppedPermanently||(this.intervalTimer=window.setInterval(this.updateRender.bind(this),16),this.ticking=!0)}stop(e=!1){window.clearInterval(this.intervalTimer),this.intervalTimer=0,e&&(this.stoppedPermanently=!0),this.ticking=!1}updateRender(){if(this.ticking){const e=Date.now(),t=e-this.lastTimestamp;this.lastTimestamp=e,this.bounds.addMax(t)}this.dataProvider.updateMaxTime(this.bounds),this.chart.setWindowTimes(this.bounds.low,this.bounds.high,!0),this.chart.scheduleUpdate()}}class L{constructor(){}windowChanged(e,t,s){}updateRangeSelection(e,t){}updateSelectedGroup(e,t){}}class F{updateMaxTimeHandle;bounds;liveEvents;eventMap;timelineDataInternal;maxLevel;constructor(e,t){this.updateMaxTimeHandle=t,this.bounds=e,this.liveEvents=new Set,this.eventMap=new Map,this.timelineDataInternal=l.FlameChart.FlameChartTimelineData.createEmpty(),this.maxLevel=0}hasTrackConfigurationMode(){return!1}addGroup(e,t){if(this.timelineDataInternal.groups){const t={name:e,startLevel:this.maxLevel,expanded:!0,selectable:!1,style:{height:20,padding:2,collapsible:!1,font:P,color:C(),backgroundColor:"rgba(100 0 0 / 10%)",nestingLevel:0,itemsHeight:20,shareHeaderLine:!1,useFirstLineForOverview:!1,useDecoratorsForOverview:!1},track:null};this.timelineDataInternal.groups.push(t),d.ThemeSupport.instance().addEventListener(d.ThemeChangeEvent.eventName,(()=>{t.style.color=C()}))}this.maxLevel+=t}startEvent(e){if(e.level=e.level||0,e.level>this.maxLevel)throw`level ${e.level} is above the maximum allowed of ${this.maxLevel}`;const t=new M(this.timelineDataInternal,{setLive:this.setLive.bind(this),setComplete:this.setComplete.bind(this),updateMaxTime:this.updateMaxTimeHandle},e);return this.eventMap.set(t.id,t),t}setLive(e){return this.liveEvents.add(e),this.bounds.max}setComplete(e){this.liveEvents.delete(e)}updateMaxTime(e){this.bounds=e;for(const e of this.liveEvents.entries())this.eventMap.get(e[0]).endTime=-1}maxStackDepth(){return this.maxLevel+1}timelineData(){return this.timelineDataInternal}minimumBoundary(){return this.bounds.low}totalTime(){return this.bounds.high}entryColor(e){return this.eventMap.get(e).color}textColor(e){return this.eventMap.get(e).fontColor}entryTitle(e){return this.eventMap.get(e).title}entryFont(e){return P}decorateEntry(e,t,s,i,a,r,n,o,l){return!1}forceDecoration(e){return!1}prepareHighlightedEntryInfo(e){const t=document.createElement("div");return this.eventMap.get(e).decorate(t),t}formatValue(e,t){return e+=Math.round(this.bounds.low),this.bounds.range<2800?E(e,2):this.bounds.range<3e4?E(e,1):E(e,0)}canJumpToEntry(e){return!1}}var V=Object.freeze({__proto__:null,HotColorScheme:S,ColdColorScheme:k,Event:M,TickingFlameChart:D});const N={playbackStatus:"Playback Status",bufferingStatus:"Buffering Status"},$=e.i18n.registerUIStrings("panels/media/EventTimelineView.ts",N),_=e.i18n.getLocalizedString.bind(void 0,$);class B extends D{normalizedTimestamp;playbackStatusLastEvent;audioBufferingStateEvent;videoBufferingStateEvent;constructor(){super(),this.element.setAttribute("jslog",`${a.pane("timeline")}`),this.normalizedTimestamp=-1.5,this.addGroup(_(N.playbackStatus),2),this.addGroup(_(N.bufferingStatus),2),this.playbackStatusLastEvent=null,this.audioBufferingStateEvent=null,this.videoBufferingStateEvent=null}ensureNoPreviousPlaybackEvent(e){null!==this.playbackStatusLastEvent&&(this.playbackStatusLastEvent.endTime=e,this.playbackStatusLastEvent=null)}onPlaybackEvent(e,t){switch(e.event){case"kPlay":this.canTick=!0,this.ensureNoPreviousPlaybackEvent(t),this.playbackStatusLastEvent=this.startEvent({level:0,startTime:t,name:"Play"});break;case"kPause":this.ensureNoPreviousPlaybackEvent(t),this.playbackStatusLastEvent=this.startEvent({level:0,startTime:t,name:"Pause",color:S[1]});break;case"kWebMediaPlayerDestroyed":this.canTick=!1,this.ensureNoPreviousPlaybackEvent(t),this.addMarker({level:1,startTime:t,name:"Destroyed",color:S[4]});break;case"kSuspended":this.canTick=!1,this.ensureNoPreviousPlaybackEvent(t),this.playbackStatusLastEvent=this.startEvent({level:1,startTime:t,name:"Suspended",color:S[3]});break;case"kEnded":this.ensureNoPreviousPlaybackEvent(t),this.playbackStatusLastEvent=this.startEvent({level:1,startTime:t,name:"Ended",color:S[2]});break;default:throw`_onPlaybackEvent cant handle ${e.event}`}}bufferedEnough(e){return"BUFFERING_HAVE_ENOUGH"===e.state}onBufferingStatus(e,t){let s=null,i=null;if("kBufferingStateChanged"!==e.event)throw`_onPlaybackEvent cant handle ${e.event}`;s=e.value.audio_buffering_state,i=e.value.video_buffering_state,s&&(null!==this.audioBufferingStateEvent&&(this.audioBufferingStateEvent.endTime=t,this.audioBufferingStateEvent=null),this.bufferedEnough(s)||(this.audioBufferingStateEvent=this.startEvent({level:3,startTime:t,name:"Audio Buffering",color:k[1]}))),i&&(null!==this.videoBufferingStateEvent&&(this.videoBufferingStateEvent.endTime=t,this.videoBufferingStateEvent=null),this.bufferedEnough(i)||(this.videoBufferingStateEvent=this.startEvent({level:2,startTime:t,name:"Video Buffering",color:k[0]})))}onEvent(e){-1.5===this.normalizedTimestamp&&(this.normalizedTimestamp=Number(e.timestamp));const t=1e3*(Number(e.timestamp)-this.normalizedTimestamp);switch(e.event){case"kPlay":case"kPause":case"kWebMediaPlayerDestroyed":case"kSuspended":case"kEnded":return this.onPlaybackEvent(e,t);case"kBufferingStateChanged":return this.onBufferingStatus(e,t)}}}const R=new CSSStyleSheet;R.replaceSync(".media-messages-header{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);min-height:26px}.media-messages-body{overflow-y:scroll}.media-messages-level-dropdown-element{height:18px;line-height:18px}.media-messages-level-dropdown-text{float:left}.media-messages-level-dropdown-checkbox{float:left;width:18px;height:100%;padding-left:2px}.media-messages-message-container{margin:4px;font-size:14px;line-height:18px;padding:4px;user-select:text}.media-messages-message-container + .media-messages-message-container{border-top:1px solid var(--sys-color-divider);&.media-message-warning,\n  &.media-message-error{border:none}}.media-message-warning{border-radius:5px;background-color:var(--sys-color-surface-yellow);color:var(--sys-color-on-surface-yellow)}.media-message-error{border-radius:5px;background-color:var(--sys-color-surface-error);color:var(--sys-color-on-surface-error)}.media-messages-message-filtered{display:none}.media-messages-message-unselected{display:none}.status-error-box{font-family:monospace;border:1px solid var(--sys-color-error-outline);border-radius:5px;padding:4px}.status-error-field-label{padding-right:10px;color:var(--sys-color-token-subtle)}.status-error-field-labeled{display:flex}\n/*# sourceURL=playerMessagesView.css */\n");const U={default:"Default",custom:"Custom",all:"All",error:"Error",warning:"Warning",info:"Info",debug:"Debug",logLevel:"Log level:",filterByLogMessages:"Filter by log messages",errorGroupLabel:"Error Group:",errorCodeLabel:"Error Code:",errorDataLabel:"Data:",errorStackLabel:"Stacktrace:",errorCauseLabel:"Caused by:"},A=e.i18n.registerUIStrings("panels/media/PlayerMessagesView.ts",U),j=e.i18n.getLocalizedString.bind(void 0,A);class z{items;view;itemMap;hiddenLevels;bitFieldValue;savedBitFieldValue;defaultTitleInternal;customTitle;allTitle;elementsForItems;constructor(e,t){this.items=e,this.view=t,this.itemMap=new Map,this.hiddenLevels=[],this.bitFieldValue=7,this.savedBitFieldValue=7,this.defaultTitleInternal=j(U.default),this.customTitle=j(U.custom),this.allTitle=j(U.all),this.elementsForItems=new WeakMap}defaultTitle(){return this.defaultTitleInternal}setDefault(e){e.selectItem(this.items.at(0))}populate(){this.items.insert(this.items.length,{title:this.defaultTitleInternal,overwrite:!0,stringValue:"",value:7,selectable:void 0}),this.items.insert(this.items.length,{title:this.allTitle,overwrite:!0,stringValue:"",value:15,selectable:void 0}),this.items.insert(this.items.length,{title:j(U.error),overwrite:!1,stringValue:"error",value:1,selectable:void 0}),this.items.insert(this.items.length,{title:j(U.warning),overwrite:!1,stringValue:"warning",value:2,selectable:void 0}),this.items.insert(this.items.length,{title:j(U.info),overwrite:!1,stringValue:"info",value:4,selectable:void 0}),this.items.insert(this.items.length,{title:j(U.debug),overwrite:!1,stringValue:"debug",value:8,selectable:void 0})}updateCheckMarks(){this.hiddenLevels=[];for(const[e,t]of this.itemMap)if(!t.overwrite){const s=this.elementsForItems.get(t);s&&s.firstChild&&s.firstChild.remove(),s&&e&this.bitFieldValue?i.UIUtils.createTextChild(s.createChild("div"),"✓"):this.hiddenLevels.push(t.stringValue)}}titleFor(e){if(e.overwrite?this.bitFieldValue=e.value:this.bitFieldValue^=e.value,7===this.bitFieldValue)return this.defaultTitleInternal;if(15===this.bitFieldValue)return this.allTitle;const t=this.itemMap.get(this.bitFieldValue);return t?t.title:this.customTitle}createElementForItem(e){const t=document.createElement("div"),s=i.UIUtils.createShadowRootWithCoreStyles(t,{cssFile:[R],delegatesFocus:void 0}).createChild("div","media-messages-level-dropdown-element"),a=s.createChild("div","media-messages-level-dropdown-checkbox"),r=s.createChild("span","media-messages-level-dropdown-text");return i.UIUtils.createTextChild(r,e.title),this.elementsForItems.set(e,a),this.itemMap.set(e.value,e),this.updateCheckMarks(),this.view.regenerateMessageDisplayCss(this.hiddenLevels),t}isItemSelectable(e){return!0}itemSelected(e){this.updateCheckMarks(),this.view.regenerateMessageDisplayCss(this.hiddenLevels)}highlightedItemChanged(e,t,s,i){}}class O extends i.Widget.VBox{headerPanel;bodyPanel;messageLevelSelector;constructor(){super(),this.element.setAttribute("jslog",`${a.pane("messages")}`),this.headerPanel=this.contentElement.createChild("div","media-messages-header"),this.bodyPanel=this.contentElement.createChild("div","media-messages-body"),this.buildToolbar()}buildToolbar(){const e=new i.Toolbar.Toolbar("media-messages-toolbar",this.headerPanel);e.appendText(j(U.logLevel)),e.appendToolbarItem(this.createDropdown()),e.appendSeparator(),e.appendToolbarItem(this.createFilterInput())}createDropdown(){const e=new i.ListModel.ListModel;this.messageLevelSelector=new z(e,this);const t=new i.SoftDropDown.SoftDropDown(e,this.messageLevelSelector,"log-level");t.setRowHeight(18),this.messageLevelSelector.populate(),this.messageLevelSelector.setDefault(t);const s=new i.Toolbar.ToolbarItem(t.element);return s.element.classList.add("toolbar-has-dropdown"),s.setEnabled(!0),s.setTitle(this.messageLevelSelector.defaultTitle()),i.ARIAUtils.setLabel(s.element,`${j(U.logLevel)} ${this.messageLevelSelector.defaultTitle()}`),s}createFilterInput(){const e=new i.Toolbar.ToolbarFilter(j(U.filterByLogMessages),1,1);return e.addEventListener("TextChanged",(e=>{this.filterByString(e)}),this),e}regenerateMessageDisplayCss(e){const t=this.bodyPanel.getElementsByClassName("media-messages-message-container");for(const s of t)this.matchesHiddenLevels(s,e)?s.classList.add("media-messages-message-unselected"):s.classList.remove("media-messages-message-unselected")}matchesHiddenLevels(e,t){for(const s of t)if(e.classList.contains("media-message-"+s))return!0;return!1}filterByString(e){const t=e.data,s=this.bodyPanel.getElementsByClassName("media-messages-message-container");for(const e of s)""===t||e.textContent&&e.textContent.includes(t)?e.classList.remove("media-messages-message-filtered"):e.classList.add("media-messages-message-filtered")}addMessage(e){const t=this.bodyPanel.createChild("div","media-messages-message-container media-message-"+e.level);i.UIUtils.createTextChild(t,e.message)}errorToDiv(e){const t=i.Fragment.Fragment.build`
    <div class="status-error-box">
    <div class="status-error-field-labeled">
      <span class="status-error-field-label" $="status-error-group"></span>
      <span>${e.errorType}</span>
    </div>
    <div class="status-error-field-labeled">
      <span class="status-error-field-label" $="status-error-code"></span>
      <span>${e.code}</span>
    </div>
    <div class="status-error-field-labeled" $="status-error-data">
    </div>
    <div class="status-error-field-labeled" $="status-error-stack">
    </div>
    <div class="status-error-field-labeled" $="status-error-cause">
    </div>
    `;if(t.$("status-error-group").textContent=j(U.errorGroupLabel),t.$("status-error-code").textContent=j(U.errorCodeLabel),0!==Object.keys(e.data).length){const s=t.$("status-error-data").createChild("span","status-error-field-label");i.UIUtils.createTextChild(s,j(U.errorDataLabel));const a=t.$("status-error-data").createChild("div");for(const[t,s]of Object.entries(e.data)){const e=a.createChild("div");i.UIUtils.createTextChild(e,`${t}: ${s}`)}}if(0!==e.stack.length){const s=t.$("status-error-stack").createChild("span","status-error-field-label");i.UIUtils.createTextChild(s,j(U.errorStackLabel));const a=t.$("status-error-stack").createChild("div");for(const t of e.stack){const e=a.createChild("div");i.UIUtils.createTextChild(e,`${t.file}:${t.line}`)}}if(0!==e.cause.length){const s=t.$("status-error-cause").createChild("span","status-error-field-label");i.UIUtils.createTextChild(s,j(U.errorCauseLabel)),t.$("status-error-cause").appendChild(this.errorToDiv(e.cause[0]))}return t.element()}addError(e){this.bodyPanel.createChild("div","media-messages-message-container media-message-error").appendChild(this.errorToDiv(e))}wasShown(){super.wasShown(),this.registerCSSFiles([R])}}const H=new CSSStyleSheet;H.replaceSync(".media-attributes-view{border-bottom:1px solid var(--sys-color-divider)}.media-property-renderer{line-height:20px;min-height:28px;padding:4px 10px;display:block;overflow:hidden;&:hover{background:var(--sys-color-state-hover-on-subtle)}}.media-property-renderer:nth-child(even):not(:hover){background:var(--sys-color-surface1)}.media-property-renderer:has(.json-view){padding-bottom:0}.media-property-renderer:has(.json-view > .expanded){padding-bottom:4px}.media-property-renderer-hidden{display:none}.media-property-renderer-title{font-size:12px;float:left;width:150px}.media-property-renderer-title::first-letter{text-transform:uppercase}.media-property-renderer-contents{position:relative;& > .json-view{overflow:hidden;padding:0}}.media-properties-frame{display:block;overflow-x:hidden}\n/*# sourceURL=playerPropertiesView.css */\n");const G={video:"Video",audio:"Audio",track:"Track",decoder:"Decoder",properties:"Properties",textTrack:"Text track",noTextTracks:"No text tracks",resolution:"Resolution",fileSize:"File size",bitrate:"Bitrate",duration:"Duration",startTime:"Start time",streaming:"Streaming",playbackFrameUrl:"Playback frame URL",playbackFrameTitle:"Playback frame title",singleoriginPlayback:"Single-origin playback",rangeHeaderSupport:"`Range` header support",frameRate:"Frame rate",videoPlaybackRoughness:"Video playback roughness",videoFreezingScore:"Video freezing score",rendererName:"Renderer name",decoderName:"Decoder name",noDecoder:"No decoder",hardwareDecoder:"Hardware decoder",decryptingDemuxer:"Decrypting demuxer",encoderName:"Encoder name",noEncoder:"No encoder",hardwareEncoder:"Hardware encoder"},W=e.i18n.registerUIStrings("panels/media/PlayerPropertiesView.ts",G),J=e.i18n.getLocalizedString.bind(void 0,W),K=e.i18n.getLazilyComputedLocalizedString.bind(void 0,W);class X extends i.Widget.VBox{title;contents;value;pseudoColorProtectionElement;constructor(e){super(),this.contentElement.classList.add("media-property-renderer");const t=this.contentElement.createChild("span","media-property-renderer-title");this.contents=this.contentElement.createChild("div","media-property-renderer-contents"),i.UIUtils.createTextChild(t,e),this.title=e,this.value=null,this.pseudoColorProtectionElement=null,this.contentElement.classList.add("media-property-renderer-hidden")}updateData(e,t){if(""===t||null===t)return this.updateDataInternal(e,null);try{t=JSON.parse(t)}catch(e){}return this.updateDataInternal(e,t)}updateDataInternal(e,t){if(null===t)this.changeContents(null);else{if(this.value===t)return;this.value=t,this.changeContents(t)}}unsetNestedContents(){this.contentElement.classList.add("media-property-renderer-hidden"),null===this.pseudoColorProtectionElement&&(this.pseudoColorProtectionElement=document.createElement("div"),this.pseudoColorProtectionElement.classList.add("media-property-renderer"),this.pseudoColorProtectionElement.classList.add("media-property-renderer-hidden"),this.contentElement.parentNode.insertBefore(this.pseudoColorProtectionElement,this.contentElement))}changeNestedContents(e){if(null===e||0===Object.keys(e).length)this.unsetNestedContents();else{null!==this.pseudoColorProtectionElement&&(this.pseudoColorProtectionElement.remove(),this.pseudoColorProtectionElement=null),this.contentElement.classList.remove("media-property-renderer-hidden"),this.contents.removeChildren();new s.JSONView.JSONView(new s.JSONView.ParsedJSON(e,"",""),!0).show(this.contents)}}changeContents(e){if(null===e)this.unsetNestedContents();else{null!==this.pseudoColorProtectionElement&&(this.pseudoColorProtectionElement.remove(),this.pseudoColorProtectionElement=null),this.contentElement.classList.remove("media-property-renderer-hidden"),this.contents.removeChildren();const t=document.createElement("span");t.textContent=e,this.contents.appendChild(t)}}}class Y extends X{formatfunction;constructor(e,t){super(e),this.formatfunction=t}updateDataInternal(e,t){null===t?this.changeContents(null):this.changeContents(this.formatfunction(t))}}class q extends X{constructor(e,t){super(e),this.changeContents(t)}}class Q extends X{constructor(e,t){super(e),this.changeNestedContents(t)}}class Z extends i.Widget.VBox{contentHash;constructor(e){super(),this.contentHash=0,this.contentElement.classList.add("media-attributes-view");for(const t of e){t.show(this.contentElement);const e=this.contentElement.textContent;null!==e&&(this.contentHash+=h.StringUtilities.hashCode(e))}}getContentHash(){return this.contentHash}}class ee{type;view;constructor(e,t){this.type=t,this.view=e}updateData(e,t){const s=this.view.getTabs(this.type),i=JSON.parse(t);let a=1;for(const e of i)this.addNewTab(s,e,a),a++}addNewTab(t,s,i){const a=[];for(const[t,i]of Object.entries(s))"object"==typeof i?a.push(new Q(e.i18n.lockedString(t),i)):a.push(new q(e.i18n.lockedString(t),i));const r=new Z(a);t.addNewTab(i,r)}}class te extends ee{constructor(e){super(e,"video")}}class se extends ee{constructor(e){super(e,"text")}}class ie extends ee{constructor(e){super(e,"audio")}}const ae={Video:K(G.video),Audio:K(G.audio)};class re extends i.TabbedPane.TabbedPane{decoderName;trackName;constructor(e,t=J(G.track)){super(),this.decoderName=e,this.trackName=t}addNewTab(e,t){const s=J(G.track),i=`track-${e}`;if(this.hasTab(i)){const e=this.tabView(i);if(null===e)return;if(e.getContentHash()===t.getContentHash())return;this.closeTab(i,!1)}this.appendTab(i,`${this.trackName} #${e}`,t,`${this.decoderName} ${s} #${e}`)}}class ne extends re{constructor(e,t){super(e);const s=`${e} ${J(G.decoder)}`,i=`${s} ${J(G.properties)}`;this.appendTab("decoder-properties",s,t,i)}}class oe extends i.Widget.VBox{isPlaceholder;wrapping;constructor(e,t){super(),this.isPlaceholder=!0,this.wrapping=e,this.wrapping.appendTab("_placeholder",t,new i.Widget.VBox,t),this.wrapping.show(this.contentElement)}addNewTab(e,t){this.isPlaceholder&&(this.wrapping.closeTab("_placeholder"),this.isPlaceholder=!1),this.wrapping.addNewTab(e,t)}}class le extends i.Widget.VBox{mediaElements;videoDecoderElements;audioDecoderElements;textTrackElements;attributeMap;videoProperties;videoDecoderProperties;audioDecoderProperties;videoDecoderTabs;audioDecoderTabs;textTracksTabs;constructor(){super(),this.element.setAttribute("jslog",`${a.pane("properties")}`),this.contentElement.classList.add("media-properties-frame"),this.mediaElements=[],this.videoDecoderElements=[],this.audioDecoderElements=[],this.textTrackElements=[],this.attributeMap=new Map,this.populateAttributesAndElements(),this.videoProperties=new Z(this.mediaElements),this.videoDecoderProperties=new Z(this.videoDecoderElements),this.audioDecoderProperties=new Z(this.audioDecoderElements),this.videoProperties.show(this.contentElement),this.videoDecoderTabs=new ne(ae.Video(),this.videoDecoderProperties),this.videoDecoderTabs.show(this.contentElement),this.audioDecoderTabs=new ne(ae.Audio(),this.audioDecoderProperties),this.audioDecoderTabs.show(this.contentElement),this.textTracksTabs=null}lazyCreateTrackTabs(){let e=this.textTracksTabs;if(null===e){const t=new re(J(G.textTrack));e=new oe(t,J(G.noTextTracks)),e.show(this.contentElement),this.textTracksTabs=e}return e}getTabs(e){if("audio"===e)return this.audioDecoderTabs;if("video"===e)return this.videoDecoderTabs;if("text"===e)return this.lazyCreateTrackTabs();throw new Error("Unreachable")}onProperty(e){const t=this.attributeMap.get(e.name);if(!t)throw new Error(`Player property "${e.name}" not supported.`);t.updateData(e.name,e.value)}formatKbps(e){if(""===e)return"0 kbps";return`${Math.floor(Number(e)/1e3)} kbps`}formatTime(e){if(""===e)return"0:00";const t=new Date;return t.setSeconds(Number(e)),t.toISOString().substr(11,8)}formatFileSize(e){if(""===e)return"0 bytes";const t=Number(e);if(t<1e3)return`${e} bytes`;const s=Math.floor(Math.log10(t)/3),i=["bytes","kB","MB","GB","TB"][s];return`${(t/Math.pow(1e3,s)).toFixed(2)} ${i}`}populateAttributesAndElements(){const e=new X(J(G.resolution));this.mediaElements.push(e),this.attributeMap.set("kResolution",e);const t=new Y(J(G.fileSize),this.formatFileSize);this.mediaElements.push(t),this.attributeMap.set("kTotalBytes",t);const s=new Y(J(G.bitrate),this.formatKbps);this.mediaElements.push(s),this.attributeMap.set("kBitrate",s);const i=new Y(J(G.duration),this.formatTime);this.mediaElements.push(i),this.attributeMap.set("kMaxDuration",i);const a=new X(J(G.startTime));this.mediaElements.push(a),this.attributeMap.set("kStartTime",a);const r=new X(J(G.streaming));this.mediaElements.push(r),this.attributeMap.set("kIsStreaming",r);const n=new X(J(G.playbackFrameUrl));this.mediaElements.push(n),this.attributeMap.set("kFrameUrl",n);const o=new X(J(G.playbackFrameTitle));this.mediaElements.push(o),this.attributeMap.set("kFrameTitle",o);const l=new X(J(G.singleoriginPlayback));this.mediaElements.push(l),this.attributeMap.set("kIsSingleOrigin",l);const d=new X(J(G.rangeHeaderSupport));this.mediaElements.push(d),this.attributeMap.set("kIsRangeHeaderSupported",d);const h=new X(J(G.frameRate));this.mediaElements.push(h),this.attributeMap.set("kFramerate",h);const c=new X(J(G.videoPlaybackRoughness));this.mediaElements.push(c),this.attributeMap.set("kVideoPlaybackRoughness",c);const m=new X(J(G.videoFreezingScore));this.mediaElements.push(m),this.attributeMap.set("kVideoPlaybackFreezing",m);const p=new X(J(G.rendererName));this.mediaElements.push(p),this.attributeMap.set("kRendererName",p);const u=new q(J(G.decoderName),J(G.noDecoder));this.videoDecoderElements.push(u),this.attributeMap.set("kVideoDecoderName",u);const g=new X(J(G.hardwareDecoder));this.videoDecoderElements.push(g),this.attributeMap.set("kIsPlatformVideoDecoder",g);const v=new q(J(G.encoderName),J(G.noEncoder));this.videoDecoderElements.push(v),this.attributeMap.set("kVideoEncoderName",v);const y=new X(J(G.hardwareEncoder));this.videoDecoderElements.push(y),this.attributeMap.set("kIsPlatformVideoEncoder",y);const b=new X(J(G.decryptingDemuxer));this.videoDecoderElements.push(b),this.attributeMap.set("kIsVideoDecryptingDemuxerStream",b);const f=new te(this);this.attributeMap.set("kVideoTracks",f);const w=new q(J(G.decoderName),J(G.noDecoder));this.audioDecoderElements.push(w),this.attributeMap.set("kAudioDecoderName",w);const E=new X(J(G.hardwareDecoder));this.audioDecoderElements.push(E),this.attributeMap.set("kIsPlatformAudioDecoder",E);const T=new X(J(G.decryptingDemuxer));this.audioDecoderElements.push(T),this.attributeMap.set("kIsAudioDecryptingDemuxerStream",T);const x=new ie(this);this.attributeMap.set("kAudioTracks",x);const P=new se(this);this.attributeMap.set("kTextTracks",P)}wasShown(){super.wasShown(),this.registerCSSFiles([H])}}var de=Object.freeze({__proto__:null,PropertyRenderer:X,FormattedPropertyRenderer:Y,DefaultPropertyRenderer:q,NestedPropertyRenderer:Q,DimensionPropertyRenderer:class extends X{width;height;constructor(e){super(e),this.width=0,this.height=0}updateDataInternal(e,t){let s=!1;"width"===e&&Number(t)!==this.width&&(this.width=Number(t),s=!0),"height"===e&&Number(t)!==this.height&&(this.height=Number(t),s=!0),0===this.width||0===this.height?this.changeContents(null):s&&this.changeContents(`${this.width}×${this.height}`)}},AttributesView:Z,TrackManager:ee,VideoTrackManager:te,TextTrackManager:se,AudioTrackManager:ie,PlayerPropertiesView:le});const he={properties:"Properties",playerProperties:"Player properties",events:"Events",playerEvents:"Player events",messages:"Messages",playerMessages:"Player messages",timeline:"Timeline",playerTimeline:"Player timeline"},ce=e.i18n.registerUIStrings("panels/media/PlayerDetailView.ts",he),me=e.i18n.getLocalizedString.bind(void 0,ce);class pe extends i.TabbedPane.TabbedPane{eventView;propertyView;messageView;timelineView;constructor(){super(),this.eventView=new y,this.propertyView=new le,this.messageView=new O,this.timelineView=new B,this.appendTab("properties",me(he.properties),this.propertyView,me(he.playerProperties)),this.appendTab("events",me(he.events),this.eventView,me(he.playerEvents)),this.appendTab("messages",me(he.messages),this.messageView,me(he.playerMessages)),this.appendTab("timeline",me(he.timeline),this.timelineView,me(he.playerTimeline))}onProperty(e){this.propertyView.onProperty(e)}onError(e){this.messageView.addError(e)}onMessage(e){this.messageView.addMessage(e)}onEvent(e){this.eventView.onEvent(e),this.timelineView.onEvent(e)}}var ue=Object.freeze({__proto__:null,PlayerDetailView:pe});const ge=new CSSStyleSheet;ge.replaceSync(".tree-outline{padding-left:0;color:var(--sys-color-token-subtle)}li.storage-group-list-item{padding:10px 8px 6px}li.storage-group-list-item:not(:first-child){border-top:1px solid var(--sys-color-divider)}li.storage-group-list-item::before{display:none}.player-entry-row{height:26px;min-height:26px;line-height:26px;&:nth-child(odd){background:var(--sys-color-surface1)}&:hover{background:var(--sys-color-state-hover-on-subtle)}&.selected{background:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}}.player-entry-status-icon-centering{margin:auto;display:inherit}.player-entry-status-icon{width:28px;min-width:28px;height:26px;border-right:1px solid var(--sys-color-divider);overflow:hidden}.player-entry-frame-title{height:26px;width:125px;min-width:125px;text-overflow:elipsis;padding:0 10px;border-right:1px solid var(--sys-color-divider);overflow:hidden}.player-entry-player-title{height:26px;padding-left:10px;overflow:hidden}.player-entry-header{height:27px;line-height:27px;min-height:27px;padding-left:10px;border-bottom:1px solid var(--sys-color-divider)}\n/*# sourceURL=playerListView.css */\n");const ve={hidePlayer:"Hide player",hideAllOthers:"Hide all others",savePlayerInfo:"Save player info",players:"Players"},ye=e.i18n.registerUIStrings("panels/media/PlayerListView.ts",ve),be=e.i18n.getLocalizedString.bind(void 0,ye);class fe extends i.Widget.VBox{playerEntryFragments;playerEntriesWithHostnameFrameTitle;mainContainer;currentlySelectedEntry;constructor(e){super(!0),this.playerEntryFragments=new Map,this.playerEntriesWithHostnameFrameTitle=new Set,this.mainContainer=e,this.currentlySelectedEntry=null,this.contentElement.createChild("div","player-entry-header").textContent=be(ve.players)}createPlayerListEntry(e){const t=i.Fragment.Fragment.build`
    <div class="player-entry-row hbox">
    <div class="player-entry-status-icon vbox">
    <div $="icon" class="player-entry-status-icon-centering"></div>
    </div>
    <div $="frame-title" class="player-entry-frame-title">FrameTitle</div>
    <div $="player-title" class="player-entry-player-title">PlayerTitle</div>
    </div>
    `,s=t.element();return s.setAttribute("jslog",`${a.item("player").track({click:!0})}`),s.addEventListener("click",this.selectPlayer.bind(this,e,s)),s.addEventListener("contextmenu",this.rightClickPlayer.bind(this,e)),t.$("icon").appendChild(c.Icon.create("pause","media-player")),t}selectPlayer(e,t){this.mainContainer.renderMainPanel(e),null!==this.currentlySelectedEntry&&(this.currentlySelectedEntry.classList.remove("selected"),this.currentlySelectedEntry.classList.remove("force-white-icons")),t.classList.add("selected"),t.classList.add("force-white-icons"),this.currentlySelectedEntry=t}rightClickPlayer(e,t){const s=new i.ContextMenu.ContextMenu(t);return s.headerSection().appendItem(be(ve.hidePlayer),this.mainContainer.markPlayerForDeletion.bind(this.mainContainer,e),{jslogContext:"hide-player"}),s.headerSection().appendItem(be(ve.hideAllOthers),this.mainContainer.markOtherPlayersForDeletion.bind(this.mainContainer,e),{jslogContext:"hide-all-others"}),s.headerSection().appendItem(be(ve.savePlayerInfo),this.mainContainer.exportPlayerData.bind(this.mainContainer,e),{jslogContext:"save-player-info"}),s.show(),!0}setMediaElementFrameTitle(e,t,s){if(this.playerEntriesWithHostnameFrameTitle.has(e))s||this.playerEntriesWithHostnameFrameTitle.delete(e);else if(s)return;if(!this.playerEntryFragments.has(e))return;const i=this.playerEntryFragments.get(e);void 0!==i&&void 0!==i.element()&&(i.$("frame-title").textContent=t)}setMediaElementPlayerTitle(e,t){if(!this.playerEntryFragments.has(e))return;const s=this.playerEntryFragments.get(e);void 0!==s&&(s.$("player-title").textContent=t)}setMediaElementPlayerIcon(e,t){if(!this.playerEntryFragments.has(e))return;const s=this.playerEntryFragments.get(e);if(void 0===s)return;const i=s.$("icon");void 0!==i&&(i.textContent="",i.appendChild(c.Icon.create(t,"media-player")))}formatAndEvaluate(e,t,s,i,a){s.length<=i||(s.length>=a&&(s=s.substring(0,a-3)+"..."),t.bind(this)(e,s))}addMediaElementItem(e){const t=this.createPlayerListEntry(e);this.contentElement.appendChild(t.element()),this.playerEntryFragments.set(e,t),this.playerEntriesWithHostnameFrameTitle.add(e)}deletePlayer(e){if(!this.playerEntryFragments.has(e))return;const t=this.playerEntryFragments.get(e);void 0!==t&&void 0!==t.element()&&(this.contentElement.removeChild(t.element()),this.playerEntryFragments.delete(e))}onEvent(e,t){const s=JSON.parse(t.value),i=s.event;if("kLoad"!==i)"kPlay"!==i?"kPause"!==i&&"kEnded"!==i?"kWebMediaPlayerDestroyed"!==i||this.setMediaElementPlayerIcon(e,"cross"):this.setMediaElementPlayerIcon(e,"pause"):this.setMediaElementPlayerIcon(e,"play");else{const t=s.url,i=t.substring(t.lastIndexOf("/")+1);this.formatAndEvaluate(e,this.setMediaElementPlayerTitle,i,1,20)}}onProperty(e,t){if("kFrameUrl"!==t.name)"kFrameTitle"===t.name&&t.value&&this.formatAndEvaluate(e,this.setMediaElementFrameTitle,t.value,1,20);else{const s=new URL(t.value).hostname;this.formatAndEvaluate(e,this.setMediaElementFrameTitle,s,1,20)}}onError(e,t){}onMessage(e,t){}wasShown(){super.wasShown(),this.registerCSSFiles([ge])}}var we=Object.freeze({__proto__:null,PlayerListView:fe});class Ee{properties;messages;events;errors;constructor(){this.properties=new Map,this.messages=[],this.events=[],this.errors=[]}onProperty(e){this.properties.set(e.name,e.value)}onError(e){this.errors.push(e)}onMessage(e){this.messages.push(e)}onEvent(e){this.events.push(e)}export(){return{properties:this.properties,messages:this.messages,events:this.events,errors:this.errors}}}class Te{playerDataCollection;constructor(){this.playerDataCollection=new Map}addPlayer(e){this.playerDataCollection.set(e,new Ee)}onProperty(e,t){const s=this.playerDataCollection.get(e);s&&s.onProperty(t)}onError(e,t){const s=this.playerDataCollection.get(e);s&&s.onError(t)}onMessage(e,t){const s=this.playerDataCollection.get(e);s&&s.onMessage(t)}onEvent(e,t){const s=this.playerDataCollection.get(e);s&&s.onEvent(t)}exportPlayerData(e){const t=this.playerDataCollection.get(e);if(!t)throw new Error("Unable to find player");return t.export()}deletePlayer(e){this.playerDataCollection.delete(e)}}class xe extends i.Panel.PanelWithSidebar{detailPanels;deletedPlayers;downloadStore;sidebar;constructor(e=new Te){super("media"),this.detailPanels=new Map,this.deletedPlayers=new Set,this.downloadStore=e,this.sidebar=new fe(this),this.sidebar.show(this.panelSidebarElement()),r.TargetManager.TargetManager.instance().observeModels(f,this,{scoped:!0})}renderMainPanel(e){if(!this.detailPanels.has(e))return;const t=this.splitWidget().mainWidget();t&&t.detachChildWidgets(),this.detailPanels.get(e)?.show(this.mainElement())}wasShown(){super.wasShown();for(const e of r.TargetManager.TargetManager.instance().models(f,{scoped:!0}))this.addEventListeners(e)}willHide(){for(const e of r.TargetManager.TargetManager.instance().models(f,{scoped:!0}))this.removeEventListeners(e)}modelAdded(e){this.isShowing()&&this.addEventListeners(e)}modelRemoved(e){this.removeEventListeners(e)}addEventListeners(e){e.ensureEnabled(),e.addEventListener("PlayerPropertiesChanged",this.propertiesChanged,this),e.addEventListener("PlayerEventsAdded",this.eventsAdded,this),e.addEventListener("PlayerMessagesLogged",this.messagesLogged,this),e.addEventListener("PlayerErrorsRaised",this.errorsRaised,this),e.addEventListener("PlayersCreated",this.playersCreated,this)}removeEventListeners(e){e.removeEventListener("PlayerPropertiesChanged",this.propertiesChanged,this),e.removeEventListener("PlayerEventsAdded",this.eventsAdded,this),e.removeEventListener("PlayerMessagesLogged",this.messagesLogged,this),e.removeEventListener("PlayerErrorsRaised",this.errorsRaised,this),e.removeEventListener("PlayersCreated",this.playersCreated,this)}onPlayerCreated(e){this.sidebar.addMediaElementItem(e),this.detailPanels.set(e,new pe),this.downloadStore.addPlayer(e)}propertiesChanged(e){for(const t of e.data.properties)this.onProperty(e.data.playerId,t)}eventsAdded(e){for(const t of e.data.events)this.onEvent(e.data.playerId,t)}messagesLogged(e){for(const t of e.data.messages)this.onMessage(e.data.playerId,t)}errorsRaised(e){for(const t of e.data.errors)this.onError(e.data.playerId,t)}shouldPropagate(e){return!this.deletedPlayers.has(e)&&this.detailPanels.has(e)}onProperty(e,t){this.shouldPropagate(e)&&(this.sidebar.onProperty(e,t),this.downloadStore.onProperty(e,t),this.detailPanels.get(e)?.onProperty(t))}onError(e,t){this.shouldPropagate(e)&&(this.sidebar.onError(e,t),this.downloadStore.onError(e,t),this.detailPanels.get(e)?.onError(t))}onMessage(e,t){this.shouldPropagate(e)&&(this.sidebar.onMessage(e,t),this.downloadStore.onMessage(e,t),this.detailPanels.get(e)?.onMessage(t))}onEvent(e,t){this.shouldPropagate(e)&&(this.sidebar.onEvent(e,t),this.downloadStore.onEvent(e,t),this.detailPanels.get(e)?.onEvent(t))}playersCreated(e){for(const t of e.data)this.onPlayerCreated(t)}markPlayerForDeletion(e){this.deletedPlayers.add(e),this.detailPanels.delete(e),this.sidebar.deletePlayer(e),this.downloadStore.deletePlayer(e)}markOtherPlayersForDeletion(e){for(const t of this.detailPanels.keys())t!==e&&this.markPlayerForDeletion(t)}exportPlayerData(e){const t=this.downloadStore.exportPlayerData(e),s="data:application/octet-stream,"+encodeURIComponent(JSON.stringify(t,null,2)),i=document.createElement("a");i.href=s,i.download=e+".json",i.click()}}var Pe=Object.freeze({__proto__:null,PlayerDataDownloadManager:Te,MainView:xe});export{Pe as MainView,w as MediaModel,ue as PlayerDetailView,b as PlayerEventsView,we as PlayerListView,de as PlayerPropertiesView,V as TickingFlameChart,x as TickingFlameChartHelpers};
