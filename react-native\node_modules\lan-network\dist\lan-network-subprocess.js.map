{"version": 3, "file": "lan-network-subprocess.js", "sources": ["../src/subprocess.ts"], "sourcesContent": ["import { lanNetwork } from './index';\n\nasync function output() {\n  const assignment = await lanNetwork();\n  process.stdout.write(JSON.stringify(assignment));\n  process.exit(0);\n}\n\noutput();\n"], "names": ["async", "output", "assignment", "lanNetwork", "process", "stdout", "write", "JSON", "stringify", "exit"], "mappings": ";;CAEAA,eAAeC;EACb,IAAMC,UAAmBC;EACzBC,QAAQC,OAAOC,MAAMC,KAAKC,UAAUN;EACpCE,QAAQK,KAAK;AACf,CAEAR"}