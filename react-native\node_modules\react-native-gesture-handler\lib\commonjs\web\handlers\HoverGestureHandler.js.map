{"version": 3, "sources": ["HoverGestureHandler.ts"], "names": ["HoverGestureHandler", "Gesture<PERSON>andler", "init", "ref", "propsRef", "transformNativeEvent", "stylusData", "updateGestureConfig", "enabled", "props", "onPointerMoveOver", "event", "GestureHandlerOrchestrator", "getInstance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "getState", "State", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "removeFromTracker", "pointerId", "end", "onPointerMove", "track", "onPointerCancel", "reset"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;;;;;AAEe,MAAMA,mBAAN,SAAkCC,uBAAlC,CAAiD;AAAA;AAAA;;AAAA;AAAA;;AAGvDC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAkD;AAC3D,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AACD;;AAESC,EAAAA,oBAAoB,GAA4B;AACxD,WAAO,EACL,GAAG,MAAMA,oBAAN,EADE;AAELC,MAAAA,UAAU,EAAE,KAAKA;AAFZ,KAAP;AAID;;AAEMC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAGC;AAArB,GAAD,EAA6C;AACrE,UAAMF,mBAAN,CAA0B;AAAEC,MAAAA,OAAO,EAAEA,OAAX;AAAoB,SAAGC;AAAvB,KAA1B;AACD;;AAESC,EAAAA,iBAAiB,CAACC,KAAD,EAA4B;AACrDC,wCAA2BC,WAA3B,GAAyCC,yBAAzC,CAAmE,IAAnE;;AAEA,SAAKC,OAAL,CAAaC,YAAb,CAA0BL,KAA1B;AACA,SAAKL,UAAL,GAAkBK,KAAK,CAACL,UAAxB;AACA,UAAMI,iBAAN,CAAwBC,KAAxB;;AAEA,QAAI,KAAKM,QAAL,OAAoBC,aAAMC,YAA9B,EAA4C;AAC1C,WAAKC,KAAL;AACA,WAAKC,QAAL;AACD;AACF;;AAESC,EAAAA,gBAAgB,CAACX,KAAD,EAA4B;AACpD,SAAKI,OAAL,CAAaQ,iBAAb,CAA+BZ,KAAK,CAACa,SAArC;AACA,SAAKlB,UAAL,GAAkBK,KAAK,CAACL,UAAxB;AAEA,UAAMgB,gBAAN,CAAuBX,KAAvB;AAEA,SAAKc,GAAL;AACD;;AAESC,EAAAA,aAAa,CAACf,KAAD,EAA4B;AACjD,SAAKI,OAAL,CAAaY,KAAb,CAAmBhB,KAAnB;AACA,SAAKL,UAAL,GAAkBK,KAAK,CAACL,UAAxB;AAEA,UAAMoB,aAAN,CAAoBf,KAApB;AACD;;AAESiB,EAAAA,eAAe,CAACjB,KAAD,EAA4B;AACnD,UAAMiB,eAAN,CAAsBjB,KAAtB;AACA,SAAKkB,KAAL;AACD;;AAlD6D", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, Config, StylusData } from '../interfaces';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport GestureHandler from './GestureHandler';\n\nexport default class HoverGestureHandler extends GestureHandler {\n  private stylusData: StylusData | undefined;\n\n  public init(ref: number, propsRef: React.RefObject<unknown>) {\n    super.init(ref, propsRef);\n  }\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    return {\n      ...super.transformNativeEvent(),\n      stylusData: this.stylusData,\n    };\n  }\n\n  public updateGestureConfig({ enabled = true, ...props }: Config): void {\n    super.updateGestureConfig({ enabled: enabled, ...props });\n  }\n\n  protected onPointerMoveOver(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.getInstance().recordHandlerIfNotPresent(this);\n\n    this.tracker.addToTracker(event);\n    this.stylusData = event.stylusData;\n    super.onPointerMoveOver(event);\n\n    if (this.getState() === State.UNDETERMINED) {\n      this.begin();\n      this.activate();\n    }\n  }\n\n  protected onPointerMoveOut(event: AdaptedEvent): void {\n    this.tracker.removeFromTracker(event.pointerId);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMoveOut(event);\n\n    this.end();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerCancel(event: AdaptedEvent): void {\n    super.onPointerCancel(event);\n    this.reset();\n  }\n}\n"]}