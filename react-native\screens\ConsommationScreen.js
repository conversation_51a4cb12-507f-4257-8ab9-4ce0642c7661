import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { API_CONFIG } from '../config/constants';

const API_BASE_URL = API_CONFIG.BASE_URL;

const ConsommationScreen = ({ navigation, route }) => {
  const [selectedClient, setSelectedClient] = useState(route.params?.selectedClient || null);
  const [clients, setClients] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [selectedContract, setSelectedContract] = useState(null);
  const [periode, setPeriode] = useState('');
  const [consommationPrecedente, setConsommationPrecedente] = useState('');
  const [consommationActuelle, setConsommationActuelle] = useState('');
  const [nombreJours, setNombreJours] = useState('');
  const [loading, setLoading] = useState(false);
  const [showClientModal, setShowClientModal] = useState(false);

  useEffect(() => {
    loadClients();
    if (selectedClient) {
      loadClientContracts(selectedClient.idclient);
    }
  }, [selectedClient]);

  useEffect(() => {
    if (selectedContract) {
      loadLastConsommation(selectedContract.idcontract);
    }
  }, [selectedContract]);

  const loadClients = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();
      if (data.success) {
        setClients(data.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
    }
  };

  const loadClientContracts = async (clientId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`);
      const data = await response.json();
      if (data.success && data.data.length > 0) {
        setContracts(data.data);
        if (data.data.length === 1) {
          setSelectedContract(data.data[0]);
        }
      } else {
        setContracts([]);
        setSelectedContract(null);
        Alert.alert('Information', 'Ce client n\'a pas de contrat associé.');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des contrats:', error);
    }
  };

  const loadLastConsommation = async (contractId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/contracts/${contractId}/last-consommation`);
      const data = await response.json();
      if (data.success && data.data) {
        setConsommationPrecedente(data.data.consommationactuelle.toString());
        if (data.data.periode) {
          const lastPeriode = new Date(data.data.periode + '-01');
          const currentPeriode = new Date(lastPeriode);
          currentPeriode.setMonth(currentPeriode.getMonth() + 1);
          
          const year = currentPeriode.getFullYear();
          const month = String(currentPeriode.getMonth() + 1).padStart(2, '0');
          setPeriode(`${year}-${month}`);
          
          // Calculer le nombre de jours
          const today = new Date();
          const lastDate = new Date(data.data.periode + '-01');
          const diffTime = Math.abs(today - lastDate);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          setNombreJours(diffDays.toString());
        }
      } else {
        setConsommationPrecedente('0');
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        setPeriode(`${year}-${month}`);
        setNombreJours('30');
      }
    } catch (error) {
      console.error('Erreur lors du chargement de la dernière consommation:', error);
    }
  };

  const handleClientSelect = (client) => {
    setSelectedClient(client);
    setShowClientModal(false);
    loadClientContracts(client.idclient);
  };

  const validateAndSubmit = async () => {
    if (!selectedClient) {
      Alert.alert('Erreur', 'Veuillez sélectionner un client');
      return;
    }

    if (!selectedContract) {
      Alert.alert('Erreur', 'Aucun contrat disponible pour ce client');
      return;
    }

    if (!periode || !consommationActuelle) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    const consommationActuelleNum = parseFloat(consommationActuelle);
    const consommationPrecedenteNum = parseFloat(consommationPrecedente);

    if (consommationActuelleNum <= consommationPrecedenteNum) {
      Alert.alert(
        'Erreur de validation',
        'La consommation actuelle doit être supérieure à la consommation précédente'
      );
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/api/consommations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idCont: selectedContract.idcontract,
          idTech: 1, // À adapter selon l'utilisateur connecté
          idTranch: 1, // À adapter selon la tranche
          consommationPre: consommationPrecedenteNum,
          consommationActuelle: consommationActuelleNum,
          jours: parseInt(nombreJours),
          periode: periode,
          status: 'active'
        }),
      });

      const data = await response.json();

      if (data.success) {
        Alert.alert(
          'Succès',
          'Consommation enregistrée avec succès',
          [
            {
              text: 'OK',
              onPress: () => {
                // Réinitialiser le formulaire
                setConsommationActuelle('');
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur', data.message || 'Erreur lors de l\'enregistrement');
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement:', error);
      Alert.alert('Erreur', 'Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const renderClientItem = ({ item }) => (
    <TouchableOpacity
      style={styles.clientItem}
      onPress={() => handleClientSelect(item)}
    >
      <View style={styles.clientAvatar}>
        <Text style={styles.clientInitials}>
          {item.prenom?.charAt(0)}{item.nom?.charAt(0)}
        </Text>
      </View>
      <View style={styles.clientInfo}>
        <Text style={styles.clientName}>{item.prenom} {item.nom}</Text>
        <Text style={styles.clientSector}>{item.secteur_nom}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.formContainer}>
        <Text style={styles.title}>Saisie de Consommation</Text>

        {/* Sélection du client */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Client *</Text>
          <TouchableOpacity
            style={styles.clientSelector}
            onPress={() => setShowClientModal(true)}
          >
            {selectedClient ? (
              <View style={styles.selectedClientInfo}>
                <Text style={styles.selectedClientName}>
                  {selectedClient.prenom} {selectedClient.nom}
                </Text>
                <Text style={styles.selectedClientDetails}>
                  {selectedClient.secteur_nom} - {selectedClient.tel}
                </Text>
              </View>
            ) : (
              <Text style={styles.placeholderText}>Sélectionner un client</Text>
            )}
            <Ionicons name="chevron-down-outline" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Contrat */}
        {contracts.length > 0 && (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Contrat</Text>
            {contracts.length === 1 ? (
              <View style={styles.contractInfo}>
                <Text style={styles.contractText}>
                  QR: {selectedContract?.codeqr}
                </Text>
              </View>
            ) : (
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={selectedContract?.idcontract}
                  onValueChange={(value) => {
                    const contract = contracts.find(c => c.idcontract === value);
                    setSelectedContract(contract);
                  }}
                >
                  <Picker.Item label="Sélectionner un contrat" value={null} />
                  {contracts.map((contract) => (
                    <Picker.Item
                      key={contract.idcontract}
                      label={`QR: ${contract.codeqr}`}
                      value={contract.idcontract}
                    />
                  ))}
                </Picker>
              </View>
            )}
          </View>
        )}

        {/* Période */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Période (YYYY-MM) *</Text>
          <TextInput
            style={styles.input}
            value={periode}
            onChangeText={setPeriode}
            placeholder="2024-01"
            maxLength={7}
          />
        </View>

        {/* Consommation précédente */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Consommation Précédente (m³)</Text>
          <TextInput
            style={[styles.input, styles.readOnlyInput]}
            value={consommationPrecedente}
            editable={false}
            placeholder="0"
          />
        </View>

        {/* Consommation actuelle */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Consommation Actuelle (m³) *</Text>
          <TextInput
            style={styles.input}
            value={consommationActuelle}
            onChangeText={setConsommationActuelle}
            placeholder="Entrer la consommation actuelle"
            keyboardType="numeric"
          />
        </View>

        {/* Nombre de jours */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Nombre de jours</Text>
          <TextInput
            style={[styles.input, styles.readOnlyInput]}
            value={nombreJours}
            editable={false}
            placeholder="30"
          />
        </View>

        {/* Bouton d'enregistrement */}
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={validateAndSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <>
              <Ionicons name="save-outline" size={20} color="#fff" />
              <Text style={styles.submitButtonText}>Enregistrer le relevé</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Modal de sélection de client */}
      <Modal
        visible={showClientModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Sélectionner un client</Text>
            <TouchableOpacity onPress={() => setShowClientModal(false)}>
              <Ionicons name="close-outline" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          <FlatList
            data={clients}
            renderItem={renderClientItem}
            keyExtractor={(item) => item.idclient.toString()}
            style={styles.clientsList}
          />
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  formContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  readOnlyInput: {
    backgroundColor: '#f9f9f9',
    color: '#666',
  },
  clientSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    backgroundColor: '#fff',
  },
  selectedClientInfo: {
    flex: 1,
  },
  selectedClientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  selectedClientDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  contractInfo: {
    padding: 15,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  contractText: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clientsList: {
    flex: 1,
  },
  clientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  clientAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  clientInitials: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  clientSector: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});

export default ConsommationScreen;
