const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur final...');

// Configuration PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Logging de toutes les requêtes
app.use((req, res, next) => {
  console.log(`\n📥 ${new Date().toISOString()}`);
  console.log(`🔗 ${req.method} ${req.url}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route racine appelée');
  res.json({ 
    message: 'Serveur final fonctionnel', 
    timestamp: new Date().toISOString(),
    port: 3005
  });
});

// Route pour tous les clients
app.get('/api/clients', async (req, res) => {
  console.log('📥 Récupération de tous les clients');
  try {
    const result = await pool.query(`
      SELECT idclient, nom, prenom, adresse, ville, tel, email
      FROM client 
      ORDER BY nom, prenom
    `);
    
    console.log(`✅ ${result.rows.length} clients trouvés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    console.error('❌ Erreur clients:', error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 🎯 ROUTE CRITIQUE : Contrats d'un client avec code QR
app.get('/api/clients/:id/contracts', async (req, res) => {
  const clientId = req.params.id;
  console.log(`\n🎯 ROUTE CRITIQUE: Contrats pour client ${clientId}`);
  
  try {
    // 1. Vérifier que le client existe
    const clientResult = await pool.query(
      'SELECT nom, prenom FROM client WHERE idclient = $1', 
      [clientId]
    );
    
    if (clientResult.rows.length === 0) {
      console.log(`❌ Client ${clientId} non trouvé`);
      return res.status(404).json({
        success: false,
        message: `Client ${clientId} non trouvé`
      });
    }
    
    const client = clientResult.rows[0];
    console.log(`✅ Client trouvé: ${client.nom} ${client.prenom}`);
    
    // 2. Récupérer les contrats depuis la table Contract
    const contractsResult = await pool.query(`
      SELECT 
        idcontract,
        codeqr,
        datecontract,
        idclient,
        marquecompteur,
        numseriecompteur,
        posx,
        posy
      FROM contract 
      WHERE idclient = $1 
      ORDER BY datecontract DESC
    `, [clientId]);
    
    console.log(`📊 ${contractsResult.rows.length} contrat(s) trouvé(s)`);
    
    // 3. Afficher les détails des contrats
    if (contractsResult.rows.length > 0) {
      console.log('📋 CONTRATS TROUVÉS:');
      contractsResult.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      📱 Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      🔧 Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
        console.log(`      📅 Date: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}`);
        console.log(`      📍 Position: (${contract.posx || 'N/A'}, ${contract.posy || 'N/A'})`);
      });
    } else {
      console.log('⚠️ AUCUN CONTRAT TROUVÉ pour ce client');
    }
    
    // 4. Retourner la réponse
    const response = {
      success: true,
      data: contractsResult.rows,
      count: contractsResult.rows.length,
      message: `${contractsResult.rows.length} contrat(s) trouvé(s) pour ${client.nom} ${client.prenom}`,
      client_id: parseInt(clientId),
      client_name: `${client.nom} ${client.prenom}`
    };
    
    console.log('📤 Envoi de la réponse contrats');
    res.json(response);
    
  } catch (error) {
    console.error('❌ ERREUR lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  const contractId = req.params.id;
  console.log(`📥 Dernière consommation pour contrat ${contractId}`);
  
  try {
    const result = await pool.query(`
      SELECT consommationactuelle, periode, jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `, [contractId]);
    
    if (result.rows.length > 0) {
      console.log(`✅ Consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      res.json({ success: true, data: result.rows[0] });
    } else {
      console.log(`ℹ️ Aucune consommation pour contrat ${contractId}`);
      res.json({ success: false, message: 'Aucune consommation trouvée' });
    }
  } catch (error) {
    console.error('❌ Erreur consommation:', error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route de connexion
app.post('/login', (req, res) => {
  const { email, motDepass } = req.body;
  console.log(`🔐 Connexion: ${email}`);
  
  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion réussie');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: { idtech: 1, nom: 'Technicien', prenom: 'Test', email, role: 'Tech' },
      redirectTo: '/technician-dashboard'
    });
  } else {
    console.log('❌ Identifiants incorrects');
    res.status(401).json({ success: false, message: 'Identifiants incorrects' });
  }
});

// Démarrage
const PORT = 3005;

async function start() {
  try {
    console.log('🔄 Test de connexion à PostgreSQL...');
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`✅ Connexion réussie - ${result.rows[0].count} clients dans la base`);
    
    // Test des contrats
    const contractsTest = await client.query('SELECT COUNT(*) FROM contract');
    console.log(`📊 ${contractsTest.rows[0].count} contrats dans la table Contract`);
    
    client.release();
    
    app.listen(PORT, () => {
      console.log(`\n🚀 SERVEUR FINAL DÉMARRÉ sur http://localhost:${PORT}`);
      console.log('📊 Base de données: Facutration');
      console.log('📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - POST /login');
      console.log('  - GET  /api/clients');
      console.log('  - GET  /api/clients/:id/contracts ⭐ CRITIQUE (affiche code QR)');
      console.log('  - GET  /api/contracts/:id/last-consommation');
      console.log('\n✅ PRÊT À RECEVOIR LES REQUÊTES !');
      console.log('🔍 Tous les appels seront loggés en détail');
      console.log('═'.repeat(60));
    });
    
  } catch (error) {
    console.error('❌ Erreur de démarrage:', error.message);
    console.error('❌ Vérifiez que PostgreSQL est démarré et que la base "Facutration" existe');
    process.exit(1);
  }
}

start();

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err.message);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
});
