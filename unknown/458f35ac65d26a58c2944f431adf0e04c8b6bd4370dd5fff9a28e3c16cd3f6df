{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperSkipTransparentExpressionWrappers", "_fields", "_misc", "hasOwnDecorators", "node", "_node$decorators", "decorators", "length", "hasDecorators", "body", "some", "incrementId", "id", "idx", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "className", "scope", "type", "varId", "generateUidIdentifierBasedOnNode", "classId", "rename", "get", "replaceWith", "cloneNode", "generateLetUidIdentifier", "parent", "newClassExpr", "classExpression", "superClass", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "assignIdForAnonymousClass", "generateUidIdentifier", "addProxyAccessorsFor", "element", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isComputed", "version", "thisArg", "thisExpression", "getterBody", "blockStatement", "returnStatement", "memberExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "template", "expression", "ast", "getComputedKeyLastElement", "skipTransparentExprWrappers", "isSequenceExpression", "expressions", "getComputedKeyMemoiser", "isConstantExpression", "isIdentifier", "hasUid", "isAssignmentExpression", "left", "Error", "toString", "prependExpressionsToComputedKey", "fieldPath", "push", "maybeSequenceExpression", "appendExpressionsToComputedKey", "completion", "scopeParent", "maybeAssignment", "memoiseComputedKey", "generateUid", "expressionSequence", "completionParent", "parentPath", "pushContainer", "prependExpressionsToFieldInitializer", "initializer", "unaryExpression", "prependExpressionsToStaticBlock", "blockPath", "unshiftContainer", "prependExpressionsToConstructor", "constructorPath", "isProtoInitCallExpression", "protoInitCall", "isCallExpression", "callee", "optimizeSuperCallAndExpressions", "protoInitLocal", "mergedSuperCall", "callExpression", "splice", "isThisExpression", "insertExpressionsAfterSuperCallAndOptimize", "CallExpression", "exit", "is<PERSON><PERSON><PERSON>", "newNodes", "map", "expr", "isCompletionRecord", "skip", "ClassMethod", "kind", "createConstructorFromExpressions", "isDerivedClass", "super", "spreadElement", "restElement", "createStaticBlockFromExpressions", "staticBlock", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC_OLD_VERSION", "STATIC", "DECORATORS_HAVE_THIS", "getElementKind", "toSortedDecoratorInfo", "info", "filter", "el", "generateDecorationList", "decoratorsThis", "decsCount", "haveOneThis", "Boolean", "decs", "i", "numericLiteral", "haveThis", "generateDecorationExprs", "decorationInfo", "arrayExpression", "flag", "decoratorsHaveThis", "decoratorsArray", "privateMethods", "extractElementLocalAssignments", "localIds", "locals", "Array", "isArray", "addCallAccessorsFor", "getId", "setId", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "staticBlockToFunctionClosure", "functionExpression", "fieldInitializerToClosure", "exprs", "createFunctionExpressionFromPrivateMethod", "isGenerator", "async", "isAsync", "createSetFunctionNameCall", "state", "addHelper", "createToPropertyKeyCall", "propertyKey", "createPrivateBrandCheckClosure", "brandName", "binaryExpression", "usesPrivateField", "traverseFast", "isPrivateName", "_unused", "convertToComputedKey", "computed", "stringLiteral", "hasInstancePrivateAccess", "containsInstancePrivateAccess", "privateNameVisitor", "privateNameVisitorFactory", "privateNamesMap", "stop", "Map", "set", "checkPrivateMethodUpdateError", "decoratedPrivateMethods", "parentParentPath", "buildCodeFrameError", "transformClass", "constant<PERSON>uper", "ignoreFunctionLength", "propertyVisitor", "_path$node$id", "classDecorators", "hasElementDecorators", "hasComputedKeysSideEffects", "elemDecsUseFnContext", "generateClassPrivateUid", "classAssignments", "memoiseExpression", "hint", "assignments", "localEvaluatedId", "staticInitLocal", "classIdName", "setClassName", "usesFunctionContextOrYieldAwait", "decorator", "isYieldExpression", "isAwaitExpression", "isMetaProperty", "meta", "_unused2", "instancePrivateNames", "elementNode", "static", "isDecorated", "ClassProperty", "ClassPrivateProperty", "ClassAccessorProperty", "newId", "newField", "keyP<PERSON>", "elementDecoratorInfo", "classInitLocal", "classIdLocal", "decoratorReceiverId", "handleDecorators", "hasSideEffects", "usesFnContext", "object", "isMemberExpression", "willExtractSomeElemDecs", "needsDeclaraionForClassBinding", "classDecorationsFlag", "classDecorations", "classDecorationsId", "computedKeyAssignments", "isClassDeclaration", "classDecsUsePrivateName", "isClassProperty", "lastInstancePrivateName", "needsInstancePrivateBrandCheck", "fieldInitializerExpressions", "staticFieldInitializerExpressions", "isStaticBlock", "isPrivate", "isClassPrivateProperty", "isClassMethod", "nameExpr", "newFieldInitId", "newValue", "initId", "valuePath", "args", "callId", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "remove", "getNextSibling", "initExtraId", "initExtraCall", "elements", "lastComputedElement", "sortedElementDecoratorInfo", "elementDecorations", "elementLocals", "classLocals", "classInitInjected", "classInitCall", "originalClassPath", "originalClass", "staticClosures", "statics", "for<PERSON>ach", "staticBlockClosureId", "fieldValueClosureId", "isClassPrivateMethod", "privateMethodDelegateId", "p", "isRestElement", "staticsClass", "toExpression", "constructorBody", "newExpr", "newExpression", "arguments", "maybeGenerateMemoised", "applyDecoratorWrapper", "applyDecsBody", "firstPublicElement", "createLocalsAssignment", "insertBefore", "classBindingInfo", "getBinding", "constantViolations", "variableDeclaration", "variableDeclarator", "classOuterBindingDelegateLocal", "classOuterBindingLocal", "replaceWithMultiple", "size", "crawl", "maybePrivateBrandName", "lhs", "rhs", "availableHelper", "arrayPattern", "objectPattern", "objectProperty", "isProtoKey", "shouldTransformElement", "shouldTransformClass", "NamedEvaluationVisitoryFactory", "isAnonymous", "visitor", "handleComputedProperty", "propertyPath", "keyValue", "ref", "VariableDeclarator", "AssignmentExpression", "operator", "AssignmentPattern", "ObjectExpression", "isObjectProperty", "isDecoratedAnonymousClassExpression", "isClassExpression", "_default", "assertVersion", "assumption", "loose", "inherits", "_assumption", "_assumption2", "VISITED", "WeakSet", "namedEvaluationVisitor", "visitClass", "_node$id", "Object", "assign", "ExportDefaultDeclaration", "declaration", "_path$splitExportDecl", "splitExportDeclaration", "NodePath", "prototype", "updatedVarDeclarationPath", "ExportNamedDeclaration", "_path$splitExportDecl2", "Class"], "sources": ["../src/decorators.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, Scope, Visitor } from \"@babel/core\";\nimport { types as t, template } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport {\n  privateNameVisitorFactory,\n  type PrivateNameVisitorState,\n} from \"./fields.ts\";\nimport { memoiseComputedKey } from \"./misc.ts\";\n\nexport function hasOwnDecorators(node: t.Class | t.ClassBody[\"body\"][number]) {\n  // @ts-expect-error: 'decorators' not in TSIndexSignature\n  return !!node.decorators?.length;\n}\n\nexport function hasDecorators(node: t.Class) {\n  return hasOwnDecorators(node) || node.body.body.some(hasOwnDecorators);\n}\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\ninterface Options {\n  /** @deprecated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.StaticBlock;\n\ntype ClassElementCanHaveComputedKeys =\n  | t.ClassMethod\n  | t.ClassProperty\n  | t.ClassAccessorProperty;\n\n// TODO(Babel 8): Only keep 2023-11\nexport type DecoratorVersionKind =\n  | \"2023-11\"\n  | \"2023-05\"\n  | \"2023-01\"\n  | \"2022-03\"\n  | \"2021-12\";\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and the desired class name if anonymous and\n * replaces it with an equivalent class declaration (path) which is then\n * assigned to a local variable (id). This allows us to reassign the local variable with the\n * decorated version of the class. The class definition retains its original\n * name so that `toString` is not affected, other references to the class\n * are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n): {\n  id: t.Identifier;\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>;\n} {\n  const id = path.node.id;\n  const scope = path.scope;\n  if (path.type === \"ClassDeclaration\") {\n    const className = id.name;\n    const varId = scope.generateUidIdentifierBasedOnNode(id);\n    const classId = t.identifier(className);\n\n    scope.rename(className, varId.name);\n\n    path.get(\"id\").replaceWith(classId);\n\n    return { id: t.cloneNode(varId), path };\n  } else {\n    let varId: t.Identifier;\n\n    if (id) {\n      className = id.name;\n      varId = generateLetUidIdentifier(scope.parent, className);\n      scope.rename(className, varId.name);\n    } else {\n      varId = generateLetUidIdentifier(\n        scope.parent,\n        typeof className === \"string\" ? className : \"decorated_class\",\n      );\n    }\n\n    const newClassExpr = t.classExpression(\n      typeof className === \"string\" ? t.identifier(className) : null,\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return {\n      id: t.cloneNode(varId),\n      path: newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    };\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction assignIdForAnonymousClass(\n  path: NodePath<t.Class>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n) {\n  if (!path.node.id) {\n    path.node.id =\n      typeof className === \"string\"\n        ? t.identifier(className)\n        : path.scope.generateUidIdentifier(\"Class\");\n  }\n}\n\nfunction addProxyAccessorsFor(\n  className: t.Identifier,\n  element: NodePath<ClassDecoratableElement>,\n  getterKey: t.PrivateName | t.Expression,\n  setterKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  isComputed: boolean,\n  isStatic: boolean,\n  version: DecoratorVersionKind,\n): void {\n  const thisArg =\n    (version === \"2023-11\" ||\n      (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n    isStatic\n      ? className\n      : t.thisExpression();\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (getterKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\"get\", getterKey, [], getterBody, isStatic);\n    setter = t.classPrivateMethod(\n      \"set\",\n      setterKey as t.PrivateName,\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      getterKey,\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      setterKey as t.Expression,\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n): (t.FunctionExpression | t.ArrowFunctionExpression)[] {\n  if (version !== \"2023-11\" && version !== \"2023-05\" && version !== \"2023-01\") {\n    return [\n      template.expression.ast`\n        function () {\n          return this.${t.cloneNode(targetKey)};\n        }\n      ` as t.FunctionExpression,\n      template.expression.ast`\n        function (value) {\n          this.${t.cloneNode(targetKey)} = value;\n        }\n      ` as t.FunctionExpression,\n    ];\n  }\n  return [\n    template.expression.ast`\n      o => o.${t.cloneNode(targetKey)}\n    ` as t.ArrowFunctionExpression,\n    template.expression.ast`\n      (o, v) => o.${t.cloneNode(targetKey)} = v\n    ` as t.ArrowFunctionExpression,\n  ];\n}\n\n/**\n * Get the last element for the given computed key path.\n *\n * This function unwraps transparent wrappers and gets the last item when\n * the key is a SequenceExpression.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element\n * @returns {NodePath<t.Expression>} The simple completion result\n */\nfunction getComputedKeyLastElement(\n  path: NodePath<t.Expression>,\n): NodePath<t.Expression> {\n  path = skipTransparentExprWrappers(path);\n  if (path.isSequenceExpression()) {\n    const expressions = path.get(\"expressions\");\n    return getComputedKeyLastElement(expressions[expressions.length - 1]);\n  }\n  return path;\n}\n\n/**\n * Get a memoiser of the computed key path.\n *\n * This function does not mutate AST. If the computed key is not a constant\n * expression, this function must be called after the key has been memoised.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element.\n * @returns {t.Expression} A clone of key if key is a constant expression,\n * otherwise a memoiser identifier.\n */\nfunction getComputedKeyMemoiser(path: NodePath<t.Expression>): t.Expression {\n  const element = getComputedKeyLastElement(path);\n  if (element.isConstantExpression()) {\n    return t.cloneNode(path.node);\n  } else if (element.isIdentifier() && path.scope.hasUid(element.node.name)) {\n    return t.cloneNode(path.node);\n  } else if (\n    element.isAssignmentExpression() &&\n    element.get(\"left\").isIdentifier()\n  ) {\n    return t.cloneNode(element.node.left as t.Identifier);\n  } else {\n    throw new Error(\n      `Internal Error: the computed key ${path.toString()} has not yet been memoised.`,\n    );\n  }\n}\n\n/**\n * Prepend expressions to the computed key of the given field path.\n *\n * If the computed key is a sequence expression, this function will unwrap\n * the sequence expression for optimal output size.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  if (key.isSequenceExpression()) {\n    expressions.push(...key.node.expressions);\n  } else {\n    expressions.push(key.node);\n  }\n  key.replaceWith(maybeSequenceExpression(expressions));\n}\n\n/**\n * Append expressions to the computed key of the given field path.\n *\n * If the computed key is a constant expression or uid reference, it\n * will prepend expressions before the comptued key. Otherwise it will\n * memoise the computed key to preserve its completion result.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction appendExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  const completion = getComputedKeyLastElement(key);\n  if (completion.isConstantExpression()) {\n    prependExpressionsToComputedKey(expressions, fieldPath);\n  } else {\n    const scopeParent = key.scope.parent;\n    const maybeAssignment = memoiseComputedKey(\n      completion.node,\n      scopeParent,\n      scopeParent.generateUid(\"computedKey\"),\n    );\n    if (!maybeAssignment) {\n      // If the memoiseComputedKey returns undefined, the key is already a uid reference,\n      // treat it as a constant expression and prepend expressions before it\n      prependExpressionsToComputedKey(expressions, fieldPath);\n    } else {\n      const expressionSequence = [\n        ...expressions,\n        // preserve the completion result\n        t.cloneNode(maybeAssignment.left),\n      ];\n      const completionParent = completion.parentPath;\n      if (completionParent.isSequenceExpression()) {\n        completionParent.pushContainer(\"expressions\", expressionSequence);\n      } else {\n        completion.replaceWith(\n          maybeSequenceExpression([\n            t.cloneNode(maybeAssignment),\n            ...expressionSequence,\n          ]),\n        );\n      }\n    }\n  }\n}\n\n/**\n * Prepend expressions to the field initializer. If the initializer is not defined,\n * this function will wrap the last expression within a `void` unary expression.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToFieldInitializer(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n  >,\n) {\n  const initializer = fieldPath.get(\"value\");\n  if (initializer.node) {\n    expressions.push(initializer.node);\n  } else if (expressions.length > 0) {\n    expressions[expressions.length - 1] = t.unaryExpression(\n      \"void\",\n      expressions[expressions.length - 1],\n    );\n  }\n  initializer.replaceWith(maybeSequenceExpression(expressions));\n}\n\nfunction prependExpressionsToStaticBlock(\n  expressions: t.Expression[],\n  blockPath: NodePath<t.StaticBlock>,\n) {\n  blockPath.unshiftContainer(\n    \"body\",\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction prependExpressionsToConstructor(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n) {\n  constructorPath.node.body.body.unshift(\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction isProtoInitCallExpression(\n  expression: t.Expression,\n  protoInitCall: t.Identifier,\n) {\n  return (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee, { name: protoInitCall.name })\n  );\n}\n\n/**\n * Optimize super call and its following expressions\n *\n * @param {t.Expression[]} expressions Mutated by this function. The first element must by a super call\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns optimized expression\n */\nfunction optimizeSuperCallAndExpressions(\n  expressions: t.Expression[],\n  protoInitLocal: t.Identifier,\n) {\n  if (protoInitLocal) {\n    if (\n      expressions.length >= 2 &&\n      isProtoInitCallExpression(expressions[1], protoInitLocal)\n    ) {\n      // Merge `super(), protoInit(this)` into `protoInit(super())`\n      const mergedSuperCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        expressions[0],\n      ]);\n      expressions.splice(0, 2, mergedSuperCall);\n    }\n    // Merge `protoInit(super()), this` into `protoInit(super())`\n    if (\n      expressions.length >= 2 &&\n      t.isThisExpression(expressions[expressions.length - 1]) &&\n      isProtoInitCallExpression(\n        expressions[expressions.length - 2],\n        protoInitLocal,\n      )\n    ) {\n      expressions.splice(expressions.length - 1, 1);\n    }\n  }\n  return maybeSequenceExpression(expressions);\n}\n\n/**\n * Insert expressions immediately after super() and optimize the output if possible.\n * This function will preserve the completion result using the trailing this expression.\n *\n * @param {t.Expression[]} expressions\n * @param {NodePath<t.ClassMethod>} constructorPath\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns\n */\nfunction insertExpressionsAfterSuperCallAndOptimize(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n  protoInitLocal: t.Identifier,\n) {\n  constructorPath.traverse({\n    CallExpression: {\n      exit(path) {\n        if (!path.get(\"callee\").isSuper()) return;\n        const newNodes = [\n          path.node,\n          ...expressions.map(expr => t.cloneNode(expr)),\n        ];\n        // preserve completion result if super() is in an RHS or a return statement\n        if (path.isCompletionRecord()) {\n          newNodes.push(t.thisExpression());\n        }\n        path.replaceWith(\n          optimizeSuperCallAndExpressions(newNodes, protoInitLocal),\n        );\n\n        path.skip();\n      },\n    },\n    ClassMethod(path) {\n      if (path.node.kind === \"constructor\") {\n        path.skip();\n      }\n    },\n  });\n}\n\n/**\n * Build a class constructor node from the given expressions. If the class is\n * derived, the constructor will call super() first to ensure that `this`\n * in the expressions work as expected.\n *\n * @param {t.Expression[]} expressions\n * @param {boolean} isDerivedClass\n * @returns The class constructor node\n */\nfunction createConstructorFromExpressions(\n  expressions: t.Expression[],\n  isDerivedClass: boolean,\n) {\n  const body: t.Statement[] = [\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ];\n  if (isDerivedClass) {\n    body.unshift(\n      t.expressionStatement(\n        t.callExpression(t.super(), [t.spreadElement(t.identifier(\"args\"))]),\n      ),\n    );\n  }\n  return t.classMethod(\n    \"constructor\",\n    t.identifier(\"constructor\"),\n    isDerivedClass ? [t.restElement(t.identifier(\"args\"))] : [],\n    t.blockStatement(body),\n  );\n}\n\nfunction createStaticBlockFromExpressions(expressions: t.Expression[]) {\n  return t.staticBlock([\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ]);\n}\n\n// 3 bits reserved to this (0-7)\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC_OLD_VERSION = 5; // Before 2023-05\nconst STATIC = 8; // 1 << 3\nconst DECORATORS_HAVE_THIS = 16; // 1 << 4\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // An array of applied decorators or a memoised identifier\n  decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n  decoratorsHaveThis: boolean;\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods:\n    | (t.FunctionExpression | t.ArrowFunctionExpression)[]\n    | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n/**\n * Sort decoration info in the application order:\n * - static non-fields\n * - instance non-fields\n * - static fields\n * - instance fields\n *\n * @param {DecoratorInfo[]} info\n * @returns {DecoratorInfo[]} Sorted decoration info\n */\nfunction toSortedDecoratorInfo(info: DecoratorInfo[]): DecoratorInfo[] {\n  return [\n    ...info.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(el => el.isStatic && el.kind === FIELD),\n    ...info.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\ntype GenerateDecorationListResult = {\n  // The zipped decorators array that will be passed to generateDecorationExprs\n  decs: t.Expression[];\n  // Whether there are non-empty decorator this values\n  haveThis: boolean;\n};\n/**\n * Zip decorators and decorator this values into an array\n *\n * @param {t.Decorator[]} decorators\n * @param {((t.Expression | undefined)[])} decoratorsThis decorator this values\n * @param {DecoratorVersionKind} version\n * @returns {GenerateDecorationListResult}\n */\nfunction generateDecorationList(\n  decorators: t.Decorator[],\n  decoratorsThis: (t.Expression | undefined)[],\n  version: DecoratorVersionKind,\n): GenerateDecorationListResult {\n  const decsCount = decorators.length;\n  const haveOneThis = decoratorsThis.some(Boolean);\n  const decs: t.Expression[] = [];\n  for (let i = 0; i < decsCount; i++) {\n    if (\n      (version === \"2023-11\" ||\n        (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n      haveOneThis\n    ) {\n      decs.push(\n        decoratorsThis[i] || t.unaryExpression(\"void\", t.numericLiteral(0)),\n      );\n    }\n    decs.push(decorators[i].expression);\n  }\n\n  return { haveThis: haveOneThis, decs };\n}\n\nfunction generateDecorationExprs(\n  decorationInfo: DecoratorInfo[],\n  version: DecoratorVersionKind,\n): t.ArrayExpression {\n  return t.arrayExpression(\n    decorationInfo.map(el => {\n      let flag = el.kind;\n      if (el.isStatic) {\n        flag +=\n          version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")\n            ? STATIC\n            : STATIC_OLD_VERSION;\n      }\n      if (el.decoratorsHaveThis) flag += DECORATORS_HAVE_THIS;\n\n      return t.arrayExpression([\n        el.decoratorsArray,\n        t.numericLiteral(flag),\n        el.name,\n        ...(el.privateMethods || []),\n      ]);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(decorationInfo: DecoratorInfo[]) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of decorationInfo) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  version: DecoratorVersionKind,\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n  isStatic: boolean,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(\n            t.cloneNode(getId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(\n            t.cloneNode(setId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? [t.identifier(\"v\")]\n              : [t.thisExpression(), t.identifier(\"v\")],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction staticBlockToFunctionClosure(block: t.StaticBlock) {\n  return t.functionExpression(null, [], t.blockStatement(block.body));\n}\n\nfunction fieldInitializerToClosure(value: t.Expression) {\n  return t.functionExpression(\n    null,\n    [],\n    t.blockStatement([t.returnStatement(value)]),\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\n/**\n * Create FunctionExpression from a ClassPrivateMethod.\n * The returned FunctionExpression node takes ownership of the private method's body and params.\n *\n * @param {t.ClassPrivateMethod} node\n * @returns\n */\nfunction createFunctionExpressionFromPrivateMethod(node: t.ClassPrivateMethod) {\n  const { params, body, generator: isGenerator, async: isAsync } = node;\n  return t.functionExpression(\n    undefined,\n    // @ts-expect-error todo: Improve typings: TSParameterProperty is only allowed in constructor\n    params,\n    body,\n    isGenerator,\n    isAsync,\n  );\n}\n\nfunction createSetFunctionNameCall(\n  state: PluginPass,\n  className: t.Identifier | t.StringLiteral,\n) {\n  return t.callExpression(state.addHelper(\"setFunctionName\"), [\n    t.thisExpression(),\n    className,\n  ]);\n}\n\nfunction createToPropertyKeyCall(state: PluginPass, propertyKey: t.Expression) {\n  return t.callExpression(state.addHelper(\"toPropertyKey\"), [propertyKey]);\n}\n\nfunction createPrivateBrandCheckClosure(brandName: t.PrivateName) {\n  return t.arrowFunctionExpression(\n    [t.identifier(\"_\")],\n    t.binaryExpression(\"in\", t.cloneNode(brandName), t.identifier(\"_\")),\n  );\n}\n\nfunction usesPrivateField(expression: t.Node) {\n  if (process.env.BABEL_8_BREAKING) {\n    return t.traverseFast(expression, node => {\n      if (t.isPrivateName(node)) {\n        return t.traverseFast.stop;\n      }\n    });\n  } else {\n    try {\n      t.traverseFast(expression, node => {\n        if (t.isPrivateName(node)) {\n          // eslint-disable-next-line @typescript-eslint/only-throw-error\n          throw null;\n        }\n      });\n      return false;\n    } catch {\n      return true;\n    }\n  }\n}\n\n/**\n * Convert a non-computed class element to its equivalent computed form.\n *\n * This function is to provide a decorator evaluation storage from non-computed\n * class elements.\n *\n * @param {(NodePath<t.ClassProperty | t.ClassMethod>)} path A non-computed class property or method\n */\nfunction convertToComputedKey(path: NodePath<t.ClassProperty | t.ClassMethod>) {\n  const { node } = path;\n  node.computed = true;\n  if (t.isIdentifier(node.key)) {\n    node.key = t.stringLiteral(node.key.name);\n  }\n}\n\nfunction hasInstancePrivateAccess(path: NodePath, privateNames: string[]) {\n  let containsInstancePrivateAccess = false;\n  if (privateNames.length > 0) {\n    const privateNameVisitor = privateNameVisitorFactory<\n      PrivateNameVisitorState<null>,\n      null\n    >({\n      PrivateName(path, state) {\n        if (state.privateNamesMap.has(path.node.id.name)) {\n          containsInstancePrivateAccess = true;\n          path.stop();\n        }\n      },\n    });\n    const privateNamesMap = new Map<string, null>();\n    for (const name of privateNames) {\n      privateNamesMap.set(name, null);\n    }\n    path.traverse(privateNameVisitor, {\n      privateNamesMap: privateNamesMap,\n    });\n  }\n  return containsInstancePrivateAccess;\n}\n\nfunction checkPrivateMethodUpdateError(\n  path: NodePath<t.Class>,\n  decoratedPrivateMethods: Set<string>,\n) {\n  const privateNameVisitor = privateNameVisitorFactory<\n    PrivateNameVisitorState<null>,\n    null\n  >({\n    PrivateName(path, state) {\n      if (!state.privateNamesMap.has(path.node.id.name)) return;\n\n      const parentPath = path.parentPath;\n      const parentParentPath = parentPath.parentPath;\n\n      if (\n        // this.bar().#x = 123;\n        (parentParentPath.node.type === \"AssignmentExpression\" &&\n          parentParentPath.node.left === parentPath.node) ||\n        // this.#x++;\n        parentParentPath.node.type === \"UpdateExpression\" ||\n        // ([...this.#x] = foo);\n        parentParentPath.node.type === \"RestElement\" ||\n        // ([this.#x] = foo);\n        parentParentPath.node.type === \"ArrayPattern\" ||\n        // ({ a: this.#x } = bar);\n        (parentParentPath.node.type === \"ObjectProperty\" &&\n          parentParentPath.node.value === parentPath.node &&\n          parentParentPath.parentPath.type === \"ObjectPattern\") ||\n        // for (this.#x of []);\n        (parentParentPath.node.type === \"ForOfStatement\" &&\n          parentParentPath.node.left === parentPath.node)\n      ) {\n        throw path.buildCodeFrameError(\n          `Decorated private methods are read-only, but \"#${path.node.id.name}\" is updated via this expression.`,\n        );\n      }\n    },\n  });\n  const privateNamesMap = new Map<string, null>();\n  for (const name of decoratedPrivateMethods) {\n    privateNamesMap.set(name, null);\n  }\n  path.traverse(privateNameVisitor, {\n    privateNamesMap: privateNamesMap,\n  });\n}\n\n/**\n * Apply decorator and accessor transform\n * @param path The class path.\n * @param state The plugin pass.\n * @param constantSuper The constantSuper compiler assumption.\n * @param ignoreFunctionLength The ignoreFunctionLength compiler assumption.\n * @param className The class name.\n * - If className is a `string`, it will be a valid identifier name that can safely serve as a class id\n * - If className is an Identifier, it is the reference to the name derived from NamedEvaluation\n * - If className is a StringLiteral, it is derived from NamedEvaluation on literal computed keys\n * @param propertyVisitor The visitor that should be applied on property prior to the transform.\n * @param version The decorator version.\n * @returns The transformed class path or undefined if there are no decorators.\n */\nfunction transformClass(\n  path: NodePath<t.Class>,\n  state: PluginPass,\n  constantSuper: boolean,\n  ignoreFunctionLength: boolean,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n  propertyVisitor: Visitor<PluginPass>,\n  version: DecoratorVersionKind,\n): NodePath | undefined {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n  let hasComputedKeysSideEffects = false;\n  let elemDecsUseFnContext = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  const classAssignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n  const memoiseExpression = (\n    expression: t.Expression,\n    hint: string,\n    assignments: t.AssignmentExpression[],\n  ) => {\n    const localEvaluatedId = generateLetUidIdentifier(scopeParent, hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  let protoInitLocal: t.Identifier;\n  let staticInitLocal: t.Identifier;\n  const classIdName = path.node.id?.name;\n  // Whether to generate a setFunctionName call to preserve the class name\n  const setClassName = typeof className === \"object\" ? className : undefined;\n  // Check if the decorator does not reference function-specific\n  // context or the given identifier name or contains yield or await expression.\n  // `true` means \"maybe\" and `false` means \"no\".\n  const usesFunctionContextOrYieldAwait = (decorator: t.Decorator) => {\n    if (process.env.BABEL_8_BREAKING) {\n      return t.traverseFast(decorator, node => {\n        if (\n          t.isThisExpression(node) ||\n          t.isSuper(node) ||\n          t.isYieldExpression(node) ||\n          t.isAwaitExpression(node) ||\n          t.isIdentifier(node, { name: \"arguments\" }) ||\n          (classIdName && t.isIdentifier(node, { name: classIdName })) ||\n          (t.isMetaProperty(node) && node.meta.name !== \"import\")\n        ) {\n          return t.traverseFast.stop;\n        }\n      });\n    } else {\n      try {\n        t.traverseFast(decorator, node => {\n          if (\n            t.isThisExpression(node) ||\n            t.isSuper(node) ||\n            t.isYieldExpression(node) ||\n            t.isAwaitExpression(node) ||\n            t.isIdentifier(node, { name: \"arguments\" }) ||\n            (classIdName && t.isIdentifier(node, { name: classIdName })) ||\n            (t.isMetaProperty(node) && node.meta.name !== \"import\")\n          ) {\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null;\n          }\n        });\n        return false;\n      } catch {\n        return true;\n      }\n    }\n  };\n\n  const instancePrivateNames: string[] = [];\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated, and handle inferred\n  // class name when the initializer of the class field is a class expression\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    const elementNode = element.node;\n\n    if (!elementNode.static && t.isPrivateName(elementNode.key)) {\n      instancePrivateNames.push(elementNode.key.id.name);\n    }\n\n    if (isDecorated(elementNode)) {\n      switch (elementNode.type) {\n        case \"ClassProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassProperty should be callable. Improve typings.\n          propertyVisitor.ClassProperty(\n            element as NodePath<t.ClassProperty>,\n            state,\n          );\n          break;\n        case \"ClassPrivateProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassPrivateProperty should be callable. Improve typings.\n          propertyVisitor.ClassPrivateProperty(\n            element as NodePath<t.ClassPrivateProperty>,\n            state,\n          );\n          break;\n        case \"ClassAccessorProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n          propertyVisitor.ClassAccessorProperty(\n            element as NodePath<t.ClassAccessorProperty>,\n            state,\n          );\n          if (version === \"2023-11\") {\n            break;\n          }\n        /* fallthrough */\n        default:\n          if (elementNode.static) {\n            staticInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initStatic\",\n            );\n          } else {\n            protoInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initProto\",\n            );\n          }\n          break;\n      }\n      hasElementDecorators = true;\n      elemDecsUseFnContext ||= elementNode.decorators.some(\n        usesFunctionContextOrYieldAwait,\n      );\n    } else if (elementNode.type === \"ClassAccessorProperty\") {\n      // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n      propertyVisitor.ClassAccessorProperty(\n        element as NodePath<t.ClassAccessorProperty>,\n        state,\n      );\n      const { key, value, static: isStatic, computed } = elementNode;\n\n      const newId = generateClassPrivateUid();\n      const newField = generateClassProperty(newId, value, isStatic);\n      const keyPath = element.get(\"key\");\n      const [newPath] = element.replaceWith(newField);\n\n      let getterKey, setterKey;\n      if (computed && !keyPath.isConstantExpression()) {\n        getterKey = memoiseComputedKey(\n          createToPropertyKeyCall(state, key as t.Expression),\n          scopeParent,\n          scopeParent.generateUid(\"computedKey\"),\n        )!;\n        setterKey = t.cloneNode(getterKey.left as t.Identifier);\n      } else {\n        getterKey = t.cloneNode(key);\n        setterKey = t.cloneNode(key);\n      }\n\n      assignIdForAnonymousClass(path, className);\n\n      addProxyAccessorsFor(\n        path.node.id,\n        newPath,\n        getterKey,\n        setterKey,\n        newId,\n        computed,\n        isStatic,\n        version,\n      );\n    }\n\n    if (\"computed\" in element.node && element.node.computed) {\n      hasComputedKeysSideEffects ||= !scopeParent.isStatic(element.node.key);\n    }\n  }\n\n  if (!classDecorators && !hasElementDecorators) {\n    if (!path.node.id && typeof className === \"string\") {\n      path.node.id = t.identifier(className);\n    }\n    if (setClassName) {\n      path.node.body.body.unshift(\n        createStaticBlockFromExpressions([\n          createSetFunctionNameCall(state, setClassName),\n        ]),\n      );\n    }\n    // If nothing is decorated and no assignments inserted, return\n    return;\n  }\n\n  const elementDecoratorInfo: DecoratorInfo[] = [];\n\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let classInitLocal: t.Identifier, classIdLocal: t.Identifier;\n  let decoratorReceiverId: t.Identifier | null = null;\n\n  // Memoise the this value `a.b` of decorator member expressions `@a.b.dec`,\n  type HandleDecoratorsResult = {\n    // whether the whole decorator list requires memoisation\n    hasSideEffects: boolean;\n    usesFnContext: boolean;\n    // the this value of each decorator if applicable\n    decoratorsThis: (t.Expression | undefined)[];\n  };\n  function handleDecorators(decorators: t.Decorator[]): HandleDecoratorsResult {\n    let hasSideEffects = false;\n    let usesFnContext = false;\n    const decoratorsThis: (t.Expression | null)[] = [];\n    for (const decorator of decorators) {\n      const { expression } = decorator;\n      let object;\n      if (\n        (version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n        t.isMemberExpression(expression)\n      ) {\n        if (t.isSuper(expression.object)) {\n          object = t.thisExpression();\n        } else if (scopeParent.isStatic(expression.object)) {\n          object = t.cloneNode(expression.object);\n        } else {\n          decoratorReceiverId ??= generateLetUidIdentifier(scopeParent, \"obj\");\n          object = t.assignmentExpression(\n            \"=\",\n            t.cloneNode(decoratorReceiverId),\n            expression.object,\n          );\n          expression.object = t.cloneNode(decoratorReceiverId);\n        }\n      }\n      decoratorsThis.push(object);\n      hasSideEffects ||= !scopeParent.isStatic(expression);\n      usesFnContext ||= usesFunctionContextOrYieldAwait(decorator);\n    }\n    return { hasSideEffects, usesFnContext, decoratorsThis };\n  }\n\n  const willExtractSomeElemDecs =\n    hasComputedKeysSideEffects ||\n    (process.env.BABEL_8_BREAKING\n      ? elemDecsUseFnContext\n      : elemDecsUseFnContext || version !== \"2023-11\");\n\n  let needsDeclaraionForClassBinding = false;\n  let classDecorationsFlag = 0;\n  let classDecorations: t.Expression[] = [];\n  let classDecorationsId: t.Identifier;\n  let computedKeyAssignments: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classInitLocal = generateLetUidIdentifier(scopeParent, \"initClass\");\n    needsDeclaraionForClassBinding = path.isClassDeclaration();\n    ({ id: classIdLocal, path } = replaceClassWithVar(path, className));\n\n    path.node.decorators = null;\n\n    const classDecsUsePrivateName = classDecorators.some(usesPrivateField);\n    const { hasSideEffects, usesFnContext, decoratorsThis } =\n      handleDecorators(classDecorators);\n\n    const { haveThis, decs } = generateDecorationList(\n      classDecorators,\n      decoratorsThis,\n      version,\n    );\n    classDecorationsFlag = haveThis ? 1 : 0;\n    classDecorations = decs;\n\n    if (\n      usesFnContext ||\n      (hasSideEffects && willExtractSomeElemDecs) ||\n      classDecsUsePrivateName\n    ) {\n      classDecorationsId = memoiseExpression(\n        t.arrayExpression(classDecorations),\n        \"classDecs\",\n        classAssignments,\n      );\n    }\n\n    if (!hasElementDecorators) {\n      // Sync body paths as non-decorated computed accessors have been transpiled\n      // to getter-setter pairs.\n      for (const element of path.get(\"body.body\")) {\n        const { node } = element;\n        const isComputed = \"computed\" in node && node.computed;\n        if (isComputed) {\n          if (element.isClassProperty({ static: true })) {\n            if (!element.get(\"key\").isConstantExpression()) {\n              const key = (node as t.ClassProperty).key;\n              const maybeAssignment = memoiseComputedKey(\n                key,\n                scopeParent,\n                scopeParent.generateUid(\"computedKey\"),\n              );\n              if (maybeAssignment != null) {\n                // If it is a static computed field within a decorated class, we move the computed key\n                // into `computedKeyAssignments` which will be then moved into the non-static class,\n                // to ensure that the evaluation order and private environment are correct\n                node.key = t.cloneNode(maybeAssignment.left);\n                computedKeyAssignments.push(maybeAssignment);\n              }\n            }\n          } else if (computedKeyAssignments.length > 0) {\n            prependExpressionsToComputedKey(\n              computedKeyAssignments,\n              element as NodePath<ClassElementCanHaveComputedKeys>,\n            );\n            computedKeyAssignments = [];\n          }\n        }\n      }\n    }\n  } else {\n    assignIdForAnonymousClass(path, className);\n    classIdLocal = t.cloneNode(path.node.id);\n  }\n\n  let lastInstancePrivateName: t.PrivateName;\n  let needsInstancePrivateBrandCheck = false;\n\n  let fieldInitializerExpressions = [];\n  let staticFieldInitializerExpressions: t.Expression[] = [];\n\n  if (hasElementDecorators) {\n    if (protoInitLocal) {\n      const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        t.thisExpression(),\n      ]);\n      fieldInitializerExpressions.push(protoInitCall);\n    }\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        if (\n          staticFieldInitializerExpressions.length > 0 &&\n          element.isStaticBlock()\n        ) {\n          prependExpressionsToStaticBlock(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = node.decorators;\n\n      const hasDecorators = !!decorators?.length;\n\n      const isComputed = \"computed\" in node && node.computed;\n\n      let name = \"computedKey\";\n\n      if (node.key.type === \"PrivateName\") {\n        name = node.key.id.name;\n      } else if (!isComputed && node.key.type === \"Identifier\") {\n        name = node.key.name;\n      }\n      let decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n      let decoratorsHaveThis;\n\n      if (hasDecorators) {\n        const { hasSideEffects, usesFnContext, decoratorsThis } =\n          handleDecorators(decorators);\n        const { decs, haveThis } = generateDecorationList(\n          decorators,\n          decoratorsThis,\n          version,\n        );\n        decoratorsHaveThis = haveThis;\n        decoratorsArray = decs.length === 1 ? decs[0] : t.arrayExpression(decs);\n        if (usesFnContext || (hasSideEffects && willExtractSomeElemDecs)) {\n          decoratorsArray = memoiseExpression(\n            decoratorsArray,\n            name + \"Decs\",\n            computedKeyAssignments,\n          );\n        }\n      }\n\n      if (isComputed) {\n        if (!element.get(\"key\").isConstantExpression()) {\n          const key = node.key as t.Expression;\n          const maybeAssignment = memoiseComputedKey(\n            hasDecorators ? createToPropertyKeyCall(state, key) : key,\n            scopeParent,\n            scopeParent.generateUid(\"computedKey\"),\n          );\n          if (maybeAssignment != null) {\n            // If it is a static computed field within a decorated class, we move the computed key\n            // into `computedKeyAssignments` which will be then moved into the non-static class,\n            // to ensure that the evaluation order and private environment are correct\n            if (classDecorators && element.isClassProperty({ static: true })) {\n              node.key = t.cloneNode(maybeAssignment.left);\n              computedKeyAssignments.push(maybeAssignment);\n            } else {\n              node.key = maybeAssignment;\n            }\n          }\n        }\n      }\n\n      const { key, static: isStatic } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const kind = getElementKind(element);\n\n      if (isPrivate && !isStatic) {\n        if (hasDecorators) {\n          needsInstancePrivateBrandCheck = true;\n        }\n        if (t.isClassPrivateProperty(node) || !lastInstancePrivateName) {\n          lastInstancePrivateName = key;\n        }\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      let locals: t.Identifier[];\n      if (hasDecorators) {\n        let privateMethods: Array<\n          t.FunctionExpression | t.ArrowFunctionExpression\n        >;\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = getComputedKeyMemoiser(\n            element.get(\"key\") as NodePath<t.Expression>,\n          );\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId = generateLetUidIdentifier(\n            scopeParent,\n            `init_${name}`,\n          );\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId, version);\n\n            const getId = generateLetUidIdentifier(scopeParent, `get_${name}`);\n            const setId = generateLetUidIdentifier(scopeParent, `set_${name}`);\n\n            addCallAccessorsFor(version, newPath, key, getId, setId, isStatic);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            assignIdForAnonymousClass(path, className);\n            addProxyAccessorsFor(\n              path.node.id,\n              newPath,\n              t.cloneNode(key),\n              t.isAssignmentExpression(key)\n                ? t.cloneNode(key.left as t.Identifier)\n                : t.cloneNode(key),\n              newId,\n              isComputed,\n              isStatic,\n              version,\n            );\n            locals = [newFieldInitId];\n          }\n        } else if (kind === FIELD) {\n          const initId = generateLetUidIdentifier(scopeParent, `init_${name}`);\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          const args: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n          if (valuePath.node) args.push(valuePath.node);\n\n          valuePath.replaceWith(t.callExpression(t.cloneNode(initId), args));\n\n          locals = [initId];\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key, version);\n          }\n        } else if (isPrivate) {\n          const callId = generateLetUidIdentifier(scopeParent, `call_${name}`);\n          locals = [callId];\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          privateMethods = [\n            createFunctionExpressionFromPrivateMethod(\n              element.node as t.ClassPrivateMethod,\n            ),\n          ];\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key),\n              t.cloneNode(callId),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(key, t.cloneNode(callId), [], node.static),\n            );\n\n            decoratedPrivateMethods.add(key.id.name);\n\n            element.remove();\n          }\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decoratorsArray,\n          decoratorsHaveThis,\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n      }\n\n      if (isComputed && computedKeyAssignments.length > 0) {\n        if (classDecorators && element.isClassProperty({ static: true })) {\n          // If the class is decorated, we don't insert computedKeyAssignments here\n          // because any non-static computed elements defined after it will be moved\n          // into the non-static class, so they will be evaluated before the key of\n          // this field. At this momemnt, its key must be either a constant expression\n          // or a uid reference which has been assigned _within_ the non-static class.\n        } else {\n          prependExpressionsToComputedKey(\n            computedKeyAssignments,\n            (kind === ACCESSOR\n              ? element.getNextSibling() // the transpiled getter of the accessor property\n              : element) as NodePath<ClassElementCanHaveComputedKeys>,\n          );\n          computedKeyAssignments = [];\n        }\n      }\n\n      if (\n        fieldInitializerExpressions.length > 0 &&\n        !isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          fieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        fieldInitializerExpressions = [];\n      }\n\n      if (\n        staticFieldInitializerExpressions.length > 0 &&\n        isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          staticFieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        staticFieldInitializerExpressions = [];\n      }\n\n      if (hasDecorators && version === \"2023-11\") {\n        if (kind === FIELD || kind === ACCESSOR) {\n          const initExtraId = generateLetUidIdentifier(\n            scopeParent,\n            `init_extra_${name}`,\n          );\n          locals.push(initExtraId);\n          const initExtraCall = t.callExpression(\n            t.cloneNode(initExtraId),\n            isStatic ? [] : [t.thisExpression()],\n          );\n          if (!isStatic) {\n            fieldInitializerExpressions.push(initExtraCall);\n          } else {\n            staticFieldInitializerExpressions.push(initExtraCall);\n          }\n        }\n      }\n    }\n  }\n\n  if (computedKeyAssignments.length > 0) {\n    const elements = path.get(\"body.body\");\n    let lastComputedElement: NodePath<ClassElementCanHaveComputedKeys>;\n    for (let i = elements.length - 1; i >= 0; i--) {\n      const path = elements[i];\n      const node = path.node as ClassElementCanHaveComputedKeys;\n      if (node.computed) {\n        if (classDecorators && t.isClassProperty(node, { static: true })) {\n          continue;\n        }\n        lastComputedElement = path as NodePath<ClassElementCanHaveComputedKeys>;\n        break;\n      }\n    }\n    if (lastComputedElement != null) {\n      appendExpressionsToComputedKey(\n        computedKeyAssignments,\n        lastComputedElement,\n      );\n      computedKeyAssignments = [];\n    } else {\n      // If there is no computed key, we will try to convert the first non-computed\n      // class element into a computed key and insert assignments there. This will\n      // be done after we handle the class elements split when the class is decorated.\n    }\n  }\n\n  if (fieldInitializerExpressions.length > 0) {\n    const isDerivedClass = !!path.node.superClass;\n    if (constructorPath) {\n      if (isDerivedClass) {\n        insertExpressionsAfterSuperCallAndOptimize(\n          fieldInitializerExpressions,\n          constructorPath,\n          protoInitLocal,\n        );\n      } else {\n        prependExpressionsToConstructor(\n          fieldInitializerExpressions,\n          constructorPath,\n        );\n      }\n    } else {\n      path.node.body.body.unshift(\n        createConstructorFromExpressions(\n          fieldInitializerExpressions,\n          isDerivedClass,\n        ),\n      );\n    }\n    fieldInitializerExpressions = [];\n  }\n\n  if (staticFieldInitializerExpressions.length > 0) {\n    path.node.body.body.push(\n      createStaticBlockFromExpressions(staticFieldInitializerExpressions),\n    );\n    staticFieldInitializerExpressions = [];\n  }\n\n  const sortedElementDecoratorInfo =\n    toSortedDecoratorInfo(elementDecoratorInfo);\n\n  const elementDecorations = generateDecorationExprs(\n    process.env.BABEL_8_BREAKING || version === \"2023-11\"\n      ? elementDecoratorInfo\n      : sortedElementDecoratorInfo,\n    version,\n  );\n\n  const elementLocals: t.Identifier[] = extractElementLocalAssignments(\n    sortedElementDecoratorInfo,\n  );\n\n  if (protoInitLocal) {\n    elementLocals.push(protoInitLocal);\n  }\n\n  if (staticInitLocal) {\n    elementLocals.push(staticInitLocal);\n  }\n\n  const classLocals: t.Identifier[] = [];\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  let originalClassPath = path;\n  const originalClass = path.node;\n\n  const staticClosures: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classLocals.push(classIdLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const staticBlockClosureId = memoiseExpression(\n            staticBlockToFunctionClosure(element.node),\n            \"staticBlock\",\n            staticClosures,\n          );\n          staticFieldInitializerExpressions.push(\n            t.callExpression(\n              t.memberExpression(staticBlockClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        } else {\n          staticFieldInitializerExpressions.push(\n            staticBlockToIIFE(element.node),\n          );\n        }\n        element.remove();\n        return;\n      }\n\n      if (\n        (element.isClassProperty() || element.isClassPrivateProperty()) &&\n        element.node.static\n      ) {\n        const valuePath = (\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n        ).get(\"value\");\n        if (hasInstancePrivateAccess(valuePath, instancePrivateNames)) {\n          const fieldValueClosureId = memoiseExpression(\n            fieldInitializerToClosure(valuePath.node),\n            \"fieldValue\",\n            staticClosures,\n          );\n          valuePath.replaceWith(\n            t.callExpression(\n              t.memberExpression(fieldValueClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        }\n        if (staticFieldInitializerExpressions.length > 0) {\n          prependExpressionsToFieldInitializer(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      } else if (element.isClassPrivateMethod({ static: true })) {\n        // At this moment the element must not have decorators, so any private name\n        // within the element must come from either params or body\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          const privateMethodDelegateId = memoiseExpression(\n            createFunctionExpressionFromPrivateMethod(element.node),\n            element.get(\"key.id\").node.name,\n            staticClosures,\n          );\n\n          if (ignoreFunctionLength) {\n            element.node.params = [t.restElement(t.identifier(\"arg\"))];\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arg\")],\n                ),\n              ),\n            ]);\n          } else {\n            element.node.params = element.node.params.map((p, i) => {\n              if (t.isRestElement(p)) {\n                return t.restElement(t.identifier(\"arg\"));\n              } else {\n                return t.identifier(\"_\" + i);\n              }\n            });\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arguments\")],\n                ),\n              ),\n            ]);\n          }\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticFieldInitializerExpressions.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        // Insert the original class to a computed key of the wrapper so that\n        // 1) they share the same function context with the wrapper class\n        // 2) the memoisation of static computed field is evaluated before they\n        //    are referenced in the wrapper class keys\n        // Note that any static elements of the wrapper class can not be accessed\n        // in the user land, so we don't have to remove the temporary class field.\n        t.classProperty(\n          t.toExpression(originalClass),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticFieldInitializerExpressions.length > 0) {\n        constructorBody.push(...staticFieldInitializerExpressions);\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classIdLocal)]),\n        );\n\n        // set isDerivedClass to false as we have already prepended super call\n        staticsClass.body.body.push(\n          createConstructorFromExpressions(\n            constructorBody,\n            /* isDerivedClass */ false,\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classIdLocal));\n      }\n\n      const [newPath] = path.replaceWith(newExpr);\n\n      // update originalClassPath according to the new AST\n      originalClassPath = (\n        newPath.get(\"callee\").get(\"body\") as NodePath<t.Class>\n      ).get(\"body.0.key\");\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  let { superClass } = originalClass;\n  if (\n    superClass &&\n    (process.env.BABEL_8_BREAKING ||\n      version === \"2023-11\" ||\n      version === \"2023-05\")\n  ) {\n    const id = path.scope.maybeGenerateMemoised(superClass);\n    if (id) {\n      originalClass.superClass = t.assignmentExpression(\"=\", id, superClass);\n      superClass = id;\n    }\n  }\n\n  const applyDecoratorWrapper = t.staticBlock([]);\n  originalClass.body.body.unshift(applyDecoratorWrapper);\n  const applyDecsBody = applyDecoratorWrapper.body;\n  if (computedKeyAssignments.length > 0) {\n    const elements = originalClassPath.get(\"body.body\");\n    let firstPublicElement: NodePath<t.ClassProperty | t.ClassMethod>;\n    for (const path of elements) {\n      if (\n        (path.isClassProperty() || path.isClassMethod()) &&\n        (path.node as t.ClassMethod).kind !== \"constructor\"\n      ) {\n        firstPublicElement = path;\n        break;\n      }\n    }\n    if (firstPublicElement != null) {\n      // Convert its key to a computed one to host the decorator evaluations.\n      convertToComputedKey(firstPublicElement);\n      prependExpressionsToComputedKey(\n        computedKeyAssignments,\n        firstPublicElement,\n      );\n    } else {\n      // When there is no public class elements, we inject a temporary computed\n      // field whose key will host the decorator evaluations. The field will be\n      // deleted immediately after it is defiend.\n      originalClass.body.body.unshift(\n        t.classProperty(\n          t.sequenceExpression([\n            ...computedKeyAssignments,\n            t.stringLiteral(\"_\"),\n          ]),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n      );\n      applyDecsBody.push(\n        t.expressionStatement(\n          t.unaryExpression(\n            \"delete\",\n            t.memberExpression(t.thisExpression(), t.identifier(\"_\")),\n          ),\n        ),\n      );\n    }\n    computedKeyAssignments = [];\n  }\n\n  applyDecsBody.push(\n    t.expressionStatement(\n      createLocalsAssignment(\n        elementLocals,\n        classLocals,\n        elementDecorations,\n        classDecorationsId ?? t.arrayExpression(classDecorations),\n        t.numericLiteral(classDecorationsFlag),\n        needsInstancePrivateBrandCheck ? lastInstancePrivateName : null,\n        setClassName,\n        t.cloneNode(superClass),\n        state,\n        version,\n      ),\n    ),\n  );\n  if (staticInitLocal) {\n    applyDecsBody.push(\n      t.expressionStatement(\n        t.callExpression(t.cloneNode(staticInitLocal), [t.thisExpression()]),\n      ),\n    );\n  }\n  if (staticClosures.length > 0) {\n    applyDecsBody.push(\n      ...staticClosures.map(expr => t.expressionStatement(expr)),\n    );\n  }\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(classAssignments.map(expr => t.expressionStatement(expr)));\n\n  if (needsDeclaraionForClassBinding) {\n    const classBindingInfo = scopeParent.getBinding(classIdLocal.name);\n    if (!classBindingInfo.constantViolations.length) {\n      // optimization: reuse the inner class binding if the outer class binding is not mutated\n      path.insertBefore(\n        t.variableDeclaration(\"let\", [\n          t.variableDeclarator(t.cloneNode(classIdLocal)),\n        ]),\n      );\n    } else {\n      const classOuterBindingDelegateLocal = scopeParent.generateUidIdentifier(\n        \"t\" + classIdLocal.name,\n      );\n      const classOuterBindingLocal = classIdLocal;\n      path.replaceWithMultiple([\n        t.variableDeclaration(\"let\", [\n          t.variableDeclarator(t.cloneNode(classOuterBindingLocal)),\n          t.variableDeclarator(classOuterBindingDelegateLocal),\n        ]),\n        t.blockStatement([\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(t.cloneNode(classIdLocal)),\n          ]),\n          // needsDeclaraionForClassBinding is true ↔ node is a class declaration\n          path.node as t.ClassDeclaration,\n          t.expressionStatement(\n            t.assignmentExpression(\n              \"=\",\n              t.cloneNode(classOuterBindingDelegateLocal),\n              t.cloneNode(classIdLocal),\n            ),\n          ),\n        ]),\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.cloneNode(classOuterBindingLocal),\n            t.cloneNode(classOuterBindingDelegateLocal),\n          ),\n        ),\n      ]);\n    }\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    checkPrivateMethodUpdateError(path, decoratedPrivateMethods);\n  }\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nfunction createLocalsAssignment(\n  elementLocals: t.Identifier[],\n  classLocals: t.Identifier[],\n  elementDecorations: t.ArrayExpression | t.Identifier,\n  classDecorations: t.ArrayExpression | t.Identifier,\n  classDecorationsFlag: t.NumericLiteral,\n  maybePrivateBrandName: t.PrivateName | null,\n  setClassName: t.Identifier | t.StringLiteral | undefined,\n  superClass: null | t.Expression,\n  state: PluginPass,\n  version: DecoratorVersionKind,\n) {\n  let lhs, rhs;\n  const args: t.Expression[] = [\n    setClassName\n      ? createSetFunctionNameCall(state, setClassName)\n      : t.thisExpression(),\n    classDecorations,\n    elementDecorations,\n  ];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (version !== \"2023-11\") {\n      args.splice(1, 2, elementDecorations, classDecorations);\n    }\n    if (\n      version === \"2021-12\" ||\n      (version === \"2022-03\" && !state.availableHelper(\"applyDecs2203R\"))\n    ) {\n      lhs = t.arrayPattern([...elementLocals, ...classLocals]);\n      rhs = t.callExpression(\n        state.addHelper(version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\"),\n        args,\n      );\n      return t.assignmentExpression(\"=\", lhs, rhs);\n    } else if (version === \"2022-03\") {\n      rhs = t.callExpression(state.addHelper(\"applyDecs2203R\"), args);\n    } else if (version === \"2023-01\") {\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      }\n      rhs = t.callExpression(state.addHelper(\"applyDecs2301\"), args);\n    } else if (version === \"2023-05\") {\n      if (\n        maybePrivateBrandName ||\n        superClass ||\n        classDecorationsFlag.value !== 0\n      ) {\n        args.push(classDecorationsFlag);\n      }\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      } else if (superClass) {\n        args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n      }\n      if (superClass) args.push(superClass);\n      rhs = t.callExpression(state.addHelper(\"applyDecs2305\"), args);\n    }\n  }\n  if (process.env.BABEL_8_BREAKING || version === \"2023-11\") {\n    if (\n      maybePrivateBrandName ||\n      superClass ||\n      classDecorationsFlag.value !== 0\n    ) {\n      args.push(classDecorationsFlag);\n    }\n    if (maybePrivateBrandName) {\n      args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n    } else if (superClass) {\n      args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n    }\n    if (superClass) args.push(superClass);\n    rhs = t.callExpression(state.addHelper(\"applyDecs2311\"), args);\n  }\n\n  // optimize `{ c: [classLocals] } = applyDecsHelper(...)` to\n  // `[classLocals] = applyDecsHelper(...).c`\n  if (elementLocals.length > 0) {\n    if (classLocals.length > 0) {\n      lhs = t.objectPattern([\n        t.objectProperty(t.identifier(\"e\"), t.arrayPattern(elementLocals)),\n        t.objectProperty(t.identifier(\"c\"), t.arrayPattern(classLocals)),\n      ]);\n    } else {\n      lhs = t.arrayPattern(elementLocals);\n      // @ts-ignore(Babel 7 vs Babel 8) optional removed in Babel 8\n      rhs = t.memberExpression(rhs, t.identifier(\"e\"), false, false);\n    }\n  } else {\n    // invariant: classLocals.length > 0\n    lhs = t.arrayPattern(classLocals);\n    // @ts-ignore(Babel 7 vs Babel 8) optional removed in Babel 8\n    rhs = t.memberExpression(rhs, t.identifier(\"c\"), false, false);\n  }\n\n  return t.assignmentExpression(\"=\", lhs, rhs);\n}\n\nfunction isProtoKey(\n  node: t.Identifier | t.StringLiteral | t.BigIntLiteral | t.NumericLiteral,\n) {\n  return node.type === \"Identifier\"\n    ? node.name === \"__proto__\"\n    : node.value === \"__proto__\";\n}\n\nfunction isDecorated(node: t.Class | ClassDecoratableElement) {\n  return node.decorators && node.decorators.length > 0;\n}\n\nfunction shouldTransformElement(node: ClassElement) {\n  switch (node.type) {\n    case \"ClassAccessorProperty\":\n      return true;\n    case \"ClassMethod\":\n    case \"ClassProperty\":\n    case \"ClassPrivateMethod\":\n    case \"ClassPrivateProperty\":\n      return isDecorated(node);\n    default:\n      return false;\n  }\n}\n\nfunction shouldTransformClass(node: t.Class) {\n  return isDecorated(node) || node.body.body.some(shouldTransformElement);\n}\n\n// Todo: unify name references logic with helper-function-name\nfunction NamedEvaluationVisitoryFactory(\n  isAnonymous: (path: NodePath) => boolean,\n  visitor: (\n    path: NodePath,\n    state: PluginPass,\n    name:\n      | string\n      | t.Identifier\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral,\n  ) => void,\n) {\n  function handleComputedProperty(\n    propertyPath: NodePath<\n      t.ObjectProperty | t.ClassProperty | t.ClassAccessorProperty\n    >,\n    key: t.Expression,\n    state: PluginPass,\n  ): t.StringLiteral | t.Identifier {\n    switch (key.type) {\n      case \"StringLiteral\":\n        return t.stringLiteral(key.value);\n      case \"NumericLiteral\":\n      case \"BigIntLiteral\": {\n        const keyValue = key.value + \"\";\n        propertyPath.get(\"key\").replaceWith(t.stringLiteral(keyValue));\n        return t.stringLiteral(keyValue);\n      }\n      default: {\n        const ref = propertyPath.scope.maybeGenerateMemoised(key);\n        propertyPath\n          .get(\"key\")\n          .replaceWith(\n            t.assignmentExpression(\n              \"=\",\n              ref,\n              createToPropertyKeyCall(state, key),\n            ),\n          );\n        return t.cloneNode(ref);\n      }\n    }\n  }\n  return {\n    VariableDeclarator(path, state) {\n      const id = path.node.id;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"init\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    AssignmentExpression(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          switch (path.node.operator) {\n            case \"=\":\n            case \"&&=\":\n            case \"||=\":\n            case \"??=\":\n              visitor(initializer, state, id.name);\n          }\n        }\n      }\n    },\n    AssignmentPattern(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    // We listen on ObjectExpression so that we don't have to visit\n    // the object properties under object patterns\n    ObjectExpression(path, state) {\n      for (const propertyPath of path.get(\"properties\")) {\n        if (!propertyPath.isObjectProperty()) continue;\n        const { node } = propertyPath;\n        const id = node.key;\n        const initializer = skipTransparentExprWrappers(\n          propertyPath.get(\"value\") as NodePath<t.Expression>,\n        );\n        if (isAnonymous(initializer)) {\n          if (!node.computed) {\n            // ******** RS: PropertyDefinitionEvaluation\n            if (!isProtoKey(id as t.StringLiteral | t.Identifier)) {\n              if (id.type === \"Identifier\") {\n                visitor(initializer, state, id.name);\n              } else {\n                const className = t.stringLiteral(\n                  (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                    .value + \"\",\n                );\n                visitor(initializer, state, className);\n              }\n            }\n          } else {\n            const ref = handleComputedProperty(\n              propertyPath,\n              // The key of a computed object property must not be a private name\n              id as t.Expression,\n              state,\n            );\n            visitor(initializer, state, ref);\n          }\n        }\n      }\n    },\n    ClassPrivateProperty(path, state) {\n      const { node } = path;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        const className = t.stringLiteral(\"#\" + node.key.id.name);\n        visitor(initializer, state, className);\n      }\n    },\n    ClassAccessorProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else if (id.type === \"PrivateName\") {\n            const className = t.stringLiteral(\"#\" + id.id.name);\n            visitor(initializer, state, className);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(\n            path,\n            // The key of a computed accessor property must not be a private name\n            id as t.Expression,\n            state,\n          );\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n    ClassProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(path, id, state);\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n  } satisfies Visitor<PluginPass>;\n}\n\nfunction isDecoratedAnonymousClassExpression(path: NodePath) {\n  return (\n    path.isClassExpression({ id: null }) && shouldTransformClass(path.node)\n  );\n}\n\nfunction generateLetUidIdentifier(scope: Scope, name: string) {\n  const id = scope.generateUidIdentifier(name);\n  scope.push({ id, kind: \"let\" });\n  return t.cloneNode(id);\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  version: DecoratorVersionKind,\n  inherits: PluginObject[\"inherits\"],\n): PluginObject {\n  if (process.env.BABEL_8_BREAKING) {\n    assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n  } else {\n    if (\n      version === \"2023-11\" ||\n      version === \"2023-05\" ||\n      version === \"2023-01\"\n    ) {\n      assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n    } else if (version === \"2021-12\") {\n      assertVersion(REQUIRED_VERSION(\"^7.16.0\"));\n    } else {\n      assertVersion(REQUIRED_VERSION(\"^7.19.0\"));\n    }\n  }\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n  const ignoreFunctionLength = assumption(\"ignoreFunctionLength\") ?? loose;\n\n  const namedEvaluationVisitor: Visitor<PluginPass> =\n    NamedEvaluationVisitoryFactory(\n      isDecoratedAnonymousClassExpression,\n      visitClass,\n    );\n\n  function visitClass(\n    path: NodePath<t.Class>,\n    state: PluginPass,\n    className: string | t.Identifier | t.StringLiteral | undefined,\n  ) {\n    if (VISITED.has(path)) return;\n    const { node } = path;\n    className ??= node.id?.name;\n    const newPath = transformClass(\n      path,\n      state,\n      constantSuper,\n      ignoreFunctionLength,\n      className,\n      namedEvaluationVisitor,\n      version,\n    );\n    if (newPath) {\n      VISITED.add(newPath);\n      return;\n    }\n    VISITED.add(path);\n  }\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: inherits,\n\n    visitor: {\n      ExportDefaultDeclaration(path, state) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          const isAnonymous = !declaration.id;\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.splitExportDeclaration ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n          }\n          const updatedVarDeclarationPath =\n            path.splitExportDeclaration() as NodePath<t.ClassDeclaration>;\n          if (isAnonymous) {\n            visitClass(\n              updatedVarDeclarationPath,\n              state,\n              t.stringLiteral(\"default\"),\n            );\n          }\n        }\n      },\n      ExportNamedDeclaration(path) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.splitExportDeclaration ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n          }\n          path.splitExportDeclaration();\n        }\n      },\n\n      Class(path, state) {\n        visitClass(path, state, undefined);\n      },\n\n      ...namedEvaluationVisitor,\n    },\n  };\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AAEA,IAAAE,wCAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAIA,IAAAI,KAAA,GAAAJ,OAAA;AAEO,SAASK,gBAAgBA,CAACC,IAA2C,EAAE;EAAA,IAAAC,gBAAA;EAE5E,OAAO,CAAC,GAAAA,gBAAA,GAACD,IAAI,CAACE,UAAU,aAAfD,gBAAA,CAAiBE,MAAM;AAClC;AAEO,SAASC,aAAaA,CAACJ,IAAa,EAAE;EAC3C,OAAOD,gBAAgB,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACK,IAAI,CAACA,IAAI,CAACC,IAAI,CAACP,gBAAgB,CAAC;AACxE;AAoCA,SAASQ,WAAWA,CAACC,EAAY,EAAEC,GAAG,GAAGD,EAAE,CAACL,MAAM,GAAG,CAAC,EAAQ;EAE5D,IAAIM,GAAG,KAAK,CAAC,CAAC,EAAE;IACdD,EAAE,CAACE,OAAO,GAAqB,CAAC;IAChC;EACF;EAEA,MAAMC,OAAO,GAAGH,EAAE,CAACC,GAAG,CAAC;EAEvB,IAAIE,OAAO,OAAyB,EAAE;IAEpCH,EAAE,CAACC,GAAG,CAAC,KAAuB;EAChC,CAAC,MAAM,IAAIE,OAAO,QAAyB,EAAE;IAE3CH,EAAE,CAACC,GAAG,CAAC,KAAuB;IAC9BF,WAAW,CAACC,EAAE,EAAEC,GAAG,GAAG,CAAC,CAAC;EAC1B,CAAC,MAAM;IAELD,EAAE,CAACC,GAAG,CAAC,GAAGE,OAAO,GAAG,CAAC;EACvB;AACF;AASA,SAASC,iCAAiCA,CACxCC,SAA2D,EACtC;EACrB,MAAMC,gBAA0B,GAAG,EAAE;EACrC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEtCH,SAAS,CAACI,QAAQ,CAAC;IACjBC,WAAWA,CAACC,IAAI,EAAE;MAChBJ,YAAY,CAACK,GAAG,CAACD,IAAI,CAACnB,IAAI,CAACQ,EAAE,CAACa,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAO,MAAqB;IAC1B,IAAIC,SAAS;IACb,GAAG;MACDf,WAAW,CAACO,gBAAgB,CAAC;MAC7BQ,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAGV,gBAAgB,CAAC;IACtD,CAAC,QAAQC,YAAY,CAACU,GAAG,CAACH,SAAS,CAAC;IAEpC,OAAOI,WAAC,CAACC,WAAW,CAACD,WAAC,CAACE,UAAU,CAACN,SAAS,CAAC,CAAC;EAC/C,CAAC;AACH;AAQA,SAASO,qCAAqCA,CAC5ChB,SAA2D,EACtC;EACrB,IAAIiB,SAA8B;EAElC,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGlB,iCAAiC,CAACC,SAAS,CAAC;IAC1D;IAEA,OAAOiB,SAAS,CAAC,CAAC;EACpB,CAAC;AACH;AAUA,SAASC,mBAAmBA,CAC1BZ,IAAsD,EACtDa,SAA8D,EAI9D;EACA,MAAMxB,EAAE,GAAGW,IAAI,CAACnB,IAAI,CAACQ,EAAE;EACvB,MAAMyB,KAAK,GAAGd,IAAI,CAACc,KAAK;EACxB,IAAId,IAAI,CAACe,IAAI,KAAK,kBAAkB,EAAE;IACpC,MAAMF,SAAS,GAAGxB,EAAE,CAACa,IAAI;IACzB,MAAMc,KAAK,GAAGF,KAAK,CAACG,gCAAgC,CAAC5B,EAAE,CAAC;IACxD,MAAM6B,OAAO,GAAGX,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IAEvCC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IAEnCF,IAAI,CAACoB,GAAG,CAAC,IAAI,CAAC,CAACC,WAAW,CAACH,OAAO,CAAC;IAEnC,OAAO;MAAE7B,EAAE,EAAEkB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MAAEhB;IAAK,CAAC;EACzC,CAAC,MAAM;IACL,IAAIgB,KAAmB;IAEvB,IAAI3B,EAAE,EAAE;MACNwB,SAAS,GAAGxB,EAAE,CAACa,IAAI;MACnBc,KAAK,GAAGO,wBAAwB,CAACT,KAAK,CAACU,MAAM,EAAEX,SAAS,CAAC;MACzDC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IACrC,CAAC,MAAM;MACLc,KAAK,GAAGO,wBAAwB,CAC9BT,KAAK,CAACU,MAAM,EACZ,OAAOX,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,iBAC9C,CAAC;IACH;IAEA,MAAMY,YAAY,GAAGlB,WAAC,CAACmB,eAAe,CACpC,OAAOb,SAAS,KAAK,QAAQ,GAAGN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GAAG,IAAI,EAC9Db,IAAI,CAACnB,IAAI,CAAC8C,UAAU,EACpB3B,IAAI,CAACnB,IAAI,CAACK,IACZ,CAAC;IAED,MAAM,CAAC0C,OAAO,CAAC,GAAG5B,IAAI,CAACqB,WAAW,CAChCd,WAAC,CAACsB,kBAAkB,CAAC,CAACJ,YAAY,EAAET,KAAK,CAAC,CAC5C,CAAC;IAED,OAAO;MACL3B,EAAE,EAAEkB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MACtBhB,IAAI,EAAE4B,OAAO,CAACR,GAAG,CAAC,eAAe;IACnC,CAAC;EACH;AACF;AAEA,SAASU,qBAAqBA,CAC5BC,GAAiC,EACjCC,KAA+B,EAC/BC,QAAiB,EACyB;EAC1C,IAAIF,GAAG,CAAChB,IAAI,KAAK,aAAa,EAAE;IAC9B,OAAOR,WAAC,CAAC2B,oBAAoB,CAACH,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEF,QAAQ,CAAC;EAChE,CAAC,MAAM;IACL,OAAO1B,WAAC,CAAC6B,aAAa,CAACL,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEA,SAAS,EAAEF,QAAQ,CAAC;EACpE;AACF;AAEA,SAASI,yBAAyBA,CAChCrC,IAAuB,EACvBa,SAA8D,EAC9D;EACA,IAAI,CAACb,IAAI,CAACnB,IAAI,CAACQ,EAAE,EAAE;IACjBW,IAAI,CAACnB,IAAI,CAACQ,EAAE,GACV,OAAOwB,SAAS,KAAK,QAAQ,GACzBN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GACvBb,IAAI,CAACc,KAAK,CAACwB,qBAAqB,CAAC,OAAO,CAAC;EACjD;AACF;AAEA,SAASC,oBAAoBA,CAC3B1B,SAAuB,EACvB2B,OAA0C,EAC1CC,SAAuC,EACvCC,SAAuC,EACvCC,SAAwB,EACxBC,UAAmB,EACnBX,QAAiB,EACjBY,OAA6B,EACvB;EACN,MAAMC,OAAO,GACX,CAACD,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDZ,QAAQ,GACJpB,SAAS,GACTN,WAAC,CAACwC,cAAc,CAAC,CAAC;EAExB,MAAMC,UAAU,GAAGzC,WAAC,CAAC0C,cAAc,CAAC,CAClC1C,WAAC,CAAC2C,eAAe,CACf3C,WAAC,CAAC4C,gBAAgB,CAAC5C,WAAC,CAACe,SAAS,CAACwB,OAAO,CAAC,EAAEvC,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC,CACjE,CAAC,CACF,CAAC;EAEF,MAAMS,UAAU,GAAG7C,WAAC,CAAC0C,cAAc,CAAC,CAClC1C,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAAC+C,oBAAoB,CACpB,GAAG,EACH/C,WAAC,CAAC4C,gBAAgB,CAAC5C,WAAC,CAACe,SAAS,CAACwB,OAAO,CAAC,EAAEvC,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC,CAAC,EAChEpC,WAAC,CAACE,UAAU,CAAC,GAAG,CAClB,CACF,CAAC,CACF,CAAC;EAEF,IAAI8C,MAA4C,EAC9CC,MAA4C;EAE9C,IAAIf,SAAS,CAAC1B,IAAI,KAAK,aAAa,EAAE;IACpCwC,MAAM,GAAGhD,WAAC,CAACkD,kBAAkB,CAAC,KAAK,EAAEhB,SAAS,EAAE,EAAE,EAAEO,UAAU,EAAEf,QAAQ,CAAC;IACzEuB,MAAM,GAAGjD,WAAC,CAACkD,kBAAkB,CAC3B,KAAK,EACLf,SAAS,EACT,CAACnC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB2C,UAAU,EACVnB,QACF,CAAC;EACH,CAAC,MAAM;IACLsB,MAAM,GAAGhD,WAAC,CAACmD,WAAW,CACpB,KAAK,EACLjB,SAAS,EACT,EAAE,EACFO,UAAU,EACVJ,UAAU,EACVX,QACF,CAAC;IACDuB,MAAM,GAAGjD,WAAC,CAACmD,WAAW,CACpB,KAAK,EACLhB,SAAS,EACT,CAACnC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB2C,UAAU,EACVR,UAAU,EACVX,QACF,CAAC;EACH;EAEAO,OAAO,CAACmB,WAAW,CAACH,MAAM,CAAC;EAC3BhB,OAAO,CAACmB,WAAW,CAACJ,MAAM,CAAC;AAC7B;AAEA,SAASK,wBAAwBA,CAC/BjB,SAAwB,EACxBE,OAA6B,EACyB;EACtD,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;IAC3E,OAAO,CACLgB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC7B;AACA,wBAAwBxD,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC;AAC9C;AACA,OAAO,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC7B;AACA,iBAAiBxD,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC;AACvC;AACA,OAAO,CACF;EACH;EACA,OAAO,CACLkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC3B,eAAexD,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC;AACrC,KAAK,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAG;AAC3B,oBAAoBxD,WAAC,CAACe,SAAS,CAACqB,SAAS,CAAC;AAC1C,KAAK,CACF;AACH;AAWA,SAASqB,yBAAyBA,CAChChE,IAA4B,EACJ;EACxBA,IAAI,GAAG,IAAAiE,oEAA2B,EAACjE,IAAI,CAAC;EACxC,IAAIA,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAE;IAC/B,MAAMC,WAAW,GAAGnE,IAAI,CAACoB,GAAG,CAAC,aAAa,CAAC;IAC3C,OAAO4C,yBAAyB,CAACG,WAAW,CAACA,WAAW,CAACnF,MAAM,GAAG,CAAC,CAAC,CAAC;EACvE;EACA,OAAOgB,IAAI;AACb;AAYA,SAASoE,sBAAsBA,CAACpE,IAA4B,EAAgB;EAC1E,MAAMwC,OAAO,GAAGwB,yBAAyB,CAAChE,IAAI,CAAC;EAC/C,IAAIwC,OAAO,CAAC6B,oBAAoB,CAAC,CAAC,EAAE;IAClC,OAAO9D,WAAC,CAACe,SAAS,CAACtB,IAAI,CAACnB,IAAI,CAAC;EAC/B,CAAC,MAAM,IAAI2D,OAAO,CAAC8B,YAAY,CAAC,CAAC,IAAItE,IAAI,CAACc,KAAK,CAACyD,MAAM,CAAC/B,OAAO,CAAC3D,IAAI,CAACqB,IAAI,CAAC,EAAE;IACzE,OAAOK,WAAC,CAACe,SAAS,CAACtB,IAAI,CAACnB,IAAI,CAAC;EAC/B,CAAC,MAAM,IACL2D,OAAO,CAACgC,sBAAsB,CAAC,CAAC,IAChChC,OAAO,CAACpB,GAAG,CAAC,MAAM,CAAC,CAACkD,YAAY,CAAC,CAAC,EAClC;IACA,OAAO/D,WAAC,CAACe,SAAS,CAACkB,OAAO,CAAC3D,IAAI,CAAC4F,IAAoB,CAAC;EACvD,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CACb,oCAAoC1E,IAAI,CAAC2E,QAAQ,CAAC,CAAC,6BACrD,CAAC;EACH;AACF;AAaA,SAASC,+BAA+BA,CACtCT,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM9C,GAAG,GAAG8C,SAAS,CAACzD,GAAG,CAAC,KAAK,CAA2B;EAC1D,IAAIW,GAAG,CAACmC,oBAAoB,CAAC,CAAC,EAAE;IAC9BC,WAAW,CAACW,IAAI,CAAC,GAAG/C,GAAG,CAAClD,IAAI,CAACsF,WAAW,CAAC;EAC3C,CAAC,MAAM;IACLA,WAAW,CAACW,IAAI,CAAC/C,GAAG,CAAClD,IAAI,CAAC;EAC5B;EACAkD,GAAG,CAACV,WAAW,CAAC0D,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AACvD;AAcA,SAASa,8BAA8BA,CACrCb,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM9C,GAAG,GAAG8C,SAAS,CAACzD,GAAG,CAAC,KAAK,CAA2B;EAC1D,MAAM6D,UAAU,GAAGjB,yBAAyB,CAACjC,GAAG,CAAC;EACjD,IAAIkD,UAAU,CAACZ,oBAAoB,CAAC,CAAC,EAAE;IACrCO,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;EACzD,CAAC,MAAM;IACL,MAAMK,WAAW,GAAGnD,GAAG,CAACjB,KAAK,CAACU,MAAM;IACpC,MAAM2D,eAAe,GAAG,IAAAC,wBAAkB,EACxCH,UAAU,CAACpG,IAAI,EACfqG,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;IACD,IAAI,CAACF,eAAe,EAAE;MAGpBP,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;IACzD,CAAC,MAAM;MACL,MAAMS,kBAAkB,GAAG,CACzB,GAAGnB,WAAW,EAEd5D,WAAC,CAACe,SAAS,CAAC6D,eAAe,CAACV,IAAI,CAAC,CAClC;MACD,MAAMc,gBAAgB,GAAGN,UAAU,CAACO,UAAU;MAC9C,IAAID,gBAAgB,CAACrB,oBAAoB,CAAC,CAAC,EAAE;QAC3CqB,gBAAgB,CAACE,aAAa,CAAC,aAAa,EAAEH,kBAAkB,CAAC;MACnE,CAAC,MAAM;QACLL,UAAU,CAAC5D,WAAW,CACpB0D,uBAAuB,CAAC,CACtBxE,WAAC,CAACe,SAAS,CAAC6D,eAAe,CAAC,EAC5B,GAAGG,kBAAkB,CACtB,CACH,CAAC;MACH;IACF;EACF;AACF;AAWA,SAASI,oCAAoCA,CAC3CvB,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAMc,WAAW,GAAGd,SAAS,CAACzD,GAAG,CAAC,OAAO,CAAC;EAC1C,IAAIuE,WAAW,CAAC9G,IAAI,EAAE;IACpBsF,WAAW,CAACW,IAAI,CAACa,WAAW,CAAC9G,IAAI,CAAC;EACpC,CAAC,MAAM,IAAIsF,WAAW,CAACnF,MAAM,GAAG,CAAC,EAAE;IACjCmF,WAAW,CAACA,WAAW,CAACnF,MAAM,GAAG,CAAC,CAAC,GAAGuB,WAAC,CAACqF,eAAe,CACrD,MAAM,EACNzB,WAAW,CAACA,WAAW,CAACnF,MAAM,GAAG,CAAC,CACpC,CAAC;EACH;EACA2G,WAAW,CAACtE,WAAW,CAAC0D,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AAC/D;AAEA,SAAS0B,+BAA+BA,CACtC1B,WAA2B,EAC3B2B,SAAkC,EAClC;EACAA,SAAS,CAACC,gBAAgB,CACxB,MAAM,EACNxF,WAAC,CAAC8C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS6B,+BAA+BA,CACtC7B,WAA2B,EAC3B8B,eAAwC,EACxC;EACAA,eAAe,CAACpH,IAAI,CAACK,IAAI,CAACA,IAAI,CAACK,OAAO,CACpCgB,WAAC,CAAC8C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS+B,yBAAyBA,CAChCpC,UAAwB,EACxBqC,aAA2B,EAC3B;EACA,OACE5F,WAAC,CAAC6F,gBAAgB,CAACtC,UAAU,CAAC,IAC9BvD,WAAC,CAAC+D,YAAY,CAACR,UAAU,CAACuC,MAAM,EAAE;IAAEnG,IAAI,EAAEiG,aAAa,CAACjG;EAAK,CAAC,CAAC;AAEnE;AASA,SAASoG,+BAA+BA,CACtCnC,WAA2B,EAC3BoC,cAA4B,EAC5B;EACA,IAAIA,cAAc,EAAE;IAClB,IACEpC,WAAW,CAACnF,MAAM,IAAI,CAAC,IACvBkH,yBAAyB,CAAC/B,WAAW,CAAC,CAAC,CAAC,EAAEoC,cAAc,CAAC,EACzD;MAEA,MAAMC,eAAe,GAAGjG,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACe,SAAS,CAACiF,cAAc,CAAC,EAAE,CACpEpC,WAAW,CAAC,CAAC,CAAC,CACf,CAAC;MACFA,WAAW,CAACuC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;IAC3C;IAEA,IACErC,WAAW,CAACnF,MAAM,IAAI,CAAC,IACvBuB,WAAC,CAACoG,gBAAgB,CAACxC,WAAW,CAACA,WAAW,CAACnF,MAAM,GAAG,CAAC,CAAC,CAAC,IACvDkH,yBAAyB,CACvB/B,WAAW,CAACA,WAAW,CAACnF,MAAM,GAAG,CAAC,CAAC,EACnCuH,cACF,CAAC,EACD;MACApC,WAAW,CAACuC,MAAM,CAACvC,WAAW,CAACnF,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/C;EACF;EACA,OAAO+F,uBAAuB,CAACZ,WAAW,CAAC;AAC7C;AAWA,SAASyC,0CAA0CA,CACjDzC,WAA2B,EAC3B8B,eAAwC,EACxCM,cAA4B,EAC5B;EACAN,eAAe,CAACnG,QAAQ,CAAC;IACvB+G,cAAc,EAAE;MACdC,IAAIA,CAAC9G,IAAI,EAAE;QACT,IAAI,CAACA,IAAI,CAACoB,GAAG,CAAC,QAAQ,CAAC,CAAC2F,OAAO,CAAC,CAAC,EAAE;QACnC,MAAMC,QAAQ,GAAG,CACfhH,IAAI,CAACnB,IAAI,EACT,GAAGsF,WAAW,CAAC8C,GAAG,CAACC,IAAI,IAAI3G,WAAC,CAACe,SAAS,CAAC4F,IAAI,CAAC,CAAC,CAC9C;QAED,IAAIlH,IAAI,CAACmH,kBAAkB,CAAC,CAAC,EAAE;UAC7BH,QAAQ,CAAClC,IAAI,CAACvE,WAAC,CAACwC,cAAc,CAAC,CAAC,CAAC;QACnC;QACA/C,IAAI,CAACqB,WAAW,CACdiF,+BAA+B,CAACU,QAAQ,EAAET,cAAc,CAC1D,CAAC;QAEDvG,IAAI,CAACoH,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IACDC,WAAWA,CAACrH,IAAI,EAAE;MAChB,IAAIA,IAAI,CAACnB,IAAI,CAACyI,IAAI,KAAK,aAAa,EAAE;QACpCtH,IAAI,CAACoH,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAWA,SAASG,gCAAgCA,CACvCpD,WAA2B,EAC3BqD,cAAuB,EACvB;EACA,MAAMtI,IAAmB,GAAG,CAC1BqB,WAAC,CAAC8C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D;EACD,IAAIqD,cAAc,EAAE;IAClBtI,IAAI,CAACK,OAAO,CACVgB,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAClH,WAAC,CAACmH,aAAa,CAACnH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,OAAOF,WAAC,CAACmD,WAAW,CAClB,aAAa,EACbnD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3B+G,cAAc,GAAG,CAACjH,WAAC,CAACoH,WAAW,CAACpH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAC3DF,WAAC,CAAC0C,cAAc,CAAC/D,IAAI,CACvB,CAAC;AACH;AAEA,SAAS0I,gCAAgCA,CAACzD,WAA2B,EAAE;EACrE,OAAO5D,WAAC,CAACsH,WAAW,CAAC,CACnBtH,WAAC,CAAC8C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D,CAAC;AACJ;AAGA,MAAM2D,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAEhB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,SAASC,cAAcA,CAAC9F,OAA0C,EAAU;EAC1E,QAAQA,OAAO,CAAC3D,IAAI,CAACkC,IAAI;IACvB,KAAK,eAAe;IACpB,KAAK,sBAAsB;MACzB,OAAO+G,KAAK;IACd,KAAK,uBAAuB;MAC1B,OAAOC,QAAQ;IACjB,KAAK,aAAa;IAClB,KAAK,oBAAoB;MACvB,IAAIvF,OAAO,CAAC3D,IAAI,CAACyI,IAAI,KAAK,KAAK,EAAE;QAC/B,OAAOW,MAAM;MACf,CAAC,MAAM,IAAIzF,OAAO,CAAC3D,IAAI,CAACyI,IAAI,KAAK,KAAK,EAAE;QACtC,OAAOY,MAAM;MACf,CAAC,MAAM;QACL,OAAOF,MAAM;MACf;EACJ;AACF;AAmCA,SAASO,qBAAqBA,CAACC,IAAqB,EAAmB;EACrE,OAAO,CACL,GAAGA,IAAI,CAACC,MAAM,CACZC,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MACzD,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CACZC,EAAE,IAAI,CAACA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MAC1D,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,EACtD,GAAGU,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAACA,EAAE,CAACzG,QAAQ,IAAIyG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,CACxD;AACH;AAgBA,SAASa,sBAAsBA,CAC7B5J,UAAyB,EACzB6J,cAA4C,EAC5C/F,OAA6B,EACC;EAC9B,MAAMgG,SAAS,GAAG9J,UAAU,CAACC,MAAM;EACnC,MAAM8J,WAAW,GAAGF,cAAc,CAACzJ,IAAI,CAAC4J,OAAO,CAAC;EAChD,MAAMC,IAAoB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;IAClC,KACGpG,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDiG,WAAW,EACX;MACAE,IAAI,CAAClE,IAAI,CACP8D,cAAc,CAACK,CAAC,CAAC,IAAI1I,WAAC,CAACqF,eAAe,CAAC,MAAM,EAAErF,WAAC,CAAC2I,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;IACH;IACAF,IAAI,CAAClE,IAAI,CAAC/F,UAAU,CAACkK,CAAC,CAAC,CAACnF,UAAU,CAAC;EACrC;EAEA,OAAO;IAAEqF,QAAQ,EAAEL,WAAW;IAAEE;EAAK,CAAC;AACxC;AAEA,SAASI,uBAAuBA,CAC9BC,cAA+B,EAC/BxG,OAA6B,EACV;EACnB,OAAOtC,WAAC,CAAC+I,eAAe,CACtBD,cAAc,CAACpC,GAAG,CAACyB,EAAE,IAAI;IACvB,IAAIa,IAAI,GAAGb,EAAE,CAACpB,IAAI;IAClB,IAAIoB,EAAE,CAACzG,QAAQ,EAAE;MACfsH,IAAI,IACF1G,OAAO,KAAK,SAAS,IACaA,OAAO,KAAK,SAAS,GACnDuF,MAAM,GACND,kBAAkB;IAC1B;IACA,IAAIO,EAAE,CAACc,kBAAkB,EAAED,IAAI,IAAIlB,oBAAoB;IAEvD,OAAO9H,WAAC,CAAC+I,eAAe,CAAC,CACvBZ,EAAE,CAACe,eAAe,EAClBlJ,WAAC,CAAC2I,cAAc,CAACK,IAAI,CAAC,EACtBb,EAAE,CAACxI,IAAI,EACP,IAAIwI,EAAE,CAACgB,cAAc,IAAI,EAAE,CAAC,CAC7B,CAAC;EACJ,CAAC,CACH,CAAC;AACH;AAEA,SAASC,8BAA8BA,CAACN,cAA+B,EAAE;EACvE,MAAMO,QAAwB,GAAG,EAAE;EAEnC,KAAK,MAAMlB,EAAE,IAAIW,cAAc,EAAE;IAC/B,MAAM;MAAEQ;IAAO,CAAC,GAAGnB,EAAE;IAErB,IAAIoB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBD,QAAQ,CAAC9E,IAAI,CAAC,GAAG+E,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIA,MAAM,KAAK1H,SAAS,EAAE;MAC/ByH,QAAQ,CAAC9E,IAAI,CAAC+E,MAAM,CAAC;IACvB;EACF;EAEA,OAAOD,QAAQ;AACjB;AAEA,SAASI,mBAAmBA,CAC1BnH,OAA6B,EAC7BL,OAAiB,EACjBT,GAAkB,EAClBkI,KAAmB,EACnBC,KAAmB,EACnBjI,QAAiB,EACjB;EACAO,OAAO,CAACmB,WAAW,CACjBpD,WAAC,CAACkD,kBAAkB,CAClB,KAAK,EACLlD,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EAChB,EAAE,EACFxB,WAAC,CAAC0C,cAAc,CAAC,CACf1C,WAAC,CAAC2C,eAAe,CACf3C,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAACe,SAAS,CAAC2I,KAAK,CAAC,EACepH,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC1B,WAAC,CAACwC,cAAc,CAAC,CAAC,CACzB,CACF,CAAC,CACF,CAAC,EACFd,QACF,CACF,CAAC;EAEDO,OAAO,CAACmB,WAAW,CACjBpD,WAAC,CAACkD,kBAAkB,CAClB,KAAK,EACLlD,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EAChB,CAACxB,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAAC0C,cAAc,CAAC,CACf1C,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAACe,SAAS,CAAC4I,KAAK,CAAC,EACerH,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,CAAC1B,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,GACnB,CAACF,WAAC,CAACwC,cAAc,CAAC,CAAC,EAAExC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC5C,CACF,CAAC,CACF,CAAC,EACFwB,QACF,CACF,CAAC;AACH;AAEA,SAASkI,mBAAmBA,CAC1B3H,OAAuC,EACvCT,GAAkB,EAClBqI,cAA4B,EAC5BnI,QAAiB,EACjB;EACA,IAAIoI,MAAwC;EAC5C,IAAIC,KAAoB;EAExB,IAAI9H,OAAO,CAAC3D,IAAI,CAACyI,IAAI,KAAK,KAAK,EAAE;IAC/B+C,MAAM,GAAG,CAAC9J,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5B6J,KAAK,GAAG,CACN/J,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAACkG,cAAc,CAAC2D,cAAc,EAAE,CAC/B7J,WAAC,CAACwC,cAAc,CAAC,CAAC,EAClBxC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF;EACH,CAAC,MAAM;IACL4J,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,CACN/J,WAAC,CAAC2C,eAAe,CAAC3C,WAAC,CAACkG,cAAc,CAAC2D,cAAc,EAAE,CAAC7J,WAAC,CAACwC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1E;EACH;EAEAP,OAAO,CAACnB,WAAW,CACjBd,WAAC,CAACkD,kBAAkB,CAClBjB,OAAO,CAAC3D,IAAI,CAACyI,IAAI,EACjB/G,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EAChBsI,MAAM,EACN9J,WAAC,CAAC0C,cAAc,CAACqH,KAAK,CAAC,EACvBrI,QACF,CACF,CAAC;AACH;AAEA,SAASsI,6BAA6BA,CACpCvK,IAA4B,EACe;EAC3C,MAAM;IAAEe;EAAK,CAAC,GAAGf,IAAI;EAErB,OACEe,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,aAAa;AAE1B;AAEA,SAASyJ,iBAAiBA,CAACF,KAAoB,EAAE;EAC/C,OAAO/J,WAAC,CAACkG,cAAc,CACrBlG,WAAC,CAACkK,uBAAuB,CAAC,EAAE,EAAElK,WAAC,CAAC0C,cAAc,CAACqH,KAAK,CAACpL,IAAI,CAAC,CAAC,EAC3D,EACF,CAAC;AACH;AAEA,SAASwL,4BAA4BA,CAACJ,KAAoB,EAAE;EAC1D,OAAO/J,WAAC,CAACoK,kBAAkB,CAAC,IAAI,EAAE,EAAE,EAAEpK,WAAC,CAAC0C,cAAc,CAACqH,KAAK,CAACpL,IAAI,CAAC,CAAC;AACrE;AAEA,SAAS0L,yBAAyBA,CAAC5I,KAAmB,EAAE;EACtD,OAAOzB,WAAC,CAACoK,kBAAkB,CACzB,IAAI,EACJ,EAAE,EACFpK,WAAC,CAAC0C,cAAc,CAAC,CAAC1C,WAAC,CAAC2C,eAAe,CAAClB,KAAK,CAAC,CAAC,CAC7C,CAAC;AACH;AAEA,SAAS+C,uBAAuBA,CAAC8F,KAAqB,EAAE;EACtD,IAAIA,KAAK,CAAC7L,MAAM,KAAK,CAAC,EAAE,OAAOuB,WAAC,CAACqF,eAAe,CAAC,MAAM,EAAErF,WAAC,CAAC2I,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAI2B,KAAK,CAAC7L,MAAM,KAAK,CAAC,EAAE,OAAO6L,KAAK,CAAC,CAAC,CAAC;EACvC,OAAOtK,WAAC,CAACsB,kBAAkB,CAACgJ,KAAK,CAAC;AACpC;AASA,SAASC,yCAAyCA,CAACjM,IAA0B,EAAE;EAC7E,MAAM;IAAEwL,MAAM;IAAEnL,IAAI;IAAEyB,SAAS,EAAEoK,WAAW;IAAEC,KAAK,EAAEC;EAAQ,CAAC,GAAGpM,IAAI;EACrE,OAAO0B,WAAC,CAACoK,kBAAkB,CACzBxI,SAAS,EAETkI,MAAM,EACNnL,IAAI,EACJ6L,WAAW,EACXE,OACF,CAAC;AACH;AAEA,SAASC,yBAAyBA,CAChCC,KAAiB,EACjBtK,SAAyC,EACzC;EACA,OAAON,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC1D7K,WAAC,CAACwC,cAAc,CAAC,CAAC,EAClBlC,SAAS,CACV,CAAC;AACJ;AAEA,SAASwK,uBAAuBA,CAACF,KAAiB,EAAEG,WAAyB,EAAE;EAC7E,OAAO/K,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAACE,WAAW,CAAC,CAAC;AAC1E;AAEA,SAASC,8BAA8BA,CAACC,SAAwB,EAAE;EAChE,OAAOjL,WAAC,CAACkK,uBAAuB,CAC9B,CAAClK,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAACkL,gBAAgB,CAAC,IAAI,EAAElL,WAAC,CAACe,SAAS,CAACkK,SAAS,CAAC,EAAEjL,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CACpE,CAAC;AACH;AAEA,SAASiL,gBAAgBA,CAAC5H,UAAkB,EAAE;EAOrC;IACL,IAAI;MACFvD,WAAC,CAACoL,YAAY,CAAC7H,UAAU,EAAEjF,IAAI,IAAI;QACjC,IAAI0B,WAAC,CAACqL,aAAa,CAAC/M,IAAI,CAAC,EAAE;UAEzB,MAAM,IAAI;QACZ;MACF,CAAC,CAAC;MACF,OAAO,KAAK;IACd,CAAC,CAAC,OAAAgN,OAAA,EAAM;MACN,OAAO,IAAI;IACb;EACF;AACF;AAUA,SAASC,oBAAoBA,CAAC9L,IAA+C,EAAE;EAC7E,MAAM;IAAEnB;EAAK,CAAC,GAAGmB,IAAI;EACrBnB,IAAI,CAACkN,QAAQ,GAAG,IAAI;EACpB,IAAIxL,WAAC,CAAC+D,YAAY,CAACzF,IAAI,CAACkD,GAAG,CAAC,EAAE;IAC5BlD,IAAI,CAACkD,GAAG,GAAGxB,WAAC,CAACyL,aAAa,CAACnN,IAAI,CAACkD,GAAG,CAAC7B,IAAI,CAAC;EAC3C;AACF;AAEA,SAAS+L,wBAAwBA,CAACjM,IAAc,EAAEJ,YAAsB,EAAE;EACxE,IAAIsM,6BAA6B,GAAG,KAAK;EACzC,IAAItM,YAAY,CAACZ,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAMmN,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;MACArM,WAAWA,CAACC,IAAI,EAAEmL,KAAK,EAAE;QACvB,IAAIA,KAAK,CAACkB,eAAe,CAAC/L,GAAG,CAACN,IAAI,CAACnB,IAAI,CAACQ,EAAE,CAACa,IAAI,CAAC,EAAE;UAChDgM,6BAA6B,GAAG,IAAI;UACpClM,IAAI,CAACsM,IAAI,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;IACF,MAAMD,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;IAC/C,KAAK,MAAMrM,IAAI,IAAIN,YAAY,EAAE;MAC/ByM,eAAe,CAACG,GAAG,CAACtM,IAAI,EAAE,IAAI,CAAC;IACjC;IACAF,IAAI,CAACF,QAAQ,CAACqM,kBAAkB,EAAE;MAChCE,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;EACA,OAAOH,6BAA6B;AACtC;AAEA,SAASO,6BAA6BA,CACpCzM,IAAuB,EACvB0M,uBAAoC,EACpC;EACA,MAAMP,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;IACArM,WAAWA,CAACC,IAAI,EAAEmL,KAAK,EAAE;MACvB,IAAI,CAACA,KAAK,CAACkB,eAAe,CAAC/L,GAAG,CAACN,IAAI,CAACnB,IAAI,CAACQ,EAAE,CAACa,IAAI,CAAC,EAAE;MAEnD,MAAMsF,UAAU,GAAGxF,IAAI,CAACwF,UAAU;MAClC,MAAMmH,gBAAgB,GAAGnH,UAAU,CAACA,UAAU;MAE9C,IAEGmH,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,sBAAsB,IACpD4L,gBAAgB,CAAC9N,IAAI,CAAC4F,IAAI,KAAKe,UAAU,CAAC3G,IAAI,IAEhD8N,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,kBAAkB,IAEjD4L,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,aAAa,IAE5C4L,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,cAAc,IAE5C4L,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,gBAAgB,IAC9C4L,gBAAgB,CAAC9N,IAAI,CAACmD,KAAK,KAAKwD,UAAU,CAAC3G,IAAI,IAC/C8N,gBAAgB,CAACnH,UAAU,CAACzE,IAAI,KAAK,eAAgB,IAEtD4L,gBAAgB,CAAC9N,IAAI,CAACkC,IAAI,KAAK,gBAAgB,IAC9C4L,gBAAgB,CAAC9N,IAAI,CAAC4F,IAAI,KAAKe,UAAU,CAAC3G,IAAK,EACjD;QACA,MAAMmB,IAAI,CAAC4M,mBAAmB,CAC5B,kDAAkD5M,IAAI,CAACnB,IAAI,CAACQ,EAAE,CAACa,IAAI,mCACrE,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACF,MAAMmM,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;EAC/C,KAAK,MAAMrM,IAAI,IAAIwM,uBAAuB,EAAE;IAC1CL,eAAe,CAACG,GAAG,CAACtM,IAAI,EAAE,IAAI,CAAC;EACjC;EACAF,IAAI,CAACF,QAAQ,CAACqM,kBAAkB,EAAE;IAChCE,eAAe,EAAEA;EACnB,CAAC,CAAC;AACJ;AAgBA,SAASQ,cAAcA,CACrB7M,IAAuB,EACvBmL,KAAiB,EACjB2B,aAAsB,EACtBC,oBAA6B,EAC7BlM,SAA8D,EAC9DmM,eAAoC,EACpCnK,OAA6B,EACP;EAAA,IAAAoK,aAAA;EACtB,MAAM/N,IAAI,GAAGc,IAAI,CAACoB,GAAG,CAAC,WAAW,CAAC;EAElC,MAAM8L,eAAe,GAAGlN,IAAI,CAACnB,IAAI,CAACE,UAAU;EAC5C,IAAIoO,oBAAoB,GAAG,KAAK;EAChC,IAAIC,0BAA0B,GAAG,KAAK;EACtC,IAAIC,oBAAoB,GAAG,KAAK;EAEhC,MAAMC,uBAAuB,GAAG5M,qCAAqC,CAACV,IAAI,CAAC;EAE3E,MAAMuN,gBAA0C,GAAG,EAAE;EACrD,MAAMrI,WAAkB,GAAGlF,IAAI,CAACc,KAAK,CAACU,MAAM;EAC5C,MAAMgM,iBAAiB,GAAGA,CACxB1J,UAAwB,EACxB2J,IAAY,EACZC,WAAqC,KAClC;IACH,MAAMC,gBAAgB,GAAGpM,wBAAwB,CAAC2D,WAAW,EAAEuI,IAAI,CAAC;IACpEC,WAAW,CAAC5I,IAAI,CAACvE,WAAC,CAAC+C,oBAAoB,CAAC,GAAG,EAAEqK,gBAAgB,EAAE7J,UAAU,CAAC,CAAC;IAC3E,OAAOvD,WAAC,CAACe,SAAS,CAACqM,gBAAgB,CAAC;EACtC,CAAC;EAED,IAAIpH,cAA4B;EAChC,IAAIqH,eAA6B;EACjC,MAAMC,WAAW,IAAAZ,aAAA,GAAGjN,IAAI,CAACnB,IAAI,CAACQ,EAAE,qBAAZ4N,aAAA,CAAc/M,IAAI;EAEtC,MAAM4N,YAAY,GAAG,OAAOjN,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGsB,SAAS;EAI1E,MAAM4L,+BAA+B,GAAIC,SAAsB,IAAK;IAe3D;MACL,IAAI;QACFzN,WAAC,CAACoL,YAAY,CAACqC,SAAS,EAAEnP,IAAI,IAAI;UAChC,IACE0B,WAAC,CAACoG,gBAAgB,CAAC9H,IAAI,CAAC,IACxB0B,WAAC,CAACwG,OAAO,CAAClI,IAAI,CAAC,IACf0B,WAAC,CAAC0N,iBAAiB,CAACpP,IAAI,CAAC,IACzB0B,WAAC,CAAC2N,iBAAiB,CAACrP,IAAI,CAAC,IACzB0B,WAAC,CAAC+D,YAAY,CAACzF,IAAI,EAAE;YAAEqB,IAAI,EAAE;UAAY,CAAC,CAAC,IAC1C2N,WAAW,IAAItN,WAAC,CAAC+D,YAAY,CAACzF,IAAI,EAAE;YAAEqB,IAAI,EAAE2N;UAAY,CAAC,CAAE,IAC3DtN,WAAC,CAAC4N,cAAc,CAACtP,IAAI,CAAC,IAAIA,IAAI,CAACuP,IAAI,CAAClO,IAAI,KAAK,QAAS,EACvD;YAEA,MAAM,IAAI;UACZ;QACF,CAAC,CAAC;QACF,OAAO,KAAK;MACd,CAAC,CAAC,OAAAmO,QAAA,EAAM;QACN,OAAO,IAAI;MACb;IACF;EACF,CAAC;EAED,MAAMC,oBAA8B,GAAG,EAAE;EAIzC,KAAK,MAAM9L,OAAO,IAAItD,IAAI,EAAE;IAC1B,IAAI,CAACqL,6BAA6B,CAAC/H,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,MAAM+L,WAAW,GAAG/L,OAAO,CAAC3D,IAAI;IAEhC,IAAI,CAAC0P,WAAW,CAACC,MAAM,IAAIjO,WAAC,CAACqL,aAAa,CAAC2C,WAAW,CAACxM,GAAG,CAAC,EAAE;MAC3DuM,oBAAoB,CAACxJ,IAAI,CAACyJ,WAAW,CAACxM,GAAG,CAAC1C,EAAE,CAACa,IAAI,CAAC;IACpD;IAEA,IAAIuO,WAAW,CAACF,WAAW,CAAC,EAAE;MAC5B,QAAQA,WAAW,CAACxN,IAAI;QACtB,KAAK,eAAe;UAElBiM,eAAe,CAAC0B,aAAa,CAC3BlM,OAAO,EACP2I,KACF,CAAC;UACD;QACF,KAAK,sBAAsB;UAEzB6B,eAAe,CAAC2B,oBAAoB,CAClCnM,OAAO,EACP2I,KACF,CAAC;UACD;QACF,KAAK,uBAAuB;UAE1B6B,eAAe,CAAC4B,qBAAqB,CACnCpM,OAAO,EACP2I,KACF,CAAC;UACD,IAAItI,OAAO,KAAK,SAAS,EAAE;YACzB;UACF;QAEF;UACE,IAAI0L,WAAW,CAACC,MAAM,EAAE;YACtBZ,eAAe,WAAfA,eAAe,GAAfA,eAAe,GAAKrM,wBAAwB,CAC1C2D,WAAW,EACX,YACF,CAAC;UACH,CAAC,MAAM;YACLqB,cAAc,WAAdA,cAAc,GAAdA,cAAc,GAAKhF,wBAAwB,CACzC2D,WAAW,EACX,WACF,CAAC;UACH;UACA;MACJ;MACAiI,oBAAoB,GAAG,IAAI;MAC3BE,oBAAoB,KAApBA,oBAAoB,GAAKkB,WAAW,CAACxP,UAAU,CAACI,IAAI,CAClD4O,+BACF,CAAC;IACH,CAAC,MAAM,IAAIQ,WAAW,CAACxN,IAAI,KAAK,uBAAuB,EAAE;MAEvDiM,eAAe,CAAC4B,qBAAqB,CACnCpM,OAAO,EACP2I,KACF,CAAC;MACD,MAAM;QAAEpJ,GAAG;QAAEC,KAAK;QAAEwM,MAAM,EAAEvM,QAAQ;QAAE8J;MAAS,CAAC,GAAGwC,WAAW;MAE9D,MAAMM,KAAK,GAAGvB,uBAAuB,CAAC,CAAC;MACvC,MAAMwB,QAAQ,GAAGhN,qBAAqB,CAAC+M,KAAK,EAAE7M,KAAK,EAAEC,QAAQ,CAAC;MAC9D,MAAM8M,OAAO,GAAGvM,OAAO,CAACpB,GAAG,CAAC,KAAK,CAAC;MAClC,MAAM,CAACQ,OAAO,CAAC,GAAGY,OAAO,CAACnB,WAAW,CAACyN,QAAQ,CAAC;MAE/C,IAAIrM,SAAS,EAAEC,SAAS;MACxB,IAAIqJ,QAAQ,IAAI,CAACgD,OAAO,CAAC1K,oBAAoB,CAAC,CAAC,EAAE;QAC/C5B,SAAS,GAAG,IAAA2C,wBAAkB,EAC5BiG,uBAAuB,CAACF,KAAK,EAAEpJ,GAAmB,CAAC,EACnDmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAE;QACF3C,SAAS,GAAGnC,WAAC,CAACe,SAAS,CAACmB,SAAS,CAACgC,IAAoB,CAAC;MACzD,CAAC,MAAM;QACLhC,SAAS,GAAGlC,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC;QAC5BW,SAAS,GAAGnC,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC;MAC9B;MAEAM,yBAAyB,CAACrC,IAAI,EAAEa,SAAS,CAAC;MAE1C0B,oBAAoB,CAClBvC,IAAI,CAACnB,IAAI,CAACQ,EAAE,EACZuC,OAAO,EACPa,SAAS,EACTC,SAAS,EACTmM,KAAK,EACL9C,QAAQ,EACR9J,QAAQ,EACRY,OACF,CAAC;IACH;IAEA,IAAI,UAAU,IAAIL,OAAO,CAAC3D,IAAI,IAAI2D,OAAO,CAAC3D,IAAI,CAACkN,QAAQ,EAAE;MACvDqB,0BAA0B,KAA1BA,0BAA0B,GAAK,CAAClI,WAAW,CAACjD,QAAQ,CAACO,OAAO,CAAC3D,IAAI,CAACkD,GAAG,CAAC;IACxE;EACF;EAEA,IAAI,CAACmL,eAAe,IAAI,CAACC,oBAAoB,EAAE;IAC7C,IAAI,CAACnN,IAAI,CAACnB,IAAI,CAACQ,EAAE,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;MAClDb,IAAI,CAACnB,IAAI,CAACQ,EAAE,GAAGkB,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IACxC;IACA,IAAIiN,YAAY,EAAE;MAChB9N,IAAI,CAACnB,IAAI,CAACK,IAAI,CAACA,IAAI,CAACK,OAAO,CACzBqI,gCAAgC,CAAC,CAC/BsD,yBAAyB,CAACC,KAAK,EAAE2C,YAAY,CAAC,CAC/C,CACH,CAAC;IACH;IAEA;EACF;EAEA,MAAMkB,oBAAqC,GAAG,EAAE;EAEhD,IAAI/I,eAAoD;EACxD,MAAMyG,uBAAuB,GAAG,IAAI7M,GAAG,CAAS,CAAC;EAEjD,IAAIoP,cAA4B,EAAEC,YAA0B;EAC5D,IAAIC,mBAAwC,GAAG,IAAI;EAUnD,SAASC,gBAAgBA,CAACrQ,UAAyB,EAA0B;IAC3E,IAAIsQ,cAAc,GAAG,KAAK;IAC1B,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAM1G,cAAuC,GAAG,EAAE;IAClD,KAAK,MAAMoF,SAAS,IAAIjP,UAAU,EAAE;MAClC,MAAM;QAAE+E;MAAW,CAAC,GAAGkK,SAAS;MAChC,IAAIuB,MAAM;MACV,KACG1M,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDtC,WAAC,CAACiP,kBAAkB,CAAC1L,UAAU,CAAC,EAChC;QACA,IAAIvD,WAAC,CAACwG,OAAO,CAACjD,UAAU,CAACyL,MAAM,CAAC,EAAE;UAChCA,MAAM,GAAGhP,WAAC,CAACwC,cAAc,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAImC,WAAW,CAACjD,QAAQ,CAAC6B,UAAU,CAACyL,MAAM,CAAC,EAAE;UAClDA,MAAM,GAAGhP,WAAC,CAACe,SAAS,CAACwC,UAAU,CAACyL,MAAM,CAAC;QACzC,CAAC,MAAM;UACLJ,mBAAmB,WAAnBA,mBAAmB,GAAnBA,mBAAmB,GAAK5N,wBAAwB,CAAC2D,WAAW,EAAE,KAAK,CAAC;UACpEqK,MAAM,GAAGhP,WAAC,CAAC+C,oBAAoB,CAC7B,GAAG,EACH/C,WAAC,CAACe,SAAS,CAAC6N,mBAAmB,CAAC,EAChCrL,UAAU,CAACyL,MACb,CAAC;UACDzL,UAAU,CAACyL,MAAM,GAAGhP,WAAC,CAACe,SAAS,CAAC6N,mBAAmB,CAAC;QACtD;MACF;MACAvG,cAAc,CAAC9D,IAAI,CAACyK,MAAM,CAAC;MAC3BF,cAAc,KAAdA,cAAc,GAAK,CAACnK,WAAW,CAACjD,QAAQ,CAAC6B,UAAU,CAAC;MACpDwL,aAAa,KAAbA,aAAa,GAAKvB,+BAA+B,CAACC,SAAS,CAAC;IAC9D;IACA,OAAO;MAAEqB,cAAc;MAAEC,aAAa;MAAE1G;IAAe,CAAC;EAC1D;EAEA,MAAM6G,uBAAuB,GAC3BrC,0BAA0B,IAGtBC,oBAAoB,IAAIxK,OAAO,KAAK,SAAU;EAEpD,IAAI6M,8BAA8B,GAAG,KAAK;EAC1C,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,gBAAgC,GAAG,EAAE;EACzC,IAAIC,kBAAgC;EACpC,IAAIC,sBAAgD,GAAG,EAAE;EACzD,IAAI5C,eAAe,EAAE;IACnB+B,cAAc,GAAG1N,wBAAwB,CAAC2D,WAAW,EAAE,WAAW,CAAC;IACnEwK,8BAA8B,GAAG1P,IAAI,CAAC+P,kBAAkB,CAAC,CAAC;IAC1D,CAAC;MAAE1Q,EAAE,EAAE6P,YAAY;MAAElP;IAAK,CAAC,GAAGY,mBAAmB,CAACZ,IAAI,EAAEa,SAAS,CAAC;IAElEb,IAAI,CAACnB,IAAI,CAACE,UAAU,GAAG,IAAI;IAE3B,MAAMiR,uBAAuB,GAAG9C,eAAe,CAAC/N,IAAI,CAACuM,gBAAgB,CAAC;IACtE,MAAM;MAAE2D,cAAc;MAAEC,aAAa;MAAE1G;IAAe,CAAC,GACrDwG,gBAAgB,CAAClC,eAAe,CAAC;IAEnC,MAAM;MAAE/D,QAAQ;MAAEH;IAAK,CAAC,GAAGL,sBAAsB,CAC/CuE,eAAe,EACftE,cAAc,EACd/F,OACF,CAAC;IACD8M,oBAAoB,GAAGxG,QAAQ,GAAG,CAAC,GAAG,CAAC;IACvCyG,gBAAgB,GAAG5G,IAAI;IAEvB,IACEsG,aAAa,IACZD,cAAc,IAAII,uBAAwB,IAC3CO,uBAAuB,EACvB;MACAH,kBAAkB,GAAGrC,iBAAiB,CACpCjN,WAAC,CAAC+I,eAAe,CAACsG,gBAAgB,CAAC,EACnC,WAAW,EACXrC,gBACF,CAAC;IACH;IAEA,IAAI,CAACJ,oBAAoB,EAAE;MAGzB,KAAK,MAAM3K,OAAO,IAAIxC,IAAI,CAACoB,GAAG,CAAC,WAAW,CAAC,EAAE;QAC3C,MAAM;UAAEvC;QAAK,CAAC,GAAG2D,OAAO;QACxB,MAAMI,UAAU,GAAG,UAAU,IAAI/D,IAAI,IAAIA,IAAI,CAACkN,QAAQ;QACtD,IAAInJ,UAAU,EAAE;UACd,IAAIJ,OAAO,CAACyN,eAAe,CAAC;YAAEzB,MAAM,EAAE;UAAK,CAAC,CAAC,EAAE;YAC7C,IAAI,CAAChM,OAAO,CAACpB,GAAG,CAAC,KAAK,CAAC,CAACiD,oBAAoB,CAAC,CAAC,EAAE;cAC9C,MAAMtC,GAAG,GAAIlD,IAAI,CAAqBkD,GAAG;cACzC,MAAMoD,eAAe,GAAG,IAAAC,wBAAkB,EACxCrD,GAAG,EACHmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;cACD,IAAIF,eAAe,IAAI,IAAI,EAAE;gBAI3BtG,IAAI,CAACkD,GAAG,GAAGxB,WAAC,CAACe,SAAS,CAAC6D,eAAe,CAACV,IAAI,CAAC;gBAC5CqL,sBAAsB,CAAChL,IAAI,CAACK,eAAe,CAAC;cAC9C;YACF;UACF,CAAC,MAAM,IAAI2K,sBAAsB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;YAC5C4F,+BAA+B,CAC7BkL,sBAAsB,EACtBtN,OACF,CAAC;YACDsN,sBAAsB,GAAG,EAAE;UAC7B;QACF;MACF;IACF;EACF,CAAC,MAAM;IACLzN,yBAAyB,CAACrC,IAAI,EAAEa,SAAS,CAAC;IAC1CqO,YAAY,GAAG3O,WAAC,CAACe,SAAS,CAACtB,IAAI,CAACnB,IAAI,CAACQ,EAAE,CAAC;EAC1C;EAEA,IAAI6Q,uBAAsC;EAC1C,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,IAAIC,2BAA2B,GAAG,EAAE;EACpC,IAAIC,iCAAiD,GAAG,EAAE;EAE1D,IAAIlD,oBAAoB,EAAE;IACxB,IAAI5G,cAAc,EAAE;MAClB,MAAMJ,aAAa,GAAG5F,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACe,SAAS,CAACiF,cAAc,CAAC,EAAE,CAClEhG,WAAC,CAACwC,cAAc,CAAC,CAAC,CACnB,CAAC;MACFqN,2BAA2B,CAACtL,IAAI,CAACqB,aAAa,CAAC;IACjD;IACA,KAAK,MAAM3D,OAAO,IAAItD,IAAI,EAAE;MAC1B,IAAI,CAACqL,6BAA6B,CAAC/H,OAAO,CAAC,EAAE;QAC3C,IACE6N,iCAAiC,CAACrR,MAAM,GAAG,CAAC,IAC5CwD,OAAO,CAAC8N,aAAa,CAAC,CAAC,EACvB;UACAzK,+BAA+B,CAC7BwK,iCAAiC,EACjC7N,OACF,CAAC;UACD6N,iCAAiC,GAAG,EAAE;QACxC;QACA;MACF;MAEA,MAAM;QAAExR;MAAK,CAAC,GAAG2D,OAAO;MACxB,MAAMzD,UAAU,GAAGF,IAAI,CAACE,UAAU;MAElC,MAAME,aAAa,GAAG,CAAC,EAACF,UAAU,YAAVA,UAAU,CAAEC,MAAM;MAE1C,MAAM4D,UAAU,GAAG,UAAU,IAAI/D,IAAI,IAAIA,IAAI,CAACkN,QAAQ;MAEtD,IAAI7L,IAAI,GAAG,aAAa;MAExB,IAAIrB,IAAI,CAACkD,GAAG,CAAChB,IAAI,KAAK,aAAa,EAAE;QACnCb,IAAI,GAAGrB,IAAI,CAACkD,GAAG,CAAC1C,EAAE,CAACa,IAAI;MACzB,CAAC,MAAM,IAAI,CAAC0C,UAAU,IAAI/D,IAAI,CAACkD,GAAG,CAAChB,IAAI,KAAK,YAAY,EAAE;QACxDb,IAAI,GAAGrB,IAAI,CAACkD,GAAG,CAAC7B,IAAI;MACtB;MACA,IAAIuJ,eAAgE;MACpE,IAAID,kBAAkB;MAEtB,IAAIvK,aAAa,EAAE;QACjB,MAAM;UAAEoQ,cAAc;UAAEC,aAAa;UAAE1G;QAAe,CAAC,GACrDwG,gBAAgB,CAACrQ,UAAU,CAAC;QAC9B,MAAM;UAAEiK,IAAI;UAAEG;QAAS,CAAC,GAAGR,sBAAsB,CAC/C5J,UAAU,EACV6J,cAAc,EACd/F,OACF,CAAC;QACD2G,kBAAkB,GAAGL,QAAQ;QAC7BM,eAAe,GAAGT,IAAI,CAAChK,MAAM,KAAK,CAAC,GAAGgK,IAAI,CAAC,CAAC,CAAC,GAAGzI,WAAC,CAAC+I,eAAe,CAACN,IAAI,CAAC;QACvE,IAAIsG,aAAa,IAAKD,cAAc,IAAII,uBAAwB,EAAE;UAChEhG,eAAe,GAAG+D,iBAAiB,CACjC/D,eAAe,EACfvJ,IAAI,GAAG,MAAM,EACb4P,sBACF,CAAC;QACH;MACF;MAEA,IAAIlN,UAAU,EAAE;QACd,IAAI,CAACJ,OAAO,CAACpB,GAAG,CAAC,KAAK,CAAC,CAACiD,oBAAoB,CAAC,CAAC,EAAE;UAC9C,MAAMtC,GAAG,GAAGlD,IAAI,CAACkD,GAAmB;UACpC,MAAMoD,eAAe,GAAG,IAAAC,wBAAkB,EACxCnG,aAAa,GAAGoM,uBAAuB,CAACF,KAAK,EAAEpJ,GAAG,CAAC,GAAGA,GAAG,EACzDmD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;UACD,IAAIF,eAAe,IAAI,IAAI,EAAE;YAI3B,IAAI+H,eAAe,IAAI1K,OAAO,CAACyN,eAAe,CAAC;cAAEzB,MAAM,EAAE;YAAK,CAAC,CAAC,EAAE;cAChE3P,IAAI,CAACkD,GAAG,GAAGxB,WAAC,CAACe,SAAS,CAAC6D,eAAe,CAACV,IAAI,CAAC;cAC5CqL,sBAAsB,CAAChL,IAAI,CAACK,eAAe,CAAC;YAC9C,CAAC,MAAM;cACLtG,IAAI,CAACkD,GAAG,GAAGoD,eAAe;YAC5B;UACF;QACF;MACF;MAEA,MAAM;QAAEpD,GAAG;QAAEyM,MAAM,EAAEvM;MAAS,CAAC,GAAGpD,IAAI;MAEtC,MAAM0R,SAAS,GAAGxO,GAAG,CAAChB,IAAI,KAAK,aAAa;MAE5C,MAAMuG,IAAI,GAAGgB,cAAc,CAAC9F,OAAO,CAAC;MAEpC,IAAI+N,SAAS,IAAI,CAACtO,QAAQ,EAAE;QAC1B,IAAIhD,aAAa,EAAE;UACjBkR,8BAA8B,GAAG,IAAI;QACvC;QACA,IAAI5P,WAAC,CAACiQ,sBAAsB,CAAC3R,IAAI,CAAC,IAAI,CAACqR,uBAAuB,EAAE;UAC9DA,uBAAuB,GAAGnO,GAAG;QAC/B;MACF;MAEA,IAAIS,OAAO,CAACiO,aAAa,CAAC;QAAEnJ,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QAClDrB,eAAe,GAAGzD,OAAO;MAC3B;MAEA,IAAIqH,MAAsB;MAC1B,IAAI5K,aAAa,EAAE;QACjB,IAAIyK,cAEH;QAED,IAAIgH,QAAsB;QAE1B,IAAI9N,UAAU,EAAE;UACd8N,QAAQ,GAAGtM,sBAAsB,CAC/B5B,OAAO,CAACpB,GAAG,CAAC,KAAK,CACnB,CAAC;QACH,CAAC,MAAM,IAAIW,GAAG,CAAChB,IAAI,KAAK,aAAa,EAAE;UACrC2P,QAAQ,GAAGnQ,WAAC,CAACyL,aAAa,CAACjK,GAAG,CAAC1C,EAAE,CAACa,IAAI,CAAC;QACzC,CAAC,MAAM,IAAI6B,GAAG,CAAChB,IAAI,KAAK,YAAY,EAAE;UACpC2P,QAAQ,GAAGnQ,WAAC,CAACyL,aAAa,CAACjK,GAAG,CAAC7B,IAAI,CAAC;QACtC,CAAC,MAAM;UACLwQ,QAAQ,GAAGnQ,WAAC,CAACe,SAAS,CAACS,GAAmB,CAAC;QAC7C;QAEA,IAAIuF,IAAI,KAAKS,QAAQ,EAAE;UACrB,MAAM;YAAE/F;UAAM,CAAC,GAAGQ,OAAO,CAAC3D,IAA+B;UAEzD,MAAMwL,MAAsB,GACOxH,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC1B,WAAC,CAACwC,cAAc,CAAC,CAAC,CAAC;UAE1B,IAAIf,KAAK,EAAE;YACTqI,MAAM,CAACvF,IAAI,CAACvE,WAAC,CAACe,SAAS,CAACU,KAAK,CAAC,CAAC;UACjC;UAEA,MAAM6M,KAAK,GAAGvB,uBAAuB,CAAC,CAAC;UACvC,MAAMqD,cAAc,GAAGpP,wBAAwB,CAC7C2D,WAAW,EACX,QAAQhF,IAAI,EACd,CAAC;UACD,MAAM0Q,QAAQ,GAAGrQ,WAAC,CAACkG,cAAc,CAC/BlG,WAAC,CAACe,SAAS,CAACqP,cAAc,CAAC,EAC3BtG,MACF,CAAC;UAED,MAAMyE,QAAQ,GAAGhN,qBAAqB,CAAC+M,KAAK,EAAE+B,QAAQ,EAAE3O,QAAQ,CAAC;UACjE,MAAM,CAACL,OAAO,CAAC,GAAGY,OAAO,CAACnB,WAAW,CAACyN,QAAQ,CAAC;UAE/C,IAAIyB,SAAS,EAAE;YACb7G,cAAc,GAAG9F,wBAAwB,CAACiL,KAAK,EAAEhM,OAAO,CAAC;YAEzD,MAAMoH,KAAK,GAAG1I,wBAAwB,CAAC2D,WAAW,EAAE,OAAOhF,IAAI,EAAE,CAAC;YAClE,MAAMgK,KAAK,GAAG3I,wBAAwB,CAAC2D,WAAW,EAAE,OAAOhF,IAAI,EAAE,CAAC;YAElE8J,mBAAmB,CAACnH,OAAO,EAAEjB,OAAO,EAAEG,GAAG,EAAEkI,KAAK,EAAEC,KAAK,EAAEjI,QAAQ,CAAC;YAElE4H,MAAM,GAAG,CAAC8G,cAAc,EAAE1G,KAAK,EAAEC,KAAK,CAAC;UACzC,CAAC,MAAM;YACL7H,yBAAyB,CAACrC,IAAI,EAAEa,SAAS,CAAC;YAC1C0B,oBAAoB,CAClBvC,IAAI,CAACnB,IAAI,CAACQ,EAAE,EACZuC,OAAO,EACPrB,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EAChBxB,WAAC,CAACiE,sBAAsB,CAACzC,GAAG,CAAC,GACzBxB,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC0C,IAAoB,CAAC,GACrClE,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EACpB8M,KAAK,EACLjM,UAAU,EACVX,QAAQ,EACRY,OACF,CAAC;YACDgH,MAAM,GAAG,CAAC8G,cAAc,CAAC;UAC3B;QACF,CAAC,MAAM,IAAIrJ,IAAI,KAAKQ,KAAK,EAAE;UACzB,MAAM+I,MAAM,GAAGtP,wBAAwB,CAAC2D,WAAW,EAAE,QAAQhF,IAAI,EAAE,CAAC;UACpE,MAAM4Q,SAAS,GACbtO,OAAO,CACPpB,GAAG,CAAC,OAAO,CAAC;UAEd,MAAM2P,IAAoB,GACSlO,OAAO,KAAK,SAAS,IAAKZ,QAAQ,GAC/D,EAAE,GACF,CAAC1B,WAAC,CAACwC,cAAc,CAAC,CAAC,CAAC;UAC1B,IAAI+N,SAAS,CAACjS,IAAI,EAAEkS,IAAI,CAACjM,IAAI,CAACgM,SAAS,CAACjS,IAAI,CAAC;UAE7CiS,SAAS,CAACzP,WAAW,CAACd,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACe,SAAS,CAACuP,MAAM,CAAC,EAAEE,IAAI,CAAC,CAAC;UAElElH,MAAM,GAAG,CAACgH,MAAM,CAAC;UAEjB,IAAIN,SAAS,EAAE;YACb7G,cAAc,GAAG9F,wBAAwB,CAAC7B,GAAG,EAAEc,OAAO,CAAC;UACzD;QACF,CAAC,MAAM,IAAI0N,SAAS,EAAE;UACpB,MAAMS,MAAM,GAAGzP,wBAAwB,CAAC2D,WAAW,EAAE,QAAQhF,IAAI,EAAE,CAAC;UACpE2J,MAAM,GAAG,CAACmH,MAAM,CAAC;UAEjB,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtCpE,aAAa;YACbqE,UAAU,EAAE3O,OAAyC;YACrD4O,SAAS,EAAElC,YAAY;YACvBmC,QAAQ,EAAErR,IAAI,CAACnB,IAAI,CAAC8C,UAAU;YAC9B2P,IAAI,EAAEnG,KAAK,CAACmG,IAAI;YAChBC,aAAa,EAAErC;UACjB,CAAC,CAAC;UAEF+B,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvB9H,cAAc,GAAG,CACfoB,yCAAyC,CACvCtI,OAAO,CAAC3D,IACV,CAAC,CACF;UAED,IAAIyI,IAAI,KAAKW,MAAM,IAAIX,IAAI,KAAKY,MAAM,EAAE;YACtCiC,mBAAmB,CACjB3H,OAAO,EACPjC,WAAC,CAACe,SAAS,CAACS,GAAG,CAAC,EAChBxB,WAAC,CAACe,SAAS,CAAC0P,MAAM,CAAC,EACnB/O,QACF,CAAC;UACH,CAAC,MAAM;YACL,MAAMpD,IAAI,GAAG2D,OAAO,CAAC3D,IAA4B;YAGjDmB,IAAI,CAACnB,IAAI,CAACK,IAAI,CAACA,IAAI,CAACK,OAAO,CACzBgB,WAAC,CAAC2B,oBAAoB,CAACH,GAAG,EAAExB,WAAC,CAACe,SAAS,CAAC0P,MAAM,CAAC,EAAE,EAAE,EAAEnS,IAAI,CAAC2P,MAAM,CAClE,CAAC;YAED9B,uBAAuB,CAACzM,GAAG,CAAC8B,GAAG,CAAC1C,EAAE,CAACa,IAAI,CAAC;YAExCsC,OAAO,CAACiP,MAAM,CAAC,CAAC;UAClB;QACF;QAEAzC,oBAAoB,CAAClK,IAAI,CAAC;UACxBwC,IAAI;UACJmC,eAAe;UACfD,kBAAkB;UAClBtJ,IAAI,EAAEwQ,QAAQ;UACdzO,QAAQ;UACRyH,cAAc;UACdG;QACF,CAAC,CAAC;QAEF,IAAIrH,OAAO,CAAC3D,IAAI,EAAE;UAChB2D,OAAO,CAAC3D,IAAI,CAACE,UAAU,GAAG,IAAI;QAChC;MACF;MAEA,IAAI6D,UAAU,IAAIkN,sBAAsB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;QACnD,IAAIkO,eAAe,IAAI1K,OAAO,CAACyN,eAAe,CAAC;UAAEzB,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE,CAMlE,CAAC,MAAM;UACL5J,+BAA+B,CAC7BkL,sBAAsB,EACrBxI,IAAI,KAAKS,QAAQ,GACdvF,OAAO,CAACkP,cAAc,CAAC,CAAC,GACxBlP,OACN,CAAC;UACDsN,sBAAsB,GAAG,EAAE;QAC7B;MACF;MAEA,IACEM,2BAA2B,CAACpR,MAAM,GAAG,CAAC,IACtC,CAACiD,QAAQ,KACRqF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClC0K,2BAA2B,EAC3B5N,OACF,CAAC;QACD4N,2BAA2B,GAAG,EAAE;MAClC;MAEA,IACEC,iCAAiC,CAACrR,MAAM,GAAG,CAAC,IAC5CiD,QAAQ,KACPqF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClC2K,iCAAiC,EACjC7N,OACF,CAAC;QACD6N,iCAAiC,GAAG,EAAE;MACxC;MAEA,IAAIpR,aAAa,IAAI4D,OAAO,KAAK,SAAS,EAAE;QAC1C,IAAIyE,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,EAAE;UACvC,MAAM4J,WAAW,GAAGpQ,wBAAwB,CAC1C2D,WAAW,EACX,cAAchF,IAAI,EACpB,CAAC;UACD2J,MAAM,CAAC/E,IAAI,CAAC6M,WAAW,CAAC;UACxB,MAAMC,aAAa,GAAGrR,WAAC,CAACkG,cAAc,CACpClG,WAAC,CAACe,SAAS,CAACqQ,WAAW,CAAC,EACxB1P,QAAQ,GAAG,EAAE,GAAG,CAAC1B,WAAC,CAACwC,cAAc,CAAC,CAAC,CACrC,CAAC;UACD,IAAI,CAACd,QAAQ,EAAE;YACbmO,2BAA2B,CAACtL,IAAI,CAAC8M,aAAa,CAAC;UACjD,CAAC,MAAM;YACLvB,iCAAiC,CAACvL,IAAI,CAAC8M,aAAa,CAAC;UACvD;QACF;MACF;IACF;EACF;EAEA,IAAI9B,sBAAsB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;IACrC,MAAM6S,QAAQ,GAAG7R,IAAI,CAACoB,GAAG,CAAC,WAAW,CAAC;IACtC,IAAI0Q,mBAA8D;IAClE,KAAK,IAAI7I,CAAC,GAAG4I,QAAQ,CAAC7S,MAAM,GAAG,CAAC,EAAEiK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,MAAMjJ,IAAI,GAAG6R,QAAQ,CAAC5I,CAAC,CAAC;MACxB,MAAMpK,IAAI,GAAGmB,IAAI,CAACnB,IAAuC;MACzD,IAAIA,IAAI,CAACkN,QAAQ,EAAE;QACjB,IAAImB,eAAe,IAAI3M,WAAC,CAAC0P,eAAe,CAACpR,IAAI,EAAE;UAAE2P,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE;UAChE;QACF;QACAsD,mBAAmB,GAAG9R,IAAiD;QACvE;MACF;IACF;IACA,IAAI8R,mBAAmB,IAAI,IAAI,EAAE;MAC/B9M,8BAA8B,CAC5B8K,sBAAsB,EACtBgC,mBACF,CAAC;MACDhC,sBAAsB,GAAG,EAAE;IAC7B,CAAC,MAAM,CAIP;EACF;EAEA,IAAIM,2BAA2B,CAACpR,MAAM,GAAG,CAAC,EAAE;IAC1C,MAAMwI,cAAc,GAAG,CAAC,CAACxH,IAAI,CAACnB,IAAI,CAAC8C,UAAU;IAC7C,IAAIsE,eAAe,EAAE;MACnB,IAAIuB,cAAc,EAAE;QAClBZ,0CAA0C,CACxCwJ,2BAA2B,EAC3BnK,eAAe,EACfM,cACF,CAAC;MACH,CAAC,MAAM;QACLP,+BAA+B,CAC7BoK,2BAA2B,EAC3BnK,eACF,CAAC;MACH;IACF,CAAC,MAAM;MACLjG,IAAI,CAACnB,IAAI,CAACK,IAAI,CAACA,IAAI,CAACK,OAAO,CACzBgI,gCAAgC,CAC9B6I,2BAA2B,EAC3B5I,cACF,CACF,CAAC;IACH;IACA4I,2BAA2B,GAAG,EAAE;EAClC;EAEA,IAAIC,iCAAiC,CAACrR,MAAM,GAAG,CAAC,EAAE;IAChDgB,IAAI,CAACnB,IAAI,CAACK,IAAI,CAACA,IAAI,CAAC4F,IAAI,CACtB8C,gCAAgC,CAACyI,iCAAiC,CACpE,CAAC;IACDA,iCAAiC,GAAG,EAAE;EACxC;EAEA,MAAM0B,0BAA0B,GAC9BxJ,qBAAqB,CAACyG,oBAAoB,CAAC;EAE7C,MAAMgD,kBAAkB,GAAG5I,uBAAuB,CAChBvG,OAAO,KAAK,SAAS,GACjDmM,oBAAoB,GACpB+C,0BAA0B,EAC9BlP,OACF,CAAC;EAED,MAAMoP,aAA6B,GAAGtI,8BAA8B,CAClEoI,0BACF,CAAC;EAED,IAAIxL,cAAc,EAAE;IAClB0L,aAAa,CAACnN,IAAI,CAACyB,cAAc,CAAC;EACpC;EAEA,IAAIqH,eAAe,EAAE;IACnBqE,aAAa,CAACnN,IAAI,CAAC8I,eAAe,CAAC;EACrC;EAEA,MAAMsE,WAA2B,GAAG,EAAE;EACtC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,MAAMC,aAAa,GACjBnD,cAAc,IAAI1O,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACe,SAAS,CAAC2N,cAAc,CAAC,EAAE,EAAE,CAAC;EAErE,IAAIoD,iBAAiB,GAAGrS,IAAI;EAC5B,MAAMsS,aAAa,GAAGtS,IAAI,CAACnB,IAAI;EAE/B,MAAM0T,cAAwC,GAAG,EAAE;EACnD,IAAIrF,eAAe,EAAE;IACnBgF,WAAW,CAACpN,IAAI,CAACoK,YAAY,EAAED,cAAc,CAAC;IAC9C,MAAMuD,OAIH,GAAG,EAAE;IACRxS,IAAI,CAACoB,GAAG,CAAC,WAAW,CAAC,CAACqR,OAAO,CAACjQ,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAAC8N,aAAa,CAAC,CAAC,EAAE;QAC3B,IAAIrE,wBAAwB,CAACzJ,OAAO,EAAE8L,oBAAoB,CAAC,EAAE;UAC3D,MAAMoE,oBAAoB,GAAGlF,iBAAiB,CAC5C9C,4BAA4B,CAAClI,OAAO,CAAC3D,IAAI,CAAC,EAC1C,aAAa,EACb0T,cACF,CAAC;UACDlC,iCAAiC,CAACvL,IAAI,CACpCvE,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAAC4C,gBAAgB,CAACuP,oBAAoB,EAAEnS,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC9D,CAACF,WAAC,CAACwC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH,CAAC,MAAM;UACLsN,iCAAiC,CAACvL,IAAI,CACpC0F,iBAAiB,CAAChI,OAAO,CAAC3D,IAAI,CAChC,CAAC;QACH;QACA2D,OAAO,CAACiP,MAAM,CAAC,CAAC;QAChB;MACF;MAEA,IACE,CAACjP,OAAO,CAACyN,eAAe,CAAC,CAAC,IAAIzN,OAAO,CAACgO,sBAAsB,CAAC,CAAC,KAC9DhO,OAAO,CAAC3D,IAAI,CAAC2P,MAAM,EACnB;QACA,MAAMsC,SAAS,GACbtO,OAAO,CACPpB,GAAG,CAAC,OAAO,CAAC;QACd,IAAI6K,wBAAwB,CAAC6E,SAAS,EAAExC,oBAAoB,CAAC,EAAE;UAC7D,MAAMqE,mBAAmB,GAAGnF,iBAAiB,CAC3C5C,yBAAyB,CAACkG,SAAS,CAACjS,IAAI,CAAC,EACzC,YAAY,EACZ0T,cACF,CAAC;UACDzB,SAAS,CAACzP,WAAW,CACnBd,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAAC4C,gBAAgB,CAACwP,mBAAmB,EAAEpS,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC7D,CAACF,WAAC,CAACwC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH;QACA,IAAIsN,iCAAiC,CAACrR,MAAM,GAAG,CAAC,EAAE;UAChD0G,oCAAoC,CAClC2K,iCAAiC,EACjC7N,OACF,CAAC;UACD6N,iCAAiC,GAAG,EAAE;QACxC;QACA7N,OAAO,CAAC3D,IAAI,CAAC2P,MAAM,GAAG,KAAK;QAC3BgE,OAAO,CAAC1N,IAAI,CAACtC,OAAO,CAAC3D,IAAI,CAAC;QAC1B2D,OAAO,CAACiP,MAAM,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIjP,OAAO,CAACoQ,oBAAoB,CAAC;QAAEpE,MAAM,EAAE;MAAK,CAAC,CAAC,EAAE;QAGzD,IAAIvC,wBAAwB,CAACzJ,OAAO,EAAE8L,oBAAoB,CAAC,EAAE;UAC3D,MAAM2C,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtCpE,aAAa;YACbqE,UAAU,EAAE3O,OAAO;YACnB4O,SAAS,EAAElC,YAAY;YACvBmC,QAAQ,EAAErR,IAAI,CAACnB,IAAI,CAAC8C,UAAU;YAC9B2P,IAAI,EAAEnG,KAAK,CAACmG,IAAI;YAChBC,aAAa,EAAErC;UACjB,CAAC,CAAC;UAEF+B,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvB,MAAMqB,uBAAuB,GAAGrF,iBAAiB,CAC/C1C,yCAAyC,CAACtI,OAAO,CAAC3D,IAAI,CAAC,EACvD2D,OAAO,CAACpB,GAAG,CAAC,QAAQ,CAAC,CAACvC,IAAI,CAACqB,IAAI,EAC/BqS,cACF,CAAC;UAED,IAAIxF,oBAAoB,EAAE;YACxBvK,OAAO,CAAC3D,IAAI,CAACwL,MAAM,GAAG,CAAC9J,WAAC,CAACoH,WAAW,CAACpH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1D+B,OAAO,CAAC3D,IAAI,CAACK,IAAI,GAAGqB,WAAC,CAAC0C,cAAc,CAAC,CACnC1C,WAAC,CAAC2C,eAAe,CACf3C,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAAC4C,gBAAgB,CAChB0P,uBAAuB,EACvBtS,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACwC,cAAc,CAAC,CAAC,EAAExC,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAC1C,CACF,CAAC,CACF,CAAC;UACJ,CAAC,MAAM;YACL+B,OAAO,CAAC3D,IAAI,CAACwL,MAAM,GAAG7H,OAAO,CAAC3D,IAAI,CAACwL,MAAM,CAACpD,GAAG,CAAC,CAAC6L,CAAC,EAAE7J,CAAC,KAAK;cACtD,IAAI1I,WAAC,CAACwS,aAAa,CAACD,CAAC,CAAC,EAAE;gBACtB,OAAOvS,WAAC,CAACoH,WAAW,CAACpH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC;cAC3C,CAAC,MAAM;gBACL,OAAOF,WAAC,CAACE,UAAU,CAAC,GAAG,GAAGwI,CAAC,CAAC;cAC9B;YACF,CAAC,CAAC;YACFzG,OAAO,CAAC3D,IAAI,CAACK,IAAI,GAAGqB,WAAC,CAAC0C,cAAc,CAAC,CACnC1C,WAAC,CAAC2C,eAAe,CACf3C,WAAC,CAACkG,cAAc,CACdlG,WAAC,CAAC4C,gBAAgB,CAChB0P,uBAAuB,EACvBtS,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACwC,cAAc,CAAC,CAAC,EAAExC,WAAC,CAACE,UAAU,CAAC,WAAW,CAAC,CAChD,CACF,CAAC,CACF,CAAC;UACJ;QACF;QACA+B,OAAO,CAAC3D,IAAI,CAAC2P,MAAM,GAAG,KAAK;QAC3BgE,OAAO,CAAC1N,IAAI,CAACtC,OAAO,CAAC3D,IAAI,CAAC;QAC1B2D,OAAO,CAACiP,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAIe,OAAO,CAACxT,MAAM,GAAG,CAAC,IAAIqR,iCAAiC,CAACrR,MAAM,GAAG,CAAC,EAAE;MACtE,MAAMgU,YAAY,GAAGnP,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClD,wBAAwBoH,KAAK,CAACC,SAAS,CAAC,UAAU,CAAC;AACnD,OAA4B;MACtB4H,YAAY,CAAC9T,IAAI,CAACA,IAAI,GAAG,CAOvBqB,WAAC,CAAC6B,aAAa,CACb7B,WAAC,CAAC0S,YAAY,CAACX,aAAa,CAAC,EAC7BnQ,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CAAC,EACD,GAAGqQ,OAAO,CACX;MAED,MAAMU,eAA+B,GAAG,EAAE;MAE1C,MAAMC,OAAO,GAAG5S,WAAC,CAAC6S,aAAa,CAACJ,YAAY,EAAE,EAAE,CAAC;MAEjD,IAAI3C,iCAAiC,CAACrR,MAAM,GAAG,CAAC,EAAE;QAChDkU,eAAe,CAACpO,IAAI,CAAC,GAAGuL,iCAAiC,CAAC;MAC5D;MACA,IAAI+B,aAAa,EAAE;QACjBD,iBAAiB,GAAG,IAAI;QACxBe,eAAe,CAACpO,IAAI,CAACsN,aAAa,CAAC;MACrC;MACA,IAAIc,eAAe,CAAClU,MAAM,GAAG,CAAC,EAAE;QAC9BkU,eAAe,CAAC3T,OAAO,CACrBgB,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAClH,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC,CACzD,CAAC;QAGD8D,YAAY,CAAC9T,IAAI,CAACA,IAAI,CAAC4F,IAAI,CACzByC,gCAAgC,CAC9B2L,eAAe,EACM,KACvB,CACF,CAAC;MACH,CAAC,MAAM;QACLC,OAAO,CAACE,SAAS,CAACvO,IAAI,CAACvE,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC;MACnD;MAEA,MAAM,CAACtN,OAAO,CAAC,GAAG5B,IAAI,CAACqB,WAAW,CAAC8R,OAAO,CAAC;MAG3Cd,iBAAiB,GACfzQ,OAAO,CAACR,GAAG,CAAC,QAAQ,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CACjCA,GAAG,CAAC,YAAY,CAAC;IACrB;EACF;EACA,IAAI,CAAC+Q,iBAAiB,IAAIC,aAAa,EAAE;IACvCpS,IAAI,CAACnB,IAAI,CAACK,IAAI,CAACA,IAAI,CAAC4F,IAAI,CACtBvE,WAAC,CAACsH,WAAW,CAAC,CAACtH,WAAC,CAAC8C,mBAAmB,CAAC+O,aAAa,CAAC,CAAC,CACtD,CAAC;EACH;EAEA,IAAI;IAAEzQ;EAAW,CAAC,GAAG2Q,aAAa;EAClC,IACE3Q,UAAU,KAERkB,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,GACvB;IACA,MAAMxD,EAAE,GAAGW,IAAI,CAACc,KAAK,CAACwS,qBAAqB,CAAC3R,UAAU,CAAC;IACvD,IAAItC,EAAE,EAAE;MACNiT,aAAa,CAAC3Q,UAAU,GAAGpB,WAAC,CAAC+C,oBAAoB,CAAC,GAAG,EAAEjE,EAAE,EAAEsC,UAAU,CAAC;MACtEA,UAAU,GAAGtC,EAAE;IACjB;EACF;EAEA,MAAMkU,qBAAqB,GAAGhT,WAAC,CAACsH,WAAW,CAAC,EAAE,CAAC;EAC/CyK,aAAa,CAACpT,IAAI,CAACA,IAAI,CAACK,OAAO,CAACgU,qBAAqB,CAAC;EACtD,MAAMC,aAAa,GAAGD,qBAAqB,CAACrU,IAAI;EAChD,IAAI4Q,sBAAsB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;IACrC,MAAM6S,QAAQ,GAAGQ,iBAAiB,CAACjR,GAAG,CAAC,WAAW,CAAC;IACnD,IAAIqS,kBAA6D;IACjE,KAAK,MAAMzT,IAAI,IAAI6R,QAAQ,EAAE;MAC3B,IACE,CAAC7R,IAAI,CAACiQ,eAAe,CAAC,CAAC,IAAIjQ,IAAI,CAACyQ,aAAa,CAAC,CAAC,KAC9CzQ,IAAI,CAACnB,IAAI,CAAmByI,IAAI,KAAK,aAAa,EACnD;QACAmM,kBAAkB,GAAGzT,IAAI;QACzB;MACF;IACF;IACA,IAAIyT,kBAAkB,IAAI,IAAI,EAAE;MAE9B3H,oBAAoB,CAAC2H,kBAAkB,CAAC;MACxC7O,+BAA+B,CAC7BkL,sBAAsB,EACtB2D,kBACF,CAAC;IACH,CAAC,MAAM;MAILnB,aAAa,CAACpT,IAAI,CAACA,IAAI,CAACK,OAAO,CAC7BgB,WAAC,CAAC6B,aAAa,CACb7B,WAAC,CAACsB,kBAAkB,CAAC,CACnB,GAAGiO,sBAAsB,EACzBvP,WAAC,CAACyL,aAAa,CAAC,GAAG,CAAC,CACrB,CAAC,EACF7J,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CACF,CAAC;MACDqR,aAAa,CAAC1O,IAAI,CAChBvE,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAACqF,eAAe,CACf,QAAQ,EACRrF,WAAC,CAAC4C,gBAAgB,CAAC5C,WAAC,CAACwC,cAAc,CAAC,CAAC,EAAExC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC1D,CACF,CACF,CAAC;IACH;IACAqP,sBAAsB,GAAG,EAAE;EAC7B;EAEA0D,aAAa,CAAC1O,IAAI,CAChBvE,WAAC,CAAC8C,mBAAmB,CACnBqQ,sBAAsB,CACpBzB,aAAa,EACbC,WAAW,EACXF,kBAAkB,EAClBnC,kBAAkB,WAAlBA,kBAAkB,GAAItP,WAAC,CAAC+I,eAAe,CAACsG,gBAAgB,CAAC,EACzDrP,WAAC,CAAC2I,cAAc,CAACyG,oBAAoB,CAAC,EACtCQ,8BAA8B,GAAGD,uBAAuB,GAAG,IAAI,EAC/DpC,YAAY,EACZvN,WAAC,CAACe,SAAS,CAACK,UAAU,CAAC,EACvBwJ,KAAK,EACLtI,OACF,CACF,CACF,CAAC;EACD,IAAI+K,eAAe,EAAE;IACnB4F,aAAa,CAAC1O,IAAI,CAChBvE,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAACkG,cAAc,CAAClG,WAAC,CAACe,SAAS,CAACsM,eAAe,CAAC,EAAE,CAACrN,WAAC,CAACwC,cAAc,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,IAAIwP,cAAc,CAACvT,MAAM,GAAG,CAAC,EAAE;IAC7BwU,aAAa,CAAC1O,IAAI,CAChB,GAAGyN,cAAc,CAACtL,GAAG,CAACC,IAAI,IAAI3G,WAAC,CAAC8C,mBAAmB,CAAC6D,IAAI,CAAC,CAC3D,CAAC;EACH;EAIAlH,IAAI,CAAC2T,YAAY,CAACpG,gBAAgB,CAACtG,GAAG,CAACC,IAAI,IAAI3G,WAAC,CAAC8C,mBAAmB,CAAC6D,IAAI,CAAC,CAAC,CAAC;EAE5E,IAAIwI,8BAA8B,EAAE;IAClC,MAAMkE,gBAAgB,GAAG1O,WAAW,CAAC2O,UAAU,CAAC3E,YAAY,CAAChP,IAAI,CAAC;IAClE,IAAI,CAAC0T,gBAAgB,CAACE,kBAAkB,CAAC9U,MAAM,EAAE;MAE/CgB,IAAI,CAAC2T,YAAY,CACfpT,WAAC,CAACwT,mBAAmB,CAAC,KAAK,EAAE,CAC3BxT,WAAC,CAACyT,kBAAkB,CAACzT,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC,CAChD,CACH,CAAC;IACH,CAAC,MAAM;MACL,MAAM+E,8BAA8B,GAAG/O,WAAW,CAAC5C,qBAAqB,CACtE,GAAG,GAAG4M,YAAY,CAAChP,IACrB,CAAC;MACD,MAAMgU,sBAAsB,GAAGhF,YAAY;MAC3ClP,IAAI,CAACmU,mBAAmB,CAAC,CACvB5T,WAAC,CAACwT,mBAAmB,CAAC,KAAK,EAAE,CAC3BxT,WAAC,CAACyT,kBAAkB,CAACzT,WAAC,CAACe,SAAS,CAAC4S,sBAAsB,CAAC,CAAC,EACzD3T,WAAC,CAACyT,kBAAkB,CAACC,8BAA8B,CAAC,CACrD,CAAC,EACF1T,WAAC,CAAC0C,cAAc,CAAC,CACf1C,WAAC,CAACwT,mBAAmB,CAAC,KAAK,EAAE,CAC3BxT,WAAC,CAACyT,kBAAkB,CAACzT,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC,CAChD,CAAC,EAEFlP,IAAI,CAACnB,IAAI,EACT0B,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAAC+C,oBAAoB,CACpB,GAAG,EACH/C,WAAC,CAACe,SAAS,CAAC2S,8BAA8B,CAAC,EAC3C1T,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAC1B,CACF,CAAC,CACF,CAAC,EACF3O,WAAC,CAAC8C,mBAAmB,CACnB9C,WAAC,CAAC+C,oBAAoB,CACpB,GAAG,EACH/C,WAAC,CAACe,SAAS,CAAC4S,sBAAsB,CAAC,EACnC3T,WAAC,CAACe,SAAS,CAAC2S,8BAA8B,CAC5C,CACF,CAAC,CACF,CAAC;IACJ;EACF;EAEA,IAAIvH,uBAAuB,CAAC0H,IAAI,GAAG,CAAC,EAAE;IACpC3H,6BAA6B,CAACzM,IAAI,EAAE0M,uBAAuB,CAAC;EAC9D;EAGA1M,IAAI,CAACc,KAAK,CAACuT,KAAK,CAAC,CAAC;EAElB,OAAOrU,IAAI;AACb;AAEA,SAAS0T,sBAAsBA,CAC7BzB,aAA6B,EAC7BC,WAA2B,EAC3BF,kBAAoD,EACpDpC,gBAAkD,EAClDD,oBAAsC,EACtC2E,qBAA2C,EAC3CxG,YAAwD,EACxDnM,UAA+B,EAC/BwJ,KAAiB,EACjBtI,OAA6B,EAC7B;EACA,IAAI0R,GAAG,EAAEC,GAAG;EACZ,MAAMzD,IAAoB,GAAG,CAC3BjD,YAAY,GACR5C,yBAAyB,CAACC,KAAK,EAAE2C,YAAY,CAAC,GAC9CvN,WAAC,CAACwC,cAAc,CAAC,CAAC,EACtB6M,gBAAgB,EAChBoC,kBAAkB,CACnB;EAEkC;IACjC,IAAInP,OAAO,KAAK,SAAS,EAAE;MACzBkO,IAAI,CAACrK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEsL,kBAAkB,EAAEpC,gBAAgB,CAAC;IACzD;IACA,IACE/M,OAAO,KAAK,SAAS,IACpBA,OAAO,KAAK,SAAS,IAAI,CAACsI,KAAK,CAACsJ,eAAe,CAAC,gBAAgB,CAAE,EACnE;MACAF,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAAC,CAAC,GAAGzC,aAAa,EAAE,GAAGC,WAAW,CAAC,CAAC;MACxDsC,GAAG,GAAGjU,WAAC,CAACkG,cAAc,CACpB0E,KAAK,CAACC,SAAS,CAACvI,OAAO,KAAK,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC,EACtEkO,IACF,CAAC;MACD,OAAOxQ,WAAC,CAAC+C,oBAAoB,CAAC,GAAG,EAAEiR,GAAG,EAAEC,GAAG,CAAC;IAC9C,CAAC,MAAM,IAAI3R,OAAO,KAAK,SAAS,EAAE;MAChC2R,GAAG,GAAGjU,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAE2F,IAAI,CAAC;IACjE,CAAC,MAAM,IAAIlO,OAAO,KAAK,SAAS,EAAE;MAChC,IAAIyR,qBAAqB,EAAE;QACzBvD,IAAI,CAACjM,IAAI,CAACyG,8BAA8B,CAAC+I,qBAAqB,CAAC,CAAC;MAClE;MACAE,GAAG,GAAGjU,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE2F,IAAI,CAAC;IAChE,CAAC,MAAM,IAAIlO,OAAO,KAAK,SAAS,EAAE;MAChC,IACEyR,qBAAqB,IACrB3S,UAAU,IACVgO,oBAAoB,CAAC3N,KAAK,KAAK,CAAC,EAChC;QACA+O,IAAI,CAACjM,IAAI,CAAC6K,oBAAoB,CAAC;MACjC;MACA,IAAI2E,qBAAqB,EAAE;QACzBvD,IAAI,CAACjM,IAAI,CAACyG,8BAA8B,CAAC+I,qBAAqB,CAAC,CAAC;MAClE,CAAC,MAAM,IAAI3S,UAAU,EAAE;QACrBoP,IAAI,CAACjM,IAAI,CAACvE,WAAC,CAACqF,eAAe,CAAC,MAAM,EAAErF,WAAC,CAAC2I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;MACA,IAAIvH,UAAU,EAAEoP,IAAI,CAACjM,IAAI,CAACnD,UAAU,CAAC;MACrC6S,GAAG,GAAGjU,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE2F,IAAI,CAAC;IAChE;EACF;EACA,IAAoClO,OAAO,KAAK,SAAS,EAAE;IACzD,IACEyR,qBAAqB,IACrB3S,UAAU,IACVgO,oBAAoB,CAAC3N,KAAK,KAAK,CAAC,EAChC;MACA+O,IAAI,CAACjM,IAAI,CAAC6K,oBAAoB,CAAC;IACjC;IACA,IAAI2E,qBAAqB,EAAE;MACzBvD,IAAI,CAACjM,IAAI,CAACyG,8BAA8B,CAAC+I,qBAAqB,CAAC,CAAC;IAClE,CAAC,MAAM,IAAI3S,UAAU,EAAE;MACrBoP,IAAI,CAACjM,IAAI,CAACvE,WAAC,CAACqF,eAAe,CAAC,MAAM,EAAErF,WAAC,CAAC2I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAIvH,UAAU,EAAEoP,IAAI,CAACjM,IAAI,CAACnD,UAAU,CAAC;IACrC6S,GAAG,GAAGjU,WAAC,CAACkG,cAAc,CAAC0E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE2F,IAAI,CAAC;EAChE;EAIA,IAAIkB,aAAa,CAACjT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAIkT,WAAW,CAAClT,MAAM,GAAG,CAAC,EAAE;MAC1BuV,GAAG,GAAGhU,WAAC,CAACoU,aAAa,CAAC,CACpBpU,WAAC,CAACqU,cAAc,CAACrU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACmU,YAAY,CAACzC,aAAa,CAAC,CAAC,EAClE1R,WAAC,CAACqU,cAAc,CAACrU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACmU,YAAY,CAACxC,WAAW,CAAC,CAAC,CACjE,CAAC;IACJ,CAAC,MAAM;MACLqC,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAACzC,aAAa,CAAC;MAEnCuC,GAAG,GAAGjU,WAAC,CAAC4C,gBAAgB,CAACqR,GAAG,EAAEjU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IAEL8T,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAACxC,WAAW,CAAC;IAEjCsC,GAAG,GAAGjU,WAAC,CAAC4C,gBAAgB,CAACqR,GAAG,EAAEjU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,OAAOF,WAAC,CAAC+C,oBAAoB,CAAC,GAAG,EAAEiR,GAAG,EAAEC,GAAG,CAAC;AAC9C;AAEA,SAASK,UAAUA,CACjBhW,IAAyE,EACzE;EACA,OAAOA,IAAI,CAACkC,IAAI,KAAK,YAAY,GAC7BlC,IAAI,CAACqB,IAAI,KAAK,WAAW,GACzBrB,IAAI,CAACmD,KAAK,KAAK,WAAW;AAChC;AAEA,SAASyM,WAAWA,CAAC5P,IAAuC,EAAE;EAC5D,OAAOA,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACE,UAAU,CAACC,MAAM,GAAG,CAAC;AACtD;AAEA,SAAS8V,sBAAsBA,CAACjW,IAAkB,EAAE;EAClD,QAAQA,IAAI,CAACkC,IAAI;IACf,KAAK,uBAAuB;MAC1B,OAAO,IAAI;IACb,KAAK,aAAa;IAClB,KAAK,eAAe;IACpB,KAAK,oBAAoB;IACzB,KAAK,sBAAsB;MACzB,OAAO0N,WAAW,CAAC5P,IAAI,CAAC;IAC1B;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAASkW,oBAAoBA,CAAClW,IAAa,EAAE;EAC3C,OAAO4P,WAAW,CAAC5P,IAAI,CAAC,IAAIA,IAAI,CAACK,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC2V,sBAAsB,CAAC;AACzE;AAGA,SAASE,8BAA8BA,CACrCC,WAAwC,EACxCC,OASS,EACT;EACA,SAASC,sBAAsBA,CAC7BC,YAEC,EACDrT,GAAiB,EACjBoJ,KAAiB,EACe;IAChC,QAAQpJ,GAAG,CAAChB,IAAI;MACd,KAAK,eAAe;QAClB,OAAOR,WAAC,CAACyL,aAAa,CAACjK,GAAG,CAACC,KAAK,CAAC;MACnC,KAAK,gBAAgB;MACrB,KAAK,eAAe;QAAE;UACpB,MAAMqT,QAAQ,GAAGtT,GAAG,CAACC,KAAK,GAAG,EAAE;UAC/BoT,YAAY,CAAChU,GAAG,CAAC,KAAK,CAAC,CAACC,WAAW,CAACd,WAAC,CAACyL,aAAa,CAACqJ,QAAQ,CAAC,CAAC;UAC9D,OAAO9U,WAAC,CAACyL,aAAa,CAACqJ,QAAQ,CAAC;QAClC;MACA;QAAS;UACP,MAAMC,GAAG,GAAGF,YAAY,CAACtU,KAAK,CAACwS,qBAAqB,CAACvR,GAAG,CAAC;UACzDqT,YAAY,CACThU,GAAG,CAAC,KAAK,CAAC,CACVC,WAAW,CACVd,WAAC,CAAC+C,oBAAoB,CACpB,GAAG,EACHgS,GAAG,EACHjK,uBAAuB,CAACF,KAAK,EAAEpJ,GAAG,CACpC,CACF,CAAC;UACH,OAAOxB,WAAC,CAACe,SAAS,CAACgU,GAAG,CAAC;QACzB;IACF;EACF;EACA,OAAO;IACLC,kBAAkBA,CAACvV,IAAI,EAAEmL,KAAK,EAAE;MAC9B,MAAM9L,EAAE,GAAGW,IAAI,CAACnB,IAAI,CAACQ,EAAE;MACvB,IAAIA,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM4E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,MAAM,CAAC,CAAC;QACjE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;UAC5B,MAAMzF,IAAI,GAAGb,EAAE,CAACa,IAAI;UACpBgV,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEjL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IACDsV,oBAAoBA,CAACxV,IAAI,EAAEmL,KAAK,EAAE;MAChC,MAAM9L,EAAE,GAAGW,IAAI,CAACnB,IAAI,CAAC4F,IAAI;MACzB,IAAIpF,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM4E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;UAC5B,QAAQ3F,IAAI,CAACnB,IAAI,CAAC4W,QAAQ;YACxB,KAAK,GAAG;YACR,KAAK,KAAK;YACV,KAAK,KAAK;YACV,KAAK,KAAK;cACRP,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAE9L,EAAE,CAACa,IAAI,CAAC;UACxC;QACF;MACF;IACF,CAAC;IACDwV,iBAAiBA,CAAC1V,IAAI,EAAEmL,KAAK,EAAE;MAC7B,MAAM9L,EAAE,GAAGW,IAAI,CAACnB,IAAI,CAAC4F,IAAI;MACzB,IAAIpF,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM4E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;UAC5B,MAAMzF,IAAI,GAAGb,EAAE,CAACa,IAAI;UACpBgV,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEjL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IAGDyV,gBAAgBA,CAAC3V,IAAI,EAAEmL,KAAK,EAAE;MAC5B,KAAK,MAAMiK,YAAY,IAAIpV,IAAI,CAACoB,GAAG,CAAC,YAAY,CAAC,EAAE;QACjD,IAAI,CAACgU,YAAY,CAACQ,gBAAgB,CAAC,CAAC,EAAE;QACtC,MAAM;UAAE/W;QAAK,CAAC,GAAGuW,YAAY;QAC7B,MAAM/V,EAAE,GAAGR,IAAI,CAACkD,GAAG;QACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAC7CmR,YAAY,CAAChU,GAAG,CAAC,OAAO,CAC1B,CAAC;QACD,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;UAC5B,IAAI,CAAC9G,IAAI,CAACkN,QAAQ,EAAE;YAElB,IAAI,CAAC8I,UAAU,CAACxV,EAAoC,CAAC,EAAE;cACrD,IAAIA,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;gBAC5BmU,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAE9L,EAAE,CAACa,IAAI,CAAC;cACtC,CAAC,MAAM;gBACL,MAAMW,SAAS,GAAGN,WAAC,CAACyL,aAAa,CAC9B3M,EAAE,CACA2C,KAAK,GAAG,EACb,CAAC;gBACDkT,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEtK,SAAS,CAAC;cACxC;YACF;UACF,CAAC,MAAM;YACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAChCC,YAAY,EAEZ/V,EAAE,EACF8L,KACF,CAAC;YACD+J,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEmK,GAAG,CAAC;UAClC;QACF;MACF;IACF,CAAC;IACD3G,oBAAoBA,CAAC3O,IAAI,EAAEmL,KAAK,EAAE;MAChC,MAAM;QAAEtM;MAAK,CAAC,GAAGmB,IAAI;MACrB,MAAM2F,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;QAC5B,MAAM9E,SAAS,GAAGN,WAAC,CAACyL,aAAa,CAAC,GAAG,GAAGnN,IAAI,CAACkD,GAAG,CAAC1C,EAAE,CAACa,IAAI,CAAC;QACzDgV,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEtK,SAAS,CAAC;MACxC;IACF,CAAC;IACD+N,qBAAqBA,CAAC5O,IAAI,EAAEmL,KAAK,EAAE;MACjC,MAAM;QAAEtM;MAAK,CAAC,GAAGmB,IAAI;MACrB,MAAMX,EAAE,GAAGR,IAAI,CAACkD,GAAG;MACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAC9G,IAAI,CAACkN,QAAQ,EAAE;UAClB,IAAI1M,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;YAC5BmU,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAE9L,EAAE,CAACa,IAAI,CAAC;UACtC,CAAC,MAAM,IAAIb,EAAE,CAAC0B,IAAI,KAAK,aAAa,EAAE;YACpC,MAAMF,SAAS,GAAGN,WAAC,CAACyL,aAAa,CAAC,GAAG,GAAG3M,EAAE,CAACA,EAAE,CAACa,IAAI,CAAC;YACnDgV,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEtK,SAAS,CAAC;UACxC,CAAC,MAAM;YACL,MAAMA,SAAS,GAAGN,WAAC,CAACyL,aAAa,CAC9B3M,EAAE,CACA2C,KAAK,GAAG,EACb,CAAC;YACDkT,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEtK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAChCnV,IAAI,EAEJX,EAAE,EACF8L,KACF,CAAC;UACD+J,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEmK,GAAG,CAAC;QAClC;MACF;IACF,CAAC;IACD5G,aAAaA,CAAC1O,IAAI,EAAEmL,KAAK,EAAE;MACzB,MAAM;QAAEtM;MAAK,CAAC,GAAGmB,IAAI;MACrB,MAAMX,EAAE,GAAGR,IAAI,CAACkD,GAAG;MACnB,MAAM4D,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACoB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACtP,WAAW,CAAC,EAAE;QAC5B,IAAI,CAAC9G,IAAI,CAACkN,QAAQ,EAAE;UAClB,IAAI1M,EAAE,CAAC0B,IAAI,KAAK,YAAY,EAAE;YAC5BmU,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAE9L,EAAE,CAACa,IAAI,CAAC;UACtC,CAAC,MAAM;YACL,MAAMW,SAAS,GAAGN,WAAC,CAACyL,aAAa,CAC9B3M,EAAE,CACA2C,KAAK,GAAG,EACb,CAAC;YACDkT,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEtK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAACnV,IAAI,EAAEX,EAAE,EAAE8L,KAAK,CAAC;UACnD+J,OAAO,CAACvP,WAAW,EAAEwF,KAAK,EAAEmK,GAAG,CAAC;QAClC;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASO,mCAAmCA,CAAC7V,IAAc,EAAE;EAC3D,OACEA,IAAI,CAAC8V,iBAAiB,CAAC;IAAEzW,EAAE,EAAE;EAAK,CAAC,CAAC,IAAI0V,oBAAoB,CAAC/U,IAAI,CAACnB,IAAI,CAAC;AAE3E;AAEA,SAAS0C,wBAAwBA,CAACT,KAAY,EAAEZ,IAAY,EAAE;EAC5D,MAAMb,EAAE,GAAGyB,KAAK,CAACwB,qBAAqB,CAACpC,IAAI,CAAC;EAC5CY,KAAK,CAACgE,IAAI,CAAC;IAAEzF,EAAE;IAAEiI,IAAI,EAAE;EAAM,CAAC,CAAC;EAC/B,OAAO/G,WAAC,CAACe,SAAS,CAACjC,EAAE,CAAC;AACxB;AAEe,SAAA0W,SACb;EAAEC,aAAa;EAAEC;AAAsB,CAAC,EACxC;EAAEC;AAAe,CAAC,EAClBrT,OAA6B,EAC7BsT,QAAkC,EACpB;EAAA,IAAAC,WAAA,EAAAC,YAAA;EAGP;IACL,IACExT,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;MACAmT,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM,IAAInT,OAAO,KAAK,SAAS,EAAE;MAChCmT,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM;MACLA,aAAa,CAAkB,SAAU,CAAC;IAC5C;EACF;EAEA,MAAMM,OAAO,GAAG,IAAIC,OAAO,CAAW,CAAC;EACvC,MAAMzJ,aAAa,IAAAsJ,WAAA,GAAGH,UAAU,CAAC,eAAe,CAAC,YAAAG,WAAA,GAAIF,KAAK;EAC1D,MAAMnJ,oBAAoB,IAAAsJ,YAAA,GAAGJ,UAAU,CAAC,sBAAsB,CAAC,YAAAI,YAAA,GAAIH,KAAK;EAExE,MAAMM,sBAA2C,GAC/CxB,8BAA8B,CAC5Ba,mCAAmC,EACnCY,UACF,CAAC;EAEH,SAASA,UAAUA,CACjBzW,IAAuB,EACvBmL,KAAiB,EACjBtK,SAA8D,EAC9D;IAAA,IAAA6V,QAAA;IACA,IAAIJ,OAAO,CAAChW,GAAG,CAACN,IAAI,CAAC,EAAE;IACvB,MAAM;MAAEnB;IAAK,CAAC,GAAGmB,IAAI;IACrBa,SAAS,WAATA,SAAS,GAATA,SAAS,IAAA6V,QAAA,GAAK7X,IAAI,CAACQ,EAAE,qBAAPqX,QAAA,CAASxW,IAAI;IAC3B,MAAM0B,OAAO,GAAGiL,cAAc,CAC5B7M,IAAI,EACJmL,KAAK,EACL2B,aAAa,EACbC,oBAAoB,EACpBlM,SAAS,EACT2V,sBAAsB,EACtB3T,OACF,CAAC;IACD,IAAIjB,OAAO,EAAE;MACX0U,OAAO,CAACrW,GAAG,CAAC2B,OAAO,CAAC;MACpB;IACF;IACA0U,OAAO,CAACrW,GAAG,CAACD,IAAI,CAAC;EACnB;EAEA,OAAO;IACLE,IAAI,EAAE,qBAAqB;IAC3BiW,QAAQ,EAAEA,QAAQ;IAElBjB,OAAO,EAAAyB,MAAA,CAAAC,MAAA;MACLC,wBAAwBA,CAAC7W,IAAI,EAAEmL,KAAK,EAAE;QACpC,MAAM;UAAE2L;QAAY,CAAC,GAAG9W,IAAI,CAACnB,IAAI;QACjC,IACE,CAAAiY,WAAW,oBAAXA,WAAW,CAAE/V,IAAI,MAAK,kBAAkB,IAGxC0N,WAAW,CAACqI,WAAW,CAAC,EACxB;UACA,MAAM7B,WAAW,GAAG,CAAC6B,WAAW,CAACzX,EAAE;UAC8B;YAAA,IAAA0X,qBAAA;YAE/D,CAAAA,qBAAA,GAAA/W,IAAI,CAACgX,sBAAsB,YAAAD,qBAAA,GAA3B/W,IAAI,CAACgX,sBAAsB,GAEzBzY,OAAO,CAAC,iBAAiB,CAAC,CAAC0Y,QAAQ,CAACC,SAAS,CAACF,sBAAsB;UACxE;UACA,MAAMG,yBAAyB,GAC7BnX,IAAI,CAACgX,sBAAsB,CAAC,CAAiC;UAC/D,IAAI/B,WAAW,EAAE;YACfwB,UAAU,CACRU,yBAAyB,EACzBhM,KAAK,EACL5K,WAAC,CAACyL,aAAa,CAAC,SAAS,CAC3B,CAAC;UACH;QACF;MACF,CAAC;MACDoL,sBAAsBA,CAACpX,IAAI,EAAE;QAC3B,MAAM;UAAE8W;QAAY,CAAC,GAAG9W,IAAI,CAACnB,IAAI;QACjC,IACE,CAAAiY,WAAW,oBAAXA,WAAW,CAAE/V,IAAI,MAAK,kBAAkB,IAGxC0N,WAAW,CAACqI,WAAW,CAAC,EACxB;UACiE;YAAA,IAAAO,sBAAA;YAE/D,CAAAA,sBAAA,GAAArX,IAAI,CAACgX,sBAAsB,YAAAK,sBAAA,GAA3BrX,IAAI,CAACgX,sBAAsB,GAEzBzY,OAAO,CAAC,iBAAiB,CAAC,CAAC0Y,QAAQ,CAACC,SAAS,CAACF,sBAAsB;UACxE;UACAhX,IAAI,CAACgX,sBAAsB,CAAC,CAAC;QAC/B;MACF,CAAC;MAEDM,KAAKA,CAACtX,IAAI,EAAEmL,KAAK,EAAE;QACjBsL,UAAU,CAACzW,IAAI,EAAEmL,KAAK,EAAEhJ,SAAS,CAAC;MACpC;IAAC,GAEEqU,sBAAsB;EAE7B,CAAC;AACH", "ignoreList": []}