{"version": 3, "sources": ["../../../../src/run/ios/runIosAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as XcodeBuild from './XcodeBuild';\nimport { Options } from './XcodeBuild.types';\nimport { getLaunchInfoForBinaryAsync, launchAppAsync } from './launchApp';\nimport { resolveOptionsAsync } from './options/resolveOptions';\nimport { getValidBinaryPathAsync } from './validateExternalBinary';\nimport { exportEagerAsync } from '../../export/embed/exportEager';\nimport * as Log from '../../log';\nimport { AppleAppIdResolver } from '../../start/platforms/ios/AppleAppIdResolver';\nimport { getContainerPathAsync, simctlAsync } from '../../start/platforms/ios/simctl';\nimport { resolveBuildCache, uploadBuildCache } from '../../utils/build-cache-providers';\nimport { maybePromptToSyncPodsAsync } from '../../utils/cocoapods';\nimport { CommandError } from '../../utils/errors';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { ensurePortAvailabilityAsync } from '../../utils/port';\nimport { profile } from '../../utils/profile';\nimport { getSchemesForIosAsync } from '../../utils/scheme';\nimport { ensureNativeProjectAsync } from '../ensureNativeProject';\nimport { logProjectLogsLocation } from '../hints';\nimport { startBundlerAsync } from '../startBundler';\n\nconst debug = require('debug')('expo:run:ios');\n\nexport async function runIosAsync(projectRoot: string, options: Options) {\n  setNodeEnv(options.configuration === 'Release' ? 'production' : 'development');\n  require('@expo/env').load(projectRoot);\n\n  assertPlatform();\n\n  const install = !!options.install;\n\n  if ((await ensureNativeProjectAsync(projectRoot, { platform: 'ios', install })) && install) {\n    await maybePromptToSyncPodsAsync(projectRoot);\n  }\n\n  // Resolve the CLI arguments into useable options.\n  const props = await profile(resolveOptionsAsync)(projectRoot, options);\n\n  const projectConfig = getConfig(projectRoot);\n  if (!options.binary && props.buildCacheProvider && props.isSimulator) {\n    const localPath = await resolveBuildCache({\n      projectRoot,\n      platform: 'ios',\n      runOptions: options,\n      provider: props.buildCacheProvider,\n    });\n    if (localPath) {\n      options.binary = localPath;\n    }\n  }\n\n  if (options.rebundle) {\n    Log.warn(`The --unstable-rebundle flag is experimental and may not work as expected.`);\n    // Get the existing binary path to re-bundle the app.\n\n    let binaryPath: string;\n    if (!options.binary) {\n      if (!props.isSimulator) {\n        throw new Error('Re-bundling on physical devices requires the --binary flag.');\n      }\n      const appId = await new AppleAppIdResolver(projectRoot).getAppIdAsync();\n      const possibleBinaryPath = await getContainerPathAsync(props.device, {\n        appId,\n      });\n      if (!possibleBinaryPath) {\n        throw new CommandError(\n          `Cannot rebundle because no --binary was provided and no existing binary was found on the device for ID: ${appId}.`\n        );\n      }\n      binaryPath = possibleBinaryPath;\n      Log.log('Re-using existing binary path:', binaryPath);\n      // Set the binary path to the existing binary path.\n      options.binary = binaryPath;\n    }\n\n    Log.log('Rebundling the Expo config file');\n    // Re-bundle the config file the same way the app was originally bundled.\n    await spawnAsync('node', [\n      path.join(require.resolve('expo-constants/package.json'), '../scripts/getAppConfig.js'),\n      projectRoot,\n      path.join(options.binary, 'EXConstants.bundle'),\n    ]);\n    // Re-bundle the app.\n\n    const possibleBundleOutput = path.join(options.binary, 'main.jsbundle');\n\n    if (fs.existsSync(possibleBundleOutput)) {\n      Log.log('Rebundling the app...');\n      await exportEagerAsync(projectRoot, {\n        resetCache: false,\n        dev: false,\n        platform: 'ios',\n        assetsDest: path.join(options.binary, 'assets'),\n        bundleOutput: possibleBundleOutput,\n      });\n    } else {\n      Log.warn('Bundle output not found at expected location:', possibleBundleOutput);\n    }\n  }\n\n  let binaryPath: string;\n  let shouldUpdateBuildCache = false;\n  if (options.binary) {\n    binaryPath = await getValidBinaryPathAsync(options.binary, props);\n    Log.log('Using custom binary path:', binaryPath);\n  } else {\n    let eagerBundleOptions: string | undefined;\n\n    if (options.configuration === 'Release') {\n      eagerBundleOptions = JSON.stringify(\n        await exportEagerAsync(projectRoot, {\n          dev: false,\n          platform: 'ios',\n        })\n      );\n    }\n\n    // Spawn the `xcodebuild` process to create the app binary.\n    const buildOutput = await XcodeBuild.buildAsync({\n      ...props,\n      eagerBundleOptions,\n    });\n\n    // Find the path to the built app binary, this will be used to install the binary\n    // on a device.\n    binaryPath = await profile(XcodeBuild.getAppBinaryPath)(buildOutput);\n    shouldUpdateBuildCache = true;\n  }\n  debug('Binary path:', binaryPath);\n\n  // Ensure the port hasn't become busy during the build.\n  if (props.shouldStartBundler && !(await ensurePortAvailabilityAsync(projectRoot, props))) {\n    props.shouldStartBundler = false;\n  }\n\n  const launchInfo = await getLaunchInfoForBinaryAsync(binaryPath);\n  const isCustomBinary = !!options.binary;\n\n  // Always close the app before launching on a simulator. Otherwise certain cached resources like the splashscreen will not be available.\n  if (props.isSimulator) {\n    try {\n      await simctlAsync(['terminate', props.device.udid, launchInfo.bundleId]);\n    } catch (error) {\n      // If we failed it's likely that the app was not running to begin with and we will get an `invalid device` error\n      debug('Failed to terminate app (possibly because it was not running):', error);\n    }\n  }\n\n  // Start the dev server which creates all of the required info for\n  // launching the app on a simulator.\n  const manager = await startBundlerAsync(projectRoot, {\n    port: props.port,\n    headless: !props.shouldStartBundler,\n    // If a scheme is specified then use that instead of the package name.\n\n    scheme: isCustomBinary\n      ? // If launching a custom binary, use the schemes in the Info.plist.\n        launchInfo.schemes[0]\n      : // If a scheme is specified then use that instead of the package name.\n        (await getSchemesForIosAsync(projectRoot))?.[0],\n  });\n\n  // Install and launch the app binary on a device.\n  await launchAppAsync(\n    binaryPath,\n    manager,\n    {\n      isSimulator: props.isSimulator,\n      device: props.device,\n      shouldStartBundler: props.shouldStartBundler,\n    },\n    launchInfo.bundleId\n  );\n\n  // Log the location of the JS logs for the device.\n  if (props.shouldStartBundler) {\n    logProjectLogsLocation();\n  } else {\n    await manager.stopAsync();\n  }\n\n  if (shouldUpdateBuildCache && props.buildCacheProvider) {\n    await uploadBuildCache({\n      projectRoot,\n      platform: 'ios',\n      provider: props.buildCacheProvider,\n      buildPath: binaryPath,\n      runOptions: options,\n    });\n  }\n}\n\nfunction assertPlatform() {\n  if (process.platform !== 'darwin') {\n    Log.exit(\n      chalk`iOS apps can only be built on macOS devices. Use {cyan eas build -p ios} to build in the cloud.`\n    );\n  }\n}\n"], "names": ["runIosAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "configuration", "load", "assertPlatform", "install", "ensureNativeProjectAsync", "platform", "maybePromptToSyncPodsAsync", "props", "profile", "resolveOptionsAsync", "projectConfig", "getConfig", "binary", "buildCacheProvider", "isSimulator", "localPath", "resolveBuildCache", "runOptions", "provider", "rebundle", "Log", "warn", "binaryPath", "Error", "appId", "AppleAppIdResolver", "getAppIdAsync", "possibleBinary<PERSON>ath", "getContainerPathAsync", "device", "CommandError", "log", "spawnAsync", "path", "join", "resolve", "possibleBundleOutput", "fs", "existsSync", "exportEagerAsync", "resetCache", "dev", "assetsDest", "bundleOutput", "shouldUpdateBuildCache", "getValidBinaryPathAsync", "eagerBundleOptions", "JSON", "stringify", "buildOutput", "XcodeBuild", "buildAsync", "getAppBinaryPath", "shouldStartBundler", "ensurePortAvailabilityAsync", "launchInfo", "getLaunchInfoForBinaryAsync", "isCustomBinary", "simctlAsync", "udid", "bundleId", "error", "manager", "startBundlerAsync", "port", "headless", "scheme", "schemes", "getSchemesForIosAsync", "launchAppAsync", "logProjectLogsLocation", "stopAsync", "uploadBuildCache", "buildPath", "process", "exit", "chalk"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;;yBA5BI;;;;;;;gEACH;;;;;;;gEACL;;;;;;;gEACH;;;;;;;gEACE;;;;;;oEAEW;2BAEgC;gCACxB;wCACI;6BACP;6DACZ;oCACc;wBACgB;qCACC;2BACT;wBACd;yBACF;sBACiB;yBACpB;wBACc;qCACG;uBACF;8BACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,YAAYG,WAAmB,EAAEC,OAAgB;QAwI9D;IAvIPC,IAAAA,mBAAU,EAACD,QAAQE,aAAa,KAAK,YAAY,eAAe;IAChEJ,QAAQ,aAAaK,IAAI,CAACJ;IAE1BK;IAEA,MAAMC,UAAU,CAAC,CAACL,QAAQK,OAAO;IAEjC,IAAI,AAAC,MAAMC,IAAAA,6CAAwB,EAACP,aAAa;QAAEQ,UAAU;QAAOF;IAAQ,MAAOA,SAAS;QAC1F,MAAMG,IAAAA,qCAA0B,EAACT;IACnC;IAEA,kDAAkD;IAClD,MAAMU,QAAQ,MAAMC,IAAAA,gBAAO,EAACC,mCAAmB,EAAEZ,aAAaC;IAE9D,MAAMY,gBAAgBC,IAAAA,mBAAS,EAACd;IAChC,IAAI,CAACC,QAAQc,MAAM,IAAIL,MAAMM,kBAAkB,IAAIN,MAAMO,WAAW,EAAE;QACpE,MAAMC,YAAY,MAAMC,IAAAA,sCAAiB,EAAC;YACxCnB;YACAQ,UAAU;YACVY,YAAYnB;YACZoB,UAAUX,MAAMM,kBAAkB;QACpC;QACA,IAAIE,WAAW;YACbjB,QAAQc,MAAM,GAAGG;QACnB;IACF;IAEA,IAAIjB,QAAQqB,QAAQ,EAAE;QACpBC,KAAIC,IAAI,CAAC,CAAC,0EAA0E,CAAC;QACrF,qDAAqD;QAErD,IAAIC;QACJ,IAAI,CAACxB,QAAQc,MAAM,EAAE;YACnB,IAAI,CAACL,MAAMO,WAAW,EAAE;gBACtB,MAAM,IAAIS,MAAM;YAClB;YACA,MAAMC,QAAQ,MAAM,IAAIC,sCAAkB,CAAC5B,aAAa6B,aAAa;YACrE,MAAMC,qBAAqB,MAAMC,IAAAA,6BAAqB,EAACrB,MAAMsB,MAAM,EAAE;gBACnEL;YACF;YACA,IAAI,CAACG,oBAAoB;gBACvB,MAAM,IAAIG,oBAAY,CACpB,CAAC,wGAAwG,EAAEN,MAAM,CAAC,CAAC;YAEvH;YACAF,aAAaK;YACbP,KAAIW,GAAG,CAAC,kCAAkCT;YAC1C,mDAAmD;YACnDxB,QAAQc,MAAM,GAAGU;QACnB;QAEAF,KAAIW,GAAG,CAAC;QACR,yEAAyE;QACzE,MAAMC,IAAAA,qBAAU,EAAC,QAAQ;YACvBC,eAAI,CAACC,IAAI,CAACtC,QAAQuC,OAAO,CAAC,gCAAgC;YAC1DtC;YACAoC,eAAI,CAACC,IAAI,CAACpC,QAAQc,MAAM,EAAE;SAC3B;QACD,qBAAqB;QAErB,MAAMwB,uBAAuBH,eAAI,CAACC,IAAI,CAACpC,QAAQc,MAAM,EAAE;QAEvD,IAAIyB,aAAE,CAACC,UAAU,CAACF,uBAAuB;YACvChB,KAAIW,GAAG,CAAC;YACR,MAAMQ,IAAAA,6BAAgB,EAAC1C,aAAa;gBAClC2C,YAAY;gBACZC,KAAK;gBACLpC,UAAU;gBACVqC,YAAYT,eAAI,CAACC,IAAI,CAACpC,QAAQc,MAAM,EAAE;gBACtC+B,cAAcP;YAChB;QACF,OAAO;YACLhB,KAAIC,IAAI,CAAC,iDAAiDe;QAC5D;IACF;IAEA,IAAId;IACJ,IAAIsB,yBAAyB;IAC7B,IAAI9C,QAAQc,MAAM,EAAE;QAClBU,aAAa,MAAMuB,IAAAA,+CAAuB,EAAC/C,QAAQc,MAAM,EAAEL;QAC3Da,KAAIW,GAAG,CAAC,6BAA6BT;IACvC,OAAO;QACL,IAAIwB;QAEJ,IAAIhD,QAAQE,aAAa,KAAK,WAAW;YACvC8C,qBAAqBC,KAAKC,SAAS,CACjC,MAAMT,IAAAA,6BAAgB,EAAC1C,aAAa;gBAClC4C,KAAK;gBACLpC,UAAU;YACZ;QAEJ;QAEA,2DAA2D;QAC3D,MAAM4C,cAAc,MAAMC,YAAWC,UAAU,CAAC;YAC9C,GAAG5C,KAAK;YACRuC;QACF;QAEA,iFAAiF;QACjF,eAAe;QACfxB,aAAa,MAAMd,IAAAA,gBAAO,EAAC0C,YAAWE,gBAAgB,EAAEH;QACxDL,yBAAyB;IAC3B;IACAjD,MAAM,gBAAgB2B;IAEtB,uDAAuD;IACvD,IAAIf,MAAM8C,kBAAkB,IAAI,CAAE,MAAMC,IAAAA,iCAA2B,EAACzD,aAAaU,QAAS;QACxFA,MAAM8C,kBAAkB,GAAG;IAC7B;IAEA,MAAME,aAAa,MAAMC,IAAAA,sCAA2B,EAAClC;IACrD,MAAMmC,iBAAiB,CAAC,CAAC3D,QAAQc,MAAM;IAEvC,wIAAwI;IACxI,IAAIL,MAAMO,WAAW,EAAE;QACrB,IAAI;YACF,MAAM4C,IAAAA,mBAAW,EAAC;gBAAC;gBAAanD,MAAMsB,MAAM,CAAC8B,IAAI;gBAAEJ,WAAWK,QAAQ;aAAC;QACzE,EAAE,OAAOC,OAAO;YACd,gHAAgH;YAChHlE,MAAM,kEAAkEkE;QAC1E;IACF;IAEA,kEAAkE;IAClE,oCAAoC;IACpC,MAAMC,UAAU,MAAMC,IAAAA,+BAAiB,EAAClE,aAAa;QACnDmE,MAAMzD,MAAMyD,IAAI;QAChBC,UAAU,CAAC1D,MAAM8C,kBAAkB;QACnC,sEAAsE;QAEtEa,QAAQT,iBAEJF,WAAWY,OAAO,CAAC,EAAE,IAEpB,QAAA,MAAMC,IAAAA,6BAAqB,EAACvE,iCAA7B,AAAC,KAA2C,CAAC,EAAE;IACrD;IAEA,iDAAiD;IACjD,MAAMwE,IAAAA,yBAAc,EAClB/C,YACAwC,SACA;QACEhD,aAAaP,MAAMO,WAAW;QAC9Be,QAAQtB,MAAMsB,MAAM;QACpBwB,oBAAoB9C,MAAM8C,kBAAkB;IAC9C,GACAE,WAAWK,QAAQ;IAGrB,kDAAkD;IAClD,IAAIrD,MAAM8C,kBAAkB,EAAE;QAC5BiB,IAAAA,6BAAsB;IACxB,OAAO;QACL,MAAMR,QAAQS,SAAS;IACzB;IAEA,IAAI3B,0BAA0BrC,MAAMM,kBAAkB,EAAE;QACtD,MAAM2D,IAAAA,qCAAgB,EAAC;YACrB3E;YACAQ,UAAU;YACVa,UAAUX,MAAMM,kBAAkB;YAClC4D,WAAWnD;YACXL,YAAYnB;QACd;IACF;AACF;AAEA,SAASI;IACP,IAAIwE,QAAQrE,QAAQ,KAAK,UAAU;QACjCe,KAAIuD,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,+FAA+F,CAAC;IAE1G;AACF"}