{"version": 3, "names": ["useLinkTo", "navigation", "React", "useContext", "NavigationContainerRefContext", "linking", "LinkingContext", "linkTo", "useCallback", "to", "undefined", "Error", "navigate", "screen", "params", "startsWith", "options", "state", "getStateFromPath", "config", "action", "getActionFromState", "dispatch", "reset"], "sourceRoot": "../../src", "sources": ["useLinkTo.tsx"], "mappings": ";;;;;;AAAA;AAKA;AAEA;AAA8C;AAAA;AAAA;AAiB/B,SAASA,SAAS,GAE7B;EACF,MAAMC,UAAU,GAAGC,KAAK,CAACC,UAAU,CAACC,mCAA6B,CAAC;EAClE,MAAMC,OAAO,GAAGH,KAAK,CAACC,UAAU,CAACG,uBAAc,CAAC;EAEhD,MAAMC,MAAM,GAAGL,KAAK,CAACM,WAAW,CAC7BC,EAAiB,IAAK;IACrB,IAAIR,UAAU,KAAKS,SAAS,EAAE;MAC5B,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;IACH;IAEA,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;MAC1B;MACAR,UAAU,CAACW,QAAQ,CAACH,EAAE,CAACI,MAAM,EAAEJ,EAAE,CAACK,MAAM,CAAC;MACzC;IACF;IAEA,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIJ,KAAK,CAAE,iCAAgCF,EAAG,IAAG,CAAC;IAC1D;IAEA,MAAM;MAAEO;IAAQ,CAAC,GAAGX,OAAO;IAE3B,MAAMY,KAAK,GAAGD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,gBAAgB,GACnCF,OAAO,CAACE,gBAAgB,CAACT,EAAE,EAAEO,OAAO,CAACG,MAAM,CAAC,GAC5C,IAAAD,sBAAgB,EAACT,EAAE,EAAEO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,MAAM,CAAC;IAEzC,IAAIF,KAAK,EAAE;MACT,MAAMG,MAAM,GAAG,IAAAC,wBAAkB,EAACJ,KAAK,EAAED,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,MAAM,CAAC;MAEzD,IAAIC,MAAM,KAAKV,SAAS,EAAE;QACxBT,UAAU,CAACqB,QAAQ,CAACF,MAAM,CAAC;MAC7B,CAAC,MAAM;QACLnB,UAAU,CAACsB,KAAK,CAACN,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,iDAAiD,CAAC;IACpE;EACF,CAAC,EACD,CAACN,OAAO,EAAEJ,UAAU,CAAC,CACtB;EAED,OAAOM,MAAM;AACf"}