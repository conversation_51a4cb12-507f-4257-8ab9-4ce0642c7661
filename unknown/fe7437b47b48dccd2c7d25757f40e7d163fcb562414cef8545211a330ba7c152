<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Sélection Client</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        .info { background: #f0f8ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #f0fdf4; border: 2px solid #10b981; }
        .error { background: #fef2f2; border: 2px solid #dc2626; }
        .debug { background: #f9fafb; padding: 10px; border-left: 4px solid #6b7280; margin: 10px 0; }
        button { background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2563eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Debug - Sélection Client avec Contrat</h1>
        
        <div class="form-group">
            <label>Client</label>
            <select id="clientSelect">
                <option value="">Sélectionner un client</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Contrat</label>
            <select id="contractSelect" disabled>
                <option value="">Sélectionnez d'abord un client</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Consommation Précédente (m³)</label>
            <input type="number" id="prevConsumption" readonly placeholder="Sera rempli automatiquement">
        </div>
        
        <div id="debugInfo" class="debug">
            <h3>🔍 Informations de Debug</h3>
            <div id="debugContent">Chargement...</div>
        </div>
        
        <button onclick="simulateListSelection()">🎯 Simuler sélection depuis liste (Benali Fatima)</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        let clients = [];
        let filteredContracts = [];
        
        function log(message) {
            console.log(message);
            const debugContent = document.getElementById('debugContent');
            debugContent.innerHTML += '<div>' + new Date().toLocaleTimeString() + ' - ' + message + '</div>';
        }
        
        async function loadClients() {
            try {
                log('📤 Chargement des clients...');
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (data.success) {
                    clients = data.data;
                    log(`✅ ${clients.length} clients chargés`);
                    
                    const clientSelect = document.getElementById('clientSelect');
                    clients.forEach(client => {
                        const option = document.createElement('option');
                        option.value = client.idclient;
                        option.textContent = `${client.nom} ${client.prenom} - ${client.ville}`;
                        clientSelect.appendChild(option);
                    });
                } else {
                    log('❌ Erreur lors du chargement des clients');
                }
            } catch (error) {
                log('❌ Erreur: ' + error.message);
            }
        }
        
        async function handleClientChange(clientId) {
            log(`🔄 handleClientChange appelé avec clientId: ${clientId}`);
            
            const contractSelect = document.getElementById('contractSelect');
            const prevConsumption = document.getElementById('prevConsumption');
            
            if (!clientId) {
                log('❌ ClientId vide - réinitialisation');
                contractSelect.innerHTML = '<option value="">Sélectionnez d\'abord un client</option>';
                contractSelect.disabled = true;
                prevConsumption.value = '';
                filteredContracts = [];
                return;
            }
            
            try {
                log(`🔍 Récupération des contrats pour le client ${clientId}...`);
                const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`);
                log(`📡 Réponse API reçue, status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📋 Données reçues: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success && data.data) {
                        filteredContracts = data.data;
                        log(`✅ ${filteredContracts.length} contrat(s) trouvé(s)`);
                        
                        // Vider et remplir le select des contrats
                        contractSelect.innerHTML = '';
                        contractSelect.disabled = false;
                        
                        if (filteredContracts.length === 0) {
                            contractSelect.innerHTML = '<option value="">Ce client n\'a pas de contrat</option>';
                            contractSelect.disabled = true;
                            log('⚠️ Aucun contrat trouvé');
                        } else if (filteredContracts.length === 1) {
                            // Sélection automatique pour un seul contrat
                            const contract = filteredContracts[0];
                            log(`🎯 Sélection automatique du contrat unique: ${contract.idcontract}`);
                            
                            const option = document.createElement('option');
                            option.value = contract.idcontract;
                            option.textContent = `${contract.codeqr} - Contrat #${contract.idcontract}`;
                            option.selected = true;
                            contractSelect.appendChild(option);
                            contractSelect.disabled = true;
                            contractSelect.className = 'success';
                            
                            log(`✅ Contrat auto-sélectionné: ${contract.codeqr}`);
                            
                            // Récupérer la dernière consommation
                            await fetchLastConsommation(contract.idcontract);
                            
                        } else {
                            // Plusieurs contrats - sélection manuelle
                            log(`📋 ${filteredContracts.length} contrats trouvés - sélection manuelle`);
                            
                            const defaultOption = document.createElement('option');
                            defaultOption.value = '';
                            defaultOption.textContent = 'Sélectionner un contrat';
                            contractSelect.appendChild(defaultOption);
                            
                            filteredContracts.forEach(contract => {
                                const option = document.createElement('option');
                                option.value = contract.idcontract;
                                option.textContent = `${contract.codeqr} - Contrat #${contract.idcontract}`;
                                contractSelect.appendChild(option);
                            });
                        }
                    } else {
                        log('❌ Réponse API invalide');
                    }
                } else {
                    log(`❌ Erreur API: ${response.status}`);
                }
            } catch (error) {
                log('❌ Erreur lors de la récupération des contrats: ' + error.message);
            }
        }
        
        async function fetchLastConsommation(contractId) {
            try {
                log(`🔍 Récupération de la dernière consommation pour le contrat ${contractId}...`);
                const response = await fetch(`${API_BASE_URL}/api/contracts/${contractId}/last-consommation`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        const prevConsumption = document.getElementById('prevConsumption');
                        prevConsumption.value = data.data.consommationactuelle;
                        log(`✅ Dernière consommation trouvée: ${data.data.consommationactuelle} m³`);
                    } else {
                        log('ℹ️ Aucune consommation précédente trouvée');
                    }
                } else {
                    log('⚠️ Erreur lors de la récupération de la dernière consommation');
                }
            } catch (error) {
                log('❌ Erreur: ' + error.message);
            }
        }
        
        function simulateListSelection() {
            log('🎯 Simulation de sélection depuis la liste - Benali Fatima');
            const benaliClient = clients.find(c => c.nom === 'Benali' && c.prenom === 'Fatima');
            
            if (benaliClient) {
                log(`✅ Client trouvé: ${benaliClient.nom} ${benaliClient.prenom} (ID: ${benaliClient.idclient})`);
                document.getElementById('clientSelect').value = benaliClient.idclient;
                handleClientChange(benaliClient.idclient.toString());
            } else {
                log('❌ Client Benali Fatima non trouvé');
            }
        }
        
        // Event listeners
        document.getElementById('clientSelect').addEventListener('change', (e) => {
            handleClientChange(e.target.value);
        });
        
        document.getElementById('contractSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                fetchLastConsommation(e.target.value);
            }
        });
        
        // Initialisation
        loadClients();
    </script>
</body>
</html>
