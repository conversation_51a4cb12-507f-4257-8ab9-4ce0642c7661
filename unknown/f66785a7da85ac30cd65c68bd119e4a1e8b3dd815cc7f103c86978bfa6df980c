import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as a from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";import*as r from"./preloading/helper/helper.js";const o={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},n=t.i18n.registerUIStrings("panels/application/application-meta.ts",o),s=t.i18n.getLazilyComputedLocalizedString.bind(void 0,n);let l;async function c(){return l||(l=await import("./application.js")),l}i.ViewManager.registerViewExtension({location:"panel",id:"resources",title:s(o.application),commandPrompt:s(o.showApplication),order:70,loadView:async()=>(await c()).ResourcesPanel.ResourcesPanel.instance(),tags:[s(o.pwa)]}),i.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear",title:s(o.clearSiteData),loadActionDelegate:async()=>new((await c()).StorageView.ActionDelegate)}),i.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear-incl-third-party-cookies",title:s(o.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>new((await c()).StorageView.ActionDelegate)}),i.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes(){return e=e=>[e.BackgroundServiceView.BackgroundServiceView],void 0===l?[]:e(l);var e},loadActionDelegate:async()=>new((await c()).BackgroundServiceView.ActionDelegate),category:"BACKGROUND_SERVICES",options:[{value:!0,title:s(o.startRecordingEvents)},{value:!1,title:s(o.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),e.Revealer.registerRevealer({contextTypes:()=>[a.Resource.Resource],destination:e.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await c()).ResourcesPanel.ResourceRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[a.ResourceTreeModel.ResourceTreeFrame],destination:e.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await c()).ResourcesPanel.FrameDetailsRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[r.PreloadingForward.RuleSetView],destination:e.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await c()).ResourcesPanel.RuleSetViewRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[r.PreloadingForward.AttemptViewWithFilter],destination:e.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await c()).ResourcesPanel.AttemptViewWithFilterRevealer)});
