import*as e from"../helpers/helpers.js";import*as t from"../types/types.js";import*as n from"../../../core/platform/platform.js";import*as a from"../../cpu_profile/cpu_profile.js";const r=[],i=[];let s=1;var o=Object.freeze({__proto__:null,reset:function(){r.length=0,i.length=0},handleEvent:function(e){t.TraceEvents.isTraceEventAnimation(e)&&r.push(e)},finalize:async function(){const t=e.Trace.createMatchedSortedSyntheticEvents(r);i.push(...t),s=3},data:function(){if(3!==s)throw new Error("Animation handler is not finalized");return{animations:Array.from(i)}}});const c=new Map,d=new Map,l=new Map,m=new Map,u=new Map;function f(e){switch(e){case"seller":return"seller";case"bidder":return"bidder";default:return"unknown"}}function T(e){return{name:"SyntheticAuctionWorkletEvent",s:"t",cat:e.cat,tid:e.tid,ts:e.ts,ph:"I",pid:e.args.data.pid,host:e.args.data.host,target:e.args.data.target,type:f(e.args.data.type)}}function g(){return{worklets:new Map(l)}}var h=Object.freeze({__proto__:null,reset:function(){c.clear(),d.clear(),l.clear(),m.clear(),u.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventAuctionWorkletRunningInProcess(e))c.set(e.args.data.pid,e);else if(t.TraceEvents.isTraceEventAuctionWorkletDoneWithProcess(e))d.set(e.args.data.pid,e);else if(t.TraceEvents.isThreadName(e)){if("auction_worklet.CrUtilityMain"===e.args.name)return void m.set(e.pid,e);"AuctionV8HelperThread"===e.args.name&&u.set(e.pid,e)}},finalize:async function(){for(const[e,t]of m){const n=u.get(e);if(!n)continue;const a=c.get(e),r=d.get(e);let i=null;a?(i={...T(a),args:{data:{runningInProcessEvent:a,utilityThread:t,v8HelperThread:n}}},r&&(i.args.data.doneWithProcessEvent=r)):r&&(i={...T(r),args:{data:{doneWithProcessEvent:r,utilityThread:t,v8HelperThread:n}}},a&&(i.args.data.runningInProcessEvent=a)),null!==i&&l.set(e,i)}},data:g});const v=new Map;let p="",E="";const w=new Map;let M=t.TraceEvents.ProcessID(-1),S=t.TraceEvents.ThreadID(-1),I=t.TraceEvents.ProcessID(-1),y=t.TraceEvents.ThreadID(-1),F=null;const P=new Map,_=new Set,k={min:t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),max:t.Timing.MicroSeconds(Number.NEGATIVE_INFINITY),range:t.Timing.MicroSeconds(Number.POSITIVE_INFINITY)},D=new Map,R=new Map,C=[],L=new Map;let N=t.Timing.MicroSeconds(-1);const b=new Set(["B","E","X","I"]);let W=1,z=!0;const O=new Set(["TracingStartedInPage","TracingSessionIdForWorker","TracingStartedInBrowser"]);function B(e,a){n.MapUtilities.getWithDefault(w,a.processId,(()=>new Map)).set(a.frame,a);const r=n.MapUtilities.getWithDefault(v,a.frame,(()=>new Map)),i=n.MapUtilities.getWithDefault(r,a.processId,(()=>[])),s=i.at(-1);s&&s.frame.url===a.url||i.push({frame:a,window:{min:e.ts,max:t.Timing.MicroSeconds(0),range:t.Timing.MicroSeconds(0)}})}function q(){if(3!==W)throw new Error("Meta Handler is not finalized");return{traceBounds:{...k},browserProcessId:M,browserThreadId:S,processNames:new Map(P),gpuProcessId:I,gpuThreadId:y===t.TraceEvents.ThreadID(-1)?void 0:y,viewportRect:F||void 0,mainFrameId:p,mainFrameURL:E,navigationsByFrameId:new Map(D),navigationsByNavigationId:new Map(R),threadsInProcess:new Map(L),rendererProcessesByFrame:new Map(v),topLevelRendererIds:new Set(_),frameByProcessId:new Map(w),mainFrameNavigations:[...C],traceIsGeneric:z}}var A=Object.freeze({__proto__:null,reset:function(){D.clear(),R.clear(),P.clear(),C.length=0,M=t.TraceEvents.ProcessID(-1),S=t.TraceEvents.ThreadID(-1),I=t.TraceEvents.ProcessID(-1),y=t.TraceEvents.ThreadID(-1),F=null,_.clear(),L.clear(),v.clear(),w.clear(),k.min=t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),k.max=t.Timing.MicroSeconds(Number.NEGATIVE_INFINITY),k.range=t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),N=t.Timing.MicroSeconds(-1),z=!0,W=1},initialize:function(){if(1!==W)throw new Error("Meta Handler was not reset");W=2},handleEvent:function(e){if(2!==W)throw new Error("Meta Handler is not initialized");if(z&&O.has(e.name)&&(z=!1),t.TraceEvents.isProcessName(e)&&P.set(e.pid,e),0!==e.ts&&!e.name.endsWith("::UMA")&&b.has(e.ph)){k.min=t.Timing.MicroSeconds(Math.min(e.ts,k.min));const n=e.dur||t.Timing.MicroSeconds(0);k.max=t.Timing.MicroSeconds(Math.max(e.ts+n,k.max))}if(!t.TraceEvents.isProcessName(e)||"Browser"!==e.args.name&&"HeadlessBrowser"!==e.args.name)if(!t.TraceEvents.isProcessName(e)||"Gpu"!==e.args.name&&"GPU Process"!==e.args.name)if(t.TraceEvents.isThreadName(e)&&"CrGpuMain"===e.args.name)y=e.tid;else{if(t.TraceEvents.isThreadName(e)&&"CrBrowserMain"===e.args.name&&(S=e.tid),t.TraceEvents.isTraceEventMainFrameViewport(e)&&null===F){const t=e.args.data.viewport_rect,n=t[0],a=t[1],r=t[2],i=t[5];F=new DOMRect(n,a,r,i)}if(t.TraceEvents.isTraceEventTracingStartedInBrowser(e)){if(N=e.ts,!e.args.data)throw new Error("No frames found in trace data");for(const t of e.args.data.frames??[])B(e,t),t.parent||(p=t.frame,E=t.url,_.add(t.processId))}else if(t.TraceEvents.isTraceEventFrameCommittedInBrowser(e)){const t=e.args.data;if(!t)return;if(B(e,t),t.parent)return;_.add(t.processId)}else if(t.TraceEvents.isTraceEventCommitLoad(e)){const t=e.args.data;if(!t)return;const{frame:n,name:a,url:r}=t;B(e,{processId:e.pid,frame:n,name:a,url:r})}else if(t.TraceEvents.isThreadName(e)){n.MapUtilities.getWithDefault(L,e.pid,(()=>new Map)).set(e.tid,e)}else if(t.TraceEvents.isTraceEventNavigationStartWithURL(e)&&e.args.data){const t=e.args.data.navigationId;if(R.has(t))return;R.set(t,e);const n=e.args.frame,a=D.get(n)||[];return a.push(e),D.set(n,a),void(n===p&&C.push(e))}}else I=e.pid;else M=e.pid},finalize:async function(){if(2!==W)throw new Error("Handler is not initialized");N>=0&&(k.min=N),k.range=t.Timing.MicroSeconds(k.max-k.min);for(const[,e]of v){const n=[...e.values()].flat();for(let e=0;e<n.length;e++){const a=n[e],r=n[e+1];r?(a.window.max=t.Timing.MicroSeconds(r.window.min-1),a.window.range=t.Timing.MicroSeconds(a.window.max-a.window.min)):(a.window.max=t.Timing.MicroSeconds(k.max),a.window.range=t.Timing.MicroSeconds(k.max-a.window.min))}}for(const[e,t]of D)if(!v.has(e)){D.delete(e);for(const e of t)e.args.data&&R.delete(e.args.data.navigationId)}const n=C.at(0),a=e.Timing.secondsToMicroseconds(t.Timing.Seconds(.5));if(n){const e=n.ts-k.min<a;n.args.data?.isOutermostMainFrame&&n.args.data?.documentLoaderURL&&e&&(E=n.args.data.documentLoaderURL)}W=3},data:q});let H=1;const U=[],x=[],j=new Map;let G={},V=null;const $=[],Y=[];function K(){return{paints:Array.from(U),snapshots:Array.from(x),paintsToSnapshots:new Map(j)}}var Q=Object.freeze({__proto__:null,reset:function(){H=1,U.length=0,x.length=0,j.clear(),G={},V=null,$.length=0,Y.length=0},initialize:function(){if(1!==H)throw new Error("LayerTree Handler was not reset before being initialized");H=2},handleEvent:function(e){(t.TraceEvents.isTraceEventPaint(e)||t.TraceEvents.isTraceEventDisplayListItemListSnapshot(e)||t.TraceEvents.isTraceEventUpdateLayer(e)||t.TraceEvents.isTraceEventSetLayerId(e))&&Y.push(e)},finalize:async function(){if(2!==H)throw new Error("LayerTree Handler is not initialized");const n=q();e.Trace.sortTraceEventsInPlace(Y);for(const e of Y)if(t.TraceEvents.isTraceEventSetLayerId(e)){if(n.mainFrameId!==e.args.data.frame)continue;V=e.args.data.layerTreeId}else if(t.TraceEvents.isTraceEventUpdateLayer(e))$.push(e);else{if(t.TraceEvents.isTraceEventPaint(e)){if(!e.args.data.layerId)continue;U.push(e),G[e.args.data.layerId]=e;continue}if(t.TraceEvents.isTraceEventDisplayListItemListSnapshot(e)){let t=null;for(let n=$.length-1;n>-1;n--){const a=$[n];if(a.pid===e.pid&&a.tid===e.tid){t=a;break}}if(!t)continue;if(t.args.layerTreeId!==V)continue;const n=G[t.args.layerId];if(!n)continue;x.push(e),j.set(n,e)}}H=3},data:K,deps:function(){return["Meta"]}});const X=new Map,J=new Map,Z=new Map,ee=new Map;let te=1;function ne(){if(3!==te)throw new Error("Samples Handler is not finalized");return{profilesInProcess:new Map(J),entryToNode:new Map(Z)}}function ae(e,t){const a=n.MapUtilities.getWithDefault(ee,e,(()=>new Map));return n.MapUtilities.getWithDefault(a,t,(()=>({rawProfile:{startTime:0,endTime:0,nodes:[],samples:[],timeDeltas:[],lines:[]},profileId:t})))}var re=Object.freeze({__proto__:null,reset:function(){X.clear(),ee.clear(),J.clear(),Z.clear(),te=1},initialize:function(){if(1!==te)throw new Error("Samples Handler was not reset");te=2},handleEvent:function(e){if(2!==te)throw new Error("Samples Handler is not initialized");if(t.TraceEvents.isSyntheticCpuProfile(e)){const t=e.pid,n=e.tid,a=ae(t,"0x1");return a.rawProfile=e.args.data.cpuProfile,void(a.threadId=n)}if(t.TraceEvents.isTraceEventProfile(e)){const t=ae(e.pid,e.id);return t.rawProfile.startTime=e.ts,void(t.threadId=e.tid)}if(t.TraceEvents.isTraceEventProfileChunk(e)){const t=ae(e.pid,e.id).rawProfile,n=e.args?.data?.cpuProfile||{samples:[]},a=n?.samples||[],r=[];for(const e of n?.nodes||[]){const t=void 0===e.callFrame.lineNumber?-1:e.callFrame.lineNumber,n=void 0===e.callFrame.columnNumber?-1:e.callFrame.columnNumber,a=String(e.callFrame.scriptId),i=e.callFrame.url||"",s={...e,callFrame:{...e.callFrame,url:i,lineNumber:t,columnNumber:n,scriptId:a}};r.push(s)}const i=e.args.data?.timeDeltas||[],s=e.args.data?.lines||Array(a.length).fill(0);if(t.nodes.push(...r),t.samples?.push(...a),t.timeDeltas?.push(...i),t.lines?.push(...s),t.samples&&t.timeDeltas&&t.samples.length!==t.timeDeltas.length)return void console.error("Failed to parse CPU profile.");if(!t.endTime&&t.timeDeltas){const e=t.timeDeltas;t.endTime=e.reduce(((e,t)=>e+t),t.startTime)}}else;},finalize:async function(){if(2!==te)throw new Error("Samples Handler is not initialized");!function(){for(const[s,o]of ee)for(const[c,d]of o){const l=d.threadId;if(!d.rawProfile.nodes.length||void 0===l)continue;const m=[],u=new a.CPUProfileDataModel.CPUProfileDataModel(d.rawProfile),f=e.TreeHelpers.makeEmptyTraceEntryTree();f.maxDepth=u.maxDepth;const T={rawProfile:d.rawProfile,parsedProfile:u,profileCalls:[],profileTree:f},g=n.MapUtilities.getWithDefault(J,s,(()=>new Map));function r(n,a,r){if(void 0===l)return;const i=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(r)),o=a.id,c=e.Trace.makeProfileCall(a,i,s,l);T.profileCalls.push(c),m.push(T.profileCalls.length-1);const d=e.TreeHelpers.makeEmptyTraceEntryNode(c,o);Z.set(c,d),d.depth=n,1===m.length&&T.profileTree?.roots.add(d)}function i(n,a,r,i,s){const o=m.pop(),d=void 0!==o&&T.profileCalls[o];if(!d)return;const{callFrame:l,ts:u,pid:f,tid:g}=d,h=Z.get(d);if(void 0===l||void 0===u||void 0===f||void 0===c||void 0===g||void 0===h)return;const v=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(i)),p=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(s));d.dur=v,d.selfTime=p;const E=m.at(-1),w=void 0!==E&&T.profileCalls.at(E),M=w&&Z.get(w);M&&(h.parent=M,M.children.push(h))}u.forEachFrame(r,i),g.set(l,T)}}(),te=3},data:ne,getProfileCallFunctionName:function(e,t){const n=e.profilesInProcess.get(t.pid)?.get(t.tid),a=n?.parsedProfile.nodeById(t.nodeId);return a?.functionName?a.functionName:t.callFrame.functionName}});const ie=new Map,se=Array(),oe=new Map;let ce=[];const de=[];let le=1,me=t.Configuration.DEFAULT;const ue=()=>({url:null,isOnMainFrame:!1,threads:new Map}),fe=()=>({name:null,entries:[]}),Te=(e,t)=>n.MapUtilities.getWithDefault(e,t,ue),ge=(e,t)=>n.MapUtilities.getWithDefault(e.threads,t,fe);function he(){if(3!==le)throw new Error("Renderer Handler is not finalized");return{processes:new Map(ie),compositorTileWorkers:new Map(ve()),entryToNode:new Map(oe),allTraceEntries:[...ce]}}function ve(){const e=new Map;for(const t of se){const n=e.get(t.pid)||[];n.push(t.tid),e.set(t.pid,n)}return e}function pe(e,t,n,a){Ee(e,n),we(e,t,n),Me(e,n,a)}function Ee(e,t){for(const n of t.values())for(const[t,a]of n)for(const n of a.flat()){const a=Te(e,t);if(null===a.url||"about:blank"===a.url)try{new URL(n.frame.url),a.url=n.frame.url}catch(e){a.url=null}}}function we(e,t,n){for(const[a,r]of n)for(const[n]of r){const r=Te(e,n);a===t&&(r.isOnMainFrame=!0)}}function Me(e,t,n){for(const[t,a]of e)for(const[e,r]of n.get(t)??[]){ge(a,e).name=r?.args.name??`${e}`}}function Se(e){const t=g().worklets;if(!q().traceIsGeneric)for(const[n,a]of e)if(null!==a.url);else{const r=t.get(n);r?a.url=r.host:e.delete(n)}}function Ie(e){for(const[,t]of e)for(const[e,n]of t.threads)n.tree?.roots.size||t.threads.delete(e)}function ye(t,n){const a=ne();for(const[r,i]of t)for(const[t,s]of i.threads){if(!s.entries.length){s.tree=e.TreeHelpers.makeEmptyTraceEntryTree();continue}e.Trace.sortTraceEventsInPlace(s.entries);const i=a.profilesInProcess.get(r)?.get(t)?.parsedProfile,o=i&&new e.SamplesIntegrator.SamplesIntegrator(i,r,t,me),c=o?.buildProfileCalls(s.entries);c&&(ce=[...ce,...c],s.entries=e.Trace.mergeEventsInOrder(s.entries,c));const d=e.TreeHelpers.treify(s.entries,n);s.tree=d.tree;for(const[e,t]of d.entryToNode)oe.set(e,t)}}function Fe(e){if(t.TraceEvents.isTraceEventEnd(e)){const n=de.pop();return n?n.name!==e.name||n.cat!==e.cat?(console.error("Begin/End events mismatch at "+n.ts+" ("+n.name+") vs. "+e.ts+" ("+e.name+")"),null):(n.dur=t.Timing.MicroSeconds(e.ts-n.ts),null):null}const n={...e,ph:"X",dur:t.Timing.MicroSeconds(0)};return de.push(n),n}var Pe=Object.freeze({__proto__:null,handleUserConfig:function(e){me=e},reset:function(){ie.clear(),oe.clear(),ce.length=0,de.length=0,se.length=0,le=1},initialize:function(){if(1!==le)throw new Error("Renderer Handler was not reset");le=2},handleEvent:function(e){if(2!==le)throw new Error("Renderer Handler is not initialized");if(t.TraceEvents.isThreadName(e)&&e.args.name?.startsWith("CompositorTileWorker")&&se.push({pid:e.pid,tid:e.tid}),t.TraceEvents.isTraceEventBegin(e)||t.TraceEvents.isTraceEventEnd(e)){const t=Te(ie,e.pid),n=ge(t,e.tid),a=Fe(e);if(!a)return;return n.entries.push(a),void ce.push(a)}if(t.TraceEvents.isTraceEventInstant(e)||t.TraceEvents.isTraceEventComplete(e)){const t=Te(ie,e.pid);ge(t,e.tid).entries.push(e),ce.push(e)}},finalize:async function(){if(2!==le)throw new Error("Renderer Handler is not initialized");const{mainFrameId:t,rendererProcessesByFrame:n,threadsInProcess:a}=q();pe(ie,t,n,a),Se(ie),ye(ie),Ie(ie),e.Trace.sortTraceEventsInPlace(ce),le=3},data:he,assignMeta:pe,assignOrigin:Ee,assignIsMainFrame:we,assignThreadName:Me,sanitizeProcesses:Se,sanitizeThreads:Ie,buildHierarchy:ye,makeCompleteEvent:Fe,deps:function(){return["Meta","Samples","AuctionWorklets"]}});function _e(e,t,n){let a="OTHER";return"CrRendererMain"===n.name?a="MAIN_THREAD":"DedicatedWorker thread"===n.name?a="WORKER":n.name?.startsWith("CompositorTileWorker")?a="RASTERIZER":e.worklets.has(t)?a="AUCTION_WORKLET":n.name?.startsWith("ThreadPool")&&(a="THREAD_POOL"),a}function ke(e,t){const n=[];if(e.processes.size)for(const[a,r]of e.processes)for(const[i,s]of r.threads){if(!s.tree)continue;const o=_e(t,a,s);n.push({name:s.name,pid:a,tid:i,processIsOnMainFrame:r.isOnMainFrame,entries:s.entries,tree:s.tree,type:o,entryToNode:e.entryToNode})}return n}var De=Object.freeze({__proto__:null,threadsInRenderer:ke,threadsInTrace:function(e){const t=ke(e.Renderer,e.AuctionWorklets);if(t.length)return t;const n=[];if(e.Samples.profilesInProcess.size)for(const[t,a]of e.Samples.profilesInProcess)for(const[r,i]of a)i.profileTree&&n.push({pid:t,tid:r,name:null,entries:i.profileCalls,processIsOnMainFrame:!1,tree:i.profileTree,type:"CPU_PROFILE",entryToNode:e.Samples.entryToNode});return n}});let Re=1;const Ce=[];let Le=null;class Ne{#e=[];#t={};#n=new qe;#a=null;#r=!1;#i=!1;#s=null;#o=null;#c=null;#d=null;#l=null;#m=null;#u=null;#f=null;#T=null;#g;constructor(e,t,n,a,r){const i=ke(t,n).filter((e=>"MAIN_THREAD"===e.type&&e.processIsOnMainFrame)).map((e=>({tid:e.tid,pid:e.pid,startTime:e.entries[0].ts})));this.#g=r,this.#h(e,i,a.mainFrameId)}framesById(){return this.#t}frames(){return this.#e}#v(e,t){this.#a||this.#p(e,t),this.#d=e,this.#n.addFrameIfNotExists(t,e,!1,!1)}#E(e,t,n){this.#a||this.#p(e,t),this.#n.addFrameIfNotExists(t,e,!0,n),this.#n.setDropped(t,!0),this.#n.setPartial(t,n)}#w(e,t){if(this.#a){if(this.#r||!this.#i){if(this.#l){(this.#o?this.#o.triggerTime:this.#d||this.#l)>this.#a.startTime&&(this.#a.idle=!0,this.#d=null),this.#l=null}const e=this.#n.processPendingBeginFramesOnDrawFrame(t);for(const n of e){const e=this.#a.idle;this.#p(n.startTime,t),e&&this.#o&&this.#M(),n.isDropped&&(this.#a.dropped=!0),n.isPartial&&(this.#a.isPartial=!0)}}this.#r=!1}else this.#p(e,t)}#S(){this.#a&&this.#o&&!this.#l&&this.#M()}#I(){this.#a&&(this.#i=!0)}#y(){this.#c&&(this.#o=this.#c,this.#c=null,this.#i=!1,this.#r=!0)}#F(e){this.#s=e}#P(e,t){t&&(this.#l=e)}#p(e,n){this.#a&&this.#_(this.#a,e),this.#a=new We(n,e,t.Timing.MicroSeconds(e-q().traceBounds.min))}#_(e,t){e.setLayerTree(this.#s),e.setEndTime(t),this.#s&&(this.#s.paints=e.paints);const n=this.#e[this.#e.length-1];this.#e.length&&n&&(e.startTime!==n.endTime||e.startTime>e.endTime)&&console.assert(!1,`Inconsistent frame time for frame ${this.#e.length} (${e.startTime} - ${e.endTime})`),this.#e.push(e),"number"==typeof e.mainFrameId&&(this.#t[e.mainFrameId]=e)}#M(){this.#o&&this.#a&&(this.#a.paints=this.#o.paints,this.#a.mainFrameId=this.#o.mainFrameId,this.#o=null)}#h(e,t,n){let a=0;this.#T=t.length&&t[0].tid||null,this.#f=t.length&&t[0].pid||null;for(let r=0;r<e.length;++r){for(;a+1<t.length&&t[a+1].startTime<=e[r].ts;)this.#T=t[++a].tid,this.#f=t[a].pid;this.#k(e[r],n)}this.#T=null,this.#f=null}#k(e,n){t.TraceEvents.isTraceEventSetLayerId(e)&&e.args.data.frame===n?this.#u=e.args.data.layerTreeId:t.TraceEvents.isTraceEventLayerTreeHostImplSnapshot(e)&&Number(e.id)===this.#u?this.#F({entry:e,paints:[]}):(function(e){return t.TraceEvents.isTraceEventSetLayerId(e)||t.TraceEvents.isTraceEventBeginFrame(e)||t.TraceEvents.isTraceEventDroppedFrame(e)||t.TraceEvents.isTraceEventRequestMainThreadFrame(e)||t.TraceEvents.isTraceEventBeginMainThreadFrame(e)||t.TraceEvents.isTraceEventNeedsBeginFrameChanged(e)||t.TraceEvents.isTraceEventCommit(e)||t.TraceEvents.isTraceEventCompositeLayers(e)||t.TraceEvents.isTraceEventActivateLayerTree(e)||t.TraceEvents.isTraceEventDrawFrame(e)}(e)&&this.#D(e),e.tid===this.#T&&e.pid===this.#f&&this.#R(e))}#D(e){e.args.layerTreeId===this.#u&&(t.TraceEvents.isTraceEventBeginFrame(e)?this.#v(e.ts,e.args.frameSeqId):t.TraceEvents.isTraceEventDrawFrame(e)?this.#w(e.ts,e.args.frameSeqId):t.TraceEvents.isTraceEventActivateLayerTree(e)?this.#S():t.TraceEvents.isTraceEventRequestMainThreadFrame(e)?this.#I():t.TraceEvents.isTraceEventNeedsBeginFrameChanged(e)?this.#P(e.ts,e.args.data&&Boolean(e.args.data.needsBeginFrame)):t.TraceEvents.isTraceEventDroppedFrame(e)&&this.#E(e.ts,e.args.frameSeqId,Boolean(e.args.hasPartialUpdate)))}#R(e){if(function(e){return"RunTask"===e.name&&e.cat.includes("disabled-by-default-devtools.timeline")}(e)&&(this.#m=e.ts),!this.#c&&be.has(e.name)&&(this.#c=new Oe(this.#m||e.ts)),this.#c){if(t.TraceEvents.isTraceEventBeginMainThreadFrame(e)&&e.args.data.frameId&&(this.#c.mainFrameId=e.args.data.frameId),t.TraceEvents.isTraceEventPaint(e)){const t=this.#g.paintsToSnapshots.get(e);t&&this.#c.paints.push(new ze(e,t))}(t.TraceEvents.isTraceEventCompositeLayers(e)||t.TraceEvents.isTraceEventCommit(e))&&e.args.layerTreeId===this.#u&&this.#y()}}}const be=new Set(["ScheduleStyleRecalculation","InvalidateLayout","BeginMainThreadFrame","ScrollLayer"]);class We{startTime;startTimeOffset;endTime;duration;idle;dropped;isPartial;layerTree;paints;mainFrameId;seqId;constructor(e,n,a){this.seqId=e,this.startTime=n,this.startTimeOffset=a,this.endTime=this.startTime,this.duration=t.Timing.MicroSeconds(0),this.idle=!1,this.dropped=!1,this.isPartial=!1,this.layerTree=null,this.paints=[],this.mainFrameId=void 0}setEndTime(e){this.endTime=e,this.duration=t.Timing.MicroSeconds(this.endTime-this.startTime)}setLayerTree(e){this.layerTree=e}}class ze{#C;#L;constructor(e,t){this.#C=e,this.#L=t}layerId(){return this.#C.args.data.layerId}event(){return this.#C}picture(){const e=this.#L.args.snapshot.params?.layer_rect,t=this.#L.args.snapshot.skp64;return e&&t?{rect:e,serializedPicture:t}:null}}class Oe{paints;mainFrameId;triggerTime;constructor(e){this.paints=[],this.mainFrameId=void 0,this.triggerTime=e}}class Be{seqId;startTime;isDropped;isPartial;constructor(e,t,n,a){this.seqId=e,this.startTime=t,this.isDropped=n,this.isPartial=a}}class qe{queueFrames=[];mapFrames={};addFrameIfNotExists(e,t,n,a){e in this.mapFrames||(this.mapFrames[e]=new Be(e,t,n,a),this.queueFrames.push(e))}setDropped(e,t){e in this.mapFrames&&(this.mapFrames[e].isDropped=t)}setPartial(e,t){e in this.mapFrames&&(this.mapFrames[e].isPartial=t)}processPendingBeginFramesOnDrawFrame(e){const t=[];if(e in this.mapFrames){for(;this.queueFrames[0]!==e;){const e=this.queueFrames[0];this.mapFrames[e].isDropped&&t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}return t}}var Ae=Object.freeze({__proto__:null,reset:function(){Re=1,Ce.length=0},initialize:function(){if(1!==Re)throw new Error("FramesHandler was not reset before being initialized");Re=2},handleEvent:function(e){Ce.push(e)},finalize:async function(){if(2!==Re)throw new Error("FramesHandler is not initialized");e.Trace.sortTraceEventsInPlace(Ce);const t=new Ne(Ce,he(),g(),q(),K());Le=t},data:function(){return{frames:Le?Array.from(Le.frames()):[],framesById:Le?{...Le.framesById()}:{}}},deps:function(){return["Meta","Renderer","AuctionWorklets","LayerTree"]},TimelineFrameModel:Ne,TimelineFrame:We,LayerPaintEvent:ze,PendingFrame:Oe,TimelineFrameBeginFrameQueue:qe,framesWithinWindow:function(e,t,a){const r=n.ArrayUtilities.lowerBound(e,t||0,((e,t)=>e-t.endTime)),i=n.ArrayUtilities.lowerBound(e,a||1/0,((e,t)=>e-t.startTime));return e.slice(r,i)}});let He=1;const Ue=new Map;let xe=[];var je=Object.freeze({__proto__:null,reset:function(){Ue.clear(),xe=[],He=1},initialize:function(){if(1!==He)throw new Error("GPU Handler was not reset before being initialized");He=2},handleEvent:function(n){if(2!==He)throw new Error("GPU Handler is not initialized");t.TraceEvents.isTraceEventGPUTask(n)&&e.Trace.addEventToProcessThread(n,Ue)},finalize:async function(){if(2!==He)throw new Error("GPU Handler is not initialized");const{gpuProcessId:e,gpuThreadId:t}=q(),n=Ue.get(e);n&&t&&(xe=n.get(t)||[]),He=3},data:function(){if(3!==He)throw new Error("GPU Handler is not finalized");return{mainGPUThreadTasks:[...xe]}},deps:function(){return["Meta"]}});let Ge=1;const Ve=new Map,$e=new Map,Ye=new Map,Ke=new Map,Qe=new Map,Xe=new Map,Je=new Map,Ze=new Map,et=new Map;function tt(e){Ke.set(e.event,e.initiator);const t=Qe.get(e.initiator)||[];t.push(e.event),Qe.set(e.initiator,t)}var nt=Object.freeze({__proto__:null,reset:function(){Ve.clear(),$e.clear(),Ye.clear(),Je.clear(),Ke.clear(),Qe.clear(),Xe.clear(),Ze.clear(),et.clear(),Ge=1},initialize:function(){if(1!==Ge)throw new Error("InitiatorsHandler was not reset before being initialized");Ge=2},handleEvent:function(n){if(t.TraceEvents.isTraceEventScheduleStyleRecalculation(n))Ve.set(n.args.data.frame,n);else if(t.TraceEvents.isTraceEventUpdateLayoutTree(n)){if(n.args.beginData){Ye.set(n.args.beginData.frame,n);const e=Ve.get(n.args.beginData.frame);e&&tt({event:n,initiator:e})}}else if(t.TraceEvents.isTraceEventInvalidateLayout(n)){let t=n;if(!$e.has(n.args.data.frame)){const a=Ye.get(n.args.data.frame);if(a){const{endTime:r}=e.Timing.eventTimingsMicroSeconds(a),i=Ke.get(a);i&&r&&r>n.ts&&(t=i)}}$e.set(n.args.data.frame,t)}else if(t.TraceEvents.isTraceEventLayout(n)){const e=$e.get(n.args.beginData.frame);e&&tt({event:n,initiator:e}),$e.delete(n.args.beginData.frame)}else if(t.TraceEvents.isTraceEventRequestAnimationFrame(n))Xe.set(n.args.data.id,n);else if(t.TraceEvents.isTraceEventFireAnimationFrame(n)){const e=Xe.get(n.args.data.id);e&&tt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventTimerInstall(n))Je.set(n.args.data.timerId,n);else if(t.TraceEvents.isTraceEventTimerFire(n)){const e=Je.get(n.args.data.timerId);e&&tt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventRequestIdleCallback(n))Ze.set(n.args.data.id,n);else if(t.TraceEvents.isTraceEventFireIdleCallback(n)){const e=Ze.get(n.args.data.id);e&&tt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventWebSocketCreate(n))et.set(n.args.data.identifier,n);else if(t.TraceEvents.isTraceEventWebSocketSendHandshakeRequest(n)){const e=et.get(n.args.data.identifier);e&&tt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventWebSocketSendHandshakeRequest(n)||t.TraceEvents.isTraceEventWebSocketReceiveHandshakeResponse(n)||t.TraceEvents.isTraceEventWebSocketDestroy(n)){const e=et.get(n.args.data.identifier);e&&tt({event:n,initiator:e})}},finalize:async function(){if(2!==Ge)throw new Error("InitiatorsHandler is not initialized");Ge=3},data:function(){return{eventToInitiator:new Map(Ke),initiatorToEvents:new Map(Qe)}}});let at=1;const rt=new Map;let it=null,st=!1;const ot=[];function ct(e,t){const n=rt.get(e)||[],a={...t,name:"SyntheticInvalidation",frame:t.args.data.frame,nodeId:t.args.data.nodeId,rawEvent:t};t.args.data.nodeName&&(a.nodeName=t.args.data.nodeName),t.args.data.reason&&(a.reason=t.args.data.reason),t.args.data.stackTrace&&(a.stackTrace=t.args.data.stackTrace),n.push(a),rt.set(e,n)}var dt=Object.freeze({__proto__:null,reset:function(){at=1,rt.clear(),it=null,ot.length=0,st=!1},initialize:function(){if(1!==at)throw new Error("InvalidationsHandler was not reset before being initialized");at=2},handleEvent:function(e){if(t.TraceEvents.isTraceEventUpdateLayoutTree(e)){it=e;for(const n of ot){if(t.TraceEvents.isTraceEventLayoutInvalidationTracking(n))continue;const a=it.args.beginData?.frame;a&&n.args.data.frame===a&&ct(e,n)}}else if(t.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleInvalidatorInvalidationTracking(e)||t.TraceEvents.isTraceEventLayoutInvalidationTracking(e)){if(st&&(ot.length=0,it=null,st=!1),it&&(t.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleInvalidatorInvalidationTracking(e))){const t=it.ts+(it.dur||0);e.ts>=it.ts&&e.ts<=t&&it.args.beginData?.frame===e.args.data.frame&&ct(it,e)}ot.push(e)}else if(t.TraceEvents.isTraceEventPaint(e))st=!0;else if(t.TraceEvents.isTraceEventLayout(e)){const n=e.args.beginData.frame;for(const a of ot)t.TraceEvents.isTraceEventLayoutInvalidationTracking(a)&&a.args.data.frame===n&&ct(e,a)}},finalize:async function(){if(2!==at)throw new Error("InvalidationsHandler is not initialized");at=3},data:function(){return{invalidationsForEvent:new Map(rt)}}});const lt=new Map;var mt=Object.freeze({__proto__:null,reset:function(){lt.clear()},handleEvent:function(e){t.TraceEvents.isTraceEventLargestImagePaintCandidate(e)&&e.args.data&&lt.set(e.args.data.DOMNodeId,e)},data:function(){return new Map(lt)}});const ut=new Map;var ft=Object.freeze({__proto__:null,reset:function(){ut.clear()},handleEvent:function(e){t.TraceEvents.isTraceEventLargestTextPaintCandidate(e)&&e.args.data&&ut.set(e.args.data.DOMNodeId,e)},data:function(){return new Map(ut)}});const Tt=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(5e3)),gt=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(1e3)),ht=[],vt=[],pt=[],Et=[],wt=new Set,Mt=[];let St=0,It=-1;const yt=[],Ft=[];let Pt=1;function _t(e){return{min:e,max:e,range:t.Timing.MicroSeconds(0)}}function kt(e,n){e.max=n,e.range=t.Timing.MicroSeconds(e.max-e.min)}var Dt=Object.freeze({__proto__:null,MAX_CLUSTER_DURATION:Tt,MAX_SHIFT_TIME_DELTA:gt,initialize:function(){if(1!==Pt)throw new Error("LayoutShifts Handler was not reset");Pt=2},reset:function(){Pt=1,ht.length=0,vt.length=0,pt.length=0,Et.length=0,Mt.length=0,wt.clear(),yt.length=0,St=0,Ft.length=0,It=-1},handleEvent:function(e){if(2!==Pt)throw new Error("Handler is not initialized");!t.TraceEvents.isTraceEventLayoutShift(e)||e.args.data?.had_recent_input?t.TraceEvents.isTraceEventLayoutInvalidationTracking(e)?vt.push(e):(t.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(e)&&pt.push(e),t.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(e)&&Et.push(e),t.TraceEvents.isTraceEventPrePaint(e)&&Mt.push(e)):ht.push(e)},finalize:async function(){ht.sort(((e,t)=>e.ts-t.ts)),Mt.sort(((e,t)=>e.ts-t.ts)),vt.sort(((e,t)=>e.ts-t.ts)),await async function(){const{navigationsByFrameId:e,mainFrameId:a,traceBounds:r}=q(),i=e.get(a)||[];if(0===ht.length)return;let s=ht[0].ts,o=ht[0].ts,c=null;for(const e of ht){const a=e.ts-s>Tt,r=e.ts-o>gt,d=n.ArrayUtilities.nearestIndexFromEnd(i,(t=>t.ts<e.ts)),l=c!==d&&null!==d;if(a||r||l||!yt.length){const n=e.ts,c=a?s+Tt:1/0,m=r?o+gt:1/0,u=l?i[d].ts:1/0,f=Math.min(c,m,u);if(yt.length>0){kt(yt[yt.length-1].clusterWindow,t.Timing.MicroSeconds(f))}yt.push({events:[],clusterWindow:_t(n),clusterCumulativeScore:0,scoreWindows:{good:_t(n),needsImprovement:null,bad:null}}),s=n}const m=yt[yt.length-1],u=null!==d?t.Timing.MicroSeconds(e.ts-i[d].ts):void 0;if(m.clusterCumulativeScore+=e.args.data?e.args.data.weighted_score_delta:0,!e.args.data)continue;const f={...e,args:{frame:e.args.frame,data:{...e.args.data,rawEvent:e}},parsedData:{timeFromNavigation:u,cumulativeWeightedScoreInWindow:m.clusterCumulativeScore,sessionWindowData:{cumulativeWindowScore:0,id:yt.length}}};m.events.push(f),kt(m.clusterWindow,e.ts),o=e.ts,c=d}for(const e of yt){let a=0,s=-1;if(e===yt[yt.length-1]){const a=Tt+e.clusterWindow.min,s=e.clusterWindow.max+gt,o=n.ArrayUtilities.nearestIndexFromBeginning(i,(t=>t.ts>e.clusterWindow.max)),c=o?i[o].ts:1/0,d=Math.min(a,s,r.max,c);kt(e.clusterWindow,t.Timing.MicroSeconds(d))}for(const n of e.events){a+=n.args.data?n.args.data.weighted_score_delta:0,s=n.parsedData.sessionWindowData.id;const r=n.ts;n.parsedData.sessionWindowData.cumulativeWindowScore=e.clusterCumulativeScore,a<.1?kt(e.scoreWindows.good,r):a>=.1&&a<.25?(e.scoreWindows.needsImprovement||(kt(e.scoreWindows.good,t.Timing.MicroSeconds(r-1)),e.scoreWindows.needsImprovement=_t(r)),kt(e.scoreWindows.needsImprovement,r)):a>=.25&&(e.scoreWindows.bad||(e.scoreWindows.needsImprovement?kt(e.scoreWindows.needsImprovement,t.Timing.MicroSeconds(r-1)):kt(e.scoreWindows.good,t.Timing.MicroSeconds(r-1)),e.scoreWindows.bad=_t(n.ts)),kt(e.scoreWindows.bad,r)),e.scoreWindows.bad?kt(e.scoreWindows.bad,e.clusterWindow.max):e.scoreWindows.needsImprovement?kt(e.scoreWindows.needsImprovement,e.clusterWindow.max):kt(e.scoreWindows.good,e.clusterWindow.max)}a>St&&(It=s,St=a)}}(),function(){const{traceBounds:e}=q();Ft.push({ts:e.min,score:0});for(const e of yt){let t=0;e.events[0].args.data&&Ft.push({ts:e.clusterWindow.min,score:e.events[0].args.data.weighted_score_delta});for(let n=0;n<e.events.length;n++){const a=e.events[n];a.args.data&&(t+=a.args.data.weighted_score_delta,Ft.push({ts:a.ts,score:t}))}Ft.push({ts:e.clusterWindow.max,score:0})}}(),function(){wt.clear();for(const e of ht)if(e.args.data?.impacted_nodes)for(const t of e.args.data.impacted_nodes)wt.add(t.node_id);for(const e of vt)e.args.data?.nodeId&&wt.add(e.args.data.nodeId);for(const e of pt)e.args.data?.nodeId&&wt.add(e.args.data.nodeId)}(),Pt=3},data:function(){if(3!==Pt)throw new Error("Layout Shifts Handler is not finalized");return{clusters:[...yt],sessionMaxScore:St,clsWindowID:It,prePaintEvents:[...Mt],layoutInvalidationEvents:[...vt],scheduleStyleInvalidationEvents:[...pt],styleRecalcInvalidationEvents:[],scoreRecords:[...Ft],backendNodeIds:[...wt]}},deps:function(){return["Screenshots","Meta"]},stateForLayoutShiftScore:function(e){let t="good";return e>=.1&&(t="ok"),e>=.25&&(t="bad"),t}});const Rt=new Map;var Ct=Object.freeze({__proto__:null,reset:function(){Rt.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventUpdateCounters(e)){const t=n.MapUtilities.getWithDefault(Rt,e.pid,(()=>[]));t.push(e),Rt.set(e.pid,t)}},data:function(){return{updateCountersByProcess:new Map(Rt)}}});const Lt=1e3,Nt=1e6,bt=new Map,Wt=new Map,zt=[];function Ot(e,t,n){bt.has(e)||bt.set(e,{});const a=bt.get(e);if(!a)throw new Error(`Unable to locate trace events for request ID ${e}`);if(Array.isArray(a[t])){const e=n;a[t].push(...e)}else a[t]=n}function Bt(e){for(const t of e)if(t>0)return t;return 0}let qt=1;var At=Object.freeze({__proto__:null,reset:function(){Wt.clear(),bt.clear(),zt.length=0,qt=1},initialize:function(){qt=2},handleEvent:function(e){if(2!==qt)throw new Error("Network Request handler is not initialized");t.TraceEvents.isTraceEventResourceChangePriority(e)?Ot(e.args.data.requestId,"changePriority",e):t.TraceEvents.isTraceEventResourceWillSendRequest(e)?Ot(e.args.data.requestId,"willSendRequests",[e]):t.TraceEvents.isTraceEventResourceSendRequest(e)?Ot(e.args.data.requestId,"sendRequests",[e]):t.TraceEvents.isTraceEventResourceReceiveResponse(e)?Ot(e.args.data.requestId,"receiveResponse",e):t.TraceEvents.isTraceEventResourceReceivedData(e)?Ot(e.args.data.requestId,"receivedData",[e]):t.TraceEvents.isTraceEventResourceFinish(e)?Ot(e.args.data.requestId,"resourceFinish",e):t.TraceEvents.isTraceEventResourceMarkAsCached(e)&&Ot(e.args.data.requestId,"resourceMarkAsCached",e)},finalize:async function(){if(2!==qt)throw new Error("Network Request handler is not initialized");const{rendererProcessesByFrame:a}=q();for(const[r,i]of bt.entries()){if(!i.sendRequests||!i.receiveResponse)continue;const s=[];for(let e=0;e<i.sendRequests.length-1;e++){const n=i.sendRequests[e],a=i.sendRequests[e+1];let r=n.ts,o=t.Timing.MicroSeconds(a.ts-n.ts);if(i.willSendRequests&&i.willSendRequests[e]&&i.willSendRequests[e+1]){const n=i.willSendRequests[e],a=i.willSendRequests[e+1];r=n.ts,o=t.Timing.MicroSeconds(a.ts-n.ts)}s.push({url:n.args.data.url,priority:n.args.data.priority,requestMethod:n.args.data.requestMethod,ts:r,dur:o})}const o=0!==i.resourceFinish?.args.data.encodedDataLength,c=i.receiveResponse.args.data.fromCache&&!i.receiveResponse.args.data.fromServiceWorker&&!o,d=void 0!==i.resourceMarkAsCached,l=d||c,m=i.receiveResponse.args.data.timing;if(!m&&!l)continue;const u=i.sendRequests[0],f=i.sendRequests[i.sendRequests.length-1],T=f.args.data.priority;let g=T;i.changePriority&&(g=i.changePriority.args.data.priority);const h=i.willSendRequests&&i.willSendRequests.length?t.Timing.MicroSeconds(i.willSendRequests[0].ts):t.Timing.MicroSeconds(u.ts),v=i.willSendRequests&&i.willSendRequests.length?t.Timing.MicroSeconds(i.willSendRequests[i.willSendRequests.length-1].ts):t.Timing.MicroSeconds(f.ts),p=i.resourceFinish?i.resourceFinish.ts:v,E=i.resourceFinish?.args.data.finishTime?t.Timing.MicroSeconds(i.resourceFinish.args.data.finishTime*Nt):t.Timing.MicroSeconds(p),w=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((E||v)-v),M=t.Timing.MicroSeconds(p-(E||p)),S=t.Timing.MicroSeconds(v-h),I=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds(n.NumberUtilities.clamp(m.requestTime*Nt-v,0,Number.MAX_VALUE)),y=l?t.Timing.MicroSeconds(i.receiveResponse.ts-h):t.Timing.MicroSeconds(Bt([m.dnsStart*Lt,m.connectStart*Lt,m.sendStart*Lt,i.receiveResponse.ts-v])),F=l?h:t.Timing.MicroSeconds(m.requestTime*Nt+m.sendStart*Lt),P=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.receiveHeadersEnd-m.sendEnd)*Lt),_=l?h:t.Timing.MicroSeconds(m.requestTime*Nt+m.receiveHeadersEnd*Lt),k=l?t.Timing.MicroSeconds(p-i.receiveResponse.ts):t.Timing.MicroSeconds((E||_)-_),D=t.Timing.MicroSeconds(w+M),R=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.dnsEnd-m.dnsStart)*Lt),C=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.sslEnd-m.sslStart)*Lt),L=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.proxyEnd-m.proxyStart)*Lt),N=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.sendEnd-m.sendStart)*Lt),b=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.connectEnd-m.connectStart)*Lt),{frame:W,url:z,renderBlocking:O}=f.args.data,{encodedDataLength:B,decodedBodyLength:q}=i.resourceFinish?i.resourceFinish.args.data:{encodedDataLength:0,decodedBodyLength:0},{host:A,protocol:H,pathname:U,search:x}=new URL(z),j="https:"===H,G=e.Trace.activeURLForFrameAtTime(W,f.ts,a)||"",V={args:{data:{syntheticData:{dnsLookup:R,download:k,downloadStart:_,finishTime:E,initialConnection:b,isDiskCached:c,isHttps:j,isMemoryCached:d,isPushedResource:o,networkDuration:w,processingDuration:M,proxyNegotiation:L,queueing:I,redirectionDuration:S,requestSent:N,sendStartTime:F,ssl:C,stalled:y,totalTime:D,waiting:P},decodedBodyLength:q,encodedDataLength:B,frame:W,fromServiceWorker:i.receiveResponse.args.data.fromServiceWorker,isLinkPreload:i.receiveResponse.args.data.isLinkPreload||!1,host:A,mimeType:i.receiveResponse.args.data.mimeType,pathname:U,priority:g,initialPriority:T,protocol:H,redirects:s,renderBlocking:O||"non_blocking",requestId:r,requestingFrameUrl:G,requestMethod:f.args.data.requestMethod,resourceType:f.args.data.resourceType,search:x,statusCode:i.receiveResponse.args.data.statusCode,responseHeaders:i.receiveResponse.args.data.headers||[],fetchPriorityHint:f.args.data.fetchPriorityHint,initiator:f.args.data.initiator,stackTrace:f.args.data.stackTrace,timing:m,url:z}},cat:"loading",name:"SyntheticNetworkRequest",ph:"X",dur:t.Timing.MicroSeconds(p-h),tdur:t.Timing.MicroSeconds(p-h),ts:t.Timing.MicroSeconds(h),tts:t.Timing.MicroSeconds(h),pid:f.pid,tid:f.tid},$=n.MapUtilities.getWithDefault(Wt,A,(()=>({renderBlocking:[],nonRenderBlocking:[],all:[]})));"non_blocking"===V.args.data.renderBlocking?$.nonRenderBlocking.push(V):$.renderBlocking.push(V),$.all.push(V),zt.push(V)}qt=3},data:function(){if(3!==qt)throw new Error("Network Request handler is not finalized");return{byOrigin:new Map(Wt),byTime:[...zt]}},deps:function(){return["Meta"]}});const Ht=new Map;let Ut=[];let xt=[];const jt=new Set,Gt=[t.TraceEvents.isTraceEventMarkDOMContent,t.TraceEvents.isTraceEventMarkLoad,t.TraceEvents.isTraceEventFirstPaint,t.TraceEvents.isTraceEventFirstContentfulPaint,t.TraceEvents.isTraceEventLargestContentfulPaintCandidate,t.TraceEvents.isTraceEventNavigationStart];function Vt(e){return Gt.some((t=>t(e)))}const $t=[...Gt,t.TraceEvents.isTraceEventInteractiveTime];function Yt(e){return $t.some((t=>t(e)))}function Kt(a,r){const i=a.args.data?.navigationId;if(!i)throw new Error("Navigation event unexpectedly had no navigation ID.");const s=Xt(r),{rendererProcessesByFrame:o}=q(),c=o.get(s);if(!c)return;if(c.get(r.pid)&&!t.TraceEvents.isTraceEventNavigationStart(r))if(t.TraceEvents.isTraceEventFirstContentfulPaint(r)){const n=t.Timing.MicroSeconds(r.ts-a.ts);Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(n,{format:2,maximumFractionDigits:2}),metricName:"FCP",classification:Zt(n),navigation:a})}else if(t.TraceEvents.isTraceEventFirstPaint(r)){const n=t.Timing.MicroSeconds(r.ts-a.ts);Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(n,{format:2,maximumFractionDigits:2}),metricName:"FP",classification:"unclassified",navigation:a})}else if(t.TraceEvents.isTraceEventMarkDOMContent(r)){const n=t.Timing.MicroSeconds(r.ts-a.ts);Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(n,{format:2,maximumFractionDigits:2}),metricName:"DCL",classification:"unclassified",navigation:a})}else if(t.TraceEvents.isTraceEventInteractiveTime(r)){const n=t.Timing.MicroSeconds(r.ts-a.ts);Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(n,{format:2,maximumFractionDigits:2}),metricName:"TTI",classification:en(n),navigation:a});const o=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(r.args.args.total_blocking_time_ms));Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(o,{format:1,maximumFractionDigits:2}),metricName:"TBT",classification:an(o),navigation:a})}else if(t.TraceEvents.isTraceEventMarkLoad(r)){const n=t.Timing.MicroSeconds(r.ts-a.ts);Qt(s,i,{event:r,score:e.Timing.formatMicrosecondsTime(n,{format:2,maximumFractionDigits:2}),metricName:"L",classification:"unclassified",navigation:a})}else if(t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(r)){const o=r.args.data?.candidateIndex;if(!o)throw new Error("Largest Contenful Paint unexpectedly had no candidateIndex.");const c=t.Timing.MicroSeconds(r.ts-a.ts),d={event:r,score:e.Timing.formatMicrosecondsTime(c,{format:2,maximumFractionDigits:2}),metricName:"LCP",classification:tn(c),navigation:a},l=n.MapUtilities.getWithDefault(Ht,s,(()=>new Map)),m=n.MapUtilities.getWithDefault(l,i,(()=>new Map)).get("LCP");if(void 0===m)return jt.add(d.event),void Qt(s,i,d);const u=m.event;if(!t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(u))return;const f=u.args.data?.candidateIndex;if(!f)return;f<o&&(jt.delete(u),jt.add(d.event),Qt(s,i,d))}else if(!t.TraceEvents.isTraceEventLayoutShift(r))return n.assertNever(r,`Unexpected event type: ${r}`)}function Qt(e,t,a){const r=n.MapUtilities.getWithDefault(Ht,e,(()=>new Map)),i=n.MapUtilities.getWithDefault(r,t,(()=>new Map));i.delete(a.metricName),i.set(a.metricName,a)}function Xt(e){if(t.TraceEvents.isTraceEventFirstContentfulPaint(e)||t.TraceEvents.isTraceEventInteractiveTime(e)||t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)||t.TraceEvents.isTraceEventNavigationStart(e)||t.TraceEvents.isTraceEventLayoutShift(e)||t.TraceEvents.isTraceEventFirstPaint(e))return e.args.frame;if(t.TraceEvents.isTraceEventMarkDOMContent(e)||t.TraceEvents.isTraceEventMarkLoad(e)){const t=e.args.data?.frame;if(!t)throw new Error("MarkDOMContent unexpectedly had no frame ID.");return t}n.assertNever(e,`Unexpected event type: ${e}`)}function Jt(a){if(t.TraceEvents.isTraceEventFirstContentfulPaint(a)||t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(a)||t.TraceEvents.isTraceEventFirstPaint(a)){const e=a.args.data?.navigationId;if(!e)throw new Error("Trace event unexpectedly had no navigation ID.");const{navigationsByNavigationId:t}=q(),n=t.get(e);return n||null}if(t.TraceEvents.isTraceEventMarkDOMContent(a)||t.TraceEvents.isTraceEventInteractiveTime(a)||t.TraceEvents.isTraceEventLayoutShift(a)||t.TraceEvents.isTraceEventMarkLoad(a)){const t=Xt(a),{navigationsByFrameId:n}=q();return e.Trace.getNavigationForTraceEvent(a,t,n)}return t.TraceEvents.isTraceEventNavigationStart(a)?null:n.assertNever(a,`Unexpected event type: ${a}`)}function Zt(n){const a=e.Timing.secondsToMicroseconds(t.Timing.Seconds(1.8));let r="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(3))&&(r="ok"),n<=a&&(r="good"),r}function en(n){const a=e.Timing.secondsToMicroseconds(t.Timing.Seconds(3.8));let r="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(7.3))&&(r="ok"),n<=a&&(r="good"),r}function tn(n){const a=e.Timing.secondsToMicroseconds(t.Timing.Seconds(2.5));let r="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(4))&&(r="ok"),n<=a&&(r="good"),r}function nn(e){return"unclassified"}function an(n){const a=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(200));let r="bad";return n<=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(600))&&(r="ok"),n<=a&&(r="good"),r}var rn=Object.freeze({__proto__:null,reset:function(){Ht.clear(),xt=[],Ut=[],jt.clear()},MarkerName:["MarkDOMContent","MarkLoad","firstPaint","firstContentfulPaint","largestContentfulPaint::Candidate"],isTraceEventMarkerEvent:Vt,eventIsPageLoadEvent:Yt,handleEvent:function(e){Yt(e)&&xt.push(e)},getFrameIdForPageLoadEvent:Xt,scoreClassificationForFirstContentfulPaint:Zt,scoreClassificationForTimeToInteractive:en,scoreClassificationForLargestContentfulPaint:tn,scoreClassificationForDOMContentLoaded:nn,scoreClassificationForTotalBlockingTime:an,finalize:async function(){xt.sort(((e,t)=>e.ts-t.ts));for(const e of xt){const t=Jt(e);t&&Kt(t,e)}const e=function(){const e=[],t=[...Ht.values()].flatMap((e=>[...e.values()]));for(let n=0;n<t.length;n++){const a=t[n].get("LCP");a&&a.event&&e.push(a.event)}return e}(),n=q().mainFrameId,a=[...e,...xt.filter((e=>!t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)))].filter(Vt);Ut=a.filter((e=>Xt(e)===n)).sort(((e,t)=>e.ts-t.ts))},data:function(){return{metricScoresByFrameId:new Map(Ht),allMarkerEvents:[...Ut]}},deps:function(){return["Meta"]}});const sn=[],on=[],cn=[];let dn={};function ln(e){const t=parseInt(e.id,16);if(1===t)return e.ts;return dn[t]??e.ts}var mn=Object.freeze({__proto__:null,reset:function(){sn.length=0,on.length=0,cn.length=0,dn={}},handleEvent:function(e){t.TraceEvents.isTraceEventScreenshot(e)?on.push(e):t.TraceEvents.isTraceEventPipelineReporter(e)&&sn.push(e)},finalize:async function(){const n=e.Trace.createMatchedSortedSyntheticEvents(sn);dn=Object.fromEntries(n.map((e=>[e.args.data.beginEvent.args.chrome_frame_reporter.frame_sequence,t.Timing.MicroSeconds(e.ts+e.dur)])));for(const e of on){const{cat:t,name:n,ph:a,pid:r,tid:i}=e,s={cat:t,name:n,ph:a,pid:r,tid:i,ts:ln(e),args:{dataUri:`data:image/jpg;base64,${e.args.snapshot}`}};cn.push(s)}},data:function(){return[...cn]},deps:function(){return["Meta"]}});const un=[],fn=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(200));let Tn=null;const gn=[],hn=[],vn=new Map,pn=[];let En=1;const wn=new Set(["pointerdown","touchstart","pointerup","touchend","mousedown","mouseup","click"]),Mn=new Set(["keydown","keypress","keyup"]);function Sn(e){return wn.has(e.type)?"POINTER":Mn.has(e.type)?"KEYBOARD":"OTHER"}function In(e){const n={POINTER:new Map,KEYBOARD:new Map,OTHER:new Map};function a(e){const a=Sn(e),r=n[a],i=t.Timing.MicroSeconds(e.ts+e.dur),s=r.get(i);if(s){if(e.ts<s.ts)r.set(i,e);else if(e.ts===s.ts&&e.interactionId===s.interactionId){const t=s.processingEnd-s.processingStart;e.processingEnd-e.processingStart>t&&r.set(i,e)}e.processingStart<s.processingStart&&(s.processingStart=e.processingStart,yn(s)),e.processingEnd>s.processingEnd&&(s.processingEnd=e.processingEnd,yn(s))}else r.set(i,e)}for(const t of e)a(t);const r=Object.values(n).flatMap((e=>Array.from(e.values())));return r.sort(((e,t)=>e.ts-t.ts)),r}function yn(e){const n=e.args.data.beginEvent,a=e.args.data.endEvent;e.inputDelay=t.Timing.MicroSeconds(e.processingStart-n.ts),e.mainThreadHandling=t.Timing.MicroSeconds(e.processingEnd-e.processingStart),e.presentationDelay=t.Timing.MicroSeconds(a.ts-e.processingEnd)}function Fn(){return{allEvents:[...un],interactionEvents:[...gn],interactionEventsWithNoNesting:[...hn],longestInteractionEvent:Tn,interactionsOverThreshold:new Set(gn.filter((e=>e.dur>fn)))}}var Pn=Object.freeze({__proto__:null,LONG_INTERACTION_THRESHOLD:fn,reset:function(){un.length=0,gn.length=0,pn.length=0,vn.clear(),hn.length=0,Tn=null,En=2},handleEvent:function(e){if(2!==En)throw new Error("Handler is not initialized");if(!t.TraceEvents.isTraceEventEventTiming(e))return;if(t.TraceEvents.isTraceEventEventTimingEnd(e)&&vn.set(e.id,e),un.push(e),!e.args.data||!t.TraceEvents.isTraceEventEventTimingStart(e))return;const{duration:n,interactionId:a}=e.args.data;n<1||void 0===a||0===a||pn.push(e)},categoryOfInteraction:Sn,removeNestedInteractions:In,finalize:async function(){for(const n of pn){const a=vn.get(n.id);if(!a)continue;if(!n.args.data?.type||!n.args.data?.interactionId)continue;const r=t.Timing.MicroSeconds(e.Timing.millisecondsToMicroseconds(n.args.data.processingStart)-e.Timing.millisecondsToMicroseconds(n.args.data.timeStamp)+n.ts),i=t.Timing.MicroSeconds(e.Timing.millisecondsToMicroseconds(n.args.data.processingEnd)-e.Timing.millisecondsToMicroseconds(n.args.data.timeStamp)+n.ts),s={cat:n.cat,name:n.name,pid:n.pid,tid:n.tid,ph:n.ph,processingStart:r,processingEnd:i,inputDelay:t.Timing.MicroSeconds(-1),mainThreadHandling:t.Timing.MicroSeconds(-1),presentationDelay:t.Timing.MicroSeconds(-1),args:{data:{beginEvent:n,endEvent:a}},ts:n.ts,dur:t.Timing.MicroSeconds(a.ts-n.ts),type:n.args.data.type,interactionId:n.args.data.interactionId};yn(s),gn.push(s)}En=3,hn.push(...In(gn));for(const e of hn)(!Tn||Tn.dur<e.dur)&&(Tn=e)},data:Fn});let _n=[];const kn=[],Dn=[],Rn=[],Cn=[];let Ln=1;const Nn=["workerStart","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd"],bn=["navigationStart","unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","commitNavigationEnd","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"];var Wn=Object.freeze({__proto__:null,reset:function(){_n.length=0,kn.length=0,Dn.length=0,Rn.length=0,Cn.length=0,Ln=2},handleEvent:function(e){if(2!==Ln)throw new Error("UserTimings handler is not initialized");[...Nn,...bn].includes(e.name)||(t.TraceEvents.isTraceEventPerformanceMeasure(e)?kn.push(e):(t.TraceEvents.isTraceEventPerformanceMark(e)&&Dn.push(e),t.TraceEvents.isTraceEventConsoleTime(e)&&Rn.push(e),t.TraceEvents.isTraceEventTimeStamp(e)&&Cn.push(e)))},finalize:async function(){if(2!==Ln)throw new Error("UserTimings handler is not initialized");const t=[...kn,...Rn];_n=e.Trace.createMatchedSortedSyntheticEvents(t),Ln=3},data:function(){if(3!==Ln)throw new Error("UserTimings handler is not finalized");return{performanceMeasures:_n.filter((e=>"blink.user_timing"===e.cat)),consoleTimings:_n.filter((e=>"blink.console"===e.cat)),performanceMarks:[...Dn],timestampEvents:[...Cn]}}});const zn=new Map,On=new Map,Bn=[],qn=[],An=[],Hn=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(30)),Un=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(50));function xn(e,t){const a=n.MapUtilities.getWithDefault(zn,e,(()=>[]));a.push(t),zn.set(e,a);const r=n.MapUtilities.getWithDefault(On,t,(()=>[]));r.push(e),On.set(t,r)}function jn(e,t,n=!0){let a=t.at(-1);for(;a&&e.ts>a.ts+(a.dur||0);)t.pop(),a=t.at(-1);n&&t.push(e)}var Gn=Object.freeze({__proto__:null,FORCED_REFLOW_THRESHOLD:Hn,LONG_MAIN_THREAD_TASK_THRESHOLD:Un,reset:function(){zn.clear(),On.clear(),Bn.length=0,qn.length=0,An.length=0},handleEvent:function(n){if(function(e){if(jn(e,Bn),jn(e,qn,t.TraceEvents.isJSInvocationEvent(e)),qn.length&&("Layout"===e.name||"RecalculateStyles"===e.name||"UpdateLayoutTree"===e.name))return void An.push(e);if(1===Bn.length){const e=An.reduce(((e,t)=>e+(t.dur||0)),0);e>=Hn&&An.forEach((e=>xn(e,"FORCED_REFLOW"))),An.length=0}}(n),"RunTask"!==n.name)if(t.TraceEvents.isTraceEventFireIdleCallback(n)){const{duration:t}=e.Timing.eventTimingsMilliSeconds(n);t>n.args.data.allottedMilliseconds&&xn(n,"IDLE_CALLBACK_OVER_TIME")}else;else{const{duration:t}=e.Timing.eventTimingsMicroSeconds(n);t>Un&&xn(n,"LONG_TASK")}},deps:function(){return["UserInteractions"]},finalize:async function(){const e=Fn().interactionsOverThreshold;for(const t of e)xn(t,"LONG_INTERACTION")},data:function(){return{perEvent:new Map(zn),perWarning:new Map(On)}}});let Vn=1;const $n=[],Yn=new Map,Kn=new Map;var Qn=Object.freeze({__proto__:null,initialize:function(){if(1!==Vn)throw new Error("Workers Handler was not reset");Vn=2},reset:function(){$n.length=0,Yn.clear(),Kn.clear(),Vn=1},handleEvent:function(e){if(2!==Vn)throw new Error("Workers Handler is not initialized");t.TraceEvents.isTraceEventTracingSessionIdForWorker(e)&&$n.push(e)},finalize:async function(){if(2!==Vn)throw new Error("Handler is not initialized");for(const e of $n)e.args.data&&(Yn.set(e.args.data.workerThreadId,e.args.data.workerId),Kn.set(e.args.data.workerId,e.args.data.url));Vn=3},data:function(){if(3!==Vn)throw new Error("Workers Handler is not finalized");return{workerSessionIdEvents:[...$n],workerIdByThread:new Map(Yn),workerURLById:new Map(Kn)}}}),Xn=Object.freeze({__proto__:null,Animations:o,AuctionWorklets:h,Frames:Ae,GPU:je,Initiators:nt,Invalidations:dt,LargestImagePaint:mt,LargestTextPaint:ft,LayerTree:Q,LayoutShifts:Dt,Memory:Ct,Meta:A,NetworkRequests:At,PageLoadMetrics:rn,Renderer:Pe,Samples:re,Screenshots:mn,UserInteractions:Pn,UserTimings:Wn,Warnings:Gn,Workers:Qn});var Jn=Object.freeze({__proto__:null,handlerDataHasAllHandlers:function(e){let t=!1;for(const n of Object.keys(Xn))if(n in e==!1){t=!0;break}return!t}});export{Xn as ModelHandlers,De as Threads,Jn as Types};
