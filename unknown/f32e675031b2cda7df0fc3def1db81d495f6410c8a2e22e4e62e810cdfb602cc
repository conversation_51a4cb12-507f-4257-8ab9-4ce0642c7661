{"version": 3, "names": ["ANDROID_VERSION_PIE", "ANDROID_VERSION_10", "SlideFromRightIOS", "gestureDirection", "transitionSpec", "open", "TransitionIOSSpec", "close", "cardStyleInterpolator", "forHorizontalIOS", "headerStyleInterpolator", "forFade", "ModalSlideFromBottomIOS", "forVerticalIOS", "ModalPresentationIOS", "forModalPresentationIOS", "FadeFromBottomAndroid", "FadeInFromBottomAndroidSpec", "FadeOutToBottomAndroidSpec", "forFadeFromBottomAndroid", "RevealFromBottomAndroid", "RevealFromBottomAndroidSpec", "forRevealFromBottomAndroid", "ScaleFromCenterAndroid", "ScaleFromCenterAndroidSpec", "forScaleFromCenterAndroid", "BottomSheetAndroid", "BottomSheetSlideInSpec", "BottomSheetSlideOutSpec", "forBottomSheetAndroid", "ModalFadeTransition", "forFadeCard", "DefaultTransition", "Platform", "select", "ios", "android", "Version", "default", "ModalTransition"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/TransitionPresets.tsx"], "mappings": ";;;;;;AAAA;AAGA;AAUA;AACA;AAUA,MAAMA,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,kBAAkB,GAAG,EAAE;;AAE7B;AACA;AACA;AACO,MAAMC,iBAAmC,GAAG;EACjDC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEC,kCAAiB;IACvBC,KAAK,EAAED;EACT,CAAC;EACDE,qBAAqB,EAAEC,wCAAgB;EACvCC,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMC,uBAAyC,GAAG;EACvDT,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEC,kCAAiB;IACvBC,KAAK,EAAED;EACT,CAAC;EACDE,qBAAqB,EAAEK,sCAAc;EACrCH,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMG,oBAAsC,GAAG;EACpDX,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEC,kCAAiB;IACvBC,KAAK,EAAED;EACT,CAAC;EACDE,qBAAqB,EAAEO,+CAAuB;EAC9CL,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMK,qBAAuC,GAAG;EACrDb,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEY,4CAA2B;IACjCV,KAAK,EAAEW;EACT,CAAC;EACDV,qBAAqB,EAAEW,gDAAwB;EAC/CT,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMS,uBAAyC,GAAG;EACvDjB,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEgB,4CAA2B;IACjCd,KAAK,EAAEc;EACT,CAAC;EACDb,qBAAqB,EAAEc,kDAA0B;EACjDZ,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMY,sBAAwC,GAAG;EACtDpB,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEmB,2CAA0B;IAChCjB,KAAK,EAAEiB;EACT,CAAC;EACDhB,qBAAqB,EAAEiB,iDAAyB;EAChDf,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMe,kBAAoC,GAAG;EAClDvB,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEsB,uCAAsB;IAC5BpB,KAAK,EAAEqB;EACT,CAAC;EACDpB,qBAAqB,EAAEqB,6CAAqB;EAC5CnB,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMmB,mBAAqC,GAAG;EACnD3B,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEsB,uCAAsB;IAC5BpB,KAAK,EAAEqB;EACT,CAAC;EACDpB,qBAAqB,EAAEuB,yCAAW;EAClCrB,uBAAuB,EAAEC;AAC3B,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMqB,iBAAiB,GAAGC,qBAAQ,CAACC,MAAM,CAAC;EAC/CC,GAAG,EAAEjC,iBAAiB;EACtBkC,OAAO,EACLH,qBAAQ,CAACI,OAAO,IAAIpC,kBAAkB,GAClCsB,sBAAsB,GACtBU,qBAAQ,CAACI,OAAO,IAAIrC,mBAAmB,GACvCoB,uBAAuB,GACvBJ,qBAAqB;EAC3BsB,OAAO,EAAEf;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AAFA;AAGO,MAAMgB,eAAe,GAAGN,qBAAQ,CAACC,MAAM,CAAC;EAC7CC,GAAG,EAAErB,oBAAoB;EACzBwB,OAAO,EAAEZ;AACX,CAAC,CAAC;AAAC"}