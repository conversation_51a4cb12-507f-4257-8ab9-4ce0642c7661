<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Connexion Serveur</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test de Connexion Serveur Facturation</h1>
        
        <div class="status info">
            <strong>Configuration:</strong><br>
            Serveur: http://localhost:3001<br>
            Base de données: Facutration (PostgreSQL)
        </div>

        <div>
            <button onclick="testServerConnection()">🔗 Tester Connexion Serveur</button>
            <button onclick="testClientsAPI()">👥 Tester API Clients</button>
            <button onclick="testSecteurs()">🏢 Tester API Secteurs</button>
            <button onclick="testDashboardStats()">📊 Tester Statistiques</button>
        </div>

        <div id="results"></div>
        <div id="data"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001';
        
        function showResult(type, message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showData(data) {
            const dataDiv = document.getElementById('data');
            dataDiv.innerHTML = `<h3>Données reçues:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        async function testServerConnection() {
            showResult('info', '🔄 Test de connexion au serveur...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('success', `✅ Serveur connecté ! Base: ${data.database}, Tables: ${data.tables_detected}`);
                    showData(data);
                } else {
                    showResult('error', `❌ Erreur serveur: ${response.status}`);
                }
            } catch (error) {
                showResult('error', `❌ Erreur de connexion: ${error.message}`);
            }
        }

        async function testClientsAPI() {
            showResult('info', '🔄 Test de l\'API clients...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('success', `✅ API Clients OK ! ${data.count} clients trouvés`);
                    showData(data);
                } else {
                    showResult('error', `❌ Erreur API clients: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                showResult('error', `❌ Erreur API clients: ${error.message}`);
            }
        }

        async function testSecteurs() {
            showResult('info', '🔄 Test de l\'API secteurs...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/secteurs`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('success', `✅ API Secteurs OK ! ${data.count} secteurs trouvés`);
                    showData(data);
                } else {
                    showResult('error', `❌ Erreur API secteurs: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                showResult('error', `❌ Erreur API secteurs: ${error.message}`);
            }
        }

        async function testDashboardStats() {
            showResult('info', '🔄 Test des statistiques dashboard...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/dashboard/stats`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('success', `✅ Statistiques OK ! Clients: ${data.data.clients}, Interventions: ${data.data.interventions}`);
                    showData(data);
                } else {
                    showResult('error', `❌ Erreur statistiques: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                showResult('error', `❌ Erreur statistiques: ${error.message}`);
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            testServerConnection();
        };
    </script>
</body>
</html>
