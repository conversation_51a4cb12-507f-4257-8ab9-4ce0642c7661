{"version": 3, "names": ["useEffect", "useRef", "makeMutable", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "KeyboardState", "useAnimatedKeyboard", "options", "isStatusBarTranslucentAndroid", "isNavigationBarTranslucentAndroid", "ref", "listenerId", "isSubscribed", "current", "keyboardEventData", "state", "UNKNOWN", "height", "value"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedKeyboard.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SACEC,WAAW,EACXC,0BAA0B,EAC1BC,6BAA6B,QACxB,YAAS;AAKhB,SAASC,aAAa,QAAQ,mBAAgB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CACjCC,OAAgC,GAAG;EACjCC,6BAA6B,EAAE,KAAK;EACpCC,iCAAiC,EAAE;AACrC,CAAC,EACqB;EACtB,MAAMC,GAAG,GAAGT,MAAM,CAA8B,IAAI,CAAC;EACrD,MAAMU,UAAU,GAAGV,MAAM,CAAS,CAAC,CAAC,CAAC;EACrC,MAAMW,YAAY,GAAGX,MAAM,CAAU,KAAK,CAAC;EAE3C,IAAIS,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;IACxB,MAAMC,iBAAuC,GAAG;MAC9CC,KAAK,EAAEb,WAAW,CAAgBG,aAAa,CAACW,OAAO,CAAC;MACxDC,MAAM,EAAEf,WAAW,CAAC,CAAC;IACvB,CAAC;IACDS,UAAU,CAACE,OAAO,GAAGV,0BAA0B,CAAC,CAACY,KAAK,EAAEE,MAAM,KAAK;MACjE,SAAS;;MACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;MACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;IACzC,CAAC,EAAEV,OAAO,CAAC;IACXG,GAAG,CAACG,OAAO,GAAGC,iBAAiB;IAC/BF,YAAY,CAACC,OAAO,GAAG,IAAI;EAC7B;EACAb,SAAS,CAAC,MAAM;IACd,IAAIY,YAAY,CAACC,OAAO,KAAK,KAAK,IAAIH,GAAG,CAACG,OAAO,KAAK,IAAI,EAAE;MAC1D,MAAMC,iBAAiB,GAAGJ,GAAG,CAACG,OAAO;MACrC;MACAF,UAAU,CAACE,OAAO,GAAGV,0BAA0B,CAAC,CAACY,KAAK,EAAEE,MAAM,KAAK;QACjE,SAAS;;QACTH,iBAAiB,CAACC,KAAK,CAACG,KAAK,GAAGH,KAAK;QACrCD,iBAAiB,CAACG,MAAM,CAACC,KAAK,GAAGD,MAAM;MACzC,CAAC,EAAEV,OAAO,CAAC;MACXK,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IACA,OAAO,MAAM;MACXT,6BAA6B,CAACO,UAAU,CAACE,OAAO,CAAC;MACjDD,YAAY,CAACC,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,GAAG,CAACG,OAAO;AACpB", "ignoreList": []}