{"version": 3, "file": "commonTypes.d.ts", "sourceRoot": "", "sources": ["../../src/commonTypes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,SAAS,EACT,SAAS,EACT,eAAe,EACf,UAAU,EACX,MAAM,cAAc,CAAC;AAEtB,MAAM,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1E,MAAM,WAAW,UAAW,SAAQ,SAAS,EAAE,SAAS;IACtD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB;AAED;;;;;;;;;GASG;AACH,MAAM,WAAW,WAAW,CAAC,KAAK,GAAG,OAAO;IAC1C,KAAK,EAAE,KAAK,CAAC;IACb,GAAG,IAAI,KAAK,CAAC;IACb,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;IACpD,WAAW,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC;IAC5E,cAAc,EAAE,CAAC,UAAU,EAAE,MAAM,KAAK,IAAI,CAAC;IAC7C,MAAM,EAAE,CACN,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,EAC3C,WAAW,CAAC,EAAE,OAAO,KAClB,IAAI,CAAC;CACX;AAED;;;;;GAKG;AACH,KAAK,gCAAgC,CAAC,KAAK,GAAG,OAAO,IAAI,IAAI,CAC3D,WAAW,CAAC,KAAK,CAAC,EAClB,KAAK,CACN,CAAC;AAEF,MAAM,WAAW,OAAO,CAAC,KAAK,GAAG,OAAO,CAAE,SAAQ,WAAW,CAAC,KAAK,CAAC;IAClE,wBAAwB,EAAE,IAAI,CAAC;IAC/B,UAAU,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;IAC3C;;;;;;OAMG;IACH,MAAM,EAAE,KAAK,CAAC;CACf;AASD,MAAM,MAAM,YAAY,CAAC,CAAC,GAAG,OAAO,IAAI;IACtC,0BAA0B,EAAE,CAAC,CAAC;CAC/B,CAAC;AAIF,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAC5B,CAAC,SAAS,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAEtE,MAAM,MAAM,eAAe,GAAG,OAAO,EAAE,CAAC;AAExC,MAAM,MAAM,aAAa,GAAG,WAAW,EAAE,CAAC;AAE1C,MAAM,MAAM,cAAc,GAAG;IAC3B,KAAK,EAAE,CACL,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,MAAM,IAAI,EACnB,MAAM,EAAE,eAAe,EACvB,OAAO,CAAC,EAAE,aAAa,KACpB,IAAI,CAAC;IACV,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,MAAM;IAClB,YAAY,EAAE,MAAM;CACrB,CAAC;AAEF,KAAK,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAE9C,UAAU,qBAAqB;IAC7B,IAAI,EAAE,MAAM,CAAC;CACd;AAED,KAAK,sBAAsB,GAAG,qBAAqB,CAAC;AAEpD,UAAU,kBAAmB,SAAQ,qBAAqB;IACxD,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,UAAU,iBAAiB;IACzB,SAAS,EAAE,cAAc,CAAC;IAC1B,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,UAAU,kBAAmB,SAAQ,iBAAiB;IACpD,UAAU,EAAE,sBAAsB,CAAC;CACpC;AAED,UAAU,cAAe,SAAQ,iBAAiB;IAChD,UAAU,EAAE,kBAAkB,CAAC;IAC/B,iDAAiD;IACjD,cAAc,CAAC,EAAE,mBAAmB,CAAC;CACtC;AAED,MAAM,MAAM,eAAe,CACzB,IAAI,SAAS,OAAO,EAAE,GAAG,OAAO,EAAE,EAClC,WAAW,GAAG,OAAO,IACnB,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,WAAW,CAAC,GAAG,CAAC,kBAAkB,GAAG,cAAc,CAAC,CAAC;AAE7E;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAgB,iBAAiB,CAC/B,IAAI,SAAS,OAAO,EAAE,GAAG,OAAO,EAAE,EAClC,WAAW,GAAG,OAAO,EACrB,SAAS,SAAS,cAAc,GAAG,kBAAkB,GAAG,cAAc,EACtE,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,SAAS,CAQzE;AAED,MAAM,MAAM,4BAA4B,GAAG,CACzC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,KAC3B,IAAI,CAAC;AAEV,MAAM,MAAM,2BAA2B,GAAG,eAAe,CACvD;IAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;CAAC,EAChC,IAAI,CACL,CAAC;AAEF,MAAM,WAAW,YAAY,CAAC,CAAC;IAC7B,CAAC,GAAG,EAAE,MAAM,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;CACtC;AAED,MAAM,MAAM,kBAAkB,CAAC,CAAC,IAC5B,CAAC,GACD,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAC5B,YAAY,CAAC,CAAC,CAAC,CAAC;AAEpB,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAElD,MAAM,MAAM,qBAAqB,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,CAAA;CAAE,CAAC;AAElE,MAAM,MAAM,eAAe,GAAG,UAAU,GAAG,qBAAqB,CAAC;AAEjE,MAAM,WAAW,eAAe,CAAC,CAAC,GAAG,eAAe;IAClD,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;IACnB,QAAQ,CAAC,EAAE,iBAAiB,CAAC;IAC7B,OAAO,CAAC,EAAE,CAAC,CAAC;IACZ,OAAO,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACxC,UAAU,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAC3C,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,KAAK,OAAO,CAAC;IAC3D,OAAO,EAAE,CACP,aAAa,EAAE,GAAG,EAClB,OAAO,EAAE,GAAG,EACZ,SAAS,EAAE,SAAS,EACpB,iBAAiB,EAAE,GAAG,KACnB,IAAI,CAAC;CACX;AAED,MAAM,WAAW,SAAS,CAAC,CAAC,SAAS,eAAe,CAAE,SAAQ,eAAe;IAC3E,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,KAAK,OAAO,CAAC;IACzD,OAAO,EAAE,CACP,aAAa,EAAE,CAAC,EAChB,OAAO,EAAE,eAAe,EACxB,SAAS,EAAE,SAAS,EACpB,iBAAiB,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KACzC,IAAI,CAAC;CACX;AAED,oBAAY,UAAU;IACpB,aAAa,IAAI;IACjB,SAAS,IAAI;IACb,OAAO,IAAI;IACX,cAAc,IAAI;IAClB,QAAQ,IAAI;CACb;AACD,oBAAY,iBAAiB;IAC3B,mBAAmB,IAAA;IACnB,4BAA4B,IAAA;IAC5B,uBAAuB,IAAA;IACvB,mBAAmB,IAAA;IACnB,IAAI,IAAA;CACL;AAED,MAAM,MAAM,YAAY,GAAG;IACzB,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC;IAC1B,4BAA4B,EAAE,OAAO,CAAC;IACtC,iBAAiB,EAAE,iBAAiB,CAAC;CACtC,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,OAAO,GAAG,aAAa,IAAI;IAC9D,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IACvB,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB,WAAW,EAAE,OAAO,CAAC;IACrB,MAAM,EAAE,YAAY,CAAC;CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,GAAG,CAC9B,QAAQ,CAAC,EAAE,OAAO,EAClB,OAAO,CAAC,EAAE,eAAe,KACtB,IAAI,CAAC;AAEV,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC;AAE/B,MAAM,MAAM,OAAO,GAAG;IACpB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,oBAAoB,EAAE,oBAAoB,CAAC;CAC5C,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;IACX,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,oBAAoB,EAAE,oBAAoB,CAAC;CAC5C,CAAC;AAEF,oBAAY,oBAAoB;IAC9B,UAAU,IAAI;IACd,WAAW,KAAK;IAChB,YAAY,MAAM;IAClB,YAAY,MAAM;CACnB;AAED,MAAM,MAAM,iBAAiB,GAAG;IAC9B,6BAA6B,EAAE,KAAK,CAAC;CACtC,CAAC;AAEF,oBAAY,aAAa;IACvB,OAAO,IAAI;IACX,OAAO,IAAI;IACX,IAAI,IAAI;IACR,OAAO,IAAI;IACX,MAAM,IAAI;CACX;AAED,MAAM,MAAM,oBAAoB,GAAG;IACjC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;IAC5B,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC;CACnC,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,WAAW,kBAAkB;IACjC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,uBAAuB;IACtC,6BAA6B,CAAC,EAAE,OAAO,CAAC;IACxC,iCAAiC,CAAC,EAAE,OAAO,CAAC;CAC7C;AAED;;;;;;GAMG;AACH,oBAAY,YAAY;IACtB,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,KAAK,UAAU;CAChB;AAED,MAAM,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;AAEnD,MAAM,MAAM,kBAAkB,GAAG,OAAO,CACtC,eAAe,CAAC,WAAW,CAAC,EAC5B,KAAK,CAAC,OAAO,CAAC,CACf,CAAC,MAAM,CAAC,CAAC;AAEV,KAAK,gBAAgB,CAAC,KAAK,IACvB,KAAK,GACL,CAAC,KAAK,SAAS,eAAe,GAC1B,gCAAgC,CAAC,KAAK,CAAC,GACvC,KAAK,CAAC,CAAC;AAEf,KAAK,yBAAyB,CAAC,KAAK,IAAI,KAAK,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,GAE5D,gCAAgC,CAAC,IAAI,EAAE,CAAC,GACxC,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAC9C,KAAK,SAAS,MAAM,GAEd,gCAAgC,CAAC,KAAK,CAAC,GACvC;KACG,GAAG,IAAI,MAAM,KAAK,GACf,yBAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GACrC,KAAK,CAAC,GAAG,CAAC;CACf,GACL,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAE9B,KAAK,YAAY,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;AAIvD,MAAM,MAAM,aAAa,CAAC,KAAK,GAAG,YAAY,IAC1C,KAAK,GACL,yBAAyB,CAAC,KAAK,CAAC,CAAC;AAErC,MAAM,MAAM,iBAAiB,GAAG,yBAAyB,CACvD,eAAe,CAAC,WAAW,CAAC,CAC7B,CAAC;AAEF,iEAAiE;AACjE,MAAM,MAAM,YAAY,CAAC,KAAK,GAAG,YAAY,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AAEtE,mDAAmD;AACnD,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI,OAAO,SAAS,MAAM,CAAC,GACpD,yBAAyB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GACrC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC"}