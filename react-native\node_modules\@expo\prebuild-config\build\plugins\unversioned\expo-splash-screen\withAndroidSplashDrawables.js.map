{"version": 3, "file": "withAndroidSplashDrawables.js", "names": ["_configPlugins", "data", "require", "withAndroidSplashDrawables", "config", "splash", "withDangerousMod", "setSplashDrawableAsync", "modRequest", "projectRoot", "exports", "image", "filePath", "AndroidConfig", "Paths", "getResourceXMLPathAsync", "name", "kind", "xmlContent", "$", "item", "bitmap", "filter", "Boolean", "XML", "writeXMLAsync", "path", "xml"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashDrawables.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, withDangerousMod, XML } from '@expo/config-plugins';\n\nimport { SplashScreenConfig } from './getAndroidSplashConfig';\n\nexport const withAndroidSplashDrawables: ConfigPlugin<Pick<SplashScreenConfig, 'resizeMode'>> = (\n  config,\n  splash\n) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      if (splash) {\n        await setSplashDrawableAsync(splash, config.modRequest.projectRoot);\n      }\n      return config;\n    },\n  ]);\n};\n\nexport async function setSplashDrawableAsync({ image }: SplashScreenConfig, projectRoot: string) {\n  const filePath = (await AndroidConfig.Paths.getResourceXMLPathAsync(projectRoot, {\n    name: 'ic_launcher_background',\n    kind: 'drawable',\n  }))!;\n\n  // Nuke and rewrite the splashscreen.xml drawable\n  const xmlContent = {\n    'layer-list': {\n      $: {\n        'xmlns:android': 'http://schemas.android.com/apk/res/android',\n      },\n      item: [\n        {\n          $: {\n            // TODO: Ensure these keys don't get out of sync\n            'android:drawable': '@color/splashscreen_background',\n          },\n        },\n        image && {\n          bitmap: [\n            {\n              $: {\n                'android:gravity': 'center',\n                // TODO: Ensure these keys don't get out of sync\n                'android:src': '@drawable/splashscreen_logo',\n              },\n            },\n          ],\n        },\n      ].filter(Boolean),\n    },\n  };\n  await XML.writeXMLAsync({ path: filePath, xml: xmlContent });\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIO,MAAME,0BAAgF,GAAGA,CAC9FC,MAAM,EACNC,MAAM,KACH;EACH,OAAO,IAAAC,iCAAgB,EAACF,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,IAAIC,MAAM,EAAE;MACV,MAAME,sBAAsB,CAACF,MAAM,EAAED,MAAM,CAACI,UAAU,CAACC,WAAW,CAAC;IACrE;IACA,OAAOL,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACM,OAAA,CAAAP,0BAAA,GAAAA,0BAAA;AAEK,eAAeI,sBAAsBA,CAAC;EAAEI;AAA0B,CAAC,EAAEF,WAAmB,EAAE;EAC/F,MAAMG,QAAQ,GAAI,MAAMC,8BAAa,CAACC,KAAK,CAACC,uBAAuB,CAACN,WAAW,EAAE;IAC/EO,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE;EACR,CAAC,CAAG;;EAEJ;EACA,MAAMC,UAAU,GAAG;IACjB,YAAY,EAAE;MACZC,CAAC,EAAE;QACD,eAAe,EAAE;MACnB,CAAC;MACDC,IAAI,EAAE,CACJ;QACED,CAAC,EAAE;UACD;UACA,kBAAkB,EAAE;QACtB;MACF,CAAC,EACDR,KAAK,IAAI;QACPU,MAAM,EAAE,CACN;UACEF,CAAC,EAAE;YACD,iBAAiB,EAAE,QAAQ;YAC3B;YACA,aAAa,EAAE;UACjB;QACF,CAAC;MAEL,CAAC,CACF,CAACG,MAAM,CAACC,OAAO;IAClB;EACF,CAAC;EACD,MAAMC,oBAAG,CAACC,aAAa,CAAC;IAAEC,IAAI,EAAEd,QAAQ;IAAEe,GAAG,EAAET;EAAW,CAAC,CAAC;AAC9D", "ignoreList": []}