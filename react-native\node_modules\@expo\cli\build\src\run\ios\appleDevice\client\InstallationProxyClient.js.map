{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/InstallationProxyClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport { LockdownProtocolClient } from '../protocol/LockdownProtocol';\nimport type { LockdownCommand, LockdownResponse } from '../protocol/LockdownProtocol';\n\nconst debug = Debug('expo:apple-device:client:installation_proxy');\n\nexport type OnInstallProgressCallback = (props: {\n  status: string;\n  isComplete: boolean;\n  // copiedFiles: number;\n  progress: number;\n}) => void;\n\ninterface IPOptions {\n  ApplicationsType?: 'Any';\n  PackageType?: 'Developer';\n  CFBundleIdentifier?: string;\n\n  ReturnAttributes?: (\n    | 'CFBundleIdentifier'\n    | 'ApplicationDSID'\n    | 'ApplicationType'\n    | 'CFBundleExecutable'\n    | 'CFBundleDisplayName'\n    | 'CFBundleIconFile'\n    | 'CFBundleName'\n    | 'CFBundleShortVersionString'\n    | 'CFBundleSupportedPlatforms'\n    | 'CFBundleURLTypes'\n    | 'CodeInfoIdentifier'\n    | 'Container'\n    | 'Entitlements'\n    | 'HasSettingsBundle'\n    | 'IsUpgradeable'\n    | 'MinimumOSVersion'\n    | 'Path'\n    | 'SignerIdentity'\n    | 'UIDeviceFamily'\n    | 'UIFileSharingEnabled'\n    | 'UIStatusBarHidden'\n    | 'UISupportedInterfaceOrientations'\n  )[];\n  BundleIDs?: string[];\n  [key: string]: undefined | string | string[];\n}\n\ninterface IPInstallPercentCompleteResponseItem extends LockdownResponse {\n  PercentComplete: number;\n}\n\ninterface IPInstallCFBundleIdentifierResponseItem {\n  CFBundleIdentifier: string;\n}\n\ninterface IPInstallCompleteResponseItem extends LockdownResponse {\n  Status: 'Complete';\n}\n/*\n *  [{ \"PercentComplete\": 5, \"Status\": \"CreatingStagingDirectory\" }]\n *  ...\n *  [{ \"PercentComplete\": 90, \"Status\": \"GeneratingApplicationMap\" }]\n *  [{ \"CFBundleIdentifier\": \"my.company.app\" }]\n *  [{ \"Status\": \"Complete\" }]\n */\ntype IPInstallPercentCompleteResponse = IPInstallPercentCompleteResponseItem[];\ntype IPInstallCFBundleIdentifierResponse = IPInstallCFBundleIdentifierResponseItem[];\ntype IPInstallCompleteResponse = IPInstallCompleteResponseItem[];\n\ninterface IPMessage extends LockdownCommand {\n  Command: string;\n  ClientOptions: IPOptions;\n}\n\ninterface IPLookupResponseItem extends LockdownResponse {\n  LookupResult: IPLookupResult;\n}\n/*\n * [{\n *    LookupResult: IPLookupResult,\n *    Status: \"Complete\"\n *  }]\n */\ntype IPLookupResponse = IPLookupResponseItem[];\n\nexport interface IPLookupResult {\n  // BundleId\n  [key: string]: {\n    Container: string;\n    CFBundleIdentifier: string;\n    CFBundleExecutable: string;\n    Path: string;\n  };\n}\n\nfunction isIPLookupResponse(resp: any): resp is IPLookupResponse {\n  return resp.length && resp[0].LookupResult !== undefined;\n}\n\nfunction isIPInstallPercentCompleteResponse(resp: any): resp is IPInstallPercentCompleteResponse {\n  return resp.length && resp[0].PercentComplete !== undefined;\n}\n\nfunction isIPInstallCFBundleIdentifierResponse(\n  resp: any\n): resp is IPInstallCFBundleIdentifierResponse {\n  return resp.length && resp[0].CFBundleIdentifier !== undefined;\n}\n\nfunction isIPInstallCompleteResponse(resp: any): resp is IPInstallCompleteResponse {\n  return resp.length && resp[0].Status === 'Complete';\n}\n\nexport class InstallationProxyClient extends ServiceClient<LockdownProtocolClient<IPMessage>> {\n  constructor(public socket: Socket) {\n    super(socket, new LockdownProtocolClient(socket));\n  }\n\n  async lookupApp(\n    bundleIds: string[],\n    options: IPOptions = {\n      ReturnAttributes: ['Path', 'Container', 'CFBundleExecutable', 'CFBundleIdentifier'],\n      ApplicationsType: 'Any',\n    }\n  ) {\n    debug(`lookupApp, options: ${JSON.stringify(options)}`);\n\n    let resp = await this.protocolClient.sendMessage({\n      Command: 'Lookup',\n      ClientOptions: {\n        BundleIDs: bundleIds,\n        ...options,\n      },\n    });\n    if (resp && !Array.isArray(resp)) resp = [resp];\n    if (isIPLookupResponse(resp)) {\n      return resp[0].LookupResult;\n    } else {\n      throw new ResponseError(`There was an error looking up app`, resp);\n    }\n  }\n\n  async installApp(\n    packagePath: string,\n    bundleId: string,\n    options: IPOptions = {\n      ApplicationsType: 'Any',\n      PackageType: 'Developer',\n    },\n    onProgress: OnInstallProgressCallback\n  ) {\n    debug(`installApp, packagePath: ${packagePath}, bundleId: ${bundleId}`);\n\n    return this.protocolClient.sendMessage(\n      {\n        Command: 'Install',\n        PackagePath: packagePath,\n        ClientOptions: {\n          CFBundleIdentifier: bundleId,\n          ...options,\n        },\n      },\n      (resp, resolve, reject) => {\n        if (resp && !Array.isArray(resp)) resp = [resp];\n\n        if (isIPInstallCompleteResponse(resp)) {\n          onProgress({\n            isComplete: true,\n            progress: 100,\n            status: resp[0].Status,\n          });\n          resolve();\n        } else if (isIPInstallPercentCompleteResponse(resp)) {\n          onProgress({\n            isComplete: false,\n            progress: resp[0].PercentComplete,\n            status: resp[0].Status,\n          });\n          debug(`Installation status: ${resp[0].Status}, %${resp[0].PercentComplete}`);\n        } else if (isIPInstallCFBundleIdentifierResponse(resp)) {\n          debug(`Installed app: ${resp[0].CFBundleIdentifier}`);\n        } else {\n          reject(\n            new ResponseError(\n              'There was an error installing app: ' + require('util').inspect(resp),\n              resp\n            )\n          );\n        }\n      }\n    );\n  }\n}\n"], "names": ["InstallationProxyClient", "debug", "Debug", "isIPLookupResponse", "resp", "length", "LookupResult", "undefined", "isIPInstallPercentCompleteResponse", "PercentComplete", "isIPInstallCFBundleIdentifierResponse", "CFBundleIdentifier", "isIPInstallCompleteResponse", "Status", "ServiceClient", "constructor", "socket", "LockdownProtocolClient", "lookupApp", "bundleIds", "options", "ReturnAttributes", "ApplicationsType", "JSON", "stringify", "protocolClient", "sendMessage", "Command", "ClientOptions", "BundleIDs", "Array", "isArray", "ResponseError", "installApp", "packagePath", "bundleId", "PackageType", "onProgress", "PackagePath", "resolve", "reject", "isComplete", "progress", "status", "require", "inspect"], "mappings": "AAAA;;;;;;CAMC;;;;+BAoHYA;;;eAAAA;;;;gEAnHK;;;;;;+BAG2B;kCACN;;;;;;AAGvC,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AA0FpB,SAASC,mBAAmBC,IAAS;IACnC,OAAOA,KAAKC,MAAM,IAAID,IAAI,CAAC,EAAE,CAACE,YAAY,KAAKC;AACjD;AAEA,SAASC,mCAAmCJ,IAAS;IACnD,OAAOA,KAAKC,MAAM,IAAID,IAAI,CAAC,EAAE,CAACK,eAAe,KAAKF;AACpD;AAEA,SAASG,sCACPN,IAAS;IAET,OAAOA,KAAKC,MAAM,IAAID,IAAI,CAAC,EAAE,CAACO,kBAAkB,KAAKJ;AACvD;AAEA,SAASK,4BAA4BR,IAAS;IAC5C,OAAOA,KAAKC,MAAM,IAAID,IAAI,CAAC,EAAE,CAACS,MAAM,KAAK;AAC3C;AAEO,MAAMb,gCAAgCc,4BAAa;IACxDC,YAAY,AAAOC,MAAc,CAAE;QACjC,KAAK,CAACA,QAAQ,IAAIC,wCAAsB,CAACD,eADxBA,SAAAA;IAEnB;IAEA,MAAME,UACJC,SAAmB,EACnBC,UAAqB;QACnBC,kBAAkB;YAAC;YAAQ;YAAa;YAAsB;SAAqB;QACnFC,kBAAkB;IACpB,CAAC,EACD;QACArB,MAAM,CAAC,oBAAoB,EAAEsB,KAAKC,SAAS,CAACJ,UAAU;QAEtD,IAAIhB,OAAO,MAAM,IAAI,CAACqB,cAAc,CAACC,WAAW,CAAC;YAC/CC,SAAS;YACTC,eAAe;gBACbC,WAAWV;gBACX,GAAGC,OAAO;YACZ;QACF;QACA,IAAIhB,QAAQ,CAAC0B,MAAMC,OAAO,CAAC3B,OAAOA,OAAO;YAACA;SAAK;QAC/C,IAAID,mBAAmBC,OAAO;YAC5B,OAAOA,IAAI,CAAC,EAAE,CAACE,YAAY;QAC7B,OAAO;YACL,MAAM,IAAI0B,4BAAa,CAAC,CAAC,iCAAiC,CAAC,EAAE5B;QAC/D;IACF;IAEA,MAAM6B,WACJC,WAAmB,EACnBC,QAAgB,EAChBf,UAAqB;QACnBE,kBAAkB;QAClBc,aAAa;IACf,CAAC,EACDC,UAAqC,EACrC;QACApC,MAAM,CAAC,yBAAyB,EAAEiC,YAAY,YAAY,EAAEC,UAAU;QAEtE,OAAO,IAAI,CAACV,cAAc,CAACC,WAAW,CACpC;YACEC,SAAS;YACTW,aAAaJ;YACbN,eAAe;gBACbjB,oBAAoBwB;gBACpB,GAAGf,OAAO;YACZ;QACF,GACA,CAAChB,MAAMmC,SAASC;YACd,IAAIpC,QAAQ,CAAC0B,MAAMC,OAAO,CAAC3B,OAAOA,OAAO;gBAACA;aAAK;YAE/C,IAAIQ,4BAA4BR,OAAO;gBACrCiC,WAAW;oBACTI,YAAY;oBACZC,UAAU;oBACVC,QAAQvC,IAAI,CAAC,EAAE,CAACS,MAAM;gBACxB;gBACA0B;YACF,OAAO,IAAI/B,mCAAmCJ,OAAO;gBACnDiC,WAAW;oBACTI,YAAY;oBACZC,UAAUtC,IAAI,CAAC,EAAE,CAACK,eAAe;oBACjCkC,QAAQvC,IAAI,CAAC,EAAE,CAACS,MAAM;gBACxB;gBACAZ,MAAM,CAAC,qBAAqB,EAAEG,IAAI,CAAC,EAAE,CAACS,MAAM,CAAC,GAAG,EAAET,IAAI,CAAC,EAAE,CAACK,eAAe,EAAE;YAC7E,OAAO,IAAIC,sCAAsCN,OAAO;gBACtDH,MAAM,CAAC,eAAe,EAAEG,IAAI,CAAC,EAAE,CAACO,kBAAkB,EAAE;YACtD,OAAO;gBACL6B,OACE,IAAIR,4BAAa,CACf,wCAAwCY,QAAQ,QAAQC,OAAO,CAACzC,OAChEA;YAGN;QACF;IAEJ;AACF"}