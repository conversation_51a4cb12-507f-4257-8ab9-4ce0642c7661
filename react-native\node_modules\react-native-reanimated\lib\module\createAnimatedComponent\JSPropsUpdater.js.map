{"version": 3, "names": ["NativeEventEmitter", "Platform", "shouldBeUseWeb", "runOnJS", "runOnUIImmediately", "NativeReanimatedModule", "SHOULD_BE_USE_WEB", "JSPropsUpdaterPaper", "_tagToComponentMapping", "Map", "constructor", "_reanimatedEventEmitter", "OS", "undefined", "addOnJSPropsChangeListener", "animatedComponent", "viewTag", "getComponentViewTag", "set", "size", "listener", "data", "component", "get", "_updateFromNative", "props", "addListener", "removeOnJSPropsChangeListener", "delete", "removeAllListeners", "JSPropsUpdaterFabric", "isInitialized", "updater", "global", "updateJSProps", "JSPropsUpdaterWeb", "_animatedComponent", "JSPropsUpdater", "_IS_FABRIC"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/JSPropsUpdater.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,EAAEC,QAAQ,QAAQ,cAAc;AAE3D,SAASC,cAAc,QAAQ,uBAAoB;AAEnD,SAASC,OAAO,EAAEC,kBAAkB,QAAQ,eAAY;AAOxD,OAAOC,sBAAsB,MAAM,oCAAiC;AAOpE,MAAMC,iBAAiB,GAAGJ,cAAc,CAAC,CAAC;AAE1C,MAAMK,mBAAmB,CAA4B;EACnD,OAAeC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAGjDC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,uBAAuB,GAAG,IAAIX,kBAAkB;IACnD;IACAC,QAAQ,CAACW,EAAE,KAAK,KAAK,IAAIX,QAAQ,CAACW,EAAE,KAAK,OAAO,GAC3CP,sBAAsB,GACvBQ,SACN,CAAC;EACH;EAEOC,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;IACvDV,mBAAmB,CAACC,sBAAsB,CAACU,GAAG,CAACF,OAAO,EAAED,iBAAiB,CAAC;IAC1E,IAAIR,mBAAmB,CAACC,sBAAsB,CAACW,IAAI,KAAK,CAAC,EAAE;MACzD,MAAMC,QAAQ,GAAIC,IAAkB,IAAK;QACvC,MAAMC,SAAS,GAAGf,mBAAmB,CAACC,sBAAsB,CAACe,GAAG,CAC9DF,IAAI,CAACL,OACP,CAAC;QACDM,SAAS,EAAEE,iBAAiB,CAACH,IAAI,CAACI,KAAK,CAAC;MAC1C,CAAC;MACD,IAAI,CAACd,uBAAuB,CAACe,WAAW,CACtC,yBAAyB,EACzBN,QACF,CAAC;IACH;EACF;EAEOO,6BAA6BA,CAClCZ,iBAG4B,EAC5B;IACA,MAAMC,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;IACvDV,mBAAmB,CAACC,sBAAsB,CAACoB,MAAM,CAACZ,OAAO,CAAC;IAC1D,IAAIT,mBAAmB,CAACC,sBAAsB,CAACW,IAAI,KAAK,CAAC,EAAE;MACzD,IAAI,CAACR,uBAAuB,CAACkB,kBAAkB,CAC7C,yBACF,CAAC;IACH;EACF;AACF;AAEA,MAAMC,oBAAoB,CAA4B;EACpD,OAAetB,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjD,OAAesB,aAAa,GAAG,KAAK;EAEpCrB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACoB,oBAAoB,CAACC,aAAa,EAAE;MACvC,MAAMC,OAAO,GAAGA,CAAChB,OAAe,EAAES,KAAc,KAAK;QACnD,MAAMH,SAAS,GACbQ,oBAAoB,CAACtB,sBAAsB,CAACe,GAAG,CAACP,OAAO,CAAC;QAC1DM,SAAS,EAAEE,iBAAiB,CAACC,KAAK,CAAC;MACrC,CAAC;MACDrB,kBAAkB,CAAC,MAAM;QACvB,SAAS;;QACT6B,MAAM,CAACC,aAAa,GAAG,CAAClB,OAAe,EAAES,KAAc,KAAK;UAC1DtB,OAAO,CAAC6B,OAAO,CAAC,CAAChB,OAAO,EAAES,KAAK,CAAC;QAClC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC;MACJK,oBAAoB,CAACC,aAAa,GAAG,IAAI;IAC3C;EACF;EAEOjB,0BAA0BA,CAC/BC,iBAG4B,EAC5B;IACA,IAAI,CAACe,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMf,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;IACvDa,oBAAoB,CAACtB,sBAAsB,CAACU,GAAG,CAACF,OAAO,EAAED,iBAAiB,CAAC;EAC7E;EAEOY,6BAA6BA,CAClCZ,iBAG4B,EAC5B;IACA,IAAI,CAACe,oBAAoB,CAACC,aAAa,EAAE;MACvC;IACF;IACA,MAAMf,OAAO,GAAGD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC;IACvDa,oBAAoB,CAACtB,sBAAsB,CAACoB,MAAM,CAACZ,OAAO,CAAC;EAC7D;AACF;AAEA,MAAMmB,iBAAiB,CAA4B;EAC1CrB,0BAA0BA,CAC/BsB,kBAG4B,EAC5B;IACA;EAAA;EAGKT,6BAA6BA,CAClCS,kBAG4B,EAC5B;IACA;EAAA;AAEJ;AAOA,IAAIC,cAAqC;AACzC,IAAI/B,iBAAiB,EAAE;EACrB+B,cAAc,GAAGF,iBAAiB;AACpC,CAAC,MAAM,IAAIF,MAAM,CAACK,UAAU,EAAE;EAC5BD,cAAc,GAAGP,oBAAoB;AACvC,CAAC,MAAM;EACLO,cAAc,GAAG9B,mBAAmB;AACtC;AAEA,eAAe8B,cAAc", "ignoreList": []}