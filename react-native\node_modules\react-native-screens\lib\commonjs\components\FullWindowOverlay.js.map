{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_FullWindowOverlayNativeComponent", "obj", "__esModule", "default", "NativeFullWindowOverlay", "FullWindowOverlayNativeComponent", "FullWindowOverlay", "props", "width", "height", "useWindowDimensions", "Platform", "OS", "console", "warn", "createElement", "View", "style", "StyleSheet", "absoluteFill", "children", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/FullWindowOverlay.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,iCAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA0F,SAAAD,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAD1F;;AAEA,MAAMG,uBAIL,GAAGC,yCAAuC;AAE3C,SAASC,iBAAiBA,CAACC,KAA8B,EAAE;EACzD,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAC/C,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzBC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACrE,oBAAOlB,MAAA,CAAAO,OAAA,CAAAY,aAAA,CAAChB,YAAA,CAAAiB,IAAI,EAAKT,KAAQ,CAAC;EAC5B;EACA,oBACEX,MAAA,CAAAO,OAAA,CAAAY,aAAA,CAACX,uBAAuB;IACtBa,KAAK,EAAE,CAACC,uBAAU,CAACC,YAAY,EAAE;MAAEX,KAAK;MAAEC;IAAO,CAAC;EAAE,GACnDF,KAAK,CAACa,QACgB,CAAC;AAE9B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAEcG,iBAAiB"}