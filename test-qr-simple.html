<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QR Code Simple - AquaTrack</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            max-width: 800px;
            width: 100%;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            margin: 10px 0;
            opacity: 0.9;
        }

        .qr-test-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .qr-test-card:hover {
            transform: translateY(-5px);
        }

        .qr-test-card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .qr-code-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            display: inline-block;
        }

        .qr-code {
            margin: 0 auto;
        }

        .client-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            border-left: 5px solid #28a745;
        }

        .client-info h3 {
            color: #155724;
            margin-top: 0;
            font-size: 1.3em;
        }

        .client-info p {
            margin: 8px 0;
            color: #155724;
            font-size: 1.1em;
        }

        .instructions {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }

        .instructions h3 {
            color: #856404;
            margin-top: 0;
        }

        .instructions ol {
            color: #856404;
            font-size: 1.1em;
        }

        .instructions li {
            margin: 10px 0;
        }

        .success {
            color: #28a745;
            font-weight: bold;
        }

        .code-display {
            background: #f8f9fa;
            border: 2px dashed #6c757d;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            font-weight: bold;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .qr-test-card {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Test Scanner QR - AquaTrack</h1>
            <p>Scanner ce QR code avec votre application</p>
        </div>

        <div class="qr-test-card">
            <h2>✅ QR Code Test - Benali Fatima</h2>
            
            <div class="code-display">
                QR-2025-0001
            </div>

            <div class="qr-code-container">
                <div id="qr-benali" class="qr-code"></div>
            </div>

            <div class="client-info">
                <h3>👤 Informations Client</h3>
                <p><strong>Nom :</strong> Benali Fatima</p>
                <p><strong>Adresse :</strong> 45 Avenue Hassan II, près de l'école Omar Ibn Al Khattab</p>
                <p><strong>Ville :</strong> Sefrou</p>
                <p><strong>Téléphone :</strong> 0647895655</p>
                <p><strong>Email :</strong> <EMAIL></p>
                <p><strong>Secteur :</strong> Résidentiel</p>
            </div>

            <div class="client-info">
                <h3>⚙️ Informations Compteur</h3>
                <p><strong>QR Code :</strong> QR-2025-0001</p>
                <p><strong>Marque :</strong> SAGEMCOM</p>
                <p><strong>N° Série :</strong> SN-123456789</p>
                <p><strong>Date contrat :</strong> 02/07/2025</p>
                <p><strong>Position :</strong> 34.0331, -4.8369</p>
            </div>

            <div class="instructions">
                <h3>📱 Comment tester :</h3>
                <ol>
                    <li>Ouvrez votre application AquaTrack</li>
                    <li>Allez dans le tableau de bord technicien</li>
                    <li>Cliquez sur "Scanner QR"</li>
                    <li>Cliquez sur "📷 Démarrer le scan"</li>
                    <li>Pointez votre caméra vers le QR code ci-dessus</li>
                    <li>Le scanner devrait automatiquement détecter le code</li>
                    <li>Les informations de Benali Fatima s'afficheront</li>
                </ol>
            </div>
        </div>

        <div class="qr-test-card">
            <h2>✅ QR Code Test - Client1</h2>
            
            <div class="code-display">
                QR123
            </div>

            <div class="qr-code-container">
                <div id="qr-client1" class="qr-code"></div>
            </div>

            <div class="client-info">
                <h3>👤 Informations Client</h3>
                <p><strong>Nom :</strong> Client1 Client1</p>
                <p><strong>Compteur :</strong> Sagemcom COM123456</p>
                <p><strong>Statut :</strong> <span class="success">✅ Valide</span></p>
            </div>
        </div>

        <div class="qr-test-card">
            <h2>❌ QR Code Test - Échec</h2>
            
            <div class="code-display">
                999
            </div>

            <div class="qr-code-container">
                <div id="qr-fail" class="qr-code"></div>
            </div>

            <div class="client-info" style="background: #f8d7da; border-left-color: #dc3545;">
                <h3 style="color: #721c24;">❌ Test d'Échec</h3>
                <p style="color: #721c24;"><strong>Résultat attendu :</strong> "QR Code non trouvé dans la base de données"</p>
            </div>
        </div>
    </div>

    <script>
        // Générer les QR codes
        const qrCodes = [
            { id: 'qr-benali', text: 'QR-2025-0001' },
            { id: 'qr-client1', text: 'QR123' },
            { id: 'qr-fail', text: '999' }
        ];

        // Générer chaque QR code
        qrCodes.forEach(qr => {
            QRCode.toCanvas(document.getElementById(qr.id), qr.text, {
                width: 200,
                height: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('Erreur génération QR:', error);
                    document.getElementById(qr.id).innerHTML = `<p>Erreur: ${error}</p>`;
                } else {
                    console.log(`✅ QR code ${qr.id} généré avec succès`);
                }
            });
        });

        console.log('🎯 Page de test QR chargée');
        console.log('📱 Codes disponibles:', qrCodes.map(qr => qr.text));
    </script>
</body>
</html>
