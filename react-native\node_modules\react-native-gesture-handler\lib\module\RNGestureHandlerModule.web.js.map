{"version": 3, "sources": ["RNGestureHandlerModule.web.ts"], "names": ["React", "isNewWebImplementationEnabled", "Gestures", "HammerGestures", "InteractionManager", "NodeManager", "HammerNodeManager", "GestureHandlerWebDelegate", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "getInstance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "HTMLElement", "Component", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAGA,SAASC,6BAAT,QAA8C,8BAA9C;AACA,SAASC,QAAT,EAAmBC,cAAnB,QAAyC,gBAAzC;AAEA,OAAOC,kBAAP,MAA+B,gCAA/B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AACA,OAAO,KAAKC,iBAAZ,MAAmC,0BAAnC;AACA,SAASC,yBAAT,QAA0C,uCAA1C;AAEA,eAAe;AACbC,EAAAA,oBAAoB,CAACC,GAAD,EAAcC,oBAAd,EAA6C;AAC/DC,IAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb,EAAuCH,GAAvC,EAA4CC,oBAA5C;AACD,GAHY;;AAIbG,EAAAA,sBAAsB,GAAG;AACvBF,IAAAA,OAAO,CAACC,IAAR,CAAa,0BAAb;AACD,GANY;;AAObE,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAIhB,6BAA6B,EAAjC,EAAqC;AACnC,UAAI,EAAEc,WAAW,IAAIb,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIgB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAGjB,QAAQ,CAACa,WAAD,CAA7B;AACAV,MAAAA,WAAW,CAACS,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAIZ,yBAAJ,EAAjB,CAFF;AAIAH,MAAAA,kBAAkB,CAACgB,WAAnB,GAAiCC,qBAAjC,CACEhB,WAAW,CAACiB,UAAZ,CAAuBN,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIZ,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIe,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGhB,cAAc,CAACY,WAAD,CAAnC,CATK,CAUL;;AACAT,MAAAA,iBAAiB,CAACQ,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKI,oBAAL,CAA0BP,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbO,EAAAA,oBAAoB,CAClBR,UADkB,EAElB;AACAS,EAAAA,OAHkB,EAIlBC,WAJkB,EAKlBC,QALkB,EAMlB;AACA,QACE,EAAEF,OAAO,YAAYG,WAAnB,IAAkCH,OAAO,YAAYzB,KAAK,CAAC6B,SAA7D,CADF,EAEE;AACA;AACD;;AAED,QAAI5B,6BAA6B,EAAjC,EAAqC;AACnC;AACAI,MAAAA,WAAW,CAACiB,UAAZ,CAAuBN,UAAvB,EAAmCc,IAAnC,CAAwCL,OAAxC,EAAiDE,QAAjD;AACD,KAHD,MAGO;AACL;AACArB,MAAAA,iBAAiB,CAACgB,UAAlB,CAA6BN,UAA7B,EAAyCe,OAAzC,CAAiDN,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAhEY;;AAiEbJ,EAAAA,oBAAoB,CAACP,UAAD,EAAqBgB,SAArB,EAAwC;AAC1D,QAAI/B,6BAA6B,EAAjC,EAAqC;AACnCI,MAAAA,WAAW,CAACiB,UAAZ,CAAuBN,UAAvB,EAAmCiB,mBAAnC,CAAuDD,SAAvD;AAEA5B,MAAAA,kBAAkB,CAACgB,WAAnB,GAAiCC,qBAAjC,CACEhB,WAAW,CAACiB,UAAZ,CAAuBN,UAAvB,CADF,EAEEgB,SAFF;AAID,KAPD,MAOO;AACL1B,MAAAA,iBAAiB,CAACgB,UAAlB,CAA6BN,UAA7B,EAAyCiB,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GA5EY;;AA6EbE,EAAAA,qBAAqB,CAAClB,UAAD,EAAqB;AACxC,QAAIf,6BAA6B,EAAjC,EAAqC;AACnC,aAAOI,WAAW,CAACiB,UAAZ,CAAuBN,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOV,iBAAiB,CAACgB,UAAlB,CAA6BN,UAA7B,CAAP;AACD;AACF,GAnFY;;AAoFbmB,EAAAA,kBAAkB,CAACnB,UAAD,EAAqB;AACrC,QAAIf,6BAA6B,EAAjC,EAAqC;AACnCI,MAAAA,WAAW,CAAC8B,kBAAZ,CAA+BnB,UAA/B;AACD,KAFD,MAEO;AACLV,MAAAA,iBAAiB,CAAC6B,kBAAlB,CAAqCnB,UAArC;AACD;AACF,GA1FY;;AA2Fb;AACAoB,EAAAA,eAAe,GAAG,CAAE;;AA5FP,CAAf", "sourcesContent": ["import React from 'react';\n\nimport type { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\nimport { Gestures, HammerGestures } from './web/Gestures';\nimport type { Config } from './web/interfaces';\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\nexport default {\n  handleSetJSResponder(tag: number, blockNativeResponder: boolean) {\n    console.warn('handleSetJSResponder: ', tag, blockNativeResponder);\n  },\n  handleClearJSResponder() {\n    console.warn('handleClearJSResponder: ');\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    newView: any,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (\n      !(newView instanceof HTMLElement || newView instanceof React.Component)\n    ) {\n      return;\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.getInstance().configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}