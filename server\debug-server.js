const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Route de test simple
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur debug fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route clients avec données de test
app.get('/api/clients', (req, res) => {
  console.log('✅ Route /api/clients appelée');
  
  // Données de test
  const testClients = [
    {
      idclient: 1,
      nom: 'Test',
      prenom: 'Client1',
      adresse: '123 Rue Test',
      ville: 'TestVille',
      tel: '0123456789',
      email: '<EMAIL>',
      secteur_nom: 'Test Secteur'
    },
    {
      idclient: 2,
      nom: 'Test',
      prenom: 'Client2',
      adresse: '456 Avenue Test',
      ville: 'TestVille2',
      tel: '0987654321',
      email: '<EMAIL>',
      secteur_nom: 'Test Secteur 2'
    }
  ];

  console.log(`✅ Retour de ${testClients.length} clients de test`);
  
  res.json({
    success: true,
    data: testClients,
    count: testClients.length,
    message: `${testClients.length} client(s) de test trouvé(s)`
  });
});

// Route contrats de test
app.get('/api/clients/:id/contracts', (req, res) => {
  const { id } = req.params;
  console.log(`✅ Route /api/clients/${id}/contracts appelée`);
  
  // Données de test selon l'ID
  let testContracts = [];
  
  if (id === '1') {
    // Client avec un seul contrat
    testContracts = [
      {
        idcontract: 1,
        codeqr: 'QR-TEST-001',
        datecontract: '2025-01-01T00:00:00.000Z',
        idclient: 1,
        marquecompteur: 'TEST-METER',
        nom: 'Test',
        prenom: 'Client1'
      }
    ];
  } else if (id === '2') {
    // Client avec plusieurs contrats
    testContracts = [
      {
        idcontract: 2,
        codeqr: 'QR-TEST-002A',
        datecontract: '2025-01-01T00:00:00.000Z',
        idclient: 2,
        marquecompteur: 'TEST-METER-A',
        nom: 'Test',
        prenom: 'Client2'
      },
      {
        idcontract: 3,
        codeqr: 'QR-TEST-002B',
        datecontract: '2025-01-02T00:00:00.000Z',
        idclient: 2,
        marquecompteur: 'TEST-METER-B',
        nom: 'Test',
        prenom: 'Client2'
      }
    ];
  }
  // Sinon, client sans contrat (testContracts reste vide)

  console.log(`✅ Retour de ${testContracts.length} contrat(s) pour le client ${id}`);
  
  res.json({
    success: true,
    data: testContracts,
    count: testContracts.length,
    message: `${testContracts.length} contrat(s) trouvé(s) pour le client ${id}`,
    client_id: parseInt(id)
  });
});

// Route dernière consommation de test
app.get('/api/contracts/:id/last-consommation', (req, res) => {
  const { id } = req.params;
  console.log(`✅ Route /api/contracts/${id}/last-consommation appelée`);
  
  // Données de test
  const testConsommation = {
    consommationactuelle: 1250.5,
    periode: '2025-01',
    jours: 30
  };

  console.log(`✅ Retour de la dernière consommation pour le contrat ${id}`);
  
  res.json({
    success: true,
    data: testConsommation,
    message: 'Dernière consommation de test trouvée'
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Route 404
app.use('*', (req, res) => {
  console.log(`❌ Route non trouvée: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    message: 'Route non trouvée',
    url: req.originalUrl
  });
});

// Démarrage du serveur
const PORT = 3002;

app.listen(PORT, () => {
  console.log(`\n🚀 Serveur debug démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes de test disponibles:');
  console.log('  - GET  / (test de base)');
  console.log('  - GET  /api/clients (2 clients de test)');
  console.log('  - GET  /api/clients/1/contracts (1 contrat)');
  console.log('  - GET  /api/clients/2/contracts (2 contrats)');
  console.log('  - GET  /api/clients/3/contracts (0 contrat)');
  console.log('  - GET  /api/contracts/:id/last-consommation');
  console.log('\n✅ Prêt à recevoir les requêtes !');
  console.log('🔍 Tous les appels API seront loggés dans ce terminal');
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err);
});
