import React, { useEffect, useState } from 'react';

function FormulaireClient() {
  const [secteurs, setSecteurs] = useState([]);
  const [secteurError, setSecteurError] = useState(false);

  useEffect(() => {
    // Utilise l'API backend pour récupérer les secteurs de la base "Facutration"
    fetch('http://localhost:3003/api/secteurs')
      .then(res => {
        if (!res.ok) throw new Error('Erreur réseau');
        return res.json();
      })
      .then(data => {
        if (data.success && Array.isArray(data.data) && data.data.length > 0) {
          setSecteurs(data.data);
          setSecteurError(false);
        } else {
          setSecteurs([]);
          setSecteurError(true);
        }
      })
      .catch(() => {
        setSecteurs([]);
        setSecteurError(true);
      });
  }, []);

  return (
    <form>
      {/* ...autres champs du formulaire... */}
      <label>
        Secteur
        <select name="secteur" required disabled={secteurs.length === 0}>
          <option value="">
            {secteurError
              ? 'Erreur de connexion à la base de données'
              : (secteurs.length === 0 ? 'Aucun secteur disponible' : 'Sélectionner un secteur')}
          </option>
          {secteurs.map(s => (
            <option key={s.ids} value={s.ids}>{s.nom}</option>
          ))}
        </select>
      </label>
      {/* ...bouton de soumission, etc... */}
    </form>
  );
}

export default FormulaireClient;