{"version": 3, "sources": ["../../../../../src/start/server/metro/metroVirtualModules.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Bundler from 'metro/src/Bundler';\nimport DependencyGraph from 'metro/src/node-haste/DependencyGraph';\nimport { FileSystem } from 'metro-file-map';\n\ntype ExpoPatchedFileSystem = Omit<FileSystem, 'getSha1'> & {\n  getSha1: FileSystem['getSha1'] & { __patched?: boolean };\n  expoVirtualModules?: Map<string, Buffer>;\n};\n\ntype ActualDependencyGraph = DependencyGraph & {\n  _fileSystem: ExpoPatchedFileSystem;\n};\n\ntype ActualBundler = Bundler & {\n  _depGraph: ActualDependencyGraph;\n};\n\ntype ExpoPatchedBundler = Bundler & {\n  setVirtualModule: (id: string, contents: string) => void;\n  hasVirtualModule: (id: string) => boolean;\n};\n\nfunction assertBundlerHasPrivateMembers(bundler: <PERSON><PERSON><PERSON>): asserts bundler is ActualBundler {\n  if (!('_depGraph' in bundler)) {\n    throw new Error(\n      'Expected bundler to have member: _depGraph. Upstream metro may have removed this property.'\n    );\n  }\n\n  assertDepGraphHasPrivateMembers(bundler._depGraph);\n}\n\nfunction assertDepGraphHasPrivateMembers(\n  depGraph: unknown\n): asserts depGraph is ActualDependencyGraph {\n  if (!depGraph || typeof depGraph !== 'object' || !('_fileSystem' in depGraph)) {\n    throw new Error(\n      'Expected bundler._depGraph to have member: _fileSystem. Upstream metro may have removed this property.'\n    );\n  }\n}\n\nfunction ensureMetroBundlerPatchedWithSetVirtualModule(\n  bundler: Bundler & {\n    setVirtualModule?: (id: string, contents: string) => void;\n    hasVirtualModule?: (id: string) => boolean;\n  }\n): ExpoPatchedBundler {\n  if (!bundler.setVirtualModule) {\n    bundler.setVirtualModule = function (this: Bundler, id: string, contents: string) {\n      assertBundlerHasPrivateMembers(this);\n      const fs = ensureFileSystemPatched(this._depGraph._fileSystem);\n      fs.expoVirtualModules!.set(ensureStartsWithNullByte(id), Buffer.from(contents));\n    };\n    bundler.hasVirtualModule = function (this: Bundler, id: string) {\n      assertBundlerHasPrivateMembers(this);\n      const fs = ensureFileSystemPatched(this._depGraph._fileSystem);\n      return fs.expoVirtualModules!.has(ensureStartsWithNullByte(id));\n    };\n  }\n\n  return bundler as ExpoPatchedBundler;\n}\n\nfunction ensureStartsWithNullByte(id: string): string {\n  // Because you'll likely need to return the path somewhere, we should just assert with a useful error message instead of\n  // attempting to mutate the value behind the scenes. This ensures correctness in the resolution.\n  if (!id.startsWith('\\0')) {\n    throw new Error(`Virtual modules in Expo CLI must start with with null byte (\\\\0), got: ${id}`);\n  }\n  return id;\n}\n\nexport function getMetroBundlerWithVirtualModules(\n  bundler: Bundler & {\n    transformFile: Bundler['transformFile'] & { __patched?: boolean };\n  }\n): ExpoPatchedBundler {\n  if (!bundler.transformFile.__patched) {\n    const originalTransformFile = bundler.transformFile.bind(bundler);\n\n    bundler.transformFile = async function (\n      filePath: string,\n      transformOptions: any,\n      /** Optionally provide the file contents, this can be used to provide virtual contents for a file. */\n      fileBuffer?: Buffer\n    ) {\n      // file buffer will be defined for virtual modules in Metro, e.g. context modules.\n      if (!fileBuffer) {\n        if (filePath.startsWith('\\0')) {\n          const graph = await this.getDependencyGraph();\n\n          assertDepGraphHasPrivateMembers(graph);\n\n          if (graph._fileSystem.expoVirtualModules) {\n            fileBuffer = graph._fileSystem.expoVirtualModules.get(filePath);\n          }\n\n          if (!fileBuffer) {\n            throw new Error(`Virtual module \"${filePath}\" not found.`);\n          }\n        }\n      }\n      return originalTransformFile(filePath, transformOptions, fileBuffer);\n    };\n\n    bundler.transformFile.__patched = true;\n  }\n\n  return ensureMetroBundlerPatchedWithSetVirtualModule(bundler);\n}\n\nfunction ensureFileSystemPatched(fs: ExpoPatchedFileSystem): ExpoPatchedFileSystem {\n  if (!fs.getSha1.__patched) {\n    const original_getSha1 = fs.getSha1.bind(fs);\n    fs.getSha1 = (filename: string) => {\n      // Rollup virtual module format.\n      if (filename.startsWith('\\0')) {\n        return filename;\n      }\n\n      return original_getSha1(filename);\n    };\n    fs.getSha1.__patched = true;\n  }\n\n  // TODO: Connect virtual modules to a specific context so they don't cross-bundles.\n  if (!fs.expoVirtualModules) {\n    fs.expoVirtualModules = new Map<string, Buffer>();\n  }\n\n  return fs;\n}\n"], "names": ["getMetroBundlerWithVirtualModules", "assertBundlerHasPrivateMembers", "bundler", "Error", "assertDepGraphHasPrivateMembers", "_depGraph", "depGraph", "ensureMetroBundlerPatchedWithSetVirtualModule", "setVirtualModule", "id", "contents", "fs", "ensureFileSystemPatched", "_fileSystem", "expoVirtualModules", "set", "ensureStartsWithNullByte", "<PERSON><PERSON><PERSON>", "from", "hasVirtualModule", "has", "startsWith", "transformFile", "__patched", "originalTransformFile", "bind", "filePath", "transformOptions", "fileBuffer", "graph", "getDependencyGraph", "get", "getSha1", "original_getSha1", "filename", "Map"], "mappings": "AAAA;;;;;CAKC;;;;+BA0EeA;;;eAAAA;;;AAnDhB,SAASC,+BAA+BC,OAAgB;IACtD,IAAI,CAAE,CAAA,eAAeA,OAAM,GAAI;QAC7B,MAAM,IAAIC,MACR;IAEJ;IAEAC,gCAAgCF,QAAQG,SAAS;AACnD;AAEA,SAASD,gCACPE,QAAiB;IAEjB,IAAI,CAACA,YAAY,OAAOA,aAAa,YAAY,CAAE,CAAA,iBAAiBA,QAAO,GAAI;QAC7E,MAAM,IAAIH,MACR;IAEJ;AACF;AAEA,SAASI,8CACPL,OAGC;IAED,IAAI,CAACA,QAAQM,gBAAgB,EAAE;QAC7BN,QAAQM,gBAAgB,GAAG,SAAyBC,EAAU,EAAEC,QAAgB;YAC9ET,+BAA+B,IAAI;YACnC,MAAMU,KAAKC,wBAAwB,IAAI,CAACP,SAAS,CAACQ,WAAW;YAC7DF,GAAGG,kBAAkB,CAAEC,GAAG,CAACC,yBAAyBP,KAAKQ,OAAOC,IAAI,CAACR;QACvE;QACAR,QAAQiB,gBAAgB,GAAG,SAAyBV,EAAU;YAC5DR,+BAA+B,IAAI;YACnC,MAAMU,KAAKC,wBAAwB,IAAI,CAACP,SAAS,CAACQ,WAAW;YAC7D,OAAOF,GAAGG,kBAAkB,CAAEM,GAAG,CAACJ,yBAAyBP;QAC7D;IACF;IAEA,OAAOP;AACT;AAEA,SAASc,yBAAyBP,EAAU;IAC1C,wHAAwH;IACxH,gGAAgG;IAChG,IAAI,CAACA,GAAGY,UAAU,CAAC,OAAO;QACxB,MAAM,IAAIlB,MAAM,CAAC,uEAAuE,EAAEM,IAAI;IAChG;IACA,OAAOA;AACT;AAEO,SAAST,kCACdE,OAEC;IAED,IAAI,CAACA,QAAQoB,aAAa,CAACC,SAAS,EAAE;QACpC,MAAMC,wBAAwBtB,QAAQoB,aAAa,CAACG,IAAI,CAACvB;QAEzDA,QAAQoB,aAAa,GAAG,eACtBI,QAAgB,EAChBC,gBAAqB,EACrB,mGAAmG,GACnGC,UAAmB;YAEnB,kFAAkF;YAClF,IAAI,CAACA,YAAY;gBACf,IAAIF,SAASL,UAAU,CAAC,OAAO;oBAC7B,MAAMQ,QAAQ,MAAM,IAAI,CAACC,kBAAkB;oBAE3C1B,gCAAgCyB;oBAEhC,IAAIA,MAAMhB,WAAW,CAACC,kBAAkB,EAAE;wBACxCc,aAAaC,MAAMhB,WAAW,CAACC,kBAAkB,CAACiB,GAAG,CAACL;oBACxD;oBAEA,IAAI,CAACE,YAAY;wBACf,MAAM,IAAIzB,MAAM,CAAC,gBAAgB,EAAEuB,SAAS,YAAY,CAAC;oBAC3D;gBACF;YACF;YACA,OAAOF,sBAAsBE,UAAUC,kBAAkBC;QAC3D;QAEA1B,QAAQoB,aAAa,CAACC,SAAS,GAAG;IACpC;IAEA,OAAOhB,8CAA8CL;AACvD;AAEA,SAASU,wBAAwBD,EAAyB;IACxD,IAAI,CAACA,GAAGqB,OAAO,CAACT,SAAS,EAAE;QACzB,MAAMU,mBAAmBtB,GAAGqB,OAAO,CAACP,IAAI,CAACd;QACzCA,GAAGqB,OAAO,GAAG,CAACE;YACZ,gCAAgC;YAChC,IAAIA,SAASb,UAAU,CAAC,OAAO;gBAC7B,OAAOa;YACT;YAEA,OAAOD,iBAAiBC;QAC1B;QACAvB,GAAGqB,OAAO,CAACT,SAAS,GAAG;IACzB;IAEA,mFAAmF;IACnF,IAAI,CAACZ,GAAGG,kBAAkB,EAAE;QAC1BH,GAAGG,kBAAkB,GAAG,IAAIqB;IAC9B;IAEA,OAAOxB;AACT"}