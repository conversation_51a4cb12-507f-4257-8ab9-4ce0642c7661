# lodash.throttle v4.1.1

The [lodash](https://lodash.com/) method `_.throttle` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.throttle
```

In Node.js:
```js
var throttle = require('lodash.throttle');
```

See the [documentation](https://lodash.com/docs#throttle) or [package source](https://github.com/lodash/lodash/blob/4.1.1-npm-packages/lodash.throttle) for more details.
