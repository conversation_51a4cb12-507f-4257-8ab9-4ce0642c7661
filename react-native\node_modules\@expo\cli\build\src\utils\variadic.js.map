{"version": 3, "sources": ["../../../src/utils/variadic.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\n\nconst debug = require('debug')('expo:utils:variadic') as typeof console.log;\n\n/** Given a list of CLI args, return a sorted set of args based on categories used in a complex command. */\nexport function parseVariadicArguments(\n  argv: string[],\n  strFlags: string[] = []\n): {\n  variadic: string[];\n  extras: string[];\n  flags: Record<string, boolean | string | string[] | undefined>;\n} {\n  const variadic: string[] = [];\n  const parsedFlags: Record<string, boolean | string | string[]> = {};\n\n  let i = 0;\n  while (i < argv.length) {\n    const arg = argv[i];\n\n    if (!arg.startsWith('-')) {\n      variadic.push(arg);\n    } else if (arg === '--') {\n      break;\n    } else {\n      const flagIndex = strFlags.indexOf(arg.split('=')[0]);\n      if (flagIndex !== -1) {\n        // Handle flags that expect a value\n        const [flag, value] = arg.split('=');\n        if (value !== undefined) {\n          // If the flag has a value inline (e.g., --flag=value)\n          if (parsedFlags[flag] === undefined) {\n            parsedFlags[flag] = value;\n          } else if (Array.isArray(parsedFlags[flag])) {\n            (parsedFlags[flag] as string[]).push(value);\n          } else {\n            parsedFlags[flag] = [parsedFlags[flag] as string, value];\n          }\n        } else {\n          const nextArg = argv[i + 1];\n          if (nextArg && !nextArg.startsWith('-')) {\n            if (parsedFlags[arg] === undefined) {\n              parsedFlags[arg] = nextArg;\n            } else if (Array.isArray(parsedFlags[arg])) {\n              (parsedFlags[arg] as string[]).push(nextArg);\n            } else {\n              parsedFlags[arg] = [parsedFlags[arg] as string, nextArg];\n            }\n            i++; // Skip the next argument since it's part of the current flag\n          } else {\n            if (parsedFlags[arg] === undefined) {\n              parsedFlags[arg] = true; // Flag without a value\n            } else if (Array.isArray(parsedFlags[arg])) {\n              (parsedFlags[arg] as (string | boolean)[]).push(true);\n            } else {\n              parsedFlags[arg] = [parsedFlags[arg] as any, true];\n            }\n          }\n        }\n      } else {\n        if (parsedFlags[arg] === undefined) {\n          parsedFlags[arg] = true; // Unknown flag\n        } else if (Array.isArray(parsedFlags[arg])) {\n          (parsedFlags[arg] as (string | boolean)[]).push(true);\n        } else {\n          parsedFlags[arg] = [parsedFlags[arg] as any, true];\n        }\n      }\n    }\n    i++;\n  }\n\n  // Everything after `--` that is not an option is passed to the underlying install command.\n  const extras: string[] = [];\n  const extraOperator = argv.indexOf('--');\n  if (extraOperator > -1 && argv.length > extraOperator + 1) {\n    const extraArgs = argv.slice(extraOperator + 1);\n    if (extraArgs.includes('--')) {\n      throw new CommandError('BAD_ARGS', 'Unexpected multiple --');\n    }\n    extras.push(...extraArgs);\n    debug('Extra arguments: ' + extras.join(', '));\n  }\n\n  debug(`Parsed arguments (variadic: %O, flags: %O, extra: %O)`, variadic, parsedFlags, extras);\n\n  return {\n    variadic,\n    flags: parsedFlags,\n    extras,\n  };\n}\n\nexport function assertUnexpectedObjectKeys(keys: string[], obj: Record<string, any>): void {\n  const unexpectedKeys = Object.keys(obj).filter((key) => !keys.includes(key));\n  if (unexpectedKeys.length > 0) {\n    throw new CommandError('BAD_ARGS', `Unexpected: ${unexpectedKeys.join(', ')}`);\n  }\n}\n\nexport function assertUnexpectedVariadicFlags(\n  expectedFlags: string[],\n  { extras, flags, variadic }: ReturnType<typeof parseVariadicArguments>,\n  prefixCommand = ''\n) {\n  const unexpectedFlags = Object.keys(flags).filter((key) => !expectedFlags.includes(key));\n\n  if (unexpectedFlags.length > 0) {\n    const intendedFlags = Object.entries(flags)\n      .filter(([key]) => expectedFlags.includes(key))\n      .map(([key]) => key);\n\n    const cmd = [\n      prefixCommand,\n      ...variadic,\n      ...intendedFlags,\n      '--',\n      ...extras.concat(unexpectedFlags),\n    ].join(' ');\n\n    throw new CommandError(\n      'BAD_ARGS',\n      `Unexpected: ${unexpectedFlags.join(', ')}\\nDid you mean: ${cmd.trim()}`\n    );\n  }\n}\n"], "names": ["assertUnexpectedObjectKeys", "assertUnexpectedVariadicFlags", "parseVariadicArguments", "debug", "require", "argv", "strFlags", "variadic", "parsedFlags", "i", "length", "arg", "startsWith", "push", "flagIndex", "indexOf", "split", "flag", "value", "undefined", "Array", "isArray", "nextArg", "extras", "extraOperator", "extraArgs", "slice", "includes", "CommandError", "join", "flags", "keys", "obj", "<PERSON><PERSON><PERSON><PERSON>", "Object", "filter", "key", "expectedFlags", "prefixCommand", "unexpectedFlags", "intendedFlags", "entries", "map", "cmd", "concat", "trim"], "mappings": ";;;;;;;;;;;IA6FgBA,0BAA0B;eAA1BA;;IAOAC,6BAA6B;eAA7BA;;IA/FAC,sBAAsB;eAAtBA;;;wBALa;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASF,uBACdG,IAAc,EACdC,WAAqB,EAAE;IAMvB,MAAMC,WAAqB,EAAE;IAC7B,MAAMC,cAA2D,CAAC;IAElE,IAAIC,IAAI;IACR,MAAOA,IAAIJ,KAAKK,MAAM,CAAE;QACtB,MAAMC,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,CAACE,IAAIC,UAAU,CAAC,MAAM;YACxBL,SAASM,IAAI,CAACF;QAChB,OAAO,IAAIA,QAAQ,MAAM;YACvB;QACF,OAAO;YACL,MAAMG,YAAYR,SAASS,OAAO,CAACJ,IAAIK,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,IAAIF,cAAc,CAAC,GAAG;gBACpB,mCAAmC;gBACnC,MAAM,CAACG,MAAMC,MAAM,GAAGP,IAAIK,KAAK,CAAC;gBAChC,IAAIE,UAAUC,WAAW;oBACvB,sDAAsD;oBACtD,IAAIX,WAAW,CAACS,KAAK,KAAKE,WAAW;wBACnCX,WAAW,CAACS,KAAK,GAAGC;oBACtB,OAAO,IAAIE,MAAMC,OAAO,CAACb,WAAW,CAACS,KAAK,GAAG;wBAC1CT,WAAW,CAACS,KAAK,CAAcJ,IAAI,CAACK;oBACvC,OAAO;wBACLV,WAAW,CAACS,KAAK,GAAG;4BAACT,WAAW,CAACS,KAAK;4BAAYC;yBAAM;oBAC1D;gBACF,OAAO;oBACL,MAAMI,UAAUjB,IAAI,CAACI,IAAI,EAAE;oBAC3B,IAAIa,WAAW,CAACA,QAAQV,UAAU,CAAC,MAAM;wBACvC,IAAIJ,WAAW,CAACG,IAAI,KAAKQ,WAAW;4BAClCX,WAAW,CAACG,IAAI,GAAGW;wBACrB,OAAO,IAAIF,MAAMC,OAAO,CAACb,WAAW,CAACG,IAAI,GAAG;4BACzCH,WAAW,CAACG,IAAI,CAAcE,IAAI,CAACS;wBACtC,OAAO;4BACLd,WAAW,CAACG,IAAI,GAAG;gCAACH,WAAW,CAACG,IAAI;gCAAYW;6BAAQ;wBAC1D;wBACAb,KAAK,6DAA6D;oBACpE,OAAO;wBACL,IAAID,WAAW,CAACG,IAAI,KAAKQ,WAAW;4BAClCX,WAAW,CAACG,IAAI,GAAG,MAAM,uBAAuB;wBAClD,OAAO,IAAIS,MAAMC,OAAO,CAACb,WAAW,CAACG,IAAI,GAAG;4BACzCH,WAAW,CAACG,IAAI,CAA0BE,IAAI,CAAC;wBAClD,OAAO;4BACLL,WAAW,CAACG,IAAI,GAAG;gCAACH,WAAW,CAACG,IAAI;gCAAS;6BAAK;wBACpD;oBACF;gBACF;YACF,OAAO;gBACL,IAAIH,WAAW,CAACG,IAAI,KAAKQ,WAAW;oBAClCX,WAAW,CAACG,IAAI,GAAG,MAAM,eAAe;gBAC1C,OAAO,IAAIS,MAAMC,OAAO,CAACb,WAAW,CAACG,IAAI,GAAG;oBACzCH,WAAW,CAACG,IAAI,CAA0BE,IAAI,CAAC;gBAClD,OAAO;oBACLL,WAAW,CAACG,IAAI,GAAG;wBAACH,WAAW,CAACG,IAAI;wBAAS;qBAAK;gBACpD;YACF;QACF;QACAF;IACF;IAEA,2FAA2F;IAC3F,MAAMc,SAAmB,EAAE;IAC3B,MAAMC,gBAAgBnB,KAAKU,OAAO,CAAC;IACnC,IAAIS,gBAAgB,CAAC,KAAKnB,KAAKK,MAAM,GAAGc,gBAAgB,GAAG;QACzD,MAAMC,YAAYpB,KAAKqB,KAAK,CAACF,gBAAgB;QAC7C,IAAIC,UAAUE,QAAQ,CAAC,OAAO;YAC5B,MAAM,IAAIC,oBAAY,CAAC,YAAY;QACrC;QACAL,OAAOV,IAAI,IAAIY;QACftB,MAAM,sBAAsBoB,OAAOM,IAAI,CAAC;IAC1C;IAEA1B,MAAM,CAAC,qDAAqD,CAAC,EAAEI,UAAUC,aAAae;IAEtF,OAAO;QACLhB;QACAuB,OAAOtB;QACPe;IACF;AACF;AAEO,SAASvB,2BAA2B+B,IAAc,EAAEC,GAAwB;IACjF,MAAMC,iBAAiBC,OAAOH,IAAI,CAACC,KAAKG,MAAM,CAAC,CAACC,MAAQ,CAACL,KAAKJ,QAAQ,CAACS;IACvE,IAAIH,eAAevB,MAAM,GAAG,GAAG;QAC7B,MAAM,IAAIkB,oBAAY,CAAC,YAAY,CAAC,YAAY,EAAEK,eAAeJ,IAAI,CAAC,OAAO;IAC/E;AACF;AAEO,SAAS5B,8BACdoC,aAAuB,EACvB,EAAEd,MAAM,EAAEO,KAAK,EAAEvB,QAAQ,EAA6C,EACtE+B,gBAAgB,EAAE;IAElB,MAAMC,kBAAkBL,OAAOH,IAAI,CAACD,OAAOK,MAAM,CAAC,CAACC,MAAQ,CAACC,cAAcV,QAAQ,CAACS;IAEnF,IAAIG,gBAAgB7B,MAAM,GAAG,GAAG;QAC9B,MAAM8B,gBAAgBN,OAAOO,OAAO,CAACX,OAClCK,MAAM,CAAC,CAAC,CAACC,IAAI,GAAKC,cAAcV,QAAQ,CAACS,MACzCM,GAAG,CAAC,CAAC,CAACN,IAAI,GAAKA;QAElB,MAAMO,MAAM;YACVL;eACG/B;eACAiC;YACH;eACGjB,OAAOqB,MAAM,CAACL;SAClB,CAACV,IAAI,CAAC;QAEP,MAAM,IAAID,oBAAY,CACpB,YACA,CAAC,YAAY,EAAEW,gBAAgBV,IAAI,CAAC,MAAM,gBAAgB,EAAEc,IAAIE,IAAI,IAAI;IAE5E;AACF"}