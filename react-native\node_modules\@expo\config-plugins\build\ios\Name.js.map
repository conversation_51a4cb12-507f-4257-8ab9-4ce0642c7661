{"version": 3, "file": "Name.js", "names": ["_Target", "data", "require", "_Xcodeproj", "_iosPlugins", "withDisplayName", "exports", "createInfoPlistPluginWithPropertyGuard", "setDisplayName", "infoPlistProperty", "expoConfigProperty", "with<PERSON><PERSON>", "setName", "withProductName", "config", "withXcodeProject", "modResults", "setProductName", "getName", "name", "configOrName", "CFBundleDisplayName", "infoPlist", "CFBundleName", "project", "sanitizedName", "quotedName", "ensureQuotes", "nativeTarget", "findFirstNativeTarget", "getBuildConfigurationsForListId", "buildConfigurationList", "for<PERSON>ach", "item", "buildSettings", "PRODUCT_NAME", "value", "match"], "sources": ["../../src/ios/Name.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { XcodeProject } from 'xcode';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { findFirstNativeTarget } from './Target';\nimport { ConfigPlugin } from '../Plugin.types';\nimport {\n  ConfigurationSectionEntry,\n  getBuildConfigurationsForListId,\n  sanitizedName,\n} from './utils/Xcodeproj';\nimport { createInfoPlistPluginWithPropertyGuard, withXcodeProject } from '../plugins/ios-plugins';\n\nexport const withDisplayName = createInfoPlistPluginWithPropertyGuard(\n  setDisplayName,\n  {\n    infoPlistProperty: 'CFBundleDisplayName',\n    expoConfigProperty: 'name',\n  },\n  'withDisplayName'\n);\n\nexport const withName = createInfoPlistPluginWithPropertyGuard(\n  setName,\n  {\n    infoPlistProperty: 'CFBundleName',\n    expoConfigProperty: 'name',\n  },\n  'withName'\n);\n\n/** Set the PRODUCT_NAME variable in the xcproj file based on the app.json name property. */\nexport const withProductName: ConfigPlugin = (config) => {\n  return withXcodeProject(config, (config) => {\n    config.modResults = setProductName(config, config.modResults);\n    return config;\n  });\n};\n\nexport function getName(config: Pick<ExpoConfig, 'name'>) {\n  return typeof config.name === 'string' ? config.name : null;\n}\n\n/**\n * CFBundleDisplayName is used for most things: the name on the home screen, in\n * notifications, and others.\n */\nexport function setDisplayName(\n  configOrName: Pick<ExpoConfig, 'name'> | string,\n  { CFBundleDisplayName, ...infoPlist }: InfoPlist\n): InfoPlist {\n  let name: string | null = null;\n  if (typeof configOrName === 'string') {\n    name = configOrName;\n  } else {\n    name = getName(configOrName);\n  }\n\n  if (!name) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleDisplayName: name,\n  };\n}\n\n/**\n * CFBundleName is recommended to be 16 chars or less and is used in lists, eg:\n * sometimes on the App Store\n */\nexport function setName(\n  config: Pick<ExpoConfig, 'name'>,\n  { CFBundleName, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const name = getName(config);\n\n  if (!name) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleName: name,\n  };\n}\n\nexport function setProductName(\n  config: Pick<ExpoConfig, 'name'>,\n  project: XcodeProject\n): XcodeProject {\n  const name = sanitizedName(getName(config) ?? '');\n\n  if (!name) {\n    return project;\n  }\n  const quotedName = ensureQuotes(name);\n\n  const [, nativeTarget] = findFirstNativeTarget(project);\n\n  getBuildConfigurationsForListId(project, nativeTarget.buildConfigurationList).forEach(\n    ([, item]: ConfigurationSectionEntry) => {\n      item.buildSettings.PRODUCT_NAME = quotedName;\n    }\n  );\n\n  return project;\n}\n\nconst ensureQuotes = (value: string) => {\n  if (!value.match(/^['\"]/)) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n"], "mappings": ";;;;;;;;;;AAIA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,SAAAG,YAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,WAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMI,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,IAAAE,oDAAsC,EACnEC,cAAc,EACd;EACEC,iBAAiB,EAAE,qBAAqB;EACxCC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBACF,CAAC;AAEM,MAAMC,QAAQ,GAAAL,OAAA,CAAAK,QAAA,GAAG,IAAAJ,oDAAsC,EAC5DK,OAAO,EACP;EACEH,iBAAiB,EAAE,cAAc;EACjCC,kBAAkB,EAAE;AACtB,CAAC,EACD,UACF,CAAC;;AAED;AACO,MAAMG,eAA6B,GAAIC,MAAM,IAAK;EACvD,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC1CA,MAAM,CAACE,UAAU,GAAGC,cAAc,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACR,OAAA,CAAAO,eAAA,GAAAA,eAAA;AAEK,SAASK,OAAOA,CAACJ,MAAgC,EAAE;EACxD,OAAO,OAAOA,MAAM,CAACK,IAAI,KAAK,QAAQ,GAAGL,MAAM,CAACK,IAAI,GAAG,IAAI;AAC7D;;AAEA;AACA;AACA;AACA;AACO,SAASX,cAAcA,CAC5BY,YAA+C,EAC/C;EAAEC,mBAAmB;EAAE,GAAGC;AAAqB,CAAC,EACrC;EACX,IAAIH,IAAmB,GAAG,IAAI;EAC9B,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;IACpCD,IAAI,GAAGC,YAAY;EACrB,CAAC,MAAM;IACLD,IAAI,GAAGD,OAAO,CAACE,YAAY,CAAC;EAC9B;EAEA,IAAI,CAACD,IAAI,EAAE;IACT,OAAOG,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,mBAAmB,EAAEF;EACvB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACO,SAASP,OAAOA,CACrBE,MAAgC,EAChC;EAAES,YAAY;EAAE,GAAGD;AAAqB,CAAC,EAC9B;EACX,MAAMH,IAAI,GAAGD,OAAO,CAACJ,MAAM,CAAC;EAE5B,IAAI,CAACK,IAAI,EAAE;IACT,OAAOG,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZC,YAAY,EAAEJ;EAChB,CAAC;AACH;AAEO,SAASF,cAAcA,CAC5BH,MAAgC,EAChCU,OAAqB,EACP;EACd,MAAML,IAAI,GAAG,IAAAM,0BAAa,EAACP,OAAO,CAACJ,MAAM,CAAC,IAAI,EAAE,CAAC;EAEjD,IAAI,CAACK,IAAI,EAAE;IACT,OAAOK,OAAO;EAChB;EACA,MAAME,UAAU,GAAGC,YAAY,CAACR,IAAI,CAAC;EAErC,MAAM,GAAGS,YAAY,CAAC,GAAG,IAAAC,+BAAqB,EAACL,OAAO,CAAC;EAEvD,IAAAM,4CAA+B,EAACN,OAAO,EAAEI,YAAY,CAACG,sBAAsB,CAAC,CAACC,OAAO,CACnF,CAAC,GAAGC,IAAI,CAA4B,KAAK;IACvCA,IAAI,CAACC,aAAa,CAACC,YAAY,GAAGT,UAAU;EAC9C,CACF,CAAC;EAED,OAAOF,OAAO;AAChB;AAEA,MAAMG,YAAY,GAAIS,KAAa,IAAK;EACtC,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;IACzB,OAAO,IAAID,KAAK,GAAG;EACrB;EACA,OAAOA,KAAK;AACd,CAAC", "ignoreList": []}