{"version": 3, "names": ["convertAnimationObjectToKeyframes", "DEFAULT_BOUNCE_TIME", "BounceInData", "BounceIn", "name", "style", "transform", "scale", "duration", "BounceInRight", "translateX", "BounceInLeft", "BounceInUp", "translateY", "BounceInDown", "BounceOutData", "BounceOut", "BounceOutRight", "BounceOutLeft", "BounceOutUp", "BounceOutDown"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/animation/Bounce.web.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,iCAAiC,QAAQ,uBAAoB;AAEtE,MAAMC,mBAAmB,GAAG,GAAG;AAE/B,OAAO,MAAMC,YAAY,GAAG;EAC1BC,QAAQ,EAAE;IACRC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE;IACnC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDQ,aAAa,EAAE;IACbL,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDU,YAAY,EAAE;IACZP,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDW,UAAU,EAAE;IACVR,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEP;EACZ,CAAC;EAEDa,YAAY,EAAE;IACZV,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE;IAC5C,CAAC;IACDL,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAMc,aAAa,GAAG;EAC3BC,SAAS,EAAE;IACTZ,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;MAAE,CAAC;MAChC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,EAAE,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE,CAAC;MACnC,GAAG,EAAE;QAAED,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC;MAAE;IACrC,CAAC;IACDC,QAAQ,EAAEP;EACZ,CAAC;EAEDgB,cAAc,EAAE;IACdb,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDiB,aAAa,EAAE;IACbd,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;UAAEI,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDF,QAAQ,EAAEP;EACZ,CAAC;EAEDkB,WAAW,EAAE;IACXf,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAS,CAAC;MAAE;IAC/C,CAAC;IACDL,QAAQ,EAAEP;EACZ,CAAC;EAEDmB,aAAa,EAAE;IACbhB,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;MACL,CAAC,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAM,CAAC;MAAE,CAAC;MACzC,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAO,CAAC;MAAE,CAAC;MAC3C,EAAE,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE,CAAC;MAC5C,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;UAAEO,UAAU,EAAE;QAAQ,CAAC;MAAE;IAC9C,CAAC;IACDL,QAAQ,EAAEP;EACZ;AACF,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBA,QAAQ,EAAE;IACRE,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACC,QAAQ,CAAC;IAC/DK,QAAQ,EAAEN,YAAY,CAACC,QAAQ,CAACK;EAClC,CAAC;EACDC,aAAa,EAAE;IACbJ,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACO,aAAa,CAAC;IACpED,QAAQ,EAAEN,YAAY,CAACO,aAAa,CAACD;EACvC,CAAC;EACDG,YAAY,EAAE;IACZN,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACS,YAAY,CAAC;IACnEH,QAAQ,EAAEN,YAAY,CAACS,YAAY,CAACH;EACtC,CAAC;EACDI,UAAU,EAAE;IACVP,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACU,UAAU,CAAC;IACjEJ,QAAQ,EAAEN,YAAY,CAACU,UAAU,CAACJ;EACpC,CAAC;EACDM,YAAY,EAAE;IACZT,KAAK,EAAEL,iCAAiC,CAACE,YAAY,CAACY,YAAY,CAAC;IACnEN,QAAQ,EAAEN,YAAY,CAACY,YAAY,CAACN;EACtC;AACF,CAAC;AAED,OAAO,MAAMQ,SAAS,GAAG;EACvBA,SAAS,EAAE;IACTX,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACC,SAAS,CAAC;IACjER,QAAQ,EAAEO,aAAa,CAACC,SAAS,CAACR;EACpC,CAAC;EACDS,cAAc,EAAE;IACdZ,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACE,cAAc,CAAC;IACtET,QAAQ,EAAEO,aAAa,CAACE,cAAc,CAACT;EACzC,CAAC;EACDU,aAAa,EAAE;IACbb,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACG,aAAa,CAAC;IACrEV,QAAQ,EAAEO,aAAa,CAACG,aAAa,CAACV;EACxC,CAAC;EACDW,WAAW,EAAE;IACXd,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACI,WAAW,CAAC;IACnEX,QAAQ,EAAEO,aAAa,CAACI,WAAW,CAACX;EACtC,CAAC;EACDY,aAAa,EAAE;IACbf,KAAK,EAAEL,iCAAiC,CAACe,aAAa,CAACK,aAAa,CAAC;IACrEZ,QAAQ,EAAEO,aAAa,CAACK,aAAa,CAACZ;EACxC;AACF,CAAC", "ignoreList": []}