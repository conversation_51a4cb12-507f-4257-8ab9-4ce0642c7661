{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "tintColor", "style", "rest", "colors", "useTheme", "styles", "title", "color", "undefined", "text", "StyleSheet", "create", "Platform", "select", "ios", "fontSize", "fontWeight", "android", "fontFamily", "default"], "sourceRoot": "../../../src", "sources": ["Header/HeaderTitle.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAOsB;AAAA;AAAA;AAOP,SAASA,WAAW,OAAuC;EAAA,IAAtC;IAAEC,SAAS;IAAEC,KAAK;IAAE,GAAGC;EAAY,CAAC;EACtE,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAE7B,oBACE,oBAAC,qBAAQ,CAAC,IAAI;IACZ,iBAAiB,EAAC,QAAQ;IAC1B,cAAW,GAAG;IACd,aAAa,EAAE;EAAE,GACbF,IAAI;IACR,KAAK,EAAE,CACLG,MAAM,CAACC,KAAK,EACZ;MAAEC,KAAK,EAAEP,SAAS,KAAKQ,SAAS,GAAGL,MAAM,CAACM,IAAI,GAAGT;IAAU,CAAC,EAC5DC,KAAK;EACL,GACF;AAEN;AAEA,MAAMI,MAAM,GAAGK,uBAAU,CAACC,MAAM,CAAC;EAC/BL,KAAK,EAAEM,qBAAQ,CAACC,MAAM,CAAC;IACrBC,GAAG,EAAE;MACHC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPF,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,mBAAmB;MAC/BF,UAAU,EAAE;IACd,CAAC;IACDG,OAAO,EAAE;MACPJ,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC"}