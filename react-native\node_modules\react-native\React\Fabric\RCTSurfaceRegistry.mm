/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import "RCTSurfaceRegistry.h"

#import <mutex>
#import <shared_mutex>

#import <React/RCTFabricSurface.h>

using namespace facebook;

@implementation RCTSurfaceRegistry {
  std::shared_mutex _mutex;
  NSMapTable<id, RCTFabricSurface *> *_registry;
}

- (instancetype)init
{
  if (self = [super init]) {
    _registry = [NSMapTable mapTableWithKeyOptions:NSPointerFunctionsIntegerPersonality | NSPointerFunctionsOpaqueMemory
                                      valueOptions:NSPointerFunctionsObjectPersonality | NSPointerFunctionsWeakMemory];
  }

  return self;
}

- (void)enumerateWithBlock:(RCTSurfaceEnumeratorBlock)block
{
  std::shared_lock lock(_mutex);
  block([_registry objectEnumerator]);
}

- (void)registerSurface:(RCTFabricSurface *)surface
{
  std::unique_lock lock(_mutex);

  ReactTag rootTag = surface.rootViewTag.integerValue;
  [_registry setObject:surface forKey:(__bridge id)(void *)rootTag];
}

- (void)unregisterSurface:(RCTFabricSurface *)surface
{
  std::unique_lock lock(_mutex);

  ReactTag rootTag = surface.rootViewTag.integerValue;
  [_registry removeObjectForKey:(__bridge id)(void *)rootTag];
}

- (RCTFabricSurface *)surfaceForRootTag:(ReactTag)rootTag
{
  std::shared_lock lock(_mutex);

  return [_registry objectForKey:(__bridge id)(void *)rootTag];
}

@end
