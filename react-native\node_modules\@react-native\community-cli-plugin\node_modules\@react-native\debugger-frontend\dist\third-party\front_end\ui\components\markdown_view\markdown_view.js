import"../../legacy/legacy.js";import*as e from"../../../core/host/host.js";import*as t from"../../../core/i18n/i18n.js";import*as o from"../../../third_party/codemirror.next/codemirror.next.js";import*as r from"../text_editor/text_editor.js";import*as s from"../icon_button/icon_button.js";import*as i from"../../lit-html/lit-html.js";import*as n from"../../visual_logging/visual_logging.js";const a=new CSSStyleSheet;a.replaceSync("*{margin:0;padding:0;box-sizing:border-box}code{color:var(--sys-color-on-surface);font-size:11px;user-select:text;cursor:text;background:var(--sys-color-surface1)}.codeblock{margin-bottom:8px;box-sizing:border-box;border-radius:4px;background-color:var(--sys-color-surface5);color:var(--sys-color-on-surface)}.codeblock .toolbar{box-sizing:border-box;display:flex;height:28px;flex-direction:row;padding:0 11px;font-size:11px;font-style:normal;font-weight:400;line-height:16px}.codeblock .editor-wrapper{color:var(--sys-color-on-surface);background:var(--sys-color-surface1);padding:10px 5px}.codeblock .lang{padding:6px 0;flex:1}.codeblock .copy{padding:4px 0;align-items:center;display:flex}.codeblock .copy-button{margin:0;padding:0;background:transparent;border:none;font-size:11px;line-height:16px;display:flex;flex-direction:row;align-items:center;gap:4px}.codeblock .copy-button.copied{color:var(--sys-color-primary);--copy-icon-color:var(--sys-color-primary)}.notice{margin-top:3px;margin-left:3px;.link{color:var(--sys-color-primary);text-decoration-line:underline}}\n/*# sourceURL=codeBlock.css */\n");const c={copy:"Copy code",copied:"Copied to clipboard",disclaimer:"Use code snippets with caution"},d=t.i18n.registerUIStrings("ui/components/markdown_view/CodeBlock.ts",c),l=t.i18n.getLocalizedString.bind(void 0,d);class h extends HTMLElement{static litTagName=i.literal`devtools-code-block`;#e=this.attachShadow({mode:"open"});#t="";#o="";#r=1e3;#s;#i=!1;#n;#a=new o.Compartment;#c=!1;connectedCallback(){this.#e.adoptedStyleSheets=[a],this.#d()}set code(e){this.#t=e,this.#n=o.EditorState.create({doc:this.#t,extensions:[r.Config.baseConfiguration(this.#t),o.EditorState.readOnly.of(!0),o.EditorView.lineWrapping,this.#a.of(o.javascript.javascript())]}),this.#d()}get code(){return this.#t}set codeLang(e){this.#o=e,this.#d()}set timeout(e){this.#r=e,this.#d()}set displayNotice(e){this.#c=e,this.#d()}#l(){e.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.#t),this.#i=!0,this.#d(),clearTimeout(this.#s),this.#s=setTimeout((()=>{this.#i=!1,this.#d()}),this.#r)}#d(){const e=i.Directives.classMap({copied:this.#i,"copy-button":!0});i.render(i.html`<div class="codeblock">
      <div class="toolbar">
        <div class="lang">${this.#o}</div>
        <div class="copy">
          <button class=${e}
            title=${l(c.copy)}
            @click=${this.#l}>
            <${s.Icon.Icon.litTagName}
              .data=${{iconName:"copy",width:"16px",height:"16px",color:"var(--copy-icon-color, var(--icon-default))"}}
            >
            </${s.Icon.Icon.litTagName}>
            <span>${this.#i?l(c.copied):l(c.copy)}</span>
          </button>
        </div>
      </div>
      <div class="editor-wrapper">
        <${r.TextEditor.TextEditor.litTagName} .state=${this.#n}></${r.TextEditor.TextEditor.litTagName}>
        ${this.#c?i.html`<p class="notice">
          <x-link class="link" href="https://support.google.com/legal/answer/13505487">${l(c.disclaimer)}</x-link>
        </p>`:i.nothing}
      </div>
    </div>`,this.#e,{host:this});const t=this.#e?.querySelector("devtools-text-editor")?.editor;if(!t)return;let n=o.html.html();switch(this.#o){case"js":n=o.javascript.javascript();break;case"ts":n=o.javascript.javascript({typescript:!0});break;case"jsx":n=o.javascript.javascript({jsx:!0})}t.dispatch({effects:this.#a.reconfigure(n)})}}customElements.define("devtools-code-block",h);var p=Object.freeze({__proto__:null,CodeBlock:h});const m=new CSSStyleSheet;m.replaceSync(".markdown-image{display:block}\n/*# sourceURL=markdownImage.css */\n");const g=new Map([]),u=e=>{const t=g.get(e);if(!t)throw new Error(`Markdown image with key '${e}' is not available, please check MarkdownImagesMap.ts`);return t};var w=Object.freeze({__proto__:null,markdownImages:g,getMarkdownImage:u});class k extends HTMLElement{static litTagName=i.literal`devtools-markdown-image`;#e=this.attachShadow({mode:"open"});#h;#p;connectedCallback(){this.#e.adoptedStyleSheets=[m]}set data(e){const{key:t,title:o}=e,r=u(t);this.#h=r,this.#p=o,this.#d()}#m(){if(!this.#h)return i.html``;const{src:e,color:t,width:o="100%",height:r="100%"}=this.#h;return i.html`
      <${s.Icon.Icon.litTagName} .data=${{iconPath:e,color:t,width:o,height:r}}></${s.Icon.Icon.litTagName}>
    `}#g(){if(!this.#h)return i.html``;const{src:e,width:t="100%",height:o="100%"}=this.#h;return i.html`
      <img class="markdown-image" src=${e} alt=${this.#p} width=${t} height=${o}/>
    `}#d(){if(!this.#h)return;const{isIcon:e}=this.#h,t=e?this.#m():this.#g();i.render(t,this.#e,{host:this})}}customElements.define("devtools-markdown-image",k);var x=Object.freeze({__proto__:null,MarkdownImage:k});const v=new CSSStyleSheet;v.replaceSync(".devtools-link{color:var(--sys-color-primary);outline-offset:2px;text-decoration:none}.devtools-link:hover{text-decoration:underline}\n/*# sourceURL=markdownLink.css */\n");const y=new Map([["issuesContrastWCAG21AA","https://www.w3.org/TR/WCAG21/#contrast-minimum"],["issuesContrastWCAG21AAA","https://www.w3.org/TR/WCAG21/#contrast-enhanced"],["issuesContrastSuggestColor","https://developers.google.com/web/updates/2020/08/devtools#accessible-color"],["issuesCSPSetStrict","https://web.dev/strict-csp"],["issuesCSPWhyStrictOverAllowlist","https://web.dev/strict-csp/#why-a-strict-csp-is-recommended-over-allowlist-csps"],["issueCorsPreflightRequest","https://web.dev/cross-origin-resource-sharing/#preflight-requests-for-complex-http-calls"],["issueQuirksModeDoctype","https://web.dev/doctype/"],["sameSiteAndSameOrigin","https://web.dev/same-site-same-origin/"],["punycodeReference","https://wikipedia.org/wiki/Punycode"],["https://xhr.spec.whatwg.org/","https://xhr.spec.whatwg.org/"],["https://goo.gle/chrome-insecure-origins","https://goo.gle/chrome-insecure-origins"],["https://webrtc.org/web-apis/chrome/unified-plan/","https://webrtc.org/web-apis/chrome/unified-plan/"],["https://developer.chrome.com/blog/enabling-shared-array-buffer/","https://developer.chrome.com/blog/enabling-shared-array-buffer/"],["https://developer.chrome.com/docs/extensions/mv3/","https://developer.chrome.com/docs/extensions/mv3/"],["https://developer.chrome.com/blog/immutable-document-domain/","https://developer.chrome.com/blog/immutable-document-domain/"],["https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md","https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"],["https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled","https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled"],["PNASecureContextRestrictionFeatureStatus","https://chromestatus.com/feature/5954091755241472"],["https://w3c.github.io/uievents/#legacy-event-types","https://w3c.github.io/uievents/#legacy-event-types"],["https://support.google.com/chrome/answer/95647","https://support.google.com/chrome/answer/95647"]]),b=e=>{if(/^https:\/\/www.chromestatus.com\/feature\/\d+$/.test(e))return e;const t=y.get(e);if(!t)throw new Error(`Markdown link with key '${e}' is not available, please check MarkdownLinksMap.ts`);return t};var f=Object.freeze({__proto__:null,markdownLinks:y,getMarkdownLink:b});class T extends HTMLElement{static litTagName=i.literal`devtools-markdown-link`;#e=this.attachShadow({mode:"open"});#u="";#w="";connectedCallback(){this.#e.adoptedStyleSheets=[v]}set data(e){const{key:t,title:o}=e,r=b(t);this.#u=o,this.#w=r,this.#d()}#d(){const e=i.html`
      <x-link class="devtools-link" href=${this.#w} jslog=${n.link().track({click:!0})}>${this.#u}</x-link>
    `;i.render(e,this.#e,{host:this})}}customElements.define("devtools-markdown-link",T);var S=Object.freeze({__proto__:null,MarkdownLink:T});const $=new CSSStyleSheet;$.replaceSync(".message{line-height:18px;font-size:12px;color:var(--override-markdown-view-message-color,--sys-color-token-subtle);margin-bottom:4px;user-select:text}.message p{margin-bottom:10px;margin-block-start:2px}.message ul{list-style-type:none;padding-inline-start:1em}.message ul ul{padding-inline-start:19px}.message li{margin-top:8px;display:list-item;list-style-type:disc}.message code{color:var(--sys-color-on-surface);font-size:11px;user-select:text;cursor:text;background:var(--sys-color-surface1)}.devtools-link{color:var(--sys-color-primary);outline-offset:2px;text-decoration:none}.devtools-link:hover{text-decoration:underline}\n/*# sourceURL=markdownView.css */\n");const C=i.html,_=i.render;class j extends HTMLElement{static litTagName=i.literal`devtools-markdown-view`;#e=this.attachShadow({mode:"open"});#k=[];#x=new I;connectedCallback(){this.#e.adoptedStyleSheets=[$]}set data(e){this.#k=e.tokens,e.renderer&&(this.#x=e.renderer),this.#v()}#v(){this.#d()}#d(){_(C`
      <div class='message'>
        ${this.#k.map((e=>this.#x.renderToken(e)))}
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-markdown-view",j);class I{renderChildTokens(e){if("tokens"in e&&e.tokens)return e.tokens.map((e=>this.renderToken(e)));throw new Error("Tokens not found")}unescape(e){const t=new Map([["&amp;","&"],["&lt;","<"],["&gt;",">"],["&quot;",'"'],["&#39;","'"]]);return e.replace(/&(amp|lt|gt|quot|#39);/g,(e=>{const o=t.get(e);return o||e}))}renderText(e){return"tokens"in e&&e.tokens?C`${this.renderChildTokens(e)}`:C`${this.unescape("text"in e?e.text:"")}`}renderHeading(e){switch(e.depth){case 1:return C`<h1>${this.renderText(e)}</h1>`;case 2:return C`<h2>${this.renderText(e)}</h2>`;case 3:return C`<h3>${this.renderText(e)}</h3>`;case 4:return C`<h4>${this.renderText(e)}</h4>`;case 5:return C`<h5>${this.renderText(e)}</h5>`;default:return C`<h6>${this.renderText(e)}</h6>`}}renderCodeBlock(e){return C`<${h.litTagName}
      .code=${this.unescape(e.text)}
      .codeLang=${e.lang}>
    </${h.litTagName}>`}templateForToken(e){switch(e.type){case"paragraph":return C`<p>${this.renderChildTokens(e)}`;case"list":return C`<ul>${e.items.map((e=>this.renderToken(e)))}</ul>`;case"list_item":return C`<li>${this.renderChildTokens(e)}`;case"text":return this.renderText(e);case"codespan":return C`<code>${this.unescape(e.text)}</code>`;case"code":return this.renderCodeBlock(e);case"space":return C``;case"link":return C`<${T.litTagName} .data=${{key:e.href,title:e.text}}></${T.litTagName}>`;case"image":return C`<${k.litTagName} .data=${{key:e.href,title:e.text}}></${k.litTagName}>`;case"heading":return this.renderHeading(e);case"strong":return C`<strong>${this.renderText(e)}</strong>`;case"em":return C`<em>${this.renderText(e)}</em>`;default:return null}}renderToken(e){const t=this.templateForToken(e);if(null===t)throw new Error(`Markdown token type '${e.type}' not supported.`);return t}}var M=Object.freeze({__proto__:null,MarkdownView:j,MarkdownLitRenderer:I});export{p as CodeBlock,x as MarkdownImage,w as MarkdownImagesMap,S as MarkdownLink,f as MarkdownLinksMap,M as MarkdownView};
