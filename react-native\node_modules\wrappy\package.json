{"name": "wrappy", "version": "1.0.2", "description": "Callback wrapping utility", "main": "wrappy.js", "files": ["wrappy.js"], "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.1"}, "scripts": {"test": "tap --coverage test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/wrappy"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy"}