const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4001;

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3002', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'API AquaTrack - Serveur Simple',
    version: '1.0.0',
    port: PORT,
    status: 'Fonctionnel'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');

    const clientsQuery = `
      SELECT 
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(clientsQuery);

    console.log(`✅ ${result.rows.length} clients récupérés`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(client => ({
        idclient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        secteur_nom: client.secteur_nom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer un secteur spécifique par ID
app.get('/api/secteurs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/secteurs/${id}`);

    const result = await pool.query('SELECT * FROM secteur WHERE ids = $1', [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Secteur trouvé: ${result.rows[0].nom}`);
      res.json({
        success: true,
        data: result.rows[0]
      });
    } else {
      console.log(`❌ Secteur non trouvé pour l'ID: ${id}`);
      res.status(404).json({
        success: false,
        message: 'Secteur non trouvé'
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du secteur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du secteur',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`📥 Requête GET /api/clients/${id}/contracts`);

    const contractsQuery = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    const result = await pool.query(contractsQuery, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows,
      client_id: parseInt(id)
    });

  } catch (error) {
    console.error(`❌ Erreur lors de la récupération des contrats du client ${id}:`, error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération des contrats du client ${id}`,
      error: error.message
    });
  }
});

// Route pour scanner un QR code
app.get('/api/scan/:qrCode', async (req, res) => {
  const { qrCode } = req.params;
  
  try {
    console.log(`📥 Scan QR Code: ${qrCode}`);

    const scanQuery = `
      SELECT
        c.idcontract,
        c.codeqr,
        cl.idclient,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.codeqr = $1
    `;

    const result = await pool.query(scanQuery, [qrCode]);

    if (result.rows.length > 0) {
      const clientData = result.rows[0];
      console.log(`✅ Client trouvé pour QR ${qrCode}: ${clientData.nom} ${clientData.prenom}`);
      
      res.json({
        success: true,
        data: {
          client: {
            idclient: clientData.idclient,
            nom: clientData.nom,
            prenom: clientData.prenom,
            adresse: clientData.adresse,
            ville: clientData.ville,
            tel: clientData.tel,
            email: clientData.email
          },
          contract: {
            idcontract: clientData.idcontract,
            codeqr: clientData.codeqr
          }
        }
      });
    } else {
      console.log(`❌ Aucun client trouvé pour QR ${qrCode}`);
      res.status(404).json({
        success: false,
        message: 'Ce QR code ne correspond à aucun client'
      });
    }

  } catch (error) {
    console.error(`❌ Erreur lors du scan QR ${qrCode}:`, error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du scan QR',
      error: error.message
    });
  }
});

// Route pour récupérer les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/secteurs');
    const result = await pool.query('SELECT ids, nom FROM secteur ORDER BY nom');
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});

// Démarrage du serveur
const server = app.listen(PORT, () => {
  console.log(`🚀 Serveur AquaTrack démarré sur le port ${PORT}`);
  console.log(`📡 Endpoints disponibles:`);
  console.log(`   - GET  /api/clients          - Liste des clients`);
  console.log(`   - GET  /api/clients/:id/contracts - Contrats d'un client`);
  console.log(`   - GET  /api/scan/:qrCode     - Scanner un QR code`);
  console.log(`   - GET  /api/secteurs         - Liste des secteurs`);
  console.log(`🌐 CORS autorisé pour: localhost:3000, localhost:3001, localhost:3002`);
  console.log(`🎯 Interface web: http://localhost:3002/technician-dashboard`);
  console.log(`✅ PRÊT À RECEVOIR LES REQUÊTES !`);
});

server.on('error', (error) => {
  console.error('❌ Erreur serveur:', error);
});
