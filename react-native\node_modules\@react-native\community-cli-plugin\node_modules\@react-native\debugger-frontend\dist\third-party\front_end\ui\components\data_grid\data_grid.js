import*as e from"../../../core/host/host.js";import*as t from"../../../core/platform/platform.js";import*as s from"../../legacy/legacy.js";import*as o from"../../lit-html/lit-html.js";import*as r from"../../visual_logging/visual_logging.js";import*as i from"../render_coordinator/render_coordinator.js";import*as n from"../icon_button/icon_button.js";import*as l from"../../../core/i18n/i18n.js";import*as a from"../../../core/common/common.js";const d=new CSSStyleSheet;d.replaceSync(':host{height:100%;display:block;position:relative}.wrapping-container{overflow-y:auto;height:100%}.wrapping-container:not(.show-scrollbar)::-webkit-scrollbar{display:none}table{border-spacing:0;width:100%;height:100%;table-layout:fixed}tr{outline:none}thead tr{height:27px}tbody tr{background-color:var(--override-data-grid-row-background-color,--sys-color-surface)}tbody tr:nth-child(odd){background-color:var(--sys-color-surface1)}tbody tr:hover:not(.selected){background-color:var(--sys-color-state-hover-on-subtle)}tbody tr.selected{background-color:var(--sys-color-neutral-container)}tbody tr.selected:focus-within{background-color:var(--sys-color-tonal-container)}.wrapping-container.striped tr:nth-child(odd):not(.selected):not(.padding-row):not(:hover){background-color:var(--sys-color-surface1)}td,\nth{box-sizing:border-box;padding:1px 4px;border-left:1px solid var(--sys-color-divider);color:var(--sys-color-on-surface);height:var(--table-row-height);user-select:text;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}th{font-weight:normal;text-align:left;border-bottom:1px solid var(--sys-color-divider);position:sticky;top:0;z-index:2;background-color:var(--sys-color-surface1)}td:focus,\nth:focus{outline:var(--sys-color-primary) auto 1px}th:hover.sortable{background-color:var(--sys-color-surface2)}.cell-resize-handle{top:0;height:100%;z-index:3;width:20px;cursor:col-resize;position:absolute}td.firstVisibleColumn,\nth.firstVisibleColumn{border-left-width:0}.hidden{display:none}.filler-row td{height:100%;pointer-events:none;padding:0}.filler-row.empty-table td{padding:1px}tbody tr.selected:focus-within td{border-left-color:var(--sys-color-divider-on-tonal-container)}[aria-sort="ascending"],\n[aria-sort="descending"]{padding-right:20px}[aria-sort="descending"]::after{content:"";width:0;border-left:0.4em solid transparent;border-right:0.4em solid transparent;border-top:0.4em solid var(--sys-color-token-subtle);position:absolute;right:0.5em;top:0.85em}[aria-sort="ascending"]::after{content:"";width:0;border-bottom:0.4em solid var(--sys-color-token-subtle);border-left:0.4em solid transparent;border-right:0.4em solid transparent;position:absolute;right:0.5em;top:0.7em}@media (forced-colors: active){th.sortable{outline:Highlight auto 1px}th:hover.sortable{background-color:Highlight}[aria-sort="ascending"]::after{forced-color-adjust:none;border-bottom:0.4em solid buttonText}[aria-sort="descending"]::after{forced-color-adjust:none;border-top:0.4em solid buttonText}}\n/*# sourceURL=dataGrid.css */\n');class c extends Event{static eventName="columnheaderclick";data;constructor(e,t){super(c.eventName),this.data={column:e,columnIndex:t}}}class h extends Event{static eventName="contextmenucolumnsortclick";data;constructor(e){super(h.eventName),this.data={column:e}}}class u extends Event{static eventName="contextmenuheaderresetclick";constructor(){super(u.eventName)}}class m extends Event{static eventName="newuserfiltertext";data;constructor(e){super(m.eventName,{composed:!0}),this.data={filterText:e}}}class p extends Event{static eventName="cellfocused";data;constructor(e,t){super(p.eventName,{composed:!0}),this.data={cell:e,row:t}}}class g extends Event{static eventName="rowmouseenter";data;constructor(e){super(g.eventName,{composed:!0}),this.data={row:e}}}class f extends Event{static eventName="rowmouseleave";data;constructor(e){super(f.eventName,{composed:!0}),this.data={row:e}}}var w=Object.freeze({__proto__:null,ColumnHeaderClickEvent:c,ContextMenuColumnSortClickEvent:h,ContextMenuHeaderResetClickEvent:u,NewUserFilterTextEvent:m,BodyCellFocusedEvent:p,RowMouseEnterEvent:g,RowMouseLeaveEvent:f});function b(e,t){const s=!t.visible,o=e.data.columns.map((e=>(e===t&&(e.visible=s),e)));e.data={...e.data,columns:o}}function v(e,t){const{columns:s}=e.data;for(const o of s)o.hideable&&t.defaultSection().appendCheckboxItem(o.title,(()=>{b(e,o)}),{checked:o.visible,jslogContext:o.id})}function C(e,t){const s=e.data.columns.filter((e=>!0===e.sortable));if(s.length>0)for(const o of s)t.defaultSection().appendItem(o.title,(()=>{e.dispatchEvent(new h(o))}),{jslogContext:o.id})}const x=e=>o.html`${e}`;var S=Object.freeze({__proto__:null,primitiveRenderer:x,codeBlockRenderer:e=>{if(!e)return o.nothing;const t=String(e);return o.html`<code>${t}</code>`},iconRenderer:e=>e?o.html`<div style="display: flex; justify-content: center;">${e}</div>`:o.nothing});function y(e){return JSON.stringify(e.map((e=>e.value instanceof n.Icon.Icon?null:e.value))).toLowerCase()}function R(e,t){const s=e.cells.find((e=>e.columnId===t));if(void 0===s)throw new Error(`Found a row that was missing an entry for column ${t}.`);return s}function M(e){return e.renderer?e.renderer(e.value):x(e.value)}function I(e,t){const s=e.filter((e=>e.visible)).reduce(((e,t)=>e+t.widthWeighting),0),o=e.find((e=>e.id===t));if(!o)throw new Error(`Could not find column with ID ${t}`);if(o.widthWeighting<1)throw new Error(`Error with column ${t}: width weightings must be >= 1.`);return o.visible?Math.round(o.widthWeighting/s*100):0}function $(e){const{key:s,currentFocusedCell:o,columns:r,rows:i}=e,[n,l]=o;switch(s){case"ArrowLeft":{if(n===r.findIndex((e=>e.visible)))return[n,l];let e=n;for(let t=e-1;t>=0;t--){if(r[t].visible){e=t;break}}return[e,l]}case"ArrowRight":{let e=n;for(let t=e+1;t<r.length;t++){if(r[t].visible){e=t;break}}return[e,l]}case"ArrowUp":{const e=r.some((e=>!0===e.sortable))?0:1;if(l===e)return[n,l];let t=l;for(let s=l-1;s>=e;s--){if(0===s){t=0;break}if(!i[s-1].hidden){t=s;break}}return[n,t]}case"ArrowDown":{if(0===l){const e=i.findIndex((e=>!e.hidden));return e>-1?[n,e+1]:[n,l]}let e=l;for(let t=e+1;t<i.length+1;t++){if(!i[t-1].hidden){e=t;break}}return[n,e]}default:return t.assertNever(s,`Unknown arrow key: ${s}`)}}const H=e=>{const{columns:t,rows:s}=e,o=t.some((e=>!0===e.sortable))?0:s.findIndex((e=>!e.hidden))+1;return[t.findIndex((e=>e.visible)),o]},T=e=>e.length<25?e:e.substr(0,20)+"…";var k=Object.freeze({__proto__:null,getStringifiedCellValues:y,getRowEntryForColumnId:R,renderCellValue:M,calculateColumnWidthPercentageFromWeighting:I,handleArrowKeyNavigation:$,calculateFirstFocusableCell:H,getCellTitleFromCellContent:T});const D=i.RenderCoordinator.RenderCoordinator.instance(),O={sortBy:"Sort By",resetColumns:"Reset Columns",headerOptions:"Header Options",enterToSort:"Column sort state: {PH1}. Press enter to apply sorting filter",sortAsc:"ascending",sortDesc:"descending",sortNone:"none"},F=l.i18n.registerUIStrings("ui/components/data_grid/DataGrid.ts",O),z=l.i18n.getLocalizedString.bind(void 0,F),E=new Set([" ","Enter"]),A=20;class L extends HTMLElement{static litTagName=o.literal`devtools-data-grid`;#e=this.attachShadow({mode:"open"});#t=[];#s=[];#o=null;#r=!1;#i="NOT_SCROLLED";#n=void 0;#l=void 0;#a=10;#d=!1;#c=!1;#h=null;#u=new WeakMap;#m=new ResizeObserver((()=>{this.#p()}));#g=this.#f.bind(this);#w=[0,1];#b=null;#v=!1;#C=!1;#x=!1;connectedCallback(){this.#e.adoptedStyleSheets=[d],this.style.setProperty("--table-row-height","20px"),this.#S()}get data(){return{columns:this.#t,rows:this.#s,activeSort:this.#o,contextMenus:this.#n,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#d,striped:this.#c}}set data(e){if(this.#t=e.columns,this.#s=e.rows,this.#s.forEach(((e,t)=>{this.#u.set(e,t)})),this.#o=e.activeSort,this.#n=e.contextMenus,this.#l=e.label,this.#d=e.showScrollbar,this.#c=e.striped,this.#v||(this.#w=H({columns:this.#t,rows:this.#s})),void 0!==e.paddingRowsCount&&(this.#a=e.paddingRowsCount),this.#v&&this.#y()){const[e,t]=this.#R(),s=e>this.#t.length,o=t>this.#s.length;(s||o)&&(this.#b=[s?this.#t.length:e,o?this.#s.length:t])}this.#S()}#M(){return"SCROLLED_TO_BOTTOM"===this.#i||!this.#C&&"MANUAL_SCROLL_NOT_BOTTOM"!==this.#i}#I(){if(!1===this.#v||!this.#M())return;const e=this.#e.querySelector(".wrapping-container");e&&D.scroll((()=>{const t=e.scrollHeight;e.scrollTo(0,t)}))}#$(){this.#v||this.#m.observe(this.#e.host)}#y(){return null!==this.#b}#H(){if(!this.#b)return null;const[e,t]=this.#b;return this.#e.querySelector(`[data-row-index="${t}"][data-col-index="${e}"]`)}#T(e){e.focus()}#k([e,t]){if(this.#C=!0,this.#b&&this.#b[0]===e&&this.#b[1]===t)return;this.#b=[e,t],this.#S();const o=this.#H();if(o&&(this.#T(o),0===t&&this.#t[e].sortable)){const t=this.#D(this.#t[e]);s.ARIAUtils.alert(z(O.enterToSort,{PH1:t||""}))}}#D(e){switch(this.#O(e)){case"ascending":return O.sortAsc;case"descending":return O.sortDesc;case"none":return O.sortNone}}#F(e){const s=e.key;if(!this.#b)return;if(E.has(s)){const[e,t]=this.#b,s=this.#t[e];0===t&&s&&s.sortable&&this.#z(s,e)}if(!t.KeyboardUtilities.keyIsArrowKey(s))return;const o=$({key:s,currentFocusedCell:this.#b,columns:this.#t,rows:this.#s});e.preventDefault(),this.#k(o)}#z(e,t){this.dispatchEvent(new c(e,t))}#O(e){return!e.sortable||this.#o&&this.#o.columnId===e.id?this.#o&&this.#o.columnId===e.id?"ASC"===this.#o.direction?"ascending":"descending":void 0:"none"}#E(e){const t=this.#t.map(((e,t)=>{if(!e.visible)return o.nothing;const s=o.Directives.classMap({firstVisibleColumn:0===t});return o.html`<td aria-hidden="true" class=${s} data-filler-row-column-index=${t}></td>`})),s=o.Directives.classMap({"filler-row":!0,"padding-row":!0,"empty-table":0===e});return o.html`<tr aria-hidden="true" class=${s}>${t}</tr>`}#A(){this.#h&&(this.#h.documentForCursorChange.body.style.cursor=this.#h.cursorToRestore,this.#h=null,this.#p())}#L(t){if(1!==t.buttons||e.Platform.isMac()&&t.ctrlKey)return;t.preventDefault();const s=t.target;if(!s)return;const o=s.dataset.columnIndex;if(!o)return;const r=globalThis.parseInt(o,10),i=this.#t.findIndex(((e,t)=>t>r&&!0===e.visible)),n=this.#e.querySelector(`td[data-filler-row-column-index="${r}"]`),l=this.#e.querySelector(`td[data-filler-row-column-index="${i}"]`);if(!n||!l)return;const a=this.#e.querySelector(`col[data-col-column-index="${r}"]`),d=this.#e.querySelector(`col[data-col-column-index="${i}"]`);if(!a||!d)return;const c=t.target.ownerDocument;c&&(this.#h={leftCellCol:a,rightCellCol:d,leftCellColInitialPercentageWidth:globalThis.parseInt(a.style.width,10),rightCellColInitialPercentageWidth:globalThis.parseInt(d.style.width,10),initialLeftCellWidth:n.clientWidth,initialRightCellWidth:l.clientWidth,initialMouseX:t.x,documentForCursorChange:c,cursorToRestore:s.style.cursor},c.body.style.cursor="col-resize",s.setPointerCapture(t.pointerId),s.addEventListener("pointermove",this.#g))}#f(e){if(e.preventDefault(),!this.#h)return;const s=this.#h.leftCellColInitialPercentageWidth+this.#h.rightCellColInitialPercentageWidth-10,o=e.x-this.#h.initialMouseX,r=Math.abs(o)/(this.#h.initialLeftCellWidth+this.#h.initialRightCellWidth)*100;let i,n;o>0?(i=t.NumberUtilities.clamp(this.#h.leftCellColInitialPercentageWidth+r,10,s),n=t.NumberUtilities.clamp(this.#h.rightCellColInitialPercentageWidth-r,10,s)):o<0&&(i=t.NumberUtilities.clamp(this.#h.leftCellColInitialPercentageWidth-r,10,s),n=t.NumberUtilities.clamp(this.#h.rightCellColInitialPercentageWidth+r,10,s)),i&&n&&(this.#h.leftCellCol.style.width=i.toFixed(2)+"%",this.#h.rightCellCol.style.width=n.toFixed(2)+"%")}#U(e){e.preventDefault();const t=e.target;t&&(t.releasePointerCapture(e.pointerId),t.removeEventListener("pointermove",this.#g),this.#A())}#_(e,t){const[s]=t;return s!==this.#N()&&e.visible?o.html`<span class="cell-resize-handle"
     @pointerdown=${this.#L}
     @pointerup=${this.#U}
     data-column-index=${s}
    ></span>`:o.nothing}#N(){let e=this.#t.length-1;for(;e>-1;e--){if(this.#t[e].visible)break}return e}#P(e){if(2!==e.button&&-1!==e.button)return;const t=new s.ContextMenu.ContextMenu(e);v(this,t);C(this,t.defaultSection().appendSubMenuItem(z(O.sortBy),!1,"sort-by")),t.defaultSection().appendItem(z(O.resetColumns),(()=>{this.dispatchEvent(new u)}),{jslogContext:"reset-columns"}),this.#n&&this.#n.headerRow&&this.#n.headerRow(t,this.#t),t.show()}#W(e){if(2!==e.button&&-1!==e.button)return;if(!(e.target&&e.target instanceof HTMLElement))return;const t=e.target.dataset.rowIndex;if(!t)return;const o=parseInt(t,10),r=this.#s[o-1],i=new s.ContextMenu.ContextMenu(e);C(this,i.defaultSection().appendSubMenuItem(z(O.sortBy),!1,"sort-by"));const n=i.defaultSection().appendSubMenuItem(z(O.headerOptions),!1,"header-options");v(this,n),n.defaultSection().appendItem(z(O.resetColumns),(()=>{this.dispatchEvent(new u)}),{jslogContext:"reset-columns"}),this.#n&&this.#n.bodyRow&&this.#n.bodyRow(i,this.#t,r),i.show()}#j(e){const t=e.target;if(!t)return;const s=Math.round(t.scrollTop+t.clientHeight)===Math.round(t.scrollHeight);this.#i=s?"SCROLLED_TO_BOTTOM":"MANUAL_SCROLL_NOT_BOTTOM",this.#S()}#p(){return D.read((()=>{const e=this.#e.querySelectorAll("th:not(.hidden)"),t=this.#e.querySelectorAll(".cell-resize-handle");this.#e.querySelector("table")&&e.forEach((async(e,s)=>{const o=e.clientWidth,r=e.offsetLeft;if(t[s]){const e=t[s].clientWidth;D.write((()=>{t[s].style.left=r+o-e+"px"}))}}))}))}#G(){return D.read((()=>{const e=this.#e.querySelector(".wrapping-container");let t=0,s=window.innerHeight;e&&(t=e.scrollTop,s=e.clientHeight);const o=A*this.#a;let r=Math.floor((t-o)/A),i=Math.ceil((t+s+o)/A);return r=Math.max(0,r),i=Math.min(this.#s.filter((e=>!e.hidden)).length,i),{topVisibleRow:r,bottomVisibleRow:i}}))}#B(){this.#C=!1}#R(){return this.#b||this.#w}async#S(){if(!this.isConnected)return;if(this.#r)return void(this.#x=!0);this.#r=!0;const{topVisibleRow:e,bottomVisibleRow:t}=await this.#G(),s=this.#s.filter((e=>!e.hidden)),i=s.filter(((s,o)=>o>=e&&o<=t)),n=this.#t.findIndex((e=>e.visible)),l=this.#t.some((e=>!0===e.sortable)),a={"wrapping-container":!0,"show-scrollbar":!0===this.#d,striped:!0===this.#c};await D.write((()=>{o.render(o.html`
      ${this.#t.map(((e,t)=>this.#_(e,[t,0])))}
      <div class=${o.Directives.classMap(a)} @scroll=${this.#j} @focusout=${this.#B}>
        <table
          aria-label=${o.Directives.ifDefined(this.#l)}
          aria-rowcount=${this.#s.length}
          aria-colcount=${this.#t.length}
          @keydown=${this.#F}
        >
          <colgroup>
            ${this.#t.map(((e,t)=>{const s=`width: ${I(this.#t,e.id)}%`;return e.visible?o.html`<col style=${s} data-col-column-index=${t}>`:o.nothing}))}
          </colgroup>
          <thead>
            <tr @contextmenu=${this.#P}>
              ${this.#t.map(((e,t)=>{const s=o.Directives.classMap({hidden:!e.visible,firstVisibleColumn:t===n,sortable:l}),i=this.#R(),a=l&&t===i[0]&&0===i[1];return o.html`<th class=${s}
                  jslog=${r.tableHeader().track({click:l}).context(e.id)}
                  style=${o.Directives.ifDefined(e.styles?o.Directives.styleMap(e.styles):void 0)}
                  data-grid-header-cell=${e.id}
                  @focus=${()=>{this.#k([t,0])}}
                  @click=${()=>{this.#z(e,t)}}
                  title=${e.title}
                  aria-sort=${o.Directives.ifDefined(this.#O(e))}
                  aria-colindex=${t+1}
                  data-row-index='0'
                  data-col-index=${t}
                  tabindex=${o.Directives.ifDefined(l?a?"0":"-1":void 0)}
                >${e.titleElement||e.title}</th>`}))}
            </tr>
          </thead>
          <tbody>
            <tr class="filler-row-top padding-row" style=${o.Directives.styleMap({height:e*A+"px"})} aria-hidden="true"></tr>
            ${o.Directives.repeat(i,(e=>this.#u.get(e)),(e=>{const t=this.#u.get(e);if(void 0===t)throw new Error("Trying to render a row that has no index in the rowIndexMap");const s=this.#R(),i=t+1,l=!!this.#b&&i===this.#b[1],a=o.Directives.classMap({selected:l,hidden:!0===e.hidden});return o.html`
                <tr
                  aria-rowindex=${t+1}
                  class=${a}
                  style=${o.Directives.ifDefined(e.styles?o.Directives.styleMap(e.styles):void 0)}
                  @contextmenu=${this.#W}
                  @mouseenter=${()=>{this.dispatchEvent(new g(e))}}
                  @mouseleave=${()=>{this.dispatchEvent(new f(e))}}
                >${this.#t.map(((t,l)=>{const a=R(e,t.id),d=o.Directives.classMap({hidden:!t.visible,firstVisibleColumn:l===n}),c=l===s[0]&&i===s[1],h=t.visible?M(a):null;return o.html`<td
                    class=${d}
                    jslog=${r.tableCell().track({click:!0,resize:!0})}).context(col.id)}
                    style=${o.Directives.ifDefined(t.styles?o.Directives.styleMap(t.styles):void 0)}
                    tabindex=${c?"0":"-1"}
                    aria-colindex=${l+1}
                    title=${a.title||T(String(a.value))}
                    data-row-index=${i}
                    data-col-index=${l}
                    data-grid-value-cell-for-column=${t.id}
                    @focus=${()=>{this.#k([l,i]),this.dispatchEvent(new p(a,e))}}
                  >${h}</td>`}))}
              `}))}
            ${this.#E(i.length)}
            <tr class="filler-row-bottom padding-row" style=${o.Directives.styleMap({height:Math.max(0,s.length-t)*A+"px"})} aria-hidden="true"></tr>
          </tbody>
        </table>
      </div>
      `,this.#e,{host:this})}));const d=this.#R()[1],c=this.#H();this.#C&&d>0&&c&&this.#T(c),this.#I(),this.#$(),this.#v&&this.#p(),this.#r=!1,this.#v=!0,this.#x&&(this.#x=!1,this.#S())}}customElements.define("devtools-data-grid",L);var U=Object.freeze({__proto__:null,DataGrid:L});let _,N;function P(e){e.style.position="absolute",e.style.left="-999em",e.style.width="100em",e.style.overflow="hidden"}let W=!1;function j(e){(function(){if(!_){const e=document.body.createChild("div");P(e),e.setAttribute("role","alert"),e.setAttribute("aria-atomic","true"),_=e}if(!N){const e=document.body.createChild("div");P(e),e.setAttribute("role","alert"),e.setAttribute("aria-atomic","true"),N=e}return W=!W,W?(N.textContent="",_):(_.textContent="",N)}()).textContent=t.StringUtilities.trimEndWithMaxLength(e,1e4)}const G=new CSSStyleSheet;G.replaceSync(":host{display:block;height:100%;overflow:hidden}\n/*# sourceURL=dataGridController.css */\n");const B={sortInAscendingOrder:"{PH1} sorted in ascending order",sortInDescendingOrder:"{PH1} sorted in descending order",sortingCanceled:"{PH1} sorting canceled"},q=l.i18n.registerUIStrings("ui/components/data_grid/DataGridController.ts",B),V=l.i18n.getLocalizedString.bind(void 0,q);class K extends HTMLElement{static litTagName=o.literal`devtools-data-grid-controller`;#e=this.attachShadow({mode:"open"});#v=!1;#t=[];#s=[];#n=void 0;#l=void 0;#d=!1;#c=!1;#q=[];#V=[];#o=null;#K=[];#a;connectedCallback(){this.#e.adoptedStyleSheets=[G]}get data(){return{columns:this.#q,rows:this.#V,filters:this.#K,contextMenus:this.#n,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#d,striped:this.#c}}set data(e){this.#q=e.columns,this.#V=e.rows,this.#n=e.contextMenus,this.#K=e.filters||[],this.#n=e.contextMenus,this.#l=e.label,this.#d=e.showScrollbar,this.#c=e.striped,this.#t=[...this.#q],this.#s=this.#X(e.rows,this.#K),!this.#v&&e.initialSort&&(this.#o=e.initialSort),this.#o&&this.#J(this.#o),this.#a=e.paddingRowsCount,this.#S()}#Q(e,t,s){let o=!1;const{key:r,text:i,negative:n,regex:l}=t;let a;return a=y(r?[R(e,r)]:e.cells.filter((e=>s.has(e.columnId)))),l?o=l.test(a):i&&(o=a.includes(i.toLowerCase())),n?!o:o}#X(e,t){if(0===t.length)return[...e];const s=new Set(this.#t.filter((e=>e.visible)).map((e=>e.id)));return e.map((e=>{let o=!0;for(const r of t){if(!this.#Q(e,r,s)){o=!1;break}}return{...e,hidden:!o}}))}#J(e){const{columnId:t,direction:s}=e;this.#s.sort(((e,o)=>{const r=R(e,t),i=R(o,t),n="number"==typeof r.value?r.value:String(r.value).toUpperCase(),l="number"==typeof i.value?i.value:String(i.value).toUpperCase();return n<l?"ASC"===s?-1:1:n>l?"ASC"===s?1:-1:0})),this.#S()}#z(e){const{column:t}=e.data;t.sortable&&this.#Y(t)}#Y(e){if(this.#o&&this.#o.columnId===e.id){const{columnId:e,direction:t}=this.#o;this.#o="DESC"===t?null:{columnId:e,direction:"DESC"}}else this.#o={columnId:e.id,direction:"ASC"};const t=e.title;this.#o?(this.#J(this.#o),j("ASC"===this.#o.direction?V(B.sortInAscendingOrder,{PH1:t||""}):V(B.sortInDescendingOrder,{PH1:t||""}))):(this.#s=this.#X(this.#V,this.#K),this.#S(),j(V(B.sortingCanceled,{PH1:t||""})))}#Z(e){this.#Y(e.data.column)}#ee(){this.#o=null,this.#s=[...this.#V],this.#S()}#S(){o.render(o.html`
      <${L.litTagName} .data=${{columns:this.#t,rows:this.#s,activeSort:this.#o,contextMenus:this.#n,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#d,striped:this.#c}}
        @columnheaderclick=${this.#z}
        @contextmenucolumnsortclick=${this.#Z}
        @contextmenuheaderresetclick=${this.#ee}
     ></${L.litTagName}>
    `,this.#e,{host:this}),this.#v=!0}}customElements.define("devtools-data-grid-controller",K);var X=Object.freeze({__proto__:null,DataGridController:K});class J extends s.Widget.VBox{dataGrid;#te;#se;constructor(e){super(!0,!0),this.dataGrid=new K,this.dataGrid.data=e,this.#se=e,this.contentElement.appendChild(this.dataGrid),this.#te=new a.Throttler.Throttler(0)}data(){return this.#se}update(e){this.#se=e,this.#te.schedule((async()=>{this.dataGrid.data=e}))}}var Q=Object.freeze({__proto__:null,DataGridControllerIntegrator:J});export{U as DataGrid,X as DataGridController,Q as DataGridControllerIntegrator,w as DataGridEvents,S as DataGridRenderers,k as DataGridUtils};
