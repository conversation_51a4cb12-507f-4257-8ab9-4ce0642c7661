{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "NativeScreen", "InnerScreen", "_reactNative", "require", "_react", "_interopRequireDefault", "_core", "obj", "__esModule", "_extends", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "View", "React", "Component", "render", "active", "activityState", "style", "enabled", "screensEnabled", "rest", "props", "undefined", "createElement", "hidden", "display", "Screen", "Animated", "createAnimatedComponent", "createContext", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.web.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,YAAA,GAAAJ,OAAA,CAAAK,WAAA;AAGb,IAAAC,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAAyC,SAAAE,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAT,OAAA,EAAAS,GAAA;AAAA,SAAAE,SAAA,IAAAA,QAAA,GAAAf,MAAA,CAAAgB,MAAA,GAAAhB,MAAA,CAAAgB,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAtB,MAAA,CAAAwB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAH,QAAA,CAAAY,KAAA,OAAAP,SAAA;AAElC,MAAMb,WAAW,GAAAL,OAAA,CAAAK,WAAA,GAAGqB,iBAAI;;AAE/B;AACA;AACA;AACO,MAAMtB,YAAY,SAASuB,cAAK,CAACC,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;MAC1B,GAAGC;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIH,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKO,SAAS,IAAIN,aAAa,KAAKM,SAAS,EAAE;QACvDN,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MACA,oBACEtB,MAAA,CAAAN,OAAA,CAAAoC,aAAA,CAAChC,YAAA,CAAAoB;MACC;MAAA,EAAAb,QAAA;QACA0B,MAAM,EAAER,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEQ,OAAO,EAAET,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DI,IAAI,CACT,CAAC;IAEN;IAEA,oBAAO3B,MAAA,CAAAN,OAAA,CAAAoC,aAAA,CAAChC,YAAA,CAAAoB,IAAI,EAAKS,IAAO,CAAC;EAC3B;AACF;AAACnC,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAED,MAAMqC,MAAM,GAAGC,qBAAQ,CAACC,uBAAuB,CAACvC,YAAY,CAAC;AAEtD,MAAMD,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAGwB,cAAK,CAACiB,aAAa,CAACH,MAAM,CAAC;AAAC,IAAAI,QAAA,GAAA7C,OAAA,CAAAE,OAAA,GAE1CuC,MAAM"}