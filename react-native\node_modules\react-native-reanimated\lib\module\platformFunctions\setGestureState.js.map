{"version": 3, "names": ["isChromeDebugger", "isJest", "shouldBeUseWeb", "logger", "setGestureState", "setGestureStateNative", "handlerTag", "newState", "_WORKLET", "warn", "global", "_setGestureState", "setGestureStateJest", "setGestureStateChromeDebugger", "setGestureStateDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/setGestureState.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgB,EAAEC,MAAM,EAAEC,cAAc,QAAQ,uBAAoB;AAC7E,SAASC,MAAM,QAAQ,oBAAW;AAIlC,OAAO,IAAIC,eAAgC;AAE3C,SAASC,qBAAqBA,CAACC,UAAkB,EAAEC,QAAgB,EAAE;EACnE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbL,MAAM,CAACM,IAAI,CAAC,0DAA0D,CAAC;IACvE;EACF;EACAC,MAAM,CAACC,gBAAgB,CAACL,UAAU,EAAEC,QAAQ,CAAC;AAC/C;AAEA,SAASK,mBAAmBA,CAAA,EAAG;EAC7BT,MAAM,CAACM,IAAI,CAAC,6CAA6C,CAAC;AAC5D;AAEA,SAASI,6BAA6BA,CAAA,EAAG;EACvCV,MAAM,CAACM,IAAI,CAAC,wDAAwD,CAAC;AACvE;AAEA,SAASK,sBAAsBA,CAAA,EAAG;EAChCX,MAAM,CAACM,IAAI,CAAC,2DAA2D,CAAC;AAC1E;AAEA,IAAI,CAACP,cAAc,CAAC,CAAC,EAAE;EACrBE,eAAe,GAAGC,qBAAqB;AACzC,CAAC,MAAM,IAAIJ,MAAM,CAAC,CAAC,EAAE;EACnBG,eAAe,GAAGQ,mBAAmB;AACvC,CAAC,MAAM,IAAIZ,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,eAAe,GAAGS,6BAA6B;AACjD,CAAC,MAAM;EACLT,eAAe,GAAGU,sBAAsB;AAC1C", "ignoreList": []}