{"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaInsetsContext", "SafeAreaProvider", "width", "height", "get", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "_ref", "children", "style", "createElement", "Consumer", "styles", "container", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/SafeAreaProviderCompat.tsx"], "mappings": "AAAA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EACVC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAOvC,MAAM;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGR,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBT,QAAQ,CAACU,EAAE,KAAK,KAAK,IAAIP,oBAAoB,IAAI,IAAI,GACjD;EACEQ,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK;IAAEC;EAAO,CAAC;EACpCO,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDf,oBAAoB;AAE1B,eAAe,SAASgB,sBAAsBA,CAAAC,IAAA,EAA6B;EAAA,IAA5B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAF,IAAA;EACvE,oBACEtB,KAAA,CAAAyB,aAAA,CAACnB,qBAAqB,CAACoB,QAAQ,QAC5BV,MAAM,IAAI;IACT,IAAIA,MAAM,EAAE;MACV;MACA;MACA;MACA,oBAAOhB,KAAA,CAAAyB,aAAA,CAACrB,IAAI;QAACoB,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAEJ,KAAK;MAAE,GAAED,QAAe,CAAC;IAClE;IAEA,oBACEvB,KAAA,CAAAyB,aAAA,CAAClB,gBAAgB;MAACI,cAAc,EAAEA,cAAe;MAACa,KAAK,EAAEA;IAAM,GAC5DD,QACe,CAAC;EAEvB,CAC8B,CAAC;AAErC;AAEAF,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,MAAMgB,MAAM,GAAGxB,UAAU,CAAC0B,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}