{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/DebugserverClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport { Socket } from 'net';\nimport * as path from 'path';\n\nimport { ServiceClient } from './ServiceClient';\nimport { GDBProtocolClient } from '../protocol/GDBProtocol';\n\nconst debug = Debug('expo:apple-device:client:debugserver');\n\nexport class DebugserverClient extends ServiceClient<GDBProtocolClient> {\n  constructor(public socket: Socket) {\n    super(socket, new GDBProtocolClient(socket));\n  }\n\n  async setMaxPacketSize(size: number) {\n    return this.sendCommand('QSetMaxPacketSize:', [size.toString()]);\n  }\n\n  async setWorkingDir(workingDir: string) {\n    return this.sendCommand('QSetWorkingDir:', [workingDir]);\n  }\n\n  async checkLaunchSuccess() {\n    return this.sendCommand('qLaunchSuccess', []);\n  }\n\n  async attachByName(name: string) {\n    const hexName = Buffer.from(name).toString('hex');\n    return this.sendCommand(`vAttachName;${hexName}`, []);\n  }\n\n  async continue() {\n    return this.sendCommand('c', []);\n  }\n\n  halt() {\n    // ^C\n    debug('Sending ^C to debugserver');\n    return this.protocolClient.socket.write('\\u0003');\n  }\n\n  async kill() {\n    debug(`kill`);\n    const msg: any = { cmd: 'k', args: [] };\n    return this.protocolClient.sendMessage(msg, (resp: string, resolve: any) => {\n      debug(`kill:response: ${resp}`);\n      this.protocolClient.socket.write('+');\n      const parts = resp.split(';');\n      for (const part of parts) {\n        if (part.includes('description')) {\n          // description:{hex encoded message like: \"Terminated with signal 9\"}\n          resolve(Buffer.from(part.split(':')[1], 'hex').toString('ascii'));\n        }\n      }\n    });\n  }\n\n  // TODO support app args\n  // https://sourceware.org/gdb/onlinedocs/gdb/Packets.html#Packets\n  // A arglen,argnum,arg,\n  async launchApp(appPath: string, executableName: string) {\n    const fullPath = path.join(appPath, executableName);\n    const hexAppPath = Buffer.from(fullPath).toString('hex');\n    const appCommand = `A${hexAppPath.length},0,${hexAppPath}`;\n    return this.sendCommand(appCommand, []);\n  }\n\n  async sendCommand(cmd: string, args: string[]) {\n    const msg = { cmd, args };\n    debug(`Sending command: ${cmd}, args: ${args}`);\n    const resp = await this.protocolClient.sendMessage(msg);\n    // we need to ACK as well\n    this.protocolClient.socket.write('+');\n    return resp;\n  }\n}\n"], "names": ["DebugserverClient", "debug", "Debug", "ServiceClient", "constructor", "socket", "GDBProtocolClient", "setMaxPacketSize", "size", "sendCommand", "toString", "setWorkingDir", "workingDir", "checkLaunchSuccess", "attachByName", "name", "hexName", "<PERSON><PERSON><PERSON>", "from", "continue", "halt", "protocolClient", "write", "kill", "msg", "cmd", "args", "sendMessage", "resp", "resolve", "parts", "split", "part", "includes", "launchApp", "appPath", "executableName", "fullPath", "path", "join", "hexAppPath", "appCommand", "length"], "mappings": "AAAA;;;;;;CAMC;;;;+BAUYA;;;eAAAA;;;;gEATK;;;;;;;iEAEI;;;;;;+BAEQ;6BACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAEb,MAAMF,0BAA0BG,4BAAa;IAClDC,YAAY,AAAOC,MAAc,CAAE;QACjC,KAAK,CAACA,QAAQ,IAAIC,8BAAiB,CAACD,eADnBA,SAAAA;IAEnB;IAEA,MAAME,iBAAiBC,IAAY,EAAE;QACnC,OAAO,IAAI,CAACC,WAAW,CAAC,sBAAsB;YAACD,KAAKE,QAAQ;SAAG;IACjE;IAEA,MAAMC,cAAcC,UAAkB,EAAE;QACtC,OAAO,IAAI,CAACH,WAAW,CAAC,mBAAmB;YAACG;SAAW;IACzD;IAEA,MAAMC,qBAAqB;QACzB,OAAO,IAAI,CAACJ,WAAW,CAAC,kBAAkB,EAAE;IAC9C;IAEA,MAAMK,aAAaC,IAAY,EAAE;QAC/B,MAAMC,UAAUC,OAAOC,IAAI,CAACH,MAAML,QAAQ,CAAC;QAC3C,OAAO,IAAI,CAACD,WAAW,CAAC,CAAC,YAAY,EAAEO,SAAS,EAAE,EAAE;IACtD;IAEA,MAAMG,WAAW;QACf,OAAO,IAAI,CAACV,WAAW,CAAC,KAAK,EAAE;IACjC;IAEAW,OAAO;QACL,KAAK;QACLnB,MAAM;QACN,OAAO,IAAI,CAACoB,cAAc,CAAChB,MAAM,CAACiB,KAAK,CAAC;IAC1C;IAEA,MAAMC,OAAO;QACXtB,MAAM,CAAC,IAAI,CAAC;QACZ,MAAMuB,MAAW;YAAEC,KAAK;YAAKC,MAAM,EAAE;QAAC;QACtC,OAAO,IAAI,CAACL,cAAc,CAACM,WAAW,CAACH,KAAK,CAACI,MAAcC;YACzD5B,MAAM,CAAC,eAAe,EAAE2B,MAAM;YAC9B,IAAI,CAACP,cAAc,CAAChB,MAAM,CAACiB,KAAK,CAAC;YACjC,MAAMQ,QAAQF,KAAKG,KAAK,CAAC;YACzB,KAAK,MAAMC,QAAQF,MAAO;gBACxB,IAAIE,KAAKC,QAAQ,CAAC,gBAAgB;oBAChC,qEAAqE;oBACrEJ,QAAQZ,OAAOC,IAAI,CAACc,KAAKD,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,OAAOrB,QAAQ,CAAC;gBAC1D;YACF;QACF;IACF;IAEA,wBAAwB;IACxB,iEAAiE;IACjE,uBAAuB;IACvB,MAAMwB,UAAUC,OAAe,EAAEC,cAAsB,EAAE;QACvD,MAAMC,WAAWC,QAAKC,IAAI,CAACJ,SAASC;QACpC,MAAMI,aAAavB,OAAOC,IAAI,CAACmB,UAAU3B,QAAQ,CAAC;QAClD,MAAM+B,aAAa,CAAC,CAAC,EAAED,WAAWE,MAAM,CAAC,GAAG,EAAEF,YAAY;QAC1D,OAAO,IAAI,CAAC/B,WAAW,CAACgC,YAAY,EAAE;IACxC;IAEA,MAAMhC,YAAYgB,GAAW,EAAEC,IAAc,EAAE;QAC7C,MAAMF,MAAM;YAAEC;YAAKC;QAAK;QACxBzB,MAAM,CAAC,iBAAiB,EAAEwB,IAAI,QAAQ,EAAEC,MAAM;QAC9C,MAAME,OAAO,MAAM,IAAI,CAACP,cAAc,CAACM,WAAW,CAACH;QACnD,yBAAyB;QACzB,IAAI,CAACH,cAAc,CAAChB,MAAM,CAACiB,KAAK,CAAC;QACjC,OAAOM;IACT;AACF"}