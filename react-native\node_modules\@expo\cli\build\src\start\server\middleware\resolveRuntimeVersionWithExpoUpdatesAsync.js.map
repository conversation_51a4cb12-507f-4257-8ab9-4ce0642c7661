{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveRuntimeVersionWithExpoUpdatesAsync.ts"], "sourcesContent": ["import { RuntimePlatform } from './resolvePlatform';\nimport { env } from '../../../utils/env';\nimport {\n  ExpoUpdatesCLIModuleNotFoundError,\n  expoUpdatesCommandAsync,\n} from '../../../utils/expoUpdatesCli';\n\nconst debug = require('debug')('expo:start:server:middleware:resolveRuntimeVersion');\n\nexport async function resolveRuntimeVersionWithExpoUpdatesAsync({\n  projectRoot,\n  platform,\n}: {\n  projectRoot: string;\n  platform: RuntimePlatform;\n}): Promise<string | null> {\n  try {\n    debug('Using expo-updates runtimeversion:resolve CLI for runtime version resolution');\n    const extraArgs = env.EXPO_DEBUG ? ['--debug'] : [];\n    const resolvedRuntimeVersionJSONResult = await expoUpdatesCommandAsync(projectRoot, [\n      'runtimeversion:resolve',\n      '--platform',\n      platform,\n      ...extraArgs,\n    ]);\n    const runtimeVersionResult: { runtimeVersion: string | null } = JSON.parse(\n      resolvedRuntimeVersionJSONResult\n    );\n    debug('runtimeversion:resolve output:');\n    debug(resolvedRuntimeVersionJSONResult);\n\n    return runtimeVersionResult.runtimeVersion ?? null;\n  } catch (e: any) {\n    if (e instanceof ExpoUpdatesCLIModuleNotFoundError) {\n      return null;\n    }\n    throw e;\n  }\n}\n"], "names": ["resolveRuntimeVersionWithExpoUpdatesAsync", "debug", "require", "projectRoot", "platform", "extraArgs", "env", "EXPO_DEBUG", "resolvedRuntimeVersionJSONResult", "expoUpdatesCommandAsync", "runtimeVersionResult", "JSON", "parse", "runtimeVersion", "e", "ExpoUpdatesCLIModuleNotFoundError"], "mappings": ";;;;+BASsBA;;;eAAAA;;;qBARF;gCAIb;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,0CAA0C,EAC9DG,WAAW,EACXC,QAAQ,EAIT;IACC,IAAI;QACFH,MAAM;QACN,MAAMI,YAAYC,QAAG,CAACC,UAAU,GAAG;YAAC;SAAU,GAAG,EAAE;QACnD,MAAMC,mCAAmC,MAAMC,IAAAA,uCAAuB,EAACN,aAAa;YAClF;YACA;YACAC;eACGC;SACJ;QACD,MAAMK,uBAA0DC,KAAKC,KAAK,CACxEJ;QAEFP,MAAM;QACNA,MAAMO;QAEN,OAAOE,qBAAqBG,cAAc,IAAI;IAChD,EAAE,OAAOC,GAAQ;QACf,IAAIA,aAAaC,iDAAiC,EAAE;YAClD,OAAO;QACT;QACA,MAAMD;IACR;AACF"}