<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QR Codes Mobile - AquaTrack Scanner</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .instructions {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .instructions h3 {
            color: #333;
            margin-top: 0;
            font-size: 20px;
        }

        .instructions ol {
            color: #555;
            line-height: 1.6;
        }

        .qr-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .qr-code {
            margin: 20px auto;
            display: block;
            border: 5px solid #f0f0f0;
            border-radius: 15px;
        }

        .client-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
            border-left: 5px solid #4caf50;
        }

        .client-info h4 {
            color: #2e7d32;
            margin-top: 0;
            font-size: 18px;
        }

        .info-item {
            margin: 8px 0;
            font-size: 14px;
            color: #333;
        }

        .qr-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .qr-code-text {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #666;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .status-badge {
            background: #4caf50;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }

        .test-invalid {
            border-left-color: #f44336;
        }

        .test-invalid h4 {
            color: #c62828;
        }

        .test-invalid .status-badge {
            background: #f44336;
        }

        @media (max-width: 600px) {
            .container {
                padding: 10px;
            }
            
            .qr-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Test Scanner QR Mobile</h1>
            <p>QR Codes optimisés pour la détection mobile</p>
        </div>

        <div class="instructions">
            <h3>🎯 Instructions de Test</h3>
            <ol>
                <li><strong>Ouvrez AquaTrack</strong> sur votre téléphone : <code>localhost:3002/technician-dashboard</code></li>
                <li><strong>Cliquez sur "Scanner QR"</strong> dans la navigation</li>
                <li><strong>Autorisez l'accès caméra</strong> si demandé</li>
                <li><strong>Pointez votre caméra</strong> vers l'un des QR codes ci-dessous</li>
                <li><strong>Attendez la détection automatique</strong> (vibration + affichage des infos)</li>
            </ol>
        </div>

        <!-- QR Code Client Valide 1 -->
        <div class="qr-card">
            <div class="qr-title">✅ Client Valide - Benali Fatima</div>
            <canvas id="qr-benali" class="qr-code"></canvas>
            <div class="qr-code-text">QR-2025-0001</div>
            
            <div class="client-info">
                <h4>👤 Benali Fatima</h4>
                <div class="info-item"><strong>🏠 Adresse:</strong> 45 Avenue Hassan II, Sefrou</div>
                <div class="info-item"><strong>📞 Téléphone:</strong> 0647895655</div>
                <div class="info-item"><strong>📧 Email:</strong> <EMAIL></div>
                <div class="info-item"><strong>⚙️ Compteur:</strong> SAGEMCOM SN-123456789</div>
                <div class="status-badge">✅ Client Enregistré</div>
            </div>
        </div>

        <!-- QR Code Client Valide 2 -->
        <div class="qr-card">
            <div class="qr-title">✅ Client Valide - Client1</div>
            <canvas id="qr-client1" class="qr-code"></canvas>
            <div class="qr-code-text">QR123</div>
            
            <div class="client-info">
                <h4>👤 Client1 Client1</h4>
                <div class="info-item"><strong>🏠 Adresse:</strong> Adresse Client1</div>
                <div class="info-item"><strong>📞 Téléphone:</strong> Tel Client1</div>
                <div class="info-item"><strong>⚙️ Compteur:</strong> Sagemcom COM123456</div>
                <div class="status-badge">✅ Client Enregistré</div>
            </div>
        </div>

        <!-- QR Code Client Inexistant -->
        <div class="qr-card">
            <div class="qr-title">❌ Test Client Inexistant</div>
            <canvas id="qr-invalid" class="qr-code"></canvas>
            <div class="qr-code-text">QR-INEXISTANT-999</div>
            
            <div class="client-info test-invalid">
                <h4>❌ Client Non Enregistré</h4>
                <div class="info-item">Ce QR code ne correspond à aucun client dans le système</div>
                <div class="info-item">Message attendu: <strong>"Cette personne n'est pas un client"</strong></div>
                <div class="status-badge">❌ Non Trouvé</div>
            </div>
        </div>
    </div>

    <script>
        // Configuration optimisée pour la détection mobile
        const qrOptions = {
            width: 350,
            height: 350,
            margin: 4,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            },
            errorCorrectionLevel: 'M' // Niveau de correction d'erreur moyen
        };

        // Générer QR Code Benali Fatima
        QRCode.toCanvas(document.getElementById('qr-benali'), 'QR-2025-0001', qrOptions, function (error) {
            if (error) {
                console.error('Erreur QR Benali:', error);
            } else {
                console.log('✅ QR Code Benali Fatima généré (QR-2025-0001)');
            }
        });

        // Générer QR Code Client1
        QRCode.toCanvas(document.getElementById('qr-client1'), 'QR123', qrOptions, function (error) {
            if (error) {
                console.error('Erreur QR Client1:', error);
            } else {
                console.log('✅ QR Code Client1 généré (QR123)');
            }
        });

        // Générer QR Code Inexistant
        QRCode.toCanvas(document.getElementById('qr-invalid'), 'QR-INEXISTANT-999', qrOptions, function (error) {
            if (error) {
                console.error('Erreur QR Inexistant:', error);
            } else {
                console.log('✅ QR Code Inexistant généré (QR-INEXISTANT-999)');
            }
        });

        console.log('🎯 Page de test QR codes mobile chargée');
        console.log('📱 QR Codes disponibles:');
        console.log('   - QR-2025-0001 (Benali Fatima) ✅');
        console.log('   - QR123 (Client1) ✅');
        console.log('   - QR-INEXISTANT-999 (Test erreur) ❌');
    </script>
</body>
</html>
