const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true
}));
app.use(express.json());

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Variable pour savoir si la DB est connectée
let dbConnected = false;

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    
    const clientResult = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${clientResult.rows[0].count} clients trouvés dans la base`);
    
    client.release();
    dbConnected = true;
    return true;
  } catch (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    console.log('🔄 Utilisation des données de test à la place...');
    dbConnected = false;
    return false;
  }
}

// Données de test (fallback)
const testClients = [
  {
    idclient: 1,
    nom: 'Dupont',
    prenom: 'Jean',
    adresse: '123 Rue de la Paix',
    ville: 'Paris',
    tel: '0123456789',
    email: '<EMAIL>',
    ids: 1
  },
  {
    idclient: 2,
    nom: 'Martin',
    prenom: 'Marie',
    adresse: '456 Avenue des Champs',
    ville: 'Lyon',
    tel: '0987654321',
    email: '<EMAIL>',
    ids: 2
  },
  {
    idclient: 3,
    nom: 'Bernard',
    prenom: 'Pierre',
    adresse: '789 Boulevard Saint-Michel',
    ville: 'Marseille',
    tel: '0147258369',
    email: '<EMAIL>',
    ids: 1
  }
];

const testContracts = {
  1: [{ idcontract: 1, codeqr: 'QR-2025-0001', datecontract: '2024-01-15', marquecompteur: 'AquaMeter Pro', numseriecompteur: 'AM1001', posx: 36.8065, posy: 10.1815 }],
  2: [{ idcontract: 2, codeqr: 'QR-2025-0002', datecontract: '2024-02-10', marquecompteur: 'WaterFlow', numseriecompteur: 'WF2001', posx: 36.8075, posy: 10.1825 }],
  3: [{ idcontract: 3, codeqr: 'QR-2025-0003', datecontract: '2024-03-05', marquecompteur: 'HydroMeter', numseriecompteur: 'HM3001', posx: 36.8085, posy: 10.1835 }]
};

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur AquaTrack Hybride (DB + Test)',
    timestamp: new Date().toISOString(),
    database: dbConnected ? 'Facutration (PostgreSQL)' : 'Données de test',
    port: PORT
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Récupération de tous les clients');

    if (dbConnected) {
      // Utiliser la vraie base de données
      const query = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          c.ids,
          s.nom as secteur_nom
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        ORDER BY c.nom, c.prenom
      `;

      const result = await pool.query(query);
      console.log(`✅ ${result.rows.length} clients récupérés depuis la base de données`);
      
      res.json({
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} client(s) trouvé(s) dans la base Facutration`
      });
    } else {
      // Utiliser les données de test
      console.log(`✅ ${testClients.length} clients récupérés depuis les données de test`);
      
      res.json({
        success: true,
        data: testClients,
        count: testClients.length,
        message: `${testClients.length} client(s) trouvé(s) (données de test)`
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    
    // Fallback vers les données de test
    res.json({
      success: true,
      data: testClients,
      count: testClients.length,
      message: `${testClients.length} client(s) trouvé(s) (données de test - fallback)`
    });
  }
});

// Route pour scanner un QR Code
app.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    console.log(`🔍 Scan du QR Code: ${qrCode}`);

    if (dbConnected) {
      // Rechercher dans la vraie base de données
      const contractQuery = `
        SELECT
          cont.idcontract,
          cont.codeqr,
          cont.datecontract,
          cont.marquecompteur,
          cont.numseriecompteur,
          cont.posx,
          cont.posy,
          cont.idclient
        FROM contract cont
        WHERE cont.codeqr = $1
      `;

      const contractResult = await pool.query(contractQuery, [qrCode]);

      if (contractResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'QR Code non trouvé dans la base de données',
          qrCode: qrCode
        });
      }

      const contract = contractResult.rows[0];

      // Récupérer les informations du client
      const clientQuery = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          c.ids
        FROM client c
        WHERE c.idclient = $1
      `;

      const clientResult = await pool.query(clientQuery, [contract.idclient]);
      const client = clientResult.rows[0];

      console.log(`✅ QR Code ${qrCode} trouvé - Client: ${client.nom} ${client.prenom}`);

      res.json({
        success: true,
        data: {
          client: client,
          contract: contract
        },
        message: 'QR Code trouvé avec succès'
      });

    } else {
      // Utiliser les données de test
      let foundClient = null;
      let foundContract = null;

      // Chercher dans les contrats de test
      for (const clientId in testContracts) {
        const contracts = testContracts[clientId];
        const contract = contracts.find(c => c.codeqr === qrCode);
        if (contract) {
          foundContract = contract;
          foundClient = testClients.find(c => c.idclient === parseInt(clientId));
          break;
        }
      }

      if (!foundClient || !foundContract) {
        return res.status(404).json({
          success: false,
          message: 'QR Code non trouvé dans les données de test',
          qrCode: qrCode
        });
      }

      console.log(`✅ QR Code ${qrCode} trouvé (test) - Client: ${foundClient.nom} ${foundClient.prenom}`);

      res.json({
        success: true,
        data: {
          client: foundClient,
          contract: foundContract
        },
        message: 'QR Code trouvé avec succès (données de test)'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors du scan du QR Code:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors du scan du QR Code'
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Récupération des contrats pour le client ID: ${id}`);

    if (dbConnected) {
      const query = `
        SELECT
          cont.idcontract,
          cont.codeqr,
          cont.datecontract,
          cont.marquecompteur,
          cont.numseriecompteur,
          cont.posx,
          cont.posy
        FROM contract cont
        WHERE cont.idclient = $1
        ORDER BY cont.datecontract DESC
      `;

      const result = await pool.query(query, [id]);
      
      res.json({
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} contrat(s) trouvé(s)`
      });
    } else {
      const contracts = testContracts[id] || [];
      
      res.json({
        success: true,
        data: contracts,
        count: contracts.length,
        message: `${contracts.length} contrat(s) trouvé(s) (données de test)`
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Erreur lors de la récupération des contrats'
    });
  }
});

// Démarrage du serveur
async function startServer() {
  console.log('🔄 Démarrage du serveur AquaTrack Hybride...');
  
  // Tester la connexion à la base de données
  await testDatabaseConnection();

  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur AquaTrack démarré sur http://localhost:${PORT}`);
    console.log(`📊 Mode: ${dbConnected ? 'Base de données Facutration' : 'Données de test'}`);
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/scan/:qrCode (scanner QR Code)');
    console.log('  - GET  /api/clients/:id/contracts (contrats d\'un client)');
    console.log('\n✅ Serveur prêt à recevoir les requêtes du frontend');
  });
}

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err.message);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée non gérée:', err.message);
});

// Démarrer le serveur
startServer();
