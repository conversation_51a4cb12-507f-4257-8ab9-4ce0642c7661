const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration', // Nom de votre base de données
  password: '123456', // Mot de passe correct
  port: 5432,
});

// Route pour récupérer tous les clients
router.get('/clients', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les clients...');

    const clientsQuery = `
      SELECT 
        c.idClient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM Client c
      LEFT JOIN Secteur s ON c.idS = s.idS
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(clientsQuery);

    console.log(`✅ ${result.rows.length} clients trouvés`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(client => ({
        idClient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        secteur_nom: client.secteur_nom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer un client par ID
router.get('/clients/:id', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération du client ID: ${id}`);

    const clientQuery = `
      SELECT 
        c.idClient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM Client c
      LEFT JOIN Secteur s ON c.idS = s.idS
      WHERE c.idClient = $1
    `;

    const result = await pool.query(clientQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const client = result.rows[0];

    res.json({
      success: true,
      client: {
        idClient: client.idclient,
        nom: client.nom,
        prenom: client.prenom,
        adresse: client.adresse,
        ville: client.ville,
        tel: client.tel,
        email: client.email,
        secteur_nom: client.secteur_nom
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
router.get('/clients/:id/contracts', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération des contrats pour le client ID: ${id}`);

    const contractsQuery = `
      SELECT 
        cont.idContract,
        cont.codeQr,
        cont.dateContract,
        cont.marqueCompteur,
        cont.numSerieCompteur,
        cont.posX,
        cont.posY
      FROM Contract cont
      WHERE cont.idClient = $1
      ORDER BY cont.dateContract DESC
    `;

    const result = await pool.query(contractsQuery, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(contract => ({
        idContract: contract.idcontract,
        codeQr: contract.codeqr,
        dateContract: contract.datecontract,
        marqueCompteur: contract.marquecompteur,
        numSerieCompteur: contract.numseriecompteur,
        posX: contract.posx,
        posY: contract.posy
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer tous les secteurs
router.get('/secteurs', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les secteurs...');

    const secteursQuery = `
      SELECT idS, nom
      FROM Secteur
      ORDER BY nom
    `;

    const result = await pool.query(secteursQuery);

    console.log(`✅ ${result.rows.length} secteur(s) trouvé(s)`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(secteur => ({
        idS: secteur.ids,
        nom: secteur.nom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour tester la connexion à la base de données
router.get('/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW()');
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

module.exports = router;
