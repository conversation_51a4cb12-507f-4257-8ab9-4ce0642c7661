{"name": "@react-navigation/core", "description": "Core utilities for building navigators", "version": "6.4.17", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "lib/commonjs/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "module": "lib/module/index.js", "types": "lib/typescript/src/index.d.ts", "files": ["src", "lib", "!**/__tests__"], "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/routers": "^6.1.9", "escape-string-regexp": "^4.0.0", "nanoid": "^3.1.23", "query-string": "^7.1.3", "react-is": "^16.13.0", "use-latest-callback": "^0.2.1"}, "devDependencies": {"@testing-library/react-native": "^11.5.0", "@types/react": "~18.0.27", "@types/react-is": "^17.0.0", "del-cli": "^5.0.0", "immer": "^9.0.2", "react": "18.2.0", "react-native-builder-bob": "^0.20.4", "react-test-renderer": "18.1.0", "typescript": "^4.9.4"}, "peerDependencies": {"react": "*"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "a2269aa24bc0c2ceec227dbdcc4fa4c38902396a"}