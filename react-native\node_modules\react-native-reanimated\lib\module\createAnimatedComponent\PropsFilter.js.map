{"version": 3, "names": ["isSharedValue", "isChromeDebugger", "WorkletEventHandler", "initialUpdaterRun", "hasInlineStyles", "getInlineStyle", "flattenArray", "has", "StyleSheet", "dummyListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_initialStyle", "filterNonAnimatedProps", "component", "inputProps", "props", "key", "value", "styleProp", "style", "styles", "processedStyle", "map", "viewDescriptors", "_isFirstRender", "initial", "updater", "flatten", "animatedProp", "animatedProps", "undefined", "Object", "keys", "for<PERSON>ach", "initialValueKey", "workletEventHandler", "eventNames", "length", "eventName", "listeners"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/PropsFilter.tsx"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,aAAa,QAAQ,qBAAkB;AAChD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,mBAAmB,QAAQ,2BAAwB;AAC5D,SAASC,iBAAiB,QAAQ,uBAAc;AAChD,SAASC,eAAe,EAAEC,cAAc,QAAQ,wBAAqB;AAQrE,SAASC,YAAY,EAAEC,GAAG,QAAQ,YAAS;AAC3C,SAASC,UAAU,QAAQ,cAAc;AAEzC,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA;AAAA;AAGF,OAAO,MAAMC,WAAW,CAAyB;EACvCC,aAAa,GAAG,CAAC,CAAC;EAEnBC,sBAAsBA,CAC3BC,SAAyE,EAChD;IACzB,MAAMC,UAAU,GACdD,SAAS,CAACE,KAAsD;IAClE,MAAMA,KAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMC,GAAG,IAAIF,UAAU,EAAE;MAC5B,MAAMG,KAAK,GAAGH,UAAU,CAACE,GAAG,CAAC;MAC7B,IAAIA,GAAG,KAAK,OAAO,EAAE;QACnB,MAAME,SAAS,GAAGJ,UAAU,CAACK,KAAK;QAClC,MAAMC,MAAM,GAAGd,YAAY,CAAaY,SAAS,IAAI,EAAE,CAAC;QACxD,MAAMG,cAA4B,GAAGD,MAAM,CAACE,GAAG,CAAEH,KAAK,IAAK;UACzD,IAAIA,KAAK,IAAIA,KAAK,CAACI,eAAe,EAAE;YAClC;YACA,IAAIV,SAAS,CAACW,cAAc,EAAE;cAC5B,IAAI,CAACb,aAAa,GAAG;gBACnB,GAAGQ,KAAK,CAACM,OAAO,CAACR,KAAK;gBACtB,GAAG,IAAI,CAACN,aAAa;gBACrB,GAAGR,iBAAiB,CAAagB,KAAK,CAACM,OAAO,CAACC,OAAO;cACxD,CAAC;YACH;YACA,OAAO,IAAI,CAACf,aAAa;UAC3B,CAAC,MAAM,IAAIP,eAAe,CAACe,KAAK,CAAC,EAAE;YACjC,OAAOd,cAAc,CAACc,KAAK,EAAEN,SAAS,CAACW,cAAc,CAAC;UACxD,CAAC,MAAM;YACL,OAAOL,KAAK;UACd;QACF,CAAC,CAAC;QACFJ,KAAK,CAACC,GAAG,CAAC,GAAGR,UAAU,CAACmB,OAAO,CAACN,cAAc,CAAC;MACjD,CAAC,MAAM,IAAIL,GAAG,KAAK,eAAe,EAAE;QAClC,MAAMY,YAAY,GAAGd,UAAU,CAACe,aAE/B;QACD,IAAID,YAAY,CAACH,OAAO,KAAKK,SAAS,EAAE;UACtCC,MAAM,CAACC,IAAI,CAACJ,YAAY,CAACH,OAAO,CAACR,KAAK,CAAC,CAACgB,OAAO,CAAEC,eAAe,IAAK;YACnEnB,KAAK,CAACmB,eAAe,CAAC,GACpBN,YAAY,CAACH,OAAO,EAAER,KAAK,CAACiB,eAAe,CAAC;UAChD,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IACL3B,GAAG,CAAC,qBAAqB,EAAEU,KAAK,CAAC,IACjCA,KAAK,CAACkB,mBAAmB,YAAYjC,mBAAmB,EACxD;QACA,IAAIe,KAAK,CAACkB,mBAAmB,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;UACnDpB,KAAK,CAACkB,mBAAmB,CAACC,UAAU,CAACH,OAAO,CAAEK,SAAS,IAAK;YAC1DvB,KAAK,CAACuB,SAAS,CAAC,GAAG/B,GAAG,CAAC,WAAW,EAAEU,KAAK,CAACkB,mBAAmB,CAAC,GAExDlB,KAAK,CAACkB,mBAAmB,CAACI,SAAS,CACnCD,SAAS,CAAC,GACZ7B,aAAa;UACnB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLM,KAAK,CAACC,GAAG,CAAC,GAAGP,aAAa;QAC5B;MACF,CAAC,MAAM,IAAIT,aAAa,CAACiB,KAAK,CAAC,EAAE;QAC/B,IAAIJ,SAAS,CAACW,cAAc,EAAE;UAC5BT,KAAK,CAACC,GAAG,CAAC,GAAGC,KAAK,CAACA,KAAK;QAC1B;MACF,CAAC,MAAM,IAAID,GAAG,KAAK,6BAA6B,IAAI,CAACf,gBAAgB,CAAC,CAAC,EAAE;QACvEc,KAAK,CAACC,GAAG,CAAC,GAAGC,KAAK;MACpB;IACF;IACA,OAAOF,KAAK;EACd;AACF", "ignoreList": []}