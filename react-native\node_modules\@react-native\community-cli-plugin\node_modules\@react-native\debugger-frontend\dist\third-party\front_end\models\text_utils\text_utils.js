import*as t from"../../third_party/codemirror.next/codemirror.next.js";import*as e from"../../core/platform/platform.js";import*as n from"../../core/common/common.js";var r=Object.freeze({__proto__:null,createCssTokenizer:function(){return async function(e,n){const r=await t.cssStreamParser(),s=new t.StringStream(e,4,2),i=r.startState(2);let o=s.pos;for(;!s.eol();){s.start=o;let t=r.token(s,i);"error"===t&&"maybeprop"===i.state&&(t="property");n(s.current(),t),o=s.pos}}}});class s{lineNumber;lineContent;columnNumber;matchLength;constructor(t,e,n,r){this.lineNumber=t,this.lineContent=e,this.columnNumber=n,this.matchLength=r}static comparator(t,e){return t.lineNumber-e.lineNumber||t.columnNumber-e.columnNumber}}const i=function(t,e,n,r,s=!0){return null==t||s&&t.length>1048576?null:"data:"+e+(r?";charset="+r:"")+(n?";base64":"")+","+(t=n?t:encodeURIComponent(t))};var o=Object.freeze({__proto__:null,SearchMatch:s,contentAsDataURL:i,isStreamingContentProvider:function(t){return"requestStreamingContent"in t}});class a{mimeType;charset;#t;#e;constructor(t,e,n,r){this.charset=r||"utf-8",e?this.#t=t:this.#e=t,this.mimeType=n,this.mimeType||(this.mimeType=e?"application/octet-stream":"text/plain")}get base64(){if(void 0===this.#t)throw new Error("Encoding text content as base64 is not supported");return this.#t}get text(){if(void 0!==this.#e)return this.#e;if(!this.isTextContent)throw new Error("Cannot interpret binary data as text");const t=window.atob(this.#t),e=Uint8Array.from(t,(t=>t.codePointAt(0)));return this.#e=new TextDecoder(this.charset).decode(e),this.#e}get isTextContent(){return e.MimeType.isTextType(this.mimeType)}get isEmpty(){return!Boolean(this.#t)&&!Boolean(this.#e)}get createdFromBase64(){return void 0!==this.#t}asDataUrl(){if(void 0!==this.#t){const t=this.isTextContent?this.charset:null;return i(this.#t,this.mimeType??"",!0,t)}return i(this.text,this.mimeType??"",!1)}asDeferedContent(){if(this.isTextContent)return{content:this.text,isEncoded:!1};if(void 0!==this.#e)return{content:this.#e,isEncoded:!1};if(void 0!==this.#t)return{content:this.#t,isEncoded:!0};throw new Error("Unreachable")}static isError(t){return"error"in t}static asDeferredContent(t){return a.isError(t)?{error:t.error,content:null,isEncoded:!1}:t.asDeferedContent()}}var l=Object.freeze({__proto__:null,ContentData:a});class u{lineEndings;offsetInternal;lineNumberInternal;columnNumberInternal;constructor(t){this.lineEndings=t,this.offsetInternal=0,this.lineNumberInternal=0,this.columnNumberInternal=0}advance(t){for(this.offsetInternal=t;this.lineNumberInternal<this.lineEndings.length&&this.lineEndings[this.lineNumberInternal]<this.offsetInternal;)++this.lineNumberInternal;this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}offset(){return this.offsetInternal}resetTo(t){this.offsetInternal=t,this.lineNumberInternal=e.ArrayUtilities.lowerBound(this.lineEndings,t,e.ArrayUtilities.DEFAULT_COMPARATOR),this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}lineNumber(){return this.lineNumberInternal}columnNumber(){return this.columnNumberInternal}}var c=Object.freeze({__proto__:null,TextCursor:u});const h=2**31-1;class d{startLine;startColumn;endLine;endColumn;constructor(t,e,n,r){this.startLine=t,this.startColumn=e,this.endLine=n,this.endColumn=r}static createFromLocation(t,e){return new d(t,e,t,e)}static createUnboundedFromLocation(t,e){return new d(t,e,h,h)}static fromObject(t){return new d(t.startLine,t.startColumn,t.endLine,t.endColumn)}static comparator(t,e){return t.compareTo(e)}static fromEdit(t,n){let r=t.startLine,s=t.startColumn+n.length;const i=e.StringUtilities.findLineEndingIndexes(n);if(i.length>1){r=t.startLine+i.length-1;const e=i.length;s=i[e-1]-i[e-2]-1}return new d(t.startLine,t.startColumn,r,s)}isEmpty(){return this.startLine===this.endLine&&this.startColumn===this.endColumn}immediatelyPrecedes(t){return!!t&&(this.endLine===t.startLine&&this.endColumn===t.startColumn)}immediatelyFollows(t){return!!t&&t.immediatelyPrecedes(this)}follows(t){return t.endLine===this.startLine&&t.endColumn<=this.startColumn||t.endLine<this.startLine}get linesCount(){return this.endLine-this.startLine}collapseToEnd(){return new d(this.endLine,this.endColumn,this.endLine,this.endColumn)}collapseToStart(){return new d(this.startLine,this.startColumn,this.startLine,this.startColumn)}normalize(){return this.startLine>this.endLine||this.startLine===this.endLine&&this.startColumn>this.endColumn?new d(this.endLine,this.endColumn,this.startLine,this.startColumn):this.clone()}clone(){return new d(this.startLine,this.startColumn,this.endLine,this.endColumn)}serializeToObject(){return{startLine:this.startLine,startColumn:this.startColumn,endLine:this.endLine,endColumn:this.endColumn}}compareTo(t){return this.startLine>t.startLine?1:this.startLine<t.startLine?-1:this.startColumn>t.startColumn?1:this.startColumn<t.startColumn?-1:0}compareToPosition(t,e){return t<this.startLine||t===this.startLine&&e<this.startColumn?-1:t>this.endLine||t===this.endLine&&e>this.endColumn?1:0}equal(t){return this.startLine===t.startLine&&this.endLine===t.endLine&&this.startColumn===t.startColumn&&this.endColumn===t.endColumn}relativeTo(t,e){const n=this.clone();return this.startLine===t&&(n.startColumn-=e),this.endLine===t&&(n.endColumn-=e),n.startLine-=t,n.endLine-=t,n}relativeFrom(t,e){const n=this.clone();return 0===this.startLine&&(n.startColumn+=e),0===this.endLine&&(n.endColumn+=e),n.startLine+=t,n.endLine+=t,n}rebaseAfterTextEdit(t,e){console.assert(t.startLine===e.startLine),console.assert(t.startColumn===e.startColumn);const n=this.clone();if(!this.follows(t))return n;const r=e.endLine-t.endLine,s=e.endColumn-t.endColumn;return n.startLine+=r,n.endLine+=r,n.startLine===e.endLine&&(n.startColumn+=s),n.endLine===e.endLine&&(n.endColumn+=s),n}toString(){return JSON.stringify(this)}containsLocation(t,e){return this.startLine===this.endLine?this.startLine===t&&this.startColumn<=e&&e<this.endColumn:this.startLine===t?this.startColumn<=e:this.endLine===t?e<this.endColumn:this.startLine<t&&t<this.endLine}get start(){return{lineNumber:this.startLine,columnNumber:this.startColumn}}get end(){return{lineNumber:this.endLine,columnNumber:this.endColumn}}intersection(t){let{startLine:e,startColumn:n}=this;e<t.startLine?(e=t.startLine,n=t.startColumn):e===t.startLine&&(n=Math.max(n,t.startColumn));let{endLine:r,endColumn:s}=this;return r>t.endLine?(r=t.endLine,s=t.endColumn):r===t.endLine&&(s=Math.min(s,t.endColumn)),e>r||e===r&&n>=s?new d(0,0,0,0):new d(e,n,r,s)}}class m{offset;length;constructor(t,e){this.offset=t,this.length=e}}var f=Object.freeze({__proto__:null,TextRange:d,SourceRange:m});class g{valueInternal;lineEndingsInternal;constructor(t){this.valueInternal=t}lineEndings(){return this.lineEndingsInternal||(this.lineEndingsInternal=e.StringUtilities.findLineEndingIndexes(this.valueInternal)),this.lineEndingsInternal}value(){return this.valueInternal}lineCount(){return this.lineEndings().length}offsetFromPosition(t,e){return(t?this.lineEndings()[t-1]+1:0)+e}positionFromOffset(t){const n=this.lineEndings(),r=e.ArrayUtilities.lowerBound(n,t,e.ArrayUtilities.DEFAULT_COMPARATOR);return{lineNumber:r,columnNumber:t-(r&&n[r-1]+1)}}lineAt(t){const e=this.lineEndings(),n=t>0?e[t-1]+1:0,r=e[t];let s=this.valueInternal.substring(n,r);return s.length>0&&"\r"===s.charAt(s.length-1)&&(s=s.substring(0,s.length-1)),s}toSourceRange(t){const e=this.offsetFromPosition(t.startLine,t.startColumn),n=this.offsetFromPosition(t.endLine,t.endColumn);return new m(e,n-e)}toTextRange(t){const e=new u(this.lineEndings()),n=d.createFromLocation(0,0);return e.resetTo(t.offset),n.startLine=e.lineNumber(),n.startColumn=e.columnNumber(),e.advance(t.offset+t.length),n.endLine=e.lineNumber(),n.endColumn=e.columnNumber(),n}replaceRange(t,e){const n=this.toSourceRange(t);return this.valueInternal.substring(0,n.offset)+e+this.valueInternal.substring(n.offset+n.length)}extract(t){const e=this.toSourceRange(t);return this.valueInternal.substr(e.offset,e.length)}}var C=Object.freeze({__proto__:null,Text:g});const p={get _keyValueFilterRegex(){return/(?:^|\s)(\-)?([\w\-]+):([^\s]+)/},get _regexFilterRegex(){return/(?:^|\s)(\-)?\/([^\/\\]+(\\.[^\/]*)*)\//},get _textFilterRegex(){return/(?:^|\s)(\-)?([^\s]+)/},get _SpaceCharRegex(){return/\s/},isSpaceChar:function(t){return p._SpaceCharRegex.test(t)},lineIndent:function(t){let e=0;for(;e<t.length&&p.isSpaceChar(t.charAt(e));)++e;return t.substr(0,e)},splitStringByRegexes(t,e){const n=[],r=[];for(let t=0;t<e.length;t++){const n=e[t];n.global?r.push(n):r.push(new RegExp(n.source,n.flags?n.flags+"g":"g"))}return function t(e,s,i){if(s>=r.length)return void n.push({value:e,position:i,regexIndex:-1,captureGroups:[]});const o=r[s];let a,l=0;o.lastIndex=0;for(;null!==(a=o.exec(e));){const r=e.substring(l,a.index);r&&t(r,s+1,i+l);const o=a[0];n.push({value:o,position:i+a.index,regexIndex:s,captureGroups:a.slice(1)}),l=a.index+o.length}const u=e.substring(l);u&&t(u,s+1,i+l)}(t,0,0),n}};const x=function(t,e,n,r){return a.isError(t)||!t.isTextContent?[]:L(t.text,e,n,r)},L=function(t,n,r,i){const o=e.StringUtilities.createSearchRegex(n,r,i),a=new g(t),l=[];for(let t=0;t<a.lineCount();++t){const e=a.lineAt(t),n=e.matchAll(o);for(const r of n)l.push(new s(t,e,r.index,r[0].length))}return l};var b=Object.freeze({__proto__:null,Utils:p,FilterParser:class{keys;constructor(t){this.keys=t}static cloneFilter(t){return{key:t.key,text:t.text,regex:t.regex,negative:t.negative}}parse(t){const e=p.splitStringByRegexes(t,[p._keyValueFilterRegex,p._regexFilterRegex,p._textFilterRegex]),n=[];for(const{regexIndex:t,captureGroups:r}of e)if(-1!==t)if(0===t){const t=r[0],e=r[1],s=r[2];-1!==this.keys.indexOf(e)?n.push({key:e,regex:void 0,text:s,negative:Boolean(t)}):n.push({key:void 0,regex:void 0,text:`${e}:${s}`,negative:Boolean(t)})}else if(1===t){const t=r[0],e=r[1];try{n.push({key:void 0,regex:new RegExp(e,"i"),text:void 0,negative:Boolean(t)})}catch(r){n.push({key:void 0,regex:void 0,text:`/${e}/`,negative:Boolean(t)})}}else if(2===t){const t=r[0],e=r[1];n.push({key:void 0,regex:void 0,text:e,negative:Boolean(t)})}return n}},BalancedJSONTokenizer:class{callback;index;balance;buffer;findMultiple;closingDoubleQuoteRegex;lastBalancedIndex;constructor(t,e){this.callback=t,this.index=0,this.balance=0,this.buffer="",this.findMultiple=e||!1,this.closingDoubleQuoteRegex=/[^\\](?:\\\\)*"/g}write(t){this.buffer+=t;const e=this.buffer.length,n=this.buffer;let r;for(r=this.index;r<e;++r){const t=n[r];if('"'===t){if(this.closingDoubleQuoteRegex.lastIndex=r,!this.closingDoubleQuoteRegex.test(n))break;r=this.closingDoubleQuoteRegex.lastIndex-1}else if("{"===t)++this.balance;else if("}"===t){if(--this.balance,this.balance<0)return this.reportBalanced(),!1;if(!this.balance&&(this.lastBalancedIndex=r+1,!this.findMultiple))break}else if("]"===t&&!this.balance)return this.reportBalanced(),!1}return this.index=r,this.reportBalanced(),!0}reportBalanced(){this.lastBalancedIndex&&(this.callback(this.buffer.slice(0,this.lastBalancedIndex)),this.buffer=this.buffer.slice(this.lastBalancedIndex),this.index-=this.lastBalancedIndex,this.lastBalancedIndex=0)}remainder(){return this.buffer}},detectIndentation:function(t){const e=[0,0,0,0,0,0,0,0,0];let n=0,r=0;for(const s of t){let t=0;if(0!==s.length){let e=s.charAt(0);if("\t"===e){n++;continue}for(;" "===e;)e=s.charAt(++t)}if(t===s.length){r=0;continue}const i=Math.abs(t-r);i<e.length&&(e[i]=e[i]+1),r=t}let s=0,i=0;for(let t=1;t<e.length;++t){const n=e[t];n>i&&(i=n,s=t)}return n>s?"\t":s?" ".repeat(s):null},isMinified:function(t){let e=0;for(let n=0;n<t.length;++e){let e=t.indexOf("\n",n);e<0&&(e=t.length),n=e+1}return(t.length-e)/e>=80},performSearchInContentData:x,performSearchInContent:L,performSearchInSearchMatches:function(t,n,r,i){const o=e.StringUtilities.createSearchRegex(n,r,i),a=[];for(const{lineNumber:e,lineContent:n}of t){const t=n.matchAll(o);for(const r of t)a.push(new s(e,n,r.index,r[0].length))}return a}});class I{contentURLInternal;contentTypeInternal;lazyContent;constructor(t,e,n){this.contentURLInternal=t,this.contentTypeInternal=e,this.lazyContent=n}static fromString(t,e,n){return new I(t,e,(()=>Promise.resolve({content:n,isEncoded:!1})))}contentURL(){return this.contentURLInternal}contentType(){return this.contentTypeInternal}requestContent(){return this.lazyContent()}async searchInContent(t,e,n){const{content:r}=await this.lazyContent();return r?L(r,t,e,n):[]}}var y=Object.freeze({__proto__:null,StaticContentProvider:I,SafeStaticContentProvider:class{#n;#r;#s;constructor(t,e,n){this.#n=t,this.#r=e,this.#s=n}contentURL(){return this.#n}contentType(){return this.#r}requestContent(){return this.#s().then(a.asDeferredContent.bind(void 0))}requestContentData(){return this.#s()}async searchInContent(t,e,n){const r=await this.requestContentData();return x(r,t,e,n)}}});class T extends n.ObjectWrapper.ObjectWrapper{mimeType;#i;#o;#a=[];#l;constructor(t,e,n){super(),this.mimeType=t,this.#i=e,this.#o=Boolean(n&&!n.createdFromBase64),this.#l=n}static create(t,e){return new T(t,e)}static from(t){return new T(t.mimeType,t.charset,t)}get isTextContent(){return e.MimeType.isTextType(this.mimeType)}addChunk(t){if(this.#o)throw new Error("Cannot add base64 data to a text-only ContentData.");this.#a.push(t),this.dispatchEventToListeners("ChunkAdded",{content:this,chunk:t})}content(){if(this.#l&&0===this.#a.length)return this.#l;const t=this.#l?.base64??"",n=this.#a.reduce(((t,n)=>e.StringUtilities.concatBase64(t,n)),t);return this.#l=new a(n,!0,this.mimeType,this.#i),this.#a=[],this.#l}}const v=function(t){return"error"in t};var _=Object.freeze({__proto__:null,StreamingContentData:T,isError:v,asDeferredContent:function(t){return v(t)?{error:t.error,content:null,isEncoded:!1}:t.content().asDeferedContent()}});export{r as CodeMirrorUtils,l as ContentData,o as ContentProvider,y as StaticContentProvider,_ as StreamingContentData,C as Text,c as TextCursor,f as TextRange,b as TextUtils};
