-- <PERSON>ript de données de test pour la base de données Facutration
-- Exécuter ce script dans PostgreSQL pour créer des données de test

-- Insérer des secteurs
INSERT INTO Secteur (nom) VALUES 
('Centre-Ville'),
('Quartier Nord'),
('Zone Industrielle'),
('Résidentiel Sud'),
('Périphérie Est');

-- Insérer des clients de test
INSERT INTO Client (nom, prenom, adresse, ville, tel, email, idS) VALUES 
('<PERSON><PERSON>', '<PERSON>', '123 Rue de la Paix', 'Paris', '0123456789', '<EMAIL>', 1),
('<PERSON>', '<PERSON>', '456 Avenue des Fleurs', 'Lyon', '0234567890', '<EMAIL>', 2),
('<PERSON>', '<PERSON>', '789 Boulevard Central', 'Marseille', '0345678901', '<EMAIL>', 1),
('<PERSON><PERSON>', '<PERSON>', '321 <PERSON> du Commerce', 'Toulouse', '0456789012', '<EMAIL>', 3),
('<PERSON><PERSON>', '<PERSON>', '654 <PERSON>emin des Vignes', '<PERSON>', '0567890123', '<EMAIL>', 4),
('<PERSON>', '<PERSON>', '987 Place de la <PERSON>rie', 'Nantes', '0678901234', '<EMAIL>', 2),
('Roux', '<PERSON>', '147 <PERSON> de la Gare', 'Strasbourg', '0789012345', '<EMAIL>', 5),
('Fournier', 'Claire', '258 Avenue du Parc', 'Montpellier', '0890123456', '<EMAIL>', 1),
('Girard', 'Paul', '369 Rue des Écoles', 'Bordeaux', '0901234567', '<EMAIL>', 3),
('Bonnet', 'Julie', '741 Boulevard des Arts', 'Lille', '0123456780', '<EMAIL>', 4);

-- Insérer des contrats avec QR codes
INSERT INTO Contract (codeQr, dateContract, idClient, marqueCompteur, numSerieCompteur, posX, posY) VALUES 
('QR001', '2024-01-15', 1, 'Sensus', 'SN123456', 48.8566, 2.3522),
('QR002', '2024-01-20', 2, 'Itron', 'IT789012', 45.7640, 4.8357),
('QR003', '2024-02-01', 3, 'Elster', 'EL345678', 43.2965, 5.3698),
('QR004', '2024-02-10', 4, 'Sensus', 'SN901234', 43.6047, 1.4442),
('QR005', '2024-02-15', 5, 'Itron', 'IT567890', 43.7102, 7.2620),
('QR006', '2024-03-01', 6, 'Elster', 'EL123456', 47.2184, -1.5536),
('QR007', '2024-03-05', 7, 'Sensus', 'SN789012', 48.5734, 7.7521),
('QR008', '2024-03-10', 8, 'Itron', 'IT345678', 43.6108, 3.8767),
('QR009', '2024-03-15', 9, 'Elster', 'EL901234', 44.8378, -0.5792),
('QR010', '2024-03-20', 10, 'Sensus', 'SN567890', 50.6292, 3.0573);

-- Insérer des consommations
INSERT INTO Consommation (consommationPre, consommationActuelle, idCont, jours, periode, status) VALUES 
(150, 175, 1, 30, '2024-01', 'Validée'),
(200, 220, 2, 31, '2024-01', 'Validée'),
(180, 195, 3, 29, '2024-02', 'En attente'),
(160, 185, 4, 30, '2024-02', 'Validée'),
(140, 165, 5, 28, '2024-02', 'Validée'),
(190, 210, 6, 31, '2024-03', 'En attente'),
(170, 190, 7, 30, '2024-03', 'Validée'),
(155, 180, 8, 29, '2024-03', 'Validée'),
(165, 185, 9, 31, '2024-03', 'En attente'),
(145, 170, 10, 30, '2024-03', 'Validée');

-- Insérer des consommations supplémentaires (historique)
INSERT INTO Consommation (consommationPre, consommationActuelle, idCont, jours, periode, status) VALUES 
(125, 150, 1, 31, '2023-12', 'Validée'),
(175, 200, 2, 30, '2023-12', 'Validée'),
(160, 180, 3, 31, '2024-01', 'Validée'),
(140, 160, 4, 29, '2024-01', 'Validée'),
(120, 140, 5, 31, '2024-01', 'Validée');

-- Insérer des factures
INSERT INTO Facture (date, idConst, montant, periode, reference, status) VALUES 
('2024-02-01', 1, 45.50, '2024-01', 'FAC-2024-001', 'Payée'),
('2024-02-01', 2, 52.30, '2024-01', 'FAC-2024-002', 'En attente'),
('2024-03-01', 3, 48.75, '2024-02', 'FAC-2024-003', 'Payée'),
('2024-03-01', 4, 51.20, '2024-02', 'FAC-2024-004', 'Payée'),
('2024-03-01', 5, 42.80, '2024-02', 'FAC-2024-005', 'En attente'),
('2024-04-01', 6, 55.60, '2024-03', 'FAC-2024-006', 'En attente'),
('2024-04-01', 7, 49.90, '2024-03', 'FAC-2024-007', 'Payée'),
('2024-04-01', 8, 53.40, '2024-03', 'FAC-2024-008', 'Payée'),
('2024-04-01', 9, 47.25, '2024-03', 'FAC-2024-009', 'En attente'),
('2024-04-01', 10, 44.70, '2024-03', 'FAC-2024-010', 'Payée');

-- Insérer des factures supplémentaires (historique)
INSERT INTO Facture (date, idConst, montant, periode, reference, status) VALUES 
('2024-01-01', 11, 43.20, '2023-12', 'FAC-2023-012', 'Payée'),
('2024-01-01', 12, 48.90, '2023-12', 'FAC-2023-013', 'Payée'),
('2024-02-01', 13, 46.80, '2024-01', 'FAC-2024-011', 'Payée'),
('2024-02-01', 14, 44.60, '2024-01', 'FAC-2024-012', 'Payée'),
('2024-02-01', 15, 41.30, '2024-01', 'FAC-2024-013', 'Payée');

-- Vérification des données insérées
SELECT 'Secteurs' as table_name, COUNT(*) as count FROM Secteur
UNION ALL
SELECT 'Clients', COUNT(*) FROM Client
UNION ALL
SELECT 'Contrats', COUNT(*) FROM Contract
UNION ALL
SELECT 'Consommations', COUNT(*) FROM Consommation
UNION ALL
SELECT 'Factures', COUNT(*) FROM Facture;

-- Afficher les QR codes disponibles pour les tests
SELECT 
    cont.codeQr as "QR Code",
    CONCAT(c.nom, ' ', c.prenom) as "Client",
    CONCAT(cont.marqueCompteur, ' - ', cont.numSerieCompteur) as "Compteur",
    s.nom as "Secteur"
FROM Contract cont
LEFT JOIN Client c ON cont.idClient = c.idClient
LEFT JOIN Secteur s ON c.idS = s.idS
ORDER BY cont.codeQr;

-- Afficher un exemple de données complètes pour QR001
SELECT 
    'CLIENT:' as type,
    CONCAT(c.nom, ' ', c.prenom) as info
FROM Client c
LEFT JOIN Contract cont ON c.idClient = cont.idClient
WHERE cont.codeQr = 'QR001'

UNION ALL

SELECT 
    'ADRESSE:' as type,
    CONCAT(c.adresse, ', ', c.ville) as info
FROM Client c
LEFT JOIN Contract cont ON c.idClient = cont.idClient
WHERE cont.codeQr = 'QR001'

UNION ALL

SELECT 
    'COMPTEUR:' as type,
    CONCAT(cont.marqueCompteur, ' - ', cont.numSerieCompteur) as info
FROM Contract cont
WHERE cont.codeQr = 'QR001'

UNION ALL

SELECT 
    'DERNIÈRE CONSO:' as type,
    CONCAT(cons.consommationActuelle, ' m³ (', cons.periode, ')') as info
FROM Consommation cons
LEFT JOIN Contract cont ON cons.idCont = cont.idContract
WHERE cont.codeQr = 'QR001'
ORDER BY cons.periode DESC
LIMIT 1;
