const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

console.log('🚀 Démarrage du serveur simple...');

const app = express();
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.url}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur simple fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Récupération des clients...');
    
    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email
      FROM client c
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    console.log(`✅ ${result.rows.length} clients trouvés`);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// 🎯 ROUTE CRITIQUE : Contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: Contrats pour client ${id}`);

    // Vérifier si le client existe
    const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [id]);
    if (clientCheck.rows.length === 0) {
      console.log(`❌ Client ${id} non trouvé`);
      return res.status(404).json({
        success: false,
        message: `Client ${id} non trouvé`
      });
    }

    const clientInfo = clientCheck.rows[0];
    console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

    // Récupérer les contrats
    const contractQuery = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy
      FROM contract c
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    const contractResult = await pool.query(contractQuery, [id]);
    console.log(`📊 ${contractResult.rows.length} contrat(s) trouvé(s)`);

    if (contractResult.rows.length > 0) {
      console.log('📋 Contrats:');
      contractResult.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. ID: ${contract.idcontract}, QR: ${contract.codeqr || 'Non défini'}`);
      });
    } else {
      console.log('⚠️ Aucun contrat pour ce client');
    }

    res.json({
      success: true,
      data: contractResult.rows,
      count: contractResult.rows.length,
      message: `${contractResult.rows.length} contrat(s) trouvé(s)`,
      client_id: parseInt(id),
      client_name: `${clientInfo.nom} ${clientInfo.prenom}`
    });

  } catch (error) {
    console.error('❌ Erreur contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Dernière consommation pour contrat ${id}`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0]
      });
    } else {
      console.log(`ℹ️ Aucune consommation pour contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur consommation:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route de connexion
app.post('/login', (req, res) => {
  const { email, motDepass } = req.body;
  console.log(`🔐 Tentative de connexion: ${email}`);

  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion réussie');
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: email,
        role: 'Tech'
      },
      redirectTo: '/technician-dashboard'
    });
  } else {
    console.log('❌ Identifiants incorrects');
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Démarrage du serveur
const PORT = 3004;

async function testConnection() {
  try {
    console.log('🔄 Test de connexion à la base...');
    const client = await pool.connect();
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`✅ Connexion OK - ${result.rows[0].count} clients`);
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    return false;
  }
}

async function start() {
  console.log('🚀 Démarrage...');
  
  const connected = await testConnection();
  if (!connected) {
    console.error('❌ Impossible de se connecter à la base');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 SERVEUR DÉMARRÉ sur http://localhost:${PORT}`);
    console.log('📡 Routes:');
    console.log('  - GET  / (test)');
    console.log('  - POST /login');
    console.log('  - GET  /api/clients');
    console.log('  - GET  /api/clients/:id/contracts ⭐ CRITIQUE');
    console.log('  - GET  /api/contracts/:id/last-consommation');
    console.log('\n✅ PRÊT !');
    console.log('═'.repeat(50));
  });
}

start();

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur:', err.message);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
});
