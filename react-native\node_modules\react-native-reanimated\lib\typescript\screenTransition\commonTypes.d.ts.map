{"version": 3, "file": "commonTypes.d.ts", "sourceRoot": "", "sources": ["../../../src/screenTransition/commonTypes.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EACV,kBAAkB,EAClB,iBAAiB,EACjB,WAAW,EACZ,MAAM,gBAAgB,CAAC;AAExB,MAAM,MAAM,6BAA6B,GAAG;IAC1C,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG;IACrC,cAAc,EAAE,CACd,KAAK,EAAE,6BAA6B,EACpC,gBAAgB,EAAE,kBAAkB,KACjC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,mBAAmB,EAAE,CACnB,KAAK,EAAE,6BAA6B,EACpC,gBAAgB,EAAE,kBAAkB,KACjC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,aAAa,GACrB,YAAY,GACZ,WAAW,GACX,SAAS,GACT,WAAW,GACX,eAAe,GACf,iBAAiB,GACjB,qBAAqB,CAAC;AAE1B,MAAM,MAAM,sBAAsB,GAAG;IACnC,QAAQ,EAAE,MAAM,CAAC;IACjB,gBAAgB,EAAE,MAAM,GAAG,iBAAiB,CAAC;IAC7C,WAAW,EAAE,MAAM,GAAG,iBAAiB,CAAC;IACxC,gBAAgB,EAAE,wBAAwB,CAAC;IAC3C,WAAW,EAAE,WAAW,CAAC,6BAA6B,CAAC,CAAC;IACxD,uBAAuB,EAAE,WAAW,CAAC,6BAA6B,CAAC,CAAC;IACpE,iBAAiB,CAAC,EAAE,MAAM,IAAI,CAAC;IAC/B,oBAAoB,EAAE,OAAO,CAAC;IAC9B,aAAa,EAAE,aAAa,CAAC;IAC7B,gBAAgB,EAAE,kBAAkB,CAAC;CACtC,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG;IACrC,eAAe,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK;QACrC,WAAW,EAAE,MAAM,GAAG,iBAAiB,CAAC;QACxC,gBAAgB,EAAE,MAAM,GAAG,iBAAiB,CAAC;QAC7C,kBAAkB,EAAE,OAAO,CAAC;KAC7B,CAAC;IACF,gBAAgB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/D,gBAAgB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,CAAC;CACnE,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC"}