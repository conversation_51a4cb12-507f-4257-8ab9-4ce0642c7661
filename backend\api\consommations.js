const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Route pour récupérer toutes les consommations
router.get('/consommations', async (req, res) => {
  try {
    console.log('🔍 Récupération de toutes les consommations...');

    const consommationsQuery = `
      SELECT 
        cons.idCons,
        cons.consommationPre,
        cons.consommationActuelle,
        cons.jours,
        cons.periode,
        cons.status,
        c.nom as client_nom,
        c.prenom as client_prenom,
        cont.codeQr,
        t.nom as tech_nom,
        t.prenom as tech_prenom
      FROM Consommation cons
      LEFT JOIN Contract cont ON cons.idCont = cont.idContract
      LEFT JOIN Client c ON cont.idClient = c.idClient
      LEFT JOIN Utilisateur t ON cons.idTech = t.idTech
      ORDER BY cons.periode DESC, cons.idCons DESC
    `;

    const result = await pool.query(consommationsQuery);

    console.log(`✅ ${result.rows.length} consommation(s) trouvée(s)`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(cons => ({
        idCons: cons.idcons,
        consommationPre: cons.consommationpre,
        consommationActuelle: cons.consommationactuelle,
        jours: cons.jours,
        periode: cons.periode,
        status: cons.status,
        client_nom: cons.client_nom,
        client_prenom: cons.client_prenom,
        codeQr: cons.codeqr,
        tech_nom: cons.tech_nom,
        tech_prenom: cons.tech_prenom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations',
      error: error.message
    });
  }
});

// Route pour créer une nouvelle consommation
router.post('/consommations', async (req, res) => {
  const {
    consommationPre,
    consommationActuelle,
    idCont,
    idTech,
    idTranch,
    jours,
    periode,
    status = 'en_attente'
  } = req.body;

  try {
    console.log('📝 Création d\'une nouvelle consommation...');

    const insertQuery = `
      INSERT INTO Consommation (
        consommationPre,
        consommationActuelle,
        idCont,
        idTech,
        idTranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      consommationPre,
      consommationActuelle,
      idCont,
      idTech,
      idTranch,
      jours,
      periode,
      status
    ];

    const result = await pool.query(insertQuery, values);
    const newConsommation = result.rows[0];

    console.log('✅ Consommation créée avec succès:', newConsommation.idcons);

    res.json({
      success: true,
      message: 'Consommation créée avec succès',
      data: {
        idCons: newConsommation.idcons,
        consommationPre: newConsommation.consommationpre,
        consommationActuelle: newConsommation.consommationactuelle,
        jours: newConsommation.jours,
        periode: newConsommation.periode,
        status: newConsommation.status
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création de la consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la consommation',
      error: error.message
    });
  }
});

// Route pour récupérer les consommations d'un client
router.get('/clients/:id/consommations', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération des consommations pour le client ID: ${id}`);

    const consommationsQuery = `
      SELECT 
        cons.idCons,
        cons.consommationPre,
        cons.consommationActuelle,
        cons.jours,
        cons.periode,
        cons.status,
        cont.codeQr,
        t.nom as tech_nom,
        t.prenom as tech_prenom
      FROM Consommation cons
      LEFT JOIN Contract cont ON cons.idCont = cont.idContract
      LEFT JOIN Utilisateur t ON cons.idTech = t.idTech
      WHERE cont.idClient = $1
      ORDER BY cons.periode DESC
    `;

    const result = await pool.query(consommationsQuery, [id]);

    console.log(`✅ ${result.rows.length} consommation(s) trouvée(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(cons => ({
        idCons: cons.idcons,
        consommationPre: cons.consommationpre,
        consommationActuelle: cons.consommationactuelle,
        jours: cons.jours,
        periode: cons.periode,
        status: cons.status,
        codeQr: cons.codeqr,
        tech_nom: cons.tech_nom,
        tech_prenom: cons.tech_prenom
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations',
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
router.get('/contracts/:id/last-consommation', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération de la dernière consommation pour le contrat ID: ${id}`);

    const lastConsommationQuery = `
      SELECT 
        cons.idCons,
        cons.consommationPre,
        cons.consommationActuelle,
        cons.jours,
        cons.periode,
        cons.status
      FROM Consommation cons
      WHERE cons.idCont = $1
      ORDER BY cons.periode DESC, cons.idCons DESC
      LIMIT 1
    `;

    const result = await pool.query(lastConsommationQuery, [id]);

    if (result.rows.length === 0) {
      return res.json({
        success: true,
        message: 'Aucune consommation trouvée pour ce contrat',
        data: null
      });
    }

    const lastCons = result.rows[0];

    res.json({
      success: true,
      data: {
        idCons: lastCons.idcons,
        consommationPre: lastCons.consommationpre,
        consommationActuelle: lastCons.consommationactuelle,
        jours: lastCons.jours,
        periode: lastCons.periode,
        status: lastCons.status
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

module.exports = router;
