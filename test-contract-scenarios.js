// Test des différents scénarios de contrats pour les clients
const axios = require('axios');

async function testContractScenarios() {
  console.log('🧪 Test des Scénarios de Contrats\n');

  try {
    // 1. Récupérer tous les clients
    console.log('📤 1. Récupération de tous les clients...');
    const clientsResponse = await axios.get('http://localhost:3002/api/clients');
    
    if (!clientsResponse.data.success) {
      console.log('❌ Erreur lors de la récupération des clients');
      return;
    }

    const clients = clientsResponse.data.data;
    console.log(`✅ ${clients.length} clients récupérés\n`);

    // 2. Tester chaque client pour voir ses contrats
    console.log('📋 Analyse des contrats par client:\n');
    
    for (const client of clients.slice(0, 10)) { // Limiter à 10 clients pour le test
      console.log(`👤 Client: ${client.nom} ${client.prenom} (ID: ${client.idclient})`);
      
      try {
        const contractsResponse = await axios.get(`http://localhost:3002/api/clients/${client.idclient}/contracts`);
        
        if (contractsResponse.data.success) {
          const contracts = contractsResponse.data.data;
          
          if (contracts.length === 0) {
            console.log('   ❌ Aucun contrat - Message: "Ce client n\'a pas de contrat"');
            console.log('   📝 Comportement attendu: Afficher message d\'erreur\n');
            
          } else if (contracts.length === 1) {
            const contract = contracts[0];
            console.log(`   🎯 1 contrat trouvé - Sélection automatique`);
            console.log(`   📋 Code QR: ${contract.codeqr}`);
            console.log(`   📋 Marque: ${contract.marquecompteur}`);
            console.log(`   📋 Date: ${new Date(contract.datecontract).toLocaleDateString()}`);
            console.log(`   📝 Comportement attendu: Sélection automatique du contrat "${contract.codeqr}"\n`);
            
          } else {
            console.log(`   📋 ${contracts.length} contrats trouvés - Sélection manuelle requise`);
            contracts.forEach((contract, index) => {
              console.log(`      ${index + 1}. ${contract.codeqr} (${contract.marquecompteur})`);
            });
            console.log(`   📝 Comportement attendu: Dropdown avec ${contracts.length} options\n`);
          }
        } else {
          console.log('   ❌ Erreur lors de la récupération des contrats\n');
        }
        
      } catch (error) {
        console.log(`   ❌ Erreur API: ${error.message}\n`);
      }
    }

    // 3. Test spécifique pour Benali Fatima
    console.log('🎯 Test spécifique - Benali Fatima:\n');
    const benaliClient = clients.find(c => c.nom === 'Benali' && c.prenom === 'Fatima');
    
    if (benaliClient) {
      console.log(`✅ Client trouvé: ${benaliClient.nom} ${benaliClient.prenom} (ID: ${benaliClient.idclient})`);
      
      const contractsResponse = await axios.get(`http://localhost:3002/api/clients/${benaliClient.idclient}/contracts`);
      
      if (contractsResponse.data.success) {
        const contracts = contractsResponse.data.data;
        console.log(`📋 ${contracts.length} contrat(s) trouvé(s)`);
        
        if (contracts.length === 1) {
          const contract = contracts[0];
          console.log('🎯 RÉSULTAT ATTENDU:');
          console.log(`   - Le champ "Contrat" doit afficher automatiquement: "${contract.codeqr} - Contrat #${contract.idcontract}"`);
          console.log(`   - Le champ doit être en vert (sélection automatique)`);
          console.log(`   - Message: "🎯 Contrat unique sélectionné automatiquement"`);
          console.log(`   - La consommation précédente doit se charger automatiquement`);
        }
      }
    } else {
      console.log('❌ Client Benali Fatima non trouvé');
    }

    // 4. Résumé des comportements attendus
    console.log('\n📝 RÉSUMÉ DES COMPORTEMENTS ATTENDUS:\n');
    console.log('🎯 CLIENT AVEC 1 CONTRAT:');
    console.log('   - Sélection automatique du contrat');
    console.log('   - Champ en vert avec bordure');
    console.log('   - Message: "🎯 Contrat unique sélectionné automatiquement"');
    console.log('   - Chargement automatique de la consommation précédente\n');
    
    console.log('📋 CLIENT AVEC PLUSIEURS CONTRATS:');
    console.log('   - Dropdown avec toutes les options');
    console.log('   - Message: "📋 X contrats disponibles - Veuillez en sélectionner un"');
    console.log('   - Sélection manuelle requise\n');
    
    console.log('❌ CLIENT SANS CONTRAT:');
    console.log('   - Message: "Ce client n\'a pas de contrat"');
    console.log('   - Message d\'erreur: "❌ Ce client n\'a pas de contrat dans la table Contract"');
    console.log('   - Impossible de créer une consommation\n');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

// Test de l'API de contrats spécifique
async function testContractAPI() {
  console.log('🔧 Test de l\'API /api/clients/:id/contracts\n');
  
  try {
    // Test avec le client ID 15 (Benali Fatima)
    const response = await axios.get('http://localhost:3002/api/clients/15/contracts');
    
    console.log('📡 Réponse de l\'API:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.data.length > 0) {
      const contract = response.data.data[0];
      console.log('\n✅ Structure du contrat conforme à la table Contract:');
      console.log(`   - idcontract: ${contract.idcontract}`);
      console.log(`   - codeqr: ${contract.codeqr}`);
      console.log(`   - datecontract: ${contract.datecontract}`);
      console.log(`   - idclient: ${contract.idclient}`);
      console.log(`   - marquecompteur: ${contract.marquecompteur}`);
      console.log(`   - numseriecompteur: ${contract.numseriecompteur}`);
      console.log(`   - posx: ${contract.posx}`);
      console.log(`   - posy: ${contract.posy}`);
    }
    
  } catch (error) {
    console.error('❌ Erreur API:', error.message);
  }
}

// Exécuter les tests
testContractScenarios()
  .then(() => testContractAPI())
  .then(() => {
    console.log('\n🎉 Tests terminés !');
    console.log('💡 Maintenant, testez dans l\'interface web:');
    console.log('   1. Allez sur http://localhost:3001');
    console.log('   2. Connectez-<NAME_EMAIL> / Tech123');
    console.log('   3. Cliquez sur "CLIENTS (ANCIEN)"');
    console.log('   4. Sélectionnez "Benali Fatima - Sefrou"');
    console.log('   5. Vérifiez que le contrat s\'affiche automatiquement');
  })
  .catch(console.error);
