import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

// Import des pages
import LoginPage from './pages/LoginPage';
import TechnicianDashboard from './pages/TechnicianDashboard';
import HistoriquePageRN from './HistoriquePageRN';
import ConsommationPage from './pages/ConsommationPage';
import FacturePage from './pages/FacturePage';
import QRCodePage from './pages/QRCodePage';

const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

// Composant pour le contenu personnalisé du drawer
const CustomDrawerContent = ({ navigation }) => {
  const menuItems = [
    { 
      title: '🏠 Tableau de Bord', 
      screen: 'Dashboard',
      icon: '🏠'
    },
    { 
      title: '👥 Liste des Clients', 
      screen: 'ClientsList',
      icon: '👥'
    },
    { 
      title: '💧 Consommation', 
      screen: 'Consommation',
      icon: '💧'
    },
    { 
      title: '🧾 Factures', 
      screen: 'Factures',
      icon: '🧾'
    },
    { 
      title: '📱 Scanner QR', 
      screen: 'QRCode',
      icon: '📱'
    },
    { 
      title: '🗺️ Localisation', 
      screen: 'Localisation',
      icon: '🗺️'
    },
  ];

  return (
    <View style={styles.drawerContainer}>
      <View style={styles.drawerHeader}>
        <Text style={styles.drawerTitle}>💧 AquaTrack</Text>
        <Text style={styles.drawerSubtitle}>Gestion de Facturation</Text>
      </View>
      
      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={() => navigation.navigate(item.screen)}
          >
            <Text style={styles.menuIcon}>{item.icon}</Text>
            <Text style={styles.menuText}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.drawerFooter}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={styles.logoutText}>🚪 Déconnexion</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Navigation avec Drawer pour les pages principales
const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: '#f8fafc',
          width: 280,
        },
      }}
    >
      <Drawer.Screen 
        name="Dashboard" 
        component={TechnicianDashboard}
        options={{ title: 'Tableau de Bord' }}
      />
      <Drawer.Screen 
        name="ClientsList" 
        component={HistoriquePageRN}
        options={{ title: 'Liste des Clients' }}
      />
      <Drawer.Screen 
        name="Consommation" 
        component={ConsommationPage}
        options={{ title: 'Consommation' }}
      />
      <Drawer.Screen 
        name="Factures" 
        component={FacturePage}
        options={{ title: 'Factures' }}
      />
      <Drawer.Screen 
        name="QRCode" 
        component={QRCodePage}
        options={{ title: 'Scanner QR' }}
      />
    </Drawer.Navigator>
  );
};

// Navigation principale avec Stack
const App = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen 
          name="Login" 
          component={LoginPage}
          options={{ title: 'Connexion' }}
        />
        <Stack.Screen 
          name="Main" 
          component={DrawerNavigator}
          options={{ title: 'Application' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  drawerHeader: {
    backgroundColor: '#3b82f6',
    padding: 20,
    paddingTop: 50,
  },
  drawerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  drawerSubtitle: {
    fontSize: 14,
    color: '#bfdbfe',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 15,
    width: 30,
  },
  menuText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  drawerFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  logoutButton: {
    backgroundColor: '#dc2626',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default App;
