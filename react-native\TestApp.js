import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { API_CONFIG } from './config/constants';

export default function TestApp() {
  const testConnection = async () => {
    try {
      console.log('🔄 Test de connexion à:', API_CONFIG.BASE_URL);
      
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/clients`);
      const data = await response.json();
      
      if (data.success) {
        Alert.alert('✅ Succès', `Connexion réussie ! ${data.data.length} clients trouvés.`);
      } else {
        Alert.alert('❌ Erreur', 'Réponse du serveur invalide');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert('❌ Erreur de connexion', 
        `Impossible de se connecter au serveur.\n\nURL: ${API_CONFIG.BASE_URL}\n\nErreur: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>🧪 Test AquaTrack</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Configuration API :</Text>
        <Text style={styles.infoText}>{API_CONFIG.BASE_URL}</Text>
      </View>
      
      <TouchableOpacity style={styles.testButton} onPress={testConnection}>
        <Text style={styles.testButtonText}>🔗 Tester la connexion</Text>
      </TouchableOpacity>
      
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>📋 Instructions :</Text>
        <Text style={styles.instructionsText}>
          1. Assurez-vous que le serveur backend fonctionne sur le port 3007{'\n'}
          2. Vérifiez que l'IP dans config/constants.js est correcte{'\n'}
          3. Votre téléphone et ordinateur doivent être sur le même réseau WiFi{'\n'}
          4. Cliquez sur "Tester la connexion" pour vérifier
        </Text>
      </View>
      
      <View style={styles.credentialsContainer}>
        <Text style={styles.credentialsTitle}>🔐 Identifiants de test :</Text>
        <Text style={styles.credentialsText}>
          Email: <EMAIL>{'\n'}
          Mot de passe: Tech123
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#2196F3',
  },
  infoContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'monospace',
  },
  testButton: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  instructionsText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  credentialsContainer: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  credentialsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  credentialsText: {
    fontSize: 14,
    color: '#2e7d32',
    fontFamily: 'monospace',
  },
});
