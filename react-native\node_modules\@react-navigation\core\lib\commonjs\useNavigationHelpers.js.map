{"version": 3, "names": ["PrivateValueStore", "useNavigationHelpers", "id", "navigatorId", "onAction", "getState", "emitter", "router", "onUnhandledAction", "React", "useContext", "UnhandledActionContext", "parentNavigationHelpers", "NavigationContext", "useMemo", "dispatch", "op", "action", "handled", "actions", "actionCreators", "CommonActions", "helpers", "Object", "keys", "reduce", "acc", "name", "navigationHelpers", "emit", "isFocused", "canGoBack", "state", "getStateForAction", "goBack", "routeNames", "routeParamList", "routeGetIdList", "getId", "getParent", "undefined", "current"], "sourceRoot": "../../src", "sources": ["useNavigationHelpers.tsx"], "mappings": ";;;;;;AAAA;AAOA;AAEA;AACA;AACA;AAA8D;AAAA;AAAA;AAG9D;AACA;AACAA,wBAAiB;AAUjB;AACA;AACA;AACA;AACe,SAASC,oBAAoB,OAWjB;EAAA,IANzB;IACAC,EAAE,EAAEC,WAAW;IACfC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC;EACsB,CAAC;EACvB,MAAMC,iBAAiB,GAAGC,KAAK,CAACC,UAAU,CAACC,+BAAsB,CAAC;EAClE,MAAMC,uBAAuB,GAAGH,KAAK,CAACC,UAAU,CAACG,0BAAiB,CAAC;EAEnE,OAAOJ,KAAK,CAACK,OAAO,CAAC,MAAM;IACzB,MAAMC,QAAQ,GAAIC,EAAuC,IAAK;MAC5D,MAAMC,MAAM,GAAG,OAAOD,EAAE,KAAK,UAAU,GAAGA,EAAE,CAACX,QAAQ,EAAE,CAAC,GAAGW,EAAE;MAE7D,MAAME,OAAO,GAAGd,QAAQ,CAACa,MAAM,CAAC;MAEhC,IAAI,CAACC,OAAO,EAAE;QACZV,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGS,MAAM,CAAC;MAC7B;IACF,CAAC;IAED,MAAME,OAAO,GAAG;MACd,GAAGZ,MAAM,CAACa,cAAc;MACxB,GAAGC;IACL,CAAC;IAED,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACzD;MACAD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA,OAAkBZ,QAAQ,CAACI,OAAO,CAACQ,IAAI,CAAC,CAAC,YAAO,CAAC,CAAC;MAAA;MAC9D,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAkB;IAEvB,MAAME,iBAAiB,GAAG;MACxB,GAAGhB,uBAAuB;MAC1B,GAAGU,OAAO;MACVP,QAAQ;MACRc,IAAI,EAAEvB,OAAO,CAACuB,IAAI;MAClBC,SAAS,EAAElB,uBAAuB,GAC9BA,uBAAuB,CAACkB,SAAS,GACjC,MAAM,IAAI;MACdC,SAAS,EAAE,MAAM;QACf,MAAMC,KAAK,GAAG3B,QAAQ,EAAE;QAExB,OACEE,MAAM,CAAC0B,iBAAiB,CAACD,KAAK,EAAEX,sBAAa,CAACa,MAAM,EAAE,EAAY;UAChEC,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BC,cAAc,EAAE,CAAC,CAAC;UAClBC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,KAAK,IAAI,KACXzB,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEmB,SAAS,EAAE,KACpC,KAAK;MAET,CAAC;MACDO,KAAK,EAAE,MAAMnC,WAAW;MACxBoC,SAAS,EAAGrC,EAAW,IAAK;QAC1B,IAAIA,EAAE,KAAKsC,SAAS,EAAE;UACpB,IAAIC,OAAO,GAAGb,iBAAiB;UAE/B,OAAOa,OAAO,IAAIvC,EAAE,KAAKuC,OAAO,CAACH,KAAK,EAAE,EAAE;YACxCG,OAAO,GAAGA,OAAO,CAACF,SAAS,EAAE;UAC/B;UAEA,OAAOE,OAAO;QAChB;QAEA,OAAO7B,uBAAuB;MAChC,CAAC;MACDP;IACF,CAA+D;IAE/D,OAAOuB,iBAAiB;EAC1B,CAAC,EAAE,CACDzB,WAAW,EACXG,OAAO,CAACuB,IAAI,EACZxB,QAAQ,EACRD,QAAQ,EACRI,iBAAiB,EACjBI,uBAAuB,EACvBL,MAAM,CACP,CAAC;AACJ"}