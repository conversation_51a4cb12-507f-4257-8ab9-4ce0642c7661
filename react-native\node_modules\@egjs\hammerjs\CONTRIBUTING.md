# Contributing to <PERSON>.js

Looking to contribute something to Hammer.js? **Here's how you can help.**


## Reporting issues

We only accept issues that are bug reports or feature requests. Bugs must be
isolated and reproducible problems that can be fixed within the Hammer.js.
Please read the following guidelines before opening any issue.

1. [**Read the documentation**](https://hammerjs.github.io)

2. **Search for existing issues.** We get a lot of duplicate issues, and you'd
help us out a lot by first checking if someone else has reported the same issue.
Moreover, the issue may have already been resolved with a fix available. Also
take a look if your problem is explained at the Wiki.

3. **Create an isolated and reproducible test case.** Be sure the problem exists
in <PERSON>'s code with a reduced test case that should be included in each bug
report.

4. **Include a live example.** Make use of jsFiddle or jsBin to share your
isolated test cases. Also, a screen capture would work, with tools like LICEcap.

5. **Share as much information as possible.** Include operating system and
version, browser and version, version of Hammer.js, customized or vanilla build,
etc. where appropriate. Also include steps to reproduce the bug.

## Pull requests

1. Changes must be done in `/src` files, never just the compiled files. Also, don't 
commit the compiled files.

2. Try not to pollute your pull request with unintended changes. Keep them simple
and small

3. Try to share which browsers your code has been tested in before submitting a
pull request

4. Write tests for your code, these can be found in `/tests`.
