<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Codes de Test - AquaTrack</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .qr-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
        }
        .qr-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .qr-code {
            width: 150px;
            height: 150px;
            margin: 10px auto;
            border: 1px solid #ccc;
            background: white;
        }
        .client-info {
            text-align: left;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>📱 QR Codes de Test - AquaTrack</h1>
        
        <div class="instructions">
            <h3>📋 Instructions d'utilisation :</h3>
            <ol>
                <li><strong>Ouvrir le scanner</strong> : Allez sur localhost:3002/technician-dashboard → Scanner QR</li>
                <li><strong>Cliquer "DÉMARRER LE SCAN"</strong> pour activer la caméra</li>
                <li><strong>Pointer la caméra</strong> vers l'un des QR codes ci-dessous</li>
                <li><strong>Attendre la détection</strong> automatique (bordure verte = détection active)</li>
                <li><strong>Alternative</strong> : Utiliser les boutons "✅ Test QR001" pour simuler</li>
            </ol>
        </div>

        <div class="qr-grid">
            <div class="qr-card">
                <h3 class="success">✅ QR-2025-0001 - Benali Fatima</h3>
                <div id="qr001" class="qr-code"></div>
                <div class="client-info">
                    <strong>Client :</strong> Benali Fatima<br>
                    <strong>Adresse :</strong> 45 Avenue Hassan II, Sefrou<br>
                    <strong>Téléphone :</strong> 0647895655<br>
                    <strong>Compteur :</strong> SAGEMCOM SN-123456789<br>
                    <strong>Statut :</strong> <span class="success">✅ Valide</span>
                </div>
            </div>

            <div class="qr-card">
                <h3 class="success">✅ QR123 - Client1</h3>
                <div id="qr002" class="qr-code"></div>
                <div class="client-info">
                    <strong>Client :</strong> Client1 Client1<br>
                    <strong>Adresse :</strong> Adresse Client1<br>
                    <strong>Téléphone :</strong> Tel Client1<br>
                    <strong>Compteur :</strong> Sagemcom COM123456<br>
                    <strong>Statut :</strong> <span class="success">✅ Valide</span>
                </div>
            </div>

            <div class="qr-card">
                <h3 class="success">✅ AQ-21-DUP-JEA-353400 - Client5</h3>
                <div id="qr003" class="qr-code"></div>
                <div class="client-info">
                    <strong>Client :</strong> Client5 Client5<br>
                    <strong>Adresse :</strong> Adresse Client5<br>
                    <strong>Téléphone :</strong> Tel Client5<br>
                    <strong>Compteur :</strong> Sensus SN123456789<br>
                    <strong>Statut :</strong> <span class="success">✅ Valide</span>
                </div>
            </div>

            <div class="qr-card">
                <h3 class="error">❌ QR999 - Code Invalide</h3>
                <div id="qr999" class="qr-code"></div>
                <div class="client-info">
                    <strong>Code :</strong> 999<br>
                    <strong>Statut :</strong> <span class="error">❌ Invalide</span><br>
                    <strong>Utilisation :</strong> Test d'erreur<br>
                    <strong>Résultat attendu :</strong> Message d'erreur
                </div>
            </div>

            <div class="qr-card">
                <h3 class="warning">⚠️ QR004 - Test Spécial</h3>
                <div id="qr004" class="qr-code"></div>
                <div class="client-info">
                    <strong>Client :</strong> Sophie Dubois<br>
                    <strong>Adresse :</strong> 321 Rue de la Liberté, Toulouse<br>
                    <strong>Téléphone :</strong> 0456789012<br>
                    <strong>Compteur :</strong> Kamstrup KA901234<br>
                    <strong>Statut :</strong> <span class="warning">⚠️ Test</span>
                </div>
            </div>

            <div class="qr-card">
                <h3 class="success">✅ QR005 - Test Mobile</h3>
                <div id="qr005" class="qr-code"></div>
                <div class="client-info">
                    <strong>Client :</strong> Lucas Moreau<br>
                    <strong>Adresse :</strong> 654 Place de la République, Nice<br>
                    <strong>Téléphone :</strong> 0567890123<br>
                    <strong>Compteur :</strong> Diehl DI567890<br>
                    <strong>Statut :</strong> <span class="success">✅ Valide</span>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <h3>🔧 Dépannage :</h3>
            <ul>
                <li><strong>Caméra ne s'ouvre pas :</strong> Autoriser l'accès caméra dans le navigateur</li>
                <li><strong>QR non détecté :</strong> Rapprocher/éloigner le téléphone, améliorer l'éclairage</li>
                <li><strong>Détection lente :</strong> Utiliser Chrome pour de meilleures performances</li>
                <li><strong>Test sans caméra :</strong> Utiliser les boutons "✅ Test QR001" directement</li>
            </ul>
        </div>
    </div>

    <script>
        // Générer les QR codes (codes réels de la base de données)
        const qrCodes = [
            { id: 'qr001', text: 'QR-2025-0001' },
            { id: 'qr002', text: 'QR123' },
            { id: 'qr003', text: 'AQ-21-DUP-JEA-353400' },
            { id: 'qr999', text: '999' },
            { id: 'qr004', text: 'QR-TEST-BENALI' },
            { id: 'qr005', text: 'AQ-22-MAR-PIE-351916' }
        ];

        qrCodes.forEach(qr => {
            QRCode.toCanvas(document.getElementById(qr.id), qr.text, {
                width: 150,
                height: 150,
                margin: 1,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('Erreur génération QR', qr.id, error);
                    document.getElementById(qr.id).innerHTML = `<div style="line-height: 150px; color: #999;">Erreur QR</div>`;
                } else {
                    console.log('QR généré:', qr.id);
                }
            });
        });

        // Ajouter des informations de debug
        console.log('📱 Page QR Codes de Test chargée');
        console.log('🎯 Codes disponibles:', qrCodes.map(qr => qr.text));
        console.log('🔗 Scanner URL: http://localhost:3002/technician-dashboard');
    </script>
</body>
</html>
