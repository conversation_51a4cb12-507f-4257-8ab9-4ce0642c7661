const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Route pour récupérer toutes les factures
router.get('/factures', async (req, res) => {
  try {
    console.log('🔍 Récupération de toutes les factures...');

    const facturesQuery = `
      SELECT 
        f.idFact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        cons.consommationPre,
        cons.consommationActuelle,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        cont.codeQr
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idConst = cons.idCons
      LEFT JOIN Contract cont ON cons.idCont = cont.idContract
      LEFT JOIN Client c ON cont.idClient = c.idClient
      ORDER BY f.date DESC, f.idFact DESC
    `;

    const result = await pool.query(facturesQuery);

    console.log(`✅ ${result.rows.length} facture(s) trouvée(s)`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(facture => ({
        idFact: facture.idfact,
        date: facture.date,
        montant: facture.montant,
        periode: facture.periode,
        reference: facture.reference,
        status: facture.status,
        consommationPre: facture.consommationpre,
        consommationActuelle: facture.consommationactuelle,
        client_nom: facture.client_nom,
        client_prenom: facture.client_prenom,
        client_adresse: facture.client_adresse,
        codeQr: facture.codeqr
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures',
      error: error.message
    });
  }
});

// Route pour créer une nouvelle facture
router.post('/factures', async (req, res) => {
  const {
    idConst,
    montant,
    periode,
    reference,
    status = 'non payée'
  } = req.body;

  try {
    console.log('📝 Création d\'une nouvelle facture...');

    const insertQuery = `
      INSERT INTO Facture (
        date,
        idConst,
        montant,
        periode,
        reference,
        status
      ) VALUES (NOW(), $1, $2, $3, $4, $5)
      RETURNING *
    `;

    const values = [idConst, montant, periode, reference, status];

    const result = await pool.query(insertQuery, values);
    const newFacture = result.rows[0];

    console.log('✅ Facture créée avec succès:', newFacture.idfact);

    res.json({
      success: true,
      message: 'Facture créée avec succès',
      data: {
        idFact: newFacture.idfact,
        date: newFacture.date,
        montant: newFacture.montant,
        periode: newFacture.periode,
        reference: newFacture.reference,
        status: newFacture.status
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la facture',
      error: error.message
    });
  }
});

// Route pour récupérer une facture par ID
router.get('/factures/:id', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération de la facture ID: ${id}`);

    const factureQuery = `
      SELECT 
        f.idFact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        cons.consommationPre,
        cons.consommationActuelle,
        cons.jours,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cont.codeQr,
        cont.marqueCompteur,
        cont.numSerieCompteur
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idConst = cons.idCons
      LEFT JOIN Contract cont ON cons.idCont = cont.idContract
      LEFT JOIN Client c ON cont.idClient = c.idClient
      WHERE f.idFact = $1
    `;

    const result = await pool.query(factureQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    const facture = result.rows[0];

    res.json({
      success: true,
      data: {
        idFact: facture.idfact,
        date: facture.date,
        montant: facture.montant,
        periode: facture.periode,
        reference: facture.reference,
        status: facture.status,
        consommation: {
          consommationPre: facture.consommationpre,
          consommationActuelle: facture.consommationactuelle,
          jours: facture.jours
        },
        client: {
          nom: facture.client_nom,
          prenom: facture.client_prenom,
          adresse: facture.client_adresse,
          ville: facture.client_ville,
          tel: facture.client_tel,
          email: facture.client_email
        },
        compteur: {
          codeQr: facture.codeqr,
          marque: facture.marquecompteur,
          numSerie: facture.numseriecompteur
        }
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la facture',
      error: error.message
    });
  }
});

// Route pour mettre à jour le statut d'une facture
router.put('/factures/:id/status', async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  try {
    console.log(`🔄 Mise à jour du statut de la facture ID: ${id} vers: ${status}`);

    const updateQuery = `
      UPDATE Facture 
      SET status = $1
      WHERE idFact = $2
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    const updatedFacture = result.rows[0];

    res.json({
      success: true,
      message: 'Statut de la facture mis à jour avec succès',
      data: {
        idFact: updatedFacture.idfact,
        status: updatedFacture.status
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut',
      error: error.message
    });
  }
});

// Route pour récupérer les factures d'un client
router.get('/clients/:id/factures', async (req, res) => {
  const { id } = req.params;
  
  try {
    console.log(`🔍 Récupération des factures pour le client ID: ${id}`);

    const facturesQuery = `
      SELECT 
        f.idFact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idConst = cons.idCons
      LEFT JOIN Contract cont ON cons.idCont = cont.idContract
      WHERE cont.idClient = $1
      ORDER BY f.date DESC
    `;

    const result = await pool.query(facturesQuery, [id]);

    console.log(`✅ ${result.rows.length} facture(s) trouvée(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows.map(facture => ({
        idFact: facture.idfact,
        date: facture.date,
        montant: facture.montant,
        periode: facture.periode,
        reference: facture.reference,
        status: facture.status
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures',
      error: error.message
    });
  }
});

module.exports = router;
