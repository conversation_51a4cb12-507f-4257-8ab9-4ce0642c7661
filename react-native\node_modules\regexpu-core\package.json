{"name": "regexpu-core", "version": "6.2.0", "description": "regexpu’s core functionality (i.e. `rewritePattern(pattern, flag)`), capable of translating ES6 Unicode regular expressions to ES5.", "homepage": "https://mths.be/regexpu", "main": "rewrite-pattern.js", "engines": {"node": ">=4"}, "keywords": ["codegen", "desugaring", "ecmascript", "es5", "es6", "harmony", "javascript", "refactoring", "regex", "regexp", "regular expressions", "rewriting", "syntax", "transformation", "transpile", "transpiler", "unicode"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regexpu-core.git"}, "bugs": "https://github.com/mathiasbynens/regexpu-core/issues", "files": ["LICENSE-MIT.txt", "rewrite-pattern.js", "data/all-characters.js", "data/character-class-escape-sets.js", "data/i-bmp-mappings.js", "data/iu-foldings.js", "data/iu-mappings.js"], "scripts": {"build": "node scripts/index.js", "test": "node --test tests/tests.js", "test-node6": "mocha tests", "cover": "NODE_V8_COVERAGE=coverage node --test  --experimental-test-coverage tests/tests.js"}, "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "devDependencies": {"jsesc": "^3.0.2", "@unicode/unicode-16.0.0": "^1.6.2"}}