{"version": 3, "sources": ["updateHandlers.ts"], "names": ["registerHandler", "RNGestureHandlerModule", "filterConfig", "scheduleFlushOperations", "ghQueueMicrotask", "extractGestureRelations", "checkGestureCallbacksForWorklets", "ALLOWED_PROPS", "updateHandlers", "preparedGesture", "gestureConfig", "newGestures", "prepare", "i", "length", "handler", "attachedGestures", "handlerTag", "handlers", "isMounted", "shouldUpdateSharedValueIfUsed", "gestureId", "shouldUseReanimated", "config", "updateGestureHandler", "testId", "animatedHandlers", "newHandlersValue", "filter", "g", "map", "value"], "mappings": "AACA,SAASA,eAAT,QAAgC,wBAAhC;AACA,OAAOC,sBAAP,MAAmC,iCAAnC;AACA,SAASC,YAAT,EAAuBC,uBAAvB,QAAsD,aAAtD;AAEA,SAASC,gBAAT,QAAiC,2BAAjC;AAEA,SACEC,uBADF,EAEEC,gCAFF,EAGEC,aAHF,QAIO,SAJP;AAMA,OAAO,SAASC,cAAT,CACLC,eADK,EAELC,aAFK,EAGLC,WAHK,EAIL;AACAD,EAAAA,aAAa,CAACE,OAAd;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,WAAW,CAACG,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AAC3C,UAAME,OAAO,GAAGN,eAAe,CAACO,gBAAhB,CAAiCH,CAAjC,CAAhB;AACAP,IAAAA,gCAAgC,CAACS,OAAD,CAAhC,CAF2C,CAI3C;AACA;;AACA,QAAIJ,WAAW,CAACE,CAAD,CAAX,CAAeI,UAAf,KAA8BF,OAAO,CAACE,UAA1C,EAAsD;AACpDN,MAAAA,WAAW,CAACE,CAAD,CAAX,CAAeI,UAAf,GAA4BF,OAAO,CAACE,UAApC;AACAN,MAAAA,WAAW,CAACE,CAAD,CAAX,CAAeK,QAAf,CAAwBD,UAAxB,GAAqCF,OAAO,CAACE,UAA7C;AACD;AACF,GAbD,CAeA;AACA;AACA;;;AACAb,EAAAA,gBAAgB,CAAC,MAAM;AACrB,QAAI,CAACK,eAAe,CAACU,SAArB,EAAgC;AAC9B;AACD,KAHoB,CAKrB;;;AACA,QAAIC,6BAA6B,GAC/BX,eAAe,CAACO,gBAAhB,CAAiCF,MAAjC,KAA4CH,WAAW,CAACG,MAD1D;;AAGA,SAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,WAAW,CAACG,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AAC3C,YAAME,OAAO,GAAGN,eAAe,CAACO,gBAAhB,CAAiCH,CAAjC,CAAhB,CAD2C,CAG3C;AACA;;AACA,UACEE,OAAO,CAACG,QAAR,CAAiBG,SAAjB,KAA+BV,WAAW,CAACE,CAAD,CAAX,CAAeK,QAAf,CAAwBG,SAAvD,KACCV,WAAW,CAACE,CAAD,CAAX,CAAeS,mBAAf,IAAsCP,OAAO,CAACO,mBAD/C,CADF,EAGE;AACAF,QAAAA,6BAA6B,GAAG,IAAhC;AACD;;AAEDL,MAAAA,OAAO,CAACQ,MAAR,GAAiBZ,WAAW,CAACE,CAAD,CAAX,CAAeU,MAAhC;AACAR,MAAAA,OAAO,CAACG,QAAR,GAAmBP,WAAW,CAACE,CAAD,CAAX,CAAeK,QAAlC;AAEAjB,MAAAA,sBAAsB,CAACuB,oBAAvB,CACET,OAAO,CAACE,UADV,EAEEf,YAAY,CACVa,OAAO,CAACQ,MADE,EAEVhB,aAFU,EAGVF,uBAAuB,CAACU,OAAD,CAHb,CAFd;AASAf,MAAAA,eAAe,CAACe,OAAO,CAACE,UAAT,EAAqBF,OAArB,EAA8BA,OAAO,CAACQ,MAAR,CAAeE,MAA7C,CAAf;AACD;;AAED,QAAIhB,eAAe,CAACiB,gBAAhB,IAAoCN,6BAAxC,EAAuE;AACrE,YAAMO,gBAAgB,GAAGlB,eAAe,CAACO,gBAAhB,CACtBY,MADsB,CACdC,CAAD,IAAOA,CAAC,CAACP,mBADM,EACe;AADf,OAEtBQ,GAFsB,CAEjBD,CAAD,IAAOA,CAAC,CAACX,QAFS,CAAzB;AAMAT,MAAAA,eAAe,CAACiB,gBAAhB,CAAiCK,KAAjC,GAAyCJ,gBAAzC;AACD;;AAEDxB,IAAAA,uBAAuB;AACxB,GA/Ce,CAAhB;AAgDD", "sourcesContent": ["import { GestureType, HandlerCallbacks } from '../gesture';\nimport { registerHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { filterConfig, scheduleFlushOperations } from '../../utils';\nimport { ComposedGesture } from '../gestureComposition';\nimport { ghQueueMicrotask } from '../../../ghQueueMicrotask';\nimport { AttachedGestureState } from './types';\nimport {\n  extractGestureRelations,\n  checkGestureCallbacksForWorklets,\n  ALLOWED_PROPS,\n} from './utils';\n\nexport function updateHandlers(\n  preparedGesture: AttachedGestureState,\n  gestureConfig: ComposedGesture | GestureType,\n  newGestures: GestureType[]\n) {\n  gestureConfig.prepare();\n\n  for (let i = 0; i < newGestures.length; i++) {\n    const handler = preparedGesture.attachedGestures[i];\n    checkGestureCallbacksForWorklets(handler);\n\n    // Only update handlerTag when it's actually different, it may be the same\n    // if gesture config object is wrapped with useMemo\n    if (newGestures[i].handlerTag !== handler.handlerTag) {\n      newGestures[i].handlerTag = handler.handlerTag;\n      newGestures[i].handlers.handlerTag = handler.handlerTag;\n    }\n  }\n\n  // Use queueMicrotask to extract handlerTags, because when it's ran, all refs should be updated\n  // and handlerTags in BaseGesture references should be updated in the loop above (we need to wait\n  // in case of external relations)\n  ghQueueMicrotask(() => {\n    if (!preparedGesture.isMounted) {\n      return;\n    }\n\n    // If amount of gesture configs changes, we need to update the callbacks in shared value\n    let shouldUpdateSharedValueIfUsed =\n      preparedGesture.attachedGestures.length !== newGestures.length;\n\n    for (let i = 0; i < newGestures.length; i++) {\n      const handler = preparedGesture.attachedGestures[i];\n\n      // If the gestureId is different (gesture isn't wrapped with useMemo or its dependencies changed),\n      // we need to update the shared value, assuming the gesture runs on UI thread or the thread changed\n      if (\n        handler.handlers.gestureId !== newGestures[i].handlers.gestureId &&\n        (newGestures[i].shouldUseReanimated || handler.shouldUseReanimated)\n      ) {\n        shouldUpdateSharedValueIfUsed = true;\n      }\n\n      handler.config = newGestures[i].config;\n      handler.handlers = newGestures[i].handlers;\n\n      RNGestureHandlerModule.updateGestureHandler(\n        handler.handlerTag,\n        filterConfig(\n          handler.config,\n          ALLOWED_PROPS,\n          extractGestureRelations(handler)\n        )\n      );\n\n      registerHandler(handler.handlerTag, handler, handler.config.testId);\n    }\n\n    if (preparedGesture.animatedHandlers && shouldUpdateSharedValueIfUsed) {\n      const newHandlersValue = preparedGesture.attachedGestures\n        .filter((g) => g.shouldUseReanimated) // Ignore gestures that shouldn't run on UI\n        .map((g) => g.handlers) as unknown as HandlerCallbacks<\n        Record<string, unknown>\n      >[];\n\n      preparedGesture.animatedHandlers.value = newHandlersValue;\n    }\n\n    scheduleFlushOperations();\n  });\n}\n"]}