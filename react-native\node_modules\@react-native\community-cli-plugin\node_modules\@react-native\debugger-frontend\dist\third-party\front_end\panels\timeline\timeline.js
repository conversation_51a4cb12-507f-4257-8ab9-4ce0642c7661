import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/theme_support/theme_support.js";import*as i from"../../models/trace/trace.js";import*as n from"../../core/sdk/sdk.js";import*as r from"../../core/common/common.js";import*as a from"./components/components.js";import*as s from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as o from"../../core/platform/platform.js";import*as l from"../../core/root/root.js";import*as c from"../../models/bindings/bindings.js";import*as d from"../../models/timeline_model/timeline_model.js";import*as h from"../../ui/legacy/legacy.js";import*as m from"../../services/trace_bounds/trace_bounds.js";import*as p from"../../ui/legacy/components/utils/utils.js";import*as u from"../../ui/legacy/components/data_grid/data_grid.js";import*as g from"../../models/source_map_scopes/source_map_scopes.js";import*as T from"../../core/host/host.js";import*as v from"../../models/workspace/workspace.js";import*as f from"../../ui/components/adorners/adorners.js";import*as y from"../../ui/components/panel_feedback/panel_feedback.js";import*as w from"../../ui/visual_logging/visual_logging.js";import*as S from"../mobile_throttling/mobile_throttling.js";import*as b from"../../ui/components/menus/menus.js";import*as C from"../../models/text_utils/text_utils.js";import*as P from"../layer_viewer/layer_viewer.js";const k={sSelfS:"{PH1} (self {PH2})"},E=e.i18n.registerUIStrings("panels/timeline/AppenderUtils.ts",k),M=e.i18n.getLocalizedString.bind(void 0,E);function F(e){const i={padding:4,height:17,collapsible:!0,color:t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),backgroundColor:t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),nestingLevel:0,shareHeaderLine:!0};return Object.assign(i,e)}function I(e,t,i,n,r,a,s){const o={startLevel:e,name:t,style:i,selectable:n,expanded:r,showStackContextMenu:s};return n&&a&&(o.track=a),o}function L(t,n){const r=i.Helpers.Timing.microSecondsToMilliseconds(t||0);if(r===i.Types.Timing.MilliSeconds(0))return"";const a=i.Helpers.Timing.microSecondsToMilliseconds(n||0),s=1e-6;return Math.abs(r-a)>s&&a>s?M(k.sSelfS,{PH1:e.TimeUtilities.millisToString(r,!0),PH2:e.TimeUtilities.millisToString(a,!0)}):e.TimeUtilities.millisToString(r,!0)}function x(e,t){let i=0;const n=e.ts,r=e.ts+(e.dur||0);for(;i<t.length&&n<t[i];)++i;return t[i]=r,i}function R(e,t,i){const n=e.entryDecorations[t]||[];n.push(i),e.entryDecorations[t]=n}var D=Object.freeze({__proto__:null,buildGroupStyle:F,buildTrackHeader:I,getFormattedTime:L,getEventLevel:x,addDecorationToEvent:R});const A={animations:"Animations"},B=e.i18n.registerUIStrings("panels/timeline/AnimationsTrackAppender.ts",A),H=e.i18n.getLocalizedString.bind(void 0,B);class N{appenderName="Animations";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){const i=this.#t.Animations.animations;return 0===i.length?e:(this.#i(e,t),this.#e.appendEventsAtLevel(i,e,this))}#i(e,t){const i=F({useFirstLineForOverview:!1}),n=I(e,H(A.animations),i,!0,t);this.#e.registerTrackForGroup(n,this)}colorForEvent(){return t.ThemeSupport.instance().getComputedValue("--app-color-rendering")}titleForEvent(e){return e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:L(e.dur)}}}var U=Object.freeze({__proto__:null,AnimationsTrackAppender:N});class W extends Event{duration;static eventName="traceload";constructor(e){super(W.eventName,{bubbles:!0,composed:!0}),this.duration=e}}var V=Object.freeze({__proto__:null,TraceLoadEvent:W});class O{x;y;width;height;color;outlineColor;constructor([e,t,i,n]){this.x=e,this.y=t,this.width=i,this.height=n,this.color={r:238,g:111,b:99,a:.4},this.outlineColor={r:238,g:111,b:99,a:.7}}}let z;class G{static instance(e={forceNew:null}){const{forceNew:t}=e;return z&&!t||(z=new G),z}linkify(e,t){const i=document.createElement("span"),r=e,{x:a,y:s,width:o,height:l}=r;return i.textContent=`Location: [${a},${s}], Size: [${o}x${l}]`,i.addEventListener("mouseover",(()=>n.OverlayModel.OverlayModel.highlightRect(r))),i.addEventListener("mouseleave",(()=>n.OverlayModel.OverlayModel.clearHighlight())),i}}var _=Object.freeze({__proto__:null,CLSRect:O,Linkifier:G});const j={loading:"Loading",experience:"Experience",scripting:"Scripting",rendering:"Rendering",painting:"Painting",gpu:"GPU",async:"Async",system:"System",idle:"Idle",task:"Task",other:"Other",animation:"Animation",event:"Event",requestMainThreadFrame:"Request Main Thread Frame",frameStart:"Frame Start",frameStartMainThread:"Frame Start (main thread)",drawFrame:"Draw Frame",profilingOverhead:"Profiling Overhead",hitTest:"Hit Test",scheduleStyleRecalculation:"Schedule Style Recalculation",recalculateStyle:"Recalculate Style",invalidateLayout:"Invalidate Layout",layerize:"Layerize",layout:"Layout",paintSetup:"Paint Setup",paintImage:"Paint Image",prePaint:"Pre-Paint",updateLayer:"Update Layer",updateLayerTree:"Update Layer Tree",paint:"Paint",rasterizePaint:"Rasterize Paint",scroll:"Scroll",commit:"Commit",compositeLayers:"Composite Layers",computeIntersections:"Compute Intersections",parseHtml:"Parse HTML",parseStylesheet:"Parse Stylesheet",installTimer:"Install Timer",removeTimer:"Remove Timer",timerFired:"Timer Fired",xhrReadyStateChange:"`XHR` Ready State Change",xhrLoad:"`XHR` Load",compileScript:"Compile Script",cacheScript:"Cache Script Code",compileCode:"Compile Code",optimizeCode:"Optimize Code",evaluateScript:"Evaluate Script",compileModule:"Compile Module",cacheModule:"Cache Module Code",evaluateModule:"Evaluate Module",streamingCompileTask:"Streaming Compile Task",waitingForNetwork:"Waiting for Network",parseAndCompile:"Parse and Compile",deserializeCodeCache:"Deserialize Code Cache",streamingWasmResponse:"Streaming Wasm Response",compiledWasmModule:"Compiled Wasm Module",cachedWasmModule:"Cached Wasm Module",wasmModuleCacheHit:"Wasm Module Cache Hit",wasmModuleCacheInvalid:"Wasm Module Cache Invalid",frameStartedLoading:"Frame Started Loading",onloadEvent:"Onload Event",domcontentloadedEvent:"DOMContentLoaded Event",firstPaint:"First Paint",firstContentfulPaint:"First Contentful Paint",largestContentfulPaint:"Largest Contentful Paint",timestamp:"Timestamp",consoleTime:"Console Time",userTiming:"User Timing",willSendRequest:"Will Send Request",sendRequest:"Send Request",receiveResponse:"Receive Response",finishLoading:"Finish Loading",receiveData:"Receive Data",runMicrotasks:"Run Microtasks",functionCall:"Function Call",gcEvent:"GC Event",majorGc:"Major GC",minorGc:"Minor GC",requestAnimationFrame:"Request Animation Frame",cancelAnimationFrame:"Cancel Animation Frame",animationFrameFired:"Animation Frame Fired",requestIdleCallback:"Request Idle Callback",cancelIdleCallback:"Cancel Idle Callback",fireIdleCallback:"Fire Idle Callback",createWebsocket:"Create WebSocket",sendWebsocketHandshake:"Send WebSocket Handshake",receiveWebsocketHandshake:"Receive WebSocket Handshake",destroyWebsocket:"Destroy WebSocket",embedderCallback:"Embedder Callback",imageDecode:"Image Decode",imageResize:"Image Resize",domGc:"DOM GC",cppGc:"CPP GC",encrypt:"Encrypt",encryptReply:"Encrypt Reply",decrypt:"Decrypt",decryptReply:"Decrypt Reply",digest:"Digest",digestReply:"Digest Reply",sign:"Sign",signReply:"Sign Reply",verify:"Verify",verifyReply:"Verify Reply",asyncTask:"Async Task",layoutShift:"Layout Shift",eventTiming:"Event Timing",jsFrame:"JS Frame"},q=e.i18n.registerUIStrings("panels/timeline/EventUICategory.ts",j),J=e.i18n.getLocalizedString.bind(void 0,q);class ${title;category;hidden;constructor(e,t,i=!1){this.title=e,this.category=t,this.hidden=i}}class X{name;title;visible;childColor;colorInternal;hiddenInternal;constructor(e,t,i,n,r){this.name=e,this.title=t,this.visible=i,this.childColor=n,this.colorInternal=r,this.hidden=!1}get hidden(){return Boolean(this.hiddenInternal)}get color(){return this.getComputedColorValue()}getCSSValue(){return`var(${this.colorInternal})`}getComputedColorValue(){return t.ThemeSupport.instance().getComputedValue(this.colorInternal)}set hidden(e){this.hiddenInternal=e}}let K,Y;function Z(e){return function(){if(Y)return Y;const e=Q();return Y=new Map([["RunTask",new $(J(j.task),e.Other)],["ProfileCall",new $(J(j.jsFrame),e.Scripting)],["Program",new $(J(j.other),e.Other)],["CpuProfiler::StartProfiling",new $(J(j.profilingOverhead),e.Other)],["Animation",new $(J(j.animation),e.Rendering)],["EventDispatch",new $(J(j.event),e.Scripting)],["RequestMainThreadFrame",new $(J(j.requestMainThreadFrame),e.Rendering,!0)],["BeginFrame",new $(J(j.frameStart),e.Rendering,!0)],["BeginMainThreadFrame",new $(J(j.frameStartMainThread),e.Rendering,!0)],["DrawFrame",new $(J(j.drawFrame),e.Rendering,!0)],["HitTest",new $(J(j.hitTest),e.Rendering)],["ScheduleStyleRecalculation",new $(J(j.scheduleStyleRecalculation),e.Rendering)],["RecalculateStyles",new $(J(j.recalculateStyle),e.Rendering)],["UpdateLayoutTree",new $(J(j.recalculateStyle),e.Rendering)],["InvalidateLayout",new $(J(j.invalidateLayout),e.Rendering,!0)],["Layerize",new $(J(j.layerize),e.Rendering)],["Layout",new $(J(j.layout),e.Rendering)],["PaintSetup",new $(J(j.paintSetup),e.Painting)],["PaintImage",new $(J(j.paintImage),e.Painting,!0)],["UpdateLayer",new $(J(j.updateLayer),e.Painting,!0)],["UpdateLayerTree",new $(J(j.updateLayerTree),e.Rendering)],["Paint",new $(J(j.paint),e.Painting)],["PrePaint",new $(J(j.prePaint),e.Rendering)],["RasterTask",new $(J(j.rasterizePaint),e.Painting)],["ScrollLayer",new $(J(j.scroll),e.Rendering)],["Commit",new $(J(j.commit),e.Painting)],["CompositeLayers",new $(J(j.compositeLayers),e.Painting)],["ComputeIntersections",new $(J(j.computeIntersections),e.Rendering)],["ParseHTML",new $(J(j.parseHtml),e.Loading)],["ParseAuthorStyleSheet",new $(J(j.parseStylesheet),e.Loading)],["TimerInstall",new $(J(j.installTimer),e.Scripting)],["TimerRemove",new $(J(j.removeTimer),e.Scripting)],["TimerFire",new $(J(j.timerFired),e.Scripting)],["XHRReadyStateChange",new $(J(j.xhrReadyStateChange),e.Scripting)],["XHRLoad",new $(J(j.xhrLoad),e.Scripting)],["v8.compile",new $(J(j.compileScript),e.Scripting)],["v8.produceCache",new $(J(j.cacheScript),e.Scripting)],["V8.CompileCode",new $(J(j.compileCode),e.Scripting)],["V8.OptimizeCode",new $(J(j.optimizeCode),e.Scripting)],["EvaluateScript",new $(J(j.evaluateScript),e.Scripting)],["V8.CompileModule",new $(J(j.compileModule),e.Scripting)],["v8.produceModuleCache",new $(J(j.cacheModule),e.Scripting)],["v8.evaluateModule",new $(J(j.evaluateModule),e.Scripting)],["v8.parseOnBackground",new $(J(j.streamingCompileTask),e.Other)],["v8.parseOnBackgroundWaiting",new $(J(j.waitingForNetwork),e.Idle)],["v8.parseOnBackgroundParsing",new $(J(j.parseAndCompile),e.Scripting)],["v8.deserializeOnBackground",new $(J(j.deserializeCodeCache),e.Scripting)],["V8.FinalizeDeserialization",new $(J(j.profilingOverhead),e.Other)],["v8.wasm.streamFromResponseCallback",new $(J(j.streamingWasmResponse),e.Scripting)],["v8.wasm.compiledModule",new $(J(j.compiledWasmModule),e.Scripting)],["v8.wasm.cachedModule",new $(J(j.cachedWasmModule),e.Scripting)],["v8.wasm.moduleCacheHit",new $(J(j.wasmModuleCacheHit),e.Scripting)],["v8.wasm.moduleCacheInvalid",new $(J(j.wasmModuleCacheInvalid),e.Scripting)],["FrameStartedLoading",new $(J(j.frameStartedLoading),e.Loading,!0)],["MarkLoad",new $(J(j.onloadEvent),e.Scripting,!0)],["MarkDOMContent",new $(J(j.domcontentloadedEvent),e.Scripting,!0)],["firstPaint",new $(J(j.firstPaint),e.Painting,!0)],["firstContentfulPaint",new $(J(j.firstContentfulPaint),e.Rendering,!0)],["largestContentfulPaint::Candidate",new $(J(j.largestContentfulPaint),e.Rendering,!0)],["TimeStamp",new $(J(j.timestamp),e.Scripting)],["ConsoleTime",new $(J(j.consoleTime),e.Scripting)],["UserTiming",new $(J(j.userTiming),e.Scripting)],["ResourceWillSendRequest",new $(J(j.willSendRequest),e.Loading)],["ResourceSendRequest",new $(J(j.sendRequest),e.Loading)],["ResourceReceiveResponse",new $(J(j.receiveResponse),e.Loading)],["ResourceFinish",new $(J(j.finishLoading),e.Loading)],["ResourceReceivedData",new $(J(j.receiveData),e.Loading)],["RunMicrotasks",new $(J(j.runMicrotasks),e.Scripting)],["FunctionCall",new $(J(j.functionCall),e.Scripting)],["GCEvent",new $(J(j.gcEvent),e.Scripting)],["MajorGC",new $(J(j.majorGc),e.Scripting)],["MinorGC",new $(J(j.minorGc),e.Scripting)],["V8.GCIncrementalMarking",new $(J(j.gcEvent),e.Scripting)],["CppGC.IncrementalSweep",new $(J(j.cppGc),e.Scripting)],["RequestAnimationFrame",new $(J(j.requestAnimationFrame),e.Scripting)],["CancelAnimationFrame",new $(J(j.cancelAnimationFrame),e.Scripting)],["FireAnimationFrame",new $(J(j.animationFrameFired),e.Scripting)],["RequestIdleCallback",new $(J(j.requestIdleCallback),e.Scripting)],["CancelIdleCallback",new $(J(j.cancelIdleCallback),e.Scripting)],["FireIdleCallback",new $(J(j.fireIdleCallback),e.Scripting)],["WebSocketCreate",new $(J(j.createWebsocket),e.Scripting)],["WebSocketSendHandshakeRequest",new $(J(j.sendWebsocketHandshake),e.Scripting)],["WebSocketReceiveHandshakeResponse",new $(J(j.receiveWebsocketHandshake),e.Scripting)],["WebSocketDestroy",new $(J(j.destroyWebsocket),e.Scripting)],["EmbedderCallback",new $(J(j.embedderCallback),e.Scripting)],["Decode Image",new $(J(j.imageDecode),e.Painting)],["Resize Image",new $(J(j.imageResize),e.Painting)],["GPUTask",new $(J(j.gpu),e.GPU)],["BlinkGC.AtomicPhase",new $(J(j.domGc),e.Scripting)],["BlinkGC.AtomicPhase",new $(J(j.domGc),e.Scripting)],["DoEncrypt",new $(J(j.encrypt),e.Scripting)],["DoEncryptReply",new $(J(j.encryptReply),e.Scripting)],["DoDecrypt",new $(J(j.decrypt),e.Scripting)],["DoDecryptReply",new $(J(j.decryptReply),e.Scripting)],["DoDigest",new $(J(j.digest),e.Scripting)],["DoDigestReply",new $(J(j.digestReply),e.Scripting)],["DoSign",new $(J(j.sign),e.Scripting)],["DoSignReply",new $(J(j.signReply),e.Scripting)],["DoVerify",new $(J(j.verify),e.Scripting)],["DoVerifyReply",new $(J(j.verifyReply),e.Scripting)],["AsyncTask",new $(J(j.asyncTask),e.Async)],["LayoutShift",new $(J(j.layoutShift),e.Experience)],["EventTiming",new $(J(j.eventTiming),e.Experience)]]),Y}().get(e)}function Q(){return K||(K={Loading:new X("loading",J(j.loading),!0,"--app-color-loading-children","--app-color-loading"),Experience:new X("experience",J(j.experience),!1,"--app-color-rendering-children","--app-color-rendering"),Scripting:new X("scripting",J(j.scripting),!0,"--app-color-scripting-children","--app-color-scripting"),Rendering:new X("rendering",J(j.rendering),!0,"--app-color-rendering-children","--app-color-rendering"),Painting:new X("painting",J(j.painting),!0,"--app-color-painting-children","--app-color-painting"),GPU:new X("gpu",J(j.gpu),!1,"--app-color-painting-children","--app-color-painting"),Async:new X("async",J(j.async),!1,"--app-color-async-children","--app-color-async"),Other:new X("other",J(j.system),!1,"--app-color-system-children","--app-color-system"),Idle:new X("idle",J(j.idle),!1,"--app-color-idle-children","--app-color-idle")},K)}var ee=Object.freeze({__proto__:null,EventCategories:["Loading","Experience","Scripting","Rendering","Painting","GPU","Async","Other","Idle"],TimelineRecordStyle:$,TimelineCategory:X,getEventStyle:Z,getCategoryStyles:Q});const te={gpu:"GPU"},ie=e.i18n.registerUIStrings("panels/timeline/GPUTrackAppender.ts",te),ne=e.i18n.getLocalizedString.bind(void 0,ie);class re{appenderName="GPU";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){const i=this.#t.GPU.mainGPUThreadTasks;return 0===i.length?e:(this.#i(e,t),this.#e.appendEventsAtLevel(i,e,this))}#i(e,t){const i=F({shareHeaderLine:!1}),n=I(e,ne(te.gpu),i,!0,t);this.#e.registerTrackForGroup(n,this)}colorForEvent(e){if(!i.Types.TraceEvents.isTraceEventGPUTask(e))throw new Error(`Unexpected GPU Task: The event's type is '${e.name}'`);return t.ThemeSupport.instance().getComputedValue("--app-color-painting")}titleForEvent(e){return i.Types.TraceEvents.isTraceEventGPUTask(e)?"GPU":e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:L(e.dur)}}}var ae=Object.freeze({__proto__:null,GPUTrackAppender:re});const se={interactions:"Interactions"},oe=e.i18n.registerUIStrings("panels/timeline/InteractionsTrackAppender.ts",se),le=e.i18n.getLocalizedString.bind(void 0,oe);class ce{appenderName="Interactions";#n;#e;#t;constructor(e,t,i){this.#e=e,this.#n=i,this.#t=t}appendTrackAtLevel(e,t){return 0===this.#t.UserInteractions.interactionEvents.length?e:(this.#i(e,t),this.#r(e))}#i(e,t){const i=F({shareHeaderLine:!1,collapsible:this.#t.UserInteractions.interactionEvents.length>0}),n=I(e,le(se.interactions),i,!0,t);this.#e.registerTrackForGroup(n,this)}#r(e){const{interactionEventsWithNoNesting:t,interactionsOverThreshold:i}=this.#t.UserInteractions;return this.#e.appendEventsAtLevel(t,e,this,((e,t)=>{i.has(e)&&void 0!==t&&this.#a(e,t)}))}#a(e,t){const n=this.#e.getFlameChartTimelineData().entryDecorations[t]||[];n.push({type:"CANDY",startAtTime:i.Handlers.ModelHandlers.UserInteractions.LONG_INTERACTION_THRESHOLD,endAtTime:e.processingEnd},{type:"WARNING_TRIANGLE",customEndTime:e.processingEnd}),this.#e.getFlameChartTimelineData().entryDecorations[t]=n}colorForEvent(e){let t=this.titleForEvent(e);return i.Types.TraceEvents.isSyntheticInteractionEvent(e)&&(t+=e.interactionId),this.#n.colorForID(t)}titleForEvent(e){return i.Types.TraceEvents.isSyntheticInteractionEvent(e)?de(e):e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:L(e.dur)}}}function de(e){const t=i.Handlers.ModelHandlers.UserInteractions.categoryOfInteraction(e);return"OTHER"===t?"Other":"KEYBOARD"===t?"Keyboard":"POINTER"===t?"Pointer":e.type}var he=Object.freeze({__proto__:null,InteractionsTrackAppender:ce,titleForInteractionEvent:de});const me={layoutShifts:"Layout Shifts"},pe=e.i18n.registerUIStrings("panels/timeline/LayoutShiftsTrackAppender.ts",me),ue=e.i18n.getLocalizedString.bind(void 0,pe);class ge{appenderName="LayoutShifts";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){return 0===this.#t.LayoutShifts.clusters.length?e:(this.#i(e,t),this.#s(e))}#i(e,t){const i=F({collapsible:!1}),n=I(e,ue(me.layoutShifts),i,!0,t);this.#e.registerTrackForGroup(n,this)}#s(e){const t=this.#t.LayoutShifts.clusters.flatMap((e=>e.events)),n=i.Types.Timing.MicroSeconds(5e3);return this.#e.appendEventsAtLevel(t,e,this,((e,t)=>{this.#e.getFlameChartTimelineData().entryTotalTimes[t]=i.Helpers.Timing.microSecondsToMilliseconds(n)}))}colorForEvent(e){return t.ThemeSupport.instance().getComputedValue("--app-color-rendering")}titleForEvent(e){return i.Types.TraceEvents.isTraceEventLayoutShift(e)?"Layout shift":e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:L(e.dur)}}}var Te=Object.freeze({__proto__:null,LayoutShiftsTrackAppender:ge});const ve={onIgnoreList:"On ignore list",mainS:"Main — {PH1}",main:"Main",frameS:"Frame — {PH1}",workerS:"`Worker` — {PH1}",workerSS:"`Worker`: {PH1} — {PH2}",dedicatedWorker:"Dedicated `Worker`",anonymous:"(anonymous)",threadS:"Thread {PH1}",raster:"Raster",threadPool:"Thread Pool",rasterizerThreadS:"Rasterizer Thread {PH1}",threadPoolThreadS:"Thread Pool Worker {PH1}",bidderWorkletS:"Bidder Worklet — {PH1}",bidderWorklet:"Bidder Worklet",sellerWorklet:"Seller Worklet",unknownWorklet:"Auction Worklet",workletService:"Auction Worklet Service",sellerWorkletS:"Seller Worklet — {PH1}",unknownWorkletS:"Auction Worklet — {PH1}",workletServiceS:"Auction Worklet Service — {PH1}",eventDispatchS:"Event: {PH1}"},fe=e.i18n.registerUIStrings("panels/timeline/ThreadAppender.ts",ve),ye=e.i18n.getLocalizedString.bind(void 0,fe);class we{appenderName="Thread";#n;#e;#t;#o=[];#l;#c;#d;#h;#m=!1;#p=!1;threadType="MAIN_THREAD";isOnMainFrame;#u=l.Runtime.experiments.isEnabled("ignore-list-js-frames-on-timeline");#g=l.Runtime.experiments.isEnabled("timeline-show-all-events");#T;#v="";#f=null;constructor(e,t,n,a,s,o){this.#e=e,this.#n=new r.Color.Generator({min:30,max:330,count:void 0},{min:50,max:80,count:3},85),this.#n.setColorForID("","#f2ecdc"),this.#t=t,this.#c=n,this.#d=a;const l="CPU_PROFILE"===o?this.#t.Samples?.profilesInProcess.get(n)?.get(a)?.profileCalls:this.#t.Renderer?.processes.get(n)?.threads?.get(a)?.entries,c="CPU_PROFILE"===o?this.#t.Samples?.profilesInProcess.get(n)?.get(a)?.profileTree:this.#t.Renderer?.processes.get(n)?.threads?.get(a)?.tree;if(!l||!c)throw new Error(`Could not find data for thread with id ${a} in process with id ${n}`);this.#o=l,this.#l=c,this.#h=s||ye(ve.threadS,{PH1:a}),this.isOnMainFrame=Boolean(this.#t.Renderer?.processes.get(n)?.isOnMainFrame),this.threadType=o,this.#t.AuctionWorklets.worklets.has(n)&&(this.appenderName="Thread_AuctionWorklet"),this.#T=new i.EntriesFilter.EntriesFilter("CPU_PROFILE"===this.threadType?t.Samples.entryToNode:t.Renderer.entryToNode),this.#v=this.#t.Renderer?.processes.get(this.#c)?.url||""}entriesFilter(){return this.#T}processId(){return this.#c}threadId(){return this.#d}appendTrackAtLevel(e,t=!1){return 0===this.#o.length?e:(this.#m=t,this.#y(e))}setHeaderNestingLevel(e){this.#f=e}#w(e){this.#p||("RASTERIZER"===this.threadType||"THREAD_POOL"===this.threadType?this.#S(e,this.threadType):this.#i(e),this.#p=!0)}setHeaderAppended(e){this.#p=e}headerAppended(){return this.#p}#i(e){const t=F({shareHeaderLine:!1,collapsible:this.#o.length>0});null!==this.#f&&(t.nestingLevel=this.#f);const i=I(e,this.trackName(),t,!0,this.#m,null,!0);this.#e.registerTrackForGroup(i,this)}#S(e,t){const i=this.#e.getCurrentTrackCountForThreadType(t);if(0===i){const t=F({shareHeaderLine:!1,collapsible:this.#o.length>0}),i=I(e,this.trackName(),t,!1,this.#m);this.#e.getFlameChartTimelineData().groups.push(i)}const n=F({padding:2,nestingLevel:1,collapsible:!1}),r=I(e,"RASTERIZER"===this.threadType?ye(ve.rasterizerThreadS,{PH1:i+1}):ye(ve.threadPoolThreadS,{PH1:i+1}),n,!0,this.#m);this.#e.registerTrackForGroup(r,this)}trackName(){let e=null;switch(this.threadType){case"MAIN_THREAD":e=this.isOnMainFrame?ye(ve.mainS,{PH1:this.#v}):ye(ve.frameS,{PH1:this.#v});break;case"CPU_PROFILE":e=ye(ve.main);break;case"WORKER":e=this.#b();break;case"RASTERIZER":e=ye(ve.raster);break;case"THREAD_POOL":e=ye(ve.threadPool);break;case"OTHER":break;case"AUCTION_WORKLET":e=this.#C();break;default:return o.assertNever(this.threadType,`Unknown thread type: ${this.threadType}`)}let t="";return this.#t.Meta.traceIsGeneric&&(t+=` (${this.threadId()})`),(e||this.#h)+t}getUrl(){return this.#v}#C(){const e=this.#t.AuctionWorklets.worklets.get(this.#c);if(!e)return ye(ve.unknownWorklet);const t=e.host?`https://${e.host}`:"",i=t.length>0,n=e.args.data.utilityThread.tid===this.#d,r=e.args.data.v8HelperThread.tid===this.#d;if(n)return i?ye(ve.workletServiceS,{PH1:t}):ye(ve.workletService);if(r)switch(e.type){case"seller":return i?ye(ve.sellerWorkletS,{PH1:t}):ye(ve.sellerWorklet);case"bidder":return i?ye(ve.bidderWorkletS,{PH1:t}):ye(ve.bidderWorklet);case"unknown":return i?ye(ve.unknownWorkletS,{PH1:t}):ye(ve.unknownWorklet);default:o.assertNever(e.type,`Unexpected Auction Worklet Type ${e.type}`)}return i?ye(ve.unknownWorkletS,{PH1:t}):ye(ve.unknownWorklet)}#b(){const e=this.#t.Renderer?.processes.get(this.#c)?.url||"",t=this.#t.Workers.workerIdByThread.get(this.#d),i=t?this.#t.Workers.workerURLById.get(t):e;let r=i?ye(ve.workerS,{PH1:i}):ye(ve.dedicatedWorker);const a=void 0!==t&&n.TargetManager.TargetManager.instance().targetById(t);return a&&(r=ye(ve.workerSS,{PH1:a.name(),PH2:e})),r}#y(e){return this.#P(this.#l.roots,e)}#P(e,t,i=!1){const n=this.#T?.invisibleEntries()??[];let r=t;for(const a of e){let e=t;const s=a.entry,o=this.isIgnoreListedEntry(s);(!n.includes(s)&&this.#e.entryIsVisibleInTimeline(s)||this.#g)&&!(o&&i)&&(this.#k(s,t),e++);const l=this.#P(a.children,e,o);r=Math.max(l,r)}return r}#k(e,t){this.#w(t);const i=this.#e.appendEventAtLevel(e,t,this);this.#E(e,i)}#E(e,t){const n=this.#e.getFlameChartTimelineData();this.#T?.isEntryModified(e)&&R(n,t,{type:"HIDDEN_DESCENDANTS_ARROW"});const r=this.#t.Warnings.perEvent.get(e);r&&(R(n,t,{type:"WARNING_TRIANGLE"}),r.includes("LONG_TASK")&&R(n,t,{type:"CANDY",startAtTime:i.Handlers.ModelHandlers.Warnings.LONG_MAIN_THREAD_TASK_THRESHOLD}))}isIgnoreListedEntry(e){if(!this.#u)return!1;if(!i.Types.TraceEvents.isProfileCall(e))return!1;const t=e.callFrame.url;return t&&this.isIgnoreListedURL(t)}isIgnoreListedURL(e){return c.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(e)}colorForEvent(e){if(this.#t.Meta.traceIsGeneric)return e.name?`hsl(${o.StringUtilities.hashCode(e.name)%300+30}, 40%, 70%)`:"#ccc";if(i.Types.TraceEvents.isProfileCall(e))return"(idle)"===e.callFrame.functionName?Q().Idle.getComputedColorValue():"0"===e.callFrame.scriptId?Q().Scripting.getComputedColorValue():this.#n.colorForID(e.callFrame.url);const t=Z(e.name)?.category.getComputedColorValue();return t||Q().Other.getComputedColorValue()}titleForEvent(e){if(this.isIgnoreListedEntry(e))return ye(ve.onIgnoreList);if(i.Types.TraceEvents.isProfileCall(e)){if(this.#t.Samples){const t=i.Handlers.ModelHandlers.Samples.getProfileCallFunctionName(this.#t.Samples,e);if(t)return t}return e.callFrame.functionName||ye(ve.anonymous)}if(i.Types.TraceEvents.isTraceEventDispatch(e))return ye(ve.eventDispatchS,{PH1:e.args.data.type});const t=Z(e.name)?.title;return t||e.name}highlightedEntryInfo(e){let t=this.titleForEvent(e);if(i.Types.TraceEvents.isTraceEventParseHTML(e)){const i=e.args.beginData.startLine,n=e.args.endData&&e.args.endData.endLine,r=e.args.beginData.url;t+=` - ${c.ResourceUtils.displayNameForURL(r)} [${-1!==n||n===i?`${i}...${n}`:i}]`}return{title:t,formattedTime:L(e.dur,e.selfTime)}}}var Se=Object.freeze({__proto__:null,ThreadAppender:we});let be=null;class Ce{static instance(e={forceNew:null}){const t=Boolean(e.forceNew);return be&&!t||(be=new Ce),be}static removeInstance(){be=null}#M=[];activeFilters(){return this.#M}setFilters(e){this.#M=e}isVisible(e){return this.#M.every((t=>t.accept(e)))}}function Pe(e,t,i,n){const r=[...ke(e,t),...Ee(e,t)];return r.forEach((t=>function(e,t,i,n){if(i.includes(e.event)){let i=n.Renderer.entryToNode.get(e.event)?.parent;for(;i?.entry&&!t.includes(i?.entry);)i=i.parent??void 0;e.event=i?.entry??e.event,e.isEntryHidden=!0}if(i.includes(e.initiator)){let i=n.Renderer.entryToNode.get(e.initiator)?.parent;for(;i?.entry&&!t.includes(i?.entry);)i=i.parent??void 0;e.initiator=i?.entry??e.initiator,e.isInitiatorHidden=!0}return e}(t,n,i,e))),r}function ke(e,t){const n=[];let r=t;for(;r;){const t=e.Initiators.eventToInitiator.get(r);if(t){n.push({event:r,initiator:t}),r=t;continue}if(!i.Types.TraceEvents.isSyntheticTraceEntry(r)){r=null;break}const a=e.Renderer.entryToNode.get(r);if(!a){r=null;break}r=a.parent?.entry||null}return n}function Ee(e,t){const i=[],n=e.Initiators.initiatorToEvents.get(t);return n&&n.forEach((e=>{i.push({event:e,initiator:t})})),i}var Me=Object.freeze({__proto__:null,initiatorsDataToDraw:Pe});const Fe=new CSSStyleSheet;Fe.replaceSync(".timeline-flamechart-popover{overflow:hidden}.timeline-flamechart-popover devtools-interaction-breakdown{margin-top:10px}.timeline-flamechart-popover span{margin-right:5px}.timeline-flamechart-popover span.timeline-info-network-time{color:var(--sys-color-primary)}.timeline-flamechart-popover span.timeline-info-time{color:var(--sys-color-green)}.timeline-flamechart-popover span.timeline-info-warning{color:var(--sys-color-error)}.timeline-flamechart-popover span.timeline-info-warning *{color:inherit}\n/*# sourceURL=timelineFlamechartPopover.css */\n");const Ie={jsHeap:"JS Heap",documents:"Documents",nodes:"Nodes",listeners:"Listeners",gpuMemory:"GPU Memory",ss:"[{PH1} – {PH2}]"},Le=e.i18n.registerUIStrings("panels/timeline/CountersGraph.ts",Ie),xe=e.i18n.getLocalizedString.bind(void 0,Le);class Re extends h.Widget.VBox{delegate;calculator;header;toolbar;graphsContainer;canvasContainer;canvas;timelineGrid;counters;counterUI;countersByName;gpuMemoryCounter;#F=null;currentValuesBar;markerXPosition;#I=this.#L.bind(this);constructor(e){super(),this.element.id="memory-graphs-container",this.delegate=e,this.calculator=new Be,this.header=new h.Widget.HBox,this.header.element.classList.add("timeline-memory-header"),this.header.show(this.element),this.toolbar=new h.Toolbar.Toolbar("timeline-memory-toolbar"),this.header.element.appendChild(this.toolbar.element),this.graphsContainer=new h.Widget.VBox,this.graphsContainer.show(this.element);const t=new h.Widget.VBoxWithResizeCallback(this.resize.bind(this));t.show(this.graphsContainer.element),this.createCurrentValuesBar(),this.canvasContainer=t.element,this.canvasContainer.id="memory-graphs-canvas-container",this.canvas=document.createElement("canvas"),this.canvasContainer.appendChild(this.canvas),this.canvas.id="memory-counters-graph",this.canvasContainer.addEventListener("mouseover",this.onMouseMove.bind(this),!0),this.canvasContainer.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.canvasContainer.addEventListener("mouseleave",this.onMouseLeave.bind(this),!0),this.canvasContainer.addEventListener("click",this.onClick.bind(this),!0),this.timelineGrid=new s.TimelineGrid.TimelineGrid,this.canvasContainer.appendChild(this.timelineGrid.dividersElement),this.counters=[],this.counterUI=[],this.countersByName=new Map,this.countersByName.set("jsHeapSizeUsed",this.createCounter(xe(Ie.jsHeap),"js-heap-size-used","hsl(220, 90%, 43%)",o.NumberUtilities.bytesToString)),this.countersByName.set("documents",this.createCounter(xe(Ie.documents),"documents","hsl(0, 90%, 43%)")),this.countersByName.set("nodes",this.createCounter(xe(Ie.nodes),"nodes","hsl(120, 90%, 43%)")),this.countersByName.set("jsEventListeners",this.createCounter(xe(Ie.listeners),"js-event-listeners","hsl(38, 90%, 43%)")),this.gpuMemoryCounter=this.createCounter(xe(Ie.gpuMemory),"gpu-memory-used-kb","hsl(300, 90%, 43%)",o.NumberUtilities.bytesToString),this.countersByName.set("gpuMemoryUsedKB",this.gpuMemoryCounter),m.TraceBounds.onChange(this.#I)}#L(e){if("RESET"===e.updateType||"VISIBLE_WINDOW"===e.updateType){const t=e.state.milli.timelineTraceWindow;this.calculator.setWindow(t.min,t.max),this.#x()}}setModel(e,t){if(this.#F=t,!t)return;const n=e?i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).min:0;this.calculator.setZeroTime(n);for(let e=0;e<this.counters.length;++e)this.counters[e].reset(),this.counterUI[e].reset();this.#x();for(let e=0;e<t.length;++e){const n=t[e];if(!i.Types.TraceEvents.isTraceEventUpdateCounters(n))continue;const r=n.args.data;if(!r)return;for(const e in r){const t=this.countersByName.get(e);if(t){const{startTime:a}=i.Legacy.timesForEventInMilliseconds(n);t.appendSample(a,r[e])}}void 0!==r.gpuMemoryLimitKB&&this.gpuMemoryCounter.setLimit(r.gpuMemoryLimitKB)}}createCurrentValuesBar(){this.currentValuesBar=this.graphsContainer.element.createChild("div"),this.currentValuesBar.id="counter-values-bar"}createCounter(e,t,i,n){const r=new De;return this.counters.push(r),this.counterUI.push(new Ae(this,e,t,i,r,n)),r}resizerElement(){return this.header.element}resize(){const e=this.canvas.parentElement;this.canvas.width=e.clientWidth*window.devicePixelRatio,this.canvas.height=e.clientHeight*window.devicePixelRatio,this.calculator.setDisplayWidth(this.canvas.width),this.refresh()}#x(){h.UIUtils.invokeOnceAfterBatchUpdate(this,this.refresh)}draw(){this.clear();for(const e of this.counters)e.calculateVisibleIndexes(this.calculator),e.calculateXValues(this.canvas.width);for(const e of this.counterUI)e.drawGraph(this.canvas)}onClick(e){const t=e.x-this.canvasContainer.getBoundingClientRect().left;let i,n=1/0;for(const e of this.counterUI){if(!e.counter.times.length)continue;const r=e.recordIndexAt(t),a=Math.abs(t*window.devicePixelRatio-e.counter.x[r]);a<n&&(n=a,i=e.counter.times[r])}void 0!==i&&this.#F&&this.delegate.selectEntryAtTime(this.#F,i)}onMouseLeave(e){delete this.markerXPosition,this.clearCurrentValueAndMarker()}clearCurrentValueAndMarker(){for(let e=0;e<this.counterUI.length;e++)this.counterUI[e].clearCurrentValueAndMarker()}onMouseMove(e){const t=e.x-this.canvasContainer.getBoundingClientRect().left;this.markerXPosition=t,this.refreshCurrentValues()}refreshCurrentValues(){if(void 0!==this.markerXPosition)for(let e=0;e<this.counterUI.length;++e)this.counterUI[e].updateCurrentValue(this.markerXPosition)}refresh(){this.timelineGrid.updateDividers(this.calculator),this.draw(),this.refreshCurrentValues()}clear(){const e=this.canvas.getContext("2d");if(!e)throw new Error("Unable to get canvas context");e.clearRect(0,0,e.canvas.width,e.canvas.height)}}class De{times;values;x;minimumIndex;maximumIndex;maxTime;minTime;limitValue;constructor(){this.times=[],this.values=[],this.x=[],this.minimumIndex=0,this.maximumIndex=0,this.maxTime=0,this.minTime=0}appendSample(e,t){this.values.length&&this.values[this.values.length-1]===t||(this.times.push(e),this.values.push(t))}reset(){this.times=[],this.values=[]}setLimit(e){this.limitValue=e}calculateBounds(){let e,t;for(let i=this.minimumIndex;i<=this.maximumIndex;i++){const n=this.values[i];(void 0===t||n<t)&&(t=n),(void 0===e||n>e)&&(e=n)}return t=t||0,e=e||1,this.limitValue&&(e>.5*this.limitValue&&(e=Math.max(e,this.limitValue)),t=Math.min(t,this.limitValue)),{min:t,max:e}}calculateVisibleIndexes(e){const t=e.minimumBoundary(),i=e.maximumBoundary();this.minimumIndex=o.NumberUtilities.clamp(o.ArrayUtilities.upperBound(this.times,t,o.ArrayUtilities.DEFAULT_COMPARATOR)-1,0,this.times.length-1),this.maximumIndex=o.NumberUtilities.clamp(o.ArrayUtilities.lowerBound(this.times,i,o.ArrayUtilities.DEFAULT_COMPARATOR),0,this.times.length-1),this.minTime=t,this.maxTime=i}calculateXValues(e){if(!this.values.length)return;const t=e/(this.maxTime-this.minTime);this.x=new Array(this.values.length);for(let e=this.minimumIndex+1;e<=this.maximumIndex;e++)this.x[e]=t*(this.times[e]-this.minTime)}}class Ae{countersPane;counter;formatter;setting;filter;range;value;graphColor;limitColor;graphYValues;verticalPadding;currentValueLabel;marker;constructor(e,t,i,n,a,s){this.countersPane=e,this.counter=a,this.formatter=s||o.NumberUtilities.withThousandsSeparator,this.setting=r.Settings.Settings.instance().createSetting("timeline-counters-graph-"+i,!0),this.setting.setTitle(t),this.filter=new h.Toolbar.ToolbarSettingCheckbox(this.setting,t),this.filter.inputElement.classList.add("-theme-preserve-input");const l=r.Color.parse(n);if(l){const e=l.setAlpha(.5).asString("rgba"),t=this.filter.element;e&&(t.style.backgroundColor=e),t.style.borderColor="transparent"}this.filter.inputElement.addEventListener("click",this.toggleCounterGraph.bind(this)),e.toolbar.appendToolbarItem(this.filter),this.range=this.filter.element.createChild("span","range"),this.value=e.currentValuesBar.createChild("span","memory-counter-value"),this.value.style.color=n,this.graphColor=n,l&&(this.limitColor=l.setAlpha(.3).asString("rgba")),this.graphYValues=[],this.verticalPadding=10,this.currentValueLabel=t,this.marker=e.canvasContainer.createChild("div","memory-counter-marker"),this.marker.style.backgroundColor=n,this.clearCurrentValueAndMarker()}reset(){this.range.textContent=""}setRange(e,t){const i=this.formatter(e),n=this.formatter(t);this.range.textContent=xe(Ie.ss,{PH1:i,PH2:n})}toggleCounterGraph(){this.value.classList.toggle("hidden",!this.filter.checked()),this.countersPane.refresh()}recordIndexAt(e){return o.ArrayUtilities.upperBound(this.counter.x,e*window.devicePixelRatio,o.ArrayUtilities.DEFAULT_COMPARATOR,this.counter.minimumIndex+1,this.counter.maximumIndex+1)-1}updateCurrentValue(e){if(!this.visible()||!this.counter.values.length||!this.counter.x)return;const t=this.recordIndexAt(e),i=o.NumberUtilities.withThousandsSeparator(this.counter.values[t]);this.value.textContent=`${this.currentValueLabel}: ${i}`;const n=this.graphYValues[t]/window.devicePixelRatio;this.marker.style.left=e+"px",this.marker.style.top=n+"px",this.marker.classList.remove("hidden")}clearCurrentValueAndMarker(){this.value.textContent="",this.marker.classList.add("hidden")}drawGraph(e){const t=e.getContext("2d");if(!t)throw new Error("Unable to get canvas context");const i=e.width,n=e.height-2*this.verticalPadding;if(n<=0)return void(this.graphYValues=[]);const r=this.verticalPadding,a=this.counter,s=a.values;if(!s.length)return;const o=a.calculateBounds(),l=o.min,c=o.max;if(this.setRange(l,c),!this.visible())return;const d=this.graphYValues,h=c-l,m=h?n/h:1;t.save(),t.lineWidth=window.devicePixelRatio,t.lineWidth%2&&t.translate(.5,.5),t.beginPath();let p=s[a.minimumIndex],u=Math.round(r+n-(p-l)*m);t.moveTo(0,u);let g=a.minimumIndex;for(;g<=a.maximumIndex;g++){const e=Math.round(a.x[g]);t.lineTo(e,u);const i=s[g];void 0!==i&&(p=i),u=Math.round(r+n-(p-l)*m),t.lineTo(e,u),d[g]=u}if(d.length=g,t.lineTo(i,u),t.strokeStyle=this.graphColor,t.stroke(),a.limitValue){const e=Math.round(r+n-(a.limitValue-l)*m);t.moveTo(0,e),t.lineTo(i,e),this.limitColor&&(t.strokeStyle=this.limitColor),t.stroke()}t.closePath(),t.restore()}visible(){return this.filter.checked()}}class Be{minimumBoundaryInternal;maximumBoundaryInternal;workingArea;zeroTimeInternal;constructor(){this.minimumBoundaryInternal=0,this.maximumBoundaryInternal=0,this.workingArea=0,this.zeroTimeInternal=0}setZeroTime(e){this.zeroTimeInternal=e}computePosition(e){return(e-this.minimumBoundaryInternal)/this.boundarySpan()*this.workingArea}setWindow(e,t){this.minimumBoundaryInternal=e,this.maximumBoundaryInternal=t}setDisplayWidth(e){this.workingArea=e}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t-this.zeroTime(),i)}maximumBoundary(){return this.maximumBoundaryInternal}minimumBoundary(){return this.minimumBoundaryInternal}zeroTime(){return this.zeroTimeInternal}boundarySpan(){return this.maximumBoundaryInternal-this.minimumBoundaryInternal}}var He=Object.freeze({__proto__:null,CountersGraph:Re,Counter:De,CounterUI:Ae,Calculator:Be});const Ne=new CSSStyleSheet;Ne.replaceSync(".image-preview-container{background:transparent;text-align:center;border-spacing:0}.image-preview-container img{margin:6px 0;max-width:100px;max-height:100px;background-image:var(--image-file-checker);user-select:text;vertical-align:top;-webkit-user-drag:auto}.image-container{padding:0}.image-container > div{min-height:50px;display:flex;align-items:center;justify-content:center;cursor:pointer}.image-preview-container .row{line-height:1.2;vertical-align:baseline}.image-preview-container .title{padding-right:0.5em;text-align:right;color:var(--sys-color-token-subtle);white-space:nowrap}.image-preview-container .description{white-space:nowrap;text-align:left;color:var(--sys-color-on-surface)}.image-preview-container .description-link{max-width:20em}.image-preview-container .source-link{white-space:normal;word-break:break-all;color:var(--sys-color-primary);cursor:pointer}\n/*# sourceURL=imagePreview.css */\n");class Ue extends Event{static eventName="nodenamesupdated";constructor(){super(Ue.eventName,{composed:!0,bubbles:!0})}}const We=new Map;class Ve extends EventTarget{#R;#D=!1;#A=new Set;constructor(e){super(),this.#R=e}static clearResolvedNodeNames(){We.clear()}static resolvedNodeNameForEntry(e){return We.get(e.pid)?.get(e.tid)?.get(e.nodeId)??null}static storeResolvedNodeNameForEntry(e,t,i,n){const r=We.get(e)||new Map,a=r.get(t)||new Map;a.set(i,n),r.set(t,a),We.set(e,r)}async install(){if(this.#R.Samples){for(const e of this.#R.Samples.profilesInProcess.values())for(const[t,i]of e){const e=i.parsedProfile.nodes();if(!e||0===e.length)continue;const r=this.#B(t),a=r?.model(n.DebuggerModel.DebuggerModel);if(a)for(const t of e){const e=a.scriptForId(String(t.callFrame.scriptId));(!e||e.sourceMapURL)&&this.#A.add(a)}}for(const e of this.#A)e.sourceMapManager().addEventListener(n.SourceMapManager.Events.SourceMapAttached,this.#H,this);await this.#N()}}uninstall(){for(const e of this.#A)e.sourceMapManager().removeEventListener(n.SourceMapManager.Events.SourceMapAttached,this.#H,this);this.#A.clear()}async#N(){if(this.#R.Samples){for(const[e,t]of this.#R.Samples.profilesInProcess)for(const[i,n]of t){const t=n.parsedProfile.nodes()??[],r=this.#B(i);if(r)for(const n of t){const t=await g.NamesResolver.resolveProfileFrameFunctionName(n.callFrame,r);n.setFunctionName(t),Ve.storeResolvedNodeNameForEntry(e,i,n.id,t)}}this.dispatchEvent(new Ue)}}#H(){this.#D||(this.#D=!0,setTimeout((async()=>{this.#D=!1,await this.#N()}),500))}#B(e){const t=this.#R.Workers.workerIdByThread.get(e);return t?n.TargetManager.TargetManager.instance().targetById(t):n.TargetManager.TargetManager.instance().primaryPageTarget()}}var Oe=Object.freeze({__proto__:null,NodeNamesUpdated:Ue,SourceMapsResolver:Ve});const ze=new CSSStyleSheet;ze.replaceSync('.content{margin-left:5px}.history-dropdown-button{width:160px;height:26px;text-align:left;display:flex;border:1px solid transparent}.history-dropdown-button[disabled]{opacity:50%;border:1px solid transparent}.history-dropdown-button > .content{padding-right:5px;overflow:hidden;text-overflow:ellipsis;flex:1 1;min-width:35px;&::after{float:right;user-select:none;mask-image:var(--image-file-triangle-down);width:14px;height:14px;content:"";position:absolute;background-color:var(--icon-default);right:-3px}}.history-dropdown-button:focus-visible::before{content:"";position:absolute;top:2px;left:0;right:0;bottom:2px;border-radius:2px;background:var(--divider-line)}@media (forced-colors: active){.history-dropdown-button[disabled]{opacity:100%}.history-dropdown-button > .content::after{background-color:ButtonText}.history-dropdown-button[disabled] > .content::after{background-color:GrayText}}\n/*# sourceURL=historyToolbarButton.css */\n');const Ge={empty:"(empty)",selectJavascriptVmInstance:"Select JavaScript VM instance"},_e=e.i18n.registerUIStrings("panels/timeline/IsolateSelector.ts",Ge),je=e.i18n.getLocalizedString.bind(void 0,_e);class qe extends h.Toolbar.ToolbarItem{menu;options;items;itemByIsolate=new Map;constructor(){const e=new b.SelectMenu.SelectMenu;super(e),this.menu=e,e.buttonTitle=je(Ge.selectJavascriptVmInstance),e.showArrow=!0,e.style.whiteSpace="normal",e.addEventListener("selectmenuselected",this.#U.bind(this)),n.IsolateManager.IsolateManager.instance().observeIsolates(this),n.TargetManager.TargetManager.instance().addEventListener("NameChanged",this.targetChanged,this),n.TargetManager.TargetManager.instance().addEventListener("InspectedURLChanged",this.targetChanged,this)}#W(e,t){const i=new Map;for(const t of e.models()){const e=t.target(),a=n.TargetManager.TargetManager.instance().rootTarget()!==e?e.name():"",s=new r.ParsedURL.ParsedURL(e.inspectedURL()),o=s.isValid?s.domain():"",l=e.decorateLabel(o&&a?`${o}: ${a}`:a||o||je(Ge.empty));i.set(l,(i.get(l)||0)+1)}t.removeChildren();for(const[e,n]of i){const i=n>1?`${e} (${n})`:e;t.createChild("div").textContent=i}}#U(e){this.itemByIsolate.forEach(((t,i)=>{if(t.selected=t.value===e.itemValue,t.selected){const e=t.textContent?.slice(0,29);this.menu.buttonTitle=e||je(Ge.empty);const r=i.runtimeModel();h.Context.Context.instance().setFlavor(n.CPUProfilerModel.CPUProfilerModel,r&&r.target().model(n.CPUProfilerModel.CPUProfilerModel))}}))}isolateAdded(e){const t=new b.Menu.MenuItem;this.menu.appendChild(t),t.value=e.id(),this.itemByIsolate.set(e,t),this.#W(e,t)}isolateRemoved(e){const t=this.itemByIsolate.get(e);t&&(t.selected&&(this.menu.buttonTitle=je(Ge.selectJavascriptVmInstance),h.Context.Context.instance().setFlavor(n.CPUProfilerModel.CPUProfilerModel,null)),this.menu.removeChild(t))}isolateChanged(e){const t=this.itemByIsolate.get(e);t&&this.#W(e,t)}targetChanged(e){const t=e.data.model(n.RuntimeModel.RuntimeModel);if(!t)return;const i=n.IsolateManager.IsolateManager.instance().isolateByModel(t);i&&this.isolateChanged(i)}}class Je{timelineModelInternal;constructor(){this.timelineModelInternal=new d.TimelineModel.TimelineModelImpl}async setTracingModel(e,t=!1){this.timelineModelInternal.setEvents(e,t)}timelineModel(){return this.timelineModelInternal}}var $e=Object.freeze({__proto__:null,PerformanceModel:Je});function*Xe(e){if(yield"[\n",e.length>0){const t=e[Symbol.iterator](),i=t.next().value;yield`  ${JSON.stringify(i)}`;let n=1e4,r="";for(const e of t)r+=`,\n  ${JSON.stringify(e)}`,n--,0===n&&(yield r,n=1e4,r="");yield r}yield"\n]"}function*Ke(e,t){yield'{"traceEvents": ',yield*Xe(e),yield`,\n"metadata": ${JSON.stringify(t||{},null,2)}`,yield"}\n"}function Ye(e){return JSON.stringify(e)}var Ze=Object.freeze({__proto__:null,arrayOfObjectsJsonGenerator:Xe,traceJsonGenerator:Ke,cpuprofileJsonGenerator:Ye});const Qe={tracingNotSupported:"Performance trace recording not supported for this type of target"},et=e.i18n.registerUIStrings("panels/timeline/TimelineController.ts",Qe),tt=e.i18n.getLocalizedString.bind(void 0,et);class it{primaryPageTarget;rootTarget;tracingManager;performanceModel;#V=[];#O=null;client;tracingModel;tracingCompleteCallback;constructor(e,t,n){this.primaryPageTarget=t,this.rootTarget=e,this.tracingManager=e.model(i.TracingManager.TracingManager),this.performanceModel=new Je,this.client=n,this.tracingModel=new i.Legacy.TracingModel}async dispose(){this.tracingManager&&await this.tracingManager.reset()}async startRecording(e){function t(e){return"disabled-by-default-"+e}const i=[l.Runtime.experiments.isEnabled("timeline-show-all-events")?"*":"-*",d.TimelineModel.TimelineModelImpl.Category.Console,d.TimelineModel.TimelineModelImpl.Category.UserTiming,"devtools.timeline",t("devtools.timeline"),t("devtools.timeline.frame"),t("devtools.timeline.stack"),t("v8.compile"),t("v8.cpu_profiler.hires"),d.TimelineModel.TimelineModelImpl.Category.Loading,t("lighthouse"),"v8.execute","v8","cppgc"];l.Runtime.experiments.isEnabled("timeline-v8-runtime-call-stats")&&e.enableJSSampling&&i.push(t("v8.runtime_stats_sampling")),e.enableJSSampling&&i.push(t("v8.cpu_profiler")),l.Runtime.experiments.isEnabled("timeline-invalidation-tracking")&&i.push(t("devtools.timeline.invalidationTracking")),e.capturePictures&&i.push(t("devtools.timeline.layers"),t("devtools.timeline.picture"),t("blink.graphics_context_annotations")),e.captureFilmStrip&&i.push(t("devtools.screenshot")),this.#O=Date.now();const r=await this.startRecordingWithCategories(i.join(","));return r.getError()&&(await this.waitForTracingToStop(!1),await n.TargetManager.TargetManager.instance().resumeAllTargets()),r}async stopRecording(){return this.tracingManager&&this.tracingManager.stop(),this.client.loadingStarted(),await this.waitForTracingToStop(!0),await this.allSourcesFinished(),this.performanceModel}getPerformanceModel(){return this.performanceModel}async waitForTracingToStop(e){const t=[];this.tracingManager&&e&&t.push(new Promise((e=>{this.tracingCompleteCallback=e}))),await Promise.all(t)}async startRecordingWithCategories(e){if(!this.tracingManager)throw new Error(tt(Qe.tracingNotSupported));await n.TargetManager.TargetManager.instance().suspendAllTargets("performance-timeline");const t=await this.tracingManager.start(this,e,"");return await this.warmupJsProfiler(),t}async warmupJsProfiler(){const e=this.primaryPageTarget.model(n.RuntimeModel.RuntimeModel);e&&await e.checkSideEffectSupport()}traceEventsCollected(e){this.#V=this.#V.concat(e),this.tracingModel.addEvents(e)}tracingComplete(){this.tracingCompleteCallback&&(this.tracingCompleteCallback(void 0),this.tracingCompleteCallback=null)}async allSourcesFinished(){this.client.processingStarted(),await this.finalizeTrace()}async finalizeTrace(){await n.TargetManager.TargetManager.instance().resumeAllTargets(),this.tracingModel.tracingComplete(),await this.client.loadingComplete(this.#V,this.tracingModel,null,!1,this.#O),this.client.loadingCompleteForTest()}tracingBufferUsage(e){this.client.recordingProgress(e)}eventsRetrievalProgress(e){this.client.loadingProgress(e)}}var nt=Object.freeze({__proto__:null,TimelineController:it});const rt={net:"NET",cpu:"CPU",heap:"HEAP",sSDash:"{PH1} – {PH2}"},at=e.i18n.registerUIStrings("panels/timeline/TimelineEventOverview.ts",rt),st=e.i18n.getLocalizedString.bind(void 0,at);class ot extends s.TimelineOverviewPane.TimelineOverviewBase{constructor(e,t){super(),this.element.id="timeline-overview-"+e,this.element.classList.add("overview-strip"),t&&(this.element.createChild("div","timeline-overview-strip-title").textContent=t)}renderBar(e,t,i,n,r){const a=e,s=t-e,o=this.context();o.fillStyle=r,o.fillRect(a,i,s,n)}}const lt=new Set(["VeryHigh","High","Medium"]);class ct extends ot{#t;constructor(e){super("network",st(rt.net)),this.#t=e}update(e,t){this.resetCanvas(),this.#z(e,t)}#z(e,t){if(!this.#t)return;const n=e&&t?{min:e,max:t,range:t-e}:i.Helpers.Timing.traceWindowMilliSeconds(this.#t.Meta.traceBounds),r=this.height()/2,a=this.width(),s=a/n.range,o=new Path2D,l=new Path2D;for(const e of this.#t.NetworkRequests.byTime){const t=lt.has(e.args.data.priority)?o:l,{startTime:c,endTime:d}=i.Helpers.Timing.eventTimingsMilliSeconds(e),h=Math.max(Math.floor((c-n.min)*s),0),m=Math.min(Math.ceil((d-n.min)*s+1),a);t.rect(h,0,m-h,r-1)}const c=this.context();c.save(),c.fillStyle="hsl(214, 60%, 60%)",c.fill(o),c.translate(0,r),c.fillStyle="hsl(214, 80%, 80%)",c.fill(l),c.restore()}}const dt=new WeakMap;class ht extends ot{backgroundCanvas;#t;#G=!1;#_;#j;constructor(e){super("cpu-activity",st(rt.cpu)),this.#t=e,this.backgroundCanvas=this.element.createChild("canvas","fill background"),this.#_=i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).min,this.#j=i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).max}#q(e){if(i.Types.TraceEvents.isProfileCall(e)&&"(idle)"===e.callFrame.functionName)return"idle";return(Z(e.name)?.category||Q().Other).name}resetCanvas(){super.resetCanvas(),this.#G=!1,this.backgroundCanvas.width=this.element.clientWidth*window.devicePixelRatio,this.backgroundCanvas.height=this.element.clientHeight*window.devicePixelRatio}#J(e){const t=4*window.devicePixelRatio,n=this.width(),r=this.height(),a=r,s=this.#j-this.#_,o=t/(n/s),l=vi.categories(),c=vi.getTimelineMainEventCategories(),d=c.indexOf("other");console.assert(0===c.indexOf("idle"));for(let e=0;e<c.length;++e)dt.set(l[c[e]],e);const h=(e,h)=>{const m=new gt(this.#_,o,(function(e){let i=a;for(let n=1;n<c.length;++n){i-=(e[n]||0)/o*r,g[n].bezierCurveTo(p,T[n],p,i,p+t/2,i),T[n]=i}p+=t}));let p=0;const u=[],g=[],T=[];for(let e=0;e<c.length;++e)g[e]=new Path2D,g[e].moveTo(0,r),T[e]=r;const v=i.Helpers.Timing.millisecondsToMicroseconds(this.#_),f=i.Helpers.Timing.millisecondsToMicroseconds(this.#j),y={min:v,max:f,range:i.Types.Timing.MicroSeconds(f-v)},w=i.Types.Timing.MicroSeconds(y.range>2e5?16e3:0);i.Helpers.TreeHelpers.walkEntireTree(h.entryToNode,h.tree,(e=>{const t=this.#q(e);if(!t||"idle"===t)return;const n=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),r=u.length?u[u.length-1]:0;m.appendInterval(n,r);const a=c.indexOf(t);u.push(a||d)}),(function(e){const t=i.Helpers.Timing.microSecondsToMilliseconds(e.ts)+i.Helpers.Timing.microSecondsToMilliseconds(i.Types.Timing.MicroSeconds(e.dur||0)),n=u.pop();void 0!==t&&n&&m.appendInterval(t,n)}),y,w),m.appendInterval(this.#_+s+o,0);for(let t=c.length-1;t>0;--t){g[t].lineTo(n,r);const i=l[c[t]].getComputedColorValue();e.fillStyle=i,e.fill(g[t]),e.strokeStyle="white",e.lineWidth=1,e.stroke(g[t])}},m=this.backgroundCanvas.getContext("2d");if(!m)throw new Error("Could not find 2d canvas");const p=i.Handlers.Threads.threadsInTrace(e),u=this.context();for(const e of p){h("MAIN_THREAD"===e.type||"CPU_PROFILE"===e.type?u:m,e)}!function(e){const t=4*window.devicePixelRatio;e.save(),e.lineWidth=t/Math.sqrt(8);for(let i=.5;i<n+r;i+=t)e.moveTo(i,0),e.lineTo(i-r,r);e.globalCompositeOperation="destination-out",e.stroke(),e.restore()}(m)}update(){const e=m.TraceBounds.BoundsManager.instance().state(),t=e?.milli.minimapTraceBounds;t&&(t.min===this.#_&&t.max===this.#j&&this.#G||(this.#_=t.min,this.#j=t.max,this.resetCanvas(),this.#G=!0,this.#J(this.#t)))}}class mt extends ot{#t;constructor(e){super("responsiveness",null),this.#t=e}#$(){const{topLevelRendererIds:e}=this.#t.Meta,t=new Set(["LONG_TASK","FORCED_REFLOW","IDLE_CALLBACK_OVER_TIME"]),i=new Set;for(const n of t){const t=this.#t.Warnings.perWarning.get(n);if(t)for(const n of t)e.has(n.pid)&&i.add(n)}return i}update(e,t){this.resetCanvas();const n=this.height(),r=e&&t?{min:i.Helpers.Timing.millisecondsToMicroseconds(e),max:i.Helpers.Timing.millisecondsToMicroseconds(t),range:i.Helpers.Timing.millisecondsToMicroseconds(i.Types.Timing.MilliSeconds(t-e))}:this.#t.Meta.traceBounds,a=r.range,s=this.width()/a,o=this.context(),l=new Path2D,c=new Path2D,d=this.#$();for(const e of d)h(e);function h(e){const{startTime:t,duration:a}=i.Helpers.Timing.eventTimingsMicroSeconds(e),o=Math.round(s*(t-r.min)),d=Math.round(s*a);l.rect(o,0,d,n),c.moveTo(o+d,0),c.lineTo(o+d,n)}o.fillStyle="hsl(0, 80%, 90%)",o.strokeStyle="red",o.lineWidth=2*window.devicePixelRatio,o.fill(l),o.stroke(c)}}class pt extends ot{frameToImagePromise;lastFrame=null;lastElement;drawGeneration;emptyImage;#X=null;constructor(e){super("filmstrip",null),this.frameToImagePromise=new Map,this.#X=e,this.lastFrame=null,this.lastElement=null,this.reset()}update(e,t){this.resetCanvas();const i=this.#X?this.#X.frames:[];if(!i.length)return;if(0===this.height())return void console.warn("TimelineFilmStrip could not be drawn as its canvas height is 0");const n=Symbol("drawGeneration");this.drawGeneration=n,this.imageByFrame(i[0]).then((i=>{if(this.drawGeneration!==n)return;if(!i||!i.naturalWidth||!i.naturalHeight)return;const r=this.height()-2*pt.Padding,a=Math.ceil(r*i.naturalWidth/i.naturalHeight),s=Math.min(200/i.naturalWidth,1);this.emptyImage=new Image(i.naturalWidth*s,i.naturalHeight*s),this.drawFrames(a,r,e,t)}))}async imageByFrame(e){let t=this.frameToImagePromise.get(e);return t||(t=h.UIUtils.loadImage(e.screenshotEvent.args.dataUri),this.frameToImagePromise.set(e,t)),t}drawFrames(e,t,n,r){if(!e)return;if(!this.#X||this.#X.frames.length<1)return;const a=pt.Padding,s=this.width(),o=n??i.Helpers.Timing.microSecondsToMilliseconds(this.#X.zeroTime),l=(r?r-o:i.Helpers.Timing.microSecondsToMilliseconds(this.#X.spanTime))/s,c=this.context(),d=this.drawGeneration;c.beginPath();for(let n=a;n<s;n+=e+2*a){const r=i.Types.Timing.MilliSeconds(o+(n+e/2)*l),a=i.Helpers.Timing.millisecondsToMicroseconds(r),s=i.Extras.FilmStrip.frameClosestToTimestamp(this.#X,a);s&&(c.rect(n-.5,.5,e+1,t+1),this.imageByFrame(s).then(h.bind(this,n)))}function h(i,n){this.drawGeneration===d&&n&&c.drawImage(n,i,1,e,t)}c.strokeStyle="#ddd",c.stroke()}async overviewInfoPromise(e){if(!this.#X||0===this.#X.frames.length)return null;const t=this.calculator();if(!t)return null;const n=i.Types.Timing.MilliSeconds(t.positionToTime(e)),r=i.Helpers.Timing.millisecondsToMicroseconds(n),a=i.Extras.FilmStrip.frameClosestToTimestamp(this.#X,r);if(a===this.lastFrame)return this.lastElement;const s=a?this.imageByFrame(a):Promise.resolve(this.emptyImage),o=await s,l=document.createElement("div");return l.classList.add("frame"),o&&l.createChild("div","thumbnail").appendChild(o),this.lastFrame=a,this.lastElement=l,l}reset(){this.lastFrame=null,this.lastElement=null,this.frameToImagePromise=new Map}static Padding=2}class ut extends ot{heapSizeLabel;#t;constructor(e){super("memory",st(rt.heap)),this.heapSizeLabel=this.element.createChild("div","memory-graph-label"),this.#t=e}resetHeapSizeLabels(){this.heapSizeLabel.textContent=""}update(e,t){this.resetCanvas();const n=window.devicePixelRatio;if(0===this.#t.Memory.updateCountersByProcess.size)return void this.resetHeapSizeLabels();const r=Array.from(this.#t.Meta.topLevelRendererIds).map((e=>this.#t.Memory.updateCountersByProcess.get(e)||[])).filter((e=>e.length>0)),a=3*n;let s=0,l=1e11;const c=e&&t?{min:e,max:t,range:t-e}:i.Helpers.Timing.traceWindowMilliSeconds(this.#t.Meta.traceBounds),d=c.min,h=c.max;function m(e){const t=e.args.data;t&&t.jsHeapSizeUsed&&(s=Math.max(s,t.jsHeapSizeUsed),l=Math.min(l,t.jsHeapSizeUsed))}for(let e=0;e<r.length;e++)r[e].forEach(m);l=Math.min(l,s);const p=this.width(),u=this.height()-a,g=p/(h-d),T=(u-1)/Math.max(s-l,1),v=new Array(p);function f(e){const t=e.args.data;if(!t||!t.jsHeapSizeUsed)return;const{startTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(e),r=Math.round((n-d)*g),a=Math.round((t.jsHeapSizeUsed-l)*T);v[r]=Math.max(v[r]||0,a)}for(let e=0;e<r.length;e++)r[e].forEach(f);const y=this.context(),w=u+a+1;y.translate(.5,.5),y.beginPath(),y.moveTo(-1,w);let S=0,b=!0,C=0;for(let e=0;e<v.length;e++){if(void 0===v[e])continue;b&&(b=!1,S=v[e],y.lineTo(-1,u-S));const t=v[e];Math.abs(t-S)>2&&Math.abs(e-C)>1&&y.lineTo(e,u-S),S=t,y.lineTo(e,u-S),C=e}y.lineTo(p+1,u-S),y.lineTo(p+1,w),y.closePath(),y.fillStyle="hsla(220, 90%, 70%, 0.2)",y.fill(),y.lineWidth=1,y.strokeStyle="hsl(220, 90%, 70%)",y.stroke(),this.heapSizeLabel.textContent=st(rt.sSDash,{PH1:o.NumberUtilities.bytesToString(l),PH2:o.NumberUtilities.bytesToString(s)})}}class gt{lastTime;quantDuration;callback;counters;remainder;constructor(e,t,i){this.lastTime=e,this.quantDuration=t,this.callback=i,this.counters=[],this.remainder=t}appendInterval(e,t){let i=e-this.lastTime;if(i<=this.remainder)return this.counters[t]=(this.counters[t]||0)+i,this.remainder-=i,void(this.lastTime=e);for(this.counters[t]=(this.counters[t]||0)+this.remainder,this.callback(this.counters),i-=this.remainder;i>=this.quantDuration;){const e=[];e[t]=this.quantDuration,this.callback(e),i-=this.quantDuration}this.counters=[],this.counters[t]=i,this.lastTime=e,this.remainder=this.quantDuration-i}}var Tt=Object.freeze({__proto__:null,TimelineEventOverview:ot,TimelineEventOverviewNetwork:ct,TimelineEventOverviewCPUActivity:ht,TimelineEventOverviewResponsiveness:mt,TimelineFilmStripOverview:pt,TimelineEventOverviewMemory:ut,Quantizer:gt});const vt=new CSSStyleSheet;vt.replaceSync(".drop-down{padding:1px;box-shadow:var(--drop-shadow);background:var(--sys-color-cdt-base-container)}.preview-item{border-color:transparent;border-style:solid;border-width:1px 5px;padding:2px 0;margin:2px 1px}.preview-item.selected{border-color:var(--sys-color-primary)}.preview-item canvas{width:100%;height:100%}.text-details{font-size:11px;padding:3px}.text-details span{flex:1 0;padding-left:8px;padding-right:8px}.text-details .name{font-weight:bold}.text-details span.time{color:var(--sys-color-token-subtle);text-align:right}.screenshot-thumb{display:flex;border:1px solid var(--sys-color-neutral-outline);margin:2px 4px}.screenshot-thumb img{margin:auto;max-width:100%;max-height:100%}\n/*# sourceURL=timelineHistoryManager.css */\n");const ft={currentSessionSS:"Current Session: {PH1}. {PH2}",noRecordings:"(no recordings)",sAgo:"({PH1} ago)",moments:"moments",sM:"{PH1} m",sH:"{PH1} h",sD:"{PH1} #{PH2}",selectTimelineSession:"Select Timeline Session"},yt=e.i18n.registerUIStrings("panels/timeline/TimelineHistoryManager.ts",ft),wt=e.i18n.getLocalizedString.bind(void 0,yt);class St{recordings;action;nextNumberByDomain;buttonInternal;allOverviews;totalHeight;enabled;lastActiveTraceIndex=null;#K;constructor(e){this.recordings=[],this.#K=e,this.action=h.ActionRegistry.ActionRegistry.instance().getAction("timeline.show-history"),this.nextNumberByDomain=new Map,this.buttonInternal=new Et(this.action),h.ARIAUtils.markAsMenuButton(this.buttonInternal.element),this.clear(),this.allOverviews=[{constructor:e=>{const t=this.#K?.getControls().find((e=>e instanceof mt));return t||new mt(e)},height:3},{constructor:e=>{const t=this.#K?.getControls().find((e=>e instanceof ht));return t||new ht(e)},height:20},{constructor:e=>{const t=this.#K?.getControls().find((e=>e instanceof ct));return t||new ct(e)},height:8}],this.totalHeight=this.allOverviews.reduce(((e,t)=>e+t.height),0),this.enabled=!0}addRecording(e){const{legacyModel:t,traceParseDataIndex:i}=e.data,n=e.filmStripForPreview;this.lastActiveTraceIndex=i,this.recordings.unshift({legacyModel:t,traceParseDataIndex:i}),this.#Y(i,e.traceParsedData,n,e.startTime);const r=this.title(i);this.buttonInternal.setText(r);const a=this.action.title();if(h.ARIAUtils.setLabel(this.buttonInternal.element,wt(ft.currentSessionSS,{PH1:r,PH2:a})),this.updateState(),this.recordings.length<=bt)return;const s=this.recordings.reduce(((e,t)=>o(e.traceParseDataIndex)<o(t.traceParseDataIndex)?e:t));function o(e){const t=St.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");return t.lastUsed}this.recordings.splice(this.recordings.indexOf(s),1)}setEnabled(e){this.enabled=e,this.updateState()}button(){return this.buttonInternal}clear(){this.recordings=[],this.lastActiveTraceIndex=null,this.updateState(),this.buttonInternal.setText(wt(ft.noRecordings)),this.nextNumberByDomain.clear()}async showHistoryDropDown(){if(this.recordings.length<2||!this.enabled)return null;const e=await kt.show(this.recordings.map((e=>e.traceParseDataIndex)),this.lastActiveTraceIndex,this.buttonInternal.element);if(null===e)return null;const t=this.recordings.findIndex((t=>t.traceParseDataIndex===e));return t<0?(console.assert(!1,"selected recording not found"),null):(this.setCurrentModel(e),this.recordings[t])}cancelIfShowing(){kt.cancelIfShowing()}navigate(e){if(!this.enabled||null===this.lastActiveTraceIndex)return null;const t=this.recordings.findIndex((e=>e.traceParseDataIndex===this.lastActiveTraceIndex));if(t<0)return null;const i=o.NumberUtilities.clamp(t+e,0,this.recordings.length-1),{traceParseDataIndex:n}=this.recordings[i];return this.setCurrentModel(n),this.recordings[i]}setCurrentModel(e){const t=St.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");t.lastUsed=Date.now(),this.lastActiveTraceIndex=e;const i=this.title(e),n=this.action.title();this.buttonInternal.setText(i),h.ARIAUtils.setLabel(this.buttonInternal.element,wt(ft.currentSessionSS,{PH1:i,PH2:n}))}updateState(){this.action.setEnabled(this.recordings.length>1&&this.enabled)}static previewElement(e){const t=St.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");const i=t.startTime;return t.time.textContent=i?wt(ft.sAgo,{PH1:St.coarseAge(i)}):"",t.preview}static coarseAge(e){const t=Math.round((Date.now()-e)/1e3);if(t<50)return wt(ft.moments);const i=Math.round(t/60);if(i<50)return wt(ft.sM,{PH1:i});const n=Math.round(i/60);return wt(ft.sH,{PH1:n})}title(e){const t=St.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");return t.title}#Y(e,t,i,n){const a=r.ParsedURL.ParsedURL.fromString(t.Meta.mainFrameURL),s=a?a.host:"",o=this.nextNumberByDomain.get(s)||1,l=wt(ft.sD,{PH1:s,PH2:o});this.nextNumberByDomain.set(s,o+1);const c=document.createElement("span"),d=document.createElement("div");d.classList.add("preview-item"),d.classList.add("vbox");const h={preview:d,title:l,time:c,lastUsed:Date.now(),startTime:n};Pt.set(e,h),d.appendChild(this.#Z(t,s,c));const m=d.createChild("div","hbox");return m.appendChild(this.#Q(i)),m.appendChild(this.#ee(t)),h.preview}#Z(t,n,r){const a=document.createElement("div");a.classList.add("text-details"),a.classList.add("hbox");const s=a.createChild("span","name");s.textContent=n,h.ARIAUtils.setLabel(s,n);const o=i.Helpers.Timing.traceWindowMilliSeconds(t.Meta.traceBounds),l=e.TimeUtilities.millisToString(o.range,!1),c=a.createChild("span","time");return c.appendChild(document.createTextNode(l)),c.appendChild(r),a}#Q(e){const t=document.createElement("div");t.classList.add("screenshot-thumb");if(t.style.width=1.5*this.totalHeight+"px",t.style.height=this.totalHeight+"px",!e)return t;const i=e.frames.at(-1);return i?(h.UIUtils.loadImage(i.screenshotEvent.args.dataUri).then((e=>{e&&t.appendChild(e)})),t):t}#ee(e){const t=document.createElement("div"),i=window.devicePixelRatio;t.style.width=Ct+"px",t.style.height=this.totalHeight+"px";const n=t.createChild("canvas");n.width=i*Ct,n.height=i*this.totalHeight;const r=n.getContext("2d");let a=0;for(const t of this.allOverviews){const n=t.constructor(e);n.update(),r&&r.drawImage(n.context().canvas,0,a,i*Ct,t.height*i),a+=t.height*i}return t}static dataForTraceIndex(e){return Pt.get(e)||null}}const bt=5,Ct=450,Pt=new Map;class kt{glassPane;listControl;focusRestorer;selectionDone;constructor(e){this.glassPane=new h.GlassPane.GlassPane,this.glassPane.setSizeBehavior("MeasureContent"),this.glassPane.setOutsideClickCallback((()=>this.close(null))),this.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.element.addEventListener("blur",(()=>this.close(null)));const t=h.Utils.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:[vt],delegatesFocus:void 0}).createChild("div","drop-down"),i=new h.ListModel.ListModel;this.listControl=new h.ListControl.ListControl(i,this,h.ListControl.ListMode.NonViewport),this.listControl.element.addEventListener("mousemove",this.onMouseMove.bind(this),!1),i.replaceAll(e),h.ARIAUtils.markAsMenu(this.listControl.element),h.ARIAUtils.setLabel(this.listControl.element,wt(ft.selectTimelineSession)),t.appendChild(this.listControl.element),t.addEventListener("keydown",this.onKeyDown.bind(this),!1),t.addEventListener("click",this.onClick.bind(this),!1),this.focusRestorer=new h.UIUtils.ElementFocusRestorer(this.listControl.element),this.selectionDone=null}static show(e,t,i){if(kt.instance)return Promise.resolve(null);return new kt(e).show(i,t)}static cancelIfShowing(){kt.instance&&kt.instance.close(null)}show(e,t){return kt.instance=this,this.glassPane.setContentAnchorBox(e.boxInWindow()),this.glassPane.show(this.glassPane.contentElement.ownerDocument),this.listControl.element.focus(),this.listControl.selectItem(t),new Promise((e=>{this.selectionDone=e}))}onMouseMove(e){const t=e.target.enclosingNodeOrSelfWithClass("preview-item"),i=t&&this.listControl.itemForNode(t);null!==i&&this.listControl.selectItem(i)}onClick(e){e.target.enclosingNodeOrSelfWithClass("preview-item")&&this.close(this.listControl.selectedItem())}onKeyDown(e){switch(e.key){case"Tab":case"Escape":this.close(null);break;case"Enter":this.close(this.listControl.selectedItem());break;default:return}e.consume(!0)}close(e){this.selectionDone&&this.selectionDone(e),this.focusRestorer.restore(),this.glassPane.hide(),kt.instance=null}createElementForItem(e){const t=St.previewElement(e);return h.ARIAUtils.markAsMenuItem(t),t.classList.remove("selected"),t}heightForItem(e){return console.assert(!1,"Should not be called"),0}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,n){i&&i.classList.remove("selected"),n&&n.classList.add("selected")}updateSelectedItemARIA(e,t){return!1}static instance=null}class Et extends h.Toolbar.ToolbarItem{contentElement;constructor(e){const t=document.createElement("button");t.classList.add("history-dropdown-button"),super(t),this.contentElement=this.element.createChild("span","content"),this.element.addEventListener("click",(()=>{e.execute()}),!1),this.setEnabled(e.enabled()),e.addEventListener("Enabled",(e=>this.setEnabled(e.data))),this.setTitle(e.title())}setText(e){this.contentElement.textContent=e}}var Mt=Object.freeze({__proto__:null,TimelineHistoryManager:St,maxRecordings:bt,previewWidth:Ct,DropDown:kt,ToolbarButton:Et});const Ft={malformedTimelineDataUnknownJson:"Malformed timeline data: Unknown JSON format",malformedTimelineInputWrongJson:"Malformed timeline input, wrong JSON brackets balance",malformedTimelineDataS:"Malformed timeline data: {PH1}",legacyTimelineFormatIsNot:"Legacy Timeline format is not supported.",malformedCpuProfileFormat:"Malformed CPU profile format"},It=e.i18n.registerUIStrings("panels/timeline/TimelineLoader.ts",Ft),Lt=e.i18n.getLocalizedString.bind(void 0,It);class xt{client;tracingModel;canceledCallback;state;buffer;firstRawChunk;firstChunk;loadedBytes;totalSize;jsonTokenizer;filter;#V=[];#te;#ie;constructor(e,t){this.client=e,this.tracingModel=new i.Legacy.TracingModel(t),this.canceledCallback=null,this.state="Initial",this.buffer="",this.firstRawChunk=!0,this.firstChunk=!0,this.loadedBytes=0,this.jsonTokenizer=new C.TextUtils.BalancedJSONTokenizer(this.writeBalancedJSON.bind(this),!0),this.filter=null,this.#ie=new Promise((e=>{this.#te=e}))}static async loadFromFile(e,t){const i=new xt(t),n=new c.FileUtils.ChunkedFileReader(e,Rt);return i.canceledCallback=n.cancel.bind(n),i.totalSize=e.size,setTimeout((async()=>{!await n.read(i)&&n.error()&&i.reportErrorAndCancelLoading(n.error().message)})),i}static loadFromEvents(e,t){const i=new xt(t);return window.setTimeout((async()=>{i.addEvents(e)})),i}static getCpuProfileFilter(){const e=[];return e.push(d.TimelineModel.RecordType.JSFrame),e.push(d.TimelineModel.RecordType.JSIdleFrame),e.push(d.TimelineModel.RecordType.JSSystemFrame),new d.TimelineModelFilter.TimelineVisibleEventsFilter(e)}static loadFromCpuProfile(e,t,i){const n=new xt(t,i);n.state="LoadingCPUProfileFromRecording";try{const t=d.TimelineJSProfile.TimelineJSProfileProcessor.createFakeTraceFromCpuProfile(e,1,!0);n.filter=xt.getCpuProfileFilter(),window.setTimeout((async()=>{n.addEvents(t)}))}catch(e){console.error(e.stack)}return n}static async loadFromURL(e,t){const i=new xt(t),n=new r.StringOutputStream.StringOutputStream;await t.loadingStarted();const a=r.Settings.Settings.instance().moduleSetting("network.enable-remote-file-loading").get();return T.ResourceLoader.loadAsStream(e,null,n,(async function(e,t,r){if(!e)return i.reportErrorAndCancelLoading(r.message);const a=n.data(),s=JSON.parse(a);if(Array.isArray(s.nodes))return i.state="LoadingCPUProfileFromFile",i.buffer=a,void await i.close();const o=Array.isArray(s.traceEvents)?s.traceEvents:s;i.addEvents(o)}),a),i}async addEvents(e){await(this.client?.loadingStarted());for(let t=0;t<e.length;t+=15e3){const i=e.slice(t,t+15e3);this.#ne(i),this.tracingModel.addEvents(i),await(this.client?.loadingProgress((t+i.length)/e.length)),await new Promise((e=>window.setTimeout(e)))}this.close()}async cancel(){this.tracingModel=null,this.client&&(await this.client.loadingComplete([],null,null,!1,null),this.client=null),this.canceledCallback&&this.canceledCallback()}async write(e){if(!this.client)return Promise.resolve();if(this.loadedBytes+=e.length,this.firstRawChunk)await this.client.loadingStarted(),await new Promise((e=>requestAnimationFrame((()=>requestAnimationFrame(e)))));else{let e;this.totalSize&&(e=this.loadedBytes/this.totalSize,e=e>1?e-Math.floor(e):e),await this.client.loadingProgress(e)}if(this.firstRawChunk=!1,"Initial"===this.state)if(e.match(/^{(\s)*"nodes":(\s)*\[/))this.state="LoadingCPUProfileFromFile";else if("{"===e[0])this.state="LookingForEvents";else{if("["!==e[0])return this.reportErrorAndCancelLoading(Lt(Ft.malformedTimelineDataUnknownJson)),Promise.resolve();this.state="ReadingEvents"}if("LoadingCPUProfileFromFile"===this.state)return this.buffer+=e,Promise.resolve();if("LookingForEvents"===this.state){const t='"traceEvents":',i=this.buffer.length-t.length;this.buffer+=e;const n=this.buffer.indexOf(t,i);if(-1===n)return Promise.resolve();e=this.buffer.slice(n+t.length),this.state="ReadingEvents"}return"ReadingEvents"!==this.state||this.jsonTokenizer.write(e)||(this.state="SkippingTail",this.firstChunk&&this.reportErrorAndCancelLoading(Lt(Ft.malformedTimelineInputWrongJson))),Promise.resolve()}writeBalancedJSON(e){let t,i=e+"]";if(!this.firstChunk){const e=i.indexOf(",");-1!==e&&(i=i.slice(e+1)),i="["+i}try{t=JSON.parse(i)}catch(e){return void this.reportErrorAndCancelLoading(Lt(Ft.malformedTimelineDataS,{PH1:e.toString()}))}if(this.firstChunk&&(this.firstChunk=!1,this.looksLikeAppVersion(t[0])))this.reportErrorAndCancelLoading(Lt(Ft.legacyTimelineFormatIsNot));else try{this.tracingModel.addEvents(t),this.#ne(t)}catch(e){this.reportErrorAndCancelLoading(Lt(Ft.malformedTimelineDataS,{PH1:e.toString()}))}}reportErrorAndCancelLoading(e){e&&r.Console.Console.instance().error(e),this.cancel()}looksLikeAppVersion(e){return"string"==typeof e&&-1!==e.indexOf("Chrome")}async close(){this.client&&(await this.client.processingStarted(),await this.finalizeTrace())}isCpuProfile(){return"LoadingCPUProfileFromFile"===this.state||"LoadingCPUProfileFromRecording"===this.state}async finalizeTrace(){"LoadingCPUProfileFromFile"===this.state&&(this.parseCPUProfileFormat(this.buffer),this.buffer=""),this.tracingModel.tracingComplete(),await this.client.loadingComplete(this.#V,this.tracingModel,this.filter,this.isCpuProfile(),null),this.#te?.()}traceFinalizedForTest(){return this.#ie}parseCPUProfileFormat(e){let t;try{const i=JSON.parse(e);t=d.TimelineJSProfile.TimelineJSProfileProcessor.createFakeTraceFromCpuProfile(i,1,!0)}catch(e){return void this.reportErrorAndCancelLoading(Lt(Ft.malformedCpuProfileFormat))}this.filter=xt.getCpuProfileFilter(),this.tracingModel.addEvents(t),this.#ne(t)}#ne(e){this.#V=this.#V.concat(e)}}const Rt=5e6;var Dt=Object.freeze({__proto__:null,TimelineLoader:xt,TransferChunkLengthBytes:Rt});const At=new CSSStyleSheet;At.replaceSync('.timeline-minimap .overview-strip{margin-top:2px;justify-content:center}.timeline-minimap .overview-strip .timeline-overview-strip-title{color:var(--sys-color-token-subtle);font-size:10px;font-weight:bold;z-index:100;background-color:var(--sys-color-cdt-base-container);padding:0 4px;position:absolute;top:-2px;right:0}.timeline-minimap #timeline-overview-cpu-activity{flex-basis:20px}.timeline-minimap #timeline-overview-network{flex-basis:8px}.timeline-minimap #timeline-overview-filmstrip{flex-basis:30px}.timeline-minimap #timeline-overview-memory{flex-basis:20px}.timeline-minimap #timeline-overview-network::before,\n.timeline-minimap #timeline-overview-cpu-activity::before{content:"";position:absolute;left:0;right:0;bottom:0;border-bottom:1px solid var(--divider-line);z-index:-200}.timeline-minimap .overview-strip .background{z-index:-10}.timeline-minimap #timeline-overview-responsiveness{flex-basis:5px;margin-top:0!important}.timeline-minimap #timeline-overview-input{flex-basis:6px}.timeline-minimap #timeline-overview-pane{flex:auto;position:relative;overflow:hidden}.timeline-minimap #timeline-overview-container{display:flex;flex-direction:column;flex:none;position:relative;overflow:hidden}.timeline-minimap #timeline-overview-container canvas{width:100%;height:100%}.timeline-minimap .memory-graph-label{position:absolute;right:0;bottom:0;font-size:9px;color:var(--sys-color-token-subtle);white-space:nowrap;padding:0 4px;background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=timelineMiniMap.css */\n');class Bt extends(r.ObjectWrapper.eventMixin(h.Widget.VBox)){breadcrumbsActivated=!1;#re=new s.TimelineOverviewPane.TimelineOverviewPane("timeline");#ae=[];breadcrumbs=null;#se;#oe=null;#I=this.#L.bind(this);constructor(){super(),this.element.classList.add("timeline-minimap"),this.#se=new a.BreadcrumbsUI.BreadcrumbsUI,this.#re.show(this.element),this.#re.addEventListener("OverviewPaneWindowChanged",(e=>{this.#le(e)})),this.#ce(),m.TraceBounds.onChange(this.#I)}#le(e){const t=this.#oe?.traceParsedData;if(!t)return;const n=m.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=e.data.startTime>0?e.data.startTime:n.milli.entireTraceBounds.min,a=Number.isFinite(e.data.endTime)?e.data.endTime:n.milli.entireTraceBounds.max;m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(r),i.Types.Timing.MilliSeconds(a)),{shouldAnimate:!0})}#L(e){"RESET"!==e.updateType&&"VISIBLE_WINDOW"!==e.updateType||this.#re.setWindowTimes(e.state.milli.timelineTraceWindow.min,e.state.milli.timelineTraceWindow.max),"RESET"!==e.updateType&&"MINIMAP_BOUNDS"!==e.updateType||this.#re.setBounds(e.state.milli.minimapTraceBounds.min,e.state.milli.minimapTraceBounds.max)}#ce(){this.breadcrumbsActivated=!0,this.element.prepend(this.#se),this.#re.addEventListener("OverviewPaneBreadcrumbAdded",(e=>{this.addBreadcrumb(e.data)})),this.#se.addEventListener(a.BreadcrumbsUI.BreadcrumbRemovedEvent.eventName,(e=>{const t=e.breadcrumb;this.#de(t)})),this.#re.enableCreateBreadcrumbsButton()}addBreadcrumb({startTime:e,endTime:t}){const n=m.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=n.milli.minimapTraceBounds,s={startTime:i.Types.Timing.MilliSeconds(Math.max(e,r.min)),endTime:i.Types.Timing.MilliSeconds(Math.min(t,r.max))},o=i.Helpers.Timing.traceWindowFromMilliSeconds(s.startTime,s.endTime);m.TraceBounds.BoundsManager.instance().setMiniMapBounds(o),m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(o),null===this.breadcrumbs?this.breadcrumbs=new a.Breadcrumbs.Breadcrumbs(o):this.breadcrumbs.add(o),this.#se.data={breadcrumb:this.breadcrumbs.initialBreadcrumb}}#de(e){const t=i.Helpers.Timing.traceWindowMilliSeconds(e.window);this.breadcrumbs&&(this.breadcrumbs.makeBreadcrumbActive(e),this.#se.data={breadcrumb:this.breadcrumbs.initialBreadcrumb});const n=i.Helpers.Timing.traceWindowFromMilliSeconds(t.min,t.max);m.TraceBounds.BoundsManager.instance().setMiniMapBounds(n),m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(n)}wasShown(){super.wasShown(),this.registerCSSFiles([At])}reset(){this.#oe=null,this.#re.reset()}#he(e){const t=new Map,{Meta:n,PageLoadMetrics:r}=e,a=n.mainFrameNavigations,s=i.Helpers.Timing.microSecondsToMilliseconds(n.traceBounds.min);for(const e of a){const{startTime:n}=i.Legacy.timesForEventInMilliseconds(e);t.set(n,vi.createEventDivider(e,s))}for(const e of r.allMarkerEvents){const{startTime:n}=i.Legacy.timesForEventInMilliseconds(e);t.set(n,vi.createEventDivider(e,s))}this.#re.setMarkers(t)}#me(e){this.#re.setNavStartTimes(e.Meta.mainFrameNavigations)}getControls(){return this.#ae}setData(e){if(this.#oe?.traceParsedData!==e.traceParsedData){if(this.#oe=e,this.#ae=[],this.#he(e.traceParsedData),this.#me(e.traceParsedData),this.#ae.push(new mt(e.traceParsedData)),this.#ae.push(new ht(e.traceParsedData)),this.#ae.push(new ct(e.traceParsedData)),e.settings.showScreenshots){const t=i.Extras.FilmStrip.fromTraceData(e.traceParsedData);t.frames.length&&this.#ae.push(new pt(t))}e.settings.showMemory&&this.#ae.push(new ut(e.traceParsedData)),this.#re.setOverviewControls(this.#ae),this.#re.showingScreenshots=e.settings.showScreenshots}}addInitialBreadcrumb(){this.breadcrumbs=null;const e=m.TraceBounds.BoundsManager.instance().state();e&&this.addBreadcrumb({startTime:e.milli.entireTraceBounds.min,endTime:e.milli.entireTraceBounds.max})}}var Ht=Object.freeze({__proto__:null,TimelineMiniMap:Bt});const Nt=new CSSStyleSheet;Nt.replaceSync('.timeline-toolbar-container{display:flex;flex:none}.timeline-toolbar-container > .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.timeline-main-toolbar{flex:1 1 auto}.timeline-settings-pane{flex:none;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}#timeline-overview-panel{flex:none;position:relative;border-bottom:1px solid var(--sys-color-divider)}#timeline-overview-grid{background-color:var(--sys-color-cdt-base-container)}#timeline-overview-grid .timeline-grid-header{height:12px}#timeline-overview-grid .resources-dividers-label-bar{pointer-events:auto;height:12px}#timeline-overview-grid .resources-divider-label{top:1px}.timeline-details-split{flex:auto}.timeline.panel .status-pane-container{z-index:1000;display:flex;align-items:center;pointer-events:none}.timeline.panel .status-pane-container.tinted{background-color:var(--sys-color-cdt-base-container);pointer-events:auto}.popover ul{margin:0;padding:0;list-style-type:none}#memory-graphs-canvas-container{overflow:hidden;flex:auto;position:relative}#memory-counters-graph{flex:auto}#memory-graphs-canvas-container .memory-counter-marker{position:absolute;border-radius:3px;width:5px;height:5px;margin-left:-3px;margin-top:-2px}#memory-graphs-container .timeline-memory-header{flex:0 0 26px;background-color:var(--sys-color-surface2);border-bottom:1px solid var(--sys-color-divider);justify-content:space-between}#memory-graphs-container .timeline-memory-header::after{content:"";background-image:var(--image-file-toolbarResizerVertical);background-repeat:no-repeat;background-position:right center,center;flex:20px 0 0;margin:0 4px}.timeline-memory-toolbar{flex-shrink:1}.memory-counter-value{margin:8px}#counter-values-bar{flex:0 0 20px;border-top:solid 1px var(--sys-color-divider);width:100%;overflow:hidden;line-height:18px}.timeline-details{vertical-align:top}.timeline-details-view{color:var(--sys-color-on-surface);overflow:hidden}.timeline-details-view-body{flex:auto;overflow:auto;position:relative;background-color:var(--sys-color-cdt-base-container);user-select:text}.timeline-details-view-block{flex:none;display:flex;background-color:var(--sys-color-cdt-base-container);flex-direction:column;padding-bottom:5px;border-bottom:1px solid var(--sys-color-divider)}.timeline-details-view-row{padding-left:10px;line-height:1.3}.timeline-details-view-block .timeline-details-stack-values{flex-direction:column!important}.timeline-details-chip-title{font-size:12px;padding:8px;display:flex;align-items:center}.timeline-details-view-block:first-child > .timeline-details-chip-title{font-size:13px}.timeline-details-view-row-title:not(:empty){color:var(--sys-color-token-subtle);overflow:hidden;padding-right:10px;display:inline-block}.timeline-details-warning{--override-details-warning-background-color:rgb(250 209 209/48%);background-color:var(--override-details-warning-background-color)}.-theme-with-dark-background .timeline-details-warning,\n:host-context(.-theme-with-dark-background) .timeline-details-warning{--override-details-warning-background-color:rgb(87 10 10/48%)}.timeline-details-warning .timeline-details-view-row-title{color:var(--sys-color-error)}.timeline-details-view-row-value{display:inline-block;user-select:text;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;padding:0 3px}.timeline-details-warning .timeline-details-view-row-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timeline-details-view-pie-chart-wrapper{margin:4px 0}.timeline-details-view-pie-chart{margin-top:5px}.timeline-flamechart{overflow:hidden}.brick-game{background-color:var(--sys-color-neutral-container);position:fixed;top:0;left:0;width:100%;height:100%;z-index:9999}.game-close-button{display:flex;align-items:center;justify-content:center;width:25px;height:25px;position:absolute;right:15px;top:15px;border-radius:50%;cursor:pointer}.scorePanel{display:flex;align-items:center;justify-content:center;flex-direction:column;white-space:pre-line;padding:15px;position:absolute;left:15px;bottom:15px;border:double 7px transparent;border-radius:20px;background-origin:border-box;background-clip:content-box,border-box;font-weight:200}.confetti-100{display:block;top:0;left:0;width:100%;height:100%}.confetti-100 > .confetti-100-particle{opacity:0%;position:fixed;animation:confetti-100-animation 1s none ease-out;font-size:30px}@keyframes confetti-100-animation{0%{opacity:100%;transform:translateY(0%) translateY(0%) rotate(0deg)}100%{opacity:0%;transform:translateY(var(--to-Y)) translateX(var(--to-X)) rotate(var(--rotation))}}@media (prefers-reduced-motion){.confetti-100 > .confetti-100-particle{animation-name:dissolve}}.timeline-flamechart-resizer{flex:8px 0 0;background-color:var(--sys-color-surface2);border:1px var(--sys-color-divider);border-style:solid none;display:flex;flex-direction:row;align-items:flex-end;justify-content:center}.timeline-network-resizer-disabled > .timeline-flamechart-resizer{display:none}.timeline-flamechart-resizer::after{content:"...";font-size:14px;margin-bottom:-1px}.timeline-layers-view-properties table{width:100%;border-collapse:collapse}.timeline-layers-view-properties td{border:1px solid var(--sys-color-divider);line-height:22px}.timeline-filmstrip-preview > img{margin-top:5px;max-width:500px;max-height:300px;cursor:pointer;border:1px solid var(--sys-color-divider)}.timeline-tree-view{display:flex;overflow:hidden}.timeline-tree-view .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.timeline-tree-view .data-grid{border:none;flex:auto}.timeline-tree-view .data-grid .data-container{overflow-y:scroll}.timeline-tree-view .data-grid.data-grid-fits-viewport .corner{display:table-cell}.timeline-tree-view .data-grid table.data{background:var(--sys-color-cdt-base-container)}.timeline-tree-view .data-grid .odd{background-color:var(--sys-color-surface1)}.timeline-tree-view .data-grid tr:hover td:not(.bottom-filler-td){background-color:var(--sys-color-state-hover-on-subtle)}.timeline-tree-view .data-grid td.numeric-column{text-align:right;position:relative}.timeline-tree-view .data-grid div.background-percent-bar{float:right;position:relative;z-index:1}.timeline-tree-view .data-grid span.percent-column{color:var(--sys-color-token-subtle);width:45px;display:inline-block}.timeline-tree-view .data-grid tr.selected span{color:inherit}.timeline-tree-view .data-grid tr.selected{background-color:var(--sys-color-tonal-container)}.timeline-tree-view .data-grid .name-container{display:flex;align-items:center;padding-left:2px}.timeline-tree-view .data-grid .name-container .activity-icon{width:12px;height:12px;border:1px solid var(--divider-line);margin:3px 0}.timeline-tree-view .data-grid .name-container .activity-icon-container{margin-right:3px;display:flex;flex-wrap:wrap;align-items:center;justify-content:center;width:18px;height:18px;overflow:hidden}.timeline-tree-view .data-grid .name-container .activity-warning::after{content:"[deopt]";margin:0 4px;line-height:12px;font-size:10px;color:var(--sys-color-state-disabled)}.timeline-tree-view .data-grid tr.selected .name-container .activity-warning::after{color:var(--sys-color-on-tonal-container)}.timeline-tree-view .data-grid .name-container .activity-link{flex:auto;text-align:right;overflow:hidden;text-overflow:ellipsis;margin-left:5px}.timeline-tree-view .data-grid .background-bar-container{position:absolute;left:3px;right:0}.timeline-tree-view .data-grid .background-bar{float:right;height:18px;background-color:var(--sys-color-surface-yellow);border-bottom:1px solid var(--sys-color-yellow-outline)}.timeline-tree-view .data-grid .selected .background-bar{background-color:var(--app-color-selected-progress-bar);border-bottom:1px solid var(--app-border-selected-progress-bar)}.timeline-tree-view .timeline-details-view-body .full-widget-dimmed-banner{background-color:inherit}.timeline-details .filter-input-field{width:120px}.timeline-tree-view .data-grid thead{height:21px;z-index:2}.timeline-stack-view-header{height:27px;background-color:var(--sys-color-cdt-base-container);padding:6px 10px;color:var(--sys-color-on-surface);white-space:nowrap;border-bottom:1px solid var(--sys-color-divider)}.timeline-landing-page{position:absolute;background-color:var(--sys-color-cdt-base-container);justify-content:center;align-items:center;overflow:auto;font-size:13px;color:var(--sys-color-on-surface-subtle)}@media (forced-colors: active){.timeline-tree-view .data-grid .name-container .activity-icon{forced-color-adjust:none}.timeline-tree-view .data-grid tr.selected span.percent-column,\n  .timeline-tree-view .data-grid tr.selected div.background-percent-bar span,\n  .timeline-tree-view .data-grid tr.selected .name-container .activity-link .devtools-link{color:HighlightText}.timeline-tree-view .data-grid .background-bar,\n  .timeline-tree-view .data-grid tr:hover td:not(.bottom-filler-td){background-color:transparent}.timeline-tree-view .data-grid tr.selected .background-bar{background-color:transparent;border-bottom-color:HighlightText}}.timeline-details-view-body > div{overflow-y:hidden;overflow-x:hidden}.timeline-landing-page > div{max-width:450px;margin:10px}.timeline-details-chip-title > div{width:12px;height:12px;border:1px solid var(--sys-color-divider);display:inline-block;margin-right:4px;content:" "}.timeline-paint-profiler-log-split > div:last-child{background-color:var(--color-background-elevation-1);z-index:0}.timeline-layers-view > div:last-child,\n.timeline-layers-view-properties > div:last-child{background-color:var(--color-background-elevation-1)}.timeline.panel .status-pane-container > div{pointer-events:auto}.timeline-landing-page > div > p{flex:none;white-space:pre-line;line-height:18px}.timeline-tree-view .data-grid .name-container div{flex:none}.status-pane-container > .small-dialog{width:100%;height:100%}.timeline-concurrency-input{width:50px}.timeline-concurrency-hidden{visibility:hidden}devtools-feedback-button{float:right}\n/*# sourceURL=timelinePanel.css */\n');const Ut=Symbol("SelectionRange");class Wt{startTime;endTime;object;constructor(e,t,i){this.startTime=e,this.endTime=t,this.object=i}static isFrameObject(e){return e instanceof i.Handlers.ModelHandlers.Frames.TimelineFrame}static fromFrame(e){return new Wt(i.Helpers.Timing.microSecondsToMilliseconds(e.startTime),i.Helpers.Timing.microSecondsToMilliseconds(e.endTime),e)}static isSyntheticNetworkRequestDetailsEventSelection(e){return!(e instanceof i.Legacy.Event)&&(!Wt.isFrameObject(e)&&!Wt.isRangeSelection(e)&&(!!i.Legacy.eventIsFromNewEngine(e)&&i.Types.TraceEvents.isSyntheticNetworkRequestDetailsEvent(e)))}static isTraceEventSelection(e){return e instanceof i.Legacy.Event||!Wt.isFrameObject(e)&&!Wt.isRangeSelection(e)&&(!i.Types.TraceEvents.isSyntheticNetworkRequestDetailsEvent(e)&&i.Legacy.eventIsFromNewEngine(e))}static fromTraceEvent(e){const{startTime:t,endTime:n}=i.Legacy.timesForEventInMilliseconds(e);return new Wt(t,i.Types.Timing.MilliSeconds(n||t+1),e)}static isRangeSelection(e){return e===Ut}static fromRange(e,t){return new Wt(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t),Ut)}}var Vt=Object.freeze({__proto__:null,TimelineSelection:Wt});const Ot=new CSSStyleSheet;Ot.replaceSync(".timeline-status-dialog{display:flex;flex-direction:column;padding:16px 16px 12px;align-self:center;background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);border-radius:10px}.status-dialog-line{margin:2px;height:14px;min-height:auto;display:flex;align-items:baseline}.status-dialog-line .label{display:inline-block;width:80px;text-align:right;color:var(--sys-color-on-surface);margin-right:10px}.timeline-status-dialog .progress .indicator-container{display:inline-block;width:200px;height:8px;background-color:var(--sys-color-surface5)}.timeline-status-dialog .progress .indicator{background-color:var(--sys-color-primary);height:100%;width:0;margin:0}.timeline-status-dialog .stop-button{margin-top:8px;height:100%;align-self:flex-end}.timeline-status-dialog .stop-button button{border-radius:12px}.timeline-status-dialog.small-dialog{width:inherit;justify-content:center}.small-dialog > .stop-button{align-self:center;margin-top:20px;height:initial}@media (forced-colors: active){.timeline-status-dialog{border:1px solid canvastext}.timeline-status-dialog .progress .indicator-container{border:1px solid ButtonText;background-color:ButtonFace}.timeline-status-dialog .progress .indicator{forced-color-adjust:none;background-color:ButtonText}}\n/*# sourceURL=timelineStatusDialog.css */\n");const zt={frameStart:"Frame Start",drawFrame:"Draw Frame",layout:"Layout",rasterizing:"Rasterizing",drawing:"Drawing",painting:"Painting",system:"System",idle:"Idle"},Gt=e.i18n.registerUIStrings("panels/timeline/UIDevtoolsUtils.ts",zt),_t=e.i18n.getLocalizedString.bind(void 0,Gt);let jt=null,qt=null;class Jt{static isUiDevTools(){return"true"===l.Runtime.Runtime.queryParam("uiDevTools")}static categorizeEvents(){if(jt)return jt;const e=$t,t=Jt.categories(),i=t.drawing,n=t.rasterizing,r=t.layout,a=t.painting,s=t.other,o={};return o[e.ViewPaint]=new $("View::Paint",a),o[e.ViewOnPaint]=new $("View::OnPaint",a),o[e.ViewPaintChildren]=new $("View::PaintChildren",a),o[e.ViewOnPaintBackground]=new $("View::OnPaintBackground",a),o[e.ViewOnPaintBorder]=new $("View::OnPaintBorder",a),o[e.LayerPaintContentsToDisplayList]=new $("Layer::PaintContentsToDisplayList",a),o[e.ViewLayout]=new $("View::Layout",r),o[e.ViewLayoutBoundsChanged]=new $("View::Layout(bounds_changed)",r),o[e.RasterTask]=new $("RasterTask",n),o[e.RasterizerTaskImplRunOnWorkerThread]=new $("RasterizerTaskImpl::RunOnWorkerThread",n),o[e.DirectRendererDrawFrame]=new $("DirectRenderer::DrawFrame",i),o[e.BeginFrame]=new $(_t(zt.frameStart),i,!0),o[e.DrawFrame]=new $(_t(zt.drawFrame),i,!0),o[e.NeedsBeginFrameChanged]=new $("NeedsBeginFrameChanged",i,!0),o[e.ThreadControllerImplRunTask]=new $("ThreadControllerImpl::RunTask",s),jt=o,o}static categories(){return qt||(qt={layout:new X("layout",_t(zt.layout),!0,"--app-color-loading-children","--app-color-loading"),rasterizing:new X("rasterizing",_t(zt.rasterizing),!0,"--app-color-children","--app-color-scripting"),drawing:new X("drawing",_t(zt.drawing),!0,"--app-color-rendering-children","--app-color-rendering"),painting:new X("painting",_t(zt.painting),!0,"--app-color-painting-children","--app-color-painting"),other:new X("other",_t(zt.system),!1,"--app-color-system-children","--app-color-system"),idle:new X("idle",_t(zt.idle),!1,"--app-color-idle-children","--app-color-idle")},qt)}static getMainCategoriesList(){return["idle","drawing","painting","rasterizing","layout","other"]}}var $t;!function(e){e.ViewPaint="View::Paint",e.ViewOnPaint="View::OnPaint",e.ViewPaintChildren="View::PaintChildren",e.ViewOnPaintBackground="View::OnPaintBackground",e.ViewOnPaintBorder="View::OnPaintBorder",e.ViewLayout="View::Layout",e.ViewLayoutBoundsChanged="View::Layout(bounds_changed)",e.LayerPaintContentsToDisplayList="Layer::PaintContentsToDisplayList",e.DirectRendererDrawFrame="DirectRenderer::DrawFrame",e.RasterTask="RasterTask",e.RasterizerTaskImplRunOnWorkerThread="RasterizerTaskImpl::RunOnWorkerThread",e.BeginFrame="BeginFrame",e.DrawFrame="DrawFrame",e.NeedsBeginFrameChanged="NeedsBeginFrameChanged",e.ThreadControllerImplRunTask="ThreadControllerImpl::RunTask"}($t||($t={}));var Xt=Object.freeze({__proto__:null,UIDevtoolsUtils:Jt,get RecordType(){return $t}});class Kt extends it{constructor(e,t,i){super(e,t,i),vi.setEventStylesMap(Jt.categorizeEvents()),vi.setCategories(Jt.categories()),vi.setTimelineMainEventCategories(Jt.getMainCategoriesList())}}var Yt=Object.freeze({__proto__:null,UIDevtoolsController:Kt});const Zt={dropTimelineFileOrUrlHere:"Drop timeline file or URL here",disableJavascriptSamples:"Disable JavaScript samples",enableAdvancedPaint:"Enable advanced paint instrumentation (slow)",screenshots:"Screenshots",memory:"Memory",clear:"Clear",fixMe:"Fix me",loadProfile:"Load profile…",saveProfile:"Save profile…",captureScreenshots:"Capture screenshots",showMemoryTimeline:"Show memory timeline",captureSettings:"Capture settings",disablesJavascriptSampling:"Disables JavaScript sampling, reduces overhead when running against mobile devices",capturesAdvancedPaint:"Captures advanced paint instrumentation, introduces significant performance overhead",network:"Network:",cpu:"CPU:",networkConditions:"Network conditions",failedToSaveTimelineSS:"Failed to save timeline: {PH1} ({PH2})",CpuThrottlingIsEnabled:"- CPU throttling is enabled",NetworkThrottlingIsEnabled:"- Network throttling is enabled",HardwareConcurrencyIsEnabled:"- Hardware concurrency override is enabled",SignificantOverheadDueToPaint:"- Significant overhead due to paint instrumentation",JavascriptSamplingIsDisabled:"- JavaScript sampling is disabled",stoppingTimeline:"Stopping timeline…",received:"Received",close:"Close",downloadAfterError:"Download raw trace events",recordingFailed:"Recording failed",profiling:"Profiling…",bufferUsage:"Buffer usage",learnmore:"Learn more",wasd:"WASD",clickTheRecordButtonSOrHitSTo:"Click the record button {PH1} or hit {PH2} to start a new recording.",clickTheReloadButtonSOrHitSTo:"Click the reload button {PH1} or hit {PH2} to record the page load.",afterRecordingSelectAnAreaOf:"After recording, select an area of interest in the overview by dragging. Then, zoom and pan the timeline with the mousewheel or {PH1} keys. {PH2}",loadingProfile:"Loading profile…",processingProfile:"Processing profile…",initializingProfiler:"Initializing profiler…",status:"Status",time:"Time",description:"Description",stop:"Stop",ssec:"{PH1} sec"},Qt=e.i18n.registerUIStrings("panels/timeline/TimelinePanel.ts",Zt),ei=e.i18n.getLocalizedString.bind(void 0,Qt);let ti,ii;class ni extends h.Panel.Panel{dropTarget;recordingOptionUIControls;state;recordingPageReload;millisecondsToRecordAfterLoadEvent;toggleRecordAction;recordReloadAction;#pe;performanceModel;disableCaptureJSProfileSetting;captureLayersAndPicturesSetting;showScreenshotsSetting;showMemorySetting;panelToolbar;panelRightToolbar;timelinePane;#K=new Bt;statusPaneContainer;flameChart;searchableViewInternal;showSettingsPaneButton;showSettingsPaneSetting;settingsPane;controller;cpuProfiler;clearButton;fixMeButton;fixMeButtonAdded=!1;loadButton;saveButton;statusPane;landingPage;loader;showScreenshotsToolbarCheckbox;showMemoryToolbarCheckbox;networkThrottlingSelect;cpuThrottlingSelect;fileSelectorElement;selection;traceLoadStart;primaryPageTargetPromiseCallback=e=>{};primaryPageTargetPromise=new Promise((e=>{this.primaryPageTargetPromiseCallback=e}));#ue;#ge=-1;#Te=null;#ve=this.#fe.bind(this);#ye;constructor(){super("timeline");const e=document.createElement("span");e.innerHTML='<div style="\n      font-size: 12px;\n      transform: scale(1.25);\n      color: transparent;\n      background: linear-gradient(90deg, rgb(255 0 0 / 100%) 0%, rgb(255 154 0 / 100%) 10%, rgb(208 222 33 / 100%) 20%, rgb(79 220 74 / 100%) 30%, rgb(63 218 216 / 100%) 40%, rgb(47 201 226 / 100%) 50%, rgb(28 127 238 / 100%) 60%, rgb(95 21 242 / 100%) 70%, rgb(186 12 248 / 100%) 80%, rgb(251 7 217 / 100%) 90%, rgb(255 0 0 / 100%) 100%);\n      -webkit-background-clip: text;\n      ">💫</div>';const t=new f.Adorner.Adorner;t.classList.add("fix-perf-icon"),t.data={name:ei(Zt.fixMe),content:e},this.fixMeButton=new h.Toolbar.ToolbarButton(ei(Zt.fixMe),t),this.fixMeButton.addEventListener("Click",(()=>this.onFixMe()));const a=i.Types.Configuration.DEFAULT;a.experiments.timelineShowAllEvents=l.Runtime.experiments.isEnabled("timeline-show-all-events"),a.experiments.timelineV8RuntimeCallStats=l.Runtime.experiments.isEnabled("timeline-v8-runtime-call-stats"),this.#ue=i.TraceModel.Model.createWithAllHandlers(a),this.element.addEventListener("contextmenu",this.contextMenu.bind(this),!1),this.dropTarget=new h.DropTarget.DropTarget(this.element,[h.DropTarget.Type.File,h.DropTarget.Type.URI],ei(Zt.dropTimelineFileOrUrlHere),this.handleDrop.bind(this)),this.recordingOptionUIControls=[],this.state="Idle",this.recordingPageReload=!1,this.millisecondsToRecordAfterLoadEvent=5e3,this.toggleRecordAction=h.ActionRegistry.ActionRegistry.instance().getAction("timeline.toggle-recording"),this.recordReloadAction=h.ActionRegistry.ActionRegistry.instance().getAction("timeline.record-reload"),this.#pe=new St(this.#K),this.performanceModel=null,this.traceLoadStart=null,this.disableCaptureJSProfileSetting=r.Settings.Settings.instance().createSetting("timeline-disable-js-sampling",!1),this.disableCaptureJSProfileSetting.setTitle(ei(Zt.disableJavascriptSamples)),this.captureLayersAndPicturesSetting=r.Settings.Settings.instance().createSetting("timeline-capture-layers-and-pictures",!1),this.captureLayersAndPicturesSetting.setTitle(ei(Zt.enableAdvancedPaint)),this.showScreenshotsSetting=r.Settings.Settings.instance().createSetting("timeline-show-screenshots",!ii),this.showScreenshotsSetting.setTitle(ei(Zt.screenshots)),this.showScreenshotsSetting.addChangeListener(this.updateOverviewControls,this),this.showMemorySetting=r.Settings.Settings.instance().createSetting("timeline-show-memory",!1),this.showMemorySetting.setTitle(ei(Zt.memory)),this.showMemorySetting.addChangeListener(this.onModeChanged,this);const s=this.element.createChild("div","timeline-toolbar-container");s.setAttribute("jslog",`${w.toolbar()}`),this.panelToolbar=new h.Toolbar.Toolbar("timeline-main-toolbar",s),this.panelToolbar.makeWrappable(!0),this.panelRightToolbar=new h.Toolbar.Toolbar("",s),ii||(this.createSettingsPane(),this.updateShowSettingsToolbarButton()),this.timelinePane=new h.Widget.VBox,this.timelinePane.show(this.element);const o=this.timelinePane.element.createChild("div","hbox");if(o.id="timeline-overview-panel",this.#K.show(o),this.statusPaneContainer=this.timelinePane.element.createChild("div","status-pane-container fill"),this.createFileSelector(),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.Load,this.loadEventFired,this),this.flameChart=new Tn(this),this.#ye=this.#we.bind(this),this.flameChart.getMainFlameChart().addEventListener("ChartPlayableStateChange",this.#ye,this),this.searchableViewInternal=new h.SearchableView.SearchableView(this.flameChart,null),this.searchableViewInternal.setMinimumSize(0,100),this.searchableViewInternal.element.classList.add("searchable-view"),this.searchableViewInternal.show(this.timelinePane.element),this.flameChart.show(this.searchableViewInternal.element),this.flameChart.setSearchableView(this.searchableViewInternal),this.searchableViewInternal.hideWidget(),this.onModeChanged(),this.populateToolbar(),this.showLandingPage(),this.updateTimelineControls(),n.TargetManager.TargetManager.instance().addEventListener("SuspendStateChanged",this.onSuspendStateChanged,this),l.Runtime.experiments.isEnabled("timeline-as-console-profile-result-panel")){const e=n.TargetManager.TargetManager.instance().models(n.CPUProfilerModel.CPUProfilerModel);for(const t of e)for(const e of t.registeredConsoleProfileMessages)this.consoleProfileFinished(e);n.TargetManager.TargetManager.instance().observeModels(n.CPUProfilerModel.CPUProfilerModel,{modelAdded:e=>{e.addEventListener("ConsoleProfileFinished",(e=>this.consoleProfileFinished(e.data)))},modelRemoved:e=>{}})}n.TargetManager.TargetManager.instance().observeTargets({targetAdded:e=>{e===n.TargetManager.TargetManager.instance().primaryPageTarget()&&this.primaryPageTargetPromiseCallback(e)},targetRemoved:e=>{}})}static instance(e={forceNew:null,isNode:!1}){const{forceNew:t,isNode:i}=e;return ii=i,ti&&!t||(ti=new ni),ti}searchableView(){return this.searchableViewInternal}wasShown(){super.wasShown(),h.Context.Context.instance().setFlavor(ni,this),this.registerCSSFiles([Nt]),T.userMetrics.panelLoaded("timeline","DevTools.Launch.Timeline")}willHide(){h.Context.Context.instance().setFlavor(ni,null),this.#pe.cancelIfShowing()}loadFromEvents(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=xt.loadFromEvents(e,this))}getFlameChart(){return this.flameChart}getMinimap(){return this.#K}#we(e){if(e.data){const e=new Date,t=e.getUTCMonth()+1,i=e.getUTCDate();4===t&&(1===i||2===i)&&this.fixMeButtonAdded,0}else this.fixMeButtonAdded=!1,this.panelToolbar.removeToolbarItem(this.fixMeButton)}loadFromCpuProfile(e,t){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=xt.loadFromCpuProfile(e,this,t))}setState(e){this.state=e,this.updateTimelineControls()}createSettingCheckbox(e,t){const i=new h.Toolbar.ToolbarSettingCheckbox(e,t);return this.recordingOptionUIControls.push(i),i}populateToolbar(){this.panelToolbar.appendToolbarItem(h.Toolbar.Toolbar.createActionButton(this.toggleRecordAction)),this.panelToolbar.appendToolbarItem(h.Toolbar.Toolbar.createActionButton(this.recordReloadAction)),this.clearButton=new h.Toolbar.ToolbarButton(ei(Zt.clear),"clear"),this.clearButton.addEventListener("Click",(()=>this.onClearButton())),this.panelToolbar.appendToolbarItem(this.clearButton),this.loadButton=new h.Toolbar.ToolbarButton(ei(Zt.loadProfile),"import"),this.loadButton.addEventListener("Click",(()=>{T.userMetrics.actionTaken(T.UserMetrics.Action.PerfPanelTraceImported),this.selectFileToLoad()})),this.saveButton=new h.Toolbar.ToolbarButton(ei(Zt.saveProfile),"download"),this.saveButton.addEventListener("Click",(e=>{T.userMetrics.actionTaken(T.UserMetrics.Action.PerfPanelTraceExported),this.saveToFile()})),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.loadButton),this.panelToolbar.appendToolbarItem(this.saveButton),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.#pe.button()),this.panelToolbar.registerCSSFiles([ze]),this.panelToolbar.appendSeparator(),this.panelToolbar.appendSeparator(),ii||(this.showScreenshotsToolbarCheckbox=this.createSettingCheckbox(this.showScreenshotsSetting,ei(Zt.captureScreenshots)),this.panelToolbar.appendToolbarItem(this.showScreenshotsToolbarCheckbox)),this.showMemoryToolbarCheckbox=this.createSettingCheckbox(this.showMemorySetting,ei(Zt.showMemoryTimeline)),this.panelToolbar.appendToolbarItem(this.showMemoryToolbarCheckbox),this.panelToolbar.appendToolbarItem(h.Toolbar.Toolbar.createActionButtonForId("components.collect-garbage"));const e=new qe;ii&&(this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(e)),ii||(this.panelRightToolbar.appendSeparator(),this.panelRightToolbar.appendToolbarItem(this.showSettingsPaneButton))}createSettingsPane(){this.showSettingsPaneSetting=r.Settings.Settings.instance().createSetting("timeline-show-settings-toolbar",!1),this.showSettingsPaneButton=new h.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"gear",ei(Zt.captureSettings),"gear-filled"),n.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",this.updateShowSettingsToolbarButton,this),n.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("RateChanged",this.updateShowSettingsToolbarButton,this),n.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("HardwareConcurrencyChanged",this.updateShowSettingsToolbarButton,this),this.disableCaptureJSProfileSetting.addChangeListener(this.updateShowSettingsToolbarButton,this),this.captureLayersAndPicturesSetting.addChangeListener(this.updateShowSettingsToolbarButton,this),this.settingsPane=new h.Widget.HBox,this.settingsPane.element.classList.add("timeline-settings-pane"),this.settingsPane.show(this.element);const e=new h.Toolbar.Toolbar("",this.settingsPane.element);e.element.classList.add("flex-auto"),e.makeVertical(),e.appendToolbarItem(this.createSettingCheckbox(this.disableCaptureJSProfileSetting,ei(Zt.disablesJavascriptSampling))),e.appendToolbarItem(this.createSettingCheckbox(this.captureLayersAndPicturesSetting,ei(Zt.capturesAdvancedPaint)));const t=new h.Widget.VBox;t.element.classList.add("flex-auto"),t.show(this.settingsPane.element);const i=new h.Toolbar.Toolbar("",t.element);i.appendText(ei(Zt.cpu)),this.cpuThrottlingSelect=S.ThrottlingManager.throttlingManager().createCPUThrottlingSelector(),i.appendToolbarItem(this.cpuThrottlingSelect);const a=new h.Toolbar.Toolbar("",t.element);a.appendText(ei(Zt.network)),this.networkThrottlingSelect=this.createNetworkConditionsSelect(),a.appendToolbarItem(this.networkThrottlingSelect);const s=new h.Widget.VBox;s.element.classList.add("flex-auto"),s.show(this.settingsPane.element);const{toggle:o,input:l,reset:c,warning:d}=S.ThrottlingManager.throttlingManager().createHardwareConcurrencySelector(),m=new h.Toolbar.Toolbar("",s.element);m.registerCSSFiles([Nt]),l.element.classList.add("timeline-concurrency-input"),m.appendToolbarItem(o),m.appendToolbarItem(l),m.appendToolbarItem(c),m.appendToolbarItem(d),this.showSettingsPaneSetting.addChangeListener(this.updateSettingsPaneVisibility.bind(this)),this.updateSettingsPaneVisibility()}createNetworkConditionsSelect(){const e=new h.Toolbar.ToolbarComboBox(null,ei(Zt.networkConditions));return e.setMaxWidth(140),S.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(e.selectElement()),e}prepareToLoadTimeline(){console.assert("Idle"===this.state),this.setState("Loading"),this.performanceModel&&(this.performanceModel=null)}createFileSelector(){this.fileSelectorElement&&this.fileSelectorElement.remove(),this.fileSelectorElement=h.UIUtils.createFileSelectorElement(this.loadFromFile.bind(this)),this.timelinePane.element.appendChild(this.fileSelectorElement)}contextMenu(e){const t=new h.ContextMenu.ContextMenu(e);t.appendItemsAtLocation("timelineMenu"),t.show()}async saveToFile(){if("Idle"!==this.state)return;if(!this.performanceModel)return;const e=this.#ue.traceEvents(this.#ge),t=this.#ue.metadata(this.#ge);if(!e)return;const i=o.DateUtilities.toISO8601Compact(new Date);let n;n="CPUProfile"===t?.dataOrigin?`CPU-${i}.cpuprofile`:`Trace-${i}.json`;try{let i;if("CPUProfile"===t?.dataOrigin){const t=e.find((e=>"CpuProfile"===e.name));if(!t||!t.args?.data)return;const n=t.args?.data;if(n.hasOwnProperty("cpuProfile")){i=Ye(n.cpuProfile)}}else{const n=Ke(e,t);i=Array.from(n).join("")}if(!i)throw new Error("Trace content empty");await v.FileManager.FileManager.instance().save(n,i,!0),v.FileManager.FileManager.instance().close(n)}catch(e){if(console.error(e.stack),"AbortError"===e.name)return;r.Console.Console.instance().error(ei(Zt.failedToSaveTimelineSS,{PH1:e.message,PH2:e.name}))}}async showHistory(){const e=await this.#pe.showHistoryDropDown();e&&e.traceParseDataIndex!==this.#ge&&this.setModel(e.legacyModel,null,e.traceParseDataIndex)}navigateHistory(e){const t=this.#pe.navigate(e);return t&&t.traceParseDataIndex!==this.#ge&&this.setModel(t.legacyModel,null,t.traceParseDataIndex),!0}selectFileToLoad(){this.fileSelectorElement&&this.fileSelectorElement.click()}async loadFromFile(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=await xt.loadFromFile(e,this),this.createFileSelector())}async loadFromURL(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=await xt.loadFromURL(e,this))}updateOverviewControls(){const e=this.#ue.traceParsedData(this.#ge),t="CPUProfile"===this.#ue.metadata(this.#ge)?.dataOrigin;e&&this.#K.setData({traceParsedData:e,isCpuProfile:t,settings:{showScreenshots:this.showScreenshotsSetting.get(),showMemory:this.showMemorySetting.get()}})}onModeChanged(){this.updateOverviewControls(),this.doResize(),this.select(null)}updateSettingsPaneVisibility(){ii||(this.showSettingsPaneSetting.get()?this.settingsPane.showWidget():this.settingsPane.hideWidget())}updateShowSettingsToolbarButton(){const e=[];if(1!==n.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate()&&e.push(ei(Zt.CpuThrottlingIsEnabled)),S.ThrottlingManager.throttlingManager().hardwareConcurrencyOverrideEnabled&&e.push(ei(Zt.HardwareConcurrencyIsEnabled)),n.NetworkManager.MultitargetNetworkManager.instance().isThrottling()&&e.push(ei(Zt.NetworkThrottlingIsEnabled)),this.captureLayersAndPicturesSetting.get()&&e.push(ei(Zt.SignificantOverheadDueToPaint)),this.disableCaptureJSProfileSetting.get()&&e.push(ei(Zt.JavascriptSamplingIsDisabled)),this.showSettingsPaneButton.setDefaultWithRedColor(e.length>0),this.showSettingsPaneButton.setToggleWithRedColor(e.length>0),e.length){const t=document.createElement("div");e.forEach((e=>{t.createChild("div").textContent=e})),this.showSettingsPaneButton.setTitle(t.textContent||"")}else this.showSettingsPaneButton.setTitle(ei(Zt.captureSettings))}setUIControlsEnabled(e){this.recordingOptionUIControls.forEach((t=>t.setEnabled(e)))}async#Se(){if(!this.controller)return o.DevToolsPath.EmptyUrlString;const e=this.controller.primaryPageTarget.inspectedURL(),t=this.controller.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel),i=t&&await t.navigationHistory();if(!t||!i)return e;const{currentIndex:r,entries:a}=i;return a[r].url}async#be(){const e=new Promise((async(e,t)=>{if(!this.controller)return void t("Could not find TimelineController");const i=this.controller.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel);i?(i.addEventListener(n.ResourceTreeModel.Events.FrameNavigated,(function r(a){"about:blank"===a.data.url?e():t(`Unexpected navigation to ${a.data.url}`),i?.removeEventListener(n.ResourceTreeModel.Events.FrameNavigated,r)})),await i.navigate("about:blank")):t("Could not load resourceModel")}));await e}async#Ce(){try{if(this.cpuProfiler=h.Context.Context.instance().flavor(n.CPUProfilerModel.CPUProfilerModel),!this.cpuProfiler){const e=n.TargetManager.TargetManager.instance().targets().find((e=>e.type()===n.Target.Type.Node));if(!e)throw new Error("Could not load any Node target.");e&&(this.cpuProfiler=e.model(n.CPUProfilerModel.CPUProfilerModel))}if(this.setUIControlsEnabled(!1),this.hideLandingPage(),!this.cpuProfiler)throw new Error("No Node target is found.");await n.TargetManager.TargetManager.instance().suspendAllTargets("performance-timeline"),await this.cpuProfiler.startRecording(),this.recordingStarted()}catch(e){await this.recordingFailed(e.message)}}async#Pe(){try{const e=n.TargetManager.TargetManager.instance().rootTarget(),t=n.TargetManager.TargetManager.instance().primaryPageTarget();if(!t)throw new Error("Could not load primary page target.");if(!e)throw new Error("Could not load root target.");if(Jt.isUiDevTools()?this.controller=new Kt(e,t,this):this.controller=new it(e,t,this),this.setUIControlsEnabled(!1),this.hideLandingPage(),!this.controller)throw new Error("Could not create Timeline controller");const i=await this.#Se();this.recordingPageReload&&await this.#be();const r={enableJSSampling:!this.disableCaptureJSProfileSetting.get(),capturePictures:this.captureLayersAndPicturesSetting.get(),captureFilmStrip:this.showScreenshotsSetting.get()},a=await this.controller.startRecording(r);if(a.getError())throw new Error(a.getError());const s=this.recordingPageReload?{navigateToUrl:i}:void 0;this.recordingStarted(s)}catch(e){await this.recordingFailed(e.message)}}async startRecording(){console.assert(!this.statusPane,"Status pane is already opened."),this.setState("StartPending"),this.showRecordingStarted(),ii?await this.#Ce():await this.#Pe()}async stopRecording(){if(this.statusPane&&(this.statusPane.finish(),this.statusPane.updateStatus(ei(Zt.stoppingTimeline)),this.statusPane.updateProgressBar(ei(Zt.received),0)),this.setState("StopPending"),this.controller)return this.performanceModel=this.controller.getPerformanceModel(),await this.controller.stopRecording(),this.setUIControlsEnabled(!0),await this.controller.dispose(),void(this.controller=null);if(this.cpuProfiler){const e=await this.cpuProfiler.stopRecording();this.setState("Idle"),this.loadFromCpuProfile(e),this.setUIControlsEnabled(!0),this.cpuProfiler=null,await n.TargetManager.TargetManager.instance().resumeAllTargets()}}async recordingFailed(e,t){this.statusPane&&this.statusPane.remove(),this.statusPane=new ri({description:e,buttonText:ei(Zt.close),buttonDisabled:!1,showProgress:void 0,showTimer:void 0},(async()=>{this.statusPane?.remove(),await this.loadingComplete([],null,null,!1,null)})),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(ei(Zt.recordingFailed)),t&&this.statusPane.enableDownloadOfEvents(t),this.setState("RecordingFailed"),this.performanceModel=null,this.traceLoadStart=null,this.setUIControlsEnabled(!0),this.controller&&(await this.controller.dispose(),this.controller=null),n.TargetManager.TargetManager.instance().resumeAllTargets()}onSuspendStateChanged(){this.updateTimelineControls()}consoleProfileFinished(e){this.loadFromCpuProfile(e.cpuProfile,e.title),h.InspectorView.InspectorView.instance().showPanel("timeline")}updateTimelineControls(){this.toggleRecordAction.setToggled("Recording"===this.state),this.toggleRecordAction.setEnabled("Recording"===this.state||"Idle"===this.state),this.recordReloadAction.setEnabled(!ii&&"Idle"===this.state),this.#pe.setEnabled("Idle"===this.state),this.clearButton.setEnabled("Idle"===this.state),this.panelToolbar.setEnabled("Loading"!==this.state),this.panelRightToolbar.setEnabled("Loading"!==this.state),this.dropTarget.setEnabled("Idle"===this.state),this.loadButton.setEnabled("Idle"===this.state),this.saveButton.setEnabled("Idle"===this.state&&Boolean(this.performanceModel))}async toggleRecording(){"Idle"===this.state?(this.recordingPageReload=!1,await this.startRecording(),T.userMetrics.actionTaken(T.UserMetrics.Action.TimelineStarted)):"Recording"===this.state&&await this.stopRecording()}recordReload(){"Idle"===this.state&&(this.recordingPageReload=!0,this.startRecording(),T.userMetrics.actionTaken(T.UserMetrics.Action.TimelinePageReloadStarted))}onClearButton(){this.#pe.clear(),this.clear()}onFixMe(){this.performanceModel&&this.flameChart.fixMe()}clear(){this.statusPane&&this.statusPane.remove(),this.showLandingPage(),this.reset()}reset(){s.LineLevelProfile.Performance.instance().reset(),this.#Te&&(this.#Te.removeEventListener(Ue.eventName,this.#ve),this.#Te.uninstall(),this.#Te=null),this.setModel(null)}#ke(e,t=null){if(e||l.Runtime.experiments.isEnabled("timeline-show-all-events"))return;const i=t?[t]:[vi.visibleEventsFilter()];Ce.instance().setFilters(i)}applyFilters(e,t=null){this.#ke(!1,t)}setModel(e,t=null,r=-1){this.performanceModel=e,this.#ge=r;const a=this.#ue.traceParsedData(this.#ge),o="CPUProfile"===this.#ue.metadata(this.#ge)?.dataOrigin;if(this.#K.reset(),a&&(m.TraceBounds.BoundsManager.instance().resetWithNewBounds(a.Meta.traceBounds),this.#ke(a.Meta.traceIsGeneric,t)),e?this.searchableViewInternal.showWidget():(this.searchableViewInternal.hideWidget(),this.fixMeButtonAdded=!1,this.panelToolbar.removeToolbarItem(this.fixMeButton)),this.flameChart.setModel(e,a,o),this.flameChart.setSelection(null),s.LineLevelProfile.Performance.instance().reset(),a&&a.Samples.profilesInProcess.size){const e=n.TargetManager.TargetManager.instance().rootTarget(),t=Array.from(a.Samples.profilesInProcess).flatMap((([e,t])=>Array.from(t.values()).map((e=>e.parsedProfile))));for(const i of t)s.LineLevelProfile.Performance.instance().appendCPUProfile(i,e)}if(this.updateOverviewControls(),this.flameChart&&this.flameChart.resizeToPreferredHeights(),a){this.#K.breadcrumbsActivated&&this.#K.addInitialBreadcrumb();const e=["MAIN_THREAD","CPU_PROFILE"],t=i.Handlers.Threads.threadsInTrace(a).filter((t=>e.includes(t.type))).at(-1);if(t){const e=i.Extras.MainThreadActivity.calculateWindow(a.Meta.traceBounds,t.entries);m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(e)}}this.updateTimelineControls()}recordingStarted(e){if(e&&this.recordingPageReload&&this.controller){const t=this.controller?.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel);if(!t)return void this.recordingFailed("Could not navigate to original URL");t.navigate(e.navigateToUrl)}this.reset(),this.setState("Recording"),this.showRecordingStarted(),this.statusPane&&(this.statusPane.enableAndFocusButton(),this.statusPane.updateStatus(ei(Zt.profiling)),this.statusPane.updateProgressBar(ei(Zt.bufferUsage),0),this.statusPane.startTimer()),this.hideLandingPage()}recordingProgress(e){this.statusPane&&this.statusPane.updateProgressBar(ei(Zt.bufferUsage),100*e)}showLandingPage(){if(this.updateSettingsPaneVisibility(),this.landingPage)return void this.landingPage.show(this.statusPaneContainer);function t(e,t){const i=document.createElement(e);return i.textContent=t,i}const i=h.XLink.XLink.create("https://developer.chrome.com/docs/devtools/evaluate-performance/",ei(Zt.learnmore),void 0,void 0,"learn-more"),n=t("b",h.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("timeline.toggle-recording")[0].title()),r=t("b",h.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("timeline.record-reload")[0].title()),a=t("b",ei(Zt.wasd));this.landingPage=new h.Widget.VBox,this.landingPage.contentElement.classList.add("timeline-landing-page","fill");const s=this.landingPage.contentElement.createChild("div"),o=h.UIUtils.createInlineButton(h.Toolbar.Toolbar.createActionButton(this.toggleRecordAction)),l=h.UIUtils.createInlineButton(h.Toolbar.Toolbar.createActionButtonForId("timeline.record-reload"));if(s.createChild("p").appendChild(e.i18n.getFormatLocalizedString(Qt,Zt.clickTheRecordButtonSOrHitSTo,{PH1:o,PH2:n})),s.createChild("p").appendChild(e.i18n.getFormatLocalizedString(Qt,Zt.clickTheReloadButtonSOrHitSTo,{PH1:l,PH2:r})),s.createChild("p").appendChild(e.i18n.getFormatLocalizedString(Qt,Zt.afterRecordingSelectAnAreaOf,{PH1:a,PH2:i})),ii){const e=new y.PanelFeedback.PanelFeedback;e.data={feedbackUrl:"https://crbug.com/1354548",quickStartUrl:"https://goo.gle/js-profiler-deprecation",quickStartLinkText:ei(Zt.learnmore)},s.appendChild(e);const t=new y.FeedbackButton.FeedbackButton;t.data={feedbackUrl:"https://crbug.com/1354548"},s.appendChild(t)}this.landingPage.show(this.statusPaneContainer)}hideLandingPage(){this.landingPage.detach(),this.showSettingsPaneButton?.setToggled(!1),this.settingsPane?.hideWidget()}async loadingStarted(){this.hideLandingPage(),this.statusPane&&this.statusPane.remove(),this.statusPane=new ri({showProgress:!0,showTimer:void 0,buttonDisabled:void 0,buttonText:void 0,description:void 0},(()=>this.cancelLoading())),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(ei(Zt.loadingProfile)),this.loader||this.statusPane.finish(),this.traceLoadStart=i.Types.Timing.MilliSeconds(performance.now()),await this.loadingProgress(0)}async loadingProgress(e){"number"==typeof e&&this.statusPane&&this.statusPane.updateProgressBar(ei(Zt.received),100*e)}async processingStarted(){this.statusPane&&this.statusPane.updateStatus(ei(Zt.processingProfile))}#fe(){this.flameChart.updateColorMapper()}async loadingComplete(e,t,n=null,r,a){this.#ue.resetProcessor(),Ve.clearResolvedNodeNames(),delete this.loader;const s="StopPending"===this.state;if(this.setState("Idle"),t){this.performanceModel||(this.performanceModel=new Je);try{await Promise.all([this.performanceModel.setTracingModel(t,s),this.#Ee(e,s,r,a)]),this.#ge=this.#ue.size()-1,this.setModel(this.performanceModel,n,this.#ge),this.statusPane&&this.statusPane.remove(),this.statusPane=null;const o=this.#ue.traceParsedData(this.#ge);if(!o)throw new Error(`Could not get trace data at index ${this.#ge}`);this.#Te=new Ve(o),this.#Te.addEventListener(Ue.eventName,this.#ve),await this.#Te.install(),this.#pe.addRecording({data:{legacyModel:this.performanceModel,traceParseDataIndex:this.#ge},filmStripForPreview:i.Extras.FilmStrip.fromTraceData(o),traceParsedData:o,startTime:a??null})}catch(e){let i;try{i=t.allRawEvents()}catch{}this.recordingFailed(e.message,i),console.error(e)}finally{this.recordTraceLoadMetric()}}else this.clear()}recordTraceLoadMetric(){if(!this.traceLoadStart)return;const e=this.traceLoadStart;requestAnimationFrame((()=>{setTimeout((()=>{const t=i.Types.Timing.MilliSeconds(performance.now()),n=performance.measure("TraceLoad",{start:e,end:t}),r=i.Types.Timing.MilliSeconds(n.duration);this.element.dispatchEvent(new W(r)),T.userMetrics.performanceTraceLoad(n)}),0)}))}async#Ee(e,t,n,r){const a=t&&!n?await i.Extras.Metadata.forNewRecording(r??void 0):{};return a.dataOrigin=n?"CPUProfile":"TraceEvents",this.#ue.parse(e,{metadata:a,isFreshRecording:t})}loadingCompleteForTest(){}showRecordingStarted(){this.statusPane&&this.statusPane.remove(),this.statusPane=new ri({showTimer:!0,showProgress:!0,buttonDisabled:!0,description:void 0,buttonText:void 0},(()=>this.stopRecording())),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(ei(Zt.initializingProfiler))}cancelLoading(){this.loader&&this.loader.cancel()}async loadEventFired(e){if("Recording"!==this.state||!this.recordingPageReload||!this.controller||this.controller.primaryPageTarget!==e.data.resourceTreeModel.target())return;const t=this.controller;await new Promise((e=>window.setTimeout(e,this.millisecondsToRecordAfterLoadEvent))),t===this.controller&&"Recording"===this.state&&this.stopRecording()}frameForSelection(e){if(Wt.isFrameObject(e.object))return e.object;if(Wt.isRangeSelection(e.object)||Wt.isSyntheticNetworkRequestDetailsEventSelection(e.object))return null;if(Wt.isTraceEventSelection(e.object)){if(!this.performanceModel)return null;const t=this.#ue.traceParsedData(this.#ge);if(!t)return null;const n=i.Helpers.Timing.millisecondsToMicroseconds(e.endTime);return i.Handlers.ModelHandlers.Frames.framesWithinWindow(t.Frames.frames,n,n).at(0)||null}return console.assert(!1,"Should never be reached"),null}jumpToFrame(e){const t=this.selection&&this.frameForSelection(this.selection);if(!t)return;const n=this.#ue.traceParsedData(this.#ge);if(!n)return;let r=n.Frames.frames.indexOf(t);console.assert(r>=0,"Can't find current frame in the frame list"),r=o.NumberUtilities.clamp(r+e,0,n.Frames.frames.length-1);const a=n.Frames.frames[r];return this.#Me(i.Helpers.Timing.microSecondsToMilliseconds(a.startTime),i.Helpers.Timing.microSecondsToMilliseconds(a.endTime)),this.select(Wt.fromFrame(a)),!0}select(e){this.selection=e,this.flameChart.setSelection(e)}selectEntryAtTime(e,t){if(e)if(0!==e.length){for(let n=o.ArrayUtilities.upperBound(e,t,((e,t)=>e-t.ts))-1;n>=0;--n){const r=e[n],{endTime:a}=i.Helpers.Timing.eventTimingsMilliSeconds(r);if(i.Legacy.TracingModel.isTopLevelEvent(r)&&a<t)break;if(Ce.instance().isVisible(r)&&a>=t)return void this.select(Wt.fromTraceEvent(r))}this.select(null)}else this.select(null)}highlightEvent(e){this.flameChart.highlightEvent(e)}#Me(e,t){const n=m.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=n.milli.timelineTraceWindow;let a=0;r.max<t?a=t-r.max:r.min>e&&(a=e-r.min),m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(r.min+a),i.Types.Timing.MilliSeconds(r.max+a)),{shouldAnimate:!0})}handleDrop(e){const t=e.items;if(!t.length)return;const i=t[0];if(T.userMetrics.actionTaken(T.UserMetrics.Action.PerfPanelTraceImported),"string"===i.kind){const t=e.getData("text/uri-list");new r.ParsedURL.ParsedURL(t).isValid&&this.loadFromURL(t)}else if("file"===i.kind){const e=t[0].getAsFile();if(!e)return;this.loadFromFile(e)}}}class ri extends h.Widget.VBox{status;time;progressLabel;progressBar;description;button;downloadTraceButton;startTime;timeUpdateTimer;#Fe;constructor(e,t){super(!0),this.contentElement.classList.add("timeline-status-dialog");const i=this.contentElement.createChild("div","status-dialog-line status");if(i.createChild("div","label").textContent=ei(Zt.status),this.status=i.createChild("div","content"),h.ARIAUtils.markAsStatus(this.status),e.showTimer){const e=this.contentElement.createChild("div","status-dialog-line time");e.createChild("div","label").textContent=ei(Zt.time),this.time=e.createChild("div","content")}if(e.showProgress){const e=this.contentElement.createChild("div","status-dialog-line progress");this.progressLabel=e.createChild("div","label"),this.progressBar=e.createChild("div","indicator-container").createChild("div","indicator"),h.ARIAUtils.markAsProgressBar(this.progressBar)}if("string"==typeof e.description){const t=this.contentElement.createChild("div","status-dialog-line description");t.createChild("div","label").textContent=ei(Zt.description),this.description=t.createChild("div","content"),this.description.innerText=e.description}const n=this.contentElement.createChild("div","stop-button");this.downloadTraceButton=h.UIUtils.createTextButton(ei(Zt.downloadAfterError),(()=>{this.#Ie()}),{jslogContext:"timeline.download-after-error"}),this.downloadTraceButton.disabled=!0,this.downloadTraceButton.style.visibility="hidden";const r=e.buttonText||ei(Zt.stop);this.button=h.UIUtils.createTextButton(r,t,{jslogContext:"timeline.stop-recording",primary:!0}),this.button.disabled=!1==!e.buttonDisabled,n.append(this.downloadTraceButton),n.append(this.button)}finish(){this.stopTimer(),this.button.disabled=!0}async#Ie(){if(!this.#Fe||0===this.#Fe.length)return;const e=`Trace-Load-Error-${o.DateUtilities.toISO8601Compact(new Date)}.json`,t=Ke(this.#Fe,{}),i=Array.from(t).join("");await v.FileManager.FileManager.instance().save(e,i,!0),v.FileManager.FileManager.instance().close(e)}enableDownloadOfEvents(e){this.#Fe=e,this.downloadTraceButton.disabled=!1,this.downloadTraceButton.style.visibility="visible"}remove(){this.element.parentNode&&(this.element.parentNode.classList.remove("tinted"),this.arrangeDialog(this.element.parentNode)),this.stopTimer(),this.element.remove()}showPane(e){this.arrangeDialog(e),this.show(e),e.classList.add("tinted")}enableAndFocusButton(){this.button.disabled=!1,this.button.focus()}updateStatus(e){this.status.textContent=e}updateProgressBar(e,t){this.progressLabel.textContent=e,this.progressBar.style.width=t.toFixed(1)+"%",h.ARIAUtils.setValueNow(this.progressBar,t),this.updateTimer()}startTimer(){this.startTime=Date.now(),this.timeUpdateTimer=window.setInterval(this.updateTimer.bind(this,!1),1e3),this.updateTimer()}stopTimer(){this.timeUpdateTimer&&(clearInterval(this.timeUpdateTimer),this.updateTimer(!0),delete this.timeUpdateTimer)}updateTimer(e){if(this.arrangeDialog(this.element.parentNode),!this.timeUpdateTimer||!this.time)return;const t=(Date.now()-this.startTime)/1e3;this.time.textContent=ei(Zt.ssec,{PH1:t.toFixed(e?1:0)})}arrangeDialog(e){const t=e.clientWidth<325;this.element.classList.toggle("small-dialog",t),this.contentElement.classList.toggle("small-dialog",t)}wasShown(){super.wasShown(),this.registerCSSFiles([Ot])}}let ai;class si{static instance(e={forceNew:null}){const{forceNew:t}=e;return ai&&!t||(ai=new si),ai}handleQueryParam(e){h.ViewManager.ViewManager.instance().showView("timeline").then((async()=>{await ni.instance().loadFromURL(window.decodeURIComponent(e))}))}}var oi=Object.freeze({__proto__:null,TimelinePanel:ni,rowHeight:18,headerHeight:20,StatusPane:ri,LoadTimelineHandler:si,ActionDelegate:class{handleAction(e,t){const i=e.flavor(ni);if(null===i)return!1;switch(t){case"timeline.toggle-recording":return i.toggleRecording(),!0;case"timeline.record-reload":return i.recordReload(),!0;case"timeline.save-to-file":return i.saveToFile(),!0;case"timeline.load-from-file":return i.selectFileToLoad(),!0;case"timeline.jump-to-previous-frame":return i.jumpToFrame(-1),!0;case"timeline.jump-to-next-frame":return i.jumpToFrame(1),!0;case"timeline.show-history":return i.showHistory(),!0;case"timeline.previous-recording":return i.navigateHistory(1),!0;case"timeline.next-recording":return i.navigateHistory(-1),!0}return!1}}});const li={emptyPlaceholder:"{PH1}",task:"Task",other:"Other",animation:"Animation",event:"Event",requestMainThreadFrame:"Request Main Thread Frame",frameStart:"Frame Start",frameStartMainThread:"Frame Start (main thread)",drawFrame:"Draw Frame",profilingOverhead:"Profiling Overhead",hitTest:"Hit Test",scheduleStyleRecalculation:"Schedule Style Recalculation",recalculateStyle:"Recalculate Style",invalidateLayout:"Invalidate Layout",layerize:"Layerize",layout:"Layout",paintSetup:"Paint Setup",paintImage:"Paint Image",prePaint:"Pre-Paint",updateLayer:"Update Layer",updateLayerTree:"Update Layer Tree",initialPriority:"Initial Priority",paint:"Paint",rasterizePaint:"Rasterize Paint",scroll:"Scroll",commit:"Commit",compositeLayers:"Composite Layers",computeIntersections:"Compute Intersections",parseHtml:"Parse HTML",parseStylesheet:"Parse Stylesheet",installTimer:"Install Timer",removeTimer:"Remove Timer",timerFired:"Timer Fired",xhrReadyStateChange:"`XHR` Ready State Change",xhrLoad:"`XHR` Load",compileScript:"Compile Script",cacheScript:"Cache Script Code",compileCode:"Compile Code",optimizeCode:"Optimize Code",evaluateScript:"Evaluate Script",compileModule:"Compile Module",cacheModule:"Cache Module Code",evaluateModule:"Evaluate Module",streamingCompileTask:"Streaming Compile Task",waitingForNetwork:"Waiting for Network",parseAndCompile:"Parse and Compile",deserializeCodeCache:"Deserialize Code Cache",streamingWasmResponse:"Streaming Wasm Response",compiledWasmModule:"Compiled Wasm Module",cachedWasmModule:"Cached Wasm Module",wasmModuleCacheHit:"Wasm Module Cache Hit",wasmModuleCacheInvalid:"Wasm Module Cache Invalid",frameStartedLoading:"Frame Started Loading",onloadEvent:"Onload Event",domcontentloadedEvent:"DOMContentLoaded Event",firstPaint:"First Paint",firstContentfulPaint:"First Contentful Paint",largestContentfulPaint:"Largest Contentful Paint",timestamp:"Timestamp",consoleTime:"Console Time",userTiming:"User Timing",willSendRequest:"Will Send Request",sendRequest:"Send Request",receiveResponse:"Receive Response",finishLoading:"Finish Loading",receiveData:"Receive Data",runMicrotasks:"Run Microtasks",functionCall:"Function Call",gcEvent:"GC Event",majorGc:"Major GC",minorGc:"Minor GC",jsRoot:"JS Root",jsFrame:"JS Frame",jsIdleFrame:"JS Idle Frame",jsSystemFrame:"JS System Frame",requestAnimationFrame:"Request Animation Frame",interactionID:"ID",inputDelay:"Input delay",processingTime:"Processing time",presentationDelay:"Presentation delay",cancelAnimationFrame:"Cancel Animation Frame",animationFrameFired:"Animation Frame Fired",requestIdleCallback:"Request Idle Callback",cancelIdleCallback:"Cancel Idle Callback",fireIdleCallback:"Fire Idle Callback",createWebsocket:"Create WebSocket",sendWebsocketHandshake:"Send WebSocket Handshake",receiveWebsocketHandshake:"Receive WebSocket Handshake",destroyWebsocket:"Destroy WebSocket",embedderCallback:"Embedder Callback",imageDecode:"Image Decode",imageResize:"Image Resize",gpu:"GPU",domGc:"DOM GC",encrypt:"Encrypt",encryptReply:"Encrypt Reply",decrypt:"Decrypt",decryptReply:"Decrypt Reply",digest:"Digest",digestReply:"Digest Reply",sign:"Sign",signReply:"Sign Reply",verify:"Verify",verifyReply:"Verify Reply",asyncTask:"Async Task",layoutShift:"Layout Shift",eventTiming:"Event Timing",compile:"Compile",parse:"Parse",sS:"{PH1}: {PH2}",sCollected:"{PH1} collected",sSs:"{PH1} [{PH2}…{PH3}]",sSSquareBrackets:"{PH1} [{PH2}…]",learnMore:"Learn more",compilationCacheStatus:"Compilation cache status",compilationCacheSize:"Compilation cache size",compilationCacheKind:"Compilation cache kind",scriptLoadedFromCache:"script loaded from cache",failedToLoadScriptFromCache:"failed to load script from cache",scriptNotEligible:"script not eligible",totalTime:"Total Time",selfTime:"Self Time",collected:"Collected",function:"Function",timerId:"Timer ID",timeout:"Timeout",repeats:"Repeats",callbackId:"Callback ID",requestMethod:"Request Method",priority:"Priority",encodedData:"Encoded Data",decodedBody:"Decoded Body",module:"Module",script:"Script",streamed:"Streamed",eagerCompile:"Compiling all functions eagerly",url:"Url",producedCacheSize:"Produced Cache Size",consumedCacheSize:"Consumed Cache Size",location:"Location",sSCurlyBrackets:"({PH1}, {PH2})",dimensions:"Dimensions",sSDimensions:"{PH1} × {PH2}",layerRoot:"Layer Root",ownerElement:"Owner Element",imageUrl:"Image URL",stylesheetUrl:"Stylesheet URL",elementsAffected:"Elements Affected",nodesThatNeedLayout:"Nodes That Need Layout",sOfS:"{PH1} of {PH2}",layoutRoot:"Layout root",message:"Message",callbackFunction:"Callback Function",state:"State",range:"Range",allottedTime:"Allotted Time",invokedByTimeout:"Invoked by Timeout",type:"Type",size:"Size",details:"Details",cumulativeLayoutShifts:"Cumulative Layout Shifts",evolvedClsLink:"evolved",sCLSInformation:"{PH1} can result in poor user experiences. It has recently {PH2}.",warning:"Warning",score:"Score",cumulativeScore:"Cumulative Score",currentClusterScore:"Current Cluster Score",currentClusterId:"Current Cluster ID",hadRecentInput:"Had recent input",yes:"Yes",no:"No",movedFrom:"Moved from",movedTo:"Moved to",relatedNode:"Related Node",preview:"Preview",aggregatedTime:"Aggregated Time",networkRequest:"Network request",loadFromCache:"load from cache",networkTransfer:"network transfer",SSSResourceLoading:" ({PH1} {PH2} + {PH3} resource loading)",duration:"Duration",mimeType:"Mime Type",FromMemoryCache:" (from memory cache)",FromCache:" (from cache)",FromPush:" (from push)",FromServiceWorker:" (from `service worker`)",initiatorStackTrace:"Initiator Stack Trace",initiatedBy:"Initiated by",initiatorFor:"Initiator for",timerInstalled:"Timer Installed",animationFrameRequested:"Animation Frame Requested",idleCallbackRequested:"Idle Callback Requested",recalculationForced:"Recalculation Forced",firstLayoutInvalidation:"First Layout Invalidation",layoutForced:"Layout Forced",stackTrace:"Stack Trace",invalidations:"Invalidations",pendingFor:"Pending for",firstInvalidated:"First Invalidated",paintProfiler:"Paint Profiler",sAtS:"{PH1} at {PH2}",loading:"Loading",experience:"Experience",scripting:"Scripting",rendering:"Rendering",painting:"Painting",async:"Async",system:"System",idle:"Idle",sSelf:"{PH1} (self)",sChildren:"{PH1} (children)",timeSpentInRendering:"Time spent in rendering",frame:"Frame",sAtSParentheses:"{PH1} (at {PH2})",UnknownNode:"[ unknown node ]",invalidationWithCallFrame:"{PH1} at {PH2}",outsideBreadcrumbRange:"(outside of the breadcrumb range)"},ci=e.i18n.registerUIStrings("panels/timeline/TimelineUIUtils.ts",li),di=e.i18n.getLocalizedString.bind(void 0,ci);let hi,mi,pi,ui,gi;const Ti=new WeakMap;class vi{static initEventStyles(){if(hi)return hi;const e=d.TimelineModel.RecordType,t=vi.categories(),i=t.rendering,n=t.scripting,r=t.loading,a=t.experience,s=t.painting,o=t.other,l=t.idle,c={};return c[e.Task]=new $(di(li.task),o),c[e.Program]=new $(di(li.other),o),c[e.StartProfiling]=new $(li.profilingOverhead,o),c[e.Animation]=new $(di(li.animation),i),c[e.EventDispatch]=new $(di(li.event),n),c[e.RequestMainThreadFrame]=new $(di(li.requestMainThreadFrame),i,!0),c[e.BeginFrame]=new $(di(li.frameStart),i,!0),c[e.BeginMainThreadFrame]=new $(di(li.frameStartMainThread),i,!0),c[e.DrawFrame]=new $(di(li.drawFrame),i,!0),c[e.HitTest]=new $(di(li.hitTest),i),c[e.ScheduleStyleRecalculation]=new $(di(li.scheduleStyleRecalculation),i),c[e.RecalculateStyles]=new $(di(li.recalculateStyle),i),c[e.UpdateLayoutTree]=new $(di(li.recalculateStyle),i),c[e.InvalidateLayout]=new $(di(li.invalidateLayout),i,!0),c[e.Layerize]=new $(di(li.layerize),i),c[e.Layout]=new $(di(li.layout),i),c[e.PaintSetup]=new $(di(li.paintSetup),s),c[e.PaintImage]=new $(di(li.paintImage),s,!0),c[e.UpdateLayer]=new $(di(li.updateLayer),s,!0),c[e.UpdateLayerTree]=new $(di(li.updateLayerTree),i),c[e.Paint]=new $(di(li.paint),s),c[e.PrePaint]=new $(di(li.prePaint),i),c[e.RasterTask]=new $(di(li.rasterizePaint),s),c[e.ScrollLayer]=new $(di(li.scroll),i),c[e.Commit]=new $(di(li.commit),s),c[e.CompositeLayers]=new $(di(li.compositeLayers),s),c[e.ComputeIntersections]=new $(di(li.computeIntersections),i),c[e.ParseHTML]=new $(di(li.parseHtml),r),c[e.ParseAuthorStyleSheet]=new $(di(li.parseStylesheet),r),c[e.TimerInstall]=new $(di(li.installTimer),n),c[e.TimerRemove]=new $(di(li.removeTimer),n),c[e.TimerFire]=new $(di(li.timerFired),n),c[e.XHRReadyStateChange]=new $(di(li.xhrReadyStateChange),n),c[e.XHRLoad]=new $(di(li.xhrLoad),n),c[e.CompileScript]=new $(di(li.compileScript),n),c[e.CacheScript]=new $(di(li.cacheScript),n),c[e.CompileCode]=new $(di(li.compileCode),n),c[e.OptimizeCode]=new $(di(li.optimizeCode),n),c[e.EvaluateScript]=new $(di(li.evaluateScript),n),c[e.CompileModule]=new $(di(li.compileModule),n),c[e.CacheModule]=new $(di(li.cacheModule),n),c[e.EvaluateModule]=new $(di(li.evaluateModule),n),c[e.StreamingCompileScript]=new $(di(li.streamingCompileTask),o),c[e.StreamingCompileScriptWaiting]=new $(di(li.waitingForNetwork),l),c[e.StreamingCompileScriptParsing]=new $(di(li.parseAndCompile),n),c[e.BackgroundDeserialize]=new $(di(li.deserializeCodeCache),n),c[e.FinalizeDeserialization]=new $(li.profilingOverhead,o),c[e.WasmStreamFromResponseCallback]=new $(di(li.streamingWasmResponse),n),c[e.WasmCompiledModule]=new $(di(li.compiledWasmModule),n),c[e.WasmCachedModule]=new $(di(li.cachedWasmModule),n),c[e.WasmModuleCacheHit]=new $(di(li.wasmModuleCacheHit),n),c[e.WasmModuleCacheInvalid]=new $(di(li.wasmModuleCacheInvalid),n),c[e.FrameStartedLoading]=new $(di(li.frameStartedLoading),r,!0),c[e.MarkLoad]=new $(di(li.onloadEvent),n,!0),c[e.MarkDOMContent]=new $(di(li.domcontentloadedEvent),n,!0),c[e.MarkFirstPaint]=new $(di(li.firstPaint),s,!0),c[e.MarkFCP]=new $(di(li.firstContentfulPaint),i,!0),c[e.MarkLCPCandidate]=new $(di(li.largestContentfulPaint),i,!0),c[e.TimeStamp]=new $(di(li.timestamp),n),c[e.ConsoleTime]=new $(di(li.consoleTime),n),c[e.UserTiming]=new $(di(li.userTiming),n),c[e.ResourceWillSendRequest]=new $(di(li.willSendRequest),r),c[e.ResourceSendRequest]=new $(di(li.sendRequest),r),c[e.ResourceReceiveResponse]=new $(di(li.receiveResponse),r),c[e.ResourceFinish]=new $(di(li.finishLoading),r),c[e.ResourceReceivedData]=new $(di(li.receiveData),r),c[e.RunMicrotasks]=new $(di(li.runMicrotasks),n),c[e.FunctionCall]=new $(di(li.functionCall),n),c[e.GCEvent]=new $(di(li.gcEvent),n),c[e.MajorGC]=new $(di(li.majorGc),n),c[e.MinorGC]=new $(di(li.minorGc),n),c[e.JSRoot]=new $(di(li.jsRoot),l,!0),c[e.JSFrame]=new $(di(li.jsFrame),n),c[e.JSIdleFrame]=new $(di(li.jsIdleFrame),l,!0),c[e.JSSystemFrame]=new $(di(li.jsSystemFrame),o,!0),c[e.RequestAnimationFrame]=new $(di(li.requestAnimationFrame),n),c[e.CancelAnimationFrame]=new $(di(li.cancelAnimationFrame),n),c[e.FireAnimationFrame]=new $(di(li.animationFrameFired),n),c[e.RequestIdleCallback]=new $(di(li.requestIdleCallback),n),c[e.CancelIdleCallback]=new $(di(li.cancelIdleCallback),n),c[e.FireIdleCallback]=new $(di(li.fireIdleCallback),n),c[e.WebSocketCreate]=new $(di(li.createWebsocket),n),c[e.WebSocketSendHandshakeRequest]=new $(di(li.sendWebsocketHandshake),n),c[e.WebSocketReceiveHandshakeResponse]=new $(di(li.receiveWebsocketHandshake),n),c[e.WebSocketDestroy]=new $(di(li.destroyWebsocket),n),c[e.EmbedderCallback]=new $(di(li.embedderCallback),n),c[e.DecodeImage]=new $(di(li.imageDecode),s),c[e.ResizeImage]=new $(di(li.imageResize),s),c[e.GPUTask]=new $(di(li.gpu),t.gpu),c[e.GCCollectGarbage]=new $(di(li.domGc),n),c[e.CryptoDoEncrypt]=new $(di(li.encrypt),n),c[e.CryptoDoEncryptReply]=new $(di(li.encryptReply),n),c[e.CryptoDoDecrypt]=new $(di(li.decrypt),n),c[e.CryptoDoDecryptReply]=new $(di(li.decryptReply),n),c[e.CryptoDoDigest]=new $(di(li.digest),n),c[e.CryptoDoDigestReply]=new $(di(li.digestReply),n),c[e.CryptoDoSign]=new $(di(li.sign),n),c[e.CryptoDoSignReply]=new $(di(li.signReply),n),c[e.CryptoDoVerify]=new $(di(li.verify),n),c[e.CryptoDoVerifyReply]=new $(di(li.verifyReply),n),c[e.AsyncTask]=new $(di(li.asyncTask),t.async),c[e.LayoutShift]=new $(di(li.layoutShift),a),c[e.EventTiming]=new $(li.eventTiming,a),hi=c,c}static setEventStylesMap(e){hi=e}static frameDisplayName(e){if(!d.TimelineJSProfile.TimelineJSProfileProcessor.isNativeRuntimeFrame(e))return h.UIUtils.beautifyFunctionName(e.functionName);switch(d.TimelineJSProfile.TimelineJSProfileProcessor.nativeGroup(e.functionName)){case"Compile":return di(li.compile);case"Parse":return di(li.parse)}return e.functionName}static testContentMatching(e,t,n){const r=[vi.eventStyle(e).title];i.Legacy.eventIsFromNewEngine(e)&&i.Types.TraceEvents.isProfileCall(e)&&(n&&n.Samples?r.push(i.Handlers.ModelHandlers.Samples.getProfileCallFunctionName(n.Samples,e)):r.push(e.callFrame.functionName));const a=d.TimelineModel.EventOnTimelineData.forEvent(e).url;a&&r.push(a),function e(t,i){if(!i)return;for(const n in t){const a=t[n];"string"==typeof a?r.push(a):"number"==typeof a?r.push(String(a)):"object"==typeof a&&null!==a&&e(a,i-1)}}(e.args,2);const s=r.join("|").match(t);return!!s&&s.length>0}static eventStyle(e){const t=vi.initEventStyles();if(i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.Console)||i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.UserTiming))return new $(e.name,vi.categories().scripting);if(i.Legacy.eventIsFromNewEngine(e)){if(i.Types.TraceEvents.isProfileCall(e)&&"(idle)"===e.callFrame.functionName)return new $(e.name,Q().Idle);const t=new $(e.name,Q().Other);return Z(e.name)||t}let n=t[e.name];return n||(n=new $(e.name,vi.categories().other,!0),t[e.name]=n),n}static eventColor(e){if(i.Legacy.eventIsFromNewEngine(e)&&i.Types.TraceEvents.isProfileCall(e)){const t=e.callFrame;if(vi.isUserFrame(t))return vi.colorForId(t.url)}let t=vi.eventStyle(e).category.getComputedColorValue();if(e.name===d.TimelineModel.RecordType.StreamingCompileScriptWaiting&&(t=vi.categories().scripting.getComputedColorValue(),!t))throw new Error("Unable to parse color from TimelineUIUtils.categories().scripting.color");return t}static eventTitle(e){if(i.Legacy.eventIsFromNewEngine(e)&&i.Types.TraceEvents.isProfileCall(e)){return Ve.resolvedNodeNameForEntry(e)||vi.frameDisplayName(e.callFrame)}const t=d.TimelineModel.RecordType,n=e.args.data;if("EventTiming"===e.name){let t=null;if(e instanceof i.Legacy.PayloadEvent?t=e.rawPayload():i.Legacy.eventIsFromNewEngine(e)&&(t=e),t&&i.Types.TraceEvents.isSyntheticInteractionEvent(t))return de(t)}const r=vi.eventStyle(e).title;return i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.Console)?r:e.name===t.TimeStamp?di(li.sS,{PH1:r,PH2:n.message}):e.name===t.Animation&&n&&n.name?di(li.sS,{PH1:r,PH2:n.name}):e.name===t.EventDispatch&&n&&n.type?di(li.sS,{PH1:r,PH2:n.type}):r}static isUserFrame(e){return"0"!==e.scriptId&&!(e.url&&e.url.startsWith("native "))}static syntheticNetworkRequestCategory(e){switch(e.args.data.mimeType){case"text/html":return"HTML";case"application/javascript":case"application/x-javascript":case"text/javascript":return"Script";case"text/css":return"Style";case"audio/ogg":case"image/gif":case"image/jpeg":case"image/png":case"image/svg+xml":case"image/webp":case"image/x-icon":case"font/opentype":case"font/woff2":case"font/ttf":case"application/font-woff":return"Media";default:return"Other"}}static networkCategoryColor(e){let i="--app-color-system";switch(e){case"HTML":i="--app-color-loading";break;case"Script":i="--app-color-scripting";break;case"Style":i="--app-color-rendering";break;case"Media":i="--app-color-painting";break;default:i="--app-color-system"}return t.ThemeSupport.instance().getComputedValue(i)}static async buildDetailsTextForTraceEvent(e){const t=d.TimelineModel.RecordType;let n;const r=e.args.data;switch(e.name){case t.GCEvent:case t.MajorGC:case t.MinorGC:{const t=e.args.usedHeapSizeBefore-e.args.usedHeapSizeAfter;n=di(li.sCollected,{PH1:o.NumberUtilities.bytesToString(t)});break}case t.FunctionCall:r&&r.url&&void 0!==r.lineNumber&&void 0!==r.columnNumber&&(n=r.url+":"+(r.lineNumber+1)+":"+(r.columnNumber+1));break;case t.JSRoot:case t.JSFrame:case t.JSIdleFrame:case t.JSSystemFrame:n=vi.frameDisplayName(r);break;case t.EventDispatch:n=r?r.type:null;break;case t.Paint:{const e=vi.quadWidth(r.clip),t=vi.quadHeight(r.clip);e&&t&&(n=di(li.sSDimensions,{PH1:e,PH2:t}));break}case t.ParseHTML:{const t=e.args.beginData.startLine,i=e.args.endData&&e.args.endData.endLine,r=c.ResourceUtils.displayNameForURL(e.args.beginData.url);n=i>=0?di(li.sSs,{PH1:r,PH2:t+1,PH3:i+1}):di(li.sSSquareBrackets,{PH1:r,PH2:t+1});break}case t.CompileModule:case t.CacheModule:n=c.ResourceUtils.displayNameForURL(e.args.fileName);break;case t.CompileScript:case t.CacheScript:case t.EvaluateScript:{const e=r&&r.url;e&&(n=c.ResourceUtils.displayNameForURL(e)+":"+(r.lineNumber+1));break}case t.WasmCompiledModule:case t.WasmModuleCacheHit:{const t=e.args.url;t&&(n=c.ResourceUtils.displayNameForURL(t));break}case t.StreamingCompileScript:case t.BackgroundDeserialize:case t.XHRReadyStateChange:case t.XHRLoad:{const e=r.url;e&&(n=c.ResourceUtils.displayNameForURL(e));break}case t.TimeStamp:n=r.message;break;case t.WebSocketCreate:case t.WebSocketSendHandshakeRequest:case t.WebSocketReceiveHandshakeResponse:case t.WebSocketDestroy:case t.ResourceWillSendRequest:case t.ResourceSendRequest:case t.ResourceReceivedData:case t.ResourceReceiveResponse:case t.ResourceFinish:case t.PaintImage:case t.DecodeImage:case t.ResizeImage:case t.DecodeLazyPixelRef:{const t=d.TimelineModel.EventOnTimelineData.forEvent(e).url;t&&(n=c.ResourceUtils.displayNameForURL(t));break}case t.EmbedderCallback:n=r.callbackName;break;case t.Animation:n=r&&r.name;break;case t.AsyncTask:n=r?r.name:null;break;default:n=i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.Console)?null:await async function(){const t=d.TimelineModel.EventOnTimelineData.forEvent(e).topFrame();if(!t)return null;return t.url+":"+(t.lineNumber+1)+":"+(t.columnNumber+1)}()}return n}static async buildDetailsNodeForTraceEvent(e,t,n,r=!1){const a=d.TimelineModel.RecordType;let s,o=null;const l=e.args.data;switch(e.name){case a.GCEvent:case a.MajorGC:case a.MinorGC:case a.EventDispatch:case a.Paint:case a.Animation:case a.EmbedderCallback:case a.ParseHTML:case a.WasmStreamFromResponseCallback:case a.WasmCompiledModule:case a.WasmModuleCacheHit:case a.WasmCachedModule:case a.WasmModuleCacheInvalid:case a.WebSocketCreate:case a.WebSocketSendHandshakeRequest:case a.WebSocketReceiveHandshakeResponse:case a.WebSocketDestroy:s=await vi.buildDetailsTextForTraceEvent(e);break;case a.PaintImage:case a.DecodeImage:case a.ResizeImage:case a.DecodeLazyPixelRef:case a.XHRReadyStateChange:case a.XHRLoad:case a.ResourceWillSendRequest:case a.ResourceSendRequest:case a.ResourceReceivedData:case a.ResourceReceiveResponse:case a.ResourceFinish:{const t=d.TimelineModel.EventOnTimelineData.forEvent(e).url;if(t){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};o=p.Linkifier.Linkifier.linkifyURL(t,e)}break}case a.JSRoot:case a.FunctionCall:case a.JSIdleFrame:case a.JSSystemFrame:case a.JSFrame:{o=document.createElement("span"),h.UIUtils.createTextChild(o,vi.frameDisplayName(l));const e=this.linkifyLocation({scriptId:l.scriptId,url:l.url,lineNumber:l.lineNumber,columnNumber:l.columnNumber,target:t,isFreshRecording:r,linkifier:n});e&&(h.UIUtils.createTextChild(o," @ "),o.appendChild(e));break}case a.CompileModule:case a.CacheModule:o=this.linkifyLocation({scriptId:null,url:e.args.fileName,lineNumber:0,columnNumber:0,target:t,isFreshRecording:r,linkifier:n});break;case a.CompileScript:case a.CacheScript:case a.EvaluateScript:{const e=l.url;e&&(o=this.linkifyLocation({scriptId:null,url:e,lineNumber:l.lineNumber,columnNumber:0,target:t,isFreshRecording:r,linkifier:n}));break}case a.BackgroundDeserialize:case a.StreamingCompileScript:{const e=l.url;e&&(o=this.linkifyLocation({scriptId:null,url:e,lineNumber:0,columnNumber:0,target:t,isFreshRecording:r,linkifier:n}));break}case"ProfileCall":{if(o=document.createElement("span"),!i.Legacy.eventIsFromNewEngine(e)||!i.Types.TraceEvents.isProfileCall(e))break;const a=Ve.resolvedNodeNameForEntry(e)||vi.frameDisplayName(e.callFrame);h.UIUtils.createTextChild(o,a);const s=this.linkifyLocation({scriptId:e.callFrame.scriptId,url:e.callFrame.url,lineNumber:e.callFrame.lineNumber,columnNumber:e.callFrame.columnNumber,target:t,isFreshRecording:r,linkifier:n});s&&(h.UIUtils.createTextChild(o," @ "),o.appendChild(s));break}default:i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.Console)?s=null:o=i.Legacy.eventIsFromNewEngine(e)?this.linkifyTopCallFrame(e,t,n,r):null}return!o&&s&&(o=document.createTextNode(s)),o}static linkifyLocation(e){const{scriptId:t,url:i,lineNumber:n,columnNumber:r,isFreshRecording:a,linkifier:s,target:o}=e,l={lineNumber:n,columnNumber:r,showColumnNumber:!0,inlineFrameIndex:0,className:"timeline-details",tabStop:!0};return a?s.linkifyScriptLocation(o,t,i,n,l):p.Linkifier.Linkifier.linkifyURL(i,l)}static linkifyTopCallFrame(e,t,i,n=!1){const r=d.TimelineProfileTree.eventStackFrame(e);if(!r)return null;const a={className:"timeline-details",tabStop:!0,inlineFrameIndex:0,showColumnNumber:!0,columnNumber:r.columnNumber,lineNumber:r.lineNumber};return n?i.maybeLinkifyConsoleCallFrame(t,r,{showColumnNumber:!0,inlineFrameIndex:0}):p.Linkifier.Linkifier.linkifyURL(r.url,a)}static buildDetailsNodeForPerformanceEvent(e){let t="https://web.dev/user-centric-performance-metrics/",i="page performance metrics";const n=d.TimelineModel.RecordType;switch(e.name){case n.MarkLCPCandidate:t="https://web.dev/lcp/",i="largest contentful paint";break;case n.MarkFCP:t="https://web.dev/first-contentful-paint/",i="first contentful paint"}return h.Fragment.html`<div>${h.XLink.XLink.create(t,di(li.learnMore),void 0,void 0,"learn-more")} about ${i}.</div>`}static buildConsumeCacheDetails(e,t){if("number"==typeof e.consumedCacheSize){t.appendTextRow(di(li.compilationCacheStatus),di(li.scriptLoadedFromCache)),t.appendTextRow(di(li.compilationCacheSize),o.NumberUtilities.bytesToString(e.consumedCacheSize));const i=e.cacheKind;i&&t.appendTextRow(di(li.compilationCacheKind),i)}else"cacheRejected"in e&&e.cacheRejected?t.appendTextRow(di(li.compilationCacheStatus),di(li.failedToLoadScriptFromCache)):t.appendTextRow(di(li.compilationCacheStatus),di(li.scriptNotEligible))}static async buildTraceEventDetails(t,s,l,c,m=null){const u=s.targetByEvent(t),{duration:g,selfTime:T}=i.Legacy.timesForEventInMilliseconds(t);let v=null;if(u){const e=u;if(void 0===t[yi]){let n=null;const r=d.TimelineModel.EventOnTimelineData.forEvent(t).url;r?n=await p.ImagePreview.ImagePreview.build(e,r,!1,{imageAltText:p.ImagePreview.ImagePreview.defaultAltTextForImageURL(r),precomputedFeatures:void 0}):m&&i.Legacy.eventIsFromNewEngine(t)&&i.Types.TraceEvents.isTraceEventPaint(t)&&(n=await vi.buildPicturePreviewContent(m,t,e)),t[yi]=n}const r=new Set,a=d.TimelineModel.EventOnTimelineData.forEvent(t);if(a.backendNodeIds)for(let e=0;e<a.backendNodeIds.length;++e)r.add(a.backendNodeIds[e]);if(r.size){const t=e.model(n.DOMModel.DOMModel);t&&(v=await t.pushNodesByBackendIdsToFrontend(r))}m&&i.Legacy.eventIsFromNewEngine(t)&&i.Types.TraceEvents.isSyntheticLayoutShift(t)&&(v=await i.Extras.FetchNodes.extractRelatedDOMNodesFromEvent(m,t))}const f=d.TimelineModel.RecordType;let y;t.name===f.LayoutShift&&(c=!1);const w=new Si(s.targetByEvent(t),l),S=i.Legacy.eventIsFromNewEngine(t)?Z(t.name)?.category.getComputedColorValue():vi.eventStyle(t).category.getComputedColorValue(),b=s.isMarkerEvent(t)?vi.markerStyleForEvent(t).color:S;w.addSection(vi.eventTitle(t),b);const C=t.args.data,P=d.TimelineModel.EventOnTimelineData.forEvent(t),k=i.Legacy.eventIsFromNewEngine(t)?m?.Initiators.eventToInitiator.get(t)??null:null,E=i.Legacy.eventIsFromNewEngine(t)?m?.Initiators.initiatorToEvents.get(t)??null:null;let M=null;if(i.Legacy.eventIsFromNewEngine(t)&&m){const e=a.DetailsView.buildWarningElementsForEvent(t,m);for(const t of e)w.appendElementRow(di(li.warning),t,!0)}if(c&&!Number.isNaN(g||0)&&(w.appendTextRow(di(li.totalTime),e.TimeUtilities.millisToString(g||0,!0)),w.appendTextRow(di(li.selfTime),e.TimeUtilities.millisToString(T,!0))),m?.Meta.traceIsGeneric){for(const e in t.args)try{w.appendTextRow(e,JSON.stringify(t.args[e]))}catch(i){w.appendTextRow(e,`<${typeof t.args[e]}>`)}return w.fragment}if(i.Legacy.eventIsFromNewEngine(t)&&i.Types.TraceEvents.isTraceEventV8Compile(t)){if(M=t.args.data?.url,M){const e=t.args?.data?.lineNumber||0,i=t.args?.data?.columnNumber;w.appendLocationRow(di(li.script),M,e,i)}Boolean(t.args.data?.eager)&&w.appendTextRow(di(li.eagerCompile),!0);const e=Boolean(t.args.data?.streamed);w.appendTextRow(di(li.streamed),e+(e?"":`: ${t.args.data?.notStreamedReason||""}`)),vi.buildConsumeCacheDetails(C,w)}switch(t.name){case f.GCEvent:case f.MajorGC:case f.MinorGC:{const e=t.args.usedHeapSizeBefore-t.args.usedHeapSizeAfter;w.appendTextRow(di(li.collected),o.NumberUtilities.bytesToString(e));break}case f.JSRoot:case f.JSFrame:case"ProfileCall":case f.JSIdleFrame:case f.JSSystemFrame:case f.FunctionCall:{const e=await vi.buildDetailsNodeForTraceEvent(t,s.targetByEvent(t),l,s.isFreshRecording());e&&w.appendElementRow(di(li.function),e);break}case f.TimerFire:case f.TimerInstall:case f.TimerRemove:w.appendTextRow(di(li.timerId),C.timerId),t.name===f.TimerInstall&&(w.appendTextRow(di(li.timeout),e.TimeUtilities.millisToString(C.timeout)),w.appendTextRow(di(li.repeats),!C.singleShot));break;case f.FireAnimationFrame:w.appendTextRow(di(li.callbackId),C.id);break;case f.CompileModule:w.appendLocationRow(di(li.module),t.args.fileName,0);break;case f.CompileScript:break;case f.CacheModule:M=C&&C.url,w.appendTextRow(di(li.compilationCacheSize),o.NumberUtilities.bytesToString(C.producedCacheSize));break;case f.CacheScript:M=C&&C.url,M&&w.appendLocationRow(di(li.script),M,C.lineNumber,C.columnNumber),w.appendTextRow(di(li.compilationCacheSize),o.NumberUtilities.bytesToString(C.producedCacheSize));break;case f.EvaluateScript:M=C&&C.url,M&&w.appendLocationRow(di(li.script),M,C.lineNumber,C.columnNumber);break;case f.WasmStreamFromResponseCallback:case f.WasmCompiledModule:case f.WasmCachedModule:case f.WasmModuleCacheHit:case f.WasmModuleCacheInvalid:if(C){M=t.args.url,M&&w.appendTextRow(di(li.url),M);const e=t.args.producedCachedSize;e&&w.appendTextRow(di(li.producedCacheSize),e);const i=t.args.consumedCachedSize;i&&w.appendTextRow(di(li.consumedCacheSize),i)}break;case f.Paint:{const e=C.clip;w.appendTextRow(di(li.location),di(li.sSCurlyBrackets,{PH1:e[0],PH2:e[1]}));const t=vi.quadWidth(e),i=vi.quadHeight(e);w.appendTextRow(di(li.dimensions),di(li.sSDimensions,{PH1:t,PH2:i}))}case f.PaintSetup:case f.Rasterize:case f.ScrollLayer:y=di(li.layerRoot);break;case f.PaintImage:case f.DecodeLazyPixelRef:case f.DecodeImage:case f.ResizeImage:case f.DrawLazyPixelRef:if(y=di(li.ownerElement),M=P.url,M){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};w.appendElementRow(di(li.imageUrl),p.Linkifier.Linkifier.linkifyURL(M,e))}break;case f.ParseAuthorStyleSheet:if(M=C.styleSheetUrl,M){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};w.appendElementRow(di(li.stylesheetUrl),p.Linkifier.Linkifier.linkifyURL(M,e))}break;case f.UpdateLayoutTree:case f.RecalculateStyles:w.appendTextRow(di(li.elementsAffected),t.args.elementCount);break;case f.Layout:{const e=t.args.beginData;w.appendTextRow(di(li.nodesThatNeedLayout),di(li.sOfS,{PH1:e.dirtyObjects,PH2:e.totalObjects})),y=di(li.layoutRoot);break}case f.ConsoleTime:w.appendTextRow(di(li.message),t.name);break;case f.WebSocketCreate:case f.WebSocketSendHandshakeRequest:case f.WebSocketReceiveHandshakeResponse:case f.WebSocketDestroy:if(i.Legacy.eventIsFromNewEngine(t)&&i.Types.TraceEvents.isWebSocketTraceEvent(t)&&m){const e=a.DetailsView.buildRowsForWebSocketEvent(t,m);for(const{key:t,value:i}of e)w.appendTextRow(t,i)}break;case f.EmbedderCallback:w.appendTextRow(di(li.callbackFunction),C.callbackName);break;case f.Animation:"n"===i.Legacy.phaseForEvent(t)&&w.appendTextRow(di(li.state),C.state);break;case f.ParseHTML:{const e=t.args.beginData,i=e.startLine-1,n=t.args.endData?t.args.endData.endLine-1:void 0;M=e.url,M&&w.appendLocationRange(di(li.range),M,i,n);break}case f.FireIdleCallback:w.appendTextRow(di(li.allottedTime),e.TimeUtilities.millisToString(C.allottedMilliseconds)),w.appendTextRow(di(li.invokedByTimeout),C.timedOut);case f.RequestIdleCallback:case f.CancelIdleCallback:w.appendTextRow(di(li.callbackId),C.id);break;case f.EventDispatch:w.appendTextRow(di(li.type),C.type);break;case f.MarkLCPCandidate:w.appendTextRow(di(li.type),String(C.type)),w.appendTextRow(di(li.size),String(C.size));case f.MarkFirstPaint:case f.MarkFCP:case f.MarkLoad:case f.MarkDOMContent:if(m&&i.Legacy.eventIsFromNewEngine(t)){const i=Ci(t,m);w.appendTextRow(di(li.timestamp),e.TimeUtilities.preciseMillisToString(i,1)),w.appendElementRow(di(li.details),vi.buildDetailsNodeForPerformanceEvent(t))}break;case f.EventTiming:{const e=await vi.buildDetailsNodeForTraceEvent(t,s.targetByEvent(t),l,s.isFreshRecording());e&&w.appendElementRow(di(li.details),e);let n=null;if(i.Legacy.eventIsFromNewEngine(t)?n=t:i.Legacy.eventHasPayload(t)&&(n=t.rawPayload()),n&&i.Types.TraceEvents.isSyntheticInteractionEvent(n)){const e=i.Helpers.Timing.formatMicrosecondsTime(n.inputDelay),t=i.Helpers.Timing.formatMicrosecondsTime(n.mainThreadHandling),r=i.Helpers.Timing.formatMicrosecondsTime(n.presentationDelay);w.appendTextRow(di(li.interactionID),n.interactionId),w.appendTextRow(di(li.inputDelay),e),w.appendTextRow(di(li.processingTime),t),w.appendTextRow(di(li.presentationDelay),r)}break}case f.LayoutShift:{if(!i.Legacy.eventIsFromNewEngine(t)||!i.Types.TraceEvents.isSyntheticLayoutShift(t)){console.error("Unexpected type for LayoutShift event");break}const n=t,a=n.args.data,s=document.createElement("span"),o=h.XLink.XLink.create("https://web.dev/cls/",di(li.cumulativeLayoutShifts),void 0,void 0,"cumulative-layout-shifts"),l=h.XLink.XLink.create("https://web.dev/evolving-cls/",di(li.evolvedClsLink),void 0,void 0,"evolved-cls");if(s.appendChild(e.i18n.getFormatLocalizedString(ci,li.sCLSInformation,{PH1:o,PH2:l})),w.appendElementRow(di(li.warning),s,!0),!a)break;w.appendTextRow(di(li.score),a.score.toPrecision(4)),w.appendTextRow(di(li.cumulativeScore),a.cumulative_score.toPrecision(4)),w.appendTextRow(di(li.currentClusterId),n.parsedData.sessionWindowData.id),w.appendTextRow(di(li.currentClusterScore),n.parsedData.sessionWindowData.cumulativeWindowScore.toPrecision(4)),w.appendTextRow(di(li.hadRecentInput),C.had_recent_input?di(li.yes):di(li.no));for(const e of C.impacted_nodes){const t=new O(e.old_rect),i=new O(e.new_rect),n=await r.Linkifier.Linkifier.linkify(t),a=await r.Linkifier.Linkifier.linkify(i);w.appendElementRow(di(li.movedFrom),n),w.appendElementRow(di(li.movedTo),a)}break}default:{const e=await vi.buildDetailsNodeForTraceEvent(t,s.targetByEvent(t),l,s.isFreshRecording());e&&w.appendElementRow(di(li.details),e);break}}const F=v?.values()||[];for(const e of F)if(e){const t=await r.Linkifier.Linkifier.linkify(e);w.appendElementRow(y||di(li.relatedNode),t)}if(t[yi]&&(w.addSection(di(li.preview)),w.appendElementRow("",t[yi])),i.Legacy.eventIsFromNewEngine(t)&&m){const e=i.Helpers.Trace.stackTraceForEvent(t);(k||E||e||m?.Invalidations.invalidationsForEvent.get(t))&&await vi.generateCauses(t,w,m)}const I={};if(c&&m&&vi.aggregatedStatsForTraceEvent(I,m,t)){w.addSection(di(li.aggregatedTime));const e=vi.generatePieChart(I,vi.eventStyle(t).category,T);w.appendElementRow("",e)}return w.fragment}static statsForTimeRange(e,t,n){if(!e.length)return{idle:n-t};!function(e){if(e[bi])return;const t={},n=[];let r=0;function a(e,i){let n=t[e];if(n||(n={time:[],value:[]},t[e]=n),n.time.length&&n.time[n.time.length-1]===i||r>i)return;const a=n.value.length>0?n.value[n.value.length-1]:0;n.value.push(a+i-r),n.time.push(i)}function s(e,t,i){e&&a(e,i),r=i,t&&a(t,i)}d.TimelineModel.TimelineModelImpl.forEachEvent(e,(function(e){const{startTime:t}=i.Legacy.timesForEventInMilliseconds(e),r=Z(e.name)?.category.name||Q().Other.name,a=n.length?n[n.length-1]:null;r!==a&&s(a||null,r,t),n.push(r)}),(function(e){const{endTime:t}=i.Legacy.timesForEventInMilliseconds(e),r=n.pop(),a=n.length?n[n.length-1]:null;r!==a&&s(r||null,a||null,t||0)}));const o=e;o[bi]=t}(e);const r=function(e,t){const i=Object.assign({},e);for(const e in t)i[e]-=t[e];return i}(s(n),s(t)),a=Object.values(r).reduce(((e,t)=>e+t),0);return r.idle=Math.max(0,n-t-a),r;function s(t){const i={},n=e[bi];for(const e in n){const r=n[e],a=o.ArrayUtilities.upperBound(r.time,t,o.ArrayUtilities.DEFAULT_COMPARATOR);let s;if(0===a)s=0;else if(a===r.time.length)s=r.value[r.value.length-1];else{const e=r.time[a-1],i=r.time[a],n=r.value[a-1];s=n+(r.value[a]-n)*(t-e)/(i-e)}i[e]=s}return i}}static async buildSyntheticNetworkRequestDetails(t,n,r){const a=n.targetByEvent(t),l=new Si(a,r),c=vi.syntheticNetworkRequestCategory(t),h=vi.networkCategoryColor(c);l.addSection(di(li.networkRequest),h);l.appendElementRow(e.i18n.lockedString("URL"),p.Linkifier.Linkifier.linkifyURL(t.args.data.url,{tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0}));const m=t.dur;if(isFinite(m)){let e=i.Helpers.Timing.formatMicrosecondsTime(m);const n=t.args.data.syntheticData.finishTime-t.ts,r=t.ts+t.dur-t.args.data.syntheticData.finishTime;if(isFinite(n)&&isFinite(r)){const a=i.Helpers.Timing.formatMicrosecondsTime(n),s=i.Helpers.Timing.formatMicrosecondsTime(r),o=t.args.data.syntheticData.isMemoryCached||t.args.data.syntheticData.isDiskCached,l=di(o?li.loadFromCache:li.networkTransfer);e+=di(li.SSSResourceLoading,{PH1:a,PH2:l,PH3:s})}l.appendTextRow(di(li.duration),e)}if(t.args.data.requestMethod&&l.appendTextRow(di(li.requestMethod),t.args.data.requestMethod),t.args.data.initialPriority){const e=s.NetworkPriorities.uiLabelForNetworkPriority(t.args.data.initialPriority);l.appendTextRow(di(li.initialPriority),e)}const u=s.NetworkPriorities.uiLabelForNetworkPriority(t.args.data.priority);l.appendTextRow(di(li.priority),u),t.args.data.mimeType&&l.appendTextRow(di(li.mimeType),t.args.data.mimeType);let g="";t.args.data.syntheticData.isMemoryCached?g+=di(li.FromMemoryCache):t.args.data.syntheticData.isDiskCached?g+=di(li.FromCache):t.args.data.timing?.pushStart&&(g+=di(li.FromPush)),t.args.data.fromServiceWorker&&(g+=di(li.FromServiceWorker)),!t.args.data.encodedDataLength&&g||(g=`${o.NumberUtilities.bytesToString(t.args.data.encodedDataLength)}${g}`),l.appendTextRow(di(li.encodedData),g),t.args.data.decodedBodyLength&&l.appendTextRow(di(li.decodedBody),o.NumberUtilities.bytesToString(t.args.data.decodedBodyLength));const T=di(li.initiatedBy),v=d.TimelineModel.EventOnTimelineData.forEvent(t).topFrame();if(v){const e=r.maybeLinkifyConsoleCallFrame(a,v,{tabStop:!0,inlineFrameIndex:0,showColumnNumber:!0});e&&l.appendElementRow(T,e)}if(!Ti.get(t)&&t.args.data.url&&a){const e=await p.ImagePreview.ImagePreview.build(a,t.args.data.url,!1,{imageAltText:p.ImagePreview.ImagePreview.defaultAltTextForImageURL(t.args.data.url),precomputedFeatures:void 0});Ti.set(t,e)}const f=Ti.get(t);return f&&l.appendElementRow(di(li.preview),f),l.fragment}static stackTraceFromCallFrames(e){return{callFrames:e}}static async generateCauses(t,n,r){const{startTime:a}=i.Legacy.timesForEventInMilliseconds(t);let s=di(li.initiatorStackTrace),o=di(li.stackTrace);switch(t.name){case"TimerFire":s=di(li.timerInstalled);break;case"FireAnimationFrame":s=di(li.animationFrameRequested);break;case"FireIdleCallback":s=di(li.idleCallbackRequested);break;case"UpdateLayoutTree":case"RecalculateStyles":s=di(li.firstInvalidated),o=di(li.recalculationForced);break;case"Layout":s=di(li.firstLayoutInvalidation),o=di(li.layoutForced)}const l=i.Helpers.Trace.stackTraceForEvent(t);l&&l.length&&(n.addSection(o),n.createChildStackTraceElement(vi.stackTraceFromCallFrames(l)));const c=r.Initiators.eventToInitiator.get(t),d=r.Initiators.initiatorToEvents.get(t),h=r.Invalidations.invalidationsForEvent.get(t);if(c){const t=i.Helpers.Trace.stackTraceForEvent(c);t&&(n.addSection(s),n.createChildStackTraceElement(vi.stackTraceFromCallFrames(t.map((e=>({...e,scriptId:String(e.scriptId)}))))));const r=this.createEntryLink(c);n.appendElementRow(di(li.initiatedBy),r);const{startTime:o}=i.Legacy.timesForEventInMilliseconds(c),l=a-o;n.appendTextRow(di(li.pendingFor),e.TimeUtilities.preciseMillisToString(l,1))}if(d){const e=document.createElement("div");d.map(((t,i)=>{e.appendChild(this.createEntryLink(t)),i<d.length-1&&e.append(" ")})),n.appendElementRow(li.initiatorFor,e)}h&&h.length&&(n.addSection(di(li.invalidations)),await vi.generateInvalidationsList(h,n))}static createEntryLink(e){const t=document.createElement("span"),i=m.TraceBounds.BoundsManager.instance().state();if(!i)return t;const n=i.micro.minimapTraceBounds.min>e.ts+(e.dur||0)||i.micro.minimapTraceBounds.max<e.ts;return n||(t.classList.add("devtools-link"),h.ARIAUtils.markAsLink(t),t.tabIndex=0,t.addEventListener("click",(()=>{ni.instance().select(Wt.fromTraceEvent(e))})),t.addEventListener("keydown",(t=>{"Enter"===t.key&&(ni.instance().select(Wt.fromTraceEvent(e)),t.consume(!0))}))),t.textContent=this.eventTitle(e)+(n?" "+di(li.outsideBreadcrumbRange):""),t}static async generateInvalidationsList(e,t){const{groupedByReason:i,backendNodeIds:r}=a.DetailsView.generateInvalidationsList(e);let s=null;const o=n.TargetManager.TargetManager.instance().primaryPageTarget(),l=o?.model(n.DOMModel.DOMModel);l&&(s=await l.pushNodesByBackendIdsToFrontend(r)),Object.keys(i).forEach((e=>{vi.generateInvalidationsForReason(e,i[e],s,t)}))}static generateInvalidationsForReason(t,a,s,o){function l(e){const t=e.nodeId&&s?s.get(e.nodeId):null;if(t){const e=document.createElement("span");return r.Linkifier.Linkifier.linkify(t).then((t=>e.appendChild(t))),e}if(e.nodeName){const t=document.createElement("span");return t.textContent=e.nodeName,t}const i=document.createElement("span");return h.UIUtils.createTextChild(i,di(li.UnknownNode)),i}const c=new Set;for(const r of a){const a=i.Helpers.Trace.stackTraceForEvent(r);let s=null;const d=a?.at(0);d&&(s=o.linkifier()?.maybeLinkifyScriptLocation(n.TargetManager.TargetManager.instance().rootTarget(),d.scriptId,d.url,d.lineNumber)||null);const h=l(r),m=s?e.i18n.getFormatLocalizedString(ci,li.invalidationWithCallFrame,{PH1:h,PH2:s}):h,p="string"==typeof m?m:m.innerText;c.has(p)||(c.add(p),o.appendElementRow(t,m))}}static aggregatedStatsForTraceEvent(e,t,n){const r=t.Renderer?.allTraceEntries||[],{startTime:a,endTime:s}=i.Legacy.timesForEventInMilliseconds(n);const l=o.ArrayUtilities.binaryIndexOf(r,a,(function(e,t){const{startTime:n}=i.Legacy.timesForEventInMilliseconds(t);return e-n}));if(l<0)return!1;let c=!1;if(s)for(let t=l;t<r.length;t++){const a=r[t],{startTime:o,selfTime:d}=i.Legacy.timesForEventInMilliseconds(a);if(o>=s)break;if(!a.selfTime)continue;if(i.Legacy.threadIDForEvent(a)!==i.Legacy.threadIDForEvent(n))continue;t>l&&(c=!0);const h=vi.eventStyle(a).category.name;e[h]=(e[h]||0)+d}if(i.Types.TraceEvents.isAsyncPhase(i.Legacy.phaseForEvent(n))){if(s){let t=0;for(const i in e)t+=e[i];e.idle=Math.max(0,s-a-t)}return!1}return c}static async buildPicturePreviewContent(e,t,i){const r=e.LayerTree.paintsToSnapshots.get(t);if(!r)return null;const a=i.model(n.PaintProfiler.PaintProfilerModel);if(!a)return null;const s=await a.loadSnapshot(r.args.snapshot.skp64);if(!s)return null;const o={snapshot:s,rect:r.args.snapshot.params?.layer_rect};if(!o)return null;const l=o.snapshot.replay();o.snapshot.release();const c=await l;if(!c)return null;const d=document.createElement("div"),m=d.attachShadow({mode:"open"});m.adoptedStyleSheets=[Ne];const u=m.createChild("div");u.classList.add("image-preview-container","vbox","link");const g=u.createChild("img");g.src=c,g.alt=p.ImagePreview.ImagePreview.defaultAltTextForImageURL(c);return u.createChild("a").textContent=di(li.paintProfiler),h.ARIAUtils.markAsLink(u),u.tabIndex=0,u.addEventListener("click",(()=>ni.instance().select(Wt.fromTraceEvent(t))),!1),u.addEventListener("keydown",(e=>{"Enter"===e.key&&(ni.instance().select(Wt.fromTraceEvent(t)),e.consume(!0))})),d}static createEventDivider(t,n){const r=document.createElement("div");r.classList.add("resources-event-divider");const{startTime:a}=i.Legacy.timesForEventInMilliseconds(t),s=e.TimeUtilities.millisToString(a-n);h.Tooltip.Tooltip.install(r,di(li.sAtS,{PH1:vi.eventTitle(t),PH2:s}));const o=vi.markerStyleForEvent(t);return o.tall&&(r.style.backgroundColor=o.color),r}static visibleTypes(){const e=vi.initEventStyles(),t=[];for(const i in e)e[i].hidden||t.push(i);return t}static visibleEventsFilter(){return new d.TimelineModelFilter.TimelineVisibleEventsFilter(vi.visibleTypes())}static categories(){return mi||(mi={loading:new X("loading",di(li.loading),!0,"--app-color-loading-children","--app-color-loading"),experience:new X("experience",di(li.experience),!1,"--app-color-rendering-children","--app-color-rendering"),scripting:new X("scripting",di(li.scripting),!0,"--app-color-scripting-children","--app-color-scripting"),rendering:new X("rendering",di(li.rendering),!0,"--app-color-rendering-children","--app-color-rendering"),painting:new X("painting",di(li.painting),!0,"--app-color-painting-children","--app-color-painting"),gpu:new X("gpu",di(li.gpu),!1,"--app-color-painting-children","--app-color-painting"),async:new X("async",di(li.async),!1,"--app-color-async-children","--app-color-async"),other:new X("other",di(li.system),!1,"--app-color-system-children","--app-color-system"),idle:new X("idle",di(li.idle),!1,"--app-color-idle-children","--app-color-idle")},mi)}static setCategories(e){mi=e}static getTimelineMainEventCategories(){return pi||(pi=["idle","loading","painting","rendering","scripting","other"],pi)}static setTimelineMainEventCategories(e){pi=e}static generatePieChart(t,i,n){let r=0;for(const e in t)r+=t[e];const a=document.createElement("div");a.classList.add("timeline-details-view-pie-chart-wrapper"),a.classList.add("hbox");const o=new s.PieChart.PieChart,l=[];function c(e,t,i,n){i&&l.push({value:i,color:n,title:t})}if(i){n&&c(i.name,di(li.sSelf,{PH1:i.title}),n,i.getCSSValue());const e=t[i.name]-(n||0);e>0&&c(i.name,di(li.sChildren,{PH1:i.title}),e,i.getCSSValue())}for(const e in vi.categories()){const n=vi.categories()[e];e!==i?.name&&c(n.name,n.title,t[n.name],n.getCSSValue())}o.data={chartName:di(li.timeSpentInRendering),size:110,formatter:t=>e.TimeUtilities.preciseMillisToString(t),showLegend:!0,total:r,slices:l};return a.createChild("div","vbox").appendChild(o),a}static generateDetailsContentForFrame(e,t,i){const n=new Si(null,null);n.addSection(di(li.frame));const r=vi.frameDuration(e);if(n.appendElementRow(di(li.duration),r),t&&i){const e=document.createElement("div");e.classList.add("timeline-filmstrip-preview"),h.UIUtils.loadImage(i.screenshotEvent.args.dataUri).then((t=>t&&e.appendChild(t))),n.appendElementRow("",e),e.addEventListener("click",function(e,t){s.FilmStripView.Dialog.fromFilmStrip(e,t.index)}.bind(null,t,i),!1)}return n.fragment}static frameDuration(t){const n=i.Helpers.Timing.microSecondsToMilliseconds(t.startTimeOffset),r=i.Helpers.Timing.microSecondsToMilliseconds(i.Types.Timing.MicroSeconds(t.endTime-t.startTime)),a=di(li.sAtSParentheses,{PH1:e.TimeUtilities.millisToString(r,!0),PH2:e.TimeUtilities.millisToString(n,!0)});return e.i18n.getFormatLocalizedString(ci,li.emptyPlaceholder,{PH1:a})}static quadWidth(e){return Math.round(Math.sqrt(Math.pow(e[0]-e[2],2)+Math.pow(e[1]-e[3],2)))}static quadHeight(e){return Math.round(Math.sqrt(Math.pow(e[0]-e[6],2)+Math.pow(e[1]-e[7],2)))}static eventDispatchDesciptors(){if(ui)return ui;const e="hsl(40,100%,80%)",t="hsl(40,100%,50%)";return ui=[new wi(1,e,["mousemove","mouseenter","mouseleave","mouseout","mouseover"]),new wi(1,e,["pointerover","pointerout","pointerenter","pointerleave","pointermove"]),new wi(2,"hsl(90,100%,40%)",["wheel"]),new wi(3,t,["click","mousedown","mouseup"]),new wi(3,t,["touchstart","touchend","touchmove","touchcancel"]),new wi(3,t,["pointerdown","pointerup","pointercancel","gotpointercapture","lostpointercapture"]),new wi(3,"hsl(256,100%,75%)",["keydown","keyup","keypress"])],ui}static markerShortTitle(t){const i=d.TimelineModel.RecordType;switch(t.name){case i.MarkDOMContent:return e.i18n.lockedString("DCL");case i.MarkLoad:return e.i18n.lockedString("L");case i.MarkFirstPaint:return e.i18n.lockedString("FP");case i.MarkFCP:return e.i18n.lockedString("FCP");case i.MarkLCPCandidate:return e.i18n.lockedString("LCP")}return null}static markerStyleForEvent(e){const t=[6,4],n=vi.eventTitle(e),r=d.TimelineModel.RecordType;if(e.name!==r.NavigationStart&&(i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.Console)||i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.UserTiming)))return{title:n,dashStyle:t,lineWidth:.5,color:i.Legacy.eventHasCategory(e,d.TimelineModel.TimelineModelImpl.Category.UserTiming)?"purple":"orange",tall:!1,lowPriority:!1};let a=!1,s="grey";switch(e.name){case r.NavigationStart:s="#FF9800",a=!0;break;case r.FrameStartedLoading:s="green",a=!0;break;case r.MarkDOMContent:s="#0867CB",a=!0;break;case r.MarkLoad:s="#B31412",a=!0;break;case r.MarkFirstPaint:s="#228847",a=!0;break;case r.MarkFCP:s="#1A6937",a=!0;break;case r.MarkLCPCandidate:s="#1A3422",a=!0;break;case r.TimeStamp:s="orange"}return{title:n,dashStyle:t,lineWidth:.5,color:s,tall:a,lowPriority:!1}}static colorForId(e){return gi||(gi=new r.Color.Generator({min:30,max:330,count:void 0},{min:50,max:80,count:3},85),gi.setColorForID("","#f2ecdc")),gi.colorForID(e)}static displayNameForFrame(e,t=30){const i=e.url;return t||(t=30),r.ParsedURL.schemeIs(i,"about:")?`"${o.StringUtilities.trimMiddle(e.name,t)}"`:e.url.trimEnd(t)}}const fi=Symbol("aggregatedStats"),yi=Symbol("previewElement");class wi{priority;color;eventTypes;constructor(e,t,i){this.priority=e,this.color=t,this.eventTypes=i}}class Si{fragment;linkifierInternal;target;element;tableElement;constructor(e,t){this.fragment=document.createDocumentFragment(),this.linkifierInternal=t,this.target=e,this.element=document.createElement("div"),this.element.classList.add("timeline-details-view-block"),this.tableElement=this.element.createChild("div","vbox timeline-details-chip-body"),this.fragment.appendChild(this.element)}addSection(e,t){if(this.tableElement.hasChildNodes()?(this.element=document.createElement("div"),this.element.classList.add("timeline-details-view-block"),this.fragment.appendChild(this.element)):this.element.removeChildren(),e){const i=this.element.createChild("div","timeline-details-chip-title");t&&(i.createChild("div").style.backgroundColor=t),h.UIUtils.createTextChild(i,e)}this.tableElement=this.element.createChild("div","vbox timeline-details-chip-body"),this.fragment.appendChild(this.element)}linkifier(){return this.linkifierInternal}appendTextRow(e,t){const i=this.tableElement.createChild("div","timeline-details-view-row");i.createChild("div","timeline-details-view-row-title").textContent=e,i.createChild("div","timeline-details-view-row-value").textContent=t.toString()}appendElementRow(e,t,i,n){const r=this.tableElement.createChild("div","timeline-details-view-row");r.setAttribute("data-row-title",e),i&&r.classList.add("timeline-details-warning"),n&&r.classList.add("timeline-details-stack-values");r.createChild("div","timeline-details-view-row-title").textContent=e;const a=r.createChild("div","timeline-details-view-row-value");t instanceof Node?a.appendChild(t):h.UIUtils.createTextChild(a,t||"")}appendLocationRow(e,t,i,n){if(!this.linkifierInternal)return;const r={tabStop:!0,columnNumber:n,showColumnNumber:!0,inlineFrameIndex:0},a=this.linkifierInternal.maybeLinkifyScriptLocation(this.target,null,t,i,r);a&&this.appendElementRow(e,a)}appendLocationRange(e,t,i,n){if(!this.linkifierInternal||!this.target)return;const r=document.createElement("span"),a=this.linkifierInternal.maybeLinkifyScriptLocation(this.target,null,t,i,{tabStop:!0,inlineFrameIndex:0});a&&(r.appendChild(a),h.UIUtils.createTextChild(r,o.StringUtilities.sprintf(" [%s…%s]",i+1,(n||0)+1||"")),this.appendElementRow(e,r))}createChildStackTraceElement(e){if(!this.linkifierInternal)return;const t=this.tableElement.createChild("div","timeline-details-view-row timeline-details-stack-values"),i=p.JSPresentationUtils.buildStackTracePreviewContents(this.target,this.linkifierInternal,{stackTrace:e,tabStops:!0});t.appendChild(i.element)}}const bi=Symbol("categoryBreakdownCache");function Ci(e,t){if(!t){const{startTime:t}=i.Legacy.timesForEventInMilliseconds(e);return t}const n=i.Helpers.Timing.timeStampForEventAdjustedByClosestNavigation(e,t.Meta.traceBounds,t.Meta.navigationsByNavigationId,t.Meta.navigationsByFrameId);return i.Helpers.Timing.microSecondsToMilliseconds(n)}var Pi=Object.freeze({__proto__:null,TimelineUIUtils:vi,aggregatedStatsKey:fi,previewElementSymbol:yi,EventDispatchTypeDescriptor:wi,TimelineDetailsContentHelper:Si,categoryBreakdownCacheSymbol:bi,timeStampForEventAdjustedForClosestNavigationIfPossible:Ci});class ki extends d.TimelineModelFilter.TimelineModelFilter{minimumRecordDuration;constructor(){super(),this.minimumRecordDuration=0}setMinimumRecordDuration(e){this.minimumRecordDuration=e}accept(e){if(i.Legacy.eventIsFromNewEngine(e)){const{duration:t}=i.Helpers.Timing.eventTimingsMilliSeconds(e);return t>=this.minimumRecordDuration}return(e.endTime?e.endTime-e.startTime:0)>=this.minimumRecordDuration}}class Ei extends d.TimelineModelFilter.TimelineModelFilter{constructor(){super()}accept(e){return!vi.eventStyle(e).category.hidden}}class Mi extends d.TimelineModelFilter.TimelineModelFilter{regExpInternal;constructor(e){super(),this.setRegExp(e||null)}setRegExp(e){this.regExpInternal=e}regExp(){return this.regExpInternal}accept(e,t){return!this.regExpInternal||vi.testContentMatching(e,this.regExpInternal,t)}}var Fi=Object.freeze({__proto__:null,IsLong:ki,Category:Ei,TimelineRegExp:Mi});const Ii={performance:"Performance",filter:"Filter",selfTime:"Self Time",totalTime:"Total Time",activity:"Activity",selectItemForDetails:"Select item for details.",notOptimizedS:"Not optimized: {PH1}",fms:"{PH1} ms",percentPlaceholder:"{PH1} %",chromeExtensionsOverhead:"[`Chrome` extensions overhead]",vRuntime:"[`V8` Runtime]",unattributed:"[unattributed]",page:"Page",noGrouping:"No Grouping",groupByActivity:"Group by Activity",groupByCategory:"Group by Category",groupByDomain:"Group by Domain",groupByFrame:"Group by Frame",groupBySubdomain:"Group by Subdomain",groupByUrl:"Group by URL",groupBy:"Group by",filterCallTree:"Filter call tree",filterBottomup:"Filter bottom-up",heaviestStack:"Heaviest stack",showHeaviestStack:"Show Heaviest stack",hideHeaviestStack:"Hide Heaviest stack",heaviestStackShown:"Heaviest stack sidebar shown",heaviestStackHidden:"Heaviest stack sidebar hidden",timelineStack:"Timeline Stack",matchCase:"Match Case",useRegularExpression:"Use Regular Expression",matchWholeWord:"Match whole word"},Li=e.i18n.registerUIStrings("panels/timeline/TimelineTreeView.ts",Ii),xi=e.i18n.getLocalizedString.bind(void 0,Li);class Ri extends h.Widget.VBox{modelInternal;#Le;searchResults;linkifier;dataGrid;lastHoveredProfileNode;textFilterInternal;taskFilter;startTime;endTime;splitWidget;detailsView;searchableView;currentThreadSetting;lastSelectedNodeInternal;root;currentResult;textFilterUI;caseSensitiveButton;regexButton;matchWholeWord;#xe=null;constructor(){super(),this.modelInternal=null,this.#Le=null,this.element.classList.add("timeline-tree-view"),this.searchResults=[]}static eventNameForSorting(e){return(vi.eventTitle(e)||e.name)+":@"+d.TimelineProfileTree.eventURL(e)}setSearchableView(e){this.searchableView=e}setModelWithEvents(e,t,i=null){this.modelInternal=e,this.#xe=i,this.#Le=t}setModel(e,t){this.setModelWithEvents(e,t?.eventsForTreeView()||null)}getToolbarInputAccessiblePlaceHolder(){return""}model(){return this.modelInternal}traceParseData(){return this.#xe}init(){this.linkifier=new p.Linkifier.Linkifier,this.taskFilter=new d.TimelineModelFilter.ExclusiveNameFilter([d.TimelineModel.RecordType.Task]),this.textFilterInternal=new Mi,this.currentThreadSetting=r.Settings.Settings.instance().createSetting("timeline-tree-current-thread",0),this.currentThreadSetting.addChangeListener(this.refreshTree,this);const e=[];this.populateColumns(e),this.splitWidget=new h.SplitWidget.SplitWidget(!0,!0,"timeline-tree-view-details-split-widget");const t=new h.Widget.VBox,i=new h.Toolbar.Toolbar("",t.element);i.makeWrappable(!0),this.populateToolbar(i),this.dataGrid=new u.SortableDataGrid.SortableDataGrid({displayName:xi(Ii.performance),columns:e,refreshCallback:void 0,editCallback:void 0,deleteCallback:void 0}),this.dataGrid.addEventListener("SortingChanged",this.sortingChanged,this),this.dataGrid.element.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.dataGrid.setResizeMethod("last"),this.dataGrid.setRowContextMenuCallback(this.onContextMenu.bind(this)),this.dataGrid.asWidget().show(t.element),this.dataGrid.addEventListener("SelectedNode",this.updateDetailsForSelection,this),this.detailsView=new h.Widget.VBox,this.detailsView.element.classList.add("timeline-details-view","timeline-details-view-body"),this.splitWidget.setMainWidget(t),this.splitWidget.setSidebarWidget(this.detailsView),this.splitWidget.hideSidebar(),this.splitWidget.show(this.element),this.splitWidget.addEventListener("ShowModeChanged",this.onShowModeChanged,this),this.lastSelectedNodeInternal}lastSelectedNode(){return this.lastSelectedNodeInternal}updateContents(e){this.setRange(e.startTime,e.endTime)}setRange(e,t){this.startTime=e,this.endTime=t,this.refreshTree()}filters(){return[this.taskFilter,this.textFilterInternal,...Ce.instance().activeFilters()]}filtersWithoutTextFilter(){return[this.taskFilter,...Ce.instance().activeFilters()]}textFilter(){return this.textFilterInternal}exposePercentages(){return!1}populateToolbar(e){this.caseSensitiveButton=new h.Toolbar.ToolbarToggle(xi(Ii.matchCase)),this.caseSensitiveButton.setText("Aa"),this.caseSensitiveButton.addEventListener("Click",(()=>{this.#Re(this.caseSensitiveButton)}),this),e.appendToolbarItem(this.caseSensitiveButton),this.regexButton=new h.Toolbar.ToolbarToggle(xi(Ii.useRegularExpression)),this.regexButton.setText(".*"),this.regexButton.addEventListener("Click",(()=>{this.#Re(this.regexButton)}),this),e.appendToolbarItem(this.regexButton),this.matchWholeWord=new h.Toolbar.ToolbarToggle(xi(Ii.matchWholeWord),"match-whole-word"),this.matchWholeWord.addEventListener("Click",(()=>{this.#Re(this.matchWholeWord)}),this),e.appendToolbarItem(this.matchWholeWord);const t=new h.Toolbar.ToolbarInput(xi(Ii.filter),this.getToolbarInputAccessiblePlaceHolder());this.textFilterUI=t,t.addEventListener("TextChanged",this.#De,this),e.appendToolbarItem(t)}modelEvents(){return this.#Le||[]}onHover(e){}appendContextMenuItems(e,t){}selectProfileNode(e,t){const i=[];let n=e;for(;n;n=n.parent)i.push(n);for(let e=i.length-1;e>0;--e){const t=this.dataGridNodeForTreeNode(i[e]);t&&t.dataGrid&&t.expand()}const r=this.dataGridNodeForTreeNode(e);r&&r.dataGrid&&(r.reveal(),r.select(t))}refreshTree(){if(this.linkifier.reset(),this.dataGrid.rootNode().removeChildren(),!this.modelInternal)return void this.updateDetailsForSelection();this.root=this.buildTree();const e=this.root.children();let t=0,i=0;const n=this.root.totalTime-this.root.selfTime;for(const n of e.values())t=Math.max(t,n.selfTime),i=Math.max(i,n.totalTime);for(const r of e.values()){const e=new Ai(r,n,t,i,this);this.dataGrid.insertChild(e)}this.sortingChanged(),this.updateDetailsForSelection(),this.searchableView&&this.searchableView.refreshSearch();const r=this.dataGrid.rootNode();r.children.length>0&&r.children[0].select(!0)}buildTree(){throw new Error("Not Implemented")}buildTopDownTree(e,t){return new d.TimelineProfileTree.TopDownRootNode(this.modelEvents(),this.filters(),this.startTime,this.endTime,e,t)}populateColumns(e){e.push({id:"self",title:xi(Ii.selfTime),width:"120px",fixedWidth:!0,sortable:!0}),e.push({id:"total",title:xi(Ii.totalTime),width:"120px",fixedWidth:!0,sortable:!0}),e.push({id:"activity",title:xi(Ii.activity),disclosure:!0,sortable:!0})}sortingChanged(){const e=this.dataGrid.sortColumnId();if(!e)return;let t;switch(e){case"start-time":t=function(e,t){const i=t,n=e.profileNode.event,r=i.profileNode.event;return n.startTime-r.startTime};break;case"self":t=function(e,t){const i=t;return e.profileNode.selfTime-i.profileNode.selfTime};break;case"total":t=function(e,t){const i=t;return e.profileNode.totalTime-i.profileNode.totalTime};break;case"activity":t=function(e,t){const i=t,n=e.profileNode.event,r=i.profileNode.event,a=Ri.eventNameForSorting(n),s=Ri.eventNameForSorting(r);return a.localeCompare(s)};break;default:return void console.assert(!1,"Unknown sort field: "+e)}this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}#De(){const e=this.textFilterUI&&this.textFilterUI.value(),t=void 0!==this.caseSensitiveButton&&this.caseSensitiveButton.toggled(),i=void 0!==this.regexButton&&this.regexButton.toggled(),n=void 0!==this.matchWholeWord&&this.matchWholeWord.toggled();this.textFilterInternal.setRegExp(e?o.StringUtilities.createSearchRegex(e,t,i,n):null),this.refreshTree()}#Re(e){e&&e.setToggled(!e.toggled()),this.#De()}onShowModeChanged(){"OnlyMain"!==this.splitWidget.showMode()&&(this.lastSelectedNodeInternal=void 0,this.updateDetailsForSelection())}updateDetailsForSelection(){const e=this.dataGrid.selectedNode?this.dataGrid.selectedNode.profileNode:null;if(e===this.lastSelectedNodeInternal)return;if(this.lastSelectedNodeInternal=e,"OnlyMain"===this.splitWidget.showMode())return;if(this.detailsView.detachChildWidgets(),this.detailsView.element.removeChildren(),e&&this.showDetailsForNode(e))return;const t=this.detailsView.element.createChild("div","full-widget-dimmed-banner");h.UIUtils.createTextChild(t,xi(Ii.selectItemForDetails))}showDetailsForNode(e){return!1}onMouseMove(e){const t=e.target&&e.target instanceof Node?this.dataGrid.dataGridNodeFromNode(e.target):null,i=t&&t._profileNode;i!==this.lastHoveredProfileNode&&(this.lastHoveredProfileNode=i,this.onHover(i))}onContextMenu(e,t){const i=t;i.linkElement&&e.appendApplicableItems(i.linkElement);const n=i.profileNode;n&&this.appendContextMenuItems(e,n)}dataGridNodeForTreeNode(e){return Bi.get(e)||null}onSearchCanceled(){this.searchResults=[],this.currentResult=0}performSearch(e,t,i){if(this.searchResults=[],this.currentResult=0,!this.root)return;const n=e.toSearchRegex();this.searchResults=this.root.searchTree((e=>vi.testContentMatching(e,n.regex,this.#xe||void 0))),this.searchableView.updateSearchMatchesCount(this.searchResults.length)}jumpToNextSearchResult(){this.searchResults.length&&void 0!==this.currentResult&&(this.selectProfileNode(this.searchResults[this.currentResult],!1),this.currentResult=o.NumberUtilities.mod(this.currentResult+1,this.searchResults.length))}jumpToPreviousSearchResult(){this.searchResults.length&&void 0!==this.currentResult&&(this.selectProfileNode(this.searchResults[this.currentResult],!1),this.currentResult=o.NumberUtilities.mod(this.currentResult-1,this.searchResults.length))}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}}class Di extends u.SortableDataGrid.SortableDataGridNode{populated;profileNode;treeView;grandTotalTime;maxSelfTime;maxTotalTime;linkElement;constructor(e,t,i,n,r){super(null,!1),this.populated=!1,this.profileNode=e,this.treeView=r,this.grandTotalTime=t,this.maxSelfTime=i,this.maxTotalTime=n,this.linkElement=null}createCell(e){return"activity"===e?this.createNameCell(e):this.createValueCell(e)||super.createCell(e)}createNameCell(e){const t=this.createTD(e),n=t.createChild("div","name-container"),r=n.createChild("div","activity-icon-container"),a=r.createChild("div","activity-icon"),s=n.createChild("div","activity-name"),o=this.profileNode.event;if(this.profileNode.isGroupNode()){const e=this.treeView.displayInfoForGroupNode(this.profileNode);s.textContent=e.name,a.style.backgroundColor=e.color,e.icon&&r.insertBefore(e.icon,a)}else if(o){const e=o.args.data,t=e&&e.deoptReason;t&&(n.createChild("div","activity-warning").title=xi(Ii.notOptimizedS,{PH1:t})),s.textContent=vi.eventTitle(o);const r=this.treeView.modelInternal?.timelineModel().targetByEvent(o)||null,l=this.treeView.linkifier,c=Boolean(this.treeView.modelInternal?.timelineModel().isFreshRecording());this.linkElement=i.Legacy.eventIsFromNewEngine(o)?vi.linkifyTopCallFrame(o,r,l,c):null,this.linkElement&&n.createChild("div","activity-link").appendChild(this.linkElement);const d=vi.eventStyle(o).category;h.ARIAUtils.setLabel(a,d.title),a.style.backgroundColor=d.getComputedColorValue()}return t}createValueCell(e){if("self"!==e&&"total"!==e&&"start-time"!==e)return null;let t,n,r,a=!1;switch(e){case"start-time":{r=this.profileNode.event;const e=this.treeView.traceParseData();if(!e)throw new Error("Unable to load trace data for tree view");const n=r&&i.Legacy.timesForEventInMilliseconds(r);t=(n?.startTime??0)-i.Helpers.Timing.microSecondsToMilliseconds(e.Meta.traceBounds.min)}break;case"self":t=this.profileNode.selfTime,n=this.maxSelfTime,a=!0;break;case"total":t=this.profileNode.totalTime,n=this.maxTotalTime,a=!0;break;default:return null}const s=this.createTD(e);s.className="numeric-column",s.setAttribute("title",xi(Ii.fms,{PH1:t.toFixed(4)}));const o=s.createChild("div");return o.createChild("span").textContent=xi(Ii.fms,{PH1:t.toFixed(1)}),a&&this.treeView.exposePercentages()&&(o.createChild("span","percent-column").textContent=xi(Ii.percentPlaceholder,{PH1:(t/this.grandTotalTime*100).toFixed(1)})),n&&(o.classList.add("background-percent-bar"),s.createChild("div","background-bar-container").createChild("div","background-bar").style.width=(100*t/n).toFixed(1)+"%"),s}}class Ai extends Di{constructor(e,t,i,n,r){super(e,t,i,n,r),this.setHasChildren(this.profileNode.hasChildren()),Bi.set(e,this)}populate(){if(!this.populated&&(this.populated=!0,this.profileNode.children))for(const e of this.profileNode.children().values()){const t=new Ai(e,this.grandTotalTime,this.maxSelfTime,this.maxTotalTime,this.treeView);this.insertChildOrdered(t)}}}const Bi=new WeakMap;class Hi extends Ri{groupBySetting;stackView;executionContextNamesByOrigin=new Map;constructor(){super(),this.groupBySetting=r.Settings.Settings.instance().createSetting("timeline-tree-group-by",Hi.GroupBy.None),this.groupBySetting.addChangeListener(this.refreshTree.bind(this)),this.init(),this.stackView=new Wi(this),this.stackView.addEventListener("SelectionChanged",this.onStackViewSelectionChanged,this)}setGroupBySettingForTests(e){this.groupBySetting.set(e)}setModelWithEvents(e,t,i=null){super.setModelWithEvents(e,t,i)}setModel(e,t){super.setModel(e,t)}updateContents(e){this.updateExtensionResolver(),super.updateContents(e);const t=this.dataGrid.rootNode();t.children.length&&t.children[0].select(!0)}updateExtensionResolver(){this.executionContextNamesByOrigin=new Map;for(const e of n.TargetManager.TargetManager.instance().models(n.RuntimeModel.RuntimeModel))for(const t of e.executionContexts())this.executionContextNamesByOrigin.set(t.origin,t.name)}beautifyDomainName(e){return Hi.isExtensionInternalURL(e)?e=xi(Ii.chromeExtensionsOverhead):Hi.isV8NativeURL(e)?e=xi(Ii.vRuntime):e.startsWith("chrome-extension")&&(e=this.executionContextNamesByOrigin.get(e)||e),e}displayInfoForGroupNode(e){const t=vi.categories(),i=e.id&&e.event?vi.eventColor(e.event):t.other.color,n=xi(Ii.unattributed),r="symbol"==typeof e.id?void 0:e.id;switch(this.groupBySetting.get()){case Hi.GroupBy.Category:{const e=r?t[r]||t.other:{title:n,color:n};return{name:e.title,color:e.color,icon:void 0}}case Hi.GroupBy.Domain:case Hi.GroupBy.Subdomain:return{name:(r?this.beautifyDomainName(r):void 0)||n,color:i,icon:void 0};case Hi.GroupBy.EventName:if(!e.event)throw new Error("Unable to find event for group by operation");return{name:vi.eventTitle(e.event),color:i,icon:void 0};case Hi.GroupBy.URL:break;case Hi.GroupBy.Frame:{if(!this.modelInternal)throw new Error("Unable to find model for group by frame operation");const e=r?this.modelInternal.timelineModel().pageFrameById(r):void 0;return{name:e?vi.displayNameForFrame(e,80):xi(Ii.page),color:i,icon:void 0}}default:console.assert(!1,"Unexpected grouping type")}return{name:r||n,color:i,icon:void 0}}populateToolbar(e){super.populateToolbar(e);const t=Hi.GroupBy,i=[{label:xi(Ii.noGrouping),value:t.None},{label:xi(Ii.groupByActivity),value:t.EventName},{label:xi(Ii.groupByCategory),value:t.Category},{label:xi(Ii.groupByDomain),value:t.Domain},{label:xi(Ii.groupByFrame),value:t.Frame},{label:xi(Ii.groupBySubdomain),value:t.Subdomain},{label:xi(Ii.groupByUrl),value:t.URL}];e.appendToolbarItem(new h.Toolbar.ToolbarSettingComboBox(i,this.groupBySetting,xi(Ii.groupBy))),e.appendSpacer(),e.appendToolbarItem(this.splitWidget.createShowHideSidebarButton(xi(Ii.showHeaviestStack),xi(Ii.hideHeaviestStack),xi(Ii.heaviestStackShown),xi(Ii.heaviestStackHidden)))}buildHeaviestStack(e){console.assert(Boolean(e.parent),"Attempt to build stack for tree root");let t=[];for(let i=e;i&&i.parent;i=i.parent)t.push(i);t=t.reverse();for(let i=e;i&&i.children()&&i.children().size;){const e=Array.from(i.children().values());i=e.reduce(((e,t)=>e.totalTime>t.totalTime?e:t)),t.push(i)}return t}exposePercentages(){return!0}onStackViewSelectionChanged(){const e=this.stackView.selectedTreeNode();e&&this.selectProfileNode(e,!0)}showDetailsForNode(e){const t=this.buildHeaviestStack(e);return this.stackView.setStack(t,e),this.stackView.show(this.detailsView.element),!0}groupingFunction(e){const t=Hi.GroupBy;switch(e){case t.None:return null;case t.EventName:return e=>vi.eventStyle(e).title;case t.Category:return e=>vi.eventStyle(e).category.name;case t.Subdomain:return this.domainByEvent.bind(this,!1);case t.Domain:return this.domainByEvent.bind(this,!0);case t.URL:return e=>d.TimelineProfileTree.eventURL(e)||"";case t.Frame:return e=>d.TimelineModel.EventOnTimelineData.forEvent(e).frameId||"";default:return console.assert(!1,`Unexpected aggregation setting: ${e}`),null}}domainByEvent(e,t){const i=d.TimelineProfileTree.eventURL(t);if(!i)return"";if(Hi.isExtensionInternalURL(i))return Hi.extensionInternalPrefix;if(Hi.isV8NativeURL(i))return Hi.v8NativePrefix;const n=r.ParsedURL.ParsedURL.fromString(i);if(!n)return"";if("chrome-extension"===n.scheme)return n.scheme+"://"+n.host;if(!e)return n.host;if(/^[.0-9]+$/.test(n.host))return n.host;const a=/([^.]*\.)?[^.]*$/.exec(n.host);return a&&a[0]||""}appendContextMenuItems(e,t){if(this.groupBySetting.get()!==Hi.GroupBy.Frame)return;if(!t.isGroupNode())return;if(!this.modelInternal)return;const i=this.modelInternal.timelineModel().pageFrameById(t.id);i&&i.ownerNode&&e.appendApplicableItems(i.ownerNode)}static isExtensionInternalURL(e){return e.startsWith(Hi.extensionInternalPrefix)}static isV8NativeURL(e){return e.startsWith(Hi.v8NativePrefix)}static extensionInternalPrefix="extensions::";static v8NativePrefix="native "}!function(e){let t;!function(e){e.None="None",e.EventName="EventName",e.Category="Category",e.Domain="Domain",e.Subdomain="Subdomain",e.URL="URL",e.Frame="Frame"}(t=e.GroupBy||(e.GroupBy={}))}(Hi||(Hi={}));class Ni extends Hi{constructor(){super(),this.dataGrid.markColumnAsSortedBy("total",u.DataGrid.Order.Descending)}getToolbarInputAccessiblePlaceHolder(){return xi(Ii.filterCallTree)}buildTree(){const e=this.groupBySetting.get();return this.buildTopDownTree(!1,this.groupingFunction(e))}}class Ui extends Hi{constructor(){super(),this.dataGrid.markColumnAsSortedBy("self",u.DataGrid.Order.Descending)}getToolbarInputAccessiblePlaceHolder(){return xi(Ii.filterBottomup)}buildTree(){return new d.TimelineProfileTree.BottomUpRootNode(this.modelEvents(),this.textFilter(),this.filtersWithoutTextFilter(),this.startTime,this.endTime,this.groupingFunction(this.groupBySetting.get()))}}class Wi extends(r.ObjectWrapper.eventMixin(h.Widget.VBox)){treeView;dataGrid;constructor(e){super();this.element.createChild("div","timeline-stack-view-header").textContent=xi(Ii.heaviestStack),this.treeView=e;const t=[{id:"total",title:xi(Ii.totalTime),fixedWidth:!0,width:"110px"},{id:"activity",title:xi(Ii.activity)}];this.dataGrid=new u.ViewportDataGrid.ViewportDataGrid({displayName:xi(Ii.timelineStack),columns:t,deleteCallback:void 0,editCallback:void 0,refreshCallback:void 0}),this.dataGrid.setResizeMethod("last"),this.dataGrid.addEventListener("SelectedNode",this.onSelectionChanged,this),this.dataGrid.asWidget().show(this.element)}setStack(e,t){const i=this.dataGrid.rootNode();i.removeChildren();let n=null;const r=Math.max.apply(Math,e.map((e=>e.totalTime)));for(const a of e){const e=new Di(a,r,r,r,this.treeView);i.appendChild(e),a===t&&(n=e)}n&&n.revealAndSelect()}selectedTreeNode(){const e=this.dataGrid.selectedNode;return e&&e.profileNode}onSelectionChanged(){this.dispatchEventToListeners("SelectionChanged")}}var Vi=Object.freeze({__proto__:null,TimelineTreeView:Ri,GridNode:Di,TreeGridNode:Ai,get AggregatedTimelineTreeView(){return Hi},CallTreeTimelineTreeView:Ni,BottomUpTimelineTreeView:Ui,TimelineStackView:Wi});const Oi={filterEventLog:"Filter event log",startTime:"Start Time",durationFilter:"Duration filter",Dms:"{PH1} ms",all:"All"},zi=e.i18n.registerUIStrings("panels/timeline/EventsTimelineTreeView.ts",Oi),Gi=e.i18n.getLocalizedString.bind(void 0,zi);class _i extends Ri{filtersControl;delegate;currentTree;constructor(e){super(),this.filtersControl=new ji,this.filtersControl.addEventListener("FilterChanged",this.onFilterChanged,this),this.init(),this.delegate=e,this.dataGrid.markColumnAsSortedBy("start-time",u.DataGrid.Order.Ascending),this.splitWidget.showBoth()}filters(){return[...super.filters(),...this.filtersControl.filters()]}updateContents(e){super.updateContents(e),Wt.isTraceEventSelection(e.object)&&this.selectEvent(e.object,!0)}getToolbarInputAccessiblePlaceHolder(){return Gi(Oi.filterEventLog)}buildTree(){return this.currentTree=this.buildTopDownTree(!0,null),this.currentTree}onFilterChanged(){const e=this.lastSelectedNode(),t=e&&e.event;this.refreshTree(),t&&this.selectEvent(t,!1)}findNodeWithEvent(e){if("RunTask"===e.name)return null;const t=[this.currentTree.children().values()];for(;t.length;){const{done:i,value:n}=t[t.length-1].next();if(i)t.pop();else{if(n.event===e)return n;t.push(n.children().values())}}return null}selectEvent(e,t){const i=this.findNodeWithEvent(e);if(i&&(this.selectProfileNode(i,!1),t)){const e=this.dataGridNodeForTreeNode(i);e&&e.expand()}}populateColumns(e){e.push({id:"start-time",title:Gi(Oi.startTime),width:"80px",fixedWidth:!0,sortable:!0}),super.populateColumns(e),e.filter((e=>e.fixedWidth)).forEach((e=>{e.width="80px"}))}populateToolbar(e){super.populateToolbar(e),this.filtersControl.populateToolbar(e)}showDetailsForNode(e){const t=e.event;if(!t)return!1;const i=this.model();return!!i&&(vi.buildTraceEventDetails(t,i.timelineModel(),this.linkifier,!1,this.traceParseData()).then((e=>this.detailsView.element.appendChild(e))),!0)}onHover(e){this.delegate.highlightEvent(e&&e.event)}}class ji extends r.ObjectWrapper.ObjectWrapper{categoryFilter;durationFilter;filtersInternal;constructor(){super(),this.categoryFilter=new Ei,this.durationFilter=new ki,this.filtersInternal=[this.categoryFilter,this.durationFilter]}filters(){return this.filtersInternal}populateToolbar(e){const t=new h.Toolbar.ToolbarComboBox(function(){const e=t.selectedOption().value,i=parseInt(e,10);this.durationFilter.setMinimumRecordDuration(i),this.notifyFiltersChanged()}.bind(this),Gi(Oi.durationFilter));for(const e of ji.durationFilterPresetsMs)t.addOption(t.createOption(e?`≥ ${Gi(Oi.Dms,{PH1:e})}`:Gi(Oi.all),String(e)));e.appendToolbarItem(t);const i=new Map,n=vi.categories();for(const t in n){const a=n[t];if(!a.visible)continue;const s=new h.Toolbar.ToolbarCheckbox(a.title,void 0,r.bind(this,t));s.setChecked(!0),s.inputElement.style.backgroundColor=a.color,i.set(a.name,s),e.appendToolbarItem(s)}function r(e){const t=vi.categories(),n=i.get(e);t[e].hidden=!n||!n.checked(),this.notifyFiltersChanged()}}notifyFiltersChanged(){this.dispatchEventToListeners("FilterChanged")}static durationFilterPresetsMs=[0,1,15]}var qi=Object.freeze({__proto__:null,EventsTimelineTreeView:_i,Filters:ji});class Ji extends h.SplitWidget.SplitWidget{model;showPaintProfilerCallback;rightSplitWidget;layerViewHost;layers3DView;frameLayerTree;updateWhenVisible;constructor(e,t){super(!0,!1,"timeline-layers-view"),this.model=e,this.showPaintProfilerCallback=t,this.element.classList.add("timeline-layers-view"),this.rightSplitWidget=new h.SplitWidget.SplitWidget(!0,!0,"timeline-layers-view-details"),this.rightSplitWidget.element.classList.add("timeline-layers-view-properties"),this.setMainWidget(this.rightSplitWidget);const i=new h.Widget.VBox;this.setSidebarWidget(i),this.layerViewHost=new P.LayerViewHost.LayerViewHost;const n=new P.LayerTreeOutline.LayerTreeOutline(this.layerViewHost);i.element.appendChild(n.element),this.layers3DView=new P.Layers3DView.Layers3DView(this.layerViewHost),this.layers3DView.addEventListener("PaintProfilerRequested",this.onPaintProfilerRequested,this),this.rightSplitWidget.setMainWidget(this.layers3DView);const r=new P.LayerDetailsView.LayerDetailsView(this.layerViewHost);this.rightSplitWidget.setSidebarWidget(r),r.addEventListener("PaintProfilerRequested",this.onPaintProfilerRequested,this)}showLayerTree(e){this.frameLayerTree=e,this.isShowing()?this.update():this.updateWhenVisible=!0}wasShown(){this.updateWhenVisible&&(this.updateWhenVisible=!1,this.update())}async onPaintProfilerRequested(e){const t=e.data,i=await this.layers3DView.snapshotForSelection(t);i&&this.showPaintProfilerCallback(i.snapshot)}update(){this.frameLayerTree&&this.frameLayerTree.layerTreePromise().then((e=>this.layerViewHost.setLayerTree(e)))}}var $i=Object.freeze({__proto__:null,TimelineLayersView:Ji});const Xi=new CSSStyleSheet;Xi.replaceSync(".paint-profiler-image-view{overflow:hidden}.paint-profiler-image-view .paint-profiler-image-container{transform-origin:0 0}.paint-profiler-image-view .paint-profiler-image-container div{border-color:1px solid var(--sys-color-divider);border-style:solid;z-index:100;position:absolute;top:0;left:0}.paint-profiler-image-view img{border:solid 1px var(--sys-color-inverse-surface)}\n/*# sourceURL=timelinePaintProfiler.css */\n");class Ki extends h.SplitWidget.SplitWidget{logAndImageSplitWidget;imageView;paintProfilerView;logTreeView;needsUpdateWhenVisible;pendingSnapshot;event;paintProfilerModel;lastLoadedSnapshot;#Ae;constructor(e){super(!1,!1),this.element.classList.add("timeline-paint-profiler-view"),this.setSidebarSize(60),this.setResizable(!1),this.#Ae=e,this.logAndImageSplitWidget=new h.SplitWidget.SplitWidget(!0,!1),this.logAndImageSplitWidget.element.classList.add("timeline-paint-profiler-log-split"),this.setMainWidget(this.logAndImageSplitWidget),this.imageView=new Yi,this.logAndImageSplitWidget.setMainWidget(this.imageView),this.paintProfilerView=new P.PaintProfilerView.PaintProfilerView(this.imageView.showImage.bind(this.imageView)),this.paintProfilerView.addEventListener("WindowChanged",this.onWindowChanged,this),this.setSidebarWidget(this.paintProfilerView),this.logTreeView=new P.PaintProfilerView.PaintProfilerCommandLogView,this.logAndImageSplitWidget.setSidebarWidget(this.logTreeView),this.needsUpdateWhenVisible=!1,this.pendingSnapshot=null,this.event=null,this.paintProfilerModel=null,this.lastLoadedSnapshot=null}wasShown(){super.wasShown(),this.needsUpdateWhenVisible&&(this.needsUpdateWhenVisible=!1,this.update())}setSnapshot(e){this.releaseSnapshot(),this.pendingSnapshot=e,this.event=null,this.updateWhenVisible()}#Be(e){const t=e.args.tileData;if(!t)return!1;const i=this.#Ae.Frames.framesById[t.sourceFrameNumber];return!(!i||!i.layerTree)}setEvent(e,t){if(this.releaseSnapshot(),this.paintProfilerModel=e,this.pendingSnapshot=null,this.event=t,this.updateWhenVisible(),i.Types.TraceEvents.isTraceEventPaint(t)){const e=this.#Ae.LayerTree.paintsToSnapshots.get(t);return Boolean(e)}return!!i.Types.TraceEvents.isTraceEventRasterTask(t)&&this.#Be(t)}updateWhenVisible(){this.isShowing()?this.update():this.needsUpdateWhenVisible=!0}async#He(e){const t=e.args.tileData;if(!t)return null;if(!t.tileId.id_ref)return null;const i=n.TargetManager.TargetManager.instance().rootTarget();if(!i)return null;const r=this.#Ae.Frames.framesById[t.sourceFrameNumber];if(!r||!r.layerTree)return null;const a=new d.TracingLayerTree.TracingFrameLayerTree(i,r.layerTree),s=await a.layerTreePromise();return s?s.pictureForRasterTile(t.tileId.id_ref):null}update(){let e;if(this.logTreeView.setCommandLog([]),this.paintProfilerView.setSnapshotAndLog(null,[],null),this.pendingSnapshot)e=Promise.resolve({rect:null,snapshot:this.pendingSnapshot});else if(this.event&&this.paintProfilerModel&&i.Types.TraceEvents.isTraceEventPaint(this.event)){const t=this.#Ae.LayerTree.paintsToSnapshots.get(this.event);if(t){const i=t.args.snapshot.skp64;e=this.paintProfilerModel.loadSnapshot(i).then((e=>e&&{rect:null,snapshot:e}))}else e=Promise.resolve(null)}else{if(!this.event||!i.Types.TraceEvents.isTraceEventRasterTask(this.event))return void console.assert(!1,"Unexpected event type or no snapshot");e=this.#He(this.event)}function t(e,t,i){this.logTreeView.setCommandLog(i||[]),this.paintProfilerView.setSnapshotAndLog(e,i||[],t)}e.then((e=>{if(this.releaseSnapshot(),!e)return void this.imageView.showImage();const i=e.snapshot;this.lastLoadedSnapshot=i,this.imageView.setMask(e.rect),i.commandLog().then((n=>t.call(this,i,e.rect,n||[])))}))}releaseSnapshot(){this.lastLoadedSnapshot&&(this.lastLoadedSnapshot.release(),this.lastLoadedSnapshot=null)}onWindowChanged(){this.logTreeView.updateWindow(this.paintProfilerView.selectionWindow())}}class Yi extends h.Widget.Widget{imageContainer;imageElement;maskElement;transformController;maskRectangle;constructor(){super(!0),this.contentElement.classList.add("fill","paint-profiler-image-view"),this.imageContainer=this.contentElement.createChild("div","paint-profiler-image-container"),this.imageElement=this.imageContainer.createChild("img"),this.maskElement=this.imageContainer.createChild("div"),this.imageElement.addEventListener("load",this.updateImagePosition.bind(this),!1),this.transformController=new P.TransformController.TransformController(this.contentElement,!0),this.transformController.addEventListener("TransformChanged",this.updateImagePosition,this)}onResize(){this.imageElement.src&&this.updateImagePosition()}updateImagePosition(){const e=this.imageElement.naturalWidth,t=this.imageElement.naturalHeight,i=this.contentElement.clientWidth,n=this.contentElement.clientHeight,r=.1*i,a=.1*n,s=(i-r)/e,o=(n-a)/t,l=Math.min(s,o);if(this.maskRectangle){const i=this.maskElement.style;i.width=e+"px",i.height=t+"px",i.borderLeftWidth=this.maskRectangle.x+"px",i.borderTopWidth=this.maskRectangle.y+"px",i.borderRightWidth=e-this.maskRectangle.x-this.maskRectangle.width+"px",i.borderBottomWidth=t-this.maskRectangle.y-this.maskRectangle.height+"px"}this.transformController.setScaleConstraints(.5,10/l);let c=(new WebKitCSSMatrix).scale(this.transformController.scale(),this.transformController.scale()).translate(i/2,n/2).scale(l,l).translate(-e/2,-t/2);const d=h.Geometry.boundsForTransformedPoints(c,[0,0,0,e,t,0]);this.transformController.clampOffsets(r-d.maxX,i-r-d.minX,a-d.maxY,n-a-d.minY),c=(new WebKitCSSMatrix).translate(this.transformController.offsetX(),this.transformController.offsetY()).multiply(c),this.imageContainer.style.webkitTransform=c.toString()}showImage(e){this.imageContainer.classList.toggle("hidden",!e),e&&(this.imageElement.src=e)}setMask(e){this.maskRectangle=e,this.maskElement.classList.toggle("hidden",!e)}wasShown(){super.wasShown(),this.registerCSSFiles([Xi])}}var Zi=Object.freeze({__proto__:null,TimelinePaintProfilerView:Ki,TimelinePaintImageView:Yi});const Qi={summary:"Summary",bottomup:"Bottom-Up",callTree:"Call Tree",eventLog:"Event Log",paintProfiler:"Paint Profiler",layers:"Layers",rangeSS:"Range:  {PH1} – {PH2}"},en=e.i18n.registerUIStrings("panels/timeline/TimelineDetailsView.ts",Qi),tn=e.i18n.getLocalizedString.bind(void 0,en);class nn extends h.Widget.VBox{detailsLinkifier;tabbedPane;defaultDetailsWidget;defaultDetailsContentElement;rangeDetailViews;model;#Le;lazyPaintProfilerView;lazyLayersView;preferredTabId;selection;updateContentsScheduled;#Ae=null;#X=null;#I=this.#L.bind(this);constructor(e){super(),this.element.classList.add("timeline-details"),this.detailsLinkifier=new p.Linkifier.Linkifier,this.tabbedPane=new h.TabbedPane.TabbedPane,this.tabbedPane.show(this.element),this.defaultDetailsWidget=new h.Widget.VBox,this.defaultDetailsWidget.element.classList.add("timeline-details-view"),this.defaultDetailsContentElement=this.defaultDetailsWidget.element.createChild("div","timeline-details-view-body vbox"),this.appendTab(rn.Details,tn(Qi.summary),this.defaultDetailsWidget),this.setPreferredTab(rn.Details),this.rangeDetailViews=new Map,this.updateContentsScheduled=!1;const t=new Ui;this.appendTab(rn.BottomUp,tn(Qi.bottomup),t),this.rangeDetailViews.set(rn.BottomUp,t);const i=new Ni;this.appendTab(rn.CallTree,tn(Qi.callTree),i),this.rangeDetailViews.set(rn.CallTree,i);const n=new _i(e);this.appendTab(rn.EventLog,tn(Qi.eventLog),n),this.rangeDetailViews.set(rn.EventLog,n),this.tabbedPane.addEventListener(h.TabbedPane.Events.TabSelected,this.tabSelected,this),m.TraceBounds.onChange(this.#I)}getDetailsContentElementForTest(){return this.defaultDetailsContentElement}async#L(e){"MINIMAP_BOUNDS"===e.updateType&&this.selection&&await this.setSelection(this.selection),"RESET"!==e.updateType&&"VISIBLE_WINDOW"!==e.updateType||this.selection||this.scheduleUpdateContentsFromWindow()}async setModel(e,t,n){this.model!==e&&(this.model=e),this.#Ae=t,t&&(this.#X=i.Extras.FilmStrip.fromTraceData(t)),this.#Le=n,this.tabbedPane.closeTabs([rn.PaintProfiler,rn.LayerViewer],!1);for(const i of this.rangeDetailViews.values())i.setModelWithEvents(e,n,t);this.lazyPaintProfilerView=null,this.lazyLayersView=null,await this.setSelection(null)}setContent(e){const t=this.tabbedPane.otherTabs(rn.Details);for(let e=0;e<t.length;++e)this.rangeDetailViews.has(t[e])||this.tabbedPane.closeTab(t[e]);this.defaultDetailsContentElement.removeChildren(),this.defaultDetailsContentElement.appendChild(e)}updateContents(){const e=this.rangeDetailViews.get(this.tabbedPane.selectedTabId||"");if(e){const t=m.TraceBounds.BoundsManager.instance().state();if(!t)return;const i=t.milli.timelineTraceWindow;e.updateContents(this.selection||Wt.fromRange(i.min,i.max))}}appendTab(e,t,i,n){this.tabbedPane.appendTab(e,t,i,void 0,void 0,n),this.preferredTabId!==this.tabbedPane.selectedTabId&&this.tabbedPane.selectTab(e)}headerElement(){return this.tabbedPane.headerElement()}setPreferredTab(e){this.preferredTabId=e}scheduleUpdateContentsFromWindow(e=!1){this.model?e?this.updateContentsFromWindow():this.updateContentsScheduled||(this.updateContentsScheduled=!0,setTimeout((()=>{this.updateContentsScheduled=!1,this.updateContentsFromWindow()}),100)):this.setContent(h.Fragment.html`<div/>`)}updateContentsFromWindow(){const e=m.TraceBounds.BoundsManager.instance().state();if(!e)return;const t=e.milli.timelineTraceWindow;this.updateSelectedRangeStats(t.min,t.max),this.updateContents()}#Ne(e){if(!this.#X)return null;const t=e.idle?e.startTime:e.endTime,n=i.Extras.FilmStrip.frameClosestToTimestamp(this.#X,t);if(!n)return null;return i.Helpers.Timing.microSecondsToMilliseconds(n.screenshotEvent.ts)-e.endTime<10?n:null}async setSelection(e){if(this.detailsLinkifier.reset(),this.selection=e,!this.selection)return void this.scheduleUpdateContentsFromWindow(!0);const t=this.selection.object;if(Wt.isSyntheticNetworkRequestDetailsEventSelection(t)){const e=t,i=await vi.buildSyntheticNetworkRequestDetails(e,this.model.timelineModel(),this.detailsLinkifier);this.setContent(i)}else if(Wt.isTraceEventSelection(t)){const e=t,i=await vi.buildTraceEventDetails(e,this.model.timelineModel(),this.detailsLinkifier,!0,this.#Ae);this.appendDetailsTabsForTraceEventAndShowDetails(e,i)}else if(Wt.isFrameObject(t)){const e=t,i=this.#Ne(e);this.setContent(vi.generateDetailsContentForFrame(e,this.#X,i));const r=n.TargetManager.TargetManager.instance().rootTarget();if(e.layerTree&&r){const t=new d.TracingLayerTree.TracingFrameLayerTree(r,e.layerTree),i=this.layersView();i.showLayerTree(t),this.tabbedPane.hasTab(rn.LayerViewer)||this.appendTab(rn.LayerViewer,tn(Qi.layers),i)}}else Wt.isRangeSelection(t)&&this.updateSelectedRangeStats(this.selection.startTime,this.selection.endTime);this.updateContents()}tabSelected(e){e.data.isUserGesture&&(this.setPreferredTab(e.data.tabId),this.updateContents())}layersView(){return this.lazyLayersView||(this.lazyLayersView=new Ji(this.model.timelineModel(),this.showSnapshotInPaintProfiler.bind(this))),this.lazyLayersView}paintProfilerView(){return this.lazyPaintProfilerView?this.lazyPaintProfilerView:this.#Ae?(this.lazyPaintProfilerView=new Ki(this.#Ae),this.lazyPaintProfilerView):null}showSnapshotInPaintProfiler(e){const t=this.paintProfilerView();t&&(t.setSnapshot(e),this.tabbedPane.hasTab(rn.PaintProfiler)||this.appendTab(rn.PaintProfiler,tn(Qi.paintProfiler),t,!0),this.tabbedPane.selectTab(rn.PaintProfiler,!0))}appendDetailsTabsForTraceEventAndShowDetails(e,t){this.setContent(t),i.Legacy.eventIsFromNewEngine(e)&&(i.Types.TraceEvents.isTraceEventPaint(e)||i.Types.TraceEvents.isTraceEventRasterTask(e))&&this.showEventInPaintProfiler(e)}showEventInPaintProfiler(e){const t=n.TargetManager.TargetManager.instance().models(n.PaintProfiler.PaintProfilerModel)[0];if(!t)return;const i=this.paintProfilerView();if(!i)return;i.setEvent(t,e)&&(this.tabbedPane.hasTab(rn.PaintProfiler)||this.appendTab(rn.PaintProfiler,tn(Qi.paintProfiler),i))}updateSelectedRangeStats(t,i){if(!this.model||!this.#Le)return;const n=vi.statsForTimeRange(this.#Le,t,i),r=t-this.model.timelineModel().minimumRecordTime(),a=i-this.model.timelineModel().minimumRecordTime(),s=new Si(null,null);s.addSection(tn(Qi.rangeSS,{PH1:e.TimeUtilities.millisToString(r),PH2:e.TimeUtilities.millisToString(a)}));const o=vi.generatePieChart(n);s.appendElementRow("",o),this.setContent(s.fragment)}}var rn;!function(e){e.Details="details",e.EventLog="event-log",e.CallTree="call-tree",e.BottomUp="bottom-up",e.PaintProfiler="paint-profiler",e.LayerViewer="layer-viewer"}(rn||(rn={}));var an=Object.freeze({__proto__:null,TimelineDetailsView:nn,get Tab(){return rn}});const sn={network:"Network"},on=e.i18n.registerUIStrings("panels/timeline/NetworkTrackAppender.ts",sn),ln=e.i18n.getLocalizedString.bind(void 0,on);class cn{appenderName="Network";#t;#Ue;#We;#Ve;constructor(e,i){this.#t=e,this.#Ue=i,this.#We=`${s.Font.DEFAULT_FONT_SIZE} ${s.Font.getFontFamilyForCanvas()}`,t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{this.#Ve&&(this.#Ve.style.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),this.#Ve.style.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"))}))}group(){return this.#Ve}font(){return this.#We}appendTrackAtLevel(e,t){const i=this.#t.NetworkRequests.byTime;return 0===i.length?e:(this.#i(e,t),this.#Oe(i,e))}#i(e,t){const i=F({font:this.#Ue,shareHeaderLine:!1,useFirstLineForOverview:!1,useDecoratorsForOverview:!0});this.#Ve=I(0,ln(sn.network),i,!0,t),this.#Ue.groups.push(this.#Ve)}#Oe(e,t){const i=[];for(let n=0;n<e.length;++n){const r=e[n],a=x(r,i);this.#ze(r,t+a)}return t+i.length}#ze(e,t){const n=this.#Ue.entryLevels.length;this.#Ue.entryLevels[n]=t,this.#Ue.entryStartTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(e.ts);const r=e.dur||i.Helpers.Timing.millisecondsToMicroseconds(kn);return this.#Ue.entryTotalTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(r),t}filterTimelineDataBetweenTimes(e,t){const n=this.#t.NetworkRequests.byTime;if(!this.#Ue||0===n.length)return 0;const r=[];let a=0;for(let s=0;s<n.length;++s){const o=n[s],l=i.Helpers.Timing.microSecondsToMilliseconds(o.ts),c=i.Helpers.Timing.microSecondsToMilliseconds(o.ts+o.dur);if(!(l<t&&c>e)){this.#Ue.entryLevels[s]=-1;continue}const d=x(o,r);this.#Ue.entryLevels[s]=d,a=Math.max(a,r.length)}for(let e=0;e<n.length;++e)-1===this.#Ue.entryLevels[e]&&(this.#Ue.entryLevels[e]=a);return a}colorForEvent(e){if(!i.Types.TraceEvents.isSyntheticNetworkRequestDetailsEvent(e))throw new Error(`Unexpected Network Request: The event's type is '${e.name}'`);const t=vi.syntheticNetworkRequestCategory(e);return vi.networkCategoryColor(t)}titleForEvent(e){return e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:L(e.dur)}}}var dn=Object.freeze({__proto__:null,NetworkTrackAppender:cn});class hn{#Ge;#_e;#F;#je;#qe;#Je;#$e;#Xe;#Ae;constructor(){this.#Ge=0,this.#_e=0,this.#F=[],this.#je=0,this.#qe=null,this.#Ae=null}setModel(e){this.#Je=null,this.#Ae=e,this.#F=e?.NetworkRequests.byTime||[],this.#Ae&&this.#Ke(this.#Ae)}isEmpty(){return this.timelineData(),!this.#F.length}maxStackDepth(){return this.#je}timelineData(){return this.#Je&&0!==this.#Je.entryLevels.length?this.#Je:(this.#Je=s.FlameChart.FlameChartTimelineData.createEmpty(),this.#Ae?(this.#F=this.#Ae.NetworkRequests.byTime,this.#qe=new cn(this.#Ae,this.#Je),this.#je=this.#qe.appendTrackAtLevel(0),this.#Je):this.#Je)}minimumBoundary(){return this.#Ge}totalTime(){return this.#_e}setWindowTimes(e,t){this.#Ye(e,t)}createSelection(e){if(-1===e)return null;const t=this.#F[e];return this.#$e=new vn(Wt.fromTraceEvent(t),e),this.#$e.timelineSelection}entryIndexForSelection(e){if(!e)return-1;if(this.#$e&&this.#$e.timelineSelection.object===e.object)return this.#$e.entryIndex;if(!Wt.isSyntheticNetworkRequestDetailsEventSelection(e.object))return-1;const t=this.#F.indexOf(e.object);return-1!==t&&(this.#$e=new vn(Wt.fromTraceEvent(e.object),t)),t}entryColor(e){if(!this.#qe)throw new Error("networkTrackAppender should not be empty");return this.#qe.colorForEvent(this.#F[e])}textColor(e){return fn.textColor}entryTitle(e){const t=this.#F[e],i=new r.ParsedURL.ParsedURL(t.args.data.url);return i.isValid?`${i.displayName} (${i.host})`:t.args.data.url||null}entryFont(e){return this.#qe?.font()||null}getDecorationPixels(e,t,n){const r=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),a=e=>Math.floor(t+(e-r)*n),s=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),o=i.Helpers.Timing.microSecondsToMilliseconds(e.ts+e.dur),l=i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.sendStartTime),c=i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.downloadStart),d=Math.max(a(l),t),h=Math.max(a(c),d),m=Math.max(a(i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.finishTime)),h+2);return{sendStart:d,headersEnd:h,finish:m,start:a(s),end:Math.max(a(o),m)}}decorateEntry(e,i,n,r,a,s,o,l,c){const d=this.#F[e],{sendStart:m,headersEnd:p,finish:u,start:g,end:T}=this.getDecorationPixels(d,l,c);function v(e,t,n){i.moveTo(e,n-3),i.lineTo(e,n+3),i.moveTo(e,n),i.lineTo(t,n)}i.fillStyle="hsla(0, 100%, 100%, 0.8)",i.fillRect(m+.5,a+.5,p-m-.5,o-2),i.fillStyle=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),i.fillRect(r,a-.5,m-r,o),i.fillRect(u,a-.5,r+s-u,o),i.beginPath(),i.lineWidth=1,i.strokeStyle="#ccc";const f=Math.floor(a+o/2)+.5,y=T-.5;v(g+.5,m,f),v(y,u,f),i.stroke();const w=this.#Ze(d.args.data.priority);w&&(i.fillStyle=w,i.fillRect(m+.5,a+.5,3.5,3.5));const S=Math.max(m,0),b=u-S;if(b>=20){let t=this.entryTitle(e)||"";if(d.args.data.fromServiceWorker&&(t="⚙ "+t),t){const e=4,n=o-5,r=h.UIUtils.trimTextEnd(i,t,b-2*e);i.fillStyle="#333",i.fillText(r,S+e,a+n)}}return!0}forceDecoration(e){return!0}prepareHighlightedEntryInfo(t){const n=this.#F[t],r=document.createElement("div"),a=h.Utils.createShadowRootWithCoreStyles(r,{cssFile:[Fe],delegatesFocus:void 0}).createChild("div","timeline-flamechart-popover"),l=i.Helpers.Timing.microSecondsToMilliseconds(n.ts),c=i.Helpers.Timing.microSecondsToMilliseconds(n.dur);l&&isFinite(c)&&(a.createChild("span","timeline-info-network-time").textContent=e.TimeUtilities.millisToString(c,!0));const d=a.createChild("span");return d.textContent=s.NetworkPriorities.uiLabelForNetworkPriority(n.args.data.priority),d.style.color=this.#Ze(n.args.data.priority)||"black",a.createChild("span").textContent=o.StringUtilities.trimMiddle(n.args.data.url,80),r}#Ze(e){this.#Xe||(this.#Xe=new Map([["VeryLow",1],["Low",2],["Medium",3],["High",4],["VeryHigh",5]]));const t=this.#Xe.get(e);return t?`hsla(214, 80%, 50%, ${t/5})`:null}#Ke(e){const{traceBounds:t}=e.Meta,n=i.Helpers.Timing.microSecondsToMilliseconds(t.min),r=i.Helpers.Timing.microSecondsToMilliseconds(t.max);this.#Ge=n,this.#_e=n===r?1e3:r-this.#Ge}#Ye(e,t){this.#qe&&this.#Je&&(this.#je=this.#qe.filterTimelineDataBetweenTimes(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t)),this.#Je=s.FlameChart.FlameChartTimelineData.create({entryLevels:this.#Je?.entryLevels,entryTotalTimes:this.#Je?.entryTotalTimes,entryStartTimes:this.#Je?.entryStartTimes,groups:this.#Je?.groups}))}preferredHeight(){if(!this.#qe||0===this.#je)return 0;const e=this.#qe.group();return e?e.style.height*(this.isExpanded()?o.NumberUtilities.clamp(this.#je+1,4,8.5):1):0}isExpanded(){return Boolean(this.#qe?.group()?.expanded)}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t,i)}canJumpToEntry(e){return!1}mainFrameNavigationStartEvents(){return this.#Ae?this.#Ae.Meta.mainFrameNavigations:[]}}var mn=Object.freeze({__proto__:null,TimelineFlameChartNetworkDataProvider:hn});const pn={sAtS:"{PH1} at {PH2}"},un=e.i18n.registerUIStrings("panels/timeline/TimelineFlameChartView.ts",pn),gn=e.i18n.getLocalizedString.bind(void 0,un);class Tn extends h.Widget.VBox{delegate;model;searchResults;eventListeners;showMemoryGraphSetting;networkSplitWidget;mainDataProvider;mainFlameChart;networkFlameChartGroupExpansionSetting;networkDataProvider;networkFlameChart;networkPane;splitResizer;chartSplitWidget;brickGame;countersView;detailsSplitWidget;detailsView;onMainEntrySelected;onNetworkEntrySelected;boundRefresh;#Le;groupBySetting;searchableView;needsResizeToPreferredHeights;selectedSearchResult;searchRegex;#Ae;#Qe=null;#I=this.#L.bind(this);#et=0;#tt=setTimeout((()=>({})),0);constructor(e){super(),this.element.classList.add("timeline-flamechart"),this.delegate=e,this.model=null,this.eventListeners=[],this.#Ae=null,this.showMemoryGraphSetting=r.Settings.Settings.instance().createSetting("timeline-show-memory",!1),this.networkSplitWidget=new h.SplitWidget.SplitWidget(!1,!1,"timeline-flamechart-main-view",150),this.networkSplitWidget.sidebarElement().style.zIndex="120";const t=r.Settings.Settings.instance().createSetting("timeline-flamechart-main-view-group-expansion",{});this.mainDataProvider=new Pn,this.mainDataProvider.addEventListener("DataChanged",(()=>this.mainFlameChart.scheduleUpdate())),this.mainFlameChart=new s.FlameChart.FlameChart(this.mainDataProvider,this,t),this.mainFlameChart.alwaysShowVerticalScroll(),this.mainFlameChart.enableRuler(!1),this.networkFlameChartGroupExpansionSetting=r.Settings.Settings.instance().createSetting("timeline-flamechart-network-view-group-expansion",{}),this.networkDataProvider=new hn,this.networkFlameChart=new s.FlameChart.FlameChart(this.networkDataProvider,this,this.networkFlameChartGroupExpansionSetting),this.networkFlameChart.alwaysShowVerticalScroll(),this.networkPane=new h.Widget.VBox,this.networkPane.setMinimumSize(23,23),this.networkFlameChart.show(this.networkPane.element),this.splitResizer=this.networkPane.element.createChild("div","timeline-flamechart-resizer"),this.networkSplitWidget.hideDefaultResizer(!0),this.networkSplitWidget.installResizer(this.splitResizer),this.networkSplitWidget.setMainWidget(this.mainFlameChart),this.networkSplitWidget.setSidebarWidget(this.networkPane),this.chartSplitWidget=new h.SplitWidget.SplitWidget(!1,!0,"timeline-counters-split-view-state"),this.countersView=new Re(this.delegate),this.chartSplitWidget.setMainWidget(this.networkSplitWidget),this.chartSplitWidget.setSidebarWidget(this.countersView),this.chartSplitWidget.hideDefaultResizer(),this.chartSplitWidget.installResizer(this.countersView.resizerElement()),this.updateCountersGraphToggle(),this.detailsSplitWidget=new h.SplitWidget.SplitWidget(!1,!0,"timeline-panel-details-split-view-state"),this.detailsSplitWidget.element.classList.add("timeline-details-split"),this.detailsView=new nn(e),this.detailsSplitWidget.installResizer(this.detailsView.headerElement()),this.detailsSplitWidget.setMainWidget(this.chartSplitWidget),this.detailsSplitWidget.setSidebarWidget(this.detailsView),this.detailsSplitWidget.show(this.element),this.onMainEntrySelected=this.onEntrySelected.bind(this,this.mainDataProvider),this.onNetworkEntrySelected=this.onEntrySelected.bind(this,this.networkDataProvider),this.mainFlameChart.addEventListener("EntrySelected",this.onMainEntrySelected,this),this.mainFlameChart.addEventListener("EntryInvoked",this.onMainEntrySelected,this),this.networkFlameChart.addEventListener("EntrySelected",this.onNetworkEntrySelected,this),this.networkFlameChart.addEventListener("EntryInvoked",this.onNetworkEntrySelected,this),this.mainFlameChart.addEventListener("EntryHighlighted",this.onEntryHighlighted,this),this.element.addEventListener("keydown",this.#it.bind(this)),this.boundRefresh=this.#nt.bind(this),this.#Le=null,this.mainDataProvider.setEventColorMapping(vi.eventColor),this.groupBySetting=r.Settings.Settings.instance().createSetting("timeline-tree-group-by",Hi.GroupBy.None),this.groupBySetting.addChangeListener(this.updateColorMapper,this),this.updateColorMapper(),m.TraceBounds.onChange(this.#I)}#it(e){const t="fixme";e.key===t[this.#et]?(this.#et++,clearTimeout(this.#tt),this.#tt=setTimeout((()=>{this.#et=0}),2e3)):(this.#et=0,clearTimeout(this.#tt)),5===this.#et&&this.fixMe()}fixMe(){}#L(e){if("MINIMAP_BOUNDS"===e.updateType)return;const t=e.state.milli.timelineTraceWindow,i=Boolean(e.options.shouldAnimate);this.mainFlameChart.setWindowTimes(t.min,t.max,i),this.networkDataProvider.setWindowTimes(t.min,t.max),this.networkFlameChart.setWindowTimes(t.min,t.max,i),this.updateSearchResults(!1,!1)}isNetworkTrackShownForTests(){return"OnlyMain"!==this.networkSplitWidget.showMode()}getMainDataProvider(){return this.mainDataProvider}updateColorMapper(){this.model&&(this.mainDataProvider.setEventColorMapping(vi.eventColor),this.mainFlameChart.update())}windowChanged(e,t,n){m.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t)),{shouldAnimate:n})}updateRangeSelection(e,t){this.delegate.select(Wt.fromRange(e,t))}getMainFlameChart(){return this.mainFlameChart}updateSelectedGroup(e,t){e===this.mainFlameChart&&this.#Qe!==t?.name&&(this.#Qe=t?.name||null,this.#Le=t?this.mainDataProvider.groupTreeEvents(t):null,this.#rt())}setModel(e,t,i=!1){if(e===this.model)return;this.#Qe=null,this.#Ae=t,r.EventTarget.removeEventListeners(this.eventListeners),this.model=e,this.#Le=null,this.mainDataProvider.setModel(this.model,t,i),this.networkDataProvider.setModel(t),this.#nt();const n=m.TraceBounds.BoundsManager.instance().state();if(!n)throw new Error("TimelineFlameChartView could not set the window bounds.");const a=n.milli.timelineTraceWindow;this.mainFlameChart.setWindowTimes(a.min,a.max),this.networkDataProvider.setWindowTimes(a.min,a.max),this.networkFlameChart.setWindowTimes(a.min,a.max),this.updateSearchResults(!1,!1),this.updateColorMapper(),this.#at()}#nt(){this.networkDataProvider.isEmpty()?(this.mainFlameChart.enableRuler(!0),this.networkSplitWidget.hideSidebar()):(this.mainFlameChart.enableRuler(!1),this.networkSplitWidget.showBoth(),this.resizeToPreferredHeights()),this.mainFlameChart.reset(),this.networkFlameChart.reset(),this.updateSearchResults(!1,!1)}#rt(){this.countersView.setModel(this.#Ae,this.#Le),this.detailsView.setModel(this.model,this.#Ae,this.#Le)}#at(){this.mainFlameChart.scheduleUpdate(),this.networkFlameChart.scheduleUpdate()}onEntryHighlighted(e){n.OverlayModel.OverlayModel.hideDOMNodeHighlight();const t=e.data,r=this.mainDataProvider.eventByIndex(t);if(!r)return;const a=this.model&&this.model.timelineModel().targetByEvent(r);if(!a)return;let s;if(r instanceof i.Legacy.Event){s=d.TimelineModel.EventOnTimelineData.forEvent(r).backendNodeIds}else if(i.Types.TraceEvents.isTraceEventLayoutShift(r)){s=(r.args.data?.impacted_nodes??[]).map((e=>e.node_id))}if(s)for(let e=0;e<s.length;++e)new n.DOMModel.DeferredDOMNode(a,s[e]).highlight()}highlightEvent(e){const t=e?this.mainDataProvider.entryIndexForSelection(Wt.fromTraceEvent(e)):-1;t>=0?this.mainFlameChart.highlightEntry(t):this.mainFlameChart.hideHighlight()}willHide(){this.networkFlameChartGroupExpansionSetting.removeChangeListener(this.resizeToPreferredHeights,this),this.showMemoryGraphSetting.removeChangeListener(this.updateCountersGraphToggle,this),c.IgnoreListManager.IgnoreListManager.instance().removeChangeListener(this.boundRefresh)}wasShown(){this.networkFlameChartGroupExpansionSetting.addChangeListener(this.resizeToPreferredHeights,this),this.showMemoryGraphSetting.addChangeListener(this.updateCountersGraphToggle,this),c.IgnoreListManager.IgnoreListManager.instance().addChangeListener(this.boundRefresh),this.needsResizeToPreferredHeights&&this.resizeToPreferredHeights(),this.#at()}updateCountersGraphToggle(){this.showMemoryGraphSetting.get()?this.chartSplitWidget.showBoth():this.chartSplitWidget.hideSidebar()}setSelection(e){let t=this.mainDataProvider.entryIndexForSelection(e);this.mainFlameChart.setSelectedEntry(t),t=this.networkDataProvider.entryIndexForSelection(e),this.networkFlameChart.setSelectedEntry(t),this.detailsView&&this.detailsView.setSelection(e)}onEntrySelected(e,t){const i=t.data;e===this.mainDataProvider&&this.mainDataProvider.buildFlowForInitiator(i)&&this.mainFlameChart.scheduleUpdate(),this.delegate.select(e.createSelection(i))}resizeToPreferredHeights(){this.isShowing()?(this.needsResizeToPreferredHeights=!1,this.networkPane.element.classList.toggle("timeline-network-resizer-disabled",!this.networkDataProvider.isExpanded()),this.networkSplitWidget.setSidebarSize(this.networkDataProvider.preferredHeight()+this.splitResizer.clientHeight+s.FlameChart.RulerHeight+2)):this.needsResizeToPreferredHeights=!0}setSearchableView(e){this.searchableView=e}jumpToNextSearchResult(){if(!this.searchResults||!this.searchResults.length)return;const e=void 0!==this.selectedSearchResult?this.searchResults.indexOf(this.selectedSearchResult):-1;this.selectSearchResult(o.NumberUtilities.mod(e+1,this.searchResults.length))}jumpToPreviousSearchResult(){if(!this.searchResults||!this.searchResults.length)return;const e=void 0!==this.selectedSearchResult?this.searchResults.indexOf(this.selectedSearchResult):0;this.selectSearchResult(o.NumberUtilities.mod(e-1,this.searchResults.length))}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}selectSearchResult(e){this.searchableView.updateCurrentMatchIndex(e),this.searchResults&&(this.selectedSearchResult=this.searchResults[e],this.delegate.select(this.mainDataProvider.createSelection(this.selectedSearchResult)),this.mainFlameChart.showPopoverForSearchResult(this.selectedSearchResult))}updateSearchResults(e,t){const i=m.TraceBounds.BoundsManager.instance().state();if(!i)return;const n=this.selectedSearchResult;if(delete this.selectedSearchResult,this.searchResults=[],this.mainFlameChart.removeSearchResultHighlights(),!this.searchRegex||!this.model)return;const r=new Mi(this.searchRegex),a=i.milli.timelineTraceWindow;if(this.searchResults=this.mainDataProvider.search(a.min,a.max,r),this.searchableView.updateSearchMatchesCount(this.searchResults.length),this.searchResults.length<=200&&this.mainFlameChart.highlightAllEntries(this.searchResults),!e||!this.searchResults.length)return;let s=this.searchResults.indexOf(n);-1===s&&(s=t?this.searchResults.length-1:0),this.selectSearchResult(s)}getSearchResults(){return this.searchResults}onSearchCanceled(){void 0!==this.selectedSearchResult&&this.delegate.select(null),delete this.searchResults,delete this.selectedSearchResult,delete this.searchRegex,this.mainFlameChart.showPopoverForSearchResult(-1),this.mainFlameChart.removeSearchResultHighlights()}performSearch(e,t,i){this.searchRegex=e.toSearchRegex().regex,this.updateSearchResults(t,i)}}class vn{timelineSelection;entryIndex;constructor(e,t){this.timelineSelection=e,this.entryIndex=t}}const fn={textColor:"#333"};class yn{startTimeInternal;startOffset;style;constructor(e,t,i){this.startTimeInternal=e,this.startOffset=t,this.style=i}startTime(){return this.startTimeInternal}color(){return this.style.color}title(){if(this.style.lowPriority)return null;const t=e.TimeUtilities.millisToString(this.startOffset);return gn(pn.sAtS,{PH1:this.style.title,PH2:t})}draw(e,t,i,n){this.style.lowPriority&&n<4||(e.save(),this.style.tall&&(e.strokeStyle=this.style.color,e.lineWidth=this.style.lineWidth,e.translate(this.style.lineWidth<1||1&this.style.lineWidth?.5:0,.5),e.beginPath(),e.moveTo(t,0),e.setLineDash(this.style.dashStyle),e.lineTo(t,e.canvas.height),e.stroke()),e.restore())}}var wn=Object.freeze({__proto__:null,TimelineFlameChartView:Tn,Selection:vn,FlameChartStyle:fn,TimelineFlameChartMarker:yn});const Sn={onIgnoreList:"On ignore list",mainS:"Main — {PH1}",main:"Main",frameS:"Frame — {PH1}",subframe:"Subframe",raster:"Raster",rasterizerThreadS:"Rasterizer Thread {PH1}",thread:"Thread",frames:"Frames",sSelfS:"{PH1} (self {PH2})",idleFrame:"Idle Frame",droppedFrame:"Dropped Frame",partiallyPresentedFrame:"Partially Presented Frame",frame:"Frame"},bn=e.i18n.registerUIStrings("panels/timeline/TimelineFlameChartDataProvider.ts",Sn),Cn=e.i18n.getLocalizedString.bind(void 0,bn);class Pn extends r.ObjectWrapper.ObjectWrapper{droppedFramePatternCanvas;partialFramePatternCanvas;timelineDataInternal;currentLevel;legacyPerformanceModel;compatibilityTracksAppender;legacyTimelineModel;traceEngineData;isCpuProfile=!1;#st=0;minimumBoundaryInternal;timeSpan;headerLevel1;headerLevel2;staticHeader;framesHeader;screenshotsHeader;entryData;entryTypeByLevel;screenshotImageCache;entryIndexToTitle;asyncColorByCategory;lastInitiatorEntry;entryParent;lastSelection;colorForEvent;#ot=new WeakMap;#We;#lt=new WeakMap;constructor(){super(),this.reset(),this.#We=`${s.Font.DEFAULT_FONT_SIZE} ${s.Font.getFontFamilyForCanvas()}`,this.droppedFramePatternCanvas=document.createElement("canvas"),this.partialFramePatternCanvas=document.createElement("canvas"),this.preparePatternCanvas(),this.timelineDataInternal=null,this.currentLevel=0,this.legacyPerformanceModel=null,this.legacyTimelineModel=null,this.compatibilityTracksAppender=null,this.traceEngineData=null,this.minimumBoundaryInternal=0,this.timeSpan=0,this.headerLevel1=this.buildGroupStyle({shareHeaderLine:!1}),this.headerLevel2=this.buildGroupStyle({padding:2,nestingLevel:1,collapsible:!1}),this.staticHeader=this.buildGroupStyle({collapsible:!1}),this.framesHeader=this.buildGroupStyle({useFirstLineForOverview:!0}),this.screenshotsHeader=this.buildGroupStyle({useFirstLineForOverview:!0,nestingLevel:1,collapsible:!1,itemsHeight:150}),t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{const e=[this.headerLevel1,this.headerLevel2,this.staticHeader,this.framesHeader,this.screenshotsHeader];for(const i of e)i.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),i.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container")}))}modifyTree(e,t,i){const n=this.entryData[t];this.compatibilityTracksAppender?.modifyTree(e,n,i)}findPossibleContextMenuActions(e,t){const i=this.entryData[t];return this.compatibilityTracksAppender?.findPossibleContextMenuActions(e,i)}buildGroupStyle(e){const i={padding:4,height:17,collapsible:!0,color:t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),backgroundColor:t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),nestingLevel:0,shareHeaderLine:!0};return Object.assign(i,e)}setModel(e,t,n=!1){if(this.reset(),this.legacyPerformanceModel=e,this.legacyTimelineModel=e&&e.timelineModel(),this.traceEngineData=t,this.isCpuProfile=n,t){const{traceBounds:e}=t.Meta,n=i.Helpers.Timing.microSecondsToMilliseconds(e.min),r=i.Helpers.Timing.microSecondsToMilliseconds(e.max);this.minimumBoundaryInternal=n,this.timeSpan=n===r?1e3:r-this.minimumBoundaryInternal}}compatibilityTracksAppenderInstance(e=!1){if(!this.compatibilityTracksAppender||e){if(!this.traceEngineData||!this.legacyTimelineModel)throw new Error("Attempted to instantiate a CompatibilityTracksAppender without having set the trace parse data first.");this.timelineDataInternal=this.#ct(),this.compatibilityTracksAppender=new Dn(this.timelineDataInternal,this.traceEngineData,this.entryData,this.entryTypeByLevel,this.legacyTimelineModel)}return this.compatibilityTracksAppender}#ct(){return this.timelineDataInternal||(this.timelineDataInternal=s.FlameChart.FlameChartTimelineData.createEmpty()),this.timelineDataInternal}buildFromTrackAppenders(e){if(!this.compatibilityTracksAppender)return;const t=this.compatibilityTracksAppender.allVisibleTrackAppenders();for(const i of t){if(i instanceof we&&!i.trackName().includes(e?.filterThreadsByName||""))continue;const t=Boolean(e?.expandedTracks?.has(i.appenderName));this.currentLevel=i.appendTrackAtLevel(this.currentLevel,t)}}groupTrack(e){return e.track||null}groupTreeEvents(e){return this.compatibilityTracksAppender?.groupEventsForTreeView(e)??null}mainFrameNavigationStartEvents(){return this.traceEngineData?this.traceEngineData.Meta.mainFrameNavigations:[]}entryTitle(e){const t=this.entryType(e);if("Event"===t){const t=this.entryData[e];return"T"===t.phase||"p"===t.phase?t.name+":"+t.args.step:this.#ot.get(t)?Cn(Sn.onIgnoreList):vi.eventTitle(t)}if("Screenshot"===t)return"";if("TrackAppender"===t){const t=this.timelineDataInternal.entryLevels[e],i=this.entryData[e];return this.compatibilityTracksAppender?.titleForEvent(i,t)||null}let i=this.entryIndexToTitle[e];return i||(i=`Unexpected entryIndex ${e}`,console.error(i)),i}textColor(e){const t=this.entryData[e];return Pn.isEntryRegularEvent(t)&&this.isIgnoreListedEvent(t)?"#888":fn.textColor}entryFont(e){return this.#We}reset(e=!0){this.currentLevel=0,this.entryData=[],this.entryParent=[],this.entryTypeByLevel=[],this.entryIndexToTitle=[],this.asyncColorByCategory=new Map,this.screenshotImageCache=new Map,this.#lt=new Map,this.#ot=new WeakMap,e?(this.compatibilityTracksAppender=null,this.timelineDataInternal=null):!e&&this.timelineDataInternal&&(this.compatibilityTracksAppender?.setFlameChartDataAndEntryData(this.timelineDataInternal,this.entryData,this.entryTypeByLevel),this.compatibilityTracksAppender?.threadAppenders().forEach((e=>e.setHeaderAppended(!1))))}maxStackDepth(){return this.currentLevel}timelineData(e=!1){return this.timelineDataInternal&&0!==this.timelineDataInternal.entryLevels.length&&!e?this.timelineDataInternal:(this.timelineDataInternal=s.FlameChart.FlameChartTimelineData.createEmpty(),this.legacyTimelineModel?(e&&this.reset(!1),this.currentLevel=0,this.traceEngineData&&(this.compatibilityTracksAppender=this.compatibilityTracksAppenderInstance(),this.traceEngineData.Meta.traceIsGeneric?this.#dt():this.processInspectorTrace()),this.timelineDataInternal):this.timelineDataInternal)}#dt(){if(!this.compatibilityTracksAppender)return;const e=this.compatibilityTracksAppender.allThreadAppendersByProcess();for(const[t,i]of e){const e=this.buildGroupStyle({shareHeaderLine:!1}),n=this.traceEngineData?.Meta.processNames.get(t)?.args.name||"Process";this.appendHeader(`${n} (${t})`,e,!0,!1);for(const e of i)e.setHeaderNestingLevel(1),this.currentLevel=e.appendTrackAtLevel(this.currentLevel)}}processInspectorTrace(){this.isCpuProfile||this.appendFrames();const e=e=>{if(void 0!==e.appenderName)switch(e.appenderName){case"Animations":return 0;case"Timings":return 1;case"Interactions":return 2;case"LayoutShifts":return 3;case"GPU":return 8;case"Thread":return 4;case"Thread_AuctionWorklet":return 10;default:return-1}switch(e.type){case d.TimelineModel.TrackType.MainThread:return e.forMainFrame?5:6;case d.TimelineModel.TrackType.Worker:return 7;case d.TimelineModel.TrackType.Raster:return 9;case d.TimelineModel.TrackType.Other:return 11;default:return-1}};if(!this.legacyTimelineModel)return;const t=this.compatibilityTracksAppender?this.compatibilityTracksAppender.allVisibleTrackAppenders():[];t.sort(((t,i)=>e(t)-e(i)));for(const e of t)if(this.traceEngineData&&(this.currentLevel=e.appendTrackAtLevel(this.currentLevel),this.timelineDataInternal&&!this.timelineDataInternal.selectedGroup&&e instanceof we&&("MAIN_THREAD"===e.threadType||"CPU_PROFILE"===e.threadType))){const t=this.compatibilityTracksAppender?.groupForAppender(e);t&&(this.timelineDataInternal.selectedGroup=t)}this.timelineDataInternal&&this.timelineDataInternal.selectedGroup&&(this.timelineDataInternal.selectedGroup.expanded=!0)}#ht(e,t){if(!this.timelineDataInternal)return;const i=this.timelineDataInternal.entryDecorations[e]||[];i.push(t),this.timelineDataInternal.entryDecorations[e]=i}appendLegacyTrackData(e,t){this.#ct();const i="Event";switch(e.type){case d.TimelineModel.TrackType.MainThread:if(e.forMainFrame){const n=this.appendSyncEvents(e,e.events,e.url?Cn(Sn.mainS,{PH1:e.url}):Cn(Sn.main),this.headerLevel1,i,!0,t);n&&this.timelineDataInternal&&(this.timelineDataInternal.selectedGroup=n)}else this.appendSyncEvents(e,e.events,e.url?Cn(Sn.frameS,{PH1:e.url}):Cn(Sn.subframe),this.headerLevel1,i,!0,t);break;case d.TimelineModel.TrackType.Worker:this.appendSyncEvents(e,e.events,e.name,this.headerLevel1,i,!0,t);break;case d.TimelineModel.TrackType.Raster:this.#st||this.appendHeader(Cn(Sn.raster),this.headerLevel1,!1,t),++this.#st,this.appendSyncEvents(e,e.events,Cn(Sn.rasterizerThreadS,{PH1:this.#st}),this.headerLevel2,i,!0,t);break;case d.TimelineModel.TrackType.Other:this.appendSyncEvents(e,e.events,e.name||Cn(Sn.thread),this.headerLevel1,i,!0,t),this.appendAsyncEventsGroup(e,e.name,e.asyncEvents,this.headerLevel1,i,!0,t)}}minimumBoundary(){return this.minimumBoundaryInternal}totalTime(){return this.timeSpan}static isEntryRegularEvent(e){return"name"in e}search(e,t,n){const r=[];this.timelineData();for(let a=0;a<this.entryData.length;++a){const s=this.entryData[a];if(!Pn.isEntryRegularEvent(s))continue;if(!s)continue;const o=i.Legacy.eventIsFromNewEngine(s)?i.Helpers.Timing.eventTimingsMilliSeconds(s).startTime:s.startTime,l=i.Legacy.eventIsFromNewEngine(s)?i.Helpers.Timing.eventTimingsMilliSeconds(s).endTime:s.endTime;o>t||((l||o)<e||n.accept(s,this.traceEngineData||void 0)&&r.push(a))}return r.sort(((e,t)=>{let n=this.entryData[e],r=this.entryData[t];return Pn.isEntryRegularEvent(n)&&Pn.isEntryRegularEvent(r)?(n=n instanceof i.Legacy.Event?n:this.compatibilityTracksAppender?.getLegacyEvent(n)||null,r=r instanceof i.Legacy.Event?r:this.compatibilityTracksAppender?.getLegacyEvent(r)||null,n&&r?i.Legacy.Event.compareStartTime(n,r):0):0})),r}appendSyncEvents(e,t,n,r,a,s,o){if(!t.length)return null;if(!this.legacyPerformanceModel||!this.legacyTimelineModel)return null;const c=[],h=l.Runtime.experiments.isEnabled("ignore-list-js-frames-on-timeline");let m=0,p=null;e&&e.type===d.TimelineModel.TrackType.MainThread&&(p=this.appendHeader(n,r,s,o),p.track=e);for(let a=0;a<t.length;++a){const l=t[a],{duration:u}=i.Legacy.timesForEventInMilliseconds(l);if(this.legacyPerformanceModel){const t=this.legacyPerformanceModel.timelineModel().isInteractiveTimeEvent(l),i=this.legacyPerformanceModel.timelineModel().isLayoutShiftEvent(l),n=t||i;if(e&&e.type===d.TimelineModel.TrackType.MainThread&&n)continue}if(!i.Types.TraceEvents.isFlowPhase(l.phase)){if(!l.endTime&&"I"!==l.phase)continue;if(i.Types.TraceEvents.isAsyncPhase(l.phase))continue;if(!Ce.instance().isVisible(l))continue}for(;c.length&&c[c.length-1].endTime<=l.startTime;)c.pop();if(this.#ot.set(l,!1),h&&this.isIgnoreListedEvent(l)){const e=c[c.length-1];if(e&&this.#ot.get(e))continue;this.#ot.set(l,!0)}!p&&n&&(p=this.appendHeader(n,r,s,o),s&&(p.track=e));const g=this.currentLevel+c.length,T=this.appendEvent(l,g);c.length&&(this.entryParent[T]=c[c.length-1]);Boolean(e?.forMainFrame&&e?.type===d.TimelineModel.TrackType.MainThread)&&l.name===d.TimelineModel.RecordType.Task&&i.Helpers.Timing.millisecondsToMicroseconds(u)>i.Handlers.ModelHandlers.Warnings.LONG_MAIN_THREAD_TASK_THRESHOLD&&this.#ht(T,{type:"CANDY",startAtTime:i.Handlers.ModelHandlers.Warnings.LONG_MAIN_THREAD_TASK_THRESHOLD}),m=Math.max(m,c.length+1),l.endTime&&c.push(l)}return this.entryTypeByLevel.length=this.currentLevel+m,this.entryTypeByLevel.fill(a,this.currentLevel),this.currentLevel+=m,p}isIgnoreListedEvent(e){return!(!i.Legacy.eventIsFromNewEngine(e)||!i.Types.TraceEvents.isProfileCall(e))&&this.isIgnoreListedURL(e.callFrame.url)}isIgnoreListedURL(e){return c.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(e)}appendAsyncEventsGroup(e,t,i,n,r,a,s){if(!i.length)return null;const o=[];let l=null;for(let r=0;r<i.length;++r){const c=i[r];if(!this.legacyPerformanceModel||!Ce.instance().isVisible(c))continue;!l&&t&&(l=this.appendHeader(t,n,a,s),a&&(l.track=e));const d=c.startTime;let h;for(h=0;h<o.length&&o[h]>d;++h);this.appendAsyncEvent(c,this.currentLevel+h),o[h]=c.endTime}return this.entryTypeByLevel.length=this.currentLevel+o.length,this.entryTypeByLevel.fill(r,this.currentLevel),this.currentLevel+=o.length,l}getEntryTypeForLevel(e){return this.entryTypeByLevel[e]}appendFrames(){if(!(this.legacyPerformanceModel&&this.timelineDataInternal&&this.legacyTimelineModel&&this.traceEngineData))return;const e=i.Extras.FilmStrip.fromTraceData(this.traceEngineData),t=e.frames.length>0;this.framesHeader.collapsible=t;const n="frames"===l.Runtime.Runtime.queryParam("flamechart-force-expand");this.appendHeader(Cn(Sn.frames),this.framesHeader,!1,n),this.entryTypeByLevel[this.currentLevel]="Frame";for(const e of this.traceEngineData.Frames.frames)this.#mt(e);++this.currentLevel,t&&this.#pt(e)}#pt(e){if(!this.timelineDataInternal||!this.legacyTimelineModel)return;let t;this.appendHeader("",this.screenshotsHeader,!1),this.entryTypeByLevel[this.currentLevel]="Screenshot";for(const n of e.frames){const e=i.Helpers.Timing.microSecondsToMilliseconds(n.screenshotEvent.ts);this.entryData.push(n.screenshotEvent),this.timelineDataInternal.entryLevels.push(this.currentLevel),this.timelineDataInternal.entryStartTimes.push(e),t&&this.timelineDataInternal.entryTotalTimes.push(e-t),t=e}e.frames.length&&void 0!==t&&this.timelineDataInternal.entryTotalTimes.push(this.legacyTimelineModel.maximumRecordTime()-t),++this.currentLevel}entryType(e){return this.entryTypeByLevel[this.timelineDataInternal.entryLevels[e]]}prepareHighlightedEntryInfo(t){let n,r="",s=[],o="timeline-info-time";const l=[],c=this.entryType(t);if("TrackAppender"===c){if(!this.compatibilityTracksAppender)return null;const e=this.entryData[t],o=this.timelineDataInternal.entryLevels[t],c=this.compatibilityTracksAppender.highlightedEntryInfo(e,o);if(n=c.title,r=c.formattedTime,s=c.warningElements||s,i.Types.TraceEvents.isSyntheticInteractionEvent(e)){const t=new a.InteractionBreakdown.InteractionBreakdown;t.entry=e,l.push(t)}}else if("Event"===c){const i=this.entryData[t],a=i.duration,s=i.selfTime,o=1e-6;"number"==typeof a&&(r=Math.abs(a-s)>o&&s>o?Cn(Sn.sSelfS,{PH1:e.TimeUtilities.millisToString(a,!0),PH2:e.TimeUtilities.millisToString(s,!0)}):e.TimeUtilities.millisToString(a,!0)),n=this.entryTitle(t)}else{if("Frame"!==c)return null;{const a=this.entryData[t];r=e.TimeUtilities.preciseMillisToString(i.Helpers.Timing.microSecondsToMilliseconds(a.duration),1),a.idle?n=Cn(Sn.idleFrame):a.dropped?(n=a.isPartial?Cn(Sn.partiallyPresentedFrame):Cn(Sn.droppedFrame),o="timeline-info-warning"):n=Cn(Sn.frame)}}const d=document.createElement("div"),m=h.Utils.createShadowRootWithCoreStyles(d,{cssFile:[Fe],delegatesFocus:void 0}).createChild("div","timeline-flamechart-popover");if(m.createChild("span",o).textContent=r,m.createChild("span","timeline-info-title").textContent=n,s)for(const e of s)e.classList.add("timeline-info-warning"),m.appendChild(e);for(const e of l)m.appendChild(e);return d}prepareHighlightedHiddenEntriesArrowInfo(e,t){const i=document.createElement("div"),n=h.Utils.createShadowRootWithCoreStyles(i,{cssFile:[Fe],delegatesFocus:void 0}),r=this.entryData[t],a=this.compatibilityTracksAppender?.findHiddenDescendantsAmount(e,r);if(!a)return null;return n.createChild("div","timeline-flamechart-popover").createChild("span","timeline-info-title").textContent=a+" hidden",i}entryColor(e){if(!this.legacyPerformanceModel||!this.legacyTimelineModel)return"";const t=this.entryType(e);if("Event"===t){const t=this.entryData[e];if(this.legacyTimelineModel.isGenericTrace())return this.genericTraceEventColor(t);if(this.legacyPerformanceModel.timelineModel().isMarkerEvent(t))return vi.markerStyleForEvent(t).color;if(!i.Types.TraceEvents.isAsyncPhase(t.phase)&&this.colorForEvent)return this.colorForEvent(t);const n=vi.eventStyle(t).category;return function(e,t,i){let n=e.get(t);if(n)return n;const r=i(t);if(!r)throw new Error("Could not parse color from entry");return n=r,e.set(t,n),n}(this.asyncColorByCategory,n,(()=>n.getComputedColorValue()))}if("Frame"===t)return"white";if("TrackAppender"===t){const t=this.timelineDataInternal.entryLevels[e],i=this.entryData[e];return this.compatibilityTracksAppender?.colorForEvent(i,t)||""}return""}genericTraceEventColor(e){const t=e.categoriesString||e.name;return t?`hsl(${o.StringUtilities.hashCode(t)%300+30}, 40%, 70%)`:"#ccc"}preparePatternCanvas(){const e=17;this.droppedFramePatternCanvas.width=e,this.droppedFramePatternCanvas.height=e,this.partialFramePatternCanvas.width=e,this.partialFramePatternCanvas.height=e;const t=this.droppedFramePatternCanvas.getContext("2d");if(t){t.translate(8.5,8.5),t.rotate(.25*Math.PI),t.translate(-8.5,-8.5),t.fillStyle="rgb(255, 255, 255)";for(let e=-17;e<34;e+=3)t.fillRect(e,-17,1,51)}const i=this.partialFramePatternCanvas.getContext("2d");i&&(i.strokeStyle="rgb(255, 255, 255)",i.lineWidth=2,i.beginPath(),i.moveTo(17,0),i.lineTo(10,7),i.moveTo(8,9),i.lineTo(2,15),i.stroke())}drawFrame(t,n,r,a,s,o,l){const c=this.entryData[t];if(a+=1,o-=2,c.idle)n.fillStyle="white";else if(c.dropped)if(c.isPartial){n.fillStyle="#f0e442",n.fillRect(a,s,o,l);const e=n.createPattern(this.partialFramePatternCanvas,"repeat");n.fillStyle=e||n.fillStyle}else{n.fillStyle="#f08080",n.fillRect(a,s,o,l);const e=n.createPattern(this.droppedFramePatternCanvas,"repeat");n.fillStyle=e||n.fillStyle}else n.fillStyle="#d7f0d1";n.fillRect(a,s,o,l);const d=e.TimeUtilities.preciseMillisToString(i.Helpers.Timing.microSecondsToMilliseconds(c.duration),1),h=n.measureText(d).width;h<=o&&(n.fillStyle=this.textColor(t),n.fillText(d,a+(o-h)/2,s+l-4))}async drawScreenshot(e,t,i,n,r,a){const s=this.entryData[e];if(!this.screenshotImageCache.has(s)){this.screenshotImageCache.set(s,null);const e=s.args.dataUri,t=await h.UIUtils.loadImage(e);return this.screenshotImageCache.set(s,t),void this.dispatchEventToListeners("DataChanged")}const o=this.screenshotImageCache.get(s);if(!o)return;const l=i+1,c=n+1,d=a-2,m=d/o.naturalHeight,p=Math.floor(o.naturalWidth*m);t.save(),t.beginPath(),t.rect(i,n,r,a),t.clip(),t.drawImage(o,l,c,p,d),t.strokeStyle="#ccc",t.strokeRect(l-.5,c-.5,Math.min(r-1,p+1),d),t.restore()}decorateEntry(e,t,n,r,a,s,o,l,c){const d=this.entryType(e);if("Frame"===d)return this.drawFrame(e,t,n,r,a,s,o),!0;if("Screenshot"===d)return this.drawScreenshot(e,t,r,a,s,o),!0;if("TrackAppender"===d){const d=this.entryData[e];if(i.Types.TraceEvents.isSyntheticInteractionEvent(d))return this.#ut(t,e,n,d,r,a,l,s,o,c),!0}return!1}#ut(e,n,r,a,s,o,l,c,d,m){const p=i.Helpers.Timing.microSecondsToMilliseconds(a.ts),u=s+c;function g(e){const t=i.Helpers.Timing.microSecondsToMilliseconds(e);return Math.floor(l+(t-p)*m)}e.save(),e.fillStyle=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container");let T=g(a.processingStart);const v=g(a.processingEnd);function f(t,i,n){e.moveTo(t,n-3),e.lineTo(t,n+3),e.moveTo(t,n),e.lineTo(i,n)}a.processingEnd-a.processingStart==0&&(T-=1),e.fillRect(s,o-.5,T-s,d),e.fillRect(v,o-.5,u-v,d);const y=g(a.ts),w=g(i.Types.Timing.MicroSeconds(a.ts+a.dur));e.beginPath(),e.lineWidth=1,e.strokeStyle="#ccc";const S=Math.floor(o+d/2)+.5,b=w-.5;if(f(y+.5,T,S),f(b,v,S),e.stroke(),r){const t=T>0?T:s;e.font=this.#We;const i=5,a=5;h.UIUtils.measureTextWidth(e,r)<=v-t+i&&(e.fillStyle=this.textColor(n),e.fillText(r,t+i,o+d-a))}e.restore()}forceDecoration(e){const t=this.entryType(e);if("Frame"===t)return!0;if("Screenshot"===t)return!0;if("Event"===t)return!1;const n=this.entryData[e];return!!i.Types.TraceEvents.isSyntheticInteractionEvent(n)||Boolean(this.traceEngineData?.Warnings.perEvent.get(n))}appendHeader(e,t,i,n){const r={startLevel:this.currentLevel,name:e,style:t,selectable:i,expanded:n};return this.timelineDataInternal.groups.push(r),r}appendEvent(e,t){const i=this.entryData.length;this.entryData.push(e);const n=this.timelineDataInternal;return n.entryLevels[i]=t,n.entryTotalTimes[i]=e.duration||kn,n.entryStartTimes[i]=e.startTime,i}appendAsyncEvent(e,t){const i=e.steps,n=i.length>1&&"p"===i[1].phase?1:0;for(let e=0;e<i.length-1;++e){const r=this.entryData.length;this.entryData.push(i[e+n]);const a=i[e].startTime,s=this.timelineDataInternal;s.entryLevels[r]=t,s.entryTotalTimes[r]=i[e+1].startTime-a,s.entryStartTimes[r]=a}}#mt(t){const n=this.entryData.length;this.entryData.push(t);const r=i.Helpers.Timing.microSecondsToMilliseconds(t.duration);this.entryIndexToTitle[n]=e.TimeUtilities.millisToString(r,!0),this.timelineDataInternal&&(this.timelineDataInternal.entryLevels[n]=this.currentLevel,this.timelineDataInternal.entryTotalTimes[n]=r,this.timelineDataInternal.entryStartTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(t.startTime))}createSelection(e){const t=this.entryType(e);let i=null;const n=this.entryData[e];return n&&Pn.isEntryRegularEvent(n)?i=Wt.fromTraceEvent(n):"Frame"===t&&(i=Wt.fromFrame(this.entryData[e])),i&&(this.lastSelection=new vn(i,e)),i}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t,i)}canJumpToEntry(e){return!1}entryIndexForSelection(e){if(!e||Wt.isRangeSelection(e.object)||Wt.isSyntheticNetworkRequestDetailsEventSelection(e.object))return-1;if(this.lastSelection&&this.lastSelection.timelineSelection.object===e.object)return this.lastSelection.entryIndex;const t=this.entryData.indexOf(e.object);return-1!==t&&(this.lastSelection=new vn(e,t)),t}getIndexForEvent(e){const t=this.#lt.get(e);if(t)return t;const i=this.entryData.indexOf(e),n=i>-1?i:null;return this.#lt.set(e,n),n}buildFlowForInitiator(e){if(this.lastInitiatorEntry===e)return!1;if(!this.traceEngineData)return!1;if(!this.timelineDataInternal)return!1;if(!this.compatibilityTracksAppender)return!1;const t=this.timelineDataInternal.initiatorsData.length;if(-1===e)return this.lastInitiatorEntry=e,0!==t&&(this.timelineDataInternal.resetFlowData(),!0);if("TrackAppender"!==this.entryType(e))return!1;const n=this.entryData[e];if(!i.Legacy.eventIsFromNewEngine(n))return!1;this.timelineDataInternal.resetFlowData(),this.lastInitiatorEntry=e;let r=[],a=[];this.timelineDataInternal.selectedGroup&&(r=this.compatibilityTracksAppender?.getHiddenEvents(this.timelineDataInternal.selectedGroup)??[],a=this.compatibilityTracksAppender?.getModifiedEntries(this.timelineDataInternal.selectedGroup)??[]);const s=Pe(this.traceEngineData,n,r,a);if(0===t&&0===s.length)return!1;for(const e of s){const t=this.getIndexForEvent(e.event),i=this.getIndexForEvent(e.initiator);null!==t&&null!==i&&this.timelineDataInternal.initiatorsData.push({initiatorIndex:i,eventIndex:t,isInitiatorHidden:e.isInitiatorHidden,isEntryHidden:e.isEntryHidden})}return!0}eventByIndex(e){if(e<0)return null;const t=this.entryType(e);return"TrackAppender"===t||"Event"===t?this.entryData[e]:null}setEventColorMapping(e){this.colorForEvent=e}get performanceModel(){return this.legacyPerformanceModel}}const kn=.001;var En=Object.freeze({__proto__:null,TimelineFlameChartDataProvider:Pn,InstantEventVisibleDurationMs:kn});const Mn={timings:"Timings"},Fn=e.i18n.registerUIStrings("panels/timeline/TimingsTrackAppender.ts",Mn),In=e.i18n.getLocalizedString.bind(void 0,Fn);class Ln{appenderName="Timings";#n;#e;#t;constructor(e,t,i){this.#e=e,this.#n=i,this.#t=t}appendTrackAtLevel(e,t){const i=this.#t.PageLoadMetrics.allMarkerEvents,n=this.#t.UserTimings.performanceMarks,r=this.#t.UserTimings.performanceMeasures,a=this.#t.UserTimings.timestampEvents,s=this.#t.UserTimings.consoleTimings;if(0===i.length&&0===n.length&&0===r.length&&0===a.length&&0===s.length)return e;this.#i(e,t);let o=this.#gt(e);return o=this.#e.appendEventsAtLevel(n,o,this),o=this.#e.appendEventsAtLevel(r,o,this),o=this.#e.appendEventsAtLevel(a,o,this),this.#e.appendEventsAtLevel(s,o,this)}#i(e,t){const i=F({shareHeaderLine:!0,useFirstLineForOverview:!0,collapsible:this.#t.UserTimings.performanceMeasures.length>0}),n=I(e,In(Mn.timings),i,!0,t);this.#e.registerTrackForGroup(n,this)}#gt(e){const t=this.#t.PageLoadMetrics.allMarkerEvents;t.forEach((t=>{const i=this.#e.appendEventAtLevel(t,e,this);this.#e.getFlameChartTimelineData().entryTotalTimes[i]=Number.NaN}));const n=i.Helpers.Timing.microSecondsToMilliseconds(this.#t.Meta.traceBounds.min),r=t.map((e=>{const t=i.Helpers.Timing.microSecondsToMilliseconds(e.ts);return new yn(t,t-n,this.markerStyleForEvent(e))}));return this.#e.getFlameChartTimelineData().markers.push(...r),++e}markerStyleForEvent(e){let t="",n="grey";return i.Types.TraceEvents.isTraceEventMarkDOMContent(e)&&(n="#0867CB",t="DCL"),i.Types.TraceEvents.isTraceEventMarkLoad(e)&&(n="#B31412",t="L"),i.Types.TraceEvents.isTraceEventFirstPaint(e)&&(n="#228847",t="FP"),i.Types.TraceEvents.isTraceEventFirstContentfulPaint(e)&&(n="#1A6937",t="FCP"),i.Types.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)&&(n="#1A3422",t="LCP"),i.Types.TraceEvents.isTraceEventNavigationStart(e)&&(n="#FF9800",t=""),{title:t,dashStyle:[6,4],lineWidth:.5,color:n,tall:!0,lowPriority:!1}}colorForEvent(e){return i.Handlers.ModelHandlers.PageLoadMetrics.eventIsPageLoadEvent(e)?this.markerStyleForEvent(e).color:this.#n.colorForID(e.name)}titleForEvent(e){if(i.Handlers.ModelHandlers.PageLoadMetrics.eventIsPageLoadEvent(e))switch(e.name){case"MarkDOMContent":return"DCL";case"MarkLoad":return"L";case"firstContentfulPaint":return"FCP";case"firstPaint":return"FP";case"largestContentfulPaint::Candidate":return"LCP";case"navigationStart":return"";default:return e.name}return i.Types.TraceEvents.isTraceEventTimeStamp(e)?`${e.name}: ${e.args.data.message}`:i.Types.TraceEvents.isTraceEventPerformanceMark(e)?`[mark]: ${e.name}`:e.name}highlightedEntryInfo(e){const t=this.titleForEvent(e);if(i.Handlers.ModelHandlers.PageLoadMetrics.isTraceEventMarkerEvent(e)||i.Types.TraceEvents.isTraceEventPerformanceMark(e)||i.Types.TraceEvents.isTraceEventTimeStamp(e)){return{title:t,formattedTime:L(i.Helpers.Timing.timeStampForEventAdjustedByClosestNavigation(e,this.#t.Meta.traceBounds,this.#t.Meta.navigationsByNavigationId,this.#t.Meta.navigationsByFrameId))}}return{title:t,formattedTime:L(e.dur)}}}var xn=Object.freeze({__proto__:null,TimingsTrackAppender:Ln});const Rn=["Animations","Timings","Interactions","GPU","LayoutShifts","Thread","Thread_AuctionWorklet"];class Dn{#Tt=new Map;#vt=new Map;#ft=new Map;#yt=new Map;#Ue;#t;#wt;#n;#St=[];#bt=new Set([...Rn]);#Ct;#Pt;#kt;#Et;#Mt;#Ft;#It;#Lt=[];constructor(e,i,n,a,s){this.#Ue=e,this.#t=i,this.#wt=n,this.#n=new r.Color.Generator({min:30,max:55,count:void 0},{min:70,max:100,count:6},50,.7),this.#Pt=a,this.#Ct=s,this.#kt=new Ln(this,this.#t,this.#n),this.#St.push(this.#kt),this.#Mt=new ce(this,this.#t,this.#n),this.#St.push(this.#Mt),this.#Et=new N(this,this.#t),this.#St.push(this.#Et),this.#Ft=new re(this,this.#t),this.#St.push(this.#Ft),this.#It=new ge(this,this.#t),this.#St.push(this.#It),this.#xt(),t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{for(const e of this.#Ue.groups)e.style.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),e.style.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container")}))}setFlameChartDataAndEntryData(e,t,i){this.#vt.clear(),this.#Ue=e,this.#wt=t,this.#Pt=i}getFlameChartTimelineData(){return this.#Ue}getHiddenEvents(e){const t=this.#vt.get(e);if(t&&t.entriesFilter)return t.entriesFilter().invisibleEntries();console.warn("Could not get hidden events.")}getModifiedEntries(e){const t=this.#vt.get(e);if(t&&t.entriesFilter)return t.entriesFilter().modifiedEntries();console.warn("Could not get modified events.")}modifyTree(e,t,i){const n=this.#vt.get(e);n&&n.entriesFilter?n.entriesFilter().applyFilterAction({entry:t,type:i}):console.warn("Could not modify tree on a track.")}findPossibleContextMenuActions(e,t){const i=this.#vt.get(e);if(i&&i.entriesFilter)return i.entriesFilter().findPossibleActions(t);console.warn("Could not find possible context menu actions.")}findHiddenDescendantsAmount(e,t){const i=this.#vt.get(e);if(i&&i.entriesFilter)return i.entriesFilter().findHiddenDescendantsAmount(t);console.warn("Could not find hidden entries on a track.")}#xt(){const e=e=>{switch(e.threadType){case"MAIN_THREAD":if(!e.getUrl())return 2;return"about:"===new URL(e.getUrl()).protocol?2:e.isOnMainFrame&&""!==e.getUrl()?0:1;case"WORKER":return 3;case"RASTERIZER":return 4;case"THREAD_POOL":return 5;case"AUCTION_WORKLET":return 6;case"OTHER":return 7;default:return 8}},t=i.Handlers.Threads.threadsInTrace(this.#t),n=new Set;for(const{pid:e,tid:i,name:r,type:a}of t){if(this.#t.Meta.traceIsGeneric){this.#Lt.push(new we(this,this.#t,e,i,r,"OTHER"));continue}const t=this.#t.AuctionWorklets.worklets.get(e);n.has(e)||(t?(n.add(e),this.#Lt.push(new we(this,this.#t,e,t.args.data.utilityThread.tid,"auction-worket-utility","AUCTION_WORKLET")),this.#Lt.push(new we(this,this.#t,e,t.args.data.v8HelperThread.tid,"auction-worklet-v8helper","AUCTION_WORKLET"))):this.#Lt.push(new we(this,this.#t,e,i,r,a)))}this.#Lt.sort(((t,i)=>e(t)-e(i))),this.#St.push(...this.#Lt)}getLegacyEvent(e){const t=this.#Ct.tracingModel()?.getProcessById(e.pid),n=t?.threadById(e.tid);return n?i.Legacy.PayloadEvent.fromPayload(e,n):null}timingsTrackAppender(){return this.#kt}animationsTrackAppender(){return this.#Et}interactionsTrackAppender(){return this.#Mt}gpuTrackAppender(){return this.#Ft}layoutShiftsTrackAppender(){return this.#It}threadAppenders(){return this.#Lt}eventsInTrack(e){const t=this.#ft.get(e);if(t)return t;let i=null,n=null;for(const[t,r]of this.#Tt)r===e&&(null===i&&(i=t),n=t);if(null===i||null===n)throw new Error(`Could not find events for track: ${e}`);const r=this.#Ue.entryLevels,a=[];for(let e=0;e<r.length;e++)i<=r[e]&&r[e]<=n&&a.push(this.#wt[e]);return a.sort(((e,t)=>e.ts-t.ts)),this.#ft.set(e,a),a}canBuildTreesFromEvents(e){const t=[];for(const i of e){const e=i.ts,n=i.ts+(i.dur||0);let r=t.at(-1);if(void 0===r){t.push(i);continue}let a=r.ts+(r.dur||0);for(;t.length&&e>=a&&(t.pop(),r=t.at(-1),void 0!==r);)a=r.ts+(r.dur||0);if(t.length&&n>a)return!1;t.push(i)}return!0}eventsForTreeView(e){const t=this.#yt.get(e);if(t)return t;let n=this.eventsInTrack(e);return this.canBuildTreesFromEvents(n)||(n=n.filter((e=>!i.Types.TraceEvents.isAsyncPhase(e.ph)))),this.#yt.set(e,n),n}registerTrackForGroup(e,t){this.#Ue.groups.push(e),this.#vt.set(e,t)}getCurrentTrackCountForThreadType(e){return this.#Lt.filter((t=>t.threadType===e&&t.headerAppended())).length}groupForAppender(e){let t=null;for(const[i,n]of this.#vt)if(n===e){t=i;break}return t}groupEventsForTreeView(e){const t=this.#vt.get(e);return t?this.eventsForTreeView(t):null}registerTrackForLevel(e,t){this.#Tt.set(e,t)}appendEventAtLevel(e,t,n){this.#Tt.set(t,n);const r=this.#wt.length;this.#wt.push(e),this.#Pt[t]="TrackAppender",this.#Ue.entryLevels[r]=t,this.#Ue.entryStartTimes[r]=i.Helpers.Timing.microSecondsToMilliseconds(e.ts);const a=e.dur||i.Helpers.Timing.millisecondsToMicroseconds(kn);return this.#Ue.entryTotalTimes[r]=i.Helpers.Timing.microSecondsToMilliseconds(a),r}appendEventsAtLevel(e,t,i,n){const r=[];for(let a=0;a<e.length;++a){const s=e[a];if(!this.entryIsVisibleInTimeline(s))continue;const o=x(s,r),l=this.appendEventAtLevel(s,t+o,i);n?.(s,l)}return this.#Pt.length=t+r.length,this.#Pt.fill("TrackAppender",t),t+r.length}entryIsVisibleInTimeline(e){if(this.#t.Meta.traceIsGeneric)return!0;if(i.Types.TraceEvents.isTraceEventUpdateCounters(e))return!0;const t=Z(e.name),n=i.Types.TraceEvents.isTraceEventConsoleTime(e)||i.Types.TraceEvents.isTraceEventPerformanceMeasure(e)||i.Types.TraceEvents.isTraceEventPerformanceMark(e);return t&&!t.hidden||n}allVisibleTrackAppenders(){return this.#St.filter((e=>this.#bt.has(e.appenderName)))}allThreadAppendersByProcess(){const e=this.allVisibleTrackAppenders(),t=new Map;for(const i of e){if(!(i instanceof we))continue;const e=t.get(i.processId())??[];e.push(i),t.set(i.processId(),e)}return t}setVisibleTracks(e){this.#bt=e||new Set([...Rn])}colorForEvent(e,t){const i=this.#Tt.get(t);if(!i)throw new Error("Track not found for level");return i.colorForEvent(e)}titleForEvent(e,t){const i=this.#Tt.get(t);if(!i)throw new Error("Track not found for level");return i.titleForEvent(e)}highlightedEntryInfo(e,t){const i=this.#Tt.get(t);if(!i)throw new Error("Track not found for level");const n=a.DetailsView.buildWarningElementsForEvent(e,this.#t),{title:r,formattedTime:s,warningElements:o}=i.highlightedEntryInfo(e);return{title:r,formattedTime:s,warningElements:n.concat(o||[])}}}var An=Object.freeze({__proto__:null,TrackNames:Rn,CompatibilityTracksAppender:Dn});export{U as AnimationsTrackAppender,D as AppenderUtils,V as BenchmarkEvents,_ as CLSLinkifier,An as CompatibilityTracksAppender,He as CountersGraph,ee as EventUICategory,qi as EventsTimelineTreeView,ae as GPUTrackAppender,Me as Initiators,he as InteractionsTrackAppender,Te as LayoutShiftsTrackAppender,dn as NetworkTrackAppender,$e as PerformanceModel,Ze as SaveFileFormatter,Oe as SourceMapsResolver,Se as ThreadAppender,nt as TimelineController,an as TimelineDetailsView,Tt as TimelineEventOverview,Fi as TimelineFilters,En as TimelineFlameChartDataProvider,mn as TimelineFlameChartNetworkDataProvider,wn as TimelineFlameChartView,Mt as TimelineHistoryManager,$i as TimelineLayersView,Dt as TimelineLoader,Ht as TimelineMiniMap,Zi as TimelinePaintProfilerView,oi as TimelinePanel,Vt as TimelineSelection,Vi as TimelineTreeView,Pi as TimelineUIUtils,xn as TimingsTrackAppender,Yt as UIDevtoolsController,Xt as UIDevtoolsUtils};
