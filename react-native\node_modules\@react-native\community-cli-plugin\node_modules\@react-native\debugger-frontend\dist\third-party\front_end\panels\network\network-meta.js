import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as r from"../../core/sdk/sdk.js";import*as n from"../../models/workspace/workspace.js";import*as a from"../../ui/legacy/legacy.js";import*as i from"./forward/forward.js";const s={showNetwork:"Show Network",network:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},l=t.i18n.registerUIStrings("panels/network/network-meta.ts",s),c=t.i18n.getLazilyComputedLocalizedString.bind(void 0,l);let w;async function d(){return w||(w=await import("./network.js")),w}function g(e){return void 0===w?[]:e(w)}a.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:c(s.showNetwork),title:c(s.network),order:1100,condition:o.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await d()).NetworkPanel.NetworkPanel.instance()}),a.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:c(s.showNetworkRequestBlocking),title:c(s.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await d()).BlockedURLsPane.BlockedURLsPane)}),a.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:c(s.showNetworkConditions),title:c(s.networkConditions),persistence:"closeable",order:40,tags:[c(s.diskCache),c(s.networkThrottling),t.i18n.lockedLazyString("useragent"),t.i18n.lockedLazyString("user agent"),t.i18n.lockedLazyString("user-agent")],loadView:async()=>(await d()).NetworkConfigView.NetworkConfigView.instance()}),a.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:c(s.showSearch),title:c(s.search),persistence:"permanent",loadView:async()=>(await d()).NetworkPanel.SearchNetworkView.instance()}),a.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>g((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await d()).NetworkPanel.ActionDelegate),options:[{value:!0,title:c(s.recordNetworkLog)},{value:!1,title:c(s.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),a.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:c(s.clear),iconClass:"clear",loadActionDelegate:async()=>new((await d()).NetworkPanel.ActionDelegate),contextTypes:()=>g((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),a.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:c(s.hideRequestDetails),contextTypes:()=>g((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await d()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),a.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:c(s.search),contextTypes:()=>g((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await d()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),a.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:c(s.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>g((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await d()).BlockedURLsPane.ActionDelegate)}),a.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:c(s.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>g((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await d()).BlockedURLsPane.ActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:c(s.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[c(s.colorCode),c(s.resourceType)],options:[{value:!0,title:c(s.colorCodeByResourceType)},{value:!1,title:c(s.useDefaultColors)}]}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:c(s.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[c(s.netWork),c(s.frame),c(s.group)],options:[{value:!0,title:c(s.groupNetworkLogItemsByFrame)},{value:!1,title:c(s.dontGroupNetworkLogItemsByFrame)}]}),a.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await d()).NetworkPanel.NetworkPanel.instance()}),a.ContextMenu.registerProvider({contextTypes:()=>[r.NetworkRequest.NetworkRequest,r.Resource.Resource,n.UISourceCode.UISourceCode],loadProvider:async()=>(await d()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[r.NetworkRequest.NetworkRequest],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await d()).NetworkPanel.RequestRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[i.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await d()).NetworkPanel.RequestLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[i.NetworkRequestId.NetworkRequestId],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await d()).NetworkPanel.RequestIdRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[i.UIFilter.UIRequestFilter],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await d()).NetworkPanel.NetworkLogWithFilterRevealer)});
