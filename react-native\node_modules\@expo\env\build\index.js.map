{"version": 3, "file": "index.js", "names": ["_chalk", "data", "_interopRequireDefault", "require", "dotenv", "_interopRequireWildcard", "_dotenvExpand", "_getenv", "_nodeConsole", "_nodeFs", "_nodePath", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "debug", "isEnabled", "boolish", "KNOWN_MODES", "exports", "LOADED_ENV_NAME", "getEnvFiles", "mode", "process", "env", "NODE_ENV", "silent", "logError", "console", "error", "logWarning", "warn", "includes", "filter", "Boolean", "parseEnvFiles", "envFiles", "systemEnv", "files", "loadedEnvVars", "loadedEnvFiles", "reverse", "for<PERSON>ach", "envFile", "env<PERSON><PERSON><PERSON><PERSON><PERSON>", "fs", "readFileSync", "envFileParsed", "parse", "push", "key", "keys", "code", "expandEnvFromSystem", "loadEnvFiles", "force", "result", "loaded", "JSON", "parsed", "loadedEnvKeys", "stringify", "parsedEnv", "expandedEnv", "allExpandedEnv", "dotenvExpand", "processEnv", "parseProjectEnv", "projectRoot", "options", "map", "path", "join", "loadProjectEnv", "logLoadedEnv", "envInfo", "length", "log", "chalk", "gray", "file", "basename", "memo", "load", "getFiles"], "sources": ["../src/index.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport * as dotenv from 'dotenv';\nimport { expand as dotenvExpand } from 'dotenv-expand';\nimport { boolish } from 'getenv';\nimport console from 'node:console';\nimport fs from 'node:fs';\nimport path from 'node:path';\n\nconst debug = require('debug')('expo:env') as typeof console.log;\n\n/** Determine if the `.env` files are enabled or not, through `EXPO_NO_DOTENV` */\nexport function isEnabled() {\n  return !boolish('EXPO_NO_DOTENV', false);\n}\n\n/** All conventional modes that should not cause warnings */\nexport const KNOWN_MODES = ['development', 'test', 'production'];\n\n/** The environment variable name to use when marking the environment as loaded */\nexport const LOADED_ENV_NAME = '__EXPO_ENV_LOADED';\n\n/**\n * Get a list of all `.env*` files based on the `NODE_ENV` mode.\n * This returns a list of files, in order of highest priority to lowest priority.\n *\n * @see https://github.com/bkeepers/dotenv/tree/v3.1.4#customizing-rails\n */\nexport function getEnvFiles({\n  mode = process.env.NODE_ENV,\n  silent,\n}: {\n  /** The mode to use when creating the list of `.env*` files, defaults to `NODE_ENV` */\n  mode?: string;\n  /** If possible misconfiguration warnings should be logged, or only logged as debug log */\n  silent?: boolean;\n} = {}) {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return [];\n  }\n\n  const logError = silent ? debug : console.error;\n  const logWarning = silent ? debug : console.warn;\n\n  if (!mode) {\n    logError(\n      `The NODE_ENV environment variable is required but was not specified. Ensure the project is bundled with Expo CLI or NODE_ENV is set. Using only .env.local and .env`\n    );\n    return ['.env.local', '.env'];\n  }\n\n  if (!KNOWN_MODES.includes(mode)) {\n    logWarning(\n      `NODE_ENV=\"${mode}\" is non-conventional and might cause development code to run in production. Use \"development\", \"test\", or \"production\" instead. Continuing with non-conventional mode`\n    );\n  }\n\n  // see: https://github.com/bkeepers/dotenv/tree/v3.1.4#customizing-rails\n  return [\n    `.env.${mode}.local`,\n    // Don't include `.env.local` for `test` environment\n    // since normally you expect tests to produce the same\n    // results for everyone\n    mode !== 'test' && `.env.local`,\n    `.env.${mode}`,\n    `.env`,\n  ].filter(Boolean) as string[];\n}\n\n/**\n * Parse all environment variables using the list of `.env*` files, in order of higest priority to lowest priority.\n * This does not check for collisions of existing system environment variables, or mutates the system environment variables.\n */\nexport function parseEnvFiles(\n  envFiles: string[],\n  {\n    systemEnv = process.env,\n  }: {\n    /** The system environment to use when expanding environment variables, defaults to `process.env` */\n    systemEnv?: NodeJS.ProcessEnv;\n  } = {}\n) {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return { env: {}, files: [] };\n  }\n\n  // Load environment variables from .env* files. Suppress warnings using silent\n  // if this file is missing. Dotenv will only parse the environment variables,\n  // `@expo/env` will set the resulting variables to the current process.\n  // Variable expansion is supported in .env files, and executed as final step.\n  // https://github.com/motdotla/dotenv\n  // https://github.com/motdotla/dotenv-expand\n  const loadedEnvVars: dotenv.DotenvParseOutput = {};\n  const loadedEnvFiles: string[] = [];\n\n  // Iterate over each dotenv file in lowest prio to highest prio order.\n  // This step won't write to the process.env, but will overwrite the parsed envs.\n  [...envFiles].reverse().forEach((envFile) => {\n    try {\n      const envFileContent = fs.readFileSync(envFile, 'utf8');\n      const envFileParsed = dotenv.parse(envFileContent);\n\n      // If there are parsing issues, mark the file as not-parsed\n      if (!envFileParsed) {\n        return debug(`Failed to load environment variables from: ${envFile}%s`);\n      }\n\n      loadedEnvFiles.push(envFile);\n      debug(`Loaded environment variables from: ${envFile}`);\n\n      for (const key of Object.keys(envFileParsed)) {\n        if (typeof loadedEnvVars[key] !== 'undefined') {\n          debug(`\"${key}\" is already defined and overwritten by: ${envFile}`);\n        }\n\n        loadedEnvVars[key] = envFileParsed[key];\n      }\n    } catch (error: any) {\n      if ('code' in error && error.code === 'ENOENT') {\n        return debug(`${envFile} does not exist, skipping this env file`);\n      }\n      if ('code' in error && error.code === 'EISDIR') {\n        return debug(`${envFile} is a directory, skipping this env file`);\n      }\n      if ('code' in error && error.code === 'EACCES') {\n        return debug(`No permission to read ${envFile}, skipping this env file`);\n      }\n\n      throw error;\n    }\n  });\n\n  return {\n    env: expandEnvFromSystem(loadedEnvVars, systemEnv),\n    files: loadedEnvFiles.reverse(),\n  };\n}\n\n/**\n * Parse all environment variables using the list of `.env*` files, and mutate the system environment with these variables.\n * This won't override existing environment variables defined in the system environment.\n * Once the mutations are done, this will also set a propert `__EXPO_ENV=true` on the system env to avoid multiple mutations.\n * This check can be disabled through `{ force: true }`.\n */\nexport function loadEnvFiles(\n  envFiles: string[],\n  {\n    force,\n    silent = false,\n    systemEnv = process.env,\n  }: Parameters<typeof parseEnvFiles>[1] & {\n    /** If the environment variables should be applied to the system environment, regardless of previous mutations */\n    force?: boolean;\n    /** If possible misconfiguration warnings should be logged, or only logged as debug log */\n    silent?: boolean;\n  } = {}\n) {\n  if (!force && systemEnv[LOADED_ENV_NAME]) {\n    return { result: 'skipped' as const, loaded: JSON.parse(systemEnv[LOADED_ENV_NAME]) };\n  }\n\n  const parsed = parseEnvFiles(envFiles, { systemEnv });\n  const loadedEnvKeys: string[] = [];\n\n  for (const key in parsed.env) {\n    if (typeof systemEnv[key] !== 'undefined') {\n      debug(`\"${key}\" is already defined and IS NOT overwritten`);\n    } else {\n      systemEnv[key] = parsed.env[key];\n      loadedEnvKeys.push(key);\n    }\n  }\n\n  // Mark the environment as loaded\n  systemEnv[LOADED_ENV_NAME] = JSON.stringify(loadedEnvKeys);\n\n  return { result: 'loaded' as const, ...parsed, loaded: loadedEnvKeys };\n}\n\n/**\n * Expand the parsed environment variables using the existing system environment variables.\n * This does not mutate the existing system environment variables, and only returns the expanded variables.\n */\nfunction expandEnvFromSystem(\n  parsedEnv: Record<string, string>,\n  systemEnv: NodeJS.ProcessEnv = process.env\n) {\n  const expandedEnv: Record<string, string> = {};\n\n  // Pass a clone of the system environment variables to avoid mutating the original environment.\n  // When the expansion is done, we only store the environment variables that were initially parsed from `parsedEnv`.\n  const allExpandedEnv = dotenvExpand({\n    parsed: parsedEnv,\n    processEnv: { ...systemEnv } as Record<string, string>,\n  });\n\n  if (allExpandedEnv.error) {\n    console.error(\n      `Failed to expand environment variables, using non-expanded environment variables: ${allExpandedEnv.error}`\n    );\n    return parsedEnv;\n  }\n\n  // Only store the values that were initially parsed, from `parsedEnv`.\n  for (const key of Object.keys(parsedEnv)) {\n    if (allExpandedEnv.parsed?.[key]) {\n      expandedEnv[key] = allExpandedEnv.parsed[key];\n    }\n  }\n\n  return expandedEnv;\n}\n\n/**\n * Parse all environment variables using the detected list of `.env*` files from a project.\n * This does not check for collisions of existing system environment variables, or mutates the system environment variables.\n */\nexport function parseProjectEnv(\n  projectRoot: string,\n  options?: Parameters<typeof getEnvFiles>[0] & Parameters<typeof parseEnvFiles>[1]\n) {\n  return parseEnvFiles(\n    getEnvFiles(options).map((envFile) => path.join(projectRoot, envFile)),\n    options\n  );\n}\n\n/**\n * Parse all environment variables using the detected list of `.env*` files from a project.\n * This won't override existing environment variables defined in the system environment.\n * Once the mutations are done, this will also set a propert `__EXPO_ENV=true` on the system env to avoid multiple mutations.\n * This check can be disabled through `{ force: true }`.\n */\nexport function loadProjectEnv(\n  projectRoot: string,\n  options?: Parameters<typeof getEnvFiles>[0] & Parameters<typeof loadEnvFiles>[1]\n) {\n  return loadEnvFiles(\n    getEnvFiles(options).map((envFile) => path.join(projectRoot, envFile)),\n    options\n  );\n}\n\n/** Log the loaded environment info from the loaded results */\nexport function logLoadedEnv(\n  envInfo: ReturnType<typeof loadEnvFiles>,\n  options: Parameters<typeof loadEnvFiles>[1] = {}\n) {\n  // Skip when running in force mode, or no environment variables are loaded\n  if (options.force || options.silent || !envInfo.loaded.length) return envInfo;\n\n  // Log the loaded environment files, when not skipped\n  if (envInfo.result === 'loaded') {\n    console.log(\n      chalk.gray('env: load', envInfo.files.map((file) => path.basename(file)).join(' '))\n    );\n  }\n\n  // Log the loaded environment variables\n  console.log(chalk.gray('env: export', envInfo.loaded.join(' ')));\n\n  return envInfo;\n}\n\n// Legacy API - for backwards compatibility\n\nlet memo: ReturnType<typeof parseEnvFiles> | null = null;\n\n/**\n * Get the environment variables without mutating the environment.\n * This returns memoized values unless the `force` property is provided.\n *\n * @deprecated use {@link parseProjectEnv} instead\n */\nexport function get(\n  projectRoot: string,\n  {\n    force,\n    silent,\n  }: {\n    force?: boolean;\n    silent?: boolean;\n  } = {}\n) {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return { env: {}, files: [] };\n  }\n  if (force || !memo) {\n    memo = parseProjectEnv(projectRoot, { silent });\n  }\n  return memo;\n}\n\n/**\n * Load environment variables from .env files and mutate the current `process.env` with the results.\n *\n * @deprecated use {@link loadProjectEnv} instead\n */\nexport function load(\n  projectRoot: string,\n  options: {\n    force?: boolean;\n    silent?: boolean;\n  } = {}\n) {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return process.env;\n  }\n\n  const envInfo = get(projectRoot, options);\n  const loadedEnvKeys: string[] = [];\n\n  for (const key in envInfo.env) {\n    if (typeof process.env[key] !== 'undefined') {\n      debug(`\"${key}\" is already defined and IS NOT overwritten`);\n    } else {\n      // Avoid creating a new object, mutate it instead as this causes problems in Bun\n      process.env[key] = envInfo.env[key];\n      loadedEnvKeys.push(key);\n    }\n  }\n\n  // Port the result of `get` to the newer result object\n  logLoadedEnv({ ...envInfo, result: 'loaded', loaded: loadedEnvKeys }, options);\n\n  return process.env;\n}\n\n/**\n * Get a list of all `.env*` files based on the `NODE_ENV` mode.\n * This returns a list of files, in order of highest priority to lowest priority.\n *\n * @deprecated use {@link getEnvFiles} instead\n * @see https://github.com/bkeepers/dotenv/tree/v3.1.4#customizing-rails\n */\nexport function getFiles(mode: string | undefined, { silent = false }: { silent?: boolean } = {}) {\n  if (!isEnabled()) {\n    debug(`Skipping .env files because EXPO_NO_DOTENV is defined`);\n    return [];\n  }\n\n  return getEnvFiles({ mode, silent });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,cAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,aAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,aAAA;EAAA,MAAAP,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,UAAA;EAAA,MAAAT,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAO,SAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6B,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAlB,uBAAAU,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAE7B,MAAMmB,KAAK,GAAG5B,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,CAAuB;;AAEhE;AACO,SAAS6B,SAASA,CAAA,EAAG;EAC1B,OAAO,CAAC,IAAAC,iBAAO,EAAC,gBAAgB,EAAE,KAAK,CAAC;AAC1C;;AAEA;AACO,MAAMC,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;;AAEhE;AACO,MAAME,eAAe,GAAAD,OAAA,CAAAC,eAAA,GAAG,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAAC;EAC1BC,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC3BC;AAMF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAI,CAACV,SAAS,CAAC,CAAC,EAAE;IAChBD,KAAK,CAAC,uDAAuD,CAAC;IAC9D,OAAO,EAAE;EACX;EAEA,MAAMY,QAAQ,GAAGD,MAAM,GAAGX,KAAK,GAAGa,sBAAO,CAACC,KAAK;EAC/C,MAAMC,UAAU,GAAGJ,MAAM,GAAGX,KAAK,GAAGa,sBAAO,CAACG,IAAI;EAEhD,IAAI,CAACT,IAAI,EAAE;IACTK,QAAQ,CACN,qKACF,CAAC;IACD,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/B;EAEA,IAAI,CAACT,WAAW,CAACc,QAAQ,CAACV,IAAI,CAAC,EAAE;IAC/BQ,UAAU,CACR,aAAaR,IAAI,wKACnB,CAAC;EACH;;EAEA;EACA,OAAO,CACL,QAAQA,IAAI,QAAQ;EACpB;EACA;EACA;EACAA,IAAI,KAAK,MAAM,IAAI,YAAY,EAC/B,QAAQA,IAAI,EAAE,EACd,MAAM,CACP,CAACW,MAAM,CAACC,OAAO,CAAC;AACnB;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAC3BC,QAAkB,EAClB;EACEC,SAAS,GAAGd,OAAO,CAACC;AAItB,CAAC,GAAG,CAAC,CAAC,EACN;EACA,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE;IAChBD,KAAK,CAAC,uDAAuD,CAAC;IAC9D,OAAO;MAAES,GAAG,EAAE,CAAC,CAAC;MAAEc,KAAK,EAAE;IAAG,CAAC;EAC/B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,aAAuC,GAAG,CAAC,CAAC;EAClD,MAAMC,cAAwB,GAAG,EAAE;;EAEnC;EACA;EACA,CAAC,GAAGJ,QAAQ,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,OAAO,CAAEC,OAAO,IAAK;IAC3C,IAAI;MACF,MAAMC,cAAc,GAAGC,iBAAE,CAACC,YAAY,CAACH,OAAO,EAAE,MAAM,CAAC;MACvD,MAAMI,aAAa,GAAG3D,MAAM,CAAD,CAAC,CAAC4D,KAAK,CAACJ,cAAc,CAAC;;MAElD;MACA,IAAI,CAACG,aAAa,EAAE;QAClB,OAAOhC,KAAK,CAAC,8CAA8C4B,OAAO,IAAI,CAAC;MACzE;MAEAH,cAAc,CAACS,IAAI,CAACN,OAAO,CAAC;MAC5B5B,KAAK,CAAC,sCAAsC4B,OAAO,EAAE,CAAC;MAEtD,KAAK,MAAMO,GAAG,IAAI3C,MAAM,CAAC4C,IAAI,CAACJ,aAAa,CAAC,EAAE;QAC5C,IAAI,OAAOR,aAAa,CAACW,GAAG,CAAC,KAAK,WAAW,EAAE;UAC7CnC,KAAK,CAAC,IAAImC,GAAG,4CAA4CP,OAAO,EAAE,CAAC;QACrE;QAEAJ,aAAa,CAACW,GAAG,CAAC,GAAGH,aAAa,CAACG,GAAG,CAAC;MACzC;IACF,CAAC,CAAC,OAAOrB,KAAU,EAAE;MACnB,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACuB,IAAI,KAAK,QAAQ,EAAE;QAC9C,OAAOrC,KAAK,CAAC,GAAG4B,OAAO,yCAAyC,CAAC;MACnE;MACA,IAAI,MAAM,IAAId,KAAK,IAAIA,KAAK,CAACuB,IAAI,KAAK,QAAQ,EAAE;QAC9C,OAAOrC,KAAK,CAAC,GAAG4B,OAAO,yCAAyC,CAAC;MACnE;MACA,IAAI,MAAM,IAAId,KAAK,IAAIA,KAAK,CAACuB,IAAI,KAAK,QAAQ,EAAE;QAC9C,OAAOrC,KAAK,CAAC,yBAAyB4B,OAAO,0BAA0B,CAAC;MAC1E;MAEA,MAAMd,KAAK;IACb;EACF,CAAC,CAAC;EAEF,OAAO;IACLL,GAAG,EAAE6B,mBAAmB,CAACd,aAAa,EAAEF,SAAS,CAAC;IAClDC,KAAK,EAAEE,cAAc,CAACC,OAAO,CAAC;EAChC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASa,YAAYA,CAC1BlB,QAAkB,EAClB;EACEmB,KAAK;EACL7B,MAAM,GAAG,KAAK;EACdW,SAAS,GAAGd,OAAO,CAACC;AAMtB,CAAC,GAAG,CAAC,CAAC,EACN;EACA,IAAI,CAAC+B,KAAK,IAAIlB,SAAS,CAACjB,eAAe,CAAC,EAAE;IACxC,OAAO;MAAEoC,MAAM,EAAE,SAAkB;MAAEC,MAAM,EAAEC,IAAI,CAACV,KAAK,CAACX,SAAS,CAACjB,eAAe,CAAC;IAAE,CAAC;EACvF;EAEA,MAAMuC,MAAM,GAAGxB,aAAa,CAACC,QAAQ,EAAE;IAAEC;EAAU,CAAC,CAAC;EACrD,MAAMuB,aAAuB,GAAG,EAAE;EAElC,KAAK,MAAMV,GAAG,IAAIS,MAAM,CAACnC,GAAG,EAAE;IAC5B,IAAI,OAAOa,SAAS,CAACa,GAAG,CAAC,KAAK,WAAW,EAAE;MACzCnC,KAAK,CAAC,IAAImC,GAAG,6CAA6C,CAAC;IAC7D,CAAC,MAAM;MACLb,SAAS,CAACa,GAAG,CAAC,GAAGS,MAAM,CAACnC,GAAG,CAAC0B,GAAG,CAAC;MAChCU,aAAa,CAACX,IAAI,CAACC,GAAG,CAAC;IACzB;EACF;;EAEA;EACAb,SAAS,CAACjB,eAAe,CAAC,GAAGsC,IAAI,CAACG,SAAS,CAACD,aAAa,CAAC;EAE1D,OAAO;IAAEJ,MAAM,EAAE,QAAiB;IAAE,GAAGG,MAAM;IAAEF,MAAM,EAAEG;EAAc,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA,SAASP,mBAAmBA,CAC1BS,SAAiC,EACjCzB,SAA4B,GAAGd,OAAO,CAACC,GAAG,EAC1C;EACA,MAAMuC,WAAmC,GAAG,CAAC,CAAC;;EAE9C;EACA;EACA,MAAMC,cAAc,GAAG,IAAAC,sBAAY,EAAC;IAClCN,MAAM,EAAEG,SAAS;IACjBI,UAAU,EAAE;MAAE,GAAG7B;IAAU;EAC7B,CAAC,CAAC;EAEF,IAAI2B,cAAc,CAACnC,KAAK,EAAE;IACxBD,sBAAO,CAACC,KAAK,CACX,qFAAqFmC,cAAc,CAACnC,KAAK,EAC3G,CAAC;IACD,OAAOiC,SAAS;EAClB;;EAEA;EACA,KAAK,MAAMZ,GAAG,IAAI3C,MAAM,CAAC4C,IAAI,CAACW,SAAS,CAAC,EAAE;IACxC,IAAIE,cAAc,CAACL,MAAM,GAAGT,GAAG,CAAC,EAAE;MAChCa,WAAW,CAACb,GAAG,CAAC,GAAGc,cAAc,CAACL,MAAM,CAACT,GAAG,CAAC;IAC/C;EACF;EAEA,OAAOa,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACO,SAASI,eAAeA,CAC7BC,WAAmB,EACnBC,OAAiF,EACjF;EACA,OAAOlC,aAAa,CAClBd,WAAW,CAACgD,OAAO,CAAC,CAACC,GAAG,CAAE3B,OAAO,IAAK4B,mBAAI,CAACC,IAAI,CAACJ,WAAW,EAAEzB,OAAO,CAAC,CAAC,EACtE0B,OACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,cAAcA,CAC5BL,WAAmB,EACnBC,OAAgF,EAChF;EACA,OAAOf,YAAY,CACjBjC,WAAW,CAACgD,OAAO,CAAC,CAACC,GAAG,CAAE3B,OAAO,IAAK4B,mBAAI,CAACC,IAAI,CAACJ,WAAW,EAAEzB,OAAO,CAAC,CAAC,EACtE0B,OACF,CAAC;AACH;;AAEA;AACO,SAASK,YAAYA,CAC1BC,OAAwC,EACxCN,OAA2C,GAAG,CAAC,CAAC,EAChD;EACA;EACA,IAAIA,OAAO,CAACd,KAAK,IAAIc,OAAO,CAAC3C,MAAM,IAAI,CAACiD,OAAO,CAAClB,MAAM,CAACmB,MAAM,EAAE,OAAOD,OAAO;;EAE7E;EACA,IAAIA,OAAO,CAACnB,MAAM,KAAK,QAAQ,EAAE;IAC/B5B,sBAAO,CAACiD,GAAG,CACTC,gBAAK,CAACC,IAAI,CAAC,WAAW,EAAEJ,OAAO,CAACrC,KAAK,CAACgC,GAAG,CAAEU,IAAI,IAAKT,mBAAI,CAACU,QAAQ,CAACD,IAAI,CAAC,CAAC,CAACR,IAAI,CAAC,GAAG,CAAC,CACpF,CAAC;EACH;;EAEA;EACA5C,sBAAO,CAACiD,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,aAAa,EAAEJ,OAAO,CAAClB,MAAM,CAACe,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAEhE,OAAOG,OAAO;AAChB;;AAEA;;AAEA,IAAIO,IAA6C,GAAG,IAAI;;AAExD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS/E,GAAGA,CACjBiE,WAAmB,EACnB;EACEb,KAAK;EACL7B;AAIF,CAAC,GAAG,CAAC,CAAC,EACN;EACA,IAAI,CAACV,SAAS,CAAC,CAAC,EAAE;IAChBD,KAAK,CAAC,uDAAuD,CAAC;IAC9D,OAAO;MAAES,GAAG,EAAE,CAAC,CAAC;MAAEc,KAAK,EAAE;IAAG,CAAC;EAC/B;EACA,IAAIiB,KAAK,IAAI,CAAC2B,IAAI,EAAE;IAClBA,IAAI,GAAGf,eAAe,CAACC,WAAW,EAAE;MAAE1C;IAAO,CAAC,CAAC;EACjD;EACA,OAAOwD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,IAAIA,CAClBf,WAAmB,EACnBC,OAGC,GAAG,CAAC,CAAC,EACN;EACA,IAAI,CAACrD,SAAS,CAAC,CAAC,EAAE;IAChBD,KAAK,CAAC,uDAAuD,CAAC;IAC9D,OAAOQ,OAAO,CAACC,GAAG;EACpB;EAEA,MAAMmD,OAAO,GAAGxE,GAAG,CAACiE,WAAW,EAAEC,OAAO,CAAC;EACzC,MAAMT,aAAuB,GAAG,EAAE;EAElC,KAAK,MAAMV,GAAG,IAAIyB,OAAO,CAACnD,GAAG,EAAE;IAC7B,IAAI,OAAOD,OAAO,CAACC,GAAG,CAAC0B,GAAG,CAAC,KAAK,WAAW,EAAE;MAC3CnC,KAAK,CAAC,IAAImC,GAAG,6CAA6C,CAAC;IAC7D,CAAC,MAAM;MACL;MACA3B,OAAO,CAACC,GAAG,CAAC0B,GAAG,CAAC,GAAGyB,OAAO,CAACnD,GAAG,CAAC0B,GAAG,CAAC;MACnCU,aAAa,CAACX,IAAI,CAACC,GAAG,CAAC;IACzB;EACF;;EAEA;EACAwB,YAAY,CAAC;IAAE,GAAGC,OAAO;IAAEnB,MAAM,EAAE,QAAQ;IAAEC,MAAM,EAAEG;EAAc,CAAC,EAAES,OAAO,CAAC;EAE9E,OAAO9C,OAAO,CAACC,GAAG;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS4D,QAAQA,CAAC9D,IAAwB,EAAE;EAAEI,MAAM,GAAG;AAA4B,CAAC,GAAG,CAAC,CAAC,EAAE;EAChG,IAAI,CAACV,SAAS,CAAC,CAAC,EAAE;IAChBD,KAAK,CAAC,uDAAuD,CAAC;IAC9D,OAAO,EAAE;EACX;EAEA,OAAOM,WAAW,CAAC;IAAEC,IAAI;IAAEI;EAAO,CAAC,CAAC;AACtC", "ignoreList": []}