import*as e from"../../../core/i18n/i18n.js";import*as t from"../../../core/platform/platform.js";import*as o from"../../../ui/components/render_coordinator/render_coordinator.js";import*as r from"../../../ui/lit-html/lit-html.js";import*as n from"../../../ui/components/buttons/buttons.js";import*as s from"../../../ui/components/input/input.js";import*as i from"../../../ui/visual_logging/visual_logging.js";import*as a from"../../../core/common/common.js";import*as l from"../../../ui/components/helpers/helpers.js";import*as c from"../../../ui/components/icon_button/icon_button.js";import*as d from"../../../ui/components/node_text/node_text.js";import*as p from"../../../core/sdk/sdk.js";import*as u from"../../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as h from"../../../ui/legacy/legacy.js";const g=new CSSStyleSheet;g.replaceSync(".container{width:100%;display:inline-block}.container:hover{background-color:var(--sys-color-state-hover-on-subtle)}span{color:var(--sys-color-token-meta);font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}.role-value{color:var(--sys-color-token-tag)}.attribute-name{color:var(--sys-color-token-attribute)}.attribute-value{color:var(--sys-color-token-attribute-value)}\n/*# sourceURL=accessibilityTreeNode.css */\n");const m={ignored:"Ignored"},y=e.i18n.registerUIStrings("panels/elements/components/AccessibilityTreeNode.ts",m),v=e.i18n.getLocalizedString.bind(void 0,y);class f extends HTMLElement{static litTagName=r.literal`devtools-accessibility-tree-node`;#e=this.attachShadow({mode:"open"});#t=!0;#o="";#r="";#n=[];#s="";set data(e){this.#t=e.ignored,this.#o=e.name,this.#r=e.role,this.#n=e.properties,this.#s=e.id,this.#i()}connectedCallback(){this.#e.adoptedStyleSheets=[g]}async#i(){const e=r.html`<span class='role-value'>${n=this.#r,n.length>1e4?t.StringUtilities.trimMiddle(n,1e4):n}</span>`;var n;const s=r.html`"<span class='attribute-value'>${this.#o}</span>"`,i=this.#n.map((({name:e,value:t})=>function(e){switch(e){case"boolean":case"booleanOrUndefined":case"string":case"number":return!0;default:return!1}}(t.type)?r.html` <span class='attribute-name'>${e}</span>:&nbsp;<span class='attribute-value'>${t.value}</span>`:r.nothing)),a=this.#t?r.html`<span>${v(m.ignored)}</span>`:r.html`${e}&nbsp;${s}${i}`;await o.RenderCoordinator.RenderCoordinator.instance().write(`Accessibility node ${this.#s} render`,(()=>{r.render(r.html`<div class='container'>${a}</div>`,this.#e,{host:this})}))}}customElements.define("devtools-accessibility-tree-node",f);var b,x=Object.freeze({__proto__:null,AccessibilityTreeNode:f});function w(e){switch(e){case b.GRID:return{name:"grid",category:"Layout",enabledByDefault:!0};case b.SUBGRID:return{name:"subgrid",category:"Layout",enabledByDefault:!0};case b.FLEX:return{name:"flex",category:"Layout",enabledByDefault:!0};case b.AD:return{name:"ad",category:"Security",enabledByDefault:!0};case b.SCROLL_SNAP:return{name:"scroll-snap",category:"Layout",enabledByDefault:!0};case b.CONTAINER:return{name:"container",category:"Layout",enabledByDefault:!0};case b.SLOT:return{name:"slot",category:"Layout",enabledByDefault:!0};case b.TOP_LAYER:return{name:"top-layer",category:"Layout",enabledByDefault:!0};case b.REVEAL:return{name:"reveal",category:"Default",enabledByDefault:!0};case b.MEDIA:return{name:"media",category:"Default",enabledByDefault:!1}}}let S;function k(e){if(!S){S=new Map;for(const{name:e,category:t}of Object.values(b).map(w))S.set(e,t)}return S.get(e)||"Default"}!function(e){e.GRID="grid",e.SUBGRID="subgrid",e.FLEX="flex",e.AD="ad",e.SCROLL_SNAP="scroll-snap",e.CONTAINER="container",e.SLOT="slot",e.TOP_LAYER="top-layer",e.REVEAL="reveal",e.MEDIA="media"}(b||(b={}));const N=Object.values(b).map(w).map((({name:e,enabledByDefault:t})=>({adorner:e,isEnabled:t})));const $=new Map(["Security","Layout","Default"].map(((e,t)=>[e,t+1])));var C=Object.freeze({__proto__:null,get RegisteredAdorners(){return b},getRegisteredAdorner:w,DefaultAdornerSettings:N,AdornerManager:class{#a=new Map;#l;constructor(e){this.#l=e,this.#c()}updateSettings(e){this.#a=e,this.#d()}getSettings(){return this.#a}isAdornerEnabled(e){return this.#a.get(e)||!1}#d(){const e=[];for(const[t,o]of this.#a)e.push({adorner:t,isEnabled:o});this.#l.set(e)}#p(){const e=this.#l.get();for(const t of e)this.#a.set(t.adorner,t.isEnabled)}#c(){this.#p();const e=new Set(this.#a.keys());for(const{adorner:t,isEnabled:o}of N)e.delete(t),this.#a.has(t)||this.#a.set(t,o);for(const t of e)this.#a.delete(t);this.#d()}},AdornerCategoryOrder:$,compareAdornerNamesByCategory:function(e,t){return($.get(k(e))||Number.POSITIVE_INFINITY)-($.get(k(t))||Number.POSITIVE_INFINITY)}});const M=new CSSStyleSheet;M.replaceSync(".adorner-settings-pane{display:flex;height:auto;padding:6px 12px;color:var(--sys-color-on-surface);font-size:12px;align-items:center}.settings-title{font-weight:500;margin-right:6px}.setting{margin-left:1em}.adorner-status{margin:auto 0.4em auto 0}.adorner-status,\n.adorner-name{vertical-align:middle}\n/*# sourceURL=adornerSettingsPane.css */\n");const E={settingsTitle:"Show badges",closeButton:"Close"},T=e.i18n.registerUIStrings("panels/elements/components/AdornerSettingsPane.ts",E),z=e.i18n.getLocalizedString.bind(void 0,T),{render:O,html:D}=r;class L extends Event{static eventName="adornersettingupdated";data;constructor(e,t,o){super(L.eventName,{}),this.data={adornerName:e,isEnabledNow:t,newSettings:o}}}class j extends HTMLElement{static litTagName=r.literal`devtools-adorner-settings-pane`;#e=this.attachShadow({mode:"open"});#u=new Map;connectedCallback(){this.#e.adoptedStyleSheets=[s.checkboxStyles,M]}set data(e){this.#u=new Map(e.settings.entries()),this.#i()}show(){this.classList.remove("hidden");const e=this.#e.querySelector(".adorner-settings-pane");e&&e.focus()}hide(){this.classList.add("hidden")}#h(e){const t=e.target,o=t.dataset.adorner;if(void 0===o)return;const r=t.checked;this.#u.set(o,r),this.dispatchEvent(new L(o,r,this.#u)),this.#i()}#i(){const e=[];for(const[t,o]of this.#u)e.push(D`
        <label class="setting" title=${t}>
          <input
            class="adorner-status"
            type="checkbox" name=${t}
            .checked=${o}
            jslog=${i.toggle(t).track({change:!0})}
            data-adorner=${t}>
          <span class="adorner-name">${t}</span>
        </label>
      `);O(D`
      <div class="adorner-settings-pane" tabindex="-1" jslog=${i.pane("adorner-settings")}>
        <div class="settings-title">${z(E.settingsTitle)}</div>
        <div class="setting-list" @change=${this.#h}>
          ${e}
        </div>
        <${n.Button.Button.litTagName} aria-label=${z(E.closeButton)}
                                             .iconName=${"cross"}
                                             .size=${"SMALL"}
                                             .title=${z(E.closeButton)}
                                             .variant=${"round"}
                                             jslog=${i.close().track({click:!0})}
                                             @click=${this.hide}></${n.Button.Button.litTagName}>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-adorner-settings-pane",j);var P=Object.freeze({__proto__:null,AdornerSettingUpdatedEvent:L,AdornerSettingsPane:j});const _=new CSSStyleSheet;_.replaceSync(":host{position:relative;overflow:hidden;flex:auto;text-overflow:ellipsis}.computed-style-property{--goto-size:16px;font-family:var(--monospace-font-family);font-size:var(--monospace-font-size);min-height:16px;box-sizing:border-box;padding-top:2px;white-space:nowrap;user-select:text}.computed-style-property:hover{background-color:var(--sys-color-state-hover-on-subtle);cursor:text}.computed-style-property.inherited{opacity:50%}.property-name,\n.property-value{display:contents;overflow:hidden;text-overflow:ellipsis}.property-name{width:16em;max-width:52%;margin-right:calc(var(--goto-size) / 2);display:inline-block;vertical-align:text-top;color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.property-value{margin-left:2em}.goto{display:none;cursor:pointer;position:absolute;width:var(--goto-size);height:var(--goto-size);margin:-1px 0 0 calc(-1 * var(--goto-size));mask:var(--image-file-goto-filled) center /contain no-repeat;background-color:var(--sys-color-primary-bright)}.computed-style-property:hover .goto{display:inline-block}.hidden{display:none}:host-context(.computed-narrow) .computed-style-property{white-space:normal;& .goto{display:none;margin-left:0}}:host-context(.computed-narrow) .property-name,\n:host-context(.computed-narrow) .property-value{display:inline-block;width:100%;max-width:100%;margin-left:0;white-space:nowrap}:host-context(.computed-narrow) .computed-style-property:not(.inherited):hover{& .property-value{margin-left:var(--goto-size)}& .goto{display:inline-block}}@media (forced-colors: active){.computed-style-property.inherited{opacity:100%}.computed-style-property:hover{forced-color-adjust:none;background-color:Highlight}.computed-style-property:hover *{color:HighlightText}.goto{background-color:HighlightText}}\n/*# sourceURL=computedStyleProperty.css */\n");const{render:I,html:B}=r;class H extends Event{static eventName="onnavigatetosource";constructor(){super(H.eventName,{bubbles:!0,composed:!0})}}class R extends HTMLElement{static litTagName=r.literal`devtools-computed-style-property`;#e=this.attachShadow({mode:"open"});#g=!1;#m=!1;connectedCallback(){this.#e.adoptedStyleSheets=[_],this.#i()}set inherited(e){e!==this.#g&&(this.#g=e,this.#i())}set traceable(e){e!==this.#m&&(this.#m=e,this.#i())}#y(){this.dispatchEvent(new H)}#i(){I(B`
      <div class="computed-style-property ${this.#g?"inherited":""}">
        <div class="property-name">
          <slot name="name"></slot>
        </div>
        <span class="hidden" aria-hidden="false">: </span>
        ${this.#m?B`<span class="goto" @click=${this.#y} jslog=${i.action("elements.jump-to-style").track({click:!0})}></span>`:null}
        <div class="property-value">
          <slot name="value"></slot>
        </div>
        <span class="hidden" aria-hidden="false">;</span>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-computed-style-property",R);var q=Object.freeze({__proto__:null,NavigateToSourceEvent:H,ComputedStyleProperty:R});const A=new CSSStyleSheet;A.replaceSync(':host{text-overflow:ellipsis;overflow:hidden;flex-grow:1}.computed-style-trace{margin-left:16px;font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}.computed-style-trace:hover{background-color:var(--sys-color-state-hover-on-subtle);cursor:text}.goto{--size:16px;display:none;cursor:pointer;position:absolute;width:var(--size);height:var(--size);margin:-1px 0 0 calc(-1 * var(--size));mask:var(--image-file-goto-filled) center /contain no-repeat;background-color:var(--sys-color-surface-variant)}.computed-style-trace:hover .goto{display:inline-block}.devtools-link{color:var(--sys-color-on-surface);text-decoration-color:var(--sys-color-token-subtle);text-decoration-line:underline;cursor:pointer}.trace-value{margin-left:16px}.computed-style-trace.inactive slot[name="trace-value"]{text-decoration:line-through}.trace-selector{--override-trace-selector-color:var(--sys-color-neutral-bright);color:var(--override-trace-selector-color);padding-left:2em}.trace-link{user-select:none;float:right;padding-left:1em;position:relative;z-index:1}@media (forced-colors: active){.computed-style-trace:hover{forced-color-adjust:none;background-color:Highlight}.goto{background-color:Highlight}.computed-style-trace:hover *{color:HighlightText}.computed-style-trace:hover .trace-selector{--override-trace-selector-color:HighlightText}}\n/*# sourceURL=computedStyleTrace.css */\n');const{render:V,html:F}=r;class U extends HTMLElement{static litTagName=r.literal`devtools-computed-style-trace`;#e=this.attachShadow({mode:"open"});#v="";#f=!1;#b=()=>{};#x;connectedCallback(){this.#e.adoptedStyleSheets=[A]}set data(e){this.#v=e.selector,this.#f=e.active,this.#b=e.onNavigateToSource,this.#x=e.ruleOriginNode,this.#i()}#i(){V(F`
      <div class="computed-style-trace ${this.#f?"active":"inactive"}">
        <span class="goto" @click=${this.#b}></span>
        <slot name="trace-value" @click=${this.#b}></slot>
        <span class="trace-selector">${this.#v}</span>
        <span class="trace-link">${this.#x}</span>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-computed-style-trace",U);var G=Object.freeze({__proto__:null,ComputedStyleTrace:U});const W=new CSSStyleSheet;W.replaceSync(":host{padding:6px}.hint-popup-wrapper{max-width:232px;font-size:12px;line-height:1.4}code{font-weight:bold;font-family:inherit}.hint-popup-possible-fix{margin-top:8px}.clickable{color:var(--sys-color-primary)}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}\n/*# sourceURL=cssHintDetailsView.css */\n");const Q={learnMore:"Learn More"},Y=e.i18n.registerUIStrings("panels/elements/components/CSSHintDetailsView.ts",Q),X=e.i18n.getLocalizedString.bind(void 0,Y),{render:K,html:J,Directives:Z}=r;class ee extends HTMLElement{static litTagName=r.literal`devtools-css-hint-details-view`;#e=this.attachShadow({mode:"open"});#w;constructor(e){super(),this.#w=e,this.#e.adoptedStyleSheets=[W],this.#i()}#i(){const e=this.#w.getLearnMoreLink();K(J`
        <div class="hint-popup-wrapper">
          <div class="hint-popup-reason">
            ${Z.unsafeHTML(this.#w.getMessage())}
          </div>
          ${this.#w.getPossibleFixMessage()?J`
              <div class="hint-popup-possible-fix">
                  ${Z.unsafeHTML(this.#w.getPossibleFixMessage())}
                  ${e?J`
                      <x-link id="learn-more" href=${e} class="clickable underlined unbreakable-text"}>
                          ${X(Q.learnMore)}
                      </x-link>
                  `:""}
              </div>
          `:""}
        </div>
      `,this.#e,{host:this})}}customElements.define("devtools-css-hint-details-view",ee);var te=Object.freeze({__proto__:null,CSSHintDetailsView:ee});const oe=new CSSStyleSheet;oe.replaceSync(":host{padding:6px}.docs-popup-wrapper{max-width:350px;font-size:12px;line-height:1.4}.docs-popup-section{margin-top:8px}.clickable{color:var(--sys-color-primary)}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}.footer{display:flex;justify-content:space-between}.dont-show input{vertical-align:bottom}\n/*# sourceURL=cssPropertyDocsView.css */\n");const re={learnMore:"Learn more",dontShow:"Don't show"},ne=e.i18n.registerUIStrings("panels/elements/components/CSSPropertyDocsView.ts",re),se=e.i18n.getLocalizedString.bind(void 0,ne),{render:ie,html:ae}=r;class le extends HTMLElement{static litTagName=r.literal`devtools-css-property-docs-view`;#e=this.attachShadow({mode:"open"});#S;constructor(e){super(),this.#S=e,this.#e.adoptedStyleSheets=[s.checkboxStyles,oe],this.#i()}#k(e){const t=!e.target.checked;a.Settings.Settings.instance().moduleSetting("show-css-property-documentation-on-hover").set(t)}#i(){const e=this.#S.description,t=this.#S.references?.[0].url;ie(ae`
      <div class="docs-popup-wrapper">
        ${e?ae`
          <div id="description">
            ${e}
          </div>
        `:r.nothing}
        ${t?ae`
          <div class="docs-popup-section footer">
            <x-link
              id="learn-more"
              href=${t}
              class="clickable underlined unbreakable-text"
            >
              ${se(re.learnMore)}
            </x-link>
            <label class="dont-show">
              <input type="checkbox" @change=${this.#k} />
              ${se(re.dontShow)}
            </label>
          </div>
        `:r.nothing}
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-css-property-docs-view",le);var ce=Object.freeze({__proto__:null,CSSPropertyDocsView:le});const de=new Set(["tb","tb-rl","vertical-lr","vertical-rl"]);function pe(e){if("left-to-right"===e)return"right-to-left";if("right-to-left"===e)return"left-to-right";if("top-to-bottom"===e)return"bottom-to-top";if("bottom-to-top"===e)return"top-to-bottom";throw new Error("Unknown PhysicalFlexDirection")}function ue(e){return{...e,"row-reverse":pe(e.row),"column-reverse":pe(e.column)}}function he(e){const t="rtl"===e.get("direction"),o=e.get("writing-mode");return ue(o&&de.has(o)?{row:t?"bottom-to-top":"top-to-bottom",column:"vertical-lr"===o?"left-to-right":"right-to-left"}:{row:t?"right-to-left":"left-to-right",column:"top-to-bottom"})}function ge(e){let t=!0,o=!1,r=-90;return"right-to-left"===e?(r=90,o=!1,t=!1):"top-to-bottom"===e?(r=0,t=!1,o=!1):"bottom-to-top"===e&&(r=0,t=!1,o=!0),{iconName:"flex-direction",rotate:r,scaleX:t?-1:1,scaleY:o?-1:1}}function me(e,t){return{iconName:e,rotate:"right-to-left"===t?90:"left-to-right"===t?-90:0,scaleX:1,scaleY:1}}function ye(e,t){return{iconName:e,rotate:"top-to-bottom"===t?90:"bottom-to-top"===t?-90:0,scaleX:"right-to-left"===t?-1:1,scaleY:1}}function ve(e,t){return{iconName:e,rotate:"top-to-bottom"===t?90:"bottom-to-top"===t?-90:0,scaleX:"right-to-left"===t?-1:1,scaleY:1}}function fe(e,t){return{iconName:e,rotate:"right-to-left"===t?90:"left-to-right"===t?-90:0,scaleX:1,scaleY:1}}function be(e){return function(t){return ge(he(t)[e])}}function xe(e){return function(t){const o=he(t),r=new Map([["column",o.row],["row",o.column],["column-reverse",o.row],["row-reverse",o.column]]),n=t.get("flex-direction")||"row",s=r.get(n);if(!s)throw new Error("Unknown direction for flex-align icon");return me(e,s)}}function we(e){return function(t){const o=he(t);return me(e,o.column)}}function Se(e){return function(t){const o=he(t);return ye(e,o[t.get("flex-direction")||"row"])}}function ke(e){return function(t){const o=he(t);return ye(e,o.row)}}function Ne(e){return function(t){const o=he(t);return ve(e,o.row)}}function $e(e){return function(t){const o=he(t),r=new Map([["column",o.row],["row",o.column],["column-reverse",o.row],["row-reverse",o.column]]),n=t.get("flex-direction")||"row",s=r.get(n);if(!s)throw new Error("Unknown direction for flex-align icon");return fe(e,s)}}function Ce(e){return function(t){const o=he(t);return fe(e,o.column)}}function Me(){return{iconName:"align-items-baseline",rotate:0,scaleX:1,scaleY:1}}function Ee(e){return function(t,o){return $e(e)(o)}}function Te(e){return function(t,o){return Ce(e)(o)}}function ze(e,t){return{iconName:e,rotate:"bottom-to-top"===t||"top-to-bottom"===t?90:0,scaleX:1,scaleY:1}}function Oe(e){return function(t){const o=he(t),r=t.get("flex-direction")||"row";return ze(e,o[r])}}const De=new Map([["flex-direction: row",be("row")],["flex-direction: column",be("column")],["flex-direction: column-reverse",be("column-reverse")],["flex-direction: row-reverse",be("row-reverse")],["flex-direction: initial",be("row")],["flex-direction: unset",be("row")],["flex-direction: revert",be("row")],["align-content: center",xe("align-content-center")],["align-content: space-around",xe("align-content-space-around")],["align-content: space-between",xe("align-content-space-between")],["align-content: stretch",xe("align-content-stretch")],["align-content: space-evenly",xe("align-content-space-evenly")],["align-content: flex-end",xe("align-content-end")],["align-content: flex-start",xe("align-content-start")],["align-content: start",xe("align-content-start")],["align-content: end",xe("align-content-end")],["align-content: normal",xe("align-content-stretch")],["align-content: revert",xe("align-content-stretch")],["align-content: unset",xe("align-content-stretch")],["align-content: initial",xe("align-content-stretch")],["justify-content: center",Se("justify-content-center")],["justify-content: space-around",Se("justify-content-space-around")],["justify-content: space-between",Se("justify-content-space-between")],["justify-content: space-evenly",Se("justify-content-space-evenly")],["justify-content: flex-end",Se("justify-content-end")],["justify-content: flex-start",Se("justify-content-start")],["align-items: stretch",$e("align-items-stretch")],["align-items: flex-end",$e("align-items-end")],["align-items: flex-start",$e("align-items-start")],["align-items: center",$e("align-items-center")],["align-items: baseline",Me],["align-content: baseline",Me],["flex-wrap: wrap",Oe("flex-wrap")],["flex-wrap: nowrap",Oe("flex-no-wrap")]]),Le=new Map([["align-self: baseline",Me],["align-self: center",Ee("align-self-center")],["align-self: flex-start",Ee("align-self-start")],["align-self: flex-end",Ee("align-self-end")],["align-self: stretch",Ee("align-self-stretch")]]),je=new Map([["align-content: center",we("align-content-center")],["align-content: space-around",we("align-content-space-around")],["align-content: space-between",we("align-content-space-between")],["align-content: stretch",we("align-content-stretch")],["align-content: space-evenly",we("align-content-space-evenly")],["align-content: end",we("align-content-end")],["align-content: start",we("align-content-start")],["align-content: baseline",Me],["justify-content: center",ke("justify-content-center")],["justify-content: space-around",ke("justify-content-space-around")],["justify-content: space-between",ke("justify-content-space-between")],["justify-content: space-evenly",ke("justify-content-space-evenly")],["justify-content: end",ke("justify-content-end")],["justify-content: start",ke("justify-content-start")],["align-items: stretch",Ce("align-items-stretch")],["align-items: end",Ce("align-items-end")],["align-items: start",Ce("align-items-start")],["align-items: center",Ce("align-items-center")],["align-items: baseline",Me],["justify-items: center",Ne("justify-items-center")],["justify-items: stretch",Ne("justify-items-stretch")],["justify-items: end",Ne("justify-items-end")],["justify-items: start",Ne("justify-items-start")],["justify-items: baseline",Me]]),Pe=new Map([["align-self: baseline",Me],["align-self: center",Te("align-self-center")],["align-self: start",Te("align-self-start")],["align-self: end",Te("align-self-end")],["align-self: stretch",Te("align-self-stretch")]]),_e=e=>{const t=e?.get("display");return"flex"===t||"inline-flex"===t},Ie=e=>{const t=e?.get("display");return"grid"===t||"inline-grid"===t};function Be(e,t){const o=De.get(e);return o?o(t||new Map):null}function He(e,t,o){const r=Le.get(e);return r?r(t||new Map,o||new Map):null}function Re(e,t){const o=je.get(e);return o?o(t||new Map):null}function qe(e,t,o){const r=Pe.get(e);return r?r(t||new Map,o||new Map):null}var Ae=Object.freeze({__proto__:null,reverseDirection:pe,getPhysicalDirections:he,rotateFlexDirectionIcon:ge,rotateAlignContentIcon:me,rotateJustifyContentIcon:ye,rotateJustifyItemsIcon:ve,rotateAlignItemsIcon:fe,roateFlexWrapIcon:ze,findIcon:function(e,t,o){if(_e(t)){const o=Be(e,t);if(o)return o}if(_e(o)){const r=He(e,t,o);if(r)return r}if(Ie(t)){const o=Re(e,t);if(o)return o}if(Ie(o)){const r=qe(e,t,o);if(r)return r}return null},findFlexContainerIcon:Be,findFlexItemIcon:He,findGridContainerIcon:Re,findGridItemIcon:qe});const Ve=new CSSStyleSheet;Ve.replaceSync('*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-divider-border:1px solid var(--sys-color-divider);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--sys-motion-duration-short4:200ms;--sys-motion-duration-medium2:300ms;--sys-motion-duration-long2:500ms;--sys-motion-easing-emphasized:cubic-bezier(0.2,0,0,1);--sys-motion-easing-emphasized-decelerate:cubic-bezier(0.05,0.7,0.1,1);--sys-motion-easing-emphasized-accelerate:cubic-bezier(0.2,0,0,1)}.-theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{--default-font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}.platform-linux{--default-font-family:"Google Sans Text","Google Sans",system-ui,sans-serif}.platform-mac{--default-font-family:system-ui,sans-serif}.platform-windows{--default-font-family:system-ui,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:monospace;--source-code-font-size:12px;--source-code-font-family:monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace;--source-code-font-size:11px;--source-code-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.-theme-with-dark-background input::placeholder,\n:host-context(.-theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}input[type="checkbox"]:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input):not(:invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input):not(:invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.chrome-select{appearance:none;user-select:none;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--sys-color-surface);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.-theme-with-dark-background .chrome-select,\n:host-context(.-theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.-theme-with-dark-background .chrome-select,\n  :host-context(.-theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.-theme-with-dark-background .chrome-select,\n  :host-context(.-theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--sys-color-token-subtle)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-tonal-container)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{--override-link-focus-background-color:rgb(0 0 0/8%);background-color:var(--override-link-focus-background-color);border-radius:2px}.-theme-with-dark-background button.link:focus-visible,\n:host-context(.-theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.-theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-focus-border-color:var(--sys-color-state-focus-ring);--override-adorner-active-background-color:var(--sys-color-state-riple-neutral-on-subtle)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}\n/*# sourceURL=inspectorCommon.css */\n');const Fe=new CSSStyleSheet;Fe.replaceSync(".query:not(.editing-query){overflow:hidden}.editable .query-text{color:var(--sys-color-on-surface)}.editable .query-text:hover{text-decoration:var(--override-styles-section-text-hover-text-decoration);cursor:var(--override-styles-section-text-hover-cursor)}\n/*# sourceURL=cssQuery.css */\n");const{render:Ue,html:Ge}=r;class We extends HTMLElement{static litTagName=r.literal`devtools-css-query`;#e=this.attachShadow({mode:"open"});#N="";#$;#C="";#M;set data(e){this.#N=e.queryPrefix,this.#$=e.queryName,this.#C=e.queryText,this.#M=e.onQueryTextClick,this.#i()}connectedCallback(){this.#e.adoptedStyleSheets=[Fe,Ve]}#i(){const e=r.Directives.classMap({query:!0,editable:Boolean(this.#M)}),t=Ge`
      <span class="query-text" @click=${this.#M}>${this.#C}</span>
    `;Ue(Ge`
      <div class=${e}>
        <slot name="indent"></slot>${this.#N?Ge`<span>${this.#N+" "}</span>`:r.nothing}${this.#$?Ge`<span>${this.#$+" "}</span>`:r.nothing}${t} {
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-css-query",We);var Qe=Object.freeze({__proto__:null,CSSQuery:We});const Ye=new CSSStyleSheet;Ye.replaceSync(".registered-property-popup-wrapper{max-width:232px;font-size:12px;line-height:1.4}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}:host{padding:11px 7px}.divider{margin:8px -7px;border:1px solid var(--sys-color-divider)}.registered-property-links{margin-top:8px}.clickable{color:var(--sys-color-primary);cursor:pointer}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}.css-property{color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.title{color:var(--sys-color-state-disabled)}\n/*# sourceURL=cssVariableValueView.css */\n");const Xe={registeredPropertyLinkTitle:"View registered property",invalidPropertyValue:"Invalid property value, expected type {type}",sIsNotDefined:"{PH1} is not defined"},Ke=e.i18n.registerUIStrings("panels/elements/components/CSSVariableValueView.ts",Xe),Je=e.i18n.getLocalizedString.bind(void 0,Ke),Ze=r.i18nTemplate.bind(void 0,Ke),{render:et,html:tt}=r;function ot(e){return tt`<div class="registered-property-links">
            <span role="button" @click=${e?.goToDefinition} class="clickable underlined unbreakable-text"}>
              ${Je(Xe.registeredPropertyLinkTitle)}
            </span>
          </div>`}class rt extends HTMLElement{static litTagName=r.literal`devtools-css-variable-parser-error`;#e=this.attachShadow({mode:"open"});constructor(e){super(),this.#e.adoptedStyleSheets=[Ye],this.#i(e)}#i(e){const t=tt`<span class="monospace css-property">${e.registration.syntax()}</span>`;et(tt`
      <div class="variable-value-popup-wrapper">
        ${Ze(Xe.invalidPropertyValue,{type:t})}
        ${ot(e)}
      </div>`,this.#e,{host:this})}}class nt extends HTMLElement{static litTagName=r.literal`devtools-css-variable-value-view`;#e=this.attachShadow({mode:"open"});variableName;value;details;constructor({variableName:e,value:t,details:o}){super(),this.#e.adoptedStyleSheets=[Ye],this.variableName=e,this.value=t,this.details=o,this.#i()}#i(){const e=this.details?.registration.initialValue(),t=this.details?tt`
        <hr class=divider />
        <div class=registered-property-popup-wrapper>
          <div class="monospace">
            <div><span class="css-property">syntax:</span> ${this.details.registration.syntax()}</div>
            <div><span class="css-property">inherits:</span> ${this.details.registration.inherits()}</div>
            ${e?tt`<div><span class="css-property">initial-value:</span> ${e}</div>`:""}
          </div>
          ${ot(this.details)}
        </div>`:"",o=this.value??Je(Xe.sIsNotDefined,{PH1:this.variableName});et(tt`<div class="variable-value-popup-wrapper">
               ${o}
             </div>
             ${t}
             `,this.#e,{host:this})}}customElements.define("devtools-css-variable-value-view",nt),customElements.define("devtools-css-variable-parser-error",rt);var st=Object.freeze({__proto__:null,CSSVariableParserError:rt,CSSVariableValueView:nt});const it=new CSSStyleSheet;it.replaceSync(":host{--override-node-text-label-color:var(--sys-color-token-tag);--override-node-text-class-color:var(--sys-color-token-attribute);--override-node-text-id-color:var(--sys-color-token-attribute);--override-node-text-multiple-descriptors-id:var(--sys-color-on-surface);--override-node-text-multiple-descriptors-class:var(--sys-color-token-property)}.crumbs{display:inline-flex;align-items:stretch;width:100%;overflow:hidden;pointer-events:auto;cursor:default;white-space:nowrap;position:relative;background:var(--sys-color-cdt-base-container);font-size:inherit;font-family:inherit}.crumbs-window{flex-grow:2;overflow:hidden}.crumbs-scroll-container{display:inline-flex;margin:0;padding:0}.crumb{display:block;padding:0 7px;line-height:23px;white-space:nowrap}.overflow{padding:0 5px;font-weight:bold;display:block;border:none;flex-grow:0;flex-shrink:0;text-align:center;background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-token-subtle);margin:1px;outline:1px solid var(--sys-color-neutral-outline)}.overflow.hidden{display:none}.overflow:disabled{opacity:50%}.overflow:focus{outline-color:var(--sys-color-primary)}.overflow:not(:disabled):hover{background-color:var(--sys-color-state-hover-on-subtle);color:var(--sys-color-on-surface)}.crumb-link{text-decoration:none;color:inherit}.crumb:hover{background:var(--sys-color-state-hover-on-subtle)}.crumb.selected{background:var(--sys-color-tonal-container)}.crumb:focus{outline:var(--sys-color-primary) auto 1px}\n/*# sourceURL=elementsBreadcrumbs.css */\n");const at={text:"(text)"},lt=e.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbsUtils.ts",at),ct=e.i18n.getLocalizedString.bind(void 0,lt),dt=(e,t)=>t?e.filter((e=>e.nodeType!==Node.DOCUMENT_NODE)).map((e=>({title:ut(e),selected:e.id===t.id,node:e,originalNode:e.legacyDomNode}))).reverse():[],pt=(e,t={})=>({main:e,extras:t}),ut=e=>{switch(e.nodeType){case Node.ELEMENT_NODE:{if(e.pseudoType)return pt("::"+e.pseudoType);const t=pt(e.nodeNameNicelyCased),o=e.getAttribute("id");o&&(t.extras.id=o);const r=e.getAttribute("class");if(r){const e=new Set(r.split(/\s+/));t.extras.classes=Array.from(e)}return t}case Node.TEXT_NODE:return pt(ct(at.text));case Node.COMMENT_NODE:return pt("\x3c!--\x3e");case Node.DOCUMENT_TYPE_NODE:return pt("<!doctype>");case Node.DOCUMENT_FRAGMENT_NODE:return pt(e.shadowRootType?"#shadow-root":e.nodeNameNicelyCased);default:return pt(e.nodeNameNicelyCased)}};var ht=Object.freeze({__proto__:null,crumbsToRender:dt,determineElementTitle:ut});const gt={breadcrumbs:"DOM tree breadcrumbs",scrollLeft:"Scroll left",scrollRight:"Scroll right"},mt=e.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbs.ts",gt),yt=e.i18n.getLocalizedString.bind(void 0,mt);class vt extends Event{static eventName="breadcrumbsnodeselected";legacyDomNode;constructor(e){super(vt.eventName,{}),this.legacyDomNode=e.legacyDomNode}}const ft=o.RenderCoordinator.RenderCoordinator.instance();class bt extends HTMLElement{static litTagName=r.literal`devtools-elements-breadcrumbs`;#e=this.attachShadow({mode:"open"});#E=new ResizeObserver((()=>this.#T()));#z=this.#i.bind(this);#O=[];#D=null;#L=!1;#j="start";#P=!1;#_=!1;connectedCallback(){this.#e.adoptedStyleSheets=[it]}set data(e){this.#D=e.selectedNode,this.#O=e.crumbs,this.#_=!1,l.ScheduledRender.scheduleRender(this,this.#z)}disconnectedCallback(){this.#P=!1,this.#E.disconnect()}#I(e){return t=>{t.preventDefault(),this.dispatchEvent(new vt(e))}}async#T(){const e=this.#e.querySelector(".crumbs-scroll-container"),t=this.#e.querySelector(".crumbs-window");if(!e||!t)return;const o=await ft.read((()=>t.clientWidth)),r=await ft.read((()=>e.clientWidth));this.#L?r<o&&(this.#L=!1):r>o&&(this.#L=!0),this.#B(),this.#H(t)}#R(e){return()=>e.highlightNode()}#q(e){return()=>e.clearHighlight()}#A(e){return()=>e.highlightNode()}#V(e){return()=>e.clearHighlight()}#F(){if(!this.#E||!0===this.#P)return;const e=this.#e.querySelector(".crumbs");e&&(this.#E.observe(e),this.#P=!0)}async#U(){const e=this.#e.querySelector(".crumbs-scroll-container"),t=this.#e.querySelector(".crumbs-window");if(!e||!t)return;const o=await ft.read((()=>t.clientWidth)),r=await ft.read((()=>e.clientWidth));this.#L?r<o&&(this.#L=!1,this.#i()):r>o&&(this.#L=!0,this.#i())}#G(e){if(!e.target)return;const t=e.target;this.#H(t)}#H(e){const t=e.scrollWidth-e.clientWidth,o=e.scrollLeft;this.#j=o<10?"start":o>=t-10?"end":"middle",this.#i()}#W(e){return()=>{this.#_=!0;const t=this.#e.querySelector(".crumbs-window");if(!t)return;const o=t.clientWidth/2,r="left"===e?Math.max(Math.floor(t.scrollLeft-o),0):t.scrollLeft+o;t.scrollTo({behavior:"smooth",left:r})}}#Q(e,t){const o=r.Directives.classMap({overflow:!0,[e]:!0,hidden:!this.#L}),n=yt("left"===e?gt.scrollLeft:gt.scrollRight);return r.html`
      <button
        class=${o}
        @click=${this.#W(e)}
        ?disabled=${t}
        aria-label=${n}
        title=${n}>
        <${c.Icon.Icon.litTagName} .data=${{iconName:"triangle-"+e,color:"var(--sys-color-on-surface)",width:"12px",height:"10px"}}>
        </${c.Icon.Icon.litTagName}>
      </button>
      `}#i(){const e=dt(this.#O,this.#D);r.render(r.html`
      <nav class="crumbs" aria-label=${yt(gt.breadcrumbs)} jslog=${i.elementsBreadcrumbs()}>
        ${this.#Q("left","start"===this.#j)}

        <div class="crumbs-window" @scroll=${this.#G}>
          <ul class="crumbs-scroll-container">
            ${e.map((e=>{const t={crumb:!0,selected:e.selected};return r.html`
                <li class=${r.Directives.classMap(t)}
                  data-node-id=${e.node.id}
                  data-crumb="true"
                >
                  <a href="#"
                    draggable=false
                    class="crumb-link"
                    jslog=${i.item().track({click:!0})}
                    @click=${this.#I(e.node)}
                    @mousemove=${this.#R(e.node)}
                    @mouseleave=${this.#q(e.node)}
                    @focus=${this.#A(e.node)}
                    @blur=${this.#V(e.node)}
                  ><${d.NodeText.NodeText.litTagName} data-node-title=${e.title.main} .data=${{nodeTitle:e.title.main,nodeId:e.title.extras.id,nodeClasses:e.title.extras.classes}}></${d.NodeText.NodeText.litTagName}></a>
                </li>`}))}
          </ul>
        </div>
        ${this.#Q("right","end"===this.#j)}
      </nav>
    `,this.#e,{host:this}),this.#U(),this.#F(),this.#B()}async#B(){if(!this.#D||!this.#e||!this.#L||this.#_)return;const e=this.#D.id,t=this.#e.querySelector(`.crumb[data-node-id="${e}"]`);t&&await ft.scroll((()=>{t.scrollIntoView({behavior:"auto"})}))}}customElements.define("devtools-elements-breadcrumbs",bt);var xt=Object.freeze({__proto__:null,NodeSelectedEvent:vt,ElementsBreadcrumbs:bt});const wt=new CSSStyleSheet;wt.replaceSync(":host{display:inline-flex;vertical-align:middle}:host(.hidden){display:none}.expand-button{display:inline-flex;justify-content:center;align-items:center;box-sizing:border-box;width:14px;height:10px;margin:0 2px;border:1px solid var(--override-adorner-border-color,var(--sys-color-neutral-outline));border-radius:10px;background:var(--override-adorner-background-color,var(--sys-color-neutral-container))}.expand-button:hover{background:var(--sys-color-state-hover-dim-blend-protection)}.dot{height:2px;width:2px;background:var(--text-primary);border-radius:50%}.dot + .dot{margin-left:1px}\n/*# sourceURL=elementsTreeExpandButton.css */\n");class St extends HTMLElement{static litTagName=r.literal`devtools-elements-tree-expand-button`;#e=this.attachShadow({mode:"open"});#Y=()=>{};set data(e){this.#Y=e.clickHandler,this.#X()}#X(){this.#i()}connectedCallback(){this.#e.adoptedStyleSheets=[wt]}#i(){r.render(r.html`<span
        class="expand-button"
        @click=${this.#Y}><span class="dot"></span><span class="dot"></span><span class="dot"></span></span>`,this.#e,{host:this})}}customElements.define("devtools-elements-tree-expand-button",St);var kt=Object.freeze({__proto__:null,ElementsTreeExpandButton:St});const Nt=e=>({parentNode:e.parentNode?Nt(e.parentNode):null,id:e.id,nodeType:e.nodeType(),pseudoType:e.pseudoType(),shadowRootType:e.shadowRootType(),nodeName:e.nodeName(),nodeNameNicelyCased:e.nodeNameInCorrectCase(),legacyDomNode:e,highlightNode:t=>e.highlight(t),clearHighlight:()=>p.OverlayModel.OverlayModel.hideDOMNodeHighlight(),getAttribute:e.getAttribute.bind(e)});var $t=Object.freeze({__proto__:null,legacyNodeToElementsComponentsNode:Nt});const Ct=new CSSStyleSheet;Ct.replaceSync('*{box-sizing:border-box;font-size:12px}.header{background-color:var(--sys-color-surface2);border-bottom:1px solid var(--sys-color-divider);line-height:1.6;overflow:hidden;padding:0 5px;white-space:nowrap}.header::marker{color:var(--sys-color-on-surface-subtle);font-size:11px;line-height:1}.header:focus{background-color:var(--sys-color-tonal-container)}.content-section{padding:16px;border-bottom:1px solid var(--sys-color-divider);overflow-x:hidden}.content-section-title{font-size:12px;font-weight:500;line-height:1.1;margin:0;padding:0}.checkbox-settings{margin-top:8px;display:grid;grid-template-columns:1fr;gap:5px}.checkbox-label{display:flex;flex-direction:row;align-items:center;min-width:40px;width:fit-content}.checkbox-settings .checkbox-label{margin-bottom:8px}.checkbox-settings .checkbox-label:last-child{margin-bottom:0}.checkbox-label input{margin:0 6px 0 0;padding:0;flex:none}.checkbox-label input:focus{outline:auto 5px -webkit-focus-ring-color}.checkbox-label > span{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.select-settings{margin-top:16px;width:fit-content}.select-label{display:flex;flex-direction:column}.select-label span{margin-bottom:4px}.elements{margin-top:12px;color:var(--sys-color-token-tag);display:grid;grid-template-columns:repeat(auto-fill,minmax(min(250px,100%),1fr));gap:8px}.element{display:flex;flex-direction:row;align-items:center;gap:8px}.show-element{flex:none}.chrome-select{min-width:0;max-width:150px}.color-picker{opacity:0%}.color-picker-label{border:1px solid var(--sys-color-neutral-outline);cursor:default;display:inline-block;flex:none;height:10px;width:10px;position:relative;&:focus-within{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:2px}}.color-picker-label input[type="color"]{width:100%;height:100%;position:absolute}.color-picker-label:hover,\n.color-picker-label:focus{border:1px solid var(--sys-color-outline);transform:scale(1.2)}.node-text-container{line-height:16px;padding:0 0.5ex;border-radius:5px}.node-text-container:hover{background-color:var(--sys-color-state-hover-on-subtle)}\n/*# sourceURL=layoutPane.css */\n');const Mt={chooseElementOverlayColor:"Choose the overlay color for this element",showElementInTheElementsPanel:"Show element in the Elements panel",grid:"Grid",overlayDisplaySettings:"Overlay display settings",gridOverlays:"Grid overlays",noGridLayoutsFoundOnThisPage:"No grid layouts found on this page",flexbox:"Flexbox",flexboxOverlays:"Flexbox overlays",noFlexboxLayoutsFoundOnThisPage:"No flexbox layouts found on this page",colorPickerOpened:"Color picker opened."},Et=e.i18n.registerUIStrings("panels/elements/components/LayoutPane.ts",Mt),Tt=e.i18n.getLocalizedString.bind(void 0,Et),{render:zt,html:Ot}=r,Dt=e=>{const t=e.getAttribute("class");return{id:e.id,color:"var(--sys-color-inverse-surface)",name:e.localName(),domId:e.getAttribute("id"),domClasses:t?t.split(/\s+/).filter((e=>Boolean(e))):void 0,enabled:!1,reveal:()=>{a.Revealer.reveal(e),e.scrollIntoView()},highlight:()=>{e.highlight()},hideHighlight:()=>{p.OverlayModel.OverlayModel.hideDOMNodeHighlight()},toggle:e=>{throw new Error("Not implemented")},setColor(e){throw new Error("Not implemented")}}},Lt=e=>e.map((e=>{const t=Dt(e),o=e.id;return{...t,color:e.domModel().overlayModel().colorOfGridInPersistentOverlay(o)||"var(--sys-color-inverse-surface)",enabled:e.domModel().overlayModel().isHighlightedGridInPersistentOverlay(o),toggle:t=>{t?e.domModel().overlayModel().highlightGridInPersistentOverlay(o):e.domModel().overlayModel().hideGridInPersistentOverlay(o)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfGridInPersistentOverlay(o,t)}}})),jt=e=>e.map((e=>{const t=Dt(e),o=e.id;return{...t,color:e.domModel().overlayModel().colorOfFlexInPersistentOverlay(o)||"var(--sys-color-inverse-surface)",enabled:e.domModel().overlayModel().isHighlightedFlexContainerInPersistentOverlay(o),toggle:t=>{t?e.domModel().overlayModel().highlightFlexContainerInPersistentOverlay(o):e.domModel().overlayModel().hideFlexContainerInPersistentOverlay(o)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfFlexInPersistentOverlay(o,t)}}}));function Pt(e){return"enum"===e.type}function _t(e){return"boolean"===e.type}const It=o.RenderCoordinator.RenderCoordinator.instance();let Bt;class Ht extends u.LegacyWrapper.WrappableComponent{static litTagName=r.literal`devtools-layout-pane`;#e=this.attachShadow({mode:"open"});#u=[];#K;#J;constructor(){super(),this.#u=this.#Z(),this.#K=a.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom"),this.#J=[],this.#e.adoptedStyleSheets=[s.checkboxStyles,Ct,Ve]}static instance(){return Bt||(Bt=u.LegacyWrapper.legacyWrapper(h.Widget.Widget,new Ht)),Bt.element.style.minWidth="min-content",Bt.element.setAttribute("jslog",`${i.pane("layout").track({resize:!0})}`),Bt.getComponent()}modelAdded(e){const t=e.overlayModel();t.addEventListener("PersistentGridOverlayStateChanged",this.render,this),t.addEventListener("PersistentFlexContainerOverlayStateChanged",this.render,this),this.#J.push(e)}modelRemoved(e){const t=e.overlayModel();t.removeEventListener("PersistentGridOverlayStateChanged",this.render,this),t.removeEventListener("PersistentFlexContainerOverlayStateChanged",this.render,this),this.#J=this.#J.filter((t=>t!==e))}async#ee(e){const t=this.#K.get(),o=[];for(const r of this.#J)try{const n=await r.getNodesByStyle(e,!0);for(const e of n){const n=r.nodeForId(e);null===n||!t&&n.ancestorUserAgentShadowRoot()||o.push(n)}}catch(e){console.warn(e)}return o}async#te(){return await this.#ee([{name:"display",value:"grid"},{name:"display",value:"inline-grid"}])}async#oe(){return await this.#ee([{name:"display",value:"flex"},{name:"display",value:"inline-flex"}])}#Z(){const e=[];for(const t of["show-grid-line-labels","show-grid-track-sizes","show-grid-areas","extend-grid-lines"]){const o=a.Settings.Settings.instance().moduleSetting(t),r=o.get(),n=o.type();if(!n)throw new Error("A setting provided to LayoutSidebarPane does not have a setting type");if("boolean"!==n&&"enum"!==n)throw new Error("A setting provided to LayoutSidebarPane does not have a supported setting type");const s={type:n,name:o.name,title:o.title()};("boolean"==typeof r||"string"==typeof r)&&e.push({...s,value:r,options:o.options().map((e=>({...e,value:e.value})))})}return e}onSettingChanged(e,t){a.Settings.Settings.instance().moduleSetting(e).set(t)}wasShown(){for(const e of this.#u)a.Settings.Settings.instance().moduleSetting(e.name).addChangeListener(this.render,this);for(const e of this.#J)this.modelRemoved(e);this.#J=[],p.TargetManager.TargetManager.instance().observeModels(p.DOMModel.DOMModel,this,{scoped:!0}),h.Context.Context.instance().addFlavorChangeListener(p.DOMModel.DOMNode,this.render,this),this.#K.addChangeListener(this.render,this),this.render()}willHide(){for(const e of this.#u)a.Settings.Settings.instance().moduleSetting(e.name).removeChangeListener(this.render,this);p.TargetManager.TargetManager.instance().unobserveModels(p.DOMModel.DOMModel,this),h.Context.Context.instance().removeFlavorChangeListener(p.DOMModel.DOMNode,this.render,this),this.#K.removeChangeListener(this.render,this)}#re(e){if(!e.target)return;const t=e.target.parentElement;if(!t)throw new Error("<details> element is not found for a <summary> element");switch(e.key){case"ArrowLeft":t.open=!1;break;case"ArrowRight":t.open=!0}}async render(){const e=Lt(await this.#te()),t=jt(await this.#oe());await It.write("LayoutPane render",(()=>{zt(Ot`
        <details open>
          <summary class="header" @keydown=${this.#re} jslog=${i.sectionHeader("grid-settings").track({click:!0})}>
            ${Tt(Mt.grid)}
          </summary>
          <div class="content-section" jslog=${i.section("grid-settings")}>
            <h3 class="content-section-title">${Tt(Mt.overlayDisplaySettings)}</h3>
            <div class="select-settings">
              ${this.#ne().map((e=>this.#se(e)))}
            </div>
            <div class="checkbox-settings">
              ${this.#ie().map((e=>this.#ae(e)))}
            </div>
          </div>
          ${e?Ot`<div class="content-section" jslog=${i.section("grid-overlays")}>
              <h3 class="content-section-title">
                ${e.length?Tt(Mt.gridOverlays):Tt(Mt.noGridLayoutsFoundOnThisPage)}
              </h3>
              ${e.length?Ot`<div class="elements">
                  ${e.map((e=>this.#le(e)))}
                </div>`:""}
            </div>`:""}
        </details>
        ${void 0!==t?Ot`
          <details open>
            <summary class="header" @keydown=${this.#re} jslog=${i.sectionHeader("flexbox-overlays").track({click:!0})}>
              ${Tt(Mt.flexbox)}
            </summary>
            ${t?Ot`<div class="content-section" jslog=${i.section("flexbox-overlays")}>
                <h3 class="content-section-title">
                  ${t.length?Tt(Mt.flexboxOverlays):Tt(Mt.noFlexboxLayoutsFoundOnThisPage)}
                </h3>
                ${t.length?Ot`<div class="elements">
                    ${t.map((e=>this.#le(e)))}
                  </div>`:""}
              </div>`:""}
          </details>
          `:""}
      `,this.#e,{host:this})}))}#ne(){return this.#u.filter(Pt)}#ie(){return this.#u.filter(_t)}#ce(e,t){t.preventDefault(),this.onSettingChanged(e.name,t.target.checked)}#de(e,t){t.preventDefault(),this.onSettingChanged(e.name,t.target.value)}#pe(e,t){t.preventDefault(),e.toggle(t.target.checked)}#ue(e,t){t.preventDefault(),e.reveal()}#he(e,t){t.preventDefault(),e.setColor(t.target.value),this.render()}#ge(e,t){t.preventDefault(),e.highlight()}#me(e,t){t.preventDefault(),e.hideHighlight()}#le(e){const t=this.#pe.bind(this,e),o=this.#ue.bind(this,e),r=this.#he.bind(this,e),s=this.#ge.bind(this,e),a=this.#me.bind(this,e);return Ot`<div class="element" jslog=${i.item()}>
      <label data-element="true" class="checkbox-label">
        <input data-input="true" type="checkbox" .checked=${e.enabled} @change=${t} jslog=${i.toggle().track({click:!0})} />
        <span class="node-text-container" data-label="true" @mouseenter=${s} @mouseleave=${a}>
          <${d.NodeText.NodeText.litTagName} .data=${{nodeId:e.domId,nodeTitle:e.name,nodeClasses:e.domClasses}}></${d.NodeText.NodeText.litTagName}>
        </span>
      </label>
      <label @keyup=${e=>{if("Enter"!==e.key&&" "!==e.key)return;e.target.querySelector("input").click(),h.ARIAUtils.alert(Tt(Mt.colorPickerOpened)),e.preventDefault()}} @keydown=${e=>{" "===e.key&&e.preventDefault()}} class="color-picker-label" style="background: ${e.color};" jslog=${i.showStyleEditor("color").track({click:!0})}>
        <input @change=${r} @input=${r} title=${Tt(Mt.chooseElementOverlayColor)} tabindex="0" class="color-picker" type="color" value=${e.color} />
      </label>
      <${n.Button.Button.litTagName} class="show-element"
                                           title=${Tt(Mt.showElementInTheElementsPanel)}
                                           .iconName=${"select-element"}
                                           .jslogContext=${"elements.select-element"}
                                           .size=${"SMALL"}
                                           .variant=${"round"}
                                           @click=${o}></${n.Button.Button.litTagName}>
    </div>`}#ae(e){const t=this.#ce.bind(this,e);return Ot`<label data-boolean-setting="true" class="checkbox-label" title=${e.title} jslog=${i.toggle().track({click:!0}).context(e.name)}>
      <input data-input="true" type="checkbox" .checked=${e.value} @change=${t} />
      <span data-label="true">${e.title}</span>
    </label>`}#se(e){const o=this.#de.bind(this,e);return Ot`<label data-enum-setting="true" class="select-label" title=${e.title}>
      <select
        class="chrome-select"
        data-input="true"
        jslog=${i.dropDown().track({change:!0}).context(e.name)}
        @change=${o}>
        ${e.options.map((o=>Ot`<option value=${o.value} .selected=${e.value===o.value} jslog=${i.item(t.StringUtilities.toKebabCase(o.value)).track({click:!0})}>${o.title}</option>`))}
      </select>
    </label>`}}customElements.define("devtools-layout-pane",Ht);var Rt=Object.freeze({__proto__:null,LayoutPane:Ht}),qt=Object.freeze({__proto__:null});const At=new CSSStyleSheet;At.replaceSync(".container-link{display:inline-block;color:var(--sys-color-state-disabled)}.container-link:hover{color:var(--sys-color-primary)}.queried-size-details{color:var(--sys-color-on-surface)}.axis-icon{margin-left:0.4em;width:16px;height:12px;vertical-align:text-top}.axis-icon.hidden{display:none}.axis-icon.vertical{transform:rotate(90deg)}\n/*# sourceURL=queryContainer.css */\n");const{render:Vt,html:Ft}=r,{PhysicalAxis:Ut,QueryAxis:Gt}=p.CSSContainerQuery;class Wt extends Event{static eventName="queriedsizerequested";constructor(){super(Wt.eventName,{})}}class Qt extends HTMLElement{static litTagName=r.literal`devtools-query-container`;#e=this.attachShadow({mode:"open"});#$;#ye;#ve;#fe=!1;#be;set data(e){this.#$=e.queryName,this.#ye=e.container,this.#ve=e.onContainerLinkClick,this.#i()}connectedCallback(){this.#e.adoptedStyleSheets=[At]}updateContainerQueriedSizeDetails(e){this.#be=e,this.#i()}async#xe(){this.#ye?.highlightNode("container-outline"),this.#fe=!0,this.dispatchEvent(new Wt)}#we(){this.#ye?.clearHighlight(),this.#fe=!1,this.#i()}#i(){if(!this.#ye)return;let e,t;this.#$||(e=this.#ye.getAttribute("id"),t=this.#ye.getAttribute("class")?.split(/\s+/).filter(Boolean));const o=this.#$||this.#ye.nodeNameNicelyCased;Vt(Ft`
      →
      <a href="#"
        draggable=false
        class="container-link"
        @click=${this.#ve}
        @mouseenter=${this.#xe}
        @mouseleave=${this.#we}
      ><${d.NodeText.NodeText.litTagName}
          data-node-title=${o}
          .data=${{nodeTitle:o,nodeId:e,nodeClasses:t}}></${d.NodeText.NodeText.litTagName}></a>
      ${this.#fe?this.#Se():r.nothing}
    `,this.#e,{host:this})}#Se(){if(!this.#be||""===this.#be.queryAxis)return r.nothing;const e="size"===this.#be.queryAxis,t=r.Directives.classMap({"axis-icon":!0,hidden:e,vertical:"Vertical"===this.#be.physicalAxis});return Ft`
      <span class="queried-size-details">
        (${this.#be.queryAxis}<${c.Icon.Icon.litTagName}
          class=${t} .data=${{iconName:"width",color:"var(--icon-default)"}}></${c.Icon.Icon.litTagName}>)
        ${e&&this.#be.width?"width:":r.nothing}
        ${this.#be.width||r.nothing}
        ${e&&this.#be.height?"height:":r.nothing}
        ${this.#be.height||r.nothing}
      </span>
    `}}customElements.define("devtools-query-container",Qt);var Yt=Object.freeze({__proto__:null,QueriedSizeRequestedEvent:Wt,QueryContainer:Qt});const Xt=new CSSStyleSheet;Xt.replaceSync(".container{padding:12px;min-width:170px}.row{padding:0;color:var(--sys-color-on-surface);padding-bottom:16px}.row:last-child{padding-bottom:0}.property{padding-bottom:4px;white-space:nowrap}.property-name{color:var(--sys-color-token-property-special)}.property-value{color:var(--sys-color-on-surface)}.property-value.not-authored{color:var(--sys-color-state-disabled)}.buttons{display:flex;flex-direction:row}.buttons > :first-child{border-radius:3px 0 0 3px}.buttons > :last-child{border-radius:0 3px 3px 0}.button{border:1px solid var(--sys-color-neutral-outline);background-color:var(--sys-color-cdt-base-container);width:24px;height:24px;min-width:24px;min-height:24px;padding:0;margin:0;display:flex;justify-content:center;align-items:center;cursor:pointer}.button:focus-visible{outline:auto 5px -webkit-focus-ring-color}.button devtools-icon{color:var(--icon-default)}.button:hover devtools-icon{color:var(--icon-default-hover)}.button.selected devtools-icon{color:var(--icon-toggled)}\n/*# sourceURL=stylePropertyEditor.css */\n");const Kt={selectButton:"Add {propertyName}: {propertyValue}",deselectButton:"Remove {propertyName}: {propertyValue}"},Jt=e.i18n.registerUIStrings("panels/elements/components/StylePropertyEditor.ts",Kt),Zt=e.i18n.getLocalizedString.bind(void 0,Jt),{render:eo,html:to,Directives:oo}=r;class ro extends Event{static eventName="propertyselected";data;constructor(e,t){super(ro.eventName,{}),this.data={name:e,value:t}}}class no extends Event{static eventName="propertydeselected";data;constructor(e,t){super(no.eventName,{}),this.data={name:e,value:t}}}class so extends HTMLElement{#e=this.attachShadow({mode:"open"});#ke=new Map;#Ne=new Map;editableProperties=[];constructor(){super()}connectedCallback(){this.#e.adoptedStyleSheets=[Xt]}getEditableProperties(){return this.editableProperties}set data(e){this.#ke=e.authoredProperties,this.#Ne=e.computedProperties,this.#i()}#i(){eo(to`
      <div class="container">
        ${this.editableProperties.map((e=>this.#$e(e)))}
      </div>
    `,this.#e,{host:this})}#$e(e){const t=this.#ke.get(e.propertyName),o=!t,r=t||this.#Ne.get(e.propertyName),n=oo.classMap({"property-value":!0,"not-authored":o});return to`<div class="row">
      <div class="property">
        <span class="property-name">${e.propertyName}</span>: <span class=${n}>${r}</span>
      </div>
      <div class="buttons">
        ${e.propertyValues.map((o=>this.#Ce(o,e.propertyName,o===t)))}
      </div>
    </div>`}#Ce(e,t,o=!1){const r=`${t}: ${e}`,n=this.findIcon(r,this.#Ne);if(!n)throw new Error(`Icon for ${r} is not found`);const s=`transform: rotate(${n.rotate}deg) scale(${n.scaleX}, ${n.scaleY})`,a=oo.classMap({button:!0,selected:o}),l={propertyName:t,propertyValue:e},d=Zt(o?Kt.deselectButton:Kt.selectButton,l);return to`
      <button title=${d}
              class=${a}
              jslog=${i.item().track({click:!0}).context(`${t}-${e}`)}
              @click=${()=>this.#Me(t,e,o)}>
        <${c.Icon.Icon.litTagName} style=${s} name=${n.iconName}>
        </${c.Icon.Icon.litTagName}>
      </button>
    `}#Me(e,t,o){o?this.dispatchEvent(new no(e,t)):this.dispatchEvent(new ro(e,t))}findIcon(e,t){throw new Error("Not implemented")}}class io extends so{jslogContext="cssFlexboxEditor";editableProperties=lo;findIcon(e,t){return Be(e,t)}}customElements.define("devtools-flexbox-editor",io);class ao extends so{jslogContext="cssGridEditor";editableProperties=co;findIcon(e,t){return Re(e,t)}}customElements.define("devtools-grid-editor",ao);const lo=[{propertyName:"flex-direction",propertyValues:["row","column","row-reverse","column-reverse"]},{propertyName:"flex-wrap",propertyValues:["nowrap","wrap"]},{propertyName:"align-content",propertyValues:["center","flex-start","flex-end","space-around","space-between","stretch"]},{propertyName:"justify-content",propertyValues:["center","flex-start","flex-end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","flex-start","flex-end","stretch","baseline"]}],co=[{propertyName:"align-content",propertyValues:["center","space-between","space-around","space-evenly","stretch"]},{propertyName:"justify-content",propertyValues:["center","start","end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","start","end","stretch","baseline"]},{propertyName:"justify-items",propertyValues:["center","start","end","stretch"]}];var po=Object.freeze({__proto__:null,PropertySelectedEvent:ro,PropertyDeselectedEvent:no,StylePropertyEditor:so,FlexboxEditor:io,GridEditor:ao,FlexboxEditableProperties:lo,GridEditableProperties:co});export{x as AccessibilityTreeNode,C as AdornerManager,P as AdornerSettingsPane,te as CSSHintDetailsView,ce as CSSPropertyDocsView,Ae as CSSPropertyIconResolver,Qe as CSSQuery,st as CSSVariableValueView,q as ComputedStyleProperty,G as ComputedStyleTrace,xt as ElementsBreadcrumbs,ht as ElementsBreadcrumbsUtils,kt as ElementsTreeExpandButton,$t as Helper,Rt as LayoutPane,qt as LayoutPaneUtils,Yt as QueryContainer,po as StylePropertyEditor};
