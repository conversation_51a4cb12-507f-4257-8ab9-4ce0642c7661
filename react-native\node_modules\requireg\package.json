{"name": "requireg", "version": "0.2.2", "description": "Require and resolve global modules like a boss", "homepage": "http://github.com/h2non/requireg", "bugs": "https://github.com/h2non/requireg/issues", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}], "repository": {"type": "git", "url": "https://github.com/h2non/requireg.git"}, "licenses": "MIT", "main": "lib/requireg", "directories": {"lib": "./lib"}, "engines": {"node": ">= 4.0.0"}, "scripts": {"test": "mocha -u tdd --ui exports --reporter spec --slow 2000ms --bail"}, "keywords": ["global", "npm", "modules", "module", "require", "import", "resolve"], "dependencies": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "devDependencies": {"mocha": "~5.2.0", "expect.js": "~0.3.1", "rewire": "~4.0.1", "semver": "~5.5.0"}}