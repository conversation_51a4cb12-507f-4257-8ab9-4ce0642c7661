import type { AnimatedKeyboardInfo, AnimatedKeyboardOptions } from '../commonTypes';
/**
 * Lets you synchronously get the position and state of the keyboard.
 *
 * @param options - An additional keyboard configuration options.
 * @returns An object with the current keyboard `height` and `state` as [shared
 *   values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value).
 * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedKeyboard
 */
export declare function useAnimatedKeyboard(options?: AnimatedKeyboardOptions): AnimatedKeyboardInfo;
//# sourceMappingURL=useAnimatedKeyboard.d.ts.map