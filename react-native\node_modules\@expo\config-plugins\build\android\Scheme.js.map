{"version": 3, "file": "Scheme.js", "names": ["_androidPlugins", "data", "require", "_warnings", "withScheme", "exports", "createAndroidManifestPlugin", "setScheme", "getScheme", "config", "Array", "isArray", "scheme", "validate", "value", "filter", "androidManifest", "schemes", "android", "length", "ensureManifestHasValidIntentFilter", "addWarningAndroid", "currentSchemes", "getSchemesFromManifest", "uri", "index", "indexOf", "splice", "appendScheme", "isValidRedirectIntentFilter", "actions", "categories", "includes", "propertiesFromIntentFilter", "<PERSON><PERSON><PERSON><PERSON>", "action", "map", "$", "category", "host", "getSingleTaskIntentFilters", "manifest", "application", "outputSchemes", "activity", "activities", "singleTaskActivities", "intentFilters", "concat", "requestedHost", "singleTaskIntentFilters", "properties", "push", "hasScheme", "removeScheme", "dataKey"], "sources": ["../../src/android/Scheme.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, ManifestActivity } from './Manifest';\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\nimport { addWarningAndroid } from '../utils/warnings';\n\nexport type IntentFilterProps = {\n  actions: string[];\n  categories: string[];\n  data: {\n    scheme: string;\n    host?: string;\n  }[];\n};\n\nexport const withScheme = createAndroidManifestPlugin(setScheme, 'withScheme');\n\nexport function getScheme(config: { scheme?: string | string[] }): string[] {\n  if (Array.isArray(config.scheme)) {\n    const validate = (value: any): value is string => typeof value === 'string';\n\n    return config.scheme.filter<string>(validate);\n  } else if (typeof config.scheme === 'string') {\n    return [config.scheme];\n  }\n  return [];\n}\n\n// This plugin used to remove the unused schemes but this is unpredictable because other plugins could add schemes.\n// The only way to reliably remove schemes from the project is to nuke the file and regenerate the code (`npx expo prebuild --clean`).\n// Regardless, having extra schemes isn't a fatal issue and therefore a tolerable compromise is to just add new schemes that aren't currently present.\nexport function setScheme(\n  config: Pick<ExpoConfig, 'scheme' | 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const schemes = [...getScheme(config), ...getScheme(config.android ?? {})];\n  if (schemes.length === 0) {\n    return androidManifest;\n  }\n\n  if (!ensureManifestHasValidIntentFilter(androidManifest)) {\n    addWarningAndroid(\n      'scheme',\n      `Cannot add schemes because the provided manifest does not have a valid Activity with \\`android:launchMode=\"singleTask\"\\``,\n      'https://expo.fyi/setup-android-uri-scheme'\n    );\n    return androidManifest;\n  }\n\n  // Get the current schemes and remove them from the list of schemes to add.\n  const currentSchemes = getSchemesFromManifest(androidManifest);\n  for (const uri of currentSchemes) {\n    const index = schemes.indexOf(uri);\n    if (index > -1) schemes.splice(index, 1);\n  }\n\n  // Now add all of the remaining schemes.\n  for (const uri of schemes) {\n    androidManifest = appendScheme(uri, androidManifest);\n  }\n\n  return androidManifest;\n}\n\nfunction isValidRedirectIntentFilter({ actions, categories }: IntentFilterProps): boolean {\n  return (\n    actions.includes('android.intent.action.VIEW') &&\n    !categories.includes('android.intent.category.LAUNCHER')\n  );\n}\n\nfunction propertiesFromIntentFilter(intentFilter: any): IntentFilterProps {\n  const actions = intentFilter?.action?.map((data: any) => data?.$?.['android:name']) ?? [];\n  const categories = intentFilter?.category?.map((data: any) => data?.$?.['android:name']) ?? [];\n  const data =\n    intentFilter?.data\n      ?.filter((data: any) => data?.$?.['android:scheme'])\n      ?.map((data: any) => ({\n        scheme: data?.$?.['android:scheme'],\n        host: data?.$?.['android:host'],\n      })) ?? [];\n  return {\n    actions,\n    categories,\n    data,\n  };\n}\n\nfunction getSingleTaskIntentFilters(androidManifest: AndroidManifest): any[] {\n  if (!Array.isArray(androidManifest.manifest.application)) return [];\n\n  let outputSchemes: any[] = [];\n  for (const application of androidManifest.manifest.application) {\n    const { activity } = application;\n    // @ts-ignore\n    const activities = Array.isArray(activity) ? activity : [activity];\n    const singleTaskActivities = (activities as ManifestActivity[]).filter(\n      (activity) => activity?.$?.['android:launchMode'] === 'singleTask'\n    );\n    for (const activity of singleTaskActivities) {\n      const intentFilters = activity['intent-filter'];\n      outputSchemes = outputSchemes.concat(intentFilters);\n    }\n  }\n  return outputSchemes;\n}\n\nexport function getSchemesFromManifest(\n  androidManifest: AndroidManifest,\n  requestedHost: string | null = null\n): string[] {\n  const outputSchemes: string[] = [];\n\n  const singleTaskIntentFilters = getSingleTaskIntentFilters(androidManifest);\n  for (const intentFilter of singleTaskIntentFilters) {\n    const properties = propertiesFromIntentFilter(intentFilter);\n    if (isValidRedirectIntentFilter(properties) && properties.data) {\n      for (const { scheme, host } of properties.data) {\n        if (requestedHost === null || !host || host === requestedHost) {\n          outputSchemes.push(scheme);\n        }\n      }\n    }\n  }\n\n  return outputSchemes;\n}\n\nexport function ensureManifestHasValidIntentFilter(androidManifest: AndroidManifest): boolean {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return false;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          // Parse valid intent filters...\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            return true;\n          }\n        }\n        if (!activity['intent-filter']) {\n          activity['intent-filter'] = [];\n        }\n\n        activity['intent-filter'].push({\n          action: [{ $: { 'android:name': 'android.intent.action.VIEW' } }],\n          category: [\n            { $: { 'android:name': 'android.intent.category.DEFAULT' } },\n            { $: { 'android:name': 'android.intent.category.BROWSABLE' } },\n          ],\n        });\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nexport function hasScheme(scheme: string, androidManifest: AndroidManifest): boolean {\n  const schemes = getSchemesFromManifest(androidManifest);\n  return schemes.includes(scheme);\n}\n\nexport function appendScheme(scheme: string, androidManifest: AndroidManifest): AndroidManifest {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return androidManifest;\n  }\n\n  if (!ensureManifestHasValidIntentFilter(androidManifest)) {\n    addWarningAndroid(\n      'scheme',\n      `Cannot add schemes because the provided manifest does not have a valid Activity with \\`android:launchMode=\"singleTask\"\\``,\n      'https://expo.fyi/setup-android-uri-scheme'\n    );\n    return androidManifest;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            if (!intentFilter.data) intentFilter.data = [];\n            intentFilter.data.push({\n              $: { 'android:scheme': scheme },\n            });\n          }\n        }\n        break;\n      }\n    }\n  }\n  return androidManifest;\n}\n\nexport function removeScheme(scheme: string, androidManifest: AndroidManifest): AndroidManifest {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return androidManifest;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          // Parse valid intent filters...\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            for (const dataKey in intentFilter?.data || []) {\n              const data = intentFilter.data?.[dataKey];\n              if (data?.$?.['android:scheme'] === scheme) {\n                delete intentFilter.data?.[dataKey];\n              }\n            }\n          }\n        }\n        break;\n      }\n    }\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAWO,MAAMG,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,IAAAE,6CAA2B,EAACC,SAAS,EAAE,YAAY,CAAC;AAEvE,SAASC,SAASA,CAACC,MAAsC,EAAY;EAC1E,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACG,MAAM,CAAC,EAAE;IAChC,MAAMC,QAAQ,GAAIC,KAAU,IAAsB,OAAOA,KAAK,KAAK,QAAQ;IAE3E,OAAOL,MAAM,CAACG,MAAM,CAACG,MAAM,CAASF,QAAQ,CAAC;EAC/C,CAAC,MAAM,IAAI,OAAOJ,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;IAC5C,OAAO,CAACH,MAAM,CAACG,MAAM,CAAC;EACxB;EACA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACO,SAASL,SAASA,CACvBE,MAA8C,EAC9CO,eAAgC,EAChC;EACA,MAAMC,OAAO,GAAG,CAAC,GAAGT,SAAS,CAACC,MAAM,CAAC,EAAE,GAAGD,SAAS,CAACC,MAAM,CAACS,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1E,IAAID,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,OAAOH,eAAe;EACxB;EAEA,IAAI,CAACI,kCAAkC,CAACJ,eAAe,CAAC,EAAE;IACxD,IAAAK,6BAAiB,EACf,QAAQ,EACR,0HAA0H,EAC1H,2CACF,CAAC;IACD,OAAOL,eAAe;EACxB;;EAEA;EACA,MAAMM,cAAc,GAAGC,sBAAsB,CAACP,eAAe,CAAC;EAC9D,KAAK,MAAMQ,GAAG,IAAIF,cAAc,EAAE;IAChC,MAAMG,KAAK,GAAGR,OAAO,CAACS,OAAO,CAACF,GAAG,CAAC;IAClC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAER,OAAO,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC1C;;EAEA;EACA,KAAK,MAAMD,GAAG,IAAIP,OAAO,EAAE;IACzBD,eAAe,GAAGY,YAAY,CAACJ,GAAG,EAAER,eAAe,CAAC;EACtD;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASa,2BAA2BA,CAAC;EAAEC,OAAO;EAAEC;AAA8B,CAAC,EAAW;EACxF,OACED,OAAO,CAACE,QAAQ,CAAC,4BAA4B,CAAC,IAC9C,CAACD,UAAU,CAACC,QAAQ,CAAC,kCAAkC,CAAC;AAE5D;AAEA,SAASC,0BAA0BA,CAACC,YAAiB,EAAqB;EACxE,MAAMJ,OAAO,GAAGI,YAAY,EAAEC,MAAM,EAAEC,GAAG,CAAEnC,IAAS,IAAKA,IAAI,EAAEoC,CAAC,GAAG,cAAc,CAAC,CAAC,IAAI,EAAE;EACzF,MAAMN,UAAU,GAAGG,YAAY,EAAEI,QAAQ,EAAEF,GAAG,CAAEnC,IAAS,IAAKA,IAAI,EAAEoC,CAAC,GAAG,cAAc,CAAC,CAAC,IAAI,EAAE;EAC9F,MAAMpC,IAAI,GACRiC,YAAY,EAAEjC,IAAI,EACdc,MAAM,CAAEd,IAAS,IAAKA,IAAI,EAAEoC,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAClDD,GAAG,CAAEnC,IAAS,KAAM;IACpBW,MAAM,EAAEX,IAAI,EAAEoC,CAAC,GAAG,gBAAgB,CAAC;IACnCE,IAAI,EAAEtC,IAAI,EAAEoC,CAAC,GAAG,cAAc;EAChC,CAAC,CAAC,CAAC,IAAI,EAAE;EACb,OAAO;IACLP,OAAO;IACPC,UAAU;IACV9B;EACF,CAAC;AACH;AAEA,SAASuC,0BAA0BA,CAACxB,eAAgC,EAAS;EAC3E,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyB,QAAQ,CAACC,WAAW,CAAC,EAAE,OAAO,EAAE;EAEnE,IAAIC,aAAoB,GAAG,EAAE;EAC7B,KAAK,MAAMD,WAAW,IAAI1B,eAAe,CAACyB,QAAQ,CAACC,WAAW,EAAE;IAC9D,MAAM;MAAEE;IAAS,CAAC,GAAGF,WAAW;IAChC;IACA,MAAMG,UAAU,GAAGnC,KAAK,CAACC,OAAO,CAACiC,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAClE,MAAME,oBAAoB,GAAID,UAAU,CAAwB9B,MAAM,CACnE6B,QAAQ,IAAKA,QAAQ,EAAEP,CAAC,GAAG,oBAAoB,CAAC,KAAK,YACxD,CAAC;IACD,KAAK,MAAMO,QAAQ,IAAIE,oBAAoB,EAAE;MAC3C,MAAMC,aAAa,GAAGH,QAAQ,CAAC,eAAe,CAAC;MAC/CD,aAAa,GAAGA,aAAa,CAACK,MAAM,CAACD,aAAa,CAAC;IACrD;EACF;EACA,OAAOJ,aAAa;AACtB;AAEO,SAASpB,sBAAsBA,CACpCP,eAAgC,EAChCiC,aAA4B,GAAG,IAAI,EACzB;EACV,MAAMN,aAAuB,GAAG,EAAE;EAElC,MAAMO,uBAAuB,GAAGV,0BAA0B,CAACxB,eAAe,CAAC;EAC3E,KAAK,MAAMkB,YAAY,IAAIgB,uBAAuB,EAAE;IAClD,MAAMC,UAAU,GAAGlB,0BAA0B,CAACC,YAAY,CAAC;IAC3D,IAAIL,2BAA2B,CAACsB,UAAU,CAAC,IAAIA,UAAU,CAAClD,IAAI,EAAE;MAC9D,KAAK,MAAM;QAAEW,MAAM;QAAE2B;MAAK,CAAC,IAAIY,UAAU,CAAClD,IAAI,EAAE;QAC9C,IAAIgD,aAAa,KAAK,IAAI,IAAI,CAACV,IAAI,IAAIA,IAAI,KAAKU,aAAa,EAAE;UAC7DN,aAAa,CAACS,IAAI,CAACxC,MAAM,CAAC;QAC5B;MACF;IACF;EACF;EAEA,OAAO+B,aAAa;AACtB;AAEO,SAASvB,kCAAkCA,CAACJ,eAAgC,EAAW;EAC5F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyB,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO,KAAK;EACd;EAEA,KAAK,MAAMA,WAAW,IAAI1B,eAAe,CAACyB,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MACjD,IAAIA,QAAQ,EAAEP,CAAC,GAAG,oBAAoB,CAAC,KAAK,YAAY,EAAE;QACxD,KAAK,MAAMH,YAAY,IAAIU,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D;UACA,MAAMO,UAAU,GAAGlB,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACsB,UAAU,CAAC,EAAE;YAC3C,OAAO,IAAI;UACb;QACF;QACA,IAAI,CAACP,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC9BA,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE;QAChC;QAEAA,QAAQ,CAAC,eAAe,CAAC,CAACQ,IAAI,CAAC;UAC7BjB,MAAM,EAAE,CAAC;YAAEE,CAAC,EAAE;cAAE,cAAc,EAAE;YAA6B;UAAE,CAAC,CAAC;UACjEC,QAAQ,EAAE,CACR;YAAED,CAAC,EAAE;cAAE,cAAc,EAAE;YAAkC;UAAE,CAAC,EAC5D;YAAEA,CAAC,EAAE;cAAE,cAAc,EAAE;YAAoC;UAAE,CAAC;QAElE,CAAC,CAAC;QACF,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd;AAEO,SAASgB,SAASA,CAACzC,MAAc,EAAEI,eAAgC,EAAW;EACnF,MAAMC,OAAO,GAAGM,sBAAsB,CAACP,eAAe,CAAC;EACvD,OAAOC,OAAO,CAACe,QAAQ,CAACpB,MAAM,CAAC;AACjC;AAEO,SAASgB,YAAYA,CAAChB,MAAc,EAAEI,eAAgC,EAAmB;EAC9F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyB,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO1B,eAAe;EACxB;EAEA,IAAI,CAACI,kCAAkC,CAACJ,eAAe,CAAC,EAAE;IACxD,IAAAK,6BAAiB,EACf,QAAQ,EACR,0HAA0H,EAC1H,2CACF,CAAC;IACD,OAAOL,eAAe;EACxB;EAEA,KAAK,MAAM0B,WAAW,IAAI1B,eAAe,CAACyB,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MACjD,IAAIA,QAAQ,EAAEP,CAAC,GAAG,oBAAoB,CAAC,KAAK,YAAY,EAAE;QACxD,KAAK,MAAMH,YAAY,IAAIU,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D,MAAMO,UAAU,GAAGlB,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACsB,UAAU,CAAC,EAAE;YAC3C,IAAI,CAACjB,YAAY,CAACjC,IAAI,EAAEiC,YAAY,CAACjC,IAAI,GAAG,EAAE;YAC9CiC,YAAY,CAACjC,IAAI,CAACmD,IAAI,CAAC;cACrBf,CAAC,EAAE;gBAAE,gBAAgB,EAAEzB;cAAO;YAChC,CAAC,CAAC;UACJ;QACF;QACA;MACF;IACF;EACF;EACA,OAAOI,eAAe;AACxB;AAEO,SAASsC,YAAYA,CAAC1C,MAAc,EAAEI,eAAgC,EAAmB;EAC9F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyB,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO1B,eAAe;EACxB;EAEA,KAAK,MAAM0B,WAAW,IAAI1B,eAAe,CAACyB,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MACjD,IAAIA,QAAQ,EAAEP,CAAC,GAAG,oBAAoB,CAAC,KAAK,YAAY,EAAE;QACxD,KAAK,MAAMH,YAAY,IAAIU,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D;UACA,MAAMO,UAAU,GAAGlB,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACsB,UAAU,CAAC,EAAE;YAC3C,KAAK,MAAMI,OAAO,IAAIrB,YAAY,EAAEjC,IAAI,IAAI,EAAE,EAAE;cAC9C,MAAMA,IAAI,GAAGiC,YAAY,CAACjC,IAAI,GAAGsD,OAAO,CAAC;cACzC,IAAItD,IAAI,EAAEoC,CAAC,GAAG,gBAAgB,CAAC,KAAKzB,MAAM,EAAE;gBAC1C,OAAOsB,YAAY,CAACjC,IAAI,GAAGsD,OAAO,CAAC;cACrC;YACF;UACF;QACF;QACA;MACF;IACF;EACF;EAEA,OAAOvC,eAAe;AACxB", "ignoreList": []}