import"../shell/shell.js";import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as n from"../../ui/legacy/legacy.js";import*as i from"../../core/sdk/sdk.js";import*as r from"../../models/issues_manager/issues_manager.js";import*as a from"../../models/extensions/extensions.js";import*as s from"../../models/workspace/workspace.js";import*as l from"../../panels/timeline/utils/utils.js";import*as c from"../../panels/network/forward/forward.js";import*as d from"../main/main.js";import*as g from"../../core/rn_experiments/rn_experiments.js";import*as u from"../../core/host/host.js";const w={toggleDeviceToolbar:"Toggle device toolbar",captureScreenshot:"Capture screenshot",captureFullSizeScreenshot:"Capture full size screenshot",captureNodeScreenshot:"Capture node screenshot",showMediaQueries:"Show media queries",device:"device",hideMediaQueries:"Hide media queries",showRulers:"Show rulers in the Device Mode toolbar",hideRulers:"Hide rulers in the Device Mode toolbar",showDeviceFrame:"Show device frame",hideDeviceFrame:"Hide device frame"},m=t.i18n.registerUIStrings("panels/emulation/emulation-meta.ts",w),p=t.i18n.getLazilyComputedLocalizedString.bind(void 0,m);let v;async function k(){return v||(v=await import("../../panels/emulation/emulation.js")),v}n.ActionRegistration.registerActionExtension({category:"MOBILE",actionId:"emulation.toggle-device-mode",toggleable:!0,loadActionDelegate:async()=>new((await k()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(w.toggleDeviceToolbar),iconClass:"devices",bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+M"},{platform:"mac",shortcut:"Shift+Meta+M"}]}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await k()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(w.captureScreenshot)}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-full-height-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await k()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(w.captureFullSizeScreenshot)}),n.ActionRegistration.registerActionExtension({actionId:"emulation.capture-node-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await k()).DeviceModeWrapper.ActionDelegate),condition:o.Runtime.conditions.canDock,title:p(w.captureNodeScreenshot)}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"show-media-query-inspector",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(w.showMediaQueries)},{value:!1,title:p(w.hideMediaQueries)}],tags:[p(w.device)]}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-rulers",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(w.showRulers)},{value:!1,title:p(w.hideRulers)}],tags:[p(w.device)]}),e.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-device-outline",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:p(w.showDeviceFrame)},{value:!1,title:p(w.hideDeviceFrame)}],tags:[p(w.device)]}),n.Toolbar.registerToolbarItem({actionId:"emulation.toggle-device-mode",condition:o.Runtime.conditions.canDock,location:"main-toolbar-left",order:1,showLabel:void 0,loadItem:void 0,separator:void 0}),e.AppProvider.registerAppProvider({loadAppProvider:async()=>(await k()).AdvancedApp.AdvancedAppProvider.instance(),condition:o.Runtime.conditions.canDock,order:0}),n.ContextMenu.registerItem({location:"deviceModeMenu/save",order:12,actionId:"emulation.capture-screenshot"}),n.ContextMenu.registerItem({location:"deviceModeMenu/save",order:13,actionId:"emulation.capture-full-height-screenshot"});const R={sensors:"Sensors",geolocation:"geolocation",timezones:"timezones",locale:"locale",locales:"locales",accelerometer:"accelerometer",deviceOrientation:"device orientation",locations:"Locations",touch:"Touch",devicebased:"Device-based",forceEnabled:"Force enabled",emulateIdleDetectorState:"Emulate Idle Detector state",noIdleEmulation:"No idle emulation",userActiveScreenUnlocked:"User active, screen unlocked",userActiveScreenLocked:"User active, screen locked",userIdleScreenUnlocked:"User idle, screen unlocked",userIdleScreenLocked:"User idle, screen locked",showSensors:"Show Sensors",showLocations:"Show Locations"},h=t.i18n.registerUIStrings("panels/sensors/sensors-meta.ts",R),y=t.i18n.getLazilyComputedLocalizedString.bind(void 0,h);let S;async function N(){return S||(S=await import("../../panels/sensors/sensors.js")),S}n.ViewManager.registerViewExtension({location:"drawer-view",commandPrompt:y(R.showSensors),title:y(R.sensors),id:"sensors",persistence:"closeable",order:100,loadView:async()=>new((await N()).SensorsView.SensorsView),tags:[y(R.geolocation),y(R.timezones),y(R.locale),y(R.locales),y(R.accelerometer),y(R.deviceOrientation)]}),n.ViewManager.registerViewExtension({location:"settings-view",id:"emulation-locations",commandPrompt:y(R.showLocations),title:y(R.locations),order:40,loadView:async()=>new((await N()).LocationsSettingsTab.LocationsSettingsTab),settings:["emulation.locations"],iconName:"location-on"}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"emulation.locations",settingType:"array",defaultValue:[{title:"Berlin",lat:52.520007,long:13.404954,timezoneId:"Europe/Berlin",locale:"de-DE"},{title:"London",lat:51.507351,long:-.127758,timezoneId:"Europe/London",locale:"en-GB"},{title:"Moscow",lat:55.755826,long:37.6173,timezoneId:"Europe/Moscow",locale:"ru-RU"},{title:"Mountain View",lat:37.386052,long:-122.083851,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Mumbai",lat:19.075984,long:72.877656,timezoneId:"Asia/Kolkata",locale:"mr-IN"},{title:"San Francisco",lat:37.774929,long:-122.419416,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Shanghai",lat:31.230416,long:121.473701,timezoneId:"Asia/Shanghai",locale:"zh-Hans-CN"},{title:"São Paulo",lat:-23.55052,long:-46.633309,timezoneId:"America/Sao_Paulo",locale:"pt-BR"},{title:"Tokyo",lat:35.689487,long:139.691706,timezoneId:"Asia/Tokyo",locale:"ja-JP"}]}),e.Settings.registerSettingExtension({title:y(R.touch),reloadRequired:!0,settingName:"emulation.touch",settingType:"enum",defaultValue:"none",options:[{value:"none",title:y(R.devicebased),text:y(R.devicebased)},{value:"force",title:y(R.forceEnabled),text:y(R.forceEnabled)}]}),e.Settings.registerSettingExtension({title:y(R.emulateIdleDetectorState),settingName:"emulation.idle-detection",settingType:"enum",defaultValue:"none",options:[{value:"none",title:y(R.noIdleEmulation),text:y(R.noIdleEmulation)},{value:'{"isUserActive":true,"isScreenUnlocked":true}',title:y(R.userActiveScreenUnlocked),text:y(R.userActiveScreenUnlocked)},{value:'{"isUserActive":true,"isScreenUnlocked":false}',title:y(R.userActiveScreenLocked),text:y(R.userActiveScreenLocked)},{value:'{"isUserActive":false,"isScreenUnlocked":true}',title:y(R.userIdleScreenUnlocked),text:y(R.userIdleScreenUnlocked)},{value:'{"isUserActive":false,"isScreenUnlocked":false}',title:y(R.userIdleScreenLocked),text:y(R.userIdleScreenLocked)}]});const f={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},A=t.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",f),T=t.i18n.getLazilyComputedLocalizedString.bind(void 0,A);let b;async function E(){return b||(b=await import("../../panels/developer_resources/developer_resources.js")),b}n.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:T(f.developerResources),commandPrompt:T(f.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await E()).DeveloperResourcesView.DeveloperResourcesView)}),e.Revealer.registerRevealer({contextTypes:()=>[i.PageResourceLoader.ResourceKey],destination:e.Revealer.RevealerDestination.DEVELOPER_RESOURCES_PANEL,loadRevealer:async()=>new((await E()).DeveloperResourcesView.DeveloperResourcesRevealer)});const I={rendering:"Rendering",showRendering:"Show Rendering",paint:"paint",layout:"layout",fps:"fps",cssMediaType:"CSS media type",cssMediaFeature:"CSS media feature",visionDeficiency:"vision deficiency",colorVisionDeficiency:"color vision deficiency",reloadPage:"Reload page",hardReloadPage:"Hard reload page",forceAdBlocking:"Force ad blocking on this site",blockAds:"Block ads on this site",showAds:"Show ads on this site, if allowed",autoOpenDevTools:"Auto-open DevTools for popups",doNotAutoOpen:"Do not auto-open DevTools for popups",disablePaused:"Disable paused state overlay",toggleCssPrefersColorSchemeMedia:"Toggle CSS media feature prefers-color-scheme"},D=t.i18n.registerUIStrings("entrypoints/inspector_main/inspector_main-meta.ts",I),x=t.i18n.getLazilyComputedLocalizedString.bind(void 0,D);let P;async function L(){return P||(P=await import("../inspector_main/inspector_main.js")),P}n.ViewManager.registerViewExtension({location:"drawer-view",id:"rendering",title:x(I.rendering),commandPrompt:x(I.showRendering),persistence:"closeable",order:50,loadView:async()=>new((await L()).RenderingOptions.RenderingOptionsView),tags:[x(I.paint),x(I.layout),x(I.fps),x(I.cssMediaType),x(I.cssMediaFeature),x(I.visionDeficiency),x(I.colorVisionDeficiency)]}),n.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.reload",loadActionDelegate:async()=>new((await L()).InspectorMain.ReloadActionDelegate),iconClass:"refresh",title:x(I.reloadPage),bindings:[{platform:"windows,linux",shortcut:"Ctrl+R"},{platform:"windows,linux",shortcut:"F5"},{platform:"mac",shortcut:"Meta+R"}]}),n.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.hard-reload",loadActionDelegate:async()=>new((await L()).InspectorMain.ReloadActionDelegate),title:x(I.hardReloadPage),bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+R"},{platform:"windows,linux",shortcut:"Shift+F5"},{platform:"windows,linux",shortcut:"Ctrl+F5"},{platform:"windows,linux",shortcut:"Ctrl+Shift+F5"},{platform:"mac",shortcut:"Shift+Meta+R"}]}),n.ActionRegistration.registerActionExtension({actionId:"rendering.toggle-prefers-color-scheme",category:"RENDERING",title:x(I.toggleCssPrefersColorSchemeMedia),loadActionDelegate:async()=>new((await L()).RenderingOptions.ReloadActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",title:x(I.forceAdBlocking),settingName:"network.ad-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:x(I.blockAds)},{value:!1,title:x(I.showAds)}]}),e.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Synced",title:x(I.autoOpenDevTools),settingName:"auto-attach-to-created-pages",settingType:"boolean",order:2,defaultValue:!1,options:[{value:!0,title:x(I.autoOpenDevTools)},{value:!1,title:x(I.doNotAutoOpen)}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:x(I.disablePaused),settingName:"disable-paused-state-overlay",settingType:"boolean",defaultValue:!1}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await L()).InspectorMain.NodeIndicator.instance(),order:2,location:"main-toolbar-left"}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await L()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",experiment:"outermost-target-selector"}),n.Toolbar.registerToolbarItem({loadItem:async()=>(await L()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0,experiment:"outermost-target-selector"});const M={issues:"Issues",showIssues:"Show Issues"},V=t.i18n.registerUIStrings("panels/issues/issues-meta.ts",M),C=t.i18n.getLazilyComputedLocalizedString.bind(void 0,V);let U;async function O(){return U||(U=await import("../../panels/issues/issues.js")),U}n.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:C(M.issues),commandPrompt:C(M.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await O()).IssuesPane.IssuesPane)}),e.Revealer.registerRevealer({contextTypes:()=>[r.Issue.Issue],destination:e.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await O()).IssueRevealer.IssueRevealer)});const B={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},q=t.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",B),z=t.i18n.getLazilyComputedLocalizedString.bind(void 0,q);let W;async function _(){return W||(W=await import("../../panels/mobile_throttling/mobile_throttling.js")),W}n.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:z(B.throttling),commandPrompt:z(B.showThrottling),order:35,loadView:async()=>new((await _()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:z(B.goOffline),loadActionDelegate:async()=>new((await _()).ThrottlingManager.ActionDelegate),tags:[z(B.device),z(B.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:z(B.enableSlowGThrottling),loadActionDelegate:async()=>new((await _()).ThrottlingManager.ActionDelegate),tags:[z(B.device),z(B.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:z(B.enableFastGThrottling),loadActionDelegate:async()=>new((await _()).ThrottlingManager.ActionDelegate),tags:[z(B.device),z(B.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:z(B.goOnline),loadActionDelegate:async()=>new((await _()).ThrottlingManager.ActionDelegate),tags:[z(B.device),z(B.throttlingTag)]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const F={showNetwork:"Show Network",network:"Network",networkExpoUnstable:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},j=t.i18n.registerUIStrings("panels/network/network-meta.ts",F),K=t.i18n.getLazilyComputedLocalizedString.bind(void 0,j),G=t.i18n.getLocalizedString.bind(void 0,j);let H;async function Q(){return H||(H=await import("../../panels/network/network.js")),H}function J(e){return void 0===H?[]:e(H)}n.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:K(F.showNetwork),title:()=>o.Runtime.experiments.isEnabled(o.Runtime.RNExperimentName.ENABLE_NETWORK_PANEL)?G(F.network):G(F.networkExpoUnstable),order:40,isPreviewFeature:!0,condition:o.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await Q()).NetworkPanel.NetworkPanel.instance()}),n.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:K(F.showNetworkRequestBlocking),title:K(F.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await Q()).BlockedURLsPane.BlockedURLsPane)}),n.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:K(F.showNetworkConditions),title:K(F.networkConditions),persistence:"closeable",order:40,tags:[K(F.diskCache),K(F.networkThrottling),t.i18n.lockedLazyString("useragent"),t.i18n.lockedLazyString("user agent"),t.i18n.lockedLazyString("user-agent")],loadView:async()=>(await Q()).NetworkConfigView.NetworkConfigView.instance()}),n.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:K(F.showSearch),title:K(F.search),persistence:"permanent",loadView:async()=>(await Q()).NetworkPanel.SearchNetworkView.instance()}),n.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>J((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Q()).NetworkPanel.ActionDelegate),options:[{value:!0,title:K(F.recordNetworkLog)},{value:!1,title:K(F.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:K(F.clear),iconClass:"clear",loadActionDelegate:async()=>new((await Q()).NetworkPanel.ActionDelegate),contextTypes:()=>J((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:K(F.hideRequestDetails),contextTypes:()=>J((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Q()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),n.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:K(F.search),contextTypes:()=>J((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await Q()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),n.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:K(F.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>J((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await Q()).BlockedURLsPane.ActionDelegate)}),n.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:K(F.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>J((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await Q()).BlockedURLsPane.ActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:K(F.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[K(F.colorCode),K(F.resourceType)],options:[{value:!0,title:K(F.colorCodeByResourceType)},{value:!1,title:K(F.useDefaultColors)}]}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:K(F.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[K(F.netWork),K(F.frame),K(F.group)],options:[{value:!0,title:K(F.groupNetworkLogItemsByFrame)},{value:!1,title:K(F.dontGroupNetworkLogItemsByFrame)}]}),n.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await Q()).NetworkPanel.NetworkPanel.instance()}),n.ContextMenu.registerProvider({contextTypes:()=>[i.NetworkRequest.NetworkRequest,i.Resource.Resource,s.UISourceCode.UISourceCode,l.NetworkRequest.TimelineNetworkRequest],loadProvider:async()=>(await Q()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[i.NetworkRequest.NetworkRequest],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Q()).NetworkPanel.RequestRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await Q()).NetworkPanel.RequestLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.NetworkRequestId.NetworkRequestId],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Q()).NetworkPanel.RequestIdRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[c.UIFilter.UIRequestFilter,a.ExtensionServer.RevealableNetworkRequestFilter],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await Q()).NetworkPanel.NetworkLogWithFilterRevealer)});const X={rnWelcome:"⚛️ Welcome",showRnWelcome:"Show React Native Welcome panel",debuggerBrandName:"React Native JS Inspector"},Y=t.i18n.registerUIStrings("panels/rn_welcome/rn_welcome-legacy-meta.ts",X),Z=t.i18n.getLazilyComputedLocalizedString.bind(void 0,Y);let $;n.ViewManager.registerViewExtension({location:"panel",id:"rn-welcome",title:Z(X.rnWelcome),commandPrompt:Z(X.showRnWelcome),order:-10,persistence:"permanent",loadView:async()=>(await async function(){return $||($=await import("../../panels/rn_welcome/rn_welcome.js")),$}()).RNWelcome.RNWelcomeImpl.instance({debuggerBrandName:Z(X.debuggerBrandName),showTechPreviewLabel:!0}),experiment:"react-native-specific-ui"}),u.rnPerfMetrics.registerPerfMetricsGlobalPostMessageHandler(),u.rnPerfMetrics.setLaunchId(o.Runtime.Runtime.queryParam("launchId")),u.rnPerfMetrics.entryPointLoadingStarted("rn_inspector"),g.RNExperimentsImpl.setIsReactNativeEntryPoint(!0),g.RNExperimentsImpl.Instance.enableExperimentsByDefault(["js-heap-profiler-enable","react-native-specific-ui"]);const ee={networkTitle:"React Native",showReactNative:"Show React Native"},te=t.i18n.registerUIStrings("entrypoints/rn_inspector/rn_inspector.ts",ee),oe=t.i18n.getLazilyComputedLocalizedString.bind(void 0,te);let ne;n.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:oe(ee.networkTitle),commandPrompt:oe(ee.showReactNative),order:2,persistence:"permanent",loadView:async()=>(await async function(){return ne||(ne=await import("../../panels/sources/sources.js")),ne}()).SourcesNavigator.NetworkNavigatorView.instance()}),self.runtime=o.Runtime.Runtime.instance({forceNew:!0}),new d.MainImpl.MainImpl,u.rnPerfMetrics.entryPointLoadingFinished("rn_inspector");
