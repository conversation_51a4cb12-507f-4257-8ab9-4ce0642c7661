{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "Animated", "Platform", "TransitionProgressContext", "DelayedFreeze", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "ScreenNativeComponent", "ModalScreenNativeComponent", "usePrevious", "AnimatedNativeScreen", "createAnimatedComponent", "AnimatedNativeModalScreen", "SHEET_FIT_TO_CONTENTS", "SHEET_COMPAT_LARGE", "SHEET_COMPAT_MEDIUM", "SHEET_COMPAT_ALL", "SHEET_DIMMED_ALWAYS", "assertDetentsArrayIsSorted", "array", "Error", "resolveSheetAllowedDetents", "allowedDetentsCompat", "Array", "isArray", "OS", "__DEV__", "console", "warn", "slice", "resolveSheetLargestUndimmedDetent", "lud", "lastDetentIndex", "isIndexInClosedRange", "resolveSheetInitialDetentIndex", "index", "value", "lowerBound", "upperBound", "Number", "isInteger", "InnerScreen", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "freezeOnBlur", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "AnimatedScreen", "undefined", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "createElement", "freeze", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAEb,OAAOQ,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAQC,QAAQ,QAAQ,cAAc;AAEvD,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAGnD,SACEC,aAAa,EACbC,yBAAyB,EACzBC,cAAc,QACT,SAAS;;AAEhB;AACA,OAAOC,qBAAqB,MAErB,iCAAiC;AACxC,OAAOC,0BAA0B,MAE1B,sCAAsC;AAC7C,SAASC,WAAW,QAAQ,uBAAuB;AAGnD,MAAMC,oBAAoB,GAAGV,QAAQ,CAACW,uBAAuB,CAC3DJ,qBACF,CAAC;AACD,MAAMK,yBAAyB,GAAGZ,QAAQ,CAACW,uBAAuB,CAChEH,0BACF,CAAC;;AAED;AACA;AAkBA;AACA,MAAMK,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,MAAMC,kBAAkB,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMC,mBAAmB,GAAG,CAAC,GAAG,CAAC;AACjC,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAEnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;AAC9B;;AAEA,SAASC,0BAA0BA,CAACC,KAAe,EAAE;EACnD,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,KAAK,CAAC3B,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAI6B,KAAK,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAI8B,KAAK,CACb,gEACF,CAAC;IACH;EACF;AACF;;AAEA;AACA;AACA,SAASC,0BAA0BA,CACjCC,oBAAwD,EAC9C;EACV,IAAIC,KAAK,CAACC,OAAO,CAACF,oBAAoB,CAAC,EAAE;IACvC,IAAIrB,QAAQ,CAACwB,EAAE,KAAK,SAAS,IAAIH,oBAAoB,CAAC9B,MAAM,GAAG,CAAC,EAAE;MAChE,IAAIkC,OAAO,EAAE;QACXC,OAAO,CAACC,IAAI,CACV,iGACF,CAAC;MACH;MACAN,oBAAoB,GAAGA,oBAAoB,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzD;IACA,IAAIH,OAAO,EAAE;MACXR,0BAA0B,CAACI,oBAAoB,CAAC;IAClD;IACA,OAAOA,oBAAoB;EAC7B,CAAC,MAAM,IAAIA,oBAAoB,KAAK,eAAe,EAAE;IACnD,OAAOT,qBAAqB;EAC9B,CAAC,MAAM,IAAIS,oBAAoB,KAAK,OAAO,EAAE;IAC3C,OAAOR,kBAAkB;EAC3B,CAAC,MAAM,IAAIQ,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,OAAOP,mBAAmB;EAC5B,CAAC,MAAM,IAAIO,oBAAoB,KAAK,KAAK,EAAE;IACzC,OAAON,gBAAgB;EACzB,CAAC,MAAM;IACL;IACA,OAAO,CAAC,GAAG,CAAC;EACd;AACF;AAEA,SAASc,iCAAiCA,CACxCC,GAAmD,EACnDC,eAAuB,EACf;EACR,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,CAACE,oBAAoB,CAACF,GAAG,EAAEd,mBAAmB,EAAEe,eAAe,CAAC,EAAE;MACpE,IAAIN,OAAO,EAAE;QACX,MAAM,IAAIN,KAAK,CACb,uHACF,CAAC;MACH;MACA;MACA,OAAOH,mBAAmB;IAC5B;IACA,OAAOc,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,KAAK,MAAM,EAAE;IACzB,OAAOC,eAAe;EACxB,CAAC,MAAM,IAAID,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC1C,OAAOd,mBAAmB;EAC5B,CAAC,MAAM,IAAIc,GAAG,KAAK,OAAO,EAAE;IAC1B,OAAO,CAAC;EACV,CAAC,MAAM,IAAIA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAC;EACV,CAAC,MAAM;IACL;IACA,OAAOd,mBAAmB;EAC5B;AACF;AAEA,SAASiB,8BAA8BA,CACrCC,KAA6C,EAC7CH,eAAuB,EACf;EACR,IAAIG,KAAK,KAAK,MAAM,EAAE;IACpBA,KAAK,GAAGH,eAAe;EACzB,CAAC,MAAM,IAAIG,KAAK,IAAI,IAAI,EAAE;IACxB;IACAA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,CAACF,oBAAoB,CAACE,KAAK,EAAE,CAAC,EAAEH,eAAe,CAAC,EAAE;IACpD,IAAIN,OAAO,EAAE;MACX,MAAM,IAAIN,KAAK,CACb,+GACF,CAAC;IACH;IACA;IACA,OAAO,CAAC;EACV;EACA,OAAOe,KAAK;AACd;AAEA,SAASF,oBAAoBA,CAC3BG,KAAa,EACbC,UAAkB,EAClBC,UAAkB,EACT;EACT,OAAOC,MAAM,CAACC,SAAS,CAACJ,KAAK,CAAC,IAAIA,KAAK,IAAIC,UAAU,IAAID,KAAK,IAAIE,UAAU;AAC9E;AAEA,OAAO,MAAMG,WAAW,gBAAG1C,KAAK,CAAC2C,UAAU,CACzC,SAASD,WAAWA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAG9C,KAAK,CAAC+C,MAAM,CAAoB,IAAI,CAAC;EACtD/C,KAAK,CAACgD,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAGxC,WAAW,CAACkC,KAAK,CAACO,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIP,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACS,cAAc,GAAGR,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMS,OAAO,GAAGtD,KAAK,CAAC+C,MAAM,CAAC,IAAI9C,QAAQ,CAACsD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC3D,MAAMO,QAAQ,GAAGxD,KAAK,CAAC+C,MAAM,CAAC,IAAI9C,QAAQ,CAACsD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC5D,MAAMQ,YAAY,GAAGzD,KAAK,CAAC+C,MAAM,CAAC,IAAI9C,QAAQ,CAACsD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAEhE,MAAM;IACJS,OAAO,GAAGnD,cAAc,CAAC,CAAC;IAC1BoD,YAAY,GAAGtD,aAAa,CAAC,CAAC;IAC9B,GAAGuD;EACL,CAAC,GAAGhB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAiB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAG5C,mBAAmB;IACrD6C,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGZ,IAAI;EAER,IAAIF,OAAO,IAAIpD,yBAAyB,EAAE;IACxC,MAAMmE,2BAA2B,GAC/BnD,0BAA0B,CAACuC,mBAAmB,CAAC;IACjD,MAAMa,kCAAkC,GACtC3C,iCAAiC,CAC/B+B,+BAA+B,EAC/BW,2BAA2B,CAAChF,MAAM,GAAG,CACvC,CAAC;IACH,MAAMkF,+BAA+B,GAAGxC,8BAA8B,CACpEgC,uBAAuB,EACvBM,2BAA2B,CAAChF,MAAM,GAAG,CACvC,CAAC;IACD;IACA,MAAMmF,cAAc,GAClB1E,QAAQ,CAACwB,EAAE,KAAK,SAAS,IACzB0C,iBAAiB,KAAKS,SAAS,IAC/BT,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,GAC7CzD,oBAAoB,GACpBE,yBAAyB;IAE/B,IAAI;MACF;MACA;MACA;MACAiE,MAAM;MACN3B,aAAa;MACb4B,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAGvC;IACL,CAAC,GAAGgB,IAAI;IAER,IAAIkB,MAAM,KAAKD,SAAS,IAAI1B,aAAa,KAAK0B,SAAS,EAAE;MACvDjD,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDsB,aAAa,GAAG2B,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;;IAEA,IACEE,aAAa,IACb9B,iBAAiB,KAAK2B,SAAS,IAC/B1B,aAAa,KAAK0B,SAAS,EAC3B;MACA,IAAI3B,iBAAiB,GAAGC,aAAa,EAAE;QACrC,MAAM,IAAI9B,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAM+D,SAAS,GAAIvC,GAAe,IAAK;MACrC,IAAIA,GAAG,EAAEwC,UAAU,EAAEC,eAAe,EAAEH,KAAK,EAAE;QAC3CtC,GAAG,CAACwC,UAAU,CAACC,eAAe,CAACH,KAAK,GAAG;UACrC,GAAGtC,GAAG,CAACwC,UAAU,CAACC,eAAe,CAACH,KAAK;UACvCI,OAAO,EAAE;QACX,CAAC;QACDnC,MAAM,CAACP,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAE2C,WAAW,EAAEF,eAAe,EAAEH,KAAK,EAAE;QACnDtC,GAAG,CAAC2C,WAAW,CAACF,eAAe,CAACH,KAAK,GAAG;UACtC,GAAGtC,GAAG,CAAC2C,WAAW,CAACF,eAAe,CAACH,KAAK;UACxCI,OAAO,EAAE;QACX,CAAC;QACDnC,MAAM,CAACP,GAAG,CAAC;MACb;IACF,CAAC;IAED,oBACE7C,KAAA,CAAAyF,aAAA,CAACrF,aAAa;MAACsF,MAAM,EAAE/B,YAAY,IAAIR,aAAa,KAAK;IAAE,gBACzDnD,KAAA,CAAAyF,aAAA,CAACb,cAAc,EAAA1F,QAAA,KACT0D,KAAK;MACT;AACZ;AACA;AACA;AACA;MACYyB,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEU,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEQ,MAAM,EAAEd;MAAU,CAAC,CAAE;MACtC1B,aAAa,EAAEA,aAAc;MAC7BU,mBAAmB,EAAEY,2BAA4B;MACjDmB,0BAA0B,EAAElB,kCAAmC;MAC/DR,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/D4B,kBAAkB,EAAElB,+BAAgC;MACpDM,uBAAuB,EAAE;QACvBa,KAAK,EAAEb,uBAAuB,EAAEa,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAEf,uBAAuB,EAAEe,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEhB,uBAAuB,EAAEgB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACApD,GAAG,EAAEuC,SAAU;MACfc,oBAAoB,EAClB,CAAClB,aAAa,GACVH,SAAS,GACT5E,QAAQ,CAACkG,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACX5C,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAE4C,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACrB,aAAa;IAAK;IAClBD,QAAQ,gBAER/E,KAAA,CAAAyF,aAAA,CAACtF,yBAAyB,CAACmG,QAAQ;MACjCjE,KAAK,EAAE;QACLmB,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACDsB,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACN3B,aAAa;MACbgC,KAAK;MACL;MACA9B,cAAc;MACd,GAAGT;IACL,CAAC,GAAGgB,IAAI;IAER,IAAIkB,MAAM,KAAKD,SAAS,IAAI1B,aAAa,KAAK0B,SAAS,EAAE;MACvD1B,aAAa,GAAG2B,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACE9E,KAAA,CAAAyF,aAAA,CAACxF,QAAQ,CAACsG,IAAI,EAAArH,QAAA;MACZiG,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEI,OAAO,EAAEpC,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEN,GAAG,EAAEO;IAAO,GACRR,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACA,OAAO,MAAM4D,aAAa,gBAAGxG,KAAK,CAACyG,aAAa,CAAC/D,WAAW,CAAC;AAE7D,MAAMgE,MAAM,gBAAG1G,KAAK,CAAC2C,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM8D,aAAa,GAAG3G,KAAK,CAAC4G,UAAU,CAACJ,aAAa,CAAC,IAAI9D,WAAW;EAEpE,oBAAO1C,KAAA,CAAAyF,aAAA,CAACkB,aAAa,EAAAzH,QAAA,KAAK0D,KAAK;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC/C,CAAC,CAAC;AAEF6D,MAAM,CAACG,WAAW,GAAG,QAAQ;AAE7B,eAAeH,MAAM"}