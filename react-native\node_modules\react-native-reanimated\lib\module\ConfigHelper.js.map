{"version": 3, "names": ["PropsAllowlists", "executeOnUIRuntimeSync", "jsiConfigureProps", "ReanimatedError", "updateLoggerConfig", "shouldBeUseWeb", "SHOULD_BE_USE_WEB", "assertNoOverlapInLists", "key", "NATIVE_THREAD_PROPS_WHITELIST", "UI_THREAD_PROPS_WHITELIST", "configureProps", "Object", "keys", "addWhitelistedNativeProps", "props", "oldSize", "length", "addWhitelistedUIProps", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "PROCESSED_VIEW_NAMES", "Set", "adaptViewConfig", "viewConfig", "viewName", "uiViewClassName", "validAttributes", "has", "propsToAdd", "for<PERSON>ach", "add"], "sourceRoot": "../../src", "sources": ["ConfigHelper.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,sBAAmB;AACnD,SAASC,sBAAsB,EAAEC,iBAAiB,QAAQ,WAAQ;AAClE,SAASC,eAAe,QAAQ,aAAU;AAC1C,SAASC,kBAAkB,QAAQ,mBAAU;AAE7C,SAASC,cAAc,QAAQ,sBAAmB;AAElD,MAAMC,iBAAiB,GAAGD,cAAc,CAAC,CAAC;AAE1C,SAASE,sBAAsBA,CAAA,EAAG;EAChC,KAAK,MAAMC,GAAG,IAAIR,eAAe,CAACS,6BAA6B,EAAE;IAC/D,IAAID,GAAG,IAAIR,eAAe,CAACU,yBAAyB,EAAE;MACpD,MAAM,IAAIP,eAAe,CACvB,cAAcK,GAAG,wFACnB,CAAC;IACH;EACF;AACF;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAS;EACrCJ,sBAAsB,CAAC,CAAC;EACxBL,iBAAiB,CACfU,MAAM,CAACC,IAAI,CAACb,eAAe,CAACU,yBAAyB,CAAC,EACtDE,MAAM,CAACC,IAAI,CAACb,eAAe,CAACS,6BAA6B,CAC3D,CAAC;AACH;AAEA,OAAO,SAASK,yBAAyBA,CACvCC,KAA8B,EACxB;EACN,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CACzBb,eAAe,CAACS,6BAClB,CAAC,CAACQ,MAAM;EACRjB,eAAe,CAACS,6BAA6B,GAAG;IAC9C,GAAGT,eAAe,CAACS,6BAA6B;IAChD,GAAGM;EACL,CAAC;EACD,IACEC,OAAO,KACPJ,MAAM,CAACC,IAAI,CAACb,eAAe,CAACS,6BAA6B,CAAC,CAACQ,MAAM,EACjE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;AAEA,OAAO,SAASO,qBAAqBA,CAACH,KAA8B,EAAQ;EAC1E,MAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CAACb,eAAe,CAACU,yBAAyB,CAAC,CAACO,MAAM;EAC7EjB,eAAe,CAACU,yBAAyB,GAAG;IAC1C,GAAGV,eAAe,CAACU,yBAAyB;IAC5C,GAAGK;EACL,CAAC;EACD,IACEC,OAAO,KAAKJ,MAAM,CAACC,IAAI,CAACb,eAAe,CAACU,yBAAyB,CAAC,CAACO,MAAM,EACzE;IACAN,cAAc,CAAC,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,yBAAyBA,CAACC,MAAoB,EAAE;EAC9D;EACAhB,kBAAkB,CAACgB,MAAM,CAAC;EAC1B;EACA,IAAI,CAACd,iBAAiB,EAAE;IACtBL,sBAAsB,CAACG,kBAAkB,CAAC,CAACgB,MAAM,CAAC;EACpD;AACF;AAEA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAMtC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,UAAsB,EAAQ;EAC5D,MAAMC,QAAQ,GAAGD,UAAU,CAACE,eAAe;EAC3C,MAAMX,KAAK,GAAGS,UAAU,CAACG,eAAe;;EAExC;EACA,IAAI,CAACN,oBAAoB,CAACO,GAAG,CAACH,QAAQ,CAAC,EAAE;IACvC,MAAMI,UAAmC,GAAG,CAAC,CAAC;IAC9CjB,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACe,OAAO,CAAEtB,GAAG,IAAK;MAClC;MACA;MACA,IACE,EAAEA,GAAG,IAAIR,eAAe,CAACS,6BAA6B,CAAC,IACvD,EAAED,GAAG,IAAIR,eAAe,CAACU,yBAAyB,CAAC,EACnD;QACAmB,UAAU,CAACrB,GAAG,CAAC,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IACFU,qBAAqB,CAACW,UAAU,CAAC;IAEjCR,oBAAoB,CAACU,GAAG,CAACN,QAAQ,CAAC;EACpC;AACF;AAEAd,cAAc,CAAC,CAAC", "ignoreList": []}