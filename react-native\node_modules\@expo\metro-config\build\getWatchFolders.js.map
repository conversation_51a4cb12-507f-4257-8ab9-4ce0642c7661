{"version": 3, "file": "getWatchFolders.js", "sourceRoot": "", "sources": ["../src/getWatchFolders.ts"], "names": [], "mappings": ";;;;;AA2BA,0DAkBC;AAMD,kFAUC;AAMD,0CAiBC;AApFD,8CAAgF;AAChF,4CAAoB;AACpB,+BAAgC;AAChC,gDAAwB;AAExB,SAAS,YAAY,CAAC,QAAgB;IACpC,eAAe;IACf,MAAM,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC/C,wBAAwB;IACxB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,eAAe,CAAC,QAAgB;IACvC,IAAI,CAAC;QACH,uCAAuC;QACvC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CACrC,oBAA4B,EAC5B,cAAwB;IAExB,OAAO,cAAc;SAClB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,+DAA+D;QAC/D,OAAO,IAAA,eAAQ,EAAC,cAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;YACzE,GAAG,EAAE,oBAAoB;YACzB,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,CAAC,qCAAqC,CAAC;SAChD,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACjB,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,IAAI,EAAE;SACN,MAAM,CAAC,OAAO,CAAC;SACf,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAI,CAAC,IAAI,CAAC,CAAW,CAAC,CAAC,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,SAAgB,mCAAmC,CAAC,oBAA4B;IAC9E,IAAI,CAAC;QACH,6FAA6F;QAC7F,MAAM,cAAc,GAAG,IAAA,8BAAsB,EAAC,oBAAoB,CAAC,CAAC;QACpE,IAAI,CAAC,cAAc,EAAE,MAAM;YAAE,OAAO,EAAE,CAAC;QACvC,sDAAsD;QACtD,OAAO,uBAAuB,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;IACvE,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAAC,WAAmB;IACjD,MAAM,mBAAmB,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACtD,MAAM,aAAa,GAAG,IAAA,0BAAkB,EAAC,mBAAmB,CAAC,CAAC;IAC9D,iDAAiD;IACjD,IAAI,aAAa,KAAK,mBAAmB,EAAE,CAAC;QAC1C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,QAAQ,GAAG,mCAAmC,CAAC,aAAa,CAAC,CAAC;IACpE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;QACtB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,OAAO,WAAW,CAAC;QACjB,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC;QACxC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC5C,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,KAAe;IAClC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,CAAC"}