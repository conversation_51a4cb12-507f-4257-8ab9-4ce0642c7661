{"version": 3, "sources": ["utils.ts"], "names": ["toArray", "object", "Array", "isArray", "withPrevAndCurrent", "array", "mapFn", "previousArr", "currentArr", "transformedArr", "for<PERSON>ach", "current", "i", "previous", "transformed", "push", "hasProperty", "key", "Object", "prototype", "hasOwnProperty", "call", "isJestEnv", "global", "process", "env", "JEST_WORKER_ID", "tagMessage", "msg", "isF<PERSON><PERSON>", "nativeFabricUIManager", "isRemoteDebuggingEnabled", "localGlobal", "nativeCallSyncHook", "__REMOTEDEV__", "RN$Bridgeless", "deepEqual", "obj1", "obj2", "keys1", "keys", "keys2", "length", "includes", "INT32_MAX"], "mappings": ";;;;;;;;;;;;;;;AAAO,SAASA,OAAT,CAAoBC,MAApB,EAA0C;AAC/C,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAL,EAA4B;AAC1B,WAAO,CAACA,MAAD,CAAP;AACD;;AAED,SAAOA,MAAP;AACD;;AAMM,SAASG,kBAAT,CACLC,KADK,EAELC,KAFK,EAGU;AACf,QAAMC,WAAmC,GAAG,CAAC,IAAD,CAA5C;AACA,QAAMC,UAAU,GAAG,CAAC,GAAGH,KAAJ,CAAnB;AACA,QAAMI,cAA6B,GAAG,EAAtC;AACAD,EAAAA,UAAU,CAACE,OAAX,CAAmB,CAACC,OAAD,EAAUC,CAAV,KAAgB;AACjC;AACA;AACA;AACA,UAAMC,QAAQ,GAAGN,WAAW,CAACK,CAAD,CAA5B;AACA,UAAME,WAAW,GAAGR,KAAK,CAACO,QAAD,EAAWF,OAAX,CAAzB;AACAJ,IAAAA,WAAW,CAACQ,IAAZ,CAAiBD,WAAjB;AACAL,IAAAA,cAAc,CAACM,IAAf,CAAoBD,WAApB;AACD,GARD;AASA,SAAOL,cAAP;AACD,C,CAED;;;AACO,SAASO,WAAT,CAAqBf,MAArB,EAAqCgB,GAArC,EAAkD;AACvD,SAAOC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCpB,MAArC,EAA6CgB,GAA7C,CAAP;AACD;;AAEM,SAASK,SAAT,GAA8B;AACnC;AACA,SAAON,WAAW,CAACO,MAAD,EAAS,SAAT,CAAX,IAAkC,CAAC,CAACC,OAAO,CAACC,GAAR,CAAYC,cAAvD;AACD;;AAEM,SAASC,UAAT,CAAoBC,GAApB,EAAiC;AACtC,SAAQ,kCAAiCA,GAAI,EAA7C;AACD,C,CAED;AACA;;;AACO,SAASC,QAAT,GAA6B;AAAA;;AAClC;AACA,SAAO,CAAC,aAACN,MAAD,oCAAC,QAAQO,qBAAT,CAAR;AACD;;AAEM,SAASC,wBAAT,GAA6C;AAClD;AACA;AACA,QAAMC,WAAW,GAAGT,MAApB;AACA,SACE,CAAC,CAACS,WAAW,CAACC,kBAAb,IAAmC,CAAC,CAACD,WAAW,CAACE,aAAlD,KACA,CAACF,WAAW,CAACG,aAFf;AAID;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASC,SAAT,CAAmBC,IAAnB,EAA8BC,IAA9B,EAAyC;AAC9C,MAAID,IAAI,KAAKC,IAAb,EAAmB;AACjB,WAAO,IAAP;AACD;;AAED,MACE,OAAOD,IAAP,KAAgB,QAAhB,IACA,OAAOC,IAAP,KAAgB,QADhB,IAEAD,IAAI,KAAK,IAFT,IAGAC,IAAI,KAAK,IAJX,EAKE;AACA,WAAO,KAAP;AACD;;AAED,QAAMC,KAAK,GAAGrB,MAAM,CAACsB,IAAP,CAAYH,IAAZ,CAAd;AACA,QAAMI,KAAK,GAAGvB,MAAM,CAACsB,IAAP,CAAYF,IAAZ,CAAd;;AAEA,MAAIC,KAAK,CAACG,MAAN,KAAiBD,KAAK,CAACC,MAA3B,EAAmC;AACjC,WAAO,KAAP;AACD;;AAED,OAAK,MAAMzB,GAAX,IAAkBsB,KAAlB,EAAyB;AACvB,QAAI,CAACE,KAAK,CAACE,QAAN,CAAe1B,GAAf,CAAD,IAAwB,CAACmB,SAAS,CAACC,IAAI,CAACpB,GAAD,CAAL,EAAYqB,IAAI,CAACrB,GAAD,CAAhB,CAAtC,EAA8D;AAC5D,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAEM,MAAM2B,SAAS,GAAG,KAAK,EAAL,GAAU,CAA5B", "sourcesContent": ["export function toArray<T>(object: T | T[]): T[] {\n  if (!Array.isArray(object)) {\n    return [object];\n  }\n\n  return object;\n}\n\nexport type withPrevAndCurrentMapFn<T, Transformed> = (\n  previous: Transformed | null,\n  current: T\n) => Transformed;\nexport function withPrevAndCurrent<T, Transformed>(\n  array: T[],\n  mapFn: withPrevAndCurrentMapFn<T, Transformed>\n): Transformed[] {\n  const previousArr: (null | Transformed)[] = [null];\n  const currentArr = [...array];\n  const transformedArr: Transformed[] = [];\n  currentArr.forEach((current, i) => {\n    // This type cast is fine and solves problem mentioned in https://github.com/software-mansion/react-native-gesture-handler/pull/2867 (namely that `previous` can be undefined).\n    // Unfortunately, linter on our CI does not allow this type of casting as it is unnecessary. To bypass that we use eslint-disable.\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    const previous = previousArr[i] as Transformed | null;\n    const transformed = mapFn(previous, current);\n    previousArr.push(transformed);\n    transformedArr.push(transformed);\n  });\n  return transformedArr;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function hasProperty(object: object, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nexport function isJestEnv(): boolean {\n  // @ts-ignore Do not use `@types/node` because it will prioritise Node types over RN types which breaks the types (ex. setTimeout) in React Native projects.\n  return hasProperty(global, 'process') && !!process.env.JEST_WORKER_ID;\n}\n\nexport function tagMessage(msg: string) {\n  return `[react-native-gesture-handler] ${msg}`;\n}\n\n// Helper method to check whether Fabric is enabled, however global.nativeFabricUIManager\n// may not be initialized before the first render\nexport function isFabric(): boolean {\n  // @ts-expect-error nativeFabricUIManager is not yet included in the RN types\n  return !!global?.nativeFabricUIManager;\n}\n\nexport function isRemoteDebuggingEnabled(): boolean {\n  // react-native-reanimated checks if in remote debugging in the same way\n  // @ts-ignore global is available but node types are not included\n  const localGlobal = global as any;\n  return (\n    (!localGlobal.nativeCallSyncHook || !!localGlobal.__REMOTEDEV__) &&\n    !localGlobal.RN$Bridgeless\n  );\n}\n\n/**\n * Recursively compares two objects for deep equality.\n *\n * **Note:** This function does not support cyclic references.\n *\n * @param obj1 - The first object to compare.\n * @param obj2 - The second object to compare.\n * @returns `true` if the objects are deeply equal, `false` otherwise.\n */\nexport function deepEqual(obj1: any, obj2: any) {\n  if (obj1 === obj2) {\n    return true;\n  }\n\n  if (\n    typeof obj1 !== 'object' ||\n    typeof obj2 !== 'object' ||\n    obj1 === null ||\n    obj2 === null\n  ) {\n    return false;\n  }\n\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport const INT32_MAX = 2 ** 31 - 1;\n"]}