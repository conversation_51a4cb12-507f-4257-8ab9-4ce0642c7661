{"version": 3, "file": "props.js", "sourceRoot": "", "sources": ["../../src/utils/props.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,aAAa,MAAM,sBAAsB,CAAC;AAEjD,2EAA2E;AAC3E,MAAM,CAAC,MAAM,gBAAgB,GAIzB;IACF,IAAI,EAAE,aAAa,CAAC,IAAI;IACxB,KAAK,EAAE,aAAa,CAAC,SAAS;CAC/B,CAAC;AAEF,MAAM,UAAU,kBAAkB,CAAC,KAAuB;IACxD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,WAAW,GAAsB,EAAE,CAAC;IAE1C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,GAAG,GAAgC,CAAC;QAC9C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YACxD,WAAW,CAAC,GAA8B,CAAC;gBACzC,gBAAgB,CAAC,IAAwB,CAAC,CAAC,KAAY,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,WAAW,CAAC,GAA8B,CAAC,GAAG,KAAK,CAAC;QACtD,CAAC;IACH,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,KAAuB;IACvD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAE3C,QAAQ,CAAC,qBAAqB,GAAG,CAAC,CAAC,KAAK,EAAE,gBAAgB,CAAC;IAC3D,QAAQ,CAAC,SAAS,GAAG,KAAK,EAAE,KAAK,IAAI,KAAK,CAAC;IAC3C,QAAQ,CAAC,IAAI,GAAG,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;IACrC,QAAQ,CAAC,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,KAAK,CAAC;IAE/C,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { CameraNativeProps, CameraType, FlashMode, CameraViewProps } from '../Camera.types';\nimport CameraManager from '../ExpoCameraManager';\n\n// Values under keys from this object will be transformed to native options\nexport const ConversionTables: {\n  type: Record<keyof CameraType, CameraNativeProps['facing']>;\n  flash: Record<keyof FlashMode, CameraNativeProps['flashMode']>;\n  [prop: string]: unknown;\n} = {\n  type: CameraManager.Type,\n  flash: CameraManager.FlashMode,\n};\n\nexport function convertNativeProps(props?: CameraViewProps): CameraNativeProps {\n  if (!props || typeof props !== 'object') {\n    return {};\n  }\n\n  const nativeProps: CameraNativeProps = {};\n\n  for (const [key, value] of Object.entries(props)) {\n    const prop = key as 'type' | 'flash' | string;\n    if (typeof value === 'string' && ConversionTables[prop]) {\n      nativeProps[key as keyof CameraNativeProps] =\n        ConversionTables[prop as 'type' | 'flash'][value as any];\n    } else {\n      nativeProps[key as keyof CameraNativeProps] = value;\n    }\n  }\n\n  return nativeProps;\n}\n\nexport function ensureNativeProps(props?: CameraViewProps): CameraNativeProps {\n  const newProps = convertNativeProps(props);\n\n  newProps.barcodeScannerEnabled = !!props?.onBarcodeScanned;\n  newProps.flashMode = props?.flash ?? 'off';\n  newProps.mute = props?.mute ?? false;\n  newProps.autoFocus = props?.autofocus ?? 'off';\n\n  if (Platform.OS !== 'web') {\n    delete newProps.poster;\n  }\n\n  return newProps;\n}\n"]}