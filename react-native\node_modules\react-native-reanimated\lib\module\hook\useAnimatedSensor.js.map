{"version": 3, "names": ["useEffect", "useMemo", "useRef", "initializeSensor", "registerSensor", "unregisterSensor", "SensorType", "IOSReferenceFrame", "InterfaceOrientation", "callMicrotasks", "eulerToQuaternion", "pitch", "roll", "yaw", "c1", "Math", "cos", "s1", "sin", "c2", "s2", "c3", "s3", "adjustRotationToInterfaceOrientation", "data", "interfaceOrientation", "ROTATION_90", "PI", "ROTATION_270", "ROTATION_180", "q", "qx", "qy", "qz", "qw", "adjustVectorToInterfaceOrientation", "x", "y", "useAnimatedSensor", "sensorType", "userConfig", "userConfigRef", "hasConfigChanged", "current", "adjustToInterfaceOrientation", "interval", "iosReferenceFrame", "config", "Auto", "ref", "sensor", "unregister", "isAvailable", "sensorData", "id", "ROTATION", "value"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedSensor.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAS;AAO5E,SACEC,UAAU,EACVC,iBAAiB,EACjBC,oBAAoB,QACf,mBAAgB;AACvB,SAASC,cAAc,QAAQ,eAAY;;AAE3C;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAa,EAAEC,IAAY,EAAEC,GAAW,EAAE;EACnE,SAAS;;EACT,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,GAAG,CAAC,CAAC;EAC9B,MAAMM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACP,KAAK,GAAG,CAAC,CAAC;EAC9B,MAAMQ,EAAE,GAAGJ,IAAI,CAACC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC;EAC7B,MAAMQ,EAAE,GAAGL,IAAI,CAACG,GAAG,CAACN,IAAI,GAAG,CAAC,CAAC;EAC7B,MAAMS,EAAE,GAAGN,IAAI,CAACC,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC;EAC5B,MAAMS,EAAE,GAAGP,IAAI,CAACG,GAAG,CAACL,GAAG,GAAG,CAAC,CAAC;EAE5B,OAAO,CACLI,EAAE,GAAGE,EAAE,GAAGE,EAAE,GAAGP,EAAE,GAAGM,EAAE,GAAGE,EAAE,EAC3BR,EAAE,GAAGM,EAAE,GAAGC,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGG,EAAE,EAC3BR,EAAE,GAAGK,EAAE,GAAGG,EAAE,GAAGL,EAAE,GAAGG,EAAE,GAAGC,EAAE,EAC3BP,EAAE,GAAGK,EAAE,GAAGE,EAAE,GAAGJ,EAAE,GAAGG,EAAE,GAAGE,EAAE,CAC5B;AACH;AAEA,SAASC,oCAAoCA,CAACC,IAAmB,EAAE;EACjE,SAAS;;EACT,MAAM;IAAEC,oBAAoB;IAAEd,KAAK;IAAEC,IAAI;IAAEC;EAAI,CAAC,GAAGW,IAAI;EACvD,IAAIC,oBAAoB,KAAKjB,oBAAoB,CAACkB,WAAW,EAAE;IAC7DF,IAAI,CAACb,KAAK,GAAGC,IAAI;IACjBY,IAAI,CAACZ,IAAI,GAAG,CAACD,KAAK;IAClBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACY,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIF,oBAAoB,KAAKjB,oBAAoB,CAACoB,YAAY,EAAE;IACrEJ,IAAI,CAACb,KAAK,GAAG,CAACC,IAAI;IAClBY,IAAI,CAACZ,IAAI,GAAGD,KAAK;IACjBa,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGE,IAAI,CAACY,EAAE,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIF,oBAAoB,KAAKjB,oBAAoB,CAACqB,YAAY,EAAE;IACrEL,IAAI,CAACb,KAAK,IAAI,CAAC,CAAC;IAChBa,IAAI,CAACZ,IAAI,IAAI,CAAC,CAAC;IACfY,IAAI,CAACX,GAAG,IAAI,CAAC,CAAC;EAChB;EAEA,MAAMiB,CAAC,GAAGpB,iBAAiB,CAACc,IAAI,CAACb,KAAK,EAAEa,IAAI,CAACZ,IAAI,EAAEY,IAAI,CAACX,GAAG,CAAC;EAC5DW,IAAI,CAACO,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACQ,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACS,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;EACdN,IAAI,CAACU,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACd,OAAON,IAAI;AACb;AAEA,SAASW,kCAAkCA,CAACX,IAAa,EAAE;EACzD,SAAS;;EACT,MAAM;IAAEC,oBAAoB;IAAEW,CAAC;IAAEC;EAAE,CAAC,GAAGb,IAAI;EAC3C,IAAIC,oBAAoB,KAAKjB,oBAAoB,CAACkB,WAAW,EAAE;IAC7DF,IAAI,CAACY,CAAC,GAAG,CAACC,CAAC;IACXb,IAAI,CAACa,CAAC,GAAGD,CAAC;EACZ,CAAC,MAAM,IAAIX,oBAAoB,KAAKjB,oBAAoB,CAACoB,YAAY,EAAE;IACrEJ,IAAI,CAACY,CAAC,GAAGC,CAAC;IACVb,IAAI,CAACa,CAAC,GAAG,CAACD,CAAC;EACb,CAAC,MAAM,IAAIX,oBAAoB,KAAKjB,oBAAoB,CAACqB,YAAY,EAAE;IACrEL,IAAI,CAACY,CAAC,IAAI,CAAC,CAAC;IACZZ,IAAI,CAACa,CAAC,IAAI,CAAC,CAAC;EACd;EACA,OAAOb,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASc,iBAAiBA,CAC/BC,UAAsB,EACtBC,UAAkC,EACuB;EACzD,MAAMC,aAAa,GAAGvC,MAAM,CAACsC,UAAU,CAAC;EAExC,MAAME,gBAAgB,GACpBD,aAAa,CAACE,OAAO,EAAEC,4BAA4B,KACjDJ,UAAU,EAAEI,4BAA4B,IAC1CH,aAAa,CAACE,OAAO,EAAEE,QAAQ,KAAKL,UAAU,EAAEK,QAAQ,IACxDJ,aAAa,CAACE,OAAO,EAAEG,iBAAiB,KAAKN,UAAU,EAAEM,iBAAiB;EAE5E,IAAIJ,gBAAgB,EAAE;IACpBD,aAAa,CAACE,OAAO,GAAG;MAAE,GAAGH;IAAW,CAAC;EAC3C;EAEA,MAAMO,MAAoB,GAAG9C,OAAO,CAClC,OAAO;IACL4C,QAAQ,EAAE,MAAM;IAChBD,4BAA4B,EAAE,IAAI;IAClCE,iBAAiB,EAAEvC,iBAAiB,CAACyC,IAAI;IACzC,GAAGP,aAAa,CAACE;EACnB,CAAC,CAAC,EACF,CAACF,aAAa,CAACE,OAAO,CACxB,CAAC;EAED,MAAMM,GAAG,GAAG/C,MAAM,CAA0C;IAC1DgD,MAAM,EAAE/C,gBAAgB,CAACoC,UAAU,EAAEQ,MAAM,CAAC;IAC5CI,UAAU,EAAEA,CAAA,KAAM;MAChB;IAAA,CACD;IACDC,WAAW,EAAE,KAAK;IAClBL;EACF,CAAC,CAAC;EAEF/C,SAAS,CAAC,MAAM;IACdiD,GAAG,CAACN,OAAO,GAAG;MACZO,MAAM,EAAE/C,gBAAgB,CAACoC,UAAU,EAAEQ,MAAM,CAAC;MAC5CI,UAAU,EAAEA,CAAA,KAAM;QAChB;MAAA,CACD;MACDC,WAAW,EAAE,KAAK;MAClBL;IACF,CAAC;IAED,MAAMM,UAAU,GAAGJ,GAAG,CAACN,OAAO,CAACO,MAAM;IACrC,MAAMN,4BAA4B,GAChCK,GAAG,CAACN,OAAO,CAACI,MAAM,CAACH,4BAA4B;IAEjD,MAAMU,EAAE,GAAGlD,cAAc,CAACmC,UAAU,EAAEQ,MAAM,EAAGvB,IAAI,IAAK;MACtD,SAAS;;MACT,IAAIoB,4BAA4B,EAAE;QAChC,IAAIL,UAAU,KAAKjC,UAAU,CAACiD,QAAQ,EAAE;UACtC/B,IAAI,GAAGD,oCAAoC,CAACC,IAAqB,CAAC;QACpE,CAAC,MAAM;UACLA,IAAI,GAAGW,kCAAkC,CAACX,IAAe,CAAC;QAC5D;MACF;MACA6B,UAAU,CAACG,KAAK,GAAGhC,IAAI;MACvBf,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAI6C,EAAE,KAAK,CAAC,CAAC,EAAE;MACb;MACAL,GAAG,CAACN,OAAO,CAACQ,UAAU,GAAG,MAAM9C,gBAAgB,CAACiD,EAAE,CAAC;MACnDL,GAAG,CAACN,OAAO,CAACS,WAAW,GAAG,IAAI;IAChC,CAAC,MAAM;MACL;MACAH,GAAG,CAACN,OAAO,CAACQ,UAAU,GAAG,MAAM;QAC7B;MAAA,CACD;MACDF,GAAG,CAACN,OAAO,CAACS,WAAW,GAAG,KAAK;IACjC;IAEA,OAAO,MAAM;MACXH,GAAG,CAACN,OAAO,CAACQ,UAAU,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACZ,UAAU,EAAEQ,MAAM,CAAC,CAAC;EAExB,OAAOE,GAAG,CAACN,OAAO;AACpB", "ignoreList": []}