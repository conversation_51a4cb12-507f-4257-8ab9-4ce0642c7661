import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { API_CONFIG } from '../config/constants';

const API_BASE_URL = API_CONFIG.BASE_URL;

const FacturesScreen = ({ navigation, route }) => {
  const [factures, setFactures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const clientId = route.params?.clientId;

  useEffect(() => {
    loadFactures();
  }, []);

  const loadFactures = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('📥 Chargement des factures...');

      let url = `${API_BASE_URL}/api/factures`;
      if (clientId) {
        url += `?clientId=${clientId}`;
      }

      const response = await fetch(url);
      const data = await response.json();

      if (data.success) {
        console.log(`✅ ${data.data.length} factures récupérées`);
        setFactures(data.data);
      } else {
        console.error('❌ Erreur lors du chargement des factures:', data.message);
        setError(data.message || 'Erreur lors du chargement des factures');
      }
    } catch (err) {
      console.error('❌ Erreur lors de la requête factures:', err);
      setError('Impossible de charger les factures. Vérifiez votre connexion.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadFactures();
  };

  const handleDownloadPDF = async (factureId) => {
    try {
      Alert.alert('Téléchargement', 'Génération du PDF en cours...');
      
      const pdfUrl = `${API_BASE_URL}/api/factures/${factureId}/pdf`;
      const filename = `facture_${factureId}.pdf`;
      const fileUri = FileSystem.documentDirectory + filename;

      console.log('📄 Téléchargement du PDF:', pdfUrl);

      const downloadResult = await FileSystem.downloadAsync(pdfUrl, fileUri);
      
      if (downloadResult.status === 200) {
        console.log('✅ PDF téléchargé:', downloadResult.uri);
        
        // Partager le fichier PDF
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(downloadResult.uri);
        } else {
          Alert.alert('Succès', `PDF sauvegardé dans ${downloadResult.uri}`);
        }
      } else {
        throw new Error('Erreur lors du téléchargement');
      }
    } catch (error) {
      console.error('❌ Erreur lors du téléchargement PDF:', error);
      Alert.alert('Erreur', 'Impossible de télécharger le PDF');
    }
  };

  const handleChangeStatus = async (factureId, currentStatus) => {
    const newStatus = currentStatus === 'payée' ? 'nonpayée' : 'payée';
    
    Alert.alert(
      'Changer le statut',
      `Marquer cette facture comme ${newStatus} ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Confirmer', 
          onPress: async () => {
            try {
              const response = await fetch(`${API_BASE_URL}/api/factures/${factureId}/status`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: newStatus }),
              });

              const data = await response.json();

              if (data.success) {
                Alert.alert('Succès', `Facture marquée comme ${newStatus}`);
                loadFactures(); // Recharger la liste
              } else {
                Alert.alert('Erreur', data.message || 'Erreur lors de la mise à jour');
              }
            } catch (error) {
              console.error('Erreur lors de la mise à jour du statut:', error);
              Alert.alert('Erreur', 'Erreur de connexion au serveur');
            }
          }
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  };

  const formatMontant = (montant) => {
    return `${parseFloat(montant).toFixed(2)} DH`;
  };

  const getStatusColor = (status) => {
    return status === 'payée' ? '#4CAF50' : '#FF9800';
  };

  const getStatusIcon = (status) => {
    return status === 'payée' ? 'checkmark-circle' : 'time';
  };

  const renderFactureItem = ({ item }) => (
    <View style={styles.factureCard}>
      <View style={styles.factureHeader}>
        <View style={styles.factureInfo}>
          <Text style={styles.factureReference}>Facture #{item.reference}</Text>
          <Text style={styles.factureDate}>{formatDate(item.date)}</Text>
          <Text style={styles.facturePeriode}>Période: {item.periode}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Ionicons 
            name={getStatusIcon(item.status)} 
            size={16} 
            color="#fff" 
            style={styles.statusIcon}
          />
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      <View style={styles.factureDetails}>
        <Text style={styles.clientName}>
          {item.client_prenom} {item.client_nom}
        </Text>
        <Text style={styles.clientAddress}>
          {item.client_adresse}, {item.client_ville}
        </Text>
        <Text style={styles.consommationInfo}>
          Consommation: {item.consommation_precedente} → {item.consommation_actuelle} m³
        </Text>
      </View>

      <View style={styles.factureFooter}>
        <Text style={styles.montant}>{formatMontant(item.montant)}</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleChangeStatus(item.idfact, item.status)}
          >
            <Ionicons 
              name={item.status === 'payée' ? 'close-circle-outline' : 'checkmark-circle-outline'} 
              size={20} 
              color="#2196F3" 
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDownloadPDF(item.idfact)}
          >
            <Ionicons name="download-outline" size={20} color="#2196F3" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Chargement des factures...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#f44336" />
        <Text style={styles.errorTitle}>Erreur de chargement</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadFactures}>
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const facturesPayees = factures.filter(f => f.status === 'payée').length;
  const facturesEnAttente = factures.filter(f => f.status === 'nonpayée').length;

  return (
    <View style={styles.container}>
      {/* Statistiques */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{factures.length}</Text>
          <Text style={styles.statLabel}>Total</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, { color: '#4CAF50' }]}>{facturesPayees}</Text>
          <Text style={styles.statLabel}>Payées</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={[styles.statNumber, { color: '#FF9800' }]}>{facturesEnAttente}</Text>
          <Text style={styles.statLabel}>En attente</Text>
        </View>
      </View>

      {/* Liste des factures */}
      <FlatList
        data={factures}
        renderItem={renderFactureItem}
        keyExtractor={(item) => item.idfact.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              {clientId ? 'Aucune facture pour ce client' : 'Aucune facture enregistrée'}
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f44336',
    marginTop: 10,
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: '#fff',
    marginHorizontal: 15,
    marginTop: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  listContainer: {
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 20,
  },
  factureCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  factureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  factureInfo: {
    flex: 1,
  },
  factureReference: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  factureDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  facturePeriode: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  factureDetails: {
    marginBottom: 15,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  clientAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  consommationInfo: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  factureFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 10,
  },
  montant: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#ccc',
    marginTop: 10,
  },
});

export default FacturesScreen;
