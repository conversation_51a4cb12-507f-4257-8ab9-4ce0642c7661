{"version": 3, "file": "MetroParser.js", "sourceRoot": "", "sources": ["../src/MetroParser.ts"], "names": [], "mappings": ";;;AACA,qCAAkC;AAClC,+CAA4C;AAE5C,MAAa,WAAY,SAAQ,eAAM;IAIrC,YAAmB,SAAoB;QACrC,KAAK,CAAC,SAAS,CAAC,CAAC;QADA,cAAS,GAAT,SAAS,CAAW;QAH/B,2BAAsB,GAAG,KAAK,CAAC;QAC/B,eAAU,GAAa,EAAE,CAAC;IAIlC,CAAC;IAED,KAAK,CAAC,IAAY;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,wFAAwF;IACxF,6EAA6E;IAC7E,4FAA4F;IAC5F,eAAe,CAAC,IAAY;QAC1B,8HAA8H;QAC9H,OAAO,IAAA,yBAAW,EAAC,IAAI,EAAE;YACvB;gBACE,4BAA4B;gBAC5B,GAAG,EAAE;oBACH,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACrC,CAAC;aACF;YACD;gBACE,0BAA0B;gBAC1B,GAAG,EAAE;oBACH,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3C,mEAAmE;oBACnE,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBACpC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;oBACrB,IAAI,iCAAiC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACxD,OAAQ,IAAI,CAAC,SAAiB,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;oBAC1E,CAAC;oBACD,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;gBACnF,CAAC;aACF;YACD;gBACE,IAAI;gBACJ,GAAG,EAAE;oBACH,iDAAiD;oBACjD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;wBAChC,IAAI,OAAO,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;4BAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CACtB,kEAAkE,CACnE,CAAC;4BACF,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gCACtB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BAC5B,CAAC;wBACH,CAAC;wBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAChC,CAAC;gBACH,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;CACF;AA7DD,kCA6DC", "sourcesContent": ["import { Formatter } from './Formatter';\nimport { Parser } from './Parser';\nimport { switchRegex } from './switchRegex';\n\nexport class MetroParser extends Parser {\n  private isCollectingMetroError = false;\n  private metroError: string[] = [];\n\n  constructor(public formatter: Formatter) {\n    super(formatter);\n  }\n\n  parse(text: string): void | string {\n    const results = this.checkMetroError(text);\n    if (results) {\n      return results;\n    }\n    return super.parse(text);\n  }\n\n  // Error for the build script wrapper in expo-updates that catches metro bundler errors.\n  // This can be repro'd by importing a file that doesn't exist, then building.\n  // Metro will fail to generate the JS bundle, and throw an error that should be caught here.\n  checkMetroError(text: string) {\n    // In expo-updates, we wrap the bundler script and add regex around the error message so we can present it nicely to the user.\n    return switchRegex(text, [\n      [\n        /@build-script-error-begin/m,\n        () => {\n          this.isCollectingMetroError = true;\n        },\n      ],\n      [\n        /@build-script-error-end/m,\n        () => {\n          const results = this.metroError.join('\\n');\n          // Reset the metro collection error array (should never need this).\n          this.isCollectingMetroError = false;\n          this.metroError = [];\n          if ('formatMetroAssetCollectionError' in this.formatter) {\n            return (this.formatter as any).formatMetroAssetCollectionError(results);\n          }\n          throw new Error('Current `@expo/xcpretty` formatter cannot handle Metro errors');\n        },\n      ],\n      [\n        null,\n        () => {\n          // Collect all the lines in the metro build error\n          if (this.isCollectingMetroError) {\n            let results = text;\n            if (!this.metroError.length) {\n              const match = text.match(\n                /Error loading assets JSON from Metro.*steps correctly.((.|\\n)*)/m\n              );\n              if (match && match[1]) {\n                results = match[1].trim();\n              }\n            }\n            this.metroError.push(results);\n          }\n        },\n      ],\n    ]);\n  }\n}\n"]}