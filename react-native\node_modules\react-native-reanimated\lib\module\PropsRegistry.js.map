{"version": 3, "names": ["ReanimatedError", "isF<PERSON><PERSON>", "runOnUI", "VIEW_TAGS", "removeFromPropsRegistry", "viewTag", "push", "length", "queueMicrotask", "flush", "__DEV__", "removeFromPropsRegistryOnUI", "viewTags", "global", "_removeFromPropsRegistry"], "sourceRoot": "../../src", "sources": ["PropsRegistry.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,aAAU;AAC1C,SAASC,QAAQ,QAAQ,sBAAmB;AAC5C,SAASC,OAAO,QAAQ,cAAW;AAEnC,IAAIC,SAAmB,GAAG,EAAE;AAE5B,OAAO,SAASC,uBAAuBA,CAACC,OAAe,EAAE;EACvDF,SAAS,CAACG,IAAI,CAACD,OAAO,CAAC;EACvB,IAAIF,SAAS,CAACI,MAAM,KAAK,CAAC,EAAE;IAC1BC,cAAc,CAACC,KAAK,CAAC;EACvB;AACF;AAEA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAIC,OAAO,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAE;IAC1B,MAAM,IAAID,eAAe,CAAC,4CAA4C,CAAC;EACzE;EACAE,OAAO,CAACS,2BAA2B,CAAC,CAACR,SAAS,CAAC;EAC/CA,SAAS,GAAG,EAAE;AAChB;AAEA,SAASQ,2BAA2BA,CAACC,QAAkB,EAAE;EACvD,SAAS;;EACTC,MAAM,CAACC,wBAAwB,CAACF,QAAQ,CAAC;AAC3C", "ignoreList": []}