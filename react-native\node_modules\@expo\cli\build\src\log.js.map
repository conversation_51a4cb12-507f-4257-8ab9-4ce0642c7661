{"version": 3, "sources": ["../../src/log.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nexport function time(label?: string): void {\n  console.time(label);\n}\n\nexport function timeEnd(label?: string): void {\n  console.timeEnd(label);\n}\n\nexport function error(...message: string[]): void {\n  console.error(...message);\n}\n\n/** Print an error and provide additional info (the stack trace) in debug mode. */\nexport function exception(e: Error): void {\n  const { env } = require('./utils/env');\n  error(chalk.red(e.toString()) + (env.EXPO_DEBUG ? '\\n' + chalk.gray(e.stack) : ''));\n}\n\nexport function warn(...message: string[]): void {\n  console.warn(...message.map((value) => chalk.yellow(value)));\n}\n\nexport function log(...message: string[]): void {\n  console.log(...message);\n}\n\n/** @deprecated use `debug` package with the `expo:` prefix instead.  */\nexport function debug(...message: any[]): void {\n  if (require('./utils/env').env.EXPO_DEBUG) console.log(...message);\n}\n\n/** Clear the terminal of all text. */\nexport function clear(): void {\n  process.stdout.write(process.platform === 'win32' ? '\\x1B[2J\\x1B[0f' : '\\x1B[2J\\x1B[3J\\x1B[H');\n}\n\n/** Log a message and exit the current process. If the `code` is non-zero then `console.error` will be used instead of `console.log`. */\nexport function exit(message: string | Error, code: number = 1): never {\n  if (message instanceof Error) {\n    exception(message);\n    process.exit(code);\n  }\n\n  if (message) {\n    if (code === 0) {\n      log(message);\n    } else {\n      error(message);\n    }\n  }\n\n  process.exit(code);\n}\n\n// The re-export makes auto importing easier.\nexport const Log = {\n  time,\n  timeEnd,\n  error,\n  exception,\n  warn,\n  log,\n  debug,\n  clear,\n  exit,\n};\n"], "names": ["Log", "clear", "debug", "error", "exception", "exit", "log", "time", "timeEnd", "warn", "label", "console", "message", "e", "env", "require", "chalk", "red", "toString", "EXPO_DEBUG", "gray", "stack", "map", "value", "yellow", "process", "stdout", "write", "platform", "code", "Error"], "mappings": ";;;;;;;;;;;IAyDaA,GAAG;eAAHA;;IAvBGC,KAAK;eAALA;;IALAC,KAAK;eAALA;;IAnBAC,KAAK;eAALA;;IAKAC,SAAS;eAATA;;IAwBAC,IAAI;eAAJA;;IAfAC,GAAG;eAAHA;;IAtBAC,IAAI;eAAJA;;IAIAC,OAAO;eAAPA;;IAcAC,IAAI;eAAJA;;;;gEApBE;;;;;;;;;;;AAEX,SAASF,KAAKG,KAAc;IACjCC,QAAQJ,IAAI,CAACG;AACf;AAEO,SAASF,QAAQE,KAAc;IACpCC,QAAQH,OAAO,CAACE;AAClB;AAEO,SAASP,MAAM,GAAGS,OAAiB;IACxCD,QAAQR,KAAK,IAAIS;AACnB;AAGO,SAASR,UAAUS,CAAQ;IAChC,MAAM,EAAEC,GAAG,EAAE,GAAGC,QAAQ;IACxBZ,MAAMa,gBAAK,CAACC,GAAG,CAACJ,EAAEK,QAAQ,MAAOJ,CAAAA,IAAIK,UAAU,GAAG,OAAOH,gBAAK,CAACI,IAAI,CAACP,EAAEQ,KAAK,IAAI,EAAC;AAClF;AAEO,SAASZ,KAAK,GAAGG,OAAiB;IACvCD,QAAQF,IAAI,IAAIG,QAAQU,GAAG,CAAC,CAACC,QAAUP,gBAAK,CAACQ,MAAM,CAACD;AACtD;AAEO,SAASjB,IAAI,GAAGM,OAAiB;IACtCD,QAAQL,GAAG,IAAIM;AACjB;AAGO,SAASV,MAAM,GAAGU,OAAc;IACrC,IAAIG,QAAQ,eAAeD,GAAG,CAACK,UAAU,EAAER,QAAQL,GAAG,IAAIM;AAC5D;AAGO,SAASX;IACdwB,QAAQC,MAAM,CAACC,KAAK,CAACF,QAAQG,QAAQ,KAAK,UAAU,mBAAmB;AACzE;AAGO,SAASvB,KAAKO,OAAuB,EAAEiB,OAAe,CAAC;IAC5D,IAAIjB,mBAAmBkB,OAAO;QAC5B1B,UAAUQ;QACVa,QAAQpB,IAAI,CAACwB;IACf;IAEA,IAAIjB,SAAS;QACX,IAAIiB,SAAS,GAAG;YACdvB,IAAIM;QACN,OAAO;YACLT,MAAMS;QACR;IACF;IAEAa,QAAQpB,IAAI,CAACwB;AACf;AAGO,MAAM7B,MAAM;IACjBO;IACAC;IACAL;IACAC;IACAK;IACAH;IACAJ;IACAD;IACAI;AACF"}