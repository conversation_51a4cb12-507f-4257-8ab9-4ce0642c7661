{"version": 3, "names": ["assertFieldTransformed", "path", "node", "declare", "buildCodeFrameError"], "sources": ["../src/typescript.ts"], "sourcesContent": ["import type { NodePath, types as t } from \"@babel/core\";\n\nexport function assertFieldTransformed(\n  path: NodePath<t.ClassProperty | t.ClassDeclaration>,\n) {\n  if (\n    path.node.declare ||\n    (process.env.BABEL_8_BREAKING\n      ? path.isClassProperty({ definite: true })\n      : false)\n  ) {\n    throw path.buildCodeFrameError(\n      `TypeScript 'declare' fields must first be transformed by ` +\n        `@babel/plugin-transform-typescript.\\n` +\n        `If you have already enabled that plugin (or '@babel/preset-typescript'), make sure ` +\n        `that it runs before any plugin related to additional class features:\\n` +\n        ` - @babel/plugin-transform-class-properties\\n` +\n        ` - @babel/plugin-transform-private-methods\\n` +\n        ` - @babel/plugin-proposal-decorators`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAEO,SAASA,sBAAsBA,CACpCC,IAAoD,EACpD;EACA,IACEA,IAAI,CAACC,IAAI,CAACC,OAAO,IAGb,KAAM,EACV;IACA,MAAMF,IAAI,CAACG,mBAAmB,CAC5B,2DAA2D,GACzD,uCAAuC,GACvC,qFAAqF,GACrF,wEAAwE,GACxE,+CAA+C,GAC/C,8CAA8C,GAC9C,sCACJ,CAAC;EACH;AACF", "ignoreList": []}