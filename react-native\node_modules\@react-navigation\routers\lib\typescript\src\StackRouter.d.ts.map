{"version": 3, "file": "StackRouter.d.ts", "sourceRoot": "", "sources": ["../../../src/StackRouter.tsx"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAEV,oBAAoB,EACpB,eAAe,EACf,aAAa,EAEb,MAAM,EACP,MAAM,SAAS,CAAC;AAEjB,MAAM,MAAM,eAAe,GACvB;IACE,IAAI,EAAE,SAAS,CAAC;IAChB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IACrE,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,KAAK,CAAC;IACZ,OAAO,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,GACD;IACE,IAAI,EAAE,YAAY,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEN,MAAM,MAAM,kBAAkB,GAAG,oBAAoB,CAAC;AAEtD,MAAM,MAAM,oBAAoB,CAAC,SAAS,SAAS,aAAa,IAC9D,eAAe,CAAC,SAAS,CAAC,GAAG;IAC3B;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACf,CAAC;AAEJ,MAAM,MAAM,kBAAkB,CAAC,SAAS,SAAS,aAAa,IAAI;IAChE;;;;;OAKG;IACH,OAAO,CAAC,SAAS,SAAS,MAAM,SAAS,EACvC,GAAG,IAAI,EAAE,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GAC3C,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACvE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACpD,IAAI,CAAC;IAER;;;;;OAKG;IACH,IAAI,CAAC,SAAS,SAAS,MAAM,SAAS,EACpC,GAAG,IAAI,EAAE,SAAS,SAAS,SAAS,CAAC,SAAS,CAAC,GAC3C,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACvE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,GACpD,IAAI,CAAC;IAER;;OAEG;IACH,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE1B;;OAEG;IACH,QAAQ,IAAI,IAAI,CAAC;CAClB,CAAC;AAEF,eAAO,MAAM,YAAY;kBACT,MAAM,WAAW,MAAM,GAAG,eAAe;eAG5C,MAAM,WAAW,MAAM,GAAG,eAAe;gBAGzC,MAAM,GAAO,eAAe;gBAG3B,eAAe;CAG5B,CAAC;AAEF,MAAM,CAAC,OAAO,UAAU,WAAW,CAAC,OAAO,EAAE,kBAAkB,mGA4Y9D"}