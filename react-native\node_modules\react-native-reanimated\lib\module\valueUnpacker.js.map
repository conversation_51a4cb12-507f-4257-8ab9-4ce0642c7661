{"version": 3, "names": ["shouldBeUseWeb", "isWorkletFunction", "valueUnpacker", "objectToUnpack", "category", "remoteFunctionName", "workletsCache", "global", "__workletsCache", "handleCache", "__handleCache", "undefined", "Map", "WeakMap", "workletHash", "__workletHash", "workletFun", "get", "initData", "__initData", "evalWithSourceMap", "code", "location", "sourceMap", "evalWithSourceUrl", "eval", "set", "functionInstance", "bind", "_recur", "__init", "value", "fun", "label", "Error", "__remoteFunction", "_toString", "__DEV__", "testWorklet", "closure", "__closure", "Object", "keys", "length", "getValueUnpackerCode"], "sourceRoot": "../../src", "sources": ["valueUnpacker.ts"], "mappings": "AAAA;AACA,YAAY;;AACZ,SAASA,cAAc,QAAQ,sBAAmB;AAClD,SAASC,iBAAiB,QAAQ,kBAAe;AAGjD,SAASC,aAAaA,CACpBC,cAAmB,EACnBC,QAAiB,EACjBC,kBAA2B,EACtB;EACL,SAAS;;EACT,IAAIC,aAAa,GAAGC,MAAM,CAACC,eAAe;EAC1C,IAAIC,WAAW,GAAGF,MAAM,CAACG,aAAa;EACtC,IAAIJ,aAAa,KAAKK,SAAS,EAAE;IAC/B;IACAL,aAAa,GAAGC,MAAM,CAACC,eAAe,GAAG,IAAII,GAAG,CAAC,CAAC;IAClDH,WAAW,GAAGF,MAAM,CAACG,aAAa,GAAG,IAAIG,OAAO,CAAC,CAAC;EACpD;EACA,MAAMC,WAAW,GAAGX,cAAc,CAACY,aAAa;EAChD,IAAID,WAAW,KAAKH,SAAS,EAAE;IAC7B,IAAIK,UAAU,GAAGV,aAAa,CAACW,GAAG,CAACH,WAAW,CAAC;IAC/C,IAAIE,UAAU,KAAKL,SAAS,EAAE;MAC5B,MAAMO,QAAQ,GAAGf,cAAc,CAACgB,UAAU;MAC1C,IAAIZ,MAAM,CAACa,iBAAiB,EAAE;QAC5B;QACA;QACA;QACA;QACAJ,UAAU,GAAGT,MAAM,CAACa,iBAAiB,CACnC,GAAG,GAAGF,QAAQ,CAACG,IAAI,GAAG,KAAK,EAC3BH,QAAQ,CAACI,QAAQ,EACjBJ,QAAQ,CAACK,SACX,CAA4B;MAC9B,CAAC,MAAM,IAAIhB,MAAM,CAACiB,iBAAiB,EAAE;QACnC;QACA;QACA;QACA;QACAR,UAAU,GAAGT,MAAM,CAACiB,iBAAiB,CACnC,GAAG,GAAGN,QAAQ,CAACG,IAAI,GAAG,KAAK,EAC3B,WAAWP,WAAW,EACxB,CAA4B;MAC9B,CAAC,MAAM;QACL;QACA;QACAE,UAAU,GAAGS,IAAI,CAAC,GAAG,GAAGP,QAAQ,CAACG,IAAI,GAAG,KAAK,CAErC;MACV;MACAf,aAAa,CAACoB,GAAG,CAACZ,WAAW,EAAEE,UAAU,CAAC;IAC5C;IACA,MAAMW,gBAAgB,GAAGX,UAAU,CAACY,IAAI,CAACzB,cAAc,CAAC;IACxDA,cAAc,CAAC0B,MAAM,GAAGF,gBAAgB;IACxC,OAAOA,gBAAgB;EACzB,CAAC,MAAM,IAAIxB,cAAc,CAAC2B,MAAM,KAAKnB,SAAS,EAAE;IAC9C,IAAIoB,KAAK,GAAGtB,WAAW,CAACQ,GAAG,CAACd,cAAc,CAAC;IAC3C,IAAI4B,KAAK,KAAKpB,SAAS,EAAE;MACvBoB,KAAK,GAAG5B,cAAc,CAAC2B,MAAM,CAAC,CAAC;MAC/BrB,WAAW,CAACiB,GAAG,CAACvB,cAAc,EAAE4B,KAAK,CAAC;IACxC;IACA,OAAOA,KAAK;EACd,CAAC,MAAM,IAAI3B,QAAQ,KAAK,gBAAgB,EAAE;IACxC,MAAM4B,GAAG,GAAGA,CAAA,KAAM;MAChB,MAAMC,KAAK,GAAG5B,kBAAkB,GAC5B,cAAcA,kBAAkB,IAAI,GACpC,oBAAoB;MACxB,MAAM,IAAI6B,KAAK,CAAC,0DAA0DD,KAAK;AACrF,yKAAyK,CAAC;IACtK,CAAC;IACDD,GAAG,CAACG,gBAAgB,GAAGhC,cAAc;IACrC,OAAO6B,GAAG;EACZ,CAAC,MAAM;IACL,MAAM,IAAIE,KAAK,CACb,uCAAuC9B,QAAQ,wCAAwCgC,SAAS,CAC9FjC,cACF,CAAC,IACH,CAAC;EACH;AACF;AAOA,IAAIkC,OAAO,IAAI,CAACrC,cAAc,CAAC,CAAC,EAAE;EAChC,MAAMsC,WAAW,GAAIA,CAAA,KAAM;IACzB,SAAS;EACX,CAA+B;EAC/B,IAAI,CAACrC,iBAAiB,CAACqC,WAAW,CAAC,EAAE;IACnC,MAAM,IAAIJ,KAAK,CACb,0KACF,CAAC;EACH;EACA,IAAI,CAACjC,iBAAiB,CAACC,aAAa,CAAC,EAAE;IACrC,MAAM,IAAIgC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,MAAMK,OAAO,GAAIrC,aAAa,CAAmBsC,SAAS;EAC1D,IAAID,OAAO,KAAK5B,SAAS,EAAE;IACzB,MAAM,IAAIuB,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIO,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IACrC,MAAM,IAAIT,KAAK,CAAC,sDAAsD,CAAC;EACzE;AACF;AAEA,OAAO,SAASU,oBAAoBA,CAAA,EAAG;EACrC,OAAQ1C,aAAa,CAAmBiB,UAAU,CAACE,IAAI;AACzD", "ignoreList": []}