<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Clients</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Test API Clients - Port 3007</h1>
    <button onclick="testAPI()">Tester l'API Clients</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="loading">🔄 Test en cours...</div>';

            try {
                console.log('🔄 Test de l\'API clients sur le port 3007...');
                
                const response = await fetch('http://localhost:3007/api/clients');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ API clients OK - ${data.count} clients reçus<br>
                            <strong>Clients:</strong> ${data.data.map(c => `${c.nom} ${c.prenom}`).join(', ')}
                        </div>
                    `;
                    console.log('✅ API clients OK:', data);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Erreur API clients: ${data.message || 'Erreur inconnue'}
                        </div>
                    `;
                    console.error('❌ Erreur API clients:', data);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Erreur lors du test API clients: ${error.message}
                    </div>
                `;
                console.error('❌ Erreur lors du test API clients:', error);
            }
        }

        // Test automatique au chargement
        window.onload = () => {
            testAPI();
        };
    </script>
</body>
</html>
