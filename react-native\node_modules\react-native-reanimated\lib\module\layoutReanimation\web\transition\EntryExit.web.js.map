{"version": 3, "names": ["AnimationsData", "ExitingFinalStep", "EnteringStartStep", "addTransformToKeepPosition", "keyframeStyleData", "animationStyle", "transformData", "isExiting", "timestamp", "styles", "Object", "entries", "transform", "undefined", "unshift", "newTimestamp", "parseInt", "index", "Math", "min", "hideComponentBetweenAnimations", "opacityInStep", "Map", "opacity", "set", "step", "EntryExitTransition", "name", "transitionData", "exitingAnimationData", "structuredClone", "exiting", "enteringAnimationData", "entering", "additionalExitingData", "translateX", "translateY", "scale", "scaleX", "scaleY", "additionalEnteringData", "keyframeData", "style", "duration"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/transition/EntryExit.web.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,cAAc,QAAQ,cAAW;AAE1C,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,iBAAiB,GAAG,EAAE;AAQ5B;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CACjCC,iBAAiD,EACjDC,cAA8C,EAC9CC,aAA4B,EAC5BC,SAAkB,EAClB;EACA,KAAK,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,cAAc,CAAC,EAAE;IAChE,IAAII,MAAM,CAACG,SAAS,KAAKC,SAAS,EAAE;MAClC;MACAJ,MAAM,CAACG,SAAS,CAACE,OAAO,CAACR,aAAa,CAAC;IACzC,CAAC,MAAM;MACL;MACAG,MAAM,CAACG,SAAS,GAAG,CAACN,aAAa,CAAC;IACpC;IAEA,MAAMS,YAAY,GAAGC,QAAQ,CAACR,SAAS,CAAC,GAAG,CAAC;IAC5C,MAAMS,KAAK,GAAGV,SAAS,GACnBW,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEd,gBAAgB,CAAC,CAAC;IAAA,EACzCc,YAAY,GAAGb,iBAAiB,CAAC,CAAC;;IAEtCE,iBAAiB,CAAC,GAAGa,KAAK,EAAE,CAAC,GAAGR,MAAM;EACxC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,8BAA8BA,CACrChB,iBAAiD,EACjD;EACA;EACA;EACA,MAAMiB,aAAa,GAAG,IAAIC,GAAG,CAAiB,CAAC;EAE/C,IAAIlB,iBAAiB,CAAC,CAAC,CAAC,CAACmB,OAAO,KAAKV,SAAS,EAAE;IAC9CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,IAAIpB,iBAAiB,CAAC,EAAE,CAAC,CAACmB,OAAO,KAAKV,SAAS,EAAE;IAC/CQ,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxBH,aAAa,CAACG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1B;EAEA,KAAK,MAAM,CAACC,IAAI,EAAEF,OAAO,CAAC,IAAIF,aAAa,EAAE;IAC3CjB,iBAAiB,CAACqB,IAAI,CAAC,GAAG;MACxB,GAAGrB,iBAAiB,CAACqB,IAAI,CAAC;MAC1BF;IACF,CAAC;EACH;AACF;AAEA,OAAO,SAASG,mBAAmBA,CACjCC,IAAY,EACZC,cAA8B,EAC9B;EACA,MAAMC,oBAAoB,GAAGC,eAAe,CAC1C9B,cAAc,CAAC4B,cAAc,CAACG,OAAO,CACvC,CAAC;EACD,MAAMC,qBAAqB,GAAGF,eAAe,CAC3C9B,cAAc,CAAC4B,cAAc,CAACK,QAAQ,CACxC,CAAC;EAED,MAAMC,qBAAoC,GAAG;IAC3CC,UAAU,EAAE,GAAGP,cAAc,CAACO,UAAU,IAAI;IAC5CC,UAAU,EAAE,GAAGR,cAAc,CAACQ,UAAU,IAAI;IAC5CC,KAAK,EAAE,GAAGT,cAAc,CAACU,MAAM,IAAIV,cAAc,CAACW,MAAM;EAC1D,CAAC;EAED,MAAMC,sBAAqC,GAAG;IAC5CL,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMI,YAA2B,GAAG;IAClCd,IAAI;IACJe,KAAK,EAAE,CAAC,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EAEDxC,0BAA0B,CACxBsC,YAAY,CAACC,KAAK,EAClBb,oBAAoB,CAACa,KAAK,EAC1BR,qBAAqB,EACrB,IACF,CAAC;EAED/B,0BAA0B,CACxBsC,YAAY,CAACC,KAAK,EAClBV,qBAAqB,CAACU,KAAK,EAC3BF,sBAAsB,EACtB,KACF,CAAC;EAEDpB,8BAA8B,CAACqB,YAAY,CAACC,KAAK,CAAC;EAElD,OAAOD,YAAY;AACrB", "ignoreList": []}