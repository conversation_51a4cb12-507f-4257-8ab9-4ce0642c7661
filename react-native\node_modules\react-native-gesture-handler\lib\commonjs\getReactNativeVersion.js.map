{"version": 3, "sources": ["getReactNativeVersion.ts"], "names": ["majorStr", "minorStr", "pack", "version", "split", "REACT_NATIVE_VERSION", "major", "parseInt", "minor", "getReactNativeVersion"], "mappings": ";;;;;;;AAAA;;;;AAEA,MAAM,CAACA,QAAD,EAAWC,QAAX,IAAuBC,iBAAKC,OAAL,CAAaC,KAAb,CAAmB,GAAnB,CAA7B;;AACA,MAAMC,oBAAoB,GAAG;AAC3BC,EAAAA,KAAK,EAAEC,QAAQ,CAACP,QAAD,EAAW,EAAX,CADY;AAE3BQ,EAAAA,KAAK,EAAED,QAAQ,CAACN,QAAD,EAAW,EAAX;AAFY,CAA7B;;AAKO,SAASQ,qBAAT,GAAiC;AACtC,SAAOJ,oBAAP;AACD", "sourcesContent": ["import pack from 'react-native/package.json';\n\nconst [majorStr, minorStr] = pack.version.split('.');\nconst REACT_NATIVE_VERSION = {\n  major: parseInt(majorStr, 10),\n  minor: parseInt(minorStr, 10),\n};\n\nexport function getReactNativeVersion() {\n  return REACT_NATIVE_VERSION;\n}\n"]}