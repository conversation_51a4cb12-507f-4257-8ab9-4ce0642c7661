import*as e from"../../core/root/root.js";import*as n from"../../core/sdk/sdk.js";import*as t from"../../models/trace/trace.js";import"../../core/common/common.js";import*as r from"../../core/protocol_client/protocol_client.js";import*as o from"../../models/bindings/bindings.js";import*as s from"../../models/workspace/workspace.js";import*as i from"../../ui/components/code_highlighter/code_highlighter.js";import*as a from"../../ui/legacy/legacy.js";function u(){return!self.testRunner||Boolean(e.Runtime.Runtime.queryParam("debugFrontend"))}self.onerror=(e,n,t,r,o)=>{d("TEST ENDED IN ERROR: "+o.stack),g()},self.addEventListener("unhandledrejection",(e=>{d(`PROMISE FAILURE: ${e.reason.stack??e.reason}`),g()})),u()||(console.log=(...e)=>{d(`log: ${e}`)},console.error=(...e)=>{d(`error: ${e}`)},console.info=(...e)=>{d(`info: ${e}`)},console.assert=(e,...n)=>{e||d(`ASSERTION FAILURE: ${n.join(" ")}`)});let c=[],l=e=>{c.push(String(e))};function d(e){l(e)}let f=!1,m=()=>{f||(f=!0,function(){Array.prototype.forEach.call(document.documentElement.childNodes,(e=>e.remove()));const e=document.createElement("div");e.style&&(e.style.whiteSpace="pre",e.style.height="10px",e.style.overflow="hidden");document.documentElement.appendChild(e);for(let n=0;n<c.length;n++)e.appendChild(document.createTextNode(c[n])),e.appendChild(document.createElement("br"));c=[]}(),self.testRunner.notifyDone())};function g(){m()}function p(e,n){return function(){if(!e)return;const t=this;try{return e.apply(t,arguments)}catch(t){d("Exception while running: "+e+"\n"+(t.stack||t)),n?p(n)():g()}}}function T(e){return async function(){if(!e)return;const n=this;try{return await e.apply(n,arguments)}catch(n){d("Exception while running: "+e+"\n"+(n.stack||n)),g()}}}function R(e){function n(n){let t=0;for(;n&&n!==e;)"OL"!==n.nodeName||n.classList&&n.classList.contains("object-properties-section")||++t,n=n.parentNode;return Array(4*t+1).join(" ")}let t="",r=e,o=!1;for(;r.traverseNextNode(e);)if(r=r.traverseNextNode(e),r.nodeType===Node.TEXT_NODE&&r.parentNode?.nodeType!==Node.DOCUMENT_FRAGMENT_NODE)t+=r.nodeValue;else if("LI"===r.nodeName||"TR"===r.nodeName)o?o=!1:t+="\n"+n(r);else{if("STYLE"===r.nodeName){r=r.traverseNextNode(e);continue}r.classList&&r.classList.contains("object-properties-section")&&(o=!0)}return t}async function h(e,n){const t=await v(e);p(n)(t.result.value,t.exceptionDetails)}self.TestRunner=self.TestRunner||{};let y=0;async function v(n){const t=(new Error).stack.split("at "),o=e.Runtime.Runtime.queryParam("test"),s=t.reduce(((e,n)=>n.includes(o)?n:e),t[t.length-2]).trim().split("/"),i=s[s.length-1].slice(0,-1).split(":"),a=i[0],u=`test://evaluations/${y++}/`+a,c=parseInt(i[1],10);-1===(n="\n".repeat(c-1)+n).indexOf("sourceURL=")&&(n+=`//# sourceURL=${u}`);const l=await TestRunner.RuntimeAgent.invoke_evaluate({expression:n,objectGroup:"console"}),f=l[r.InspectorBackend.ProtocolError];return f?(d("Error: "+f),void g()):l}async function E(e,n){const t=await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console",userGesture:n});if(!t[r.InspectorBackend.ProtocolError])return t.result.value;d("Error: "+(t.exceptionDetails&&t.exceptionDetails.text||"exception from evaluateInPageAnonymously.")),g()}async function M(e){const n=await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console",includeCommandLineAPI:!1,awaitPromise:!0}),t=n[r.InspectorBackend.ProtocolError];if(!t&&!n.exceptionDetails)return n.result.value;let o="Error: ";t?o+=t:n.exceptionDetails&&(o+=n.exceptionDetails.text,n.exceptionDetails.exception&&(o+=" "+n.exceptionDetails.exception.description)),d(o),g()}function w(e){if(!e.includes("<base")){const n=/(<!DOCTYPE.*?>)/i,t=`<base href="${W()}">`;e=e.match(n)?e.replace(n,"$1"+t):t+e}return E(`document.write(\`${e=e.replace(/'/g,"\\'").replace(/\n/g,"\\n")}\`);document.close();`)}const A={formatAsTypeName:e=>"<"+typeof e+">",formatAsTypeNameOrNull:e=>null===e?"null":A.formatAsTypeName(e),formatAsRecentTime(e){if("object"!=typeof e||!(e instanceof Date))return A.formatAsTypeName(e);const n=Date.now()-e;return 0<=n&&n<18e5?"<plausible>":e},formatAsURL(e){if(!e)return e;const n=e.lastIndexOf("devtools/");return n<0?e:".../"+e.substr(n)},formatAsDescription:e=>e?'"'+e.replace(/^function [gs]et /,"function ")+'"':e};function x(e,n,t,r){t=t||"",d((r=r||t)+"{");const o=Object.keys(e);o.sort();for(let r=0;r<o.length;++r){const s=o[r];if(!e.hasOwnProperty(s))continue;const i="    "+t+s+" : ",a=e[s];if(n&&n[s]){const e=n[s];if("skip"!==e){d(i+(0,A[e])(a))}}else b(a,n,"    "+t,i)}d(t+"}")}function P(e,n,t,r){t=t||"",d((r=r||t)+"[");for(let r=0;r<e.length;++r)b(e[r],n,t+"    ");d(t+"]")}function b(e,n,t,r){(r=r||t)&&r.length>80?d(r+"was skipped due to prefix length limit"):null===e?d(r+"null"):e&&e.constructor&&"Array"===e.constructor.name?P(e,n,t,r):"object"==typeof e?x(e,n,t,r):d("string"==typeof e?r+'"'+e+'"':r+e)}function S(e,n,t){return t=t||function(){return!0},new Promise((r=>{n.addEventListener(e,(function o(s){if(!t(s.data))return;n.removeEventListener(e,o),r(s.data)}))}))}function N(e){return e.executionContexts().length?Promise.resolve(e.executionContexts()[0]):e.once(n.RuntimeModel.Events.ExecutionContextCreated)}let k;function I(e,t){k=p(t),TestRunner.resourceTreeModel.addEventListener(n.ResourceTreeModel.Events.Load,L),E("window.location.replace('"+e+"')")}function L(){TestRunner.resourceTreeModel.removeEventListener(n.ResourceTreeModel.Events.Load,L),j()}function O(e){D(!1,void 0,e)}function D(e,t,r){k=p(r),TestRunner.resourceTreeModel.addEventListener(n.ResourceTreeModel.Events.Load,C),TestRunner.resourceTreeModel.reloadPage(e,t)}function C(){TestRunner.resourceTreeModel.removeEventListener(n.ResourceTreeModel.Events.Load,C),d("Page reloaded."),j()}async function j(){if(await N(TestRunner.runtimeModel),k){const e=k;k=void 0,e()}}function F(e,n,t){if(e===n)return;let r;throw r=t?"Failure ("+t+"):":"Failure:",new Error(r+" expected <"+e+"> found <"+n+">")}function W(n=""){const t=e.Runtime.Runtime.queryParam("inspected_test")||e.Runtime.Runtime.queryParam("test");return new URL(n,t+"/../").href}async function B(){const n=e.Runtime.Runtime.queryParam("test");if(u())return t=console.log,l=t,m=()=>console.log("Test completed"),void(self.test=async function(){await import(n)});var t;try{await import(n)}catch(e){d("TEST ENDED EARLY DUE TO UNCAUGHT ERROR:"),d(e&&e.stack||e),d("=== DO NOT COMMIT THIS INTO -expected.txt ==="),g()}}self.testRunner,TestRunner.StringOutputStream=class{constructor(e){this.callback=e,this.buffer=""}async open(e){return!0}async write(e){this.buffer+=e}async close(){this.callback(this.buffer)}},TestRunner.MockSetting=class{constructor(e){this.value=e}get(){return this.value}set(e){this.value=e}},TestRunner.formatters=A,TestRunner.completeTest=g,TestRunner.addResult=d,TestRunner.addResults=function(e){if(e)for(let n=0,t=e.length;n<t;++n)d(e[n])},TestRunner.runTests=function(e){!function n(){const t=e.shift();if(!t)return void g();d("\ntest: "+t.name);let r=t();r instanceof Promise||(r=Promise.resolve());r.then(n)}()},TestRunner.addSniffer=function(e,n,t,r){t=p(t);const o=e[n];if("function"!=typeof o)throw new Error("Cannot find method to override: "+n);e[n]=function(s){let i;try{i=o.apply(this,arguments)}finally{r||(e[n]=o)}try{Array.prototype.push.call(arguments,i),t.apply(this,arguments)}catch(e){throw new Error("Exception in overriden method '"+n+"': "+e)}return i}},TestRunner.addSnifferPromise=function(e,n){return new Promise((function(t,r){const o=e[n];"function"==typeof o?e[n]=function(s){let i;try{i=o.apply(this,arguments)}finally{e[n]=o}try{Array.prototype.push.call(arguments,i),t.apply(this,arguments)}catch(e){r("Exception in overridden method '"+n+"': "+e),g()}return i}:r("Cannot find method to override: "+n)}))},TestRunner.showPanel=function(e){return a.ViewManager.ViewManager.instance().showView(e)},TestRunner.createKeyEvent=function(e,n,t,r,o){return new KeyboardEvent("keydown",{key:e,bubbles:!0,cancelable:!0,ctrlKey:Boolean(n),altKey:Boolean(t),shiftKey:Boolean(r),metaKey:Boolean(o)})},TestRunner.safeWrap=p,TestRunner.textContentWithLineBreaks=R,TestRunner.textContentWithLineBreaksTrimmed=function(e){return R(e).replace(/\s{3,}/g," ")},TestRunner.textContentWithoutStyles=function(e){let n="",t=e;for(;t=t.traverseNextNode(e,"DEVTOOLS-CSS-LENGTH"===t.tagName||"DEVTOOLS-ICON"===t.tagName),t;)t.nodeType===Node.TEXT_NODE?n+=t.nodeValue:"STYLE"===t.nodeName&&(t=t.traverseNextNode(e));return n},TestRunner.evaluateInPagePromise=function(e){return new Promise((n=>h(e,n)))},TestRunner.callFunctionInPageAsync=function(e,n){return M(e+"("+(n=n||[]).map((e=>JSON.stringify(e))).join(",")+")")},TestRunner.evaluateInPageWithTimeout=function(e,n){E("setTimeout(unescape('"+escape(e)+"'), 1)",n)},TestRunner.evaluateFunctionInOverlay=function(e,n){const t='internals.evaluateInInspectorOverlay("(" + '+e+' + ")()")';TestRunner.runtimeModel.executionContexts()[0].evaluate({expression:t,objectGroup:"",includeCommandLineAPI:!1,silent:!1,returnByValue:!0,generatePreview:!1},!1,!1).then((e=>{n(e.object.value)}))},TestRunner.check=function(e,n){e||d("FAIL: "+n)},TestRunner.deprecatedRunAfterPendingDispatches=function(e){r.InspectorBackend.test.deprecatedRunAfterPendingDispatches(e)},TestRunner.loadHTML=w,TestRunner.addScriptTag=function(e){return M(`\n    (function(){\n      let script = document.createElement('script');\n      script.src = '${e}';\n      document.head.append(script);\n      return new Promise(f => script.onload = f);\n    })();\n  `)},TestRunner.addStylesheetTag=function(e){return M(`\n    (function(){\n      const link = document.createElement('link');\n      link.rel = 'stylesheet';\n      link.href = '${e}';\n      link.onload = onload;\n      document.head.append(link);\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function onload() {\n        // TODO(chenwilliam): It shouldn't be necessary to force\n        // style recalc here but some tests rely on it.\n        window.getComputedStyle(document.body).color;\n        resolve();\n      }\n      return promise;\n    })();\n  `)},TestRunner.addIframe=function(e,n={}){return n.id=n.id||"",n.name=n.name||"",M(`\n    (function(){\n      const iframe = document.createElement('iframe');\n      iframe.src = '${e}';\n      iframe.id = '${n.id}';\n      iframe.name = '${n.name}';\n      document.body.appendChild(iframe);\n      return new Promise(f => iframe.onload = f);\n    })();\n  `)},TestRunner.markStep=function(e){d("\nRunning: "+e)},TestRunner.startDumpingProtocolMessages=function(){r.InspectorBackend.test.dumpProtocol=self.testRunner.logToStderr.bind(self.testRunner)},TestRunner.addScriptForFrame=function(e,n,t){n+="\n//# sourceURL="+e;const r=TestRunner.runtimeModel.executionContexts().find((e=>e.frameId===t.id));TestRunner.RuntimeAgent.evaluate(n,"console",!1,!1,r.id)},TestRunner.addObject=x,TestRunner.addArray=P,TestRunner.dumpDeepInnerHTML=function(e){!function e(n,t){const r=[];if(t.nodeType===Node.TEXT_NODE)return void(t.parentElement&&"STYLE"===t.parentElement.nodeName||d(t.nodeValue));r.push("<"+t.nodeName);const o=t.attributes;for(let e=0;o&&e<o.length;++e)r.push(o[e].name+"="+o[e].value);r.push(">"),d(n+r.join(" "));for(let r=t.firstChild;r;r=r.nextSibling)e(n+"    ",r);t.shadowRoot&&e(n+"    ",t.shadowRoot),d(n+"</"+t.nodeName+">")}("",e)},TestRunner.deepTextContent=function e(n){if(!n)return"";if(n.nodeType===Node.TEXT_NODE&&n.nodeValue)return n.parentElement&&"STYLE"===n.parentElement.nodeName?"":n.nodeValue;let t="";const r=n.childNodes;for(let n=0;n<r.length;++n)t+=e(r[n]);return n.shadowRoot&&(t+=e(n.shadowRoot)),t},TestRunner.dump=b,TestRunner.waitForEvent=S,TestRunner.waitForTarget=function(e){e=e||(e=>!0);for(const t of n.TargetManager.TargetManager.instance().targets())if(e(t))return Promise.resolve(t);return new Promise((t=>{const r={targetAdded:function(o){e(o)&&(n.TargetManager.TargetManager.instance().unobserveTargets(r),t(o))},targetRemoved:function(){}};n.TargetManager.TargetManager.instance().observeTargets(r)}))},TestRunner.waitForTargetRemoved=function(e){return new Promise((t=>{const r={targetRemoved:function(o){o===e&&(n.TargetManager.TargetManager.instance().unobserveTargets(r),t(o))},targetAdded:function(){}};n.TargetManager.TargetManager.instance().observeTargets(r)}))},TestRunner.waitForExecutionContext=N,TestRunner.waitForExecutionContextDestroyed=function(e){const t=e.runtimeModel;return-1===t.executionContexts().indexOf(e)?Promise.resolve():S(n.RuntimeModel.Events.ExecutionContextDestroyed,t,(n=>n===e))},TestRunner.assertGreaterOrEqual=function(e,n,t){e<n&&d("FAILED: "+(t?t+": ":"")+e+" < "+n)},TestRunner.navigate=I,TestRunner.navigatePromise=function(e){return new Promise((n=>I(e,n)))},TestRunner.hardReloadPage=function(e){D(!0,void 0,e)},TestRunner.reloadPage=O,TestRunner.reloadPageWithInjectedScript=function(e,n){D(!1,e,n)},TestRunner.reloadPagePromise=function(){return new Promise((e=>O(e)))},TestRunner.pageLoaded=C,TestRunner.waitForPageLoad=function(e){TestRunner.resourceTreeModel.addEventListener(n.ResourceTreeModel.Events.Load,(function t(){TestRunner.resourceTreeModel.removeEventListener(n.ResourceTreeModel.Events.Load,t),e()}))},TestRunner.runWhenPageLoads=function(e){const n=k;k=p((function(){n&&n(),e()}))},TestRunner.runTestSuite=function(e){const n=e.slice();!function e(){if(!n.length)return void g();const t=n.shift();d(""),d("Running: "+/function\s([^(]*)/.exec(t)[1]),p(t)(e)}()},TestRunner.assertEquals=F,TestRunner.assertTrue=function(e,n){F(!0,Boolean(e),n)},TestRunner.override=function(e,n,t,r){t=p(t);const o=e[n];if("function"!=typeof o)throw new Error("Cannot find method to override: "+n);return e[n]=function(s){try{return t.apply(this,arguments)}catch(e){throw new Error("Exception in overriden method '"+n+"': "+e)}finally{r||(e[n]=o)}},o},TestRunner.clearSpecificInfoFromStackFrames=function(e){let n=e.replace(/\(file:\/\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)");return n=n.replace(/\(http:\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)"),n=n.replace(/\(test:\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)"),n=n.replace(/\(<anonymous>:[^)]+\)/g,"(...)"),n=n.replace(/VM\d+/g,"VM"),n.replace(/\s*at[^()]+\(native\)/g,"")},TestRunner.hideInspectorView=function(){a.InspectorView.InspectorView.instance().element.setAttribute("style","display:none !important")},TestRunner.mainFrame=function(){return TestRunner.resourceTreeModel.mainFrame},TestRunner.waitForUISourceCode=function(e,n){function t(t){return(!n||t.project().type()===n)&&(!(!n&&t.project().type()===s.Workspace.projectTypes.Service)&&!(e&&!t.url().endsWith(e)))}for(const n of s.Workspace.WorkspaceImpl.instance().uiSourceCodes())if(e&&t(n))return Promise.resolve(n);return S(s.Workspace.Events.UISourceCodeAdded,s.Workspace.WorkspaceImpl.instance(),t)},TestRunner.waitForUISourceCodeRemoved=function(e){s.Workspace.WorkspaceImpl.instance().once(s.Workspace.Events.UISourceCodeRemoved).then(e)},TestRunner.url=W,TestRunner.dumpSyntaxHighlight=function(e,n){const t=document.createElement("span");return t.textContent=e,i.CodeHighlighter.highlightNode(t,n).then((function(){const n=[];for(let e=0;e<t.childNodes.length;e++)t.childNodes[e].getAttribute?n.push(t.childNodes[e].getAttribute("class")):n.push("*");d(e+": "+n.join(", "))}))},TestRunner.evaluateInPageRemoteObject=async function(e){const n=await v(e);return TestRunner.runtimeModel.createRemoteObject(n.result)},TestRunner.evaluateInPage=h,TestRunner.evaluateInPageAnonymously=E,TestRunner.evaluateInPageAsync=M,TestRunner.deprecatedInitAsync=async function(e){await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console"})},TestRunner.runAsyncTestSuite=async function(e){for(const n of e)d(""),d("Running: "+/function\s([^(]*)/.exec(n)[1]),await T(n)();g()},TestRunner.dumpInspectedPageElementText=async function(e){d(await M(`document.querySelector('${e}').innerText`))},TestRunner.waitForPendingLiveLocationUpdates=async function(){await o.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().pendingLiveLocationChangesPromise(),await o.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().pendingLiveLocationChangesPromise()},TestRunner.findLineEndingIndexes=function(e){const n=function(e,n){const t=[];let r=e.indexOf(n);for(;-1!==r;)t.push(r),r=e.indexOf(n,r+n.length);return t}(e,"\n");return n.push(e.length),n},TestRunner.selectTextInTextNode=function(e,n,t){n=n||0,t=t||e.textContent.length,n<0&&(n=t+n);const r=e.getComponentSelection();r.removeAllRanges();const o=e.ownerDocument.createRange();return o.setStart(e,n),o.setEnd(e,t),r.addRange(o),e},TestRunner.isScrolledToBottom=a.UIUtils.isScrolledToBottom;let U=!1;class ${targetAdded(e){if("main"===e.id()&&"frame"===e.type()||"tab"===e.parentTarget()?.type()&&"frame"===e.type()&&!e.targetInfo()?.subtype?.length){if(function(e){self.TestRunner.BrowserAgent=e.browserAgent(),self.TestRunner.CSSAgent=e.cssAgent(),self.TestRunner.DeviceOrientationAgent=e.deviceOrientationAgent(),self.TestRunner.DOMAgent=e.domAgent(),self.TestRunner.DOMDebuggerAgent=e.domdebuggerAgent(),self.TestRunner.DebuggerAgent=e.debuggerAgent(),self.TestRunner.EmulationAgent=e.emulationAgent(),self.TestRunner.HeapProfilerAgent=e.heapProfilerAgent(),self.TestRunner.InputAgent=e.inputAgent(),self.TestRunner.InspectorAgent=e.inspectorAgent(),self.TestRunner.NetworkAgent=e.networkAgent(),self.TestRunner.OverlayAgent=e.overlayAgent(),self.TestRunner.PageAgent=e.pageAgent(),self.TestRunner.ProfilerAgent=e.profilerAgent(),self.TestRunner.RuntimeAgent=e.runtimeAgent(),self.TestRunner.TargetAgent=e.targetAgent(),self.TestRunner.networkManager=e.model(n.NetworkManager.NetworkManager),self.TestRunner.securityOriginManager=e.model(n.SecurityOriginManager.SecurityOriginManager),self.TestRunner.storageKeyManager=e.model(n.StorageKeyManager.StorageKeyManager),self.TestRunner.resourceTreeModel=e.model(n.ResourceTreeModel.ResourceTreeModel),self.TestRunner.debuggerModel=e.model(n.DebuggerModel.DebuggerModel),self.TestRunner.runtimeModel=e.model(n.RuntimeModel.RuntimeModel),self.TestRunner.domModel=e.model(n.DOMModel.DOMModel),self.TestRunner.domDebuggerModel=e.model(n.DOMDebuggerModel.DOMDebuggerModel),self.TestRunner.cssModel=e.model(n.CSSModel.CSSModel),self.TestRunner.cpuProfilerModel=e.model(n.CPUProfilerModel.CPUProfilerModel),self.TestRunner.overlayModel=e.model(n.OverlayModel.OverlayModel),self.TestRunner.serviceWorkerManager=e.model(n.ServiceWorkerManager.ServiceWorkerManager),self.TestRunner.tracingManager=e.model(t.TracingManager.TracingManager),self.TestRunner.mainTarget=e}(e),U)return;U=!0,w(`\n        <head>\n          <base href="${W()}">\n        </head>\n        <body>\n        </body>\n      `).then((()=>B()))}}targetRemoved(e){}}n.TargetManager.TargetManager.instance().observeTargets(new $);const V=self.TestRunner;export{V as TestRunner,$ as _TestObserver,B as _executeTestScript};
