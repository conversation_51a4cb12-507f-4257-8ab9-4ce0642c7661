{"version": 3, "names": ["linkingHandlers", "useLinking", "ref", "independent", "enabled", "prefixes", "filter", "config", "getInitialURL", "Promise", "race", "Linking", "resolve", "setTimeout", "subscribe", "listener", "callback", "url", "subscription", "addEventListener", "removeEventListener", "bind", "remove", "getStateFromPath", "getStateFromPathDefault", "getActionFromState", "getActionFromStateDefault", "React", "useEffect", "process", "env", "NODE_ENV", "undefined", "length", "console", "error", "Platform", "OS", "join", "trim", "handler", "Symbol", "push", "index", "indexOf", "splice", "enabledRef", "useRef", "prefixesRef", "filterRef", "configRef", "getInitialURLRef", "getStateFromPathRef", "getActionFromStateRef", "current", "getStateFromURL", "useCallback", "path", "extractPathFromURL", "getInitialState", "state", "then", "thenable", "onfulfilled", "catch", "navigation", "rootState", "getRootState", "routes", "some", "r", "routeNames", "includes", "name", "warn", "action", "dispatch", "e", "message", "resetRoot"], "sourceRoot": "../../src", "sources": ["useLinking.native.tsx"], "mappings": ";;;;;;AAAA;AAMA;AACA;AAEA;AAAsD;AAAA;AAAA;AAStD,IAAIA,eAAyB,GAAG,EAAE;AAEnB,SAASC,UAAU,CAChCC,GAA2D,QAuC3D;EAAA,IAtCA;IACEC,WAAW;IACXC,OAAO,GAAG,IAAI;IACdC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,aAAa,GAAG,MACdC,OAAO,CAACC,IAAI,CAAC,CACXC,oBAAO,CAACH,aAAa,EAAE,EACvB,IAAIC,OAAO,CAAaG,OAAO;IAC7B;IACA;IACAC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CACzB,CACF,CAAC;IACJE,SAAS,GAAIC,QAAQ,IAAK;MAAA;MACxB,MAAMC,QAAQ,GAAG;QAAA,IAAC;UAAEC;QAAqB,CAAC;QAAA,OAAKF,QAAQ,CAACE,GAAG,CAAC;MAAA;MAE5D,MAAMC,YAAY,GAAGP,oBAAO,CAACQ,gBAAgB,CAAC,KAAK,EAAEH,QAAQ,CAEhD;;MAEb;MACA;MACA,MAAMI,mBAAmB,4BAAGT,oBAAO,CAACS,mBAAmB,0DAA3B,sBAA6BC,IAAI,CAACV,oBAAO,CAAC;MAEtE,OAAO,MAAM;QACX;QACA,IAAIO,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEI,MAAM,EAAE;UACxBJ,YAAY,CAACI,MAAM,EAAE;QACvB,CAAC,MAAM;UACLF,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAG,KAAK,EAAEJ,QAAQ,CAAC;QACxC;MACF,CAAC;IACH,CAAC;IACDO,gBAAgB,GAAGC,sBAAuB;IAC1CC,kBAAkB,GAAGC;EACd,CAAC;EAEVC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAOC,SAAS;IAClB;IAEA,IAAI7B,WAAW,EAAE;MACf,OAAO6B,SAAS;IAClB;IAEA,IAAI5B,OAAO,KAAK,KAAK,IAAIJ,eAAe,CAACiC,MAAM,EAAE;MAC/CC,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,EAC5DC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,sJAAsJ,GACtJ,EAAE,CACP,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,EAAE,CACV;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,EAAE;IAExB,IAAIrC,OAAO,KAAK,KAAK,EAAE;MACrBJ,eAAe,CAAC0C,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAMG,KAAK,GAAG3C,eAAe,CAAC4C,OAAO,CAACJ,OAAO,CAAC;MAE9C,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACd3C,eAAe,CAAC6C,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAACvC,OAAO,EAAED,WAAW,CAAC,CAAC;;EAE1B;EACA;EACA;EACA,MAAM2C,UAAU,GAAGnB,KAAK,CAACoB,MAAM,CAAC3C,OAAO,CAAC;EACxC,MAAM4C,WAAW,GAAGrB,KAAK,CAACoB,MAAM,CAAC1C,QAAQ,CAAC;EAC1C,MAAM4C,SAAS,GAAGtB,KAAK,CAACoB,MAAM,CAACzC,MAAM,CAAC;EACtC,MAAM4C,SAAS,GAAGvB,KAAK,CAACoB,MAAM,CAACxC,MAAM,CAAC;EACtC,MAAM4C,gBAAgB,GAAGxB,KAAK,CAACoB,MAAM,CAACvC,aAAa,CAAC;EACpD,MAAM4C,mBAAmB,GAAGzB,KAAK,CAACoB,MAAM,CAACxB,gBAAgB,CAAC;EAC1D,MAAM8B,qBAAqB,GAAG1B,KAAK,CAACoB,MAAM,CAACtB,kBAAkB,CAAC;EAE9DE,KAAK,CAACC,SAAS,CAAC,MAAM;IACpBkB,UAAU,CAACQ,OAAO,GAAGlD,OAAO;IAC5B4C,WAAW,CAACM,OAAO,GAAGjD,QAAQ;IAC9B4C,SAAS,CAACK,OAAO,GAAGhD,MAAM;IAC1B4C,SAAS,CAACI,OAAO,GAAG/C,MAAM;IAC1B4C,gBAAgB,CAACG,OAAO,GAAG9C,aAAa;IACxC4C,mBAAmB,CAACE,OAAO,GAAG/B,gBAAgB;IAC9C8B,qBAAqB,CAACC,OAAO,GAAG7B,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAM8B,eAAe,GAAG5B,KAAK,CAAC6B,WAAW,CACtCvC,GAA8B,IAAK;IAClC,IAAI,CAACA,GAAG,IAAKgC,SAAS,CAACK,OAAO,IAAI,CAACL,SAAS,CAACK,OAAO,CAACrC,GAAG,CAAE,EAAE;MAC1D,OAAOe,SAAS;IAClB;IAEA,MAAMyB,IAAI,GAAG,IAAAC,2BAAkB,EAACV,WAAW,CAACM,OAAO,EAAErC,GAAG,CAAC;IAEzD,OAAOwC,IAAI,KAAKzB,SAAS,GACrBoB,mBAAmB,CAACE,OAAO,CAACG,IAAI,EAAEP,SAAS,CAACI,OAAO,CAAC,GACpDtB,SAAS;EACf,CAAC,EACD,EAAE,CACH;EAED,MAAM2B,eAAe,GAAGhC,KAAK,CAAC6B,WAAW,CAAC,MAAM;IAC9C,IAAII,KAA8B;IAElC,IAAId,UAAU,CAACQ,OAAO,EAAE;MACtB,MAAMrC,GAAG,GAAGkC,gBAAgB,CAACG,OAAO,EAAE;MAEtC,IAAIrC,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAC1C,OAAOA,GAAG,CAAC4C,IAAI,CAAE5C,GAAG,IAAK;UACvB,MAAM2C,KAAK,GAAGL,eAAe,CAACtC,GAAG,CAAC;UAElC,OAAO2C,KAAK;QACd,CAAC,CAAC;MACJ;MAEAA,KAAK,GAAGL,eAAe,CAACtC,GAAG,CAAC;IAC9B;IAEA,MAAM6C,QAAQ,GAAG;MACfD,IAAI,CAACE,WAAsD,EAAE;QAC3D,OAAOtD,OAAO,CAACG,OAAO,CAACmD,WAAW,GAAGA,WAAW,CAACH,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDI,KAAK,GAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;EACjB,CAAC,EAAE,CAACP,eAAe,CAAC,CAAC;EAErB5B,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,MAAMb,QAAQ,GAAIE,GAAW,IAAK;MAChC,IAAI,CAACb,OAAO,EAAE;QACZ;MACF;MAEA,MAAM6D,UAAU,GAAG/D,GAAG,CAACoD,OAAO;MAC9B,MAAMM,KAAK,GAAGK,UAAU,GAAGV,eAAe,CAACtC,GAAG,CAAC,GAAGe,SAAS;MAE3D,IAAIiC,UAAU,IAAIL,KAAK,EAAE;QACvB;QACA;QACA,MAAMM,SAAS,GAAGD,UAAU,CAACE,YAAY,EAAE;QAE3C,IAAIP,KAAK,CAACQ,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAK,EAACJ,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEK,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,EAAC,EAAE;UACrEvC,OAAO,CAACwC,IAAI,CACV,0SAA0S,CAC3S;UACD;QACF;QAEA,MAAMC,MAAM,GAAGtB,qBAAqB,CAACC,OAAO,CAACM,KAAK,EAAEV,SAAS,CAACI,OAAO,CAAC;QAEtE,IAAIqB,MAAM,KAAK3C,SAAS,EAAE;UACxB,IAAI;YACFiC,UAAU,CAACW,QAAQ,CAACD,MAAM,CAAC;UAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;YACV;YACA;YACA3C,OAAO,CAACwC,IAAI,CACT,qDAAoDzD,GAAI,MACvD,OAAO4D,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACC,OAAO,GACTD,CACL,EAAC,CACH;UACH;QACF,CAAC,MAAM;UACLZ,UAAU,CAACc,SAAS,CAACnB,KAAK,CAAC;QAC7B;MACF;IACF,CAAC;IAED,OAAO9C,SAAS,CAACC,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACX,OAAO,EAAEmD,eAAe,EAAErD,GAAG,EAAEY,SAAS,CAAC,CAAC;EAE9C,OAAO;IACL6C;EACF,CAAC;AACH"}