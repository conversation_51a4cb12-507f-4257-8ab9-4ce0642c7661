{"version": 3, "file": "ExpoFontLoader.js", "sourceRoot": "", "sources": ["../src/ExpoFontLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,MAAM,CAAC,GACL,OAAO,MAAM,KAAK,WAAW;IAC3B,CAAC,CAAC,oBAAoB;QACpB;YACE,cAAc;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,SAAS,KAAI,CAAC;SACf;IACH,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAC5C,eAAe,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nconst m =\n  typeof window === 'undefined'\n    ? // React server mock\n      {\n        getLoadedFonts() {\n          return [];\n        },\n        loadAsync() {},\n      }\n    : requireNativeModule('ExpoFontLoader');\nexport default m;\n"]}