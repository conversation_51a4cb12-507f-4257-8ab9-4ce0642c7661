{"version": 3, "file": "ExpoRunFormatter.js", "sourceRoot": "", "sources": ["../src/ExpoRunFormatter.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,4CAAoB;AACpB,gDAAwB;AAExB,2CAAsE;AACtE,+CAA4C;AAC5C,yDAAsD;AACtD,+DAAyE;AACzE,6CAAwC;AASxC;;GAEG;AACH,MAAa,gBAAiB,SAAQ,qBAAS;IAC7C,MAAM,CAAC,MAAM,CACX,WAAmB,EACnB,EACE,YAAY,EACZ,OAAO,MACyE,EAAE;;QAEpF,MAAM,OAAO,GAAG,CAAA,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,0CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;QACrE,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC;YACrC,WAAW;YACX,OAAO;YACP,OAAO;SACR,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAMD,IAAI,MAAM;QACR,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YAAmB,KAA4B;;QAC7C,KAAK,CAAC,KAAK,CAAC,CAAC;QADI,UAAK,GAAL,KAAK,CAAuB;QAG7C,IAAI,OAAO,GAAgB,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QAExE,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC7D,OAAO,GAAG,MAAA,IAAA,mCAAgB,EAAC,eAAe,CAAC,mCAAI,EAAE,CAAC;QACpD,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QAEV,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,CAAC;YACrC,GAAG,KAAK;YACR,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B,CAAC,aAAqB;QACnD,MAAM,OAAO,GAAG,KAAK,eAAK,CAAC,GAAG,CAC5B,eAAK;YACH,8BAA8B;YAC9B,+BAA+B;YAC/B,aAAa,CAChB,uEAAuE,CAAC;QACzE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,wBAAwB,CAAC,QAAgB,EAAE,UAAmB,EAAE,YAAqB;QACnF,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,CACL,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC;YAC/B,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC;YAChC,qDAAqD;YACrD,CAAC,qBAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAChC,CAAC;IACJ,CAAC;IAED,uBAAuB,CACrB,UAAkB,EAClB,UAAgD;QAEhD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;QACL,0EAA0E;QAC1E,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACzF,OAAO,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,YAAY,CAAA,CAAC;QACnC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,yBAAyB,CAAC,EAAE,MAAM,EAAuB;QACvD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACzE,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,YAAY,CAAC;IAChD,CAAC;IAED,iBAAiB,CAAC,QAAgB,EAAE,MAAe;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACvE,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzD,CAAC;IAED,mBAAmB,CAAC,KAAoB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3E,OAAO,qBAAS,CAAC,MAAM,CACrB,KAAK,EACL,CAAC,aAAa,EAAE,qBAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;aACrF,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAiB;QAC5C,IAAI,YAAY,GAAG,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACxE,+DAA+D;QAC/D,kEAAkE;QAClE,IAAI,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC/C,YAAY,GAAG,qBAAS,CAAC,0BAA0B,CACjD,qBAAS,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,CAC1C,CAAC;QACJ,CAAC;QACD,MAAM,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAC7C,MAAM,eAAe,GAAG,qBAAS,CAAC,0BAA0B,CAC1D,qBAAS,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CACxC,CAAC;QACF,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACzD,OAAO,qBAAS,CAAC,MAAM,CACrB,WAAW,EACX,CAAC,aAAa,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACvF,CAAC;IACJ,CAAC;IAED,0BAA0B,CAAC,UAAkB,EAAE,MAAe,EAAE,OAAgB;QAC9E,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAEzD,IAAI,UAAU,KAAK,gBAAgB,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,MAAM,CAAC;YAClD,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAChE,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,oBAAoB,IAAI,EAAE,CAAC;YACpE,oFAAoF;YACpF,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,GAAG,eAAK,CAAC,IAAI,CAAC,GAAG,UAAU,aAAa,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,UAAU,GAAG,eAAK,CAAC,IAAI,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,qBAAS,CAAC,MAAM,CACrB,WAAW,EACX,CAAC,aAAa,EAAE,qBAAS,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;aACrE,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;IACJ,CAAC;CACF;AA3JD,4CA2JC;AAED,SAAS,QAAQ,CAAC,KAAU;IAC1B,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC;AAC1D,CAAC", "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { CopyFileProps, FileOperation, Formatter } from './Formatter';\nimport { MetroParser } from './MetroParser';\nimport { PodfileTracer } from './utils/PodfileTracer';\nimport { parsePodfileLock, PodfileLock } from './utils/parsePodfileLock';\nimport { ERROR } from './utils/symbols';\n\nexport type ExpoRunFormatterProps = {\n  projectRoot: string;\n  podfile?: Record<string, Record<string, string>>;\n  appName?: string;\n  isDebug?: boolean;\n};\n\n/**\n * A superset of `Formatter` which adds support for Metro build errors and cleaner formatting for Node projects.\n */\nexport class ExpoRunFormatter extends Formatter {\n  static create(\n    projectRoot: string,\n    {\n      xcodeProject,\n      isDebug,\n    }: { xcodeProject?: { name: string } } & Pick<ExpoRunFormatterProps, 'isDebug'> = {}\n  ) {\n    const appName = xcodeProject?.name.match(/.*\\/(.*)\\.\\w+/)?.[1] || '';\n    const formatter = new ExpoRunFormatter({\n      projectRoot,\n      appName,\n      isDebug,\n    });\n\n    return formatter;\n  }\n\n  private podfileTracer: PodfileTracer;\n\n  _parser: MetroParser | undefined;\n\n  get parser(): MetroParser {\n    if (this._parser) {\n      return this._parser;\n    }\n    this._parser = new MetroParser(this);\n    return this._parser;\n  }\n\n  constructor(public props: ExpoRunFormatterProps) {\n    super(props);\n\n    let podfile: PodfileLock = {};\n    const podfileLock = path.join(props.projectRoot, 'ios', 'Podfile.lock');\n\n    try {\n      const podfileContents = fs.readFileSync(podfileLock, 'utf8');\n      podfile = parsePodfileLock(podfileContents) ?? {};\n    } catch {}\n\n    this.podfileTracer = new PodfileTracer({\n      ...props,\n      podfile,\n    });\n  }\n\n  formatMetroAssetCollectionError(errorContents: string): string {\n    const results = `\\n${chalk.red(\n      ERROR +\n        // Provide proper attribution.\n        'Metro encountered an error:\\n' +\n        errorContents\n    )}\\nLearn more: https://docs.expo.dev/build-reference/troubleshooting\\n`;\n    this.errors.push(results);\n    return results;\n  }\n\n  shouldShowCompileWarning(filePath: string, lineNumber?: string, columnNumber?: string): boolean {\n    if (this.props.isDebug) {\n      return true;\n    }\n    return (\n      !filePath.match(/node_modules/) &&\n      !filePath.match(/\\/ios\\/Pods\\//) &&\n      // Don't show warnings in the generated build folder.\n      !Formatter.getAppRoot(filePath)\n    );\n  }\n\n  shouldShowLinkerWarning(\n    methodName: string,\n    collisions: { filePath: string; name: string }[]\n  ): boolean {\n    if (this.props.isDebug) {\n      return true;\n    }\n\n    return (\n      // Don't show warnings from cocoapods that come from non-root target pods.\n      !collisions.some(({ filePath }) => {\n        const nodeModule = this.podfileTracer.getNodeModuleNameForTarget(path.dirname(filePath));\n        return !nodeModule?.isRootTarget;\n      })\n    );\n  }\n\n  shouldShowWarningInTarget({ target }: { target?: string }): boolean {\n    if (this.props.isDebug || !target) {\n      return true;\n    }\n\n    const nodeModule = this.podfileTracer.getNodeModuleNameForTarget(target);\n    return !nodeModule || nodeModule.isRootTarget;\n  }\n\n  getNodeModuleName(filePath: string, target?: string): string | null {\n    const results = this.podfileTracer.getNodeModuleName(filePath, target);\n    return results?.name ? chalk.cyan(results.name) : null;\n  }\n\n  formatFileOperation(props: FileOperation): string {\n    const title = this.getFileOperationTitle(props.type);\n    const moduleNameTag = this.getNodeModuleName(props.filePath, props.target);\n    return Formatter.format(\n      title,\n      [moduleNameTag, Formatter.formatBreadCrumb(props.fileName, props.target, props.project)]\n        .filter(Boolean)\n        .join(' ')\n    );\n  }\n\n  formatCopy({ from, to, target }: CopyFileProps): string {\n    let relativeFile = Formatter.relativePath(this.props.projectRoot, from);\n    // If the relative file reaches outside of the project root, we\n    // should attempt to resolve relative to the app output directory.\n    if (relativeFile.startsWith('../../')) {\n      const appFileRoot = Formatter.getAppRoot(from);\n      relativeFile = Formatter.highlightLastPathComponent(\n        Formatter.relativePath(appFileRoot, from)\n      );\n    }\n    const appFileRoot = Formatter.getAppRoot(to);\n    const relativeAppFile = Formatter.highlightLastPathComponent(\n      Formatter.relativePath(appFileRoot, to)\n    );\n    const moduleNameTag = this.getNodeModuleName('', target);\n    return Formatter.format(\n      'Copying  ',\n      [moduleNameTag, [relativeFile, relativeAppFile].join(' ➜ ')].filter(Boolean).join(' ')\n    );\n  }\n\n  formatPhaseScriptExecution(scriptName: string, target?: string, project?: string): string {\n    const moduleNameTag = this.getNodeModuleName('', target);\n\n    if (scriptName === 'Start Packager') {\n      const port = process.env.RCT_METRO_PORT || '8081';\n      const isDisabled = isTruthy(process.env.RCT_NO_LAUNCH_PACKAGER);\n      const status = isDisabled ? 'disabled' : `http://localhost:${port}`;\n      // Add some insight into if the bundler was started or not, and which port was used.\n      if (isDisabled) {\n        scriptName = chalk.gray(`${scriptName} (disabled)`);\n      } else {\n        scriptName = scriptName + chalk.gray(` on ${status}`);\n      }\n    }\n\n    return Formatter.format(\n      'Executing',\n      [moduleNameTag, Formatter.formatBreadCrumb(scriptName, target, project)]\n        .filter(Boolean)\n        .join(' ')\n    );\n  }\n}\n\nfunction isTruthy(value: any): boolean {\n  return value === 'true' || value === 1 || value === '1';\n}\n"]}