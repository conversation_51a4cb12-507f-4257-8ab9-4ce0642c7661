{"version": 3, "names": ["logger", "noopFactory", "defaultReturnValue", "warn", "RNScreensTurboModule", "global", "startTransition", "topScreenId", "belowTopScreenId", "canStartTransition", "updateTransition", "finishTransition"], "sourceRoot": "../../../src", "sources": ["screenTransition/RNScreensTurboModule.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,MAAM,QAAQ,oBAAW;AAElC,SAASC,WAAWA,CAAIC,kBAAsB,EAAW;EACvD,OAAO,MAAM;IACX,SAAS;;IACTF,MAAM,CAACG,IAAI,CACT,qJACF,CAAC;IACD,OAAOD,kBAAkB;EAC3B,CAAC;AACH;AAQA,OAAO,MAAME,oBAA8C,GACzDC,MAAM,CAACD,oBAAoB,IAAI;EAC7BE,eAAe,EAAEL,WAAW,CAAoB;IAC9CM,WAAW,EAAE,CAAC,CAAC;IACfC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACFC,gBAAgB,EAAET,WAAW,CAAC,CAAC;EAC/BU,gBAAgB,EAAEV,WAAW,CAAC;AAChC,CAAC", "ignoreList": []}