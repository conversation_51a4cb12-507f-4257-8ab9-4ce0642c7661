import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { API_CONFIG } from '../config/constants';

const API_BASE_URL = API_CONFIG.BASE_URL;

const MapScreen = ({ navigation, route }) => {
  const [location, setLocation] = useState(null);
  const [clients, setClients] = useState([]);
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState(null);
  const [clientsWithContracts, setClientsWithContracts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSecteurModal, setShowSecteurModal] = useState(false);
  const clientId = route.params?.clientId;

  useEffect(() => {
    getCurrentLocation();
    loadSecteurs();
    if (clientId) {
      loadSpecificClient();
    }
  }, []);

  const getCurrentLocation = async () => {
    try {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission refusée', 'Permission de localisation refusée');
        return;
      }

      let location = await Location.getCurrentPositionAsync({});
      setLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } catch (error) {
      console.error('Erreur lors de la géolocalisation:', error);
      // Position par défaut (Sefrou, Maroc)
      setLocation({
        latitude: 33.8307,
        longitude: -4.8372,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
      });
    } finally {
      setLoading(false);
    }
  };

  const loadSecteurs = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      if (data.success) {
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des secteurs:', error);
    }
  };

  const loadSpecificClient = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();
      if (data.success) {
        const client = data.data.find(c => c.idclient === clientId);
        if (client) {
          loadClientContracts([client]);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement du client:', error);
    }
  };

  const loadClientsBySecteur = async (secteurId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();
      if (data.success) {
        const clientsInSecteur = data.data.filter(c => c.secteur_id === secteurId);
        loadClientContracts(clientsInSecteur);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
    }
  };

  const loadClientContracts = async (clientsList) => {
    try {
      const contractsResponse = await fetch(`${API_BASE_URL}/api/contracts`);
      const contractsData = await contractsResponse.json();
      
      if (contractsData.success) {
        const clientsWithContractsData = clientsList.map(client => {
          const clientContracts = contractsData.data.filter(c => c.idclient === client.idclient);
          return {
            ...client,
            contracts: clientContracts
          };
        }).filter(client => client.contracts.length > 0);
        
        setClientsWithContracts(clientsWithContractsData);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des contrats:', error);
    }
  };

  const handleSecteurSelect = (secteur) => {
    setSelectedSecteur(secteur);
    setShowSecteurModal(false);
    loadClientsBySecteur(secteur.ids);
  };

  const handleMarkerPress = (client, contract) => {
    Alert.alert(
      'Informations Client',
      `${client.prenom} ${client.nom}\n${client.adresse}\nTél: ${client.tel}\nQR: ${contract.codeqr}`,
      [
        { text: 'Fermer', style: 'cancel' },
        { 
          text: 'Saisir Consommation', 
          onPress: () => navigation.navigate('Consommation', { 
            selectedClient: client,
            selectedContract: contract 
          })
        },
        { 
          text: 'Voir Factures', 
          onPress: () => navigation.navigate('Factures', { clientId: client.idclient })
        },
      ]
    );
  };

  const renderSecteurItem = ({ item }) => (
    <TouchableOpacity
      style={styles.secteurItem}
      onPress={() => handleSecteurSelect(item)}
    >
      <Ionicons name="location-outline" size={24} color="#2196F3" />
      <Text style={styles.secteurName}>{item.nom}</Text>
      <Ionicons name="chevron-forward-outline" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {location && (
        <MapView
          style={styles.map}
          initialRegion={location}
          showsUserLocation={true}
          showsMyLocationButton={true}
        >
          {clientsWithContracts.map((client) =>
            client.contracts.map((contract) => {
              if (contract.posx && contract.posy) {
                return (
                  <Marker
                    key={`${client.idclient}-${contract.idcontract}`}
                    coordinate={{
                      latitude: parseFloat(contract.posx),
                      longitude: parseFloat(contract.posy),
                    }}
                    title={`${client.prenom} ${client.nom}`}
                    description={`QR: ${contract.codeqr}`}
                    onPress={() => handleMarkerPress(client, contract)}
                  >
                    <View style={styles.customMarker}>
                      <Ionicons name="water" size={20} color="#fff" />
                    </View>
                  </Marker>
                );
              }
              return null;
            })
          )}
        </MapView>
      )}

      {/* Bouton de sélection de secteur */}
      {!clientId && (
        <TouchableOpacity
          style={styles.secteurButton}
          onPress={() => setShowSecteurModal(true)}
        >
          <Ionicons name="list-outline" size={20} color="#fff" />
          <Text style={styles.secteurButtonText}>
            {selectedSecteur ? selectedSecteur.nom : 'Sélectionner un secteur'}
          </Text>
        </TouchableOpacity>
      )}

      {/* Informations */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          {clientsWithContracts.length} compteur{clientsWithContracts.length > 1 ? 's' : ''} affiché{clientsWithContracts.length > 1 ? 's' : ''}
        </Text>
      </View>

      {/* Modal de sélection de secteur */}
      <Modal
        visible={showSecteurModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Sélectionner un secteur</Text>
            <TouchableOpacity onPress={() => setShowSecteurModal(false)}>
              <Ionicons name="close-outline" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          <FlatList
            data={secteurs}
            renderItem={renderSecteurItem}
            keyExtractor={(item) => item.ids.toString()}
            style={styles.secteursList}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  map: {
    flex: 1,
  },
  customMarker: {
    backgroundColor: '#2196F3',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  secteurButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  secteurButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
    flex: 1,
  },
  infoContainer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
  },
  infoText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  secteursList: {
    flex: 1,
  },
  secteurItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  secteurName: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 15,
  },
});

export default MapScreen;
