{"version": 3, "sources": ["TouchableWithoutFeedback.tsx"], "names": ["React", "GenericTouchable", "TouchableWithoutFeedback", "forwardRef", "props", "ref", "defaultProps"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAEA,OAAOC,gBAAP,MAA6B,oBAA7B;AAKA,MAAMC,wBAAwB,gBAAGF,KAAK,CAACG,UAAN,CAG/B,CAACC,KAAD,EAAQC,GAAR,kBAAgB,oBAAC,gBAAD;AAAkB,EAAA,GAAG,EAAEA;AAAvB,GAAgCD,KAAhC,EAHe,CAAjC;AAKAF,wBAAwB,CAACI,YAAzB,GAAwCL,gBAAgB,CAACK,YAAzD;AAEA,eAAeJ,wBAAf", "sourcesContent": ["import * as React from 'react';\nimport { PropsWithChildren } from 'react';\nimport GenericTouchable from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\n\nexport type TouchableWithoutFeedbackProps = GenericTouchableProps;\n\nconst TouchableWithoutFeedback = React.forwardRef<\n  GenericTouchable,\n  PropsWithChildren<TouchableWithoutFeedbackProps>\n>((props, ref) => <GenericTouchable ref={ref} {...props} />);\n\nTouchableWithoutFeedback.defaultProps = GenericTouchable.defaultProps;\n\nexport default TouchableWithoutFeedback;\n"]}