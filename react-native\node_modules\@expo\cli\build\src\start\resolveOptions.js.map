{"version": 3, "sources": ["../../../src/start/resolveOptions.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { canResolveDevClient, hasDirectDevClientDependency } from './detectDevClient';\nimport { Log } from '../log';\nimport { envIsWebcontainer } from '../utils/env';\nimport { AbortCommandError, CommandError } from '../utils/errors';\nimport { resolvePortAsync } from '../utils/port';\n\nexport type Options = {\n  privateKeyPath: string | null;\n  android: boolean;\n  web: boolean;\n  ios: boolean;\n  offline: boolean;\n  clear: boolean;\n  dev: boolean;\n  https: boolean;\n  maxWorkers: number;\n  port: number;\n  /** Should instruct the bundler to create minified bundles. */\n  minify: boolean;\n  devClient: boolean;\n  scheme: string | null;\n  host: 'localhost' | 'lan' | 'tunnel';\n};\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  if (args['--dev-client'] && args['--go']) {\n    throw new CommandError('BAD_ARGS', 'Cannot use both --dev-client and --go together.');\n  }\n  const host = resolveHostType({\n    host: args['--host'],\n    offline: args['--offline'],\n    lan: args['--lan'],\n    localhost: args['--localhost'],\n    tunnel: args['--tunnel'],\n  });\n\n  if (args['--https']) {\n    Log.warn(chalk`{bold --https} option is deprecated in favor of {bold --tunnel}`);\n  }\n\n  // User can force the default target by passing either `--dev-client` or `--go`. They can also\n  // swap between them during development by pressing `s`.\n  const isUserDefinedDevClient =\n    !!args['--dev-client'] || (args['--go'] == null ? false : !args['--go']);\n\n  // If the user didn't specify `--dev-client` or `--go` we check if they have the dev client package\n  // in their package.json.\n  const isAutoDevClient =\n    args['--dev-client'] == null &&\n    args['--go'] == null &&\n    hasDirectDevClientDependency(projectRoot);\n\n  const isDevClient = isAutoDevClient || isUserDefinedDevClient;\n\n  const scheme = await resolveSchemeAsync(projectRoot, {\n    scheme: args['--scheme'],\n    devClient: isDevClient,\n  });\n\n  return {\n    privateKeyPath: args['--private-key-path'] ?? null,\n\n    android: !!args['--android'],\n    web: !!args['--web'],\n    ios: !!args['--ios'],\n    offline: !!args['--offline'],\n\n    clear: !!args['--clear'],\n    dev: !args['--no-dev'],\n    https: !!args['--https'],\n    maxWorkers: args['--max-workers'],\n    port: args['--port'],\n    minify: !!args['--minify'],\n\n    devClient: isDevClient,\n\n    scheme,\n    host,\n  };\n}\n\nexport async function resolveSchemeAsync(\n  projectRoot: string,\n  options: { scheme?: string; devClient?: boolean }\n): Promise<string | null> {\n  if (typeof options.scheme === 'string') {\n    // Use the custom scheme\n    return options.scheme ?? null;\n  }\n\n  if (options.devClient || canResolveDevClient(projectRoot)) {\n    const { getOptionalDevClientSchemeAsync } =\n      require('../utils/scheme') as typeof import('../utils/scheme');\n    // Attempt to find the scheme or warn the user how to setup a custom scheme\n    const resolvedScheme = await getOptionalDevClientSchemeAsync(projectRoot);\n    if (!resolvedScheme.scheme) {\n      if (resolvedScheme.resolution === 'shared') {\n        // This can happen if one of the native projects has no URI schemes defined in it.\n        // Normally, this should never happen.\n        Log.warn(\n          chalk`Could not find a shared URI scheme for the dev client between the local {bold /ios} and {bold /android} directories. App launches (QR code, interstitial, terminal keys) may not work as expected. You can configure a custom scheme using the {bold --scheme} option, or by running {bold npx expo prebuild} to regenerate the native directories with URI schemes.`\n        );\n      } else if (['ios', 'android'].includes(resolvedScheme.resolution)) {\n        Log.warn(\n          chalk`The {bold /${resolvedScheme.resolution}} project does not contain any URI schemes. Expo CLI will not be able to use links to launch the project. You can configure a custom URI scheme using the {bold --scheme} option.`\n        );\n      }\n    }\n    return resolvedScheme.scheme;\n  } else {\n    // Ensure this is reset when users don't use `--scheme`, `--dev-client` and don't have the `expo-dev-client` package installed.\n    return null;\n  }\n}\n\n/** Resolve and assert host type options. */\nexport function resolveHostType(options: {\n  host?: string;\n  offline?: boolean;\n  lan?: boolean;\n  localhost?: boolean;\n  tunnel?: boolean;\n}): 'lan' | 'tunnel' | 'localhost' {\n  if (\n    [options.offline, options.host, options.lan, options.localhost, options.tunnel].filter((i) => i)\n      .length > 1\n  ) {\n    throw new CommandError(\n      'BAD_ARGS',\n      'Specify at most one of: --offline, --host, --tunnel, --lan, --localhost'\n    );\n  }\n\n  if (options.offline) {\n    // Force `lan` in offline mode.\n    return 'lan';\n  } else if (options.host) {\n    assert.match(options.host, /^(lan|tunnel|localhost)$/);\n    return options.host as 'lan' | 'tunnel' | 'localhost';\n  } else if (options.tunnel) {\n    return 'tunnel';\n  } else if (options.lan) {\n    return 'lan';\n  } else if (options.localhost) {\n    return 'localhost';\n  }\n\n  // If no option is provided, and we are running in Stackblitz, enable tunnel by default\n  if (envIsWebcontainer()) {\n    return 'tunnel';\n  }\n\n  return 'lan';\n}\n\n/** Resolve the port options for all supported bundlers. */\nexport async function resolvePortsAsync(\n  projectRoot: string,\n  options: Partial<Pick<Options, 'port' | 'devClient'>>,\n  settings: { webOnly?: boolean }\n) {\n  const multiBundlerSettings: { webpackPort?: number; metroPort?: number } = {};\n\n  if (settings.webOnly) {\n    const webpackPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      // Default web port\n      fallbackPort: 19006,\n    });\n    if (!webpackPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.webpackPort = webpackPort;\n  } else {\n    const fallbackPort = process.env.RCT_METRO_PORT\n      ? parseInt(process.env.RCT_METRO_PORT, 10)\n      : 8081;\n    const metroPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      fallbackPort,\n    });\n    if (!metroPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.metroPort = metroPort;\n  }\n\n  return multiBundlerSettings;\n}\n"], "names": ["resolveHostType", "resolveOptionsAsync", "resolvePortsAsync", "resolveSchemeAsync", "projectRoot", "args", "CommandError", "host", "offline", "lan", "localhost", "tunnel", "Log", "warn", "chalk", "isUserDefinedDevClient", "isAutoDevClient", "hasDirectDevClientDependency", "isDevClient", "scheme", "devClient", "privateKeyPath", "android", "web", "ios", "clear", "dev", "https", "maxWorkers", "port", "minify", "options", "canResolveDevClient", "getOptionalDevClientSchemeAsync", "require", "resolvedScheme", "resolution", "includes", "filter", "i", "length", "assert", "match", "envIsWebcontainer", "settings", "multiBundlerSettings", "webOnly", "webpackPort", "resolvePortAsync", "defaultPort", "fallback<PERSON>ort", "AbortCommandError", "process", "env", "RCT_METRO_PORT", "parseInt", "metroPort"], "mappings": ";;;;;;;;;;;IAuHgBA,eAAe;eAAfA;;IA5FMC,mBAAmB;eAAnBA;;IAoIAC,iBAAiB;eAAjBA;;IA3EAC,kBAAkB;eAAlBA;;;;gEApFH;;;;;;;gEACD;;;;;;iCAEgD;qBAC9C;qBACc;wBACc;sBACf;;;;;;AAoB1B,eAAeF,oBAAoBG,WAAmB,EAAEC,IAAS;IACtE,IAAIA,IAAI,CAAC,eAAe,IAAIA,IAAI,CAAC,OAAO,EAAE;QACxC,MAAM,IAAIC,oBAAY,CAAC,YAAY;IACrC;IACA,MAAMC,OAAOP,gBAAgB;QAC3BO,MAAMF,IAAI,CAAC,SAAS;QACpBG,SAASH,IAAI,CAAC,YAAY;QAC1BI,KAAKJ,IAAI,CAAC,QAAQ;QAClBK,WAAWL,IAAI,CAAC,cAAc;QAC9BM,QAAQN,IAAI,CAAC,WAAW;IAC1B;IAEA,IAAIA,IAAI,CAAC,UAAU,EAAE;QACnBO,QAAG,CAACC,IAAI,CAACC,IAAAA,gBAAK,CAAA,CAAC,+DAA+D,CAAC;IACjF;IAEA,8FAA8F;IAC9F,wDAAwD;IACxD,MAAMC,yBACJ,CAAC,CAACV,IAAI,CAAC,eAAe,IAAKA,CAAAA,IAAI,CAAC,OAAO,IAAI,OAAO,QAAQ,CAACA,IAAI,CAAC,OAAO,AAAD;IAExE,mGAAmG;IACnG,yBAAyB;IACzB,MAAMW,kBACJX,IAAI,CAAC,eAAe,IAAI,QACxBA,IAAI,CAAC,OAAO,IAAI,QAChBY,IAAAA,6CAA4B,EAACb;IAE/B,MAAMc,cAAcF,mBAAmBD;IAEvC,MAAMI,SAAS,MAAMhB,mBAAmBC,aAAa;QACnDe,QAAQd,IAAI,CAAC,WAAW;QACxBe,WAAWF;IACb;IAEA,OAAO;QACLG,gBAAgBhB,IAAI,CAAC,qBAAqB,IAAI;QAE9CiB,SAAS,CAAC,CAACjB,IAAI,CAAC,YAAY;QAC5BkB,KAAK,CAAC,CAAClB,IAAI,CAAC,QAAQ;QACpBmB,KAAK,CAAC,CAACnB,IAAI,CAAC,QAAQ;QACpBG,SAAS,CAAC,CAACH,IAAI,CAAC,YAAY;QAE5BoB,OAAO,CAAC,CAACpB,IAAI,CAAC,UAAU;QACxBqB,KAAK,CAACrB,IAAI,CAAC,WAAW;QACtBsB,OAAO,CAAC,CAACtB,IAAI,CAAC,UAAU;QACxBuB,YAAYvB,IAAI,CAAC,gBAAgB;QACjCwB,MAAMxB,IAAI,CAAC,SAAS;QACpByB,QAAQ,CAAC,CAACzB,IAAI,CAAC,WAAW;QAE1Be,WAAWF;QAEXC;QACAZ;IACF;AACF;AAEO,eAAeJ,mBACpBC,WAAmB,EACnB2B,OAAiD;IAEjD,IAAI,OAAOA,QAAQZ,MAAM,KAAK,UAAU;QACtC,wBAAwB;QACxB,OAAOY,QAAQZ,MAAM,IAAI;IAC3B;IAEA,IAAIY,QAAQX,SAAS,IAAIY,IAAAA,oCAAmB,EAAC5B,cAAc;QACzD,MAAM,EAAE6B,+BAA+B,EAAE,GACvCC,QAAQ;QACV,2EAA2E;QAC3E,MAAMC,iBAAiB,MAAMF,gCAAgC7B;QAC7D,IAAI,CAAC+B,eAAehB,MAAM,EAAE;YAC1B,IAAIgB,eAAeC,UAAU,KAAK,UAAU;gBAC1C,kFAAkF;gBAClF,sCAAsC;gBACtCxB,QAAG,CAACC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,oWAAoW,CAAC;YAE/W,OAAO,IAAI;gBAAC;gBAAO;aAAU,CAACuB,QAAQ,CAACF,eAAeC,UAAU,GAAG;gBACjExB,QAAG,CAACC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,WAAW,EAAEqB,eAAeC,UAAU,CAAC,iLAAiL,CAAC;YAEnO;QACF;QACA,OAAOD,eAAehB,MAAM;IAC9B,OAAO;QACL,+HAA+H;QAC/H,OAAO;IACT;AACF;AAGO,SAASnB,gBAAgB+B,OAM/B;IACC,IACE;QAACA,QAAQvB,OAAO;QAAEuB,QAAQxB,IAAI;QAAEwB,QAAQtB,GAAG;QAAEsB,QAAQrB,SAAS;QAAEqB,QAAQpB,MAAM;KAAC,CAAC2B,MAAM,CAAC,CAACC,IAAMA,GAC3FC,MAAM,GAAG,GACZ;QACA,MAAM,IAAIlC,oBAAY,CACpB,YACA;IAEJ;IAEA,IAAIyB,QAAQvB,OAAO,EAAE;QACnB,+BAA+B;QAC/B,OAAO;IACT,OAAO,IAAIuB,QAAQxB,IAAI,EAAE;QACvBkC,iBAAM,CAACC,KAAK,CAACX,QAAQxB,IAAI,EAAE;QAC3B,OAAOwB,QAAQxB,IAAI;IACrB,OAAO,IAAIwB,QAAQpB,MAAM,EAAE;QACzB,OAAO;IACT,OAAO,IAAIoB,QAAQtB,GAAG,EAAE;QACtB,OAAO;IACT,OAAO,IAAIsB,QAAQrB,SAAS,EAAE;QAC5B,OAAO;IACT;IAEA,uFAAuF;IACvF,IAAIiC,IAAAA,sBAAiB,KAAI;QACvB,OAAO;IACT;IAEA,OAAO;AACT;AAGO,eAAezC,kBACpBE,WAAmB,EACnB2B,OAAqD,EACrDa,QAA+B;IAE/B,MAAMC,uBAAqE,CAAC;IAE5E,IAAID,SAASE,OAAO,EAAE;QACpB,MAAMC,cAAc,MAAMC,IAAAA,sBAAgB,EAAC5C,aAAa;YACtD6C,aAAalB,QAAQF,IAAI;YACzB,mBAAmB;YACnBqB,cAAc;QAChB;QACA,IAAI,CAACH,aAAa;YAChB,MAAM,IAAII,yBAAiB;QAC7B;QACAN,qBAAqBE,WAAW,GAAGA;IACrC,OAAO;QACL,MAAMG,eAAeE,QAAQC,GAAG,CAACC,cAAc,GAC3CC,SAASH,QAAQC,GAAG,CAACC,cAAc,EAAE,MACrC;QACJ,MAAME,YAAY,MAAMR,IAAAA,sBAAgB,EAAC5C,aAAa;YACpD6C,aAAalB,QAAQF,IAAI;YACzBqB;QACF;QACA,IAAI,CAACM,WAAW;YACd,MAAM,IAAIL,yBAAiB;QAC7B;QACAN,qBAAqBW,SAAS,GAAGA;IACnC;IAEA,OAAOX;AACT"}