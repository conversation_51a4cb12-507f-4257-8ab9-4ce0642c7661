{"version": 3, "sources": ["../../../../../src/start/platforms/ios/simctlLogging.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { ChildProcessWithoutNullStreams, spawn } from 'child_process';\nimport { EOL } from 'os';\nimport path from 'path';\nimport wrapAnsi from 'wrap-ansi';\n\nimport { Device, getContainerPathAsync } from './simctl';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nexport type SimControlLog = {\n  /**\n   * 258753568922927108\n   */\n  traceID: number;\n  /**\n   *\n   * \"Connection 1: done\",\n   */\n  eventMessage: string;\n  /**\n   * \"logEvent\" | \"activityCreateEvent\",\n   */\n  eventType: 'logEvent' | 'activityCreateEvent';\n  source: null | {\n    /**\n     * 'RCTDefaultLogFunction_block_invoke' | '__TCC_CRASHING_DUE_TO_PRIVACY_VIOLATION__'\n     */\n    symbol: string;\n    line: number;\n    /**\n     * 'TCC' | 'Security' | 'CFNetwork' | 'libnetwork.dylib' | 'myapp'\n     *\n     * TCC is apple sys, it means \"Transparency, Consent, and Control\"\n     */\n    image: string;\n    /**\n     * 'RCTLog.mm' | ''\n     */\n    file: string;\n  };\n  /**\n   * \"Connection %llu: done\"\n   */\n  formatString: string;\n  /**\n   * 0\n   */\n  activityIdentifier: number;\n  subsystem:\n    | ''\n    | 'com.apple.network'\n    | 'com.facebook.react.log'\n    | 'com.apple.TCC'\n    | 'com.apple.CoreTelephony'\n    | 'com.apple.WebKit'\n    | 'com.apple.runningboard'\n    | string;\n  category: '' | 'access' | 'connection' | 'plugin';\n  /**\n   * \"2021-03-15 15:36:28.004331-0700\"\n   */\n  timestamp: string;\n  /**\n   * 706567072091713\n   */\n  machTimestamp: number;\n  /**\n   * \"Default\"\n   */\n  messageType: 'Default' | 'Error';\n  /**\n   * 15192\n   */\n  processID: number;\n};\n\ntype ProcessResolver =\n  | {\n      pid: string;\n    }\n  | {\n      appId: string;\n    };\n\nexport class SimulatorLogStreamer {\n  private childProcess: ChildProcessWithoutNullStreams | null = null;\n\n  static cache: SimulatorLogStreamer[] = [];\n\n  static getStreamer = (device: Pick<Device, 'udid'>, resolver: ProcessResolver) => {\n    return (\n      SimulatorLogStreamer.cache.find((streamer) => streamer.device.udid === device.udid) ??\n      new SimulatorLogStreamer(device, resolver)\n    );\n  };\n\n  constructor(\n    public device: Pick<Device, 'udid'>,\n    public resolver: ProcessResolver\n  ) {}\n\n  isAttached() {\n    return !!this.childProcess;\n  }\n\n  async resolvePidAsync() {\n    if ('pid' in this.resolver) {\n      return this.resolver.pid;\n    }\n    return getImageNameFromBundleIdentifierAsync(this.device.udid, this.resolver.appId);\n  }\n\n  async attachAsync() {\n    await this.detachAsync();\n\n    const pid = await this.resolvePidAsync();\n\n    if (!pid) {\n      throw new CommandError(`Could not find pid for ${this.device.udid}`);\n    }\n\n    // xcrun simctl spawn booted log stream --process --style json\n    this.childProcess = spawn('xcrun', [\n      'simctl',\n      'spawn',\n      this.device.udid,\n      'log',\n      'stream',\n      '--process',\n      pid,\n      // ndjson provides a better format than json.\n      '--style',\n      'ndjson',\n      // Provide the source so we can filter logs better\n      '--source',\n      // log, activity, trace -- activity was related to layouts, trace didn't work, so that leaves log.\n      // Passing nothing combines all three, but we don't use activity.\n      '--type',\n      'log',\n      // backtrace doesn't seem very useful in basic cases.\n      // TODO: Maybe we can format as a stack trace for native errors.\n      '--no-backtrace',\n    ]);\n\n    this.childProcess.stdout.on('data', (data: Buffer) => {\n      // Sometimes more than one chunk comes at a time, here we split by system newline,\n      // then trim and filter.\n      const strings = data\n        .toString()\n        .split(EOL)\n        .map((value) => value.trim())\n        // This filters out the first log which says something like:\n        // Filtering the log data using \"process BEGINSWITH[cd] \"my-app\" AND type == 1024\"\n        .filter((value) => value.startsWith('{'));\n\n      strings.forEach((str) => {\n        const simLog = parseMessageJson(str);\n        if (!simLog) {\n          return;\n        }\n        onMessage(simLog);\n      });\n    });\n\n    this.childProcess.on('error', ({ message }) => {\n      Log.debug('[simctl error]:', message);\n    });\n\n    this.off = installExitHooks(() => {\n      this.detachAsync.bind(this);\n    });\n  }\n\n  private off: (() => void) | null = null;\n\n  detachAsync() {\n    this.off?.();\n    this.off = null;\n    if (this.childProcess) {\n      return new Promise<void>((resolve) => {\n        this.childProcess?.on('close', resolve);\n        this.childProcess?.kill();\n        this.childProcess = null;\n      });\n    }\n    return Promise.resolve();\n  }\n}\n\nfunction parseMessageJson(data: string) {\n  const stringData = data.toString();\n  try {\n    return JSON.parse(stringData) as SimControlLog;\n  } catch {\n    Log.debug('Failed to parse simctl JSON message:\\n' + stringData);\n  }\n  return null;\n}\n\n// There are a lot of networking logs in RN that aren't relevant to the user.\nfunction isNetworkLog(simLog: SimControlLog): boolean {\n  return (\n    simLog.subsystem === 'com.apple.network' ||\n    simLog.category === 'connection' ||\n    simLog.source?.image === 'CFNetwork'\n  );\n}\n\nfunction isReactLog(simLog: SimControlLog): boolean {\n  return simLog.subsystem === 'com.facebook.react.log' && simLog.source?.file === 'RCTLog.mm';\n}\n\n// It's not clear what these are but they aren't very useful.\n// (The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated)\n// We can add them later if need.\nfunction isCoreTelephonyLog(simLog: SimControlLog): boolean {\n  // [CoreTelephony] Updating selectors failed with: Error Domain=NSCocoaErrorDomain Code=4099\n  // \"The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated.\" UserInfo={NSDebugDescription=The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated.}\n  return simLog.subsystem === 'com.apple.CoreTelephony';\n}\n\n// https://stackoverflow.com/a/65313219/4047926\nfunction isWebKitLog(simLog: SimControlLog): boolean {\n  // [WebKit] 0x1143ca500 - ProcessAssertion: Failed to acquire RBS Background assertion 'WebProcess Background Assertion' for process with PID 27084, error: Error Domain=RBSAssertionErrorDomain Code=3 \"Target is not running or required target\n  // entitlement is missing\" UserInfo={RBSAssertionAttribute=<RBSDomainAttribute| domain:\"com.apple.webkit\" name:\"Background\" sourceEnvironment:\"(null)\">, NSLocalizedFailureReason=Target is not running or required target entitlement is missing}\n  return simLog.subsystem === 'com.apple.WebKit';\n}\n\n// Similar to WebKit logs\nfunction isRunningBoardServicesLog(simLog: SimControlLog): boolean {\n  // [RunningBoardServices] Error acquiring assertion: <Error Domain=RBSAssertionErrorDomain Code=3 \"Target is not running or required target entitlement is missing\" UserInfo={RBSAssertionAttribute=<RBSDomainAttribute| domain:\"com.apple.webkit\"\n  // name:\"Background\" sourceEnvironment:\"(null)\">, NSLocalizedFailureReason=Target is not running or required target entitlement is missing}>\n  return simLog.subsystem === 'com.apple.runningboard';\n}\n\nfunction formatMessage(simLog: SimControlLog): string {\n  // TODO: Maybe change \"TCC\" to \"Consent\" or \"System\".\n  const category = chalk.gray(`[${simLog.source?.image ?? simLog.subsystem}]`);\n  const message = simLog.eventMessage;\n  return wrapAnsi(category + ' ' + message, process.stdout.columns || 80);\n}\n\nexport function onMessage(simLog: SimControlLog) {\n  let hasLogged = false;\n\n  if (simLog.messageType === 'Error') {\n    if (\n      // Hide all networking errors which are mostly useless.\n      !isNetworkLog(simLog) &&\n      // Showing React errors will result in duplicate messages.\n      !isReactLog(simLog) &&\n      !isCoreTelephonyLog(simLog) &&\n      !isWebKitLog(simLog) &&\n      !isRunningBoardServicesLog(simLog)\n    ) {\n      hasLogged = true;\n      // Sim: This app has crashed because it attempted to access privacy-sensitive data without a usage description.  The app's Info.plist must contain an NSCameraUsageDescription key with a string value explaining to the user how the app uses this data.\n      Log.error(formatMessage(simLog));\n    }\n  } else if (simLog.eventMessage) {\n    // If the source has a file (i.e. not a system log).\n    if (\n      simLog.source?.file ||\n      simLog.eventMessage.includes('Terminating app due to uncaught exception')\n    ) {\n      hasLogged = true;\n      Log.log(formatMessage(simLog));\n    }\n  }\n\n  if (!hasLogged) {\n    Log.debug(formatMessage(simLog));\n  } else {\n    // console.log('DATA:', JSON.stringify(simLog));\n  }\n}\n\n/**\n *\n * @param udid\n * @param bundleIdentifier\n * @returns Image name like `Exponent` and `null` when the app is not installed on the provided simulator.\n */\nasync function getImageNameFromBundleIdentifierAsync(\n  udid: string,\n  bundleIdentifier: string\n): Promise<string | null> {\n  const containerPath = await getContainerPathAsync({ udid }, { appId: bundleIdentifier });\n\n  if (containerPath) {\n    return getImageNameFromContainerPath(containerPath);\n  }\n  return null;\n}\n\nfunction getImageNameFromContainerPath(binaryPath: string): string {\n  return path.basename(binaryPath).split('.')[0];\n}\n"], "names": ["SimulatorLogStreamer", "onMessage", "cache", "getStreamer", "device", "resolver", "find", "streamer", "udid", "constructor", "childProcess", "off", "isAttached", "resolvePidAsync", "pid", "getImageNameFromBundleIdentifierAsync", "appId", "attachAsync", "detachAsync", "CommandError", "spawn", "stdout", "on", "data", "strings", "toString", "split", "EOL", "map", "value", "trim", "filter", "startsWith", "for<PERSON>ach", "str", "simLog", "parseMessageJson", "message", "Log", "debug", "installExitHooks", "bind", "Promise", "resolve", "kill", "stringData", "JSON", "parse", "isNetworkLog", "subsystem", "category", "source", "image", "isReactLog", "file", "isCoreTelephonyLog", "isWebKitLog", "isRunningBoardServicesLog", "formatMessage", "chalk", "gray", "eventMessage", "wrapAnsi", "process", "columns", "<PERSON><PERSON><PERSON>", "messageType", "error", "includes", "log", "bundleIdentifier", "containerPath", "getContainerPathAsync", "getImageNameFromContainerPath", "binaryPath", "path", "basename"], "mappings": ";;;;;;;;;;;IAsFaA,oBAAoB;eAApBA;;IA8JGC,SAAS;eAATA;;;;gEApPE;;;;;;;yBACoC;;;;;;;yBAClC;;;;;;;gEACH;;;;;;;gEACI;;;;;;wBAEyB;6DACzB;wBACQ;sBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6E1B,MAAMD;qBAGJE,QAAgC,EAAE;sBAElCC,cAAc,CAACC,QAA8BC;QAClD,OACEL,qBAAqBE,KAAK,CAACI,IAAI,CAAC,CAACC,WAAaA,SAASH,MAAM,CAACI,IAAI,KAAKJ,OAAOI,IAAI,KAClF,IAAIR,qBAAqBI,QAAQC;IAErC;IAEAI,YACE,AAAOL,MAA4B,EACnC,AAAOC,QAAyB,CAChC;aAFOD,SAAAA;aACAC,WAAAA;aAbDK,eAAsD;aAwFtDC,MAA2B;IA1EhC;IAEHC,aAAa;QACX,OAAO,CAAC,CAAC,IAAI,CAACF,YAAY;IAC5B;IAEA,MAAMG,kBAAkB;QACtB,IAAI,SAAS,IAAI,CAACR,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAACA,QAAQ,CAACS,GAAG;QAC1B;QACA,OAAOC,sCAAsC,IAAI,CAACX,MAAM,CAACI,IAAI,EAAE,IAAI,CAACH,QAAQ,CAACW,KAAK;IACpF;IAEA,MAAMC,cAAc;QAClB,MAAM,IAAI,CAACC,WAAW;QAEtB,MAAMJ,MAAM,MAAM,IAAI,CAACD,eAAe;QAEtC,IAAI,CAACC,KAAK;YACR,MAAM,IAAIK,oBAAY,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACf,MAAM,CAACI,IAAI,EAAE;QACrE;QAEA,8DAA8D;QAC9D,IAAI,CAACE,YAAY,GAAGU,IAAAA,sBAAK,EAAC,SAAS;YACjC;YACA;YACA,IAAI,CAAChB,MAAM,CAACI,IAAI;YAChB;YACA;YACA;YACAM;YACA,6CAA6C;YAC7C;YACA;YACA,kDAAkD;YAClD;YACA,kGAAkG;YAClG,iEAAiE;YACjE;YACA;YACA,qDAAqD;YACrD,gEAAgE;YAChE;SACD;QAED,IAAI,CAACJ,YAAY,CAACW,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC;YACnC,kFAAkF;YAClF,wBAAwB;YACxB,MAAMC,UAAUD,KACbE,QAAQ,GACRC,KAAK,CAACC,SAAG,EACTC,GAAG,CAAC,CAACC,QAAUA,MAAMC,IAAI,GAC1B,4DAA4D;YAC5D,kFAAkF;aACjFC,MAAM,CAAC,CAACF,QAAUA,MAAMG,UAAU,CAAC;YAEtCR,QAAQS,OAAO,CAAC,CAACC;gBACf,MAAMC,SAASC,iBAAiBF;gBAChC,IAAI,CAACC,QAAQ;oBACX;gBACF;gBACAlC,UAAUkC;YACZ;QACF;QAEA,IAAI,CAACzB,YAAY,CAACY,EAAE,CAAC,SAAS,CAAC,EAAEe,OAAO,EAAE;YACxCC,KAAIC,KAAK,CAAC,mBAAmBF;QAC/B;QAEA,IAAI,CAAC1B,GAAG,GAAG6B,IAAAA,sBAAgB,EAAC;YAC1B,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAAC,IAAI;QAC5B;IACF;IAIAvB,cAAc;QACZ,IAAI,CAACP,GAAG,oBAAR,IAAI,CAACA,GAAG,MAAR,IAAI;QACJ,IAAI,CAACA,GAAG,GAAG;QACX,IAAI,IAAI,CAACD,YAAY,EAAE;YACrB,OAAO,IAAIgC,QAAc,CAACC;oBACxB,oBACA;iBADA,qBAAA,IAAI,CAACjC,YAAY,qBAAjB,mBAAmBY,EAAE,CAAC,SAASqB;iBAC/B,sBAAA,IAAI,CAACjC,YAAY,qBAAjB,oBAAmBkC,IAAI;gBACvB,IAAI,CAAClC,YAAY,GAAG;YACtB;QACF;QACA,OAAOgC,QAAQC,OAAO;IACxB;AACF;AAEA,SAASP,iBAAiBb,IAAY;IACpC,MAAMsB,aAAatB,KAAKE,QAAQ;IAChC,IAAI;QACF,OAAOqB,KAAKC,KAAK,CAACF;IACpB,EAAE,OAAM;QACNP,KAAIC,KAAK,CAAC,2CAA2CM;IACvD;IACA,OAAO;AACT;AAEA,6EAA6E;AAC7E,SAASG,aAAab,MAAqB;QAIvCA;IAHF,OACEA,OAAOc,SAAS,KAAK,uBACrBd,OAAOe,QAAQ,KAAK,gBACpBf,EAAAA,iBAAAA,OAAOgB,MAAM,qBAAbhB,eAAeiB,KAAK,MAAK;AAE7B;AAEA,SAASC,WAAWlB,MAAqB;QACiBA;IAAxD,OAAOA,OAAOc,SAAS,KAAK,4BAA4Bd,EAAAA,iBAAAA,OAAOgB,MAAM,qBAAbhB,eAAemB,IAAI,MAAK;AAClF;AAEA,6DAA6D;AAC7D,oGAAoG;AACpG,iCAAiC;AACjC,SAASC,mBAAmBpB,MAAqB;IAC/C,4FAA4F;IAC5F,oOAAoO;IACpO,OAAOA,OAAOc,SAAS,KAAK;AAC9B;AAEA,+CAA+C;AAC/C,SAASO,YAAYrB,MAAqB;IACxC,iPAAiP;IACjP,kPAAkP;IAClP,OAAOA,OAAOc,SAAS,KAAK;AAC9B;AAEA,yBAAyB;AACzB,SAASQ,0BAA0BtB,MAAqB;IACtD,kPAAkP;IAClP,4IAA4I;IAC5I,OAAOA,OAAOc,SAAS,KAAK;AAC9B;AAEA,SAASS,cAAcvB,MAAqB;QAEVA;IADhC,qDAAqD;IACrD,MAAMe,WAAWS,gBAAK,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzB,EAAAA,iBAAAA,OAAOgB,MAAM,qBAAbhB,eAAeiB,KAAK,KAAIjB,OAAOc,SAAS,CAAC,CAAC,CAAC;IAC3E,MAAMZ,UAAUF,OAAO0B,YAAY;IACnC,OAAOC,IAAAA,mBAAQ,EAACZ,WAAW,MAAMb,SAAS0B,QAAQ1C,MAAM,CAAC2C,OAAO,IAAI;AACtE;AAEO,SAAS/D,UAAUkC,MAAqB;IAC7C,IAAI8B,YAAY;IAEhB,IAAI9B,OAAO+B,WAAW,KAAK,SAAS;QAClC,IACE,uDAAuD;QACvD,CAAClB,aAAab,WACd,0DAA0D;QAC1D,CAACkB,WAAWlB,WACZ,CAACoB,mBAAmBpB,WACpB,CAACqB,YAAYrB,WACb,CAACsB,0BAA0BtB,SAC3B;YACA8B,YAAY;YACZ,yPAAyP;YACzP3B,KAAI6B,KAAK,CAACT,cAAcvB;QAC1B;IACF,OAAO,IAAIA,OAAO0B,YAAY,EAAE;YAG5B1B;QAFF,oDAAoD;QACpD,IACEA,EAAAA,iBAAAA,OAAOgB,MAAM,qBAAbhB,eAAemB,IAAI,KACnBnB,OAAO0B,YAAY,CAACO,QAAQ,CAAC,8CAC7B;YACAH,YAAY;YACZ3B,KAAI+B,GAAG,CAACX,cAAcvB;QACxB;IACF;IAEA,IAAI,CAAC8B,WAAW;QACd3B,KAAIC,KAAK,CAACmB,cAAcvB;IAC1B,OAAO;IACL,gDAAgD;IAClD;AACF;AAEA;;;;;CAKC,GACD,eAAepB,sCACbP,IAAY,EACZ8D,gBAAwB;IAExB,MAAMC,gBAAgB,MAAMC,IAAAA,6BAAqB,EAAC;QAAEhE;IAAK,GAAG;QAAEQ,OAAOsD;IAAiB;IAEtF,IAAIC,eAAe;QACjB,OAAOE,8BAA8BF;IACvC;IACA,OAAO;AACT;AAEA,SAASE,8BAA8BC,UAAkB;IACvD,OAAOC,eAAI,CAACC,QAAQ,CAACF,YAAYhD,KAAK,CAAC,IAAI,CAAC,EAAE;AAChD"}