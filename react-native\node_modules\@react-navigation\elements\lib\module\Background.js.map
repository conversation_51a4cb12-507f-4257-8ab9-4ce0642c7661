{"version": 3, "names": ["useTheme", "React", "View", "Background", "style", "rest", "colors", "flex", "backgroundColor", "background"], "sourceRoot": "../../src", "sources": ["Background.tsx"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAmB,cAAc;AAM9C,eAAe,SAASC,UAAU,OAA4B;EAAA,IAA3B;IAAEC,KAAK;IAAE,GAAGC;EAAY,CAAC;EAC1D,MAAM;IAAEC;EAAO,CAAC,GAAGN,QAAQ,EAAE;EAE7B,oBACE,oBAAC,IAAI,eACCK,IAAI;IACR,KAAK,EAAE,CAAC;MAAEE,IAAI,EAAE,CAAC;MAAEC,eAAe,EAAEF,MAAM,CAACG;IAAW,CAAC,EAAEL,KAAK;EAAE,GAChE;AAEN"}