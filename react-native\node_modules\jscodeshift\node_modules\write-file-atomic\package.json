{"name": "write-file-atomic", "version": "2.4.3", "description": "Write files in an atomic fashion w/configurable ownership", "main": "index.js", "scripts": {"test": "standard && tap --100 test/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "**************:iarna/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": "<PERSON> <<EMAIL>> (http://re-becca.org)", "license": "ISC", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "homepage": "https://github.com/iarna/write-file-atomic", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"mkdirp": "^0.5.1", "require-inject": "^1.4.0", "rimraf": "^2.5.4", "standard": "^12.0.1", "tap": "^12.1.3"}, "files": ["index.js"]}