{"version": 3, "names": ["createMemoryHistory", "index", "items", "pending", "interrupt", "for<PERSON>ach", "it", "cb", "history", "id", "window", "state", "findIndex", "item", "get", "backIndex", "path", "i", "push", "nanoid", "slice", "length", "pushState", "replace", "pathWithHash", "location", "hash", "replaceState", "go", "n", "nextIndex", "lastItemIndex", "Promise", "resolve", "reject", "done", "interrupted", "clearTimeout", "timer", "Error", "title", "document", "ref", "setTimeout", "splice", "onPopState", "currentIndex", "Math", "max", "last", "pop", "removeEventListener", "addEventListener", "listen", "listener"], "sourceRoot": "../../src", "sources": ["createMemoryHistory.tsx"], "mappings": ";;;;;;AACA;AAWe,SAASA,mBAAmB,GAAG;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAsB,GAAG,EAAE;;EAE/B;EACA;EACA,MAAMC,OAAgE,GAAG,EAAE;EAE3E,MAAMC,SAAS,GAAG,MAAM;IACtB;IACA;IACA;IACAD,OAAO,CAACE,OAAO,CAAEC,EAAE,IAAK;MACtB,MAAMC,EAAE,GAAGD,EAAE,CAACC,EAAE;MAChBD,EAAE,CAACC,EAAE,GAAG,MAAMA,EAAE,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,OAAO,GAAG;IACd,IAAIP,KAAK,GAAW;MAAA;MAClB;MACA;MACA,MAAMQ,EAAE,4BAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,0DAApB,sBAAsBF,EAAE;MAEnC,IAAIA,EAAE,EAAE;QACN,MAAMR,KAAK,GAAGC,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC;QAEvD,OAAOR,KAAK,GAAG,CAAC,CAAC,GAAGA,KAAK,GAAG,CAAC;MAC/B;MAEA,OAAO,CAAC;IACV,CAAC;IAEDa,GAAG,CAACb,KAAa,EAAE;MACjB,OAAOC,KAAK,CAACD,KAAK,CAAC;IACrB,CAAC;IAEDc,SAAS,OAA6B;MAAA,IAA5B;QAAEC;MAAuB,CAAC;MAClC;MACA,KAAK,IAAIC,CAAC,GAAGhB,KAAK,GAAG,CAAC,EAAEgB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnC,MAAMJ,IAAI,GAAGX,KAAK,CAACe,CAAC,CAAC;QAErB,IAAIJ,IAAI,CAACG,IAAI,KAAKA,IAAI,EAAE;UACtB,OAAOC,CAAC;QACV;MACF;MAEA,OAAO,CAAC,CAAC;IACX,CAAC;IAEDC,IAAI,QAA4D;MAAA,IAA3D;QAAEF,IAAI;QAAEL;MAAgD,CAAC;MAC5DP,SAAS,EAAE;MAEX,MAAMK,EAAE,GAAG,IAAAU,iBAAM,GAAE;;MAEnB;MACA;MACAjB,KAAK,GAAGA,KAAK,CAACkB,KAAK,CAAC,CAAC,EAAEnB,KAAK,GAAG,CAAC,CAAC;MAEjCC,KAAK,CAACgB,IAAI,CAAC;QAAEF,IAAI;QAAEL,KAAK;QAAEF;MAAG,CAAC,CAAC;MAC/BR,KAAK,GAAGC,KAAK,CAACmB,MAAM,GAAG,CAAC;;MAExB;MACA;MACA;MACA;MACAX,MAAM,CAACF,OAAO,CAACc,SAAS,CAAC;QAAEb;MAAG,CAAC,EAAE,EAAE,EAAEO,IAAI,CAAC;IAC5C,CAAC;IAEDO,OAAO,QAA4D;MAAA;MAAA,IAA3D;QAAEP,IAAI;QAAEL;MAAgD,CAAC;MAC/DP,SAAS,EAAE;MAEX,MAAMK,EAAE,GAAG,2BAAAC,MAAM,CAACF,OAAO,CAACG,KAAK,2DAApB,uBAAsBF,EAAE,KAAI,IAAAU,iBAAM,GAAE;;MAE/C;MACA;MACA,IAAIK,YAAY,GAAGR,IAAI;MAEvB,IAAI,CAACd,KAAK,CAACmB,MAAM,IAAInB,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC,GAAG,CAAC,EAAE;QAClE;QACA;QACA;QACA;QACA;QACA;QACAe,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC3CxB,KAAK,GAAG,CAAC;UAAEc,IAAI,EAAEQ,YAAY;UAAEb,KAAK;UAAEF;QAAG,CAAC,CAAC;QAC3CR,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACL,IAAIC,KAAK,CAACD,KAAK,CAAC,CAACe,IAAI,KAAKA,IAAI,EAAE;UAC9BQ,YAAY,GAAGA,YAAY,GAAGC,QAAQ,CAACC,IAAI;QAC7C;QACAxB,KAAK,CAACD,KAAK,CAAC,GAAG;UAAEe,IAAI;UAAEL,KAAK;UAAEF;QAAG,CAAC;MACpC;MAEAC,MAAM,CAACF,OAAO,CAACmB,YAAY,CAAC;QAAElB;MAAG,CAAC,EAAE,EAAE,EAAEe,YAAY,CAAC;IACvD,CAAC;IAED;IACA;IACA;IACA;IACA;IACAI,EAAE,CAACC,CAAS,EAAE;MACZzB,SAAS,EAAE;;MAEX;MACA;MACA,MAAM0B,SAAS,GAAG7B,KAAK,GAAG4B,CAAC;MAC3B,MAAME,aAAa,GAAG7B,KAAK,CAACmB,MAAM,GAAG,CAAC;MACtC,IAAIQ,CAAC,GAAG,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,EAAE;QAC9B;QACAD,CAAC,GAAG,CAAC5B,KAAK;QACVA,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAI4B,CAAC,GAAG,CAAC,IAAIC,SAAS,GAAGC,aAAa,EAAE;QAC7C;QACAF,CAAC,GAAGE,aAAa,GAAG9B,KAAK;QACzBA,KAAK,GAAG8B,aAAa;MACvB,CAAC,MAAM;QACL9B,KAAK,GAAG6B,SAAS;MACnB;MAEA,IAAID,CAAC,KAAK,CAAC,EAAE;QACX;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAIG,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;QAC5C,MAAMC,IAAI,GAAIC,WAAqB,IAAK;UACtCC,YAAY,CAACC,KAAK,CAAC;UAEnB,IAAIF,WAAW,EAAE;YACfF,MAAM,CAAC,IAAIK,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC3D;UACF;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM;YAAEC;UAAM,CAAC,GAAG9B,MAAM,CAAC+B,QAAQ;UAEjC/B,MAAM,CAAC+B,QAAQ,CAACD,KAAK,GAAG,EAAE;UAC1B9B,MAAM,CAAC+B,QAAQ,CAACD,KAAK,GAAGA,KAAK;UAE7BP,OAAO,EAAE;QACX,CAAC;QAED9B,OAAO,CAACe,IAAI,CAAC;UAAEwB,GAAG,EAAEP,IAAI;UAAE5B,EAAE,EAAE4B;QAAK,CAAC,CAAC;;QAErC;QACA;QACA;QACA;QACA;QACA,MAAMG,KAAK,GAAGK,UAAU,CAAC,MAAM;UAC7B,MAAM1C,KAAK,GAAGE,OAAO,CAACS,SAAS,CAAEN,EAAE,IAAKA,EAAE,CAACoC,GAAG,KAAKP,IAAI,CAAC;UAExD,IAAIlC,KAAK,GAAG,CAAC,CAAC,EAAE;YACdE,OAAO,CAACF,KAAK,CAAC,CAACM,EAAE,EAAE;YACnBJ,OAAO,CAACyC,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,MAAM4C,UAAU,GAAG,MAAM;UAAA;UACvB,MAAMpC,EAAE,6BAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,2DAApB,uBAAsBF,EAAE;UACnC,MAAMqC,YAAY,GAAG5C,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC;;UAE9D;UACA;UACAR,KAAK,GAAG8C,IAAI,CAACC,GAAG,CAACF,YAAY,EAAE,CAAC,CAAC;UAEjC,MAAMG,IAAI,GAAG9C,OAAO,CAAC+C,GAAG,EAAE;UAE1BxC,MAAM,CAACyC,mBAAmB,CAAC,UAAU,EAAEN,UAAU,CAAC;UAClDI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE1C,EAAE,EAAE;QACZ,CAAC;QAEDG,MAAM,CAAC0C,gBAAgB,CAAC,UAAU,EAAEP,UAAU,CAAC;QAC/CnC,MAAM,CAACF,OAAO,CAACoB,EAAE,CAACC,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACA;IACA;IACAwB,MAAM,CAACC,QAAoB,EAAE;MAC3B,MAAMT,UAAU,GAAG,MAAM;QACvB,IAAI1C,OAAO,CAACkB,MAAM,EAAE;UAClB;UACA;QACF;QAEAiC,QAAQ,EAAE;MACZ,CAAC;MAED5C,MAAM,CAAC0C,gBAAgB,CAAC,UAAU,EAAEP,UAAU,CAAC;MAE/C,OAAO,MAAMnC,MAAM,CAACyC,mBAAmB,CAAC,UAAU,EAAEN,UAAU,CAAC;IACjE;EACF,CAAC;EAED,OAAOrC,OAAO;AAChB"}