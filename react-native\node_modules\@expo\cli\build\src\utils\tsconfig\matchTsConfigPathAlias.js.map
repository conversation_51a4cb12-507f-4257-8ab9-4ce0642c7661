{"version": 3, "sources": ["../../../../src/utils/tsconfig/matchTsConfigPathAlias.ts"], "sourcesContent": ["// From TypeScript: https://github.com/microsoft/TypeScript/blob/5b1897969769449217237aecbe364f823096c63e/src/compiler/core.ts\n// License: https://github.com/microsoft/TypeScript/blob/214df64/LICENSE.txt\n\nexport interface Pattern {\n  prefix: string;\n  suffix: string;\n}\n\nconst asterisk = 0x2a;\n\nfunction hasZeroOrOneAsteriskCharacter(str: string): boolean {\n  let seenAsterisk = false;\n  for (let i = 0; i < str.length; i++) {\n    if (str.charCodeAt(i) === asterisk) {\n      if (!seenAsterisk) {\n        seenAsterisk = true;\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nfunction tryParsePattern(pattern: string): Pattern | undefined {\n  // This should be verified outside of here and a proper error thrown.\n  const indexOfStar = pattern.indexOf('*');\n  return indexOfStar === -1\n    ? undefined\n    : {\n        prefix: pattern.slice(0, indexOfStar),\n        suffix: pattern.slice(indexOfStar + 1),\n      };\n}\n\nfunction isPatternMatch({ prefix, suffix }: Pattern, candidate: string) {\n  return (\n    candidate.length >= prefix.length + suffix.length &&\n    candidate.startsWith(prefix) &&\n    candidate.endsWith(suffix)\n  );\n}\n\n/**\n * Return the object corresponding to the best pattern to match `candidate`.\n *\n * @internal\n */\nfunction findBestPatternMatch<T>(\n  values: readonly T[],\n  getPattern: (value: T) => Pattern,\n  candidate: string\n): T | undefined {\n  let matchedValue: T | undefined;\n  // use length of prefix as betterness criteria\n  let longestMatchPrefixLength = -1;\n\n  for (const v of values) {\n    const pattern = getPattern(v);\n    if (isPatternMatch(pattern, candidate) && pattern.prefix.length > longestMatchPrefixLength) {\n      longestMatchPrefixLength = pattern.prefix.length;\n      matchedValue = v;\n    }\n  }\n\n  return matchedValue;\n}\n\n/**\n * patternStrings contains both pattern strings (containing \"*\") and regular strings.\n * Return an exact match if possible, or a pattern match, or undefined.\n * (These are verified by verifyCompilerOptions to have 0 or 1 \"*\" characters.)\n */\nfunction matchPatternOrExact(\n  patternStrings: readonly string[],\n  candidate: string\n): string | Pattern | undefined {\n  const patterns: Pattern[] = [];\n  for (const patternString of patternStrings) {\n    if (!hasZeroOrOneAsteriskCharacter(patternString)) continue;\n    const pattern = tryParsePattern(patternString);\n    if (pattern) {\n      patterns.push(pattern);\n    } else if (patternString === candidate) {\n      // pattern was matched as is - no need to search further\n      return patternString;\n    }\n  }\n\n  return findBestPatternMatch(patterns, (_) => _, candidate);\n}\n\n/**\n * Given that candidate matches pattern, returns the text matching the '*'.\n * E.g.: matchedText(tryParsePattern(\"foo*baz\"), \"foobarbaz\") === \"bar\"\n */\nfunction matchedText(pattern: Pattern, candidate: string): string {\n  return candidate.substring(pattern.prefix.length, candidate.length - pattern.suffix.length);\n}\n\nfunction getStar(matchedPattern: string | Pattern, moduleName: string) {\n  return typeof matchedPattern === 'string' ? undefined : matchedText(matchedPattern, moduleName);\n}\n\nexport function matchTsConfigPathAlias(pathsKeys: string[], moduleName: string) {\n  // If the module name does not match any of the patterns in `paths` we hand off resolving to webpack\n  const matchedPattern = matchPatternOrExact(pathsKeys, moduleName);\n  if (!matchedPattern) {\n    return null;\n  }\n\n  return {\n    star: getStar(matchedPattern, moduleName),\n    text:\n      typeof matchedPattern === 'string'\n        ? matchedPattern\n        : `${matchedPattern.prefix}*${matchedPattern.suffix}`,\n  };\n}\n"], "names": ["matchTsConfigPathAlias", "asterisk", "hasZeroOrOneAsteriskCharacter", "str", "seenAsterisk", "i", "length", "charCodeAt", "tryParsePattern", "pattern", "indexOfStar", "indexOf", "undefined", "prefix", "slice", "suffix", "isPatternMatch", "candidate", "startsWith", "endsWith", "findBestPatternMatch", "values", "getPattern", "matchedValue", "longestMatchPrefixLength", "v", "matchPatternOrExact", "patternStrings", "patterns", "patternString", "push", "_", "matchedText", "substring", "getStar", "matchedPattern", "moduleName", "pathsKeys", "star", "text"], "mappings": "AAAA,8HAA8H;AAC9H,4EAA4E;;;;;+BAuG5DA;;;eAAAA;;;AAhGhB,MAAMC,WAAW;AAEjB,SAASC,8BAA8BC,GAAW;IAChD,IAAIC,eAAe;IACnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,IAAIF,IAAII,UAAU,CAACF,OAAOJ,UAAU;YAClC,IAAI,CAACG,cAAc;gBACjBA,eAAe;YACjB,OAAO;gBACL,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEA,SAASI,gBAAgBC,OAAe;IACtC,qEAAqE;IACrE,MAAMC,cAAcD,QAAQE,OAAO,CAAC;IACpC,OAAOD,gBAAgB,CAAC,IACpBE,YACA;QACEC,QAAQJ,QAAQK,KAAK,CAAC,GAAGJ;QACzBK,QAAQN,QAAQK,KAAK,CAACJ,cAAc;IACtC;AACN;AAEA,SAASM,eAAe,EAAEH,MAAM,EAAEE,MAAM,EAAW,EAAEE,SAAiB;IACpE,OACEA,UAAUX,MAAM,IAAIO,OAAOP,MAAM,GAAGS,OAAOT,MAAM,IACjDW,UAAUC,UAAU,CAACL,WACrBI,UAAUE,QAAQ,CAACJ;AAEvB;AAEA;;;;CAIC,GACD,SAASK,qBACPC,MAAoB,EACpBC,UAAiC,EACjCL,SAAiB;IAEjB,IAAIM;IACJ,8CAA8C;IAC9C,IAAIC,2BAA2B,CAAC;IAEhC,KAAK,MAAMC,KAAKJ,OAAQ;QACtB,MAAMZ,UAAUa,WAAWG;QAC3B,IAAIT,eAAeP,SAASQ,cAAcR,QAAQI,MAAM,CAACP,MAAM,GAAGkB,0BAA0B;YAC1FA,2BAA2Bf,QAAQI,MAAM,CAACP,MAAM;YAChDiB,eAAeE;QACjB;IACF;IAEA,OAAOF;AACT;AAEA;;;;CAIC,GACD,SAASG,oBACPC,cAAiC,EACjCV,SAAiB;IAEjB,MAAMW,WAAsB,EAAE;IAC9B,KAAK,MAAMC,iBAAiBF,eAAgB;QAC1C,IAAI,CAACzB,8BAA8B2B,gBAAgB;QACnD,MAAMpB,UAAUD,gBAAgBqB;QAChC,IAAIpB,SAAS;YACXmB,SAASE,IAAI,CAACrB;QAChB,OAAO,IAAIoB,kBAAkBZ,WAAW;YACtC,wDAAwD;YACxD,OAAOY;QACT;IACF;IAEA,OAAOT,qBAAqBQ,UAAU,CAACG,IAAMA,GAAGd;AAClD;AAEA;;;CAGC,GACD,SAASe,YAAYvB,OAAgB,EAAEQ,SAAiB;IACtD,OAAOA,UAAUgB,SAAS,CAACxB,QAAQI,MAAM,CAACP,MAAM,EAAEW,UAAUX,MAAM,GAAGG,QAAQM,MAAM,CAACT,MAAM;AAC5F;AAEA,SAAS4B,QAAQC,cAAgC,EAAEC,UAAkB;IACnE,OAAO,OAAOD,mBAAmB,WAAWvB,YAAYoB,YAAYG,gBAAgBC;AACtF;AAEO,SAASpC,uBAAuBqC,SAAmB,EAAED,UAAkB;IAC5E,oGAAoG;IACpG,MAAMD,iBAAiBT,oBAAoBW,WAAWD;IACtD,IAAI,CAACD,gBAAgB;QACnB,OAAO;IACT;IAEA,OAAO;QACLG,MAAMJ,QAAQC,gBAAgBC;QAC9BG,MACE,OAAOJ,mBAAmB,WACtBA,iBACA,GAAGA,eAAetB,MAAM,CAAC,CAAC,EAAEsB,eAAepB,MAAM,EAAE;IAC3D;AACF"}