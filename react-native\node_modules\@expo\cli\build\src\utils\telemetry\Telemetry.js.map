{"version": 3, "sources": ["../../../../src/utils/telemetry/Telemetry.ts"], "sourcesContent": ["import crypto from 'node:crypto';\n\nimport { FetchClient } from './clients/FetchClient';\nimport { FetchDetachedClient } from './clients/FetchDetachedClient';\nimport { TelemetryClient, TelemetryClientStrategy, TelemetryRecord } from './types';\nimport { createContext } from './utils/context';\nimport { getAnonymousId } from '../../api/user/UserSettings';\nimport { env } from '../env';\n\nconst debug = require('debug')('expo:telemetry') as typeof console.log;\n\ntype TelemetryOptions = {\n  /** A locally generated ID, untracable to an actual user */\n  anonymousId?: string;\n  /** A locally generated ID, per CLI invocation */\n  sessionId?: string;\n  /** The authenticated user ID, this is used to generate an untracable hash */\n  userId?: string;\n  /** The underlying telemetry strategy to use */\n  strategy?: TelemetryClientStrategy;\n};\n\ntype TelemetryActor = Required<Pick<TelemetryOptions, 'anonymousId' | 'sessionId'>> & {\n  /**\n   * Hashed version of the user ID, untracable to an actual user.\n   * If this value is set to `undefined`, telemetry is considered uninitialized and will wait until its set.\n   * If this value is set to `null`, telemetry is considered initialized without an authenticated user.\n   * If this value is set to a string, telemetry is considered initialized with an authenticated user.\n   */\n  userHash?: string | null;\n};\n\nexport class Telemetry {\n  private context = createContext();\n  private client: TelemetryClient = new FetchDetachedClient();\n  private actor: TelemetryActor;\n\n  /** A list of all events, recorded before the telemetry was fully initialized */\n  private earlyRecords: TelemetryRecord[] = [];\n\n  constructor({\n    anonymousId = getAnonymousId(),\n    sessionId = crypto.randomUUID(),\n    userId,\n    strategy = 'detached',\n  }: TelemetryOptions = {}) {\n    this.actor = { anonymousId, sessionId };\n    this.setStrategy(env.EXPO_NO_TELEMETRY_DETACH ? 'debug' : strategy);\n\n    if (userId) {\n      this.initialize({ userId });\n    }\n  }\n\n  get strategy() {\n    return this.client.strategy;\n  }\n\n  setStrategy(strategy: TelemetryOptions['strategy']) {\n    // Abort when client is already using the correct strategy\n    if (this.client.strategy === strategy) return;\n    // Abort when debugging the telemetry\n    if (env.EXPO_NO_TELEMETRY_DETACH && strategy !== 'debug') return;\n\n    debug('Switching strategy from %s to %s', this.client.strategy, strategy);\n\n    // Load and instantiate the correct client, based on strategy\n    const client = createClientFromStrategy(strategy);\n    // Replace the client, and re-record any pending records\n    this.client.abort().forEach((record) => client.record([record]));\n    this.client = client;\n\n    return this;\n  }\n\n  get isInitialized() {\n    return this.actor.userHash !== undefined;\n  }\n\n  initialize({ userId }: { userId: string | null }) {\n    this.actor.userHash = userId ? hashUserId(userId) : null;\n    this.flushEarlyRecords();\n  }\n\n  private flushEarlyRecords() {\n    if (this.earlyRecords.length) {\n      this.recordInternal(this.earlyRecords);\n      this.earlyRecords = [];\n    }\n  }\n\n  private recordInternal(records: TelemetryRecord[]) {\n    return this.client.record(\n      records.map((record) => ({\n        ...record,\n        type: 'track' as const,\n        sentAt: new Date(),\n        messageId: createMessageId(record),\n        anonymousId: this.actor.anonymousId,\n        userHash: this.actor.userHash,\n        context: {\n          ...this.context,\n          sessionId: this.actor.sessionId,\n          client: { mode: this.client.strategy },\n        },\n      }))\n    );\n  }\n\n  record(record: TelemetryRecord | TelemetryRecord[]) {\n    const records = Array.isArray(record) ? record : [record];\n\n    debug('Recording %d event(s)', records.length);\n\n    if (!this.isInitialized) {\n      this.earlyRecords.push(...records);\n      return;\n    }\n\n    return this.recordInternal(records);\n  }\n\n  flush() {\n    debug('Flushing events...');\n    this.flushEarlyRecords();\n    return this.client.flush();\n  }\n\n  flushOnExit() {\n    this.setStrategy('detached');\n    this.flushEarlyRecords();\n    return this.client.flush();\n  }\n}\n\nfunction createClientFromStrategy(strategy: TelemetryOptions['strategy']) {\n  // When debugging, use the actual Rudderstack client, but lazy load it\n  if (env.EXPO_NO_TELEMETRY_DETACH || strategy === 'debug' || strategy === 'instant') {\n    return new FetchClient();\n  }\n\n  return new FetchDetachedClient();\n}\n\n/** Generate a unique message ID using a random hash and UUID */\nfunction createMessageId(record: TelemetryRecord) {\n  const uuid = crypto.randomUUID();\n  const md5 = crypto.createHash('md5').update(JSON.stringify(record)).digest('hex');\n\n  return `node-${md5}-${uuid}`;\n}\n\n/** Hash the user identifier to make it untracable */\nfunction hashUserId(userId: string) {\n  return crypto.createHash('sha256').update(userId).digest('hex');\n}\n"], "names": ["Telemetry", "debug", "require", "constructor", "anonymousId", "getAnonymousId", "sessionId", "crypto", "randomUUID", "userId", "strategy", "context", "createContext", "client", "FetchDetachedClient", "earlyRecords", "actor", "setStrategy", "env", "EXPO_NO_TELEMETRY_DETACH", "initialize", "createClientFromStrategy", "abort", "for<PERSON>ach", "record", "isInitialized", "userHash", "undefined", "hashUserId", "flush<PERSON><PERSON><PERSON><PERSON><PERSON>ord<PERSON>", "length", "recordInternal", "records", "map", "type", "sentAt", "Date", "messageId", "createMessageId", "mode", "Array", "isArray", "push", "flush", "flushOnExit", "FetchClient", "uuid", "md5", "createHash", "update", "JSON", "stringify", "digest"], "mappings": ";;;;+BAgCaA;;;eAAAA;;;;gEAhCM;;;;;;6BAES;qCACQ;yBAEN;8BACC;qBACX;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAuBxB,MAAMF;IAQXG,YAAY,EACVC,cAAcC,IAAAA,4BAAc,GAAE,EAC9BC,YAAYC,qBAAM,CAACC,UAAU,EAAE,EAC/BC,MAAM,EACNC,WAAW,UAAU,EACJ,GAAG,CAAC,CAAC,CAAE;aAZlBC,UAAUC,IAAAA,sBAAa;aACvBC,SAA0B,IAAIC,wCAAmB;QAGzD,8EAA8E,QACtEC,eAAkC,EAAE;QAQ1C,IAAI,CAACC,KAAK,GAAG;YAAEZ;YAAaE;QAAU;QACtC,IAAI,CAACW,WAAW,CAACC,QAAG,CAACC,wBAAwB,GAAG,UAAUT;QAE1D,IAAID,QAAQ;YACV,IAAI,CAACW,UAAU,CAAC;gBAAEX;YAAO;QAC3B;IACF;IAEA,IAAIC,WAAW;QACb,OAAO,IAAI,CAACG,MAAM,CAACH,QAAQ;IAC7B;IAEAO,YAAYP,QAAsC,EAAE;QAClD,0DAA0D;QAC1D,IAAI,IAAI,CAACG,MAAM,CAACH,QAAQ,KAAKA,UAAU;QACvC,qCAAqC;QACrC,IAAIQ,QAAG,CAACC,wBAAwB,IAAIT,aAAa,SAAS;QAE1DT,MAAM,oCAAoC,IAAI,CAACY,MAAM,CAACH,QAAQ,EAAEA;QAEhE,6DAA6D;QAC7D,MAAMG,SAASQ,yBAAyBX;QACxC,wDAAwD;QACxD,IAAI,CAACG,MAAM,CAACS,KAAK,GAAGC,OAAO,CAAC,CAACC,SAAWX,OAAOW,MAAM,CAAC;gBAACA;aAAO;QAC9D,IAAI,CAACX,MAAM,GAAGA;QAEd,OAAO,IAAI;IACb;IAEA,IAAIY,gBAAgB;QAClB,OAAO,IAAI,CAACT,KAAK,CAACU,QAAQ,KAAKC;IACjC;IAEAP,WAAW,EAAEX,MAAM,EAA6B,EAAE;QAChD,IAAI,CAACO,KAAK,CAACU,QAAQ,GAAGjB,SAASmB,WAAWnB,UAAU;QACpD,IAAI,CAACoB,iBAAiB;IACxB;IAEQA,oBAAoB;QAC1B,IAAI,IAAI,CAACd,YAAY,CAACe,MAAM,EAAE;YAC5B,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,YAAY;YACrC,IAAI,CAACA,YAAY,GAAG,EAAE;QACxB;IACF;IAEQgB,eAAeC,OAA0B,EAAE;QACjD,OAAO,IAAI,CAACnB,MAAM,CAACW,MAAM,CACvBQ,QAAQC,GAAG,CAAC,CAACT,SAAY,CAAA;gBACvB,GAAGA,MAAM;gBACTU,MAAM;gBACNC,QAAQ,IAAIC;gBACZC,WAAWC,gBAAgBd;gBAC3BpB,aAAa,IAAI,CAACY,KAAK,CAACZ,WAAW;gBACnCsB,UAAU,IAAI,CAACV,KAAK,CAACU,QAAQ;gBAC7Bf,SAAS;oBACP,GAAG,IAAI,CAACA,OAAO;oBACfL,WAAW,IAAI,CAACU,KAAK,CAACV,SAAS;oBAC/BO,QAAQ;wBAAE0B,MAAM,IAAI,CAAC1B,MAAM,CAACH,QAAQ;oBAAC;gBACvC;YACF,CAAA;IAEJ;IAEAc,OAAOA,MAA2C,EAAE;QAClD,MAAMQ,UAAUQ,MAAMC,OAAO,CAACjB,UAAUA,SAAS;YAACA;SAAO;QAEzDvB,MAAM,yBAAyB+B,QAAQF,MAAM;QAE7C,IAAI,CAAC,IAAI,CAACL,aAAa,EAAE;YACvB,IAAI,CAACV,YAAY,CAAC2B,IAAI,IAAIV;YAC1B;QACF;QAEA,OAAO,IAAI,CAACD,cAAc,CAACC;IAC7B;IAEAW,QAAQ;QACN1C,MAAM;QACN,IAAI,CAAC4B,iBAAiB;QACtB,OAAO,IAAI,CAAChB,MAAM,CAAC8B,KAAK;IAC1B;IAEAC,cAAc;QACZ,IAAI,CAAC3B,WAAW,CAAC;QACjB,IAAI,CAACY,iBAAiB;QACtB,OAAO,IAAI,CAAChB,MAAM,CAAC8B,KAAK;IAC1B;AACF;AAEA,SAAStB,yBAAyBX,QAAsC;IACtE,sEAAsE;IACtE,IAAIQ,QAAG,CAACC,wBAAwB,IAAIT,aAAa,WAAWA,aAAa,WAAW;QAClF,OAAO,IAAImC,wBAAW;IACxB;IAEA,OAAO,IAAI/B,wCAAmB;AAChC;AAEA,8DAA8D,GAC9D,SAASwB,gBAAgBd,MAAuB;IAC9C,MAAMsB,OAAOvC,qBAAM,CAACC,UAAU;IAC9B,MAAMuC,MAAMxC,qBAAM,CAACyC,UAAU,CAAC,OAAOC,MAAM,CAACC,KAAKC,SAAS,CAAC3B,SAAS4B,MAAM,CAAC;IAE3E,OAAO,CAAC,KAAK,EAAEL,IAAI,CAAC,EAAED,MAAM;AAC9B;AAEA,mDAAmD,GACnD,SAASlB,WAAWnB,MAAc;IAChC,OAAOF,qBAAM,CAACyC,UAAU,CAAC,UAAUC,MAAM,CAACxC,QAAQ2C,MAAM,CAAC;AAC3D"}