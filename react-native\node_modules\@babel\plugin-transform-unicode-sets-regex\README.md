# @babel/plugin-transform-unicode-sets-regex

> Compile regular expressions' unicodeSets (v) flag.

See our website [@babel/plugin-transform-unicode-sets-regex](https://babeljs.io/docs/babel-plugin-transform-unicode-sets-regex) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-unicode-sets-regex
```

or using yarn:

```sh
yarn add @babel/plugin-transform-unicode-sets-regex --dev
```
