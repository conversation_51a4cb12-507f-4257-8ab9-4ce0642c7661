{"version": 3, "sources": ["../../../../src/start/server/DevToolsPluginManager.ts"], "sourcesContent": ["import type { ModuleDescriptorDevTools } from 'expo-modules-autolinking/exports';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nconst debug = require('debug')('expo:start:server:devtools');\n\nexport const DevToolsPluginEndpoint = '/_expo/plugins';\n\ninterface AutolinkingPlugin {\n  packageName: string;\n  packageRoot: string;\n  webpageRoot: string;\n}\n\nexport interface DevToolsPlugin extends AutolinkingPlugin {\n  webpageEndpoint: string;\n}\n\nexport default class DevToolsPluginManager {\n  private plugins: DevToolsPlugin[] | null = null;\n\n  constructor(private projectRoot: string) {}\n\n  public async queryPluginsAsync(): Promise<DevToolsPlugin[]> {\n    if (this.plugins) {\n      return this.plugins;\n    }\n    const plugins = (await this.queryAutolinkedPluginsAsync(this.projectRoot)).map((plugin) => ({\n      ...plugin,\n      webpageEndpoint: `${DevToolsPluginEndpoint}/${plugin.packageName}`,\n    }));\n    this.plugins = plugins;\n    return this.plugins;\n  }\n\n  public async queryPluginWebpageRootAsync(pluginName: string): Promise<string | null> {\n    const plugins = await this.queryPluginsAsync();\n    const plugin = plugins.find((p) => p.packageName === pluginName);\n    return plugin?.webpageRoot ?? null;\n  }\n\n  private async queryAutolinkedPluginsAsync(projectRoot: string): Promise<AutolinkingPlugin[]> {\n    const expoPackagePath = resolveFrom.silent(projectRoot, 'expo/package.json');\n    if (!expoPackagePath) {\n      return [];\n    }\n    const resolvedPath = resolveFrom.silent(\n      path.dirname(expoPackagePath),\n      'expo-modules-autolinking/exports'\n    );\n    if (!resolvedPath) {\n      return [];\n    }\n    const autolinkingModule = require(\n      resolvedPath\n    ) as typeof import('expo-modules-autolinking/exports');\n    if (!autolinkingModule.queryAutolinkingModulesFromProjectAsync) {\n      throw new Error(\n        'Missing exported `queryAutolinkingModulesFromProjectAsync()` function from `expo-modules-autolinking`'\n      );\n    }\n    const plugins = (await autolinkingModule.queryAutolinkingModulesFromProjectAsync(projectRoot, {\n      platform: 'devtools',\n      onlyProjectDeps: false,\n    })) as ModuleDescriptorDevTools[];\n    debug('Found autolinked plugins', this.plugins);\n    return plugins;\n  }\n}\n"], "names": ["DevToolsPluginEndpoint", "DevToolsPluginManager", "debug", "require", "constructor", "projectRoot", "plugins", "queryPluginsAsync", "queryAutolinkedPluginsAsync", "map", "plugin", "webpageEndpoint", "packageName", "queryPluginWebpageRootAsync", "pluginName", "find", "p", "webpageRoot", "expoPackagePath", "resolveFrom", "silent", "<PERSON><PERSON><PERSON>", "path", "dirname", "autolinkingModule", "queryAutolinkingModulesFromProjectAsync", "Error", "platform", "onlyProjectDeps"], "mappings": ";;;;;;;;;;;IAMaA,sBAAsB;eAAtBA;;IAYb,OAkDC;eAlDoBC;;;;gEAjBJ;;;;;;;gEACO;;;;;;;;;;;AAExB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,MAAMH,yBAAyB;AAYvB,MAAMC;IAGnBG,YAAY,AAAQC,WAAmB,CAAE;aAArBA,cAAAA;aAFZC,UAAmC;IAED;IAE1C,MAAaC,oBAA+C;QAC1D,IAAI,IAAI,CAACD,OAAO,EAAE;YAChB,OAAO,IAAI,CAACA,OAAO;QACrB;QACA,MAAMA,UAAU,AAAC,CAAA,MAAM,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACH,WAAW,CAAA,EAAGI,GAAG,CAAC,CAACC,SAAY,CAAA;gBAC1F,GAAGA,MAAM;gBACTC,iBAAiB,GAAGX,uBAAuB,CAAC,EAAEU,OAAOE,WAAW,EAAE;YACpE,CAAA;QACA,IAAI,CAACN,OAAO,GAAGA;QACf,OAAO,IAAI,CAACA,OAAO;IACrB;IAEA,MAAaO,4BAA4BC,UAAkB,EAA0B;QACnF,MAAMR,UAAU,MAAM,IAAI,CAACC,iBAAiB;QAC5C,MAAMG,SAASJ,QAAQS,IAAI,CAAC,CAACC,IAAMA,EAAEJ,WAAW,KAAKE;QACrD,OAAOJ,CAAAA,0BAAAA,OAAQO,WAAW,KAAI;IAChC;IAEA,MAAcT,4BAA4BH,WAAmB,EAAgC;QAC3F,MAAMa,kBAAkBC,sBAAW,CAACC,MAAM,CAACf,aAAa;QACxD,IAAI,CAACa,iBAAiB;YACpB,OAAO,EAAE;QACX;QACA,MAAMG,eAAeF,sBAAW,CAACC,MAAM,CACrCE,eAAI,CAACC,OAAO,CAACL,kBACb;QAEF,IAAI,CAACG,cAAc;YACjB,OAAO,EAAE;QACX;QACA,MAAMG,oBAAoBrB,QACxBkB;QAEF,IAAI,CAACG,kBAAkBC,uCAAuC,EAAE;YAC9D,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMpB,UAAW,MAAMkB,kBAAkBC,uCAAuC,CAACpB,aAAa;YAC5FsB,UAAU;YACVC,iBAAiB;QACnB;QACA1B,MAAM,4BAA4B,IAAI,CAACI,OAAO;QAC9C,OAAOA;IACT;AACF"}