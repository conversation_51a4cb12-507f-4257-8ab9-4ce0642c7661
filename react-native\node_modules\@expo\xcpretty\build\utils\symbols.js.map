{"version": 3, "file": "symbols.js", "sourceRoot": "", "sources": ["../../src/utils/symbols.ts"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS,GAAG,KAAK,CAAC;AAEX,QAAA,IAAI,GAAG,GAAG,CAAC;AACX,QAAA,IAAI,GAAG,GAAG,CAAC;AACX,QAAA,OAAO,GAAG,GAAG,CAAC;AACd,QAAA,UAAU,GAAG,QAAQ,CAAC,CAAC,MAAM;AAC7B,QAAA,OAAO,GAAG,GAAG,CAAC;AACd,QAAA,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjC,QAAA,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;AACpC,QAAA,MAAM,GAAG,MAAM,CAAC;AAChB,QAAA,UAAU,GAAG,GAAG,CAAC", "sourcesContent": ["const USE_ASCII = false;\n\nexport const PASS = '✓';\nexport const FAIL = '✗';\nexport const PENDING = '⧖';\nexport const COMPLETION = '\\u203A'; //'▸';\nexport const MEASURE = '◷';\nexport const ERROR = USE_ASCII ? '[x]' : '❌ ';\nexport const WARNING = USE_ASCII ? '[!]' : '⚠️ ';\nexport const INDENT = '    ';\nexport const BREADCRUMB = '»';\n"]}