{"name": "expo-sharing", "version": "13.0.1", "description": "Provides a way to share files directly with other compatible applications.", "main": "build/Sharing.js", "types": "build/Sharing.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-sharing", "android", "ios", "web"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-sharing"}, "jest": {"preset": "expo-module-scripts"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/sharing/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.0.3"}, "peerDependencies": {"expo": "*"}, "gitHead": "986a4689b91f3efc527f7178a320b987c0005842"}