import*as e from"../../core/common/common.js";import*as o from"../../models/formatter/formatter.js";import*as r from"../../ui/components/diff_view/diff_view.js";class i{static iconDataForResourceType(o){return o.isDocument()?{iconName:"file-document",color:"var(--icon-file-document)"}:o.isImage()?{iconName:"file-image",color:"var(--icon-file-image)"}:o.isFont()?{iconName:"file-font",color:"var(--icon-file-font)"}:o.isScript()?{iconName:"file-script",color:"var(--icon-file-script)"}:o.isStyleSheet()?{iconName:"file-stylesheet",color:"var(--icon-file-styles)"}:o.name()===e.ResourceType.resourceTypes.Manifest.name()?{iconName:"file-manifest",color:"var(--icon-default)"}:o.name()===e.ResourceType.resourceTypes.Wasm.name()?{iconName:"file-wasm",color:"var(--icon-default)"}:o.name()===e.ResourceType.resourceTypes.WebSocket.name()?{iconName:"file-websocket",color:"var(--icon-default)"}:o.name()===e.ResourceType.resourceTypes.Media.name()?{iconName:"file-media",color:"var(--icon-file-media)"}:o.isWebbundle()?{iconName:"bundle",color:"var(--icon-default)"}:o.name()===e.ResourceType.resourceTypes.Fetch.name()||o.name()===e.ResourceType.resourceTypes.XHR.name()?{iconName:"file-fetch-xhr",color:"var(--icon-default)"}:{iconName:"file-generic",color:"var(--icon-default)"}}static async formatCSSChangesFromDiff(e){const{originalLines:o,currentLines:i,rows:t}=r.DiffView.buildDiffRows(e),a=await n(o.join("\n")),c=await n(i.join("\n"));let s,l,f="",m=!1;for(const{currentLineNumber:e,originalLineNumber:r,type:n}of t){if("deletion"!==n&&"addition"!==n)continue;const t="deletion"===n,u=t?r-1:e-1,p=(t?o:i)[u].trim(),{declarationIDToStyleRule:d,styleRuleIDToStyleRule:y}=t?a:c;let g,T="";if(d.has(u)){g=d.get(u);const e=g.selector;e!==s&&e!==l&&(T+=`${e} {\n`),T+="  ",m=!0}else m&&(T="}\n\n",m=!1),y.has(u)&&(g=y.get(u));f+=T+(t?`/* ${p} */`:p)+"\n",t?s=g?.selector:l=g?.selector}return f.length>0&&(f+="}"),f}static highlightElement(e){e.scrollIntoViewIfNeeded(),e.animate([{offset:0,backgroundColor:"rgba(255, 255, 0, 0.2)"},{offset:.1,backgroundColor:"rgba(255, 255, 0, 0.7)"},{offset:1,backgroundColor:"transparent"}],{duration:2e3,easing:"cubic-bezier(0, 0, 0.2, 1)"})}}async function n(e){const r=await new Promise((r=>{const i=[];o.FormatterWorkerPool.formatterWorkerPool().parseCSS(e,((e,o)=>{i.push(...o),e&&r(i)}))})),i=new Map,n=new Map;for(const e of r)if("styleRange"in e){const o=e.selectorText.split("\n").pop()?.trim();if(!o)continue;const r={rule:e,selector:o};n.set(e.styleRange.startLine,r);for(const o of e.properties)i.set(o.range.startLine,r)}return{declarationIDToStyleRule:i,styleRuleIDToStyleRule:n}}export{i as PanelUtils};
