{"version": 3, "sources": ["../../../../../src/start/server/metro/metroPrivateServer.ts"], "sourcesContent": ["import { TransformInputOptions } from 'metro';\nimport type Metro from 'metro';\nimport { ConfigT } from 'metro-config';\nimport assert from 'node:assert';\n\nexport type MetroPrivateServer = import('metro').Server & {\n  _bundler: import('metro/src/IncrementalBundler').default;\n  _config: ConfigT;\n  _createModuleId: (path: string, context?: { environment?: string; platform: string }) => number;\n  _isEnded: boolean;\n  _nextBundleBuildNumber: number;\n  _platforms: Set<string>;\n  _reporter: import('metro/src/lib/reporting').Reporter;\n  _serverOptions: import('metro').ServerOptions | void;\n\n  getNewBuildNumber(): number;\n  _getSortedModules(\n    graph: import('metro/src/IncrementalBundler').OutputGraph\n  ): import('metro/src/DeltaBundler/types').Module[];\n\n  _resolveRelativePath(\n    filePath: string,\n    {\n      relativeTo,\n      resolverOptions,\n      transformOptions,\n    }: {\n      relativeTo: 'project' | 'server';\n      resolverOptions: import('metro/src/shared/types').ResolverInputOptions;\n      transformOptions: TransformInputOptions;\n    }\n  ): Promise<string>;\n\n  _shouldAddModuleToIgnoreList(module: import('metro/src/DeltaBundler/types').Module<any>): boolean;\n};\n\nexport function assertMetroPrivateServer(metro: Metro.Server): asserts metro is MetroPrivateServer {\n  assert(metro, 'Metro server undefined.');\n  assert(\n    '_config' in metro && '_bundler' in metro,\n    'Metro server is missing expected properties (_config, _bundler). This could be due to a version mismatch or change in the Metro API.'\n  );\n}\n"], "names": ["assertMetroPrivateServer", "metro", "assert"], "mappings": ";;;;+BAoCgBA;;;eAAAA;;;;gEAjCG;;;;;;;;;;;AAiCZ,SAASA,yBAAyBC,KAAmB;IAC1DC,IAAAA,qBAAM,EAACD,OAAO;IACdC,IAAAA,qBAAM,EACJ,aAAaD,SAAS,cAAcA,OACpC;AAEJ"}