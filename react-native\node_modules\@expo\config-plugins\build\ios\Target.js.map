{"version": 3, "file": "Target.js", "names": ["_BuildScheme", "data", "require", "_Xcodeproj", "_string", "TargetType", "exports", "getXCBuildConfigurationFromPbxproj", "project", "targetName", "buildConfiguration", "nativeTarget", "findNativeTargetByName", "findFirstNativeTarget", "xcBuildConfiguration", "getBuildConfigurationForListIdAndName", "configurationListId", "buildConfigurationList", "findApplicationTargetWithDependenciesAsync", "projectRoot", "scheme", "applicationTargetName", "getApplicationTargetNameForSchemeAsync", "getPbxproj", "applicationTarget", "dependencies", "getTargetDependencies", "name", "trimQuotes", "type", "APPLICATION", "signable", "parentTarget", "length", "undefined", "nonSignableTargetTypes", "FRAMEWORK", "map", "value", "target", "targetId", "getPBXGroupByKeyAndType", "findNativeTargetById", "isTargetOfType", "EXTENSION", "OTHER", "some", "signableTargetType", "targetType", "productType", "getNativeTargets", "section", "pbxNativeTargetSection", "Object", "entries", "filter", "isNotComment", "findSignableTargets", "targets", "signableTargetTypes", "APP_CLIP", "WATCH", "STICKER_PACK_EXTENSION", "applicationTargets", "Error", "nativeTargets", "nativeTargetEntry", "find", "i", "key"], "sources": ["../../src/ios/Target.ts"], "sourcesContent": ["import { PBXNativeTarget, PBXTargetDependency, XCBuildConfiguration, XcodeProject } from 'xcode';\n\nimport { getApplicationTargetNameForSchemeAsync } from './BuildScheme';\nimport {\n  getBuildConfigurationForListIdAndName,\n  getPbxproj,\n  isNotComment,\n  NativeTargetSectionEntry,\n} from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\n\nexport enum TargetType {\n  APPLICATION = 'com.apple.product-type.application',\n  EXTENSION = 'com.apple.product-type.app-extension',\n  WATCH = 'com.apple.product-type.application.watchapp',\n  APP_CLIP = 'com.apple.product-type.application.on-demand-install-capable',\n  STICKER_PACK_EXTENSION = 'com.apple.product-type.app-extension.messages-sticker-pack',\n  FRAMEWORK = 'com.apple.product-type.framework',\n  OTHER = 'other',\n}\n\nexport interface Target {\n  name: string;\n  type: TargetType;\n  signable: boolean;\n  dependencies?: Target[];\n}\n\nexport function getXCBuildConfigurationFromPbxproj(\n  project: XcodeProject,\n  {\n    targetName,\n    buildConfiguration = 'Release',\n  }: { targetName?: string; buildConfiguration?: string } = {}\n): XCBuildConfiguration | null {\n  const [, nativeTarget] = targetName\n    ? findNativeTargetByName(project, targetName)\n    : findFirstNativeTarget(project);\n  const [, xcBuildConfiguration] = getBuildConfigurationForListIdAndName(project, {\n    configurationListId: nativeTarget.buildConfigurationList,\n    buildConfiguration,\n  });\n  return xcBuildConfiguration ?? null;\n}\n\nexport async function findApplicationTargetWithDependenciesAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<Target> {\n  const applicationTargetName = await getApplicationTargetNameForSchemeAsync(projectRoot, scheme);\n  const project = getPbxproj(projectRoot);\n  const [, applicationTarget] = findNativeTargetByName(project, applicationTargetName);\n  const dependencies = getTargetDependencies(project, applicationTarget);\n  return {\n    name: trimQuotes(applicationTarget.name),\n    type: TargetType.APPLICATION,\n    signable: true,\n    dependencies,\n  };\n}\n\nfunction getTargetDependencies(\n  project: XcodeProject,\n  parentTarget: PBXNativeTarget\n): Target[] | undefined {\n  if (!parentTarget.dependencies || parentTarget.dependencies.length === 0) {\n    return undefined;\n  }\n\n  const nonSignableTargetTypes: TargetType[] = [TargetType.FRAMEWORK];\n\n  return parentTarget.dependencies.map(({ value }) => {\n    const { target: targetId } = project.getPBXGroupByKeyAndType(\n      value,\n      'PBXTargetDependency'\n    ) as PBXTargetDependency;\n\n    const [, target] = findNativeTargetById(project, targetId);\n\n    const type = isTargetOfType(target, TargetType.EXTENSION)\n      ? TargetType.EXTENSION\n      : TargetType.OTHER;\n    return {\n      name: trimQuotes(target.name),\n      type,\n      signable: !nonSignableTargetTypes.some((signableTargetType) =>\n        isTargetOfType(target, signableTargetType)\n      ),\n      dependencies: getTargetDependencies(project, target),\n    };\n  });\n}\n\nexport function isTargetOfType(target: PBXNativeTarget, targetType: TargetType): boolean {\n  return trimQuotes(target.productType) === targetType;\n}\n\nexport function getNativeTargets(project: XcodeProject): NativeTargetSectionEntry[] {\n  const section = project.pbxNativeTargetSection();\n  return Object.entries(section).filter(isNotComment);\n}\n\nexport function findSignableTargets(project: XcodeProject): NativeTargetSectionEntry[] {\n  const targets = getNativeTargets(project);\n\n  const signableTargetTypes: TargetType[] = [\n    TargetType.APPLICATION,\n    TargetType.APP_CLIP,\n    TargetType.EXTENSION,\n    TargetType.WATCH,\n    TargetType.STICKER_PACK_EXTENSION,\n  ];\n\n  const applicationTargets = targets.filter(([, target]) => {\n    for (const targetType of signableTargetTypes) {\n      if (isTargetOfType(target, targetType)) {\n        return true;\n      }\n    }\n    return false;\n  });\n  if (applicationTargets.length === 0) {\n    throw new Error(`Could not find any signable targets in project.pbxproj`);\n  }\n  return applicationTargets;\n}\n\nexport function findFirstNativeTarget(project: XcodeProject): NativeTargetSectionEntry {\n  const targets = getNativeTargets(project);\n  const applicationTargets = targets.filter(([, target]) =>\n    isTargetOfType(target, TargetType.APPLICATION)\n  );\n  if (applicationTargets.length === 0) {\n    throw new Error(`Could not find any application target in project.pbxproj`);\n  }\n  return applicationTargets[0];\n}\n\nexport function findNativeTargetByName(\n  project: XcodeProject,\n  targetName: string\n): NativeTargetSectionEntry {\n  const nativeTargets = getNativeTargets(project);\n  const nativeTargetEntry = nativeTargets.find(([, i]) => trimQuotes(i.name) === targetName);\n  if (!nativeTargetEntry) {\n    throw new Error(`Could not find target '${targetName}' in project.pbxproj`);\n  }\n  return nativeTargetEntry;\n}\n\nfunction findNativeTargetById(project: XcodeProject, targetId: string): NativeTargetSectionEntry {\n  const nativeTargets = getNativeTargets(project);\n  const nativeTargetEntry = nativeTargets.find(([key]) => key === targetId);\n  if (!nativeTargetEntry) {\n    throw new Error(`Could not find target with id '${targetId}' in project.pbxproj`);\n  }\n  return nativeTargetEntry;\n}\n"], "mappings": ";;;;;;;;;;;;;AAEA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAMA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4C,IAEhCI,UAAU,GAAAC,OAAA,CAAAD,UAAA,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAiBf,SAASE,kCAAkCA,CAChDC,OAAqB,EACrB;EACEC,UAAU;EACVC,kBAAkB,GAAG;AAC+B,CAAC,GAAG,CAAC,CAAC,EAC/B;EAC7B,MAAM,GAAGC,YAAY,CAAC,GAAGF,UAAU,GAC/BG,sBAAsB,CAACJ,OAAO,EAAEC,UAAU,CAAC,GAC3CI,qBAAqB,CAACL,OAAO,CAAC;EAClC,MAAM,GAAGM,oBAAoB,CAAC,GAAG,IAAAC,kDAAqC,EAACP,OAAO,EAAE;IAC9EQ,mBAAmB,EAAEL,YAAY,CAACM,sBAAsB;IACxDP;EACF,CAAC,CAAC;EACF,OAAOI,oBAAoB,IAAI,IAAI;AACrC;AAEO,eAAeI,0CAA0CA,CAC9DC,WAAmB,EACnBC,MAAc,EACG;EACjB,MAAMC,qBAAqB,GAAG,MAAM,IAAAC,qDAAsC,EAACH,WAAW,EAAEC,MAAM,CAAC;EAC/F,MAAMZ,OAAO,GAAG,IAAAe,uBAAU,EAACJ,WAAW,CAAC;EACvC,MAAM,GAAGK,iBAAiB,CAAC,GAAGZ,sBAAsB,CAACJ,OAAO,EAAEa,qBAAqB,CAAC;EACpF,MAAMI,YAAY,GAAGC,qBAAqB,CAAClB,OAAO,EAAEgB,iBAAiB,CAAC;EACtE,OAAO;IACLG,IAAI,EAAE,IAAAC,oBAAU,EAACJ,iBAAiB,CAACG,IAAI,CAAC;IACxCE,IAAI,EAAExB,UAAU,CAACyB,WAAW;IAC5BC,QAAQ,EAAE,IAAI;IACdN;EACF,CAAC;AACH;AAEA,SAASC,qBAAqBA,CAC5BlB,OAAqB,EACrBwB,YAA6B,EACP;EACtB,IAAI,CAACA,YAAY,CAACP,YAAY,IAAIO,YAAY,CAACP,YAAY,CAACQ,MAAM,KAAK,CAAC,EAAE;IACxE,OAAOC,SAAS;EAClB;EAEA,MAAMC,sBAAoC,GAAG,CAAC9B,UAAU,CAAC+B,SAAS,CAAC;EAEnE,OAAOJ,YAAY,CAACP,YAAY,CAACY,GAAG,CAAC,CAAC;IAAEC;EAAM,CAAC,KAAK;IAClD,MAAM;MAAEC,MAAM,EAAEC;IAAS,CAAC,GAAGhC,OAAO,CAACiC,uBAAuB,CAC1DH,KAAK,EACL,qBACF,CAAwB;IAExB,MAAM,GAAGC,MAAM,CAAC,GAAGG,oBAAoB,CAAClC,OAAO,EAAEgC,QAAQ,CAAC;IAE1D,MAAMX,IAAI,GAAGc,cAAc,CAACJ,MAAM,EAAElC,UAAU,CAACuC,SAAS,CAAC,GACrDvC,UAAU,CAACuC,SAAS,GACpBvC,UAAU,CAACwC,KAAK;IACpB,OAAO;MACLlB,IAAI,EAAE,IAAAC,oBAAU,EAACW,MAAM,CAACZ,IAAI,CAAC;MAC7BE,IAAI;MACJE,QAAQ,EAAE,CAACI,sBAAsB,CAACW,IAAI,CAAEC,kBAAkB,IACxDJ,cAAc,CAACJ,MAAM,EAAEQ,kBAAkB,CAC3C,CAAC;MACDtB,YAAY,EAAEC,qBAAqB,CAAClB,OAAO,EAAE+B,MAAM;IACrD,CAAC;EACH,CAAC,CAAC;AACJ;AAEO,SAASI,cAAcA,CAACJ,MAAuB,EAAES,UAAsB,EAAW;EACvF,OAAO,IAAApB,oBAAU,EAACW,MAAM,CAACU,WAAW,CAAC,KAAKD,UAAU;AACtD;AAEO,SAASE,gBAAgBA,CAAC1C,OAAqB,EAA8B;EAClF,MAAM2C,OAAO,GAAG3C,OAAO,CAAC4C,sBAAsB,CAAC,CAAC;EAChD,OAAOC,MAAM,CAACC,OAAO,CAACH,OAAO,CAAC,CAACI,MAAM,CAACC,yBAAY,CAAC;AACrD;AAEO,SAASC,mBAAmBA,CAACjD,OAAqB,EAA8B;EACrF,MAAMkD,OAAO,GAAGR,gBAAgB,CAAC1C,OAAO,CAAC;EAEzC,MAAMmD,mBAAiC,GAAG,CACxCtD,UAAU,CAACyB,WAAW,EACtBzB,UAAU,CAACuD,QAAQ,EACnBvD,UAAU,CAACuC,SAAS,EACpBvC,UAAU,CAACwD,KAAK,EAChBxD,UAAU,CAACyD,sBAAsB,CAClC;EAED,MAAMC,kBAAkB,GAAGL,OAAO,CAACH,MAAM,CAAC,CAAC,GAAGhB,MAAM,CAAC,KAAK;IACxD,KAAK,MAAMS,UAAU,IAAIW,mBAAmB,EAAE;MAC5C,IAAIhB,cAAc,CAACJ,MAAM,EAAES,UAAU,CAAC,EAAE;QACtC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EACF,IAAIe,kBAAkB,CAAC9B,MAAM,KAAK,CAAC,EAAE;IACnC,MAAM,IAAI+B,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EACA,OAAOD,kBAAkB;AAC3B;AAEO,SAASlD,qBAAqBA,CAACL,OAAqB,EAA4B;EACrF,MAAMkD,OAAO,GAAGR,gBAAgB,CAAC1C,OAAO,CAAC;EACzC,MAAMuD,kBAAkB,GAAGL,OAAO,CAACH,MAAM,CAAC,CAAC,GAAGhB,MAAM,CAAC,KACnDI,cAAc,CAACJ,MAAM,EAAElC,UAAU,CAACyB,WAAW,CAC/C,CAAC;EACD,IAAIiC,kBAAkB,CAAC9B,MAAM,KAAK,CAAC,EAAE;IACnC,MAAM,IAAI+B,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,OAAOD,kBAAkB,CAAC,CAAC,CAAC;AAC9B;AAEO,SAASnD,sBAAsBA,CACpCJ,OAAqB,EACrBC,UAAkB,EACQ;EAC1B,MAAMwD,aAAa,GAAGf,gBAAgB,CAAC1C,OAAO,CAAC;EAC/C,MAAM0D,iBAAiB,GAAGD,aAAa,CAACE,IAAI,CAAC,CAAC,GAAGC,CAAC,CAAC,KAAK,IAAAxC,oBAAU,EAACwC,CAAC,CAACzC,IAAI,CAAC,KAAKlB,UAAU,CAAC;EAC1F,IAAI,CAACyD,iBAAiB,EAAE;IACtB,MAAM,IAAIF,KAAK,CAAC,0BAA0BvD,UAAU,sBAAsB,CAAC;EAC7E;EACA,OAAOyD,iBAAiB;AAC1B;AAEA,SAASxB,oBAAoBA,CAAClC,OAAqB,EAAEgC,QAAgB,EAA4B;EAC/F,MAAMyB,aAAa,GAAGf,gBAAgB,CAAC1C,OAAO,CAAC;EAC/C,MAAM0D,iBAAiB,GAAGD,aAAa,CAACE,IAAI,CAAC,CAAC,CAACE,GAAG,CAAC,KAAKA,GAAG,KAAK7B,QAAQ,CAAC;EACzE,IAAI,CAAC0B,iBAAiB,EAAE;IACtB,MAAM,IAAIF,KAAK,CAAC,kCAAkCxB,QAAQ,sBAAsB,CAAC;EACnF;EACA,OAAO0B,iBAAiB;AAC1B", "ignoreList": []}