{"version": 3, "sources": ["../../../src/serve/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\n\nexport const expoServe: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--port': Number,\n\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Host the production server locally`,\n      chalk`npx expo serve {dim <dir>}`,\n      [\n        chalk`<dir>            Directory of the Expo project. {dim Default: Current working directory}`,\n        `--port <number>  Port to host the server on`,\n        `-h, --help       Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo config -h` shows as fast as possible.\n  const [\n    // ./configAsync\n    { serveAsync },\n    // ../utils/errors\n    { logCmdError },\n  ] = await Promise.all([import('./serveAsync.js'), import('../utils/errors.js')]);\n\n  return serveAsync(getProjectRoot(args), {\n    isDefaultDirectory: !args._[0],\n    // Parsed options\n    port: args['--port'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoServe", "argv", "args", "assertArgs", "Boolean", "Number", "printHelp", "chalk", "join", "serveAsync", "logCmdError", "Promise", "all", "getProjectRoot", "isDefaultDirectory", "_", "port", "catch"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;;gEALK;;;;;;sBAGoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMA,YAAqB,OAAOC;IACvC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,UAAUC;QAEV,UAAU;QACV,MAAM;IACR,GACAJ;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,kCAAkC,CAAC,EACpCC,IAAAA,gBAAK,CAAA,CAAC,0BAA0B,CAAC,EACjC;YACEA,IAAAA,gBAAK,CAAA,CAAC,wFAAwF,CAAC;YAC/F,CAAC,2CAA2C,CAAC;YAC7C,CAAC,2BAA2B,CAAC;SAC9B,CAACC,IAAI,CAAC;IAEX;IAEA,wFAAwF;IACxF,MAAM,CACJ,gBAAgB;IAChB,EAAEC,UAAU,EAAE,EACd,kBAAkB;IAClB,EAAEC,WAAW,EAAE,CAChB,GAAG,MAAMC,QAAQC,GAAG,CAAC;QAAC,mEAAA,QAAO;QAAoB,mEAAA,QAAO;KAAsB;IAE/E,OAAOH,WAAWI,IAAAA,oBAAc,EAACX,OAAO;QACtCY,oBAAoB,CAACZ,KAAKa,CAAC,CAAC,EAAE;QAC9B,iBAAiB;QACjBC,MAAMd,IAAI,CAAC,SAAS;IACtB,GAAGe,KAAK,CAACP;AACX"}