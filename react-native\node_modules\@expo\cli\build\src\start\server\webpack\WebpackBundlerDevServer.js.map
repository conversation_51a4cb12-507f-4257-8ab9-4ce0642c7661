{"version": 3, "sources": ["../../../../../src/start/server/webpack/WebpackBundlerDevServer.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport type { Application } from 'express';\nimport fs from 'node:fs';\nimport http from 'node:http';\nimport path from 'node:path';\nimport resolveFrom from 'resolve-from';\nimport type webpack from 'webpack';\nimport type WebpackDevServer from 'webpack-dev-server';\n\nimport { compileAsync } from './compile';\nimport {\n  importExpoWebpackConfigFromProject,\n  importWebpackDevServerFromProject,\n  importWebpackFromProject,\n} from './resolveFromProject';\nimport { ensureEnvironmentSupportsTLSAsync } from './tls';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { getIpAddress } from '../../../utils/ip';\nimport { setNodeEnv } from '../../../utils/nodeEnv';\nimport { choosePortAsync } from '../../../utils/port';\nimport { createProgressBar } from '../../../utils/progress';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\n\nconst debug = require('debug')('expo:start:server:webpack:devServer') as typeof console.log;\n\nexport type WebpackConfiguration = webpack.Configuration & {\n  devServer?: {\n    before?: (app: Application, server: WebpackDevServer, compiler: webpack.Compiler) => void;\n  };\n};\n\nfunction assertIsWebpackDevServer(value: any): asserts value is WebpackDevServer {\n  if (!value?.sockWrite && !value?.sendMessage) {\n    throw new CommandError(\n      'WEBPACK',\n      value\n        ? 'Expected Webpack dev server, found: ' + (value.constructor?.name ?? value)\n        : 'Webpack dev server not started yet.'\n    );\n  }\n}\n\nexport class WebpackBundlerDevServer extends BundlerDevServer {\n  get name(): string {\n    return 'webpack';\n  }\n\n  public async startTypeScriptServices(): Promise<void> {\n    //  noop -- this feature is Metro-only.\n  }\n\n  public broadcastMessage(\n    method: string | 'reload' | 'devMenu' | 'sendDevCommand',\n    params?: Record<string, any>\n  ): void {\n    if (!this.instance) {\n      return;\n    }\n\n    assertIsWebpackDevServer(this.instance?.server);\n\n    // TODO(EvanBacon): Custom Webpack overlay.\n    // Default webpack-dev-server sockets use \"content-changed\" instead of \"reload\" (what we use on native).\n    // For now, just manually convert the value so our CLI interface can be unified.\n    const hackyConvertedMessage = method === 'reload' ? 'content-changed' : method;\n\n    if ('sendMessage' in this.instance.server) {\n      // @ts-expect-error: https://github.com/expo/expo/issues/21994#issuecomment-1517122501\n      this.instance.server.sendMessage(this.instance.server.sockets, hackyConvertedMessage, params);\n    } else {\n      this.instance.server.sockWrite(this.instance.server.sockets, hackyConvertedMessage, params);\n    }\n  }\n\n  isTargetingNative(): boolean {\n    return false;\n  }\n\n  private async getAvailablePortAsync(options: { defaultPort?: number }): Promise<number> {\n    try {\n      const defaultPort = options?.defaultPort ?? 19006;\n      const port = await choosePortAsync(this.projectRoot, {\n        defaultPort,\n        host: env.WEB_HOST,\n      });\n      if (!port) {\n        throw new CommandError('NO_PORT_FOUND', `Port ${defaultPort} not available.`);\n      }\n      return port;\n    } catch (error: any) {\n      throw new CommandError('NO_PORT_FOUND', error.message);\n    }\n  }\n\n  async bundleAsync({ mode, clear }: { mode: 'development' | 'production'; clear: boolean }) {\n    // Do this first to fail faster.\n    const webpack = importWebpackFromProject(this.projectRoot);\n\n    if (clear) {\n      await this.clearWebProjectCacheAsync(this.projectRoot, mode);\n    }\n\n    const config = await this.loadConfigAsync({\n      isImageEditingEnabled: true,\n      mode,\n    });\n\n    if (!config.plugins) {\n      config.plugins = [];\n    }\n\n    const bar = createProgressBar(chalk`{bold Web} Bundling Javascript [:bar] :percent`, {\n      width: 64,\n      total: 100,\n      clear: true,\n      complete: '=',\n      incomplete: ' ',\n    });\n\n    // NOTE(EvanBacon): Add a progress bar to the webpack logger if defined (e.g. not in CI).\n    if (bar != null) {\n      config.plugins.push(\n        new webpack.ProgressPlugin((percent: number) => {\n          bar?.update(percent);\n          if (percent === 1) {\n            bar?.terminate();\n          }\n        })\n      );\n    }\n\n    // Create a webpack compiler that is configured with custom messages.\n    const compiler = webpack(config);\n\n    try {\n      await compileAsync(compiler);\n    } catch (error: any) {\n      Log.error(chalk.red('Failed to compile'));\n      throw error;\n    } finally {\n      bar?.terminate();\n    }\n  }\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    // Do this first to fail faster.\n    const webpack = importWebpackFromProject(this.projectRoot);\n    const WebpackDevServer = importWebpackDevServerFromProject(this.projectRoot);\n\n    await this.stopAsync();\n\n    options.port = await this.getAvailablePortAsync({\n      defaultPort: options.port,\n    });\n    const { resetDevServer, https, port, mode } = options;\n\n    this.urlCreator = this.getUrlCreator({\n      port,\n      location: {\n        scheme: https ? 'https' : 'http',\n      },\n    });\n\n    debug('Starting webpack on port: ' + port);\n\n    if (resetDevServer) {\n      await this.clearWebProjectCacheAsync(this.projectRoot, mode);\n    }\n\n    if (https) {\n      debug('Configuring TLS to enable HTTPS support');\n      await ensureEnvironmentSupportsTLSAsync(this.projectRoot).catch((error) => {\n        Log.error(`Error creating TLS certificates: ${error}`);\n      });\n    }\n\n    const config = await this.loadConfigAsync(options);\n\n    Log.log(chalk`Starting Webpack on port ${port} in {underline ${mode}} mode.`);\n\n    // Create a webpack compiler that is configured with custom messages.\n    const compiler = webpack(config);\n\n    const server = new WebpackDevServer(compiler, config.devServer);\n    // Launch WebpackDevServer.\n    server.listen(port, env.WEB_HOST, function (this: http.Server, error) {\n      if (error) {\n        Log.error(error.message);\n      }\n    });\n\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        callback?.(err);\n      });\n    };\n\n    const _host = getIpAddress();\n    const protocol = https ? 'https' : 'http';\n\n    return {\n      // Server instance\n      server,\n      // URL Info\n      location: {\n        url: `${protocol}://${_host}:${port}`,\n        port,\n        protocol,\n        host: _host,\n      },\n      middleware: null,\n      // Match the native protocol.\n      messageSocket: {\n        broadcast: this.broadcastMessage,\n      },\n    };\n  }\n\n  /** Load the Webpack config. Exposed for testing. */\n  getProjectConfigFilePath(): string | null {\n    // Check if the project has a webpack.config.js in the root.\n    return (\n      this.getConfigModuleIds().reduce<string | null | undefined>(\n        (prev, moduleId) => prev || resolveFrom.silent(this.projectRoot, moduleId),\n        null\n      ) ?? null\n    );\n  }\n\n  async loadConfigAsync(\n    options: Pick<BundlerStartOptions, 'mode' | 'isImageEditingEnabled' | 'https'>,\n    argv?: string[]\n  ): Promise<WebpackConfiguration> {\n    // let bar: ProgressBar | null = null;\n\n    const env = {\n      projectRoot: this.projectRoot,\n      pwa: !!options.isImageEditingEnabled,\n      // TODO: Use a new loader in Webpack config...\n      logger: {\n        info() {},\n      },\n      mode: options.mode,\n      https: options.https,\n    };\n    setNodeEnv(env.mode ?? 'development');\n    require('@expo/env').load(env.projectRoot);\n    // Check if the project has a webpack.config.js in the root.\n    const projectWebpackConfig = this.getProjectConfigFilePath();\n    let config: WebpackConfiguration;\n    if (projectWebpackConfig) {\n      const webpackConfig = require(projectWebpackConfig);\n      if (typeof webpackConfig === 'function') {\n        config = await webpackConfig(env, argv);\n      } else {\n        config = webpackConfig;\n      }\n    } else {\n      // Fallback to the default expo webpack config.\n      const loadDefaultConfigAsync = importExpoWebpackConfigFromProject(this.projectRoot);\n      config = await loadDefaultConfigAsync(env, argv);\n    }\n    return config;\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./webpack.config.js'];\n  }\n\n  protected async clearWebProjectCacheAsync(\n    projectRoot: string,\n    mode: string = 'development'\n  ): Promise<void> {\n    Log.log(chalk.dim(`Clearing Webpack ${mode} cache directory...`));\n\n    const dir = await ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    const cacheFolder = path.join(dir, 'web/cache', mode);\n    try {\n      await fs.promises.rm(cacheFolder, { recursive: true, force: true });\n    } catch (error: any) {\n      Log.error(`Could not clear ${mode} web cache directory: ${error.message}`);\n    }\n  }\n}\n\nexport function getProjectWebpackConfigFilePath(projectRoot: string) {\n  return resolveFrom.silent(projectRoot, './webpack.config.js');\n}\n"], "names": ["WebpackBundlerDevServer", "getProjectWebpackConfigFilePath", "debug", "require", "assertIsWebpackDevServer", "value", "sockWrite", "sendMessage", "CommandError", "constructor", "name", "BundlerDevServer", "startTypeScriptServices", "broadcastMessage", "method", "params", "instance", "server", "hackyConvertedMessage", "sockets", "isTargetingNative", "getAvailablePortAsync", "options", "defaultPort", "port", "choosePortAsync", "projectRoot", "host", "env", "WEB_HOST", "error", "message", "bundleAsync", "mode", "clear", "webpack", "importWebpackFromProject", "clearWebProjectCacheAsync", "config", "loadConfigAsync", "isImageEditingEnabled", "plugins", "bar", "createProgressBar", "chalk", "width", "total", "complete", "incomplete", "push", "ProgressPlugin", "percent", "update", "terminate", "compiler", "compileAsync", "Log", "red", "startImplementationAsync", "WebpackDevServer", "importWebpackDevServerFromProject", "stopAsync", "resetDevServer", "https", "urlCreator", "getUrlCreator", "location", "scheme", "ensureEnvironmentSupportsTLSAsync", "catch", "log", "devServer", "listen", "originalClose", "close", "bind", "callback", "err", "_host", "getIpAddress", "protocol", "url", "middleware", "messageSocket", "broadcast", "getProjectConfigFilePath", "getConfigModuleIds", "reduce", "prev", "moduleId", "resolveFrom", "silent", "argv", "pwa", "logger", "info", "setNodeEnv", "load", "projectWebpackConfig", "webpackConfig", "loadDefaultConfigAsync", "importExpoWebpackConfigFromProject", "dim", "dir", "ensureDotExpoProjectDirectoryInitialized", "cacheFolder", "path", "join", "fs", "promises", "rm", "recursive", "force"], "mappings": ";;;;;;;;;;;IA6CaA,uBAAuB;eAAvBA;;IAyPGC,+BAA+B;eAA/BA;;;;gEAtSE;;;;;;;gEAEH;;;;;;;gEAEE;;;;;;;gEACO;;;;;;yBAIK;oCAKtB;qBAC2C;6DAC7B;qBACD;wBACS;oBACA;yBACF;sBACK;0BACE;yBACuB;kCACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzE,MAAMC,QAAQC,QAAQ,SAAS;AAQ/B,SAASC,yBAAyBC,KAAU;IAC1C,IAAI,EAACA,yBAAAA,MAAOC,SAAS,KAAI,EAACD,yBAAAA,MAAOE,WAAW,GAAE;YAIIF;QAHhD,MAAM,IAAIG,oBAAY,CACpB,WACAH,QACI,yCAA0CA,CAAAA,EAAAA,qBAAAA,MAAMI,WAAW,qBAAjBJ,mBAAmBK,IAAI,KAAIL,KAAI,IACzE;IAER;AACF;AAEO,MAAML,gCAAgCW,kCAAgB;IAC3D,IAAID,OAAe;QACjB,OAAO;IACT;IAEA,MAAaE,0BAAyC;IACpD,uCAAuC;IACzC;IAEOC,iBACLC,MAAwD,EACxDC,MAA4B,EACtB;YAKmB;QAJzB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB;QACF;QAEAZ,0BAAyB,iBAAA,IAAI,CAACY,QAAQ,qBAAb,eAAeC,MAAM;QAE9C,2CAA2C;QAC3C,wGAAwG;QACxG,gFAAgF;QAChF,MAAMC,wBAAwBJ,WAAW,WAAW,oBAAoBA;QAExE,IAAI,iBAAiB,IAAI,CAACE,QAAQ,CAACC,MAAM,EAAE;YACzC,sFAAsF;YACtF,IAAI,CAACD,QAAQ,CAACC,MAAM,CAACV,WAAW,CAAC,IAAI,CAACS,QAAQ,CAACC,MAAM,CAACE,OAAO,EAAED,uBAAuBH;QACxF,OAAO;YACL,IAAI,CAACC,QAAQ,CAACC,MAAM,CAACX,SAAS,CAAC,IAAI,CAACU,QAAQ,CAACC,MAAM,CAACE,OAAO,EAAED,uBAAuBH;QACtF;IACF;IAEAK,oBAA6B;QAC3B,OAAO;IACT;IAEA,MAAcC,sBAAsBC,OAAiC,EAAmB;QACtF,IAAI;YACF,MAAMC,cAAcD,CAAAA,2BAAAA,QAASC,WAAW,KAAI;YAC5C,MAAMC,OAAO,MAAMC,IAAAA,qBAAe,EAAC,IAAI,CAACC,WAAW,EAAE;gBACnDH;gBACAI,MAAMC,QAAG,CAACC,QAAQ;YACpB;YACA,IAAI,CAACL,MAAM;gBACT,MAAM,IAAIhB,oBAAY,CAAC,iBAAiB,CAAC,KAAK,EAAEe,YAAY,eAAe,CAAC;YAC9E;YACA,OAAOC;QACT,EAAE,OAAOM,OAAY;YACnB,MAAM,IAAItB,oBAAY,CAAC,iBAAiBsB,MAAMC,OAAO;QACvD;IACF;IAEA,MAAMC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAA0D,EAAE;QACzF,gCAAgC;QAChC,MAAMC,UAAUC,IAAAA,4CAAwB,EAAC,IAAI,CAACV,WAAW;QAEzD,IAAIQ,OAAO;YACT,MAAM,IAAI,CAACG,yBAAyB,CAAC,IAAI,CAACX,WAAW,EAAEO;QACzD;QAEA,MAAMK,SAAS,MAAM,IAAI,CAACC,eAAe,CAAC;YACxCC,uBAAuB;YACvBP;QACF;QAEA,IAAI,CAACK,OAAOG,OAAO,EAAE;YACnBH,OAAOG,OAAO,GAAG,EAAE;QACrB;QAEA,MAAMC,MAAMC,IAAAA,2BAAiB,EAACC,IAAAA,gBAAK,CAAA,CAAC,8CAA8C,CAAC,EAAE;YACnFC,OAAO;YACPC,OAAO;YACPZ,OAAO;YACPa,UAAU;YACVC,YAAY;QACd;QAEA,yFAAyF;QACzF,IAAIN,OAAO,MAAM;YACfJ,OAAOG,OAAO,CAACQ,IAAI,CACjB,IAAId,QAAQe,cAAc,CAAC,CAACC;gBAC1BT,uBAAAA,IAAKU,MAAM,CAACD;gBACZ,IAAIA,YAAY,GAAG;oBACjBT,uBAAAA,IAAKW,SAAS;gBAChB;YACF;QAEJ;QAEA,qEAAqE;QACrE,MAAMC,WAAWnB,QAAQG;QAEzB,IAAI;YACF,MAAMiB,IAAAA,qBAAY,EAACD;QACrB,EAAE,OAAOxB,OAAY;YACnB0B,KAAI1B,KAAK,CAACc,gBAAK,CAACa,GAAG,CAAC;YACpB,MAAM3B;QACR,SAAU;YACRY,uBAAAA,IAAKW,SAAS;QAChB;IACF;IAEA,MAAgBK,yBACdpC,OAA4B,EACA;QAC5B,gCAAgC;QAChC,MAAMa,UAAUC,IAAAA,4CAAwB,EAAC,IAAI,CAACV,WAAW;QACzD,MAAMiC,mBAAmBC,IAAAA,qDAAiC,EAAC,IAAI,CAAClC,WAAW;QAE3E,MAAM,IAAI,CAACmC,SAAS;QAEpBvC,QAAQE,IAAI,GAAG,MAAM,IAAI,CAACH,qBAAqB,CAAC;YAC9CE,aAAaD,QAAQE,IAAI;QAC3B;QACA,MAAM,EAAEsC,cAAc,EAAEC,KAAK,EAAEvC,IAAI,EAAES,IAAI,EAAE,GAAGX;QAE9C,IAAI,CAAC0C,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC;YACnCzC;YACA0C,UAAU;gBACRC,QAAQJ,QAAQ,UAAU;YAC5B;QACF;QAEA7D,MAAM,+BAA+BsB;QAErC,IAAIsC,gBAAgB;YAClB,MAAM,IAAI,CAACzB,yBAAyB,CAAC,IAAI,CAACX,WAAW,EAAEO;QACzD;QAEA,IAAI8B,OAAO;YACT7D,MAAM;YACN,MAAMkE,IAAAA,sCAAiC,EAAC,IAAI,CAAC1C,WAAW,EAAE2C,KAAK,CAAC,CAACvC;gBAC/D0B,KAAI1B,KAAK,CAAC,CAAC,iCAAiC,EAAEA,OAAO;YACvD;QACF;QAEA,MAAMQ,SAAS,MAAM,IAAI,CAACC,eAAe,CAACjB;QAE1CkC,KAAIc,GAAG,CAAC1B,IAAAA,gBAAK,CAAA,CAAC,yBAAyB,EAAEpB,KAAK,eAAe,EAAES,KAAK,OAAO,CAAC;QAE5E,qEAAqE;QACrE,MAAMqB,WAAWnB,QAAQG;QAEzB,MAAMrB,SAAS,IAAI0C,iBAAiBL,UAAUhB,OAAOiC,SAAS;QAC9D,2BAA2B;QAC3BtD,OAAOuD,MAAM,CAAChD,MAAMI,QAAG,CAACC,QAAQ,EAAE,SAA6BC,KAAK;YAClE,IAAIA,OAAO;gBACT0B,KAAI1B,KAAK,CAACA,MAAMC,OAAO;YACzB;QACF;QAEA,qEAAqE;QACrE,MAAM0C,gBAAgBxD,OAAOyD,KAAK,CAACC,IAAI,CAAC1D;QAExCA,OAAOyD,KAAK,GAAG,CAACE;YACd,OAAOH,cAAc,CAACI;gBACpB,IAAI,CAAC7D,QAAQ,GAAG;gBAChB4D,4BAAAA,SAAWC;YACb;QACF;QAEA,MAAMC,QAAQC,IAAAA,gBAAY;QAC1B,MAAMC,WAAWjB,QAAQ,UAAU;QAEnC,OAAO;YACL,kBAAkB;YAClB9C;YACA,WAAW;YACXiD,UAAU;gBACRe,KAAK,GAAGD,SAAS,GAAG,EAAEF,MAAM,CAAC,EAAEtD,MAAM;gBACrCA;gBACAwD;gBACArD,MAAMmD;YACR;YACAI,YAAY;YACZ,6BAA6B;YAC7BC,eAAe;gBACbC,WAAW,IAAI,CAACvE,gBAAgB;YAClC;QACF;IACF;IAEA,kDAAkD,GAClDwE,2BAA0C;QACxC,4DAA4D;QAC5D,OACE,IAAI,CAACC,kBAAkB,GAAGC,MAAM,CAC9B,CAACC,MAAMC,WAAaD,QAAQE,sBAAW,CAACC,MAAM,CAAC,IAAI,CAACjE,WAAW,EAAE+D,WACjE,SACG;IAET;IAEA,MAAMlD,gBACJjB,OAA8E,EAC9EsE,IAAe,EACgB;QAC/B,sCAAsC;QAEtC,MAAMhE,MAAM;YACVF,aAAa,IAAI,CAACA,WAAW;YAC7BmE,KAAK,CAAC,CAACvE,QAAQkB,qBAAqB;YACpC,8CAA8C;YAC9CsD,QAAQ;gBACNC,SAAQ;YACV;YACA9D,MAAMX,QAAQW,IAAI;YAClB8B,OAAOzC,QAAQyC,KAAK;QACtB;QACAiC,IAAAA,mBAAU,EAACpE,IAAIK,IAAI,IAAI;QACvB9B,QAAQ,aAAa8F,IAAI,CAACrE,IAAIF,WAAW;QACzC,4DAA4D;QAC5D,MAAMwE,uBAAuB,IAAI,CAACb,wBAAwB;QAC1D,IAAI/C;QACJ,IAAI4D,sBAAsB;YACxB,MAAMC,gBAAgBhG,QAAQ+F;YAC9B,IAAI,OAAOC,kBAAkB,YAAY;gBACvC7D,SAAS,MAAM6D,cAAcvE,KAAKgE;YACpC,OAAO;gBACLtD,SAAS6D;YACX;QACF,OAAO;YACL,+CAA+C;YAC/C,MAAMC,yBAAyBC,IAAAA,sDAAkC,EAAC,IAAI,CAAC3E,WAAW;YAClFY,SAAS,MAAM8D,uBAAuBxE,KAAKgE;QAC7C;QACA,OAAOtD;IACT;IAEUgD,qBAA+B;QACvC,OAAO;YAAC;SAAsB;IAChC;IAEA,MAAgBjD,0BACdX,WAAmB,EACnBO,OAAe,aAAa,EACb;QACfuB,KAAIc,GAAG,CAAC1B,gBAAK,CAAC0D,GAAG,CAAC,CAAC,iBAAiB,EAAErE,KAAK,mBAAmB,CAAC;QAE/D,MAAMsE,MAAM,MAAMC,IAAAA,iDAAwC,EAAC9E;QAC3D,MAAM+E,cAAcC,mBAAI,CAACC,IAAI,CAACJ,KAAK,aAAatE;QAChD,IAAI;YACF,MAAM2E,iBAAE,CAACC,QAAQ,CAACC,EAAE,CAACL,aAAa;gBAAEM,WAAW;gBAAMC,OAAO;YAAK;QACnE,EAAE,OAAOlF,OAAY;YACnB0B,KAAI1B,KAAK,CAAC,CAAC,gBAAgB,EAAEG,KAAK,sBAAsB,EAAEH,MAAMC,OAAO,EAAE;QAC3E;IACF;AACF;AAEO,SAAS9B,gCAAgCyB,WAAmB;IACjE,OAAOgE,sBAAW,CAACC,MAAM,CAACjE,aAAa;AACzC"}