{"version": 3, "file": "sharp.js", "sourceRoot": "", "sources": ["../src/sharp.ts"], "names": [], "mappings": ";;;;;AAWA,8CAiBC;AAMD,4CASC;AAED,gCAwBC;AA0ED,wDA0DC;AAzMD,oEAA2C;AAC3C,gDAAwB;AACxB,gEAAuC;AACvC,oDAA4B;AAE5B,+BAA4B;AAG5B,MAAM,kBAAkB,GAAG,2CAA2C,CAAC;AACvE,MAAM,sBAAsB,GAAG,QAAQ,CAAC;AAEjC,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IACrE,MAAM,KAAK,GAAG,MAAM,sBAAsB,EAAE,CAAC;IAE7C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChD,8BAA8B;IAC9B,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CACtC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QACtB,MAAM,OAAO,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;QAC3F,OAAO,KAAK,CAAC,MAAM,EAAE;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;SACzD,CAAC;aACC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;aAC3E,QAAQ,EAAE,CAAC;IAChB,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,gBAAgB;IACpC,IAAI,SAAG,CAAC,yBAAyB,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,CAAC;QACH,OAAO,CAAC,CAAC,CAAC,MAAM,iBAAiB,EAAE,CAAC,CAAC;IACvC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,UAAU,CAC9B,OAA2B,EAC3B,WAAkC,EAAE;IAEpC,MAAM,GAAG,GAAG,MAAM,iBAAiB,EAAE,CAAC;IACtC,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,GAAG,EAAE;YACvC,GAAG,UAAU,CAAC,OAAO,CAAC;YACtB,GAAG,iBAAiB,CAAC,QAAQ,CAAC;SAC/B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,eAAe,CAAC;IACzB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACb,8CAA8C;gBAC5C,KAAK,CAAC,OAAO;gBACb,YAAY;gBACZ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAC/C,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,UAAU,CAAC,OAAgB;IAClC,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACnD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACrC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;YACxB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,QAA+B;IACxD,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAI,SAAS,GAAkB,IAAI,CAAC;AACpC,IAAI,cAAc,GAAe,IAAI,CAAC;AAEtC,KAAK,UAAU,iBAAiB;IAC9B,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtD,IACE,eAAe;YACf,gBAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,sBAAsB,CAAC;YACjE,OAAO,eAAe,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ;YAC7C,OAAO,cAAc,KAAK,QAAQ,EAClC,CAAC;YACD,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;YACtE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAAC,MAAM,CAAC;QACP,gCAAgC;IAClC,CAAC;IAED,IAAI,mBAAmB,CAAC;IACxB,IAAI,CAAC;QACH,mBAAmB,GAAG,CAAC,MAAM,IAAA,qBAAU,EAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IAC5F,CAAC;IAAC,MAAM,CAAC;QACP,MAAM,aAAa,CAAC,sBAAsB,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,EAAE,CAAC;QACnE,0BAA0B,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;IAC1E,CAAC;IACD,SAAS,GAAG,OAAO,CAAC;IACpB,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB;IAC1C,IAAI,SAAG,CAAC,yBAAyB,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CACb,gJAAgJ,CACjJ,CAAC;IACJ,CAAC;IACD,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,sCAAsC;IACtC,MAAM,iBAAiB,EAAE,CAAC;IAE1B,qCAAqC;IACrC,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,cAAc,GAAG,KAAK,CAAC;QACvB,OAAO,KAAK,CAAC;IACf,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IAEV,+DAA+D;IAC/D,IAAI,YAAY,CAAC;IACjB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC;YACH,YAAY,GAAG,CAAC,MAAM,IAAA,qBAAU,EAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACjF,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;SAAM,CAAC;QACN,qGAAqG;QACrG,2FAA2F;QAC3F,oDAAoD;QACpD,IAAI,cAAc,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC;YACH,cAAc,GAAG,cAAI,CAAC,IAAI,CACxB,CAAC,MAAM,IAAA,qBAAU,EAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EACtE,cAAc,CACf,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QACV,IAAI,CAAC;YACH,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,wBAAwB,EAAE;gBACvD,KAAK,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;aACzE,CAAC,CAAC;QACL,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,2CAA2C;IAC3C,MAAM,SAAS,GAAG,sBAAW,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IAElE,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,CAAC;YACH,8CAA8C;YAC9C,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;IAChG,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,aAAa,CAAC,kBAA0B;IAC/C,OAAO,IAAI,KAAK,CACd,iCAAiC,kBAAkB,uBAAuB;QACxE,uDAAuD,kBAAkB,QAAQ;QACjF,IAAI;QACJ,kFAAkF,CACrF,CAAC;AACJ,CAAC;AAED,IAAI,2BAA2B,GAAG,KAAK,CAAC;AAExC,SAAS,0BAA0B,CAAC,kBAA0B,EAAE,mBAA2B;IACzF,IAAI,2BAA2B,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IACD,OAAO,CAAC,IAAI,CACV,0CAA0C,kBAAkB,uBAAuB;QACjF,iCAAiC,mBAAmB,MAAM;QAC1D,sBAAsB,kBAAkB,MAAM;QAC9C,uDAAuD,kBAAkB,QAAQ;QACjF,IAAI;QACJ,kFAAkF,CACrF,CAAC;IACF,2BAA2B,GAAG,IAAI,CAAC;AACrC,CAAC"}