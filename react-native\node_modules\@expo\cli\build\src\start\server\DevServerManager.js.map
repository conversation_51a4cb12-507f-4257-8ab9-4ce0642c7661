{"version": 3, "sources": ["../../../../src/start/server/DevServerManager.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\n\nimport { BundlerDevServer, BundlerStartOptions } from './BundlerDevServer';\nimport DevToolsPluginManager from './DevToolsPluginManager';\nimport { getPlatformBundlers } from './platformBundlers';\nimport { Log } from '../../log';\nimport { FileNotifier } from '../../utils/FileNotifier';\nimport { env } from '../../utils/env';\nimport { ProjectPrerequisite } from '../doctor/Prerequisite';\nimport { TypeScriptProjectPrerequisite } from '../doctor/typescript/TypeScriptProjectPrerequisite';\nimport { printItem } from '../interface/commandsTable';\nimport * as AndroidDebugBridge from '../platforms/android/adb';\nimport { resolveSchemeAsync } from '../resolveOptions';\n\nconst debug = require('debug')('expo:start:server:devServerManager') as typeof console.log;\n\nexport type MultiBundlerStartOptions = {\n  type: keyof typeof BUNDLERS;\n  options?: BundlerStartOptions;\n}[];\n\nconst BUNDLERS = {\n  webpack: () =>\n    require('./webpack/WebpackBundlerDevServer')\n      .WebpackBundlerDevServer as typeof import('./webpack/WebpackBundlerDevServer').WebpackBundlerDevServer,\n  metro: () =>\n    require('./metro/MetroBundlerDevServer')\n      .MetroBundlerDevServer as typeof import('./metro/MetroBundlerDevServer').MetroBundlerDevServer,\n};\n\n/** Manages interacting with multiple dev servers. */\nexport class DevServerManager {\n  private devServers: BundlerDevServer[] = [];\n\n  static async startMetroAsync(projectRoot: string, startOptions: BundlerStartOptions) {\n    const devServerManager = new DevServerManager(projectRoot, startOptions);\n\n    await devServerManager.startAsync([\n      {\n        type: 'metro',\n        options: startOptions,\n      },\n    ]);\n    return devServerManager;\n  }\n\n  private projectPrerequisites: ProjectPrerequisite<any, void>[] = [];\n  public readonly devtoolsPluginManager: DevToolsPluginManager;\n\n  private notifier: FileNotifier | null = null;\n\n  constructor(\n    public projectRoot: string,\n    /** Keep track of the original CLI options for bundlers that are started interactively. */\n    public options: BundlerStartOptions\n  ) {\n    if (!options.isExporting) {\n      this.notifier = this.watchBabelConfig();\n    }\n    this.devtoolsPluginManager = new DevToolsPluginManager(projectRoot);\n  }\n\n  private watchBabelConfig() {\n    const notifier = new FileNotifier(\n      this.projectRoot,\n      [\n        './babel.config.js',\n        './babel.config.json',\n        './.babelrc.json',\n        './.babelrc',\n        './.babelrc.js',\n      ],\n      {\n        additionalWarning: chalk` You may need to clear the bundler cache with the {bold --clear} flag for your changes to take effect.`,\n      }\n    );\n\n    notifier.startObserving();\n\n    return notifier;\n  }\n\n  /** Lazily load and assert a project-level prerequisite. */\n  async ensureProjectPrerequisiteAsync(PrerequisiteClass: typeof ProjectPrerequisite<any, any>) {\n    let prerequisite = this.projectPrerequisites.find(\n      (prerequisite) => prerequisite instanceof PrerequisiteClass\n    );\n    if (!prerequisite) {\n      prerequisite = new PrerequisiteClass(this.projectRoot);\n      this.projectPrerequisites.push(prerequisite);\n    }\n    return await prerequisite.assertAsync();\n  }\n\n  /**\n   * Sends a message over web sockets to all connected devices,\n   * does nothing when the dev server is not running.\n   *\n   * @param method name of the command. In RN projects `reload`, and `devMenu` are available. In Expo Go, `sendDevCommand` is available.\n   * @param params extra event info to send over the socket.\n   */\n  broadcastMessage(method: 'reload' | 'devMenu' | 'sendDevCommand', params?: Record<string, any>) {\n    this.devServers.forEach((server) => {\n      server.broadcastMessage(method, params);\n    });\n  }\n\n  /** Get the port for the dev server (either Webpack or Metro) that is hosting code for React Native runtimes. */\n  getNativeDevServerPort() {\n    const server = this.devServers.find((server) => server.isTargetingNative());\n    return server?.getInstance()?.location.port ?? null;\n  }\n\n  /** Get the first server that targets web. */\n  getWebDevServer() {\n    const server = this.devServers.find((server) => server.isTargetingWeb());\n    return server ?? null;\n  }\n\n  getDefaultDevServer(): BundlerDevServer {\n    // Return the first native dev server otherwise return the first dev server.\n    const server = this.devServers.find((server) => server.isTargetingNative());\n    const defaultServer = server ?? this.devServers[0];\n    assert(defaultServer, 'No dev servers are running');\n    return defaultServer;\n  }\n\n  async ensureWebDevServerRunningAsync() {\n    const [server] = this.devServers.filter((server) => server.isTargetingWeb());\n    if (server) {\n      return;\n    }\n    const { exp } = getConfig(this.projectRoot, {\n      skipPlugins: true,\n      skipSDKVersionRequirement: true,\n    });\n    const bundler = getPlatformBundlers(this.projectRoot, exp).web;\n    debug(`Starting ${bundler} dev server for web`);\n    return this.startAsync([\n      {\n        type: bundler,\n        options: this.options,\n      },\n    ]);\n  }\n\n  /** Switch between Expo Go and Expo Dev Clients. */\n  async toggleRuntimeMode(isUsingDevClient: boolean = !this.options.devClient): Promise<boolean> {\n    const nextMode = isUsingDevClient ? '--dev-client' : '--go';\n    Log.log(printItem(chalk`Switching to {bold ${nextMode}}`));\n\n    const nextScheme = await resolveSchemeAsync(this.projectRoot, {\n      devClient: isUsingDevClient,\n      // NOTE: The custom `--scheme` argument is lost from this point on.\n    });\n\n    this.options.location.scheme = nextScheme;\n    this.options.devClient = isUsingDevClient;\n    for (const devServer of this.devServers) {\n      devServer.isDevClient = isUsingDevClient;\n      const urlCreator = devServer.getUrlCreator();\n      urlCreator.defaults ??= {};\n      urlCreator.defaults.scheme = nextScheme;\n    }\n\n    debug(`New runtime options (runtime: ${nextMode}):`, this.options);\n    return true;\n  }\n\n  /** Start all dev servers. */\n  async startAsync(startOptions: MultiBundlerStartOptions): Promise<ExpoConfig> {\n    const { exp } = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n    const platformBundlers = getPlatformBundlers(this.projectRoot, exp);\n\n    // Start all dev servers...\n    for (const { type, options } of startOptions) {\n      const BundlerDevServerClass = await BUNDLERS[type]();\n      const server = new BundlerDevServerClass(this.projectRoot, platformBundlers, {\n        devToolsPluginManager: this.devtoolsPluginManager,\n        isDevClient: !!options?.devClient,\n      });\n      await server.startAsync(options ?? this.options);\n      this.devServers.push(server);\n    }\n\n    return exp;\n  }\n\n  async bootstrapTypeScriptAsync() {\n    const typescriptPrerequisite = await this.ensureProjectPrerequisiteAsync(\n      TypeScriptProjectPrerequisite\n    );\n\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      return;\n    }\n\n    // Optionally, wait for the user to add TypeScript during the\n    // development cycle.\n    const server = this.devServers.find((server) => server.name === 'metro');\n    if (!server) {\n      return;\n    }\n\n    // The dev server shouldn't wait for the typescript services\n    if (!typescriptPrerequisite) {\n      server.waitForTypeScriptAsync().then(async (success) => {\n        if (success) {\n          server.startTypeScriptServices();\n        }\n      });\n    } else {\n      server.startTypeScriptServices();\n    }\n  }\n\n  async watchEnvironmentVariables() {\n    await this.devServers.find((server) => server.name === 'metro')?.watchEnvironmentVariables();\n  }\n\n  /** Stop all servers including ADB. */\n  async stopAsync(): Promise<void> {\n    await Promise.allSettled([\n      this.notifier?.stopObserving(),\n      // Stop ADB\n      AndroidDebugBridge.getServer().stopAsync(),\n      // Stop all dev servers\n      ...this.devServers.map((server) =>\n        server.stopAsync().catch((error) => {\n          Log.error(`Failed to stop dev server (bundler: ${server.name})`);\n          Log.exception(error);\n        })\n      ),\n    ]);\n  }\n}\n"], "names": ["DevServerManager", "debug", "require", "BUNDLERS", "webpack", "WebpackBundlerDevServer", "metro", "MetroBundlerDevServer", "startMetroAsync", "projectRoot", "startOptions", "devServerManager", "startAsync", "type", "options", "constructor", "devServers", "projectPrerequisites", "notifier", "isExporting", "watchBabelConfig", "devtoolsPluginManager", "DevToolsPluginManager", "FileNotifier", "additionalWarning", "chalk", "startObserving", "ensureProjectPrerequisiteAsync", "PrerequisiteClass", "prerequisite", "find", "push", "assertAsync", "broadcastMessage", "method", "params", "for<PERSON>ach", "server", "getNativeDevServerPort", "isTargetingNative", "getInstance", "location", "port", "getWebDevServer", "isTargetingWeb", "getDefaultDevServer", "defaultServer", "assert", "ensureWebDevServerRunningAsync", "filter", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "bundler", "getPlatformBundlers", "web", "toggleRuntimeMode", "isUsingDevClient", "devClient", "nextMode", "Log", "log", "printItem", "nextScheme", "resolveSchemeAsync", "scheme", "devServer", "isDevClient", "urlCreator", "getUrlCreator", "defaults", "platformBundlers", "BundlerDevServerClass", "devToolsPluginManager", "bootstrapTypeScriptAsync", "typescriptPrerequisite", "TypeScriptProjectPrerequisite", "env", "EXPO_NO_TYPESCRIPT_SETUP", "name", "waitForTypeScriptAsync", "then", "success", "startTypeScriptServices", "watchEnvironmentVariables", "stopAsync", "Promise", "allSettled", "stopObserving", "AndroidDebugBridge", "getServer", "map", "catch", "error", "exception"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;;yBAjCyB;;;;;;;gEACnB;;;;;;;gEACD;;;;;;8EAGgB;kCACE;qBAChB;8BACS;qBACT;+CAE0B;+BACpB;6DACU;gCACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,MAAMC,QAAQC,QAAQ,SAAS;AAO/B,MAAMC,WAAW;IACfC,SAAS,IACPF,QAAQ,qCACLG,uBAAuB;IAC5BC,OAAO,IACLJ,QAAQ,iCACLK,qBAAqB;AAC5B;AAGO,MAAMP;IAGX,aAAaQ,gBAAgBC,WAAmB,EAAEC,YAAiC,EAAE;QACnF,MAAMC,mBAAmB,IAAIX,iBAAiBS,aAAaC;QAE3D,MAAMC,iBAAiBC,UAAU,CAAC;YAChC;gBACEC,MAAM;gBACNC,SAASJ;YACX;SACD;QACD,OAAOC;IACT;IAOAI,YACE,AAAON,WAAmB,EAC1B,wFAAwF,GACxF,AAAOK,OAA4B,CACnC;aAHOL,cAAAA;aAEAK,UAAAA;aAtBDE,aAAiC,EAAE;aAcnCC,uBAAyD,EAAE;aAG3DC,WAAgC;QAOtC,IAAI,CAACJ,QAAQK,WAAW,EAAE;YACxB,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACE,gBAAgB;QACvC;QACA,IAAI,CAACC,qBAAqB,GAAG,IAAIC,8BAAqB,CAACb;IACzD;IAEQW,mBAAmB;QACzB,MAAMF,WAAW,IAAIK,0BAAY,CAC/B,IAAI,CAACd,WAAW,EAChB;YACE;YACA;YACA;YACA;YACA;SACD,EACD;YACEe,mBAAmBC,IAAAA,gBAAK,CAAA,CAAC,sGAAsG,CAAC;QAClI;QAGFP,SAASQ,cAAc;QAEvB,OAAOR;IACT;IAEA,yDAAyD,GACzD,MAAMS,+BAA+BC,iBAAuD,EAAE;QAC5F,IAAIC,eAAe,IAAI,CAACZ,oBAAoB,CAACa,IAAI,CAC/C,CAACD,eAAiBA,wBAAwBD;QAE5C,IAAI,CAACC,cAAc;YACjBA,eAAe,IAAID,kBAAkB,IAAI,CAACnB,WAAW;YACrD,IAAI,CAACQ,oBAAoB,CAACc,IAAI,CAACF;QACjC;QACA,OAAO,MAAMA,aAAaG,WAAW;IACvC;IAEA;;;;;;GAMC,GACDC,iBAAiBC,MAA+C,EAAEC,MAA4B,EAAE;QAC9F,IAAI,CAACnB,UAAU,CAACoB,OAAO,CAAC,CAACC;YACvBA,OAAOJ,gBAAgB,CAACC,QAAQC;QAClC;IACF;IAEA,8GAA8G,GAC9GG,yBAAyB;YAEhBD;QADP,MAAMA,SAAS,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,SAAWA,OAAOE,iBAAiB;QACxE,OAAOF,CAAAA,2BAAAA,sBAAAA,OAAQG,WAAW,uBAAnBH,oBAAuBI,QAAQ,CAACC,IAAI,KAAI;IACjD;IAEA,2CAA2C,GAC3CC,kBAAkB;QAChB,MAAMN,SAAS,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,SAAWA,OAAOO,cAAc;QACrE,OAAOP,UAAU;IACnB;IAEAQ,sBAAwC;QACtC,4EAA4E;QAC5E,MAAMR,SAAS,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,SAAWA,OAAOE,iBAAiB;QACxE,MAAMO,gBAAgBT,UAAU,IAAI,CAACrB,UAAU,CAAC,EAAE;QAClD+B,IAAAA,iBAAM,EAACD,eAAe;QACtB,OAAOA;IACT;IAEA,MAAME,iCAAiC;QACrC,MAAM,CAACX,OAAO,GAAG,IAAI,CAACrB,UAAU,CAACiC,MAAM,CAAC,CAACZ,SAAWA,OAAOO,cAAc;QACzE,IAAIP,QAAQ;YACV;QACF;QACA,MAAM,EAAEa,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAAC1C,WAAW,EAAE;YAC1C2C,aAAa;YACbC,2BAA2B;QAC7B;QACA,MAAMC,UAAUC,IAAAA,qCAAmB,EAAC,IAAI,CAAC9C,WAAW,EAAEyC,KAAKM,GAAG;QAC9DvD,MAAM,CAAC,SAAS,EAAEqD,QAAQ,mBAAmB,CAAC;QAC9C,OAAO,IAAI,CAAC1C,UAAU,CAAC;YACrB;gBACEC,MAAMyC;gBACNxC,SAAS,IAAI,CAACA,OAAO;YACvB;SACD;IACH;IAEA,iDAAiD,GACjD,MAAM2C,kBAAkBC,mBAA4B,CAAC,IAAI,CAAC5C,OAAO,CAAC6C,SAAS,EAAoB;QAC7F,MAAMC,WAAWF,mBAAmB,iBAAiB;QACrDG,QAAG,CAACC,GAAG,CAACC,IAAAA,wBAAS,EAACtC,IAAAA,gBAAK,CAAA,CAAC,mBAAmB,EAAEmC,SAAS,CAAC,CAAC;QAExD,MAAMI,aAAa,MAAMC,IAAAA,kCAAkB,EAAC,IAAI,CAACxD,WAAW,EAAE;YAC5DkD,WAAWD;QAEb;QAEA,IAAI,CAAC5C,OAAO,CAAC2B,QAAQ,CAACyB,MAAM,GAAGF;QAC/B,IAAI,CAAClD,OAAO,CAAC6C,SAAS,GAAGD;QACzB,KAAK,MAAMS,aAAa,IAAI,CAACnD,UAAU,CAAE;YACvCmD,UAAUC,WAAW,GAAGV;YACxB,MAAMW,aAAaF,UAAUG,aAAa;YAC1CD,WAAWE,QAAQ,KAAK,CAAC;YACzBF,WAAWE,QAAQ,CAACL,MAAM,GAAGF;QAC/B;QAEA/D,MAAM,CAAC,8BAA8B,EAAE2D,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC9C,OAAO;QACjE,OAAO;IACT;IAEA,2BAA2B,GAC3B,MAAMF,WAAWF,YAAsC,EAAuB;QAC5E,MAAM,EAAEwC,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAC,IAAI,CAAC1C,WAAW,EAAE;YAAE4C,2BAA2B;QAAK;QAC9E,MAAMmB,mBAAmBjB,IAAAA,qCAAmB,EAAC,IAAI,CAAC9C,WAAW,EAAEyC;QAE/D,2BAA2B;QAC3B,KAAK,MAAM,EAAErC,IAAI,EAAEC,OAAO,EAAE,IAAIJ,aAAc;YAC5C,MAAM+D,wBAAwB,MAAMtE,QAAQ,CAACU,KAAK;YAClD,MAAMwB,SAAS,IAAIoC,sBAAsB,IAAI,CAAChE,WAAW,EAAE+D,kBAAkB;gBAC3EE,uBAAuB,IAAI,CAACrD,qBAAqB;gBACjD+C,aAAa,CAAC,EAACtD,2BAAAA,QAAS6C,SAAS;YACnC;YACA,MAAMtB,OAAOzB,UAAU,CAACE,WAAW,IAAI,CAACA,OAAO;YAC/C,IAAI,CAACE,UAAU,CAACe,IAAI,CAACM;QACvB;QAEA,OAAOa;IACT;IAEA,MAAMyB,2BAA2B;QAC/B,MAAMC,yBAAyB,MAAM,IAAI,CAACjD,8BAA8B,CACtEkD,4DAA6B;QAG/B,IAAIC,QAAG,CAACC,wBAAwB,EAAE;YAChC;QACF;QAEA,6DAA6D;QAC7D,qBAAqB;QACrB,MAAM1C,SAAS,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,SAAWA,OAAO2C,IAAI,KAAK;QAChE,IAAI,CAAC3C,QAAQ;YACX;QACF;QAEA,4DAA4D;QAC5D,IAAI,CAACuC,wBAAwB;YAC3BvC,OAAO4C,sBAAsB,GAAGC,IAAI,CAAC,OAAOC;gBAC1C,IAAIA,SAAS;oBACX9C,OAAO+C,uBAAuB;gBAChC;YACF;QACF,OAAO;YACL/C,OAAO+C,uBAAuB;QAChC;IACF;IAEA,MAAMC,4BAA4B;YAC1B;QAAN,QAAM,wBAAA,IAAI,CAACrE,UAAU,CAACc,IAAI,CAAC,CAACO,SAAWA,OAAO2C,IAAI,KAAK,6BAAjD,sBAA2DK,yBAAyB;IAC5F;IAEA,oCAAoC,GACpC,MAAMC,YAA2B;YAE7B;QADF,MAAMC,QAAQC,UAAU,CAAC;aACvB,iBAAA,IAAI,CAACtE,QAAQ,qBAAb,eAAeuE,aAAa;YAC5B,WAAW;YACXC,KAAmBC,SAAS,GAAGL,SAAS;YACxC,uBAAuB;eACpB,IAAI,CAACtE,UAAU,CAAC4E,GAAG,CAAC,CAACvD,SACtBA,OAAOiD,SAAS,GAAGO,KAAK,CAAC,CAACC;oBACxBjC,QAAG,CAACiC,KAAK,CAAC,CAAC,oCAAoC,EAAEzD,OAAO2C,IAAI,CAAC,CAAC,CAAC;oBAC/DnB,QAAG,CAACkC,SAAS,CAACD;gBAChB;SAEH;IACH;AACF"}