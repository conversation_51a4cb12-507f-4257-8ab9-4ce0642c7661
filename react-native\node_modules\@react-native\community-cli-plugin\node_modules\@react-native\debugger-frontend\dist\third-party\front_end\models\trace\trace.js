import*as e from"../../core/platform/platform.js";import*as t from"./helpers/helpers.js";export{t as Helpers};import*as s from"./types/types.js";export{s as Types};import*as n from"./extras/extras.js";export{n as Extras};import*as r from"./handlers/handlers.js";export{r as Handlers};import*as i from"./insights/insights.js";export{i as Insights};import*as a from"../../core/common/common.js";import*as o from"./root-causes/root-causes.js";export{o as RootCauses};import*as d from"../../core/sdk/sdk.js";var c=Object.freeze({__proto__:null,EntriesFilter:class{#e;#t=[];#s=[];#n=new Map;constructor(e){this.#e=e}findPossibleActions(e){const t=this.#e.get(e);if(!t)return{MERGE_FUNCTION:!1,COLLAPSE_FUNCTION:!1,COLLAPSE_REPEATING_DESCENDANTS:!1,RESET_CHILDREN:!1,UNDO_ALL_ACTIONS:!1};const s=t.parent,n=this.#r(t).filter((e=>!this.#t.includes(e))),r=this.#i(t).filter((e=>!this.#t.includes(e))),i=this.#r(t).filter((e=>this.#t.includes(e)));return{MERGE_FUNCTION:null!==s,COLLAPSE_FUNCTION:n.length>0,COLLAPSE_REPEATING_DESCENDANTS:r.length>0,RESET_CHILDREN:i.length>0,UNDO_ALL_ACTIONS:this.#t.length>0}}findHiddenDescendantsAmount(e){const t=this.#e.get(e);if(!t)return 0;return this.#r(t).filter((e=>this.invisibleEntries().includes(e))).length}invisibleEntries(){return this.#t}modifiedEntries(){return this.#s}applyFilterAction(t){const s=new Set;switch(t.type){case"MERGE_FUNCTION":{s.add(t.entry);const e=this.#e.get(t.entry)||null,n=e&&this.#a(e);n&&this.#o(n.entry);break}case"COLLAPSE_FUNCTION":{const e=this.#e.get(t.entry);if(!e)break;this.#r(e).forEach((e=>s.add(e))),this.#o(t.entry);break}case"COLLAPSE_REPEATING_DESCENDANTS":{const e=this.#e.get(t.entry);if(!e)break;this.#i(e).forEach((e=>s.add(e))),s.size>0&&this.#o(t.entry);break}case"UNDO_ALL_ACTIONS":this.#t=[],this.#s=[];break;case"RESET_CHILDREN":this.#d(t.entry);break;default:e.assertNever(t.type,`Unknown EntriesFilter action: ${t.type}`)}return this.#t.push(...s),this.#t}#o(e){this.#s.push(e);const t=this.#e.get(e);if(!t)return;const s=this.#r(t);s.length>0&&(this.#s=this.#s.filter((e=>!s.includes(e))))}#a(e){let t=e.parent;for(;t&&this.#t.includes(t.entry);)t=t.parent;return t}#r(e){const t=this.#n.get(e);if(t)return t;const s=[],n=[...e.children];for(;n.length>0;){const e=n.shift();if(e){s.push(e.entry);const t=this.#n.get(e);t?s.push(...t):n.push(...e.children)}}return this.#n.set(e,s),s}#i(e){const n=[...e.children],r=[],i=s.TraceEvents.isProfileCall(e.entry);for(;n.length>0;){const a=n.shift();if(a){const o=s.TraceEvents.isProfileCall(a.entry);if(i&&o){const s=e.entry,n=a.entry;t.SamplesIntegrator.SamplesIntegrator.framesAreEqual(s.callFrame,n.callFrame)&&r.push(a.entry)}else i||o||e.entry.name===a.entry.name&&r.push(a.entry);n.push(...a.children)}}return r}#d(e){const t=this.#e.get(e);if(!t)return;const s=this.#r(t);this.#t=this.#t.filter((e=>!s.includes(e))),this.#s=this.#s.filter((t=>!s.includes(t)&&t!==e))}isEntryModified(e){return this.#s.includes(e)}}});class l{#c;#l;#h;#u;#m;#p;#f;#g;#v;#E;#y;#T=[];constructor(e){this.#c=e,this.#l=new Map,this.#h=new Map,this.#u=Number(1/0),this.#m=Number(-1/0),this.#p=[],this.#f=[],this.#g=new Map,this.#v=new Map,this.#E=new Map,this.#y=new Map}static isTopLevelEvent(e){return A(e,f)&&"RunTask"===e.name||A(e,m)||A(e,p)&&"Program"===e.name}static extractId(e){const t=e.scope||"";if(void 0===e.id2)return t&&e.id?`${t}@${e.id}`:e.id;const s=e.id2;if("object"==typeof s&&"global"in s!="local"in s)return void 0!==s.global?`:${t}:${s.global}`:`:${t}:${e.pid}:${s.local}`;console.error(`Unexpected id2 field at ${e.ts/1e3}, one and only one of 'local' and 'global' should be present.`)}static browserMainThread(e){const t=e.sortedProcesses();if(!t.length)return null;const s="CrBrowserMain",n=[],r=[];for(const e of t)e.name().toLowerCase().endsWith("browser")&&n.push(e),r.push(...e.sortedThreads().filter((e=>e.name()===s)));if(1===r.length)return r[0];if(1===n.length)return n[0].threadByName(s);const i=e.devToolsMetadataEvents().filter((e=>"TracingStartedInBrowser"===e.name));return 1===i.length?i[0].thread:(a.Console.Console.instance().error("Failed to find browser main thread in trace, some timeline features may be unavailable"),null)}allRawEvents(){return this.#T}devToolsMetadataEvents(){return this.#p}addEvents(e){for(let t=0;t<e.length;++t)this.addEvent(e[t])}tracingComplete(){this.processPendingAsyncEvents();for(const e of this.#l.values())for(const t of e.threads.values())t.tracingComplete()}addEvent(e){this.#T.push(e);let t=this.#l.get(e.pid);t||(t=new C(this,e.pid),this.#l.set(e.pid,t));const n=e.ts/1e3;if(n&&n<this.#u&&h.has(e.ph)&&!e.name.endsWith("::UMA")&&(this.#u=n),"TracingStartedInBrowser"===e.name&&(this.#u=n),h.has(e.ph)){const t=(e.ts+(e.dur||0))/1e3;this.#m=Math.max(this.#m,t)}const r=t.addEvent(e);if(r)if("P"!==e.ph){if(s.TraceEvents.isAsyncPhase(e.ph)&&this.#f.push(r),r.hasCategory(p)&&this.#p.push(r),"M"===e.ph)switch(e.name){case u.ProcessSortIndex:t.setSortIndex(e.args.sort_index);break;case u.ProcessName:{const s=e.args.name;t.setName(s),this.#h.set(s,t);break}case u.ThreadSortIndex:t.threadById(e.tid).setSortIndex(e.args.sort_index);break;case u.ThreadName:t.threadById(e.tid).setName(e.args.name)}}else this.addSampleEvent(r)}addSampleEvent(e){const t=`${e.thread.process().id()}:${e.id}`,s=this.#E.get(t);s?s.addChild(e):this.#E.set(t,new I(e))}profileGroup(e){return this.#E.get(`${e.thread.process().id()}:${e.id}`)||null}minimumRecordTime(){return this.#u}maximumRecordTime(){return this.#m}sortedProcesses(){return b.sort([...this.#l.values()])}getProcessByName(e){return this.#h.get(e)??null}getProcessById(e){return this.#l.get(e)||null}getThreadByName(e,t){const s=this.getProcessByName(e);return s&&s.threadByName(t)}processPendingAsyncEvents(){this.#f.sort(g.compareStartTime);for(let e=0;e<this.#f.length;++e){const t=this.#f[e];s.TraceEvents.isNestableAsyncPhase(t.phase)?this.addNestableAsyncEvent(t):this.addAsyncEvent(t)}this.#f=[],this.closeOpenAsyncEvents()}closeOpenAsyncEvents(){for(const e of this.#g.values())e.setEndTime(this.#m),e.steps[0].setEndTime(this.#m);this.#g.clear();for(const e of this.#v.values())for(;e.length;){const t=e.pop();t&&t.setEndTime(this.#m)}this.#v.clear()}addNestableAsyncEvent(e){const t=e.categoriesString+"."+e.id;let s=this.#v.get(t);switch(e.phase){case"b":{s||(s=[],this.#v.set(t,s));const n=new T(e);s.push(n),e.thread.addAsyncEvent(n);break}case"n":if(s&&s.length){const e=s[s.length-1];e&&e.addStep(e)}break;case"e":{if(!s||!s.length)break;const n=s.pop();if(!n)break;if(n.name!==e.name){console.error(`Begin/end event mismatch for nestable async event, ${n.name} vs. ${e.name}, key: ${t}`);break}n.addStep(e)}}}addAsyncEvent(e){const t=e.categoriesString+"."+e.name+"."+e.id;let s=this.#g.get(t);if("S"===e.phase)return s?void console.error(`Event ${e.name} has already been started`):(s=new T(e),this.#g.set(t,s),void e.thread.addAsyncEvent(s));if(s){if("F"===e.phase)return s.addStep(e),void this.#g.delete(t);if("T"===e.phase||"p"===e.phase){const t=s.steps[s.steps.length-1];return t&&"S"!==t.phase&&t.phase!==e.phase?void console.assert(!1,"Async event step phase mismatch: "+t.phase+" at "+t.startTime+" vs. "+e.phase+" at "+e.startTime):void s.addStep(e)}console.assert(!1,"Invalid async event phase")}}title(){return this.#c}parsedCategoriesForString(e){let t=this.#y.get(e);return t||(t=new Set(e?e.split(","):[]),this.#y.set(e,t)),t}}const h=new Set(["B","E","X","I"]),u={ProcessSortIndex:"process_sort_index",ProcessName:"process_name",ThreadSortIndex:"thread_sort_index",ThreadName:"thread_name"},m="toplevel",p="disabled-by-default-devtools.timeline",f="disabled-by-default-devtools.timeline";class g{categoriesString;#y;name;phase;startTime;thread;args;id;ordinal;selfTime;endTime;duration;constructor(e,t,s,n,r){this.categoriesString=e||"",this.#y=r.getModel().parsedCategoriesForString(this.categoriesString),this.name=t,this.phase=s,this.startTime=n,this.thread=r,this.args={},this.ordinal=0,this.selfTime=0}static compareStartTime(e,t){return e&&t?e.startTime-t.startTime:0}static orderedCompareStartTime(e,t){return e.startTime-t.startTime||e.ordinal-t.ordinal||-1}hasCategory(e){return this.#y.has(e)}setEndTime(e){e<this.startTime?console.assert(!1,"Event out of order: "+this.name):(this.endTime=e,this.duration=e-this.startTime)}addArgs(e){for(const t in e)t in this.args&&console.error("Same argument name ("+t+") is used for begin and end phases of "+this.name),this.args[t]=e[t]}complete(e){e.args?this.addArgs(e.args):console.error("Missing mandatory event argument 'args' at "+e.startTime),this.setEndTime(e.startTime)}}class v extends g{constructor(e,t,s,n,r){super(e,t,s,n,r)}}class E extends g{#I;rawLegacyPayload(){return this.#I}rawPayload(){return this.#I}constructor(e,t,s,n,r,i){super(e,t,s,n,r),this.#I=i}static fromPayload(e,t){const s=new E(e.cat,e.name,e.ph,e.ts/1e3,t,e);s.#I=e,e.args&&s.addArgs(e.args),"number"==typeof e.dur&&s.setEndTime((e.ts+e.dur)/1e3);const n=l.extractId(e);return void 0!==n&&(s.id=n),s}}class y extends E{constructor(e,t,s,n,r){super(e,t,"O",s,n,r)}static fromPayload(e,t){const s=new y(e.cat,e.name,e.ts/1e3,t,e),n=l.extractId(e);return void 0!==n&&(s.id=n),e.args&&e.args.snapshot?(e.args&&s.addArgs(e.args),s):(console.error("Missing mandatory 'snapshot' argument at "+e.ts/1e3),s)}getSnapshot(){const e=this.args.snapshot;if(!e)throw new Error("ObjectSnapshot has no snapshot argument.");return e}}class T extends v{steps;causedFrame;constructor(e){super(e.categoriesString,e.name,e.phase,e.startTime,e.thread),this.addArgs(e.args),this.steps=[e],this.causedFrame=!1}addStep(e){this.steps.push(e),"F"!==e.phase&&"e"!==e.phase||(this.setEndTime(e.startTime),this.steps[0].setEndTime(e.startTime))}}class I{children;constructor(e){this.children=[e]}addChild(e){this.children.push(e)}}class b{model;idInternal;#b;#C;constructor(e,t){this.model=e,this.idInternal=t,this.#b="",this.#C=0}static sort(e){return e.sort(((e,t)=>e.#C!==t.#C?e.#C-t.#C:e.name().localeCompare(t.name())))}setName(e){this.#b=e}name(){return this.#b}id(){return this.idInternal}setSortIndex(e){this.#C=e}getModel(){return this.model}}class C extends b{threads;#N;constructor(e,t){super(e,t),this.threads=new Map,this.#N=new Map}threadById(e){let t=this.threads.get(e);return t||(t=new N(this,e),this.threads.set(e,t)),t}threadByName(e){return this.#N.get(e)||null}setThreadByName(e,t){this.#N.set(e,t)}addEvent(e){return this.threadById(e.tid).addEvent(e)}sortedThreads(){return b.sort([...this.threads.values()])}}class N extends b{#S;#A;#P;#w;constructor(e,t){super(e.getModel(),t),this.#S=e,this.#A=[],this.#P=[],this.#w=null}#M(e,t){return e.phase===t}tracingComplete(){this.#P.sort(g.compareStartTime),this.#A.sort(g.compareStartTime);const e=[],t=new Set;for(let s=0;s<this.#A.length;++s){const n=this.#A[s];if(n.ordinal=s,this.#M(n,"E")){if(t.add(s),!e.length)continue;const r=e.pop();if(!r)continue;r.name!==n.name||r.categoriesString!==n.categoriesString?console.error("B/E events mismatch at "+r.startTime+" ("+r.name+") vs. "+n.startTime+" ("+n.name+")"):r.complete(n)}else this.#M(n,"B")&&e.push(n)}for(;e.length;){const t=e.pop();t&&(t.phase="I")}this.#A=this.#A.filter(((e,s)=>!t.has(s)))}addEvent(e){const t="O"===e.ph?y.fromPayload(e,this):E.fromPayload(e,this);if(l.isTopLevelEvent(t)){const e=this.#w;if(e&&(e.endTime||0)>t.startTime)return null;this.#w=t}return this.#A.push(t),t}addAsyncEvent(e){this.#P.push(e)}setName(e){super.setName(e),this.#S.setThreadByName(e,this)}process(){return this.#S}events(){return this.#A}asyncEvents(){return this.#P}removeEventsByName(e){const t=[];return this.#A=this.#A.filter((s=>!!s&&(s.name!==e||(t.push(s),!1)))),t}}const S=new Map;function A(e,t){if(e instanceof g)return e.hasCategory(t);let s=S.get(e.cat);return s||(s=new Set(e.cat.split(",")||[])),s.has(t)}var P=Object.freeze({__proto__:null,TracingModel:l,eventPhasesOfInterestForTraceBounds:h,MetadataEvent:u,LegacyTopLevelEventCategory:m,DevToolsMetadataEventCategory:p,DevToolsTimelineEventCategory:f,eventHasPayload:function(e){return"rawPayload"in e},Event:g,ConstructedEvent:v,PayloadEvent:E,ObjectSnapshot:y,AsyncEvent:T,Process:C,Thread:N,timesForEventInMilliseconds:function(e){return e instanceof g?{startTime:s.Timing.MilliSeconds(e.startTime),endTime:e.endTime?s.Timing.MilliSeconds(e.endTime):void 0,duration:s.Timing.MilliSeconds(e.duration||0),selfTime:s.Timing.MilliSeconds(e.selfTime)}:t.Timing.eventTimingsMilliSeconds(e)},eventHasCategory:A,phaseForEvent:function(e){return e instanceof g?e.phase:e.ph},threadIDForEvent:function(e){return e instanceof g?e.thread.idInternal:e.tid},eventIsFromNewEngine:function(e){return null!==e&&!(e instanceof g)}});class w extends Event{data;static eventName="traceparseprogress";constructor(e,t={bubbles:!0}){super(w.eventName,t),this.data=e}}class M extends EventTarget{#D;#R="IDLE";#x=s.Configuration.DEFAULT;#O=null;static createWithAllHandlers(){return new M(r.ModelHandlers,s.Configuration.DEFAULT)}constructor(e,t){super(),this.#_(e),this.#D={Meta:r.ModelHandlers.Meta,...e},t&&(this.#x=t),this.#B()}updateConfiguration(e){this.#x=e,this.#B()}#B(){for(const e of Object.values(this.#D))"handleUserConfig"in e&&e.handleUserConfig&&e.handleUserConfig(this.#x)}#_(e){if(Object.keys(e).length===Object.keys(r.ModelHandlers).length)return;const t=new Set;for(const[s,n]of Object.entries(e)){t.add(s);for(const e of n.deps?.()||[])t.add(e)}const s=new Set(Object.keys(e));t.delete("Meta");for(const e of t)if(!s.has(e))throw new Error(`Required handler ${e} not provided.`)}reset(){if("PARSING"===this.#R)throw new Error("Trace processor can't reset while parsing.");const e=Object.values(this.#D);for(const t of e)t.reset();this.#O=null,this.#R="IDLE"}async parse(e,t=!1){if("IDLE"!==this.#R)throw new Error(`Trace processor can't start parsing when not idle. Current state: ${this.#R}`);try{this.#R="PARSING",await this.#L(e,t),this.#R="FINISHED_PARSING"}catch(e){throw this.#R="ERRORED_WHILE_PARSING",e}}async#L(e,t){const{pauseDuration:s,eventsPerChunk:n}=this.#x.processing,r=new R(e,s,n),i=[...D(this.#D).values()];for(const e of i)e.reset();for(const e of i)e.initialize?.(t);for await(const e of r)if(2!==e.kind)for(const t of i)t.handleEvent(e.data);else this.dispatchEvent(new w(e.data));for(const e of i)await(e.finalize?.())}get traceParsedData(){if("FINISHED_PARSING"!==this.#R)return null;const e={};for(const[t,s]of Object.entries(this.#D))Object.assign(e,{[t]:s.data()});return e}#F(e){const t={};for(const[s,n]of Object.entries(i.InsightRunners)){n.deps().some((t=>!e[t]))||Object.assign(t,{[s]:n.generateInsight})}return t}get insights(){if(!this.traceParsedData)return null;if(this.#O)return this.#O;this.#O=new Map;const e=this.#F(this.traceParsedData);for(const t of this.traceParsedData.Meta.mainFrameNavigations){if(!t.args.frame||!t.args.data?.navigationId)continue;const s={frameId:t.args.frame,navigationId:t.args.data.navigationId},n={};for(const[t,r]of Object.entries(e)){let e;try{e=r(this.traceParsedData,s)}catch(t){e=t}Object.assign(n,{[t]:e})}this.#O.set(s.navigationId,n)}return this.#O}}function D(e){const t=new Map,s=new Set,n=r=>{if(t.has(r))return;if(s.has(r)){let e="";for(const t of s)(e||t===r)&&(e+=`${t}->`);throw e+=r,new Error(`Found dependency cycle in trace event handlers: ${e}`)}s.add(r);const i=e[r];if(!i)return;const a=i.deps?.();a&&a.forEach(n),t.set(r,i)};for(const t of Object.keys(e))n(t);return t}class R{traceEvents;pauseDuration;eventsPerChunk;#k;constructor(e,t,s){this.traceEvents=e,this.pauseDuration=t,this.eventsPerChunk=s,this.#k=0}async*[Symbol.asyncIterator](){for(let e=0,t=this.traceEvents.length;e<t;e++)++this.#k%this.eventsPerChunk==0&&(yield{kind:2,data:{index:e,total:t}},await new Promise((e=>setTimeout(e,this.pauseDuration)))),yield{kind:1,data:this.traceEvents[e]}}}var x=Object.freeze({__proto__:null,TraceParseProgressEvent:w,TraceProcessor:M,sortHandlers:D});class O extends EventTarget{#j=[];#U=new Map;#H=[];#$=0;#G;#z=s.Configuration.DEFAULT;static createWithAllHandlers(e){return new O(r.ModelHandlers,e)}constructor(e,t){super(),t&&(this.#z=t),this.#G=new M(e,this.#z)}updateConfiguration(e){this.#z=e,this.#G.updateConfiguration(e)}async parse(e,t){const s=t?.metadata||{},n=t?.isFreshRecording||!1,r=e=>{const{data:t}=e;this.dispatchEvent(new _({type:"PROGRESS_UPDATE",data:t}))};this.#G.addEventListener(w.eventName,r);const i={traceEvents:e,metadata:s,traceParsedData:null,traceInsights:null};try{await this.#G.parse(e,n),this.#V(i,this.#G.traceParsedData,this.#G.insights),this.#j.push(i)}catch(e){throw e}finally{this.#G.removeEventListener(w.eventName,r),this.dispatchEvent(new _({type:"COMPLETE",data:"done"}))}}#V(s,n,r){s.traceParsedData=n,s.traceInsights=r,this.#$++;let i=`Trace ${this.#$}`,a=null;if(s.traceParsedData&&(a=t.Trace.extractOriginFromTrace(s.traceParsedData.Meta.mainFrameURL),a)){const t=e.MapUtilities.getWithDefault(this.#U,a,(()=>1));i=`${a} (${t})`,this.#U.set(a,t+1)}this.#H.push(i)}traceParsedData(e=this.#j.length-1){return this.#j[e]?this.#j[e].traceParsedData:null}traceInsights(e=this.#j.length-1){return this.#j[e]?this.#j[e].traceInsights:null}metadata(e){return this.#j[e]?this.#j[e].metadata:null}traceEvents(e){return this.#j[e]?this.#j[e].traceEvents:null}size(){return this.#j.length}deleteTraceByIndex(e){this.#j.splice(e,1),this.#H.splice(e,1)}getRecordingsAvailable(){return this.#H}resetProcessor(){this.#G.reset()}}class _ extends Event{data;static eventName="modelupdate";constructor(e){super(_.eventName),this.data=e}}var B=Object.freeze({__proto__:null,Model:O,ModelUpdateEvent:_,isModelUpdateDataComplete:function(e){return"COMPLETE"===e.type},isModelUpdateDataProgress:function(e){return"PROGRESS_UPDATE"===e.type}});class L extends d.SDKModel.SDKModel{#W;#K;#q;#X;#J;constructor(e){super(e),this.#W=e.tracingAgent(),e.registerTracingDispatcher(new F(this)),this.#K=null,this.#q=0,this.#X=0}bufferUsage(e,t,s){this.#q=void 0===t?null:t,this.#K&&this.#K.tracingBufferUsage(e||s||0)}eventsCollected(e){this.#K&&(this.#K.traceEventsCollected(e),this.#X+=e.length,this.#q?(this.#X>this.#q&&(this.#X=this.#q),this.#K.eventsRetrievalProgress(this.#X/this.#q)):this.#K.eventsRetrievalProgress(0))}tracingComplete(){this.#q=0,this.#X=0,this.#K&&(this.#K.tracingComplete(),this.#K=null),this.#J=!1}async reset(){this.#K&&await this.#W.invoke_end(),this.#q=0,this.#X=0,this.#K=null,this.#J=!1}async start(e,t,s){if(this.#K)throw new Error("Tracing is already started");this.#K=e;const n={bufferUsageReportingInterval:500,categories:t,options:s,transferMode:"ReportEvents"},r=await this.#W.invoke_start(n);return r.getError()&&(this.#K=null),r}stop(){if(!this.#K)throw new Error("Tracing is not started");if(this.#J)throw new Error("Tracing is already being stopped");this.#J=!0,this.#W.invoke_end()}}class F{#Q;constructor(e){this.#Q=e}bufferUsage({value:e,eventCount:t,percentFull:s}){this.#Q.bufferUsage(e,t,s)}dataCollected({value:e}){this.#Q.eventsCollected(e)}tracingComplete(){this.#Q.tracingComplete()}}d.SDKModel.SDKModel.register(L,{capabilities:128,autostart:!1});var k=Object.freeze({__proto__:null,TracingManager:L});export{c as EntriesFilter,P as Legacy,x as Processor,B as TraceModel,k as TracingManager};
