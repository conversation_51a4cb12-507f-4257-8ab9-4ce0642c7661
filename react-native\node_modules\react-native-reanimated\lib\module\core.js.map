{"version": 3, "names": ["NativeReanimatedModule", "isWeb", "shouldBeUseWeb", "isF<PERSON><PERSON>", "makeShareableCloneRecursive", "initializeUIRuntime", "SensorContainer", "ReanimatedError", "startMapper", "stopMapper", "runOnJS", "runOnUI", "executeOnUIRuntimeSync", "createWorkletRuntime", "runOnRuntime", "makeShareable", "makeMutable", "SHOULD_BE_USE_WEB", "isReanimated3", "isConfigured", "getViewProp", "viewTag", "propName", "component", "Promise", "resolve", "reject", "result", "substr", "getSensorContainer", "global", "__sensorContainer", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "handleAndFlushAnimationFrame", "eventTimestamp", "event", "__frameTimestamp", "__flushAnimationFrame", "undefined", "unregisterEventHandler", "id", "subscribeForKeyboardEvents", "options", "state", "height", "now", "_getAnimationTimestamp", "isStatusBarTranslucentAndroid", "isNavigationBarTranslucentAndroid", "unsubscribeFromKeyboardEvents", "listenerId", "registerSensor", "sensorType", "config", "sensorContainer", "initializeSensor", "unregisterSensor", "sensorId", "featuresConfig", "enableLayoutAnimations", "setByUser", "flag", "isCallByUser", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "jsiConfigureProps", "uiProps", "nativeProps", "configureProps"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,sBAAmB;AASnE,SAASC,2BAA2B,QAAQ,iBAAc;AAC1D,SAASC,mBAAmB,QAAQ,mBAAgB;AAEpD,SAASC,eAAe,QAAQ,sBAAmB;AACnD,SAASC,eAAe,QAAQ,aAAU;AAE1C,SAASC,WAAW,EAAEC,UAAU,QAAQ,cAAW;AACnD,SAASC,OAAO,EAAEC,OAAO,EAAEC,sBAAsB,QAAQ,cAAW;AACpE,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,eAAY;AAE/D,SAASC,aAAa,EAAEX,2BAA2B,QAAQ,iBAAc;AACzE,SAASY,WAAW,QAAQ,eAAY;AAExC,MAAMC,iBAAiB,GAAGf,cAAc,CAAC,CAAC;;AAE1C;AACA,OAAO,MAAMgB,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGD,aAAa;AAEzC,OAAO,SAASE,WAAWA,CACzBC,OAAe,EACfC,QAAgB,EAChBC,SAA2B,EACf;EACZ,IAAIpB,QAAQ,CAAC,CAAC,IAAI,CAACoB,SAAS,EAAE;IAC5B,MAAM,IAAIhB,eAAe,CACvB,oFACF,CAAC;EACH;;EAEA;EACA,OAAO,IAAIiB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,OAAO1B,sBAAsB,CAACoB,WAAW,CACvCC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACRI,MAAS,IAAK;MACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClEF,MAAM,CAACC,MAAM,CAAC;MAChB,CAAC,MAAM;QACLF,OAAO,CAACE,MAAM,CAAC;MACjB;IACF,CACF,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASE,kBAAkBA,CAAA,EAAoB;EAC7C,IAAI,CAACC,MAAM,CAACC,iBAAiB,EAAE;IAC7BD,MAAM,CAACC,iBAAiB,GAAG,IAAIzB,eAAe,CAAC,CAAC;EAClD;EACA,OAAOwB,MAAM,CAACC,iBAAiB;AACjC;AAEA,OAAO,SAASC,oBAAoBA,CAClCC,YAAgC,EAChCC,SAAiB,EACjBC,eAAe,GAAG,CAAC,CAAC,EACZ;EACR,SAASC,4BAA4BA,CAACC,cAAsB,EAAEC,KAAQ,EAAE;IACtE,SAAS;;IACTR,MAAM,CAACS,gBAAgB,GAAGF,cAAc;IACxCJ,YAAY,CAACK,KAAK,CAAC;IACnBR,MAAM,CAACU,qBAAqB,CAACH,cAAc,CAAC;IAC5CP,MAAM,CAACS,gBAAgB,GAAGE,SAAS;EACrC;EACA,OAAOzC,sBAAsB,CAACgC,oBAAoB,CAChD5B,2BAA2B,CAACgC,4BAA4B,CAAC,EACzDF,SAAS,EACTC,eACF,CAAC;AACH;AAEA,OAAO,SAASO,sBAAsBA,CAACC,EAAU,EAAQ;EACvD,OAAO3C,sBAAsB,CAAC0C,sBAAsB,CAACC,EAAE,CAAC;AAC1D;AAEA,OAAO,SAASC,0BAA0BA,CACxCX,YAAqD,EACrDY,OAAgC,EACxB;EACR;EACA;EACA,SAAST,4BAA4BA,CAACU,KAAa,EAAEC,MAAc,EAAE;IACnE,SAAS;;IACT,MAAMC,GAAG,GAAGlB,MAAM,CAACmB,sBAAsB,CAAC,CAAC;IAC3CnB,MAAM,CAACS,gBAAgB,GAAGS,GAAG;IAC7Bf,YAAY,CAACa,KAAK,EAAEC,MAAM,CAAC;IAC3BjB,MAAM,CAACU,qBAAqB,CAACQ,GAAG,CAAC;IACjClB,MAAM,CAACS,gBAAgB,GAAGE,SAAS;EACrC;EACA,OAAOzC,sBAAsB,CAAC4C,0BAA0B,CACtDxC,2BAA2B,CAACgC,4BAA4B,CAAC,EACzDS,OAAO,CAACK,6BAA6B,IAAI,KAAK,EAC9CL,OAAO,CAACM,iCAAiC,IAAI,KAC/C,CAAC;AACH;AAEA,OAAO,SAASC,6BAA6BA,CAACC,UAAkB,EAAQ;EACtE,OAAOrD,sBAAsB,CAACoD,6BAA6B,CAACC,UAAU,CAAC;AACzE;AAEA,OAAO,SAASC,cAAcA,CAC5BC,UAAsB,EACtBC,MAAoB,EACpBvB,YAGS,EACD;EACR,MAAMwB,eAAe,GAAG5B,kBAAkB,CAAC,CAAC;EAC5C,OAAO4B,eAAe,CAACH,cAAc,CACnCC,UAAU,EACVC,MAAM,EACNpD,2BAA2B,CAAC6B,YAAY,CAC1C,CAAC;AACH;AAEA,OAAO,SAASyB,gBAAgBA,CAC9BH,UAAsB,EACtBC,MAAoB,EACkB;EACtC,MAAMC,eAAe,GAAG5B,kBAAkB,CAAC,CAAC;EAC5C,OAAO4B,eAAe,CAACC,gBAAgB,CAACH,UAAU,EAAEC,MAAM,CAAC;AAC7D;AAEA,OAAO,SAASG,gBAAgBA,CAACC,QAAgB,EAAQ;EACvD,MAAMH,eAAe,GAAG5B,kBAAkB,CAAC,CAAC;EAC5C,OAAO4B,eAAe,CAACE,gBAAgB,CAACC,QAAQ,CAAC;AACnD;AAEA,IAAI,CAAC3D,KAAK,CAAC,CAAC,EAAE;EACZI,mBAAmB,CAAC,CAAC;AACvB;AAOA,IAAIwD,cAA8B,GAAG;EACnCC,sBAAsB,EAAE,KAAK;EAC7BC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,SAASD,sBAAsBA,CACpCE,IAAa,EACbC,YAAY,GAAG,IAAI,EACb;EACN,IAAIA,YAAY,EAAE;IAChBJ,cAAc,GAAG;MACfC,sBAAsB,EAAEE,IAAI;MAC5BD,SAAS,EAAE;IACb,CAAC;IACD/D,sBAAsB,CAAC8D,sBAAsB,CAACE,IAAI,CAAC;EACrD,CAAC,MAAM,IACL,CAACH,cAAc,CAACE,SAAS,IACzBF,cAAc,CAACC,sBAAsB,KAAKE,IAAI,EAC9C;IACAH,cAAc,CAACC,sBAAsB,GAAGE,IAAI;IAC5ChE,sBAAsB,CAAC8D,sBAAsB,CAACE,IAAI,CAAC;EACrD;AACF;AAEA,OAAO,SAASE,6BAA6BA,CAC3CC,qBAAiD,EAC3C;EACNnE,sBAAsB,CAACkE,6BAA6B,CAACC,qBAAqB,CAAC;AAC7E;AAEA,OAAO,SAASC,6BAA6BA,CAC3C/C,OAA6B,EAC7BgD,aAAsB,EACtB;EACArE,sBAAsB,CAACoE,6BAA6B,CAClD/C,OAAO,EACPgD,aACF,CAAC;AACH;AAEA,OAAO,SAASC,iBAAiBA,CAC/BC,OAAiB,EACjBC,WAAqB,EACf;EACN,IAAI,CAACvD,iBAAiB,EAAE;IACtBjB,sBAAsB,CAACyE,cAAc,CAACF,OAAO,EAAEC,WAAW,CAAC;EAC7D;AACF", "ignoreList": []}