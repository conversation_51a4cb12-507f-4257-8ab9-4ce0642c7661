{"version": 3, "names": ["_native", "require", "React", "_interopRequireWildcard", "_reactNative", "_utils", "_ScreenStackHeaderConfig", "_SearchBar", "_interopRequireDefault", "_useBackPressSubscription", "_FontProcessor", "_warnOnce", "obj", "__esModule", "default", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "HeaderConfig", "_ref", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "backButtonDisplayMode", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "useTheme", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "useBackPressSubscription", "onBackPress", "executeNativeBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "processFonts", "fontFamily", "useEffect", "processedSearchBarOptions", "useMemo", "Platform", "OS", "onFocus", "_len", "arguments", "length", "args", "Array", "_key", "onClose", "_len2", "_key2", "isVisionOS", "isVision", "warnOnce", "color", "undefined", "createElement", "ScreenStackHeaderConfig", "backgroundColor", "card", "backTitle", "backTitleFontSize", "fontSize", "backTitleVisible", "blurEffect", "hidden", "hideBackButton", "hideShadow", "largeTitle", "largeTitleBackgroundColor", "largeTitleColor", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "largeTitleHideShadow", "name", "titleColor", "text", "titleFontSize", "titleFontWeight", "topInsetEnabled", "translucent", "onAttached", "onDetached", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "key", "source", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "isSearchBarAvailableForCurrentPlatform", "ScreenStackHeaderSearchBarView"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/HeaderConfig.tsx"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAIA,IAAAK,wBAAA,GAAAL,OAAA;AAQA,IAAAM,UAAA,GAAAC,sBAAA,CAAAP,OAAA;AAEA,IAAAQ,yBAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAH,sBAAA,CAAAP,OAAA;AAAiC,SAAAO,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAb,wBAAAa,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAH,UAAA,SAAAG,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAF,OAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAJ,CAAA,UAAAG,CAAA,CAAAE,GAAA,CAAAL,CAAA,OAAAM,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAY,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAN,CAAA,CAAAR,OAAA,GAAAE,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAc,GAAA,CAAAjB,CAAA,EAAAM,CAAA,GAAAA,CAAA;AAMlB,SAASY,YAAYA,CAAAC,IAAA,EA4Bb;EAAA,IA5Bc;IACnCC,eAAe;IACfC,sBAAsB;IACtBC,SAAS;IACTC,qBAAqB;IACrBC,qBAAqB,GAAG,SAAS;IACjCC,eAAe;IACfC,oBAAoB,GAAG,CAAC,CAAC;IACzBC,sBAAsB,GAAG,IAAI;IAC7BC,YAAY;IACZC,oBAAoB;IACpBC,gBAAgB;IAChBC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,gBAAgB;IAChBC,0BAA0B;IAC1BC,qBAAqB,GAAG,CAAC,CAAC;IAC1BC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,WAAW,GAAG,CAAC,CAAC;IAChBC,eAAe;IACfC,WAAW;IACXC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,qBAAqB,GAAG,IAAI;IAC5BC,iBAAiB;IACjBC,KAAK;IACLC,SAAS;IACTC;EACK,CAAC,GAAA3B,IAAA;EACN,MAAM;IAAE4B;EAAO,CAAC,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EAC7B,MAAMC,SAAS,GAAGV,eAAe,IAAIQ,MAAM,CAACG,OAAO;;EAEnD;EACA;EACA;EACA,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAG,IAAAC,kDAAwB,EAAC;IAC3BC,WAAW,EAAEC,6BAAsB;IACnCC,UAAU,EAAE,CAACb,SAAS,IAAI,CAAC,CAACA,SAAS,CAACc;EACxC,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE,IAAAC,2BAAY,EAAC,CACXrC,oBAAoB,CAACsC,UAAU,EAC/B9B,qBAAqB,CAAC8B,UAAU,EAChCvB,gBAAgB,CAACuB,UAAU,CAC5B,CAAC;;EAEJ;EACA;EACA9E,KAAK,CAAC+E,SAAS,CAAC,MAAMZ,iBAAiB,EAAE,CAACR,SAAS,CAAC,CAAC;EAErD,MAAMqB,yBAAyB,GAAGhF,KAAK,CAACiF,OAAO,CAAC,MAAM;IACpD,IACEC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBxB,SAAS,IACT,CAACA,SAAS,CAACc,yBAAyB,EACpC;MACA,MAAMW,OAAkC,GAAG,SAAAA,CAAA,EAAa;QACtDhB,kBAAkB,CAAC,CAAC;QAAC,SAAAiB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADwBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;QAAA;QAEjD/B,SAAS,CAACyB,OAAO,GAAG,GAAGI,IAAI,CAAC;MAC9B,CAAC;MACD,MAAMG,OAAkC,GAAG,SAAAA,CAAA,EAAa;QACtDxB,iBAAiB,CAAC,CAAC;QAAC,SAAAyB,KAAA,GAAAN,SAAA,CAAAC,MAAA,EADyBC,IAAI,OAAAC,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJL,IAAI,CAAAK,KAAA,IAAAP,SAAA,CAAAO,KAAA;QAAA;QAEjDlC,SAAS,CAACgC,OAAO,GAAG,GAAGH,IAAI,CAAC;MAC9B,CAAC;MAED,OAAO;QAAE,GAAG7B,SAAS;QAAEyB,OAAO;QAAEO;MAAQ,CAAC;IAC3C;IACA,OAAOhC,SAAS;EAClB,CAAC,EAAE,CAACA,SAAS,EAAES,kBAAkB,EAAED,iBAAiB,CAAC,CAAC;;EAEtD;EACA,MAAM2B,UAAU,GAAGZ,qBAAQ,EAAEa,QAAQ;EAErC,IAAAC,iBAAQ,EACNF,UAAU,KACPvC,gBAAgB,CAAC0C,KAAK,KAAKC,SAAS,IAAI7C,eAAe,KAAK6C,SAAS,CAAC,EACzE,2EACF,CAAC;EAED,oBACElG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAAgG,uBAAuB;IACtBjE,sBAAsB,EAAEA,sBAAuB;IAC/CkE,eAAe,EACbjD,WAAW,CAACiD,eAAe,GAAGjD,WAAW,CAACiD,eAAe,GAAGxC,MAAM,CAACyC,IACpE;IACDC,SAAS,EAAEhE,eAAgB;IAC3BmC,mBAAmB,EAAEA,mBAAoB;IACzC8B,iBAAiB,EAAEhE,oBAAoB,CAACiE,QAAS;IACjDC,gBAAgB,EAAEjE,sBAAuB;IACzCkE,UAAU,EAAEvD,WAAW,CAACuD,UAAW;IACnCV,KAAK,EAAElC,SAAU;IACjB3B,SAAS,EAAEA,SAAU;IACrBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,qBAAqB,EAAEA,qBAAsB;IAC7CsE,MAAM,EAAEzD,WAAW,KAAK,KAAM;IAC9B0D,cAAc,EAAElE,oBAAqB;IACrCmE,UAAU,EAAElE,gBAAiB;IAC7BmE,UAAU,EAAEjE,gBAAiB;IAC7BkE,yBAAyB,EAAEnE,gBAAgB,CAACwD,eAAgB;IAC5DY,eAAe,EAAEjE,qBAAqB,CAACiD,KAAM;IAC7CtB,oBAAoB,EAAEA,oBAAqB;IAC3CuC,kBAAkB,EAAElE,qBAAqB,CAACyD,QAAS;IACnDU,oBAAoB,EAAEnE,qBAAqB,CAACoE,UAAW;IACvDC,oBAAoB,EAAEtE,0BAA2B;IACjDa,KAAK,EACHN,WAAW,KAAK4C,SAAS,GACrB5C,WAAW,GACXM,KAAK,KAAKsC,SAAS,GACnBtC,KAAK,GACLF,KAAK,CAAC4D,IACX;IACDC,UAAU,EACRhE,gBAAgB,CAAC0C,KAAK,KAAKC,SAAS,GAChC3C,gBAAgB,CAAC0C,KAAK,GACtB5C,eAAe,KAAK6C,SAAS,GAC7B7C,eAAe,GACfQ,MAAM,CAAC2D,IACZ;IACD5C,eAAe,EAAEA,eAAgB;IACjC6C,aAAa,EAAElE,gBAAgB,CAACkD,QAAS;IACzCiB,eAAe,EAAEnE,gBAAgB,CAAC6D,UAAW;IAC7CO,eAAe,EAAEnE,qBAAsB;IACvCoE,WAAW,EAAEnE,iBAAiB,KAAK,IAAK;IACxCoE,UAAU,EAAE5D,cAAe;IAC3B6D,UAAU,EAAE5D;EAAe,GAC1BhB,WAAW,KAAKgD,SAAS,gBACxBlG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAA2H,0BAA0B,QACxB7E,WAAW,CAAC;IAAEa;EAAU,CAAC,CACA,CAAC,GAC3B,IAAI,EACP7B,eAAe,KAAKgE,SAAS,gBAC5BlG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAA4H,gCAAgC;IAC/BC,GAAG,EAAC,WAAW;IACfC,MAAM,EAAEhG;EAAgB,CACzB,CAAC,GACA,IAAI,EACPe,UAAU,KAAKiD,SAAS,gBACvBlG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAA+H,yBAAyB,QACvBlF,UAAU,CAAC;IAAEc;EAAU,CAAC,CACA,CAAC,GAC1B,IAAI,EACPrB,YAAY,KAAKwD,SAAS,gBACzBlG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAAgI,2BAA2B,QACzB1F,YAAY,CAAC;IAAEqB;EAAU,CAAC,CACA,CAAC,GAC5B,IAAI,EACPsE,6CAAsC,IACvCrD,yBAAyB,KAAKkB,SAAS,gBACrClG,KAAA,CAAAmG,aAAA,CAAC/F,wBAAA,CAAAkI,8BAA8B,qBAE7BtI,KAAA,CAAAmG,aAAA,CAAC9F,UAAA,CAAAO,OAAS,EAAKoE,yBAA4B,CACb,CAAC,GAC/B,IACmB,CAAC;AAE9B"}