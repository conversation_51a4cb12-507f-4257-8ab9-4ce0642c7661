{"version": 3, "sources": ["../../../../../src/start/server/middleware/DomComponentsMiddleware.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { createBundleUrlPath, ExpoMetroOptions } from './metroOptions';\nimport type { ServerRequest, ServerResponse } from './server.types';\nimport { Log } from '../../../log';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { memoize } from '../../../utils/fn';\nimport { fileURLToFilePath } from '../metro/createServerComponentsMiddleware';\n\nexport type PickPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport const DOM_COMPONENTS_BUNDLE_DIR = 'www.bundle';\n\nconst warnUnstable = memoize(() =>\n  Log.warn('Using experimental DOM Components API. Production exports may not work as expected.')\n);\n\nconst checkWebViewInstalled = memoize((projectRoot: string) => {\n  const webViewInstalled =\n    resolveFrom.silent(projectRoot, 'react-native-webview') ||\n    resolveFrom.silent(projectRoot, '@expo/dom-webview');\n  if (!webViewInstalled) {\n    throw new Error(\n      `To use DOM Components, you must install the 'react-native-webview' package. Run 'npx expo install react-native-webview' to install it.`\n    );\n  }\n});\n\ntype CreateDomComponentsMiddlewareOptions = {\n  /** The absolute metro or server root, used to calculate the relative dom entry path */\n  metroRoot: string;\n  /** The absolute project root, used to resolve the `expo/dom/entry.js` path */\n  projectRoot: string;\n};\n\nexport function createDomComponentsMiddleware(\n  { metroRoot, projectRoot }: CreateDomComponentsMiddlewareOptions,\n  instanceMetroOptions: PickPartial<ExpoMetroOptions, 'mainModuleName' | 'platform' | 'bytecode'>\n) {\n  return (req: ServerRequest, res: ServerResponse, next: (err?: Error) => void) => {\n    if (!req.url) return next();\n\n    const url = coerceUrl(req.url);\n\n    // Match `/_expo/@dom`.\n    // This URL can contain additional paths like `/_expo/@dom/foo.js?file=...` to help the Safari dev tools.\n    if (!url.pathname.startsWith('/_expo/@dom')) {\n      return next();\n    }\n\n    const file = url.searchParams.get('file');\n\n    if (!file || !file.startsWith('file://')) {\n      res.statusCode = 400;\n      res.statusMessage = 'Invalid file path: ' + file;\n      return res.end();\n    }\n\n    checkWebViewInstalled(projectRoot);\n    warnUnstable();\n\n    // Generate a unique entry file for the webview.\n    const generatedEntry = toPosixPath(file.startsWith('file://') ? fileURLToFilePath(file) : file);\n    const virtualEntry = toPosixPath(resolveFrom(projectRoot, 'expo/dom/entry.js'));\n    // The relative import path will be used like URI so it must be POSIX.\n    const relativeImport = './' + path.posix.relative(path.dirname(virtualEntry), generatedEntry);\n    // Create the script URL\n    const requestUrlBase = `http://${req.headers.host}`;\n    const metroUrl = new URL(\n      createBundleUrlPath({\n        ...instanceMetroOptions,\n        domRoot: encodeURI(relativeImport),\n        baseUrl: '/',\n        mainModuleName: path.relative(metroRoot, virtualEntry),\n        bytecode: false,\n        platform: 'web',\n        isExporting: false,\n        engine: 'hermes',\n        // Required for ensuring bundler errors are caught in the root entry / async boundary and can be recovered from automatically.\n        lazy: true,\n      }),\n      requestUrlBase\n    ).toString();\n\n    res.statusCode = 200;\n    // Return HTML file\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(\n      // Create the entry HTML file.\n      getDomComponentHtml(metroUrl, { title: path.basename(file) })\n    );\n  };\n}\n\nfunction coerceUrl(url: string) {\n  try {\n    return new URL(url);\n  } catch {\n    return new URL(url, 'https://localhost:0');\n  }\n}\n\nexport function getDomComponentHtml(src?: string, { title }: { title?: string } = {}) {\n  // This HTML is not optimized for `react-native-web` since DOM Components are meant for general React DOM web development.\n  return `\n<!DOCTYPE html>\n<html>\n    <head>\n        <meta charset=\"utf-8\" />\n        <meta httpEquiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\n        ${title ? `<title>${title}</title>` : ''}\n        <style id=\"expo-dom-component-style\">\n        /* These styles make the body full-height */\n        html,\n        body {\n          -webkit-overflow-scrolling: touch; /* Enables smooth momentum scrolling */\n        }\n        /* These styles make the root element full-height */\n        #root {\n          display: flex;\n          flex: 1;\n        }\n        </style>\n    </head>\n    <body>\n    <noscript>DOM Components require <code>javaScriptEnabled</code></noscript>\n        <!-- Root element for the DOM component. -->\n        <div id=\"root\"></div>\n        ${src ? `<script crossorigin src=\"${src.replace(/^https?:/, '')}\"></script>` : ''}\n    </body>\n</html>`;\n}\n"], "names": ["DOM_COMPONENTS_BUNDLE_DIR", "createDomComponentsMiddleware", "getDomComponentHtml", "warnUnstable", "memoize", "Log", "warn", "checkWebViewInstalled", "projectRoot", "webViewInstalled", "resolveFrom", "silent", "Error", "metroRoot", "instanceMetroOptions", "req", "res", "next", "url", "coerceUrl", "pathname", "startsWith", "file", "searchParams", "get", "statusCode", "statusMessage", "end", "generatedEntry", "toPosixPath", "fileURLToFilePath", "virtualEntry", "relativeImport", "path", "posix", "relative", "dirname", "requestUrlBase", "headers", "host", "metroUrl", "URL", "createBundleUrlPath", "domRoot", "encodeURI", "baseUrl", "mainModuleName", "bytecode", "platform", "isExporting", "engine", "lazy", "toString", "<PERSON><PERSON><PERSON><PERSON>", "title", "basename", "src", "replace"], "mappings": ";;;;;;;;;;;IAYaA,yBAAyB;eAAzBA;;IAwBGC,6BAA6B;eAA7BA;;IAoEAC,mBAAmB;eAAnBA;;;;gEAxGC;;;;;;;gEACO;;;;;;8BAE8B;qBAElC;0BACQ;oBACJ;kDACU;;;;;;AAI3B,MAAMF,4BAA4B;AAEzC,MAAMG,eAAeC,IAAAA,WAAO,EAAC,IAC3BC,QAAG,CAACC,IAAI,CAAC;AAGX,MAAMC,wBAAwBH,IAAAA,WAAO,EAAC,CAACI;IACrC,MAAMC,mBACJC,sBAAW,CAACC,MAAM,CAACH,aAAa,2BAChCE,sBAAW,CAACC,MAAM,CAACH,aAAa;IAClC,IAAI,CAACC,kBAAkB;QACrB,MAAM,IAAIG,MACR,CAAC,sIAAsI,CAAC;IAE5I;AACF;AASO,SAASX,8BACd,EAAEY,SAAS,EAAEL,WAAW,EAAwC,EAChEM,oBAA+F;IAE/F,OAAO,CAACC,KAAoBC,KAAqBC;QAC/C,IAAI,CAACF,IAAIG,GAAG,EAAE,OAAOD;QAErB,MAAMC,MAAMC,UAAUJ,IAAIG,GAAG;QAE7B,uBAAuB;QACvB,yGAAyG;QACzG,IAAI,CAACA,IAAIE,QAAQ,CAACC,UAAU,CAAC,gBAAgB;YAC3C,OAAOJ;QACT;QAEA,MAAMK,OAAOJ,IAAIK,YAAY,CAACC,GAAG,CAAC;QAElC,IAAI,CAACF,QAAQ,CAACA,KAAKD,UAAU,CAAC,YAAY;YACxCL,IAAIS,UAAU,GAAG;YACjBT,IAAIU,aAAa,GAAG,wBAAwBJ;YAC5C,OAAON,IAAIW,GAAG;QAChB;QAEApB,sBAAsBC;QACtBL;QAEA,gDAAgD;QAChD,MAAMyB,iBAAiBC,IAAAA,qBAAW,EAACP,KAAKD,UAAU,CAAC,aAAaS,IAAAA,mDAAiB,EAACR,QAAQA;QAC1F,MAAMS,eAAeF,IAAAA,qBAAW,EAACnB,IAAAA,sBAAW,EAACF,aAAa;QAC1D,sEAAsE;QACtE,MAAMwB,iBAAiB,OAAOC,eAAI,CAACC,KAAK,CAACC,QAAQ,CAACF,eAAI,CAACG,OAAO,CAACL,eAAeH;QAC9E,wBAAwB;QACxB,MAAMS,iBAAiB,CAAC,OAAO,EAAEtB,IAAIuB,OAAO,CAACC,IAAI,EAAE;QACnD,MAAMC,WAAW,IAAIC,IACnBC,IAAAA,iCAAmB,EAAC;YAClB,GAAG5B,oBAAoB;YACvB6B,SAASC,UAAUZ;YACnBa,SAAS;YACTC,gBAAgBb,eAAI,CAACE,QAAQ,CAACtB,WAAWkB;YACzCgB,UAAU;YACVC,UAAU;YACVC,aAAa;YACbC,QAAQ;YACR,8HAA8H;YAC9HC,MAAM;QACR,IACAd,gBACAe,QAAQ;QAEVpC,IAAIS,UAAU,GAAG;QACjB,mBAAmB;QACnBT,IAAIqC,SAAS,CAAC,gBAAgB;QAE9BrC,IAAIW,GAAG,CACL,8BAA8B;QAC9BzB,oBAAoBsC,UAAU;YAAEc,OAAOrB,eAAI,CAACsB,QAAQ,CAACjC;QAAM;IAE/D;AACF;AAEA,SAASH,UAAUD,GAAW;IAC5B,IAAI;QACF,OAAO,IAAIuB,IAAIvB;IACjB,EAAE,OAAM;QACN,OAAO,IAAIuB,IAAIvB,KAAK;IACtB;AACF;AAEO,SAAShB,oBAAoBsD,GAAY,EAAE,EAAEF,KAAK,EAAsB,GAAG,CAAC,CAAC;IAClF,0HAA0H;IAC1H,OAAO,CAAC;;;;;;;QAOF,EAAEA,QAAQ,CAAC,OAAO,EAAEA,MAAM,QAAQ,CAAC,GAAG,GAAG;;;;;;;;;;;;;;;;;;QAkBzC,EAAEE,MAAM,CAAC,yBAAyB,EAAEA,IAAIC,OAAO,CAAC,YAAY,IAAI,WAAW,CAAC,GAAG,GAAG;;OAEnF,CAAC;AACR"}