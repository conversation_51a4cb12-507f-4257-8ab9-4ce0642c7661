import"../shell/shell.js";import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as o from"../../ui/legacy/legacy.js";import*as i from"../../core/common/common.js";import*as n from"../../models/issues_manager/issues_manager.js";import*as r from"../../core/root/root.js";import*as a from"../../models/workspace/workspace.js";import*as s from"../../panels/network/forward/forward.js";import*as l from"../../panels/application/preloading/helper/helper.js";import*as c from"../../panels/mobile_throttling/mobile_throttling.js";import*as g from"../../ui/legacy/components/utils/utils.js";import*as d from"../main/main.js";const w={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},p=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",w),m=e.i18n.getLazilyComputedLocalizedString.bind(void 0,p);let u,R;async function y(){return u||(u=await import("../../panels/browser_debugger/browser_debugger.js")),u}async function h(){return R||(R=await import("../../panels/sources/sources.js")),R}o.ViewManager.registerViewExtension({loadView:async()=>(await y()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:m(w.showEventListenerBreakpoints),title:m(w.eventListenerBreakpoints),order:9,persistence:"permanent"}),o.ViewManager.registerViewExtension({loadView:async()=>new((await y()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:m(w.showCspViolationBreakpoints),title:m(w.cspViolationBreakpoints),order:10,persistence:"permanent"}),o.ViewManager.registerViewExtension({loadView:async()=>(await y()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:m(w.showXhrfetchBreakpoints),title:m(w.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),o.ViewManager.registerViewExtension({loadView:async()=>(await y()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:m(w.showDomBreakpoints),title:m(w.domBreakpoints),order:7,persistence:"permanent"}),o.ViewManager.registerViewExtension({loadView:async()=>new((await y()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:m(w.showGlobalListeners),title:m(w.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),o.ViewManager.registerViewExtension({loadView:async()=>(await y()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:m(w.showDomBreakpoints),title:m(w.domBreakpoints),order:6,persistence:"permanent"}),o.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:m(w.page),commandPrompt:m(w.showPage),order:2,persistence:"permanent",loadView:async()=>(await h()).SourcesNavigator.NetworkNavigatorView.instance()}),o.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:m(w.overrides),commandPrompt:m(w.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await h()).SourcesNavigator.OverridesNavigatorView.instance()}),o.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:m(w.contentScripts),commandPrompt:m(w.showContentScripts),order:5,persistence:"permanent",loadView:async()=>new((await h()).SourcesNavigator.ContentScriptsNavigatorView)}),o.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await y()).ObjectEventListenersSidebarPane.ActionDelegate),title:m(w.refreshGlobalListeners),iconClass:"refresh",contextTypes:()=>void 0===u?[]:(e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane])(u)}),o.ContextMenu.registerProvider({contextTypes:()=>[t.DOMModel.DOMNode],loadProvider:async()=>new((await y()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),o.Context.registerListener({contextTypes:()=>[t.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await y()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),o.Context.registerListener({contextTypes:()=>[t.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await y()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});const k={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},P=e.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",k),v=e.i18n.getLazilyComputedLocalizedString.bind(void 0,P);let A;o.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:v(k.developerResources),commandPrompt:v(k.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return A||(A=await import("../../panels/developer_resources/developer_resources.js")),A}()).DeveloperResourcesView.DeveloperResourcesView)});const b={issues:"Issues",showIssues:"Show Issues"},S=e.i18n.registerUIStrings("panels/issues/issues-meta.ts",b),f=e.i18n.getLazilyComputedLocalizedString.bind(void 0,S);let E;async function T(){return E||(E=await import("../../panels/issues/issues.js")),E}o.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:f(b.issues),commandPrompt:f(b.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await T()).IssuesPane.IssuesPane)}),i.Revealer.registerRevealer({contextTypes:()=>[n.Issue.Issue],destination:i.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await T()).IssueRevealer.IssueRevealer)});const x={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},N=e.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",x),L=e.i18n.getLazilyComputedLocalizedString.bind(void 0,N);o.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:"LAYERS",title:L(x.resetView),bindings:[{shortcut:"0"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:"LAYERS",title:L(x.switchToPanMode),bindings:[{shortcut:"x"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:"LAYERS",title:L(x.switchToRotateMode),bindings:[{shortcut:"v"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:"LAYERS",title:L(x.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:"LAYERS",title:L(x.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.up",category:"LAYERS",title:L(x.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.down",category:"LAYERS",title:L(x.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.left",category:"LAYERS",title:L(x.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.right",category:"LAYERS",title:L(x.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});const D={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},C=e.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",D),I=e.i18n.getLazilyComputedLocalizedString.bind(void 0,C);let V;async function M(){return V||(V=await import("../../panels/mobile_throttling/mobile_throttling.js")),V}o.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:I(D.throttling),commandPrompt:I(D.showThrottling),order:35,loadView:async()=>new((await M()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"]}),o.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:I(D.goOffline),loadActionDelegate:async()=>new((await M()).ThrottlingManager.ActionDelegate),tags:[I(D.device),I(D.throttlingTag)]}),o.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:I(D.enableSlowGThrottling),loadActionDelegate:async()=>new((await M()).ThrottlingManager.ActionDelegate),tags:[I(D.device),I(D.throttlingTag)]}),o.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:I(D.enableFastGThrottling),loadActionDelegate:async()=>new((await M()).ThrottlingManager.ActionDelegate),tags:[I(D.device),I(D.throttlingTag)]}),o.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:I(D.goOnline),loadActionDelegate:async()=>new((await M()).ThrottlingManager.ActionDelegate),tags:[I(D.device),I(D.throttlingTag)]}),i.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const O={showNetwork:"Show Network",network:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},B=e.i18n.registerUIStrings("panels/network/network-meta.ts",O),F=e.i18n.getLazilyComputedLocalizedString.bind(void 0,B);let j;async function U(){return j||(j=await import("../../panels/network/network.js")),j}function q(e){return void 0===j?[]:e(j)}o.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:F(O.showNetwork),title:F(O.network),order:1100,condition:r.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await U()).NetworkPanel.NetworkPanel.instance()}),o.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:F(O.showNetworkRequestBlocking),title:F(O.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await U()).BlockedURLsPane.BlockedURLsPane)}),o.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:F(O.showNetworkConditions),title:F(O.networkConditions),persistence:"closeable",order:40,tags:[F(O.diskCache),F(O.networkThrottling),e.i18n.lockedLazyString("useragent"),e.i18n.lockedLazyString("user agent"),e.i18n.lockedLazyString("user-agent")],loadView:async()=>(await U()).NetworkConfigView.NetworkConfigView.instance()}),o.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:F(O.showSearch),title:F(O.search),persistence:"permanent",loadView:async()=>(await U()).NetworkPanel.SearchNetworkView.instance()}),o.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await U()).NetworkPanel.ActionDelegate),options:[{value:!0,title:F(O.recordNetworkLog)},{value:!1,title:F(O.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),o.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:F(O.clear),iconClass:"clear",loadActionDelegate:async()=>new((await U()).NetworkPanel.ActionDelegate),contextTypes:()=>q((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),o.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:F(O.hideRequestDetails),contextTypes:()=>q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await U()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),o.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:F(O.search),contextTypes:()=>q((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await U()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),o.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:F(O.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>q((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await U()).BlockedURLsPane.ActionDelegate)}),o.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:F(O.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>q((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await U()).BlockedURLsPane.ActionDelegate)}),i.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:F(O.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[F(O.colorCode),F(O.resourceType)],options:[{value:!0,title:F(O.colorCodeByResourceType)},{value:!1,title:F(O.useDefaultColors)}]}),i.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:F(O.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[F(O.netWork),F(O.frame),F(O.group)],options:[{value:!0,title:F(O.groupNetworkLogItemsByFrame)},{value:!1,title:F(O.dontGroupNetworkLogItemsByFrame)}]}),o.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await U()).NetworkPanel.NetworkPanel.instance()}),o.ContextMenu.registerProvider({contextTypes:()=>[t.NetworkRequest.NetworkRequest,t.Resource.Resource,a.UISourceCode.UISourceCode],loadProvider:async()=>(await U()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),i.Revealer.registerRevealer({contextTypes:()=>[t.NetworkRequest.NetworkRequest],destination:i.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await U()).NetworkPanel.RequestRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[s.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await U()).NetworkPanel.RequestLocationRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[s.NetworkRequestId.NetworkRequestId],destination:i.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await U()).NetworkPanel.RequestIdRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[s.UIFilter.UIRequestFilter],destination:i.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await U()).NetworkPanel.NetworkLogWithFilterRevealer)});const W={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},_=e.i18n.registerUIStrings("panels/application/application-meta.ts",W),z=e.i18n.getLazilyComputedLocalizedString.bind(void 0,_);let G;async function K(){return G||(G=await import("../../panels/application/application.js")),G}o.ViewManager.registerViewExtension({location:"panel",id:"resources",title:z(W.application),commandPrompt:z(W.showApplication),order:70,loadView:async()=>(await K()).ResourcesPanel.ResourcesPanel.instance(),tags:[z(W.pwa)]}),o.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear",title:z(W.clearSiteData),loadActionDelegate:async()=>new((await K()).StorageView.ActionDelegate)}),o.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear-incl-third-party-cookies",title:z(W.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>new((await K()).StorageView.ActionDelegate)}),o.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===G?[]:(e=>[e.BackgroundServiceView.BackgroundServiceView])(G),loadActionDelegate:async()=>new((await K()).BackgroundServiceView.ActionDelegate),category:"BACKGROUND_SERVICES",options:[{value:!0,title:z(W.startRecordingEvents)},{value:!1,title:z(W.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.Revealer.registerRevealer({contextTypes:()=>[t.Resource.Resource],destination:i.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await K()).ResourcesPanel.ResourceRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[t.ResourceTreeModel.ResourceTreeFrame],destination:i.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await K()).ResourcesPanel.FrameDetailsRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[l.PreloadingForward.RuleSetView],destination:i.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await K()).ResourcesPanel.RuleSetViewRevealer)}),i.Revealer.registerRevealer({contextTypes:()=>[l.PreloadingForward.AttemptViewWithFilter],destination:i.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await K()).ResourcesPanel.AttemptViewWithFilterRevealer)});const Y={performance:"Performance",showPerformance:"Show Performance",javascriptProfiler:"JavaScript Profiler",showJavascriptProfiler:"Show JavaScript Profiler",record:"Record",stop:"Stop",startProfilingAndReloadPage:"Start profiling and reload page",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view",startStopRecording:"Start/stop recording"},H=e.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",Y),J=e.i18n.getLazilyComputedLocalizedString.bind(void 0,H);let X,Z;async function Q(){return X||(X=await import("../../panels/timeline/timeline.js")),X}async function $(){return Z||(Z=await import("../../panels/profiler/profiler.js")),Z}function ee(e){return void 0===X?[]:e(X)}o.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:J(Y.performance),commandPrompt:J(Y.showPerformance),order:50,experiment:"enable-performance-panel",loadView:async()=>(await Q()).TimelinePanel.TimelinePanel.instance()}),o.ViewManager.registerViewExtension({location:"panel",id:"js-profiler",title:J(Y.javascriptProfiler),commandPrompt:J(Y.showJavascriptProfiler),persistence:"permanent",order:65,experiment:"js-profiler-temporarily-enable",loadView:async()=>(await $()).ProfilesPanel.JSProfilerPanel.instance()}),o.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),options:[{value:!0,title:J(Y.record)},{value:!1,title:J(Y.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:J(Y.startProfilingAndReloadPage),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),o.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),title:J(Y.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),o.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),title:J(Y.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:J(Y.previousFrame),contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:J(Y.nextFrame),contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:J(Y.showRecentTimelineSessions),contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),title:J(Y.previousRecording),contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),o.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await Q()).TimelinePanel.ActionDelegate),title:J(Y.nextRecording),contextTypes:()=>ee((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),o.ActionRegistration.registerActionExtension({actionId:"profiler.js-toggle-recording",category:"JAVASCRIPT_PROFILER",title:J(Y.startStopRecording),iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===Z?[]:(e=>[e.ProfilesPanel.JSProfilerPanel])(Z),loadActionDelegate:async()=>(await $()).ProfilesPanel.JSProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:J(Y.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),i.Linkifier.registerLinkifier({contextTypes:()=>ee((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await Q()).CLSLinkifier.Linkifier.instance()}),o.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),o.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});const te={main:"Main"},oe=e.i18n.registerUIStrings("entrypoints/worker_app/WorkerMain.ts",te),ie=e.i18n.getLocalizedString.bind(void 0,oe);let ne;class re{static instance(e={forceNew:null}){const{forceNew:t}=e;return ne&&!t||(ne=new re),ne}async run(){t.Connections.initMainConnection((async()=>{await t.TargetManager.TargetManager.instance().maybeAttachInitialTarget()||t.TargetManager.TargetManager.instance().createTarget("main",ie(te.main),t.Target.Type.ServiceWorker,null)}),g.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost),new c.NetworkPanelIndicator.NetworkPanelIndicator}}i.Runnable.registerEarlyInitializationRunnable(re.instance),t.ChildTargetManager.ChildTargetManager.install((async({target:e,waitingForDebugger:o})=>{if(e.parentTarget()||e.type()!==t.Target.Type.ServiceWorker||!o)return;const i=e.model(t.DebuggerModel.DebuggerModel);i&&(i.isReadyToPause()||await i.once(t.DebuggerModel.Events.DebuggerIsReadyToPause),i.pause())})),self.runtime=r.Runtime.Runtime.instance({forceNew:!0}),new d.MainImpl.MainImpl;
