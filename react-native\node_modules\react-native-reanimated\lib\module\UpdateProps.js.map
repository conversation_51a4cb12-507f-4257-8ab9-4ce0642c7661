{"version": 3, "names": ["processColorsInProps", "_updatePropsJS", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "runOnUIImmediately", "ReanimatedError", "updateProps", "viewDescriptors", "updates", "isAnimatedProps", "value", "for<PERSON>ach", "viewDescriptor", "component", "tag", "global", "UpdatePropsManager", "update", "updatePropsJestWrapper", "animatedStyle", "adapters", "adapter", "current", "createUpdatePropsManager", "operations", "push", "shadowNodeWrapper", "length", "queueMicrotask", "flush", "_updatePropsFabric", "name", "_updatePropsPaper", "maybeThrowError", "Proxy", "get", "set"], "sourceRoot": "../../src", "sources": ["UpdateProps.ts"], "mappings": "AAAA;AACA,YAAY;;AAEZ,SAASA,oBAAoB,QAAQ,aAAU;AAQ/C,SAASC,cAAc,QAAQ,0BAAiB;AAChD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,QAAQ,sBAAmB;AACpE,SAASC,kBAAkB,QAAQ,cAAW;AAC9C,SAASC,eAAe,QAAQ,aAAU;AAE1C,IAAIC,WAIK;AAET,IAAIH,cAAc,CAAC,CAAC,EAAE;EACpBG,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,eAAe,KAAK;IAC3D,SAAS;;IACTF,eAAe,CAACG,KAAK,EAAEC,OAAO,CAAEC,cAAc,IAAK;MACjD,MAAMC,SAAS,GAAGD,cAAc,CAACE,GAA4B;MAC7Dd,cAAc,CAACQ,OAAO,EAAEK,SAAS,EAAEJ,eAAe,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,MAAM;EACLH,WAAW,GAAGA,CAACC,eAAe,EAAEC,OAAO,KAAK;IAC1C,SAAS;;IACTT,oBAAoB,CAACS,OAAO,CAAC;IAC7BO,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACV,eAAe,EAAEC,OAAO,CAAC;EAC5D,CAAC;AACH;AAEA,OAAO,MAAMU,sBAAsB,GAAGA,CACpCX,eAAuC,EACvCC,OAA2B,EAC3BW,aAAmD,EACnDC,QAAmD,KAC1C;EACTA,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAC5BA,OAAO,CAACb,OAAO,CAAC;EAClB,CAAC,CAAC;EACFW,aAAa,CAACG,OAAO,CAACZ,KAAK,GAAG;IAC5B,GAAGS,aAAa,CAACG,OAAO,CAACZ,KAAK;IAC9B,GAAGF;EACL,CAAC;EAEDF,WAAW,CAACC,eAAe,EAAEC,OAAO,CAAC;AACvC,CAAC;AAED,eAAeF,WAAW;AAE1B,MAAMiB,wBAAwB,GAAGtB,QAAQ,CAAC,CAAC,GACvC,MAAM;EACJ,SAAS;;EACT;EACA,MAAMuB,UAGH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJV,eAAuC,EACvCC,OAAwC,EACxC;MACAD,eAAe,CAACG,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDY,UAAU,CAACC,IAAI,CAAC;UACdC,iBAAiB,EAAEd,cAAc,CAACc,iBAAiB;UACnDlB;QACF,CAAC,CAAC;QACF,IAAIgB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACe,kBAAkB,CAAEN,UAAU,CAAC;MACtCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC,GACD,MAAM;EACJ,SAAS;;EACT;EACA,MAAMH,UAIH,GAAG,EAAE;EACR,OAAO;IACLP,MAAMA,CACJV,eAAuC,EACvCC,OAAwC,EACxC;MACAD,eAAe,CAACG,KAAK,CAACC,OAAO,CAAEC,cAAc,IAAK;QAChDY,UAAU,CAACC,IAAI,CAAC;UACdX,GAAG,EAAEF,cAAc,CAACE,GAAa;UACjCiB,IAAI,EAAEnB,cAAc,CAACmB,IAAI,IAAI,SAAS;UACtCvB;QACF,CAAC,CAAC;QACF,IAAIgB,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;UAC3BC,cAAc,CAAC,IAAI,CAACC,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC;IACDA,KAAKA,CAAA,EAAa;MAChBd,MAAM,CAACiB,iBAAiB,CAAER,UAAU,CAAC;MACrCA,UAAU,CAACG,MAAM,GAAG,CAAC;IACvB;EACF,CAAC;AACH,CAAC;AAEL,IAAIxB,cAAc,CAAC,CAAC,EAAE;EACpB,MAAM8B,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAAC/B,MAAM,CAAC,CAAC,EAAE;MACb,MAAM,IAAIG,eAAe,CACvB,+DACF,CAAC;IACH;EACF,CAAC;EACDU,MAAM,CAACC,kBAAkB,GAAG,IAAIkB,KAAK,CAAC,CAAC,CAAC,EAAwB;IAC9DC,GAAG,EAAEF,eAAe;IACpBG,GAAG,EAAEA,CAAA,KAAM;MACTH,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACL7B,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTW,MAAM,CAACC,kBAAkB,GAAGO,wBAAwB,CAAC,CAAC;EACxD,CAAC,CAAC,CAAC,CAAC;AACN;;AAUA;AACA;AACA;AACA", "ignoreList": []}