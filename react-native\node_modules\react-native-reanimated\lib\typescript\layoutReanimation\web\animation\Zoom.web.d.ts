export declare const ZoomInData: {
    ZoomIn: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInRotate: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
    ZoomInRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInEasyUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomInEasyDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
};
export declare const ZoomOutData: {
    ZoomOut: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutRotate: {
        name: string;
        style: {
            0: {
                transform: {
                    scale: number;
                    rotate: string;
                }[];
            };
            100: {
                transform: {
                    scale: number;
                    rotate: string;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutEasyUp: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
    ZoomOutEasyDown: {
        name: string;
        style: {
            0: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
            100: {
                transform: {
                    translateY: string;
                    scale: number;
                }[];
            };
        };
        duration: number;
    };
};
export declare const ZoomIn: {
    ZoomIn: {
        style: string;
        duration: number;
    };
    ZoomInRotate: {
        style: string;
        duration: number;
    };
    ZoomInRight: {
        style: string;
        duration: number;
    };
    ZoomInLeft: {
        style: string;
        duration: number;
    };
    ZoomInUp: {
        style: string;
        duration: number;
    };
    ZoomInDown: {
        style: string;
        duration: number;
    };
    ZoomInEasyUp: {
        style: string;
        duration: number;
    };
    ZoomInEasyDown: {
        style: string;
        duration: number;
    };
};
export declare const ZoomOut: {
    ZoomOut: {
        style: string;
        duration: number;
    };
    ZoomOutRotate: {
        style: string;
        duration: number;
    };
    ZoomOutRight: {
        style: string;
        duration: number;
    };
    ZoomOutLeft: {
        style: string;
        duration: number;
    };
    ZoomOutUp: {
        style: string;
        duration: number;
    };
    ZoomOutDown: {
        style: string;
        duration: number;
    };
    ZoomOutEasyUp: {
        style: string;
        duration: number;
    };
    ZoomOutEasyDown: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Zoom.web.d.ts.map