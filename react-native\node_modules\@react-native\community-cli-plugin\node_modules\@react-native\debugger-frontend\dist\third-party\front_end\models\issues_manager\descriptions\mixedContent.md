# Mixed content: load all resources via HTTPS to improve the security of your site

Even though the initial HTML page is loaded over a secure HTTPS connection, some resources like images, stylesheets or scripts are being accessed over an insecure HTTP connection. Usage of insecure resources is restricted to strengthen the security of your entire site.

To resolve this issue, load all resources over a secure HTTPS connection.
