{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeDebuggerGetPossibleBreakpoints.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * <PERSON><PERSON> doesn't seem to handle this request, but `locations` have to be returned.\n * Respond with an empty location to make it \"spec compliant\" with Chrome DevTools.\n */\nexport class VscodeDebuggerGetPossibleBreakpointsHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<DebuggerGetPossibleBreakpoints>) {\n    if (message.method === 'Debugger.getPossibleBreakpoints') {\n      return this.sendToDebugger<DeviceResponse<DebuggerGetPossibleBreakpoints>>({\n        id: message.id,\n        result: { locations: [] },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Debugger/#method-getPossibleBreakpoints */\nexport type DebuggerGetPossibleBreakpoints = CdpMessage<\n  'Debugger.getPossibleBreakpoints',\n  Protocol.Debugger.GetPossibleBreakpointsRequest,\n  Protocol.Debugger.GetPossibleBreakpointsResponse\n>;\n"], "names": ["VscodeDebuggerGetPossibleBreakpointsHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "sendToDebugger", "id", "result", "locations"], "mappings": ";;;;+BAUaA;;;eAAAA;;;gCARkB;iCACC;AAOzB,MAAMA,oDAAoDC,8BAAc;IAC7EC,YAAY;QACV,OAAOC,IAAAA,gCAAe,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,MAAM;IACtD;IAEAC,sBAAsBC,OAAwD,EAAE;QAC9E,IAAIA,QAAQC,MAAM,KAAK,mCAAmC;YACxD,OAAO,IAAI,CAACC,cAAc,CAAiD;gBACzEC,IAAIH,QAAQG,EAAE;gBACdC,QAAQ;oBAAEC,WAAW,EAAE;gBAAC;YAC1B;QACF;QAEA,OAAO;IACT;AACF"}