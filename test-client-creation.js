// Test de création de client avec secteur
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testClientCreation() {
  console.log('🧪 Test de création de client avec secteur\n');

  try {
    // 1. D'abord, récupérer les secteurs disponibles
    console.log('📤 1. Récupération des secteurs...');
    const secteursResponse = await axios.get(`${BASE_URL}/api/secteurs`);
    
    if (!secteursResponse.data.success) {
      console.log('❌ Erreur lors de la récupération des secteurs');
      return;
    }

    const secteurs = secteursResponse.data.data;
    console.log(`✅ ${secteurs.length} secteurs récupérés:`);
    secteurs.forEach(s => console.log(`   - ${s.nom} (ID: ${s.ids})`));

    // 2. Créer un client de test avec le premier secteur
    const premierSecteur = secteurs[0];
    console.log(`\n📤 2. Création d'un client avec le secteur "${premierSecteur.nom}"...`);

    const clientData = {
      nom: 'TestClient',
      prenom: 'TestPrenom',
      adresse: '123 Rue de Test',
      ville: 'TestVille',
      tel: '0123456789',
      email: '<EMAIL>',
      ids: premierSecteur.ids, // Utiliser l'ID du secteur
      marqueCompteur: 'TestMarque',
      numSerieCompteur: 'TEST123456',
      posX: '36.8065',
      posY: '10.1815'
    };

    console.log('📋 Données du client:', clientData);

    const response = await axios.post(`${BASE_URL}/api/clients`, clientData);

    if (response.data.success) {
      console.log('\n✅ CLIENT CRÉÉ AVEC SUCCÈS !');
      console.log('📊 Données du client créé:', response.data.client);
      console.log('📄 Contrat créé:', response.data.contract);
      console.log('🔗 QR Code généré:', response.data.qrCode ? 'Oui' : 'Non');
    } else {
      console.log('\n❌ Erreur lors de la création:', response.data.message);
    }

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.response?.data || error.message);
  }
}

// Test avec secteur invalide
async function testInvalidSector() {
  console.log('\n🧪 Test avec secteur invalide...');

  try {
    const clientData = {
      nom: 'TestClient2',
      prenom: 'TestPrenom2',
      adresse: '456 Rue de Test',
      ville: 'TestVille',
      tel: '0123456789',
      email: '<EMAIL>',
      ids: '', // Secteur vide - devrait échouer
      marqueCompteur: 'TestMarque',
      numSerieCompteur: 'TEST789',
      posX: '36.8065',
      posY: '10.1815'
    };

    const response = await axios.post(`${BASE_URL}/api/clients`, clientData);
    console.log('⚠️ Réponse inattendue:', response.data);

  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Validation correcte - Erreur 400:', error.response.data.message);
    } else {
      console.log('❌ Erreur inattendue:', error.response?.data || error.message);
    }
  }
}

// Exécuter les tests
async function runTests() {
  await testClientCreation();
  await testInvalidSector();
}

runTests();
