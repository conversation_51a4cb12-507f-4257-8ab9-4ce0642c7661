require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    
    // Test rapide pour vérifier la table Client
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${result.rows[0].count} clients trouvés dans la table Client`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    console.error('💡 Vérifiez que PostgreSQL est démarré et que la base "Facutration" existe');
    return false;
  }
}

// Route pour récupérer tous les clients depuis la table Client
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients - Récupération depuis la table Client');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} clients récupérés depuis la table Client`);
    
    // Log des premiers clients pour debug
    if (result.rows.length > 0) {
      console.log('📋 Premiers clients:');
      result.rows.slice(0, 3).forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
      });
    }

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s) dans la base Facutration`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients depuis la base Facutration',
      error: error.message
    });
  }
});

// Route pour récupérer un client spécifique par ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé dans la base Facutration'
      });
    }

    console.log(`✅ Client ${id} récupéré: ${result.rows[0].nom} ${result.rows[0].prenom}`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client trouvé dans la base Facutration'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client spécifique depuis la table Contract
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}/contracts`);

    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email,
        s.nom as secteur_nom
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      LEFT JOIN secteur s ON cl.ids = s.ids
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    const result = await pool.query(query, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);
    
    if (result.rows.length > 0) {
      console.log('📋 Contrats trouvés:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. ${contract.codeqr} - ${contract.marquecompteur || 'Marque non définie'}`);
      });
    }

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
      client_id: parseInt(id)
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée pour le contrat ${id}: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'Connected'
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  console.log('🔄 Démarrage du serveur Facutration...');
  
  // Tester la connexion à la base de données
  const dbConnected = await testDatabaseConnection();
  
  if (!dbConnected) {
    console.error('❌ Impossible de se connecter à la base de données "Facutration"');
    console.error('💡 Vérifiez que PostgreSQL est démarré et que la base existe');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur Facutration démarré sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id (client spécifique)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client)');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('\n✅ Prêt à recevoir les requêtes depuis React !');
  });
}

// Démarrer le serveur
startServer();

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err);
  process.exit(1);
});
