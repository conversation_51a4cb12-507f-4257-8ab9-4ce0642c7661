{"version": 3, "sources": ["../../../src/export/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoExport: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clear': Boolean,\n      '--dump-assetmap': Boolean,\n      '--dev': Boolean,\n      '--source-maps': Boolean,\n      '--max-workers': Number,\n      '--output-dir': String,\n      '--platform': [String],\n      '--no-minify': Boolean,\n      '--no-bytecode': Boolean,\n      '--no-ssg': Boolean,\n\n      // Hack: This is added because EAS CLI always includes the flag.\n      // If supplied, we'll do nothing with the value, but at least the process won't crash.\n      // Note that we also don't show this value in the `--help` prompt since we don't want people to use it.\n      '--experimental-bundle': <PERSON><PERSON><PERSON>,\n\n      // Aliases\n      '-h': '--help',\n      '-s': '--source-maps',\n      // '-d': '--dump-assetmap',\n      '-c': '--clear',\n      '-p': '--platform',\n      // Interop with Metro docs and RedBox errors.\n      '--reset-cache': '--clear',\n\n      // Deprecated\n      '--dump-sourcemap': '--source-maps',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Export the static files of the app for hosting it on a web server`,\n      chalk`npx expo export {dim <dir>}`,\n      [\n        chalk`<dir>                      Directory of the Expo project. {dim Default: Current working directory}`,\n        chalk`--output-dir <dir>         The directory to export the static files to. {dim Default: dist}`,\n        `--dev                      Configure static files for developing locally using a non-https server`,\n        `--no-minify                Prevent minifying source`,\n        `--no-bytecode              Prevent generating Hermes bytecode`,\n        `--max-workers <number>     Maximum number of tasks to allow the bundler to spawn`,\n        `--dump-assetmap            Emit an asset map for further processing`,\n        `--no-ssg                   Skip exporting static HTML files for web routes`,\n        chalk`-p, --platform <platform>  Options: android, ios, web, all. {dim Default: all}`,\n        `-s, --source-maps          Emit JavaScript source maps`,\n        `-c, --clear                Clear the bundler cache`,\n        `-h, --help                 Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const projectRoot = getProjectRoot(args);\n  const { resolveOptionsAsync } = await import('./resolveOptions.js');\n  const options = await resolveOptionsAsync(projectRoot, args).catch(logCmdError);\n\n  const { exportAsync } = await import('./exportAsync.js');\n  return exportAsync(projectRoot, options).catch(logCmdError);\n};\n"], "names": ["expoExport", "argv", "args", "assertArgs", "Boolean", "Number", "String", "printHelp", "chalk", "join", "projectRoot", "getProjectRoot", "resolveOptionsAsync", "options", "catch", "logCmdError", "exportAsync"], "mappings": ";;;;;+BAOaA;;;eAAAA;;;;gEANK;;;;;;sBAGoC;wBAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,aAAsB,OAAOC;IACxC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,WAAWA;QACX,mBAAmBA;QACnB,SAASA;QACT,iBAAiBA;QACjB,iBAAiBC;QACjB,gBAAgBC;QAChB,cAAc;YAACA;SAAO;QACtB,eAAeF;QACf,iBAAiBA;QACjB,YAAYA;QAEZ,gEAAgE;QAChE,sFAAsF;QACtF,uGAAuG;QACvG,yBAAyBA;QAEzB,UAAU;QACV,MAAM;QACN,MAAM;QACN,2BAA2B;QAC3B,MAAM;QACN,MAAM;QACN,6CAA6C;QAC7C,iBAAiB;QAEjB,aAAa;QACb,oBAAoB;IACtB,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBK,IAAAA,eAAS,EACP,CAAC,iEAAiE,CAAC,EACnEC,IAAAA,gBAAK,CAAA,CAAC,2BAA2B,CAAC,EAClC;YACEA,IAAAA,gBAAK,CAAA,CAAC,kGAAkG,CAAC;YACzGA,IAAAA,gBAAK,CAAA,CAAC,2FAA2F,CAAC;YAClG,CAAC,iGAAiG,CAAC;YACnG,CAAC,mDAAmD,CAAC;YACrD,CAAC,6DAA6D,CAAC;YAC/D,CAAC,gFAAgF,CAAC;YAClF,CAAC,mEAAmE,CAAC;YACrE,CAAC,0EAA0E,CAAC;YAC5EA,IAAAA,gBAAK,CAAA,CAAC,8EAA8E,CAAC;YACrF,CAAC,sDAAsD,CAAC;YACxD,CAAC,kDAAkD,CAAC;YACpD,CAAC,qCAAqC,CAAC;SACxC,CAACC,IAAI,CAAC;IAEX;IAEA,MAAMC,cAAcC,IAAAA,oBAAc,EAACT;IACnC,MAAM,EAAEU,mBAAmB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAC7C,MAAMC,UAAU,MAAMD,oBAAoBF,aAAaR,MAAMY,KAAK,CAACC,mBAAW;IAE9E,MAAM,EAAEC,WAAW,EAAE,GAAG,MAAM,mEAAA,QAAO;IACrC,OAAOA,YAAYN,aAAaG,SAASC,KAAK,CAACC,mBAAW;AAC5D"}