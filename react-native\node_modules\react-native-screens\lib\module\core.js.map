{"version": 3, "names": ["Platform", "UIManager", "isNativePlatformSupported", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "screensEnabled", "freezeEnabled"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAElD,OAAO,MAAMC,yBAAyB,GACpCF,QAAQ,CAACG,EAAE,KAAK,KAAK,IACrBH,QAAQ,CAACG,EAAE,KAAK,SAAS,IACzBH,QAAQ,CAACG,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGF,yBAAyB;AAE9C,OAAO,SAASG,aAAaA,CAAA,EAA6B;EAAA,IAA5BC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACtDH,cAAc,GAAGE,mBAAmB;EAEpC,IAAI,CAACJ,yBAAyB,EAAE;IAC9B;EACF;EAEA,IAAIE,cAAc,IAAI,CAACH,SAAS,CAACS,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACV,wGACH,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAEzB,OAAO,SAASC,YAAYA,CAAA,EAAiC;EAAA,IAAhCC,uBAAuB,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACzD,IAAI,CAACL,yBAAyB,EAAE;IAC9B;EACF;EAEAW,aAAa,GAAGE,uBAAuB;AACzC;AAEA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOZ,cAAc;AACvB;AAEA,OAAO,SAASa,aAAaA,CAAA,EAAG;EAC9B,OAAOJ,aAAa;AACtB"}