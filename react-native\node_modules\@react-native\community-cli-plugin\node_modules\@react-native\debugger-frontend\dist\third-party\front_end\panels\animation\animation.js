import*as e from"../../core/sdk/sdk.js";import*as t from"../../ui/components/icon_button/icon_button.js";import*as i from"../../ui/legacy/legacy.js";import*as n from"../../ui/visual_logging/visual_logging.js";import*as s from"../../core/common/common.js";import*as r from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as a from"../../core/platform/platform.js";import*as l from"../../ui/legacy/components/inline_editor/inline_editor.js";const d="devtools_animations",h="__devtools_report_scroll_position__",c=e=>`__devtools_scroll_listener_${e}__`;async function m(t,i){const n=t.domModel().target().model(e.ResourceTreeModel.ResourceTreeModel),s=t.domModel().target().pageAgent();for(const e of n.frames()){const{executionContextId:n}=await s.invoke_createIsolatedWorld({frameId:e.id,worldName:i}),r=await t.resolveToObject(void 0,n);if(r)return r}return null}class u{#e;#t;#i;static lastAddedListenerId=0;constructor(e){this.#e=e,this.#t=new Map}async#n(){if(this.#i)return;this.#i=e=>{const{name:t,payload:i}=e.data;if(t!==h)return;const{scrollTop:n,scrollLeft:s,id:r}=JSON.parse(i),o=this.#t.get(r);o&&o({scrollTop:n,scrollLeft:s})};const t=this.#e.domModel().target().model(e.RuntimeModel.RuntimeModel);await t.addBinding({name:h,executionContextName:d}),t.addEventListener(e.RuntimeModel.Events.BindingCalled,this.#i)}async#s(){if(!this.#i)return;const t=this.#e.domModel().target().model(e.RuntimeModel.RuntimeModel);await t.removeBinding({name:h}),t.removeEventListener(e.RuntimeModel.Events.BindingCalled,this.#i),this.#i=void 0}async addScrollEventListener(t){u.lastAddedListenerId++;const i=u.lastAddedListenerId;this.#t.set(i,t),this.#i||await this.#n();const n=await m(this.#e,d);return n?(await n.callFunction((function(e,t,i){if("scrollingElement"in this&&!this.scrollingElement)return;const n="scrollingElement"in this?this.scrollingElement:this;this[i]=()=>{globalThis[t](JSON.stringify({scrollTop:n.scrollTop,scrollLeft:n.scrollLeft,id:e}))},this.addEventListener("scroll",this[i],!0)}),[i,h,c(i)].map((t=>e.RemoteObject.RemoteObject.toCallArgument(t)))),n.release(),i):null}async removeScrollEventListener(t){const i=await m(this.#e,d);i&&(await i.callFunction((function(e){this.removeEventListener("scroll",this[e]),delete this[e]}),[c(t)].map((t=>e.RemoteObject.RemoteObject.toCallArgument(t)))),i.release(),this.#t.delete(t),0===this.#t.size&&await this.#s())}async scrollTop(){return this.#e.callFunction((function(){if("scrollingElement"in this)return this.scrollingElement?this.scrollingElement.scrollTop:0;return this.scrollTop})).then((e=>e?.value??null))}async scrollLeft(){return this.#e.callFunction((function(){if("scrollingElement"in this)return this.scrollingElement?this.scrollingElement.scrollLeft:0;return this.scrollLeft})).then((e=>e?.value??null))}async setScrollTop(e){await this.#e.callFunction((function(e){if("scrollingElement"in this){if(!this.scrollingElement)return;this.scrollingElement.scrollTop=e}else this.scrollTop=e}),[e])}async setScrollLeft(e){await this.#e.callFunction((function(e){if("scrollingElement"in this){if(!this.scrollingElement)return;this.scrollingElement.scrollLeft=e}else this.scrollLeft=e}),[e])}async verticalScrollRange(){return this.#e.callFunction((function(){if("scrollingElement"in this)return this.scrollingElement?this.scrollingElement.scrollHeight-this.scrollingElement.clientHeight:0;return this.scrollHeight-this.clientHeight})).then((e=>e?.value??null))}async horizontalScrollRange(){return this.#e.callFunction((function(){if("scrollingElement"in this)return this.scrollingElement?this.scrollingElement.scrollWidth-this.scrollingElement.clientWidth:0;return this.scrollWidth-this.clientWidth})).then((e=>e?.value??null))}}var p,f=Object.freeze({__proto__:null,AnimationDOMNode:u});class b extends e.SDKModel.SDKModel{runtimeModel;agent;#r;animationGroups;#o;playbackRate;#a;#l;constructor(t){super(t),this.runtimeModel=t.model(e.RuntimeModel.RuntimeModel),this.agent=t.animationAgent(),t.registerAnimationDispatcher(new T(this)),this.#r=new Map,this.animationGroups=new Map,this.#o=new Set,this.playbackRate=1;t.model(e.ResourceTreeModel.ResourceTreeModel).addEventListener(e.ResourceTreeModel.Events.PrimaryPageChanged,this.reset,this);const i=t.model(e.ScreenCaptureModel.ScreenCaptureModel);i&&(this.#a=new k(this,i))}reset(){this.#r.clear(),this.animationGroups.clear(),this.#o.clear(),this.dispatchEventToListeners(p.ModelReset)}async devicePixelRatio(){const e=await this.target().runtimeAgent().invoke_evaluate({expression:"window.devicePixelRatio"});return"number"===e?.result.type?e?.result.value??1:1}animationCreated(e){this.#o.add(e)}animationCanceled(e){this.#o.delete(e),this.flushPendingAnimationsIfNeeded()}async animationStarted(e){if(!e.source||!e.source.backendNodeId)return;if(e.viewOrScrollTimeline){const t=await this.devicePixelRatio();e.viewOrScrollTimeline.startOffset&&(e.viewOrScrollTimeline.startOffset/=t),e.viewOrScrollTimeline.endOffset&&(e.viewOrScrollTimeline.endOffset/=t)}const t=g.parsePayload(this,e);if(!t)return;const i=t.source().keyframesRule();"WebAnimation"===t.type()&&i&&0===i.keyframes().length?this.#o.delete(t.id()):(this.#r.set(t.id(),t),this.#o.add(t.id())),this.flushPendingAnimationsIfNeeded()}flushPendingAnimationsIfNeeded(){for(const e of this.#o)if(!this.#r.get(e))return;for(;this.#o.size;)this.matchExistingGroups(this.createGroupFromPendingAnimations())}matchExistingGroups(e){let t=null;for(const i of this.animationGroups.values())if(i.matches(e)){t=i,i.update(e);break}return t||(this.animationGroups.set(e.id(),e),this.#a&&this.#a.captureScreenshots(e.finiteDuration(),e.screenshotsInternal)),this.dispatchEventToListeners(p.AnimationGroupStarted,t||e),Boolean(t)}createGroupFromPendingAnimations(){console.assert(this.#o.size>0);const e=this.#o.values().next().value;this.#o.delete(e);const t=this.#r.get(e);if(!t)throw new Error("Unable to locate first animation");const i=e=>{const i=t.viewOrScrollTimeline(),n=e.viewOrScrollTimeline();return i?Boolean(n&&i.sourceNodeId===n.sourceNodeId&&i.axis===n.axis):!n&&t.startTime()===e.startTime()},n=[t],s=new Set;for(const e of this.#o){const t=this.#r.get(e);i(t)?n.push(t):s.add(e)}return this.#o=s,n.sort(((e,t)=>e.startTime()-t.startTime())),new x(this,e,n)}setPlaybackRate(e){this.playbackRate=e,this.agent.invoke_setPlaybackRate({playbackRate:e})}releaseAnimations(e){this.agent.invoke_releaseAnimations({animations:e})}async suspendModel(){this.reset(),await this.agent.invoke_disable()}async resumeModel(){this.#l&&await this.agent.invoke_enable()}async ensureEnabled(){this.#l||(await this.agent.invoke_enable(),this.#l=!0)}}!function(e){e.AnimationGroupStarted="AnimationGroupStarted",e.ModelReset="ModelReset"}(p||(p={}));class g{#d;#h;#c;#m;constructor(e,t){this.#d=e,this.#h=t,this.#c=new y(e,this.#h.source)}static parsePayload(e,t){return new g(e,t)}percentageToPixels(e,t){const{startOffset:i,endOffset:n}=t;if(void 0===i||void 0===n)throw new Error("startOffset or endOffset does not exist in viewOrScrollTimeline");return e/100*(n-i)}viewOrScrollTimeline(){return this.#h.viewOrScrollTimeline}payload(){return this.#h}id(){return this.#h.id}name(){return this.#h.name}paused(){return this.#h.pausedState}playState(){return this.#m||this.#h.playState}setPlayState(e){this.#m=e}playbackRate(){return this.#h.playbackRate}startTime(){const e=this.viewOrScrollTimeline();return e?this.percentageToPixels(this.playbackRate()>0?this.#h.startTime:100-this.#h.startTime,e)+(this.viewOrScrollTimeline()?.startOffset??0):this.#h.startTime}iterationDuration(){const e=this.viewOrScrollTimeline();return e?this.percentageToPixels(this.source().duration(),e):this.source().duration()}endTime(){return this.source().iterations?this.viewOrScrollTimeline()?this.startTime()+this.iterationDuration()*this.source().iterations():this.startTime()+this.source().delay()+this.source().duration()*this.source().iterations()+this.source().endDelay():1/0}finiteDuration(){const e=Math.min(this.source().iterations(),3);return this.viewOrScrollTimeline()?this.iterationDuration()*e:this.source().delay()+this.source().duration()*e}currentTime(){const e=this.viewOrScrollTimeline();return e?this.percentageToPixels(this.#h.currentTime,e):this.#h.currentTime}source(){return this.#c}type(){return this.#h.type}overlaps(e){if(!this.source().iterations()||!e.source().iterations())return!0;const t=this.startTime()<e.startTime()?this:e,i=t===this?e:this;return t.endTime()>=i.startTime()}delayOrStartTime(){return this.viewOrScrollTimeline()?this.startTime():this.source().delay()}setTiming(e,t){this.#c.node().then((i=>{if(!i)throw new Error("Unable to find node");this.updateNodeStyle(e,t,i)})),this.#c.durationInternal=e,this.#c.delayInternal=t,this.#d.agent.invoke_setTiming({animationId:this.id(),duration:e,delay:t})}updateNodeStyle(e,t,i){let n;if("CSSTransition"===this.type())n="transition-";else{if("CSSAnimation"!==this.type())return;n="animation-"}if(!i.id)throw new Error("Node has no id");const s=i.domModel().cssModel();s.setEffectivePropertyValueForNode(i.id,n+"duration",e+"ms"),s.setEffectivePropertyValueForNode(i.id,n+"delay",t+"ms")}async remoteObjectPromise(){const e=await this.#d.agent.invoke_resolveAnimation({animationId:this.id()});return e?this.#d.runtimeModel.createRemoteObject(e.remoteObject):null}cssId(){return this.#h.cssId||""}}class y{#d;#u;#p;delayInternal;durationInternal;#f;constructor(e,t){this.#d=e,this.#u=t,t.keyframesRule&&(this.#p=new v(t.keyframesRule)),this.delayInternal=this.#u.delay,this.durationInternal=this.#u.duration}delay(){return this.delayInternal}endDelay(){return this.#u.endDelay}iterationStart(){return this.#u.iterationStart}iterations(){return this.delay()||this.endDelay()||this.duration()?this.#u.iterations||1/0:0}duration(){return this.durationInternal}direction(){return this.#u.direction}fill(){return this.#u.fill}node(){return this.#f||(this.#f=new e.DOMModel.DeferredDOMNode(this.#d.target(),this.backendNodeId())),this.#f.resolvePromise()}deferredNode(){return new e.DOMModel.DeferredDOMNode(this.#d.target(),this.backendNodeId())}backendNodeId(){return this.#u.backendNodeId}keyframesRule(){return this.#p||null}easing(){return this.#u.easing}}class v{#u;#b;constructor(e){this.#u=e,this.#b=this.#u.keyframes.map((function(e){return new w(e)}))}setKeyframesPayload(e){this.#b=e.map((function(e){return new w(e)}))}name(){return this.#u.name}keyframes(){return this.#b}}class w{#u;#g;constructor(e){this.#u=e,this.#g=this.#u.offset}offset(){return this.#g}setOffset(e){this.#g=100*e+"%"}offsetAsNumber(){return parseFloat(this.#g)/100}easing(){return this.#u.easing}}class x{#d;#y;#v;#w;#x;screenshotsInternal;#T;constructor(e,t,i){this.#d=e,this.#y=t,this.#w=i,this.#x=!1,this.screenshotsInternal=[],this.#T=[]}isScrollDriven(){return Boolean(this.#w[0]?.viewOrScrollTimeline())}id(){return this.#y}animations(){return this.#w}release(){this.#d.animationGroups.delete(this.id()),this.#d.releaseAnimations(this.animationIds())}animationIds(){return this.#w.map((function(e){return e.id()}))}startTime(){return this.#w[0].startTime()}groupDuration(){let e=0;for(const t of this.#w)e=Math.max(e,t.delayOrStartTime()+t.iterationDuration());return e}finiteDuration(){let e=0;for(let t=0;t<this.#w.length;++t)e=Math.max(e,this.#w[t].finiteDuration());return e}scrollOrientation(){const e=this.#w[0]?.viewOrScrollTimeline();return e?e.axis:null}async scrollNode(){if(this.#v)return this.#v;if(!this.isScrollDriven())return null;const t=this.#w[0]?.viewOrScrollTimeline()?.sourceNodeId;if(!t)return null;const i=new e.DOMModel.DeferredDOMNode(this.#d.target(),t),n=await i.resolvePromise();return n?(this.#v=new u(n),this.#v):null}seekTo(e){this.#d.agent.invoke_seekAnimations({animations:this.animationIds(),currentTime:e})}paused(){return this.#x}togglePause(e){e!==this.#x&&(this.#x=e,this.#d.agent.invoke_setPaused({animations:this.animationIds(),paused:e}))}currentTimePromise(){let e=null;for(const t of this.#w)(!e||t.endTime()>e.endTime())&&(e=t);if(!e)throw new Error("No longest animation found");return this.#d.agent.invoke_getCurrentTime({id:e.id()}).then((({currentTime:e})=>e||0))}matches(e){function t(e){const t=(e.viewOrScrollTimeline()?.sourceNodeId??"")+(e.viewOrScrollTimeline()?.axis??"");return("WebAnimation"===e.type()?e.type()+e.id():e.cssId())+t}if(this.#w.length!==e.#w.length)return!1;const i=this.#w.map(t).sort(),n=e.#w.map(t).sort();for(let e=0;e<i.length;e++)if(i[e]!==n[e])return!1;return!0}update(e){this.#d.releaseAnimations(this.animationIds()),this.#w=e.#w,this.#v=void 0}screenshots(){for(let e=0;e<this.screenshotsInternal.length;++e){const t=new Image;t.src="data:image/jpeg;base64,"+this.screenshotsInternal[e],this.#T.push(t)}return this.screenshotsInternal=[],this.#T}}class T{#d;constructor(e){this.#d=e}animationCreated({id:e}){this.#d.animationCreated(e)}animationCanceled({id:e}){this.#d.animationCanceled(e)}animationStarted({animation:e}){this.#d.animationStarted(e)}}class k{#k;#A;#d;#I;#M;#S;constructor(e,t){this.#k=[],this.#A=t,this.#d=e,this.#d.addEventListener(p.ModelReset,this.stopScreencast,this)}captureScreenshots(e,t){const i=Math.min(e/this.#d.playbackRate,3e3),n=i+window.performance.now();this.#k.push({endTime:n,screenshots:t}),(!this.#M||n>this.#M)&&(clearTimeout(this.#I),this.#I=window.setTimeout(this.stopScreencast.bind(this),i),this.#M=n),this.#S||(this.#S=!0,this.#A.startScreencast("jpeg",80,void 0,300,2,this.screencastFrame.bind(this),(e=>{})))}screencastFrame(e,t){if(!this.#S)return;const i=window.performance.now();this.#k=this.#k.filter((function(e){return e.endTime>=i}));for(const t of this.#k)t.screenshots.push(e)}stopScreencast(){this.#S&&(this.#I=void 0,this.#M=void 0,this.#k=[],this.#S=!1,this.#A.stopScreencast())}}e.SDKModel.SDKModel.register(b,{capabilities:2,autostart:!1});var A=Object.freeze({__proto__:null,AnimationModel:b,get Events(){return p},AnimationImpl:g,AnimationEffect:y,KeyframesRule:v,KeyframeStyle:w,AnimationGroup:x,AnimationDispatcher:T,ScreenshotCapture:k});const I=new CSSStyleSheet;I.replaceSync("img{max-height:300px;border-radius:2px}.animation-progress{position:absolute;height:2px;bottom:0;left:0;background:var(--legacy-selection-bg-color)}\n/*# sourceURL=animationScreenshotPopover.css */\n");class M extends i.Widget.VBox{#C;#P;#E;#R;#L;#B;constructor(e){super(!0),console.assert(e.length>0),this.contentElement.classList.add("animation-screenshot-popover"),this.#C=e;for(const t of e)this.contentElement.appendChild(t),t.style.display="none";this.#P=0,this.#E=0,this.#C[0].style.display="block",this.#R=this.contentElement.createChild("div","animation-progress")}wasShown(){this.#P=this.contentElement.window().requestAnimationFrame(this.changeFrame.bind(this)),this.registerCSSFiles([I])}willHide(){this.contentElement.window().cancelAnimationFrame(this.#P),this.#B=void 0}changeFrame(){if(this.#P=this.contentElement.window().requestAnimationFrame(this.changeFrame.bind(this)),this.#B)return void this.#B--;if(this.#L=!this.#L,!this.#L)return;const e=this.#C.length;this.#C[this.#E%e].style.display="none",this.#E++,this.#C[this.#E%e].style.display="block",this.#E%e==e-1&&(this.#B=50),this.#R.style.width=(this.#E%e+1)/e*100+"%"}}var S=Object.freeze({__proto__:null,AnimationScreenshotPopover:M});const C=new CSSStyleSheet;C.replaceSync(':host{overflow:hidden;--timeline-controls-width:150px}.animation-node-row{width:100%;display:flex;border-bottom:1px dashed var(--sys-color-divider)}.animation-node-description{padding-left:8px;overflow:hidden;position:relative;background-color:var(--sys-color-cdt-base-container);display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;white-space:nowrap;flex:0 0 var(--timeline-controls-width)}.animation-node-description > *{flex:0 0 auto}.animation-timeline-row{height:32px;position:relative}path.animation-keyframe{fill-opacity:0.2}.animation-node-selected path.animation-keyframe,\nsvg.animation-ui g:first-child:hover path.animation-keyframe{fill-opacity:0.4}line.animation-line{stroke-width:2px;stroke-linecap:round;fill:none}line.animation-delay-line{stroke-width:2px;stroke-dasharray:6,4}line.animation-delay-line.animation-fill{stroke-dasharray:none}circle.animation-keyframe-point{fill:var(--sys-color-cdt-base-container)}circle.animation-endpoint,\ncircle.animation-keyframe-point{stroke-width:2px;transition:transform 100ms cubic-bezier(0,0,0.2,1);transform:scale(1);transform-box:fill-box;transform-origin:50% 50%}circle.animation-endpoint:active,\ncircle.animation-keyframe-point:active{transform:scale(1)}.animation-ui circle.animation-endpoint:hover,\n.animation-ui circle.animation-keyframe-point:hover{transform:scale(1.2)}.animation-name{position:absolute;top:8px;color:var(--sys-color-on-surface);text-align:center;margin-left:-8px;white-space:nowrap}.animation-timeline-toolbar-container{display:flex;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);flex:0 0 auto}.animation-timeline-toolbar{display:inline-block}.animation-timeline-header{height:28px;border-bottom:1px solid var(--sys-color-divider);flex-shrink:0;display:flex}.animation-timeline-header::after{content:"";height:calc(100% - 48px - 28px);position:absolute;width:var(--timeline-controls-width);left:0;margin-top:28px;background-color:var(--sys-color-cdt-base-container);z-index:0;border-right:1px solid var(--sys-color-divider)}.animation-controls{flex:0 0 var(--timeline-controls-width);position:relative;display:flex;justify-content:flex-end;padding-right:8px}.animation-timeline-current-time{flex:0 0 auto;line-height:28px;margin-right:5px}.animation-grid-header{flex:1 0 auto;z-index:2}.animation-grid-header.scrubber-enabled{cursor:pointer}.animation-timeline-buffer,\n.animation-timeline-buffer-hint{height:48px;flex:0 0 auto;border-bottom:1px solid var(--sys-color-divider);display:flex;padding:0 2px}.animation-timeline-buffer:empty,\n.animation-timeline-buffer-hint{display:none}.animation-timeline-buffer:empty ~ .animation-timeline-buffer-hint{align-items:center;justify-content:center;font-size:14px;z-index:101;display:flex}.animation-time-overlay{background-color:var(--sys-color-on-surface);opacity:5%;position:absolute;height:100%;width:100%;z-index:-1}.animation-timeline-end > .animation-time-overlay{visibility:hidden}.animation-scrubber{opacity:100%;position:absolute;left:10px;height:100%;width:100%;top:28px;border-left:1px solid var(--sys-color-error);z-index:2}.animation-scrubber-line{width:11px;background:linear-gradient(to right,transparent 5px,var(--sys-color-error) 5px,var(--sys-color-error) 6px,transparent 6px);position:absolute;top:-28px;height:28px;left:-6px;padding:0 5px;z-index:3}.animation-scrubber-head{width:7px;height:7px;transform:rotate(45deg);background:var(--sys-color-error);position:absolute;left:2px;top:1px;z-index:4}.grid-overflow-wrapper{position:absolute;left:calc(var(--timeline-controls-width) - 10px);top:76px;z-index:1;overflow:hidden;width:100%;height:100%}svg.animation-timeline-grid{position:absolute;left:0;top:0;right:0;bottom:0;width:100%;height:100%}rect.animation-timeline-grid-line{fill:var(--sys-color-divider)}.animation-timeline-row > svg.animation-ui{position:absolute}.animation-node-timeline{flex-grow:1}.animation-node-description > div{position:absolute;top:50%;transform:translateY(-50%);max-height:100%}.animation-node-removed{filter:saturate(0);cursor:not-allowed}.animation-node-removed-overlay{width:100%;height:100%;z-index:100;cursor:not-allowed}svg.animation-ui g:first-child{opacity:100%}svg.animation-ui circle:focus-visible,\nsvg.animation-ui path:focus-visible{outline:2px solid -webkit-focus-ring-color}.animation-tail-iterations{opacity:50%}.animation-keyframe-step line{stroke-width:2;stroke-opacity:0.3}text.animation-timeline-grid-label{font-size:10px;fill:var(--sys-color-token-subtle);text-anchor:middle}@keyframes --full-opacity-for-screenshots-container{from{opacity:100%}to{opacity:100%}}.preview-ui-container{position:relative;& .screenshot-arrow{background-image:var(--image-file-popoverArrows);background-position:0 76px;width:19px;height:19px;position:absolute;left:6px;top:-19px;z-index:100;pointer-events:none}& .screenshots-container{position:absolute;display:none;opacity:0%;left:6px;top:100%;z-index:100;border:1px solid transparent;box-shadow:var(--drop-shadow);border-radius:2px;max-width:220px;max-height:220px}& .screenshots-container.to-the-left{left:unset;right:6px}& .screenshots-container.to-the-left .screenshot-arrow{left:unset;right:6px}&:hover .screenshots-container:not(.no-screenshots){display:block;animation-name:--full-opacity-for-screenshots-container;animation-duration:0s;animation-delay:0.2s;animation-fill-mode:forwards}&:hover .screenshots-container:not(.no-screenshots):hover{display:none}&:has(.selected):hover .screenshots-container:not(.no-screenshots){display:none}}.animation-timeline-rows,\n.animation-timeline-rows-hint{flex-grow:1;overflow-y:auto;z-index:1;overflow-x:hidden}.animation-timeline-rows-hint{display:none}.animation-timeline-buffer:not(:empty) ~ .animation-timeline-rows:empty{flex-grow:0}.animation-timeline-buffer:not(:empty) ~ .animation-timeline-rows:empty ~ .animation-timeline-rows-hint{font-size:14px;display:flex;align-items:center;justify-content:center;margin-left:var(--timeline-controls-width);padding:10px}.toolbar.animation-controls-toolbar{flex:0 0 auto}.animation-node-row.animation-node-selected{background-color:var(--sys-color-state-ripple-primary)}.animation-node-selected > .animation-node-description{background-color:var(--sys-color-tonal-container)}.animation-buffer-preview{height:40px;margin:4px 2px;background-color:var(--sys-color-neutral-container);border:1px solid transparent;border-radius:2px;flex:1 1;padding:4px;max-width:100px;animation:newGroupAnim 200ms;position:relative}.animation-buffer-preview.no-animation{animation:none}.animation-buffer-preview .mouse-icon{position:absolute;width:14px;height:14px;right:1px;bottom:2px;opacity:60%}.animation-buffer-preview-animation{width:100%;height:100%;border-radius:2px 0 0 2px;position:absolute;top:0;left:0;background:var(--sys-color-tonal-container);opacity:0%;border-right:1px solid var(--sys-color-divider)}.animation-buffer-preview:focus-visible{outline:-webkit-focus-ring-color auto 5px}.animation-buffer-preview.selected .mouse-icon{opacity:100%}.animation-buffer-preview:not(.selected):focus-visible,\n.animation-buffer-preview:not(.selected):hover{background-color:var(--sys-color-surface-variant);& .mouse-icon{opacity:80%}}.animation-buffer-preview.selected{background-color:var(--sys-color-tonal-container)}.animation-paused{align-items:center;justify-content:center;display:none}.animation-paused::before,\n.animation-paused::after{content:"";background:var(--sys-color-cdt-base-container);width:7px;height:20px;border-radius:2px;margin:2px;border:1px solid var(--sys-color-divider)}.animation-buffer-preview.paused .animation-paused{display:flex}.animation-buffer-preview > svg > line{stroke-width:1px}.animation-buffer-preview.selected > svg > line{stroke:var(--sys-color-on-tonal-container)!important}@keyframes newGroupAnim{from{clip-path:polygon(0% 0%,0% 100%,50% 100%,50% 0%)}to{clip-path:polygon(0% 0%,0% 100%,100% 100%,100% 0%)}}.animation-playback-rate-control{margin:4px 0 4px 2px;display:flex;width:120px}.animation-playback-rate-button{border-width:1px;border-style:solid;border-color:var(--sys-color-tonal-outline);border-right-width:0;color:var(--sys-color-on-surface);display:inline-block;margin-right:-1px;padding:1px 4px;background-color:transparent;flex:1 0 auto;text-align:center}.animation-playback-rate-button:first-child{border-radius:4px 0 0 4px}.animation-playback-rate-button:last-child{border-radius:0 4px 4px 0;border-right-width:1px}.animation-playback-rate-button.selected{color:var(--sys-color-on-tonal-container);background-color:var(--sys-color-tonal-container);border-color:var(--sys-color-tonal-container);z-index:1}.animation-playback-rate-button.selected:focus-visible{color:var(--sys-color-on-surface)}.animation-playback-rate-button:focus-visible{outline:2px solid var(--sys-color-primary);outline-offset:2px}.animation-playback-rate-button:not(.selected):not([disabled]):hover{background:var(--sys-color-state-hover-on-subtle)}.animation-playback-rate-button[disabled]{background:unset;border-color:var(--sys-color-state-disabled);color:var(--sys-color-state-disabled)}.animation-remove-button{position:absolute;top:-3px;right:-3px;background:var(--sys-color-token-subtle);border-radius:12px;border:0;height:16px;width:16px;z-index:100;display:none;padding:0;& > devtools-icon{height:16px;width:16px;color:var(--sys-color-cdt-base-container)}&:hover{background-color:var(--sys-color-on-surface)}}.animation-buffer-preview:hover .animation-remove-button{display:flex}.timeline-controls-resizer{position:absolute;width:6px;height:100%;left:var(--timeline-controls-width);top:104px;z-index:3;margin-left:-4px}@media (forced-colors: active){.animation-playback-rate-button.selected,\n  .animation-playback-rate-button.selected:first-child,\n  .animation-playback-rate-button.selected:first-child:focus-visible,\n  .animation-playback-rate-button:focus-visible{forced-color-adjust:none;color:HighlightText;background-color:Highlight}.animation-node-description:focus-visible{background-color:var(--sys-color-cdt-base-container);forced-color-adjust:none}.monospace{forced-color-adjust:auto}}\n/*# sourceURL=animationTimeline.css */\n');const P={selectAnEffectAboveToInspectAnd:"Select an effect above to inspect and modify.",clearAll:"Clear all",pauseAll:"Pause all",playbackRates:"Playback rates",playbackRatePlaceholder:"{PH1}%",pause:"Pause",setSpeedToS:"Set speed to {PH1}",animationPreviews:"Animation previews",waitingForAnimations:"Waiting for animations...",replayTimeline:"Replay timeline",resumeAll:"Resume all",playTimeline:"Play timeline",pauseTimeline:"Pause timeline",animationPreviewS:"Animation Preview {PH1}"},E=o.i18n.registerUIStrings("panels/animation/AnimationTimeline.ts",P),R=o.i18n.getLocalizedString.bind(void 0,E),L=new WeakMap,B=new WeakMap;let G;class D extends i.Widget.VBox{#G;#D;#O;#N;#F=[];#U;#_;#z;#j;#H;#W;#K;#V;#X;#$;#q;#Q;#J;#Y;#Z;#ee;#te;#ie;#ne;#se;#re;#oe;#ae;#le;#de;#he;#ce;#me;#ue;#pe;#fe;#be=new s.Throttler.Throttler(10);#ge=!1;constructor(){super(!0),this.element.classList.add("animations-timeline"),this.element.setAttribute("jslog",`${n.panel("animations").track({resize:!0})}`),this.#me=this.contentElement.createChild("div","timeline-controls-resizer"),this.#G=this.contentElement.createChild("div","grid-overflow-wrapper"),this.#D=i.UIUtils.createSVGChild(this.#G,"svg","animation-timeline-grid"),this.#O=1,this.#N=!1,this.#he=!1,this.createHeader(),this.#U=this.contentElement.createChild("div","animation-timeline-rows");this.contentElement.createChild("div","animation-timeline-rows-hint").textContent=R(P.selectAnEffectAboveToInspectAnd),this.#X=100,this.#$=this.#X,this.#Q=new Map,this.#J=[],this.#Y=[],this.#fe=[],this.#Z=new Map,this.#ee=new Map,this.#q=150,this.element.style.setProperty("--timeline-controls-width",`${this.#q}px`),e.TargetManager.TargetManager.instance().addModelListener(e.DOMModel.DOMModel,e.DOMModel.Events.NodeRemoved,(e=>this.markNodeAsRemoved(e.data.node)),this,{scoped:!0}),e.TargetManager.TargetManager.instance().observeModels(b,this,{scoped:!0}),i.Context.Context.instance().addFlavorChangeListener(e.DOMModel.DOMNode,this.nodeChanged,this),this.#ye()}static instance(e){return G&&!e?.forceNew||(G=new D),G}#ye(){let e;i.UIUtils.installDragHandle(this.#me,(t=>(e=t.clientX,!0)),(t=>{if(void 0===e)return;const i=this.#q+t.clientX-e;this.#q=Math.min(Math.max(i,120),720),e=t.clientX,this.element.style.setProperty("--timeline-controls-width",this.#q+"px"),this.onResize()}),(()=>{e=void 0}),"ew-resize")}get previewMap(){return this.#Z}get uiAnimations(){return this.#J}get groupBuffer(){return this.#Y}wasShown(){if(!this.#ge){for(const t of e.TargetManager.TargetManager.instance().models(b,{scoped:!0}))this.addEventListeners(t);this.registerCSSFiles([C]),this.#ge=!0}}modelAdded(e){this.isShowing()&&this.addEventListeners(e)}modelRemoved(e){this.removeEventListeners(e)}addEventListeners(e){e.ensureEnabled(),e.addEventListener(p.AnimationGroupStarted,this.animationGroupStarted,this),e.addEventListener(p.ModelReset,this.reset,this)}removeEventListeners(e){e.removeEventListener(p.AnimationGroupStarted,this.animationGroupStarted,this),e.removeEventListener(p.ModelReset,this.reset,this)}nodeChanged(){for(const e of this.#Q.values())e.nodeChanged()}createScrubber(){return this.#j=document.createElement("div"),this.#j.classList.add("animation-scrubber"),this.#j.classList.add("hidden"),this.#te=this.#j.createChild("div","animation-scrubber-line"),this.#te.createChild("div","animation-scrubber-head"),this.#j.createChild("div","animation-time-overlay"),this.#j}createHeader(){const e=this.contentElement.createChild("div","animation-timeline-toolbar-container");e.setAttribute("jslog",`${n.toolbar()}`);const t=new i.Toolbar.Toolbar("animation-timeline-toolbar",e);this.#W=new i.Toolbar.ToolbarButton(R(P.clearAll),"clear",void 0,"animations.clear"),this.#W.addEventListener("Click",(()=>{r.userMetrics.actionTaken(r.UserMetrics.Action.AnimationGroupsCleared),this.reset()})),t.appendToolbarItem(this.#W),t.appendSeparator(),this.#ie=new i.Toolbar.ToolbarToggle(R(P.pauseAll),"pause","resume","animations.pause-resume-all"),this.#ie.addEventListener("Click",(()=>{this.togglePauseAll()})),t.appendToolbarItem(this.#ie);const s=e.createChild("div","animation-playback-rate-control");s.addEventListener("keydown",this.handlePlaybackRateControlKeyDown.bind(this)),i.ARIAUtils.markAsListBox(s),i.ARIAUtils.setLabel(s,R(P.playbackRates)),this.#_=[];for(const e of O){const t=s.createChild("button","animation-playback-rate-button");t.textContent=e?R(P.playbackRatePlaceholder,{PH1:100*e}):R(P.pause),t.setAttribute("jslog",`${n.action().context("animations.playback-rate-"+100*e).track({click:!0})}`),B.set(t,e),t.addEventListener("click",this.setPlaybackRate.bind(this,e)),i.ARIAUtils.markAsOption(t),i.Tooltip.Tooltip.install(t,R(P.setSpeedToS,{PH1:t.textContent})),t.tabIndex=-1,this.#_.push(t)}this.updatePlaybackControls(),this.#z=this.contentElement.createChild("div","animation-timeline-buffer"),i.ARIAUtils.markAsListBox(this.#z),i.ARIAUtils.setLabel(this.#z,R(P.animationPreviews));this.contentElement.createChild("div","animation-timeline-buffer-hint").textContent=R(P.waitingForAnimations);const o=this.contentElement.createChild("div","animation-timeline-header"),a=o.createChild("div","animation-controls");this.#H=a.createChild("div","animation-timeline-current-time monospace");const l=new i.Toolbar.Toolbar("animation-controls-toolbar",a);return this.#ne=new i.Toolbar.ToolbarButton(R(P.replayTimeline),"replay",void 0,"animations.play-replay-pause-animation-group"),this.#ne.element.classList.add("toolbar-state-on"),this.#se="replay-outline",this.#ne.addEventListener("Click",this.controlButtonToggle.bind(this)),l.appendToolbarItem(this.#ne),this.#ue=o.createChild("div","animation-grid-header"),this.#ue.setAttribute("jslog",`${n.timeline("animations.grid-header").track({drag:!0,click:!0})}`),i.UIUtils.installDragHandle(this.#ue,this.scrubberDragStart.bind(this),this.scrubberDragMove.bind(this),this.scrubberDragEnd.bind(this),null),this.#G.appendChild(this.createScrubber()),this.clearCurrentTimeText(),o}handlePlaybackRateControlKeyDown(e){switch(e.key){case"ArrowLeft":case"ArrowUp":this.focusNextPlaybackRateButton(e.target,!0);break;case"ArrowRight":case"ArrowDown":this.focusNextPlaybackRateButton(e.target)}}focusNextPlaybackRateButton(e,t){const i=e,n=this.#_.indexOf(i),s=t?n-1:n+1;if(s<0||s>=this.#_.length)return;const r=this.#_[s];r.tabIndex=0,r.focus(),e&&(e.tabIndex=-1)}togglePauseAll(){this.#N=!this.#N,r.userMetrics.actionTaken(this.#N?r.UserMetrics.Action.AnimationsPaused:r.UserMetrics.Action.AnimationsResumed),this.#ie&&this.#ie.setToggled(this.#N),this.setPlaybackRate(this.#O),this.#ie&&this.#ie.setTitle(this.#N?R(P.resumeAll):R(P.pauseAll))}setPlaybackRate(t){t!==this.#O&&r.userMetrics.animationPlaybackRateChanged(.1===t?2:.25===t?1:1===t?0:3),this.#O=t;for(const t of e.TargetManager.TargetManager.instance().models(b,{scoped:!0}))t.setPlaybackRate(this.#N?0:this.#O);r.userMetrics.actionTaken(r.UserMetrics.Action.AnimationsPlaybackRateChanged),this.#ae&&(this.#ae.playbackRate=this.effectivePlaybackRate()),this.updatePlaybackControls()}updatePlaybackControls(){for(const e of this.#_){const t=this.#O===B.get(e);e.classList.toggle("selected",t),e.tabIndex=t?0:-1}}controlButtonToggle(){"play-outline"===this.#se?this.togglePause(!1):"replay-outline"===this.#se?(r.userMetrics.actionTaken(r.UserMetrics.Action.AnimationGroupReplayed),this.replay()):this.togglePause(!0)}updateControlButton(){this.#ne&&(this.#ne.setEnabled(Boolean(this.#K)&&this.hasAnimationGroupActiveNodes()&&!this.#K?.isScrollDriven()),this.#K&&this.#K.paused()?(this.#se="play-outline",this.#ne.element.classList.toggle("toolbar-state-on",!0),this.#ne.setTitle(R(P.playTimeline)),this.#ne.setGlyph("play")):!this.#ae||!this.#ae.currentTime||"number"!=typeof this.#ae.currentTime||this.#ae.currentTime>=this.duration()?(this.#se="replay-outline",this.#ne.element.classList.toggle("toolbar-state-on",!0),this.#ne.setTitle(R(P.replayTimeline)),this.#ne.setGlyph("replay")):(this.#se="pause-outline",this.#ne.element.classList.toggle("toolbar-state-on",!1),this.#ne.setTitle(R(P.pauseTimeline)),this.#ne.setGlyph("pause")))}effectivePlaybackRate(){return this.#N||this.#K&&this.#K.paused()?0:this.#O}togglePause(e){if(this.#K){this.#K.togglePause(e);const t=this.#Z.get(this.#K);t&&t.element.classList.toggle("paused",e)}this.#ae&&(this.#ae.playbackRate=this.effectivePlaybackRate()),this.updateControlButton()}replay(){this.#K&&this.hasAnimationGroupActiveNodes()&&!this.#K.isScrollDriven()&&(this.#K.seekTo(0),this.animateTime(0),this.updateControlButton())}duration(){return this.#$}setDuration(e){this.#$=e,this.scheduleRedraw()}clearTimeline(){this.#K&&this.#pe&&this.#K.scrollNode().then((e=>{e?.removeScrollEventListener(this.#pe),this.#pe=void 0})),this.#J=[],this.#Q.clear(),this.#ee.clear(),this.#U.removeChildren(),this.#$=this.#X,this.#j.classList.add("hidden"),this.#ue.classList.remove("scrubber-enabled"),this.#K=null,this.#ae&&this.#ae.cancel(),this.#ae=void 0,this.clearCurrentTimeText(),this.updateControlButton()}reset(){this.clearTimeline(),this.setPlaybackRate(this.#O);for(const e of this.#Y)e.release();this.#Y=[],this.clearPreviews(),this.renderGrid()}animationGroupStarted({data:e}){this.addAnimationGroup(e)}clearPreviews(){this.#Z.clear(),this.#F.forEach((e=>{e.detach()})),this.#z.removeChildren(),this.#F=[]}createPreview(e){const t=new X(e),n=document.createElement("div");n.classList.add("preview-ui-container"),n.appendChild(t.element);const s=document.createElement("div");if(s.classList.add("screenshots-container","no-screenshots"),s.createChild("span","screenshot-arrow"),s.addEventListener("animationend",(()=>{const{right:e,left:t,width:i}=s.getBoundingClientRect();e>window.innerWidth&&t-i>=0&&s.classList.add("to-the-left")})),n.appendChild(s),this.#Y.push(e),this.#Z.set(e,t),this.#z.appendChild(n),t.removeButton().addEventListener("click",this.removeAnimationGroup.bind(this,e)),t.element.addEventListener("click",this.selectAnimationGroup.bind(this,e)),t.element.addEventListener("keydown",this.handleAnimationGroupKeyDown.bind(this,e)),t.element.addEventListener("mouseover",(()=>{const t=e.screenshots();if(!t.length)return;s.classList.remove("no-screenshots");const i=()=>{const e=new M(t);this.#F.push(e),e.show(s)};t[0].complete?i():t[0].onload=i}),{once:!0}),i.ARIAUtils.setLabel(t.element,R(P.animationPreviewS,{PH1:this.#Y.indexOf(e)+1})),i.ARIAUtils.markAsOption(t.element),1===this.#Z.size){const e=this.#Z.get(this.#Y[0]);e&&(e.element.tabIndex=0)}}previewsCreatedForTest(){}createPreviewForCollectedGroups(){this.#fe.sort(((e,t)=>e.isScrollDriven()&&!t.isScrollDriven()?-1:!e.isScrollDriven()&&t.isScrollDriven()?1:e.startTime()!==t.startTime()?e.startTime()-t.startTime():e.animations.length-t.animations.length));for(const e of this.#fe)this.createPreview(e);this.#fe=[],this.previewsCreatedForTest()}addAnimationGroup(e){const t=this.#Z.get(e);if(t)return void(this.#K===e?this.syncScrubber():t.replay());this.#Y.sort(((e,t)=>e.startTime()-t.startTime()));const i=[],n=this.width()/50;for(;this.#Y.length>n;){const e=this.#Y.splice(this.#Y[0]===this.#K?1:0,1);i.push(e[0])}for(const e of i){const t=this.#Z.get(e);t&&(t.element.remove(),this.#Z.delete(e),e.release())}this.#fe.push(e),this.#be.schedule((()=>Promise.resolve(this.createPreviewForCollectedGroups())))}handleAnimationGroupKeyDown(e,t){switch(t.key){case"Backspace":case"Delete":this.removeAnimationGroup(e,t);break;case"ArrowLeft":case"ArrowUp":this.focusNextGroup(e,t.target,!0);break;case"ArrowRight":case"ArrowDown":this.focusNextGroup(e,t.target)}}focusNextGroup(e,t,i){const n=this.#Y.indexOf(e),s=i?n-1:n+1;if(s<0||s>=this.#Y.length)return;const r=this.#Z.get(this.#Y[s]);r&&(r.element.tabIndex=0,r.element.focus()),t&&(t.tabIndex=-1)}removeAnimationGroup(e,t){const i=this.#Y.indexOf(e);a.ArrayUtilities.removeElement(this.#Y,e);const n=this.#Z.get(e);n&&n.element.remove(),this.#Z.delete(e),e.release(),t.consume(!0),this.#K===e&&(this.clearTimeline(),this.renderGrid());if(0===this.#Y.length)return void this.#W.element.focus();const s=i>=this.#Y.length?this.#Z.get(this.#Y[this.#Y.length-1]):this.#Z.get(this.#Y[i]);s&&(s.element.tabIndex=0,s.element.focus())}clearCurrentTimeText(){this.#H.textContent=""}setCurrentTimeText(e){this.#K&&(this.#H.textContent=this.#K?.isScrollDriven()?`${e.toFixed(0)}px`:o.TimeUtilities.millisToString(e))}async selectAnimationGroup(e){if(this.#K===e)return this.togglePause(!1),void this.replay();if(this.clearTimeline(),this.#K=e,this.#Z.forEach(((e,t)=>{e.element.classList.toggle("selected",this.#K===t)})),e.isScrollDriven()){const t=await e.scrollNode();if(!t)throw new Error("Scroll container is not found for the scroll driven animation");const i="vertical"===e.scrollOrientation()?await t.verticalScrollRange():await t.horizontalScrollRange(),n="vertical"===e.scrollOrientation()?await t.scrollTop():await t.scrollLeft();if("number"!=typeof i||"number"!=typeof n)throw new Error("Scroll range or scroll offset is not resolved for the scroll driven animation");this.#pe=await t.addScrollEventListener((({scrollTop:t,scrollLeft:i})=>{const n="vertical"===e.scrollOrientation()?t:i;this.setCurrentTimeText(n),this.setTimelineScrubberPosition(n)})),this.setDuration(i),this.setCurrentTimeText(n),this.setTimelineScrubberPosition(n),this.#_.forEach((e=>{e.setAttribute("disabled","true")})),this.#ie&&this.#ie.setEnabled(!1)}else this.setDuration(Math.max(500,e.finiteDuration()+100)),this.#_.forEach((e=>{e.removeAttribute("disabled")})),this.#ie&&this.#ie.setEnabled(!0);await Promise.all(e.animations().map((e=>this.addAnimation(e)))),this.scheduleRedraw(),this.togglePause(!1),this.replay(),this.hasAnimationGroupActiveNodes()&&(this.#j.classList.remove("hidden"),this.#ue.classList.add("scrubber-enabled")),this.animationGroupSelectedForTest()}animationGroupSelectedForTest(){}async addAnimation(e){let t=this.#Q.get(e.source().backendNodeId());t||(t=new N(e.source()),this.#U.appendChild(t.element),this.#Q.set(e.source().backendNodeId(),t));const i=t.createNewRow(),n=new H(e,this,i),s=await e.source().deferredNode().resolvePromise();n.setNode(s),s&&t&&(t.nodeResolved(s),L.set(s,t)),this.#J.push(n),this.#ee.set(e.id(),e)}markNodeAsRemoved(e){L.get(e)?.nodeRemoved();for(const t of e.pseudoElements().values())t.forEach((e=>this.markNodeAsRemoved(e)));e.children()?.forEach((e=>{this.markNodeAsRemoved(e)})),this.hasAnimationGroupActiveNodes()||(this.#ue.classList.remove("scrubber-enabled"),this.#j.classList.add("hidden"),this.#ae?.cancel(),this.#ae=void 0,this.clearCurrentTimeText(),this.updateControlButton())}hasAnimationGroupActiveNodes(){for(const e of this.#Q.values())if(e.hasActiveNode())return!0;return!1}renderGrid(){const e=this.#K?.isScrollDriven(),t=e?this.duration()/10:250;let n;this.#D.removeChildren();for(let e=0;e<this.duration();e+=t){const t=i.UIUtils.createSVGChild(this.#D,"rect","animation-timeline-grid-line");t.setAttribute("x",(e*this.pixelTimeRatio()+10).toString()),t.setAttribute("y","23"),t.setAttribute("height","100%"),t.setAttribute("width","1")}for(let s=0;s<this.duration();s+=t){const t=s*this.pixelTimeRatio();if(void 0===n||t-n>50){n=t;const r=i.UIUtils.createSVGChild(this.#D,"text","animation-timeline-grid-label");r.textContent=e?`${(100*s/this.duration()).toFixed(0)}%`:o.TimeUtilities.millisToString(s),r.setAttribute("x",(t+10).toString()),r.setAttribute("y","16")}}}scheduleRedraw(){this.renderGrid(),this.#V=[];for(const e of this.#J)this.#V.push(e);this.#re||(this.#re=!0,this.#U.window().requestAnimationFrame(this.render.bind(this)))}render(e){for(;this.#V.length&&(!e||window.performance.now()-e<50);){const e=this.#V.shift();e&&e.redraw()}this.#V.length?this.#U.window().requestAnimationFrame(this.render.bind(this)):this.#re=void 0}onResize(){this.#oe=Math.max(0,this.#U.offsetWidth-this.#q)||0,this.scheduleRedraw(),this.#ae&&this.syncScrubber(),this.#le=void 0}width(){return this.#oe||0}syncScrubber(){this.#K&&this.hasAnimationGroupActiveNodes()&&this.#K.currentTimePromise().then(this.animateTime.bind(this)).then(this.updateControlButton.bind(this))}animateTime(e){this.#K?.isScrollDriven()||(this.#ae&&this.#ae.cancel(),this.#ae=this.#j.animate([{transform:"translateX(0px)"},{transform:"translateX("+this.width()+"px)"}],{duration:this.duration(),fill:"forwards"}),this.#ae.playbackRate=this.effectivePlaybackRate(),this.#ae.onfinish=this.updateControlButton.bind(this),this.#ae.currentTime=e,this.element.window().requestAnimationFrame(this.updateScrubber.bind(this)))}pixelTimeRatio(){return this.width()/this.duration()||0}updateScrubber(e){this.#ae&&(this.setCurrentTimeText(this.#ve()),"pending"===this.#ae.playState.toString()||"running"===this.#ae.playState?this.element.window().requestAnimationFrame(this.updateScrubber.bind(this)):"finished"===this.#ae.playState&&this.clearCurrentTimeText())}scrubberDragStart(e){if(!this.#K||!this.hasAnimationGroupActiveNodes())return!1;this.#le||(this.#le=this.#D.getBoundingClientRect().left+10);const{x:t}=e,i=Math.max(0,t-this.#le)/this.pixelTimeRatio();if(this.#de=i,this.#ce=t,this.setCurrentTimeText(i),this.#K.isScrollDriven())this.setTimelineScrubberPosition(i),this.updateScrollOffsetOnPage(i);else{const e=this.#ae?.currentTime;this.#he=this.#K.paused()||"number"==typeof e&&e>=this.duration(),this.#K.seekTo(i),this.togglePause(!0),this.animateTime(i)}return!0}async updateScrollOffsetOnPage(e){const t=await(this.#K?.scrollNode());if(t)return"vertical"===this.#K?.scrollOrientation()?t.setScrollTop(e):t.setScrollLeft(e)}setTimelineScrubberPosition(e){this.#j.style.transform=`translateX(${e*this.pixelTimeRatio()}px)`}scrubberDragMove(e){const{x:t}=e,i=t-(this.#ce||0),n=Math.max(0,Math.min((this.#de||0)+i/this.pixelTimeRatio(),this.duration()));this.#ae?this.#ae.currentTime=n:(this.setTimelineScrubberPosition(n),this.updateScrollOffsetOnPage(n)),this.setCurrentTimeText(n),this.#K&&!this.#K.isScrollDriven()&&this.#K.seekTo(n)}#ve(){return"number"==typeof this.#ae?.currentTime?this.#ae.currentTime:0}scrubberDragEnd(e){if(this.#ae){const e=Math.max(0,this.#ve());this.#ae.play(),this.#ae.currentTime=e}r.userMetrics.actionTaken(r.UserMetrics.Action.AnimationGroupScrubbed),this.#H.window().requestAnimationFrame(this.updateScrubber.bind(this)),this.#he||this.togglePause(!1)}}const O=[1,.25,.1];class N{element;#we;#xe;#Te;#ke;constructor(e){this.element=document.createElement("div"),this.element.classList.add("animation-node-row"),this.#we=this.element.createChild("div","animation-node-description"),this.#we.setAttribute("jslog",`${n.tableCell("description").track({resize:!0})}`),this.#xe=this.element.createChild("div","animation-node-timeline"),this.#xe.setAttribute("jslog",`${n.tableCell("timeline").track({resize:!0})}`),i.ARIAUtils.markAsApplication(this.#xe)}nodeResolved(e){e?(this.#ke=e,this.nodeChanged(),s.Linkifier.Linkifier.linkify(e).then((e=>{e.addEventListener("click",(()=>{r.userMetrics.actionTaken(r.UserMetrics.Action.AnimatedNodeDescriptionClicked)})),this.#we.appendChild(e)})),e.ownerDocument||this.nodeRemoved()):i.UIUtils.createTextChild(this.#we,"<node>")}createNewRow(){return this.#xe.createChild("div","animation-timeline-row")}nodeRemoved(){this.element.classList.add("animation-node-removed"),this.#Te||(this.#Te=document.createElement("div"),this.#Te.classList.add("animation-node-removed-overlay"),this.#we.appendChild(this.#Te)),this.#ke=null}hasActiveNode(){return Boolean(this.#ke)}nodeChanged(){let t=!1;this.#ke&&(t=this.#ke===i.Context.Context.instance().flavor(e.DOMModel.DOMNode)),this.element.classList.toggle("animation-node-selected",t)}}class F{steps;stepAtPosition;constructor(e,t){this.steps=e,this.stepAtPosition=t}static parse(e){let t=e.match(/^steps\((\d+), (start|middle)\)$/);return t?new F(parseInt(t[1],10),t[2]):(t=e.match(/^steps\((\d+)\)$/),t?new F(parseInt(t[1],10),"end"):null)}}var U=Object.freeze({__proto__:null,AnimationTimeline:D,GlobalPlaybackRates:O,NodeUI:N,StepTimingFunction:F});const _={animationEndpointSlider:"Animation Endpoint slider",animationKeyframeSlider:"Animation Keyframe slider",sSlider:"{PH1} slider"},z=o.i18n.registerUIStrings("panels/animation/AnimationUI.ts",_),j=o.i18n.getLocalizedString.bind(void 0,z);class H{#Ae;#Ie;#Me;#Se;#Ce;#Pe;#Ee;#Re;#Le;#Be;#ke;#Ge;#De;#Oe;#Ne;#Fe;#Ue;constructor(e,t,s){this.#Ae=e,this.#Ie=t;const r=this.#Ae.source().keyframesRule();r&&(this.#Me=r.keyframes(),e.viewOrScrollTimeline()&&e.playbackRate()<0&&this.#Me.reverse()),this.#Se=s.createChild("div","animation-name"),this.#Se.textContent=this.#Ae.name(),this.#Ce=i.UIUtils.createSVGChild(s,"svg","animation-ui"),this.#Ce.setAttribute("height",W.AnimationSVGHeight.toString()),this.#Ce.style.marginLeft="-"+W.AnimationMargin+"px",this.#Ce.addEventListener("contextmenu",this.onContextMenu.bind(this)),this.#Pe=i.UIUtils.createSVGChild(this.#Ce,"g"),this.#Pe.setAttribute("jslog",`${n.animationClip().track({drag:!0})}`),this.#Ae.viewOrScrollTimeline()||(i.UIUtils.installDragHandle(this.#Pe,this.mouseDown.bind(this,"AnimationDrag",null),this.mouseMove.bind(this),this.mouseUp.bind(this),"-webkit-grabbing","-webkit-grab"),H.installDragHandleKeyboard(this.#Pe,this.keydownMove.bind(this,"AnimationDrag",null))),this.#Ee=[],this.#Re=0,this.#Le=50,this.#Be=H.colorForAnimation(this.#Ae)}static colorForAnimation(e){const t=Array.from(K.keys()),i=t[a.StringUtilities.hashCode(e.name()||e.id())%t.length],n=K.get(i);if(!n)throw new Error("Unable to locate color");return n.asString("rgb")||""}static installDragHandleKeyboard(e,t){e.addEventListener("keydown",t,!1)}animation(){return this.#Ae}get nameElement(){return this.#Se}get svg(){return this.#Ce}setNode(e){this.#ke=e}createLine(e,t){const n=i.UIUtils.createSVGChild(e,"line",t);return n.setAttribute("x1",W.AnimationMargin.toString()),n.setAttribute("y1",W.AnimationHeight.toString()),n.setAttribute("y2",W.AnimationHeight.toString()),n.style.stroke=this.#Be,n}drawAnimationLine(e,t){const i=this.#Ee[e];i.animationLine||(i.animationLine=this.createLine(t,"animation-line")),i.animationLine&&i.animationLine.setAttribute("x2",(this.duration()*this.#Ie.pixelTimeRatio()+W.AnimationMargin).toFixed(2))}drawDelayLine(e){this.#Ge&&this.#De||(this.#Ge=this.createLine(e,"animation-delay-line"),this.#De=this.createLine(e,"animation-delay-line"));const t=this.#Ae.source().fill();this.#Ge.classList.toggle("animation-fill","backwards"===t||"both"===t);const i=W.AnimationMargin;this.#Ge.setAttribute("x1",i.toString()),this.#Ge.setAttribute("x2",(this.delayOrStartTime()*this.#Ie.pixelTimeRatio()+i).toFixed(2));const n="forwards"===t||"both"===t;this.#De.classList.toggle("animation-fill",n);const s=Math.min(this.#Ie.width(),(this.delayOrStartTime()+this.duration()*this.#Ae.source().iterations())*this.#Ie.pixelTimeRatio());this.#De.style.transform="translateX("+s.toFixed(2)+"px)",this.#De.setAttribute("x1",i.toString()),this.#De.setAttribute("x2",n?(this.#Ie.width()-s+i).toFixed(2):(this.#Ae.source().endDelay()*this.#Ie.pixelTimeRatio()+i).toFixed(2))}drawPoint(e,t,s,r,o){if(this.#Ee[e].keyframePoints[r])return void this.#Ee[e].keyframePoints[r].setAttribute("cx",s.toFixed(2));const a=i.UIUtils.createSVGChild(t,"circle",r<=0?"animation-endpoint":"animation-keyframe-point");if(a.setAttribute("cx",s.toFixed(2)),a.setAttribute("cy",W.AnimationHeight.toString()),a.style.stroke=this.#Be,a.setAttribute("r",(W.AnimationMargin/2).toString()),a.setAttribute("jslog",`${n.controlPoint("animations.keyframe").track({drag:!0})}`),a.tabIndex=0,i.ARIAUtils.setLabel(a,j(r<=0?_.animationEndpointSlider:_.animationKeyframeSlider)),r<=0&&(a.style.fill=this.#Be),this.#Ee[e].keyframePoints[r]=a,!o)return;let l;l=0===r?"StartEndpointMove":-1===r?"FinishEndpointMove":"KeyframeMove",this.animation().viewOrScrollTimeline()||(i.UIUtils.installDragHandle(a,this.mouseDown.bind(this,l,r),this.mouseMove.bind(this),this.mouseUp.bind(this),"ew-resize"),H.installDragHandleKeyboard(a,this.keydownMove.bind(this,l,r)))}renderKeyframe(e,t,n,s,r,o){function a(e,t,n){const s=i.UIUtils.createSVGChild(e,"line");s.setAttribute("x1",t.toString()),s.setAttribute("x2",t.toString()),s.setAttribute("y1",W.AnimationMargin.toString()),s.setAttribute("y2",W.AnimationHeight.toString()),s.style.stroke=n}const d=i.Geometry.CubicBezier.parse(o),h=this.#Ee[e].keyframeRender;if(!h[t]){const e=d?i.UIUtils.createSVGChild(n,"path","animation-keyframe"):i.UIUtils.createSVGChild(n,"g","animation-keyframe-step");h[t]=e}const c=h[t];if(c.tabIndex=0,i.ARIAUtils.setLabel(c,j(_.sSlider,{PH1:this.#Ae.name()})),c.style.transform="translateX("+s.toFixed(2)+"px)","linear"===o){c.style.fill=this.#Be;const e=l.BezierUI.Height;c.setAttribute("d",["M",0,e,"L",0,5,"L",r.toFixed(2),5,"L",r.toFixed(2),e,"Z"].join(" "))}else if(d)c.style.fill=this.#Be,l.BezierUI.BezierUI.drawVelocityChart(d,c,r);else{const e=F.parse(o);c.removeChildren();if(e){const t={start:0,middle:.5,end:1}[e.stepAtPosition];for(let i=0;i<e.steps;i++)a(c,(i+t)*r/e.steps,this.#Be)}}}redraw(){const e=this.#Ie.width()-W.AnimationMargin;if(this.#Ce.setAttribute("width",(e+2*W.AnimationMargin).toFixed(2)),this.#Pe.style.transform="translateX("+(this.delayOrStartTime()*this.#Ie.pixelTimeRatio()).toFixed(2)+"px)",this.#Se.style.transform="translateX("+(this.delayOrStartTime()*this.#Ie.pixelTimeRatio()+W.AnimationMargin).toFixed(2)+"px)",this.#Se.style.width=(this.duration()*this.#Ie.pixelTimeRatio()).toFixed(2)+"px",this.drawDelayLine(this.#Ce),"CSSTransition"===this.#Ae.type())return void this.renderTransition();this.renderIteration(this.#Pe,0),this.#Oe||(this.#Oe=i.UIUtils.createSVGChild(this.#Pe,"g","animation-tail-iterations"));const t=this.duration()*this.#Ie.pixelTimeRatio();let n;for(n=1;n<this.#Ae.source().iterations()&&t*(n-1)<this.#Ie.width()&&(t>0||this.#Ae.source().iterations()!==1/0);n++)this.renderIteration(this.#Oe,n);for(;n<this.#Ee.length;){const e=this.#Ee.pop();e&&e.group&&e.group.remove()}}renderTransition(){const e=this.#Pe;this.#Ee[0]||(this.#Ee[0]={animationLine:null,keyframePoints:{},keyframeRender:{},group:null}),this.drawAnimationLine(0,e),this.renderKeyframe(0,0,e,W.AnimationMargin,this.duration()*this.#Ie.pixelTimeRatio(),this.#Ae.source().easing()),this.drawPoint(0,e,W.AnimationMargin,0,!0),this.drawPoint(0,e,this.duration()*this.#Ie.pixelTimeRatio()+W.AnimationMargin,-1,!0)}renderIteration(e,t){this.#Ee[t]||(this.#Ee[t]={animationLine:null,keyframePoints:{},keyframeRender:{},group:i.UIUtils.createSVGChild(e,"g")});const n=this.#Ee[t].group;if(n){if(n.style.transform="translateX("+(t*this.duration()*this.#Ie.pixelTimeRatio()).toFixed(2)+"px)",this.drawAnimationLine(t,n),this.#Me&&this.#Me.length>1)for(let e=0;e<this.#Me.length-1;e++){const i=this.offset(e)*this.duration()*this.#Ie.pixelTimeRatio()+W.AnimationMargin,s=this.duration()*(this.offset(e+1)-this.offset(e))*this.#Ie.pixelTimeRatio();this.renderKeyframe(t,e,n,i,s,this.#Me[e].easing()),(e||!e&&0===t)&&this.drawPoint(t,n,i,e,0===t)}this.drawPoint(t,n,this.duration()*this.#Ie.pixelTimeRatio()+W.AnimationMargin,-1,0===t)}}delayOrStartTime(){let e=this.#Ae.delayOrStartTime();return"AnimationDrag"!==this.#Ne&&"StartEndpointMove"!==this.#Ne||(e+=this.#Re),Math.max(0,e)}duration(){let e=this.#Ae.iterationDuration();return"FinishEndpointMove"===this.#Ne?e+=this.#Re:"StartEndpointMove"===this.#Ne&&(e-=Math.max(this.#Re,-this.#Ae.delayOrStartTime())),Math.max(0,e)}offset(e){if(!this.#Me)throw new Error("Unable to calculate offset; keyframes do not exist");let t=this.#Me[e].offsetAsNumber();return"KeyframeMove"===this.#Ne&&e===this.#Fe&&(console.assert(e>0&&e<this.#Me.length-1,"First and last keyframe cannot be moved"),t+=this.#Re/this.#Ae.iterationDuration(),t=Math.max(t,this.#Me[e-1].offsetAsNumber()),t=Math.min(t,this.#Me[e+1].offsetAsNumber())),t}mouseDown(e,t,n){const r=n;if(2===r.buttons)return!1;if(this.#Ce.enclosingNodeOrSelfWithClass("animation-node-removed"))return!1;this.#Ne=e,this.#Fe=t,this.#Ue=r.clientX,n.consume(!0);const o=i.ViewManager.ViewManager.instance(),a=o.locationNameForViewId("animations"),l=o.locationNameForViewId("elements");return this.#ke&&a!==l&&s.Revealer.reveal(this.#ke),!0}mouseMove(e){const t=e;this.setMovementAndRedraw((t.clientX-(this.#Ue||0))/this.#Ie.pixelTimeRatio())}setMovementAndRedraw(e){this.#Re=e,this.delayOrStartTime()+this.duration()>.8*this.#Ie.duration()&&this.#Ie.setDuration(1.2*this.#Ie.duration()),this.redraw()}mouseUp(e){const t=e;this.#Re=(t.clientX-(this.#Ue||0))/this.#Ie.pixelTimeRatio(),"KeyframeMove"===this.#Ne?this.#Me&&null!==this.#Fe&&void 0!==this.#Fe&&this.#Me[this.#Fe].setOffset(this.offset(this.#Fe)):this.#Ae.setTiming(this.duration(),this.delayOrStartTime()),r.userMetrics.animationPointDragged("AnimationDrag"===this.#Ne?0:"KeyframeMove"===this.#Ne?1:"StartEndpointMove"===this.#Ne?2:"FinishEndpointMove"===this.#Ne?3:4),this.#Re=0,this.redraw(),this.#Ne=void 0,this.#Ue=void 0,this.#Fe=void 0}keydownMove(e,t,i){const n=i;switch(this.#Ne=e,this.#Fe=t,n.key){case"ArrowLeft":case"ArrowUp":this.#Re=-this.#Le;break;case"ArrowRight":case"ArrowDown":this.#Re=this.#Le;break;default:return}"KeyframeMove"===this.#Ne?this.#Me&&null!==this.#Fe&&this.#Me[this.#Fe].setOffset(this.offset(this.#Fe)):this.#Ae.setTiming(this.duration(),this.delayOrStartTime()),this.setMovementAndRedraw(0),this.#Ne=void 0,this.#Fe=void 0,i.consume(!0)}onContextMenu(e){this.#Ae.remoteObjectPromise().then((function(t){if(!t)return;const n=new i.ContextMenu.ContextMenu(e);n.appendApplicableItems(t),n.show()})),e.consume(!0)}}const W={AnimationHeight:26,AnimationSVGHeight:50,AnimationMargin:7,EndpointsClickRegionSize:10,GridCanvasHeight:40},K=new Map([["Purple",s.Color.parse("#9C27B0")],["Light Blue",s.Color.parse("#03A9F4")],["Deep Orange",s.Color.parse("#FF5722")],["Blue",s.Color.parse("#5677FC")],["Lime",s.Color.parse("#CDDC39")],["Blue Grey",s.Color.parse("#607D8B")],["Pink",s.Color.parse("#E91E63")],["Green",s.Color.parse("#0F9D58")],["Brown",s.Color.parse("#795548")],["Cyan",s.Color.parse("#00BCD4")]]);var V=Object.freeze({__proto__:null,AnimationUI:H,Options:W,Colors:K});class X{#_e;element;#ze;#je;#Ce;#He;constructor(e){this.#_e=e,this.element=document.createElement("button"),this.element.setAttribute("jslog",`${n.item("animations.buffer-preview").track({click:!0})}`),this.element.classList.add("animation-buffer-preview"),this.element.addEventListener("animationend",(()=>{this.element.classList.add("no-animation")})),this.element.createChild("div","animation-paused fill"),e.isScrollDriven()&&this.element.appendChild(t.Icon.create("mouse","mouse-icon")),this.#ze=this.element.createChild("button","animation-remove-button"),this.#ze.setAttribute("jslog",`${n.action("animations.remove-preview").track({click:!0})}`),this.#ze.appendChild(t.Icon.create("cross")),this.#je=this.element.createChild("div","animation-buffer-preview-animation"),this.#Ce=i.UIUtils.createSVGChild(this.element,"svg"),this.#Ce.setAttribute("width","100%"),this.#Ce.setAttribute("preserveAspectRatio","none"),this.#Ce.setAttribute("height","100%"),this.#He=32,this.#Ce.setAttribute("viewBox","0 0 100 "+this.#He),this.#Ce.setAttribute("shape-rendering","crispEdges"),this.render()}removeButton(){return this.#ze}replay(){this.#je.animate([{offset:0,width:"0%",opacity:1},{offset:.9,width:"100%",opacity:1},{offset:1,width:"100%",opacity:0}],{duration:200,easing:"cubic-bezier(0, 0, 0.2, 1)"})}render(){this.#Ce.removeChildren();const e=Math.min(this.#_e.animations().length,10),t=100/Math.max(this.#_e.groupDuration(),750);for(let n=0;n<e;n++){const s=this.#_e.animations()[n],r=i.UIUtils.createSVGChild(this.#Ce,"line"),o=s.delayOrStartTime(),a=o+s.iterationDuration();r.setAttribute("x1",String(o*t)),r.setAttribute("x2",String(a*t));const l=String(Math.floor(this.#He/Math.max(6,e)*n+1));r.setAttribute("y1",l),r.setAttribute("y2",l),r.style.stroke=H.colorForAnimation(this.#_e.animations()[n])}}}var $=Object.freeze({__proto__:null,AnimationGroupPreviewUI:X});export{f as AnimationDOMNode,$ as AnimationGroupPreviewUI,A as AnimationModel,S as AnimationScreenshotPopover,U as AnimationTimeline,V as AnimationUI};
