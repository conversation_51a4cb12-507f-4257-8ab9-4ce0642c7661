import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Ionicons } from '@expo/vector-icons';
import { API_CONFIG } from '../config/constants';

const API_BASE_URL = API_CONFIG.BASE_URL;

const ScannerScreen = ({ navigation }) => {
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getBarCodeScannerPermissions();
  }, []);

  const getBarCodeScannerPermissions = async () => {
    const { status } = await BarCodeScanner.requestPermissionsAsync();
    setHasPermission(status === 'granted');
  };

  const handleBarCodeScanned = async ({ type, data }) => {
    setScanned(true);
    setLoading(true);

    try {
      console.log('📱 QR Code scanné:', data);
      console.log('🌐 Tentative de connexion à:', API_BASE_URL);

      // Test de connexion au serveur d'abord
      const testResponse = await fetch(`${API_BASE_URL}/api/database/schema`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      if (!testResponse.ok) {
        throw new Error(`Serveur non accessible (${testResponse.status})`);
      }

      // Rechercher le contrat avec ce QR code
      console.log('🔍 Recherche du contrat...');
      const contractResponse = await fetch(`${API_BASE_URL}/api/contracts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      if (!contractResponse.ok) {
        throw new Error(`Erreur lors de la récupération des contrats (${contractResponse.status})`);
      }

      const contractsData = await contractResponse.json();
      console.log('📄 Contrats reçus:', contractsData);

      if (contractsData.success && contractsData.data) {
        const contract = contractsData.data.find(c => c.codeqr === data);

        if (contract) {
          console.log('✅ Contrat trouvé:', contract);

          // Récupérer les informations du client
          const clientResponse = await fetch(`${API_BASE_URL}/api/clients`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 10000,
          });

          if (!clientResponse.ok) {
            throw new Error(`Erreur lors de la récupération des clients (${clientResponse.status})`);
          }

          const clientsData = await clientResponse.json();
          console.log('👥 Clients reçus:', clientsData);

          if (clientsData.success && clientsData.data) {
            const client = clientsData.data.find(c => c.idclient === contract.idclient);

            if (client) {
              console.log('✅ Client trouvé:', client);
              Alert.alert(
                'Client trouvé',
                `${client.prenom} ${client.nom}\n${client.adresse}\nTél: ${client.tel}`,
                [
                  { text: 'Annuler', style: 'cancel' },
                  {
                    text: 'Saisir Consommation',
                    onPress: () => {
                      navigation.navigate('Consommation', {
                        selectedClient: client,
                        selectedContract: contract
                      });
                    }
                  },
                  {
                    text: 'Voir Factures',
                    onPress: () => {
                      navigation.navigate('Factures', { clientId: client.idclient });
                    }
                  },
                ]
              );
            } else {
              Alert.alert('Erreur', 'Client non trouvé pour ce contrat');
            }
          } else {
            Alert.alert('Erreur', 'Erreur lors de la récupération des clients');
          }
        } else {
          Alert.alert(
            'QR Code non reconnu',
            `Le QR code "${data}" ne correspond à aucun contrat dans la base de données.\n\nCode scanné: ${data}`
          );
        }
      } else {
        Alert.alert('Erreur', 'Erreur lors de la récupération des contrats');
      }
    } catch (error) {
      console.error('❌ Erreur lors du scan:', error);

      let errorMessage = 'Erreur de connexion au serveur';

      if (error.message.includes('Failed to fetch') || error.message.includes('Network request failed')) {
        errorMessage = `Impossible de se connecter au serveur.\n\nVérifiez que :\n• Le serveur est démarré sur ${API_BASE_URL}\n• Votre téléphone et ordinateur sont sur le même WiFi\n• Le firewall autorise le port 3007`;
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Délai d\'attente dépassé. Le serveur met trop de temps à répondre.';
      } else if (error.message.includes('non accessible')) {
        errorMessage = `Serveur non accessible.\n\nURL testée: ${API_BASE_URL}\n\nVérifiez que le serveur Node.js est démarré.`;
      } else {
        errorMessage = `Erreur: ${error.message}`;
      }

      Alert.alert('Erreur de connexion', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const resetScanner = () => {
    setScanned(false);
  };

  const handleDiagnostic = async () => {
    setLoading(true);
    try {
      console.log('🔍 Diagnostic de connexion...');

      // Test simple de connexion
      const response = await fetch(`${API_BASE_URL}/api/database/schema`);

      if (response.ok) {
        const data = await response.json();
        Alert.alert(
          '✅ Diagnostic réussi',
          `Serveur accessible !\n\nURL: ${API_BASE_URL}\nStatut: ${response.status}\nTables: ${data.data?.total_tables || 'N/A'}`
        );
      } else {
        Alert.alert(
          '❌ Problème de serveur',
          `Le serveur répond mais avec une erreur.\n\nURL: ${API_BASE_URL}\nStatut: ${response.status}`
        );
      }
    } catch (error) {
      console.error('❌ Erreur de diagnostic:', error);

      let message = `Impossible de se connecter au serveur.\n\nURL: ${API_BASE_URL}\n\n`;

      if (error.message.includes('Failed to fetch') || error.message.includes('Network request failed')) {
        message += 'SOLUTIONS:\n• Vérifiez que le serveur Node.js est démarré\n• Vérifiez que votre téléphone et PC sont sur le même WiFi\n• Testez l\'URL dans votre navigateur';
      } else {
        message += `Erreur: ${error.message}`;
      }

      Alert.alert('❌ Diagnostic échoué', message);
    } finally {
      setLoading(false);
    }
  };

  if (hasPermission === null) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Demande d'autorisation caméra...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.centerContainer}>
        <Ionicons name="camera-outline" size={64} color="#ccc" />
        <Text style={styles.errorTitle}>Accès caméra refusé</Text>
        <Text style={styles.errorMessage}>
          L'application a besoin d'accéder à la caméra pour scanner les QR codes.
        </Text>
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={getBarCodeScannerPermissions}
        >
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <BarCodeScanner
        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
        style={styles.scanner}
      />
      
      {/* Overlay avec instructions */}
      <View style={styles.overlay}>
        <View style={styles.topOverlay}>
          <Text style={styles.instructionText}>
            Pointez la caméra vers le QR code du compteur
          </Text>
        </View>
        
        {/* Zone de scan */}
        <View style={styles.scanArea}>
          <View style={styles.scanFrame}>
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
          </View>
        </View>
        
        <View style={styles.bottomOverlay}>
          {scanned && (
            <TouchableOpacity 
              style={styles.scanAgainButton} 
              onPress={resetScanner}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <>
                  <Ionicons name="refresh-outline" size={20} color="#fff" />
                  <Text style={styles.scanAgainText}>Scanner à nouveau</Text>
                </>
              )}
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.manualButton}
            onPress={() => navigation.navigate('Consommation')}
          >
            <Ionicons name="create-outline" size={20} color="#2196F3" />
            <Text style={styles.manualButtonText}>Saisie manuelle</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.diagnosticButton}
            onPress={handleDiagnostic}
            disabled={loading}
          >
            <Ionicons name="bug-outline" size={20} color="#FF9800" />
            <Text style={styles.diagnosticButtonText}>Diagnostic</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f44336',
    marginTop: 10,
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scanner: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  instructionText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  scanArea: {
    height: 250,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#2196F3',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  bottomOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  scanAgainButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 20,
  },
  scanAgainText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  manualButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
  },
  manualButtonText: {
    color: '#2196F3',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  diagnosticButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
    marginTop: 10,
    borderWidth: 2,
    borderColor: '#FF9800',
  },
  diagnosticButtonText: {
    color: '#FF9800',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default ScannerScreen;
