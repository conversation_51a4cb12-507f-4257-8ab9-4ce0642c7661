<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Scanner QR</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056CC;
        }
        .success { background: #4CAF50; }
        .error { background: #F44336; }
        .warning { background: #FF9800; }
        
        #result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Scanner QR - AquaTrack</h1>
        
        <div id="status" class="status warning">
            ⏳ Prêt pour les tests...
        </div>

        <h2>🔧 Tests de Connexion</h2>
        <button onclick="testConnection()">🌐 Test Connexion API</button>
        <button onclick="testDatabase()">🗄️ Test Base de Données</button>
        <button onclick="listQRCodes()">📋 Lister QR Codes</button>

        <h2>📱 Tests de Scan</h2>
        <button onclick="testScan('QR001')" class="success">✅ Test QR001 (Jean Dupont)</button>
        <button onclick="testScan('QR002')" class="success">✅ Test QR002 (Marie Martin)</button>
        <button onclick="testScan('QR003')" class="success">✅ Test QR003 (Pierre Bernard)</button>
        <button onclick="testScan('INVALID')" class="error">❌ Test QR Invalide</button>

        <h2>📊 Résultats</h2>
        <div id="result">Aucun test exécuté...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        const resultDiv = document.getElementById('result');
        const statusDiv = document.getElementById('status');

        function updateStatus(message, type = 'warning') {
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function logResult(message) {
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        async function testConnection() {
            updateStatus('🔄 Test de connexion...', 'warning');
            logResult('🌐 Test de connexion API...');
            
            try {
                const response = await fetch(API_BASE.replace('/api', ''));
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('✅ API connectée', 'success');
                    logResult('✅ Connexion API réussie');
                    logResult(JSON.stringify(data, null, 2));
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('❌ API non accessible', 'error');
                logResult(`❌ Erreur connexion: ${error.message}`);
            }
        }

        async function testDatabase() {
            updateStatus('🔄 Test base de données...', 'warning');
            logResult('🗄️ Test de la base de données...');
            
            try {
                const response = await fetch(`${API_BASE}/test-db`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateStatus('✅ Base de données connectée', 'success');
                    logResult('✅ Base de données accessible');
                    logResult(`Timestamp: ${data.timestamp}`);
                } else {
                    throw new Error(data.message || 'Erreur base de données');
                }
            } catch (error) {
                updateStatus('❌ Base de données inaccessible', 'error');
                logResult(`❌ Erreur DB: ${error.message}`);
            }
        }

        async function listQRCodes() {
            updateStatus('🔄 Récupération QR codes...', 'warning');
            logResult('📋 Récupération de la liste des QR codes...');
            
            try {
                const response = await fetch(`${API_BASE}/qr-codes`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateStatus(`✅ ${data.qrCodes.length} QR codes trouvés`, 'success');
                    logResult(`✅ ${data.qrCodes.length} QR codes disponibles:`);
                    
                    data.qrCodes.forEach((qr, index) => {
                        logResult(`${index + 1}. ${qr.codeQr} - ${qr.client} (${qr.compteur})`);
                    });
                } else {
                    throw new Error(data.message || 'Erreur récupération QR codes');
                }
            } catch (error) {
                updateStatus('❌ Erreur récupération QR codes', 'error');
                logResult(`❌ Erreur QR codes: ${error.message}`);
            }
        }

        async function testScan(qrCode) {
            updateStatus(`🔄 Scan ${qrCode}...`, 'warning');
            logResult(`📱 Test scan QR code: ${qrCode}`);
            
            try {
                const response = await fetch(`${API_BASE}/scan/${encodeURIComponent(qrCode)}`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateStatus(`✅ Client trouvé: ${data.data.client.nom} ${data.data.client.prenom}`, 'success');
                    logResult(`✅ Scan réussi pour ${qrCode}`);
                    
                    const client = data.data.client;
                    const contract = data.data.contract;
                    const lastCons = data.data.lastConsommation;
                    
                    logResult(`👤 Client: ${client.nom} ${client.prenom}`);
                    logResult(`🏠 Adresse: ${client.adresse}, ${client.ville}`);
                    logResult(`📞 Téléphone: ${client.tel}`);
                    logResult(`📧 Email: ${client.email || 'Non renseigné'}`);
                    
                    if (contract) {
                        logResult(`⚙️ Compteur: ${contract.marqueCompteur} - ${contract.numSerieCompteur}`);
                        logResult(`📅 Date contrat: ${new Date(contract.dateContract).toLocaleDateString()}`);
                    }
                    
                    if (lastCons) {
                        logResult(`💧 Dernière conso: ${lastCons.consommationActuelle} m³ (${lastCons.periode})`);
                    }
                    
                    logResult(`🧾 Factures: ${data.data.factures.length} facture(s)`);
                    
                } else {
                    updateStatus(`❌ ${data.message}`, 'error');
                    logResult(`❌ Scan échoué: ${data.message}`);
                }
            } catch (error) {
                updateStatus('❌ Erreur de scan', 'error');
                logResult(`❌ Erreur scan: ${error.message}`);
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            logResult('🚀 Page de test chargée');
            logResult('💡 Cliquez sur les boutons pour tester l\'API');
            logResult('');
            
            // Test automatique de connexion
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
