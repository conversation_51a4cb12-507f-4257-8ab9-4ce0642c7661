{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/resolveCertificateSigningIdentity.ts"], "sourcesContent": ["import { getConfig, modifyConfigAsync } from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Security from './Security';\nimport { getLastDeveloperCodeSigningIdAsync, setLastDeveloperCodeSigningIdAsync } from './settings';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { isInteractive } from '../../../utils/interactive';\nimport { learnMore } from '../../../utils/link';\nimport { selectAsync } from '../../../utils/prompts';\n\n/**\n * Sort the code signing items so the last selected item (user's default) is the first suggested.\n */\nexport async function sortDefaultIdToBeginningAsync(\n  identities: Security.CertificateSigningInfo[]\n): Promise<[Security.CertificateSigningInfo[], string | null]> {\n  const lastSelected = await getLastDeveloperCodeSigningIdAsync();\n\n  if (lastSelected) {\n    let iterations = 0;\n    while (identities[0].signingCertificateId !== lastSelected && iterations < identities.length) {\n      identities.push(identities.shift()!);\n      iterations++;\n    }\n  }\n  return [identities, lastSelected];\n}\n\n/**\n * Assert that the computer needs code signing setup.\n * This links to an FYI page that was user tested internally.\n */\nfunction assertCodeSigningSetup(): never {\n  // TODO: We can probably do this too automatically.\n  Log.log(\n    `\\u203A Your computer requires some additional setup before you can build onto physical iOS devices.\\n  ${chalk.bold(\n      learnMore('https://expo.fyi/setup-xcode-signing')\n    )}`\n  );\n\n  throw new CommandError('No code signing certificates are available to use.');\n}\n\n/**\n * Resolve the best certificate signing identity from a given list of IDs.\n * - If no IDs: Assert that the user has to setup code signing.\n * - If one ID: Return the first ID.\n * - If multiple IDs: Ask the user to select one, then store the value to be suggested first next time (since users generally use the same ID).\n */\nexport async function resolveCertificateSigningIdentityAsync(\n  projectRoot: string,\n  ids: string[]\n): Promise<Security.CertificateSigningInfo> {\n  // The user has no valid code signing identities.\n  if (!ids.length) {\n    assertCodeSigningSetup();\n  }\n\n  //  One ID available 🤝 Program is not interactive\n  //\n  //     using the the first available option\n  if (ids.length === 1 || !isInteractive()) {\n    // This method is cheaper than `resolveIdentitiesAsync` and checking the\n    // cached user preference so we should use this as early as possible.\n    return Security.resolveCertificateSigningInfoAsync(ids[0]);\n  }\n\n  // Get identities and sort by the one that the user is most likely to choose.\n  const [identities, preferred] = await sortDefaultIdToBeginningAsync(\n    await Security.resolveIdentitiesAsync(ids)\n  );\n\n  // Read the config to interact with the `ios.appleTeamId` property\n  const { exp } = getConfig(projectRoot, {\n    // We don't need very many fields here, just use the lightest possible read.\n    skipSDKVersionRequirement: true,\n    skipPlugins: true,\n  });\n  const configuredTeamId = exp.ios?.appleTeamId;\n\n  const configuredIdentity = configuredTeamId\n    ? identities.find((identity) => identity.appleTeamId === configuredTeamId)\n    : undefined;\n\n  const selectedIdentity =\n    configuredIdentity ?? (await selectDevelopmentTeamAsync(identities, preferred));\n\n  await Promise.all([\n    // Store the last used value and suggest it as the first value\n    // next time the user has to select a code signing identity.\n    setLastDeveloperCodeSigningIdAsync(selectedIdentity.signingCertificateId),\n    // Store the last used team id in the app manifest, when no team id has been configured yet\n    configuredTeamId || !selectedIdentity.appleTeamId\n      ? Promise.resolve()\n      : modifyConfigAsync(projectRoot, { ios: { appleTeamId: selectedIdentity.appleTeamId } }),\n  ]);\n\n  return selectedIdentity;\n}\n\n/** Prompt the user to select a development team, highlighting the preferred value based on the user history. */\nexport async function selectDevelopmentTeamAsync(\n  identities: Security.CertificateSigningInfo[],\n  preferredId: string | null\n): Promise<Security.CertificateSigningInfo> {\n  const index = await selectAsync(\n    'Development team for signing the app',\n    identities.map((value, i) => {\n      const format =\n        value.signingCertificateId === preferredId ? chalk.bold : (message: string) => message;\n      return {\n        // Formatted like: `650 Industries, Inc. (A1BCDEF234) - Apple Development: Evan Bacon (AA00AABB0A)`\n        title: format(\n          [value.appleTeamName, `(${value.appleTeamId}) -`, value.codeSigningInfo].join(' ')\n        ),\n        value: i,\n      };\n    })\n  );\n\n  return identities[index];\n}\n"], "names": ["resolveCertificateSigningIdentityAsync", "selectDevelopmentTeamAsync", "sortDefaultIdToBeginningAsync", "identities", "lastSelected", "getLastDeveloperCodeSigningIdAsync", "iterations", "signingCertificateId", "length", "push", "shift", "assertCodeSigningSetup", "Log", "log", "chalk", "bold", "learnMore", "CommandError", "projectRoot", "ids", "exp", "isInteractive", "Security", "resolveCertificateSigningInfoAsync", "preferred", "resolveIdentitiesAsync", "getConfig", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "configuredTeamId", "ios", "appleTeamId", "configuredIdentity", "find", "identity", "undefined", "selectedIdentity", "Promise", "all", "setLastDeveloperCodeSigningIdAsync", "resolve", "modifyConfigAsync", "preferredId", "index", "selectAsync", "map", "value", "i", "format", "message", "title", "appleTeamName", "codeSigningInfo", "join"], "mappings": ";;;;;;;;;;;IAkDsBA,sCAAsC;eAAtCA;;IAoDAC,0BAA0B;eAA1BA;;IAxFAC,6BAA6B;eAA7BA;;;;yBAduB;;;;;;;gEAC3B;;;;;;kEAEQ;0BAC6D;6DAClE;wBACQ;6BACC;sBACJ;yBACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKrB,eAAeA,8BACpBC,UAA6C;IAE7C,MAAMC,eAAe,MAAMC,IAAAA,4CAAkC;IAE7D,IAAID,cAAc;QAChB,IAAIE,aAAa;QACjB,MAAOH,UAAU,CAAC,EAAE,CAACI,oBAAoB,KAAKH,gBAAgBE,aAAaH,WAAWK,MAAM,CAAE;YAC5FL,WAAWM,IAAI,CAACN,WAAWO,KAAK;YAChCJ;QACF;IACF;IACA,OAAO;QAACH;QAAYC;KAAa;AACnC;AAEA;;;CAGC,GACD,SAASO;IACP,mDAAmD;IACnDC,KAAIC,GAAG,CACL,CAAC,uGAAuG,EAAEC,gBAAK,CAACC,IAAI,CAClHC,IAAAA,eAAS,EAAC,0CACT;IAGL,MAAM,IAAIC,oBAAY,CAAC;AACzB;AAQO,eAAejB,uCACpBkB,WAAmB,EACnBC,GAAa;QA2BYC;IAzBzB,iDAAiD;IACjD,IAAI,CAACD,IAAIX,MAAM,EAAE;QACfG;IACF;IAEA,kDAAkD;IAClD,EAAE;IACF,2CAA2C;IAC3C,IAAIQ,IAAIX,MAAM,KAAK,KAAK,CAACa,IAAAA,0BAAa,KAAI;QACxC,wEAAwE;QACxE,qEAAqE;QACrE,OAAOC,UAASC,kCAAkC,CAACJ,GAAG,CAAC,EAAE;IAC3D;IAEA,6EAA6E;IAC7E,MAAM,CAAChB,YAAYqB,UAAU,GAAG,MAAMtB,8BACpC,MAAMoB,UAASG,sBAAsB,CAACN;IAGxC,kEAAkE;IAClE,MAAM,EAAEC,GAAG,EAAE,GAAGM,IAAAA,mBAAS,EAACR,aAAa;QACrC,4EAA4E;QAC5ES,2BAA2B;QAC3BC,aAAa;IACf;IACA,MAAMC,oBAAmBT,WAAAA,IAAIU,GAAG,qBAAPV,SAASW,WAAW;IAE7C,MAAMC,qBAAqBH,mBACvB1B,WAAW8B,IAAI,CAAC,CAACC,WAAaA,SAASH,WAAW,KAAKF,oBACvDM;IAEJ,MAAMC,mBACJJ,sBAAuB,MAAM/B,2BAA2BE,YAAYqB;IAEtE,MAAMa,QAAQC,GAAG,CAAC;QAChB,8DAA8D;QAC9D,4DAA4D;QAC5DC,IAAAA,4CAAkC,EAACH,iBAAiB7B,oBAAoB;QACxE,2FAA2F;QAC3FsB,oBAAoB,CAACO,iBAAiBL,WAAW,GAC7CM,QAAQG,OAAO,KACfC,IAAAA,2BAAiB,EAACvB,aAAa;YAAEY,KAAK;gBAAEC,aAAaK,iBAAiBL,WAAW;YAAC;QAAE;KACzF;IAED,OAAOK;AACT;AAGO,eAAenC,2BACpBE,UAA6C,EAC7CuC,WAA0B;IAE1B,MAAMC,QAAQ,MAAMC,IAAAA,oBAAW,EAC7B,wCACAzC,WAAW0C,GAAG,CAAC,CAACC,OAAOC;QACrB,MAAMC,SACJF,MAAMvC,oBAAoB,KAAKmC,cAAc5B,gBAAK,CAACC,IAAI,GAAG,CAACkC,UAAoBA;QACjF,OAAO;YACL,mGAAmG;YACnGC,OAAOF,OACL;gBAACF,MAAMK,aAAa;gBAAE,CAAC,CAAC,EAAEL,MAAMf,WAAW,CAAC,GAAG,CAAC;gBAAEe,MAAMM,eAAe;aAAC,CAACC,IAAI,CAAC;YAEhFP,OAAOC;QACT;IACF;IAGF,OAAO5C,UAAU,CAACwC,MAAM;AAC1B"}