import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as r from"../../ui/legacy/components/source_frame/source_frame.js";import*as o from"../../ui/legacy/legacy.js";import*as s from"../../core/platform/platform.js";import*as n from"../../core/sdk/sdk.js";import*as a from"../../models/logs/logs.js";import"../../ui/components/buttons/buttons.js";import*as l from"../../ui/visual_logging/visual_logging.js";import*as d from"../../ui/legacy/components/data_grid/data_grid.js";import*as c from"../mobile_throttling/mobile_throttling.js";import*as h from"../settings/emulation/components/components.js";import*as u from"../../models/bindings/bindings.js";import*as p from"../../models/workspace/workspace.js";import*as g from"./forward/forward.js";import*as m from"../../ui/components/icon_button/icon_button.js";import*as w from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as k from"../../ui/legacy/components/utils/utils.js";import{PanelUtils as b}from"../utils/utils.js";import*as v from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as f from"./components/components.js";import*as C from"../../ui/legacy/components/cookie_table/cookie_table.js";import*as S from"../../ui/legacy/components/object_ui/object_ui.js";import*as y from"../../models/text_utils/text_utils.js";import*as T from"../../core/root/root.js";import*as x from"../../models/har/har.js";import*as R from"../../models/persistence/persistence.js";import*as I from"../sources/sources.js";import*as q from"../../ui/components/adorners/adorners.js";import*as L from"../../ui/components/render_coordinator/render_coordinator.js";import*as F from"../../ui/legacy/theme_support/theme_support.js";import*as P from"../../models/trace/trace.js";import*as H from"../search/search.js";import*as M from"../timeline/utils/utils.js";const E=new CSSStyleSheet;E.replaceSync(".panel.network .toolbar.binary-view-toolbar{border-top:1px solid var(--sys-color-divider);border-bottom:0;padding-left:5px}.binary-view-copied-text{opacity:100%}.binary-view-copied-text.fadeout{opacity:0%;transition:opacity 1s}\n/*# sourceURL=binaryResourceView.css */\n");const A={copiedAsBase:"Copied as `Base64`",hexViewer:"`Hex` Viewer",copiedAsHex:"Copied as `Hex`",copiedAsUtf:"Copied as `UTF-8`",binaryViewType:"Binary view type",copyToClipboard:"Copy to clipboard",copyAsBase:"Copy as `Base64`",copyAsHex:"Copy as `Hex`",copyAsUtf:"Copy as `UTF-8`"},B=i.i18n.registerUIStrings("panels/network/BinaryResourceView.ts",A),U=i.i18n.getLocalizedString.bind(void 0,B);class N extends o.Widget.VBox{binaryResourceViewFactory;toolbar;binaryViewObjects;binaryViewTypeSetting;binaryViewTypeCombobox;copiedText;addFadeoutSettimeoutId;lastView;constructor(t,s,n){super(),this.binaryResourceViewFactory=new r.BinaryResourceViewFactory.BinaryResourceViewFactory(t,s,n),this.toolbar=new o.Toolbar.Toolbar("binary-view-toolbar",this.element),this.binaryViewObjects=[new V("base64",i.i18n.lockedString("Base64"),U(A.copiedAsBase),this.binaryResourceViewFactory.createBase64View.bind(this.binaryResourceViewFactory),(()=>Promise.resolve(this.binaryResourceViewFactory.base64()))),new V("hex",U(A.hexViewer),U(A.copiedAsHex),this.binaryResourceViewFactory.createHexView.bind(this.binaryResourceViewFactory),this.binaryResourceViewFactory.hex.bind(this.binaryResourceViewFactory)),new V("utf8",i.i18n.lockedString("UTF-8"),U(A.copiedAsUtf),this.binaryResourceViewFactory.createUtf8View.bind(this.binaryResourceViewFactory),this.binaryResourceViewFactory.utf8.bind(this.binaryResourceViewFactory))],this.binaryViewTypeSetting=e.Settings.Settings.instance().createSetting("binary-view-type","hex"),this.binaryViewTypeCombobox=new o.Toolbar.ToolbarComboBox(this.binaryViewTypeChanged.bind(this),U(A.binaryViewType));for(const e of this.binaryViewObjects)this.binaryViewTypeCombobox.addOption(this.binaryViewTypeCombobox.createOption(e.label,e.type));this.toolbar.appendToolbarItem(this.binaryViewTypeCombobox);const a=new o.Toolbar.ToolbarButton(U(A.copyToClipboard),"copy");a.addEventListener("Click",(e=>{this.copySelectedViewToClipboard()}),this),this.toolbar.appendToolbarItem(a),this.copiedText=new o.Toolbar.ToolbarText,this.copiedText.element.classList.add("binary-view-copied-text"),this.toolbar.element.appendChild(this.copiedText.element),this.addFadeoutSettimeoutId=null,this.lastView=null,this.updateView()}getCurrentViewObject(){const e=this.binaryViewObjects.find((e=>e.type===this.binaryViewTypeSetting.get()));return console.assert(Boolean(e),`No binary view found for binary view type found in setting 'binary-view-type': ${this.binaryViewTypeSetting.get()}`),e||null}async copySelectedViewToClipboard(){const e=this.getCurrentViewObject();e&&(t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(await e.content()),this.copiedText.setText(e.copiedMessage),this.copiedText.element.classList.remove("fadeout"),this.addFadeoutSettimeoutId&&(clearTimeout(this.addFadeoutSettimeoutId),this.addFadeoutSettimeoutId=null),this.addFadeoutSettimeoutId=window.setTimeout(function(){this.copiedText.element.classList.add("fadeout")}.bind(this),2e3))}wasShown(){this.updateView(),this.registerCSSFiles([E])}updateView(){const e=this.getCurrentViewObject();if(!e)return;const t=e.getView();t!==this.lastView&&(this.lastView&&this.lastView.detach(),this.lastView=t,t.show(this.element,this.toolbar.element),this.binaryViewTypeCombobox.selectElement().value=this.binaryViewTypeSetting.get())}binaryViewTypeChanged(){const e=this.binaryViewTypeCombobox.selectedOption();if(!e)return;const t=e.value;this.binaryViewTypeSetting.get()!==t&&(this.binaryViewTypeSetting.set(t),this.updateView())}addCopyToContextMenu(e,i){const r=e.clipboardSection().appendSubMenuItem(i,!1,"copy").footerSection();r.appendItem(U(A.copyAsBase),(async()=>{const e=this.binaryResourceViewFactory.base64();t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e)}),{jslogContext:"copy-as-base"}),r.appendItem(U(A.copyAsHex),(async()=>{const e=await this.binaryResourceViewFactory.hex();t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e)}),{jslogContext:"copy-as-hex"}),r.appendItem(U(A.copyAsUtf),(async()=>{const e=await this.binaryResourceViewFactory.utf8();t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e)}),{jslogContext:"copy-as-utf"})}}class V{type;label;copiedMessage;content;createViewFn;view;constructor(e,t,i,r,o){this.type=e,this.label=t,this.copiedMessage=i,this.content=o,this.createViewFn=r,this.view=null}getView(){return this.view||(this.view=this.createViewFn()),this.view}}var O=Object.freeze({__proto__:null,BinaryResourceView:N,BinaryViewObject:V});const W=new CSSStyleSheet;W.replaceSync(".list{border:none!important;border-top:1px solid var(--sys-color-divider)!important}.blocking-disabled{opacity:80%}.editor-container{padding:0 4px}.no-blocked-urls,\n.blocked-urls{overflow-x:hidden;overflow-y:auto}.no-blocked-urls{display:flex;justify-content:center;padding:10px;& devtools-button{display:flex;justify-content:center;margin-top:var(--sys-size-8)}}.no-blocked-urls > span{white-space:pre}.blocked-url{display:flex;flex-direction:row;align-items:center;flex:auto}.blocked-url-count{flex:none;padding-right:9px}.blocked-url-checkbox{margin-left:8px;flex:none}.blocked-url-checkbox:focus{outline:auto 5px -webkit-focus-ring-color}.blocked-url-label{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;flex:auto;padding:0 3px}.blocked-url-edit-row{flex:none;display:flex;flex-direction:row;margin:7px 5px 0;align-items:center}.blocked-url-edit-value{user-select:none;flex:1 1 0}.blocked-url-edit-row input{width:100%;text-align:inherit;height:22px}\n/*# sourceURL=blockedURLsPane.css */\n");const D={enableNetworkRequestBlocking:"Enable network request blocking",addPattern:"Add pattern",addNetworkRequestBlockingPattern:"Add network request blocking pattern",networkRequestsAreNotBlockedS:"Network requests are not blocked. {PH1}",dBlocked:"{PH1} blocked",textPatternToBlockMatching:"Text pattern to block matching requests; use * for wildcard",patternInputCannotBeEmpty:"Pattern input cannot be empty.",patternAlreadyExists:"Pattern already exists.",itemDeleted:"Item successfully deleted"},j=i.i18n.registerUIStrings("panels/network/BlockedURLsPane.ts",D),G=i.i18n.getLocalizedString.bind(void 0,j);class z extends o.Widget.VBox{manager;toolbar;enabledCheckbox;list;editor;blockedCountForUrl;constructor(){super(!0),this.element.setAttribute("jslog",`${l.panel("network.blocked-urls").track({resize:!0})}`),this.manager=n.NetworkManager.MultitargetNetworkManager.instance(),this.manager.addEventListener("BlockedPatternsChanged",this.update,this),this.toolbar=new o.Toolbar.Toolbar("",this.contentElement),this.enabledCheckbox=new o.Toolbar.ToolbarCheckbox(G(D.enableNetworkRequestBlocking),void 0,this.toggleEnabled.bind(this),"network.enable-request-blocking"),this.toolbar.appendToolbarItem(this.enabledCheckbox),this.toolbar.appendSeparator(),this.toolbar.appendToolbarItem(o.Toolbar.Toolbar.createActionButtonForId("network.add-network-request-blocking-pattern")),this.toolbar.appendToolbarItem(o.Toolbar.Toolbar.createActionButtonForId("network.remove-all-network-request-blocking-patterns")),this.toolbar.element.setAttribute("jslog",`${l.toolbar()}`),this.list=new o.ListWidget.ListWidget(this),this.list.element.classList.add("blocked-urls"),this.list.setEmptyPlaceholder(this.createEmptyPlaceholder()),this.list.show(this.contentElement),this.editor=null,this.blockedCountForUrl=new Map,n.TargetManager.TargetManager.instance().addModelListener(n.NetworkManager.NetworkManager,n.NetworkManager.Events.RequestFinished,this.onRequestFinished,this,{scoped:!0}),this.update(),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.Reset,this.onNetworkLogReset,this)}createEmptyPlaceholder(){const e=this.contentElement.createChild("div","no-blocked-urls"),t=o.UIUtils.createTextButton(G(D.addPattern),this.addPattern.bind(this),{className:"add-button",jslogContext:"network.add-network-request-blocking-pattern",variant:"primary"});return o.ARIAUtils.setLabel(t,G(D.addNetworkRequestBlockingPattern)),e.appendChild(i.i18n.getFormatLocalizedString(j,D.networkRequestsAreNotBlockedS,{PH1:t})),e}addPattern(){this.manager.setBlockingEnabled(!0),this.list.addNewItem(0,{url:s.DevToolsPath.EmptyUrlString,enabled:!0})}removeAllPatterns(){this.manager.setBlockedPatterns([])}renderItem(e,t){const i=this.blockedRequestsCount(e.url),r=document.createElement("div");r.classList.add("blocked-url");const o=r.createChild("input","blocked-url-checkbox");return o.type="checkbox",o.checked=e.enabled,o.disabled=!t,o.setAttribute("jslog",`${l.toggle().track({change:!0})}`),r.createChild("div","blocked-url-label").textContent=e.url,r.createChild("div","blocked-url-count").textContent=G(D.dBlocked,{PH1:i}),t&&(r.addEventListener("click",(t=>this.togglePattern(e,t))),o.addEventListener("click",(t=>this.togglePattern(e,t)))),r}togglePattern(e,t){t.consume(!0);const i=this.manager.blockedPatterns();i.splice(i.indexOf(e),1,{enabled:!e.enabled,url:e.url}),this.manager.setBlockedPatterns(i)}toggleEnabled(){this.manager.setBlockingEnabled(!this.manager.blockingEnabled()),this.update()}removeItemRequested(e,t){const i=this.manager.blockedPatterns();i.splice(t,1),this.manager.setBlockedPatterns(i),o.ARIAUtils.alert(D.itemDeleted)}beginEdit(e){return this.editor=this.createEditor(),this.editor.control("url").value=e.url,this.editor}commitEdit(e,t,i){const r=t.control("url").value,o=this.manager.blockedPatterns();i?o.push({enabled:!0,url:r}):o.splice(o.indexOf(e),1,{enabled:!0,url:r}),this.manager.setBlockedPatterns(o)}createEditor(){if(this.editor)return this.editor;const e=new o.ListWidget.Editor,t=e.contentElement();t.createChild("div","blocked-url-edit-row").createChild("div").textContent=G(D.textPatternToBlockMatching);const i=t.createChild("div","blocked-url-edit-row"),r=e.createInput("url","text","",((e,t,i)=>{let r,o=!0;return i.value?this.manager.blockedPatterns().find((e=>e.url===i.value))&&(r=G(D.patternAlreadyExists),o=!1):(r=G(D.patternInputCannotBeEmpty),o=!1),{valid:o,errorMessage:r}}));return i.createChild("div","blocked-url-edit-value").appendChild(r),e}update(){const e=this.manager.blockingEnabled();this.list.element.classList.toggle("blocking-disabled",!e&&Boolean(this.manager.blockedPatterns().length)),this.enabledCheckbox.setChecked(e),this.list.clear();for(const t of this.manager.blockedPatterns())this.list.appendItem(t,e)}blockedRequestsCount(e){if(!e)return 0;let t=0;for(const i of this.blockedCountForUrl.keys())this.matches(e,i)&&(t+=this.blockedCountForUrl.get(i));return t}matches(e,t){let i=0;const r=e.split("*");for(let e=0;e<r.length;e++){const o=r[e];if(o.length){if(i=t.indexOf(o,i),-1===i)return!1;i+=o.length}}return!0}onNetworkLogReset(e){this.blockedCountForUrl.clear(),this.update()}onRequestFinished(e){const t=e.data;if(t.wasBlocked()){const e=this.blockedCountForUrl.get(t.url())||0;this.blockedCountForUrl.set(t.url(),e+1),this.update()}}wasShown(){o.Context.Context.instance().setFlavor(z,this),super.wasShown(),this.list.registerCSSFiles([W]),this.registerCSSFiles([W])}willHide(){super.willHide(),o.Context.Context.instance().setFlavor(z,null)}}var _=Object.freeze({__proto__:null,BlockedURLsPane:z,ActionDelegate:class{handleAction(e,t){const i=e.flavor(z);if(null===i)return!1;switch(t){case"network.add-network-request-blocking-pattern":return i.addPattern(),!0;case"network.remove-all-network-request-blocking-patterns":return i.removeAllPatterns(),!0}return!1}}});const K=new CSSStyleSheet;K.replaceSync(".event-source-messages-view .data-grid{flex:auto;border:none}\n/*# sourceURL=eventSourceMessagesView.css */\n");const $={id:"Id",type:"Type",data:"Data",time:"Time",eventSource:"Event Source",copyMessage:"Copy message",clearAll:"Clear all",filterByRegex:"Filter using regex (example: https?)"},X=i.i18n.registerUIStrings("panels/network/EventSourceMessagesView.ts",$),Y=i.i18n.getLocalizedString.bind(void 0,X);class J extends o.Widget.VBox{request;dataGrid;mainToolbar;clearAllButton;filterTextInput;filterRegex;messageFilterSetting=e.Settings.Settings.instance().createSetting("network-event-source-message-filter","");constructor(e){super(),this.element.classList.add("event-source-messages-view"),this.element.setAttribute("jslog",`${l.pane("event-stream").track({resize:!0})}`),this.request=e,this.mainToolbar=new o.Toolbar.Toolbar(""),this.clearAllButton=new o.Toolbar.ToolbarButton(Y($.clearAll),"clear"),this.clearAllButton.addEventListener("Click",this.clearMessages,this),this.mainToolbar.appendToolbarItem(this.clearAllButton);const t=Y($.filterByRegex);this.filterTextInput=new o.Toolbar.ToolbarFilter(t,.4),this.filterTextInput.addEventListener("TextChanged",this.updateFilterSetting,this);const i=this.messageFilterSetting.get();this.filterRegex=null,this.setFilter(i),i&&this.filterTextInput.setValue(i),this.mainToolbar.appendToolbarItem(this.filterTextInput),this.element.appendChild(this.mainToolbar.element);const r=[{id:"id",title:Y($.id),sortable:!0,weight:8},{id:"type",title:Y($.type),sortable:!0,weight:8},{id:"data",title:Y($.data),sortable:!1,weight:88},{id:"time",title:Y($.time),sortable:!0,weight:8}];this.dataGrid=new d.SortableDataGrid.SortableDataGrid({displayName:Y($.eventSource),columns:r,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setStriped(!0),this.dataGrid.setStickToBottom(!0),this.dataGrid.setRowContextMenuCallback(this.onRowContextMenu.bind(this)),this.dataGrid.markColumnAsSortedBy("time",d.DataGrid.Order.Ascending),this.sortItems(),this.dataGrid.addEventListener("SortingChanged",this.sortItems,this),this.dataGrid.setName("event-source-messages-view"),this.dataGrid.asWidget().show(this.element)}wasShown(){this.refresh(),this.registerCSSFiles([K]),this.request.addEventListener(n.NetworkRequest.Events.EventSourceMessageAdded,this.messageAdded,this)}willHide(){this.request.removeEventListener(n.NetworkRequest.Events.EventSourceMessageAdded,this.messageAdded,this)}messageAdded(e){const t=e.data;this.messageFilter(t)&&this.dataGrid.insertChild(new Z(t))}messageFilter(e){return!this.filterRegex||this.filterRegex.test(e.eventName)||this.filterRegex.test(e.eventId)||this.filterRegex.test(e.data)}clearMessages(){te.set(this.request,this.request.eventSourceMessages().length),this.refresh()}updateFilterSetting(){const e=this.filterTextInput.value();this.messageFilterSetting.set(e),this.setFilter(e),this.refresh()}setFilter(e){if(this.filterRegex=null,e)try{this.filterRegex=new RegExp(e,"i")}catch(e){this.filterRegex=new RegExp("(?!)","i")}}sortItems(){const e=this.dataGrid.sortColumnId();if(!e)return;const t=ee[e];t&&this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}onRowContextMenu(e,i){e.clipboardSection().appendItem(Y($.copyMessage),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(t.InspectorFrontendHost.InspectorFrontendHostInstance,i.data.data),{jslogContext:"copy"})}refresh(){this.dataGrid.rootNode().removeChildren();let e=this.request.eventSourceMessages();const t=te.get(this.request)||0;e=e.slice(t),e=e.filter(this.messageFilter.bind(this)),e.forEach((e=>this.dataGrid.insertChild(new Z(e))))}}class Z extends d.SortableDataGrid.SortableDataGridNode{message;constructor(e){const t=new Date(1e3*e.time),i=("0"+t.getHours()).substr(-2)+":"+("0"+t.getMinutes()).substr(-2)+":"+("0"+t.getSeconds()).substr(-2)+"."+("00"+t.getMilliseconds()).substr(-3),r=document.createElement("div");o.UIUtils.createTextChild(r,i),o.Tooltip.Tooltip.install(r,t.toLocaleString()),super({id:e.eventId,type:e.eventName,data:e.data,time:r}),this.message=e}}function Q(e,t,i){const r=e(t.message),o=e(i.message);return r<o?-1:r>o?1:0}const ee={id:Q.bind(null,(e=>e.eventId)),type:Q.bind(null,(e=>e.eventName)),time:Q.bind(null,(e=>e.time))},te=new WeakMap;var ie=Object.freeze({__proto__:null,EventSourceMessagesView:J,EventSourceMessageNode:Z,Comparators:ee});const re=new CSSStyleSheet;re.replaceSync('.network-config{padding:12px;display:block}.network-config-group{display:flex;padding-bottom:10px;flex-wrap:wrap;flex:0 0 auto;min-height:30px}.network-config-title{margin-right:16px;width:130px}.network-config-fields{flex:2 0 200px}.network-config-fields span:first-of-type,\n.network-config-fields .network-config-accepted-encoding-custom{padding:3px 0}.panel-section-separator{height:1px;margin-bottom:10px;background:var(--sys-color-divider)}.network-config-disable-cache{line-height:28px;border-top:none;padding-top:0}.network-config-input-validation-error{color:var(--sys-color-error);margin:5px 0}.network-config-input-validation-error:empty{display:none}.network-config-throttling .chrome-select{width:100%;max-width:250px}.network-config-throttling > .network-config-title{line-height:24px}.network-config-ua > .network-config-title{line-height:20px}.network-config-ua span[is="dt-radio"].checked > *{display:none}.network-config-ua input{display:block;width:calc(100% - 20px)}.network-config-ua input[type="text"],\n.network-config-ua .chrome-select{margin-top:8px}.network-config-ua .chrome-select{width:calc(100% - 20px);max-width:250px}.network-config-ua span[is="dt-radio"]{display:block}.network-config-ua-custom{opacity:50%;padding-bottom:8px}.network-config-ua-custom.checked{opacity:100%}.client-hints-form{margin-top:14px;width:min(100%,400px)}.status-text{padding:10px;color:var(--sys-color-tertiary)}\n/*# sourceURL=networkConfigView.css */\n');const oe={custom:"Custom...",enterACustomUserAgent:"Enter a custom user agent",customUserAgentFieldIsRequired:"Custom user agent field is required",caching:"Caching",disableCache:"Disable cache",networkThrottling:"Network throttling",userAgent:"User agent",selectAutomatically:"Use browser default",acceptedEncoding:"Accepted `Content-Encoding`s",clientHintsStatusText:"User agent updated.",networkConditionsPanelShown:"Network conditions shown"},se=i.i18n.registerUIStrings("panels/network/NetworkConfigView.ts",oe),ne=i.i18n.getLocalizedString.bind(void 0,se);let ae;class le extends o.Widget.VBox{constructor(){super(!0),this.element.setAttribute("jslog",`${l.panel("network-conditions").track({resize:!0})}`),this.contentElement.classList.add("network-config"),this.createCacheSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createNetworkThrottlingSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createUserAgentSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createAcceptedEncodingSection()}static instance(e={forceNew:null}){const{forceNew:t}=e;return ae&&!t||(ae=new le),ae}static createUserAgentSelectAndInput(t){const i=e.Settings.Settings.instance().createSetting("custom-user-agent",""),r=e.Settings.Settings.instance().createSetting("custom-user-agent-metadata",null),a=document.createElement("select");a.setAttribute("jslog",`${l.dropDown().track({change:!0}).context(i.name)}`),o.ARIAUtils.setLabel(a,t);const d={title:ne(oe.custom),value:"custom"};a.appendChild(o.UIUtils.createOption(d.title,d.value,"custom"));for(const e of ce){const t=a.createChild("optgroup");t.label=e.title;for(const i of e.values){const e=n.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion(i.value);t.appendChild(o.UIUtils.createOption(i.title,e,s.StringUtilities.toKebabCase(i.title)))}}a.selectedIndex=0;const c=o.UIUtils.createInput("","text");c.setAttribute("jslog",`${l.textField().track({change:!0}).context(i.name)}`),c.value=i.get(),o.Tooltip.Tooltip.install(c,i.get()),c.placeholder=ne(oe.enterACustomUserAgent),c.required=!0,o.ARIAUtils.setLabel(c,c.placeholder);const h=document.createElement("div");function u(){const e=i.get(),t=a.options;let r=!1;for(let i=0;i<t.length;++i)if(t[i].value===e){a.selectedIndex=i,r=!0;break}r||(a.selectedIndex=0)}return h.classList.add("network-config-input-validation-error"),o.ARIAUtils.markAsAlert(h),c.value||(h.textContent=ne(oe.customUserAgentFieldIsRequired)),u(),a.addEventListener("change",(function(){const e=a.options[a.selectedIndex].value;if(e!==d.value){i.set(e),c.value=e,o.Tooltip.Tooltip.install(c,e);const t=de(e);r.set(t),n.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(e,t)}else r.set(null),c.select();h.textContent="";const t=new CustomEvent("user-agent-change",{detail:{value:e}});a.dispatchEvent(t)}),!1),c.addEventListener("input",(function(){i.get()!==c.value&&(c.value?h.textContent="":h.textContent=ne(oe.customUserAgentFieldIsRequired),i.set(c.value),o.Tooltip.Tooltip.install(c,c.value),u())}),!1),{select:a,input:c,error:h}}createSection(e,t){const i=this.contentElement.createChild("section","network-config-group");return t&&i.classList.add(t),i.createChild("div","network-config-title").textContent=e,i.createChild("div","network-config-fields")}createCacheSection(){this.createSection(ne(oe.caching),"network-config-disable-cache").appendChild(o.SettingsUI.createSettingCheckbox(ne(oe.disableCache),e.Settings.Settings.instance().moduleSetting("cache-disabled"),!0))}createNetworkThrottlingSection(){const e=ne(oe.networkThrottling),t=this.createSection(e,"network-config-throttling").createChild("select","chrome-select");c.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(t),o.ARIAUtils.setLabel(t,e)}createUserAgentSection(){const t=e.Settings.Settings.instance().createSetting("custom-user-agent-metadata",null),i=e.Settings.Settings.instance().createSetting("custom-user-agent",""),r=ne(oe.userAgent),s=this.createSection(r,"network-config-ua"),a=o.UIUtils.CheckboxLabel.create(ne(oe.selectAutomatically),!0,void 0,i.name);s.appendChild(a);const l=a.checkboxElement;i.addChangeListener((()=>{if(l.checked)return;const e=i.get(),t=de(e);n.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(e,t)}));const d=s.createChild("div","network-config-ua-custom");l.addEventListener("change",k);const c=le.createUserAgentSelectAndInput(r);c.select.classList.add("chrome-select"),d.appendChild(c.select),d.appendChild(c.input),d.appendChild(c.error);const u=d.createChild("div","client-hints-form"),p=new h.UserAgentClientHintsForm.UserAgentClientHintsForm,g=t.get(),m=de(c.select.value);p.value={showMobileCheckbox:!0,showSubmitButton:!0,metaData:g||m||void 0},u.appendChild(p),c.select.addEventListener("user-agent-change",(e=>{const t=e.detail.value,i=t?de(t):null;p.value={metaData:i||void 0,showMobileCheckbox:!0,showSubmitButton:!0},w.textContent=""})),p.addEventListener("clienthintschange",(()=>{c.select.value="custom",w.textContent=""})),p.addEventListener("clienthintssubmit",(e=>{const r=e.detail.value,o=i.get();t.set(r),n.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(o,r),w.textContent=ne(oe.clientHintsStatusText)}));const w=s.createChild("span","status-text");function k(){const e=!l.checked;d.classList.toggle("checked",e),c.select.disabled=!e,c.input.disabled=!e,c.error.hidden=!e,p.disabled=!e;const t=e?i.get():"",r=e?de(t):null;n.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(t,r)}w.textContent="",k()}createAcceptedEncodingSection(){const t=e.Settings.Settings.instance().createSetting("use-custom-accepted-encodings",!1),i=e.Settings.Settings.instance().createSetting("custom-accepted-encodings","gzip,br,deflate"),r=ne(oe.acceptedEncoding),s=this.createSection(r,"network-config-accepted-encoding"),a=o.UIUtils.CheckboxLabel.create(ne(oe.selectAutomatically),!0,void 0,t.name);s.appendChild(a);const d=a.checkboxElement;function c(){t.get()?n.NetworkManager.MultitargetNetworkManager.instance().setCustomAcceptedEncodingsOverride(""===i.get()?[]:i.get().split(",")):n.NetworkManager.MultitargetNetworkManager.instance().clearCustomAcceptedEncodingsOverride()}i.addChangeListener(c),t.addChangeListener(c);const h=s.createChild("div","network-config-accepted-encoding-custom");h.setAttribute("jslog",`${l.section().context(i.name)}`),d.checked=!t.get(),d.addEventListener("change",g);const u=new Map,p={Deflate:"deflate",Gzip:"gzip",Br:"br",Zstd:"zstd"};for(const e of Object.values(p)){const t=o.UIUtils.CheckboxLabel.create(e,!0,void 0,e);h.appendChild(t),u.set(e,t.checkboxElement)}for(const[e,t]of u)t.checked=i.get().includes(e),t.addEventListener("change",g);function g(){t.set(!d.checked);const e=[];for(const[t,i]of u)i.disabled=d.checked,i.checked&&e.push(t);i.set(e.join(","))}g()}wasShown(){super.wasShown(),this.registerCSSFiles([re]),o.ARIAUtils.alert(ne(oe.networkConditionsPanelShown))}}function de(e){for(const t of ce)for(const i of t.values)if(e===n.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion(i.value))return i.metadata?(n.NetworkManager.MultitargetNetworkManager.patchUserAgentMetadataWithChromeVersion(i.metadata),i.metadata):null;return null}const ce=[{title:"Android",values:[{title:"Android (4.0.2) Browser — Galaxy Nexus",value:"Mozilla/5.0 (Linux; U; Android 4.0.2; en-us; Galaxy Nexus Build/ICL53F) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"4.0.2",architecture:"",model:"Galaxy Nexus",mobile:!0}},{title:"Android (2.3) Browser — Nexus S",value:"Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; Nexus S Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"2.3.6",architecture:"",model:"Nexus S",mobile:!0}}]},{title:"BlackBerry",values:[{title:"BlackBerry — BB10",value:"Mozilla/5.0 (BB10; Touch) AppleWebKit/537.1+ (KHTML, like Gecko) Version/10.0.0.1337 Mobile Safari/537.1+",metadata:null},{title:"BlackBerry — PlayBook 2.1",value:"Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML, like Gecko) Version/7.2.1.0 Safari/536.2+",metadata:null},{title:"BlackBerry — 9900",value:"Mozilla/5.0 (BlackBerry; U; BlackBerry 9900; en-US) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.0.0.187 Mobile Safari/534.11+",metadata:null}]},{title:"Chrome",values:[{title:"Chrome — Android Mobile",value:"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"6.0",architecture:"",model:"Nexus 5",mobile:!0}},{title:"Chrome — Android Mobile (high-end)",value:"Mozilla/5.0 (Linux; Android 10; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"10",architecture:"",model:"Pixel 4",mobile:!0}},{title:"Chrome — Android Tablet",value:"Mozilla/5.0 (Linux; Android 4.3; Nexus 7 Build/JSS15Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"4.3",architecture:"",model:"Nexus 7",mobile:!0}},{title:"Chrome — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/%s Mobile/15E148 Safari/604.1",metadata:null},{title:"Chrome — iPad",value:"Mozilla/5.0 (iPad; CPU OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/%s Mobile/15E148 Safari/604.1",metadata:null},{title:"Chrome — Chrome OS",value:"Mozilla/5.0 (X11; CrOS x86_64 10066.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Chrome OS",platformVersion:"10066.0.0",architecture:"x86",model:"",mobile:!1}},{title:"Chrome — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"macOS",platformVersion:"10_14_6",architecture:"x86",model:"",mobile:!1}},{title:"Chrome — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Windows",platformVersion:"10.0",architecture:"x86",model:"",mobile:!1}}]},{title:"Firefox",values:[{title:"Firefox — Android Mobile",value:"Mozilla/5.0 (Android 4.4; Mobile; rv:70.0) Gecko/70.0 Firefox/70.0",metadata:null},{title:"Firefox — Android Tablet",value:"Mozilla/5.0 (Android 4.4; Tablet; rv:70.0) Gecko/70.0 Firefox/70.0",metadata:null},{title:"Firefox — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4",metadata:null},{title:"Firefox — iPad",value:"Mozilla/5.0 (iPad; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4",metadata:null},{title:"Firefox — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:70.0) Gecko/20100101 Firefox/70.0",metadata:null},{title:"Firefox — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64; rv:70.0) Gecko/20100101 Firefox/70.0",metadata:null}]},{title:"Googlebot",values:[{title:"Googlebot",value:"Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",metadata:null},{title:"Googlebot Desktop",value:"Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/%s Safari/537.36",metadata:null},{title:"Googlebot Smartphone",value:"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",metadata:null}]},{title:"Internet Explorer",values:[{title:"Internet Explorer 11",value:"Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",metadata:null},{title:"Internet Explorer 10",value:"Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)",metadata:null},{title:"Internet Explorer 9",value:"Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)",metadata:null},{title:"Internet Explorer 8",value:"Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0)",metadata:null},{title:"Internet Explorer 7",value:"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)",metadata:null}]},{title:"Microsoft Edge",values:[{title:"Microsoft Edge (Chromium) — Windows",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36 Edg/%s",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Windows",platformVersion:"10.0",architecture:"x86",model:"",mobile:!1}},{title:"Microsoft Edge (Chromium) — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Chrome/%s Safari/604.1 Edg/%s",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"macOS",platformVersion:"10_14_6",architecture:"x86",model:"",mobile:!1}},{title:"Microsoft Edge — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 EdgiOS/********* Mobile/15E148 Safari/604.1",metadata:null},{title:"Microsoft Edge — iPad",value:"Mozilla/5.0 (iPad; CPU OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 EdgiOS/44.5.2 Mobile/15E148 Safari/605.1.15",metadata:null},{title:"Microsoft Edge — Android Mobile",value:"Mozilla/5.0 (Linux; Android 8.1.0; Pixel Build/OPM4.171019.021.D1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Mobile Safari/537.36 EdgA/42.0.0.2057",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"8.1.0",architecture:"",model:"Pixel",mobile:!0}},{title:"Microsoft Edge — Android Tablet",value:"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 7 Build/MOB30X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36 EdgA/42.0.0.2057",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"6.0.1",architecture:"",model:"Nexus 7",mobile:!0}},{title:"Microsoft Edge (EdgeHTML) — Windows",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19042",metadata:null},{title:"Microsoft Edge (EdgeHTML) — XBox",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19041",metadata:null}]},{title:"Opera",values:[{title:"Opera — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36 OPR/65.0.3467.48",metadata:null},{title:"Opera — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36 OPR/65.0.3467.48",metadata:null},{title:"Opera (Presto) — Mac",value:"Opera/9.80 (Macintosh; Intel Mac OS X 10.9.1) Presto/2.12.388 Version/12.16",metadata:null},{title:"Opera (Presto) — Windows",value:"Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.16",metadata:null},{title:"Opera Mobile — Android Mobile",value:"Opera/12.02 (Android 4.1; Linux; Opera Mobi/ADR-1111101157; U; en-US) Presto/2.9.201 Version/12.02",metadata:null},{title:"Opera Mini — iOS",value:"Opera/9.80 (iPhone; Opera Mini/8.0.0/34.2336; U; en) Presto/2.8.119 Version/11.10",metadata:null}]},{title:"Safari",values:[{title:"Safari — iPad iOS 13.2",value:"Mozilla/5.0 (iPad; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",metadata:null},{title:"Safari — iPhone iOS 13.2",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",metadata:null},{title:"Safari — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Safari/605.1.15",metadata:null}]},{title:"UC Browser",values:[{title:"UC Browser — Android Mobile",value:"Mozilla/5.0 (Linux; U; Android 8.1.0; en-US; Nexus 6P Build/OPM7.181205.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.11.1.1197 Mobile Safari/537.36",metadata:null},{title:"UC Browser — iOS",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X; zh-CN) AppleWebKit/537.51.1 (KHTML, like Gecko) Mobile/16B92 UCBrowser/12.1.7.1109 Mobile AliApp(TUnionSDK/********)",metadata:null},{title:"UC Browser — Windows Phone",value:"Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920) UCBrowser/10.1.0.563 Mobile",metadata:null}]}];var he=Object.freeze({__proto__:null,NetworkConfigView:le,userAgentGroups:ce});const ue={redirect:"Redirect",sPreflight:"{PH1} + Preflight",preflight:"Preflight",selectPreflightRequest:"Select preflight request",failed:"(failed)",data:"(data)",canceled:"(canceled)",other:"other",csp:"csp",origin:"origin",devtools:"devtools",blockeds:"(blocked:{PH1})",blockedTooltip:"This request was blocked due to misconfigured response headers, click to view the headers",corsError:"CORS error",crossoriginResourceSharingErrorS:"Cross-Origin Resource Sharing error: {PH1}",finished:"Finished",pendingq:"(pending)",unknown:"(unknown)",unknownExplanation:"The request status cannot be shown here because the page that issued it unloaded while the request was in flight. You can use chrome://net-export to capture a network log and see all request details.",push:"Push / ",parser:"Parser",script:"Script",preload:"Preload",earlyHints:"early-hints",signedexchange:"signed-exchange",selectTheRequestThatTriggered:"Select the request that triggered this preflight",otherC:"Other",memoryCache:"(memory cache)",servedFromMemoryCacheResource:"Served from memory cache, resource size: {PH1}",serviceWorker:"(`ServiceWorker`)",servedFromServiceWorkerResource:"Served from `ServiceWorker`, resource size: {PH1}",servedFromSignedHttpExchange:"Served from Signed HTTP Exchange, resource size: {PH1}",servedFromWebBundle:"Served from Web Bundle, resource size: {PH1}",prefetchCache:"(prefetch cache)",servedFromPrefetchCacheResource:"Served from prefetch cache, resource size: {PH1}",diskCache:"(disk cache)",servedFromDiskCacheResourceSizeS:"Served from disk cache, resource size: {PH1}",matchedToServiceWorkerRouter:"Matched to `ServiceWorker router`#{PH1}, resource size: {PH2}",matchedToServiceWorkerRouterWithNetworkSource:"Matched to `ServiceWorker router`#{PH1}, {PH2} transferred over network, resource size: {PH3}",pending:"Pending",level:"level 1",webBundleError:"Web Bundle error",webBundleInnerRequest:"Served from Web Bundle",webBundle:"(Web Bundle)",timeSubtitleTooltipText:"Latency (response received time - start time)",alternativeJobWonWithoutRace:"`Chrome` used a `HTTP/3` connection induced by an '`Alt-Svc`' header without racing against establishing a connection using a different `HTTP` version.",alternativeJobWonRace:"`Chrome` used a `HTTP/3` connection induced by an '`Alt-Svc`' header because it won a race against establishing a connection using a different `HTTP` version.",mainJobWonRace:"`Chrome` used this protocol because it won a race against establishing a `HTTP/3` connection.",mappingMissing:"`Chrome` did not use an alternative `HTTP` version because no alternative protocol information was available when the request was issued, but an '`Alt-Svc`' header was present in the response.",broken:"`Chrome` did not try to establish a `HTTP/3` connection because it was marked as broken.",dnsAlpnH3JobWonWithoutRace:"`Chrome` used a `HTTP/3` connection due to the `DNS record` indicating `HTTP/3` support. There was no race against establishing a connection using a different `HTTP` version.",dnsAlpnH3JobWonRace:"`Chrome` used a `HTTP/3` connection due to the `DNS record` indicating `HTTP/3` support, which won a race against establishing a connection using a different `HTTP` version.",requestContentHeadersOverridden:"Both request content and headers are overridden",requestContentOverridden:"Request content is overridden",requestHeadersOverridden:"Request headers are overridden",initialPriorityToolTip:"{PH1}, Initial priority: {PH2}",thirdPartyPhaseout:"Cookies for this request are blocked due to third-party cookie phaseout. Learn more in the Issues tab."},pe=i.i18n.registerUIStrings("panels/network/NetworkDataGridNode.ts",ue),ge=i.i18n.getLocalizedString.bind(void 0,pe);class me extends d.SortableDataGrid.SortableDataGridNode{parentViewInternal;isHovered;showingInitiatorChainInternal;requestOrFirstKnownChildRequestInternal;constructor(e){super({}),this.parentViewInternal=e,this.isHovered=!1,this.showingInitiatorChainInternal=!1,this.requestOrFirstKnownChildRequestInternal=null}displayName(){return""}displayType(){return""}createCell(e){const t=this.createTD(e);return this.renderCell(t,e),t}renderCell(e,t){}isFailed(){return!1}backgroundColor(){const e=we,t=document.hasFocus(),i=this.dataGrid&&this.dataGrid.element===document.activeElement,r=this.isFailed();return this.selected&&t&&i&&r?e.FocusSelectedHasError:this.selected&&t&&i?e.FocusSelected:this.selected?e.Selected:this.hovered()?e.Hovered:this.isOnInitiatorPath()?e.InitiatorPath:this.isOnInitiatedPath()?e.InitiatedPath:this.isStriped()?e.Stripe:e.Default}updateBackgroundColor(){const e=this.existingElement();e&&(e.style.backgroundColor=`var(${this.backgroundColor()})`,this.parentViewInternal.stylesChanged())}setStriped(e){super.setStriped(e),this.updateBackgroundColor()}select(e){super.select(e),this.updateBackgroundColor(),this.parentViewInternal.updateNodeSelectedClass(!0)}deselect(e){super.deselect(e),this.updateBackgroundColor(),this.parentViewInternal.updateNodeSelectedClass(!1)}parentView(){return this.parentViewInternal}hovered(){return this.isHovered}showingInitiatorChain(){return this.showingInitiatorChainInternal}nodeSelfHeight(){return this.parentViewInternal.rowHeight()}setHovered(e,t){this.isHovered===e&&this.showingInitiatorChainInternal===t||(this.isHovered!==e&&(this.isHovered=e,this.attached()&&this.element().classList.toggle("hover",e)),this.showingInitiatorChainInternal!==t&&(this.showingInitiatorChainInternal=t,this.showingInitiatorChainChanged()),this.parentViewInternal.stylesChanged(),this.updateBackgroundColor())}showingInitiatorChainChanged(){}isOnInitiatorPath(){return!1}isOnInitiatedPath(){return!1}request(){return null}isNavigationRequest(){return!1}clearFlatNodes(){super.clearFlatNodes(),this.requestOrFirstKnownChildRequestInternal=null}requestOrFirstKnownChildRequest(){if(this.requestOrFirstKnownChildRequestInternal)return this.requestOrFirstKnownChildRequestInternal;let e=this.request();if(e||!this.hasChildren())return this.requestOrFirstKnownChildRequestInternal=e,this.requestOrFirstKnownChildRequestInternal;let t=null;const i=this.flatChildren();for(let r=0;r<i.length;r++)e=i[r].request(),(!t||e&&e.issueTime()<t.issueTime())&&(t=e);return this.requestOrFirstKnownChildRequestInternal=t,this.requestOrFirstKnownChildRequestInternal}}const we={Default:"--color-grid-default",Stripe:"--color-grid-stripe",Navigation:"--network-grid-navigation-color",Hovered:"--color-grid-hovered",InitiatorPath:"--network-grid-initiator-path-color",InitiatedPath:"--network-grid-initiated-path-color",Selected:"--color-grid-selected",FocusSelected:"--color-grid-focus-selected",FocusSelectedHasError:"--network-grid-focus-selected-color-has-error",FromFrame:"--network-grid-from-frame-color"};class ke extends me{nameCell;initiatorCell;requestInternal;isNavigationRequestInternal;selectable;isOnInitiatorPathInternal;isOnInitiatedPathInternal;linkifiedInitiatorAnchor;constructor(e,t){super(e),this.nameCell=null,this.initiatorCell=null,this.requestInternal=t,this.isNavigationRequestInternal=!1,this.selectable=!0,this.isOnInitiatorPathInternal=!1,this.isOnInitiatedPathInternal=!1}static NameComparator(e,t){const i=e.displayName().toLowerCase(),r=t.displayName().toLowerCase();if(i===r){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();return i&&r?i.identityCompare(r):i?-1:1}return i<r?-1:1}static RemoteAddressComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=i.remoteAddress(),s=r.remoteAddress();return o>s?1:s>o?-1:i.identityCompare(r)}static SizeComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();return i&&r?r.cached()&&!i.cached()?1:i.cached()&&!r.cached()?-1:i.transferSize-r.transferSize||i.resourceSize-r.resourceSize||i.identityCompare(r):i?1:-1}static TypeComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=e.displayType(),s=t.displayType();return o>s?1:s>o?-1:i.identityCompare(r)}static InitiatorComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=e instanceof ke&&e.initiatorCell,s=t instanceof ke&&t.initiatorCell;if(!o||!s)return o?1:-1;const n=e,a=t,l=n.linkifiedInitiatorAnchor?n.linkifiedInitiatorAnchor.textContent||"":n.initiatorCell.title,d=a.linkifiedInitiatorAnchor?a.linkifiedInitiatorAnchor.textContent||"":a.initiatorCell.title;return l.localeCompare(d)}static InitiatorAddressSpaceComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=i.clientSecurityState(),s=r.clientSecurityState();return o&&s?o.initiatorIPAddressSpace.localeCompare(s.initiatorIPAddressSpace):o?1:-1}static RemoteAddressSpaceComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();return i&&r?i.remoteAddressSpace().localeCompare(r.remoteAddressSpace()):i?1:-1}static RequestCookiesCountComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;return i.includedRequestCookies().length-r.includedRequestCookies().length||i.identityCompare(r)}static ResponseCookiesCountComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;return(i.responseCookies?i.responseCookies.length:0)-(r.responseCookies?r.responseCookies.length:0)||i.identityCompare(r)}static PriorityComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=i.priority();let s=o?w.NetworkPriorities.networkPriorityWeight(o):0;s=s||0;const n=r.priority();let a=n?w.NetworkPriorities.networkPriorityWeight(n):0;return a=a||0,s-a||i.identityCompare(r)}static RequestPropertyComparator(e,t,i){const r=t.requestOrFirstKnownChildRequest(),o=i.requestOrFirstKnownChildRequest();if(!r||!o)return r?1:-1;const s=r[e],n=o[e];return s===n?r.identityCompare(o):s>n?1:-1}static RequestURLComparator(e,t){const i=e.requestOrFirstKnownChildRequest(),r=t.requestOrFirstKnownChildRequest();if(!i||!r)return i?1:-1;const o=i.url(),s=r.url();return o===s?i.identityCompare(r):o>s?1:-1}static ResponseHeaderStringComparator(e,t,i){const r=t.requestOrFirstKnownChildRequest(),o=i.requestOrFirstKnownChildRequest();if(!r||!o)return r?1:-1;const s=String(r.responseHeaderValue(e)||""),n=String(o.responseHeaderValue(e)||"");return s.localeCompare(n)||r.identityCompare(o)}static ResponseHeaderNumberComparator(e,t,i){const r=t.requestOrFirstKnownChildRequest(),o=i.requestOrFirstKnownChildRequest();if(!r||!o)return r?1:-1;const s=r.responseHeaderValue(e),n=void 0!==s?parseFloat(s):-1/0,a=o.responseHeaderValue(e),l=void 0!==a?parseFloat(a):-1/0;return n===l?r.identityCompare(o):n>l?1:-1}static ResponseHeaderDateComparator(e,t,i){const r=t.requestOrFirstKnownChildRequest(),o=i.requestOrFirstKnownChildRequest();if(!r||!o)return r?1:-1;const s=r.responseHeaderValue(e),n=o.responseHeaderValue(e),a=s?new Date(s).getTime():-1/0,l=n?new Date(n).getTime():-1/0;return a===l?r.identityCompare(o):a>l?1:-1}showingInitiatorChainChanged(){const e=this.showingInitiatorChain(),t=a.NetworkLog.NetworkLog.instance().initiatorGraphForRequest(this.requestInternal);for(const i of t.initiators){if(i===this.requestInternal)continue;const t=this.parentView().nodeForRequest(i);t&&t.setIsOnInitiatorPath(e)}for(const i of t.initiated.keys()){if(i===this.requestInternal)continue;const t=this.parentView().nodeForRequest(i);t&&t.setIsOnInitiatedPath(e)}}setIsOnInitiatorPath(e){this.isOnInitiatorPathInternal!==e&&this.attached()&&(this.isOnInitiatorPathInternal=e,this.updateBackgroundColor())}isOnInitiatorPath(){return this.isOnInitiatorPathInternal}setIsOnInitiatedPath(e){this.isOnInitiatedPathInternal!==e&&this.attached()&&(this.isOnInitiatedPathInternal=e,this.updateBackgroundColor())}isOnInitiatedPath(){return this.isOnInitiatedPathInternal}displayType(){const t=this.requestInternal.mimeType||this.requestInternal.requestContentType()||"",i=this.requestInternal.resourceType();let r=i.name();return this.requestInternal.fromEarlyHints()?ge(ue.earlyHints):(i!==e.ResourceType.resourceTypes.Other&&i!==e.ResourceType.resourceTypes.Image||(r=t.replace(/^(application|image)\//,"")),this.requestInternal.isRedirect()&&(r+=" / "+ge(ue.redirect)),r)}displayName(){return this.requestInternal.name()}request(){return this.requestInternal}isNavigationRequest(){const e=n.PageLoad.PageLoad.forRequest(this.requestInternal);return!!e&&e.mainRequest===this.requestInternal}nodeSelfHeight(){return this.parentView().rowHeight()}createCells(e){this.nameCell=null,this.initiatorCell=null,e.classList.toggle("network-error-row",this.isFailed()),e.classList.toggle("network-navigation-row",this.isNavigationRequestInternal),super.createCells(e),this.updateBackgroundColor()}setTextAndTitle(e,t,i){o.UIUtils.createTextChild(e,t),o.Tooltip.Tooltip.install(e,i||t)}setTextAndTitleAsLink(e,t,i,r){const s=document.createElement("span");s.classList.add("devtools-link"),s.textContent=t,s.addEventListener("click",r),e.appendChild(s),o.Tooltip.Tooltip.install(e,i)}renderCell(e,t){const i=e;switch(t){case"name":this.renderPrimaryCell(i,t);break;case"path":this.renderPrimaryCell(i,t,this.requestInternal.pathname);break;case"url":this.renderPrimaryCell(i,t,this.requestInternal.url());break;case"method":{const e=this.requestInternal.preflightRequest();e?(this.setTextAndTitle(i,`${this.requestInternal.requestMethod} + `,ge(ue.sPreflight,{PH1:this.requestInternal.requestMethod})),i.appendChild(k.Linkifier.Linkifier.linkifyRevealable(e,ge(ue.preflight),void 0,ge(ue.selectPreflightRequest),void 0,"preflight-request"))):this.setTextAndTitle(i,this.requestInternal.requestMethod);break}case"status":this.renderStatusCell(i);break;case"protocol":this.renderProtocolCell(i);break;case"scheme":this.setTextAndTitle(i,this.requestInternal.scheme);break;case"domain":this.setTextAndTitle(i,this.requestInternal.domain);break;case"remote-address":this.setTextAndTitle(i,this.requestInternal.remoteAddress());break;case"remote-address-space":this.renderAddressSpaceCell(i,this.requestInternal.remoteAddressSpace());break;case"cookies":this.setTextAndTitle(i,this.arrayLength(this.requestInternal.includedRequestCookies()));break;case"set-cookies":this.setTextAndTitle(i,this.arrayLength(this.requestInternal.nonBlockedResponseCookies()));break;case"priority":{const e=this.requestInternal.priority(),t=this.requestInternal.initialPriority();e&&t?this.setTextAndTitle(i,w.NetworkPriorities.uiLabelForNetworkPriority(e),ge(ue.initialPriorityToolTip,{PH1:w.NetworkPriorities.uiLabelForNetworkPriority(e),PH2:w.NetworkPriorities.uiLabelForNetworkPriority(t)})):this.setTextAndTitle(i,e?w.NetworkPriorities.uiLabelForNetworkPriority(e):""),this.appendSubtitle(i,t?w.NetworkPriorities.uiLabelForNetworkPriority(t):"");break}case"connection-id":this.setTextAndTitle(i,"0"===this.requestInternal.connectionId?"":this.requestInternal.connectionId);break;case"type":this.setTextAndTitle(i,this.displayType());break;case"initiator":this.renderInitiatorCell(i);break;case"initiator-address-space":{const e=this.requestInternal.clientSecurityState();this.renderAddressSpaceCell(i,e?e.initiatorIPAddressSpace:"Unknown");break}case"size":this.renderSizeCell(i);break;case"time":this.renderTimeCell(i);break;case"timeline":this.setTextAndTitle(i,"");break;case"has-overrides":this.setTextAndTitle(i,this.requestInternal.overrideTypes.join(", "));break;default:this.setTextAndTitle(i,this.requestInternal.responseHeaderValue(t)||"")}}arrayLength(e){return e?String(e.length):""}select(e){super.select(e),this.parentView().dispatchEventToListeners("RequestSelected",this.requestInternal)}highlightMatchedSubstring(e){if(!e||!this.nameCell||null===this.nameCell.textContent)return[];this.element();const t=[],i=this.nameCell.textContent.match(e);return i&&o.UIUtils.highlightSearchResult(this.nameCell,i.index||0,i[0].length,t),t}openInNewTab(){t.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.requestInternal.url())}isFailed(){if(this.requestInternal.failed&&!this.requestInternal.statusCode)return!0;if(this.requestInternal.statusCode>=400)return!0;const e=this.requestInternal.signedExchangeInfo();return!(null===e||!Boolean(e.errors))||(!(!this.requestInternal.webBundleInfo()?.errorMessage&&!this.requestInternal.webBundleInnerRequestInfo()?.errorMessage)||!!this.requestInternal.corsErrorStatus())}renderPrimaryCell(e,t,i){if(0===(0|this.dataGrid?.indexOfVisibleColumn(t))){const t=this.leftPadding?this.leftPadding+"px":"";e.style.setProperty("padding-left",t),e.tabIndex=-1,this.nameCell=e,e.addEventListener("dblclick",this.openInNewTab.bind(this),!1),e.addEventListener("mousedown",(()=>{this.select(),this.parentView().dispatchEventToListeners("RequestActivated",{showPanel:!0})})),e.addEventListener("focus",(()=>this.parentView().resetFocus()));const i=this.getIcon(this.requestInternal);e.appendChild(i)}if("name"===t){const t=this.requestInternal.webBundleInnerRequestInfo();if(t){const i={iconName:"bundle",color:"var(--icon-info)"},r=this.createIconElement(i,ge(ue.webBundleInnerRequest));r.classList.add("icon");const o=n.NetworkManager.NetworkManager.forRequest(this.requestInternal);t.bundleRequestId&&o?e.appendChild(k.Linkifier.Linkifier.linkifyRevealable(new g.NetworkRequestId.NetworkRequestId(t.bundleRequestId,o),r,void 0,void 0,void 0,"webbundle-request")):e.appendChild(r)}const i=s.StringUtilities.trimMiddle(this.requestInternal.name(),100),r=n.NetworkManager.NetworkManager.forRequest(this.requestInternal);o.UIUtils.createTextChild(e,r?r.target().decorateLabel(i):i),this.appendSubtitle(e,this.requestInternal.path()),this.requestInternal.url().startsWith("data")||o.Tooltip.Tooltip.install(e,this.requestInternal.url())}else i&&o.UIUtils.createTextChild(e,i)}createIconElement(e,t){const i=document.createElement("div");return i.title=t,i.style.setProperty("mask",`url('${new URL(`../../Images/${e.iconName}.svg`,import.meta.url).toString()}')  no-repeat center /99%`),i.style.setProperty("background-color",e.color),i}getIcon(t){let i,r=t.resourceType();if(this.isFailed()){const e={iconName:"cross-circle-filled",color:"var(--icon-error)"};return i=this.createIconElement(e,r.title()),i.classList.add("icon"),i}if(t.hasThirdPartyCookiePhaseoutIssue()){const e={iconName:"warning-filled",color:"var(--icon-warning)"};return i=this.createIconElement(e,ge(ue.thirdPartyPhaseout)),i.classList.add("icon"),i}const o=t.hasOverriddenHeaders(),s=t.hasOverriddenContent;if(o||s){const e={iconName:"document",color:"var(--icon-default)"};let t;t=ge(o&&s?ue.requestContentHeadersOverridden:s?ue.requestContentOverridden:ue.requestHeadersOverridden);const r=this.createIconElement(e,t);return r.classList.add("icon"),i=document.createElement("div"),i.classList.add("network-override-marker"),i.appendChild(r),i}const n=e.ResourceType.ResourceType.fromMimeType(t.mimeType);if(n!==r&&n!==e.ResourceType.resourceTypes.Other&&(r===e.ResourceType.resourceTypes.Fetch||n===e.ResourceType.resourceTypes.Image||r===e.ResourceType.resourceTypes.Other&&n===e.ResourceType.resourceTypes.Script)&&(r=n),r===e.ResourceType.resourceTypes.Image){const e=document.createElement("img");return e.classList.add("image-network-icon-preview"),e.alt=t.resourceType().title(),t.populateImageSource(e),i=document.createElement("div"),i.classList.add("image","icon"),i.appendChild(e),i}if(r!==e.ResourceType.resourceTypes.Manifest&&"application/json"===e.ResourceType.ResourceType.simplifyContentType(t.mimeType)){const e={iconName:"file-json",color:"var(--icon-file-script)"};return i=this.createIconElement(e,t.resourceType().title()),i.classList.add("icon"),i}const a=b.iconDataForResourceType(r);return i=this.createIconElement(a,t.resourceType().title()),i.classList.add("icon"),i}renderStatusCell(e){e.classList.toggle("network-dim-cell",!this.isFailed()&&(this.requestInternal.cached()||!this.requestInternal.statusCode));const t=this.requestInternal.corsErrorStatus(),r=this.requestInternal.webBundleInfo()?.errorMessage||this.requestInternal.webBundleInnerRequestInfo()?.errorMessage;if(r)this.setTextAndTitle(e,ge(ue.webBundleError),r);else if(!this.requestInternal.failed||this.requestInternal.canceled||this.requestInternal.wasBlocked()||t)if(this.requestInternal.statusCode&&this.requestInternal.statusCode>=400){const t=this.requestInternal.getInferredStatusText();o.UIUtils.createTextChild(e,String(this.requestInternal.statusCode)),this.appendSubtitle(e,t),o.Tooltip.Tooltip.install(e,this.requestInternal.statusCode+" "+t)}else if(!this.requestInternal.statusCode&&this.requestInternal.parsedURL.isDataURL())this.setTextAndTitle(e,ge(ue.data));else if(!this.requestInternal.statusCode&&this.requestInternal.canceled)this.setTextAndTitle(e,ge(ue.canceled));else if(this.requestInternal.wasBlocked()){let t=ge(ue.other),r=!1;switch(this.requestInternal.blockedReason()){case"other":t=ge(ue.other);break;case"csp":t=ge(ue.csp);break;case"mixed-content":t=i.i18n.lockedString("mixed-content");break;case"origin":t=ge(ue.origin);break;case"inspector":t=ge(ue.devtools);break;case"subresource-filter":t=i.i18n.lockedString("subresource-filter");break;case"content-type":t=i.i18n.lockedString("content-type");break;case"coep-frame-resource-needs-coep-header":r=!0,t=i.i18n.lockedString("CoepFrameResourceNeedsCoepHeader");break;case"coop-sandboxed-iframe-cannot-navigate-to-coop-page":r=!0,t=i.i18n.lockedString("CoopSandboxedIframeCannotNavigateToCoopPage");break;case"corp-not-same-origin":r=!0,t=i.i18n.lockedString("NotSameOrigin");break;case"corp-not-same-site":r=!0,t=i.i18n.lockedString("NotSameSite");break;case"corp-not-same-origin-after-defaulted-to-same-origin-by-coep":r=!0,t=i.i18n.lockedString("NotSameOriginAfterDefaultedToSameOriginByCoep")}r?this.setTextAndTitleAsLink(e,ge(ue.blockeds,{PH1:t}),ge(ue.blockedTooltip),(()=>{this.parentView().dispatchEventToListeners("RequestActivated",{showPanel:!0,tab:"headers-component"})})):this.setTextAndTitle(e,ge(ue.blockeds,{PH1:t}))}else if(t)this.setTextAndTitle(e,ge(ue.corsError),ge(ue.crossoriginResourceSharingErrorS,{PH1:t.corsError}));else if(this.requestInternal.statusCode){o.UIUtils.createTextChild(e,String(this.requestInternal.statusCode));const t=this.requestInternal.getInferredStatusText();this.appendSubtitle(e,t),o.Tooltip.Tooltip.install(e,this.requestInternal.statusCode+" "+t)}else this.requestInternal.finished?this.setTextAndTitle(e,ge(ue.finished)):this.requestInternal.preserved?this.setTextAndTitle(e,ge(ue.unknown),ge(ue.unknownExplanation)):this.setTextAndTitle(e,ge(ue.pendingq));else{const t=ge(ue.failed);this.requestInternal.localizedFailDescription?(o.UIUtils.createTextChild(e,t),this.appendSubtitle(e,this.requestInternal.localizedFailDescription,!0),o.Tooltip.Tooltip.install(e,t+" "+this.requestInternal.localizedFailDescription)):this.setTextAndTitle(e,t)}}renderProtocolCell(e){switch(o.UIUtils.createTextChild(e,this.requestInternal.protocol),this.requestInternal.alternateProtocolUsage){case"alternativeJobWonWithoutRace":o.Tooltip.Tooltip.install(e,ue.alternativeJobWonWithoutRace);break;case"alternativeJobWonRace":o.Tooltip.Tooltip.install(e,ue.alternativeJobWonRace);break;case"mainJobWonRace":o.Tooltip.Tooltip.install(e,ue.mainJobWonRace);break;case"mappingMissing":o.Tooltip.Tooltip.install(e,ue.mappingMissing);break;case"broken":o.Tooltip.Tooltip.install(e,ue.broken);break;case"dnsAlpnH3JobWonWithoutRace":o.Tooltip.Tooltip.install(e,ue.dnsAlpnH3JobWonWithoutRace);break;case"dnsAlpnH3JobWonRace":o.Tooltip.Tooltip.install(e,ue.dnsAlpnH3JobWonRace);break;default:o.Tooltip.Tooltip.install(e,this.requestInternal.protocol)}}#e(){if(this.requestInternal.resourceType().isStyleSheet())return t.UserMetrics.Action.StyleSheetInitiatorLinkClicked}renderInitiatorCell(e){this.initiatorCell=e;const t=this.requestInternal,i=a.NetworkLog.NetworkLog.instance().initiatorInfoForRequest(t),r=t.timing;switch(r&&r.pushStart&&e.appendChild(document.createTextNode(ge(ue.push))),i.type){case"parser":{const t=p.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(i.url),r=t?.displayName(),o=void 0!==r&&void 0!==i.lineNumber?`${r}:${i.lineNumber}`:void 0;e.appendChild(k.Linkifier.Linkifier.linkifyURL(i.url,{text:o,lineNumber:i.lineNumber,columnNumber:i.columnNumber,userMetric:this.#e()})),this.appendSubtitle(e,ge(ue.parser));break}case"redirect":{o.Tooltip.Tooltip.install(e,i.url);const r=t.redirectSource();console.assert(null!==r),this.parentView().nodeForRequest(r)?e.appendChild(k.Linkifier.Linkifier.linkifyRevealable(r,u.ResourceUtils.displayNameForURL(r.url()),void 0,void 0,void 0,"redirect-source-request")):e.appendChild(k.Linkifier.Linkifier.linkifyURL(r.url(),{jslogContext:"redirect-source-request-url"})),this.appendSubtitle(e,ge(ue.redirect));break}case"script":{const r=n.NetworkManager.NetworkManager.forRequest(t)?.target()||null,s=this.parentView().linkifier();i.stack?this.linkifiedInitiatorAnchor=s.linkifyStackTraceTopFrame(r,i.stack):this.linkifiedInitiatorAnchor=s.linkifyScriptLocation(r,i.scriptId,i.url,i.lineNumber,{columnNumber:i.columnNumber,inlineFrameIndex:0}),o.Tooltip.Tooltip.install(this.linkifiedInitiatorAnchor,""),e.appendChild(this.linkifiedInitiatorAnchor),this.appendSubtitle(e,ge(ue.script)),e.classList.add("network-script-initiated");break}case"preload":o.Tooltip.Tooltip.install(e,ge(ue.preload)),e.classList.add("network-dim-cell"),e.appendChild(document.createTextNode(ge(ue.preload)));break;case"signedExchange":e.appendChild(k.Linkifier.Linkifier.linkifyURL(i.url)),this.appendSubtitle(e,ge(ue.signedexchange));break;case"preflight":if(e.appendChild(document.createTextNode(ge(ue.preflight))),i.initiatorRequest){const t=m.Icon.create("arrow-up-down-circle"),r=k.Linkifier.Linkifier.linkifyRevealable(i.initiatorRequest,t,void 0,ge(ue.selectTheRequestThatTriggered),"trailing-link-icon","initator-request");o.ARIAUtils.setLabel(r,ge(ue.selectTheRequestThatTriggered)),e.appendChild(r)}break;default:o.Tooltip.Tooltip.install(e,ge(ue.otherC)),e.classList.add("network-dim-cell"),e.appendChild(document.createTextNode(ge(ue.otherC)))}}renderAddressSpaceCell(e,t){"Unknown"!==t&&o.UIUtils.createTextChild(e,t)}renderSizeCell(e){const t=s.NumberUtilities.bytesToString(this.requestInternal.resourceSize);if(this.requestInternal.cachedInMemory())o.UIUtils.createTextChild(e,ge(ue.memoryCache)),o.Tooltip.Tooltip.install(e,ge(ue.servedFromMemoryCacheResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.serviceWorkerRouterInfo){const{serviceWorkerRouterInfo:r}=this.requestInternal,n=r.ruleIdMatched??0;let a;if(o.UIUtils.createTextChild(e,i.i18n.lockedString("(ServiceWorker router)")),"network"===r.matchedSourceType){const e=s.NumberUtilities.bytesToString(this.requestInternal.transferSize);a=ge(ue.matchedToServiceWorkerRouterWithNetworkSource,{PH1:n,PH2:e,PH3:t})}else a=ge(ue.matchedToServiceWorkerRouter,{PH1:n,PH2:t});o.Tooltip.Tooltip.install(e,a),e.classList.add("network-dim-cell")}else if(this.requestInternal.fetchedViaServiceWorker)o.UIUtils.createTextChild(e,ge(ue.serviceWorker)),o.Tooltip.Tooltip.install(e,ge(ue.servedFromServiceWorkerResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.redirectSourceSignedExchangeInfoHasNoErrors())o.UIUtils.createTextChild(e,i.i18n.lockedString("(signed-exchange)")),o.Tooltip.Tooltip.install(e,ge(ue.servedFromSignedHttpExchange,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.webBundleInnerRequestInfo())o.UIUtils.createTextChild(e,ge(ue.webBundle)),o.Tooltip.Tooltip.install(e,ge(ue.servedFromWebBundle,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.fromPrefetchCache())o.UIUtils.createTextChild(e,ge(ue.prefetchCache)),o.Tooltip.Tooltip.install(e,ge(ue.servedFromPrefetchCacheResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.cached())o.UIUtils.createTextChild(e,ge(ue.diskCache)),o.Tooltip.Tooltip.install(e,ge(ue.servedFromDiskCacheResourceSizeS,{PH1:t})),e.classList.add("network-dim-cell");else{const i=s.NumberUtilities.bytesToString(this.requestInternal.transferSize);o.UIUtils.createTextChild(e,i),o.Tooltip.Tooltip.install(e,`${i} transferred over network, resource size: ${t}`)}this.appendSubtitle(e,t)}renderTimeCell(e){this.requestInternal.duration>0?(this.setTextAndTitle(e,i.TimeUtilities.secondsToString(this.requestInternal.duration)),this.appendSubtitle(e,i.TimeUtilities.secondsToString(this.requestInternal.latency),!1,ge(ue.timeSubtitleTooltipText))):this.requestInternal.preserved?this.setTextAndTitle(e,ge(ue.unknown),ge(ue.unknownExplanation)):(e.classList.add("network-dim-cell"),this.setTextAndTitle(e,ge(ue.pending)))}appendSubtitle(e,t,i=!1,r=""){const s=document.createElement("div");s.classList.add("network-cell-subtitle"),i&&s.classList.add("always-visible"),s.textContent=t,r&&o.Tooltip.Tooltip.install(s,r),e.appendChild(s)}}class be extends me{createCells(e){super.createCells(e);const t=this.dataGrid.visibleColumnsArray[0],i=`${t.title}`,r=ge(ue.level);this.nodeAccessibleText=`${r} ${i}: ${this.cellAccessibleTextMap.get(t.id)}`}renderCell(e,t){if(0===this.dataGrid.indexOfVisibleColumn(t)){const i=e,r=this.leftPadding?this.leftPadding+"px":"";i.style.setProperty("padding-left",r),i.classList.add("disclosure"),this.setCellAccessibleName(i.textContent||"",i,t)}}select(e){super.select(e);const t=this.traverseNextNode(!1,void 0,!0),i=t?.request();i&&this.parentView().dispatchEventToListeners("RequestSelected",i)}}var ve=Object.freeze({__proto__:null,NetworkNode:me,_backgroundColors:we,NetworkRequestNode:ke,NetworkGroupNode:be});const fe=new CSSStyleSheet;fe.replaceSync(".request-cookies-view{overflow:auto;padding:12px;height:100%;background-color:var(--sys-color-cdt-base-container)}.request-cookies-view .request-cookies-title{font-size:12px;font-weight:bold;margin-right:30px;color:var(--sys-color-on-surface)}.request-cookies-view .cookie-line{margin-top:6px;display:flex}.request-cookies-view .cookies-panel-item{margin-top:6px;margin-bottom:16px;flex:none}@media (forced-colors: active){td.flagged-cookie-attribute-cell .cookie-warning-icon{forced-color-adjust:none;filter:grayscale()}}\n/*# sourceURL=requestCookiesView.css */\n");const Ce={thisRequestHasNoCookies:"This request has no cookies.",requestCookies:"Request Cookies",cookiesThatWereSentToTheServerIn:"Cookies that were sent to the server in the 'cookie' header of the request",showFilteredOutRequestCookies:"show filtered out request cookies",noRequestCookiesWereSent:"No request cookies were sent.",responseCookies:"Response Cookies",cookiesThatWereReceivedFromThe:"Cookies that were received from the server in the '`set-cookie`' header of the response",malformedResponseCookies:"Malformed Response Cookies",cookiesThatWereReceivedFromTheServer:"Cookies that were received from the server in the '`set-cookie`' header of the response but were malformed",siteHasCookieInOtherPartition:"This site has cookies in another partition, that were not sent with this request. {PH1}",learnMore:"Learn more"},Se=i.i18n.registerUIStrings("panels/network/RequestCookiesView.ts",Ce),ye=i.i18n.getLocalizedString.bind(void 0,Se);class Te extends o.Widget.Widget{request;showFilteredOutCookiesSetting;emptyWidget;requestCookiesTitle;requestCookiesEmpty;requestCookiesTable;responseCookiesTitle;responseCookiesTable;siteHasCookieInOtherPartition;malformedResponseCookiesTitle;malformedResponseCookiesList;constructor(t){super(),this.element.classList.add("request-cookies-view"),this.element.setAttribute("jslog",`${l.pane("cookies").track({resize:!0})}`),this.request=t,this.showFilteredOutCookiesSetting=e.Settings.Settings.instance().createSetting("show-filtered-out-request-cookies",!1),this.emptyWidget=new o.EmptyWidget.EmptyWidget(ye(Ce.thisRequestHasNoCookies)),this.emptyWidget.show(this.element),this.requestCookiesTitle=this.element.createChild("div");const r=this.requestCookiesTitle.createChild("span","request-cookies-title");r.textContent=ye(Ce.requestCookies),o.Tooltip.Tooltip.install(r,ye(Ce.cookiesThatWereSentToTheServerIn));const s=o.SettingsUI.createSettingCheckbox(ye(Ce.showFilteredOutRequestCookies),this.showFilteredOutCookiesSetting,!0);s.checkboxElement.addEventListener("change",(()=>{this.refreshRequestCookiesView()})),this.requestCookiesTitle.appendChild(s),this.requestCookiesEmpty=this.element.createChild("div","cookies-panel-item"),this.requestCookiesEmpty.textContent=ye(Ce.noRequestCookiesWereSent),this.requestCookiesTable=new C.CookiesTable.CookiesTable(!0),this.requestCookiesTable.contentElement.classList.add("cookie-table","cookies-panel-item"),this.requestCookiesTable.show(this.element),this.siteHasCookieInOtherPartition=this.element.createChild("div","cookies-panel-item site-has-cookies-in-other-partition"),this.siteHasCookieInOtherPartition.appendChild(i.i18n.getFormatLocalizedString(Se,Ce.siteHasCookieInOtherPartition,{PH1:o.XLink.XLink.create("https://developer.chrome.com/en/docs/privacy-sandbox/chips/",ye(Ce.learnMore),void 0,void 0,"learn-more")})),this.responseCookiesTitle=this.element.createChild("div","request-cookies-title"),this.responseCookiesTitle.textContent=ye(Ce.responseCookies),this.responseCookiesTitle.title=ye(Ce.cookiesThatWereReceivedFromThe),this.responseCookiesTable=new C.CookiesTable.CookiesTable(!0),this.responseCookiesTable.contentElement.classList.add("cookie-table","cookies-panel-item"),this.responseCookiesTable.show(this.element),this.malformedResponseCookiesTitle=this.element.createChild("div","request-cookies-title"),this.malformedResponseCookiesTitle.textContent=ye(Ce.malformedResponseCookies),o.Tooltip.Tooltip.install(this.malformedResponseCookiesTitle,ye(Ce.cookiesThatWereReceivedFromTheServer)),this.malformedResponseCookiesList=this.element.createChild("div")}getRequestCookies(){const e=new Map,t=new Map,i=this.request.includedRequestCookies().map((e=>e.cookie));if(this.showFilteredOutCookiesSetting.get())for(const t of this.request.blockedRequestCookies())e.set(t.cookie,t.blockedReasons.map((e=>({attribute:n.NetworkRequest.cookieBlockedReasonToAttribute(e),uiString:n.NetworkRequest.cookieBlockedReasonToUiString(e)})))),i.push(t.cookie);for(const e of this.request.includedRequestCookies())e.exemptionReason&&t.set(e.cookie,{uiString:n.NetworkRequest.cookieExemptionReasonToUiString(e.exemptionReason)});return{requestCookies:i,requestCookieToBlockedReasons:e,requestCookieToExemptionReason:t}}getResponseCookies(){let e=[];const t=new Map,i=new Map,r=[];if(this.request.responseCookies.length){e=this.request.nonBlockedResponseCookies();for(const i of this.request.blockedResponseCookies()){const o=n.CookieParser.CookieParser.parseSetCookie(i.cookieLine);if(o&&!o.length||i.blockedReasons.includes("SyntaxError")||i.blockedReasons.includes("NameValuePairExceedsMaxSize")){r.push(i);continue}let s=i.cookie;!s&&o&&(s=o[0]),s&&(t.set(s,i.blockedReasons.map((e=>({attribute:n.NetworkRequest.setCookieBlockedReasonToAttribute(e),uiString:n.NetworkRequest.setCookieBlockedReasonToUiString(e)})))),e.push(s))}for(const t of this.request.exemptedResponseCookies()){const r=e.find((e=>t.cookieLine===e.getCookieLine()));r&&i.set(r,{uiString:n.NetworkRequest.cookieExemptionReasonToUiString(t.exemptionReason)})}}return{responseCookies:e,responseCookieToBlockedReasons:t,responseCookieToExemptionReason:i,malformedResponseCookies:r}}refreshRequestCookiesView(){if(!this.isShowing())return;this.request.hasRequestCookies()||this.request.responseCookies.length?this.emptyWidget.hideWidget():this.emptyWidget.showWidget();const{requestCookies:e,requestCookieToBlockedReasons:t,requestCookieToExemptionReason:i}=this.getRequestCookies(),{responseCookies:r,responseCookieToBlockedReasons:s,responseCookieToExemptionReason:a,malformedResponseCookies:l}=this.getResponseCookies();if(e.length?(this.requestCookiesTitle.classList.remove("hidden"),this.requestCookiesEmpty.classList.add("hidden"),this.requestCookiesTable.showWidget(),this.requestCookiesTable.setCookies(e,t,i)):this.request.blockedRequestCookies().length?(this.requestCookiesTitle.classList.remove("hidden"),this.requestCookiesEmpty.classList.remove("hidden"),this.requestCookiesTable.hideWidget()):(this.requestCookiesTitle.classList.add("hidden"),this.requestCookiesEmpty.classList.add("hidden"),this.requestCookiesTable.hideWidget()),r.length?(this.responseCookiesTitle.classList.remove("hidden"),this.responseCookiesTable.showWidget(),this.responseCookiesTable.setCookies(r,s,a)):(this.responseCookiesTitle.classList.add("hidden"),this.responseCookiesTable.hideWidget()),l.length){this.malformedResponseCookiesTitle.classList.remove("hidden"),this.malformedResponseCookiesList.classList.remove("hidden"),this.malformedResponseCookiesList.removeChildren();for(const e of l){const t=this.malformedResponseCookiesList.createChild("span","cookie-line source-code"),i=new m.Icon.Icon;i.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},i.classList.add("cookie-warning-icon"),t.appendChild(i),o.UIUtils.createTextChild(t,e.cookieLine),e.blockedReasons.includes("NameValuePairExceedsMaxSize")?t.title=n.NetworkRequest.setCookieBlockedReasonToUiString("NameValuePairExceedsMaxSize"):t.title=n.NetworkRequest.setCookieBlockedReasonToUiString("SyntaxError")}}else this.malformedResponseCookiesTitle.classList.add("hidden"),this.malformedResponseCookiesList.classList.add("hidden");this.request.siteHasCookieInOtherPartition()?this.siteHasCookieInOtherPartition.classList.remove("hidden"):this.siteHasCookieInOtherPartition.classList.add("hidden")}wasShown(){super.wasShown(),this.registerCSSFiles([fe]),this.request.addEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestCookiesView,this),this.request.addEventListener(n.NetworkRequest.Events.ResponseHeadersChanged,this.refreshRequestCookiesView,this),this.refreshRequestCookiesView()}willHide(){this.request.removeEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestCookiesView,this),this.request.removeEventListener(n.NetworkRequest.Events.ResponseHeadersChanged,this.refreshRequestCookiesView,this)}}var xe=Object.freeze({__proto__:null,RequestCookiesView:Te});const Re=new CSSStyleSheet;Re.replaceSync(".request-initiator-view{display:block;margin:6px}\n/*# sourceURL=requestInitiatorView.css */\n");const Ie=new CSSStyleSheet;Ie.replaceSync(".request-initiator-view-tree .fill{right:-6px}.request-initiator-view-section-title{font-weight:bold;padding:4px}.request-initiator-view-section-title:focus-visible{background-color:var(--sys-color-state-focus-highlight)}@media (forced-colors: active){.request-initiator-view-section-title:focus-visible{forced-color-adjust:none;background-color:Highlight;color:HighlightText}}\n/*# sourceURL=requestInitiatorViewTree.css */\n");const qe={thisRequestHasNoInitiatorData:"This request has no initiator data.",requestCallStack:"Request call stack",requestInitiatorChain:"Request initiator chain"},Le=i.i18n.registerUIStrings("panels/network/RequestInitiatorView.ts",qe),Fe=i.i18n.getLocalizedString.bind(void 0,Le);class Pe extends o.Widget.VBox{linkifier;request;emptyWidget;hasShown;constructor(e){super(),this.element.classList.add("request-initiator-view"),this.element.setAttribute("jslog",`${l.pane("initiator").track({resize:!0})}`),this.linkifier=new k.Linkifier.Linkifier,this.request=e,this.emptyWidget=new o.EmptyWidget.EmptyWidget(Fe(qe.thisRequestHasNoInitiatorData)),this.emptyWidget.show(this.element),this.hasShown=!1}static createStackTracePreview(e,t,i){const r=e.initiator();if(!r||!r.stack)return null;const o=n.NetworkManager.NetworkManager.forRequest(e),s=o?o.target():null;return k.JSPresentationUtils.buildStackTracePreviewContents(s,t,{stackTrace:r.stack,tabStops:i})}createTree(){const e=new o.TreeOutline.TreeOutlineInShadow;return e.registerCSSFiles([Ie]),e.contentElement.classList.add("request-initiator-view-tree"),e.contentElement.setAttribute("jslog",`${l.tree("initiator-tree")}`),e}buildRequestChainTree(e,t,i){const r=new o.TreeOutline.TreeElement(t);i.appendChild(r),r.titleElement instanceof HTMLElement&&r.titleElement.classList.add("request-initiator-view-section-title");const s=e.initiators;let n=r;for(const e of Array.from(s).reverse()){const t=new o.TreeOutline.TreeElement(e.url());n.appendChild(t),n.expand(),n=t}r.expand(),n.select();const a=n.titleElement;a instanceof HTMLElement&&(a.style.fontWeight="bold");const l=e.initiated;return this.depthFirstSearchTreeBuilder(l,n,this.request),r}depthFirstSearchTreeBuilder(e,t,i){const r=new Set;r.add(this.request);for(const s of e.keys())if(e.get(s)===i){const i=new o.TreeOutline.TreeElement(s.url());t.appendChild(i),t.expand(),r.has(s)||(r.add(s),this.depthFirstSearchTreeBuilder(e,i,s))}}buildStackTraceSection(e,t,i){const r=new o.TreeOutline.TreeElement(t);i.appendChild(r),r.titleElement instanceof HTMLElement&&r.titleElement.classList.add("request-initiator-view-section-title");const s=new o.TreeOutline.TreeElement(e,!1);s.selectable=!1,r.appendChild(s),r.expand()}wasShown(){if(this.hasShown)return;this.registerCSSFiles([Re]);let e=!1;const t=this.createTree(),i=Pe.createStackTracePreview(this.request,this.linkifier,!0);i&&(e=!0,this.buildStackTraceSection(i.element,Fe(qe.requestCallStack),t));const r=a.NetworkLog.NetworkLog.instance().initiatorGraphForRequest(this.request);(r.initiators.size>1||r.initiated.size>1)&&(e=!0,this.buildRequestChainTree(r,Fe(qe.requestInitiatorChain),t));const o=t.firstChild();o&&o.select(!0),e&&(this.element.appendChild(t.element),this.emptyWidget.hideWidget()),this.hasShown=!0}}var He=Object.freeze({__proto__:null,RequestInitiatorView:Pe});const Me=new CSSStyleSheet;Me.replaceSync('.object-properties-section-dimmed{opacity:60%}.object-properties-section{padding:0;color:var(--sys-color-on-surface);display:flex;flex-direction:column}.object-properties-section li{user-select:text}.object-properties-section li::before{top:-1px;margin-right:2px}.object-properties-section li.editing-sub-part{padding:3px 12px 8px 6px;margin:-1px -6px -8px;text-overflow:clip}.object-properties-section li.editing{margin-left:10px;text-overflow:clip}.tree-outline ol.title-less-mode{padding-left:0}.object-properties-section .own-property{font-weight:bold}.object-properties-section .synthetic-property{color:var(--sys-color-token-subtle)}.object-properties-section .private-property-hash{color:var(--sys-color-on-surface)}.object-properties-section-root-element{display:flex;flex-direction:row}.object-properties-section .editable-div{overflow:hidden}.object-properties-section [data-webidl="true"] > .name-and-value > .adorner{flex-shrink:0;width:16px;height:16px;margin-right:2px}.object-properties-section [data-webidl="true"] > .name-and-value > .name{font-weight:bold}.name-and-value{overflow:hidden;line-height:16px;display:flex;white-space:nowrap}.name-and-value .separator{white-space:pre;flex-shrink:0}.editing-sub-part .name-and-value{overflow:visible;display:inline-flex}.property-prompt{margin-left:4px}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:none}.tree-outline.hide-selection-when-blurred .selected:focus-visible ::slotted(*),\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .tree-element-title,\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .name-and-value,\n.tree-outline.hide-selection-when-blurred .selected:focus-visible .gray-info-message{background:var(--sys-color-state-focus-highlight);border-radius:2px}@media (forced-colors: active){.object-properties-section-dimmed{opacity:100%}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:Highlight}.tree-outline li:hover .tree-element-title,\n  .tree-outline li.selected .tree-element-title{color:ButtonText}.tree-outline.hide-selection-when-blurred .selected:focus-visible .tree-element-title,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .name-and-value{background:transparent;box-shadow:none}.tree-outline.hide-selection-when-blurred .selected:focus-visible span,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .gray-info-message{color:HighlightText}.tree-outline-disclosure:hover li.parent::before{background-color:ButtonText}}\n/*# sourceURL=objectPropertiesSection.css */\n');const Ee=new CSSStyleSheet;Ee.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");const Ae=new CSSStyleSheet;Ae.replaceSync(".tree-outline{padding-left:0}.tree-outline > ol{padding-bottom:5px;border-bottom:solid 1px var(--sys-color-divider)}.tree-outline > .parent{user-select:none;font-weight:bold;color:var(--sys-color-on-surface);margin-top:-1px;display:flex;align-items:center;height:26px}.tree-outline li{padding-left:5px;line-height:20px}.tree-outline li:not(.parent){margin-left:10px;display:block}.tree-outline li:not(.parent)::before{display:none}.tree-outline li.expanded .payload-count{display:none}.tree-outline li .payload-toggle{display:none}.tree-outline li.expanded .payload-toggle{display:inline;margin-left:30px;font-weight:normal;color:var(--sys-color-on-surface);background:none;border:none}.tree-outline li.expanded .payload-toggle:focus-visible{border:2px solid var(--sys-color-state-focus-ring);border-radius:5px}.tree-outline li .header-toggle:hover{color:var(--sys-color-token-subtle)}.tree-outline .payload-name{color:var(--sys-color-token-subtle);display:inline-flex;margin-right:0.25em;font-weight:bold;vertical-align:top;white-space:pre-wrap}.tree-outline .payload-separator{user-select:none}.tree-outline .payload-value{display:inline;margin-right:1em;white-space:pre-wrap;word-break:break-all;margin-top:1px}.tree-outline .empty-request-payload{color:var(--sys-color-state-disabled)}.request-payload-show-more-button{border:none;border-radius:3px;display:inline-block;font-size:12px;font-family:sans-serif;margin:0 4px;padding:2px 4px}@media (forced-colors: active){:host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus{background:Highlight}:host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li::before{background-color:ButtonText}:host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent::before{background-color:HighlightText}:host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected *,\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent,\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent span{color:HighlightText}}.payload-decode-error{color:var(--sys-color-error)}\n/*# sourceURL=requestPayloadTree.css */\n");const Be=new CSSStyleSheet;Be.replaceSync(".request-payload-view{user-select:text;overflow:auto}.request-payload-tree{flex-grow:1;overflow-y:auto;margin:0}\n/*# sourceURL=requestPayloadView.css */\n");const Ue={copyValue:"Copy value",requestPayload:"Request Payload",unableToDecodeValue:"(unable to decode value)",queryStringParameters:"Query String Parameters",formData:"Form Data",showMore:"Show more",viewParsed:"View parsed",empty:"(empty)",viewSource:"View source",viewUrlEncoded:"View URL-encoded",viewDecoded:"View decoded",viewUrlEncodedL:"view URL-encoded",viewDecodedL:"view decoded",viewParsedL:"view parsed",viewSourceL:"view source"},Ne=i.i18n.registerUIStrings("panels/network/RequestPayloadView.ts",Ue),Ve=i.i18n.getLocalizedString.bind(void 0,Ne);class Oe extends o.Widget.VBox{request;decodeRequestParameters;queryStringCategory;formDataCategory;requestPayloadCategory;constructor(e){super(),this.element.classList.add("request-payload-view"),this.element.setAttribute("jslog",`${l.pane("payload").track({resize:!0})}`),this.request=e,this.decodeRequestParameters=!0;const t=e.requestContentType();t&&(this.decodeRequestParameters=Boolean(t.match(/^application\/x-www-form-urlencoded\s*(;.*)?$/i)));const i=new o.TreeOutline.TreeOutlineInShadow;i.registerCSSFiles([Ee,Me,Ae]),i.element.classList.add("request-payload-tree"),i.makeDense(),this.element.appendChild(i.element),this.queryStringCategory=new De(i,"query-string"),this.formDataCategory=new De(i,"form-data"),this.requestPayloadCategory=new De(i,"request-payload",Ve(Ue.requestPayload))}wasShown(){this.registerCSSFiles([Be]),this.request.addEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.refreshFormData,this),this.refreshQueryString(),this.refreshFormData()}willHide(){this.request.removeEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.refreshFormData,this)}addEntryContextMenuHandler(e,i){e.listItemElement.addEventListener("contextmenu",(e=>{e.consume(!0);const r=new o.ContextMenu.ContextMenu(e),s=decodeURIComponent(i);r.clipboardSection().appendItem(Ve(Ue.copyValue),(()=>{t.userMetrics.actionTaken(t.UserMetrics.Action.NetworkPanelCopyValue),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(s)}),{jslogContext:"copy-value"}),r.show()}))}static formatParameter(e,t,i){let r=!1;if(i&&(e=e.replace(/\+/g," ")).indexOf("%")>=0)try{e=decodeURIComponent(e)}catch(e){r=!0}const o=document.createElement("div");return t&&(o.className=t),""===e&&o.classList.add("empty-value"),r?o.createChild("span","payload-decode-error").textContent=Ve(Ue.unableToDecodeValue):o.textContent=e,o}refreshQueryString(){const e=this.request.queryString(),t=this.request.queryParameters;this.queryStringCategory.hidden=!t,t&&this.refreshParams(Ve(Ue.queryStringParameters),t,e,this.queryStringCategory)}async refreshFormData(){const e=await this.request.requestFormData();if(!e)return this.formDataCategory.hidden=!0,void(this.requestPayloadCategory.hidden=!0);const t=await this.request.formParameters();if(t)this.formDataCategory.hidden=!1,this.requestPayloadCategory.hidden=!0,this.refreshParams(Ve(Ue.formData),t,e,this.formDataCategory);else{this.requestPayloadCategory.hidden=!1,this.formDataCategory.hidden=!0;try{const t=JSON.parse(e);this.refreshRequestJSONPayload(t,e)}catch(t){this.populateTreeElementWithSourceText(this.requestPayloadCategory,e)}}}populateTreeElementWithSourceText(e,t){const i=(t||"").trim(),r=i.length>3e3,s=document.createElement("span");s.classList.add("payload-value"),s.classList.add("source-code"),s.textContent=r?i.substr(0,3e3):i;const n=new o.TreeOutline.TreeElement(s);if(e.removeChildren(),e.appendChild(n),!r)return;const a=document.createElement("button");function d(){a.remove(),s.textContent=i,n.listItemElement.removeEventListener("contextmenu",c)}function c(e){const t=new o.ContextMenu.ContextMenu(e);t.newSection().appendItem(Ve(Ue.showMore),d,{jslogContext:"show-more"}),t.show()}a.classList.add("request-payload-show-more-button"),a.textContent=Ve(Ue.showMore),a.setAttribute("jslog",`${l.action("show-more").track({click:!0})}`),a.addEventListener("click",d),n.listItemElement.addEventListener("contextmenu",c),s.appendChild(a)}refreshParams(e,t,i,r){r.removeChildren(),r.listItemElement.removeChildren(),r.listItemElement.createChild("div","selection fill"),o.UIUtils.createTextChild(r.listItemElement,e);const s=document.createElement("span");s.classList.add("payload-count");const n=t?t.length:0;s.textContent=` (${n})`,r.listItemElement.appendChild(s);We.has(r)?this.appendParamsSource(e,t,i,r):this.appendParamsParsed(e,t,i,r)}appendParamsSource(e,t,i,r){this.populateTreeElementWithSourceText(r,i);const s=r.listItemElement,n=function(o){s.removeEventListener("contextmenu",a),We.delete(r),this.refreshParams(e,t,i,r),o.consume()},a=e=>{if(!r.expanded)return;const t=new o.ContextMenu.ContextMenu(e);t.newSection().appendItem(Ve(Ue.viewParsed),n.bind(this,e),{jslogContext:"view-parsed"}),t.show()},l=this.createViewSourceToggle(!0,n.bind(this));s.appendChild(l),s.addEventListener("contextmenu",a)}appendParamsParsed(e,t,i,r){for(const e of t||[]){const t=document.createDocumentFragment();if(""!==e.name){const i=Oe.formatParameter(e.name+": ","payload-name",this.decodeRequestParameters),r=Oe.formatParameter(e.value,"payload-value source-code",this.decodeRequestParameters);t.appendChild(i),t.createChild("span","payload-separator"),t.appendChild(r)}else t.appendChild(Oe.formatParameter(Ve(Ue.empty),"empty-request-payload",this.decodeRequestParameters));const i=new o.TreeOutline.TreeElement(t);this.addEntryContextMenuHandler(i,e.value),r.appendChild(i)}const s=r.listItemElement,n=function(o){s.removeEventListener("contextmenu",d),We.add(r),this.refreshParams(e,t,i,r),o.consume()},a=function(e){s.removeEventListener("contextmenu",d),this.toggleURLDecoding(e)},d=e=>{if(!r.expanded)return;const t=new o.ContextMenu.ContextMenu(e),i=t.newSection();i.appendItem(Ve(Ue.viewSource),n.bind(this,e),{jslogContext:"view-source"});const s=this.decodeRequestParameters?Ve(Ue.viewUrlEncoded):Ve(Ue.viewDecoded);i.appendItem(s,a.bind(this,e),{jslogContext:"toggle-url-decoding"}),t.show()},c=this.createViewSourceToggle(!1,n.bind(this));s.appendChild(c);const h=this.decodeRequestParameters?Ve(Ue.viewUrlEncodedL):Ve(Ue.viewDecodedL),u=this.createToggleButton(h);u.setAttribute("jslog",`${l.toggle("decode-encode").track({click:!0})}`),u.addEventListener("click",a.bind(this),!1),s.appendChild(u),s.addEventListener("contextmenu",d)}refreshRequestJSONPayload(e,t){const i=this.requestPayloadCategory;i.removeChildren();const r=i.listItemElement;r.removeChildren(),r.createChild("div","selection fill"),o.UIUtils.createTextChild(r,this.requestPayloadCategory.title.toString()),We.has(i)?this.appendJSONPayloadSource(i,e,t):this.appendJSONPayloadParsed(i,e,t)}appendJSONPayloadSource(e,t,i){const r=e.listItemElement;this.populateTreeElementWithSourceText(e,i);const s=function(o){r.removeEventListener("contextmenu",a),We.delete(e),this.refreshRequestJSONPayload(t,i),o.consume()},n=this.createViewSourceToggle(!0,s.bind(this));r.appendChild(n);const a=t=>{if(!e.expanded)return;const i=new o.ContextMenu.ContextMenu(t);i.newSection().appendItem(Ve(Ue.viewParsed),s.bind(this,t),{jslogContext:"view-parsed"}),i.show()};r.addEventListener("contextmenu",a)}appendJSONPayloadParsed(e,t,i){const r=n.RemoteObject.RemoteObject.fromLocalObject(t),s=new S.ObjectPropertiesSection.RootElement(r);s.title=r.description,s.expand(),s.editable=!1,e.childrenListElement.classList.add("source-code","object-properties-section"),e.appendChild(s);const a=e.listItemElement,l=function(r){a.removeEventListener("contextmenu",d),We.add(e),this.refreshRequestJSONPayload(t,i),r.consume()},d=t=>{if(!e.expanded)return;const i=new o.ContextMenu.ContextMenu(t);i.newSection().appendItem(Ve(Ue.viewSource),l.bind(this,t),{jslogContext:"view-source"}),i.show()},c=this.createViewSourceToggle(!1,l.bind(this));a.appendChild(c),a.addEventListener("contextmenu",d)}createViewSourceToggle(e,t){const i=Ve(e?Ue.viewParsedL:Ue.viewSourceL),r=this.createToggleButton(i);return r.setAttribute("jslog",`${l.toggle("source-parse").track({click:!0})}`),r.addEventListener("click",t,!1),r}toggleURLDecoding(e){this.decodeRequestParameters=!this.decodeRequestParameters,this.refreshQueryString(),this.refreshFormData(),e.consume()}createToggleButton(e){const t=document.createElement("button");return t.classList.add("payload-toggle"),t.tabIndex=0,t.textContent=e,t}}const We=new WeakSet;class De extends o.TreeOutline.TreeElement{toggleOnClick;expandedSetting;expanded;constructor(t,i,r){super(r||"",!0),this.toggleOnClick=!0,this.hidden=!0,this.expandedSetting=e.Settings.Settings.instance().createSetting("request-info-"+i+"-category-expanded",!0),this.expanded=this.expandedSetting.get(),this.listItemElement.setAttribute("jslog",`${l.section().context(i)}`),t.appendChild(this)}createLeaf(){const e=new o.TreeOutline.TreeElement;return this.appendChild(e),e}onexpand(){this.expandedSetting.set(!0)}oncollapse(){this.expandedSetting.set(!1)}}var je=Object.freeze({__proto__:null,RequestPayloadView:Oe,Category:De});const Ge=new CSSStyleSheet;Ge.replaceSync(".html-preview-frame{box-shadow:var(--drop-shadow);background:var(--ref-palette-neutral100);flex-grow:1;margin:20px}\n/*# sourceURL=requestHTMLView.css */\n");class ze extends o.Widget.VBox{dataURL;constructor(e){super(!0),this.dataURL=e,this.contentElement.classList.add("html","request-view")}static create(e){const t=e.asDataUrl();return t?new ze(t):null}wasShown(){this.createIFrame(),this.registerCSSFiles([Ge])}willHide(){this.contentElement.removeChildren()}createIFrame(){this.contentElement.removeChildren();const e=document.createElement("iframe");e.className="html-preview-frame",e.setAttribute("sandbox",""),e.setAttribute("csp","default-src 'none';img-src data:;style-src 'unsafe-inline'"),e.setAttribute("src",this.dataURL),e.tabIndex=-1,o.ARIAUtils.markAsPresentation(e),this.contentElement.appendChild(e)}}var _e=Object.freeze({__proto__:null,RequestHTMLView:ze});const Ke={thisRequestHasNoResponseData:"This request has no response data available.",failedToLoadResponseData:"Failed to load response data"},$e=i.i18n.registerUIStrings("panels/network/RequestResponseView.ts",Ke),Xe=i.i18n.getLocalizedString.bind(void 0,$e);class Ye extends o.Widget.VBox{request;contentViewPromise;constructor(e){super(),this.element.classList.add("request-view"),this.element.setAttribute("jslog",`${l.pane("response").track({resize:!0})}`),this.request=e,this.contentViewPromise=null}static async sourceViewForRequest(i){let o=Je.get(i);if(void 0!==o)return o;const s=await i.requestStreamingContent();if(y.StreamingContentData.isError(s)||!s.isTextContent&&"application/wasm"!==s.mimeType)return Je.delete(i),null;let n;n="application/json"===e.ResourceType.ResourceType.simplifyContentType(i.mimeType)?i.mimeType:i.resourceType().canonicalMimeType()||i.mimeType;const a="application/wasm"!==s.mimeType&&y.TextUtils.isMinified(s.content().text),l=e.ResourceType.ResourceType.mediaTypeForMetrics(n,i.resourceType().isFromSourceMap(),a);return t.userMetrics.networkPanelResponsePreviewOpened(l),o=r.ResourceSourceFrame.ResourceSourceFrame.createSearchableView(i,n),Je.set(i,o),o}wasShown(){this.doShowPreview()}doShowPreview(){return this.contentViewPromise||(this.contentViewPromise=this.showPreview()),this.contentViewPromise}async showPreview(){const e=await this.createPreview();return e.show(this.element),e}async createPreview(){const e=await this.request.requestStreamingContent();if(y.StreamingContentData.isError(e))return new o.EmptyWidget.EmptyWidget(Xe(Ke.failedToLoadResponseData)+": "+e.error);const t=await Ye.sourceViewForRequest(this.request);return t&&204!==this.request.statusCode?t:new o.EmptyWidget.EmptyWidget(Xe(Ke.thisRequestHasNoResponseData))}async revealPosition(e){const t=await this.doShowPreview();t instanceof r.ResourceSourceFrame.SearchableContainer&&t.revealPosition(e)}}const Je=new WeakMap;var Ze=Object.freeze({__proto__:null,RequestResponseView:Ye});const Qe=new CSSStyleSheet;Qe.replaceSync(".tree-outline{padding-left:0}.tree-outline > ol{padding-bottom:5px;border-bottom:solid 1px var(--sys-color-divider)}.tree-outline > .parent{user-select:none;font-weight:bold;color:var(--sys-color-on-surface);margin-top:-1px;display:flex;align-items:center;height:26px}.tree-outline li{padding-left:5px;line-height:20px}.tree-outline li:not(.parent){display:block;margin-left:10px}.tree-outline li:not(.parent)::before{display:none}.tree-outline .header-name{color:var(--sys-color-token-subtle);display:inline-block;margin-right:0.25em;font-weight:bold;vertical-align:top;white-space:pre-wrap}.tree-outline .header-separator{user-select:none}.tree-outline .header-value{display:inline;margin-right:1em;white-space:pre-wrap;word-break:break-all;margin-top:1px}.tree-outline .header-toggle{display:inline;margin-left:30px;font-weight:normal;color:var(--sys-color-state-disabled)}.tree-outline .header-toggle:hover{color:var(--sys-color-state-hover-on-subtle)}.tree-outline .error-log{color:var(--sys-color-error);display:inline-block;margin-right:0.25em;margin-left:0.25em;font-weight:bold;vertical-align:top;white-space:pre-wrap}.tree-outline .hex-data{display:block;word-break:break-word;margin-left:20px}.tree-outline .error-field{color:var(--sys-color-error)}.prompt-icon{margin-top:2px}\n/*# sourceURL=signedExchangeInfoTree.css */\n");const et=new CSSStyleSheet;et.replaceSync(".signed-exchange-info-view{user-select:text;overflow:auto}.signed-exchange-info-tree{flex-grow:1;overflow-y:auto;margin:0}\n/*# sourceURL=signedExchangeInfoView.css */\n");const tt={errors:"Errors",signedHttpExchange:"Signed HTTP exchange",learnmore:"Learn more",requestUrl:"Request URL",responseCode:"Response code",headerIntegrityHash:"Header integrity hash",responseHeaders:"Response headers",signature:"Signature",label:"Label",certificateUrl:"Certificate URL",viewCertificate:"View certificate",integrity:"Integrity",certificateSha:"Certificate SHA256",validityUrl:"Validity URL",date:"Date",expires:"Expires",certificate:"Certificate",subject:"Subject",validFrom:"Valid from",validUntil:"Valid until",issuer:"Issuer"},it=i.i18n.registerUIStrings("panels/network/SignedExchangeInfoView.ts",tt),rt=i.i18n.getLocalizedString.bind(void 0,it);class ot extends o.Widget.VBox{responseHeadersItem;constructor(e){super(),console.assert(null!==e.signedExchangeInfo());const i=e.signedExchangeInfo();this.element.classList.add("signed-exchange-info-view");const r=new o.TreeOutline.TreeOutlineInShadow;r.registerCSSFiles([Qe]),r.element.classList.add("signed-exchange-info-tree"),r.setFocusable(!1),r.makeDense(),r.expandTreeElementsWhenArrowing=!0,this.element.appendChild(r.element);const s=new Map;if(i.errors&&i.errors.length){const e=new st(r,rt(tt.errors));for(const t of i.errors){const i=document.createDocumentFragment(),r=new m.Icon.Icon;if(r.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},r.classList.add("prompt-icon"),i.appendChild(r),i.createChild("div","error-log").textContent=t.message,e.createLeaf(i),t.errorField){let e=s.get(t.signatureIndex);e||(e=new Set,s.set(t.signatureIndex,e)),e.add(t.errorField)}}}const n=document.createDocumentFragment();n.createChild("div","header-name").textContent=rt(tt.signedHttpExchange);const a=o.XLink.XLink.create("https://github.com/WICG/webpackage",rt(tt.learnmore),"header-toggle",void 0,"learn-more");n.appendChild(a);const l=new st(r,n);if(i.header){const n=i.header,a=e.redirectDestination(),d=this.formatHeader(rt(tt.requestUrl),n.requestUrl);if(a){const e=k.Linkifier.Linkifier.linkifyRevealable(a,"View request",void 0,void 0,void 0,"redirect-destination-request");e.classList.add("header-toggle"),d.appendChild(e)}l.createLeaf(d),l.createLeaf(this.formatHeader(rt(tt.responseCode),String(n.responseCode))),l.createLeaf(this.formatHeader(rt(tt.headerIntegrityHash),n.headerIntegrity)),this.responseHeadersItem=l.createLeaf(this.formatHeader(rt(tt.responseHeaders),""));const c=n.responseHeaders;for(const e in c){const t=new o.TreeOutline.TreeElement(this.formatHeader(e,c[e]));t.selectable=!1,this.responseHeadersItem.appendChild(t)}this.responseHeadersItem.expand();for(let e=0;e<n.signatures.length;++e){const i=s.get(e)||new Set,o=n.signatures[e],a=new st(r,rt(tt.signature));if(a.createLeaf(this.formatHeader(rt(tt.label),o.label)),a.createLeaf(this.formatHeaderForHexData(rt(tt.signature),o.signature,i.has("signatureSig"))),o.certUrl){const e=this.formatHeader(rt(tt.certificateUrl),o.certUrl,i.has("signatureCertUrl"));if(o.certificates){const i=e.createChild("span","devtools-link header-toggle");i.textContent=rt(tt.viewCertificate),i.addEventListener("click",t.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer.bind(null,o.certificates),!1)}a.createLeaf(e)}a.createLeaf(this.formatHeader(rt(tt.integrity),o.integrity,i.has("signatureIntegrity"))),o.certSha256&&a.createLeaf(this.formatHeaderForHexData(rt(tt.certificateSha),o.certSha256,i.has("signatureCertSha256"))),a.createLeaf(this.formatHeader(rt(tt.validityUrl),o.validityUrl,i.has("signatureValidityUrl"))),a.createLeaf().title=this.formatHeader(rt(tt.date),new Date(1e3*o.date).toUTCString(),i.has("signatureTimestamps")),a.createLeaf().title=this.formatHeader(rt(tt.expires),new Date(1e3*o.expires).toUTCString(),i.has("signatureTimestamps"))}}if(i.securityDetails){const e=i.securityDetails,t=new st(r,rt(tt.certificate));t.createLeaf(this.formatHeader(rt(tt.subject),e.subjectName)),t.createLeaf(this.formatHeader(rt(tt.validFrom),new Date(1e3*e.validFrom).toUTCString())),t.createLeaf(this.formatHeader(rt(tt.validUntil),new Date(1e3*e.validTo).toUTCString())),t.createLeaf(this.formatHeader(rt(tt.issuer),e.issuer))}}formatHeader(e,t,i){const r=document.createDocumentFragment(),o=r.createChild("div","header-name");o.textContent=e+": ",r.createChild("span","header-separator");const s=r.createChild("div","header-value source-code");return s.textContent=t,i&&(o.classList.add("error-field"),s.classList.add("error-field")),r}formatHeaderForHexData(e,t,i){const r=document.createDocumentFragment(),o=r.createChild("div","header-name");o.textContent=e+": ",r.createChild("span","header-separator");const s=r.createChild("div","header-value source-code hex-data");return s.textContent=t.replace(/(.{2})/g,"$1 "),i&&(o.classList.add("error-field"),s.classList.add("error-field")),r}wasShown(){super.wasShown(),this.registerCSSFiles([et])}}class st extends o.TreeOutline.TreeElement{toggleOnClick;expanded;constructor(e,t){super(t,!0),this.selectable=!1,this.toggleOnClick=!0,this.expanded=!0,e.appendChild(this)}createLeaf(e){const t=new o.TreeOutline.TreeElement(e);return t.selectable=!1,this.appendChild(t),t}}var nt=Object.freeze({__proto__:null,SignedExchangeInfoView:ot,Category:st});const at={failedToLoadResponseData:"Failed to load response data",previewNotAvailable:"Preview not available"},lt=i.i18n.registerUIStrings("panels/network/RequestPreviewView.ts",at),dt=i.i18n.getLocalizedString.bind(void 0,lt);class ct extends Ye{constructor(e){super(e),this.element.setAttribute("jslog",`${l.pane("preview").track({resize:!0})}`)}async showPreview(){const e=await super.showPreview();if(!(e instanceof o.View.SimpleView))return e;const t=new o.Toolbar.Toolbar("network-item-preview-toolbar",this.element);return e.toolbarItems().then((e=>{e.map((e=>t.appendToolbarItem(e)))})),e}async htmlPreview(){const e=await this.request.requestContentData();if(y.ContentData.ContentData.isError(e))return new o.EmptyWidget.EmptyWidget(dt(at.failedToLoadResponseData)+": "+e.error);if(!new Set(["text/html","text/plain","application/xhtml+xml"]).has(this.request.mimeType))return null;const t=await r.JSONView.JSONView.createView(e.text);return t||ze.create(e)}async createPreview(){if(this.request.signedExchangeInfo())return new ot(this.request);if(this.request.webBundleInfo())return v.LegacyWrapper.legacyWrapper(o.Widget.VBox,new f.WebBundleInfoView.WebBundleInfoView(this.request));const e=await this.htmlPreview();if(e)return e;const t=await r.PreviewFactory.PreviewFactory.createPreview(this.request,this.request.mimeType);return t||new o.EmptyWidget.EmptyWidget(dt(at.previewNotAvailable))}}var ht=Object.freeze({__proto__:null,RequestPreviewView:ct});const ut=new CSSStyleSheet;ut.replaceSync('.network-timing-table{width:380px;border-spacing:0;padding-left:10px;padding-right:10px;line-height:initial;table-layout:fixed}.network-timing-start{border-top:5px solid transparent}.network-timing-start th span.network-timing-hidden-header{height:1px;width:1px;position:absolute;overflow:hidden}.network-timing-table-header td,\n.network-timing-footer td{border-top:10px solid transparent}.network-timing-table-header td{color:var(--sys-color-token-subtle)}.network-timing-table td{padding:4px 0}.network-timing-table-header td:last-child{text-align:right}.network-timing-footer td:last-child{font-weight:bold;text-align:right}table.network-timing-table > tr:not(.network-timing-table-header):not(.network-timing-footer) > td:first-child{padding-left:12px}.network-timing-table col.labels{width:156px}.network-timing-table col.duration{width:80px}.network-timing-table td.caution{font-weight:bold;color:var(--issue-color-yellow);padding:2px 0}.network-timing-table hr.break{background-color:var(--sys-color-divider);border:none;height:1px}.network-timing-row{position:relative;height:15px}.network-timing-bar{position:absolute;min-width:1px;top:0;bottom:0}.network-timing-bar-title{color:var(--sys-color-on-surface);white-space:nowrap;text-align:right}.network-timing-bar.queueing,\n.network-timing-bar.total{border:1px solid var(--sys-color-token-subtle)}.network-timing-bar.blocking,\n.-theme-preserve{background-color:var(--network-overview-blocking)}.network-timing-bar.proxy,\n.-theme-preserve{background-color:var(--override-network-overview-proxy)}.network-timing-bar.dns,\n.-theme-preserve{background-color:var(--sys-color-cyan)}.network-timing-bar.connecting,\n.network-timing-bar.serviceworker,\n.network-timing-bar.serviceworker-preparation,\n.network-timing-bar.serviceworker-routerevaluation,\n.network-timing-bar.serviceworker-cachelookup,\n.-theme-preserve{background-color:var(--network-overview-service-worker)}.network-timing-bar.ssl,\n.-theme-preserve{background-color:var(--network-overview-ssl)}.network-timing-bar.serviceworker-respondwith,\n.-theme-preserve{background-color:var(--network-overview-service-worker-respond-with)}.network-fetch-timing-bar-clickable::before{user-select:none;mask-image:var(--image-file-triangle-right);float:left;width:14px;height:14px;margin-right:2px;content:"";position:relative;background-color:var(--icon-default);transition:transform 200ms}.network-fetch-timing-bar-clickable{position:relative;left:-12px}.network-fetch-timing-bar-clickable:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.network-fetch-timing-bar-clickable[aria-checked="true"]::before{transform:rotate(90deg)}.network-fetch-timing-bar-details-collapsed{display:none}.network-fetch-timing-bar-details-expanded{display:block}.network-fetch-timing-bar-details{padding-left:11px;width:fit-content}.network-fetch-details-treeitem{width:max-content}.network-timing-bar.sending,\n.-theme-preserve{background-color:var(--override-network-overview-sending)}.network-timing-bar.waiting,\n.-theme-preserve{background-color:var(--network-overview-waiting)}.network-timing-bar.receiving,\n.network-timing-bar.receiving-push,\n.-theme-preserve{background-color:var(--network-overview-receiving)}.network-timing-bar.push,\n.-theme-preserve{background-color:var(--network-overview-push)}.server-timing-row:nth-child(even){background:var(--sys-color-surface1)}.network-timing-bar.server-timing,\n.-theme-preserve{background-color:var(--sys-color-neutral-container)}.network-timing-table td.network-timing-metric{white-space:nowrap;max-width:150px;overflow-x:hidden;text-overflow:ellipsis}.network-timing-bar.proxy,\n.network-timing-bar.dns,\n.network-timing-bar.ssl,\n.network-timing-bar.connecting,\n.network-timing-bar.blocking{height:10px;margin:auto}@media (forced-colors: active){.network-timing-bar.blocking,\n  .network-timing-bar.proxy,\n  .network-timing-bar.dns,\n  .network-timing-bar.connecting,\n  .network-timing-bar.serviceworker,\n  .network-timing-bar.serviceworker-preparation,\n  .network-timing-bar.ssl,\n  .network-timing-bar.sending,\n  .network-timing-bar.waiting,\n  .network-timing-bar.receiving,\n  .network-timing-bar.receiving-push,\n  .network-timing-bar.push,\n  .network-timing-bar.server-timing,\n  .-theme-preserve{forced-color-adjust:none}.network-timing-table-header td,\n  .network-timing-footer td{forced-color-adjust:none;color:ButtonText}}\n/*# sourceURL=networkTimingTable.css */\n');const pt={receivingPush:"Receiving `Push`",queueing:"Queueing",stalled:"Stalled",initialConnection:"Initial connection",dnsLookup:"DNS Lookup",proxyNegotiation:"Proxy negotiation",readingPush:"Reading `Push`",contentDownload:"Content Download",requestSent:"Request sent",requestToServiceworker:"Request to `ServiceWorker`",startup:"Startup",respondwith:"respondWith",ssl:"SSL",total:"Total",waitingTtfb:"Waiting for server response",label:"Label",waterfall:"Waterfall",duration:"Duration",queuedAtS:"Queued at {PH1}",startedAtS:"Started at {PH1}",serverPush:"Server Push",resourceScheduling:"Resource Scheduling",connectionStart:"Connection Start",requestresponse:"Request/Response",cautionRequestIsNotFinishedYet:"CAUTION: request is not finished yet!",explanation:"Explanation",serverTiming:"Server Timing",time:"TIME",theServerTimingApi:"the Server Timing API",duringDevelopmentYouCanUseSToAdd:"During development, you can use {PH1} to add insights into the server-side timing of this request.",durationC:"DURATION",originalRequest:"Original Request",responseReceived:"Response Received",unknown:"Unknown",sourceOfResponseS:"Source of response: {PH1}",cacheStorageCacheNameS:"Cache storage cache name: {PH1}",cacheStorageCacheNameUnknown:"Cache storage cache name: Unknown",retrievalTimeS:"Retrieval Time: {PH1}",serviceworkerCacheStorage:"`ServiceWorker` cache storage",fromHttpCache:"From HTTP cache",networkFetch:"Network fetch",fallbackCode:"Fallback code"},gt=i.i18n.registerUIStrings("panels/network/RequestTimingView.ts",pt),mt=i.i18n.getLocalizedString.bind(void 0,gt);class wt extends o.Widget.VBox{request;calculator;lastMinimumBoundary;tableElement;constructor(e,t){super(),this.element.classList.add("resource-timing-view"),this.request=e,this.calculator=t,this.lastMinimumBoundary=-1}static timeRangeTitle(e){switch(e){case"push":return mt(pt.receivingPush);case"queueing":return mt(pt.queueing);case"blocking":return mt(pt.stalled);case"connecting":return mt(pt.initialConnection);case"dns":return mt(pt.dnsLookup);case"proxy":return mt(pt.proxyNegotiation);case"receiving-push":return mt(pt.readingPush);case"receiving":return mt(pt.contentDownload);case"sending":return mt(pt.requestSent);case"serviceworker":return mt(pt.requestToServiceworker);case"serviceworker-preparation":return mt(pt.startup);case"serviceworker-respondwith":return mt(pt.respondwith);case"ssl":return mt(pt.ssl);case"total":return mt(pt.total);case"waiting":return mt(pt.waitingTtfb);default:return e}}static calculateRequestTimeRanges(e,t){const i=[];function r(e,t,r){t<Number.MAX_VALUE&&t<=r&&i.push({name:e,start:t,end:r})}function o(e){for(let t=0;t<e.length;++t)if(e[t]>0)return e[t]}function s(e,t,i){t>=0&&i>=0&&r(e,d+t/1e3,d+i/1e3)}function n(e,t,i){r(e,d+t/1e3,d+i/1e3)}const a=e.timing;if(!a){const t=-1!==e.issueTime()?e.issueTime():-1!==e.startTime?e.startTime:0,o=-1!==e.issueTime()&&-1!==e.startTime&&e.issueTime()!==e.startTime,s=-1===e.responseReceivedTime?o?e.startTime:Number.MAX_VALUE:e.responseReceivedTime,n=-1===e.endTime?Number.MAX_VALUE:e.endTime;r("total",t,n),r("blocking",t,s);return r(-1===e.responseReceivedTime?"connecting":"receiving",s,n),i}const l=e.issueTime(),d=a.requestTime,c=o([e.endTime,e.responseReceivedTime])||d;if(r("total",l<d?l:d,c),a.pushStart){const e=a.pushEnd||c;e>t&&r("push",Math.max(a.pushStart,t),e)}l<d&&r("queueing",l,d);const h=1e3*(e.responseReceivedTime-d);if(e.fetchedViaServiceWorker)s("blocking",0,a.workerStart),s("serviceworker-preparation",a.workerStart,a.workerReady),s("serviceworker-respondwith",a.workerFetchStart,a.workerRespondWithSettled),s("serviceworker",a.workerReady,a.sendEnd),s("waiting",a.sendEnd,h);else if(!a.pushStart){const e=o([a.dnsStart,a.connectStart,a.sendStart,h])||0;s("blocking",0,e),s("proxy",a.proxyStart,a.proxyEnd),s("dns",a.dnsStart,a.dnsEnd),s("connecting",a.connectStart,a.connectEnd),s("ssl",a.sslStart,a.sslEnd),s("sending",a.sendStart,a.sendEnd),s("waiting",Math.max(a.sendEnd,a.connectEnd,a.dnsEnd,a.proxyEnd,e),h)}const{serviceWorkerRouterInfo:u}=e;if(u){if(a.workerRouterEvaluationStart){let e=a.sendStart;"cache"===u?.matchedSourceType&&a.workerCacheLookupStart?e=a.workerCacheLookupStart:"fetch-event"===u?.actualSourceType&&(e=a.workerStart),n("serviceworker-routerevaluation",a.workerRouterEvaluationStart,e)}if(a.workerCacheLookupStart){let e=a.sendStart;"cache"===u?.actualSourceType&&(e=a.receiveHeadersStart),n("serviceworker-cachelookup",a.workerCacheLookupStart,e)}}return-1!==e.endTime&&r(a.pushStart?"receiving-push":"receiving",e.responseReceivedTime,c),i}static createTimingTable(t,r){const s=document.createElement("table");s.classList.add("network-timing-table"),s.setAttribute("jslog",`${l.pane("timing").track({resize:!0})}`);const n=s.createChild("colgroup");n.createChild("col","labels"),n.createChild("col","bars"),n.createChild("col","duration");const a=wt.calculateRequestTimeRanges(t,r.minimumBoundary()),d=a.map((e=>e.start)).reduce(((e,t)=>Math.min(e,t))),c=a.map((e=>e.end)).reduce(((e,t)=>Math.max(e,t))),h=100/(c-d);let u,p,g,m,w=0;const k=s.createChild("thead","network-timing-start"),b=k.createChild("tr"),v=b.createChild("th");v.createChild("span","network-timing-hidden-header").textContent=mt(pt.label),v.scope="col";const f=b.createChild("th");f.createChild("span","network-timing-hidden-header").textContent=mt(pt.waterfall),f.scope="col";const C=b.createChild("th");C.createChild("span","network-timing-hidden-header").textContent=mt(pt.duration),C.scope="col";const S=k.createChild("tr").createChild("td"),y=k.createChild("tr").createChild("td");let T;S.colSpan=y.colSpan=3,o.UIUtils.createTextChild(S,mt(pt.queuedAtS,{PH1:r.formatValue(t.issueTime(),2)})),o.UIUtils.createTextChild(y,mt(pt.startedAtS,{PH1:r.formatValue(t.startTime,2)}));for(let e=0;e<a.length;++e){const t=a[e],n=t.name;if("total"===n){w=t.end-t.start;continue}"push"===n?M(mt(pt.serverPush)):"queueing"===n?m||(m=M(mt(pt.resourceScheduling))):bt.has(n)?u||(u=M(mt(pt.connectionStart))):kt.has(n)?p||(p=M("Service Worker")):g||(g=M(mt(pt.requestresponse)));const l=h*(t.start-d);T=h*(c-t.end);const k=t.end-t.start,b=s.createChild("tr"),v=b.createChild("td");o.UIUtils.createTextChild(v,wt.timeRangeTitle(n));const f=b.createChild("td").createChild("div","network-timing-row"),C=f.createChild("span","network-timing-bar "+n);C.style.left=l+"%",C.style.right=T+"%",C.textContent="​",o.ARIAUtils.setLabel(f,mt(pt.startedAtS,{PH1:r.formatValue(t.start,2)}));b.createChild("td").createChild("div","network-timing-bar-title").textContent=i.TimeUtilities.secondsToString(k,!0),"serviceworker-respondwith"===t.name&&(v.classList.add("network-fetch-timing-bar-clickable"),s.createChild("tr","network-fetch-timing-bar-details"),v.setAttribute("tabindex","0"),v.setAttribute("role","switch"),o.ARIAUtils.setChecked(v,!1))}if(!t.finished&&!t.preserved){const e=s.createChild("tr").createChild("td","caution");e.colSpan=3,o.UIUtils.createTextChild(e,mt(pt.cautionRequestIsNotFinishedYet))}const x=s.createChild("tr","network-timing-footer"),R=x.createChild("td");R.colSpan=1;const I=o.XLink.XLink.create("https://developer.chrome.com/docs/devtools/network/reference/#timing-explanation",mt(pt.explanation),void 0,void 0,"explanation");R.appendChild(I),x.createChild("td"),o.UIUtils.createTextChild(x.createChild("td"),i.TimeUtilities.secondsToString(w,!0));const q=t.serverTimings,L=void 0===T?100:T,F=s.createChild("tr","network-timing-table-header").createChild("td");F.colSpan=3,F.createChild("hr","break");const P=s.createChild("tr","network-timing-table-header");if(o.UIUtils.createTextChild(P.createChild("td"),mt(pt.serverTiming)),P.createChild("td"),o.UIUtils.createTextChild(P.createChild("td"),mt(pt.time)),!q){const e=s.createChild("tr").createChild("td");e.colSpan=3;const t=o.XLink.XLink.create("https://web.dev/custom-metrics/#server-timing-api",mt(pt.theServerTimingApi),void 0,void 0,"server-timing-api");return e.appendChild(i.i18n.getFormatLocalizedString(gt,pt.duringDevelopmentYouCanUseSToAdd,{PH1:t})),s}return q.filter((e=>"total"!==e.metric.toLowerCase())).forEach((e=>H(e,L))),q.filter((e=>"total"===e.metric.toLowerCase())).forEach((e=>H(e,L))),s;function H(t,r){const n=new e.Color.Generator({min:0,max:360,count:36},{min:50,max:80,count:void 0},80),a="total"===t.metric.toLowerCase(),l=s.createChild("tr",a?"network-timing-footer":"server-timing-row"),u=l.createChild("td","network-timing-metric"),p=t.description||t.metric;o.UIUtils.createTextChild(u,p),o.Tooltip.Tooltip.install(u,p);const g=l.createChild("td").createChild("div","network-timing-row");if(null===t.value)return;const m=h*(c-d-t.value/1e3);if(m>=0){const e=g.createChild("span","network-timing-bar server-timing");e.style.left=m+"%",e.style.right=r+"%",e.textContent="​",a||(e.style.backgroundColor=n.colorForID(t.metric))}l.createChild("td").createChild("div","network-timing-bar-title").textContent=i.TimeUtilities.millisToString(t.value,!0)}function M(e){const t=s.createChild("tr","network-timing-table-header"),i=t.createChild("td");return o.UIUtils.createTextChild(i,e),o.ARIAUtils.markAsHeading(i,2),o.UIUtils.createTextChild(t.createChild("td"),""),o.UIUtils.createTextChild(t.createChild("td"),mt(pt.durationC)),t}}constructFetchDetailsView(){if(!this.tableElement)return;const e=this.tableElement.ownerDocument,t=e.querySelector(".network-fetch-timing-bar-details");if(!t)return;t.classList.add("network-fetch-timing-bar-details-collapsed"),self.onInvokeElement(this.tableElement,this.onToggleFetchDetails.bind(this,t));const i=new o.TreeOutline.TreeOutlineInShadow;t.appendChild(i.element);const r=a.NetworkLog.NetworkLog.instance().originalRequestForURL(this.request.url());if(r){const e=n.RemoteObject.RemoteObject.fromLocalObject(r),t=new S.ObjectPropertiesSection.RootElement(e);t.title=mt(pt.originalRequest),i.appendChild(t)}const s=a.NetworkLog.NetworkLog.instance().originalResponseForURL(this.request.url());if(s){const e=n.RemoteObject.RemoteObject.fromLocalObject(s),t=new S.ObjectPropertiesSection.RootElement(e);t.title=mt(pt.responseReceived),i.appendChild(t)}const l=e.createElement("div");l.classList.add("network-fetch-details-treeitem");let d=mt(pt.unknown);const c=this.request.serviceWorkerResponseSource();c&&(d=this.getLocalizedResponseSourceForCode(c)),l.textContent=mt(pt.sourceOfResponseS,{PH1:d});const h=new o.TreeOutline.TreeElement(l);i.appendChild(h);const u=e.createElement("div");u.classList.add("network-fetch-details-treeitem");const p=this.request.getResponseCacheStorageCacheName();u.textContent=p?mt(pt.cacheStorageCacheNameS,{PH1:p}):mt(pt.cacheStorageCacheNameUnknown);const g=new o.TreeOutline.TreeElement(u);i.appendChild(g);const m=this.request.getResponseRetrievalTime();if(m){const t=e.createElement("div");t.classList.add("network-fetch-details-treeitem"),t.textContent=mt(pt.retrievalTimeS,{PH1:m.toString()});const r=new o.TreeOutline.TreeElement(t);i.appendChild(r)}}getLocalizedResponseSourceForCode(e){switch(e){case"cache-storage":return mt(pt.serviceworkerCacheStorage);case"http-cache":return mt(pt.fromHttpCache);case"network":return mt(pt.networkFetch);default:return mt(pt.fallbackCode)}}onToggleFetchDetails(e,i){if(!i.target)return;const r=i.target;if(r.classList.contains("network-fetch-timing-bar-clickable")){e.classList.contains("network-fetch-timing-bar-details-collapsed")&&t.userMetrics.actionTaken(t.UserMetrics.Action.NetworkPanelServiceWorkerRespondWith);const i="true"===r.getAttribute("aria-checked");r.setAttribute("aria-checked",String(!i)),e.classList.toggle("network-fetch-timing-bar-details-collapsed"),e.classList.toggle("network-fetch-timing-bar-details-expanded")}}wasShown(){this.request.addEventListener(n.NetworkRequest.Events.TimingChanged,this.refresh,this),this.request.addEventListener(n.NetworkRequest.Events.FinishedLoading,this.refresh,this),this.calculator.addEventListener("BoundariesChanged",this.boundaryChanged,this),this.registerCSSFiles([ut]),this.refresh()}willHide(){this.request.removeEventListener(n.NetworkRequest.Events.TimingChanged,this.refresh,this),this.request.removeEventListener(n.NetworkRequest.Events.FinishedLoading,this.refresh,this),this.calculator.removeEventListener("BoundariesChanged",this.boundaryChanged,this)}refresh(){this.tableElement&&this.tableElement.remove(),this.tableElement=wt.createTimingTable(this.request,this.calculator),this.tableElement.classList.add("resource-timing-table"),this.element.appendChild(this.tableElement),this.request.fetchedViaServiceWorker&&this.constructFetchDetailsView()}boundaryChanged(){const e=this.calculator.minimumBoundary();e!==this.lastMinimumBoundary&&(this.lastMinimumBoundary=e,this.refresh())}}const kt=new Set(["serviceworker","serviceworker-preparation","serviceworker-respondwith","serviceworker-routerevaluation","serviceworker-cachelookup"]),bt=new Set(["queueing","blocking","connecting","dns","proxy","ssl"]);var vt=Object.freeze({__proto__:null,RequestTimingView:wt,ServiceWorkerRangeNames:kt,ConnectionSetupRangeNames:bt});const ft=new CSSStyleSheet;ft.replaceSync('.websocket-frame-view{user-select:text}.websocket-frame-view .data-grid{flex:auto;border:none}.websocket-frame-view .data-grid .data{background-image:none}.websocket-frame-view-td{border-bottom:1px solid var(--sys-color-divider)}.websocket-frame-view .data-grid td,\n.websocket-frame-view .data-grid th{border-left-color:1px solid var(--sys-color-divider)}.websocket-frame-view-row-send td:first-child::before{content:"\\2B06";color:var(--sys-color-tertiary);padding-right:4px}.websocket-frame-view-row-receive td:first-child::before{content:"\\2B07";color:var(--sys-color-error);padding-right:4px}.websocket-frame-view-row-send{background-color:color-mix(in sRGB,var(--sys-color-tertiary-container),transparent 50%)}.websocket-frame-view-row-error{background-color:var(--sys-color-surface-error);color:var(--sys-color-on-surface-error)}.websocket-frame-view .toolbar{border-bottom:1px solid var(--sys-color-divider)}\n/*# sourceURL=webSocketFrameView.css */\n');const Ct={data:"Data",length:"Length",time:"Time",webSocketFrame:"Web Socket Frame",clearAll:"Clear All",filter:"Filter",selectMessageToBrowseItsContent:"Select message to browse its content.",copyMessageD:"Copy message...",copyMessage:"Copy message",clearAllL:"Clear all",sOpcodeSMask:"{PH1} (Opcode {PH2}, mask)",sOpcodeS:"{PH1} (Opcode {PH2})",continuationFrame:"Continuation Frame",textMessage:"Text Message",binaryMessage:"Binary Message",connectionCloseMessage:"Connection Close Message",pingMessage:"Ping Message",pongMessage:"Pong Message",all:"All",send:"Send",receive:"Receive",na:"N/A",filterUsingRegex:"Filter using regex (example: (web)?socket)"},St=i.i18n.registerUIStrings("panels/network/ResourceWebSocketFrameView.ts",Ct),yt=i.i18n.getLocalizedString.bind(void 0,St),Tt=i.i18n.getLazilyComputedLocalizedString.bind(void 0,St);class xt extends o.Widget.VBox{request;splitWidget;dataGrid;timeComparator;mainToolbar;clearAllButton;filterTypeCombobox;filterType;filterTextInput;filterRegex;frameEmptyWidget;selectedNode;currentSelectedNode;messageFilterSetting=e.Settings.Settings.instance().createSetting("network-web-socket-message-filter","");constructor(e){super(),this.element.classList.add("websocket-frame-view"),this.element.setAttribute("jslog",`${l.pane("web-socket-messages").track({resize:!0})}`),this.request=e,this.splitWidget=new o.SplitWidget.SplitWidget(!1,!0,"resource-web-socket-frame-split-view-state"),this.splitWidget.show(this.element);const i=[{id:"data",title:yt(Ct.data),sortable:!1,weight:88},{id:"length",title:yt(Ct.length),sortable:!1,align:"right",weight:5},{id:"time",title:yt(Ct.time),sortable:!0,weight:7}];this.dataGrid=new d.SortableDataGrid.SortableDataGrid({displayName:yt(Ct.webSocketFrame),columns:i,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setRowContextMenuCallback(function(e,i){const r=i,o=r.binaryView();o?o.addCopyToContextMenu(e,yt(Ct.copyMessageD)):e.clipboardSection().appendItem(yt(Ct.copyMessage),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(t.InspectorFrontendHost.InspectorFrontendHostInstance,r.data.data),{jslogContext:"copy"});e.footerSection().appendItem(yt(Ct.clearAllL),this.clearFrames.bind(this),{jslogContext:"clear-all"})}.bind(this)),this.dataGrid.setStickToBottom(!0),this.dataGrid.setCellClass("websocket-frame-view-td"),this.timeComparator=Lt,this.dataGrid.sortNodes(this.timeComparator,!1),this.dataGrid.markColumnAsSortedBy("time",d.DataGrid.Order.Ascending),this.dataGrid.addEventListener("SortingChanged",this.sortItems,this),this.dataGrid.setName("resource-web-socket-frame-view"),this.dataGrid.addEventListener("SelectedNode",(e=>{this.onFrameSelected(e)}),this),this.dataGrid.addEventListener("DeselectedNode",this.onFrameDeselected,this),this.mainToolbar=new o.Toolbar.Toolbar(""),this.clearAllButton=new o.Toolbar.ToolbarButton(yt(Ct.clearAll),"clear"),this.clearAllButton.addEventListener("Click",this.clearFrames,this),this.mainToolbar.appendToolbarItem(this.clearAllButton),this.filterTypeCombobox=new o.Toolbar.ToolbarComboBox(this.updateFilterSetting.bind(this),yt(Ct.filter));for(const e of It){const t=this.filterTypeCombobox.createOption(e.label(),e.name);this.filterTypeCombobox.addOption(t)}this.mainToolbar.appendToolbarItem(this.filterTypeCombobox),this.filterType=null;const r=yt(Ct.filterUsingRegex);this.filterTextInput=new o.Toolbar.ToolbarFilter(r,.4),this.filterTextInput.addEventListener("TextChanged",this.updateFilterSetting,this);const s=this.messageFilterSetting.get();s&&this.filterTextInput.setValue(s),this.filterRegex=null,this.mainToolbar.appendToolbarItem(this.filterTextInput);const n=new o.Widget.VBox;n.element.appendChild(this.mainToolbar.element),this.dataGrid.asWidget().show(n.element),n.setMinimumSize(0,72),this.splitWidget.setMainWidget(n),this.frameEmptyWidget=new o.EmptyWidget.EmptyWidget(yt(Ct.selectMessageToBrowseItsContent)),this.splitWidget.setSidebarWidget(this.frameEmptyWidget),this.selectedNode=null,s&&this.applyFilter(s)}static opCodeDescription(e,t){const i=Rt[e]||(()=>"");return yt(t?Ct.sOpcodeSMask:Ct.sOpcodeS,{PH1:i(),PH2:e})}wasShown(){this.refresh(),this.registerCSSFiles([ft]),this.request.addEventListener(n.NetworkRequest.Events.WebsocketFrameAdded,this.frameAdded,this)}willHide(){this.request.removeEventListener(n.NetworkRequest.Events.WebsocketFrameAdded,this.frameAdded,this)}frameAdded(e){const t=e.data;this.frameFilter(t)&&this.dataGrid.insertChild(new qt(this.request.url(),t))}frameFilter(e){return(!this.filterType||e.type===this.filterType)&&(!this.filterRegex||this.filterRegex.test(e.text))}clearFrames(){Ft.set(this.request,this.request.frames().length),this.refresh()}updateFilterSetting(){const e=this.filterTextInput.value();this.messageFilterSetting.set(e),this.applyFilter(e)}applyFilter(e){const t=this.filterTypeCombobox.selectedOption().value;if(e)try{this.filterRegex=new RegExp(e,"i")}catch(t){this.filterRegex=new RegExp(s.StringUtilities.escapeForRegExp(e),"i")}else this.filterRegex=null;this.filterType="all"===t?null:t,this.refresh()}async onFrameSelected(t){this.currentSelectedNode=t.data;const i=this.currentSelectedNode.dataText(),o=this.currentSelectedNode.binaryView();if(o)return void this.splitWidget.setSidebarWidget(o);const s=await r.JSONView.JSONView.createView(i);s?this.splitWidget.setSidebarWidget(s):this.splitWidget.setSidebarWidget(new r.ResourceSourceFrame.ResourceSourceFrame(y.StaticContentProvider.StaticContentProvider.fromString(this.request.url(),e.ResourceType.resourceTypes.WebSocket,i),""))}onFrameDeselected(){this.currentSelectedNode=null,this.splitWidget.setSidebarWidget(this.frameEmptyWidget)}refresh(){this.dataGrid.rootNode().removeChildren();const e=this.request.url();let t=this.request.frames();const i=Ft.get(this.request)||0;t=t.slice(i),t=t.filter(this.frameFilter.bind(this)),t.forEach((t=>this.dataGrid.insertChild(new qt(e,t))))}sortItems(){this.dataGrid.sortNodes(this.timeComparator,!this.dataGrid.isSortOrderAscending())}}const Rt=function(){const e=[];return e[0]=Tt(Ct.continuationFrame),e[1]=Tt(Ct.textMessage),e[2]=Tt(Ct.binaryMessage),e[8]=Tt(Ct.connectionCloseMessage),e[9]=Tt(Ct.pingMessage),e[10]=Tt(Ct.pongMessage),e}(),It=[{name:"all",label:Tt(Ct.all),jslogContext:"all"},{name:"send",label:Tt(Ct.send),jslogContext:"send"},{name:"receive",label:Tt(Ct.receive),jslogContext:"receive"}];class qt extends d.SortableDataGrid.SortableDataGridNode{url;frame;isTextFrame;dataTextInternal;binaryViewInternal;constructor(e,t){let i=String(t.text.length);const r=new Date(1e3*t.time),a=("0"+r.getHours()).substr(-2)+":"+("0"+r.getMinutes()).substr(-2)+":"+("0"+r.getSeconds()).substr(-2)+"."+("00"+r.getMilliseconds()).substr(-3),l=document.createElement("div");o.UIUtils.createTextChild(l,a),o.Tooltip.Tooltip.install(l,r.toLocaleString());let d=t.text,c=xt.opCodeDescription(t.opCode,t.mask);const h=1===t.opCode;t.type===n.NetworkRequest.WebSocketFrameType.Error?(c=d,i=yt(Ct.na)):h?c=d:2===t.opCode?(i=s.NumberUtilities.bytesToString(s.StringUtilities.base64ToSize(t.text)),c=Rt[t.opCode]()):d=c,super({data:c,length:i,time:l}),this.url=e,this.frame=t,this.isTextFrame=h,this.dataTextInternal=d,this.binaryViewInternal=null}createCells(e){e.classList.toggle("websocket-frame-view-row-error",this.frame.type===n.NetworkRequest.WebSocketFrameType.Error),e.classList.toggle("websocket-frame-view-row-send",this.frame.type===n.NetworkRequest.WebSocketFrameType.Send),e.classList.toggle("websocket-frame-view-row-receive",this.frame.type===n.NetworkRequest.WebSocketFrameType.Receive),super.createCells(e)}nodeSelfHeight(){return 21}dataText(){return this.dataTextInternal}opCode(){return this.frame.opCode}binaryView(){return this.isTextFrame||this.frame.type===n.NetworkRequest.WebSocketFrameType.Error?null:(this.binaryViewInternal||this.dataTextInternal.length>0&&(this.binaryViewInternal=new N(this.dataTextInternal,s.DevToolsPath.EmptyUrlString,e.ResourceType.resourceTypes.WebSocket)),this.binaryViewInternal)}}function Lt(e,t){return e.frame.time-t.frame.time}const Ft=new WeakMap;var Pt=Object.freeze({__proto__:null,ResourceWebSocketFrameView:xt,opCodeDescriptions:Rt,ResourceWebSocketFrameNode:qt});const Ht={headers:"Headers",payload:"Payload",messages:"Messages",websocketMessages:"WebSocket messages",eventstream:"EventStream",preview:"Preview",responsePreview:"Response preview",signedexchangeError:"SignedExchange error",response:"Response",rawResponseData:"Raw response data",initiator:"Initiator",requestInitiatorCallStack:"Request initiator call stack",timing:"Timing",requestAndResponseTimeline:"Request and response timeline",thirdPartyPhaseout:"Cookies blocked due to third-party cookie phaseout.",trustTokens:"Private state tokens",trustTokenOperationDetails:"Private State Token operation details",cookies:"Cookies",requestAndResponseCookies:"Request and response cookies",containsOverriddenHeaders:"This response contains headers which are overridden by DevTools",responseIsOverridden:"This response is overridden by DevTools"},Mt=i.i18n.registerUIStrings("panels/network/NetworkItemView.ts",Ht),Et=i.i18n.getLocalizedString.bind(void 0,Mt);class At extends o.TabbedPane.TabbedPane{requestInternal;resourceViewTabSetting;headersViewComponent;payloadView;responseView;cookiesView;initialTab;constructor(t,i,r){super(),this.requestInternal=t,this.element.classList.add("network-item-view"),this.headerElement().setAttribute("jslog",`${l.toolbar("request-details").track({keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`);if(this.resourceViewTabSetting=e.Settings.Settings.instance().createSetting("resource-view-tab","headers-component"),this.headersViewComponent=new f.RequestHeadersView.RequestHeadersView(t),this.appendTab("headers-component",Et(Ht.headers),v.LegacyWrapper.legacyWrapper(o.Widget.VBox,this.headersViewComponent),Et(Ht.headers)),this.requestInternal.hasOverriddenHeaders()){const e=new m.Icon.Icon;e.data={iconName:"small-status-dot",color:"var(--sys-color-purple-bright)",width:"16px",height:"16px"},e.title=Et(Ht.containsOverriddenHeaders),this.setTabIcon("headers-component",e)}if(this.payloadView=null,this.maybeAppendPayloadPanel(),this.addEventListener(o.TabbedPane.Events.TabSelected,this.tabSelected,this),t.resourceType()===e.ResourceType.resourceTypes.WebSocket){const e=new xt(t);this.appendTab("web-socket-frames",Et(Ht.messages),e,Et(Ht.websocketMessages))}else if("text/event-stream"===t.mimeType)this.appendTab("eventSource",Et(Ht.eventstream),new J(t)),this.responseView=new Ye(t),this.appendTab("response",Et(Ht.response),this.responseView,Et(Ht.rawResponseData));else{this.responseView=new Ye(t);const e=new ct(t);this.appendTab("preview",Et(Ht.preview),e,Et(Ht.responsePreview));const i=t.signedExchangeInfo();if(i&&i.errors&&i.errors.length){const e=new m.Icon.Icon;e.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},o.Tooltip.Tooltip.install(e,Et(Ht.signedexchangeError)),this.setTabIcon("preview",e)}if(this.appendTab("response",Et(Ht.response),this.responseView,Et(Ht.rawResponseData)),this.requestInternal.hasOverriddenContent){const e=new m.Icon.Icon;e.title=Et(Ht.responseIsOverridden),e.data={iconName:"small-status-dot",color:"var(--sys-color-purple-bright)",width:"16px",height:"16px"},this.setTabIcon("response",e)}}this.appendTab("initiator",Et(Ht.initiator),new Pe(t),Et(Ht.requestInitiatorCallStack)),this.appendTab("timing",Et(Ht.timing),new wt(t,i),Et(Ht.requestAndResponseTimeline)),t.trustTokenParams()&&this.appendTab("trust-tokens",Et(Ht.trustTokens),v.LegacyWrapper.legacyWrapper(o.Widget.VBox,new f.RequestTrustTokensView.RequestTrustTokensView(t)),Et(Ht.trustTokenOperationDetails)),this.cookiesView=null,this.initialTab=r||this.resourceViewTabSetting.get(),this.setAutoSelectFirstItemOnShow(!1)}wasShown(){super.wasShown(),this.requestInternal.addEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.requestHeadersChanged,this),this.requestInternal.addEventListener(n.NetworkRequest.Events.ResponseHeadersChanged,this.maybeAppendCookiesPanel,this),this.requestInternal.addEventListener(n.NetworkRequest.Events.TrustTokenResultAdded,this.maybeShowErrorIconInTrustTokenTabHeader,this),this.maybeAppendCookiesPanel(),this.maybeShowErrorIconInTrustTokenTabHeader(),this.initialTab&&(this.selectTabInternal(this.initialTab),this.initialTab=void 0)}willHide(){this.requestInternal.removeEventListener(n.NetworkRequest.Events.RequestHeadersChanged,this.requestHeadersChanged,this),this.requestInternal.removeEventListener(n.NetworkRequest.Events.ResponseHeadersChanged,this.maybeAppendCookiesPanel,this),this.requestInternal.removeEventListener(n.NetworkRequest.Events.TrustTokenResultAdded,this.maybeShowErrorIconInTrustTokenTabHeader,this)}async requestHeadersChanged(){this.maybeAppendCookiesPanel(),this.maybeAppendPayloadPanel()}maybeAppendCookiesPanel(){const e=this.requestInternal.hasRequestCookies()||this.requestInternal.responseCookies.length>0;if(console.assert(e||!this.cookiesView,"Cookies were introduced in headers and then removed!"),e&&!this.cookiesView&&(this.cookiesView=new Te(this.requestInternal),this.appendTab("cookies",Et(Ht.cookies),this.cookiesView,Et(Ht.requestAndResponseCookies))),this.requestInternal.hasThirdPartyCookiePhaseoutIssue()){const e=new m.Icon.Icon;e.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},e.title=Et(Ht.thirdPartyPhaseout),this.setTabIcon("cookies",e)}}async maybeAppendPayloadPanel(){this.hasTab("payload")||(this.requestInternal.queryParameters||await this.requestInternal.requestFormData())&&(this.payloadView=new Oe(this.requestInternal),this.appendTab("payload",Et(Ht.payload),this.payloadView,Et(Ht.payload),void 0,void 0,void 0,1))}maybeShowErrorIconInTrustTokenTabHeader(){const e=this.requestInternal.trustTokenOperationDoneEvent();if(e&&!f.RequestTrustTokensView.statusConsideredSuccess(e.status)){const e=new m.Icon.Icon;e.data={iconName:"cross-circle-filled",color:"var(--icon-error)",width:"14px",height:"14px"},this.setTabIcon("trust-tokens",e)}}selectTabInternal(e){this.selectTab(e)||window.setTimeout((()=>{this.selectTab(e)||this.selectTab("headers-component")}),0)}tabSelected(e){e.data.isUserGesture&&this.resourceViewTabSetting.set(e.data.tabId)}request(){return this.requestInternal}async revealResponseBody(e){this.selectTabInternal("response"),await(this.responseView?.revealPosition(e))}revealHeader(e,t){this.selectTabInternal("headers-component"),this.headersViewComponent.revealHeader(e,t)}getHeadersViewComponent(){return this.headersViewComponent}}var Bt=Object.freeze({__proto__:null,NetworkItemView:At});const Ut={sLatencySDownloadSTotal:"{PH1} latency, {PH2} download ({PH3} total)",sLatency:"{PH1} latency",sDownload:"{PH1} download",sFromServiceworker:"{PH1} (from `ServiceWorker`)",sFromCache:"{PH1} (from cache)"},Nt=i.i18n.registerUIStrings("panels/network/NetworkTimeCalculator.ts",Ut),Vt=i.i18n.getLocalizedString.bind(void 0,Nt);class Ot{minimum;maximum;constructor(e,t){this.minimum=e,this.maximum=t}equals(e){return this.minimum===e.minimum&&this.maximum===e.maximum}}class Wt extends e.ObjectWrapper.ObjectWrapper{startAtZero;minimumBoundaryInternal;maximumBoundaryInternal;boundryChangedEventThrottler;window;workingArea;constructor(t){super(),this.startAtZero=t,this.minimumBoundaryInternal=-1,this.maximumBoundaryInternal=-1,this.boundryChangedEventThrottler=new e.Throttler.Throttler(0),this.window=null}setWindow(e){this.window=e,this.boundaryChanged()}setInitialUserFriendlyBoundaries(){this.minimumBoundaryInternal=0,this.maximumBoundaryInternal=1}computePosition(e){return(e-this.minimumBoundary())/this.boundarySpan()*(this.workingArea||0)}formatValue(e,t){return i.TimeUtilities.secondsToString(e,Boolean(t))}minimumBoundary(){return this.window?this.window.minimum:this.minimumBoundaryInternal}zeroTime(){return this.minimumBoundaryInternal}maximumBoundary(){return this.window?this.window.maximum:this.maximumBoundaryInternal}boundary(){return new Ot(this.minimumBoundary(),this.maximumBoundary())}boundarySpan(){return this.maximumBoundary()-this.minimumBoundary()}reset(){this.minimumBoundaryInternal=-1,this.maximumBoundaryInternal=-1,this.boundaryChanged()}value(){return 0}setDisplayWidth(e){this.workingArea=e}computeBarGraphPercentages(e){let t,i,r;return t=-1!==e.startTime?(e.startTime-this.minimumBoundary())/this.boundarySpan()*100:0,i=-1!==e.responseReceivedTime?(e.responseReceivedTime-this.minimumBoundary())/this.boundarySpan()*100:this.startAtZero?t:100,r=-1!==e.endTime?(e.endTime-this.minimumBoundary())/this.boundarySpan()*100:this.startAtZero?i:100,this.startAtZero&&(r-=t,i-=t,t=0),{start:t,middle:i,end:r}}computePercentageFromEventTime(e){return-1===e||this.startAtZero?0:(e-this.minimumBoundary())/this.boundarySpan()*100}percentageToTime(e){return e*this.boundarySpan()/100+this.minimumBoundary()}boundaryChanged(){this.boundryChangedEventThrottler.schedule((async()=>{this.dispatchEventToListeners("BoundariesChanged")}))}updateBoundariesForEventTime(e){-1===e||this.startAtZero||(void 0===this.maximumBoundaryInternal||e>this.maximumBoundaryInternal)&&(this.maximumBoundaryInternal=e,this.boundaryChanged())}computeBarGraphLabels(e){let t="";-1!==e.responseReceivedTime&&-1!==e.endTime&&(t=i.TimeUtilities.secondsToString(e.endTime-e.responseReceivedTime));const r=e.latency>0,o=r?i.TimeUtilities.secondsToString(e.latency):t;if(e.timing)return{left:o,right:t,tooltip:void 0};let s;if(r&&t){const r=i.TimeUtilities.secondsToString(e.duration);s=Vt(Ut.sLatencySDownloadSTotal,{PH1:o,PH2:t,PH3:r})}else r?s=Vt(Ut.sLatency,{PH1:o}):t&&(s=Vt(Ut.sDownload,{PH1:t}));return e.fetchedViaServiceWorker?s=Vt(Ut.sFromServiceworker,{PH1:String(s)}):e.cached()&&(s=Vt(Ut.sFromCache,{PH1:String(s)})),{left:o,right:t,tooltip:s}}updateBoundaries(e){const t=this.lowerBound(e),i=this.upperBound(e);let r=!1;(-1!==t||this.startAtZero)&&(r=this.extendBoundariesToIncludeTimestamp(this.startAtZero?0:t)),-1!==i&&(r=this.extendBoundariesToIncludeTimestamp(i)||r),r&&this.boundaryChanged()}extendBoundariesToIncludeTimestamp(e){const t=this.minimumBoundaryInternal,i=this.maximumBoundaryInternal,r=Dt;return-1===this.minimumBoundaryInternal||-1===this.maximumBoundaryInternal?(this.minimumBoundaryInternal=e,this.maximumBoundaryInternal=e+r):(this.minimumBoundaryInternal=Math.min(e,this.minimumBoundaryInternal),this.maximumBoundaryInternal=Math.max(e,this.minimumBoundaryInternal+r,this.maximumBoundaryInternal)),t!==this.minimumBoundaryInternal||i!==this.maximumBoundaryInternal}lowerBound(e){return 0}upperBound(e){return 0}}const Dt=.1;class jt extends Wt{constructor(){super(!1)}formatValue(e,t){return i.TimeUtilities.secondsToString(e-this.zeroTime(),Boolean(t))}lowerBound(e){return e.issueTime()}upperBound(e){return e.endTime}}class Gt extends Wt{constructor(){super(!0)}formatValue(e,t){return i.TimeUtilities.secondsToString(e,Boolean(t))}upperBound(e){return e.duration}}var zt=Object.freeze({__proto__:null,NetworkTimeBoundary:Ot,NetworkTimeCalculator:Wt,NetworkTransferTimeCalculator:jt,NetworkTransferDurationCalculator:Gt});class _t{parentView;activeGroups;constructor(e){this.parentView=e,this.activeGroups=new Map}groupNodeForRequest(e){const t=n.ResourceTreeModel.ResourceTreeModel.frameForRequest(e);if(!t||t.isOutermostFrame())return null;let i=this.activeGroups.get(t);return i||(i=new Kt(this.parentView,t),this.activeGroups.set(t,i),i)}reset(){this.activeGroups.clear()}}class Kt extends be{frame;constructor(e,t){super(e),this.frame=t}displayName(){return new e.ParsedURL.ParsedURL(this.frame.url).domain()||this.frame.name||"<iframe>"}renderCell(e,t){super.renderCell(e,t);if(0===this.dataGrid.indexOfVisibleColumn(t)){const i=this.displayName();e.appendChild(m.Icon.create("frame","network-frame-group-icon")),o.UIUtils.createTextChild(e,i),o.Tooltip.Tooltip.install(e,i),this.setCellAccessibleName(e.textContent||"",e,t)}}}var $t=Object.freeze({__proto__:null,NetworkFrameGrouper:_t,FrameGroupNode:Kt});const Xt=new CSSStyleSheet;Xt.replaceSync('.network-log-grid.data-grid{border:none!important;flex:auto}.network-log-grid.data-grid.no-selection:focus-visible{border:none!important}#network-container{overflow:hidden}#network-container.grid-focused.no-node-selected:focus-within{border:1px solid var(--sys-color-state-focus-ring)}.network-summary-bar{flex:0 0 27px;line-height:27px;padding-left:5px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden;user-select:text}.panel.network .toolbar.network-summary-bar{border-bottom:0}.network-summary-bar span[is="dt-icon-label"]{margin-right:6px}.network-summary-bar > *{flex:none}.network-log-grid.data-grid tbody{background:transparent}.network-log-grid.data-grid td{height:41px;border-left:1px solid var(--sys-color-divider);vertical-align:middle}.network-log-grid.data-grid .corner{display:none}.network-log-grid.data-grid.small td{height:21px}.network-log-grid.data-grid th{border-bottom:none}.network-waterfall-header,\n.network-log-grid.data-grid thead th{border-bottom:1px solid var(--sys-color-divider);border-left:1px solid var(--sys-color-divider)}.network-waterfall-header,\n.network-log-grid.data-grid thead{height:31px;background-color:var(--sys-color-surface1)}.network-waterfall-header.small,\n.network-log-grid.data-grid.small thead{height:27px}.network-log-grid.data-grid select{appearance:none;border:none;width:100%;color:inherit}.network-log-grid.data-grid .waterfall-column{padding:1px 0}.network-log-grid.data-grid .waterfall-column .sort-order-icon-container{right:15px;pointer-events:none}.network-log-grid.data-grid th.sortable:active{background-image:none!important}.network-cell-subtitle{font-weight:normal;color:var(--sys-color-token-subtle)}.network-badge{margin-right:4px}.status-column .devtools-link{color:inherit}.initiator-column .text-button.devtools-link,\n.initiator-column .text-button.devtools-link:focus-visible{color:inherit;background-color:transparent;outline-offset:0;height:16px}.network-error-row,\n.network-error-row .network-cell-subtitle{color:var(--sys-color-error)!important}.network-log-grid.data-grid tr.selected.network-error-row,\n.network-log-grid.data-grid tr.selected.network-error-row .network-cell-subtitle,\n.network-log-grid.data-grid tr.selected.network-error-row .network-dim-cell,\n.network-log-grid.data-grid:focus-within tr.selected.network-error-row .devtools-link,\n.network-log-grid.data-grid:focus-within tr.selected.network-error-row,\n.network-log-grid.data-grid:focus-within tr.selected.network-error-row .network-cell-subtitle,\n.network-log-grid.data-grid:focus-within tr.selected.network-error-row .network-dim-cell{color:var(--sys-color-error)}.network-log-grid.data-grid tr.selected,\n.network-log-grid.data-grid tr.selected .network-cell-subtitle,\n.network-log-grid.data-grid tr.selected .network-dim-cell{color:inherit}.network-log-grid.data-grid:focus tr.selected,\n.network-log-grid.data-grid:focus tr.selected .network-cell-subtitle,\n.network-log-grid.data-grid:focus tr.selected .network-dim-cell{color:var(--sys-color-on-tonal-container)}.network-header-subtitle{color:var(--sys-color-token-subtle)}.network-log-grid.data-grid.small .network-cell-subtitle,\n.network-log-grid.data-grid.small .network-header-subtitle{display:none}.network-log-grid.data-grid.small .network-cell-subtitle.always-visible{display:inline;margin-left:4px}.network-log-grid tr.highlighted-row{animation:network-row-highlight-fadeout 2s 0s}@keyframes network-row-highlight-fadeout{from{background-color:var(--sys-color-yellow-container)}to{background-color:transparent}}.network-log-grid.data-grid .icon.image{position:relative}.network-log-grid.data-grid .icon{float:left;width:32px;height:32px;margin-top:1px;margin-right:3px}.network-log-grid.data-grid:focus-within .network-error-row.selected div.icon:not(.image){filter:none}.network-log-grid.data-grid .network-error-row.data-grid-data-grid-node img.icon,\n.network-log-grid.data-grid .network-error-row.data-grid-data-grid-node.selected img.icon{filter:brightness(0) saturate(100%) invert(35%) sepia(76%) saturate(1413%) hue-rotate(338deg) brightness(92%) contrast(103%)}.data-grid-data-grid-node devtools-icon[name="arrow-up-down-circle"],\n.network-log-grid.data-grid.small .icon{width:16px;height:16px;vertical-align:sub}.image-network-icon-preview{bottom:0;left:0;margin:auto;overflow:hidden;right:0;top:0}.network-log-grid.data-grid .image-network-icon-preview{position:absolute;max-width:18px;max-height:21px;min-width:1px;min-height:1px}.network-log-grid.data-grid.small .image-network-icon-preview{left:2px;right:2px;max-width:10px;max-height:12px}.network-log-grid.data-grid .trailing-link-icon{padding-left:0.5ex}.network-dim-cell{color:var(--sys-color-token-subtle)}.network-frame-divider{width:2px;background-color:var(--network-frame-divider-color);z-index:10;visibility:hidden}#network-container.has-waterfall .data-container{overflow:hidden}.network-log-grid.data-grid .resources-dividers{z-index:0}.network-log-grid.data-grid .resources-dividers-label-bar{background-color:transparent;border:none;height:30px;pointer-events:none}.network-log-grid.data-grid span.separator-in-cell{user-select:none;min-width:1ex;display:inline-block}.network-status-pane{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);z-index:500;display:flex;justify-content:center;align-items:center;text-align:center;padding:0 20px;overflow:auto}.network-status-pane > .recording-hint{font-size:14px;text-align:center;line-height:28px}.network-waterfall-header{position:absolute;border-left:0;width:100%;display:table;z-index:200;& > div.hover-layer{display:none;background-color:var(--sys-color-state-hover-on-subtle);position:absolute;top:0;left:0;width:100%;height:100%}&:hover > div.hover-layer{display:block}}.network-waterfall-header div{display:table-cell;line-height:14px;margin:auto 0;vertical-align:middle;text-align:left;font-weight:normal;padding:0 4px}.network-waterfall-header .sort-order-icon-container{position:absolute;top:1px;right:0;bottom:1px;display:flex;align-items:center}.network-waterfall-header .sort-order-icon{align-items:center;margin-right:4px;margin-bottom:-2px}.network-frame-group-icon{display:inline-block;margin:-7px 1px;vertical-align:baseline}.network-frame-group-badge{margin-right:4px}.network-override-marker{position:relative;float:left}.network-override-marker::before{background-color:var(--sys-color-purple-bright);content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-toolbar);left:8px;position:absolute;top:10px;z-index:1}@media (forced-colors: active){.network-status-pane > .recording-hint{color:canvastext}.initiator-column .devtools-link{color:linktext}.network-log-grid.data-grid tbody tr.revealed.selected,\n  .network-log-grid.data-grid:focus-within tbody tr.revealed.selected,\n  .network-log-grid.data-grid:focus-within tr.selected .network-dim-cell,\n  .network-log-grid.data-grid tr.selected .network-dim-cell,\n  .network-log-grid.data-grid:focus-within tr.selected .initiator-column .devtools-link,\n  .network-log-grid.data-grid tr.selected .initiator-column .devtools-link,\n  .network-waterfall-header:hover *{color:HighlightText}.network-log-grid{--color-grid-default:canvas;--color-grid-stripe:canvas;--color-grid-hovered:Highlight;--color-grid-selected:ButtonText;--color-grid-focus-selected:Highlight}#network-container.no-node-selected:focus-within,\n  .network-status-pane{forced-color-adjust:none;border-color:Highlight;background-color:canvas!important}.network-waterfall-header:hover{forced-color-adjust:none;background-color:Highlight!important;& > div.hover-layer{display:none}}.network-waterfall-header.small,\n  .network-log-grid.data-grid.small thead .network-waterfall-header,\n  .network-log-grid.data-grid thead{background-color:canvas}.network-waterfall-header .sort-order-icon-container devtools-icon{background-color:inherit}.network-waterfall-header:hover .sort-order-icon-container devtools-icon{color:HighlightText}}\n/*# sourceURL=networkLogView.css */\n');const Yt=new CSSStyleSheet;Yt.replaceSync(".custom-headers-list{height:272px;width:250px}.custom-headers-wrapper{margin:10px}.header{padding:0 0 6px;font-size:18px;font-weight:normal;flex:none}.custom-headers-header{padding:2px}.custom-headers-list-item{padding-left:5px}.editor-container{padding:5px 0 0 5px}.add-button{width:150px;margin:auto;margin-top:5px}\n/*# sourceURL=networkManageCustomHeadersView.css */\n");const Jt={manageHeaderColumns:"Manage Header Columns",noCustomHeaders:"No custom headers",addCustomHeader:"Add custom header…",headerName:"Header Name"},Zt=i.i18n.registerUIStrings("panels/network/NetworkManageCustomHeadersView.ts",Jt),Qt=i.i18n.getLocalizedString.bind(void 0,Zt);class ei extends o.Widget.VBox{list;columnConfigs;addHeaderColumnCallback;changeHeaderColumnCallback;removeHeaderColumnCallback;editor;constructor(e,t,i,r){super(!0),this.contentElement.classList.add("custom-headers-wrapper"),this.contentElement.createChild("div","header").textContent=Qt(Jt.manageHeaderColumns),this.list=new o.ListWidget.ListWidget(this),this.list.element.classList.add("custom-headers-list");const s=document.createElement("div");s.classList.add("custom-headers-list-list-empty"),s.textContent=Qt(Jt.noCustomHeaders),this.list.setEmptyPlaceholder(s),this.list.show(this.contentElement),this.contentElement.appendChild(o.UIUtils.createTextButton(Qt(Jt.addCustomHeader),this.addButtonClicked.bind(this),{className:"add-button",jslogContext:"network.add-custom-header"})),this.columnConfigs=new Map,e.forEach((e=>this.columnConfigs.set(e.title.toLowerCase(),e))),this.addHeaderColumnCallback=t,this.changeHeaderColumnCallback=i,this.removeHeaderColumnCallback=r,this.contentElement.tabIndex=0}wasShown(){this.headersUpdated(),this.list.registerCSSFiles([Yt]),this.registerCSSFiles([Yt])}headersUpdated(){this.list.clear(),this.columnConfigs.forEach((e=>this.list.appendItem({header:e.title},e.editable)))}addButtonClicked(){this.list.addNewItem(this.columnConfigs.size,{header:""})}renderItem(e,t){const i=document.createElement("div");i.classList.add("custom-headers-list-item");const r=i.createChild("div","custom-header-name");return r.textContent=e.header,o.Tooltip.Tooltip.install(r,e.header),i}removeItemRequested(e,t){this.removeHeaderColumnCallback(e.header),this.columnConfigs.delete(e.header.toLowerCase()),this.headersUpdated()}commitEdit(e,t,i){const r=t.control("header").value.trim();let o;o=i?this.addHeaderColumnCallback(r):this.changeHeaderColumnCallback(e.header,r),o&&!i&&this.columnConfigs.delete(e.header.toLowerCase()),o&&this.columnConfigs.set(r.toLowerCase(),{title:r,editable:!0}),this.headersUpdated()}beginEdit(e){const t=this.createEditor();return t.control("header").value=e.header,t}createEditor(){if(this.editor)return this.editor;const e=new o.ListWidget.Editor;this.editor=e;const t=e.contentElement();t.createChild("div","custom-headers-edit-row").createChild("div","custom-headers-header").textContent=Qt(Jt.headerName);return t.createChild("div","custom-headers-edit-row").createChild("div","custom-headers-header").appendChild(e.createInput("header","text","x-custom-header",function(t,i,r){let o=!0;const s=e.control("header").value.trim().toLowerCase();this.columnConfigs.has(s)&&t.header!==s&&(o=!1);return{valid:o,errorMessage:void 0}}.bind(this))),e}}var ti=Object.freeze({__proto__:null,NetworkManageCustomHeadersView:ei});const ii=new CSSStyleSheet;ii.replaceSync(".network-waterfall-v-scroll{position:absolute;top:0;right:0;bottom:0;overflow-x:hidden;margin-top:31px;z-index:200}.theme-with-dark-background .network-waterfall-v-scroll{background:rgb(0 0 0/1%)}.network-waterfall-v-scroll.small{margin-top:27px}.network-waterfall-v-scroll-content{width:15px;pointer-events:none}\n/*# sourceURL=networkWaterfallColumn.css */\n");const ri=L.RenderCoordinator.RenderCoordinator.instance();class oi extends w.TimelineOverviewPane.TimelineOverviewBase{selectedFilmStripTime;numBands;highlightedRequest;loadEvents;domContentLoadedEvents;nextBand;bandMap;requestsList;requestsSet;span;lastBoundary;constructor(){super(),this.selectedFilmStripTime=-1,this.element.classList.add("network-overview"),this.numBands=1,this.highlightedRequest=null,n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.Load,this.loadEventFired,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this,{scoped:!0}),this.reset()}setHighlightedRequest(e){this.highlightedRequest=e,this.scheduleUpdate()}selectFilmStripFrame(e){this.selectedFilmStripTime=e,this.scheduleUpdate()}clearFilmStripFrame(){this.selectedFilmStripTime=-1,this.scheduleUpdate()}loadEventFired(e){const t=e.data.loadTime;t&&this.loadEvents.push(1e3*t),this.scheduleUpdate()}domContentLoadedEventFired(e){const{data:t}=e;t&&this.domContentLoadedEvents.push(1e3*t),this.scheduleUpdate()}bandId(e){if(!e||"0"===e)return-1;if(this.bandMap.has(e))return this.bandMap.get(e);const t=this.nextBand++;return this.bandMap.set(e,t),t}updateRequest(e){this.requestsSet.has(e)||(this.requestsSet.add(e),this.requestsList.push(e)),this.scheduleUpdate()}wasShown(){this.onResize()}calculator(){return super.calculator()}onResize(){const e=this.element.offsetWidth,t=this.element.offsetHeight;this.calculator().setDisplayWidth(e),this.resetCanvas();const i=(t-ai-1)/ni-1|0;this.numBands=i>0?i:1,this.scheduleUpdate()}reset(){this.span=1,this.lastBoundary=null,this.nextBand=0,this.bandMap=new Map,this.requestsList=[],this.requestsSet=new Set,this.loadEvents=[],this.domContentLoadedEvents=[],this.resetCanvas()}scheduleUpdate(){this.isShowing()&&ri.write("NetworkOverview.render",this.update.bind(this))}update(){const e=this.calculator(),t=new Ot(e.minimumBoundary(),e.maximumBoundary());if(!this.lastBoundary||!t.equals(this.lastBoundary)){const t=e.boundarySpan();for(;this.span<t;)this.span*=1.25;e.setBounds(e.minimumBoundary(),P.Types.Timing.MilliSeconds(e.minimumBoundary()+this.span)),this.lastBoundary=new Ot(e.minimumBoundary(),e.maximumBoundary())}const i=this.context(),r=new Map,o=ai;function s(t){const s=r.get(t);if(!s)return;const n=s.length;i.beginPath(),i.strokeStyle=F.ThemeSupport.instance().getComputedValue("--color-background-opacity-80"),i.lineWidth=di,i.fillStyle=F.ThemeSupport.instance().getComputedValue(si[t]);for(let t=0;t<n;){const r=s[t++]*ni+o,n=s[t++];let a=s[t++];a===Number.MAX_VALUE&&(a=e.maximumBoundary());const l=e.computePosition(P.Types.Timing.MilliSeconds(n)),d=e.computePosition(P.Types.Timing.MilliSeconds(a))+1;i.fillRect(l,r,Math.max(d-l,li),ni),i.strokeRect(l,r,Math.max(d-l,li),ni)}}function n(e,t,i,o){let s=r.get(e);s||(s=[],r.set(e,s)),s.push(t,i,o)}const a=this.requestsList,l=a.length;for(let e=0;e<l;++e){const t=a[e],i=this.bandId(t.connectionId),r=-1===i?0:i%this.numBands+1,o=wt.calculateRequestTimeRanges(t,this.calculator().minimumBoundary());for(let e=0;e<o.length;++e){const t=o[e].name;-1===i&&"total"!==t||n(t,r,1e3*o[e].start,1e3*o[e].end)}}if(i.clearRect(0,0,this.width(),this.height()),i.save(),i.scale(window.devicePixelRatio,window.devicePixelRatio),i.lineWidth=2,s("total"),s("blocking"),s("connecting"),s("serviceworker"),s("serviceworker-preparation"),s("serviceworker-respondwith"),s("push"),s("proxy"),s("dns"),s("ssl"),s("sending"),s("waiting"),s("receiving"),this.highlightedRequest){const t=5,r=2,s=this.highlightedRequest,n=this.bandId(s.connectionId),a=(-1===n?0:n%this.numBands+1)*ni+o,l=wt.calculateRequestTimeRanges(s,this.calculator().minimumBoundary());i.fillStyle=F.ThemeSupport.instance().getComputedValue("--sys-color-tonal-container");const d=P.Types.Timing.MilliSeconds(1e3*l[0].start),c=P.Types.Timing.MilliSeconds(1e3*l[0].end);i.fillRect(e.computePosition(d)-r,a-t/2-r,e.computePosition(c)-e.computePosition(d)+1+2*r,t*r);for(let r=0;r<l.length;++r){const o=l[r].name;if(-1!==n||"total"===o){i.beginPath(),i.strokeStyle=F.ThemeSupport.instance().getComputedValue(si[o]),i.lineWidth=t;const s=P.Types.Timing.MilliSeconds(1e3*l[r].start),n=P.Types.Timing.MilliSeconds(1e3*l[r].end);i.moveTo(e.computePosition(s)-0,a),i.lineTo(e.computePosition(n)+1,a),i.stroke()}}}const d=this.element.offsetHeight;i.lineWidth=1,i.beginPath(),i.strokeStyle=F.ThemeSupport.instance().getComputedValue(Li.getDCLEventColor());for(let t=this.domContentLoadedEvents.length-1;t>=0;--t){const r=e.computePosition(P.Types.Timing.MilliSeconds(this.domContentLoadedEvents[t])),o=Math.round(r)+.5;i.moveTo(o,0),i.lineTo(o,d)}i.stroke(),i.beginPath(),i.strokeStyle=F.ThemeSupport.instance().getComputedValue(Li.getLoadEventColor());for(let t=this.loadEvents.length-1;t>=0;--t){const r=e.computePosition(P.Types.Timing.MilliSeconds(this.loadEvents[t])),o=Math.round(r)+.5;i.moveTo(o,0),i.lineTo(o,d)}if(i.stroke(),-1!==this.selectedFilmStripTime){i.lineWidth=2,i.beginPath(),i.strokeStyle=F.ThemeSupport.instance().getComputedValue("--network-frame-divider-color");const t=P.Types.Timing.MilliSeconds(this.selectedFilmStripTime),r=Math.round(e.computePosition(t));i.moveTo(r,0),i.lineTo(r,d),i.stroke()}i.restore()}}const si={total:"--network-overview-total",blocking:"--network-overview-blocking",connecting:"--network-overview-connecting",serviceworker:"--network-overview-service-worker","serviceworker-preparation":"--network-overview-service-worker","serviceworker-respondwith":"--network-overview-service-worker-respond-with",push:"--network-overview-push",proxy:"--override-network-overview-proxy",dns:"--network-overview-dns",ssl:"--network-overview-ssl",sending:"--override-network-overview-sending",waiting:"--network-overview-waiting",receiving:"--network-overview-receiving",queueing:"--network-overview-queueing"},ni=3,ai=5,li=10,di=1;var ci=Object.freeze({__proto__:null,NetworkOverview:oi,RequestTimeRangeNameToColor:si});const hi=L.RenderCoordinator.RenderCoordinator.instance();class ui extends o.Widget.VBox{canvas;canvasPosition;leftPadding;fontSize;rightPadding;scrollTop;headerHeight;calculator;rawRowHeight;rowHeight;offsetWidth;offsetHeight;startTime;endTime;popoverHelper;nodes;hoveredNode;eventDividers;styleForTimeRangeName;styleForWaitingResourceType;styleForDownloadingResourceType;wiskerStyle;hoverDetailsStyle;pathForStyle;textLayers;constructor(e){super(!1),this.canvas=this.contentElement.createChild("canvas"),this.canvas.tabIndex=-1,this.setDefaultFocusedElement(this.canvas),this.canvasPosition=this.canvas.getBoundingClientRect(),this.leftPadding=5,this.fontSize=10,this.rightPadding=0,this.scrollTop=0,this.headerHeight=0,this.calculator=e,this.rawRowHeight=0,this.rowHeight=0,this.offsetWidth=0,this.offsetHeight=0,this.startTime=this.calculator.minimumBoundary(),this.endTime=this.calculator.maximumBoundary(),this.popoverHelper=new o.PopoverHelper.PopoverHelper(this.element,this.getPopoverRequest.bind(this),"network.timing"),this.popoverHelper.setHasPadding(!0),this.popoverHelper.setTimeout(300,300),this.nodes=[],this.hoveredNode=null,this.eventDividers=new Map,this.element.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.element.addEventListener("mouseleave",(e=>this.setHoveredNode(null,!1)),!0),this.element.addEventListener("click",this.onClick.bind(this),!0),this.styleForTimeRangeName=ui.buildRequestTimeRangeStyle();const t=ui.buildResourceTypeStyle();this.styleForWaitingResourceType=t[0],this.styleForDownloadingResourceType=t[1];const i=F.ThemeSupport.instance().getComputedValue("--sys-color-state-disabled");this.wiskerStyle={borderColor:i,lineWidth:1,fillStyle:void 0},this.hoverDetailsStyle={fillStyle:i,lineWidth:1,borderColor:i},this.pathForStyle=new Map,this.textLayers=[]}static buildRequestTimeRangeStyle(){const e=new Map;return e.set("connecting",{fillStyle:si.connecting}),e.set("ssl",{fillStyle:si.ssl}),e.set("dns",{fillStyle:si.dns}),e.set("proxy",{fillStyle:si.proxy}),e.set("blocking",{fillStyle:si.blocking}),e.set("push",{fillStyle:si.push}),e.set("queueing",{fillStyle:si.queueing,lineWidth:2,borderColor:"lightgrey"}),e.set("receiving",{fillStyle:si.receiving,lineWidth:2,borderColor:"#03A9F4"}),e.set("waiting",{fillStyle:si.waiting}),e.set("receiving-push",{fillStyle:si["receiving-push"]}),e.set("serviceworker",{fillStyle:si.serviceworker}),e.set("serviceworker-preparation",{fillStyle:si["serviceworker-preparation"]}),e.set("serviceworker-respondwith",{fillStyle:si["serviceworker-respondwith"]}),e}static buildResourceTypeStyle(){const t=new Map([["document","hsl(215, 100%, 80%)"],["font","hsl(8, 100%, 80%)"],["media","hsl(90, 50%, 80%)"],["image","hsl(90, 50%, 80%)"],["script","hsl(31, 100%, 80%)"],["stylesheet","hsl(272, 64%, 80%)"],["texttrack","hsl(8, 100%, 80%)"],["websocket","hsl(0, 0%, 95%)"],["xhr","hsl(53, 100%, 80%)"],["fetch","hsl(53, 100%, 80%)"],["other","hsl(0, 0%, 95%)"]]),i=new Map,r=new Map;for(const n of Object.values(e.ResourceType.resourceTypes)){let e=t.get(n.name());e||(e=t.get("other"));const a=o(e);i.set(n,{fillStyle:s(e),lineWidth:1,borderColor:a}),r.set(n,{fillStyle:e,lineWidth:1,borderColor:a})}return[i,r];function o(t){const i=e.Color.parse(t)?.as("hsl");if(!i)return"";let{s:r,l:o}=i;return r/=2,o-=Math.min(o,.2),new e.Color.HSL(i.h,r,o,i.alpha).asString()}function s(t){const i=e.Color.parse(t)?.as("hsl");if(!i)return"";let{l:r}=i;return r*=1.1,new e.Color.HSL(i.h,i.s,r,i.alpha).asString()}}resetPaths(){this.pathForStyle.clear(),this.pathForStyle.set(this.wiskerStyle,new Path2D),this.styleForTimeRangeName.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.styleForWaitingResourceType.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.styleForDownloadingResourceType.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.pathForStyle.set(this.hoverDetailsStyle,new Path2D)}willHide(){this.popoverHelper.hidePopover()}wasShown(){this.update(),this.registerCSSFiles([ii])}onMouseMove(e){this.setHoveredNode(this.getNodeFromPoint(e.offsetX,e.offsetY),e.shiftKey)}onClick(e){this.setSelectedNode(this.getNodeFromPoint(e.offsetX,e.offsetY))&&e.consume(!0)}getPopoverRequest(t){if(!this.hoveredNode)return null;const i=this.hoveredNode.request();if(!i)return null;let r,o,s;if(!e.Settings.Settings.instance().moduleSetting("network-color-code-resource-types").get()&&!this.calculator.startAtZero?(r=wt.calculateRequestTimeRanges(i,0).find((e=>"total"===e.name)),o=this.timeToPosition(r.start),s=this.timeToPosition(r.end)):(r=this.getSimplifiedBarRange(i,0),o=r.start,s=r.end),s-o<50){const e=(s-o)/2;o=o+e-25,s=s-e+25}if(t.clientX<this.canvasPosition.left+o||t.clientX>this.canvasPosition.left+s)return null;const n=this.nodes.findIndex((e=>e.hovered())),a=this.getBarHeight(r.name),l=this.headerHeight+(this.rowHeight*n-this.scrollTop)+(this.rowHeight-a)/2;if(t.clientY<this.canvasPosition.top+l||t.clientY>this.canvasPosition.top+l+a)return null;const d=this.element.boxInWindow();return d.x+=o,d.y+=l,d.width=s-o,d.height=a,{box:d,show:e=>{const t=wt.createTimingTable(i,this.calculator);return e.registerCSSFiles([ut]),e.contentElement.appendChild(t),Promise.resolve(!0)},hide:void 0}}setHoveredNode(e,t){this.hoveredNode&&this.hoveredNode.setHovered(!1,!1),this.hoveredNode=e,this.hoveredNode&&this.hoveredNode.setHovered(!0,t)}setSelectedNode(e){return!(!e||!e.dataGrid)&&(e.select(),e.dataGrid.element.focus(),!0)}setRowHeight(e){this.rawRowHeight=e,this.updateRowHeight()}updateRowHeight(){this.rowHeight=Math.round(this.rawRowHeight*window.devicePixelRatio)/window.devicePixelRatio}setHeaderHeight(e){this.headerHeight=e}setRightPadding(e){this.rightPadding=e,this.calculateCanvasSize()}setCalculator(e){this.calculator=e}getNodeFromPoint(e,t){return t<=this.headerHeight?null:this.nodes[Math.floor((this.scrollTop+t-this.headerHeight)/this.rowHeight)]}scheduleDraw(){hi.write("NetworkWaterfallColumn.render",(()=>this.update()))}update(e,t,i){void 0!==e&&this.scrollTop!==e&&(this.popoverHelper.hidePopover(),this.scrollTop=e),i&&(this.nodes=i,this.calculateCanvasSize()),void 0!==t&&(this.eventDividers=t),this.startTime=this.calculator.minimumBoundary(),this.endTime=this.calculator.maximumBoundary(),this.resetCanvas(),this.resetPaths(),this.textLayers=[],this.draw()}resetCanvas(){const e=window.devicePixelRatio;this.canvas.width=this.offsetWidth*e,this.canvas.height=this.offsetHeight*e,this.canvas.style.width=this.offsetWidth+"px",this.canvas.style.height=this.offsetHeight+"px"}onResize(){super.onResize(),this.updateRowHeight(),this.calculateCanvasSize(),this.scheduleDraw()}calculateCanvasSize(){this.offsetWidth=this.contentElement.offsetWidth-this.rightPadding,this.offsetHeight=this.contentElement.offsetHeight,this.calculator.setDisplayWidth(this.offsetWidth),this.canvasPosition=this.canvas.getBoundingClientRect()}timeToPosition(e){const t=(this.offsetWidth-this.leftPadding)/(this.endTime-this.startTime);return Math.floor(this.leftPadding+(e-this.startTime)*t)}didDrawForTest(){}draw(){const t=!e.Settings.Settings.instance().moduleSetting("network-color-code-resource-types").get()&&!this.calculator.startAtZero,i=this.nodes,r=this.canvas.getContext("2d");if(!r)return;r.save(),r.scale(window.devicePixelRatio,window.devicePixelRatio),r.translate(0,this.headerHeight),r.rect(0,0,this.offsetWidth,this.offsetHeight),r.clip();const o=Math.floor(this.scrollTop/this.rowHeight),s=Math.min(i.length,o+Math.ceil(this.offsetHeight/this.rowHeight));for(let e=o;e<s;e++){const o=this.rowHeight*e,s=i[e];this.decorateRow(r,s,o-this.scrollTop);let n=[];s.hasChildren()&&!s.expanded&&(n=s.flatChildren()),n.push(s);for(const e of n)t?this.buildTimingBarLayers(e,o-this.scrollTop):this.buildSimplifiedBarLayers(r,e,o-this.scrollTop)}this.drawLayers(r,t),r.save(),r.fillStyle=F.ThemeSupport.instance().getComputedValue("--sys-color-state-disabled");for(const e of this.textLayers)r.fillText(e.text,e.x,e.y);r.restore(),this.drawEventDividers(r),r.restore();const n=w.TimelineGrid.TimelineGrid.calculateGridOffsets(this.calculator);w.TimelineGrid.TimelineGrid.drawCanvasGrid(r,n),w.TimelineGrid.TimelineGrid.drawCanvasHeaders(r,n,(e=>this.calculator.formatValue(e,n.precision)),this.fontSize,this.headerHeight,75),r.save(),r.scale(window.devicePixelRatio,window.devicePixelRatio),r.clearRect(this.offsetWidth-18,0,18,this.headerHeight),r.restore(),this.didDrawForTest()}drawLayers(e,t){for(const i of this.pathForStyle){const r=i[0],o=i[1];e.save(),e.beginPath(),r.lineWidth&&(e.lineWidth=r.lineWidth,r.borderColor&&(e.strokeStyle=r.borderColor),e.stroke(o)),r.fillStyle&&(e.fillStyle=t?F.ThemeSupport.instance().getComputedValue(r.fillStyle):r.fillStyle,e.fill(o)),e.restore()}}drawEventDividers(e){e.save(),e.lineWidth=1;for(const t of this.eventDividers.keys()){e.strokeStyle=t;for(const i of this.eventDividers.get(t)||[]){e.beginPath();const t=this.timeToPosition(i);e.moveTo(t,0),e.lineTo(t,this.offsetHeight)}e.stroke()}e.restore()}getBarHeight(e){switch(e){case"connecting":case"ssl":case"dns":case"proxy":case"blocking":case"push":case"queueing":return 7;default:return 13}}getSimplifiedBarRange(e,t){const i=this.offsetWidth-this.leftPadding,r=this.calculator.computeBarGraphPercentages(e);return{start:this.leftPadding+Math.floor(r.start/100*i)+t,mid:this.leftPadding+Math.floor(r.middle/100*i)+t,end:this.leftPadding+Math.floor(r.end/100*i)+t}}buildSimplifiedBarLayers(e,t,i){const r=t.request();if(!r)return;const o=.5,s=this.getSimplifiedBarRange(r,o),n=this.getBarHeight();i+=Math.floor(this.rowHeight/2-n/2+1)-.5;const a=this.styleForWaitingResourceType.get(r.resourceType());this.pathForStyle.get(a).rect(s.start,i,s.mid-s.start,n-1);const l=Math.max(2,s.end-s.mid),d=this.styleForDownloadingResourceType.get(r.resourceType());this.pathForStyle.get(d).rect(s.mid,i,l,n-1);let c=null;if(t.hovered()){c=this.calculator.computeBarGraphLabels(r);const t=10,a=e.measureText(c.left).width,d=e.measureText(c.right).width,h=this.pathForStyle.get(this.hoverDetailsStyle);if(a<s.mid-s.start){const e=s.start+(s.mid-s.start-a)/2;this.textLayers.push({text:c.left,x:e,y:i+this.fontSize})}else t+a+this.leftPadding<s.start&&(this.textLayers.push({text:c.left,x:s.start-a-t-1,y:i+this.fontSize}),h.moveTo(s.start-t,i+Math.floor(n/2)),h.arc(s.start,i+Math.floor(n/2),2,0,2*Math.PI),h.moveTo(s.start-t,i+Math.floor(n/2)),h.lineTo(s.start,i+Math.floor(n/2)));const u=s.mid+l+o;if(d<u-s.mid){const e=s.mid+(u-s.mid-d)/2;this.textLayers.push({text:c.right,x:e,y:i+this.fontSize})}else u+t+d<this.offsetWidth-this.leftPadding&&(this.textLayers.push({text:c.right,x:u+t+1,y:i+this.fontSize}),h.moveTo(u,i+Math.floor(n/2)),h.arc(u,i+Math.floor(n/2),2,0,2*Math.PI),h.moveTo(u,i+Math.floor(n/2)),h.lineTo(u+t,i+Math.floor(n/2)))}if(!this.calculator.startAtZero){const t=wt.calculateRequestTimeRanges(r,0).find((e=>"total"===e.name)),a=c?e.measureText(c.left).width:0,l=a<s.mid-s.start,d=c&&!l?a+13:0,h=this.timeToPosition(t.start);if(s.start-d>h){const e=this.pathForStyle.get(this.wiskerStyle);e.moveTo(h,i+Math.floor(n/2)),e.lineTo(s.start-d,i+Math.floor(n/2));const t=n/2;e.moveTo(h+o,i+t/2),e.lineTo(h+o,i+n-t/2-1)}}}buildTimingBarLayers(e,t){const i=e.request();if(!i)return;const r=wt.calculateRequestTimeRanges(i,0);let o=0;for(const e of r){if("total"===e.name||"sending"===e.name||e.end-e.start==0)continue;const i=this.styleForTimeRangeName.get(e.name),r=this.pathForStyle.get(i),s=i.lineWidth||0,n=this.getBarHeight(e.name),a=t+Math.floor(this.rowHeight/2-n/2)+s/2,l=this.timeToPosition(e.start),d=this.timeToPosition(e.end);r.rect(l+1*o,a,d-l,n-s),o++}}decorateRow(e,t,i){const r=t.backgroundColor();e.save(),e.beginPath(),e.fillStyle=F.ThemeSupport.instance().getComputedValue(r),e.rect(0,i,this.offsetWidth,this.rowHeight),e.fill(),e.restore()}}var pi=Object.freeze({__proto__:null,NetworkWaterfallColumn:ui});const gi={networkLog:"Network Log",waterfall:"Waterfall",responseHeaders:"Response Headers",manageHeaderColumns:"Manage Header Columns…",startTime:"Start Time",responseTime:"Response Time",endTime:"End Time",totalDuration:"Total Duration",latency:"Latency",name:"Name",path:"Path",url:"Url",method:"Method",status:"Status",text:"Text",protocol:"Protocol",scheme:"Scheme",domain:"Domain",remoteAddress:"Remote Address",type:"Type",initiator:"Initiator",hasOverrides:"Has overrides",initiatorAddressSpace:"Initiator Address Space",cookies:"Cookies",setCookies:"Set Cookies",size:"Size",content:"Content",time:"Time",priority:"Priority",connectionId:"Connection ID",remoteAddressSpace:"Remote Address Space"},mi=i.i18n.registerUIStrings("panels/network/NetworkLogViewColumns.ts",gi),wi=i.i18n.getLocalizedString.bind(void 0,mi),ki=i.i18n.getLazilyComputedLocalizedString.bind(void 0,mi);class bi{networkLogView;persistentSettings;networkLogLargeRowsSetting;eventDividers;eventDividersShown;gridMode;columns;waterfallRequestsAreStale;waterfallScrollerWidthIsStale;popupLinkifier;calculatorsMap;lastWheelTime;dataGridInternal;splitWidget;waterfallColumn;activeScroller;dataGridScroller;waterfallScroller;waterfallScrollerContent;waterfallHeaderElement;waterfallColumnSortIcon;activeWaterfallSortId;popoverHelper;hasScrollerTouchStarted;scrollerTouchStartPos;constructor(t,i,r,o){this.networkLogView=t,this.persistentSettings=e.Settings.Settings.instance().createSetting("network-log-columns",{}),this.networkLogLargeRowsSetting=o,this.networkLogLargeRowsSetting.addChangeListener(this.updateRowsSize,this),this.eventDividers=new Map,this.eventDividersShown=!1,this.gridMode=!0,this.columns=[],this.waterfallRequestsAreStale=!1,this.waterfallScrollerWidthIsStale=!0,this.popupLinkifier=new k.Linkifier.Linkifier,this.calculatorsMap=new Map,this.calculatorsMap.set("Time",i),this.calculatorsMap.set("Duration",r),this.lastWheelTime=0,this.setupDataGrid(),this.setupWaterfall(),F.ThemeSupport.instance().addEventListener(F.ThemeChangeEvent.eventName,(()=>{this.scheduleRefresh()}))}static convertToDataGridDescriptor(e){const t=e.title instanceof Function?e.title():e.title;return{id:e.id,title:t,sortable:e.sortable,align:e.align,nonSelectable:e.nonSelectable,weight:e.weight,allowInSortByEvenWhenHidden:e.allowInSortByEvenWhenHidden}}wasShown(){this.updateRowsSize()}willHide(){this.popoverHelper&&this.popoverHelper.hidePopover()}reset(){this.popoverHelper&&this.popoverHelper.hidePopover(),this.eventDividers.clear()}setupDataGrid(){const e=Ci,t=fi;this.columns=[];for(const i of e){const e=Object.assign({},t,i);if(e.id=e.id,e.subtitle){const t=e.title instanceof Function?e.title():e.title,i=e.subtitle instanceof Function?e.subtitle():e.subtitle;e.titleDOMFragment=this.makeHeaderFragment(t,i)}this.columns.push(e)}this.loadCustomColumnsAndSettings(),this.popoverHelper=new o.PopoverHelper.PopoverHelper(this.networkLogView.element,this.getPopoverRequest.bind(this),"network.initiator-stacktrace"),this.popoverHelper.setHasPadding(!0),this.popoverHelper.setTimeout(300,300),this.dataGridInternal=new d.SortableDataGrid.SortableDataGrid({displayName:wi(gi.networkLog),columns:this.columns.map(bi.convertToDataGridDescriptor),editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGridInternal.element.addEventListener("mousedown",(e=>{!this.dataGridInternal.selectedNode&&e.button&&e.consume()}),!0),this.dataGridScroller=this.dataGridInternal.scrollContainer,this.updateColumns(),this.dataGridInternal.addEventListener("SortingChanged",this.sortHandler,this),this.dataGridInternal.setHeaderContextMenuCallback(this.innerHeaderContextMenu.bind(this)),this.activeWaterfallSortId=yi.StartTime,this.dataGridInternal.markColumnAsSortedBy(vi,d.DataGrid.Order.Ascending),this.splitWidget=new o.SplitWidget.SplitWidget(!0,!0,"network-panel-split-view-waterfall",200);const i=this.dataGridInternal.asWidget();i.setMinimumSize(150,0),this.splitWidget.setMainWidget(i)}setupWaterfall(){this.waterfallColumn=new ui(this.networkLogView.calculator()),this.waterfallColumn.element.addEventListener("contextmenu",function(e){const t=e,i=this.waterfallColumn.getNodeFromPoint(t.offsetX,t.offsetY);if(!i)return;const r=i.request();if(!r)return;const s=new o.ContextMenu.ContextMenu(t);this.networkLogView.handleContextMenuForRequest(s,r),s.show()}.bind(this)),this.waterfallColumn.element.addEventListener("wheel",this.onMouseWheel.bind(this,!1),{passive:!0}),this.waterfallColumn.element.addEventListener("touchstart",this.onTouchStart.bind(this)),this.waterfallColumn.element.addEventListener("touchmove",this.onTouchMove.bind(this)),this.waterfallColumn.element.addEventListener("touchend",this.onTouchEnd.bind(this)),this.dataGridScroller.addEventListener("wheel",this.onMouseWheel.bind(this,!0),!0),this.dataGridScroller.addEventListener("touchstart",this.onTouchStart.bind(this)),this.dataGridScroller.addEventListener("touchmove",this.onTouchMove.bind(this)),this.dataGridScroller.addEventListener("touchend",this.onTouchEnd.bind(this)),this.waterfallScroller=this.waterfallColumn.contentElement.createChild("div","network-waterfall-v-scroll"),this.waterfallScrollerContent=this.waterfallScroller.createChild("div","network-waterfall-v-scroll-content"),this.dataGridInternal.addEventListener("PaddingChanged",(()=>{this.waterfallScrollerWidthIsStale=!0,this.syncScrollers()})),this.dataGridInternal.addEventListener("ViewportCalculated",this.redrawWaterfallColumn.bind(this)),this.createWaterfallHeader(),this.waterfallColumn.contentElement.classList.add("network-waterfall-view"),this.waterfallColumn.setMinimumSize(100,0),this.splitWidget.setSidebarWidget(this.waterfallColumn),this.switchViewMode(!1)}onMouseWheel(e,t){e&&t.consume(!0);const i=t,r=Date.now()-this.lastWheelTime<80;this.activeScroller.scrollBy({top:i.deltaY,behavior:r?"auto":"smooth"}),this.syncScrollers(),this.lastWheelTime=Date.now()}onTouchStart(e){const t=e;this.hasScrollerTouchStarted=!0,this.scrollerTouchStartPos=t.changedTouches[0].pageY}onTouchMove(e){if(!this.hasScrollerTouchStarted)return;const t=e.changedTouches[0].pageY,i=this.scrollerTouchStartPos-t;this.activeScroller.scrollBy({top:i,behavior:"auto"}),this.syncScrollers(),this.scrollerTouchStartPos=t}onTouchEnd(){this.hasScrollerTouchStarted=!1}syncScrollers(){this.waterfallColumn.isShowing()&&(this.waterfallScrollerContent.style.height=this.dataGridScroller.scrollHeight-this.dataGridInternal.headerHeight()+"px",this.updateScrollerWidthIfNeeded(),this.dataGridScroller.scrollTop=this.waterfallScroller.scrollTop)}updateScrollerWidthIfNeeded(){this.waterfallScrollerWidthIsStale&&(this.waterfallScrollerWidthIsStale=!1,this.waterfallColumn.setRightPadding(this.waterfallScroller.offsetWidth-this.waterfallScrollerContent.offsetWidth))}redrawWaterfallColumn(){if(!this.waterfallRequestsAreStale)return this.updateScrollerWidthIfNeeded(),void this.waterfallColumn.update(this.activeScroller.scrollTop,this.eventDividersShown?this.eventDividers:void 0);this.syncScrollers();const e=this.networkLogView.flatNodesList();this.waterfallColumn.update(this.activeScroller.scrollTop,this.eventDividers,e)}createWaterfallHeader(){this.waterfallHeaderElement=this.waterfallColumn.contentElement.createChild("div","network-waterfall-header"),this.waterfallHeaderElement.setAttribute("jslog",`${l.tableHeader("waterfall").track({click:!0})}`),this.waterfallHeaderElement.addEventListener("click",function(){const e=d.DataGrid.Order,t="waterfall"===this.dataGridInternal.sortColumnId(),i=this.dataGridInternal.isSortOrderAscending(),r=t&&i?e.Descending:e.Ascending;this.dataGridInternal.markColumnAsSortedBy("waterfall",r),this.sortHandler()}.bind(this)),this.waterfallHeaderElement.addEventListener("contextmenu",(e=>{const t=new o.ContextMenu.ContextMenu(e);this.innerHeaderContextMenu(t),t.show()})),this.waterfallHeaderElement.createChild("div","hover-layer");this.waterfallHeaderElement.createChild("div").textContent=wi(gi.waterfall),this.waterfallColumnSortIcon=new m.Icon.Icon,this.waterfallColumnSortIcon.className="sort-order-icon",this.waterfallHeaderElement.createChild("div","sort-order-icon-container").appendChild(this.waterfallColumnSortIcon)}setCalculator(e){this.waterfallColumn.setCalculator(e)}scheduleRefresh(){this.waterfallColumn.scheduleDraw()}updateRowsSize(){const e=Boolean(this.networkLogLargeRowsSetting.get());this.dataGridInternal.element.classList.toggle("small",!e),this.dataGridInternal.scheduleUpdate(),this.waterfallScrollerWidthIsStale=!0,this.waterfallColumn.setRowHeight(e?41:21),this.waterfallScroller.classList.toggle("small",!e),this.waterfallHeaderElement.classList.toggle("small",!e),window.requestAnimationFrame((()=>{this.waterfallColumn.setHeaderHeight(this.waterfallScroller.offsetTop),this.waterfallColumn.scheduleDraw()}))}show(e){this.splitWidget.show(e)}setHidden(e){o.ARIAUtils.setHidden(this.splitWidget.element,e)}dataGrid(){return this.dataGridInternal}sortByCurrentColumn(){this.sortHandler()}sortHandler(){const e=this.dataGridInternal.sortColumnId();if(this.networkLogView.removeAllNodeHighlights(),this.waterfallRequestsAreStale=!0,"waterfall"===e){this.dataGridInternal.sortOrder()===d.DataGrid.Order.Ascending?this.waterfallColumnSortIcon.name="triangle-up":this.waterfallColumnSortIcon.name="triangle-down",this.waterfallColumnSortIcon.hidden=!1;const e=ke.RequestPropertyComparator.bind(null,this.activeWaterfallSortId);return this.dataGridInternal.sortNodes(e,!this.dataGridInternal.isSortOrderAscending()),void this.dataGridSortedForTest()}this.waterfallColumnSortIcon.hidden=!0,this.waterfallColumnSortIcon.name=null;const t=this.columns.find((t=>t.id===e));if(!t||!t.sortingFunction)return;const i=t.sortingFunction;i&&(this.dataGridInternal.sortNodes(i,!this.dataGridInternal.isSortOrderAscending()),this.dataGridSortedForTest())}dataGridSortedForTest(){}updateColumns(){if(!this.dataGridInternal)return;const e=new Set;if(this.gridMode)for(const t of this.columns)"waterfall"===t.id?this.setWaterfallVisibility(t.visible):t.visible&&e.add(t.id);else{const t=this.columns.find((e=>"path"===e.hideableGroup&&e.visible));t?e.add(t.id):e.add("name"),this.setWaterfallVisibility(!1)}this.dataGridInternal.setColumnsVisibility(e)}switchViewMode(e){this.gridMode!==e&&(this.gridMode=e,this.updateColumns(),this.updateRowsSize())}toggleColumnVisibility(e){this.loadCustomColumnsAndSettings(),e.visible=!e.visible,this.saveColumnsSettings(),this.updateColumns(),this.updateRowsSize()}setWaterfallVisibility(e){this.splitWidget&&(this.networkLogView.element.classList.toggle("has-waterfall",e),e?(this.splitWidget.showBoth(),this.activeScroller=this.waterfallScroller,this.waterfallScroller.scrollTop=this.dataGridScroller.scrollTop,this.dataGridInternal.setScrollContainer(this.waterfallScroller)):(this.networkLogView.removeAllNodeHighlights(),this.splitWidget.hideSidebar(),this.activeScroller=this.dataGridScroller,this.dataGridInternal.setScrollContainer(this.dataGridScroller)))}saveColumnsSettings(){const e={};for(const t of this.columns)e[t.id]={visible:t.visible,title:t.title};this.persistentSettings.set(e)}loadCustomColumnsAndSettings(){const e=this.persistentSettings.get(),t=Object.keys(e);for(const i of t){const t=e[i];let r=this.columns.find((e=>e.id===i));!r&&t.title&&(r=this.addCustomHeader(t.title,i)||void 0),r&&r.hideable&&"boolean"==typeof t.visible&&(r.visible=Boolean(t.visible)),r&&"string"==typeof t.title&&(r.title=t.title)}}makeHeaderFragment(e,t){const i=document.createDocumentFragment();o.UIUtils.createTextChild(i,e);const r=i.createChild("div","network-header-subtitle");return o.UIUtils.createTextChild(r,t),i}innerHeaderContextMenu(e){const t=this.columns.filter((e=>e.hideable)),i=t.filter((e=>!e.isResponseHeader)),r=new Map,o=[];for(const e of i)if(e.hideableGroup){const t=e.hideableGroup;let i=r.get(t);i||(i=[],r.set(t,i)),i.push(e)}else o.push(e);for(const t of r.values()){const i=t.filter((e=>e.visible));for(const r of t){const t=1===i.length&&i[0]===r,o=r.title instanceof Function?r.title():r.title;e.headerSection().appendCheckboxItem(o,this.toggleColumnVisibility.bind(this,r),{checked:r.visible,disabled:t,jslogContext:r.id})}e.headerSection().appendSeparator()}for(const t of o){const i=t.title instanceof Function?t.title():t.title;e.headerSection().appendCheckboxItem(i,this.toggleColumnVisibility.bind(this,t),{checked:t.visible,jslogContext:t.id})}const s=e.footerSection().appendSubMenuItem(wi(gi.responseHeaders),!1,"response-headers"),n=t.filter((e=>e.isResponseHeader));for(const e of n){const t=e.title instanceof Function?e.title():e.title;s.defaultSection().appendCheckboxItem(t,this.toggleColumnVisibility.bind(this,e),{checked:e.visible,jslogContext:e.id})}s.footerSection().appendItem(wi(gi.manageHeaderColumns),this.manageCustomHeaderDialog.bind(this),{jslogContext:"manage-header-columns"});const a=yi,l=e.footerSection().appendSubMenuItem(wi(gi.waterfall),!1,"waterfall");function c(e){let t=this.calculatorsMap.get("Time");const i=yi;e!==i.Duration&&e!==i.Latency||(t=this.calculatorsMap.get("Duration")),this.networkLogView.setCalculator(t),this.activeWaterfallSortId=e,this.dataGridInternal.markColumnAsSortedBy("waterfall",d.DataGrid.Order.Ascending),this.sortHandler()}l.defaultSection().appendCheckboxItem(wi(gi.startTime),c.bind(this,a.StartTime),{checked:this.activeWaterfallSortId===a.StartTime,jslogContext:"start-time"}),l.defaultSection().appendCheckboxItem(wi(gi.responseTime),c.bind(this,a.ResponseTime),{checked:this.activeWaterfallSortId===a.ResponseTime,jslogContext:"response-time"}),l.defaultSection().appendCheckboxItem(wi(gi.endTime),c.bind(this,a.EndTime),{checked:this.activeWaterfallSortId===a.EndTime,jslogContext:"end-time"}),l.defaultSection().appendCheckboxItem(wi(gi.totalDuration),c.bind(this,a.Duration),{checked:this.activeWaterfallSortId===a.Duration,jslogContext:"total-duration"}),l.defaultSection().appendCheckboxItem(wi(gi.latency),c.bind(this,a.Latency),{checked:this.activeWaterfallSortId===a.Latency,jslogContext:"latency"})}manageCustomHeaderDialog(){const e=[];for(const t of this.columns){const i=t.title instanceof Function?t.title():t.title;t.isResponseHeader&&e.push({title:i,editable:t.isCustomHeader})}const t=new ei(e,(e=>Boolean(this.addCustomHeader(e))),this.changeCustomHeader.bind(this),this.removeCustomHeader.bind(this)),i=new o.Dialog.Dialog("manage-custom-headers");t.show(i.contentElement),i.setSizeBehavior("MeasureContent"),i.show(this.networkLogView.element)}removeCustomHeader(e){e=e.toLowerCase();const t=this.columns.findIndex((t=>t.id===e));return-1!==t&&(this.columns.splice(t,1),this.dataGridInternal.removeColumn(e),this.saveColumnsSettings(),this.updateColumns(),!0)}addCustomHeader(e,t,i){t||(t=e.toLowerCase()),void 0===i&&(i=this.columns.length-1);const r=this.columns.find((e=>e.id===t));if(r)return null;const o=Object.assign({},fi,{id:t,title:e,isResponseHeader:!0,isCustomHeader:!0,visible:!0,sortingFunction:ke.ResponseHeaderStringComparator.bind(null,t)});return this.columns.splice(i,0,o),this.dataGridInternal&&this.dataGridInternal.addColumn(bi.convertToDataGridDescriptor(o),i),this.saveColumnsSettings(),this.updateColumns(),o}changeCustomHeader(e,t,i){i||(i=t.toLowerCase()),e=e.toLowerCase();const r=this.columns.findIndex((t=>t.id===e)),o=this.columns[r],s=this.columns.find((e=>e.id===i));return!(!o||s&&e!==i)&&(this.removeCustomHeader(e),this.addCustomHeader(t,i,r),!0)}getPopoverRequest(e){if(!this.gridMode)return null;const t=this.networkLogView.hoveredNode();if(!t||!e.target)return null;const i=e.target.enclosingNodeOrSelfWithClass("network-script-initiated");if(!i)return null;const r=t.request();return r?{box:i.boxInWindow(),show:async e=>{this.popupLinkifier.addEventListener("liveLocationUpdated",(()=>{e.setSizeBehavior("MeasureContent")}));const t=Pe.createStackTracePreview(r,this.popupLinkifier,!1);return!!t&&(e.contentElement.appendChild(t.element),!0)},hide:this.popupLinkifier.reset.bind(this.popupLinkifier)}:null}addEventDividers(e,t){let i="transparent";switch(t){case"network-dcl-divider":i="#0867CB";break;case"network-load-divider":i="#B31412";break;default:return}const r=this.eventDividers.get(i)||[];this.eventDividers.set(i,r.concat(e)),this.networkLogView.scheduleRefresh()}hideEventDividers(){this.eventDividersShown=!0,this.redrawWaterfallColumn()}showEventDividers(){this.eventDividersShown=!1,this.redrawWaterfallColumn()}selectFilmStripFrame(e){this.eventDividers.set(Si,[e]),this.redrawWaterfallColumn()}clearFilmStripFrame(){this.eventDividers.delete(Si),this.redrawWaterfallColumn()}}const vi="waterfall",fi={subtitle:null,visible:!1,weight:6,sortable:!0,hideable:!0,hideableGroup:null,nonSelectable:!1,isResponseHeader:!1,isCustomHeader:!1,allowInSortByEvenWhenHidden:!1},Ci=[{id:"name",title:ki(gi.name),subtitle:ki(gi.path),visible:!0,weight:20,hideable:!0,hideableGroup:"path",sortingFunction:ke.NameComparator},{id:"path",title:ki(gi.path),hideable:!0,hideableGroup:"path",sortingFunction:ke.RequestPropertyComparator.bind(null,"pathname")},{id:"url",title:ki(gi.url),hideable:!0,hideableGroup:"path",sortingFunction:ke.RequestURLComparator},{id:"method",title:ki(gi.method),sortingFunction:ke.RequestPropertyComparator.bind(null,"requestMethod")},{id:"status",title:ki(gi.status),visible:!0,subtitle:ki(gi.text),sortingFunction:ke.RequestPropertyComparator.bind(null,"statusCode")},{id:"protocol",title:ki(gi.protocol),sortingFunction:ke.RequestPropertyComparator.bind(null,"protocol")},{id:"scheme",title:ki(gi.scheme),sortingFunction:ke.RequestPropertyComparator.bind(null,"scheme")},{id:"domain",title:ki(gi.domain),sortingFunction:ke.RequestPropertyComparator.bind(null,"domain")},{id:"remote-address",title:ki(gi.remoteAddress),weight:10,align:"right",sortingFunction:ke.RemoteAddressComparator},{id:"remote-address-space",title:ki(gi.remoteAddressSpace),visible:!1,weight:10,sortingFunction:ke.RemoteAddressSpaceComparator},{id:"type",title:ki(gi.type),visible:!0,sortingFunction:ke.TypeComparator},{id:"initiator",title:ki(gi.initiator),visible:!0,weight:10,sortingFunction:ke.InitiatorComparator},{id:"initiator-address-space",title:ki(gi.initiatorAddressSpace),visible:!1,weight:10,sortingFunction:ke.InitiatorAddressSpaceComparator},{id:"cookies",title:ki(gi.cookies),align:"right",sortingFunction:ke.RequestCookiesCountComparator},{id:"set-cookies",title:ki(gi.setCookies),align:"right",sortingFunction:ke.ResponseCookiesCountComparator},{id:"size",title:ki(gi.size),visible:!0,subtitle:ki(gi.content),align:"right",sortingFunction:ke.SizeComparator},{id:"time",title:ki(gi.time),visible:!0,subtitle:ki(gi.latency),align:"right",sortingFunction:ke.RequestPropertyComparator.bind(null,"duration")},{id:"priority",title:ki(gi.priority),sortingFunction:ke.PriorityComparator},{id:"connection-id",title:ki(gi.connectionId),sortingFunction:ke.RequestPropertyComparator.bind(null,"connectionId")},{id:"cache-control",isResponseHeader:!0,title:i.i18n.lockedLazyString("Cache-Control"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"cache-control")},{id:"connection",isResponseHeader:!0,title:i.i18n.lockedLazyString("Connection"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"connection")},{id:"content-encoding",isResponseHeader:!0,title:i.i18n.lockedLazyString("Content-Encoding"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"content-encoding")},{id:"content-length",isResponseHeader:!0,title:i.i18n.lockedLazyString("Content-Length"),align:"right",sortingFunction:ke.ResponseHeaderNumberComparator.bind(null,"content-length")},{id:"etag",isResponseHeader:!0,title:i.i18n.lockedLazyString("ETag"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"etag")},{id:"has-overrides",title:ki(gi.hasOverrides),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"has-overrides")},{id:"keep-alive",isResponseHeader:!0,title:i.i18n.lockedLazyString("Keep-Alive"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"keep-alive")},{id:"last-modified",isResponseHeader:!0,title:i.i18n.lockedLazyString("Last-Modified"),sortingFunction:ke.ResponseHeaderDateComparator.bind(null,"last-modified")},{id:"server",isResponseHeader:!0,title:i.i18n.lockedLazyString("Server"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"server")},{id:"vary",isResponseHeader:!0,title:i.i18n.lockedLazyString("Vary"),sortingFunction:ke.ResponseHeaderStringComparator.bind(null,"vary")},{id:"waterfall",title:ki(gi.waterfall),allowInSortByEvenWhenHidden:!0}],Si="#fccc49";var yi;!function(e){e.StartTime="startTime",e.ResponseTime="responseReceivedTime",e.EndTime="endTime",e.Duration="duration",e.Latency="latency"}(yi||(yi={}));var Ti=Object.freeze({__proto__:null,NetworkLogViewColumns:bi});const xi={invertFilter:"Invert",invertsFilter:"Inverts the search filter",allStrings:"All",hideDataUrls:"Hide data URLs",hidesDataAndBlobUrls:"Hide 'data:' and 'blob:' URLs",chromeExtensions:"Hide extension URLs",hideChromeExtension:"Hide 'chrome-extension://' URLs",requestTypesToInclude:"Request types to include",requestTypesTooltip:"Filter requests by type",requestTypes:"Request types",twoTypesSelected:"{PH1}, {PH2}",overTwoTypesSelected:"{PH1}, {PH2}...",hasBlockedCookies:"Blocked response cookies",onlyShowRequestsWithBlockedCookies:"Show only requests with blocked response cookies",blockedRequests:"Blocked requests",onlyShowBlockedRequests:"Show only blocked requests",thirdParty:"3rd-party requests",onlyShowThirdPartyRequests:"Show only requests with origin different from page origin",dropHarFilesHere:"Drop HAR files here",recordingNetworkActivity:"Recording network activity…",performARequestOrHitSToRecordThe:"Perform a request or hit {PH1} to record the reload.",recordToDisplayNetworkActivity:"Record network log ({PH1}) to display network activity.",learnMore:"Learn more",networkDataAvailable:"Network Data Available",sSRequests:"{PH1} / {PH2} requests",sSTransferred:"{PH1} / {PH2} transferred",sBSBTransferredOverNetwork:"{PH1} B / {PH2} B transferred over network",sSResources:"{PH1} / {PH2} resources",sBSBResourcesLoadedByThePage:"{PH1} B / {PH2} B resources loaded by the page",sRequests:"{PH1} requests",sTransferred:"{PH1} transferred",sBTransferredOverNetwork:"{PH1} B transferred over network",sResources:"{PH1} resources",sBResourcesLoadedByThePage:"{PH1} B resources loaded by the page",finishS:"Finish: {PH1}",domcontentloadedS:"DOMContentLoaded: {PH1}",loadS:"Load: {PH1}",copy:"Copy",copyURL:"Copy URL",copyRequestHeaders:"Copy request headers",copyResponseHeaders:"Copy response headers",copyResponse:"Copy response",copyStacktrace:"Copy stack trace",copyAsPowershell:"Copy as `PowerShell`",copyAsFetch:"Copy as `fetch`",copyAsNodejsFetch:"Copy as `fetch` (`Node.js`)",copyAsCurlCmd:"Copy as `cURL` (`cmd`)",copyAsCurlBash:"Copy as `cURL` (`bash`)",copyAllURLs:"Copy all URLs",copyAllListedURLs:"Copy all listed URLs",copyAllAsPowershell:"Copy all as `PowerShell`",copyAllListedAsPowershell:"Copy all listed as `PowerShell`",copyAllAsFetch:"Copy all as `fetch`",copyAllListedAsFetch:"Copy all listed as `fetch`",copyAllAsNodejsFetch:"Copy all as `fetch` (`Node.js`)",copyAllListedAsNodejsFetch:"Copy all listed as `fetch` (`Node.js`)",copyAllAsCurlCmd:"Copy all as `cURL` (`cmd`)",copyAllListedAsCurlCmd:"Copy all listed as `cURL` (`cmd`)",copyAllAsCurlBash:"Copy all as `cURL` (`bash`)",copyAllListedAsCurlBash:"Copy all listed as `cURL` (`bash`)",copyAsCurl:"Copy as `cURL`",copyAllAsCurl:"Copy all as `cURL`",copyAllListedAsCurl:"Copy all listed as `cURL`",copyAllAsHar:"Copy all as `HAR`",copyAllListedAsHar:"Copy all listed as `HAR`",saveAllAsHarWithContent:"Save all as `HAR` with content",clearBrowserCache:"Clear browser cache",clearBrowserCookies:"Clear browser cookies",blockRequestUrl:"Block request URL",unblockS:"Unblock {PH1}",blockRequestDomain:"Block request domain",replayXhr:"Replay XHR",areYouSureYouWantToClearBrowser:"Are you sure you want to clear browser cache?",areYouSureYouWantToClearBrowserCookies:"Are you sure you want to clear browser cookies?",overrideHeaders:"Override headers",showOnlyHideRequests:"Show only/hide requests",moreFilters:"More filters",showOnly:"Show only {PH1}"},Ri=i.i18n.registerUIStrings("panels/network/NetworkLogView.ts",xi),Ii=i.i18n.getLocalizedString.bind(void 0,Ri),qi=L.RenderCoordinator.RenderCoordinator.instance();class Li extends(e.ObjectWrapper.eventMixin(o.Widget.VBox)){networkInvertFilterSetting;networkHideDataURLSetting;networkHideChromeExtensions;networkShowBlockedCookiesOnlySetting;networkOnlyBlockedRequestsSetting;networkOnlyThirdPartySetting;networkResourceTypeFiltersSetting;rawRowHeight;progressBarContainer;networkLogLargeRowsSetting;rowHeightInternal;timeCalculatorInternal;durationCalculator;calculatorInternal;columnsInternal;staleRequests;mainRequestLoadTime;mainRequestDOMContentLoadedTime;filters;timeFilter;hoveredNodeInternal;recordingHint;highlightedNode;linkifierInternal;recording;needsRefresh;headerHeightInternal;groupLookups;activeGroupLookup;textFilterUI;invertFilterUI;dataURLFilterUI;moreFiltersDropDownUI;onlyBlockedResponseCookiesFilterUI;onlyBlockedRequestsUI;onlyThirdPartyFilterUI;hideChromeExtensionsUI;resourceCategoryFilterUI;filterParser;suggestionBuilder;dataGrid;summaryToolbarInternal;filterBar;textFilterSetting;constructor(t,i,r){function l(){this.rawRowHeight=Boolean(this.networkLogLargeRowsSetting.get())?41:21,this.rowHeightInternal=this.computeRowHeight()}super(),this.setMinimumSize(50,64),this.element.id="network-container",this.element.classList.add("no-node-selected"),this.networkInvertFilterSetting=e.Settings.Settings.instance().createSetting("network-invert-filter",!1),this.networkHideDataURLSetting=e.Settings.Settings.instance().createSetting("network-hide-data-url",!1),this.networkHideChromeExtensions=e.Settings.Settings.instance().createSetting("network-hide-chrome-extensions",!1),this.networkShowBlockedCookiesOnlySetting=e.Settings.Settings.instance().createSetting("network-show-blocked-cookies-only-setting",!1),this.networkOnlyBlockedRequestsSetting=e.Settings.Settings.instance().createSetting("network-only-blocked-requests",!1),this.networkOnlyThirdPartySetting=e.Settings.Settings.instance().createSetting("network-only-third-party-setting",!1),this.networkResourceTypeFiltersSetting=e.Settings.Settings.instance().createSetting("network-resource-type-filters",{}),this.rawRowHeight=0,this.progressBarContainer=i,this.networkLogLargeRowsSetting=r,this.networkLogLargeRowsSetting.addChangeListener(l.bind(this),this),this.rawRowHeight=0,this.rowHeightInternal=0,l.call(this),this.timeCalculatorInternal=new jt,this.durationCalculator=new Gt,this.calculatorInternal=this.timeCalculatorInternal,this.columnsInternal=new bi(this,this.timeCalculatorInternal,this.durationCalculator,r),this.columnsInternal.show(this.element),this.staleRequests=new Set,this.mainRequestLoadTime=-1,this.mainRequestDOMContentLoadedTime=-1,this.filters=[],this.timeFilter=null,this.hoveredNodeInternal=null,this.recordingHint=null,this.highlightedNode=null,this.linkifierInternal=new k.Linkifier.Linkifier,this.recording=!1,this.needsRefresh=!1,this.headerHeightInternal=0,this.groupLookups=new Map,this.groupLookups.set("Frame",new _t(this)),this.activeGroupLookup=null,this.textFilterUI=new o.FilterBar.TextFilterUI,this.textFilterUI.addEventListener("FilterChanged",this.filterChanged,this),t.addFilter(this.textFilterUI),this.invertFilterUI=new o.FilterBar.CheckboxFilterUI("invert-filter",Ii(xi.invertFilter),!0,this.networkInvertFilterSetting,"invert-filter"),this.invertFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.invertFilterUI.element(),Ii(xi.invertsFilter)),t.addFilter(this.invertFilterUI),t.addDivider();const d=Object.entries(e.ResourceType.resourceCategories).map((([e,t])=>({name:t.title(),label:()=>t.shortTitle(),title:t.title(),jslogContext:s.StringUtilities.toKebabCase(e)})));T.Runtime.experiments.isEnabled("network-panel-filter-bar-redesign")?(this.resourceCategoryFilterUI=new Bi(d,this.networkResourceTypeFiltersSetting),this.resourceCategoryFilterUI.addEventListener("FilterChanged",this.filterChanged,this),o.ARIAUtils.setLabel(this.resourceCategoryFilterUI.element(),Ii(xi.requestTypesToInclude)),this.resourceCategoryFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),t.addFilter(this.resourceCategoryFilterUI),t.addDivider(),this.moreFiltersDropDownUI=new Ui,this.moreFiltersDropDownUI.addEventListener("FilterChanged",this.filterChanged,this),t.addFilter(this.moreFiltersDropDownUI)):(this.dataURLFilterUI=new o.FilterBar.CheckboxFilterUI("hide-data-url",Ii(xi.hideDataUrls),!0,this.networkHideDataURLSetting,"hide-data-urls"),this.dataURLFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.dataURLFilterUI.element(),Ii(xi.hidesDataAndBlobUrls)),t.addFilter(this.dataURLFilterUI),this.hideChromeExtensionsUI=new o.FilterBar.CheckboxFilterUI("chrome-extension",Ii(xi.chromeExtensions),!0,this.networkHideChromeExtensions,"hide-extension-urls"),this.hideChromeExtensionsUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.hideChromeExtensionsUI.element(),Ii(xi.hideChromeExtension)),t.addFilter(this.hideChromeExtensionsUI),this.resourceCategoryFilterUI=new o.FilterBar.NamedBitSetFilterUI(d,this.networkResourceTypeFiltersSetting),o.ARIAUtils.setLabel(this.resourceCategoryFilterUI.element(),Ii(xi.requestTypesToInclude)),this.resourceCategoryFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),t.addFilter(this.resourceCategoryFilterUI),this.onlyBlockedResponseCookiesFilterUI=new o.FilterBar.CheckboxFilterUI("only-show-blocked-cookies",Ii(xi.hasBlockedCookies),!0,this.networkShowBlockedCookiesOnlySetting,"only-show-blocked-cookies"),this.onlyBlockedResponseCookiesFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.onlyBlockedResponseCookiesFilterUI.element(),Ii(xi.onlyShowRequestsWithBlockedCookies)),t.addFilter(this.onlyBlockedResponseCookiesFilterUI),this.onlyBlockedRequestsUI=new o.FilterBar.CheckboxFilterUI("only-show-blocked-requests",Ii(xi.blockedRequests),!0,this.networkOnlyBlockedRequestsSetting,"only-show-blocked-requests"),this.onlyBlockedRequestsUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.onlyBlockedRequestsUI.element(),Ii(xi.onlyShowBlockedRequests)),t.addFilter(this.onlyBlockedRequestsUI),this.onlyThirdPartyFilterUI=new o.FilterBar.CheckboxFilterUI("only-show-third-party",Ii(xi.thirdParty),!0,this.networkOnlyThirdPartySetting,"only-show-third-party"),this.onlyThirdPartyFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),o.Tooltip.Tooltip.install(this.onlyThirdPartyFilterUI.element(),Ii(xi.onlyShowThirdPartyRequests)),t.addFilter(this.onlyThirdPartyFilterUI)),this.filterParser=new y.TextUtils.FilterParser(Ei),this.suggestionBuilder=new o.FilterSuggestionBuilder.FilterSuggestionBuilder(Ei,Li.sortSearchValues),this.resetSuggestionBuilder(),this.dataGrid=this.columnsInternal.dataGrid(),this.setupDataGrid(),this.columnsInternal.sortByCurrentColumn(),t.filterButton().addEventListener("Click",this.dataGrid.scheduleUpdate.bind(this.dataGrid,!0)),this.summaryToolbarInternal=new o.Toolbar.Toolbar("network-summary-bar",this.element),this.summaryToolbarInternal.element.setAttribute("role","status"),new o.DropTarget.DropTarget(this.element,[o.DropTarget.Type.File],Ii(xi.dropHarFilesHere),this.handleDrop.bind(this)),e.Settings.Settings.instance().moduleSetting("network-color-code-resource-types").addChangeListener(this.invalidateAllItems.bind(this,!1),this),n.TargetManager.TargetManager.instance().observeModels(n.NetworkManager.NetworkManager,this,{scoped:!0}),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.RequestAdded,this.onRequestUpdated,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.RequestUpdated,this.onRequestUpdated,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.RequestRemoved,this.onRequestRemoved,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.Reset,this.reset,this),this.updateGroupByFrame(),e.Settings.Settings.instance().moduleSetting("network.group-by-frame").addChangeListener((()=>this.updateGroupByFrame())),this.filterBar=t,this.textFilterSetting=e.Settings.Settings.instance().createSetting("network-text-filter",""),this.textFilterSetting.get()&&this.textFilterUI.setValue(this.textFilterSetting.get())}getMoreFiltersDropdown(){return this.moreFiltersDropDownUI}updateGroupByFrame(){const t=e.Settings.Settings.instance().moduleSetting("network.group-by-frame").get();this.setGrouping(t?"Frame":null)}static sortSearchValues(e,t){e===g.UIFilter.FilterType.Priority?t.sort(((e,t)=>{const i=w.NetworkPriorities.uiLabelToNetworkPriority(e),r=w.NetworkPriorities.uiLabelToNetworkPriority(t);return w.NetworkPriorities.networkPriorityWeight(i)-w.NetworkPriorities.networkPriorityWeight(r)})):t.sort()}static negativeFilter(e,t){return!e(t)}static requestPathFilter(e,t){return!!e&&e.test(t.path()+"/"+t.name())}static subdomains(e){const t=[e];let i=e.indexOf(".");for(;-1!==i;)t.push("*"+e.substring(i)),i=e.indexOf(".",i+1);return t}static createRequestDomainFilter(e){const t=e.split("*").map(s.StringUtilities.escapeForRegExp).join(".*");return Li.requestDomainFilter.bind(null,new RegExp("^"+t+"$","i"))}static requestDomainFilter(e,t){return e.test(t.domain)}static runningRequestFilter(e){return!e.finished}static fromCacheRequestFilter(e){return e.cached()}static interceptedByServiceWorkerFilter(e){return e.fetchedViaServiceWorker}static initiatedByServiceWorkerFilter(e){return e.initiatedByServiceWorker()}static requestResponseHeaderFilter(e,t){return void 0!==t.responseHeaderValue(e)}static requestResponseHeaderSetCookieFilter(e,t){return Boolean(t.responseHeaderValue("Set-Cookie")?.includes(e))}static requestMethodFilter(e,t){return t.requestMethod===e}static requestPriorityFilter(e,t){return t.priority()===e}static requestMimeTypeFilter(e,t){return t.mimeType===e}static requestMixedContentFilter(e,t){return"displayed"===e?"optionally-blockable"===t.mixedContentType:"blocked"===e?"blockable"===t.mixedContentType&&t.wasBlocked():"block-overridden"===e?"blockable"===t.mixedContentType&&!t.wasBlocked():"all"===e&&"none"!==t.mixedContentType}static requestSchemeFilter(e,t){return t.scheme===e}static requestCookieDomainFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.domain()===e))}static requestCookieNameFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.name()===e))}static requestCookiePathFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.path()===e))}static requestCookieValueFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.value()===e))}static requestSetCookieDomainFilter(e,t){return t.responseCookies.some((t=>t.domain()===e))}static requestSetCookieNameFilter(e,t){return t.responseCookies.some((t=>t.name()===e))}static requestSetCookieValueFilter(e,t){return t.responseCookies.some((t=>t.value()===e))}static requestSizeLargerThanFilter(e,t){return t.transferSize>=e}static statusCodeFilter(e,t){return String(t.statusCode)===e}static hasOverridesFilter(e,t){return!!e&&(e===Ai.no?0===t.overrideTypes.length:e===Ai.yes?t.overrideTypes.length>0:e===Ai.content?t.overrideTypes.includes("content"):e===Ai.headers?t.overrideTypes.includes("headers"):t.overrideTypes.join(",").includes(e))}static getHTTPRequestsFilter(e){return e.parsedURL.isValid&&e.scheme in Mi}static resourceTypeFilter(e,t){return t.resourceType().name()===e}static requestUrlFilter(e,t){return new RegExp(s.StringUtilities.escapeForRegExp(e),"i").test(t.url())}static requestTimeFilter(e,t,i){return!(i.issueTime()>t)&&!(-1!==i.endTime&&i.endTime<e)}static copyRequestHeaders(e){t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.requestHeadersText())}static copyResponseHeaders(e){t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.responseHeadersText)}static async copyResponse(e){const i=await e.requestContentData();let r;r=y.ContentData.ContentData.isError(i)?"":i.isTextContent?i.text:i.asDataUrl()??"",t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}handleDrop(e){const t=e.items;if(!t.length)return;const i=t[0].getAsFile();i&&this.onLoadFromFile(i)}async onLoadFromFile(t){const i=new e.StringOutputStream.StringOutputStream,r=new u.FileUtils.ChunkedFileReader(t,1e7);if(!await r.read(i)){const e=r.error();return void(e&&this.harLoadFailed(e.message))}let o;try{o=new x.HARFormat.HARRoot(JSON.parse(i.data()))}catch(e){return void this.harLoadFailed(e)}a.NetworkLog.NetworkLog.instance().importRequests(x.Importer.Importer.requestsFromHARLog(o.log))}harLoadFailed(t){e.Console.Console.instance().error("Failed to load HAR file with following error: "+t)}setGrouping(e){this.activeGroupLookup&&this.activeGroupLookup.reset();const t=e&&this.groupLookups.get(e)||null;this.activeGroupLookup=t,this.invalidateAllItems()}computeRowHeight(){return this.rawRowHeight}nodeForRequest(e){return Hi.get(e)||null}headerHeight(){return this.headerHeightInternal}setRecording(e){this.recording=e,this.updateSummaryBar()}columns(){return this.columnsInternal}summaryToolbar(){return this.summaryToolbarInternal}modelAdded(e){const t=e.target();if(t.outermostTarget()!==t)return;const i=t.model(n.ResourceTreeModel.ResourceTreeModel);i&&(i.addEventListener(n.ResourceTreeModel.Events.Load,this.loadEventFired,this),i.addEventListener(n.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this));for(const e of a.NetworkLog.NetworkLog.instance().requests())this.isInScope(e)&&this.refreshRequest(e)}modelRemoved(t){const i=t.target();if(i.outermostTarget()!==i)return;const r=i.model(n.ResourceTreeModel.ResourceTreeModel);r&&(r.removeEventListener(n.ResourceTreeModel.Events.Load,this.loadEventFired,this),r.removeEventListener(n.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this));e.Settings.Settings.instance().moduleSetting("network-log.preserve-log").get()||this.reset()}linkifier(){return this.linkifierInternal}setWindow(e,t){e||t?(this.timeFilter=Li.requestTimeFilter.bind(null,e,t),this.timeCalculatorInternal.setWindow(new Ot(e,t))):(this.timeFilter=null,this.timeCalculatorInternal.setWindow(null)),this.filterRequests()}resetFocus(){this.dataGrid.element.focus()}resetSuggestionBuilder(){this.suggestionBuilder.clear(),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,"running"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,"from-cache"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,"service-worker-intercepted"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,"service-worker-initiated"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"100"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"10k"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"1M"),this.textFilterUI.setSuggestionProvider(this.suggestionBuilder.completions.bind(this.suggestionBuilder)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasOverrides,Ai.yes),this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasOverrides,Ai.no),this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasOverrides,Ai.content),this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasOverrides,Ai.headers)}filterChanged(){this.removeAllNodeHighlights(),this.parseFilterQuery(this.textFilterUI.value(),this.invertFilterUI.checked()),this.filterRequests(),this.textFilterSetting.set(this.textFilterUI.value()),this.moreFiltersDropDownUI?.updateActiveFiltersCount(),this.moreFiltersDropDownUI?.updateTooltip()}async resetFilter(){this.textFilterUI.clear()}showRecordingHint(){this.hideRecordingHint(),this.recordingHint=this.element.createChild("div","network-status-pane fill");const e=this.recordingHint.createChild("div","recording-hint");if(this.recording){let t=null;const r=o.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("inspector-main.reload")[0];r&&(t=this.recordingHint.createChild("b"),t.textContent=r.title());e.createChild("span").textContent=Ii(xi.recordingNetworkActivity),t&&(e.createChild("br"),e.appendChild(i.i18n.getFormatLocalizedString(Ri,xi.performARequestOrHitSToRecordThe,{PH1:t})))}else{const t=e.createChild("b");t.textContent=o.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("network.toggle-recording")||"",e.appendChild(i.i18n.getFormatLocalizedString(Ri,xi.recordToDisplayNetworkActivity,{PH1:t}))}e.createChild("br"),e.appendChild(o.XLink.XLink.create("https://developer.chrome.com/docs/devtools/network/?utm_source=devtools&utm_campaign=2019Q1",Ii(xi.learnMore),void 0,void 0,"learn-more")),this.setHidden(!0)}hideRecordingHint(){this.setHidden(!1),this.recordingHint&&this.recordingHint.remove(),o.ARIAUtils.alert(Ii(xi.networkDataAvailable)),this.recordingHint=null}setHidden(e){this.columnsInternal.setHidden(e),o.ARIAUtils.setHidden(this.summaryToolbarInternal.element,e)}elementsToRestoreScrollPositionsFor(){return this.dataGrid?[this.dataGrid.scrollContainer]:[]}columnExtensionResolved(){this.invalidateAllItems(!0)}setupDataGrid(){return this.dataGrid.setRowContextMenuCallback(((e,t)=>{const i=t.request();i&&this.handleContextMenuForRequest(e,i)})),this.dataGrid.setStickToBottom(!0),this.dataGrid.setName("network-log"),this.dataGrid.setResizeMethod("last"),this.dataGrid.element.classList.add("network-log-grid"),this.dataGrid.element.addEventListener("mousedown",this.dataGridMouseDown.bind(this),!0),this.dataGrid.element.addEventListener("mousemove",this.dataGridMouseMove.bind(this),!0),this.dataGrid.element.addEventListener("mouseleave",(()=>this.setHoveredNode(null)),!0),this.dataGrid.element.addEventListener("keydown",(e=>{if("ArrowRight"===e.key&&this.dataGrid.selectedNode){const e=this.dataGrid.selectedNode.element().querySelector("button.devtools-link");e&&e.focus()}s.KeyboardUtilities.isEnterOrSpaceKey(e)&&(this.dispatchEventToListeners("RequestActivated",{showPanel:!0,takeFocus:!0}),e.consume(!0))})),this.dataGrid.element.addEventListener("keyup",(e=>{if(("r"===e.key||"R"===e.key)&&this.dataGrid.selectedNode){const t=this.dataGrid.selectedNode.request();if(!t)return;n.NetworkManager.NetworkManager.canReplayRequest(t)&&(n.NetworkManager.NetworkManager.replayRequest(t),l.logKeyDown(this.dataGrid.selectedNode.element(),e,"replay-xhr"))}})),this.dataGrid.element.addEventListener("focus",this.onDataGridFocus.bind(this),!0),this.dataGrid.element.addEventListener("blur",this.onDataGridBlur.bind(this),!0),this.dataGrid}dataGridMouseMove(e){const t=e,i=this.dataGrid.dataGridNodeFromNode(t.target),r=t.shiftKey;this.setHoveredNode(i,r)}hoveredNode(){return this.hoveredNodeInternal}setHoveredNode(e,t){this.hoveredNodeInternal&&this.hoveredNodeInternal.setHovered(!1,!1),this.hoveredNodeInternal=e,this.hoveredNodeInternal&&this.hoveredNodeInternal.setHovered(!0,Boolean(t))}dataGridMouseDown(e){const t=e;!this.dataGrid.selectedNode&&t.button&&t.consume()}updateSummaryBar(){this.hideRecordingHint();let t=0,r=0,l=0,d=0,c=0,h=-1,u=-1,p=0;for(const i of a.NetworkLog.NetworkLog.instance().requests()){const o=Hi.get(i);if(!o)continue;p++;const s=i.transferSize;t+=s;const a=i.resourceSize;r+=a,Pi.has(o)||(l++,d+=s,c+=a);const g=n.NetworkManager.NetworkManager.forRequest(i);g&&i.url()===g.target().inspectedURL()&&i.resourceType()===e.ResourceType.resourceTypes.Document&&g.target().parentTarget()?.type()!==n.Target.Type.Frame&&(h=i.fromPrefetchCache()?i.issueTime():i.startTime),i.endTime>u&&(u=i.endTime)}if(!p)return void this.showRecordingHint();this.summaryToolbarInternal.removeToolbarItems();const g=(e,t)=>{const i=new o.Toolbar.ToolbarText(e);return i.setTitle(t||e),this.summaryToolbarInternal.appendToolbarItem(i),i.element};if(l!==p?(g(Ii(xi.sSRequests,{PH1:l,PH2:p})),this.summaryToolbarInternal.appendSeparator(),g(Ii(xi.sSTransferred,{PH1:s.NumberUtilities.bytesToString(d),PH2:s.NumberUtilities.bytesToString(t)}),Ii(xi.sBSBTransferredOverNetwork,{PH1:d,PH2:t})),this.summaryToolbarInternal.appendSeparator(),g(Ii(xi.sSResources,{PH1:s.NumberUtilities.bytesToString(c),PH2:s.NumberUtilities.bytesToString(r)}),Ii(xi.sBSBResourcesLoadedByThePage,{PH1:c,PH2:r}))):(g(Ii(xi.sRequests,{PH1:p})),this.summaryToolbarInternal.appendSeparator(),g(Ii(xi.sTransferred,{PH1:s.NumberUtilities.bytesToString(t)}),Ii(xi.sBTransferredOverNetwork,{PH1:t})),this.summaryToolbarInternal.appendSeparator(),g(Ii(xi.sResources,{PH1:s.NumberUtilities.bytesToString(r)}),Ii(xi.sBResourcesLoadedByThePage,{PH1:r}))),-1!==h&&-1!==u){if(this.summaryToolbarInternal.appendSeparator(),g(Ii(xi.finishS,{PH1:i.TimeUtilities.secondsToString(u-h)})),-1!==this.mainRequestDOMContentLoadedTime&&this.mainRequestDOMContentLoadedTime>h){this.summaryToolbarInternal.appendSeparator();g(Ii(xi.domcontentloadedS,{PH1:i.TimeUtilities.secondsToString(this.mainRequestDOMContentLoadedTime-h)})).style.color=`var(${Li.getDCLEventColor()})`}if(-1!==this.mainRequestLoadTime){this.summaryToolbarInternal.appendSeparator();g(Ii(xi.loadS,{PH1:i.TimeUtilities.secondsToString(this.mainRequestLoadTime-h)})).style.color=`var(${Li.getLoadEventColor()})`}}}scheduleRefresh(){this.needsRefresh||(this.needsRefresh=!0,this.isShowing()&&qi.write("NetworkLogView.render",this.refresh.bind(this)))}addFilmStripFrames(e){this.columnsInternal.addEventDividers(e,"network-frame-divider")}selectFilmStripFrame(e){this.columnsInternal.selectFilmStripFrame(e)}clearFilmStripFrame(){this.columnsInternal.clearFilmStripFrame()}refreshIfNeeded(){this.needsRefresh&&this.refresh()}invalidateAllItems(e){this.staleRequests=new Set(a.NetworkLog.NetworkLog.instance().requests().filter(this.isInScope)),e?this.scheduleRefresh():this.refresh()}timeCalculator(){return this.timeCalculatorInternal}calculator(){return this.calculatorInternal}setCalculator(e){e&&this.calculatorInternal!==e&&(this.calculatorInternal!==e&&(this.calculatorInternal=e,this.columnsInternal.setCalculator(this.calculatorInternal)),this.calculatorInternal.reset(),this.calculatorInternal.startAtZero?this.columnsInternal.hideEventDividers():this.columnsInternal.showEventDividers(),this.invalidateAllItems())}loadEventFired(e){if(!this.recording)return;const t=e.data.loadTime;t&&(this.mainRequestLoadTime=t,this.columnsInternal.addEventDividers([t],"network-load-divider"))}domContentLoadedEventFired(e){if(!this.recording)return;const{data:t}=e;t&&(this.mainRequestDOMContentLoadedTime=t,this.columnsInternal.addEventDividers([t],"network-dcl-divider"))}wasShown(){this.refreshIfNeeded(),this.registerCSSFiles([Xt]),this.columnsInternal.wasShown()}willHide(){this.columnsInternal.willHide()}onResize(){this.rowHeightInternal=this.computeRowHeight()}flatNodesList(){return this.dataGrid.rootNode().flatChildren()}onDataGridFocus(){this.dataGrid.element.matches(":focus-visible")&&this.element.classList.add("grid-focused"),this.updateNodeBackground()}onDataGridBlur(){this.element.classList.remove("grid-focused"),this.updateNodeBackground()}updateNodeBackground(){this.dataGrid.selectedNode&&this.dataGrid.selectedNode.updateBackgroundColor()}updateNodeSelectedClass(e){e?this.element.classList.remove("no-node-selected"):this.element.classList.add("no-node-selected")}stylesChanged(){this.columnsInternal.scheduleRefresh()}removeNodeAndMaybeAncestors(e){let t=e.parent;if(t)for(t.removeChild(e);t&&!t.hasChildren()&&t.dataGrid&&t.dataGrid.rootNode()!==t;){const e=t.parent;e.removeChild(t),t=e}}refresh(){this.needsRefresh=!1,this.removeAllNodeHighlights(),this.timeCalculatorInternal.updateBoundariesForEventTime(this.mainRequestLoadTime),this.durationCalculator.updateBoundariesForEventTime(this.mainRequestLoadTime),this.timeCalculatorInternal.updateBoundariesForEventTime(this.mainRequestDOMContentLoadedTime),this.durationCalculator.updateBoundariesForEventTime(this.mainRequestDOMContentLoadedTime);const e=new Map,t=[],i=new Set;for(;this.staleRequests.size;){const e=this.staleRequests.values().next().value;this.staleRequests.delete(e);let t=Hi.get(e);t||(t=this.createNodeForRequest(e)),i.add(t)}for(const r of i){const i=r.request(),o=!this.applyFilter(i);o?(r===this.hoveredNodeInternal&&this.setHoveredNode(null),r.selected=!1):t.push(r),this.timeCalculatorInternal.updateBoundaries(i),this.durationCalculator.updateBoundaries(i);const s=this.parentNodeForInsert(r);if(Pi.has(r)===o&&r.parent===s)continue;o?Pi.add(r):Pi.delete(r);r.parent&&(o||r.parent!==s)&&this.removeNodeAndMaybeAncestors(r),s&&!o&&(s.dataGrid||e.has(s)||(e.set(s,this.dataGrid.rootNode()),t.push(s)),e.set(r,s))}for(const t of e.keys())e.get(t).appendChild(t);for(const e of t)e.refresh();this.updateSummaryBar(),e.size&&this.columnsInternal.sortByCurrentColumn(),this.dataGrid.updateInstantly(),this.didRefreshForTest()}didRefreshForTest(){}parentNodeForInsert(e){if(!this.activeGroupLookup)return this.dataGrid.rootNode();const t=this.activeGroupLookup.groupNodeForRequest(e.request());return t||this.dataGrid.rootNode()}reset(){this.dispatchEventToListeners("RequestActivated",{showPanel:!1}),this.setHoveredNode(null),this.columnsInternal.reset(),this.timeFilter=null,this.calculatorInternal.reset(),this.timeCalculatorInternal.setWindow(null),this.linkifierInternal.reset(),this.activeGroupLookup&&this.activeGroupLookup.reset(),this.staleRequests.clear(),this.resetSuggestionBuilder(),this.mainRequestLoadTime=-1,this.mainRequestDOMContentLoadedTime=-1,this.dataGrid.rootNode().removeChildren(),this.updateSummaryBar(),this.dataGrid.setStickToBottom(!0),this.scheduleRefresh()}setTextFilterValue(e){this.textFilterUI.setValue(e),T.Runtime.experiments.isEnabled("network-panel-filter-bar-redesign")?(this.networkHideDataURLSetting.set(!1),this.networkShowBlockedCookiesOnlySetting.set(!1),this.networkOnlyBlockedRequestsSetting.set(!1),this.networkOnlyThirdPartySetting.set(!1),this.networkHideChromeExtensions.set(!1)):(this.dataURLFilterUI?.setChecked(!1),this.onlyBlockedResponseCookiesFilterUI?.setChecked(!1),this.onlyBlockedRequestsUI?.setChecked(!1),this.onlyThirdPartyFilterUI?.setChecked(!1),this.hideChromeExtensionsUI?.setChecked(!1)),this.resourceCategoryFilterUI.reset()}createNodeForRequest(e){const t=new ke(this,e);Hi.set(e,t),Pi.add(t);for(let t=e.redirectSource();t;t=t.redirectSource())this.refreshRequest(t);return t}isInScope(e){const t=n.NetworkManager.NetworkManager.forRequest(e);return!t||n.TargetManager.TargetManager.instance().isInScope(t)}onRequestUpdated(e){const{request:t,preserveLog:i}=e.data;(this.isInScope(t)||i)&&this.refreshRequest(t)}onRequestRemoved(e){const{request:t}=e.data;this.staleRequests.delete(t);const i=Hi.get(t);i&&this.removeNodeAndMaybeAncestors(i)}refreshRequest(e){Li.subdomains(e.domain).forEach(this.suggestionBuilder.addItem.bind(this.suggestionBuilder,g.UIFilter.FilterType.Domain)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Method,e.requestMethod),this.suggestionBuilder.addItem(g.UIFilter.FilterType.MimeType,e.mimeType),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Scheme,String(e.scheme)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.StatusCode,String(e.statusCode)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.ResourceType,e.resourceType().name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Url,e.securityOrigin());const t=e.priority();if(t&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.Priority,w.NetworkPriorities.uiLabelForNetworkPriority(t)),"none"!==e.mixedContentType&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,"all"),"optionally-blockable"===e.mixedContentType&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,"displayed"),"blockable"===e.mixedContentType){const t=e.wasBlocked()?"blocked":"block-overridden";this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,t)}const i=e.responseHeaders;for(const e of i)this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasResponseHeader,e.name),"Set-Cookie"===e.name&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.ResponseHeaderValueSetCookie);for(const t of e.responseCookies)this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieDomain,t.domain()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieName,t.name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieValue,t.value());for(const t of e.allCookiesIncludingBlockedOnes())this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieDomain,t.domain()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieName,t.name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookiePath,t.path()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieValue,t.value());this.staleRequests.add(e),this.scheduleRefresh()}rowHeight(){return this.rowHeightInternal}switchViewMode(e){this.columnsInternal.switchViewMode(e)}handleContextMenuForRequest(e,i){e.appendApplicableItems(i);const r=this.filterBar.hasActiveFilter(),a=e.clipboardSection().appendSubMenuItem(Ii(xi.copy),!1,"copy");if(i){a.defaultSection().appendItem(Ii(xi.copyURL),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(t.InspectorFrontendHost.InspectorFrontendHostInstance,i.contentURL()),{jslogContext:"copy-url"}),a.footerSection().appendItem(Ii(r?xi.copyAllListedURLs:xi.copyAllURLs),this.copyAllURLs.bind(this),{jslogContext:"copy-all-urls"}),i.requestHeadersText()&&a.saveSection().appendItem(Ii(xi.copyRequestHeaders),Li.copyRequestHeaders.bind(null,i),{jslogContext:"copy-request-headers"}),i.responseHeadersText&&a.saveSection().appendItem(Ii(xi.copyResponseHeaders),Li.copyResponseHeaders.bind(null,i),{jslogContext:"copy-response-headers"}),i.finished&&a.saveSection().appendItem(Ii(xi.copyResponse),Li.copyResponse.bind(null,i),{jslogContext:"copy-response"});const l=i.initiator();if(l){const c=l.stack;if(c){const h=Fi(c);""!==h&&a.saveSection().appendItem(Ii(xi.copyStacktrace),(()=>{t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(h)}),{jslogContext:"copy-stacktrace"})}}const d=i.isBlobRequest();t.Platform.isWin()?(a.defaultSection().appendItem(Ii(xi.copyAsCurlCmd),this.copyCurlCommand.bind(this,i,"win"),{disabled:d,jslogContext:"copy-as-curl-cmd"}),a.defaultSection().appendItem(Ii(xi.copyAsCurlBash),this.copyCurlCommand.bind(this,i,"unix"),{disabled:d,jslogContext:"copy-as-curl-bash"})):a.defaultSection().appendItem(Ii(xi.copyAsCurl),this.copyCurlCommand.bind(this,i,"unix"),{disabled:d,jslogContext:"copy-as-curl"}),a.defaultSection().appendItem(Ii(xi.copyAsPowershell),this.copyPowerShellCommand.bind(this,i),{disabled:d,jslogContext:"copy-as-powershell"}),a.defaultSection().appendItem(Ii(xi.copyAsFetch),this.copyFetchCall.bind(this,i,0),{disabled:d,jslogContext:"copy-as-fetch"}),a.defaultSection().appendItem(Ii(xi.copyAsNodejsFetch),this.copyFetchCall.bind(this,i,1),{disabled:d,jslogContext:"copy-as-nodejs-fetch"}),t.Platform.isWin()?(a.footerSection().appendItem(Ii(r?xi.copyAllListedAsCurlCmd:xi.copyAllAsCurlCmd),this.copyAllCurlCommand.bind(this,"win"),{jslogContext:"copy-all-as-curl-cmd"}),a.footerSection().appendItem(Ii(r?xi.copyAllListedAsCurlBash:xi.copyAllAsCurlBash),this.copyAllCurlCommand.bind(this,"unix"),{jslogContext:"copy-all-as-curl-bash"})):a.footerSection().appendItem(Ii(r?xi.copyAllListedAsCurl:xi.copyAllAsCurl),this.copyAllCurlCommand.bind(this,"unix"),{jslogContext:"copy-all-as-curl"}),a.footerSection().appendItem(Ii(r?xi.copyAllListedAsPowershell:xi.copyAllAsPowershell),this.copyAllPowerShellCommand.bind(this),{jslogContext:"copy-all-as-powershell"}),a.footerSection().appendItem(Ii(r?xi.copyAllListedAsFetch:xi.copyAllAsFetch),this.copyAllFetchCall.bind(this,0),{jslogContext:"copy-all-as-fetch"}),a.footerSection().appendItem(Ii(r?xi.copyAllListedAsNodejsFetch:xi.copyAllAsNodejsFetch),this.copyAllFetchCall.bind(this,1),{jslogContext:"copy-all-as-nodejs-fetch"})}if(a.footerSection().appendItem(Ii(r?xi.copyAllListedAsHar:xi.copyAllAsHar),this.copyAllAsHAR.bind(this),{jslogContext:"copy-all-as-har"}),e.saveSection().appendItem(Ii(xi.saveAllAsHarWithContent),this.exportAll.bind(this),{jslogContext:"save-all-as-har-with-content"}),e.overrideSection().appendItem(Ii(xi.overrideHeaders),this.#t.bind(this,i),{disabled:R.NetworkPersistenceManager.NetworkPersistenceManager.isForbiddenNetworkUrl(i.url()),jslogContext:"override-headers"}),e.editSection().appendItem(Ii(xi.clearBrowserCache),this.clearBrowserCache.bind(this),{jslogContext:"clear-browser-cache"}),e.editSection().appendItem(Ii(xi.clearBrowserCookies),this.clearBrowserCookies.bind(this),{jslogContext:"clear-browser-cookies"}),i){const u=20,p=n.NetworkManager.MultitargetNetworkManager.instance();let g=p.blockedPatterns();function m(e){g.push({enabled:!0,url:e}),p.setBlockedPatterns(g),p.setBlockingEnabled(!0),o.ViewManager.ViewManager.instance().showView("network.blocked-urls")}function w(e){g=g.filter((t=>t.url!==e)),p.setBlockedPatterns(g),o.ViewManager.ViewManager.instance().showView("network.blocked-urls")}const k=i.parsedURL.urlWithoutScheme();if(k&&!g.find((e=>e.url===k)))e.debugSection().appendItem(Ii(xi.blockRequestUrl),m.bind(null,k),{jslogContext:"block-request-url"});else if(k){const v=s.StringUtilities.trimMiddle(k,u);e.debugSection().appendItem(Ii(xi.unblockS,{PH1:v}),w.bind(null,k),{jslogContext:"unblock"})}const b=i.parsedURL.domain();if(b&&!g.find((e=>e.url===b)))e.debugSection().appendItem(Ii(xi.blockRequestDomain),m.bind(null,b),{jslogContext:"block-request-domain"});else if(b){const f=s.StringUtilities.trimMiddle(b,u);e.debugSection().appendItem(Ii(xi.unblockS,{PH1:f}),w.bind(null,b),{jslogContext:"unblock"})}n.NetworkManager.NetworkManager.canReplayRequest(i)&&e.debugSection().appendItem(Ii(xi.replayXhr),n.NetworkManager.NetworkManager.replayRequest.bind(null,i),{jslogContext:"replay-xhr"})}}harRequests(){return a.NetworkLog.NetworkLog.instance().requests().filter((e=>this.applyFilter(e))).filter(Li.getHTTPRequestsFilter).filter((t=>t.finished||t.resourceType()===e.ResourceType.resourceTypes.WebSocket&&t.responseReceivedTime))}async copyAllAsHAR(){const e={log:await x.Log.Log.build(this.harRequests())};t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(JSON.stringify(e,null,2))}copyAllURLs(){const e=a.NetworkLog.NetworkLog.instance().requests().filter((e=>this.applyFilter(e))),i=this.filterOutBlobRequests(e).map((e=>e.url()));t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i.join("\n"))}async copyCurlCommand(e,i){const r=await Li.generateCurlCommand(e,i);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyAllCurlCommand(e){const i=a.NetworkLog.NetworkLog.instance().requests().filter((e=>this.applyFilter(e))),r=await this.generateAllCurlCommand(i,e);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyFetchCall(e,i){const r=await this.generateFetchCall(e,i);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyAllFetchCall(e){const i=a.NetworkLog.NetworkLog.instance().requests().filter((e=>this.applyFilter(e))),r=await this.generateAllFetchCall(i,e);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyPowerShellCommand(e){const i=await this.generatePowerShellCommand(e);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i)}async copyAllPowerShellCommand(){const e=a.NetworkLog.NetworkLog.instance().requests().filter((e=>this.applyFilter(e))),i=await this.generateAllPowerShellCommand(e);t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i)}async exportAll(){const t=n.TargetManager.TargetManager.instance().scopeTarget();if(!t)return;const i=t.inspectedURL(),r=e.ParsedURL.ParsedURL.fromString(i),s=r?r.host:"network-log",a=new u.FileUtils.FileOutputStream;if(!await a.open(e.ParsedURL.ParsedURL.concatenate(s,".har")))return;const l=new o.ProgressIndicator.ProgressIndicator;this.progressBarContainer.appendChild(l.element),await x.Writer.Writer.write(a,this.harRequests(),l),l.done(),a.close()}async#t(t){const i=g.UIRequestLocation.UIRequestLocation.responseHeaderMatch(t,{name:"",value:""}),r=R.NetworkPersistenceManager.NetworkPersistenceManager.instance();r.project()?(e.Settings.Settings.instance().moduleSetting("persistence-network-overrides-enabled").set(!0),await r.getOrCreateHeadersUISourceCodeFromUrl(t.url()),await e.Revealer.reveal(i)):o.InspectorView.InspectorView.instance().displaySelectOverrideFolderInfobar((async()=>{await I.SourcesNavigator.OverridesNavigatorView.instance().setupNewWorkspace(),await r.getOrCreateHeadersUISourceCodeFromUrl(t.url()),await e.Revealer.reveal(i)}))}clearBrowserCache(){confirm(Ii(xi.areYouSureYouWantToClearBrowser))&&n.NetworkManager.MultitargetNetworkManager.instance().clearBrowserCache()}clearBrowserCookies(){confirm(Ii(xi.areYouSureYouWantToClearBrowserCookies))&&n.NetworkManager.MultitargetNetworkManager.instance().clearBrowserCookies()}removeAllHighlights(){this.removeAllNodeHighlights()}applyFilter(e){if(this.timeFilter&&!this.timeFilter(e))return!1;const t=e.resourceType().category().title();if(!this.resourceCategoryFilterUI.accept(t))return!1;const[i,r,o,s,n]=T.Runtime.experiments.isEnabled("network-panel-filter-bar-redesign")?[this.networkHideDataURLSetting.get(),this.networkShowBlockedCookiesOnlySetting.get(),this.networkOnlyBlockedRequestsSetting.get(),this.networkOnlyThirdPartySetting.get(),this.networkHideChromeExtensions.get()]:[this.dataURLFilterUI?.checked(),this.onlyBlockedResponseCookiesFilterUI?.checked(),this.onlyBlockedRequestsUI?.checked(),this.onlyThirdPartyFilterUI?.checked(),this.hideChromeExtensionsUI?.checked()];if(i&&(e.parsedURL.isDataURL()||e.parsedURL.isBlobURL()))return!1;if(r&&!e.blockedResponseCookies().length)return!1;if(o&&!e.wasBlocked()&&!e.corsErrorStatus())return!1;if(s&&e.isSameSite())return!1;if(n&&"chrome-extension"===e.scheme)return!1;for(let t=0;t<this.filters.length;++t)if(!this.filters[t](e))return!1;return!0}isValidUrl(e){try{return new URL(e),!0}catch(e){return!1}}parseFilterQuery(e,t){const i=this.filterParser.parse(e);this.filters=i.map((e=>{const i=e.key,r=e.text||"",o=e.regex;let n;if(i){const e=s.StringUtilities.escapeForRegExp(i+":"+r);n=this.createSpecialFilter(i,r)||Li.requestPathFilter.bind(null,new RegExp(e,"i"))}else n=e.regex?Li.requestPathFilter.bind(null,o):this.isValidUrl(r)?Li.requestUrlFilter.bind(null,r):Li.requestPathFilter.bind(null,new RegExp(s.StringUtilities.escapeForRegExp(r),"i"));return e.negative&&!t||!e.negative&&t?Li.negativeFilter.bind(null,n):n}))}createSpecialFilter(e,t){switch(e){case g.UIFilter.FilterType.Domain:return Li.createRequestDomainFilter(t);case g.UIFilter.FilterType.HasResponseHeader:return Li.requestResponseHeaderFilter.bind(null,t);case g.UIFilter.FilterType.ResponseHeaderValueSetCookie:return Li.requestResponseHeaderSetCookieFilter.bind(null,t);case g.UIFilter.FilterType.Is:if("running"===t.toLowerCase())return Li.runningRequestFilter;if("from-cache"===t.toLowerCase())return Li.fromCacheRequestFilter;if("service-worker-intercepted"===t.toLowerCase())return Li.interceptedByServiceWorkerFilter;if("service-worker-initiated"===t.toLowerCase())return Li.initiatedByServiceWorkerFilter;break;case g.UIFilter.FilterType.LargerThan:return this.createSizeFilter(t.toLowerCase());case g.UIFilter.FilterType.Method:return Li.requestMethodFilter.bind(null,t);case g.UIFilter.FilterType.MimeType:return Li.requestMimeTypeFilter.bind(null,t);case g.UIFilter.FilterType.MixedContent:return Li.requestMixedContentFilter.bind(null,t);case g.UIFilter.FilterType.Scheme:return Li.requestSchemeFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieDomain:return Li.requestSetCookieDomainFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieName:return Li.requestSetCookieNameFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieValue:return Li.requestSetCookieValueFilter.bind(null,t);case g.UIFilter.FilterType.CookieDomain:return Li.requestCookieDomainFilter.bind(null,t);case g.UIFilter.FilterType.CookieName:return Li.requestCookieNameFilter.bind(null,t);case g.UIFilter.FilterType.CookiePath:return Li.requestCookiePathFilter.bind(null,t);case g.UIFilter.FilterType.CookieValue:return Li.requestCookieValueFilter.bind(null,t);case g.UIFilter.FilterType.Priority:return Li.requestPriorityFilter.bind(null,w.NetworkPriorities.uiLabelToNetworkPriority(t));case g.UIFilter.FilterType.StatusCode:return Li.statusCodeFilter.bind(null,t);case g.UIFilter.FilterType.HasOverrides:return Li.hasOverridesFilter.bind(null,t);case g.UIFilter.FilterType.ResourceType:return Li.resourceTypeFilter.bind(null,t);case g.UIFilter.FilterType.Url:return Li.requestUrlFilter.bind(null,t)}return null}createSizeFilter(e){let t=1;e.endsWith("k")?(t=1e3,e=e.substring(0,e.length-1)):e.endsWith("m")&&(t=1e6,e=e.substring(0,e.length-1));const i=Number(e);return isNaN(i)?null:Li.requestSizeLargerThanFilter.bind(null,i*t)}filterRequests(){this.removeAllHighlights(),this.invalidateAllItems()}reveal(e){this.removeAllNodeHighlights();const t=Hi.get(e);return t&&t.dataGrid?(t.parent&&t.parent instanceof be&&(t.parent.reveal(),t.parent.expand()),t.reveal(),t):null}revealAndHighlightRequest(e){const t=this.reveal(e);t&&this.highlightNode(t)}revealAndHighlightRequestWithId(e){const t=a.NetworkLog.NetworkLog.instance().requestByManagerAndId(e.manager,e.requestId);t&&this.revealAndHighlightRequest(t)}selectRequest(e,t){const{clearFilter:i}=t||{clearFilter:!0};i&&this.setTextFilterValue("");const r=this.reveal(e);r&&r.select()}removeAllNodeHighlights(){this.highlightedNode&&(this.highlightedNode.element().classList.remove("highlighted-row"),this.highlightedNode=null)}highlightNode(e){o.UIUtils.runCSSAnimationOnce(e.element(),"highlighted-row"),this.highlightedNode=e}filterOutBlobRequests(e){return e.filter((e=>!e.isBlobRequest()))}async generateFetchCall(e,t){const i=new Set(["method","path","scheme","version","accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via","user-agent"]),r=new Set(["cookie","authorization"]),o=JSON.stringify(e.url()),s=e.requestHeaders(),n=s.reduce(((e,t)=>{const r=t.name;return i.has(r.toLowerCase())||r.includes(":")||e.append(r,t.value),e}),new Headers),a={};for(const e of n)a[e[0]]=e[1];const l=e.includedRequestCookies().length||s.some((({name:e})=>r.has(e.toLowerCase())))?"include":"omit",d=s.find((({name:e})=>"referer"===e.toLowerCase())),c=d?d.value:void 0,h=e.referrerPolicy()||void 0,u=await e.requestFormData(),p={headers:Object.keys(a).length?a:void 0,referrer:c,referrerPolicy:h,body:u,method:e.requestMethod,mode:"cors"};if(1===t){const e=s.find((e=>"cookie"===e.name.toLowerCase())),t={};delete p.mode,e&&(t.cookie=e.value),c&&(delete p.referrer,t.Referer=c),c&&(delete p.referrerPolicy,t["Referrer-Policy"]=h),Object.keys(t).length&&(p.headers={...a,...t})}else p.credentials=l;return`fetch(${o}, ${JSON.stringify(p,null,2)});`}async generateAllFetchCall(e,t){const i=this.filterOutBlobRequests(e);return(await Promise.all(i.map((e=>this.generateFetchCall(e,t))))).join(" ;\n")}static async generateCurlCommand(e,t){let i=[];const r=new Set(["accept-encoding","host","method","path","scheme","version","authority","protocol"]);const o="win"===t?function(e){const t=/[\r\n]|[^a-zA-Z0-9\s_\-:=+~'\/.',?;()*`&]/.test(e)?'^"':'"';return t+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/[^a-zA-Z0-9\s_\-:=+~'\/.',?;()*`&]/g,"^$&").replace(/%(?=[a-zA-Z0-9_])/g,"%^").replace(/\r?\n/g,"^\n\n")+t}:function(e){return/[\0-\x1F\x7F-\x9F!]|\'/.test(e)?"$'"+e.replace(/\\/g,"\\\\").replace(/\'/g,"\\'").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\0-\x1F\x7F-\x9F!]/g,(function(e){let t=e.charCodeAt(0).toString(16);for(;t.length<4;)t="0"+t;return"\\u"+t}))+"'":"'"+e+"'"};i.push(o(e.url()).replace(/[[{}\]]/g,"\\$&"));let s="GET";const n=[],a=await e.requestFormData();a&&(n.push("--data-raw "+o(a)),r.add("content-length"),s="POST"),e.requestMethod!==s&&i.push("-X "+o(e.requestMethod));const l=e.requestHeaders();for(let e=0;e<l.length;e++){const t=l[e],s=t.name.replace(/^:/,"");r.has(s.toLowerCase())||(t.value.trim()?i.push("-H "+o(s+": "+t.value)):i.push("-H "+o(s+";")))}return i=i.concat(n),"insecure"===e.securityState()&&i.push("--insecure"),"curl "+i.join(i.length>=3?"win"===t?" ^\n  ":" \\\n  ":" ")}async generateAllCurlCommand(e,t){const i=this.filterOutBlobRequests(e),r=await Promise.all(i.map((e=>Li.generateCurlCommand(e,t))));return"win"===t?r.join(" &\r\n"):r.join(" ;\n")}async generatePowerShellCommand(e){const t=[],i=new Set(["host","connection","proxy-connection","content-length","expect","range","content-type","user-agent","cookie"]);function r(e){return'"'+e.replace(/[`\$"]/g,"`$&").replace(/[^\x20-\x7E]/g,(e=>"$([char]"+e.charCodeAt(0)+")"))+'"'}t.push("-Uri "+r(e.url())),"GET"!==e.requestMethod&&t.push("-Method "+r(e.requestMethod));const o=function(e){const t=[],i=e.requestHeaders().find((({name:e})=>"user-agent"===e.toLowerCase()));i&&t.push(`$session.UserAgent = ${r(i.value)}`);for(const i of e.includedRequestCookies()){const e=r(i.cookie.name()),o=r(i.cookie.value()),s=r(i.cookie.domain());t.push(`$session.Cookies.Add((New-Object System.Net.Cookie(${e}, ${o}, "/", ${s})))`)}return t.length?"$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession\n"+t.join("\n")+"\n":null}(e);o&&t.push("-WebSession $session");const s=e.requestHeaders(),n=[];for(const e of s){const t=e.name.replace(/^:/,"");i.has(t.toLowerCase())||n.push(r(t)+"="+r(e.value))}n.length&&t.push("-Headers @{\n"+n.join("\n  ")+"\n}");const a=s.find((({name:e})=>"content-type"===e.toLowerCase()));a&&t.push("-ContentType "+r(a.value));const l=await e.requestFormData();if(l){const e=r(l);/[^\x20-\x7E]/.test(l)?t.push("-Body ([System.Text.Encoding]::UTF8.GetBytes("+e+"))"):t.push("-Body "+e)}return(o||"")+"Invoke-WebRequest -UseBasicParsing "+t.join(t.length>=3?" `\n":" ")}async generateAllPowerShellCommand(e){const t=this.filterOutBlobRequests(e);return(await Promise.all(t.map((e=>this.generatePowerShellCommand(e))))).join(";\r\n")}static getDCLEventColor(){return"--sys-color-token-attribute-value"}static getLoadEventColor(){return"--sys-color-token-property-special"}}function Fi(e){let t="";for(const i of e.callFrames){t+=`${o.UIUtils.beautifyFunctionName(i.functionName)} @ ${i.url}:${i.lineNumber+1}\n`}return e.parent&&(t+=Fi(e.parent)),t}const Pi=new WeakSet,Hi=new WeakMap;const Mi={http:!0,https:!0,ws:!0,wss:!0},Ei=Object.values(g.UIFilter.FilterType),Ai={yes:"yes",no:"no",content:"content",headers:"headers"};class Bi extends e.ObjectWrapper.ObjectWrapper{filterElement;dropDownButton;displayedTypes;setting;items;contextMenu;selectedTypesCount;typesCountAdorner;hasChanged=!1;constructor(e,t){super(),this.items=e,this.filterElement=document.createElement("div"),this.filterElement.setAttribute("jslog",`${l.dropDown("request-types").track({click:!0})}`),this.typesCountAdorner=new q.Adorner.Adorner,this.selectedTypesCount=document.createElement("span"),this.typesCountAdorner.data={name:"countWrapper",content:this.selectedTypesCount},this.typesCountAdorner.classList.add("active-filters-count"),this.dropDownButton=new o.Toolbar.ToolbarButton(Ii(xi.requestTypesTooltip),this.typesCountAdorner),this.dropDownButton.setText(Ii(xi.requestTypes)),this.filterElement.appendChild(this.dropDownButton.element),this.dropDownButton.turnIntoSelect(),this.dropDownButton.element.classList.add("dropdown-filterbar"),this.dropDownButton.addEventListener("Click",this.showContextMenu.bind(this)),o.ARIAUtils.markAsMenuButton(this.dropDownButton.element),this.displayedTypes=new Set,this.setting=t,t.addChangeListener(this.settingChanged.bind(this)),this.setting.addChangeListener(this.filterChanged.bind(this)),this.settingChanged()}discard(){this.contextMenu?.discard()}emitUMA(){if(this.hasChanged){t.userMetrics.resourceTypeFilterNumberOfSelectedChanged(this.displayedTypes.size);for(const e of this.displayedTypes)t.userMetrics.resourceTypeFilterItemSelected(e)}}showContextMenu(e){const t=e.data;this.hasChanged=!1,this.contextMenu=new o.ContextMenu.ContextMenu(t,{useSoftMenu:!0,keepOpen:!0,x:this.dropDownButton.element.getBoundingClientRect().left,y:this.dropDownButton.element.getBoundingClientRect().top+this.dropDownButton.element.offsetHeight,onSoftMenuClosed:this.emitUMA.bind(this)}),this.addRequestType(this.contextMenu,Bi.ALL_TYPES,Ii(xi.allStrings)),this.contextMenu.defaultSection().appendSeparator();for(const e of this.items)this.addRequestType(this.contextMenu,e.name,e.name);this.update(),this.contextMenu.show()}addRequestType(e,t,i){const r=t.toLowerCase().replace(/\s/g,"-");e.defaultSection().appendCheckboxItem(i,(()=>{this.setting.get()[t]=!this.setting.get()[t],this.toggleTypeFilter(t)}),{checked:this.setting.get()[t],jslogContext:r})}toggleTypeFilter(e){e!==Bi.ALL_TYPES?this.displayedTypes.delete(Bi.ALL_TYPES):this.displayedTypes=new Set,this.displayedTypes.has(e)?this.displayedTypes.delete(e):this.displayedTypes.add(e),0===this.displayedTypes.size&&this.displayedTypes.add(Bi.ALL_TYPES);const t={};for(const e of this.displayedTypes)t[e]=!0;this.setting.set(t);const i=this.contextMenu?.getItems()||[];for(const e of i)e.label&&this.contextMenu?.setChecked(e,this.displayedTypes.has(e.label));this.contextMenu?.setChecked(i[0],this.displayedTypes.has("all"))}filterChanged(){this.dispatchEventToListeners("FilterChanged")}settingChanged(){this.hasChanged=!0,this.displayedTypes=new Set;for(const e in this.setting.get())this.displayedTypes.add(e);this.update()}update(){(0===this.displayedTypes.size||this.displayedTypes.has(Bi.ALL_TYPES))&&(this.displayedTypes=new Set,this.displayedTypes.add(Bi.ALL_TYPES)),this.updateSelectedTypesCount(),this.updateLabel(),this.updateTooltip()}updateSelectedTypesCount(){this.displayedTypes.has(Bi.ALL_TYPES)?this.typesCountAdorner.classList.add("hidden"):(this.selectedTypesCount.textContent=this.displayedTypes.size.toString(),this.typesCountAdorner.classList.remove("hidden"))}updateLabel(){if(this.displayedTypes.has(Bi.ALL_TYPES))return void this.dropDownButton.setText(Ii(xi.requestTypes));let t;if(1===this.displayedTypes.size){const i=this.displayedTypes.values().next().value;t=e.ResourceType.ResourceCategory.categoryByTitle(i)?.shortTitle()||""}else{const i=[...this.displayedTypes].slice(-2).reverse().map((t=>e.ResourceType.ResourceCategory.categoryByTitle(t)?.shortTitle()||"")),r={PH1:i[0],PH2:i[1]};t=2===this.displayedTypes.size?Ii(xi.twoTypesSelected,r):Ii(xi.overTwoTypesSelected,r)}this.dropDownButton.setText(t)}updateTooltip(){let t=Ii(xi.requestTypesTooltip);if(!this.displayedTypes.has(Bi.ALL_TYPES)){const i=[...this.displayedTypes].reverse().map((t=>e.ResourceType.ResourceCategory.categoryByTitle(t)?.title()||"")).join(", ");t=Ii(xi.showOnly,{PH1:i})}this.dropDownButton.setTitle(t)}isActive(){return!this.displayedTypes.has(Bi.ALL_TYPES)}element(){return this.filterElement}reset(){this.toggleTypeFilter(Bi.ALL_TYPES)}accept(e){return this.displayedTypes.has(Bi.ALL_TYPES)||this.displayedTypes.has(e)}static ALL_TYPES="all"}class Ui extends e.ObjectWrapper.ObjectWrapper{filterElement;dropDownButton;networkHideDataURLSetting;networkHideChromeExtensionsSetting;networkShowBlockedCookiesOnlySetting;networkOnlyBlockedRequestsSetting;networkOnlyThirdPartySetting;contextMenu;activeFiltersCount;activeFiltersCountAdorner;hasChanged=!1;constructor(){super(),this.networkHideDataURLSetting=e.Settings.Settings.instance().createSetting("network-hide-data-url",!1),this.networkHideChromeExtensionsSetting=e.Settings.Settings.instance().createSetting("network-hide-chrome-extensions",!1),this.networkShowBlockedCookiesOnlySetting=e.Settings.Settings.instance().createSetting("network-show-blocked-cookies-only-setting",!1),this.networkOnlyBlockedRequestsSetting=e.Settings.Settings.instance().createSetting("network-only-blocked-requests",!1),this.networkOnlyThirdPartySetting=e.Settings.Settings.instance().createSetting("network-only-third-party-setting",!1),this.filterElement=document.createElement("div"),this.filterElement.setAttribute("aria-label","Show only/hide requests dropdown"),this.filterElement.setAttribute("jslog",`${l.dropDown("more-filters").track({click:!0})}`),this.activeFiltersCountAdorner=new q.Adorner.Adorner,this.activeFiltersCount=document.createElement("span"),this.activeFiltersCountAdorner.data={name:"countWrapper",content:this.activeFiltersCount},this.activeFiltersCountAdorner.classList.add("active-filters-count"),this.updateActiveFiltersCount(),this.dropDownButton=new o.Toolbar.ToolbarButton(Ii(xi.showOnlyHideRequests),this.activeFiltersCountAdorner,Ii(xi.moreFilters)),this.filterElement.appendChild(this.dropDownButton.element),this.dropDownButton.turnIntoSelect(),this.dropDownButton.element.classList.add("dropdown-filterbar"),this.dropDownButton.addEventListener("Click",this.showMoreFiltersContextMenu.bind(this)),o.ARIAUtils.markAsMenuButton(this.dropDownButton.element),this.updateTooltip()}emitUMA(){if(this.hasChanged){const e=this.selectedFilters();t.userMetrics.networkPanelMoreFiltersNumberOfSelectedChanged(e.length);for(const i of e)t.userMetrics.networkPanelMoreFiltersItemSelected(i)}}#i(){this.hasChanged=!0,this.dispatchEventToListeners("FilterChanged")}showMoreFiltersContextMenu(e){const t=e.data;this.hasChanged=!1,this.networkHideDataURLSetting.addChangeListener(this.#i.bind(this)),this.networkHideChromeExtensionsSetting.addChangeListener(this.#i.bind(this)),this.networkShowBlockedCookiesOnlySetting.addChangeListener(this.#i.bind(this)),this.networkOnlyBlockedRequestsSetting.addChangeListener(this.#i.bind(this)),this.networkOnlyThirdPartySetting.addChangeListener(this.#i.bind(this)),this.contextMenu=new o.ContextMenu.ContextMenu(t,{useSoftMenu:!0,keepOpen:!0,x:this.dropDownButton.element.getBoundingClientRect().left,y:this.dropDownButton.element.getBoundingClientRect().top+this.dropDownButton.element.offsetHeight,onSoftMenuClosed:this.emitUMA.bind(this)}),this.contextMenu.defaultSection().appendCheckboxItem(Ii(xi.hideDataUrls),(()=>this.networkHideDataURLSetting.set(!this.networkHideDataURLSetting.get())),{checked:this.networkHideDataURLSetting.get(),tooltip:Ii(xi.hidesDataAndBlobUrls),jslogContext:"hide-data-urls"}),this.contextMenu.defaultSection().appendCheckboxItem(Ii(xi.chromeExtensions),(()=>this.networkHideChromeExtensionsSetting.set(!this.networkHideChromeExtensionsSetting.get())),{checked:this.networkHideChromeExtensionsSetting.get(),tooltip:Ii(xi.hideChromeExtension),jslogContext:"hide-extension-urls"}),this.contextMenu.defaultSection().appendSeparator(),this.contextMenu.defaultSection().appendCheckboxItem(Ii(xi.hasBlockedCookies),(()=>this.networkShowBlockedCookiesOnlySetting.set(!this.networkShowBlockedCookiesOnlySetting.get())),{checked:this.networkShowBlockedCookiesOnlySetting.get(),tooltip:Ii(xi.onlyShowRequestsWithBlockedCookies),jslogContext:"only-blocked-response-cookies"}),this.contextMenu.defaultSection().appendCheckboxItem(Ii(xi.blockedRequests),(()=>this.networkOnlyBlockedRequestsSetting.set(!this.networkOnlyBlockedRequestsSetting.get())),{checked:this.networkOnlyBlockedRequestsSetting.get(),tooltip:Ii(xi.onlyShowBlockedRequests),jslogContext:"only-blocked-requests"}),this.contextMenu.defaultSection().appendCheckboxItem(Ii(xi.thirdParty),(()=>this.networkOnlyThirdPartySetting.set(!this.networkOnlyThirdPartySetting.get())),{checked:this.networkOnlyThirdPartySetting.get(),tooltip:Ii(xi.onlyShowThirdPartyRequests),jslogContext:"only-3rd-party-requests"}),this.contextMenu.show()}selectedFilters(){return[...this.networkHideDataURLSetting.get()?[Ii(xi.hideDataUrls)]:[],...this.networkHideChromeExtensionsSetting.get()?[Ii(xi.chromeExtensions)]:[],...this.networkShowBlockedCookiesOnlySetting.get()?[Ii(xi.hasBlockedCookies)]:[],...this.networkOnlyBlockedRequestsSetting.get()?[Ii(xi.blockedRequests)]:[],...this.networkOnlyThirdPartySetting.get()?[Ii(xi.thirdParty)]:[]]}updateActiveFiltersCount(){const e=this.selectedFilters().length;this.activeFiltersCount.textContent=e.toString(),e?this.activeFiltersCountAdorner.classList.remove("hidden"):this.activeFiltersCountAdorner.classList.add("hidden")}updateTooltip(){this.selectedFilters().length?this.dropDownButton.setTitle(this.selectedFilters().join(", ")):this.dropDownButton.setTitle(xi.showOnlyHideRequests)}discard(){this.contextMenu&&this.contextMenu.discard()}isActive(){return 0!==this.selectedFilters().length}element(){return this.filterElement}}var Ni=Object.freeze({__proto__:null,NetworkLogView:Li,computeStackTraceText:Fi,isRequestFilteredOut:function(e){return Pi.has(e)},HTTPSchemas:Mi,overrideFilter:Ai,DropDownTypesUI:Bi,MoreFiltersDropDownUI:Ui});const Vi={url:"URL"},Oi=i.i18n.registerUIStrings("panels/network/NetworkSearchScope.ts",Vi),Wi=i.i18n.getLocalizedString.bind(void 0,Oi);class Di{#r;constructor(e){this.#r=e}performIndexing(e){queueMicrotask((()=>{e.done()}))}async performSearch(e,t,i,r){const o=[],s=this.#r.requests().filter((t=>e.filePathMatchesFileQuery(t.url())));t.setTotalWork(s.length);for(const i of s){const r=this.searchRequest(e,i,t);o.push(r)}const n=(await Promise.all(o)).filter((e=>null!==e));if(t.isCanceled())r(!1);else{for(const e of n.sort(((e,t)=>e.label().localeCompare(t.label()))))e.matchesCount()>0&&i(e);t.done(),r(!0)}}async searchRequest(e,t,i){const r=await Di.#o(e,t);if(i.isCanceled())return null;const o=[];a(t.url())&&o.push(g.UIRequestLocation.UIRequestLocation.urlMatch(t));for(const e of t.requestHeaders())n(e)&&o.push(g.UIRequestLocation.UIRequestLocation.requestHeaderMatch(t,e));for(const e of t.responseHeaders)n(e)&&o.push(g.UIRequestLocation.UIRequestLocation.responseHeaderMatch(t,e));for(const e of r)o.push(g.UIRequestLocation.UIRequestLocation.bodyMatch(t,e));return i.incrementWorked(),new ji(t,o);function n(e){return a(`${e.name}: ${e.value}`)}function a(t){const i=e.ignoreCase()?"i":"",r=e.queries().map((e=>new RegExp(s.StringUtilities.escapeForRegExp(e),i)));let o=0;for(const e of r){const i=t.substr(o).match(e);if(!i||void 0===i.index)return!1;o+=i.index+i[0].length}return!0}}static async#o(e,t){if(!t.contentType().isTextType())return[];let i=[];for(const r of e.queries()){const o=await t.searchInContent(r,!e.ignoreCase(),e.isRegex());if(0===o.length)return[];i=s.ArrayUtilities.mergeOrdered(i,o,y.ContentProvider.SearchMatch.comparator)}return i}stopSearch(){}}class ji{request;locations;constructor(e,t){this.request=e,this.locations=t}matchesCount(){return this.locations.length}label(){return this.request.displayName}description(){const e=this.request.parsedURL;return e?e.urlWithoutScheme():this.request.url()}matchLineContent(e){const t=this.locations[e];if(t.isUrlMatch)return this.request.url();const i=t?.header?.header;return i?i.value:t.searchMatch.lineContent}matchRevealable(e){return this.locations[e]}matchLabel(e){const t=this.locations[e];if(t.isUrlMatch)return Wi(Vi.url);const i=t?.header?.header;return i?`${i.name}:`:(t.searchMatch.lineNumber+1).toString()}matchColumn(e){const t=this.locations[e];return t.searchMatch?.columnNumber}matchLength(e){const t=this.locations[e];return t.searchMatch?.matchLength}}var Gi=Object.freeze({__proto__:null,NetworkSearchScope:Di,NetworkSearchResult:ji});const zi=new CSSStyleSheet;zi.replaceSync(":root{--network-overview-total:var(--sys-color-surface-variant);--network-overview-blocking:var(--sys-color-neutral-bright);--network-overview-connecting:var(--ref-palette-yellow60);--network-overview-service-worker:var(--sys-color-orange-bright);--network-overview-service-worker-respond-with:var(--sys-color-cyan-bright);--network-overview-push:var(--ref-palette-blue60);--override-network-overview-proxy:var(--ref-palette-neutral60);--network-overview-dns:var(--sys-color-cyan);--network-overview-ssl:var(--ref-palette-purple70);--override-network-overview-sending:var(--ref-palette-cyan60);--network-overview-waiting:var(--ref-palette-green70);--network-overview-receiving:var(--ref-palette-blue60);--network-overview-queueing:var(--ref-palette-neutral100);--network-grid-navigation-color:var(--ref-palette-blue90);--network-grid-initiator-path-color:var(--ref-palette-green90);--network-grid-initiated-path-color:var(--ref-palette-error80);--network-grid-focus-selected-color-has-error:var(--sys-color-error-container);--network-grid-from-frame-color:var(--ref-palette-cyan95);--network-grid-is-product-color:var(--ref-palette-yellow95);--network-frame-divider-color:var(--ref-palette-yellow60)}.theme-with-dark-background{--network-grid-initiator-path-color:var(--ref-palette-green40);--network-grid-initiated-path-color:var(--ref-palette-error20);--network-grid-from-frame-color:var(--ref-palette-neutral50);--network-grid-is-product-color:var(--ref-palette-neutral70)}.network-details-view{background:var(--app-color-toolbar-background)}.network-details-view-tall-header{border-top:4px solid var(--app-color-toolbar-background)}.network-item-view{display:flex;background:var(--sys-color-cdt-base-container)}.network-item-preview-toolbar{border-top:1px solid var(--sys-color-divider);background-color:var(--sys-color-surface1)}.resource-timing-view{display:block;margin:6px;color:var(--sys-color-on-surface);overflow:auto;background-color:var(--sys-color-cdt-base-container)}.resource-timing-table{width:100%!important}#network-overview-panel{flex:none;position:relative}#network-overview-container{overflow:hidden;flex:auto;display:flex;flex-direction:column;position:relative;border-bottom:1px solid var(--sys-color-divider)}#network-overview-container canvas{width:100%;height:100%}.resources-dividers-label-bar{background-color:var(--sys-color-cdt-base-container)}#network-overview-grid .resources-dividers-label-bar{pointer-events:auto}.network .network-overview{flex:0 0 60px}.network-overview .resources-dividers-label-bar .resources-divider{background-color:transparent}.network-overview .resources-dividers{z-index:250}.request-view.html iframe{width:100%;height:100%;position:absolute}.network-film-strip{border-bottom:solid 1px var(--sys-color-divider);flex:none!important}.network-film-strip-placeholder{flex-shrink:0}.network-tabbed-pane{background-color:var(--sys-color-cdt-base-container)}.network-settings-pane{flex:none;background-color:var(--sys-color-cdt-base-container)}.network-settings-pane .toolbar{flex:1 1}.network-toolbar-container{display:flex;flex:none}.network-toolbar-container > :first-child{flex:1 1 auto}.panel.network .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}@media (forced-colors: active){.panel.network .toolbar{background-color:canvas}}devtools-request-headers{min-width:360px}\n/*# sourceURL=networkPanel.css */\n");const _i={close:"Close",search:"Search",doNotClearLogOnPageReload:"Do not clear log on page reload / navigation",preserveLog:"Preserve log",disableCacheWhileDevtoolsIsOpen:"Disable cache (while DevTools is open)",disableCache:"Disable cache",networkSettings:"Network settings",showMoreInformationInRequestRows:"Show more information in request rows",useLargeRequestRows:"Big request rows",showOverviewOfNetworkRequests:"Show overview of network requests",showOverview:"Overview",groupRequestsByTopLevelRequest:"Group requests by top level request frame",groupByFrame:"Group by frame",captureScreenshotsWhenLoadingA:"Capture screenshots when loading a page",captureScreenshots:"Screenshots",importHarFile:"Import `HAR` file...",exportHar:"Export `HAR`...",throttling:"Throttling",hitSToReloadAndCaptureFilmstrip:"Hit {PH1} to reload and capture filmstrip.",revealInNetworkPanel:"Reveal in Network panel",recordingFrames:"Recording frames...",fetchingFrames:"Fetching frames...",moreNetworkConditions:"More network conditions…"},Ki=i.i18n.registerUIStrings("panels/network/NetworkPanel.ts",_i),$i=i.i18n.getLocalizedString.bind(void 0,Ki);let Xi,Yi;class Ji extends o.Panel.Panel{networkLogShowOverviewSetting;networkLogLargeRowsSetting;networkRecordFilmStripSetting;toggleRecordAction;pendingStopTimer;networkItemView;filmStripView;filmStripRecorder;currentRequest;panelToolbar;rightToolbar;filterBar;settingsPane;showSettingsPaneSetting;filmStripPlaceholderElement;overviewPane;networkOverview;overviewPlaceholderElement;calculator;splitWidget;sidebarLocation;progressBarContainer;networkLogView;fileSelectorElement;detailsWidget;closeButtonElement;preserveLogSetting;recordLogSetting;throttlingSelect;displayScreenshotDelay;constructor(t){super("network"),this.displayScreenshotDelay=t,this.networkLogShowOverviewSetting=e.Settings.Settings.instance().createSetting("network-log-show-overview",!0),this.networkLogLargeRowsSetting=e.Settings.Settings.instance().createSetting("network-log-large-rows",!1),this.networkRecordFilmStripSetting=e.Settings.Settings.instance().createSetting("network-record-film-strip-setting",!1),this.toggleRecordAction=o.ActionRegistry.ActionRegistry.instance().getAction("network.toggle-recording"),this.networkItemView=null,this.filmStripView=null,this.filmStripRecorder=null,this.currentRequest=null;const i=new o.Widget.VBox,r=i.contentElement.createChild("div","network-toolbar-container");this.panelToolbar=new o.Toolbar.Toolbar("",r),this.panelToolbar.makeWrappable(!0),this.panelToolbar.element.setAttribute("jslog",`${l.toolbar("network-main")}`),this.rightToolbar=new o.Toolbar.Toolbar("",r),this.filterBar=new o.FilterBar.FilterBar("network-panel",!0),this.filterBar.show(i.contentElement),this.filterBar.addEventListener("Changed",this.handleFilterChanged.bind(this)),this.settingsPane=new o.Widget.HBox,this.settingsPane.element.classList.add("network-settings-pane"),this.settingsPane.show(i.contentElement),this.showSettingsPaneSetting=e.Settings.Settings.instance().createSetting("network-show-settings-toolbar",!1),this.showSettingsPaneSetting.addChangeListener(this.updateSettingsPaneVisibility.bind(this)),this.updateSettingsPaneVisibility(),this.filmStripPlaceholderElement=i.contentElement.createChild("div","network-film-strip-placeholder"),this.overviewPane=new w.TimelineOverviewPane.TimelineOverviewPane("network"),this.overviewPane.addEventListener("OverviewPaneWindowChanged",this.onWindowChanged.bind(this)),this.overviewPane.element.id="network-overview-panel",this.overviewPane.element.setAttribute("jslog",`${l.pane("network-overview").track({click:!0,drag:!0})}`),this.networkOverview=new oi,this.overviewPane.setOverviewControls([this.networkOverview]),this.overviewPlaceholderElement=i.contentElement.createChild("div"),this.calculator=new jt,this.splitWidget=new o.SplitWidget.SplitWidget(!0,!1,"network-panel-split-view-state"),this.splitWidget.hideMain(),this.splitWidget.show(i.contentElement),i.setDefaultFocusedChild(this.filterBar);const d=new o.SplitWidget.SplitWidget(!0,!1,"network-panel-sidebar-state",225);d.hideSidebar(),d.enableShowModeSaving(),d.show(this.element),this.sidebarLocation=o.ViewManager.ViewManager.instance().createTabbedLocation((async()=>{o.ViewManager.ViewManager.instance().showView("network"),d.showBoth()}),"network-sidebar",!0);const c=this.sidebarLocation.tabbedPane();c.setMinimumSize(100,25),c.element.classList.add("network-tabbed-pane"),c.element.addEventListener("keydown",(e=>{e.key===s.KeyboardUtilities.ESCAPE_KEY&&(d.hideSidebar(),e.consume(),l.logKeyDown(e.currentTarget,e,"hide-sidebar"))}));const h=new o.Toolbar.ToolbarButton($i(_i.close),"cross");h.addEventListener("Click",(()=>d.hideSidebar())),h.element.setAttribute("jslog",`${l.close().track({click:!0})}`),c.rightToolbar().appendToolbarItem(h),d.setSidebarWidget(c),d.setMainWidget(i),d.setDefaultFocusedChild(i),this.setDefaultFocusedChild(d),this.progressBarContainer=document.createElement("div"),this.networkLogView=new Li(this.filterBar,this.progressBarContainer,this.networkLogLargeRowsSetting),this.splitWidget.setSidebarWidget(this.networkLogView),this.fileSelectorElement=o.UIUtils.createFileSelectorElement(this.networkLogView.onLoadFromFile.bind(this.networkLogView)),i.element.appendChild(this.fileSelectorElement),this.detailsWidget=new o.Widget.VBox,this.detailsWidget.element.classList.add("network-details-view"),this.splitWidget.setMainWidget(this.detailsWidget),this.closeButtonElement=document.createElement("div",{is:"dt-close-button"}),this.closeButtonElement.addEventListener("click",(async()=>{const e=o.ActionRegistry.ActionRegistry.instance().getAction("network.hide-request-details");await e.execute()}),!1),this.closeButtonElement.style.margin="0 5px",this.networkLogShowOverviewSetting.addChangeListener(this.toggleShowOverview,this),this.networkLogLargeRowsSetting.addChangeListener(this.toggleLargerRequests,this),this.networkRecordFilmStripSetting.addChangeListener(this.toggleRecordFilmStrip,this),this.preserveLogSetting=e.Settings.Settings.instance().moduleSetting("network-log.preserve-log"),this.recordLogSetting=e.Settings.Settings.instance().moduleSetting("network-log.record-log"),this.recordLogSetting.addChangeListener((({data:e})=>this.toggleRecord(e))),this.throttlingSelect=this.createThrottlingConditionsSelect(),this.setupToolbarButtons(d),this.toggleRecord(this.recordLogSetting.get()),this.toggleShowOverview(),this.toggleLargerRequests(),this.toggleRecordFilmStrip(),this.updateUI(),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.WillReloadPage,this.willReloadPage,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.Load,this.load,this,{scoped:!0}),this.networkLogView.addEventListener("RequestSelected",this.onRequestSelected,this),this.networkLogView.addEventListener("RequestActivated",this.onRequestActivated,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.RequestAdded,this.onUpdateRequest,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.RequestUpdated,this.onUpdateRequest,this),a.NetworkLog.NetworkLog.instance().addEventListener(a.NetworkLog.Events.Reset,this.onNetworkLogReset,this)}static instance(e){return Xi&&!e?.forceNew||(Xi=new Ji(e?.displayScreenshotDelay??1e3)),Xi}static revealAndFilter(e){const t=Ji.instance();let i="";for(const t of e)t.filterType?i+=`${t.filterType}:${t.filterValue} `:i+=`${t.filterValue} `;return t.networkLogView.setTextFilterValue(i),o.ViewManager.ViewManager.instance().showView("network")}static async selectAndShowRequest(e,t,i){const r=Ji.instance();await r.selectAndActivateRequest(e,t,i)}throttlingSelectForTest(){return this.throttlingSelect}onWindowChanged(e){const t=Math.max(this.calculator.minimumBoundary(),e.data.startTime/1e3),i=Math.min(this.calculator.maximumBoundary(),e.data.endTime/1e3);t===this.calculator.minimumBoundary()&&i===this.calculator.maximumBoundary()?this.networkLogView.setWindow(0,0):this.networkLogView.setWindow(t,i)}async searchToggleClick(){const e=o.ActionRegistry.ActionRegistry.instance().getAction("network.search");await e.execute()}setupToolbarButtons(t){const i=new o.Toolbar.ToolbarToggle($i(_i.search),"search",void 0,"search");function r(){const e="OnlyMain"!==t.showMode();i.setToggled(e),e||i.element.focus()}this.panelToolbar.appendToolbarItem(o.Toolbar.Toolbar.createActionButton(this.toggleRecordAction)),this.panelToolbar.appendToolbarItem(o.Toolbar.Toolbar.createActionButtonForId("network.clear")),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.filterBar.filterButton()),r(),t.addEventListener("ShowModeChanged",r),i.addEventListener("Click",(()=>{this.searchToggleClick()})),this.panelToolbar.appendToolbarItem(i),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(new o.Toolbar.ToolbarSettingCheckbox(this.preserveLogSetting,$i(_i.doNotClearLogOnPageReload),$i(_i.preserveLog))),this.panelToolbar.appendSeparator();const s=new o.Toolbar.ToolbarSettingCheckbox(e.Settings.Settings.instance().moduleSetting("cache-disabled"),$i(_i.disableCacheWhileDevtoolsIsOpen),$i(_i.disableCache));this.panelToolbar.appendToolbarItem(s),this.panelToolbar.appendToolbarItem(this.throttlingSelect);const n=new o.Toolbar.ToolbarButton($i(_i.moreNetworkConditions),"network-settings",void 0,"network-conditions");n.addEventListener("Click",(()=>{o.ViewManager.ViewManager.instance().showView("network.config")}),this),this.panelToolbar.appendToolbarItem(n),this.rightToolbar.appendToolbarItem(new o.Toolbar.ToolbarItem(this.progressBarContainer)),this.rightToolbar.appendSeparator(),this.rightToolbar.appendToolbarItem(new o.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"gear",$i(_i.networkSettings),"gear-filled","network-settings"));const a=new o.Toolbar.Toolbar("",this.settingsPane.element);a.makeVertical(),a.appendToolbarItem(new o.Toolbar.ToolbarSettingCheckbox(this.networkLogLargeRowsSetting,$i(_i.showMoreInformationInRequestRows),$i(_i.useLargeRequestRows))),a.appendToolbarItem(new o.Toolbar.ToolbarSettingCheckbox(this.networkLogShowOverviewSetting,$i(_i.showOverviewOfNetworkRequests),$i(_i.showOverview)));const l=new o.Toolbar.Toolbar("",this.settingsPane.element);l.makeVertical(),l.appendToolbarItem(new o.Toolbar.ToolbarSettingCheckbox(e.Settings.Settings.instance().moduleSetting("network.group-by-frame"),$i(_i.groupRequestsByTopLevelRequest),$i(_i.groupByFrame))),l.appendToolbarItem(new o.Toolbar.ToolbarSettingCheckbox(this.networkRecordFilmStripSetting,$i(_i.captureScreenshotsWhenLoadingA),$i(_i.captureScreenshots))),this.panelToolbar.appendSeparator();const d=new o.Toolbar.ToolbarButton($i(_i.importHarFile),"import",void 0,"import-har");d.addEventListener("Click",(()=>this.fileSelectorElement.click()),this),this.panelToolbar.appendToolbarItem(d);const c=new o.Toolbar.ToolbarButton($i(_i.exportHar),"download",void 0,"export-har");c.addEventListener("Click",(e=>{this.networkLogView.exportAll()}),this),this.panelToolbar.appendToolbarItem(c)}updateSettingsPaneVisibility(){this.settingsPane.element.classList.toggle("hidden",!this.showSettingsPaneSetting.get())}createThrottlingConditionsSelect(){const e=new o.Toolbar.ToolbarComboBox(null,$i(_i.throttling));return e.setMaxWidth(160),c.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(e.selectElement()),e}toggleRecord(e){this.toggleRecordAction.setToggled(e),this.recordLogSetting.get()!==e&&this.recordLogSetting.set(e),this.networkLogView.setRecording(e),!e&&this.filmStripRecorder&&this.filmStripRecorder.stopRecording(this.filmStripAvailable.bind(this))}filmStripAvailable(e){this.filmStripView&&this.filmStripView.setModel(e);const t=e.frames.map((e=>P.Helpers.Timing.microSecondsToSeconds(e.screenshotEvent.ts)));this.networkLogView.addFilmStripFrames(t)}onNetworkLogReset(e){const{clearIfPreserved:t}=e.data;this.preserveLogSetting.get()&&!t||(this.calculator.reset(),this.overviewPane.reset()),this.filmStripView&&this.resetFilmStripView()}willReloadPage(){this.pendingStopTimer&&(clearTimeout(this.pendingStopTimer),delete this.pendingStopTimer),this.isShowing()&&this.filmStripRecorder&&this.filmStripRecorder.startRecording()}load(){this.filmStripRecorder&&this.filmStripRecorder.isRecording()&&(this.pendingStopTimer&&window.clearTimeout(this.pendingStopTimer),this.pendingStopTimer=window.setTimeout(this.stopFilmStripRecording.bind(this),this.displayScreenshotDelay))}stopFilmStripRecording(){this.filmStripRecorder&&this.filmStripRecorder.stopRecording(this.filmStripAvailable.bind(this)),delete this.pendingStopTimer}toggleLargerRequests(){this.updateUI()}toggleShowOverview(){this.networkLogShowOverviewSetting.get()?this.overviewPane.show(this.overviewPlaceholderElement):this.overviewPane.detach(),this.doResize()}toggleRecordFilmStrip(){const e=this.networkRecordFilmStripSetting.get();e&&!this.filmStripRecorder&&(this.filmStripView=new w.FilmStripView.FilmStripView,this.filmStripView.element.classList.add("network-film-strip"),this.filmStripView.element.setAttribute("jslog",`${l.pane("network-film-strip")}`),this.filmStripRecorder=new Zi(this.networkLogView.timeCalculator(),this.filmStripView),this.filmStripView.show(this.filmStripPlaceholderElement),this.filmStripView.addEventListener("FrameSelected",this.onFilmFrameSelected,this),this.filmStripView.addEventListener("FrameEnter",this.onFilmFrameEnter,this),this.filmStripView.addEventListener("FrameExit",this.onFilmFrameExit,this),this.resetFilmStripView()),!e&&this.filmStripRecorder&&(this.filmStripView&&this.filmStripView.detach(),this.filmStripView=null,this.filmStripRecorder=null)}resetFilmStripView(){const e=o.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("inspector-main.reload")[0];this.filmStripView&&(this.filmStripView.reset(),e&&this.filmStripView.setStatusText($i(_i.hitSToReloadAndCaptureFilmstrip,{PH1:e.title()})))}elementsToRestoreScrollPositionsFor(){return this.networkLogView.elementsToRestoreScrollPositionsFor()}wasShown(){o.Context.Context.instance().setFlavor(Ji,this),this.registerCSSFiles([zi]),t.userMetrics.panelLoaded("network","DevTools.Launch.Network")}willHide(){o.Context.Context.instance().setFlavor(Ji,null)}revealAndHighlightRequest(e){this.hideRequestPanel(),e&&this.networkLogView.revealAndHighlightRequest(e)}revealAndHighlightRequestWithId(e){this.hideRequestPanel(),e&&this.networkLogView.revealAndHighlightRequestWithId(e)}async selectAndActivateRequest(e,t,i){return await o.ViewManager.ViewManager.instance().showView("network"),this.networkLogView.selectRequest(e,i),this.showRequestPanel(t),this.networkLogView.revealAndHighlightRequest(e),this.networkItemView}handleFilterChanged(){this.hideRequestPanel()}onRowSizeChanged(){this.updateUI()}onRequestSelected(e){const t=e.data;this.currentRequest=t,this.networkOverview.setHighlightedRequest(t),this.updateNetworkItemView()}onRequestActivated(e){const{showPanel:t,tab:i,takeFocus:r}=e.data;t?this.showRequestPanel(i,r):this.hideRequestPanel()}showRequestPanel(e,t){if("Both"!==this.splitWidget.showMode()||e||t){if(this.clearNetworkItemView(),this.currentRequest){const i=this.createNetworkItemView(e);i&&t&&i.focus()}this.updateUI()}}hideRequestPanel(){this.clearNetworkItemView(),this.splitWidget.hideMain(),this.updateUI()}updateNetworkItemView(){"Both"===this.splitWidget.showMode()&&(this.clearNetworkItemView(),this.createNetworkItemView(),this.updateUI())}clearNetworkItemView(){this.networkItemView&&(this.networkItemView.detach(),this.networkItemView=null)}createNetworkItemView(e){if(this.currentRequest)return this.networkItemView=new At(this.currentRequest,this.networkLogView.timeCalculator(),e),this.networkItemView.leftToolbar().appendToolbarItem(new o.Toolbar.ToolbarItem(this.closeButtonElement)),this.networkItemView.show(this.detailsWidget.element),this.splitWidget.showBoth(),this.networkItemView}updateUI(){this.detailsWidget&&this.detailsWidget.element.classList.toggle("network-details-view-tall-header",this.networkLogLargeRowsSetting.get()),this.networkLogView&&this.networkLogView.switchViewMode(!this.splitWidget.isResizable())}appendApplicableItems(e,t,i){const r=e=>{t.revealSection().appendItem($i(_i.revealInNetworkPanel),(()=>o.ViewManager.ViewManager.instance().showView("network").then(this.networkLogView.resetFilter.bind(this.networkLogView)).then(this.revealAndHighlightRequest.bind(this,e))),{jslogContext:"reveal-in-network"})};if(!e.target.isSelfOrDescendant(this.element))if(i instanceof n.Resource.Resource)i.request&&r(i.request);else if(i instanceof p.UISourceCode.UISourceCode){const e=u.ResourceUtils.resourceForURL(i.url());e&&e.request&&r(e.request)}else i instanceof M.NetworkRequest.TimelineNetworkRequest?i.request&&r(i.request):this.networkItemView&&this.networkItemView.isShowing()&&this.networkItemView.request()===i||r(i)}onFilmFrameSelected(e){const t=e.data;this.overviewPane.setWindowTimes(0,t)}onFilmFrameEnter(e){const t=e.data;this.networkOverview.selectFilmStripFrame(t),this.networkLogView.selectFilmStripFrame(t/1e3)}onFilmFrameExit(){this.networkOverview.clearFilmStripFrame(),this.networkLogView.clearFilmStripFrame()}onUpdateRequest(e){const{request:t}=e.data;this.calculator.updateBoundaries(t),this.overviewPane.setBounds(P.Types.Timing.MilliSeconds(1e3*this.calculator.minimumBoundary()),P.Types.Timing.MilliSeconds(1e3*this.calculator.maximumBoundary())),this.networkOverview.updateRequest(t)}resolveLocation(e){return"network-sidebar"===e?this.sidebarLocation:null}}class Zi{tracingManager;resourceTreeModel;timeCalculator;filmStripView;callback;#s;#n=[];constructor(e,t){this.#s=P.TraceModel.Model.createWithSubsetOfHandlers({Screenshots:P.Handlers.ModelHandlers.Screenshots}),this.tracingManager=null,this.resourceTreeModel=null,this.timeCalculator=e,this.filmStripView=t,this.callback=null}traceEventsCollected(e){this.#n.push(...e)}async tracingComplete(){if(!this.tracingManager)return;this.tracingManager=null,await this.#s.parse(this.#n);const e=this.#s.traceParsedData(this.#s.size()-1);if(!e)return;const t=P.Types.Timing.Seconds(this.timeCalculator.minimumBoundary()),i=P.Extras.FilmStrip.fromTraceData(e,P.Helpers.Timing.secondsToMicroseconds(t));this.callback&&this.callback(i),this.callback=null,this.#s.resetProcessor(),this.resourceTreeModel&&this.resourceTreeModel.resumeReload(),this.resourceTreeModel=null}tracingBufferUsage(){}eventsRetrievalProgress(e){}startRecording(){this.#n=[],this.filmStripView.reset(),this.filmStripView.setStatusText($i(_i.recordingFrames));const e=n.TargetManager.TargetManager.instance().scopeTarget()?.model(P.TracingManager.TracingManager);!this.tracingManager&&e&&(this.tracingManager=e,this.resourceTreeModel=this.tracingManager.target().model(n.ResourceTreeModel.ResourceTreeModel),this.tracingManager.start(this,"-*,disabled-by-default-devtools.screenshot",""),t.userMetrics.actionTaken(t.UserMetrics.Action.FilmStripStartedRecording))}isRecording(){return Boolean(this.tracingManager)}stopRecording(e){this.tracingManager&&(this.tracingManager.stop(),this.resourceTreeModel&&this.resourceTreeModel.suspendReload(),this.callback=e,this.filmStripView.setStatusText($i(_i.fetchingFrames)))}}class Qi extends H.SearchView.SearchView{constructor(){super("network",new e.Throttler.Throttler(200))}static instance(e={forceNew:null}){const{forceNew:t}=e;return Yi&&!t||(Yi=new Qi),Yi}static async openSearch(e,t){await o.ViewManager.ViewManager.instance().showView("network.search-network-tab");const i=Qi.instance();return i.toggle(e,Boolean(t)),i}createScope(){return new Di(a.NetworkLog.NetworkLog.instance())}}var er=Object.freeze({__proto__:null,NetworkPanel:Ji,RequestRevealer:class{reveal(e){const t=Ji.instance();return o.ViewManager.ViewManager.instance().showView("network").then(t.revealAndHighlightRequest.bind(t,e))}},RequestIdRevealer:class{reveal(e){const t=Ji.instance();return o.ViewManager.ViewManager.instance().showView("network").then(t.revealAndHighlightRequestWithId.bind(t,e))}},NetworkLogWithFilterRevealer:class{reveal(e){return"filters"in e?Ji.revealAndFilter(e.filters):Ji.revealAndFilter(e.filter?[{filterType:null,filterValue:e.filter}]:[])}},FilmStripRecorder:Zi,ActionDelegate:class{handleAction(e,t){const i=e.flavor(Ji);if(null===i)return!1;switch(t){case"network.toggle-recording":return i.toggleRecord(!i.recordLogSetting.get()),!0;case"network.hide-request-details":return!!i.networkItemView&&(i.hideRequestPanel(),i.networkLogView.resetFocus(),!0);case"network.search":{const e=o.InspectorView.InspectorView.instance().element.window().getSelection();if(!e)return!1;let t="";return e.rangeCount&&(t=e.toString().replace(/\r?\n.*/,"")),Qi.openSearch(t),!0}case"network.clear":return a.NetworkLog.NetworkLog.instance().reset(!0),!0}return!1}},RequestLocationRevealer:class{async reveal(e){const t=await Ji.instance().selectAndActivateRequest(e.request,e.tab,e.filterOptions);if(t){if(e.searchMatch){const{lineNumber:i,columnNumber:r,matchLength:o}=e.searchMatch,s={from:{lineNumber:i,columnNumber:r},to:{lineNumber:i,columnNumber:r+o}};await t.revealResponseBody(s)}e.header&&t.revealHeader(e.header.section,e.header.header?.name)}}},SearchNetworkView:Qi});export{O as BinaryResourceView,_ as BlockedURLsPane,ie as EventSourceMessagesView,he as NetworkConfigView,ve as NetworkDataGridNode,$t as NetworkFrameGrouper,Bt as NetworkItemView,Ni as NetworkLogView,Ti as NetworkLogViewColumns,ti as NetworkManageCustomHeadersView,ci as NetworkOverview,er as NetworkPanel,Gi as NetworkSearchScope,zt as NetworkTimeCalculator,pi as NetworkWaterfallColumn,xe as RequestCookiesView,_e as RequestHTMLView,He as RequestInitiatorView,je as RequestPayloadView,ht as RequestPreviewView,Ze as RequestResponseView,vt as RequestTimingView,Pt as ResourceWebSocketFrameView,nt as SignedExchangeInfoView};
