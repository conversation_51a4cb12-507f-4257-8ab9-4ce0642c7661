{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "allowFontScaling", "backImage", "label", "labelStyle", "labelVisible", "Platform", "OS", "onLabelLayout", "onPress", "pressColor", "pressOpacity", "screenLayout", "tintColor", "customTintColor", "titleLayout", "truncatedLabel", "accessibilityLabel", "testID", "style", "colors", "useTheme", "initial<PERSON><PERSON><PERSON><PERSON>", "setInitialLabel<PERSON>", "React", "useState", "undefined", "select", "ios", "primary", "default", "text", "handleLabelLayout", "e", "nativeEvent", "layout", "x", "width", "shouldTruncateLabel", "renderBackImage", "styles", "icon", "Boolean", "iconWithLabel", "require", "renderLabel", "leftLabelText", "labelElement", "labelWrapper", "min<PERSON><PERSON><PERSON>", "color", "iconMaskContainer", "iconMask", "iconMaskFillerRect", "handlePress", "requestAnimationFrame", "androidRipple", "container", "top", "right", "bottom", "left", "borderless", "foreground", "Version", "radius", "StyleSheet", "create", "alignItems", "flexDirection", "hairlineWidth", "marginVertical", "marginHorizontal", "opacity", "fontSize", "letterSpacing", "height", "marginLeft", "marginRight", "resizeMode", "transform", "scaleX", "I18nManager", "getConstants", "isRTL", "margin", "flex", "justifyContent", "backgroundColor", "alignSelf"], "sourceRoot": "../../../src", "sources": ["Header/HeaderBackButton.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AAUA;AACA;AAAqD;AAAA;AAAA;AAGtC,SAASA,gBAAgB,OAkBd;EAAA,IAlBe;IACvCC,QAAQ;IACRC,gBAAgB;IAChBC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;IACpCC,aAAa;IACbC,OAAO;IACPC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZC,SAAS,EAAEC,eAAe;IAC1BC,WAAW;IACXC,cAAc,GAAG,MAAM;IACvBC,kBAAkB,GAAGd,KAAK,IAAIA,KAAK,KAAK,MAAM,GAAI,GAAEA,KAAM,QAAO,GAAG,SAAS;IAC7Ee,MAAM;IACNC;EACqB,CAAC;EACtB,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAE7B,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAE9DC,SAAS,CAAC;EAEZ,MAAMb,SAAS,GACbC,eAAe,KAAKY,SAAS,GACzBZ,eAAe,GACfR,qBAAQ,CAACqB,MAAM,CAAC;IACdC,GAAG,EAAER,MAAM,CAACS,OAAO;IACnBC,OAAO,EAAEV,MAAM,CAACW;EAClB,CAAC,CAAC;EAER,MAAMC,iBAAiB,GAAIC,CAAoB,IAAK;IAClDzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGyB,CAAC,CAAC;IAElBV,oBAAoB,CAACU,CAAC,CAACC,WAAW,CAACC,MAAM,CAACC,CAAC,GAAGH,CAAC,CAACC,WAAW,CAACC,MAAM,CAACE,KAAK,CAAC;EAC3E,CAAC;EAED,MAAMC,mBAAmB,GAAG,MAAM;IAChC,OACE,CAACnC,KAAK,IACLmB,iBAAiB,IAChBP,WAAW,IACXH,YAAY,IACZ,CAACA,YAAY,CAACyB,KAAK,GAAGtB,WAAW,CAACsB,KAAK,IAAI,CAAC,GAAGf,iBAAiB,GAAG,EAAG;EAE5E,CAAC;EAED,MAAMiB,eAAe,GAAG,MAAM;IAC5B,IAAIrC,SAAS,EAAE;MACb,OAAOA,SAAS,CAAC;QAAEW;MAAU,CAAC,CAAC;IACjC,CAAC,MAAM;MACL,oBACE,oBAAC,kBAAK;QACJ,KAAK,EAAE,CACL2B,MAAM,CAACC,IAAI,EACXC,OAAO,CAACrC,YAAY,CAAC,IAAImC,MAAM,CAACG,aAAa,EAC7CD,OAAO,CAAC7B,SAAS,CAAC,IAAI;UAAEA;QAAU,CAAC,CACnC;QACF,MAAM,EAAE+B,OAAO,CAAC,yBAAyB,CAAE;QAC3C,YAAY,EAAE;MAAE,EAChB;IAEN;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAM;IACxB,MAAMC,aAAa,GAAGR,mBAAmB,EAAE,GAAGtB,cAAc,GAAGb,KAAK;IAEpE,IAAI,CAACE,YAAY,IAAIyC,aAAa,KAAKpB,SAAS,EAAE;MAChD,OAAO,IAAI;IACb;IAEA,MAAMqB,YAAY,gBAChB,oBAAC,iBAAI;MACH,KAAK,EACHnC,YAAY;MACR;MACA;MACA,CAAC4B,MAAM,CAACQ,YAAY,EAAE;QAAEC,QAAQ,EAAErC,YAAY,CAACyB,KAAK,GAAG,CAAC,GAAG;MAAG,CAAC,CAAC,GAChE;IACL,gBAED,oBAAC,qBAAQ,CAAC,IAAI;MACZ,UAAU,EAAE,KAAM;MAClB,QAAQ;MACN;MACA;MACAS,aAAa,KAAK3C,KAAK,GAAG6B,iBAAiB,GAAGN,SAC/C;MACD,KAAK,EAAE,CACLc,MAAM,CAACrC,KAAK,EACZU,SAAS,GAAG;QAAEqC,KAAK,EAAErC;MAAU,CAAC,GAAG,IAAI,EACvCT,UAAU,CACV;MACF,aAAa,EAAE,CAAE;MACjB,gBAAgB,EAAE,CAAC,CAACH;IAAiB,GAEpC6C,aAAa,CACA,CAEnB;IAED,IAAI5C,SAAS,IAAII,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACtC;MACA;MACA,OAAOwC,YAAY;IACrB;IAEA,oBACE,oBAAC,mBAAU;MACT,WAAW,eACT,oBAAC,iBAAI;QAAC,KAAK,EAAEP,MAAM,CAACW;MAAkB,gBACpC,oBAAC,kBAAK;QACJ,MAAM,EAAEP,OAAO,CAAC,8BAA8B,CAAE;QAChD,KAAK,EAAEJ,MAAM,CAACY;MAAS,EACvB,eACF,oBAAC,iBAAI;QAAC,KAAK,EAAEZ,MAAM,CAACa;MAAmB,EAAG;IAE7C,GAEAN,YAAY,CACF;EAEjB,CAAC;EAED,MAAMO,WAAW,GAAG,MAAM7C,OAAO,IAAI8C,qBAAqB,CAAC9C,OAAO,CAAC;EAEnE,oBACE,oBAAC,0BAAiB;IAChB,QAAQ,EAAET,QAAS;IACnB,UAAU;IACV,iBAAiB,EAAC,QAAQ;IAC1B,kBAAkB,EAAEiB,kBAAmB;IACvC,MAAM,EAAEC,MAAO;IACf,OAAO,EAAElB,QAAQ,GAAG0B,SAAS,GAAG4B,WAAY;IAC5C,UAAU,EAAE5C,UAAW;IACvB,YAAY,EAAEC,YAAa;IAC3B,cAAc,EAAE6C,aAAc;IAC9B,KAAK,EAAE,CAAChB,MAAM,CAACiB,SAAS,EAAEzD,QAAQ,IAAIwC,MAAM,CAACxC,QAAQ,EAAEmB,KAAK,CAAE;IAC9D,OAAO,EAAEb,qBAAQ,CAACqB,MAAM,CAAC;MACvBC,GAAG,EAAEF,SAAS;MACdI,OAAO,EAAE;QAAE4B,GAAG,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG;IACtD,CAAC;EAAE,gBAEH,oBAAC,KAAK,CAAC,QAAQ,QACZtB,eAAe,EAAE,EACjBM,WAAW,EAAE,CACC,CACC;AAExB;AAEA,MAAMW,aAAa,GAAG;EACpBM,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAEzD,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAAC0D,OAAO,IAAI,EAAE;EAC/DC,MAAM,EAAE;AACV,CAAC;AAED,MAAMzB,MAAM,GAAG0B,uBAAU,CAACC,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,UAAU,EAAE,QAAQ;IACpBC,aAAa,EAAE,KAAK;IACpBpB,QAAQ,EAAEiB,uBAAU,CAACI,aAAa;IAAE;IACpC,GAAGhE,qBAAQ,CAACqB,MAAM,CAAC;MACjBC,GAAG,EAAE,IAAI;MACTE,OAAO,EAAE;QACPyC,cAAc,EAAE,CAAC;QACjBC,gBAAgB,EAAE;MACpB;IACF,CAAC;EACH,CAAC;EACDxE,QAAQ,EAAE;IACRyE,OAAO,EAAE;EACX,CAAC;EACDtE,KAAK,EAAE;IACLuE,QAAQ,EAAE,EAAE;IACZ;IACA;IACAC,aAAa,EAAE;EACjB,CAAC;EACD3B,YAAY,EAAE;IACZ;IACA;IACAqB,aAAa,EAAE,KAAK;IACpBD,UAAU,EAAE;EACd,CAAC;EACD3B,IAAI,EAAEnC,qBAAQ,CAACqB,MAAM,CAAC;IACpBC,GAAG,EAAE;MACHgD,MAAM,EAAE,EAAE;MACVvC,KAAK,EAAE,EAAE;MACTwC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,EAAE;MACfP,cAAc,EAAE,EAAE;MAClBQ,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEC,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC;IACnE,CAAC;IACDtD,OAAO,EAAE;MACP8C,MAAM,EAAE,EAAE;MACVvC,KAAK,EAAE,EAAE;MACTgD,MAAM,EAAE,CAAC;MACTN,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEC,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC;IACnE;EACF,CAAC,CAAC;EACFzC,aAAa,EACXrC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjB;IACEuE,WAAW,EAAE;EACf,CAAC,GACD,CAAC,CAAC;EACR3B,iBAAiB,EAAE;IACjBmC,IAAI,EAAE,CAAC;IACPjB,aAAa,EAAE,KAAK;IACpBkB,cAAc,EAAE;EAClB,CAAC;EACDlC,kBAAkB,EAAE;IAClBiC,IAAI,EAAE,CAAC;IACPE,eAAe,EAAE;EACnB,CAAC;EACDpC,QAAQ,EAAE;IACRwB,MAAM,EAAE,EAAE;IACVvC,KAAK,EAAE,EAAE;IACTwC,UAAU,EAAE,CAAC,IAAI;IACjBN,cAAc,EAAE,EAAE;IAClBkB,SAAS,EAAE,QAAQ;IACnBV,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,CAAC;MAAEC,MAAM,EAAEC,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG;IAAE,CAAC;EACnE;AACF,CAAC,CAAC"}