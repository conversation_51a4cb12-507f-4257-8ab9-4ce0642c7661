{"version": 3, "file": "NativeRequest.d.ts", "sourceRoot": "", "sources": ["../../../src/winter/fetch/NativeRequest.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEtD,MAAM,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;AAEnD,MAAM,CAAC,OAAO,OAAO,aAAc,SAAQ,YAAY;IAC9C,KAAK,CACV,GAAG,EAAE,MAAM,EACX,WAAW,EAAE,iBAAiB,EAC9B,WAAW,EAAE,UAAU,GAAG,IAAI,GAC7B,OAAO,CAAC,cAAc,CAAC;IACnB,MAAM,IAAI,IAAI;CACtB;AAED,MAAM,WAAW,iBAAiB;IAChC,WAAW,CAAC,EAAE,kBAAkB,CAAC;IACjC,OAAO,CAAC,EAAE,iBAAiB,CAAC;IAC5B,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,MAAM,oBAAoB,GAAG;IACjC,sBAAsB,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC;IAC/C,WAAW,IAAI,IAAI,CAAC;IACpB,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACtC,sBAAsB,IAAI,IAAI,CAAC;CAChC,CAAC;AAEF,MAAM,CAAC,OAAO,OAAO,cAAe,SAAQ,YAAY,CAAC,oBAAoB,CAAC;IAC5E,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC3B,QAAQ,CAAC,WAAW,EAAE,iBAAiB,CAAC;IACxC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;IAC7B,cAAc,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAC5C,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IACrC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC;IACnC,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC;CACxB"}