{"version": 3, "file": "urql-exchange-retry.mjs", "sources": ["../src/retryExchange.ts"], "sourcesContent": null, "names": ["retryExchange", "options", "retryIf", "retryWith", "MIN_DELAY", "initialDelayMs", "MAX_DELAY", "max<PERSON>elay<PERSON>", "MAX_ATTEMPTS", "maxNumberAttempts", "RANDOM_DELAY", "randomDelay", "forward", "dispatchDebug", "operations$", "source", "retry$", "next", "nextRetryOperation", "makeSubject", "retryWithBackoff$", "mergeMap", "operation", "retry", "context", "count", "delay", "retryCount", "delayAmount", "backoffFactor", "Math", "random", "min", "teardown$", "filter", "op", "kind", "key", "process", "env", "NODE_ENV", "type", "message", "data", "takeUntil", "debounce", "fromValue", "makeOperation", "res", "error", "networkError", "merge"], "mappings": ";;;;AA8GO,IAAMA,gBAAgBA,CAACC,IAAgC;EAC5D,KAAMC,SAAEA,GAAOC,WAAEA,KAAcF;EAC/B,IAAMG,IAAYH,EAAQI,kBAAkB;EAC5C,IAAMC,IAAYL,EAAQM,cAAc;EACxC,IAAMC,IAAeP,EAAQQ,qBAAqB;EAClD,IAAMC,IACmB,QAAvBT,EAAQU,gBAAwBV,EAAQU,eAAc;EAExD,OAAO,EAAGC,YAASC,sBACjBC;IACE,KAAQC,QAAQC,GAAQC,MAAMC,KAC5BC;IAEF,IAAMC,IAEJC,GAAUC;MACR,IAAMC,IAAoBD,EAAUE,QAAQD,SAAS;QACnDE,OAAO;QACPC,OAAO;;MAGT,IAAMC,MAAeJ,EAAME;MAC3B,IAAIG,IAAcL,EAAMG,SAAStB;MAEjC,IAAMyB,IAAgBC,KAAKC,WAAW;MACtC,IAAIrB;QAGF,IAAIkB,IAAcC,IAAgBvB;UAChCsB,KAAeC;;UAEfD,IAActB;;;QAIhBsB,IAAcE,KAAKE,IAAIL,IAAavB,GAAWE;;MAIjDiB,EAAMG,QAAQE;MAKd,IAAMK,IAEJC,GAAOC,MAEU,YAAZA,EAAGC,QAAgC,eAAZD,EAAGC,SAC3BD,EAAGE,QAAQf,EAAUe,KAHzBH,CADApB;MASF,iBAAAwB,QAAAC,IAAAC,YAAA3B,EAAc;QACZ4B,MAAM;QACNC,SAAS,4DAA4Df,OAAgBnB;QACrFc;QACAqB,MAAM;UACJhB;UACAC;;QACDb,QAAA;;MAIH,OASE6B,EAAUX,EATZ,CAOEY,GAAS,MAAMjB,GAAfiB,CANAC,EACEC,EAAczB,EAAUc,MAAMd,GAAW;WACpCA,EAAUE;QACbD;;AAEH,OAxDLF,CADAL;IAiEF,OAGEkB,GAAOc;MACL,IAAMzB,IAAQyB,EAAI1B,UAAUE,QAAQD;MAGpC,MACGyB,EAAIC,UACJ/C,IACIA,EAAQ8C,EAAIC,OAAOD,EAAI1B,aACvBnB,KAAc6C,EAAIC,MAAMC,gBAC7B;QAEA,IAAI3B,GAAO;UACTA,EAAME,QAAQ;UACdF,EAAMG,QAAQ;AAChB;QACA,QAAO;AACT;MAIA,OADIH,KAASA,EAAME,SAAU,MAAMjB,IAAe,IAClB;QAC9B,IAAMc,IAAYnB,IACdA,EAAU6C,EAAIC,OAAOD,EAAI1B,aACzB0B,EAAI1B;QACR,KAAKA;UAAW,QAAO;;QAIvBJ,EAAmBI;QACnB,QAAO;AACT;MAEA,iBAAAgB,QAAAC,IAAAC,YAAA3B,EAAc;QACZ4B,MAAM;QACNC,SACE;QACFpB,WAAW0B,EAAI1B;QAASP,QAAA;;MAG1B,QAAO;AAAI,OAvCbmB,CADAtB,EADAuC,EAAM,EAACrC,GAAaM;AAAmB;AA4C1C;;"}