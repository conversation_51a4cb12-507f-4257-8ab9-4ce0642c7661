{"version": 3, "names": ["React", "UNINTIALIZED_STATE", "useSyncState", "initialState", "stateRef", "useRef", "isSchedulingRef", "isMountedRef", "useEffect", "current", "trackingState", "setTrackingState", "useState", "getState", "useCallback", "setState", "state", "scheduleUpdate", "callback", "flushUpdates", "useDebugValue"], "sourceRoot": "../../src", "sources": ["useSyncState.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,kBAAkB,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACA,eAAe,SAASC,YAAY,CAAIC,YAA4B,EAAE;EACpE,MAAMC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAAIJ,kBAAkB,CAAQ;EAC3D,MAAMK,eAAe,GAAGN,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EAC3C,MAAME,YAAY,GAAGP,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;EAEvCL,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpBD,YAAY,CAACE,OAAO,GAAG,IAAI;IAE3B,OAAO,MAAM;MACXF,YAAY,CAACE,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIL,QAAQ,CAACK,OAAO,KAAKR,kBAAkB,EAAE;IAC3CG,QAAQ,CAACK,OAAO;IACd;IACA,OAAON,YAAY,KAAK,UAAU,GAAGA,YAAY,EAAE,GAAGA,YAAY;EACtE;EAEA,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAACR,QAAQ,CAACK,OAAO,CAAC;EAE1E,MAAMI,QAAQ,GAAGb,KAAK,CAACc,WAAW,CAAC,MAAMV,QAAQ,CAACK,OAAO,EAAE,EAAE,CAAC;EAE9D,MAAMM,QAAQ,GAAGf,KAAK,CAACc,WAAW,CAAEE,KAAQ,IAAK;IAC/C,IAAIA,KAAK,KAAKZ,QAAQ,CAACK,OAAO,IAAI,CAACF,YAAY,CAACE,OAAO,EAAE;MACvD;IACF;IAEAL,QAAQ,CAACK,OAAO,GAAGO,KAAK;IAExB,IAAI,CAACV,eAAe,CAACG,OAAO,EAAE;MAC5BE,gBAAgB,CAACK,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGjB,KAAK,CAACc,WAAW,CAAEI,QAAoB,IAAK;IACjEZ,eAAe,CAACG,OAAO,GAAG,IAAI;IAE9B,IAAI;MACFS,QAAQ,EAAE;IACZ,CAAC,SAAS;MACRZ,eAAe,CAACG,OAAO,GAAG,KAAK;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,YAAY,GAAGnB,KAAK,CAACc,WAAW,CAAC,MAAM;IAC3C,IAAI,CAACP,YAAY,CAACE,OAAO,EAAE;MACzB;IACF;;IAEA;IACA;IACAE,gBAAgB,CAACP,QAAQ,CAACK,OAAO,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,IAAIC,aAAa,KAAKN,QAAQ,CAACK,OAAO,EAAE;IACtCE,gBAAgB,CAACP,QAAQ,CAACK,OAAO,CAAC;EACpC;EAEA,MAAMO,KAAK,GAAGZ,QAAQ,CAACK,OAAO;EAE9BT,KAAK,CAACoB,aAAa,CAACJ,KAAK,CAAC;EAE1B,OAAO,CAACA,KAAK,EAAEH,QAAQ,EAAEE,QAAQ,EAAEE,cAAc,EAAEE,YAAY,CAAC;AAClE"}