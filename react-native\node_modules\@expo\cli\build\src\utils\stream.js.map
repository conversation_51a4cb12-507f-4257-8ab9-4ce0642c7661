{"version": 3, "sources": ["../../../src/utils/stream.ts"], "sourcesContent": ["export async function streamToStringAsync(stream: ReadableStream): Promise<string> {\n  const decoder = new TextDecoder();\n  const reader = stream.getReader();\n  const outs: string[] = [];\n  let result: ReadableStreamReadResult<unknown>;\n  do {\n    result = await reader.read();\n    if (result.value) {\n      if (!(result.value instanceof Uint8Array)) {\n        throw new Error('Unexepected buffer type');\n      }\n      outs.push(decoder.decode(result.value, { stream: true }));\n    }\n  } while (!result.done);\n  outs.push(decoder.decode());\n  return outs.join('');\n}\n"], "names": ["streamToStringAsync", "stream", "decoder", "TextDecoder", "reader", "<PERSON><PERSON><PERSON><PERSON>", "outs", "result", "read", "value", "Uint8Array", "Error", "push", "decode", "done", "join"], "mappings": ";;;;+BAAsBA;;;eAAAA;;;AAAf,eAAeA,oBAAoBC,MAAsB;IAC9D,MAAMC,UAAU,IAAIC;IACpB,MAAMC,SAASH,OAAOI,SAAS;IAC/B,MAAMC,OAAiB,EAAE;IACzB,IAAIC;IACJ,GAAG;QACDA,SAAS,MAAMH,OAAOI,IAAI;QAC1B,IAAID,OAAOE,KAAK,EAAE;YAChB,IAAI,CAAEF,CAAAA,OAAOE,KAAK,YAAYC,UAAS,GAAI;gBACzC,MAAM,IAAIC,MAAM;YAClB;YACAL,KAAKM,IAAI,CAACV,QAAQW,MAAM,CAACN,OAAOE,KAAK,EAAE;gBAAER,QAAQ;YAAK;QACxD;IACF,QAAS,CAACM,OAAOO,IAAI,EAAE;IACvBR,KAAKM,IAAI,CAACV,QAAQW,MAAM;IACxB,OAAOP,KAAKS,IAAI,CAAC;AACnB"}