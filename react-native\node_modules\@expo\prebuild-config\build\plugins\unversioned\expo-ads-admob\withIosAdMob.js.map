{"version": 3, "file": "withIosAdMob.js", "names": ["_configPlugins", "data", "require", "withIosAdMob", "config", "withInfoPlist", "modResults", "setAdMobConfig", "exports", "getGoogleMobileAdsAppId", "ios", "googleMobileAdsAppId", "setGoogleMobileAdsAppId", "GADApplicationIdentifier", "infoPlist", "appId"], "sources": ["../../../../src/plugins/unversioned/expo-ads-admob/withIosAdMob.ts"], "sourcesContent": ["import { ConfigPlugin, InfoPlist, withInfoPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nexport const withIosAdMob: ConfigPlugin = (config) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setAdMobConfig(config, config.modResults);\n    return config;\n  });\n};\n\n// NOTE(brentvatne): if the developer has installed the google ads sdk and does\n// not provide an app id their app will crash. Standalone apps get around this by\n// providing some default value, we will instead here assume that the user can\n// do the right thing if they have installed the package. This is a slight discrepancy\n// that arises in ejecting because it's possible for the package to be installed and\n// not crashing in the managed workflow, then you eject and the app crashes because\n// you don't have an id to fall back to.\nexport function getGoogleMobileAdsAppId(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.config?.googleMobileAdsAppId ?? null;\n}\n\nexport function setGoogleMobileAdsAppId(\n  config: Pick<ExpoConfig, 'ios'>,\n  { GADApplicationIdentifier, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const appId = getGoogleMobileAdsAppId(config);\n\n  if (appId === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    GADApplicationIdentifier: appId,\n  };\n}\n\nfunction setAdMobConfig(config: Pick<ExpoConfig, 'ios'>, infoPlist: InfoPlist): InfoPlist {\n  infoPlist = setGoogleMobileAdsAppId(config, infoPlist);\n  return infoPlist;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,YAA0B,GAAIC,MAAM,IAAK;EACpD,OAAO,IAAAC,8BAAa,EAACD,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACE,UAAU,GAAGC,cAAc,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AAAAI,OAAA,CAAAL,YAAA,GAAAA,YAAA;AACO,SAASM,uBAAuBA,CAACL,MAA+B,EAAE;EACvE,OAAOA,MAAM,CAACM,GAAG,EAAEN,MAAM,EAAEO,oBAAoB,IAAI,IAAI;AACzD;AAEO,SAASC,uBAAuBA,CACrCR,MAA+B,EAC/B;EAAES,wBAAwB;EAAE,GAAGC;AAAqB,CAAC,EAC1C;EACX,MAAMC,KAAK,GAAGN,uBAAuB,CAACL,MAAM,CAAC;EAE7C,IAAIW,KAAK,KAAK,IAAI,EAAE;IAClB,OAAOD,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,wBAAwB,EAAEE;EAC5B,CAAC;AACH;AAEA,SAASR,cAAcA,CAACH,MAA+B,EAAEU,SAAoB,EAAa;EACxFA,SAAS,GAAGF,uBAAuB,CAACR,MAAM,EAAEU,SAAS,CAAC;EACtD,OAAOA,SAAS;AAClB", "ignoreList": []}