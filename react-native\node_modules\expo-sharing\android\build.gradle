apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '13.0.1'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.sharing"
  defaultConfig {
    versionCode 16
    versionName '13.0.1'
  }
}

dependencies {
  api "androidx.legacy:legacy-support-v4:1.0.0"
}
