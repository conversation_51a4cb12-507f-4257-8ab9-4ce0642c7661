const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Configuration directe de la base de données (plus simple)
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

console.log('🚀 Démarrage du serveur contrats sur la base "Facutration"...');

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    console.log('🔄 Test de connexion à la base de données "Facutration"...');
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');

    // Test simple
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${result.rows[0].count} clients dans la table Client`);6

    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    return false;
  }
}

// Fonction pour récupérer tous les clients
async function getAllClients() {
  try {
    console.log('📥 Récupération de tous les clients depuis la table Client');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    console.log(`✅ ${result.rows.length} clients récupérés depuis la table Client`);

    return {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    };

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    throw error;
  }
}

// Fonction CRITIQUE pour récupérer les contrats d'un client spécifique
async function getClientContracts(clientId) {
  try {
    console.log(`🎯 Récupération des contrats pour le client ID: ${clientId}`);

    // Vérifier d'abord si le client existe
    const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [clientId]);
    if (clientCheck.rows.length === 0) {
      console.log(`❌ Client ID ${clientId} n'existe pas dans la base`);
      return {
        success: false,
        message: `Client ID ${clientId} non trouvé`,
        client_id: parseInt(clientId)
      };
    }

    const clientInfo = clientCheck.rows[0];
    console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

    // Requête pour récupérer les contrats depuis la table Contract
    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL pour les contrats...');
    const result = await pool.query(query, [clientId]);

    console.log(`📊 RÉSULTAT: ${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`);

    if (result.rows.length > 0) {
      console.log('📋 CONTRATS TROUVÉS:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
        console.log(`      Client: ${contract.nom} ${contract.prenom}`);
        console.log(`      Date contrat: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}`);
      });
    } else {
      console.log('⚠️ AUCUN CONTRAT TROUVÉ pour ce client');
    }

    return {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`,
      client_id: parseInt(clientId),
      client_name: `${clientInfo.nom} ${clientInfo.prenom}`
    };

  } catch (error) {
    console.error('❌ ERREUR lors de la récupération des contrats:', error.message);
    throw error;
  }
}

// Fonction pour récupérer la dernière consommation d'un contrat
async function getLastConsommation(contractId) {
  try {
    console.log(`📥 Récupération de la dernière consommation pour le contrat ${contractId}`);

    const query = `
      SELECT
        consommationactuelle,
        periode,
        jours
      FROM consommation
      WHERE idcont = $1
      ORDER BY periode DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [contractId]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      return {
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      };
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${contractId}`);
      return {
        success: false,
        message: 'Aucune consommation précédente trouvée'
      };
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    throw error;
  }
}

// Middleware
app.use(cors());
app.use(express.json());

// Middleware de logging détaillé
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`\n📥 ${timestamp}`);
  console.log(`🔗 ${req.method} ${req.url}`);
  console.log('─'.repeat(50));
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée - Serveur fonctionnel');
  res.json({
    message: 'Serveur contrats fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 GET /api/clients - Récupération de tous les clients');
    
    const result = await getAllClients();
    
    console.log('📤 Envoi de la réponse clients');
    res.json(result);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// 🎯 ROUTE CRITIQUE : Récupérer les contrats d'un client spécifique
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: GET /api/clients/${id}/contracts`);
    
    const result = await getClientContracts(id);
    
    if (result.success) {
      console.log('📤 ENVOI DE LA RÉPONSE CONTRATS');
      res.json(result);
    } else {
      console.log('❌ Client non trouvé');
      res.status(404).json(result);
    }

  } catch (error) {
    console.error('❌ ERREUR CRITIQUE lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message,
      client_id: parseInt(req.params.id)
    });
  }
});

// Route pour récupérer la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 GET /api/contracts/${id}/last-consommation`);

    const result = await getLastConsommation(id);
    res.json(result);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route de connexion (pour l'authentification)
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Pour le test, accepter <EMAIL> / Tech123
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion réussie pour:', email);
      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          idtech: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: email,
          role: 'Tech'
        },
        redirectTo: '/technician-dashboard'
      });
    } else {
      console.log('❌ Identifiants incorrects pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

  } catch (err) {
    console.error('Erreur lors de la connexion:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: err.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur non gérée:', err.message);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  try {
    console.log('🚀 Démarrage du serveur contrats...');
    // Tester la connexion à la base de données
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      console.error('❌ Impossible de se connecter à la base de données');
      console.error('❌ Vérifiez que PostgreSQL est démarré et que la base "Facutration" existe');
      process.exit(1);
    }
    app.listen(PORT, () => {
      console.log(`\n🚀 SERVEUR CONTRATS DÉMARRÉ sur http://localhost:${PORT}`);
      console.log('📊 Base de données: Facutration');
      console.log('📡 Routes disponibles:');
      console.log('  - GET  / (test)');
      console.log('  - POST /login (authentification)');
      console.log('  - GET  /api/clients (tous les clients)');
      console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE');
      console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
      console.log('\n✅ PRÊT À RECEVOIR LES REQUÊTES !');
      console.log('🔍 TOUS LES APPELS SERONT LOGGÉS EN DÉTAIL');
      console.log('═'.repeat(60));
    });

  } catch (error) {
    console.error('❌ Erreur lors du démarrage du serveur:', error.message);
    process.exit(1);
  }
}

startServer();

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err.message);
  console.error('❌ Stack:', err.stack);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
  console.error('❌ Stack:', err.stack);
});
