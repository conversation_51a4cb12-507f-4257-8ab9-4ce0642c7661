.saisie-client-qr-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.saisie-client-qr-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.back-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.saisie-client-qr-header h1 {
  margin: 0;
  color: white;
  font-size: 28px;
  font-weight: 600;
}

/* Messages */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

/* Résultat QR */
.qr-result {
  background: #e7f3ff;
  border: 2px solid #007bff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.qr-result h3 {
  margin: 0 0 15px 0;
  color: #007bff;
  font-size: 20px;
}

.qr-info {
  text-align: left;
}

.qr-info p {
  margin: 8px 0;
  font-size: 14px;
}

.qr-info code {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
  font-weight: bold;
}

/* Formulaire */
.saisie-client-form {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f8f9fa;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
  color: #6c757d;
  opacity: 0.7;
}

/* Actions */
.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 2px solid #f8f9fa;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Responsive */
@media (max-width: 768px) {
  .saisie-client-qr-container {
    padding: 10px;
  }
  
  .saisie-client-qr-header h1 {
    font-size: 22px;
  }
  
  .saisie-client-form {
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .form-section h3 {
    font-size: 16px;
  }
  
  .submit-button {
    min-width: 200px;
    padding: 12px 30px;
    font-size: 14px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.saisie-client-form {
  animation: fadeIn 0.5s ease-out;
}

.qr-result {
  animation: fadeIn 0.5s ease-out;
}

/* États des champs requis */
.form-group input:required:invalid {
  border-color: #dc3545;
}

.form-group input:required:valid {
  border-color: #28a745;
}

/* Indicateur de champ requis */
.form-group label::after {
  content: '';
}

.form-group:has(input:required) label::after,
.form-group:has(select:required) label::after {
  content: ' *';
  color: #dc3545;
  font-weight: bold;
}
