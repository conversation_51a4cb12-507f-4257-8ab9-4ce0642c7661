{"version": 3, "names": ["React", "memo", "Header", "back", "layout", "progress", "options", "route", "navigation", "styleInterpolator", "insets", "useSafeAreaInsets", "previousTitle", "headerBackTitle", "undefined", "title", "goBack", "useCallback", "debounce", "isFocused", "canGoBack", "dispatch", "StackActions", "pop", "source", "key", "isModal", "useContext", "ModalPresentationContext", "isParentHeaderShown", "HeaderShownContext", "statusBarHeight", "headerStatusBarHeight", "top", "getHeaderTitle", "name"], "sourceRoot": "../../../../src", "sources": ["views/Header/Header.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAA4C;AAAA;AAAA;AAAA;AAAA,4BAE7BA,KAAK,CAACC,IAAI,CAAC,SAASC,MAAM,OAQpB;EAAA,IARqB;IACxCC,IAAI;IACJC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC;EACgB,CAAC;EACjB,MAAMC,MAAM,GAAG,IAAAC,6CAAiB,GAAE;EAElC,IAAIC,aAAa;;EAEjB;EACA;EACA,IAAIN,OAAO,CAACO,eAAe,KAAKC,SAAS,EAAE;IACzCF,aAAa,GAAGN,OAAO,CAACO,eAAe;EACzC,CAAC,MAAM,IAAIV,IAAI,EAAE;IACfS,aAAa,GAAGT,IAAI,CAACY,KAAK;EAC5B;;EAEA;EACA,MAAMC,MAAM,GAAGhB,KAAK,CAACiB,WAAW,CAC9B,IAAAC,iBAAQ,EAAC,MAAM;IACb,IAAIV,UAAU,CAACW,SAAS,EAAE,IAAIX,UAAU,CAACY,SAAS,EAAE,EAAE;MACpDZ,UAAU,CAACa,QAAQ,CAAC;QAClB,GAAGC,oBAAY,CAACC,GAAG,EAAE;QACrBC,MAAM,EAAEjB,KAAK,CAACkB;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC,EACN,CAACjB,UAAU,EAAED,KAAK,CAACkB,GAAG,CAAC,CACxB;EAED,MAAMC,OAAO,GAAG1B,KAAK,CAAC2B,UAAU,CAACC,iCAAwB,CAAC;EAC1D,MAAMC,mBAAmB,GAAG7B,KAAK,CAAC2B,UAAU,CAACG,4BAAkB,CAAC;EAEhE,MAAMC,eAAe,GACnBzB,OAAO,CAAC0B,qBAAqB,KAAKlB,SAAS,GACvCR,OAAO,CAAC0B,qBAAqB,GAC7BN,OAAO,IAAIG,mBAAmB,GAC9B,CAAC,GACDnB,MAAM,CAACuB,GAAG;EAEhB,oBACE,oBAAC,sBAAa,eACR3B,OAAO;IACX,KAAK,EAAE,IAAA4B,wBAAc,EAAC5B,OAAO,EAAEC,KAAK,CAAC4B,IAAI,CAAE;IAC3C,QAAQ,EAAE9B,QAAS;IACnB,MAAM,EAAED,MAAO;IACf,KAAK,EAAEsB,OAAQ;IACf,eAAe,EACbpB,OAAO,CAACO,eAAe,KAAKC,SAAS,GACjCR,OAAO,CAACO,eAAe,GACvBD,aACL;IACD,qBAAqB,EAAEmB,eAAgB;IACvC,QAAQ,EAAE5B,IAAI,GAAGa,MAAM,GAAGF,SAAU;IACpC,iBAAiB,EAAEL;EAAkB,GACrC;AAEN,CAAC,CAAC;AAAA"}