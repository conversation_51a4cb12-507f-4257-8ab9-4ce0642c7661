{"version": 3, "names": ["React", "CHILD_STATE", "Symbol", "useRouteCache", "routes", "cache", "useMemo", "current", "Map", "process", "env", "NODE_ENV", "reduce", "acc", "route", "previous", "get", "set", "state", "proxy", "Object", "defineProperty", "enumerable", "value", "Array", "from", "values"], "sourceRoot": "../../src", "sources": ["useRouteCache.tsx"], "mappings": "AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAM9B;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAGC,MAAM,CAAC,aAAa,CAAC;;AAEhD;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAa,CACnCC,MAAuB,EACvB;EACA;EACA,MAAMC,KAAK,GAAGL,KAAK,CAACM,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAE,IAAIC,GAAG;EAAiB,CAAC,CAAC,EAAE,EAAE,CAAC;EAE7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,OAAOP,MAAM;EACf;EAEAC,KAAK,CAACE,OAAO,GAAGH,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC5C,MAAMC,QAAQ,GAAGV,KAAK,CAACE,OAAO,CAACS,GAAG,CAACF,KAAK,CAAC;IAEzC,IAAIC,QAAQ,EAAE;MACZ;MACAF,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEC,QAAQ,CAAC;IAC1B,CAAC,MAAM;MACL,MAAM;QAAEG,KAAK;QAAE,GAAGC;MAAM,CAAC,GAAGL,KAAK;MAEjCM,MAAM,CAACC,cAAc,CAACF,KAAK,EAAElB,WAAW,EAAE;QACxCqB,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAEL;MACT,CAAC,CAAC;MAEFL,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEK,KAAK,CAAC;IACvB;IAEA,OAAON,GAAG;EACZ,CAAC,EAAE,IAAIL,GAAG,EAAE,CAAe;EAE3B,OAAOgB,KAAK,CAACC,IAAI,CAACpB,KAAK,CAACE,OAAO,CAACmB,MAAM,EAAE,CAAC;AAC3C"}