import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as i from"../../core/platform/platform.js";import*as n from"../../core/root/root.js";import*as s from"../../core/host/host.js";import*as o from"../visual_logging/visual_logging.js";import*as r from"../../models/text_utils/text_utils.js";import*as a from"../components/icon_button/icon_button.js";import*as l from"../components/adorners/adorners.js";import*as h from"../components/settings/settings.js";import*as d from"./utils/utils.js";export{d as Utils};import*as c from"../../core/dom_extension/dom_extension.js";import*as u from"../components/helpers/helpers.js";import*as m from"./theme_support/theme_support.js";import*as p from"../lit-html/lit-html.js";let g;class b{flavorsInternal;eventDispatchers;constructor(){this.flavorsInternal=new Map,this.eventDispatchers=new Map}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new b),g}static removeInstance(){g=void 0}setFlavor(e,t){(this.flavorsInternal.get(e)||null)!==t&&(t?this.flavorsInternal.set(e,t):this.flavorsInternal.delete(e),this.dispatchFlavorChange(e,t))}dispatchFlavorChange(e,t){for(const i of f)i.contextTypes().includes(e)&&i.loadListener().then((e=>e.flavorChanged(t)));const i=this.eventDispatchers.get(e);i&&i.dispatchEventToListeners("FlavorChanged",t)}addFlavorChangeListener(t,i,n){let s=this.eventDispatchers.get(t);s||(s=new e.ObjectWrapper.ObjectWrapper,this.eventDispatchers.set(t,s)),s.addEventListener("FlavorChanged",i,n)}removeFlavorChangeListener(e,t,i){const n=this.eventDispatchers.get(e);n&&(n.removeEventListener("FlavorChanged",t,i),n.hasEventListeners("FlavorChanged")||this.eventDispatchers.delete(e))}flavor(e){return this.flavorsInternal.get(e)||null}flavors(){return new Set(this.flavorsInternal.keys())}}const f=[];var v=Object.freeze({__proto__:null,Context:b,registerListener:function(e){f.push(e)}});const w={elements:"Elements",screenshot:"Screenshot",network:"Network",memory:"Memory",javascript_profiler:"JavaScript Profiler",console:"Console",performance:"Performance",mobile:"Mobile",help:"Help",layers:"Layers",navigation:"Navigation",drawer:"Drawer",global:"Global",resources:"Resources",background_services:"Background Services",settings:"Settings",debugger:"Debugger",sources:"Sources",rendering:"Rendering",recorder:"Recorder",changes:"Changes"},x=t.i18n.registerUIStrings("ui/legacy/ActionRegistration.ts",w),E=t.i18n.getLocalizedString.bind(void 0,x);class I extends e.ObjectWrapper.ObjectWrapper{enabledInternal=!0;toggledInternal=!1;actionRegistration;constructor(e){super(),this.actionRegistration=e}id(){return this.actionRegistration.actionId}async execute(){if(!this.actionRegistration.loadActionDelegate)return!1;const e=await this.actionRegistration.loadActionDelegate(),t=this.id();return e.handleAction(b.instance(),t)}icon(){return this.actionRegistration.iconClass}toggledIcon(){return this.actionRegistration.toggledIconClass}toggleWithRedColor(){return Boolean(this.actionRegistration.toggleWithRedColor)}setEnabled(e){this.enabledInternal!==e&&(this.enabledInternal=e,this.dispatchEventToListeners("Enabled",e))}enabled(){return this.enabledInternal}category(){return this.actionRegistration.category}tags(){if(this.actionRegistration.tags)return this.actionRegistration.tags.map((e=>e())).join("\0")}toggleable(){return Boolean(this.actionRegistration.toggleable)}title(){let e=this.actionRegistration.title?this.actionRegistration.title():t.i18n.lockedString("");const i=this.actionRegistration.options;if(i)for(const t of i)t.value!==this.toggledInternal&&(e=t.title());return e}toggled(){return this.toggledInternal}setToggled(e){console.assert(this.toggleable(),"Shouldn't be toggling an untoggleable action",this.id()),this.toggledInternal!==e&&(this.toggledInternal=e,this.dispatchEventToListeners("Toggled",e))}options(){return this.actionRegistration.options}contextTypes(){if(this.actionRegistration.contextTypes)return this.actionRegistration.contextTypes()}canInstantiate(){return Boolean(this.actionRegistration.loadActionDelegate)}bindings(){return this.actionRegistration.bindings}experiment(){return this.actionRegistration.experiment}setting(){return this.actionRegistration.setting}condition(){return this.actionRegistration.condition}order(){return this.actionRegistration.order}}const y=new Map;function S(){y.clear()}function C(){return Array.from(y.values()).filter((t=>{const i=t.setting();try{if(i&&!e.Settings.moduleSetting(i).get())return!1}catch(e){if(e.message.startsWith("No setting registered"))return!1}return n.Runtime.Runtime.isDescriptorEnabled({experiment:t.experiment(),condition:t.condition()})})).sort(((e,t)=>(e.order()||0)-(t.order()||0)))}var T=Object.freeze({__proto__:null,Action:I,registerActionExtension:function(e){const t=e.actionId;if(y.has(t))throw new Error(`Duplicate action ID '${t}'`);if(!i.StringUtilities.isExtendedKebabCase(t))throw new Error(`Invalid action ID '${t}'`);y.set(t,new I(e))},reset:S,getRegisteredActionExtensions:C,maybeRemoveActionExtension:function(e){return y.delete(e)},getLocalizedActionCategory:function(e){switch(e){case"ELEMENTS":return E(w.elements);case"SCREENSHOT":return E(w.screenshot);case"NETWORK":return E(w.network);case"MEMORY":return E(w.memory);case"JAVASCRIPT_PROFILER":return E(w.javascript_profiler);case"CONSOLE":return E(w.console);case"PERFORMANCE":return E(w.performance);case"MOBILE":return E(w.mobile);case"HELP":return E(w.help);case"LAYERS":return E(w.layers);case"NAVIGATION":return E(w.navigation);case"DRAWER":return E(w.drawer);case"GLOBAL":return E(w.global);case"RESOURCES":return E(w.resources);case"BACKGROUND_SERVICES":return E(w.background_services);case"SETTINGS":return E(w.settings);case"DEBUGGER":return E(w.debugger);case"SOURCES":return E(w.sources);case"RENDERING":return E(w.rendering);case"RECORDER":return E(w.recorder);case"CHANGES":return E(w.changes);case"":return t.i18n.lockedString("")}return t.i18n.lockedString(e)}});let k;class M{actionsById;constructor(){this.actionsById=new Map,this.registerActions()}static instance(e={forceNew:null}){const{forceNew:t}=e;return k&&!t||(k=new M),k}static removeInstance(){k=void 0}static reset(){M.removeInstance(),S()}registerActions(){for(const e of C())this.actionsById.set(e.id(),e),e.canInstantiate()||e.setEnabled(!1)}availableActions(){return this.applicableActions([...this.actionsById.keys()],b.instance())}actions(){return[...this.actionsById.values()]}applicableActions(e,t){const i=[];for(const s of e){const e=this.actionsById.get(s);e&&e.enabled()&&n(e,t.flavors())&&i.push(e)}return i;function n(e,t){const i=e.contextTypes();if(!i)return!0;for(let e=0;e<i.length;++e){const n=i[e];if(Boolean(n)&&t.has(n))return!0}return!1}}hasAction(e){return this.actionsById.has(e)}getAction(e){const t=this.actionsById.get(e);if(t)return t;throw new Error(`Cannot find registered action with ID '${e}'`)}}var L=Object.freeze({__proto__:null,ActionRegistry:M});let P,D,A=0;function R(e){return(e||"")+ ++A}function B(e,t){const i=R("labelledControl");t.id=i,e.setAttribute("for",i)}function O(e){e.setAttribute("role","alert"),e.setAttribute("aria-live","polite")}function F(e){e.setAttribute("role","button")}function z(e){e.setAttribute("role","dialog"),e.setAttribute("aria-modal","true")}function W(e){e.setAttribute("role","group")}function H(e){e.setAttribute("role","link")}function N(e){F(e),e.setAttribute("aria-haspopup","true")}function _(e){e.setAttribute("role","tab")}function j(e){e.setAttribute("role","tabpanel")}function V(e){e.setAttribute("role","tree")}function U(e){e.setAttribute("role","treeitem")}function K(e){e.setAttribute("role","textbox")}function q(e){e.setAttribute("role","menu")}function $(e){e.setAttribute("role","menuitem")}function G(e){e.setAttribute("role","menuitemcheckbox")}function X(e){$(e),e.setAttribute("aria-haspopup","true")}function Y(e){e.setAttribute("role","complementary")}function Q(e){e.setAttribute("role","navigation")}function Z(e){e.setAttribute("role","listbox")}function J(e){e.setAttribute("aria-multiselectable","true")}function ee(e){e.setAttribute("role","option")}function te(e){e.setAttribute("aria-hidden","true")}function ie(e,t){e.setAttribute("role","heading"),e.setAttribute("aria-level",t.toString())}function ne(e){return e.hasAttribute("role")}function se(e,t){t?e.setAttribute("aria-placeholder",t):e.removeAttribute("aria-placeholder")}function oe(e){e.id||(e.id=R("ariaElement"))}function re(e,t){t?(oe(t),e.setAttribute("aria-controls",t.id)):e.removeAttribute("aria-controls")}function ae(e,t){e.setAttribute("aria-expanded",Boolean(t).toString())}function le(e){e.removeAttribute("aria-expanded")}function he(e,t="none"){e.setAttribute("aria-autocomplete",t)}function de(e){e.removeAttribute("aria-autocomplete")}function ce(e,t="false"){"false"!==t?e.setAttribute("aria-haspopup",t):e.removeAttribute("aria-haspopup")}function ue(e,t){e.setAttribute("aria-selected",Boolean(t).toString())}function me(e){e.removeAttribute("aria-selected")}function pe(e,t){t?e.setAttribute("aria-invalid",t.toString()):e.removeAttribute("aria-invalid")}function ge(e,t){e.setAttribute("aria-pressed",Boolean(t).toString())}function be(e,t){e.setAttribute("aria-label",t)}function fe(e,t){e.setAttribute("aria-description",t)}function ve(e,t){t?(t.isConnected&&e.isConnected&&console.assert(i.DOMUtilities.getEnclosingShadowRootForNode(t)===i.DOMUtilities.getEnclosingShadowRootForNode(e),"elements are not in the same shadow dom"),oe(t),e.setAttribute("aria-activedescendant",t.id)):e.removeAttribute("aria-activedescendant")}function we(e,t){e.setAttribute("aria-setsize",t.toString())}function xe(e,t){e.setAttribute("aria-posinset",t.toString())}function Ee(e){e.style.position="absolute",e.style.left="-999em",e.style.width="100em",e.style.overflow="hidden"}let Ie=!1;function ye(){if(!P){const e=document.body.createChild("div");Ee(e),e.setAttribute("role","alert"),e.setAttribute("aria-atomic","true"),P=e}if(!D){const e=document.body.createChild("div");Ee(e),e.setAttribute("role","alert"),e.setAttribute("aria-atomic","true"),D=e}return Ie=!Ie,Ie?(D.textContent="",P):(P.textContent="",D)}function Se(e){ye().textContent=i.StringUtilities.trimEndWithMaxLength(e,1e4)}var Ce=Object.freeze({__proto__:null,nextId:R,bindLabelToControl:B,markAsAlert:O,markAsApplication:function(e){e.setAttribute("role","application")},markAsButton:F,markAsCheckbox:function(e){e.setAttribute("role","checkbox")},markAsCombobox:function(e){e.setAttribute("role","combobox")},markAsModalDialog:z,markAsGroup:W,markAsLink:H,markAsMenuButton:N,markAsProgressBar:function(e,t=0,i=100){e.setAttribute("role","progressbar"),e.setAttribute("aria-valuemin",t.toString()),e.setAttribute("aria-valuemax",i.toString())},markAsTab:_,markAsTablist:function(e){e.setAttribute("role","tablist")},markAsTabpanel:j,markAsTree:V,markAsTreeitem:U,markAsTextBox:K,markAsMenu:q,markAsMenuItem:$,markAsMenuItemCheckBox:G,markAsMenuItemSubMenu:X,markAsList:function(e){e.setAttribute("role","list")},markAsListitem:function(e){e.setAttribute("role","listitem")},markAsMain:function(e){e.setAttribute("role","main")},markAsComplementary:Y,markAsNavigation:Q,markAsListBox:Z,markAsMultiSelectable:J,markAsOption:ee,markAsRadioGroup:function(e){e.setAttribute("role","radiogroup")},markAsHidden:te,markAsSlider:function(e,t=0,i=100){e.setAttribute("role","slider"),e.setAttribute("aria-valuemin",String(t)),e.setAttribute("aria-valuemax",String(i))},markAsHeading:ie,markAsPoliteLiveRegion:function(e,t){e.setAttribute("aria-live","polite"),t&&e.setAttribute("aria-atomic","true")},markAsLog:function(e){e.setAttribute("role","log")},hasRole:ne,removeRole:function(e){e.removeAttribute("role")},setPlaceholder:se,markAsPresentation:function(e){e.setAttribute("role","presentation")},markAsStatus:function(e){e.setAttribute("role","status")},ensureId:oe,setAriaValueText:function(e,t){e.setAttribute("aria-valuetext",t)},setAriaValueNow:function(e,t){e.setAttribute("aria-valuenow",t)},setAriaValueMinMax:function(e,t,i){e.setAttribute("aria-valuemin",t),e.setAttribute("aria-valuemax",i)},setControls:re,setChecked:function(e,t){e.setAttribute("aria-checked",Boolean(t).toString())},setCheckboxAsIndeterminate:function(e){e.setAttribute("aria-checked","mixed")},setDisabled:function(e,t){e.setAttribute("aria-disabled",Boolean(t).toString())},setExpanded:ae,unsetExpandable:le,setHidden:function(e,t){e.setAttribute("aria-hidden",Boolean(t).toString())},setLevel:function(e,t){e.setAttribute("aria-level",t.toString())},setAutocomplete:he,clearAutocomplete:de,setHasPopup:ce,setSelected:ue,clearSelected:me,setInvalid:pe,setPressed:ge,setValueNow:function(e,t){e.setAttribute("aria-valuenow",t.toString())},setValueText:function(e,t){e.setAttribute("aria-valuetext",t.toString())},setProgressBarValue:function(e,t,i){e.setAttribute("aria-valuenow",t.toString()),i&&e.setAttribute("aria-valuetext",i)},setLabel:be,setDescription:fe,setActiveDescendant:ve,setSetSize:we,setPositionInSet:xe,alertElementInstance:ye,alert:Se}),Te=Object.freeze({__proto__:null}),ke={cssContent:".widget{box-shadow:var(--drop-shadow);background:var(--sys-color-cdt-base-container);justify-content:flex-start;align-items:stretch;display:flex}.dialog-close-button{position:absolute;right:9px;top:9px;z-index:1}"},Me={cssContent:":host{position:absolute!important;top:0;bottom:0;left:0;right:0;overflow:hidden;contain:strict;background-color:transparent}:host-context(.dimmed-pane){background-color:var(--color-background-opacity-50)}:host-context(.no-pointer-events){pointer-events:none}.widget{display:flex;background-color:transparent;pointer-events:auto;flex:none}.no-pointer-events{pointer-events:none}.arrow{background-image:var(--image-file-popoverArrows);width:19px;height:19px}.arrow-top{background-position:0 76px;margin-top:-19px;margin-left:-9px}.arrow-bottom{background-position:0 57px;margin-left:-9px}.arrow-left{background-position:0 38px;margin-left:-19px;margin-top:-9px}.arrow-right{background-position:0 19px;margin-top:-9px}.arrow-none{display:none}:host-context(.-theme-with-dark-background) .arrow{filter:invert(80%)}"},Le={cssContent:":host{padding:0;margin:0;display:inline-flex;flex-shrink:0;align-items:center!important;overflow:hidden;white-space:nowrap}input{height:12px;width:12px;flex-shrink:0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary);&:focus{outline:auto 5px -webkit-focus-ring-color}}input.inside-datagrid{height:10px;width:10px}.dt-checkbox-text{margin-left:3px;overflow:hidden;text-overflow:ellipsis}.dt-checkbox-subtitle{color:var(--sys-color-token-subtle);white-space:break-spaces}@media (forced-colors: active){input.dt-checkbox-theme-preserve{forced-color-adjust:none}input.dt-checkbox-theme-preserve:active{background:HighlightText}input.dt-checkbox-theme-preserve:checked,\n  input.dt-checkbox-theme-preserve:active:checked{background:Highlight;border-color:Highlight}input.dt-checkbox-theme-preserve:hover:enabled{border-color:Highlight}input.dt-checkbox-theme-preserve:active::before,\n  input.dt-checkbox-theme-preserve:active::after{background-color:Highlight}input.dt-checkbox-theme-preserve:checked::before,\n  input.dt-checkbox-theme-preserve:checked::after,\n  input.dt-checkbox-theme-preserve:active:checked::before,\n  input.dt-checkbox-theme-preserve:active:checked::after{background-color:HighlightText}input.dt-checkbox-theme-preserve:hover:checked::before,\n  input.dt-checkbox-theme-preserve:hover:checked::after{background-color:Highlight!important}input.dt-checkbox-theme-preserve:hover:checked{background:HighlightText}}"},Pe={cssContent:".close-button{width:20px;height:20px;display:flex;align-items:center;justify-content:center}.close-button:hover devtools-icon,\n.close-button:focus-visible devtools-icon{color:var(--icon-default-hover)}.close-button:focus{outline:2px solid var(--sys-color-state-focus-ring)}@media (forced-colors: active) and (prefers-color-scheme: dark){.close-button{filter:brightness(100)}}"},De={cssContent:".widget{padding:20px;box-sizing:border-box;max-width:400px;overflow:hidden}.message{text-align:center}.message,\n.button{font-size:larger;white-space:pre;margin:10px 0}.button{text-align:center;margin-top:20px}.button button{min-width:100px}.reason{color:var(--sys-color-error);margin-top:10px}.message span{white-space:normal;word-wrap:break-word;max-width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;padding:5px;margin:0}"};const Ae=1e-5;class Re{x;y;z;constructor(e,t,i){this.x=e,this.y=t,this.z=i}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}normalize(){const e=this.length();e<=Ae||(this.x/=e,this.y/=e,this.z/=e)}}class Be{x;y;constructor(e,t){this.x=e,this.y=t}distanceTo(e){return Math.sqrt(Math.pow(e.x-this.x,2)+Math.pow(e.y-this.y,2))}projectOn(e){return 0===e.x&&0===e.y?new Be(0,0):e.scale((this.x*e.x+this.y*e.y)/(Math.pow(e.x,2)+Math.pow(e.y,2)))}scale(e){return new Be(this.x*e,this.y*e)}toString(){return Math.round(100*this.x)/100+", "+Math.round(100*this.y)/100}}class Oe{controlPoints;constructor(e,t){this.controlPoints=[e,t]}static parse(e){const t=Oe.KeywordValues,i=e.toLowerCase().replace(/\s+/g,"");if(t.has(i))return Oe.parse(t.get(i));const n=i.match(/^cubic-bezier\(([^,]+),([^,]+),([^,]+),([^,]+)\)$/);if(n){const e=new Be(parseFloat(n[1]),parseFloat(n[2])),t=new Be(parseFloat(n[3]),parseFloat(n[4]));return new Oe(e,t)}return null}evaluateAt(e){function t(e,t,i){return 3*(1-i)*(1-i)*i*e+3*(1-i)*i*i*t+Math.pow(i,3)}const i=t(this.controlPoints[0].x,this.controlPoints[1].x,e),n=t(this.controlPoints[0].y,this.controlPoints[1].y,e);return new Be(i,n)}asCSSText(){const e="cubic-bezier("+this.controlPoints.join(", ")+")",t=Oe.KeywordValues;for(const[i,n]of t)if(e===n&&"linear"!==i)return i;return e}static Regex=/((cubic-bezier\([^)]+\))|\b(linear(?![-\(])|ease-in-out|ease-in|ease-out|ease)\b)|(linear\([^)]+\))/g;static KeywordValues=new Map([["linear","cubic-bezier(0, 0, 1, 1)"],["ease","cubic-bezier(0.25, 0.1, 0.25, 1)"],["ease-in","cubic-bezier(0.42, 0, 1, 1)"],["ease-in-out","cubic-bezier(0.42, 0, 0.58, 1)"],["ease-out","cubic-bezier(0, 0, 0.58, 1)"]])}const Fe=new Oe(new Be(0,0),new Be(1,1));class ze{alpha;beta;gamma;constructor(e,t,i){this.alpha=e,this.beta=t,this.gamma=i}static fromDeviceOrientationRotationMatrix(e){let t,i,n;return Math.abs(e.m33)<Ae?Math.abs(e.m13)<Ae?(t=Math.atan2(e.m12,e.m11),i=e.m23>0?Math.PI/2:-Math.PI/2,n=0):e.m13>0?(t=Math.atan2(-e.m21,e.m22),i=Math.asin(e.m23),n=-Math.PI/2):(t=Math.atan2(e.m21,-e.m22),i=-Math.asin(e.m23),i+=i>0||Math.abs(i)<Ae?-Math.PI:Math.PI,n=-Math.PI/2):e.m33>0?(t=Math.atan2(-e.m21,e.m22),i=Math.asin(e.m23),n=Math.atan2(-e.m13,e.m33)):(t=Math.atan2(e.m21,-e.m22),i=-Math.asin(e.m23),i+=i>0||Math.abs(i)<Ae?-Math.PI:Math.PI,n=Math.atan2(e.m13,-e.m33)),t<-Ae&&(t+=2*Math.PI),t=Number(Ne(t).toFixed(6)),i=Number(Ne(i).toFixed(6)),n=Number(Ne(n).toFixed(6)),new ze(t,i,n)}}const We=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z},He=function(e,t){const i=e.x*t.m14+e.y*t.m24+e.z*t.m34+t.m44,n=(e.x*t.m11+e.y*t.m21+e.z*t.m31+t.m41)/i,s=(e.x*t.m12+e.y*t.m22+e.z*t.m32+t.m42)/i,o=(e.x*t.m13+e.y*t.m23+e.z*t.m33+t.m43)/i;return new Re(n,s,o)},Ne=function(e){return 180*e/Math.PI};class _e{width;height;constructor(e,t){this.width=e,this.height=t}clipTo(e){return e?new _e(Math.min(this.width,e.width),Math.min(this.height,e.height)):this}scale(e){return new _e(this.width*e,this.height*e)}isEqual(e){return null!==e&&this.width===e.width&&this.height===e.height}widthToMax(e){return new _e(Math.max(this.width,"number"==typeof e?e:e.width),this.height)}addWidth(e){return new _e(this.width+("number"==typeof e?e:e.width),this.height)}heightToMax(e){return new _e(this.width,Math.max(this.height,"number"==typeof e?e:e.height))}addHeight(e){return new _e(this.width,this.height+("number"==typeof e?e:e.height))}}class je{minimum;preferred;constructor(e,t){if(this.minimum=e||new _e(0,0),this.preferred=t||this.minimum,this.minimum.width>this.preferred.width||this.minimum.height>this.preferred.height)throw new Error("Minimum size is greater than preferred.")}isEqual(e){return null!==e&&this.minimum.isEqual(e.minimum)&&this.preferred.isEqual(e.preferred)}widthToMax(e){return"number"==typeof e?new je(this.minimum.widthToMax(e),this.preferred.widthToMax(e)):new je(this.minimum.widthToMax(e.minimum),this.preferred.widthToMax(e.preferred))}addWidth(e){return"number"==typeof e?new je(this.minimum.addWidth(e),this.preferred.addWidth(e)):new je(this.minimum.addWidth(e.minimum),this.preferred.addWidth(e.preferred))}heightToMax(e){return"number"==typeof e?new je(this.minimum.heightToMax(e),this.preferred.heightToMax(e)):new je(this.minimum.heightToMax(e.minimum),this.preferred.heightToMax(e.preferred))}addHeight(e){return"number"==typeof e?new je(this.minimum.addHeight(e),this.preferred.addHeight(e)):new je(this.minimum.addHeight(e.minimum),this.preferred.addHeight(e.preferred))}}var Ve=Object.freeze({__proto__:null,_Eps:Ae,Vector:Re,Point:Be,CubicBezier:Oe,LINEAR_BEZIER:Fe,EulerAngles:ze,scalarProduct:We,crossProduct:function(e,t){const i=e.y*t.z-e.z*t.y,n=e.z*t.x-e.x*t.z,s=e.x*t.y-e.y*t.x;return new Re(i,n,s)},subtract:function(e,t){const i=e.x-t.x,n=e.y-t.y,s=e.z-t.z;return new Re(i,n,s)},multiplyVectorByMatrixAndNormalize:He,calculateAngle:function(e,t){const i=e.length(),n=t.length();if(i<=Ae||n<=Ae)return 0;const s=We(e,t)/i/n;return Math.abs(s)>1?0:Ne(Math.acos(s))},degreesToRadians:function(e){return e*Math.PI/180},degreesToGradians:function(e){return e/9*10},degreesToTurns:function(e){return e/360},radiansToDegrees:Ne,radiansToGradians:function(e){return 200*e/Math.PI},radiansToTurns:function(e){return e/(2*Math.PI)},gradiansToRadians:function(e){return e*Math.PI/200},turnsToRadians:function(e){return 2*e*Math.PI},boundsForTransformedPoints:function(e,t,i){i||(i={minX:1/0,maxX:-1/0,minY:1/0,maxY:-1/0}),t.length%3&&console.warn("Invalid size of points array");for(let n=0;n<t.length;n+=3){let s=new Re(t[n],t[n+1],t[n+2]);s=He(s,e),i.minX=Math.min(i.minX,s.x),i.maxX=Math.max(i.maxX,s.x),i.minY=Math.min(i.minY,s.y),i.maxY=Math.max(i.maxY,s.y)}return i},Size:_e,Constraints:je}),Ue={cssContent:":host{display:inline-flex;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;position:relative;vertical-align:sub;margin:2px;background-color:var(--sys-color-cdt-base-container);justify-content:center;width:28px}:host:hover{border:none;background-color:var(--sys-color-state-hover-on-subtle)}"};class Ke{descriptors;action;type;keybindSets;constructor(e,t,i,n){this.descriptors=e,this.action=t,this.type=i,this.keybindSets=n||new Set}title(){return this.descriptors.map((e=>e.name)).join(" ")}isDefault(){return"DefaultShortcut"===this.type||"DisabledDefault"===this.type||"KeybindSetShortcut"===this.type&&this.keybindSets.has(Hs)}changeType(e){return new Ke(this.descriptors,this.action,e)}changeKeys(e){return this.descriptors=e,this}descriptorsMatch(e){return e.length===this.descriptors.length&&e.every(((e,t)=>e.key===this.descriptors[t].key))}hasKeybindSet(e){return!this.keybindSets||this.keybindSets.has(e)}equals(e){return this.descriptorsMatch(e.descriptors)&&this.type===e.type&&this.action===e.action}static createShortcutFromSettingObject(e){return new Ke(e.descriptors,e.action,e.type)}static makeKey(e,t){return"string"==typeof e&&(e=e.charCodeAt(0)-(/^[a-z]/.test(e)?32:0)),t=t||qe.None,Ke.makeKeyFromCodeAndModifiers(e,t)}static makeKeyFromEvent(e){let t=qe.None;e.shiftKey&&(t|=qe.Shift),e.ctrlKey&&(t|=qe.Ctrl),e.altKey&&(t|=qe.Alt),e.metaKey&&(t|=qe.Meta);const i=e.keyCode||e.__keyCode;return Ke.makeKeyFromCodeAndModifiers(i,t)}static makeKeyFromEventIgnoringModifiers(e){const t=e.keyCode||e.__keyCode;return Ke.makeKeyFromCodeAndModifiers(t,qe.None)}static eventHasCtrlEquivalentKey(e){return s.Platform.isMac()?e.metaKey&&!e.ctrlKey:e.ctrlKey&&!e.metaKey}static eventHasEitherCtrlOrMeta(e){return e.metaKey||e.ctrlKey}static hasNoModifiers(e){const t=e;return!(t.ctrlKey||t.shiftKey||t.altKey||t.metaKey)}static makeDescriptor(e,t){return{key:Ke.makeKey("string"==typeof e?e:e.code,t),name:Ke.shortcutToString(e,t)}}static makeDescriptorFromBindingShortcut(e){const[t,...i]=e.split(/\+(?!$)/).reverse();let n=0;for(const t of i){const i=qe[t];console.assert(void 0!==i,`Only one key other than modifier is allowed in shortcut <${e}>`),n|=i}console.assert(t.length>0,`Modifiers-only shortcuts are not allowed (encountered <${e}>)`);const s=nt[t]||st[t];return s&&"shiftKey"in s&&s.shiftKey&&(n|=qe.Shift),Ke.makeDescriptor(s||t,n)}static shortcutToString(e,t){return"string"!=typeof e&&Ke.isModifier(e.code)?Ke.modifiersToString(t):Ke.modifiersToString(t)+Ke.keyName(e)}static keyName(e){return"string"==typeof e?e.toUpperCase():"string"==typeof e.name?e.name:e.name[s.Platform.platform()]||e.name.other||""}static makeKeyFromCodeAndModifiers(e,t){return 255&e|(t||0)<<8}static keyCodeAndModifiersFromKey(e){return{keyCode:255&e,modifiers:e>>8}}static isModifier(e){const{keyCode:t}=Ke.keyCodeAndModifiersFromKey(e);return t===nt.Shift.code||t===nt.Ctrl.code||t===nt.Alt.code||t===nt.Meta.code}static modifiersToString(e){const t=s.Platform.isMac(),i=qe,n=new Map([[i.Ctrl,t?"Ctrl ":"Ctrl + "],[i.Alt,t?"⌥ ":"Alt + "],[i.Shift,t?"⇧ ":"Shift + "],[i.Meta,t?"⌘ ":"Win + "]]);return[i.Meta,i.Ctrl,i.Alt,i.Shift].map((function(t){return(e||0)&t?n.get(t):""})).join("")}}const qe={None:0,Shift:1,Ctrl:2,Alt:4,Meta:8,CtrlOrMeta:s.Platform.isMac()?8:2,ShiftOrOption:s.Platform.isMac()?4:1},$e={code:37,name:"←"},Ge={code:38,name:"↑"},Xe={code:39,name:"→"},Ye={code:40,name:"↓"},Qe={code:17,name:"Ctrl"},Ze={code:27,name:"Esc"},Je={code:32,name:"Space"},et={code:187,name:"+"},tt={code:192,name:"`"},it={code:222,name:"'"},nt={Backspace:{code:8,name:"↤"},Tab:{code:9,name:{mac:"⇥",other:"Tab"}},Enter:{code:13,name:{mac:"↩",other:"Enter"}},Shift:{code:16,name:{mac:"⇧",other:"Shift"}},Ctrl:Qe,Control:Qe,Alt:{code:18,name:"Alt"},Esc:Ze,Escape:Ze,Space:Je," ":Je,PageUp:{code:33,name:{mac:"⇞",other:"PageUp"}},PageDown:{code:34,name:{mac:"⇟",other:"PageDown"}},End:{code:35,name:{mac:"↗",other:"End"}},Home:{code:36,name:{mac:"↖",other:"Home"}},Left:$e,Up:Ge,Right:Xe,Down:Ye,ArrowLeft:$e,ArrowUp:Ge,ArrowRight:Xe,ArrowDown:Ye,Delete:{code:46,name:"Del"},Zero:{code:48,name:"0"},H:{code:72,name:"H"},N:{code:78,name:"N"},P:{code:80,name:"P"},Meta:{code:91,name:"Meta"},F1:{code:112,name:"F1"},F2:{code:113,name:"F2"},F3:{code:114,name:"F3"},F4:{code:115,name:"F4"},F5:{code:116,name:"F5"},F6:{code:117,name:"F6"},F7:{code:118,name:"F7"},F8:{code:119,name:"F8"},F9:{code:120,name:"F9"},F10:{code:121,name:"F10"},F11:{code:122,name:"F11"},F12:{code:123,name:"F12"},Semicolon:{code:186,name:";"},NumpadPlus:{code:107,name:"Numpad +"},NumpadMinus:{code:109,name:"Numpad -"},Numpad0:{code:96,name:"Numpad 0"},Plus:et,Equal:et,Comma:{code:188,name:","},Minus:{code:189,name:"-"},Period:{code:190,name:"."},Slash:{code:191,name:"/"},QuestionMark:{code:191,name:"?"},Apostrophe:tt,Tilde:{code:192,name:"Tilde"},Backquote:tt,IntlBackslash:tt,LeftSquareBracket:{code:219,name:"["},RightSquareBracket:{code:221,name:"]"},Backslash:{code:220,name:"\\"},SingleQuote:it,Quote:it,get CtrlOrMeta(){return s.Platform.isMac()?this.Meta:this.Ctrl}},st={};!function(){for(const e in nt){const t=nt[e];if("object"==typeof t&&t.code){const i="string"==typeof t.name?t.name:e;st[i]=t}}}();var ot=Object.freeze({__proto__:null,KeyboardShortcut:Ke,Modifiers:qe,Keys:nt,KeyBindings:st}),rt={cssContent:"::slotted(input.dt-radio-button){height:17px;width:17px;min-width:17px;border-radius:8px;vertical-align:middle;margin:0 5px 5px 0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}::slotted(input.dt-radio-button:focus){box-shadow:var(--legacy-focus-ring-active-shadow)}@media (forced-colors: active){::slotted(input.dt-radio-button){--gradient-start:ButtonFace;--gradient-end:ButtonFace}::slotted(input.dt-radio-button:checked){--gradient-start:Highlight;--gradient-end:Highlight}}"},at={cssContent:".dt-range-input{appearance:none;margin:0;padding:0;height:10px;width:88px;outline:none;background:none}.dt-range-input::-webkit-slider-thumb,\n.-theme-preserve{appearance:none;margin:0;padding:0;border:0;width:12px;height:12px;margin-top:-5px;border-radius:50%;background-color:var(--sys-color-primary)}.dt-range-input::-webkit-slider-runnable-track{appearance:none;margin:0;padding:0;width:100%;height:2px;background-color:var(--sys-color-surface-variant)}.dt-range-input:focus::-webkit-slider-thumb{box-shadow:0 0 0 2px var(--sys-color-inverse-primary)}.dt-range-input:disabled::-webkit-slider-thumb{background-color:var(--sys-color-state-disabled)}@media (forced-colors: active){.dt-range-input{forced-color-adjust:none}}"},lt={cssContent:"div{display:inline-flex;height:14px;align-items:center;vertical-align:middle;white-space:nowrap;padding:1px 4px;text-align:left;font-size:11px;line-height:normal;font-weight:bold;text-shadow:none;color:var(--sys-color-inverse-on-surface);border-radius:7px}div.verbose{background-color:var(--sys-color-token-attribute-value)}:host-context(.-theme-with-dark-background) div.verbose{background-color:var(--sys-color-token-tag)}div.info{background-color:var(--sys-color-token-meta)}div.warning{background-color:var(--sys-color-token-attribute)}:host-context(.-theme-with-dark-background) div.warning{background-color:var(--sys-color-token-attribute-value)}div.error{background-color:var(--sys-color-error-bright)}"};const ht={close:"Close",dockToRight:"Dock to right",dockToBottom:"Dock to bottom",dockToLeft:"Dock to left",undockIntoSeparateWindow:"Undock into separate window",devtoolsUndocked:"DevTools is undocked",devToolsDockedTo:"DevTools is docked to {PH1}"},dt=t.i18n.registerUIStrings("ui/legacy/DockController.ts",ht),ct=t.i18n.getLocalizedString.bind(void 0,dt);let ut;class mt extends e.ObjectWrapper.ObjectWrapper{canDockInternal;closeButton;currentDockStateSetting;lastDockStateSetting;dockSideInternal=void 0;titles;savedFocus;constructor(t){if(super(),this.canDockInternal=t,this.closeButton=new wn(ct(ht.close),"cross"),this.closeButton.element.setAttribute("jslog",`${o.close().track({click:!0})}`),this.closeButton.element.classList.add("close-devtools"),this.closeButton.addEventListener("Click",s.InspectorFrontendHost.InspectorFrontendHostInstance.closeWindow.bind(s.InspectorFrontendHost.InspectorFrontendHostInstance)),this.currentDockStateSetting=e.Settings.Settings.instance().moduleSetting("currentDockState"),this.lastDockStateSetting=e.Settings.Settings.instance().createSetting("last-dock-state","bottom"),!t)return this.dockSideInternal="undocked",void this.closeButton.setVisible(!1);this.currentDockStateSetting.addChangeListener(this.dockSideChanged,this),-1===pt.indexOf(this.currentDockStateSetting.get())&&this.currentDockStateSetting.set("right"),-1===pt.indexOf(this.lastDockStateSetting.get())&&this.currentDockStateSetting.set("bottom")}static instance(e={forceNew:null,canDock:!1}){const{forceNew:t,canDock:i}=e;return ut&&!t||(ut=new mt(i)),ut}initialize(){this.canDockInternal&&(this.titles=[ct(ht.dockToRight),ct(ht.dockToBottom),ct(ht.dockToLeft),ct(ht.undockIntoSeparateWindow)],this.dockSideChanged())}dockSideChanged(){this.setDockSide(this.currentDockStateSetting.get()),setTimeout(this.announceDockLocation.bind(this),2e3)}dockSide(){return this.dockSideInternal}canDock(){return this.canDockInternal}isVertical(){return"right"===this.dockSideInternal||"left"===this.dockSideInternal}setDockSide(e){if(-1===pt.indexOf(e)&&(e=pt[0]),this.dockSideInternal===e)return;void 0!==this.dockSideInternal&&document.body.classList.remove(this.dockSideInternal),document.body.classList.add(e),this.dockSideInternal&&this.lastDockStateSetting.set(this.dockSideInternal),this.savedFocus=i.DOMUtilities.deepActiveElement(document);const t={from:this.dockSideInternal,to:e};this.dispatchEventToListeners("BeforeDockSideChanged",t),console.timeStamp("DockController.setIsDocked"),this.dockSideInternal=e,this.currentDockStateSetting.set(e),s.InspectorFrontendHost.InspectorFrontendHostInstance.setIsDocked("undocked"!==e,this.setIsDockedResponse.bind(this,t)),this.closeButton.setVisible("undocked"!==this.dockSideInternal),this.dispatchEventToListeners("DockSideChanged",t)}setIsDockedResponse(e){this.dispatchEventToListeners("AfterDockSideChanged",e),this.savedFocus&&(this.savedFocus.focus(),this.savedFocus=null)}toggleDockSide(){if(this.lastDockStateSetting.get()===this.currentDockStateSetting.get()){const e=pt.indexOf(this.currentDockStateSetting.get())||0;this.lastDockStateSetting.set(pt[(e+1)%pt.length])}this.setDockSide(this.lastDockStateSetting.get())}announceDockLocation(){"undocked"===this.dockSideInternal?Se(ct(ht.devtoolsUndocked)):Se(ct(ht.devToolsDockedTo,{PH1:this.dockSideInternal||""}))}}const pt=["right","bottom","left","undocked"];let gt;class bt{static instance(e={forceNew:null}){const{forceNew:t}=e;return gt&&!t||(gt=new bt),gt}item(){return mt.instance().closeButton}}var ft=Object.freeze({__proto__:null,DockController:mt,ToggleDockActionDelegate:class{handleAction(e,t){return mt.instance().toggleDockSide(),!0}},CloseButtonProvider:bt}),vt={cssContent:".infobar{color:var(--sys-color-on-surface);display:flex;flex:auto;flex-direction:column;position:relative;padding:6px;min-width:fit-content}.infobar:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:-2px}.infobar-warning{background-color:var(--sys-color-surface-yellow);color:var(--sys-color-on-surface-yellow)}.infobar-error{--override-infobar-error-background:var(--sys-color-surface-error);--override-infobar-error-text:var(--sys-color-on-surface-error);background-color:var(--override-infobar-error-background);color:var(--override-infobar-error-text)}.infobar-main-row{display:flex;flex-direction:row;justify-content:flex-start;min-height:25px}.infobar-info-container{display:flex;align-items:center;flex-grow:1;flex-wrap:wrap}.infobar-info-message{display:flex;margin:5px 0}.infobar-info-text{display:flex;align-items:center;margin:0 4px}.infobar-details-rows{padding:5px 5px 0}.infobar-details-row{display:flex;flex-direction:column;line-height:18px;padding-bottom:6px}.infobar-close-container{display:flex;flex-shrink:0;align-items:center}.infobar-close-container > .infobar-button.link-style{margin:4px}.infobar-selectable{user-select:text}.infobar-button{color:var(--sys-color-token-subtle);padding:0 4px}.info-icon{mask-image:var(--image-file-info);background-color:var(--icon-info)}.warning-icon{mask-image:var(--image-file-warning);background-color:var(--icon-warning)}.error-icon{mask-image:var(--image-file-cross-circle);background-color:var(--icon-error)}.issue-icon{mask-image:var(--image-file-issue-text-filled);background-color:var(--icon-info)}.icon{mask-size:20px 20px;width:20px;height:20px;flex-shrink:0}.devtools-link.text-button:hover,\n.devtools-link.text-button:focus,\n.devtools-link.text-button:active{background-color:transparent;box-shadow:none}"};const wt={dontShowAgain:"Don't show again",showMore:"Show more",close:"Close"},xt=t.i18n.registerUIStrings("ui/legacy/Infobar.ts",wt),Et=t.i18n.getLocalizedString.bind(void 0,xt);class It{element;shadowRoot;contentElement;mainRow;detailsRows;hasDetails;detailsMessage;infoContainer;infoMessage;infoText;actionContainer;disableSetting;closeContainer;toggleElement;closeButton;closeCallback;#e=null;parentView;constructor(e,t,i,n,s=!0,r){if(this.element=document.createElement("div"),r&&this.element.setAttribute("jslog",`${o.dialog(r).track({resize:!0})}`),this.element.classList.add("flex-none"),this.shadowRoot=d.createShadowRootWithCoreStyles(this.element,{cssFile:vt,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","infobar infobar-"+e),this.mainRow=this.contentElement.createChild("div","infobar-main-row"),this.detailsRows=this.contentElement.createChild("div","infobar-details-rows hidden"),this.hasDetails=!1,this.detailsMessage="",this.infoContainer=this.mainRow.createChild("div","infobar-info-container"),this.infoMessage=this.infoContainer.createChild("div","infobar-info-message"),this.infoMessage.createChild("div",e+"-icon icon"),this.infoText=this.infoMessage.createChild("div","infobar-info-text"),this.infoText.textContent=t,O(this.infoText),this.actionContainer=this.infoContainer.createChild("div","infobar-info-actions"),i){this.contentElement.setAttribute("role","group");for(const e of i){const t=this.actionCallbackFactory(e);let i="infobar-button";e.highlight&&(i+=" primary-button");const n=os(e.text,t,{className:i,jslogContext:e.jslogContext});e.highlight&&!this.#e&&(this.#e=n),this.actionContainer.appendChild(n)}}if(this.disableSetting=n||null,n){const e=os(Et(wt.dontShowAgain),this.onDisable.bind(this),{className:"infobar-button"});this.actionContainer.appendChild(e)}this.closeContainer=this.mainRow.createChild("div","infobar-close-container"),this.toggleElement=os(Et(wt.showMore),this.onToggleDetails.bind(this),{className:"link-style devtools-link hidden",jslogContext:"show-more"}),this.toggleElement.setAttribute("role","link"),this.closeContainer.appendChild(this.toggleElement),this.closeButton=this.closeContainer.createChild("div","close-button","dt-close-button"),this.closeButton.hidden=!s,this.closeButton.setTabbable(!0),fe(this.closeButton,Et(wt.close)),self.onInvokeElement(this.closeButton,this.dispose.bind(this)),"issue"!==e&&(this.contentElement.tabIndex=0),be(this.contentElement,t),this.contentElement.addEventListener("keydown",(e=>e.keyCode===nt.Esc.code?(this.dispose(),void e.consume()):e.target===this.contentElement&&"Enter"===e.key&&this.hasDetails?(this.onToggleDetails(),void e.consume()):void 0)),this.closeCallback=null}static create(e,t,i,n,s){return n&&n.get()?null:new It(e,t,i,n,void 0,s)}dispose(){this.element.remove(),this.onResize(),this.closeCallback&&this.closeCallback.call(null)}setText(e){this.infoText.textContent=e,this.onResize()}setCloseCallback(e){this.closeCallback=e}setParentView(e){this.parentView=e}actionCallbackFactory(e){return e.delegate?e.dismiss?(()=>{e.delegate&&e.delegate(),this.dispose()}).bind(this):e.delegate:e.dismiss?this.dispose.bind(this):()=>{}}onResize(){this.parentView&&this.parentView.doResize()}onDisable(){this.disableSetting&&this.disableSetting.set(!0),this.dispose()}onToggleDetails(){this.detailsRows.classList.remove("hidden"),this.toggleElement.remove(),this.onResize(),Se("string"==typeof this.detailsMessage?this.detailsMessage:this.detailsMessage.textContent||""),this.#e?this.#e.focus():this.closeButton.focus()}createDetailsRowMessage(e){this.hasDetails=!0,this.detailsMessage=e,this.toggleElement.classList.remove("hidden");const t=this.detailsRows.createChild("div","infobar-details-row").createChild("span","infobar-row-message");return"string"==typeof e?t.textContent=e:t.appendChild(e),t}}var yt=Object.freeze({__proto__:null,Infobar:It}),St={cssContent:".tabbed-pane-header-tab{height:26px;margin:0;border:none;border-left:2px solid transparent;border-right:2px solid transparent;&.selected{border-width:0 2px}& > .tabbed-pane-header-tab-icon > devtools-icon{width:14px;height:14px;color:var(--icon-warning)}}.tabbed-pane-header-contents{margin-left:0;min-width:min-content}.tabbed-pane-left-toolbar{margin-right:0!important}"};class Ct extends e.ObjectWrapper.ObjectWrapper{isEnabledInternal;elementsInternal;installDragOnMouseDownBound;cursorInternal;startX;startY;constructor(){super(),this.isEnabledInternal=!0,this.elementsInternal=new Set,this.installDragOnMouseDownBound=this.installDragOnMouseDown.bind(this),this.cursorInternal="nwse-resize"}isEnabled(){return this.isEnabledInternal}setEnabled(e){this.isEnabledInternal=e,this.updateElementCursors()}elements(){return[...this.elementsInternal]}addElement(e){this.elementsInternal.has(e)||(this.elementsInternal.add(e),e.addEventListener("pointerdown",this.installDragOnMouseDownBound,!1),this.updateElementCursor(e))}removeElement(e){this.elementsInternal.has(e)&&(this.elementsInternal.delete(e),e.removeEventListener("pointerdown",this.installDragOnMouseDownBound,!1),e.style.removeProperty("cursor"))}updateElementCursors(){this.elementsInternal.forEach(this.updateElementCursor.bind(this))}updateElementCursor(e){this.isEnabledInternal?(e.style.setProperty("cursor",this.cursor()),e.style.setProperty("touch-action","none")):(e.style.removeProperty("cursor"),e.style.removeProperty("touch-action"))}cursor(){return this.cursorInternal}setCursor(e){this.cursorInternal=e,this.updateElementCursors()}installDragOnMouseDown(e){const t=e.target;if(!this.elementsInternal.has(t))return!1;Rn(t,this.dragStart.bind(this),(e=>{this.drag(e)}),this.dragEnd.bind(this),this.cursor(),e)}dragStart(e){return!!this.isEnabledInternal&&(this.startX=e.pageX,this.startY=e.pageY,this.sendDragStart(this.startX,this.startY),!0)}sendDragStart(e,t){this.dispatchEventToListeners("ResizeStart",{startX:e,currentX:e,startY:t,currentY:t})}drag(e){return this.isEnabledInternal?(this.sendDragMove(this.startX,e.pageX,this.startY,e.pageY,e.shiftKey),e.preventDefault(),!1):(this.dragEnd(e),!0)}sendDragMove(e,t,i,n,s){this.dispatchEventToListeners("ResizeUpdateXY",{startX:e,currentX:t,startY:i,currentY:n,shiftKey:s})}dragEnd(e){this.dispatchEventToListeners("ResizeEnd"),delete this.startX,delete this.startY}}class Tt extends Ct{isVerticalInternal;constructor(){super(),this.isVerticalInternal=!0}isVertical(){return this.isVerticalInternal}setVertical(e){this.isVerticalInternal=e,this.updateElementCursors()}cursor(){return this.isVerticalInternal?"ns-resize":"ew-resize"}sendDragStart(e,t){const i=this.isVerticalInternal?t:e;this.dispatchEventToListeners("ResizeStart",{startPosition:i,currentPosition:i})}sendDragMove(e,t,i,n,s){this.isVerticalInternal?this.dispatchEventToListeners("ResizeUpdatePosition",{startPosition:i,currentPosition:n,shiftKey:s}):this.dispatchEventToListeners("ResizeUpdatePosition",{startPosition:e,currentPosition:t,shiftKey:s})}}var kt=Object.freeze({__proto__:null,ResizerWidget:Ct,SimpleResizerWidget:Tt}),Mt={cssContent:".shadow-split-widget{display:flex;overflow:hidden}.shadow-split-widget-contents{display:flex;position:relative;flex-direction:column;contain:layout size style}.shadow-split-widget-sidebar{flex:none}.shadow-split-widget-main,\n.shadow-split-widget-sidebar.maximized{flex:auto}.shadow-split-widget.hbox > .shadow-split-widget-resizer{position:absolute;top:0;bottom:0;width:6px;z-index:4000}.shadow-split-widget.vbox > .shadow-split-widget-resizer{position:absolute;left:0;right:0;height:6px;z-index:4000}.shadow-split-widget.vbox > .shadow-split-widget-sidebar.no-default-splitter{border:0!important}.shadow-split-widget.vbox > .shadow-split-widget-sidebar:not(.maximized){border:0;border-top:1px solid var(--sys-color-divider)}.shadow-split-widget.hbox > .shadow-split-widget-sidebar:not(.maximized){border:0;border-left:1px solid var(--sys-color-divider)}.shadow-split-widget.vbox > .shadow-split-widget-sidebar:first-child:not(.maximized){border:0;border-bottom:1px solid var(--sys-color-divider)}.shadow-split-widget.hbox > .shadow-split-widget-sidebar:first-child:not(.maximized){border:0;border-right:1px solid var(--sys-color-divider)}:host-context(.disable-resizer-for-elements-hack) .shadow-split-widget-resizer{pointer-events:none}"};class Lt extends HTMLElement{static get observedAttributes(){return["flex","padding","padding-top","padding-bottom","padding-left","padding-right","margin","margin-top","margin-bottom","margin-left","margin-right","overflow","overflow-x","overflow-y","font-size","color","background","background-color","border","border-top","border-bottom","border-left","border-right","max-width","max-height"]}attributeChangedCallback(e,t,i){if("flex"!==e)if(null===i){if(this.style.removeProperty(e),e.startsWith("padding-")||e.startsWith("margin-")||e.startsWith("border-")||e.startsWith("background-")||e.startsWith("overflow-")){const t=e.substring(0,e.indexOf("-")),i=this.getAttribute(t);null!==i&&this.style.setProperty(t,i)}}else this.style.setProperty(e,i);else null===i?this.style.removeProperty("flex"):"initial"===i||"auto"===i||"none"===i||-1!==i.indexOf(" ")?this.style.setProperty("flex",i):this.style.setProperty("flex","0 0 "+i)}}class Pt extends Lt{constructor(e){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction",e),this.style.setProperty("justify-content","flex-start")}static get observedAttributes(){return super.observedAttributes.concat(["x-start","x-center","x-stretch","x-baseline","justify-content"])}attributeChangedCallback(e,t,i){"x-start"!==e&&"x-center"!==e&&"x-stretch"!==e&&"x-baseline"!==e?super.attributeChangedCallback(e,t,i):null===i?this.style.removeProperty("align-items"):this.style.setProperty("align-items","x-start"===e?"flex-start":e.substr(2))}}customElements.define("x-vbox",class extends Pt{constructor(){super("column")}}),customElements.define("x-hbox",class extends Pt{constructor(){super("row")}}),customElements.define("x-cbox",class extends Lt{constructor(){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction","column"),this.style.setProperty("justify-content","center"),this.style.setProperty("align-items","center")}}),customElements.define("x-div",class extends Lt{constructor(){super(),this.style.setProperty("display","block")}}),customElements.define("x-span",class extends Lt{constructor(){super(),this.style.setProperty("display","inline")}}),customElements.define("x-text",class extends Lt{constructor(){super(),this.style.setProperty("display","inline"),this.style.setProperty("white-space","pre")}});var Dt=Object.freeze({__proto__:null,XElement:Lt});let At=null;const Rt=new WeakMap;class Bt extends Lt{visible;shadowRootInternal;defaultFocusedElement;elementsToRestoreScrollPositionsFor;onShownCallback;onHiddenCallback;onResizedCallback;constructor(){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction","column"),this.style.setProperty("align-items","stretch"),this.style.setProperty("justify-content","flex-start"),this.style.setProperty("contain","layout style"),this.visible=!1,this.defaultFocusedElement=null,this.elementsToRestoreScrollPositionsFor=[],At||(At=new ResizeObserver((e=>{for(const t of e){const e=t.target;e.visible&&e.onResizedCallback&&e.onResizedCallback.call(null)}}))),At.observe(this),this.setElementsToRestoreScrollPositionsFor([this])}isShowing(){return this.visible}setOnShown(e){this.onShownCallback=e}setOnHidden(e){this.onHiddenCallback=e}setOnResized(e){this.onResizedCallback=e}setElementsToRestoreScrollPositionsFor(e){for(const e of this.elementsToRestoreScrollPositionsFor)e.removeEventListener("scroll",Bt.storeScrollPosition,{capture:!1});this.elementsToRestoreScrollPositionsFor=e;for(const e of this.elementsToRestoreScrollPositionsFor)e.addEventListener("scroll",Bt.storeScrollPosition,{passive:!0,capture:!1})}restoreScrollPositions(){for(const e of this.elementsToRestoreScrollPositionsFor){const t=Rt.get(e);t&&(e.scrollTop=t.scrollTop,e.scrollLeft=t.scrollLeft)}}static storeScrollPosition(e){const t=e.currentTarget;Rt.set(t,{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop})}setDefaultFocusedElement(e){if(e&&!this.isSelfOrAncestor(e))throw new Error("Default focus must be descendant");this.defaultFocusedElement=e}focus(){if(!this.visible)return;let e;if(this.defaultFocusedElement&&this.isSelfOrAncestor(this.defaultFocusedElement))e=this.defaultFocusedElement;else if(-1!==this.tabIndex)e=this;else{let t=this.traverseNextNode(this);for(;t;){if(t instanceof Bt&&t.visible){e=t;break}t=t.traverseNextNode(this)}}e&&!e.hasFocus()&&(e===this?HTMLElement.prototype.focus.call(this):e.focus())}connectedCallback(){this.visible=!0,this.restoreScrollPositions(),this.onShownCallback&&this.onShownCallback.call(null)}disconnectedCallback(){this.visible=!1,this.onHiddenCallback&&this.onHiddenCallback.call(null)}}customElements.define("x-widget",Bt);var Ot=Object.freeze({__proto__:null,XWidget:Bt});class Ft extends HTMLDivElement{__widget;__widgetCounter;constructor(){super()}}function zt(e,t){if(!e)throw new Error(t)}class Wt{element;contentElement;shadowRoot;isWebComponent;visibleInternal;isRoot;isShowingInternal;childrenInternal;hideOnDetach;notificationDepth;invalidationsSuspended;defaultFocusedChild;parentWidgetInternal;registeredCSSFiles;defaultFocusedElement;cachedConstraints;constraintsInternal;invalidationsRequested;externallyManaged;constructor(e,t){this.contentElement=document.createElement("div"),this.contentElement.classList.add("widget"),e?(this.element=document.createElement("div"),this.element.classList.add("vbox"),this.element.classList.add("flex-auto"),this.shadowRoot=d.createShadowRootWithCoreStyles(this.element,{cssFile:void 0,delegatesFocus:t}),this.shadowRoot.appendChild(this.contentElement)):this.element=this.contentElement,this.isWebComponent=e,this.element.__widget=this,this.visibleInternal=!1,this.isRoot=!1,this.isShowingInternal=!1,this.childrenInternal=[],this.hideOnDetach=!1,this.notificationDepth=0,this.invalidationsSuspended=0,this.defaultFocusedChild=null,this.parentWidgetInternal=null,this.registeredCSSFiles=!1}static incrementWidgetCounter(e,t){const i=(t.__widgetCounter||0)+(t.__widget?1:0);if(!i)return;let n=e;for(;n;)n.__widgetCounter=(n.__widgetCounter||0)+i,n=Vt(n)}static decrementWidgetCounter(e,t){const i=(t.__widgetCounter||0)+(t.__widget?1:0);if(!i)return;let n=e;for(;n;)n.__widgetCounter&&(n.__widgetCounter-=i),n=Vt(n)}markAsRoot(){zt(!this.element.parentElement,"Attempt to mark as root attached node"),this.isRoot=!0}parentWidget(){return this.parentWidgetInternal}children(){return this.childrenInternal}childWasDetached(e){}isShowing(){return this.isShowingInternal}shouldHideOnDetach(){if(!this.element.parentElement)return!1;if(this.hideOnDetach)return!0;for(const e of this.childrenInternal)if(e.shouldHideOnDetach())return!0;return!1}setHideOnDetach(){this.hideOnDetach=!0}inNotification(){return Boolean(this.notificationDepth)||Boolean(this.parentWidgetInternal&&this.parentWidgetInternal.inNotification())}parentIsShowing(){return!!this.isRoot||null!==this.parentWidgetInternal&&this.parentWidgetInternal.isShowing()}callOnVisibleChildren(e){const t=this.childrenInternal.slice();for(let i=0;i<t.length;++i)t[i].parentWidgetInternal===this&&t[i].visibleInternal&&e.call(t[i])}processWillShow(){this.callOnVisibleChildren(this.processWillShow),this.isShowingInternal=!0}processWasShown(){this.inNotification()||(this.restoreScrollPositions(),this.notify(this.wasShown),this.callOnVisibleChildren(this.processWasShown))}processWillHide(){this.inNotification()||(this.storeScrollPositions(),this.callOnVisibleChildren(this.processWillHide),this.notify(this.willHide),this.isShowingInternal=!1)}processWasHidden(){this.callOnVisibleChildren(this.processWasHidden)}processOnResize(){this.inNotification()||this.isShowing()&&(this.notify(this.onResize),this.callOnVisibleChildren(this.processOnResize))}notify(e){++this.notificationDepth;try{e.call(this)}finally{--this.notificationDepth}}wasShown(){}willHide(){}onResize(){}onLayout(){}onDetach(){}async ownerViewDisposed(){}show(e,t){if(zt(e,"Attempt to attach widget with no parent element"),!this.isRoot){let t=e;for(;t&&!t.__widget;)t=Vt(t);if(!t||!t.__widget)throw new Error("Attempt to attach widget to orphan node");this.attach(t.__widget)}this.showWidgetInternal(e,t)}attach(e){e!==this.parentWidgetInternal&&(this.parentWidgetInternal&&this.detach(),this.parentWidgetInternal=e,this.parentWidgetInternal.childrenInternal.push(this),this.isRoot=!1)}showWidget(){if(!this.visibleInternal){if(!this.element.parentElement)throw new Error("Attempt to show widget that is not hidden using hideWidget().");this.showWidgetInternal(this.element.parentElement,this.element.nextSibling)}}showWidgetInternal(e,t){let i=e;for(;i&&!i.__widget;)i=Vt(i);this.isRoot?zt(!i,"Attempt to show root widget under another widget"):zt(i&&i.__widget===this.parentWidgetInternal,"Attempt to show under node belonging to alien widget");const n=this.visibleInternal;n&&this.element.parentElement===e||(this.visibleInternal=!0,!n&&this.parentIsShowing()&&this.processWillShow(),this.element.classList.remove("hidden"),this.element.parentElement!==e&&(this.externallyManaged||Wt.incrementWidgetCounter(e,this.element),t?c.DOMExtension.originalInsertBefore.call(e,this.element,t):c.DOMExtension.originalAppendChild.call(e,this.element)),!n&&this.parentIsShowing()&&this.processWasShown(),this.parentWidgetInternal&&this.hasNonZeroConstraints()?this.parentWidgetInternal.invalidateConstraints():this.processOnResize())}hideWidget(){this.visibleInternal&&this.hideWidgetInternal(!1)}hideWidgetInternal(e){this.visibleInternal=!1;const t=this.element.parentElement;this.parentIsShowing()&&this.processWillHide(),e?(Wt.decrementWidgetCounter(t,this.element),c.DOMExtension.originalRemoveChild.call(t,this.element),this.onDetach()):this.element.classList.add("hidden"),this.parentIsShowing()&&this.processWasHidden(),this.parentWidgetInternal&&this.hasNonZeroConstraints()&&this.parentWidgetInternal.invalidateConstraints()}detach(e){if(!this.parentWidgetInternal&&!this.isRoot)return;const t=e||!this.shouldHideOnDetach();if(this.visibleInternal)this.hideWidgetInternal(t);else if(t&&this.element.parentElement){const e=this.element.parentElement;Wt.decrementWidgetCounter(e,this.element),c.DOMExtension.originalRemoveChild.call(e,this.element)}if(this.parentWidgetInternal){const e=this.parentWidgetInternal.childrenInternal.indexOf(this);zt(e>=0,"Attempt to remove non-child widget"),this.parentWidgetInternal.childrenInternal.splice(e,1),this.parentWidgetInternal.defaultFocusedChild===this&&(this.parentWidgetInternal.defaultFocusedChild=null),this.parentWidgetInternal.childWasDetached(this),this.parentWidgetInternal=null}else zt(this.isRoot,"Removing non-root widget from DOM")}detachChildWidgets(){const e=this.childrenInternal.slice();for(let t=0;t<e.length;++t)e[t].detach()}elementsToRestoreScrollPositionsFor(){return[this.element]}storeScrollPositions(){const e=this.elementsToRestoreScrollPositionsFor();for(const t of e)Ht.set(t,{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop})}restoreScrollPositions(){const e=this.elementsToRestoreScrollPositionsFor();for(const t of e){const e=Ht.get(t);e&&(t.scrollLeft=e.scrollLeft,t.scrollTop=e.scrollTop)}}doResize(){this.isShowing()&&(this.inNotification()||this.callOnVisibleChildren(this.processOnResize))}doLayout(){this.isShowing()&&(this.notify(this.onLayout),this.doResize())}registerRequiredCSS(e){this.isWebComponent?m.ThemeSupport.instance().appendStyle(this.shadowRoot,e):m.ThemeSupport.instance().appendStyle(this.element,e)}registerCSSFiles(e){let t;t=this.isWebComponent&&void 0!==this.shadowRoot?this.shadowRoot:u.GetRootNode.getRootNode(this.contentElement),t.adoptedStyleSheets=t.adoptedStyleSheets.concat(e),this.registeredCSSFiles=!0}printWidgetHierarchy(){const e=[];this.collectWidgetHierarchy("",e),console.log(e.join("\n"))}collectWidgetHierarchy(e,t){t.push(e+"["+this.element.className+"]"+(this.childrenInternal.length?" {":""));for(let i=0;i<this.childrenInternal.length;++i)this.childrenInternal[i].collectWidgetHierarchy(e+"    ",t);this.childrenInternal.length&&t.push(e+"}")}setDefaultFocusedElement(e){this.defaultFocusedElement=e}setDefaultFocusedChild(e){zt(e.parentWidgetInternal===this,"Attempt to set non-child widget as default focused."),this.defaultFocusedChild=e}focus(){if(!this.isShowing())return;const e=this.defaultFocusedElement;if(e)e.hasFocus()||e.focus();else if(this.defaultFocusedChild&&this.defaultFocusedChild.visibleInternal)this.defaultFocusedChild.focus();else{for(const e of this.childrenInternal)if(e.visibleInternal)return void e.focus();let e=this.contentElement.traverseNextNode(this.contentElement);for(;e;){if(e instanceof Bt)return void e.focus();e=e.traverseNextNode(this.contentElement)}}}hasFocus(){return this.element.hasFocus()}calculateConstraints(){return new je}constraints(){return void 0!==this.constraintsInternal?this.constraintsInternal:(void 0===this.cachedConstraints&&(this.cachedConstraints=this.calculateConstraints()),this.cachedConstraints)}setMinimumAndPreferredSizes(e,t,i,n){this.constraintsInternal=new je(new _e(e,t),new _e(i,n)),this.invalidateConstraints()}setMinimumSize(e,t){this.constraintsInternal=new je(new _e(e,t)),this.invalidateConstraints()}hasNonZeroConstraints(){const e=this.constraints();return Boolean(e.minimum.width||e.minimum.height||e.preferred.width||e.preferred.height)}suspendInvalidations(){++this.invalidationsSuspended}resumeInvalidations(){--this.invalidationsSuspended,!this.invalidationsSuspended&&this.invalidationsRequested&&this.invalidateConstraints()}invalidateConstraints(){if(this.invalidationsSuspended)return void(this.invalidationsRequested=!0);this.invalidationsRequested=!1;const e=this.cachedConstraints;delete this.cachedConstraints;!this.constraints().isEqual(e||null)&&this.parentWidgetInternal?this.parentWidgetInternal.invalidateConstraints():this.doLayout()}markAsExternallyManaged(){zt(!this.parentWidgetInternal,"Attempt to mark widget as externally managed after insertion to the DOM"),this.externallyManaged=!0}}const Ht=new WeakMap;class Nt extends Wt{constructor(e,t){super(e,t),this.contentElement.classList.add("vbox")}calculateConstraints(){let e=new je;return this.callOnVisibleChildren((function(){const t=this.constraints();e=e.widthToMax(t),e=e.addHeight(t)})),e}}class _t extends Wt{constructor(e){super(e),this.contentElement.classList.add("hbox")}calculateConstraints(){let e=new je;return this.callOnVisibleChildren((function(){const t=this.constraints();e=e.addWidth(t),e=e.heightToMax(t)})),e}}class jt{widget;previous;constructor(e){this.widget=e,this.previous=i.DOMUtilities.deepActiveElement(e.element.ownerDocument),e.focus()}restore(){this.widget&&(this.widget.hasFocus()&&this.previous&&this.previous.focus(),this.previous=null,this.widget=null)}}function Vt(e){return e.parentElementOrShadowHost()}var Ut=Object.freeze({__proto__:null,WidgetElement:Ft,Widget:Wt,VBox:Nt,HBox:_t,VBoxWithResizeCallback:class extends Nt{resizeCallback;constructor(e){super(),this.resizeCallback=e}onResize(){this.resizeCallback()}},WidgetFocusRestorer:jt});let Kt;class qt extends e.ObjectWrapper.ObjectWrapper{frontendHost;zoomFactorInternal;constructor(e,t){super(),this.frontendHost=t,this.zoomFactorInternal=this.frontendHost.zoomFactor(),e.addEventListener("resize",this.onWindowResize.bind(this),!0)}static instance(e={forceNew:null,win:null,frontendHost:null}){const{forceNew:t,win:i,frontendHost:n}=e;if(!Kt||t){if(!i||!n)throw new Error(`Unable to create zoom manager: window and frontendHost must be provided: ${(new Error).stack}`);Kt=new qt(i,n)}return Kt}static removeInstance(){Kt=void 0}zoomFactor(){return this.zoomFactorInternal}cssToDIP(e){return e*this.zoomFactorInternal}dipToCSS(e){return e/this.zoomFactorInternal}onWindowResize(){const e=this.zoomFactorInternal;this.zoomFactorInternal=this.frontendHost.zoomFactor(),e!==this.zoomFactorInternal&&this.dispatchEventToListeners("ZoomChanged",{from:e,to:this.zoomFactorInternal})}}var $t=Object.freeze({__proto__:null,ZoomManager:qt});class Gt extends(e.ObjectWrapper.eventMixin(Wt)){sidebarElementInternal;mainElement;resizerElementInternal;resizerElementSize;resizerWidget;defaultSidebarWidth;defaultSidebarHeight;constraintsInDip;resizeStartSizeDIP;setting;totalSizeCSS;totalSizeOtherDimensionCSS;mainWidgetInternal;sidebarWidgetInternal;animationFrameHandle;animationCallback;showSidebarButtonTitle;hideSidebarButtonTitle;shownSidebarString;hiddenSidebarString;showHideSidebarButton;isVerticalInternal;sidebarMinimized;detaching;sidebarSizeDIP;savedSidebarSizeDIP;secondIsSidebar;shouldSaveShowMode;savedVerticalMainSize;savedHorizontalMainSize;showModeInternal;savedShowMode;constructor(t,i,n,s,o,r){super(!0),this.element.classList.add("split-widget"),this.registerRequiredCSS(Mt),this.contentElement.classList.add("shadow-split-widget"),this.sidebarElementInternal=this.contentElement.createChild("div","shadow-split-widget-contents shadow-split-widget-sidebar vbox"),this.mainElement=this.contentElement.createChild("div","shadow-split-widget-contents shadow-split-widget-main vbox"),this.mainElement.createChild("slot").name="insertion-point-main",this.sidebarElementInternal.createChild("slot").name="insertion-point-sidebar",this.resizerElementInternal=this.contentElement.createChild("div","shadow-split-widget-resizer"),this.resizerElementSize=null,this.resizerWidget=new Tt,this.resizerWidget.setEnabled(!0),this.resizerWidget.addEventListener("ResizeStart",this.onResizeStart,this),this.resizerWidget.addEventListener("ResizeUpdatePosition",this.onResizeUpdate,this),this.resizerWidget.addEventListener("ResizeEnd",this.onResizeEnd,this),this.defaultSidebarWidth=s||200,this.defaultSidebarHeight=o||this.defaultSidebarWidth,this.constraintsInDip=Boolean(r),this.resizeStartSizeDIP=0,this.setting=n?e.Settings.Settings.instance().createSetting(n,{}):null,this.totalSizeCSS=0,this.totalSizeOtherDimensionCSS=0,this.mainWidgetInternal=null,this.sidebarWidgetInternal=null,this.animationFrameHandle=0,this.animationCallback=null,this.showSidebarButtonTitle=e.UIString.LocalizedEmptyString,this.hideSidebarButtonTitle=e.UIString.LocalizedEmptyString,this.shownSidebarString=e.UIString.LocalizedEmptyString,this.hiddenSidebarString=e.UIString.LocalizedEmptyString,this.showHideSidebarButton=null,this.isVerticalInternal=!1,this.sidebarMinimized=!1,this.detaching=!1,this.sidebarSizeDIP=-1,this.savedSidebarSizeDIP=this.sidebarSizeDIP,this.secondIsSidebar=!1,this.shouldSaveShowMode=!1,this.savedVerticalMainSize=null,this.savedHorizontalMainSize=null,this.setSecondIsSidebar(i),this.innerSetVertical(t),this.showModeInternal="Both",this.savedShowMode=this.showModeInternal,this.installResizer(this.resizerElementInternal)}isVertical(){return this.isVerticalInternal}setVertical(e){this.isVerticalInternal!==e&&(this.innerSetVertical(e),this.isShowing()&&this.updateLayout())}innerSetVertical(e){this.contentElement.classList.toggle("vbox",!e),this.contentElement.classList.toggle("hbox",e),this.isVerticalInternal=e,this.resizerElementSize=null,this.sidebarSizeDIP=-1,this.restoreSidebarSizeFromSettings(),this.shouldSaveShowMode&&this.restoreAndApplyShowModeFromSettings(),this.updateShowHideSidebarButton(),this.resizerWidget.setVertical(!e),this.invalidateConstraints()}updateLayout(e){this.totalSizeCSS=0,this.totalSizeOtherDimensionCSS=0,this.mainElement.style.removeProperty("width"),this.mainElement.style.removeProperty("height"),this.sidebarElementInternal.style.removeProperty("width"),this.sidebarElementInternal.style.removeProperty("height"),this.innerSetSidebarSizeDIP(this.preferredSidebarSizeDIP(),Boolean(e))}setMainWidget(e){this.mainWidgetInternal!==e&&(this.suspendInvalidations(),this.mainWidgetInternal&&this.mainWidgetInternal.detach(),this.mainWidgetInternal=e,e&&(e.element.slot="insertion-point-main","OnlyMain"!==this.showModeInternal&&"Both"!==this.showModeInternal||e.show(this.element)),this.resumeInvalidations())}setSidebarWidget(e){this.sidebarWidgetInternal!==e&&(this.suspendInvalidations(),this.sidebarWidgetInternal&&this.sidebarWidgetInternal.detach(),this.sidebarWidgetInternal=e,e&&(e.element.slot="insertion-point-sidebar","OnlySidebar"!==this.showModeInternal&&"Both"!==this.showModeInternal||e.show(this.element)),this.resumeInvalidations())}mainWidget(){return this.mainWidgetInternal}sidebarWidget(){return this.sidebarWidgetInternal}sidebarElement(){return this.sidebarElementInternal}childWasDetached(e){this.detaching||(this.mainWidgetInternal===e&&(this.mainWidgetInternal=null),this.sidebarWidgetInternal===e&&(this.sidebarWidgetInternal=null),this.invalidateConstraints())}isSidebarSecond(){return this.secondIsSidebar}enableShowModeSaving(){this.shouldSaveShowMode=!0,this.restoreAndApplyShowModeFromSettings()}showMode(){return this.showModeInternal}setSecondIsSidebar(e){e!==this.secondIsSidebar&&(this.secondIsSidebar=e,this.mainWidgetInternal&&this.mainWidgetInternal.shouldHideOnDetach()?this.sidebarWidgetInternal&&this.sidebarWidgetInternal.shouldHideOnDetach()?(console.error("Could not swap split widget side. Both children widgets contain iframes."),this.secondIsSidebar=!e):e?this.contentElement.insertBefore(this.sidebarElementInternal,this.resizerElementInternal):this.contentElement.insertBefore(this.sidebarElementInternal,this.mainElement):e?this.contentElement.insertBefore(this.mainElement,this.sidebarElementInternal):this.contentElement.insertBefore(this.mainElement,this.resizerElementInternal))}sidebarSide(){return"Both"!==this.showModeInternal?null:this.isVerticalInternal?this.secondIsSidebar?"right":"left":this.secondIsSidebar?"bottom":"top"}resizerElement(){return this.resizerElementInternal}hideMain(e){this.showOnly(this.sidebarWidgetInternal,this.mainWidgetInternal,this.sidebarElementInternal,this.mainElement,e),this.updateShowMode("OnlySidebar")}hideSidebar(e){this.showOnly(this.mainWidgetInternal,this.sidebarWidgetInternal,this.mainElement,this.sidebarElementInternal,e),this.updateShowMode("OnlyMain")}setSidebarMinimized(e){this.sidebarMinimized=e,this.invalidateConstraints()}isSidebarMinimized(){return this.sidebarMinimized}showOnly(e,t,i,n,s){function o(){e&&(e===this.mainWidgetInternal?this.mainWidgetInternal.show(this.element,this.sidebarWidgetInternal?this.sidebarWidgetInternal.element:null):this.sidebarWidgetInternal&&this.sidebarWidgetInternal.show(this.element)),t&&(this.detaching=!0,t.detach(),this.detaching=!1),this.resizerElementInternal.classList.add("hidden"),i.classList.remove("hidden"),i.classList.add("maximized"),n.classList.add("hidden"),n.classList.remove("maximized"),this.removeAllLayoutProperties(),this.doResize(),this.showFinishedForTest()}this.cancelAnimation(),s?this.animate(!0,o.bind(this)):o.call(this),this.sidebarSizeDIP=-1,this.setResizable(!1)}showFinishedForTest(){}removeAllLayoutProperties(){this.sidebarElementInternal.style.removeProperty("flexBasis"),this.mainElement.style.removeProperty("width"),this.mainElement.style.removeProperty("height"),this.sidebarElementInternal.style.removeProperty("width"),this.sidebarElementInternal.style.removeProperty("height"),this.resizerElementInternal.style.removeProperty("left"),this.resizerElementInternal.style.removeProperty("right"),this.resizerElementInternal.style.removeProperty("top"),this.resizerElementInternal.style.removeProperty("bottom"),this.resizerElementInternal.style.removeProperty("margin-left"),this.resizerElementInternal.style.removeProperty("margin-right"),this.resizerElementInternal.style.removeProperty("margin-top"),this.resizerElementInternal.style.removeProperty("margin-bottom")}showBoth(e){"Both"===this.showModeInternal&&(e=!1),this.cancelAnimation(),this.mainElement.classList.remove("maximized","hidden"),this.sidebarElementInternal.classList.remove("maximized","hidden"),this.resizerElementInternal.classList.remove("hidden"),this.setResizable(!0),this.suspendInvalidations(),this.sidebarWidgetInternal&&this.sidebarWidgetInternal.show(this.element),this.mainWidgetInternal&&this.mainWidgetInternal.show(this.element,this.sidebarWidgetInternal?this.sidebarWidgetInternal.element:null),this.resumeInvalidations(),this.setSecondIsSidebar(this.secondIsSidebar),this.sidebarSizeDIP=-1,this.updateShowMode("Both"),this.updateLayout(e)}setResizable(e){this.resizerWidget.setEnabled(e)}isResizable(){return this.resizerWidget.isEnabled()}setSidebarSize(e){const t=qt.instance().cssToDIP(e);this.savedSidebarSizeDIP=t,this.saveSetting(),this.innerSetSidebarSizeDIP(t,!1,!0)}sidebarSize(){const e=Math.max(0,this.sidebarSizeDIP);return qt.instance().dipToCSS(e)}totalSizeDIP(){return this.totalSizeCSS||(this.totalSizeCSS=this.isVerticalInternal?this.contentElement.offsetWidth:this.contentElement.offsetHeight,this.totalSizeOtherDimensionCSS=this.isVerticalInternal?this.contentElement.offsetHeight:this.contentElement.offsetWidth),qt.instance().cssToDIP(this.totalSizeCSS)}updateShowMode(e){this.showModeInternal=e,this.saveShowModeToSettings(),this.updateShowHideSidebarButton(),this.dispatchEventToListeners("ShowModeChanged",e),this.invalidateConstraints()}innerSetSidebarSizeDIP(e,t,i){if("Both"!==this.showModeInternal||!this.isShowing())return;if(e=this.applyConstraints(e,i),this.sidebarSizeDIP===e)return;this.resizerElementSize||(this.resizerElementSize=this.isVerticalInternal?this.resizerElementInternal.offsetWidth:this.resizerElementInternal.offsetHeight),this.removeAllLayoutProperties();const n=Math.round(qt.instance().dipToCSS(e)),s=n+"px",o=this.totalSizeCSS-n+"px";this.sidebarElementInternal.style.flexBasis=s,this.isVerticalInternal?(this.sidebarElementInternal.style.width=s,this.mainElement.style.width=o,this.sidebarElementInternal.style.height=this.totalSizeOtherDimensionCSS+"px",this.mainElement.style.height=this.totalSizeOtherDimensionCSS+"px"):(this.sidebarElementInternal.style.height=s,this.mainElement.style.height=o,this.sidebarElementInternal.style.width=this.totalSizeOtherDimensionCSS+"px",this.mainElement.style.width=this.totalSizeOtherDimensionCSS+"px"),this.isVerticalInternal?this.secondIsSidebar?(this.resizerElementInternal.style.right=s,this.resizerElementInternal.style.marginRight=-this.resizerElementSize/2+"px"):(this.resizerElementInternal.style.left=s,this.resizerElementInternal.style.marginLeft=-this.resizerElementSize/2+"px"):this.secondIsSidebar?(this.resizerElementInternal.style.bottom=s,this.resizerElementInternal.style.marginBottom=-this.resizerElementSize/2+"px"):(this.resizerElementInternal.style.top=s,this.resizerElementInternal.style.marginTop=-this.resizerElementSize/2+"px"),this.sidebarSizeDIP=e,t?this.animate(!1):(this.doResize(),this.dispatchEventToListeners("SidebarSizeChanged",this.sidebarSize()))}animate(e,t){let i;this.animationCallback=t||null,i=this.isVerticalInternal?this.secondIsSidebar?"margin-right":"margin-left":this.secondIsSidebar?"margin-bottom":"margin-top";const n=e?"0":"-"+qt.instance().dipToCSS(this.sidebarSizeDIP)+"px",s=e?"-"+qt.instance().dipToCSS(this.sidebarSizeDIP)+"px":"0";this.contentElement.style.setProperty(i,n),this.contentElement.style.setProperty("overflow","hidden"),e||(Yt(this.mainElement.offsetWidth),Yt(this.sidebarElementInternal.offsetWidth)),!e&&this.sidebarWidgetInternal&&this.sidebarWidgetInternal.doResize(),this.contentElement.style.setProperty("transition",i+" 50ms linear");const o=function(){if(this.animationFrameHandle=0,r){if(!(window.performance.now()<r+50))return this.cancelAnimation(),this.mainWidgetInternal&&this.mainWidgetInternal.doResize(),void this.dispatchEventToListeners("SidebarSizeChanged",this.sidebarSize());this.mainWidgetInternal&&this.mainWidgetInternal.doResize()}else this.contentElement.style.setProperty(i,s),r=window.performance.now();this.animationFrameHandle=this.contentElement.window().requestAnimationFrame(o)}.bind(this);let r=null;this.animationFrameHandle=this.contentElement.window().requestAnimationFrame(o)}cancelAnimation(){this.contentElement.style.removeProperty("margin-top"),this.contentElement.style.removeProperty("margin-right"),this.contentElement.style.removeProperty("margin-bottom"),this.contentElement.style.removeProperty("margin-left"),this.contentElement.style.removeProperty("transition"),this.contentElement.style.removeProperty("overflow"),this.animationFrameHandle&&(this.contentElement.window().cancelAnimationFrame(this.animationFrameHandle),this.animationFrameHandle=0),this.animationCallback&&(this.animationCallback(),this.animationCallback=null)}applyConstraints(e,t){const n=this.totalSizeDIP(),s=this.constraintsInDip?1:qt.instance().zoomFactor();let o=this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new je,r=this.isVertical()?o.minimum.width:o.minimum.height;r||(r=Xt),r*=s,this.sidebarMinimized&&(e=r);let a=this.isVertical()?o.preferred.width:o.preferred.height;a||(a=Xt),a*=s,e<a&&(a=Math.max(e,r)),a+=s,o=this.mainWidgetInternal?this.mainWidgetInternal.constraints():new je;let l=this.isVertical()?o.minimum.width:o.minimum.height;l||(l=Xt),l*=s;let h=this.isVertical()?o.preferred.width:o.preferred.height;h||(h=Xt),h*=s;const d=this.isVertical()?this.savedVerticalMainSize:this.savedHorizontalMainSize;null!==d&&(h=Math.min(h,d*s)),t&&(h=l);const c=h+a;if(c<=n)return i.NumberUtilities.clamp(e,a,n-h);if(l+r<=n){return e=a-(c-n)*a/c,i.NumberUtilities.clamp(e,r,n-l)}return Math.max(0,n-l)}wasShown(){this.forceUpdateLayout(),qt.instance().addEventListener("ZoomChanged",this.onZoomChanged,this)}willHide(){qt.instance().removeEventListener("ZoomChanged",this.onZoomChanged,this)}onResize(){this.updateLayout()}onLayout(){this.updateLayout()}calculateConstraints(){if("OnlyMain"===this.showModeInternal)return this.mainWidgetInternal?this.mainWidgetInternal.constraints():new je;if("OnlySidebar"===this.showModeInternal)return this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new je;let e=this.mainWidgetInternal?this.mainWidgetInternal.constraints():new je,t=this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new je;const i=Xt;return this.isVerticalInternal?(e=e.widthToMax(i).addWidth(1),t=t.widthToMax(i),e.addWidth(t).heightToMax(t)):(e=e.heightToMax(i).addHeight(1),t=t.heightToMax(i),e.widthToMax(t).addHeight(t))}onResizeStart(){this.resizeStartSizeDIP=this.sidebarSizeDIP}onResizeUpdate(e){const t=e.data.currentPosition-e.data.startPosition,i=qt.instance().cssToDIP(t),n=this.secondIsSidebar?this.resizeStartSizeDIP-i:this.resizeStartSizeDIP+i,s=this.applyConstraints(n,!0);this.savedSidebarSizeDIP=s,this.saveSetting(),this.innerSetSidebarSizeDIP(s,!1,!0),this.isVertical()?this.savedVerticalMainSize=this.totalSizeDIP()-this.sidebarSizeDIP:this.savedHorizontalMainSize=this.totalSizeDIP()-this.sidebarSizeDIP}onResizeEnd(){this.resizeStartSizeDIP=0}hideDefaultResizer(e){this.resizerElementInternal.classList.toggle("hidden",Boolean(e)),this.uninstallResizer(this.resizerElementInternal),this.sidebarElementInternal.classList.toggle("no-default-splitter",Boolean(e))}installResizer(e){this.resizerWidget.addElement(e)}uninstallResizer(e){this.resizerWidget.removeElement(e)}hasCustomResizer(){const e=this.resizerWidget.elements();return e.length>1||1===e.length&&e[0]!==this.resizerElementInternal}toggleResizer(e,t){t?this.installResizer(e):this.uninstallResizer(e)}settingForOrientation(){const e=this.setting?this.setting.get():{};return this.isVerticalInternal?e.vertical:e.horizontal}preferredSidebarSizeDIP(){let e=this.savedSidebarSizeDIP;return e||(e=this.isVerticalInternal?this.defaultSidebarWidth:this.defaultSidebarHeight,0<e&&e<1&&(e*=this.totalSizeDIP())),e}restoreSidebarSizeFromSettings(){const e=this.settingForOrientation();this.savedSidebarSizeDIP=e?e.size:0}restoreAndApplyShowModeFromSettings(){const e=this.settingForOrientation();switch(this.savedShowMode=e&&e.showMode?e.showMode:this.showModeInternal,this.showModeInternal=this.savedShowMode,this.savedShowMode){case"Both":this.showBoth();break;case"OnlyMain":this.hideSidebar();break;case"OnlySidebar":this.hideMain()}}saveShowModeToSettings(){this.savedShowMode=this.showModeInternal,this.saveSetting()}saveSetting(){if(!this.setting)return;const e=this.setting.get(),t=(this.isVerticalInternal?e.vertical:e.horizontal)||{};t.size=this.savedSidebarSizeDIP,this.shouldSaveShowMode&&(t.showMode=this.savedShowMode),this.isVerticalInternal?e.vertical=t:e.horizontal=t,this.setting.set(e)}forceUpdateLayout(){this.sidebarSizeDIP=-1,this.updateLayout()}onZoomChanged(){this.forceUpdateLayout()}createShowHideSidebarButton(e,t,i,n,s){return this.showSidebarButtonTitle=e,this.hideSidebarButtonTitle=t,this.shownSidebarString=i,this.hiddenSidebarString=n,this.showHideSidebarButton=new wn("",""),this.showHideSidebarButton.addEventListener("Click",(function(){this.toggleSidebar()}),this),s&&this.showHideSidebarButton.element.setAttribute("jslog",`${o.toggleSubpane().track({click:!0}).context(s)}`),this.updateShowHideSidebarButton(),this.showHideSidebarButton}toggleSidebar(){"Both"!==this.showModeInternal?(this.showBoth(!0),Se(this.shownSidebarString)):(this.hideSidebar(!0),Se(this.hiddenSidebarString))}updateShowHideSidebarButton(){if(!this.showHideSidebarButton)return;const e="OnlyMain"===this.showModeInternal;let t="";t=e?this.isVertical()?this.isSidebarSecond()?"right-panel-open":"left-panel-open":this.isSidebarSecond()?"bottom-panel-open":"top-panel-open":this.isVertical()?this.isSidebarSecond()?"right-panel-close":"left-panel-close":this.isSidebarSecond()?"bottom-panel-close":"top-panel-close",this.showHideSidebarButton.setGlyph(t),this.showHideSidebarButton.setTitle(e?this.showSidebarButtonTitle:this.hideSidebarButtonTitle)}}const Xt=20,Yt=function(e){};var Qt=Object.freeze({__proto__:null,SplitWidget:Gt}),Zt={cssContent:".tabbed-pane{flex:auto;overflow:hidden}.tabbed-pane-content{position:relative;overflow:auto;flex:auto;display:flex;flex-direction:column}.tabbed-pane-content.has-no-tabs{background-color:var(--sys-color-cdt-base-container)}.tabbed-pane-placeholder{font-size:14px;text-align:center;width:fit-content;margin:40px auto 0;text-shadow:var(--color-background-opacity-80) 0 1px 0;line-height:28px}.tabbed-pane-placeholder-row{margin-inline:10px;& span{display:inline-block;padding-inline:10px}&:has(.workspace){max-width:400px;border:2px dashed var(--sys-color-neutral-outline);margin-block-start:20px;padding:10px;margin-inline:20px}& button{cursor:pointer;color:var(--text-link);background:transparent;border:none;padding:0;text-decoration:underline;margin-inline:5px;&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:2px}}}.tabbed-pane-header{display:flex;flex:0 0 27px;border-bottom:1px solid var(--sys-color-divider);overflow:visible;width:100%;background-color:var(--app-color-toolbar-background);& > *{cursor:initial}}.tabbed-pane-header-contents{flex:auto;pointer-events:none;margin-left:0;position:relative;cursor:default}.tabbed-pane-header-contents > *{pointer-events:initial}.tabbed-pane-header-tab-icon{min-width:14px;display:flex;align-items:center;margin-right:2px}.tabbed-pane-header-tab-icon devtools-icon{margin-left:-1px}.tabbed-pane-header-tab{float:left;padding:2px 0.8em;height:26px;line-height:16px;white-space:nowrap;cursor:default;display:flex;align-items:center;color:var(--ui-text)}.tabbed-pane-header-tab.closeable{padding-right:4px}.tabbed-pane-header-tab.preview.closeable{padding-right:5px}.tabbed-pane-header-tab devtools-icon.dot::before{outline-color:var(--icon-gap-toolbar)}.tabbed-pane-header-tab:hover devtools-icon.dot::before{outline-color:var(--icon-gap-toolbar-hover)}.tabbed-pane-header-tab:hover,\n.tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible{color:var(--sys-color-on-surface);background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tab-title{text-overflow:ellipsis;overflow:hidden}.tabbed-pane-header-tab.measuring{visibility:hidden}.tabbed-pane-header-tab.selected{border-bottom:none;color:var(--sys-color-primary)}.tabbed-pane-header-tab.dragging{--override-dragging-box-shadow-color:rgb(0 0 0/37%);position:relative;box-shadow:0 1px 4px 0 var(--override-dragging-box-shadow-color);background-color:var(--sys-color-state-hover-on-subtle)}.-theme-with-dark-background .tabbed-pane-header-tab.dragging,\n:host-context(.-theme-with-dark-background) .tabbed-pane-header-tab.dragging{--override-dragging-box-shadow-color:rgb(230 230 230/37%)}.tabbed-pane-header-tab .tabbed-pane-close-button{margin:0 -3px 0 4px;visibility:hidden}.tabbed-pane-header-tab:hover .tabbed-pane-close-button,\n.tabbed-pane-header-tab.selected .tabbed-pane-close-button{visibility:visible}.tabbed-pane-header-tabs-drop-down-container{float:left;opacity:80%;display:flex;align-items:center;height:100%}.tabbed-pane-header-tabs-drop-down-container > .chevron-icon:hover,\n.tabbed-pane-header-tabs-drop-down-container > .chevron-icon:focus-visible{color:var(--icon-default-hover)}.tabbed-pane-header-tabs-drop-down-container:hover,\n.tabbed-pane-header-tabs-drop-down-container:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tabs-drop-down-container.measuring{visibility:hidden}.tabbed-pane-header-tabs-drop-down-container:active{opacity:80%}.tabbed-pane-shadow.vertical-tab-layout{flex-direction:row!important}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header{background-color:transparent;border:none transparent!important;width:auto;flex:0 0 auto;flex-direction:column;padding-top:10px;padding-right:4px;overflow:hidden}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-content{padding:10px 10px 10px 4px;overflow-x:hidden}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-contents{margin:0;flex:none}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tabs{display:flex;flex-direction:column;width:120px}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tab{border:none transparent;font-weight:normal;text-shadow:none;color:var(--sys-color-token-subtle);height:26px;padding-left:10px;margin:0;display:flex;align-items:center}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tab.selected{color:inherit;border:none transparent;background-color:var(--sys-color-neutral-container)}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tab:focus{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.tabbed-pane-tab-slider{height:2px;position:absolute;bottom:-1px;background-color:var(--sys-color-primary-bright);left:0;transform-origin:0 100%;transition:transform 150ms cubic-bezier(0,0,0.2,1);visibility:hidden}@media (-webkit-min-device-pixel-ratio: 1.1){.tabbed-pane-tab-slider{border-top:none}}.tabbed-pane-tab-slider.enabled{visibility:visible}.tabbed-pane-header-tab.disabled{opacity:50%;pointer-events:none}.tabbed-pane-left-toolbar{margin-right:-4px;flex:none}.tabbed-pane-right-toolbar{margin-left:-4px;flex:none}.preview-icon{--override-tabbed-pane-preview-icon-color:var(--icon-default);width:16px;height:16px;display:flex;align-items:center;justify-content:center;margin-left:3px;flex-shrink:0}.close-button{--tabbed-pane-close-icon-color:var(--icon-default);width:16px;height:16px;display:flex;align-items:center;justify-content:center;border-radius:50%;background-color:transparent;border:none}.close-button:hover,\n.close-button:active,\n.close-button:focus{background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tab.preview.closeable .close-button{margin-left:0}@media (forced-colors: active){.tabbed-pane-tab-slider{forced-color-adjust:none;background-color:Highlight}.tabbed-pane-header{forced-color-adjust:none;border-bottom:1px solid transparent;background-color:ButtonFace}.tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab{background:ButtonFace;color:ButtonText}.tabbed-pane-header-tabs .tabbed-pane-header-tab:hover,\n  .tabbed-pane-header-tabs .tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible{background-color:Highlight;color:HighlightText}.tabbed-pane-header-tab .tabbed-pane-header-tab-title{color:inherit}.tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab.selected,\n  .tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab.selected:focus-visible{background-color:Highlight;color:HighlightText}.tabbed-pane-header-tab:hover .tabbed-pane-close-button,\n  .tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible .tabbed-pane-close-button{color:HighlightText}.tabbed-pane-header-tabs-drop-down-container{opacity:100%}.tabbed-pane-header-tabs-drop-down-container:hover,\n  .tabbed-pane-header-tabs-drop-down-container:focus-visible{background-color:Highlight}.tabbed-pane-header-tabs-drop-down-container > .chevron-icon{color:ButtonText}.tabbed-pane-header-tabs-drop-down-container:hover > .chevron-icon,\n  .tabbed-pane-header-tabs-drop-down-container:focus-visible > .chevron-icon{color:HighlightText}.tabbed-pane-header-tabs .tabbed-pane-header-tab .preview-icon{--override-tabbed-pane-preview-icon-color:ButtonText}.tabbed-pane-header-tab.selected .preview-icon,\n  .tabbed-pane-header-tab:hover .preview-icon{--override-tabbed-pane-preview-icon-color:HighlightText}.close-button{--tabbed-pane-close-icon-color:ButtonText;forced-color-adjust:none}.close-button:hover,\n  .close-button:active{--tabbed-pane-close-icon-color:HighlightText;background-color:Highlight}.selected .close-button{--tabbed-pane-close-icon-color:HighlightText}}"};class Jt{static install(e,t){e.title=t||""}static installWithActionBinding(e,t,i){let n=t;const s=Bs.instance().shortcutsForAction(i);for(const e of s)n+=` - ${e.title()}`;e.title=n}}var ei=Object.freeze({__proto__:null,Tooltip:Jt});const ti={moreTabs:"More tabs",closeS:"Close {PH1}",close:"Close",closeOthers:"Close others",closeTabsToTheRight:"Close tabs to the right",closeAll:"Close all",previewFeature:"Preview feature"},ii=t.i18n.registerUIStrings("ui/legacy/TabbedPane.ts",ti),ni=t.i18n.getLocalizedString.bind(void 0,ii);class si extends(e.ObjectWrapper.eventMixin(Nt)){headerElementInternal;headerContentsElement;tabSlider;tabsElement;contentElementInternal;tabs;tabsHistory;tabsById;currentTabLocked;autoSelectFirstItemOnShow;triggerDropDownTimeout;dropDownButton;currentDevicePixelRatio;shrinkableTabs;verticalTabLayout;closeableTabs;delegate;currentTab;sliderEnabled;placeholderElement;focusedPlaceholderElement;placeholderContainerElement;lastSelectedOverflowTab;overflowDisabled;measuredDropDownButtonWidth;leftToolbarInternal;rightToolbarInternal;allowTabReorder;automaticReorder;constructor(){super(!0),this.registerRequiredCSS(Zt),this.element.classList.add("tabbed-pane"),this.contentElement.classList.add("tabbed-pane-shadow"),this.contentElement.tabIndex=-1,this.setDefaultFocusedElement(this.contentElement),this.headerElementInternal=this.contentElement.createChild("div","tabbed-pane-header"),this.headerContentsElement=this.headerElementInternal.createChild("div","tabbed-pane-header-contents"),this.tabSlider=document.createElement("div"),this.tabSlider.classList.add("tabbed-pane-tab-slider"),this.tabsElement=this.headerContentsElement.createChild("div","tabbed-pane-header-tabs"),this.tabsElement.setAttribute("role","tablist"),this.tabsElement.addEventListener("keydown",this.keyDown.bind(this),!1),this.contentElementInternal=this.contentElement.createChild("div","tabbed-pane-content"),this.contentElementInternal.createChild("slot"),this.tabs=[],this.tabsHistory=[],this.tabsById=new Map,this.currentTabLocked=!1,this.autoSelectFirstItemOnShow=!0,this.triggerDropDownTimeout=null,this.dropDownButton=this.createDropDownButton(),this.currentDevicePixelRatio=window.devicePixelRatio,qt.instance().addEventListener("ZoomChanged",this.zoomChanged,this),this.makeTabSlider()}setAccessibleName(e){be(this.tabsElement,e)}setCurrentTabLocked(e){this.currentTabLocked=e,this.headerElementInternal.classList.toggle("locked",this.currentTabLocked)}setAutoSelectFirstItemOnShow(e){this.autoSelectFirstItemOnShow=e}get visibleView(){return this.currentTab?this.currentTab.view:null}tabIds(){return this.tabs.map((e=>e.id))}tabIndex(e){return this.tabs.findIndex((t=>t.id===e))}tabViews(){return this.tabs.map((e=>e.view))}tabView(e){const t=this.tabsById.get(e);return t?t.view:null}get selectedTabId(){return this.currentTab?this.currentTab.id:null}setShrinkableTabs(e){this.shrinkableTabs=e}makeVerticalTabLayout(){this.verticalTabLayout=!0,this.setTabSlider(!1),this.contentElement.classList.add("vertical-tab-layout"),this.invalidateConstraints()}setCloseableTabs(e){this.closeableTabs=e}focus(){this.visibleView?this.visibleView.focus():this.contentElement.focus()}focusSelectedTabHeader(){const e=this.currentTab;e&&e.tabElement.focus()}headerElement(){return this.headerElementInternal}tabbedPaneContentElement(){return this.contentElementInternal}isTabCloseable(e){const t=this.tabsById.get(e);return!!t&&t.isCloseable()}setTabDelegate(e){const t=this.tabs.slice();for(let i=0;i<t.length;++i)t[i].setDelegate(e);this.delegate=e}appendTab(e,t,i,n,s,r,a,l){const h="boolean"==typeof r?r:Boolean(this.closeableTabs),d=new ri(this,e,t,h,Boolean(a),i,n);d.setDelegate(this.delegate),console.assert(!this.tabsById.has(e),`Tabbed pane already contains a tab with id '${e}'`),this.tabsById.set(e,d),d.tabElement.tabIndex=-1;const c="console-view"===e?"console":e;d.tabElement.setAttribute("jslog",`${o.panelTabHeader().track({click:!0,drag:!0}).context(c)}`),void 0!==l?this.tabs.splice(l,0,d):this.tabs.push(d),this.tabsHistory.push(d),this.tabsHistory[0]===d&&this.isShowing()&&this.selectTab(d.id,s),this.updateTabElements()}closeTab(e,t){this.closeTabs([e],t)}closeTabs(e,t){if(0===e.length)return;const i=this.hasFocus();for(let i=0;i<e.length;++i)this.innerCloseTab(e[i],t);this.updateTabElements(),this.tabsHistory.length&&this.selectTab(this.tabsHistory[0].id,!1),i&&this.focus()}innerCloseTab(e,t){const i=this.tabsById.get(e);if(!i)return;if(t&&!i.closeable)return;this.currentTab&&this.currentTab.id===e&&this.hideCurrentTab(),this.tabsById.delete(e),this.tabsHistory.splice(this.tabsHistory.indexOf(i),1),this.tabs.splice(this.tabs.indexOf(i),1),i.shown&&this.hideTabElement(i);const n={prevTabId:void 0,tabId:e,view:i.view,isUserGesture:t};return this.dispatchEventToListeners(oi.TabClosed,n),!0}hasTab(e){return this.tabsById.has(e)}otherTabs(e){const t=[];for(let i=0;i<this.tabs.length;++i)this.tabs[i].id!==e&&t.push(this.tabs[i].id);return t}tabsToTheRight(e){let t=-1;for(let i=0;i<this.tabs.length;++i)if(this.tabs[i].id===e){t=i;break}return-1===t?[]:this.tabs.slice(t+1).map((function(e){return e.id}))}viewHasFocus(){if(this.visibleView&&this.visibleView.hasFocus())return!0;const e=this.contentElement.getComponentRoot();return e instanceof Document&&this.contentElement===e.activeElement}selectTab(e,t,i){if(this.currentTabLocked)return!1;const n=this.viewHasFocus(),s=this.tabsById.get(e);if(!s)return!1;const o={prevTabId:this.currentTab?this.currentTab.id:void 0,tabId:e,view:s.view,isUserGesture:t};return this.dispatchEventToListeners(oi.TabInvoked,o),this.currentTab&&this.currentTab.id===e||(this.suspendInvalidations(),this.hideCurrentTab(),this.showTab(s),this.resumeInvalidations(),this.currentTab=s,this.tabsHistory.splice(this.tabsHistory.indexOf(s),1),this.tabsHistory.splice(0,0,s),this.updateTabElements(),(n||i)&&this.focus(),this.dispatchEventToListeners(oi.TabSelected,o)),!0}selectNextTab(){const e=this.tabs.indexOf(this.currentTab),t=i.NumberUtilities.mod(e+1,this.tabs.length);this.selectTab(this.tabs[t].id,!0)}selectPrevTab(){const e=this.tabs.indexOf(this.currentTab),t=i.NumberUtilities.mod(e-1,this.tabs.length);this.selectTab(this.tabs[t].id,!0)}lastOpenedTabIds(e){return this.tabsHistory.slice(0,e).map((function(e){return e.id}))}setTabIcon(e,t){const i=this.tabsById.get(e);i&&(i.setIcon(t),this.updateTabElements())}setTabEnabled(e,t){const i=this.tabsById.get(e);i&&i.tabElement.classList.toggle("disabled",!t)}toggleTabClass(e,t,i){const n=this.tabsById.get(e);n&&n.toggleClass(t,i)&&this.updateTabElements()}zoomChanged(){this.clearMeasuredWidths(),this.isShowing()&&this.updateTabElements()}clearMeasuredWidths(){for(let e=0;e<this.tabs.length;++e)delete this.tabs[e].measuredWidth}changeTabTitle(e,t,i){const n=this.tabsById.get(e);n&&void 0!==i&&(n.tooltip=i),n&&n.title!==t&&(n.title=t,be(n.tabElement,t),this.updateTabElements())}changeTabView(e,t){const i=this.tabsById.get(e);if(!i||i.view===t)return;this.suspendInvalidations();const n=this.currentTab&&this.currentTab.id===e,s=i.view.hasFocus();n&&this.hideTab(i),i.view=t,n&&this.showTab(i),s&&i.view.focus(),this.resumeInvalidations()}onResize(){this.currentDevicePixelRatio!==window.devicePixelRatio&&(this.clearMeasuredWidths(),this.currentDevicePixelRatio=window.devicePixelRatio),this.updateTabElements()}headerResized(){this.updateTabElements()}wasShown(){const e=this.currentTab||this.tabsHistory[0];e&&this.autoSelectFirstItemOnShow&&this.selectTab(e.id)}makeTabSlider(){this.verticalTabLayout||this.setTabSlider(!0)}setTabSlider(e){this.sliderEnabled=e,this.tabSlider.classList.toggle("enabled",e)}calculateConstraints(){let e=super.calculateConstraints();const t=new je(new _e(0,0),new _e(50,50));return e=e.widthToMax(t).heightToMax(t),e=this.verticalTabLayout?e.addWidth(new je(new _e(120,0))):e.addHeight(new je(new _e(0,30))),e}updateTabElements(){ts(this,this.innerUpdateTabElements)}setPlaceholderElement(e,t){this.placeholderElement=e,t&&(this.focusedPlaceholderElement=t),this.placeholderContainerElement&&(this.placeholderContainerElement.removeChildren(),this.placeholderContainerElement.appendChild(e))}async waitForTabElementUpdate(){this.innerUpdateTabElements()}innerUpdateTabElements(){this.isShowing()&&(this.tabs.length?(this.contentElementInternal.classList.remove("has-no-tabs"),this.placeholderContainerElement&&(this.placeholderContainerElement.remove(),this.setDefaultFocusedElement(this.contentElement),delete this.placeholderContainerElement)):(this.contentElementInternal.classList.add("has-no-tabs"),this.placeholderElement&&!this.placeholderContainerElement&&(this.placeholderContainerElement=this.contentElementInternal.createChild("div","tabbed-pane-placeholder fill"),this.placeholderContainerElement.appendChild(this.placeholderElement),this.focusedPlaceholderElement&&this.setDefaultFocusedElement(this.focusedPlaceholderElement))),this.measureDropDownButton(),this.adjustToolbarWidth(),this.updateWidths(),this.updateTabsDropDown(),this.updateTabSlider())}adjustToolbarWidth(){if(!this.rightToolbarInternal||!this.measuredDropDownButtonWidth)return;const e=this.leftToolbarInternal?.element.getBoundingClientRect().width??0,t=this.rightToolbarInternal.element.getBoundingClientRect().width,i=this.headerElementInternal.getBoundingClientRect().width;!this.rightToolbarInternal.hasCompactLayout()&&i-t-e<this.measuredDropDownButtonWidth+10?this.rightToolbarInternal.setCompactLayout(!0):this.rightToolbarInternal.hasCompactLayout()&&i-2*t-e>this.measuredDropDownButtonWidth+10&&this.rightToolbarInternal.setCompactLayout(!1)}showTabElement(e,t){e>=this.tabsElement.children.length?this.tabsElement.appendChild(t.tabElement):this.tabsElement.insertBefore(t.tabElement,this.tabsElement.children[e]),t.shown=!0}hideTabElement(e){this.tabsElement.removeChild(e.tabElement),e.shown=!1}createDropDownButton(){const e=document.createElement("div");e.classList.add("tabbed-pane-header-tabs-drop-down-container"),e.setAttribute("jslog",`${o.dropDown("more-tabs").track({click:!0})}`);const t=a.Icon.create("chevron-double-right","chevron-icon"),i=ni(ti.moreTabs);return e.title=i,N(e),be(e,i),ae(e,!1),e.tabIndex=0,e.appendChild(t),e.addEventListener("click",this.dropDownClicked.bind(this)),e.addEventListener("keydown",this.dropDownKeydown.bind(this)),e.addEventListener("mousedown",(e=>{0!==e.button||this.triggerDropDownTimeout||(this.triggerDropDownTimeout=window.setTimeout(this.dropDownClicked.bind(this,e),200))})),e}dropDownClicked(e){const t=e;if(0!==t.button)return;this.triggerDropDownTimeout&&(clearTimeout(this.triggerDropDownTimeout),this.triggerDropDownTimeout=null);const i=this.dropDownButton.getBoundingClientRect(),n=new Ys(t,{useSoftMenu:!1,x:i.left,y:i.bottom,onSoftMenuClosed:()=>{ae(this.dropDownButton,!1)}});for(const e of this.tabs)e.shown||(0===this.numberOfTabsShown()&&this.tabsHistory[0]===e?n.defaultSection().appendCheckboxItem(e.title,this.dropDownMenuItemSelected.bind(this,e),{checked:!0,jslogContext:e.id}):n.defaultSection().appendItem(e.title,this.dropDownMenuItemSelected.bind(this,e),{jslogContext:e.id}));n.show().then((()=>ae(this.dropDownButton,n.isHostedMenuOpen())))}dropDownKeydown(e){i.KeyboardUtilities.isEnterOrSpaceKey(e)&&(this.dropDownButton.click(),e.consume(!0))}dropDownMenuItemSelected(e){this.lastSelectedOverflowTab=e,this.selectTab(e.id,!0,!0)}totalWidth(){return this.headerContentsElement.getBoundingClientRect().width}numberOfTabsShown(){let e=0;for(const t of this.tabs)t.shown&&e++;return e}disableOverflowMenu(){this.overflowDisabled=!0}updateTabsDropDown(){const e=this.tabsToShowIndexes(this.tabs,this.tabsHistory,this.totalWidth(),this.measuredDropDownButtonWidth||0);if(this.lastSelectedOverflowTab&&this.numberOfTabsShown()!==e.length)return delete this.lastSelectedOverflowTab,void this.updateTabsDropDown();for(let t=0;t<this.tabs.length;++t)this.tabs[t].shown&&-1===e.indexOf(t)&&this.hideTabElement(this.tabs[t]);for(let t=0;t<e.length;++t){const i=this.tabs[e[t]];i.shown||this.showTabElement(t,i)}this.overflowDisabled||this.maybeShowDropDown(e.length!==this.tabs.length)}maybeShowDropDown(e){e&&!this.dropDownButton.parentElement?this.headerContentsElement.appendChild(this.dropDownButton):!e&&this.dropDownButton.parentElement&&this.headerContentsElement.removeChild(this.dropDownButton)}measureDropDownButton(){this.overflowDisabled||this.measuredDropDownButtonWidth||(this.dropDownButton.classList.add("measuring"),this.headerContentsElement.appendChild(this.dropDownButton),this.measuredDropDownButtonWidth=this.dropDownButton.getBoundingClientRect().width,this.headerContentsElement.removeChild(this.dropDownButton),this.dropDownButton.classList.remove("measuring"))}updateWidths(){const e=this.measureWidths(),t=this.shrinkableTabs?this.calculateMaxWidth(e.slice(),this.totalWidth()):Number.MAX_VALUE;let i=0;for(const n of this.tabs)n.setWidth(this.verticalTabLayout?-1:Math.min(t,e[i++]))}measureWidths(){this.tabsElement.style.setProperty("width","2000px");const e=new Map;for(const t of this.tabs){if("number"==typeof t.measuredWidth)continue;const i=t.createTabElement(!0);e.set(i,t),this.tabsElement.appendChild(i)}for(const[t,i]of e){const e=t.getBoundingClientRect().width;i.measuredWidth=Math.ceil(e)}for(const t of e.keys())t.remove();const t=[];for(const e of this.tabs)t.push(e.measuredWidth||0);return this.tabsElement.style.removeProperty("width"),t}calculateMaxWidth(e,t){if(!e.length)return 0;e.sort((function(e,t){return e-t}));let i=0;for(let t=0;t<e.length;++t)i+=e[t];if(t>=i)return e[e.length-1];let n=0;for(let s=e.length-1;s>0;--s){const o=e[s]-e[s-1];if(n+=(e.length-s)*o,t+n>=i)return e[s-1]+(t+n-i)/(e.length-s)}return t/e.length}tabsToShowIndexes(e,t,i,n){const s=[];let o=0;const r=e.length,a=e.slice(0);void 0!==this.currentTab&&a.unshift(a.splice(a.indexOf(this.currentTab),1)[0]),void 0!==this.lastSelectedOverflowTab&&a.unshift(a.splice(a.indexOf(this.lastSelectedOverflowTab),1)[0]);for(let l=0;l<r;++l){const h=this.automaticReorder?t[l]:a[l];o+=h.width();let d=o;if(l!==r-1&&(d+=n),!this.verticalTabLayout&&d>i)break;s.push(e.indexOf(h))}return s.sort((function(e,t){return e-t})),s}hideCurrentTab(){this.currentTab&&(this.hideTab(this.currentTab),delete this.currentTab)}showTab(e){e.tabElement.tabIndex=0,e.tabElement.classList.add("selected"),ue(e.tabElement,!0),e.view.show(this.element),this.updateTabSlider()}updateTabSlider(){if(!this.sliderEnabled)return;if(!this.currentTab)return void(this.tabSlider.style.width="0");let e=0;for(let t=0;t<this.tabs.length&&this.currentTab!==this.tabs[t];t++)this.tabs[t].shown&&(e+=this.tabs[t].measuredWidth||0);const t=this.currentTab.shown?this.currentTab.measuredWidth:this.dropDownButton.offsetWidth,i=window.devicePixelRatio>=1.5?" scaleY(0.75)":"";this.tabSlider.style.transform="translateX("+e+"px)"+i,this.tabSlider.style.width=t+"px",this.tabSlider.parentElement!==this.headerContentsElement&&this.headerContentsElement.appendChild(this.tabSlider)}hideTab(e){e.tabElement.removeAttribute("tabIndex"),e.tabElement.classList.remove("selected"),e.tabElement.tabIndex=-1,e.tabElement.setAttribute("aria-selected","false"),e.view.detach()}elementsToRestoreScrollPositionsFor(){return[this.contentElementInternal]}insertBefore(e,t){this.tabsElement.insertBefore(e.tabElement,this.tabsElement.childNodes[t]);const i=this.tabs.indexOf(e);this.tabs.splice(i,1),i<t&&--t,this.tabs.splice(t,0,e);const n={prevTabId:void 0,tabId:e.id,view:e.view,isUserGesture:void 0};this.dispatchEventToListeners(oi.TabOrderChanged,n)}leftToolbar(){return this.leftToolbarInternal||(this.leftToolbarInternal=new gn("tabbed-pane-left-toolbar"),this.headerElementInternal.insertBefore(this.leftToolbarInternal.element,this.headerElementInternal.firstChild)),this.leftToolbarInternal}rightToolbar(){return this.rightToolbarInternal||(this.rightToolbarInternal=new gn("tabbed-pane-right-toolbar"),this.headerElementInternal.appendChild(this.rightToolbarInternal.element)),this.rightToolbarInternal}setAllowTabReorder(e,t){this.allowTabReorder=e,this.automaticReorder=t}keyDown(e){if(!this.currentTab)return;let t=null;switch(e.key){case"ArrowUp":case"ArrowLeft":t=this.currentTab.tabElement.previousElementSibling,t||this.dropDownButton.parentElement||(t=this.currentTab.tabElement.parentElement?this.currentTab.tabElement.parentElement.lastElementChild:null);break;case"ArrowDown":case"ArrowRight":t=this.currentTab.tabElement.nextElementSibling,t||this.dropDownButton.parentElement||(t=this.currentTab.tabElement.parentElement?this.currentTab.tabElement.parentElement.firstElementChild:null);break;case"Enter":case" ":return void this.currentTab.view.focus();default:return}if(!t)return void this.dropDownButton.click();const i=this.tabs.find((e=>e.tabElement===t));i&&this.selectTab(i.id,!0),t.focus()}}var oi;!function(e){e.TabInvoked="TabInvoked",e.TabSelected="TabSelected",e.TabClosed="TabClosed",e.TabOrderChanged="TabOrderChanged"}(oi||(oi={}));class ri{closeable;previewFeature=!1;tabbedPane;idInternal;titleInternal;tooltipInternal;viewInternal;shown;measuredWidth;tabElementInternal;icon=null;widthInternal;delegate;titleElement;dragStartX;constructor(e,t,i,n,s,o,r){this.closeable=n,this.previewFeature=s,this.tabbedPane=e,this.idInternal=t,this.titleInternal=i,this.tooltipInternal=r,this.viewInternal=o,this.shown=!1}get id(){return this.idInternal}get title(){return this.titleInternal}set title(e){if(e!==this.titleInternal){if(this.titleInternal=e,this.titleElement){this.titleElement.textContent=e;const t=this.tabElementInternal?.querySelector(".close-button");t?.setAttribute("title",ni(ti.closeS,{PH1:e})),t?.setAttribute("aria-label",ni(ti.closeS,{PH1:e}))}delete this.measuredWidth}}isCloseable(){return this.closeable}setIcon(e){this.icon=e,this.tabElementInternal&&this.titleElement&&this.createIconElement(this.tabElementInternal,this.titleElement,!1),delete this.measuredWidth}toggleClass(e,t){const i=this.tabElement;return i.classList.contains(e)!==t&&(i.classList.toggle(e,t),delete this.measuredWidth,!0)}get view(){return this.viewInternal}set view(e){this.viewInternal=e}get tooltip(){return this.tooltipInternal}set tooltip(e){this.tooltipInternal=e,this.titleElement&&Jt.install(this.titleElement,e||"")}get tabElement(){return this.tabElementInternal||(this.tabElementInternal=this.createTabElement(!1)),this.tabElementInternal}width(){return this.widthInternal||0}setWidth(e){this.tabElement.style.width=-1===e?"":e+"px",this.widthInternal=e}setDelegate(e){this.delegate=e}createIconElement(e,t,i){const n=ai.get(e);if(n&&(n.remove(),ai.delete(e)),!this.icon)return;const s=document.createElement("span");s.classList.add("tabbed-pane-header-tab-icon");const o=i?this.createMeasureClone(this.icon):this.icon;s.appendChild(o),e.insertBefore(s,t),ai.set(e,s)}createMeasureClone(e){const t=document.createElement("div");return t.style.width=e.style.width,t.style.height=e.style.height,t}createTabElement(e){const t=document.createElement("div");t.classList.add("tabbed-pane-header-tab"),t.id="tab-"+this.idInternal,_(t),ue(t,!1),be(t,this.title);const i=t.createChild("span","tabbed-pane-header-tab-title");if(i.textContent=this.title,Jt.install(i,this.tooltip||""),this.createIconElement(t,i,e),e||(this.titleElement=i),this.previewFeature){const e=this.createPreviewIcon();t.appendChild(e),t.classList.add("preview")}if(this.closeable){const e=this.createCloseIconButton();t.appendChild(e),t.classList.add("closeable")}return e?t.classList.add("measuring"):(t.addEventListener("click",this.tabClicked.bind(this),!1),t.addEventListener("auxclick",this.tabClicked.bind(this),!1),t.addEventListener("mousedown",this.tabMouseDown.bind(this),!1),t.addEventListener("mouseup",this.tabMouseUp.bind(this),!1),t.addEventListener("contextmenu",this.tabContextMenu.bind(this),!1),this.tabbedPane.allowTabReorder&&An(t,this.startTabDragging.bind(this),this.tabDragging.bind(this),this.endTabDragging.bind(this),null,null,200)),t}createCloseIconButton(){const e=document.createElement("button");e.classList.add("close-button","tabbed-pane-close-button"),e.setAttribute("jslog",`${o.close().track({click:!0})}`);const t=new a.Icon.Icon;return t.data={iconName:"cross",color:"var(--tabbed-pane-close-icon-color)",width:"16px"},e.appendChild(t),e.setAttribute("role","button"),e.setAttribute("title",ni(ti.closeS,{PH1:this.title})),e.setAttribute("aria-label",ni(ti.closeS,{PH1:this.title})),e}createPreviewIcon(){const e=document.createElement("div");e.classList.add("preview-icon");const t=new a.Icon.Icon;return t.data={iconName:"experiment",color:"var(--override-tabbed-pane-preview-icon-color)",width:"16px"},e.appendChild(t),e.setAttribute("title",ni(ti.previewFeature)),e.setAttribute("aria-label",ni(ti.previewFeature)),e}isCloseIconClicked(e){return e?.classList.contains("tabbed-pane-close-button")||e?.parentElement?.classList.contains("tabbed-pane-close-button")||!1}tabClicked(e){const t=e,i=1===t.button;this.closeable&&(i||this.isCloseIconClicked(t.target))?(this.closeTabs([this.id]),t.consume(!0)):this.tabbedPane.focus()}tabMouseDown(e){const t=e;this.isCloseIconClicked(t.target)||0!==t.button||this.tabbedPane.selectTab(this.id,!0)}tabMouseUp(e){const t=e;1===t.button&&t.consume(!0)}closeTabs(e){this.delegate?this.delegate.closeTabs(this.tabbedPane,e):this.tabbedPane.closeTabs(e,!0)}tabContextMenu(e){const t=new Ys(e);this.closeable&&(t.defaultSection().appendItem(ni(ti.close),function(){this.closeTabs([this.id])}.bind(this),{jslogContext:"close"}),t.defaultSection().appendItem(ni(ti.closeOthers),function(){this.closeTabs(this.tabbedPane.otherTabs(this.id))}.bind(this),{jslogContext:"close-others"}),t.defaultSection().appendItem(ni(ti.closeTabsToTheRight),function(){this.closeTabs(this.tabbedPane.tabsToTheRight(this.id))}.bind(this),{jslogContext:"close-tabs-to-the-right"}),t.defaultSection().appendItem(ni(ti.closeAll),function(){this.closeTabs(this.tabbedPane.tabIds())}.bind(this),{jslogContext:"close-all"})),this.delegate&&this.delegate.onContextMenu(this.id,t),t.show()}startTabDragging(e){const t=e;return!this.isCloseIconClicked(t.target)&&(this.dragStartX=t.pageX,this.tabElementInternal&&this.tabElementInternal.classList.add("dragging"),this.tabbedPane.tabSlider.remove(),!0)}tabDragging(e){const t=e,i=this.tabbedPane.tabsElement.childNodes;for(let e=0;e<i.length;++e){let n=i[e];if(!this.tabElementInternal||n===this.tabElementInternal)continue;if(!(n.offsetLeft+n.clientWidth>this.tabElementInternal.offsetLeft&&this.tabElementInternal.offsetLeft+this.tabElementInternal.clientWidth>n.offsetLeft))continue;const s=this.dragStartX;if(Math.abs(t.pageX-s)<n.clientWidth/2+5)break;t.pageX-s>0&&(n=n.nextSibling,++e);const o=this.tabElementInternal.offsetLeft;this.tabbedPane.insertBefore(this,e),this.dragStartX=s+this.tabElementInternal.offsetLeft-o;break}const n=this.dragStartX,s=this.tabElementInternal;!s.previousSibling&&t.pageX-n<0||!s.nextSibling&&t.pageX-n>0?s.style.setProperty("left","0px"):s.style.setProperty("left",t.pageX-n+"px")}endTabDragging(e){const t=this.tabElementInternal;t.classList.remove("dragging"),t.style.removeProperty("left"),delete this.dragStartX,this.tabbedPane.updateTabSlider()}}const ai=new WeakMap;var li=Object.freeze({__proto__:null,TabbedPane:si,get Events(){return oi},TabbedPaneTab:ri}),hi={cssContent:".expandable-view-title{display:flex;align-items:center;background-color:var(--sys-color-surface2);height:22px;padding:0 5px;white-space:nowrap;overflow:hidden;position:relative;border-bottom:1px solid transparent}.expandable-view-title.expanded,\n.expandable-view-title:last-child{border-bottom:1px solid var(--sys-color-divider)}.expandable-view-title .toolbar{margin-top:-3px}.expandable-view-title > .toolbar{position:absolute;right:0;top:0}.expandable-view-title:not(.expanded) .toolbar{display:none}.title-expand-icon{margin-right:2px;margin-bottom:-2px}.expandable-view-title:focus-visible{background-color:var(--sys-color-state-focus-highlight)}@media (forced-colors: active){.expandable-view-title:focus-visible{forced-color-adjust:none;color:HighlightText;background-color:Highlight;box-shadow:0 0 0 2px Highlight inset}.expandable-view-title:focus-visible .title-expand-icon{color:HighlightText}}"};const di={elements:"Elements",drawer:"Drawer",drawer_sidebar:"Drawer sidebar",panel:"Panel",network:"Network",settings:"Settings",sources:"Sources"},ci=t.i18n.registerUIStrings("ui/legacy/ViewRegistration.ts",di),ui=t.i18n.getLocalizedString.bind(void 0,ci),mi=[],pi=new Set;function gi(){return mi.filter((e=>n.Runtime.Runtime.isDescriptorEnabled({experiment:e.experiment(),condition:e.condition()})))}const bi=[],fi=new Set;function vi(){return bi}const wi={sPanel:"{PH1} panel"},xi=t.i18n.registerUIStrings("ui/legacy/ViewManager.ts",wi),Ei=t.i18n.getLocalizedString.bind(void 0,xi),Ii={security:!0};class yi{viewRegistration;widgetPromise;constructor(e){this.viewRegistration=e,this.widgetPromise=null}title(){return this.viewRegistration.title()}commandPrompt(){return this.viewRegistration.commandPrompt()}isCloseable(){return"closeable"===this.viewRegistration.persistence}isPreviewFeature(){return Boolean(this.viewRegistration.isPreviewFeature)}isTransient(){return"transient"===this.viewRegistration.persistence}viewId(){return this.viewRegistration.id}location(){return this.viewRegistration.location}order(){return this.viewRegistration.order}settings(){return this.viewRegistration.settings}tags(){if(this.viewRegistration.tags)return this.viewRegistration.tags.map((e=>e())).join("\0")}persistence(){return this.viewRegistration.persistence}async toolbarItems(){if(!this.viewRegistration.hasToolbar)return[];return(await this.widget()).toolbarItems()}widget(){return null===this.widgetPromise&&(this.widgetPromise=this.viewRegistration.loadView()),this.widgetPromise}async disposeView(){if(null===this.widgetPromise)return;const e=await this.widgetPromise;await e.ownerViewDisposed()}experiment(){return this.viewRegistration.experiment}condition(){return this.viewRegistration.condition}}let Si;class Ci{views;locationNameByViewId;locationOverrideSetting;constructor(){this.views=new Map,this.locationNameByViewId=new Map,this.locationOverrideSetting=e.Settings.Settings.instance().createSetting("views-location-override",{});const t=this.locationOverrideSetting.get(),n=new Map;for(const e of gi()){const t=e.location()||"none",i=n.get(t)||[];i.push(e),n.set(t,i)}let s=[];for(const e of n.values())e.sort(((e,t)=>{const i=e.order(),n=t.order();return void 0!==i&&void 0!==n?i-n:0})),s=s.concat(e);for(const e of s){const n=e.viewId(),s=e.location();if(this.views.has(n))throw new Error(`Duplicate view id '${n}'`);if(!i.StringUtilities.isExtendedKebabCase(n))throw new Error(`Invalid view ID '${n}'`);this.views.set(n,e);const o=t[n]||s;this.locationNameByViewId.set(n,o)}}static instance(e={forceNew:null}){const{forceNew:t}=e;return Si&&!t||(Si=new Ci),Si}static removeInstance(){Si=void 0}static createToolbar(e){if(!e.length)return null;const t=new gn("");for(const i of e)t.appendToolbarItem(i);return t.element}locationNameForViewId(e){const t=this.locationNameByViewId.get(e);if(!t)throw new Error(`No location name for view with id ${e}`);return t}moveView(e,t,i){const{shouldSelectTab:n,overrideSaving:s}=i||{shouldSelectTab:!0,overrideSaving:!1};if(!e||!t)return;const o=this.view(e);if(o){if(!s){this.locationNameByViewId.set(e,t);const i=this.locationOverrideSetting.get();i[e]=t,this.locationOverrideSetting.set(i)}this.resolveLocation(t).then((t=>{if(!t)throw new Error("Move view: Could not resolve location for view: "+e);return t.reveal(),t.showView(o,void 0,!0,!1,n)}))}}revealView(e){const t=Di.get(e);return t?(t.reveal(),t.showView(e)):Promise.resolve()}showViewInLocation(e,t,i=!0){this.moveView(e,t,{shouldSelectTab:i,overrideSaving:!0})}view(e){const t=this.views.get(e);if(!t)throw new Error(`No view with id ${e} found!`);return t}materializedWidget(e){const t=this.view(e);return t&&Ti.get(t)||null}async showView(e,t,i){const n=this.views.get(e);if(!n)return void console.error("Could not find view for id: '"+e+"' "+(new Error).stack);const s=Di.get(n)??await this.resolveLocation(this.locationNameByViewId.get(e));if(!s)throw new Error("Could not resolve location for view: "+e);s.reveal(),await s.showView(n,void 0,t,i)}async resolveLocation(e){if(!e)return Promise.resolve(null);const t=vi().filter((t=>t.name===e));if(t.length>1)throw new Error("Duplicate resolver for location: "+e);if(t.length){return(await t[0].loadResolver()).resolveLocation(e)}throw new Error("Unresolved location: "+e)}createTabbedLocation(e,t,i,n,s){return new Ai(this,e,t,i,n,s)}createStackLocation(e,t,i){return new Ri(this,e,t,i)}hasViewsForLocation(e){return Boolean(this.viewsForLocation(e).length)}viewsForLocation(e){const t=[];for(const[i,n]of this.views.entries())this.locationNameByViewId.get(i)===e&&t.push(n);return t}}const Ti=new WeakMap;class ki extends Nt{view;materializePromise;constructor(e){super(),this.element.classList.add("flex-auto","view-container","overflow-auto"),this.view=e,this.element.tabIndex=-1,j(this.element),be(this.element,Ei(wi.sPanel,{PH1:e.title()})),this.setDefaultFocusedElement(this.element)}materialize(){if(this.materializePromise)return this.materializePromise;const e=[];return e.push(this.view.toolbarItems().then((e=>{const t=Ci.createToolbar(e);t&&this.element.insertBefore(t,this.element.firstChild)}))),e.push(this.view.widget().then((e=>{const t=this.element.hasFocus();this.setDefaultFocusedElement(null),Ti.set(this.view,e),e.show(this.element),t&&e.focus()}))),this.materializePromise=Promise.all(e).then((()=>{})),this.materializePromise}wasShown(){this.materialize().then((()=>{const e=Ti.get(this.view);e&&(e.show(this.element),this.wasShownForTest())}))}wasShownForTest(){}}class Mi extends Nt{titleElement;titleExpandIcon;view;widget;materializePromise;constructor(e){super(!0),this.element.classList.add("flex-none"),this.registerRequiredCSS(hi),this.titleElement=document.createElement("div"),this.titleElement.classList.add("expandable-view-title"),this.titleElement.setAttribute("jslog",`${o.sectionHeader().context(e.viewId()).track({click:!0})}`),U(this.titleElement),this.titleExpandIcon=a.Icon.create("triangle-right","title-expand-icon"),this.titleElement.appendChild(this.titleExpandIcon);const t=e.title();ss(this.titleElement,t),be(this.titleElement,t),ae(this.titleElement,!1),this.titleElement.tabIndex=0,self.onInvokeElement(this.titleElement,this.toggleExpanded.bind(this)),this.titleElement.addEventListener("keydown",this.onTitleKeyDown.bind(this),!1),this.contentElement.insertBefore(this.titleElement,this.contentElement.firstChild),re(this.titleElement,this.contentElement.createChild("slot")),this.view=e,Li.set(e,this)}wasShown(){this.widget&&this.materializePromise&&this.materializePromise.then((()=>{this.titleElement.classList.contains("expanded")&&this.widget&&this.widget.show(this.element)}))}materialize(){if(this.materializePromise)return this.materializePromise;const e=[];return e.push(this.view.toolbarItems().then((e=>{const t=Ci.createToolbar(e);t&&this.titleElement.appendChild(t)}))),e.push(this.view.widget().then((e=>{this.widget=e,Ti.set(this.view,e),e.show(this.element)}))),this.materializePromise=Promise.all(e).then((()=>{})),this.materializePromise}expand(){return this.titleElement.classList.contains("expanded")?this.materialize():(this.titleElement.classList.add("expanded"),ae(this.titleElement,!0),this.titleExpandIcon.name="triangle-down",this.materialize().then((()=>{this.widget&&this.widget.show(this.element)})))}collapse(){this.titleElement.classList.contains("expanded")&&(this.titleElement.classList.remove("expanded"),ae(this.titleElement,!1),this.titleExpandIcon.name="triangle-right",this.materialize().then((()=>{this.widget&&this.widget.detach()})))}toggleExpanded(e){"keydown"===e.type&&e.target!==this.titleElement||(this.titleElement.classList.contains("expanded")?this.collapse():this.expand())}onTitleKeyDown(e){if(e.target!==this.titleElement)return;const t=e;"ArrowLeft"===t.key?this.collapse():"ArrowRight"===t.key&&(this.titleElement.classList.contains("expanded")?this.widget&&this.widget.focus():this.expand())}}const Li=new WeakMap;class Pi{manager;revealCallback;widgetInternal;constructor(e,t,i){this.manager=e,this.revealCallback=i,this.widgetInternal=t}widget(){return this.widgetInternal}reveal(){this.revealCallback&&this.revealCallback()}showView(e,t,i,n,s){throw new Error("not implemented")}removeView(e){throw new Error("not implemented")}}const Di=new WeakMap;class Ai extends Pi{tabbedPaneInternal;allowReorder;closeableTabSetting;tabOrderSetting;lastSelectedTabSetting;defaultTab;views;constructor(t,i,n,s,o,r){const a=new si;o&&a.setAllowTabReorder(!0),super(t,a,i),this.tabbedPaneInternal=a,this.allowReorder=o,this.tabbedPaneInternal.addEventListener(oi.TabSelected,this.tabSelected,this),this.tabbedPaneInternal.addEventListener(oi.TabClosed,this.tabClosed,this),this.closeableTabSetting=e.Settings.Settings.instance().createSetting("closeable-tabs",{}),this.setOrUpdateCloseableTabsSetting(),this.tabOrderSetting=e.Settings.Settings.instance().createSetting(n+"-tab-order",{}),this.tabbedPaneInternal.addEventListener(oi.TabOrderChanged,this.persistTabOrder,this),s&&(this.lastSelectedTabSetting=e.Settings.Settings.instance().createSetting(n+"-selected-tab","")),this.defaultTab=r,this.views=new Map,n&&this.appendApplicableItems(n)}setOrUpdateCloseableTabsSetting(){const e={...Ii,...this.closeableTabSetting.get()};this.closeableTabSetting.set(e)}widget(){return this.tabbedPaneInternal}tabbedPane(){return this.tabbedPaneInternal}enableMoreTabsButton(){const e=new En(this.appendTabsToMenu.bind(this),void 0,"more-tabs");return this.tabbedPaneInternal.leftToolbar().appendToolbarItem(e),this.tabbedPaneInternal.disableOverflowMenu(),e}appendApplicableItems(e){const t=this.manager.viewsForLocation(e);if(this.allowReorder){let e=0;const i=this.tabOrderSetting.get(),n=new Map;for(const s of t)n.set(s.viewId(),i[s.viewId()]||++e*Ai.orderStep);t.sort(((e,t)=>n.get(e.viewId())-n.get(t.viewId())))}for(const e of t){const t=e.viewId();this.views.set(t,e),Di.set(e,this),e.isTransient()||(e.isCloseable()?this.closeableTabSetting.get()[t]&&this.appendTab(e):this.appendTab(e))}if(this.defaultTab)if(this.tabbedPaneInternal.hasTab(this.defaultTab))this.tabbedPaneInternal.selectTab(this.defaultTab);else{const e=Array.from(this.views.values()).find((e=>e.viewId()===this.defaultTab));e&&this.showView(e)}else this.lastSelectedTabSetting&&this.tabbedPaneInternal.hasTab(this.lastSelectedTabSetting.get())&&this.tabbedPaneInternal.selectTab(this.lastSelectedTabSetting.get())}appendTabsToMenu(e){const t=Array.from(this.views.values());t.sort(((e,t)=>e.title().localeCompare(t.title())));for(const i of t){const t=i.title();"issues-pane"!==i.viewId()?e.defaultSection().appendItem(t,this.showView.bind(this,i,void 0,!0),{jslogContext:i.viewId()}):e.defaultSection().appendItem(t,(()=>{s.userMetrics.issuesPanelOpenedFrom(3),this.showView(i,void 0,!0)}),{jslogContext:"issues-pane"})}}appendTab(e,t){this.tabbedPaneInternal.appendTab(e.viewId(),e.title(),new ki(e),void 0,!1,e.isCloseable()||e.isTransient(),e.isPreviewFeature(),t)}appendView(e,t){if(this.tabbedPaneInternal.hasTab(e.viewId()))return;const i=Di.get(e);let n;i&&i!==this&&i.removeView(e),Di.set(e,this),this.manager.views.set(e.viewId(),e),this.views.set(e.viewId(),e);const s=this.tabbedPaneInternal.tabIds();if(this.allowReorder){const t=this.tabOrderSetting.get(),i=t[e.viewId()];for(let e=0;i&&e<s.length;++e)if(t[s[e]]&&t[s[e]]>i){n=e;break}}else if(t)for(let e=0;e<s.length;++e)if(s[e]===t.viewId()){n=e;break}if(this.appendTab(e,n),e.isCloseable()){const t=this.closeableTabSetting.get(),i=e.viewId();t[i]||(t[i]=!0,this.closeableTabSetting.set(t))}this.persistTabOrder()}async showView(e,t,i,n,s=!0){this.appendView(e,t),s&&this.tabbedPaneInternal.selectTab(e.viewId(),i),n||this.tabbedPaneInternal.focus();const o=this.tabbedPaneInternal.tabView(e.viewId());await o.materialize()}removeView(e){this.tabbedPaneInternal.hasTab(e.viewId())&&(Di.delete(e),this.manager.views.delete(e.viewId()),this.tabbedPaneInternal.closeTab(e.viewId()),this.views.delete(e.viewId()))}tabSelected(e){const{tabId:t}=e.data;this.lastSelectedTabSetting&&e.data.isUserGesture&&this.lastSelectedTabSetting.set(t)}tabClosed(e){const{tabId:t}=e.data,i=this.closeableTabSetting.get();i[t]&&(i[t]=!1,this.closeableTabSetting.set(i));const n=this.views.get(t);n&&n.disposeView()}persistTabOrder(){const e=this.tabbedPaneInternal.tabIds(),t={};for(let i=0;i<e.length;i++)t[e[i]]=(i+1)*Ai.orderStep;const i=this.tabOrderSetting.get(),n=Object.keys(i);n.sort(((e,t)=>i[e]-i[t]));let s=0;for(const e of n)e in t?s=t[e]:t[e]=++s;this.tabOrderSetting.set(t)}getCloseableTabSetting(){return this.closeableTabSetting.get()}static orderStep=10}class Ri extends Pi{vbox;expandableContainers;constructor(e,t,i,n){const s=new Nt;s.element.setAttribute("jslog",`${o.pane(n||"sidebar").track({resize:!0})}`),super(e,s,t),this.vbox=s,V(s.element),this.expandableContainers=new Map,i&&this.appendApplicableItems(i)}appendView(e,t){const i=Di.get(e);i&&i!==this&&i.removeView(e);let n=this.expandableContainers.get(e.viewId());if(!n){Di.set(e,this),this.manager.views.set(e.viewId(),e),n=new Mi(e);let i=null;if(t){const e=Li.get(t);i=e?e.element:null}n.show(this.vbox.contentElement,i),this.expandableContainers.set(e.viewId(),n)}}async showView(e,t){this.appendView(e,t);const i=this.expandableContainers.get(e.viewId());i&&await i.expand()}removeView(e){const t=this.expandableContainers.get(e.viewId());t&&(t.detach(),this.expandableContainers.delete(e.viewId()),Di.delete(e),this.manager.views.delete(e.viewId()))}appendApplicableItems(e){for(const t of this.manager.viewsForLocation(e))this.appendView(t)}}var Bi=Object.freeze({__proto__:null,defaultOptionsForTabs:Ii,PreRegisteredView:yi,ViewManager:Ci,ContainerWidget:ki,getRegisteredViewExtensions:gi,maybeRemoveViewExtension:function(e){const t=mi.findIndex((t=>t.viewId()===e));return!(t<0||!pi.delete(e))&&(mi.splice(t,1),!0)},registerViewExtension:function(e){const t=e.id;if(pi.has(t))throw new Error(`Duplicate view id '${t}'`);pi.add(t),mi.push(new yi(e))},getRegisteredLocationResolvers:vi,registerLocationResolver:function(e){const t=e.name;if(fi.has(t))throw new Error(`Duplicate view location name registration '${t}'`);fi.add(t),bi.push(e)},getLocalizedViewLocationCategory:function(e){switch(e){case"ELEMENTS":return ui(di.elements);case"DRAWER":return ui(di.drawer);case"DRAWER_SIDEBAR":return ui(di.drawer_sidebar);case"PANEL":return ui(di.panel);case"NETWORK":return ui(di.network);case"SETTINGS":return ui(di.settings);case"SOURCES":return ui(di.sources);case"":return t.i18n.lockedString("")}},resetViewRegistration:function(){mi.length=0,bi.length=0,fi.clear(),pi.clear()}});const Oi={moreTools:"More Tools",closeDrawer:"Close drawer",panels:"Panels",reloadDevtools:"Reload DevTools",moveToTop:"Move to top",moveToBottom:"Move to bottom",devToolsLanguageMissmatch:"DevTools is now available in {PH1}!",setToBrowserLanguage:"Always match Chrome's language",setToSpecificLanguage:"Switch DevTools to {PH1}",mainToolbar:"Main toolbar",drawer:"Tool drawer",drawerShown:"Drawer shown",drawerHidden:"Drawer hidden",selectOverrideFolder:"Select a folder to store override files in.",selectFolder:"Select folder"},Fi=t.i18n.registerUIStrings("ui/legacy/InspectorView.ts",Oi),zi=t.i18n.getLocalizedString.bind(void 0,Fi);let Wi=null;class Hi extends Nt{drawerSplitWidget;tabDelegate;drawerTabbedLocation;drawerTabbedPane;infoBarDiv;tabbedLocation;tabbedPane;keyDownBound;currentPanelLocked;focusRestorer;ownerSplitWidget;reloadRequiredInfobar;#t;constructor(){super(),Ts.setContainer(this.element),this.setMinimumSize(250,72),this.drawerSplitWidget=new Gt(!1,!0,"inspector.drawer-split-view-state",200,200),this.drawerSplitWidget.hideSidebar(),this.drawerSplitWidget.enableShowModeSaving(),this.drawerSplitWidget.show(this.element),this.tabDelegate=new ji,this.drawerTabbedLocation=Ci.instance().createTabbedLocation(this.showDrawer.bind(this,{focus:!1,hasTargetDrawer:!0}),"drawer-view",!0,!0);this.drawerTabbedLocation.enableMoreTabsButton().setTitle(zi(Oi.moreTools)),this.drawerTabbedPane=this.drawerTabbedLocation.tabbedPane(),this.drawerTabbedPane.setMinimumSize(0,27),this.drawerTabbedPane.element.classList.add("drawer-tabbed-pane"),this.drawerTabbedPane.element.setAttribute("jslog",`${o.drawer()}`);const i=new wn(zi(Oi.closeDrawer),"cross");i.element.setAttribute("jslog",`${o.close().track({click:!0})}`),i.addEventListener("Click",this.closeDrawer,this),this.drawerTabbedPane.addEventListener(oi.TabSelected,(e=>this.tabSelected(e.data.tabId,"drawer")),this);const r=this.drawerTabbedPane.selectedTabId;"OnlyMain"!==this.drawerSplitWidget.showMode()&&r&&(s.userMetrics.panelShown(r,!0),s.userMetrics.panelShownInLocation(r,"drawer")),this.drawerTabbedPane.setTabDelegate(this.tabDelegate);const a=this.drawerTabbedPane.element;Y(a),be(a,zi(Oi.drawer)),this.drawerSplitWidget.installResizer(this.drawerTabbedPane.headerElement()),this.drawerSplitWidget.setSidebarWidget(this.drawerTabbedPane),this.drawerTabbedPane.rightToolbar().appendToolbarItem(i),this.drawerTabbedPane.headerElement().setAttribute("jslog",`${o.toolbar("drawer").track({drag:!0})}`),this.tabbedLocation=Ci.instance().createTabbedLocation(s.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront.bind(s.InspectorFrontendHost.InspectorFrontendHostInstance),"panel",!0,!0,n.Runtime.Runtime.queryParam("panel")),this.tabbedPane=this.tabbedLocation.tabbedPane(),this.tabbedPane.element.classList.add("main-tabbed-pane");const l=n.Runtime.conditions.canDock()?"69px":"41px";this.tabbedPane.leftToolbar().element.style.minWidth=l,this.tabbedPane.registerRequiredCSS(St),this.tabbedPane.addEventListener(oi.TabSelected,(e=>this.tabSelected(e.data.tabId,"main")),this);const h=this.tabbedPane.selectedTabId;h&&(s.userMetrics.panelShown(h,!0),s.userMetrics.panelShownInLocation(h,"main")),this.tabbedPane.setAccessibleName(zi(Oi.panels)),this.tabbedPane.setTabDelegate(this.tabDelegate);const d=this.tabbedPane.headerElement();if(Q(d),be(d,zi(Oi.mainToolbar)),d.setAttribute("jslog",`${o.toolbar("main").track({drag:!0})}`),s.userMetrics.setLaunchPanel(this.tabbedPane.selectedTabId),s.InspectorFrontendHost.isUnderTest()&&this.tabbedPane.setAutoSelectFirstItemOnShow(!1),this.drawerSplitWidget.setMainWidget(this.tabbedPane),this.keyDownBound=this.keyDown.bind(this),s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.ShowPanel,function({data:e}){this.showPanel(e)}.bind(this)),function(){if(Ni().get())return!1;const i=e.Settings.Settings.instance().moduleSetting("language").get();if("en-US"!==i)return!1;return!t.DevToolsLocale.localeLanguagesMatch(navigator.language,i)&&t.DevToolsLocale.DevToolsLocale.instance().languageIsSupportedByDevTools(navigator.language)}()){const i=function(){const i=t.DevToolsLocale.DevToolsLocale.instance(),n=i.lookupClosestDevToolsLocale(navigator.language),s=new Intl.Locale(n),o=new Intl.DisplayNames([i.locale],{type:"language"}).of(s.language||"en")||"English",r=e.Settings.Settings.instance().moduleSetting("language");return new It("info",zi(Oi.devToolsLanguageMissmatch,{PH1:o}),[{text:zi(Oi.setToBrowserLanguage),highlight:!0,delegate:()=>{r.set("browserLanguage"),Ni().set(!0),_i()},dismiss:!0,jslogContext:"set-to-browser-language"},{text:zi(Oi.setToSpecificLanguage,{PH1:o}),highlight:!0,delegate:()=>{r.set(n),Ni().set(!0),_i()},dismiss:!0,jslogContext:"set-to-specific-language"}],Ni(),void 0,"language-mismatch")}();i.setParentView(this),this.attachInfobar(i)}}static instance(e={forceNew:null}){const{forceNew:t}=e;return Wi&&!t||(Wi=new Hi),Wi}static maybeGetInspectorViewInstance(){return Wi}static removeInstance(){Wi=null}wasShown(){this.element.ownerDocument.addEventListener("keydown",this.keyDownBound,!1)}willHide(){this.element.ownerDocument.removeEventListener("keydown",this.keyDownBound,!1)}resolveLocation(e){return"drawer-view"===e?this.drawerTabbedLocation:"panel"===e?this.tabbedLocation:null}async createToolbars(){await this.tabbedPane.leftToolbar().appendItemsAtLocation("main-toolbar-left"),await this.tabbedPane.rightToolbar().appendItemsAtLocation("main-toolbar-right")}addPanel(e){this.tabbedLocation.appendView(e)}hasPanel(e){return this.tabbedPane.hasTab(e)}async panel(e){const t=Ci.instance().view(e);if(!t)throw new Error(`Expected view for panel '${e}'`);return t.widget()}onSuspendStateChanged(e){this.currentPanelLocked=e,this.tabbedPane.setCurrentTabLocked(this.currentPanelLocked),this.tabbedPane.leftToolbar().setEnabled(!this.currentPanelLocked),this.tabbedPane.rightToolbar().setEnabled(!this.currentPanelLocked)}canSelectPanel(e){return!this.currentPanelLocked||this.tabbedPane.selectedTabId===e}async showPanel(e){await Ci.instance().showView(e)}setPanelWarnings(e,t){const i=this.getTabbedPaneForTabId(e);if(i){let n=null;if(0!==t.length){const e=1===t.length?t[0]:"· "+t.join("\n· ");n=a.Icon.create("warning-filled"),Jt.install(n,e)}i.setTabIcon(e,n)}}emitDrawerChangeEvent(e){const t=new CustomEvent("drawerchange",{bubbles:!0,cancelable:!0,detail:{isDrawerOpen:e}});document.body.dispatchEvent(t)}getTabbedPaneForTabId(e){return this.tabbedPane.hasTab(e)?this.tabbedPane:this.drawerTabbedPane.hasTab(e)?this.drawerTabbedPane:null}currentPanelDeprecated(){return Ci.instance().materializedWidget(this.tabbedPane.selectedTabId||"")}showDrawer({focus:e,hasTargetDrawer:t}){this.drawerTabbedPane.isShowing()||(this.drawerTabbedPane.setAutoSelectFirstItemOnShow(!t),this.drawerSplitWidget.showBoth(),this.focusRestorer=e?new jt(this.drawerTabbedPane):null,this.emitDrawerChangeEvent(!0),Se(zi(Oi.drawerShown)))}drawerVisible(){return this.drawerTabbedPane.isShowing()}closeDrawer(){this.drawerTabbedPane.isShowing()&&(this.focusRestorer&&this.focusRestorer.restore(),this.drawerSplitWidget.hideSidebar(!0),this.emitDrawerChangeEvent(!1),Se(zi(Oi.drawerHidden)))}setDrawerMinimized(e){this.drawerSplitWidget.setSidebarMinimized(e),this.drawerSplitWidget.setResizable(!e)}isDrawerMinimized(){return this.drawerSplitWidget.isSidebarMinimized()}closeDrawerTab(e,t){this.drawerTabbedPane.closeTab(e,t),s.userMetrics.panelClosed(e)}keyDown(t){const i=t;if(!Ke.eventHasCtrlEquivalentKey(i)||i.altKey||i.shiftKey)return;if(e.Settings.moduleSetting("shortcut-panel-switch").get()){let e=-1;if(i.keyCode>48&&i.keyCode<58?e=i.keyCode-49:i.keyCode>96&&i.keyCode<106&&i.location===KeyboardEvent.DOM_KEY_LOCATION_NUMPAD&&(e=i.keyCode-97),-1!==e){const i=this.tabbedPane.tabIds()[e];i&&(Ds.hasInstance()||this.currentPanelLocked||this.showPanel(i),t.consume(!0))}}}onResize(){Ts.containerMoved(this.element)}topResizerElement(){return this.tabbedPane.headerElement()}toolbarItemResized(){this.tabbedPane.headerResized()}tabSelected(e,t){s.userMetrics.panelShown(e),s.userMetrics.panelShownInLocation(e,t)}setOwnerSplit(e){this.ownerSplitWidget=e}ownerSplit(){return this.ownerSplitWidget||null}minimize(){this.ownerSplitWidget&&this.ownerSplitWidget.setSidebarMinimized(!0)}restore(){this.ownerSplitWidget&&this.ownerSplitWidget.setSidebarMinimized(!1)}displayReloadRequiredWarning(e){if(!this.reloadRequiredInfobar){const t=new It("info",e,[{text:zi(Oi.reloadDevtools),highlight:!0,delegate:()=>_i(),dismiss:!1,jslogContext:"main.debug-reload"}],void 0,void 0,"reload-required");t.setParentView(this),this.attachInfobar(t),this.reloadRequiredInfobar=t,t.setCloseCallback((()=>{delete this.reloadRequiredInfobar}))}}displaySelectOverrideFolderInfobar(e){if(!this.#t){const t=new It("info",zi(Oi.selectOverrideFolder),[{text:zi(Oi.selectFolder),highlight:!0,delegate:()=>e(),dismiss:!0,jslogContext:"select-folder"}],void 0,void 0,"select-override-folder");t.setParentView(this),this.attachInfobar(t),this.#t=t,t.setCloseCallback((()=>{this.#t=void 0}))}}createInfoBarDiv(){this.infoBarDiv||(this.infoBarDiv=document.createElement("div"),this.infoBarDiv.classList.add("flex-none"),this.contentElement.insertBefore(this.infoBarDiv,this.contentElement.firstChild))}attachInfobar(e){this.createInfoBarDiv(),this.infoBarDiv?.appendChild(e.element)}}function Ni(){return e.Settings.Settings.instance().createSetting("disable-locale-info-bar",!1)}function _i(){mt.instance().canDock()&&"undocked"===mt.instance().dockSide()&&s.InspectorFrontendHost.InspectorFrontendHostInstance.setIsDocked(!0,(function(){})),s.InspectorFrontendHost.InspectorFrontendHostInstance.reattach((()=>window.location.reload()))}class ji{closeTabs(e,t){e.closeTabs(t,!0),t.forEach((e=>{s.userMetrics.panelClosed(e)}))}moveToDrawer(e){s.userMetrics.actionTaken(s.UserMetrics.Action.TabMovedToDrawer),Ci.instance().moveView(e,"drawer-view")}moveToMainPanel(e){s.userMetrics.actionTaken(s.UserMetrics.Action.TabMovedToMainPanel),Ci.instance().moveView(e,"panel")}onContextMenu(e,t){if("console"===e||"console-view"===e)return;"drawer-view"===Ci.instance().locationNameForViewId(e)?t.defaultSection().appendItem(zi(Oi.moveToTop),this.moveToMainPanel.bind(this,e),{jslogContext:"move-to-top"}):t.defaultSection().appendItem(zi(Oi.moveToBottom),this.moveToDrawer.bind(this,e),{jslogContext:"move-to-bottom"})}}var Vi=Object.freeze({__proto__:null,InspectorView:Hi,ActionDelegate:class{handleAction(e,t){switch(t){case"main.toggle-drawer":return Hi.instance().drawerVisible()?Hi.instance().closeDrawer():Hi.instance().showDrawer({focus:!0,hasTargetDrawer:!1}),!0;case"main.next-tab":return Hi.instance().tabbedPane.selectNextTab(),Hi.instance().tabbedPane.focus(),!0;case"main.previous-tab":return Hi.instance().tabbedPane.selectPrevTab(),Hi.instance().tabbedPane.focus(),!0}return!1}},InspectorViewTabDelegate:ji});const Ui={srequiresReload:"*Requires reload",oneOrMoreSettingsHaveChanged:"One or more settings have changed which requires a reload to take effect."},Ki=t.i18n.registerUIStrings("ui/legacy/SettingsUI.ts",Ui),qi=t.i18n.getLocalizedString.bind(void 0,Ki),$i=function(e,t,i){const n=e;function o(){n.checked!==t.get()&&(n.checked=t.get())}t.addChangeListener(o),o(),n.addEventListener("change",(function(){t.get()!==n.checked&&t.set(n.checked),t.get()&&i?.enable&&s.userMetrics.actionTaken(i.enable),!t.get()&&i?.disable&&s.userMetrics.actionTaken(i.disable),i?.toggle&&s.userMetrics.actionTaken(i.toggle)}),!1)};var Gi,Xi=Object.freeze({__proto__:null,createSettingCheckbox:function(e,t,i,n){const s=ls.create(e,void 0,void 0,t.name);n&&Jt.install(s,n);const o=s.checkboxElement;if(o.name=e,$i(o,t),i)return s;const r=document.createElement("p");return r.appendChild(s),r},bindCheckbox:$i,createCustomSetting:function(e,t){const i=document.createElement("p");i.classList.add("settings-select");const n=i.createChild("label");return n.textContent=e,B(n,t),i.appendChild(t),i},createControlForSetting:function(e,t){const n=e.title();switch(e.type()){case"boolean":{const t=new h.SettingCheckbox.SettingCheckbox;return t.data={setting:e},t.onchange=()=>{Hi.instance().displayReloadRequiredWarning(qi(Ui.oneOrMoreSettingsHaveChanged))},t}case"enum":return Array.isArray(e.options())?function(e,t,n,s,r){const a=document.createElement("div"),l=a.createChild("p");l.classList.add("settings-select");const d=l.createChild("label"),c=l.createChild("select","chrome-select");d.textContent=e,r&&(a.classList.add("chrome-select-label"),d.createChild("p").textContent=r),c.setAttribute("jslog",`${o.dropDown().track({change:!0}).context(s.name)}`),B(d,c);for(const e of t)e.text&&"string"==typeof e.value&&c.add(as(e.text,e.value,i.StringUtilities.toKebabCase(e.value)));let u=null;n&&(u=a.createChild("span","reload-warning hidden"),u.textContent=qi(Ui.srequiresReload),O(u));const{deprecation:m}=s;if(m){const e=new h.SettingDeprecationWarning.SettingDeprecationWarning;e.data=m,d.appendChild(e)}return s.addChangeListener(p),p(),c.addEventListener("change",(function(){s.set(t[c.selectedIndex].value),u&&(u.classList.remove("hidden"),Hi.instance().displayReloadRequiredWarning(qi(Ui.oneOrMoreSettingsHaveChanged)))}),!1),a;function p(){const e=s.get();for(let i=0;i<t.length;i++)t[i].value===e&&(c.selectedIndex=i);c.disabled=s.disabled()}}(n,e.options(),e.reloadRequired(),e,t):(console.error("Enum setting defined without options"),null);default:return console.error("Invalid setting type: "+e.type()),null}}});!function(e){e.NonViewport="UI.ListMode.NonViewport",e.EqualHeightItems="UI.ListMode.EqualHeightItems",e.VariousHeightItems="UI.ListMode.VariousHeightItems"}(Gi||(Gi={}));class Yi{element;topElement;bottomElement;firstIndex;lastIndex;renderedHeight;topHeight;bottomHeight;model;itemToElement;selectedIndexInternal;selectedItemInternal;delegate;mode;fixedHeight;variableOffsets;constructor(e,t,i){this.element=document.createElement("div"),this.element.style.overflowY="auto",this.topElement=this.element.createChild("div"),this.bottomElement=this.element.createChild("div"),this.firstIndex=0,this.lastIndex=0,this.renderedHeight=0,this.topHeight=0,this.bottomHeight=0,this.model=e,this.model.addEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.itemToElement=new Map,this.selectedIndexInternal=-1,this.selectedItemInternal=null,this.element.tabIndex=-1,this.element.addEventListener("click",this.onClick.bind(this),!1),this.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),Z(this.element),this.delegate=t,this.mode=i||Gi.EqualHeightItems,this.fixedHeight=0,this.variableOffsets=new Int32Array(0),this.clearContents(),this.mode!==Gi.NonViewport&&this.element.addEventListener("scroll",(()=>{this.updateViewport(this.element.scrollTop,this.element.offsetHeight)}),!1)}setModel(e){this.itemToElement.clear();const t=this.model.length;this.model.removeEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.model=e,this.model.addEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.invalidateRange(0,t)}replacedItemsInRange(e){const t=e.data,i=t.index,n=i+t.removed.length,s=t.keepSelectedIndex,o=this.selectedItemInternal,r=null!==o&&this.itemToElement.get(o)||null;for(let e=0;e<t.removed.length;e++)this.itemToElement.delete(t.removed[e]);if(this.invalidate(i,n,t.inserted),this.selectedIndexInternal>=n)this.selectedIndexInternal+=t.inserted-(n-i),this.selectedItemInternal=this.model.at(this.selectedIndexInternal);else if(this.selectedIndexInternal>=i){const e=s?i:i+t.inserted;let n=this.findFirstSelectable(e,1,!1);if(-1===n){const e=s?i:i-1;n=this.findFirstSelectable(e,-1,!1)}this.select(n,o,r)}}refreshItem(e){const t=this.model.indexOf(e);-1!==t?this.refreshItemByIndex(t):console.error("Item to refresh is not present")}refreshItemByIndex(e){const t=this.model.at(e);this.itemToElement.delete(t),this.invalidateRange(e,e+1),-1!==this.selectedIndexInternal&&this.select(this.selectedIndexInternal,null,null)}refreshAllItems(){this.itemToElement.clear(),this.invalidateRange(0,this.model.length),-1!==this.selectedIndexInternal&&this.select(this.selectedIndexInternal,null,null)}invalidateRange(e,t){this.invalidate(e,t,t-e)}viewportResized(){if(this.mode===Gi.NonViewport)return;const e=this.element.scrollTop,t=this.element.offsetHeight;this.clearViewport(),this.updateViewport(i.NumberUtilities.clamp(e,0,this.totalHeight()-t),t)}invalidateItemHeight(){this.mode===Gi.EqualHeightItems?(this.fixedHeight=0,this.model.length&&(this.itemToElement.clear(),this.invalidate(0,this.model.length,this.model.length))):console.error("Only supported in equal height items mode")}itemForNode(e){for(;e&&e.parentNodeOrShadowHost()!==this.element;)e=e.parentNodeOrShadowHost();if(!e)return null;const t=e,i=this.model.findIndex((e=>this.itemToElement.get(e)===t));return-1!==i?this.model.at(i):null}scrollItemIntoView(e,t){const i=this.model.indexOf(e);-1!==i?this.scrollIntoView(i,t):console.error("Attempt to scroll onto missing item")}selectedItem(){return this.selectedItemInternal}selectedIndex(){return this.selectedIndexInternal}selectItem(e,t,i){let n=-1;if(null!==e){if(n=this.model.indexOf(e),-1===n)return void console.error("Attempt to select missing item");if(!this.delegate.isItemSelectable(e))return void console.error("Attempt to select non-selectable item")}-1===n||i||this.scrollIntoView(n,t),this.selectedIndexInternal!==n&&this.select(n)}selectPreviousItem(e,t){if(-1===this.selectedIndexInternal&&!e)return!1;let i=-1===this.selectedIndexInternal?this.model.length-1:this.selectedIndexInternal-1;return i=this.findFirstSelectable(i,-1,Boolean(e)),-1!==i&&(this.scrollIntoView(i,t),this.select(i),!0)}selectNextItem(e,t){if(-1===this.selectedIndexInternal&&!e)return!1;let i=-1===this.selectedIndexInternal?0:this.selectedIndexInternal+1;return i=this.findFirstSelectable(i,1,Boolean(e)),-1!==i&&(this.scrollIntoView(i,t),this.select(i),!0)}selectItemPreviousPage(e){if(this.mode===Gi.NonViewport)return!1;let t=-1===this.selectedIndexInternal?this.model.length-1:this.selectedIndexInternal;return t=this.findPageSelectable(t,-1),-1!==t&&(this.scrollIntoView(t,e),this.select(t),!0)}selectItemNextPage(e){if(this.mode===Gi.NonViewport)return!1;let t=-1===this.selectedIndexInternal?0:this.selectedIndexInternal;return t=this.findPageSelectable(t,1),-1!==t&&(this.scrollIntoView(t,e),this.select(t),!0)}scrollIntoView(e,t){if(this.mode===Gi.NonViewport)return void this.elementAtIndex(e).scrollIntoViewIfNeeded(Boolean(t));const n=this.offsetAtIndex(e),s=this.offsetAtIndex(e+1),o=this.element.offsetHeight;if(t){const e=(n+s)/2-o/2;return void this.updateViewport(i.NumberUtilities.clamp(e,0,this.totalHeight()-o),o)}const r=this.element.scrollTop;n<r?this.updateViewport(n,o):s>r+o&&this.updateViewport(s-o,o)}onClick(e){const t=this.itemForNode(e.target);null!==t&&this.delegate.isItemSelectable(t)&&this.selectItem(t)}onKeyDown(e){const t=e;let i=!1;switch(t.key){case"ArrowUp":i=this.selectPreviousItem(!0,!1);break;case"ArrowDown":i=this.selectNextItem(!0,!1);break;case"PageUp":i=this.selectItemPreviousPage(!1);break;case"PageDown":i=this.selectItemNextPage(!1)}i&&t.consume(!0)}totalHeight(){return this.offsetAtIndex(this.model.length)}indexAtOffset(e){if(this.mode===Gi.NonViewport)throw"There should be no offset conversions in non-viewport mode";return!this.model.length||e<0?0:this.mode===Gi.VariousHeightItems?Math.min(this.model.length-1,i.ArrayUtilities.lowerBound(this.variableOffsets,e,i.ArrayUtilities.DEFAULT_COMPARATOR,0,this.model.length)):(this.fixedHeight||this.measureHeight(),Math.min(this.model.length-1,Math.floor(e/this.fixedHeight)))}elementAtIndex(e){const t=this.model.at(e);let i=this.itemToElement.get(t);return i||(i=this.delegate.createElementForItem(t),i.hasAttribute("jslog")||i.setAttribute("jslog",`${o.item().track({click:!0})}`),this.itemToElement.set(t,i),this.updateElementARIA(i,e)),i}refreshARIA(){for(let e=this.firstIndex;e<=this.lastIndex;e++){const t=this.model.at(e),i=this.itemToElement.get(t);i&&this.updateElementARIA(i,e)}}updateElementARIA(e,t){ne(e)||ee(e),we(e,this.model.length),xe(e,t+1)}offsetAtIndex(e){if(this.mode===Gi.NonViewport)throw new Error("There should be no offset conversions in non-viewport mode");return this.model.length?this.mode===Gi.VariousHeightItems?this.variableOffsets[e]:(this.fixedHeight||this.measureHeight(),e*this.fixedHeight):0}measureHeight(){this.fixedHeight=this.delegate.heightForItem(this.model.at(0)),this.fixedHeight||(this.fixedHeight=Qn(this.elementAtIndex(0),this.element).height)}select(e,t,i){void 0===t&&(t=this.selectedItemInternal),void 0===i&&(i=this.itemToElement.get(t)||null),this.selectedIndexInternal=e,this.selectedItemInternal=-1===e?null:this.model.at(e);const n=this.selectedItemInternal,s=-1!==this.selectedIndexInternal?this.elementAtIndex(e):null;this.delegate.selectedItemChanged(t,n,i,s),this.delegate.updateSelectedItemARIA(i,s)||(i&&ue(i,!1),s&&ue(s,!0),ve(this.element,s))}findFirstSelectable(e,t,i){const n=this.model.length;if(!n)return-1;for(let s=0;s<=n;s++){if(e<0||e>=n){if(!i)return-1;e=(e+n)%n}if(this.delegate.isItemSelectable(this.model.at(e)))return e;e+=t}return-1}findPageSelectable(e,t){let i=-1;const n=this.offsetAtIndex(e),s=this.element.offsetHeight-1;for(;e>=0&&e<this.model.length;){if(this.delegate.isItemSelectable(this.model.at(e))){if(Math.abs(this.offsetAtIndex(e)-n)>=s)return e;i=e}e+=t}return i}reallocateVariableOffsets(e,t){if(this.variableOffsets.length<e){const i=new Int32Array(Math.max(e,2*this.variableOffsets.length));i.set(this.variableOffsets.slice(0,t),0),this.variableOffsets=i}else if(this.variableOffsets.length>=2*e){const i=new Int32Array(e);i.set(this.variableOffsets.slice(0,t),0),this.variableOffsets=i}}invalidate(e,t,n){if(this.mode===Gi.NonViewport)return void this.invalidateNonViewportMode(e,t-e,n);if(this.mode===Gi.VariousHeightItems){this.reallocateVariableOffsets(this.model.length+1,e+1);for(let t=e+1;t<=this.model.length;t++)this.variableOffsets[t]=this.variableOffsets[t-1]+this.delegate.heightForItem(this.model.at(t-1))}const s=this.element.offsetHeight,o=this.totalHeight(),r=this.element.scrollTop;if(this.renderedHeight<s||o<s)return this.clearViewport(),void this.updateViewport(i.NumberUtilities.clamp(r,0,o-s),s);const a=o-this.renderedHeight;if(t<=this.firstIndex){const i=this.topHeight+a;this.topElement.style.height=i+"px",this.element.scrollTop=r+a,this.topHeight=i,this.renderedHeight=o;const s=n-(t-e);return this.firstIndex+=s,void(this.lastIndex+=s)}if(e>=this.lastIndex){const e=this.bottomHeight+a;return this.bottomElement.style.height=e+"px",this.bottomHeight=e,void(this.renderedHeight=o)}this.clearViewport(),this.updateViewport(i.NumberUtilities.clamp(r,0,o-s),s),this.refreshARIA()}invalidateNonViewportMode(e,t,i){let n=this.topElement;for(let t=0;t<e;t++)n=n.nextElementSibling;for(;t--;)n.nextElementSibling.remove();for(;i--;)this.element.insertBefore(this.elementAtIndex(e+i),n.nextElementSibling)}clearViewport(){this.mode!==Gi.NonViewport?(this.firstIndex=0,this.lastIndex=0,this.renderedHeight=0,this.topHeight=0,this.bottomHeight=0,this.clearContents()):console.error("There should be no viewport updates in non-viewport mode")}clearContents(){this.topElement.style.height="0",this.bottomElement.style.height="0",this.element.removeChildren(),this.element.appendChild(this.topElement),this.element.appendChild(this.bottomElement)}updateViewport(e,t){if(this.mode===Gi.NonViewport)return void console.error("There should be no viewport updates in non-viewport mode");const i=this.totalHeight();if(!i)return this.firstIndex=0,this.lastIndex=0,this.topHeight=0,this.bottomHeight=0,this.renderedHeight=0,this.topElement.style.height="0",void(this.bottomElement.style.height="0");const n=this.indexAtOffset(e-t),s=this.indexAtOffset(e+2*t)+1;for(;this.firstIndex<Math.min(n,this.lastIndex);)this.elementAtIndex(this.firstIndex).remove(),this.firstIndex++;for(;this.lastIndex>Math.max(s,this.firstIndex);)this.elementAtIndex(this.lastIndex-1).remove(),this.lastIndex--;this.firstIndex=Math.min(this.firstIndex,s),this.lastIndex=Math.max(this.lastIndex,n);for(let e=this.firstIndex-1;e>=n;e--){const t=this.elementAtIndex(e);this.element.insertBefore(t,this.topElement.nextSibling)}for(let e=this.lastIndex;e<s;e++){const t=this.elementAtIndex(e);this.element.insertBefore(t,this.bottomElement)}this.firstIndex=n,this.lastIndex=s,this.topHeight=this.offsetAtIndex(n),this.topElement.style.height=this.topHeight+"px",this.bottomHeight=i-this.offsetAtIndex(s),this.bottomElement.style.height=this.bottomHeight+"px",this.renderedHeight=i,this.element.scrollTop=e}}var Qi=Object.freeze({__proto__:null,get ListMode(){return Gi},ListControl:Yi});class Zi extends e.ObjectWrapper.ObjectWrapper{items;constructor(e){super(),this.items=e||[]}[Symbol.iterator](){return this.items[Symbol.iterator]()}get length(){return this.items.length}at(e){return this.items[e]}every(e){return this.items.every(e)}filter(e){return this.items.filter(e)}find(e){return this.items.find(e)}findIndex(e){return this.items.findIndex(e)}indexOf(e,t){return this.items.indexOf(e,t)}insert(e,t){this.items.splice(e,0,t),this.replaced(e,[],1)}insertWithComparator(e,t){this.insert(i.ArrayUtilities.lowerBound(this.items,e,t),e)}join(e){return this.items.join(e)}remove(e){const t=this.items[e];return this.items.splice(e,1),this.replaced(e,[t],0),t}replace(e,t,i){const n=this.items[e];return this.items[e]=t,this.replaced(e,[n],1,i),n}replaceRange(e,t,i){let n;if(i.length<1e4)n=this.items.splice(e,t-e,...i);else{n=this.items.slice(e,t);const s=this.items.slice(0,e),o=this.items.slice(t);this.items=[...s,...i,...o]}return this.replaced(e,n,i.length),n}replaceAll(e){const t=this.items.slice();return this.items=e,this.replaced(0,t,e.length),t}slice(e,t){return this.items.slice(e,t)}some(e){return this.items.some(e)}replaced(e,t,i,n){this.dispatchEventToListeners("ItemsReplaced",{index:e,removed:t,inserted:i,keepSelectedIndex:n})}}var Ji=Object.freeze({__proto__:null,ListModel:Zi}),en={cssContent:":host{display:flex;flex:auto}.suggest-box{flex:auto;background-color:var(--sys-color-cdt-base-container);pointer-events:auto;margin-left:-3px;box-shadow:var(--drop-shadow);overflow-x:hidden}.suggest-box-content-item{padding:1px 0 1px 1px;margin:0;border:1px solid transparent;white-space:nowrap;display:flex;align-items:center;justify-content:space-between}.suggest-box-content-item.secondary{background-color:var(--sys-color-neutral-container);justify-content:normal}.suggestion-title{overflow:hidden;text-overflow:ellipsis}.suggestion-title span{white-space:pre}.suggestion-subtitle{flex:auto;text-align:right;color:var(--sys-color-token-subtle);margin-right:3px;overflow:hidden;text-overflow:ellipsis}.suggest-box-content-item devtools-icon{color:var(--sys-color-on-surface-subtle);margin-right:1px}.suggest-box-content-item .query{font-weight:bold}.suggest-box-content-item .spacer{display:inline-block;width:20px}.suggest-box-content-item.selected{background-color:var(--sys-color-tonal-container)}.suggest-box-content-item.selected .suggestion-subtitle,\n.suggest-box-content-item.selected > span{color:var(--sys-color-on-tonal-container)}.suggest-box-content-item:hover:not(.selected){background-color:var(--sys-color-state-hover-on-subtle)}@media (forced-colors: active){.suggest-box-content-item.selected{forced-color-adjust:none;background-color:Highlight}.suggest-box-content-item.selected > span{color:HighlightText}}"};const tn={sSuggestionSOfS:"{PH1}, suggestion {PH2} of {PH3}",sSuggestionSSelected:"{PH1}, suggestion selected"},nn=t.i18n.registerUIStrings("ui/legacy/SuggestBox.ts",tn),sn=t.i18n.getLocalizedString.bind(void 0,nn);class on{suggestBoxDelegate;maxItemsHeight;rowHeight;userEnteredText;defaultSelectionIsDimmed;onlyCompletion;items;list;element;glassPane;constructor(e,t){this.suggestBoxDelegate=e,this.maxItemsHeight=t,this.rowHeight=17,this.userEnteredText="",this.defaultSelectionIsDimmed=!1,this.onlyCompletion=null,this.items=new Zi,this.list=new Yi(this.items,this,Gi.EqualHeightItems),this.element=this.list.element,this.element.classList.add("suggest-box"),this.element.addEventListener("mousedown",(e=>e.preventDefault()),!0),this.element.addEventListener("click",this.onClick.bind(this),!1),this.glassPane=new Ts,this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.setOutsideClickCallback(this.hide.bind(this));d.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:en,delegatesFocus:void 0}).appendChild(this.element)}visible(){return this.glassPane.isShowing()}setPosition(e){this.glassPane.setContentAnchorBox(e)}setAnchorBehavior(e){this.glassPane.setAnchorBehavior(e)}updateMaxSize(e){const t=this.maxWidth(e),i=(this.maxItemsHeight?Math.min(this.maxItemsHeight,e.length):e.length)*this.rowHeight;this.glassPane.setMaxContentSize(new _e(t,i))}maxWidth(e){if(!e.length)return 300;let t,i=-1/0;for(let n=0;n<e.length;n++){const s=(e[n].title||e[n].text).length+(e[n].subtitle||"").length;s>i&&(i=s,t=e[n])}const n=Qn(this.createElementForItem(t),this.element).width+d.measuredScrollbarWidth(this.element.ownerDocument);return Math.min(300,n)}show(){if(this.visible())return;this.glassPane.show(document);this.rowHeight=Qn(this.createElementForItem({text:"1",subtitle:"12"}),this.element).height,re(this.suggestBoxDelegate.ariaControlledBy(),this.element),ae(this.suggestBoxDelegate.ariaControlledBy(),!0)}hide(){this.visible()&&(this.glassPane.hide(),re(this.suggestBoxDelegate.ariaControlledBy(),null),ae(this.suggestBoxDelegate.ariaControlledBy(),!1))}applySuggestion(e){if(this.onlyCompletion)return Se(e?sn(tn.sSuggestionSOfS,{PH1:this.onlyCompletion.text,PH2:this.list.selectedIndex()+1,PH3:this.items.length}):sn(tn.sSuggestionSSelected,{PH1:this.onlyCompletion.text})),this.suggestBoxDelegate.applySuggestion(this.onlyCompletion,e),!0;const t=this.list.selectedItem();return t&&t.text&&Se(e?sn(tn.sSuggestionSOfS,{PH1:t.title||t.text,PH2:this.list.selectedIndex()+1,PH3:this.items.length}):sn(tn.sSuggestionSSelected,{PH1:t.title||t.text})),this.suggestBoxDelegate.applySuggestion(t,e),this.visible()&&Boolean(t)}acceptSuggestion(){const e=this.applySuggestion();return this.hide(),!!e&&(this.suggestBoxDelegate.acceptSuggestion(),!0)}createElementForItem(e){const t=this.userEnteredText,n=document.createElement("div");n.classList.add("suggest-box-content-item"),n.classList.add("source-code"),e.isSecondary&&n.classList.add("secondary"),n.tabIndex=-1;const s=50+t.length,o=i.StringUtilities.trimEndWithMaxLength((e.title||e.text).trim(),s).replace(/\n/g,"↵"),r=n.createChild("span","suggestion-title"),a=o.toLowerCase().indexOf(t.toLowerCase());if(a>0&&(r.createChild("span").textContent=o.substring(0,a)),a>-1&&(r.createChild("span","query").textContent=o.substring(a,a+t.length)),r.createChild("span").textContent=o.substring(a>-1?a+t.length:0),r.createChild("span","spacer"),e.subtitleRenderer){const t=e.subtitleRenderer.call(null);t.classList.add("suggestion-subtitle"),n.appendChild(t)}else if(e.subtitle){n.createChild("span","suggestion-subtitle").textContent=i.StringUtilities.trimEndWithMaxLength(e.subtitle,s-o.length)}return e.iconElement&&n.appendChild(e.iconElement),n}heightForItem(e){return this.rowHeight}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,n){i&&i.classList.remove("selected","force-white-icons"),n&&(n.classList.add("selected"),n.classList.add("force-white-icons")),this.applySuggestion(!0)}updateSelectedItemARIA(e,t){return!1}onClick(e){const t=this.list.itemForNode(e.target);t&&(this.list.selectItem(t),this.acceptSuggestion(),e.consume(!0))}canShowBox(e,t,i,n){return!(!e||!e.length)&&(e.length>1||(!(t&&!t.isSecondary&&t.text.startsWith(n))||i&&t.text!==n))}updateSuggestions(e,t,i,n,s){this.onlyCompletion=null;const o=i?t.reduce(((e,t)=>(e.priority||0)>=(t.priority||0)?e:t)):null;this.canShowBox(t,o,n,s)?(this.userEnteredText=s,this.show(),this.updateMaxSize(t),this.glassPane.setContentAnchorBox(e),this.list.invalidateItemHeight(),this.items.replaceAll(t),o&&!o.isSecondary?this.list.selectItem(o,!0):this.list.selectItem(null)):(1===t.length&&(this.onlyCompletion=t[0],this.applySuggestion(!0)),this.hide())}keyPressed(e){switch(e.key){case"Enter":return this.enterKeyPressed();case"ArrowUp":return this.list.selectPreviousItem(!0,!1);case"ArrowDown":return this.list.selectNextItem(!0,!1);case"PageUp":return this.list.selectItemPreviousPage(!1);case"PageDown":return this.list.selectItemNextPage(!1)}return!1}enterKeyPressed(){const e=Boolean(this.list.selectedItem())||Boolean(this.onlyCompletion);return this.acceptSuggestion(),e}}var rn=Object.freeze({__proto__:null,SuggestBox:on}),an={cssContent:'.text-prompt-root{display:flex;align-items:center}.text-prompt-editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;margin:0 -2px -1px;padding:0 2px 1px;opacity:100%!important}.text-prompt{cursor:text;overflow-x:visible}.text-prompt::-webkit-scrollbar{display:none}.text-prompt-editing > .text-prompt{color:var(--sys-color-on-surface)!important;text-decoration:none!important;white-space:pre}.text-prompt > .auto-complete-text{color:var(--sys-color-token-subtle)!important}.text-prompt[data-placeholder]:empty::before{content:attr(data-placeholder);color:var(--sys-color-token-subtle)}.text-prompt:not([data-placeholder]):empty::after{content:"\\00A0";width:0;display:block}.text-prompt.disabled{opacity:50%;cursor:default}.text-prompt-editing br{display:none}.text-prompt-root:not(:focus-within) ::selection{background:transparent}@media (forced-colors: active){.text-prompt[data-placeholder]:empty::before{color:GrayText!important}.text-prompt.disabled{opacity:100%}}'};class ln extends e.ObjectWrapper.ObjectWrapper{proxyElement;proxyElementDisplay;autocompletionTimeout;titleInternal;queryRange;previousText;currentSuggestion;completionRequestId;ghostTextElement;leftParenthesesIndices;loadCompletions;completionStopCharacters;usesSuggestionBuilder;elementInternal;boundOnKeyDown;boundOnInput;boundOnMouseWheel;boundClearAutocomplete;contentElement;suggestBox;isEditing;focusRestorer;blurListener;oldTabIndex;completeTimeout;disableDefaultSuggestionForEmptyInputInternal;jslogContext=void 0;constructor(){super(),this.proxyElementDisplay="inline-block",this.autocompletionTimeout=hn,this.titleInternal="",this.queryRange=null,this.previousText="",this.currentSuggestion=null,this.completionRequestId=0,this.ghostTextElement=document.createElement("span"),this.ghostTextElement.classList.add("auto-complete-text"),this.ghostTextElement.setAttribute("contenteditable","false"),this.leftParenthesesIndices=[],te(this.ghostTextElement)}initialize(e,t,i){this.loadCompletions=e,this.completionStopCharacters=t||" =:[({;,!+-*/&|^<>.",this.usesSuggestionBuilder=i||!1}setAutocompletionTimeout(e){this.autocompletionTimeout=e}renderAsBlock(){this.proxyElementDisplay="block"}attach(e){return this.attachInternal(e)}attachAndStartEditing(e,t){const i=this.attachInternal(e);return this.startEditing(t),i}attachInternal(e){if(this.proxyElement)throw"Cannot attach an attached TextPrompt";this.elementInternal=e,this.boundOnKeyDown=this.onKeyDown.bind(this),this.boundOnInput=this.onInput.bind(this),this.boundOnMouseWheel=this.onMouseWheel.bind(this),this.boundClearAutocomplete=this.clearAutocomplete.bind(this),this.proxyElement=e.ownerDocument.createElement("span"),m.ThemeSupport.instance().appendStyle(this.proxyElement,an),this.contentElement=this.proxyElement.createChild("div","text-prompt-root"),this.proxyElement.style.display=this.proxyElementDisplay,e.parentElement&&e.parentElement.insertBefore(this.proxyElement,e),this.contentElement.appendChild(e);let t=o.textField().track({keydown:!0});return this.jslogContext&&(t=t.context(this.jslogContext)),this.elementInternal.setAttribute("jslog",`${t}`),this.elementInternal.classList.add("text-prompt"),K(this.elementInternal),he(this.elementInternal,"both"),ce(this.elementInternal,"listbox"),this.elementInternal.setAttribute("contenteditable","plaintext-only"),this.element().addEventListener("keydown",this.boundOnKeyDown,!1),this.elementInternal.addEventListener("input",this.boundOnInput,!1),this.elementInternal.addEventListener("wheel",this.boundOnMouseWheel,!1),this.elementInternal.addEventListener("selectstart",this.boundClearAutocomplete,!1),this.elementInternal.addEventListener("blur",this.boundClearAutocomplete,!1),this.suggestBox=new on(this,20),this.titleInternal&&Jt.install(this.proxyElement,this.titleInternal),this.proxyElement}element(){if(!this.elementInternal)throw new Error("Expected an already attached element!");return this.elementInternal}detach(){this.removeFromElement(),this.focusRestorer&&this.focusRestorer.restore(),this.proxyElement&&this.proxyElement.parentElement&&(this.proxyElement.parentElement.insertBefore(this.element(),this.proxyElement),this.proxyElement.remove()),delete this.proxyElement,this.element().classList.remove("text-prompt"),this.element().removeAttribute("contenteditable"),this.element().removeAttribute("role"),de(this.element()),ce(this.element(),"false")}textWithCurrentSuggestion(){const e=this.text();if(!this.queryRange||!this.currentSuggestion)return e;const t=this.currentSuggestion.text;return e.substring(0,this.queryRange.startColumn)+t+e.substring(this.queryRange.endColumn)}text(){let e=this.element().textContent||"";if(this.ghostTextElement.parentNode){const t=this.ghostTextElement.textContent||"";e=e.substring(0,e.length-t.length)}return e}setText(e){this.clearAutocomplete(),this.element().textContent=e,this.previousText=this.text(),this.element().hasFocus()&&(this.moveCaretToEndOfPrompt(),this.element().scrollIntoView())}setSelectedRange(e,t){if(e<0)throw new RangeError("Selected range start must be a nonnegative integer");const i=this.element().textContent,n=i?i.length:0;t>n&&(t=n),t<e&&(t=e);const s=this.element().childNodes[0],o=new Range;o.setStart(s,e),o.setEnd(s,t);const r=window.getSelection();r&&(r.removeAllRanges(),r.addRange(o))}focus(){this.element().focus()}title(){return this.titleInternal}setTitle(e){this.titleInternal=e,this.proxyElement&&Jt.install(this.proxyElement,e)}setPlaceholder(e,t){e?(this.element().setAttribute("data-placeholder",e),se(this.element(),t||e)):(this.element().removeAttribute("data-placeholder"),se(this.element(),null))}setEnabled(e){e?this.element().setAttribute("contenteditable","plaintext-only"):this.element().removeAttribute("contenteditable"),this.element().classList.toggle("disabled",!e)}removeFromElement(){this.clearAutocomplete(),this.element().removeEventListener("keydown",this.boundOnKeyDown,!1),this.element().removeEventListener("input",this.boundOnInput,!1),this.element().removeEventListener("selectstart",this.boundClearAutocomplete,!1),this.element().removeEventListener("blur",this.boundClearAutocomplete,!1),this.isEditing&&this.stopEditing(),this.suggestBox&&this.suggestBox.hide()}startEditing(e){this.isEditing=!0,this.contentElement&&this.contentElement.classList.add("text-prompt-editing"),this.focusRestorer=new Gn(this.element()),e&&(this.blurListener=e,this.element().addEventListener("blur",this.blurListener,!1)),this.oldTabIndex=this.element().tabIndex,this.element().tabIndex<0&&(this.element().tabIndex=0),this.text()||this.autoCompleteSoon()}stopEditing(){this.element().tabIndex=this.oldTabIndex,this.blurListener&&this.element().removeEventListener("blur",this.blurListener,!1),this.contentElement&&this.contentElement.classList.remove("text-prompt-editing"),delete this.isEditing}onMouseWheel(e){}onKeyDown(e){let t=!1;const i=e;if(this.isSuggestBoxVisible()&&this.suggestBox&&this.suggestBox.keyPressed(i))i.consume(!0);else{switch(i.key){case"Tab":t=this.tabKeyPressed(i);break;case"ArrowLeft":case"ArrowUp":case"PageUp":case"Home":this.clearAutocomplete();break;case"PageDown":case"ArrowRight":case"ArrowDown":case"End":this.isCaretAtEndOfPrompt()?t=this.acceptAutoComplete():this.clearAutocomplete();break;case"Escape":this.isSuggestBoxVisible()&&(this.clearAutocomplete(),t=!0);break;case" ":!i.ctrlKey||i.metaKey||i.altKey||i.shiftKey||(this.autoCompleteSoon(!0),t=!0)}"Enter"===i.key&&i.preventDefault(),t&&i.consume(!0)}}acceptSuggestionOnStopCharacters(e){if(!(this.currentSuggestion&&this.queryRange&&1===e.length&&this.completionStopCharacters&&this.completionStopCharacters.includes(e)))return!1;const t=this.text().substring(this.queryRange.startColumn,this.queryRange.endColumn);return!(!t||!this.currentSuggestion.text.startsWith(t+e))&&(this.queryRange.endColumn+=1,this.acceptAutoComplete())}onInput(e){const t=e;let n=this.text();const s=t.data;"insertFromPaste"===t.inputType&&n.includes("\n")&&(n=i.StringUtilities.stripLineBreaks(n),this.setText(n));const o=this.getCaretPosition();if(")"===s&&o>=0&&this.leftParenthesesIndices.length>0){if(")"===n[o]&&this.tryMatchingLeftParenthesis(o))return n=n.substring(0,o)+n.substring(o+1),void this.setText(n)}if(s&&!this.acceptSuggestionOnStopCharacters(s)){const e=n.startsWith(this.previousText)||this.previousText.startsWith(n);this.queryRange&&e&&(this.queryRange.endColumn+=n.length-this.previousText.length)}this.refreshGhostText(),this.previousText=n,this.dispatchEventToListeners("TextChanged"),this.autoCompleteSoon()}acceptAutoComplete(){let e=!1;return this.isSuggestBoxVisible()&&this.suggestBox&&(e=this.suggestBox.acceptSuggestion()),e||(e=this.acceptSuggestionInternal()),this.usesSuggestionBuilder&&e&&this.autoCompleteSoon(),e}clearAutocomplete(){const e=this.textWithCurrentSuggestion();this.isSuggestBoxVisible()&&this.suggestBox&&this.suggestBox.hide(),this.clearAutocompleteTimeout(),this.queryRange=null,this.refreshGhostText(),e!==this.textWithCurrentSuggestion()&&this.dispatchEventToListeners("TextChanged")}refreshGhostText(){this.currentSuggestion&&this.currentSuggestion.hideGhostText?this.ghostTextElement.remove():this.queryRange&&this.currentSuggestion&&this.isCaretAtEndOfPrompt()&&this.currentSuggestion.text.startsWith(this.text().substring(this.queryRange.startColumn))?(this.ghostTextElement.textContent=this.currentSuggestion.text.substring(this.queryRange.endColumn-this.queryRange.startColumn),this.element().appendChild(this.ghostTextElement)):this.ghostTextElement.remove()}clearAutocompleteTimeout(){this.completeTimeout&&(clearTimeout(this.completeTimeout),delete this.completeTimeout),this.completionRequestId++}autoCompleteSoon(e){const t=this.isSuggestBoxVisible()||e;this.completeTimeout||(this.completeTimeout=window.setTimeout(this.complete.bind(this,e),t?0:this.autocompletionTimeout))}async complete(e){this.clearAutocompleteTimeout();const t=this.element().getComponentSelection();if(!t||0===t.rangeCount)return;const n=t.getRangeAt(0);let s;if((e||this.isCaretAtEndOfPrompt()||this.isSuggestBoxVisible())&&t.isCollapsed||(s=!0),s)return void this.clearAutocomplete();const o=i.DOMUtilities.rangeOfWord(n.startContainer,n.startOffset,this.completionStopCharacters,this.element(),"backward"),r=o.cloneRange();r.collapse(!0),r.setStartBefore(this.element());const a=++this.completionRequestId,l=await this.loadCompletions.call(null,r.toString(),o.toString(),Boolean(e));this.completionsReady(a,t,o,Boolean(e),l)}disableDefaultSuggestionForEmptyInput(){this.disableDefaultSuggestionForEmptyInputInternal=!0}boxForAnchorAtStart(e,t){const i=e.getRangeAt(0).cloneRange(),n=document.createElement("span");n.textContent="​",t.insertNode(n);const s=n.boxInWindow(window);return n.remove(),e.removeAllRanges(),e.addRange(i),s}additionalCompletions(e){return[]}completionsReady(e,t,i,n,s){if(this.completionRequestId!==e)return;const o=i.toString(),a=new Set;if(s=s.filter((e=>!a.has(e.text)&&Boolean(a.add(e.text)))),(o||n)&&(s=o?s.concat(this.additionalCompletions(o)):this.additionalCompletions(o).concat(s)),!s.length)return void this.clearAutocomplete();const l=t.getRangeAt(0),h=document.createRange();if(h.setStart(i.startContainer,i.startOffset),h.setEnd(l.endContainer,l.endOffset),o+l.toString()!==h.toString())return;const d=document.createRange();d.setStart(this.element(),0),d.setEnd(h.startContainer,h.startOffset),this.queryRange=new r.TextRange.TextRange(0,d.toString().length,0,d.toString().length+h.toString().length);const c=!this.disableDefaultSuggestionForEmptyInputInternal||Boolean(this.text());this.suggestBox&&this.suggestBox.updateSuggestions(this.boxForAnchorAtStart(t,h),s,c,!this.isCaretAtEndOfPrompt(),this.text())}applySuggestion(e,t){this.currentSuggestion=e,this.refreshGhostText(),t&&this.dispatchEventToListeners("TextChanged")}acceptSuggestion(){this.acceptSuggestionInternal()}acceptSuggestionInternal(){if(!this.queryRange)return!1;const e=this.currentSuggestion?this.currentSuggestion.text.length:0,t=this.currentSuggestion?this.currentSuggestion.selectionRange:null,i=t?t.endColumn:e,n=t?t.startColumn:e;return this.element().textContent=this.textWithCurrentSuggestion(),this.setDOMSelection(this.queryRange.startColumn+n,this.queryRange.startColumn+i),this.updateLeftParenthesesIndices(),this.clearAutocomplete(),this.dispatchEventToListeners("TextChanged"),!0}ariaControlledBy(){return this.element()}setDOMSelection(e,t){this.element().normalize();const i=this.element().childNodes[0];if(!i||i===this.ghostTextElement)return;const n=document.createRange();n.setStart(i,e),n.setEnd(i,t);const s=this.element().getComponentSelection();s&&(s.removeAllRanges(),s.addRange(n))}isSuggestBoxVisible(){return void 0!==this.suggestBox&&this.suggestBox.visible()}isCaretInsidePrompt(){const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return!1;return e.getRangeAt(0).startContainer.isSelfOrDescendant(this.element())}isCaretAtEndOfPrompt(){const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return!1;const t=e.getRangeAt(0);let i=t.startContainer;if(!i.isSelfOrDescendant(this.element()))return!1;if(this.ghostTextElement.isAncestor(i))return!0;if(i.nodeType===Node.TEXT_NODE&&t.startOffset<(i.nodeValue||"").length)return!1;let n=!1;for(;i;){if(i.nodeType===Node.TEXT_NODE&&i.nodeValue&&i.nodeValue.length){if(n&&!this.ghostTextElement.isAncestor(i))return!1;n=!0}i=i.traverseNextNode(this.elementInternal)}return!0}moveCaretToEndOfPrompt(){const e=this.element().getComponentSelection(),t=document.createRange();let i=this.element();for(;i.lastChild;)i=i.lastChild;let n=0;if(i.nodeType===Node.TEXT_NODE){n=(i.textContent||"").length}t.setStart(i,n),t.setEnd(i,n),e&&(e.removeAllRanges(),e.addRange(t))}getCaretPosition(){if(!this.element().hasFocus())return-1;const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return-1;const t=e.getRangeAt(0);return t.startOffset!==t.endOffset?-1:t.startOffset}tabKeyPressed(e){return this.acceptAutoComplete()}proxyElementForTests(){return this.proxyElement||null}tryMatchingLeftParenthesis(e){const t=this.leftParenthesesIndices;if(0===t.length||e<0)return!1;for(let i=t.length-1;i>=0;--i)if(t[i]<e)return t.splice(i,1),!0;return!1}updateLeftParenthesesIndices(){const e=this.text(),t=this.leftParenthesesIndices=[];for(let i=0;i<e.length;++i)"("===e[i]&&t.push(i)}suggestBoxForTest(){return this.suggestBox}}const hn=250;var dn=Object.freeze({__proto__:null,TextPrompt:ln}),cn={cssContent:':host{flex:none;padding:0 2px;--toolbar-height:26px}.toolbar-shadow{position:relative;white-space:nowrap;height:var(--toolbar-height);overflow:hidden;display:flex;flex:none;align-items:center;z-index:0}.toolbar-shadow.wrappable{flex-wrap:wrap;overflow:visible}.toolbar-shadow.toolbar-grow-vertical{height:initial}.toolbar-shadow.vertical{flex-direction:column;height:auto;align-items:flex-start}.toolbar-item{position:relative;display:flex;background-color:transparent;flex:none;align-items:center;justify-content:center;padding:0;height:var(--toolbar-height);border:none;white-space:pre;overflow:hidden;max-width:100%}devtools-adorner.fix-perf-icon{--override-adorner-text-color:transparent;--override-adorner-border-color:transparent;--override-adorner-background-color:transparent}.toolbar-item,\n.toolbar-item .devtools-link{color:var(--icon-default)}.toolbar-shadow.vertical .toolbar-item{height:auto;min-height:var(--toolbar-height);white-space:normal}devtools-issue-counter.toolbar-item{margin-top:-4px;padding-left:1px}devtools-issue-counter.main-toolbar{margin-top:1px;margin-left:1px;margin-right:1px}.toolbar-dropdown-arrow{pointer-events:none;flex:none;margin-left:-4px}.toolbar-button.dark-text .toolbar-dropdown-arrow{color:var(--sys-color-on-surface)}select.toolbar-item:disabled + .toolbar-dropdown-arrow{opacity:50%}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0}.toolbar-item-search{min-width:5.2em;max-width:300px;flex:1 1 auto;justify-content:start;overflow:revert}.toolbar-text{margin:0 5px;flex:none;color:var(--ui-text)}.toolbar-text:empty{margin:0}.toolbar-has-dropdown{justify-content:space-between;padding:0 3px 0 5px;border:1px solid transparent}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.toolbar-has-dropdown .toolbar-text{margin:0 4px 0 0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.toolbar-render-as-links *{font-weight:initial;color:var(--sys-color-primary);text-decoration:underline;cursor:pointer}.toolbar-render-as-links button{height:15px;margin:2px}.toolbar-render-as-links button:focus-visible{outline:auto 5px -webkit-focus-ring-color}:not(.toolbar-render-as-links) .toolbar-button:focus-visible::before{position:absolute;top:2px;bottom:2px;left:2px;right:2px;background-color:var(--sys-color-state-focus-highlight);border-radius:2px;content:"";z-index:-1}.toolbar-glyph{flex:none}.toolbar-button:disabled{opacity:50%}.toolbar-button.close-devtools{position:absolute;right:0}:host-context(.right) .toolbar-button.main-menu,\n:host-context(.left) .toolbar-button.main-menu,\n:host-context(.bottom) .toolbar-button.main-menu{margin-inline-end:28px}.toolbar-button.copied-to-clipboard::after{content:attr(data-content);position:fixed;margin-top:calc(2 * var(--toolbar-height));padding:3px 5px;color:var(--sys-color-token-subtle);background:var(--sys-color-cdt-base-container);animation:2s fade-out;font-weight:normal;border:1px solid var(--sys-color-divider);border-radius:3px}@keyframes fade-out{from{opacity:100%}to{opacity:0%}}.toolbar-button.toolbar-state-on .toolbar-glyph{color:var(--icon-toggled)}.toolbar-state-on.toolbar-toggle-with-dot .toolbar-text::after{content:"";position:absolute;bottom:2px;background-color:var(--sys-color-primary-bright);width:4.5px;height:4.5px;border:2px solid var(--override-toolbar-background-color,--sys-color-cdt-base-container);border-radius:50%;right:0}.toolbar-button.toolbar-state-on.toolbar-toggle-with-red-color .toolbar-glyph,\n.toolbar-button.toolbar-state-off.toolbar-default-with-red-color .toolbar-glyph{color:var(--icon-error)!important}.toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):not(.toolbar-button-secondary){font-weight:bold}.toolbar-button.dark-text .toolbar-text{color:var(--sys-color-on-surface)!important}.toolbar-button.toolbar-state-on .toolbar-text{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:active .toolbar-text{color:var(--sys-color-primary-bright)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:hover:not(:active){background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:active:hover{background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-toggled-gray:not(.toolbar-render-as-links) .toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):hover{background-color:var(--sys-color-state-hover-on-subtle)}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-on-surface)}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:hover .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-primary)}.toolbar-toggled-gray .toolbar-button.toolbar-state-on{background-color:var(--sys-color-neutral-container)!important}.toolbar-item.checkbox{padding:0 5px 0 2px}.toolbar-select-container{display:inline-flex;flex-shrink:0;margin-right:6px}select.toolbar-item{min-width:38px;appearance:none;border:1px solid transparent;padding:0 13px 0 5px;margin-right:-17px;position:relative;height:22px;margin-top:2px;margin-bottom:2px}select.toolbar-item:disabled{opacity:50%}button.toolbar-item:focus-visible,\nselect.toolbar-item:focus-visible{background:var(--sys-color-state-hover-on-subtle);border-radius:2px}select.toolbar-item:focus-visible > *{background:var(--sys-color-cdt-base-container);border-radius:7px}.toolbar-input{width:120px;height:19px;padding:4px 3px 3px;margin:1px 3px;background:var(--sys-color-cdt-base-container);border:1px solid var(--sys-color-neutral-outline);border-radius:4px;min-width:35px;&.focused,\n  &:not(.toolbar-input-empty){border-color:var(--sys-color-state-focus-ring)}&:hover:not(.focused){background:var(--sys-color-state-hover-on-subtle)}& > input{border:none;flex-grow:1}}.toolbar-input-clear-button{opacity:70%;flex-basis:13px;flex-shrink:0;height:16px}.toolbar-input-clear-button > .search-cancel-button{display:block}.toolbar-input-clear-button:hover{opacity:99%}.toolbar-input-empty .toolbar-input-clear-button{display:none}.toolbar-prompt-proxy{flex:1}.toolbar-input-prompt{flex:1;overflow:hidden;white-space:nowrap;cursor:auto;color:var(--sys-color-on-surface)}.toolbar-divider{background-color:var(--sys-color-on-base-divider);width:1px;margin:5px 4px;height:16px}.toolbar-spacer{flex:auto}.long-click-glyph{position:absolute;top:2px;left:3px}.toolbar-button.emulate-active{background-color:var(--sys-color-surface-variant)}.toolbar-shadow.floating{flex-direction:column;height:auto;background-color:var(--sys-color-cdt-base-container);border:1px solid var(--sys-color-divider);margin-top:-1px;width:28px;left:-2px}.toolbar-shadow:not(.floating) .toolbar-item:last-child:not(:first-child, .toolbar-select-container){flex-shrink:1;justify-content:left}.toolbar-shadow:not(.floating) .toolbar-button.toolbar-item:last-child:not(:first-child, .toolbar-select-container){justify-content:center;margin-right:2px}input[is="history-input"]{margin:0 1px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;line-height:16px;padding:1px 1px 1px 3px;color:var(--sys-color-on-surface)}input[is="history-input"]:focus,\ninput[is="history-input"]:not(:placeholder-shown){border-color:var(--sys-color-state-focus-ring)}input[is="history-input"]:hover:not(:focus){background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-item.highlight::before{content:"";position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:2px;background:var(--sys-color-neutral-container);z-index:-1}.toolbar-item.highlight:focus-visible{background:var(--sys-color-tonal-container);& > .title{color:var(--sys-color-on-tonal-container)}}devtools-icon.leading-issue-icon{margin:0 7px}@media (forced-colors: active){.toolbar-button:disabled{opacity:100%;color:Graytext}.toolbar-item,\n  .toolbar-text{color:ButtonText}.toolbar-button:disabled .toolbar-text{color:Graytext}select.toolbar-item:disabled,\n  select.toolbar-item:disabled + .toolbar-dropdown-arrow{opacity:100%;color:Graytext}.toolbar-button.toolbar-state-on .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button.toolbar-state-on .toolbar-text{forced-color-adjust:none;color:Highlight}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-text,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:focus:not(:active) .toolbar-text{color:HighlightText}.toolbar-button:disabled devtools-icon{color:GrayText}:not(.toolbar-render-as-links) .toolbar-button:disabled .toolbar-glyph{color:GrayText}.toolbar-button:enabled.hover:not(:active) .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button:focus,\n  .toolbar-button:hover:enabled,\n  .toolbar-toggled-gray:not(.toolbar-render-as-links) .toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):hover{forced-color-adjust:none;background-color:Highlight}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover .toolbar-glyph,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:focus .toolbar-glyph,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-glyph,\n  .toolbar-button:enabled:hover devtools-icon,\n  .toolbar-button:enabled:focus devtools-icon{color:HighlightText}.toolbar-input{forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.toolbar-input.focused,\n  .toolbar-input:not(.toolbar-input-empty){forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-input:hover{box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-item .devtools-link{color:linktext}.toolbar-has-dropdown{forced-color-adjust:none;background:ButtonFace;color:ButtonText}}[aria-label="[FB-only] Send feedback"]{height:20px;padding:0 4px;border-radius:4px;background:color-mix(in srgb,var(--color-green) 80%,transparent)}[aria-label="[FB-only] Send feedback"]:hover{background:color-mix(in srgb,var(--color-green) 90%,transparent)}[aria-label="[FB-only] Send feedback"] .toolbar-text,\n[aria-label="[FB-only] Send feedback"] .toolbar-glyph{color:white!important}.fusebox-connection-status{margin:4px;height:20px;padding:0 4px;border-radius:4px;background:color-mix(in srgb,var(--color-red) 80%,transparent)}.fusebox-connection-status:hover{background:color-mix(in srgb,var(--color-red) 90%,transparent)}.fusebox-connection-status .toolbar-text,\n.fusebox-connection-status .toolbar-glyph{color:white!important}'};const un={pressed:"pressed",notPressed:"not pressed",clearInput:"Clear input"},mn=t.i18n.registerUIStrings("ui/legacy/Toolbar.ts",un),pn=t.i18n.getLocalizedString.bind(void 0,mn);class gn{items;element;enabled;shadowRoot;contentElement;compactLayout=!1;constructor(e,t){this.items=[],this.element=t?t.createChild("div"):document.createElement("div"),this.element.className=e,this.element.classList.add("toolbar"),this.enabled=!0,this.shadowRoot=d.createShadowRootWithCoreStyles(this.element,{cssFile:cn,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","toolbar-shadow")}hasCompactLayout(){return this.compactLayout}registerCSSFiles(e){this.shadowRoot.adoptedStyleSheets=this.shadowRoot.adoptedStyleSheets.concat(e)}setCompactLayout(e){if(this.compactLayout!==e){this.compactLayout=e;for(const t of this.items)t.setCompactLayout(e)}}static createLongPressActionButton(e,t,i){const n=gn.createActionButton(e),s=gn.createActionButton(e);let o=null,r=null,l=null;return e.addEventListener("Toggled",h),h(),n;function h(){const s=e.toggled()?t||null:i||null;s&&s.length?o||(o=new is(n.element,d),l=a.Icon.create("triangle-bottom-right","long-click-glyph"),n.element.appendChild(l),r=s):o&&(o.dispose(),o=null,l&&l.remove(),l=null,r=null)}function d(){let e=r?r.slice():[];e.push(s);const t=n.element.ownerDocument;t.documentElement.addEventListener("mouseup",(function n(s){if(1!==s.which)return;i.hide(),t.documentElement.removeEventListener("mouseup",n,!1);for(let t=0;t<e.length;++t)if(e[t].element.classList.contains("emulate-active")){e[t].element.classList.remove("emulate-active"),e[t].clicked(s);break}}),!1);const i=new Ts;i.setPointerEventsBehavior("BlockedByGlassPane"),i.show(t);const o=new gn("fill",i.contentElement);o.contentElement.classList.add("floating");const a=n.element.boxInWindow().relativeToElement(Ts.container(t)),l=a.y+26*e.length<t.documentElement.offsetHeight;l&&(e=e.reverse()),o.element.style.height=26*e.length+"px",o.element.style.top=l?a.y-5+"px":a.y-26*(e.length-1)-6+"px",o.element.style.left=a.x-5+"px";for(let t=0;t<e.length;++t)e[t].element.addEventListener("mousemove",d,!1),e[t].element.addEventListener("mouseout",c,!1),o.appendToolbarItem(e[t]);const h=l?0:e.length-1;function d(e){if(1===e.which&&e.target instanceof HTMLElement){e.target.enclosingNodeOrSelfWithClass("toolbar-item").classList.add("emulate-active")}}function c(e){if(1===e.which&&e.target instanceof HTMLElement){e.target.enclosingNodeOrSelfWithClass("toolbar-item").classList.remove("emulate-active")}}e[h].element.classList.add("emulate-active")}}static createActionButton(e,t=bn){const i=e.toggleable()?function(){const t=new xn(e.title(),e.icon(),e.toggledIcon(),e.id());return t.setToggleWithRedColor(e.toggleWithRedColor()),e.addEventListener("Toggled",i),i(),t;function i(){t.setToggled(e.toggled()),e.title()&&(t.setTitle(e.title()),Jt.installWithActionBinding(t.element,e.title(),e.id()))}}():function(){const t=new wn(e.title(),e.icon(),void 0,e.id());e.title()&&Jt.installWithActionBinding(t.element,e.title(),e.id());return t}();t.showLabel&&i.setText(t.label?.()||e.title());let n=t=>{e.execute()};if(t.userActionCode){const i=t.userActionCode;n=()=>{s.userMetrics.actionTaken(i),e.execute()}}return i.addEventListener("Click",n,e),e.addEventListener("Enabled",(function(e){i.setEnabled(e.data)})),i.setEnabled(e.enabled()),i}static createActionButtonForId(e,t){const i=M.instance().getAction(e);return gn.createActionButton(i,t)}gripElementForResize(){return this.contentElement}makeWrappable(e){this.contentElement.classList.add("wrappable"),e&&this.contentElement.classList.add("toolbar-grow-vertical")}makeVertical(){this.contentElement.classList.add("vertical")}makeBlueOnHover(){this.contentElement.classList.add("toolbar-blue-on-hover")}makeToggledGray(){this.contentElement.classList.add("toolbar-toggled-gray")}renderAsLinks(){this.contentElement.classList.add("toolbar-render-as-links")}empty(){return!this.items.length}setEnabled(e){this.enabled=e;for(const e of this.items)e.applyEnabledState(this.enabled&&e.enabled)}appendToolbarItem(e){this.items.push(e),e.toolbar=this,e.setCompactLayout(this.hasCompactLayout()),this.enabled||e.applyEnabledState(!1),this.contentElement.appendChild(e.element),this.hideSeparatorDupes()}appendSeparator(){this.appendToolbarItem(new yn)}appendSpacer(){this.appendToolbarItem(new yn(!0))}appendText(e){this.appendToolbarItem(new vn(e))}removeToolbarItem(e){const t=[];for(const i of this.items)i===e?i.element.remove():t.push(i);this.items=t}removeToolbarItems(){for(const e of this.items)e.toolbar=null;this.items=[],this.contentElement.removeChildren()}setColor(e){const t=document.createElement("style");t.textContent=".toolbar-glyph { background-color: "+e+" !important }",this.shadowRoot.appendChild(t)}setToggledColor(e){const t=document.createElement("style");t.textContent=".toolbar-button.toolbar-state-on .toolbar-glyph { background-color: "+e+" !important }",this.shadowRoot.appendChild(t)}hideSeparatorDupes(){if(!this.items.length)return;let e,t=!1,i=!1;for(let n=0;n<this.items.length;++n)this.items[n]instanceof yn?(this.items[n].setVisible(!t),t=!0,e=this.items[n]):this.items[n].visible()&&(t=!1,e=null,i=!0);e&&e!==this.items[this.items.length-1]&&e.setVisible(!1),this.element.classList.toggle("hidden",null!=e&&e.visible()&&!i)}async appendItemsAtLocation(e){const t=Tn.filter((e=>n.Runtime.Runtime.isDescriptorEnabled({experiment:e.experiment,condition:e.condition})));t.sort(((e,t)=>(e.order||0)-(t.order||0)));const i=t.filter((t=>t.location===e)),s=await Promise.all(i.map((e=>{const{separator:t,actionId:i,showLabel:n,label:s,loadItem:o}=e;if(t)return new yn;if(i)return gn.createActionButtonForId(i,{label:s,showLabel:Boolean(n),userActionCode:void 0});if(!o)throw new Error("Could not load a toolbar item registration with no loadItem function");return o().then((e=>e.item()))})));for(const e of s)e&&this.appendToolbarItem(e)}}const bn={showLabel:!1,userActionCode:void 0};class fn extends e.ObjectWrapper.ObjectWrapper{element;visibleInternal;enabled;toolbar;title;constructor(e){super(),this.element=e,this.element.classList.add("toolbar-item"),this.visibleInternal=!0,this.enabled=!0,this.toolbar=null}setTitle(e,t=void 0){this.title!==e&&(this.title=e,be(this.element,e),void 0===t?Jt.install(this.element,e):Jt.installWithActionBinding(this.element,e,t))}setEnabled(e){this.enabled!==e&&(this.enabled=e,this.applyEnabledState(this.enabled&&(!this.toolbar||this.toolbar.enabled)))}applyEnabledState(e){this.element.disabled=!e}visible(){return this.visibleInternal}setVisible(e){this.visibleInternal!==e&&(this.element.classList.toggle("hidden",!e),this.visibleInternal=e,!this.toolbar||this instanceof yn||this.toolbar.hideSeparatorDupes())}setRightAligned(e){this.element.classList.toggle("toolbar-item-right-aligned",e)}setCompactLayout(e){}}class vn extends fn{constructor(e){const t=document.createElement("div");t.classList.add("toolbar-text"),super(t),this.element.classList.add("toolbar-text"),this.setText(e||"")}text(){return this.element.textContent||""}setText(e){this.element.textContent=e}}class wn extends fn{glyphElement;textElement;text;glyph;adorner;constructor(e,t,i,n){const s=document.createElement("button");s.classList.add("toolbar-button"),super(s),this.element.addEventListener("click",this.clicked.bind(this),!1),this.element.addEventListener("mousedown",this.mouseDown.bind(this),!1),this.glyphElement=new a.Icon.Icon,this.glyphElement.className="toolbar-glyph hidden",this.element.appendChild(this.glyphElement),this.textElement=this.element.createChild("div","toolbar-text hidden"),this.setTitle(e),t&&this.setGlyphOrAdorner(t),this.setText(i||""),n&&this.element.setAttribute("jslog",`${o.action().track({click:!0}).context(n)}`),this.title=""}focus(){this.element.focus()}setText(e){this.text!==e&&(this.textElement.textContent=e,this.textElement.classList.toggle("hidden",!e),this.text=e)}setGlyphOrAdorner(e){e instanceof l.Adorner.Adorner?(this.adorner?this.adorner.replaceWith(e):this.element.prepend(e),this.adorner=e):this.setGlyph(e)}setGlyph(e){this.glyph!==e&&(this.glyphElement.name=e||null,this.glyphElement.classList.toggle("hidden",!e),this.element.classList.toggle("toolbar-has-glyph",Boolean(e)),this.glyph=e)}setBackgroundImage(e){this.element.style.backgroundImage="url("+e+")"}setSecondary(){this.element.classList.add("toolbar-button-secondary")}setDarkText(){this.element.classList.add("dark-text")}turnIntoSelect(e=!1){this.element.classList.add("toolbar-has-dropdown"),e&&this.element.classList.add("toolbar-has-dropdown-shrinkable");const t=a.Icon.create("triangle-down","toolbar-dropdown-arrow");this.element.appendChild(t)}clicked(e){this.enabled&&(this.dispatchEventToListeners("Click",e),e.consume())}mouseDown(e){this.enabled&&this.dispatchEventToListeners("MouseDown",e)}}class xn extends wn{toggledInternal;untoggledGlyph;toggledGlyph;constructor(e,t,i,n){super(e,t,""),this.toggledInternal=!1,this.untoggledGlyph=t,this.toggledGlyph=i,this.element.classList.add("toolbar-state-off"),ge(this.element,!1),n&&this.element.setAttribute("jslog",`${o.toggle().track({click:!0}).context(n)}`)}toggled(){return this.toggledInternal}setToggled(e){this.toggledInternal!==e&&(this.toggledInternal=e,this.element.classList.toggle("toolbar-state-on",e),this.element.classList.toggle("toolbar-state-off",!e),ge(this.element,e),this.toggledGlyph&&this.untoggledGlyph&&this.setGlyph(e?this.toggledGlyph:this.untoggledGlyph))}setDefaultWithRedColor(e){this.element.classList.toggle("toolbar-default-with-red-color",e)}setToggleWithRedColor(e){this.element.classList.toggle("toolbar-toggle-with-red-color",e)}setToggleWithDot(e){this.element.classList.toggle("toolbar-toggle-with-dot",e)}}class En extends wn{contextMenuHandler;useSoftMenu;triggerTimeout;constructor(e,t,i){super("","dots-vertical",void 0,i),i&&this.element.setAttribute("jslog",`${o.dropDown().track({click:!0}).context(i)}`),this.contextMenuHandler=e,this.useSoftMenu=Boolean(t),N(this.element)}mouseDown(e){1===e.buttons?this.triggerTimeout||(this.triggerTimeout=window.setTimeout(this.trigger.bind(this,e),200)):super.mouseDown(e)}trigger(e){delete this.triggerTimeout;const t=new Ys(e,{useSoftMenu:this.useSoftMenu,x:this.element.getBoundingClientRect().left,y:this.element.getBoundingClientRect().top+this.element.offsetHeight});this.contextMenuHandler(t),t.show()}clicked(e){this.triggerTimeout&&clearTimeout(this.triggerTimeout),this.trigger(e)}}class In extends xn{defaultTitle;setting;willAnnounceState;constructor(e,t,i,n,s){super(i,t,n,s),this.defaultTitle=i,this.setting=e,this.settingChanged(),this.setting.addChangeListener(this.settingChanged,this),this.willAnnounceState=!1}settingChanged(){const e=this.setting.get();this.setToggled(e);const t=pn(e?un.pressed:un.notPressed);this.willAnnounceState&&Se(t),this.willAnnounceState=!1,this.setTitle(this.defaultTitle)}clicked(e){this.willAnnounceState=!0,this.setting.set(!this.toggled()),super.clicked(e)}}class yn extends fn{constructor(e){const t=document.createElement("div");t.classList.add(e?"toolbar-spacer":"toolbar-divider"),super(t)}}class Sn extends fn{selectElementInternal;constructor(e,t,i,n){const s=document.createElement("span");s.classList.add("toolbar-select-container"),super(s),this.selectElementInternal=this.element.createChild("select","toolbar-item");const r=a.Icon.create("triangle-down","toolbar-dropdown-arrow");this.element.appendChild(r),e&&this.selectElementInternal.addEventListener("change",e,!1),be(this.selectElementInternal,t),super.setTitle(t),i&&this.selectElementInternal.classList.add(i),n&&this.selectElementInternal.setAttribute("jslog",`${o.dropDown().track({change:!0}).context(n)}`)}selectElement(){return this.selectElementInternal}size(){return this.selectElementInternal.childElementCount}options(){return Array.prototype.slice.call(this.selectElementInternal.children,0)}addOption(e){this.selectElementInternal.appendChild(e)}createOption(e,t){const n=this.selectElementInternal.createChild("option");n.text=e,void 0!==t&&(n.value=t);const s=t?i.StringUtilities.toKebabCase(t):void 0;return n.setAttribute("jslog",`${o.item(s).track({click:!0})}`),n}applyEnabledState(e){super.applyEnabledState(e),this.selectElementInternal.disabled=!e}removeOption(e){this.selectElementInternal.removeChild(e)}removeOptions(){this.selectElementInternal.removeChildren()}selectedOption(){return this.selectElementInternal.selectedIndex>=0?this.selectElementInternal[this.selectElementInternal.selectedIndex]:null}select(e){this.selectElementInternal.selectedIndex=Array.prototype.indexOf.call(this.selectElementInternal,e)}setSelectedIndex(e){this.selectElementInternal.selectedIndex=e}selectedIndex(){return this.selectElementInternal.selectedIndex}setMaxWidth(e){this.selectElementInternal.style.maxWidth=e+"px"}setMinWidth(e){this.selectElementInternal.style.minWidth=e+"px"}}class Cn extends fn{inputElement;constructor(e,t,i,n){super(ls.create(e)),this.element.classList.add("checkbox"),this.inputElement=this.element.checkboxElement,t&&(Jt.install(this.inputElement,t),Jt.install(this.element.textElement,t)),i&&this.inputElement.addEventListener("click",i,!1),n&&this.inputElement.setAttribute("jslog",`${o.toggle().track({change:!0}).context(n)}`)}checked(){return this.inputElement.checked}setChecked(e){this.inputElement.checked=e}applyEnabledState(e){super.applyEnabledState(e),this.inputElement.disabled=!e}setIndeterminate(e){this.inputElement.indeterminate=e}}const Tn=[];var kn=Object.freeze({__proto__:null,Toolbar:gn,ToolbarItem:fn,ToolbarItemWithCompactLayout:class extends fn{constructor(e){super(e)}setCompactLayout(e){this.dispatchEventToListeners("CompactLayoutUpdated",e)}},ToolbarText:vn,ToolbarButton:wn,ToolbarInput:class extends fn{prompt;proxyElement;constructor(e,t,i,n,s,o,r,l){const h=document.createElement("div");h.classList.add("toolbar-input"),super(h);const d=this.element.createChild("div","toolbar-input-prompt");be(d,t||e),d.addEventListener("focus",(()=>this.element.classList.add("focused"))),d.addEventListener("blur",(()=>this.element.classList.remove("focused"))),this.prompt=new ln,this.prompt.jslogContext=l,this.proxyElement=this.prompt.attach(d),this.proxyElement.classList.add("toolbar-prompt-proxy"),this.proxyElement.addEventListener("keydown",(e=>this.onKeydownCallback(e))),this.prompt.initialize(o||(()=>Promise.resolve([]))," ",r),s&&this.prompt.setTitle(s),this.prompt.setPlaceholder(e,t),this.prompt.addEventListener("TextChanged",this.onChangeCallback.bind(this)),i&&(this.element.style.flexGrow=String(i)),n&&(this.element.style.flexShrink=String(n));const c=this.element.createChild("div","toolbar-input-clear-button");c.title=un.clearInput;const u=new a.Icon.Icon;u.data={color:"var(--icon-default)",width:"16px",height:"16px",iconName:"cross-circle-filled"},u.classList.add("search-cancel-button"),c.appendChild(u),c.addEventListener("click",(()=>{this.setValue("",!0),this.prompt.focus()})),this.updateEmptyStyles()}applyEnabledState(e){this.prompt.setEnabled(e)}setValue(e,t){this.prompt.setText(e),t&&this.onChangeCallback(),this.updateEmptyStyles()}value(){return this.prompt.textWithCurrentSuggestion()}valueWithoutSuggestion(){return this.prompt.text()}onKeydownCallback(e){"Enter"===e.key&&this.prompt.text()&&this.dispatchEventToListeners("EnterPressed",this.prompt.text()),i.KeyboardUtilities.isEscKey(e)&&this.prompt.text()&&(this.setValue("",!0),e.consume(!0))}onChangeCallback(){this.updateEmptyStyles(),this.dispatchEventToListeners("TextChanged",this.prompt.text())}updateEmptyStyles(){this.element.classList.toggle("toolbar-input-empty",!this.prompt.text())}},ToolbarToggle:xn,ToolbarMenuButton:En,ToolbarSettingToggle:In,ToolbarSeparator:yn,ToolbarComboBox:Sn,ToolbarSettingComboBox:class extends Sn{optionsInternal;setting;muteSettingListener;constructor(e,t,i){super(null,i),this.optionsInternal=e,this.setting=t,this.selectElementInternal.addEventListener("change",this.valueChanged.bind(this),!1),this.setOptions(e),t.addChangeListener(this.settingChanged,this)}setOptions(e){this.optionsInternal=e,this.selectElementInternal.removeChildren();for(let t=0;t<e.length;++t){const i=e[t],n=this.createOption(i.label,i.value);this.selectElementInternal.appendChild(n),this.setting.get()===i.value&&this.setSelectedIndex(t)}}value(){return this.optionsInternal[this.selectedIndex()].value}settingChanged(){if(this.muteSettingListener)return;const e=this.setting.get();for(let t=0;t<this.optionsInternal.length;++t)if(e===this.optionsInternal[t].value){this.setSelectedIndex(t);break}}valueChanged(e){const t=this.optionsInternal[this.selectedIndex()];this.muteSettingListener=!0,this.setting.set(t.value),this.muteSettingListener=!1}},ToolbarCheckbox:Cn,ToolbarSettingCheckbox:class extends Cn{constructor(e,t,i){super(i||e.title()||"",t,void 0,e.name),$i(this.inputElement,e)}},registerToolbarItem:function(e){Tn.push(e)}});const Mn={openInNewTab:"Open in new tab",copyLinkAddress:"Copy link address",copyFileName:"Copy file name",anotherProfilerIsAlreadyActive:"Another profiler is already active",promiseResolvedAsync:"Promise resolved (async)",promiseRejectedAsync:"Promise rejected (async)",sAsync:"{PH1} (async)",asyncCall:"Async Call",anonymous:"(anonymous)",close:"Close",ok:"OK",cancel:"Cancel"},Ln=t.i18n.registerUIStrings("ui/legacy/UIUtils.ts",Mn),Pn=t.i18n.getLocalizedString.bind(void 0,Ln),Dn="highlighted-search-result";function An(e,t,i,n,s,o,r){let a;e.addEventListener("pointerdown",(function(o){const l=new Bn,h=()=>l.elementDragStart(e,t,i,n,s,o);r?a=window.setTimeout(h,r):h()}),!1),r&&e.addEventListener("pointerup",(function(){a&&window.clearTimeout(a),a=null}),!1),null!==o&&(e.style.cursor=o||s||"")}function Rn(e,t,i,n,s,o){(new Bn).elementDragStart(e,t,i,n,s,o)}class Bn{glassPaneInUse;elementDraggingEventListener;elementEndDraggingEventListener;dragEventsTargetDocument;dragEventsTargetDocumentTop;restoreCursorAfterDrag;constructor(){this.elementDragMove=this.elementDragMove.bind(this),this.elementDragEnd=this.elementDragEnd.bind(this),this.mouseOutWhileDragging=this.mouseOutWhileDragging.bind(this)}createGlassPane(){this.glassPaneInUse=!0,Bn.glassPaneUsageCount++||(Bn.glassPane=new Ts,Bn.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),Bn.documentForMouseOut&&Bn.glassPane.show(Bn.documentForMouseOut))}disposeGlassPane(){this.glassPaneInUse&&(this.glassPaneInUse=!1,--Bn.glassPaneUsageCount||(Bn.glassPane&&(Bn.glassPane.hide(),Bn.glassPane=null),Bn.documentForMouseOut=null,Bn.rootForMouseOut=null))}elementDragStart(e,t,i,n,o,r){const a=r;if(a.button||s.Platform.isMac()&&a.ctrlKey)return;if(this.elementDraggingEventListener)return;if(t&&!t(a))return;const l=a.target instanceof Node&&a.target.ownerDocument;this.elementDraggingEventListener=i,this.elementEndDraggingEventListener=n,console.assert((Bn.documentForMouseOut||l)===l,"Dragging on multiple documents."),Bn.documentForMouseOut=l,Bn.rootForMouseOut=a.target instanceof Node&&a.target.getRootNode()||null,this.dragEventsTargetDocument=l;try{l.defaultView&&l.defaultView.top&&(this.dragEventsTargetDocumentTop=l.defaultView.top.document)}catch(e){this.dragEventsTargetDocumentTop=this.dragEventsTargetDocument}l.addEventListener("pointermove",this.elementDragMove,!0),l.addEventListener("pointerup",this.elementDragEnd,!0),Bn.rootForMouseOut&&Bn.rootForMouseOut.addEventListener("pointerout",this.mouseOutWhileDragging,{capture:!0}),this.dragEventsTargetDocumentTop&&l!==this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocumentTop.addEventListener("pointerup",this.elementDragEnd,!0);const h=e;"string"==typeof o&&(this.restoreCursorAfterDrag=function(e){l.body.style.removeProperty("cursor"),h.style.cursor=e,this.restoreCursorAfterDrag=void 0}.bind(this,h.style.cursor),h.style.cursor=o,l.body.style.cursor=o),a.preventDefault()}mouseOutWhileDragging(){this.unregisterMouseOutWhileDragging(),this.createGlassPane()}unregisterMouseOutWhileDragging(){Bn.rootForMouseOut&&Bn.rootForMouseOut.removeEventListener("pointerout",this.mouseOutWhileDragging,{capture:!0})}unregisterDragEvents(){this.dragEventsTargetDocument&&(this.dragEventsTargetDocument.removeEventListener("pointermove",this.elementDragMove,!0),this.dragEventsTargetDocument.removeEventListener("pointerup",this.elementDragEnd,!0),this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocument!==this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocumentTop.removeEventListener("pointerup",this.elementDragEnd,!0),delete this.dragEventsTargetDocument,delete this.dragEventsTargetDocumentTop)}elementDragMove(e){1===e.buttons?this.elementDraggingEventListener&&this.elementDraggingEventListener(e)&&this.cancelDragEvents(e):this.elementDragEnd(e)}cancelDragEvents(e){this.unregisterDragEvents(),this.unregisterMouseOutWhileDragging(),this.restoreCursorAfterDrag&&this.restoreCursorAfterDrag(),this.disposeGlassPane(),delete this.elementDraggingEventListener,delete this.elementEndDraggingEventListener}elementDragEnd(e){const t=this.elementEndDraggingEventListener;this.cancelDragEvents(e),e.preventDefault(),t&&t(e)}static glassPaneUsageCount=0;static glassPane=null;static documentForMouseOut=null;static rootForMouseOut=null}function On(){if(zn.size)return!0;const e=i.DOMUtilities.deepActiveElement(document);return!!e&&(e.classList.contains("text-prompt")||"INPUT"===e.nodeName||"TEXTAREA"===e.nodeName||"true"===e.contentEditable||"plaintext-only"===e.contentEditable)}function Fn(e,t){if(t){if(zn.has(e))return!1;e.classList.add("being-edited"),zn.add(e)}else{if(!zn.has(e))return!1;e.classList.remove("being-edited"),zn.delete(e)}return!0}const zn=new Set,Wn=/^(-?(?:\d+(?:\.\d+)?|\.\d+))$/,Hn="  \t\n\"':;,/()";function Nn(e){let t=null;if("wheel"===e.type){const i=e;i.deltaY<0||i.deltaX<0?t="Up":(i.deltaY>0||i.deltaX>0)&&(t="Down")}else{const i=e;"ArrowUp"===i.key||"PageUp"===i.key?t="Up":"ArrowDown"!==i.key&&"PageDown"!==i.key||(t="Down")}return t}function _n(e,t,i){const n=Nn(t);if(!n)return null;const s=t;let o=1;Ke.eventHasCtrlEquivalentKey(s)?o=100:s.shiftKey?o=10:s.altKey&&(o=.1),"Down"===n&&(o*=-1),i&&(o*=i);const r=Number((e+o).toFixed(6));return String(r).match(Wn)?r:null}function jn(e,t,n){let s,o,r,a=null,l=/(.*#)([\da-fA-F]+)(.*)/.exec(e);return l&&l.length?(s=l[1],o=l[3],r=function(e,t){const n=Nn(t);if(!n)return null;const s=t,o=parseInt(e,16);if(isNaN(o)||!isFinite(o))return null;const r=e.length,a=r/3;if(1!==a&&2!==a)return null;let l=0;Ke.eventHasCtrlEquivalentKey(s)&&(l+=Math.pow(16,2*a)),s.shiftKey&&(l+=Math.pow(16,a)),s.altKey&&(l+=1),0===l&&(l=1),"Down"===n&&(l*=-1);const h=Math.pow(16,r)-1;let d=i.NumberUtilities.clamp(o+l,0,h).toString(16).toUpperCase();for(let e=0,t=r-d.length;e<t;++e)d="0"+d;return d}(l[2],t),null!==r&&(a=s+r+o)):(l=/(.*?)(-?(?:\d+(?:\.\d+)?|\.\d+))(.*)/.exec(e),l&&l.length&&(s=l[1],o=l[3],r=_n(parseFloat(l[2]),t),null!==r&&(a=n?n(s,r,o):s+r+o))),a}function Vn(e){const t="ArrowUp"===e.key||"ArrowDown"===e.key||"wheel"===e.type,i="PageUp"===e.key||"PageDown"===e.key;return t||i}function Un(){return Pn(Mn.openInNewTab)}function Kn(){return Pn(Mn.copyLinkAddress)}function qn(e,t){t.target instanceof Window&&t.target.document.nodeType===Node.DOCUMENT_NODE&&e.body.classList.remove("inactive")}function $n(e,t){t.target instanceof Window&&t.target.document.nodeType===Node.DOCUMENT_NODE&&e.body.classList.add("inactive")}class Gn{element;previous;constructor(e){this.element=e,this.previous=i.DOMUtilities.deepActiveElement(e.ownerDocument),e.focus()}restore(){this.element&&(this.element.hasFocus()&&this.previous&&this.previous.focus(),this.previous=null,this.element=null)}}function Xn(e,t,i){return Yn(e,t,Dn,i)}function Yn(e,t,i,n){n=n||[];const s=[],o=e.childTextNodes(),a=o.map((function(e){return e.textContent})).join(""),l=e.ownerDocument;if(0===o.length)return s;const h=[];let d=0;for(const e of o){const t=new r.TextRange.SourceRange(d,e.textContent?e.textContent.length:0);d=t.offset+t.length,h.push(t)}let c=0;for(let e=0;e<t.length;++e){const r=t[e].offset,d=r+t[e].length;for(;c<o.length&&h[c].offset+h[c].length<=r;)c++;let u=c;for(;u<o.length&&h[u].offset+h[u].length<d;)u++;if(u===o.length)break;const m=l.createElement("span");m.className=i,m.textContent=a.substring(r,d);const p=o[u],g=p.textContent||"";if(p.textContent=g.substring(d-h[u].offset),n.push({node:p,type:"changed",oldText:g,newText:p.textContent,nextSibling:void 0,parent:void 0}),c===u&&p.parentElement){p.parentElement.insertBefore(m,p),n.push({node:m,type:"added",nextSibling:p,parent:p.parentElement,oldText:void 0,newText:void 0}),s.push(m);const e=l.createTextNode(g.substring(0,r-h[c].offset));p.parentElement.insertBefore(e,m),n.push({node:e,type:"added",nextSibling:m,parent:p.parentElement,oldText:void 0,newText:void 0})}else{const e=o[c],t=e.textContent||"",i=e.nextSibling;e.parentElement&&(e.parentElement.insertBefore(m,i),n.push({node:m,type:"added",nextSibling:i||void 0,parent:e.parentElement,oldText:void 0,newText:void 0}),s.push(m)),e.textContent=t.substring(0,r-h[c].offset),n.push({node:e,type:"changed",oldText:t,newText:e.textContent,nextSibling:void 0,parent:void 0});for(let e=c+1;e<u;e++){const t=o[e],i=t.textContent;t.textContent="",n.push({node:t,type:"changed",oldText:i||void 0,newText:t.textContent,nextSibling:void 0,parent:void 0})}}c=u,h[c].offset=d,h[c].length=p.textContent.length}return s}function Qn(e,t){const i=e.parentElement,n=e.nextSibling;(t=t||e.ownerDocument.body).appendChild(e),e.positionAt(0,0);const s=e.getBoundingClientRect();return e.positionAt(void 0,void 0),i?i.insertBefore(e,n):e.remove(),new _e(s.width,s.height)}class Zn{handlers;autoInvoke;constructor(e){this.handlers=null,this.autoInvoke=e}add(e,t){this.handlers||(this.handlers=new Map,this.autoInvoke&&this.scheduleInvoke());let i=this.handlers.get(e);i||(i=new Set,this.handlers.set(e,i)),i.add(t)}scheduleInvoke(){this.handlers&&requestAnimationFrame(this.invoke.bind(this))}invoke(){const e=this.handlers;if(this.handlers=null,e)for(const[t,i]of e)for(const e of i)e.call(t)}}let Jn=0,es=null;function ts(e,t){es||(es=new Zn(!0)),es.add(e,t)}class is{element;callback;editKey;longClickData;longClickInterval;constructor(e,t,n=(e=>i.KeyboardUtilities.isEnterOrSpaceKey(e))){this.element=e,this.callback=t,this.editKey=n,this.enable()}reset(){this.longClickInterval&&(clearInterval(this.longClickInterval),delete this.longClickInterval)}enable(){if(this.longClickData)return;const e=function(e){if(this.editKey(e)){const t=this.callback;this.longClickInterval=window.setTimeout(t.bind(null,e),is.TIME_MS)}}.bind(this),t=function(e){this.editKey(e)&&this.reset()}.bind(this),i=function(e){if(1!==e.which)return;const t=this.callback;this.longClickInterval=window.setTimeout(t.bind(null,e),is.TIME_MS)}.bind(this),n=function(e){if(1!==e.which)return;this.reset()}.bind(this),s=this.reset.bind(this);this.element.addEventListener("keydown",e,!1),this.element.addEventListener("keyup",t,!1),this.element.addEventListener("pointerdown",i,!1),this.element.addEventListener("pointerout",s,!1),this.element.addEventListener("pointerup",n,!1),this.element.addEventListener("click",s,!0),this.longClickData={mouseUp:n,mouseDown:i,reset:s}}dispose(){this.longClickData&&(this.element.removeEventListener("pointerdown",this.longClickData.mouseDown,!1),this.element.removeEventListener("pointerout",this.longClickData.reset,!1),this.element.removeEventListener("pointerup",this.longClickData.mouseUp,!1),this.element.addEventListener("click",this.longClickData.reset,!0),delete this.longClickData)}static TIME_MS=200}function ns(e){return e||Pn(Mn.anonymous)}const ss=(e,t)=>{const i=e.ownerDocument.createTextNode(t);return e.appendChild(i),i};function os(e,t,i){const n=document.createElement("button");return i?.className&&(n.className=i.className),n.textContent=e,n.classList.add("text-button"),i?.primary&&n.classList.add("primary-button"),t&&n.addEventListener("click",t),i?.jslogContext&&n.setAttribute("jslog",`${o.action().track({click:!0}).context(i.jslogContext)}`),n.type="button",n}function rs(e,t,i){const n=document.createElement("input");return e&&(n.className=e),n.spellcheck=!1,n.classList.add("harmony-input"),t&&(n.type=t),i&&n.setAttribute("jslog",`${o.textField().track({keydown:!0}).context(i)}`),n}function as(e,t,i){const n=new Option(e,t||e);return i&&n.setAttribute("jslog",`${o.item(i).track({click:!0})}`),n}class ls extends HTMLSpanElement{shadowRootInternal;checkboxElement;textElement;constructor(){super(),ls.lastId=ls.lastId+1;const e="ui-checkbox-label"+ls.lastId;this.shadowRootInternal=d.createShadowRootWithCoreStyles(this,{cssFile:Le,delegatesFocus:void 0}),this.checkboxElement=this.shadowRootInternal.createChild("input"),this.checkboxElement.type="checkbox",this.checkboxElement.setAttribute("id",e),this.textElement=this.shadowRootInternal.createChild("label","dt-checkbox-text"),this.textElement.setAttribute("for",e),this.shadowRootInternal.createChild("slot")}static create(e,t,i,n){ls.constructorInternal||(ls.constructorInternal=d.registerCustomElement("span","dt-checkbox",ls));const s=ls.constructorInternal();return s.checkboxElement.checked=Boolean(t),n&&s.checkboxElement.setAttribute("jslog",`${o.toggle().track({change:!0}).context(n)}`),void 0!==e&&(s.textElement.textContent=e,s.checkboxElement.title=e,void 0!==i&&(s.textElement.createChild("div","dt-checkbox-subtitle").textContent=i)),s}static lastId=0;static constructorInternal=null}class hs extends HTMLSpanElement{#i;constructor(){super();const e=d.createShadowRootWithCoreStyles(this,{cssFile:void 0,delegatesFocus:void 0});this.#i=new a.Icon.Icon,this.#i.style.setProperty("margin-right","4px"),this.#i.style.setProperty("vertical-align","baseline"),e.appendChild(this.#i),e.createChild("slot")}set data(e){this.#i.data=e,"14px"===e.height?this.#i.style.setProperty("margin-bottom","-2px"):"20px"===e.height&&this.#i.style.setProperty("margin-bottom","2px")}}let ds=0;class cs extends HTMLSpanElement{radioElement;labelElement;constructor(){super(),this.radioElement=this.createChild("input","dt-radio-button"),this.labelElement=this.createChild("label");const e="dt-radio-button-id"+ ++ds;this.radioElement.id=e,this.radioElement.type="radio",this.labelElement.htmlFor=e;d.createShadowRootWithCoreStyles(this,{cssFile:rt,delegatesFocus:void 0}).createChild("slot"),this.addEventListener("click",this.radioClickHandler.bind(this),!1)}radioClickHandler(){this.radioElement.checked||this.radioElement.disabled||(this.radioElement.checked=!0,this.radioElement.dispatchEvent(new Event("change")))}}d.registerCustomElement("span","dt-radio",cs),d.registerCustomElement("span","dt-icon-label",hs);class us extends HTMLSpanElement{sliderElement;constructor(){super();const e=d.createShadowRootWithCoreStyles(this,{cssFile:at,delegatesFocus:void 0});this.sliderElement=document.createElement("input"),this.sliderElement.classList.add("dt-range-input"),this.sliderElement.type="range",e.appendChild(this.sliderElement)}set value(e){this.sliderElement.value=String(e)}get value(){return Number(this.sliderElement.value)}}d.registerCustomElement("span","dt-slider",us);class ms extends HTMLSpanElement{textElement;constructor(){super();const e=d.createShadowRootWithCoreStyles(this,{cssFile:lt,delegatesFocus:void 0});this.textElement=e.createChild("div"),this.textElement.className="info",this.textElement.createChild("slot")}set type(e){this.textElement.className=e}}d.registerCustomElement("span","dt-small-bubble",ms);class ps extends HTMLDivElement{buttonElement;constructor(){super();const e=d.createShadowRootWithCoreStyles(this,{cssFile:Pe,delegatesFocus:void 0});this.buttonElement=e.createChild("div","close-button"),this.buttonElement.setAttribute("jslog",`${o.close().track({click:!0})}`),Jt.install(this.buttonElement,Pn(Mn.close)),be(this.buttonElement,Pn(Mn.close)),F(this.buttonElement);const t=a.Icon.create("cross");this.buttonElement.appendChild(t)}setAccessibleName(e){be(this.buttonElement,e)}setTabbable(e){this.buttonElement.tabIndex=e?0:-1}}function gs(e,t,i,n){if(i<=10)return"";t.length>200&&(t=n(t,200));const s=bs(e,t);if(s<=i)return t;let o=0,r=t.length,a=0,l=s;for(;o<r&&a!==l&&a!==i;){const s=Math.ceil(o+(r-o)*(i-a)/(l-a)),h=bs(e,n(t,s));h<=i?(o=s,a=h):(r=s-1,l=h)}return"…"!==(t=n(t,o))?t:""}function bs(e,t){if(t.length>200)return e.measureText(t).width;fs||(fs=new Map);const i=e.font;let n=fs.get(i);n||(n=new Map,fs.set(i,n));let s=n.get(t);return s||(s=e.measureText(t).width,n.set(t,s)),s}d.registerCustomElement("div","dt-close-button",ps);let fs=null;function vs(e){return/(\?|&)utm_source=devtools/.test(e)?e:-1===e.indexOf("?")?e.replace(/^([^#]*)(#.*)?$/g,"$1?utm_source=devtools$2"):e.replace(/^([^#]*)(#.*)?$/g,"$1&utm_source=devtools$2")}function ws(e){return/(\/\/developers.google.com\/|\/\/web.dev\/|\/\/developer.chrome.com\/)/.test(e)?vs(e):e}const xs=(e,t)=>{let i=e;for(;i&&i!==e.ownerDocument;i=i.parentNodeOrShadowHost())for(let e=0;e<t.length;++e)if(i.nodeName.toLowerCase()===t[e].toLowerCase())return i;return null},Es=(e,t,i)=>{let n=e,s=null;for(;n;){const e=n.elementFromPoint(t,i);if(!e||s===e)break;s=e,n=s.shadowRoot}return s},Is=e=>{const t=e;if(!(t.which||t.pageX||t.pageY||t.clientX||t.clientY||t.movementX||t.movementY))return null;const i=t.target&&t.target.getComponentRoot();return i?Es(i,t.pageX,t.pageY):null},ys=[];function Ss(e){return ys.filter((function(t){if(!t.contextTypes)return!0;for(const i of t.contextTypes())if(e instanceof i)return!0;return!1}))}var Cs=Object.freeze({__proto__:null,highlightedSearchResultClassName:Dn,highlightedCurrentSearchResultClassName:"current-search-result",installDragHandle:An,elementDragStart:Rn,isBeingEdited:function(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=e;if(t.classList.contains("text-prompt")||"INPUT"===t.nodeName||"TEXTAREA"===t.nodeName)return!0;if(!zn.size)return!1;let i=t;for(;i;){if(zn.has(t))return!0;i=i.parentElementOrShadowHost()}return!1},isEditing:On,markBeingEdited:Fn,StyleValueDelimiters:Hn,getValueModificationDirection:Nn,modifiedFloatNumber:_n,createReplacementString:jn,isElementValueModification:Vn,handleElementValueModifications:function(e,t,n,s,r){if(!Vn(e))return!1;o.logKeyDown(e,"element-value-modification");const a=t.getComponentSelection();if(!a||!a.rangeCount)return!1;const l=a.getRangeAt(0);if(!l.commonAncestorContainer.isSelfOrDescendant(t))return!1;const h=t.textContent,d=i.DOMUtilities.rangeOfWord(l.startContainer,l.startOffset,Hn,t),c=d.toString();if(s&&s(c))return!1;const u=jn(c,e,r);if(u){const t=document.createTextNode(u);d.deleteContents(),d.insertNode(t);const i=document.createRange();return i.setStart(t,0),i.setEnd(t,u.length),a.removeAllRanges(),a.addRange(i),e.handled=!0,e.preventDefault(),n&&n(h||"",u),!0}return!1},openLinkExternallyLabel:Un,copyLinkAddressLabel:Kn,copyFileNameLabel:function(){return Pn(Mn.copyFileName)},anotherProfilerActiveLabel:function(){return Pn(Mn.anotherProfilerIsAlreadyActive)},asyncStackTraceLabel:function(e,t){if(e){if("Promise.resolve"===e)return Pn(Mn.promiseResolvedAsync);if("Promise.reject"===e)return Pn(Mn.promiseRejectedAsync);if(("await"===e||"async function"===e)&&0!==t.length){e=`await in ${ns(t[t.length-1].functionName)}`}return Pn(Mn.sAsync,{PH1:e})}return Pn(Mn.asyncCall)},installComponentRootStyles:function(e){d.injectCoreStyles(e),e.classList.add("platform-"+s.Platform.platform()),s.Platform.isMac()||0!==d.measuredScrollbarWidth(e.ownerDocument)||e.classList.add("overlay-scrollbar-enabled")},ElementFocusRestorer:Gn,highlightSearchResult:function(e,t,i,n){const s=Xn(e,[new r.TextRange.SourceRange(t,i)],n);return s.length?s[0]:null},highlightSearchResults:Xn,runCSSAnimationOnce:function(e,t){e.classList.contains(t)&&e.classList.remove(t),e.addEventListener("webkitAnimationEnd",(function i(){e.classList.remove(t),e.removeEventListener("webkitAnimationEnd",i,!1)}),!1),e.classList.add(t)},highlightRangesWithStyleClass:Yn,applyDomChanges:function(e){for(let t=0,i=e.length;t<i;++t){const i=e[t];switch(i.type){case"added":i.parent?.insertBefore(i.node,i.nextSibling??null);break;case"changed":i.node.textContent=i.newText??null}}},revertDomChanges:function(e){for(let t=e.length-1;t>=0;--t){const i=e[t];switch(i.type){case"added":i.node.remove();break;case"changed":i.node.textContent=i.oldText??null}}},measurePreferredSize:Qn,startBatchUpdate:function(){Jn++||(es=new Zn(!1))},endBatchUpdate:function(){--Jn||es&&(es.scheduleInvoke(),es=null)},invokeOnceAfterBatchUpdate:ts,animateFunction:function(e,t,n,s,o){const r=e.performance.now();let a=e.requestAnimationFrame((function l(h){const d=i.NumberUtilities.clamp((h-r)/s,0,1);t(...n.map((e=>e.from+(e.to-e.from)*d))),d<1?a=e.requestAnimationFrame(l):o&&o()}));return()=>e.cancelAnimationFrame(a)},LongClickController:is,initializeUIUtils:function(e){e.body.classList.toggle("inactive",!e.hasFocus()),e.defaultView&&(e.defaultView.addEventListener("focus",qn.bind(void 0,e),!1),e.defaultView.addEventListener("blur",$n.bind(void 0,e),!1)),e.addEventListener("focus",d.focusChanged.bind(void 0),!0);const t=e.body;Ts.setContainer(t)},beautifyFunctionName:ns,createTextChild:ss,createTextChildren:(e,...t)=>{for(const i of t)ss(e,i)},createTextButton:os,createInput:rs,createSelect:function(e,t){const n=document.createElement("select");n.classList.add("chrome-select"),be(n,e);for(const e of t)if(e instanceof Map)for(const[t,s]of e){const e=n.createChild("optgroup");e.label=t;for(const t of s)"string"==typeof t&&e.appendChild(as(t,t,i.StringUtilities.toKebabCase(t)))}else"string"==typeof e&&n.add(as(e,e,i.StringUtilities.toKebabCase(e)));return n},createOption:as,createLabel:function(e,t,i){const n=document.createElement("label");return t&&(n.className=t),n.textContent=e,i&&B(n,i),n},createRadioLabel:function(e,t,i,n){const s=document.createElement("span",{is:"dt-radio"});return s.radioElement.name=e,s.radioElement.checked=Boolean(i),ss(s.labelElement,t),n&&s.radioElement.setAttribute("jslog",`${o.toggle().track({change:!0}).context(n)}`),s},createIconLabel:function(e){const t=document.createElement("span",{is:"dt-icon-label"});return e.title&&(t.createChild("span").textContent=e.title),t.data={iconName:e.iconName,color:e.color??"var(--icon-default)",width:e.width??"14px",height:e.height??"14px"},t},createSlider:function(e,t,i){const n=document.createElement("span",{is:"dt-slider"});return n.sliderElement.min=String(e),n.sliderElement.max=String(t),n.sliderElement.step=String(1),n.sliderElement.tabIndex=i,n},setTitle:function(e,t){be(e,t),Jt.install(e,t)},CheckboxLabel:ls,DevToolsIconLabel:hs,DevToolsRadioButton:cs,DevToolsSlider:us,DevToolsSmallBubble:ms,DevToolsCloseButton:ps,bindInput:function(e,t,i,n,s){function o(t){if(t===e.value)return;const{valid:n}=i(t);e.classList.toggle("error-input",!n),e.value=t}return e.addEventListener("change",(function(){const{valid:n}=i(e.value);e.classList.toggle("error-input",!n),n&&t(e.value)}),!1),e.addEventListener("input",(function(){e.classList.toggle("error-input",!i(e.value))}),!1),e.addEventListener("keydown",(function(r){if("Enter"===r.key){const{valid:n}=i(e.value);return n&&t(e.value),void r.preventDefault()}if(!n)return;const a=_n(parseFloat(e.value),r,s);if(null===a)return;const l=String(a),{valid:h}=i(l);h&&o(l);r.preventDefault()}),!1),e.addEventListener("focus",e.select.bind(e),!1),o},trimText:gs,trimTextMiddle:function(e,t,n){return gs(e,t,n,((e,t)=>i.StringUtilities.trimMiddle(e,t)))},trimTextEnd:function(e,t,n){return gs(e,t,n,((e,t)=>i.StringUtilities.trimEndWithMaxLength(e,t)))},measureTextWidth:bs,addReferrerToURL:vs,addReferrerToURLIfNecessary:ws,loadImage:function(e){return new Promise((t=>{const i=new Image;i.addEventListener("load",(()=>t(i))),i.addEventListener("error",(()=>t(null))),i.src=e}))},createFileSelectorElement:function(e){const t=document.createElement("input");return t.type="file",t.style.display="none",t.tabIndex=-1,t.onchange=()=>{t.files&&e(t.files[0])},t},MaxLengthForDisplayedURLs:150,MessageDialog:class{static async show(e,t,i){const n=new Ds(i);n.setSizeBehavior("MeasureContent"),n.setDimmed(!0);const s=d.createShadowRootWithCoreStyles(n.contentElement,{cssFile:De,delegatesFocus:void 0}).createChild("div","widget");await new Promise((i=>{const o=os(Pn(Mn.ok),i,{jslogContext:"confirm",primary:!0});s.createChild("div","message").createChild("span").textContent=e,s.createChild("div","button").appendChild(o),n.setOutsideClickCallback((e=>{e.consume(),i(void 0)})),n.show(t),o.focus()})),n.hide()}},ConfirmDialog:class{static async show(e,t,i){const n=new Ds(i?.jslogContext);n.setSizeBehavior("MeasureContent"),n.setDimmed(!0),be(n.contentElement,e);const s=d.createShadowRootWithCoreStyles(n.contentElement,{cssFile:De,delegatesFocus:void 0}).createChild("div","widget");s.createChild("div","message").createChild("span").textContent=e;const o=s.createChild("div","button"),r=await new Promise((e=>{const s=os(i?.okButtonLabel||Pn(Mn.ok),(()=>e(!0)),{jslogContext:"confirm",primary:!0});o.appendChild(s),o.appendChild(os(i?.cancelButtonLabel||Pn(Mn.cancel),(()=>e(!1)),{jslogContext:"cancel"})),n.setOutsideClickCallback((t=>{t.consume(),e(!1)})),n.show(t),s.focus()}));return n.hide(),r}},createInlineButton:function(e){const t=document.createElement("span"),i=d.createShadowRootWithCoreStyles(t,{cssFile:Ue,delegatesFocus:void 0});t.classList.add("inline-button");const n=new gn("");return n.appendToolbarItem(e),i.appendChild(n.element),t},Renderer:class{static async render(e,t){if(!e)throw new Error("Can't render "+e);const i=Ss(e)[0];if(!i)return null;return(await i.loadRenderer()).render(e,t)}},formatTimestamp:function(e,t){const i=new Date(e),n=i.getFullYear()+"-"+o(i.getMonth()+1,2)+"-"+o(i.getDate(),2),s=o(i.getHours(),2)+":"+o(i.getMinutes(),2)+":"+o(i.getSeconds(),2)+"."+o(i.getMilliseconds(),3);return t?n+" "+s:s;function o(e,t){return String(e).padStart(t,"0")}},isScrolledToBottom:e=>Math.abs(e.scrollTop+e.clientHeight-e.scrollHeight)<=2,createSVGChild:function(e,t,i){const n=e.ownerDocument.createElementNS("http://www.w3.org/2000/svg",t);return i&&n.setAttribute("class",i),e.appendChild(n),n},enclosingNodeOrSelfWithNodeNameInArray:xs,enclosingNodeOrSelfWithNodeName:function(e,t){return xs(e,[t])},deepElementFromPoint:Es,deepElementFromEvent:Is,registerRenderer:function(e){ys.push(e)},getApplicableRegisteredRenderers:Ss});class Ts{widgetInternal;element;contentElement;arrowElement;onMouseDownBound;onClickOutsideCallback;maxSize;positionX;positionY;anchorBox;anchorBehavior;sizeBehavior;marginBehavior;#n=!1;constructor(e){this.widgetInternal=new Wt(!0),this.widgetInternal.markAsRoot(),this.element=this.widgetInternal.element,this.contentElement=this.widgetInternal.contentElement,e&&this.contentElement.setAttribute("jslog",e),this.arrowElement=document.createElement("span"),this.arrowElement.classList.add("arrow","hidden"),this.element.shadowRoot&&this.element.shadowRoot.appendChild(this.arrowElement),this.registerRequiredCSS(Me),this.setPointerEventsBehavior("PierceGlassPane"),this.onMouseDownBound=this.onMouseDown.bind(this),this.onClickOutsideCallback=null,this.maxSize=null,this.positionX=null,this.positionY=null,this.anchorBox=null,this.anchorBehavior="PreferTop",this.sizeBehavior="SetExactSize",this.marginBehavior="DefaultMargin"}setJsLog(e){this.contentElement.setAttribute("jslog",e)}isShowing(){return this.widgetInternal.isShowing()}registerRequiredCSS(e){this.widgetInternal.registerRequiredCSS(e)}registerCSSFiles(e){this.widgetInternal.registerCSSFiles(e)}setDefaultFocusedElement(e){this.widgetInternal.setDefaultFocusedElement(e)}setDimmed(e){this.element.classList.toggle("dimmed-pane",e)}setPointerEventsBehavior(e){this.element.classList.toggle("no-pointer-events","BlockedByGlassPane"!==e),this.contentElement.classList.toggle("no-pointer-events","PierceContents"===e)}setOutsideClickCallback(e){this.onClickOutsideCallback=e}setMaxContentSize(e){this.maxSize=e,this.positionContent()}setSizeBehavior(e){this.sizeBehavior=e,this.positionContent()}setContentPosition(e,t){this.positionX=e,this.positionY=t,this.positionContent()}setContentAnchorBox(e){this.anchorBox=e,this.positionContent()}setAnchorBehavior(e){this.anchorBehavior=e}setMarginBehavior(e){this.marginBehavior=e,this.arrowElement.classList.toggle("hidden","Arrow"!==e)}setIgnoreLeftMargin(e){this.#n=e}show(e){this.isShowing()||(this.element.style.zIndex=""+(3e3+1e3*Ms.size),this.element.setAttribute("data-devtools-glass-pane",""),e.body.addEventListener("mousedown",this.onMouseDownBound,!0),e.body.addEventListener("pointerdown",this.onMouseDownBound,!0),this.widgetInternal.show(e.body),Ms.add(this),this.positionContent())}hide(){this.isShowing()&&(Ms.delete(this),this.element.ownerDocument.body.removeEventListener("mousedown",this.onMouseDownBound,!0),this.element.ownerDocument.body.removeEventListener("pointerdown",this.onMouseDownBound,!0),this.widgetInternal.detach())}onMouseDown(e){if(!this.onClickOutsideCallback)return;const t=Is(e);t&&!this.contentElement.isSelfOrAncestor(t)&&this.onClickOutsideCallback.call(null,e)}positionContent(){if(!this.isShowing())return;const e="Arrow"===this.marginBehavior,t=e?8:"NoMargin"===this.marginBehavior?0:3,n=d.measuredScrollbarWidth(this.element.ownerDocument),s=10,o=ks.get(this.element.ownerDocument);"MeasureContent"===this.sizeBehavior&&(this.contentElement.positionAt(0,0),this.contentElement.style.width="",this.contentElement.style.maxWidth="",this.contentElement.style.height="",this.contentElement.style.maxHeight="");const r=o.offsetWidth,a=o.offsetHeight;let l=r-2*t,h=a-2*t,c=t,u=t;if(this.maxSize&&(l=Math.min(l,this.maxSize.width),h=Math.min(h,this.maxSize.height)),"MeasureContent"===this.sizeBehavior){const e=this.contentElement.getBoundingClientRect(),t=h<e.height?n:0,i=l<e.width?n:0;l=Math.min(l,e.width+t),h=Math.min(h,e.height+i)}if(this.anchorBox){const n=this.anchorBox.relativeToElement(o);let d=this.anchorBehavior;if(this.arrowElement.classList.remove("arrow-none","arrow-top","arrow-bottom","arrow-left","arrow-right"),"PreferTop"===d||"PreferBottom"===d){const m=n.y-2*t,p=a-n.y-n.height-2*t;let g;"PreferTop"===d&&m<h&&p>m&&(d="PreferBottom"),"PreferBottom"===d&&p<h&&m>p&&(d="PreferTop");let b=!0;if("PreferTop"===d){u=Math.max(t,n.y-h-t);const e=n.y-u-t;"MeasureContent"===this.sizeBehavior?h>e&&(this.arrowElement.classList.add("arrow-none"),b=!1):h=Math.min(h,e),this.arrowElement.classList.add("arrow-bottom"),g=n.y-t}else{u=n.y+n.height+t;const e=a-u-t;"MeasureContent"===this.sizeBehavior?h>e&&(this.arrowElement.classList.add("arrow-none"),u=a-t-h,b=!1):h=Math.min(h,e),this.arrowElement.classList.add("arrow-top"),g=n.y+n.height+t}const f=Math.min(n.x,r-l-t);if(c=Math.max(t,f),this.#n&&t>f&&(c=0),b?e&&c-s>=t&&(c-=s):c=Math.min(c+s,r-l-t),l=Math.min(l,r-c-t),20>=l)this.arrowElement.classList.add("arrow-none");else{let e=n.x+Math.min(50,Math.floor(n.width/2));e=i.NumberUtilities.clamp(e,c+s,c+l-s),this.arrowElement.positionAt(e,g,o)}}else{const m=n.x-2*t,p=r-n.x-n.width-2*t;let g;"PreferLeft"===d&&m<l&&p>m&&(d="PreferRight"),"PreferRight"===d&&p<l&&m>p&&(d="PreferLeft");let b=!0;if("PreferLeft"===d){c=Math.max(t,n.x-l-t);const e=n.x-c-t;"MeasureContent"===this.sizeBehavior?l>e&&(this.arrowElement.classList.add("arrow-none"),b=!1):l=Math.min(l,e),this.arrowElement.classList.add("arrow-right"),g=n.x-t}else{c=n.x+n.width+t;const e=r-c-t;"MeasureContent"===this.sizeBehavior?l>e&&(this.arrowElement.classList.add("arrow-none"),c=r-t-l,b=!1):l=Math.min(l,e),this.arrowElement.classList.add("arrow-left"),g=n.x+n.width+t}if(u=Math.max(t,Math.min(n.y,a-h-t)),b?e&&u-s>=t&&(u-=s):u=Math.min(u+s,a-h-t),h=Math.min(h,a-u-t),20>=h)this.arrowElement.classList.add("arrow-none");else{let e=n.y+Math.min(50,Math.floor(n.height/2));e=i.NumberUtilities.clamp(e,u+s,u+h-s),this.arrowElement.positionAt(g,e,o)}}}else c=null!==this.positionX?this.positionX:(r-l)/2,u=null!==this.positionY?this.positionY:(a-h)/2,l=Math.min(l,r-c-t),h=Math.min(h,a-u-t),this.arrowElement.classList.add("arrow-none");this.contentElement.style.width=l+"px","SetExactWidthMaxHeight"===this.sizeBehavior?this.contentElement.style.maxHeight=h+"px":this.contentElement.style.height=h+"px",this.contentElement.positionAt(c,u,o),this.widgetInternal.doResize()}widget(){return this.widgetInternal}static setContainer(e){ks.set(e.ownerDocument,e),Ts.containerMoved(e)}static container(e){return ks.get(e)}static containerMoved(e){for(const t of Ms)t.isShowing()&&t.element.ownerDocument===e.ownerDocument&&t.positionContent()}}const ks=new Map,Ms=new Set,Ls=Ms;var Ps=Object.freeze({__proto__:null,GlassPane:Ts,GlassPanePanes:Ls});class Ds extends(e.ObjectWrapper.eventMixin(Ts)){tabIndexBehavior;tabIndexMap;focusRestorer;closeOnEscape;targetDocument;targetDocumentKeyDownHandler;escapeKeyCallback;constructor(e){super(),this.registerRequiredCSS(ke),this.contentElement.tabIndex=0,this.contentElement.addEventListener("focus",(()=>this.widget().focus()),!1),e&&this.contentElement.setAttribute("jslog",`${o.dialog(e).track({resize:!0})}`),this.widget().setDefaultFocusedElement(this.contentElement),this.setPointerEventsBehavior("BlockedByGlassPane"),this.setOutsideClickCallback((e=>{this.hide(),e.consume(!0)})),z(this.contentElement),this.tabIndexBehavior="DisableAllTabIndex",this.tabIndexMap=new Map,this.focusRestorer=null,this.closeOnEscape=!0,this.targetDocumentKeyDownHandler=this.onKeyDown.bind(this),this.escapeKeyCallback=null}static hasInstance(){return Boolean(Ds.instance)}show(e){const t=e instanceof Document?e:(e||Hi.instance().element).ownerDocument;this.targetDocument=t,this.targetDocument.addEventListener("keydown",this.targetDocumentKeyDownHandler,!0),Ds.instance&&Ds.instance.hide(),Ds.instance=this,this.disableTabIndexOnElements(t),super.show(t),this.focusRestorer=new jt(this.widget())}hide(){this.focusRestorer&&this.focusRestorer.restore(),super.hide(),this.targetDocument&&this.targetDocument.removeEventListener("keydown",this.targetDocumentKeyDownHandler,!0),this.restoreTabIndexOnElements(),this.dispatchEventToListeners("hidden"),Ds.instance=null}setCloseOnEscape(e){this.closeOnEscape=e}setEscapeKeyCallback(e){this.escapeKeyCallback=e}addCloseButton(){this.contentElement.createChild("div","dialog-close-button","dt-close-button").addEventListener("click",(()=>this.hide()),!1)}setOutsideTabIndexBehavior(e){this.tabIndexBehavior=e}disableTabIndexOnElements(e){if("PreserveTabIndex"===this.tabIndexBehavior)return;let t=null;"PreserveMainViewTabIndex"===this.tabIndexBehavior&&(t=this.getMainWidgetTabIndexElements(Hi.instance().ownerSplit())),this.tabIndexMap.clear();let i=e;for(;i;i=i.traverseNextNode(e))if(i instanceof HTMLElement){const e=i,n=e.tabIndex;t?.has(e)||(n>=0?(this.tabIndexMap.set(e,n),e.tabIndex=-1):e.hasAttribute("contenteditable")&&(this.tabIndexMap.set(e,e.hasAttribute("tabindex")?n:0),e.tabIndex=-1))}}getMainWidgetTabIndexElements(e){const t=new Set;if(!e)return t;const i=e.mainWidget();if(!i||!i.element)return t;let n=i.element;for(;n;n=n.traverseNextNode(i.element)){if(!(n instanceof HTMLElement))continue;const e=n;e.tabIndex<0||t.add(e)}return t}restoreTabIndexOnElements(){for(const e of this.tabIndexMap.keys())e.tabIndex=this.tabIndexMap.get(e);this.tabIndexMap.clear()}onKeyDown(e){if(e.keyCode===nt.Esc.code&&Ke.hasNoModifiers(e)){if(this.escapeKeyCallback&&this.escapeKeyCallback(e),e.handled)return;this.closeOnEscape&&(e.consume(!0),this.hide())}}static instance=null}var As=Object.freeze({__proto__:null,Dialog:Ds});let Rs;class Bs{actionRegistry;actionToShortcut;keyMap;activePrefixKey;activePrefixTimeout;consumePrefix;devToolsDefaultShortcutActions;disabledDefaultShortcutsForAction;keybindSetSetting;userShortcutsSetting;constructor(t){this.actionRegistry=t,this.actionToShortcut=new i.MapUtilities.Multimap,this.keyMap=new Os(0,0),this.activePrefixKey=null,this.activePrefixTimeout=null,this.consumePrefix=null,this.devToolsDefaultShortcutActions=new Set,this.disabledDefaultShortcutsForAction=new i.MapUtilities.Multimap,this.keybindSetSetting=e.Settings.Settings.instance().moduleSetting("active-keybind-set"),this.keybindSetSetting.addChangeListener((e=>{s.userMetrics.keybindSetSettingChanged(e.data),this.registerBindings()})),this.userShortcutsSetting=e.Settings.Settings.instance().moduleSetting("user-shortcuts"),this.userShortcutsSetting.addChangeListener(this.registerBindings,this),this.registerBindings()}static instance(e={forceNew:null,actionRegistry:null}){const{forceNew:t,actionRegistry:i}=e;if(!Rs||t){if(!i)throw new Error("Missing actionRegistry for shortcutRegistry");Rs=new Bs(i)}return Rs}static removeInstance(){Rs=void 0}applicableActions(e,t={}){let i=[];const n=(this.activePrefixKey||this.keyMap).getNode(e);n&&(i=n.actions());const s=this.actionRegistry.applicableActions(i,b.instance());if(n)for(const e of Object.keys(t))if(n.actions().indexOf(e)>=0&&this.actionRegistry.hasAction(e)){const t=this.actionRegistry.getAction(e);s.push(t)}return s}shortcutsForAction(e){return[...this.actionToShortcut.get(e)]}actionsForDescriptors(e){let t=this.keyMap;for(const{key:i}of e){if(!t)return[];t=t.getNode(i)}return t?t.actions():[]}globalShortcutKeys(){const e=[];for(const t of this.keyMap.chords().values()){const i=t.actions();(this.actionRegistry.applicableActions(i,b.instance()).length||t.hasChords())&&e.push(t.key())}return e}keysForActions(e){const t=e.flatMap((e=>[...this.actionToShortcut.get(e)].flatMap((e=>e.descriptors.map((e=>e.key))))));return[...new Set(t)]}shortcutTitleForAction(e){for(const t of this.actionToShortcut.get(e))return t.title()}handleShortcut(e,t){this.handleKey(Ke.makeKeyFromEvent(e),e.key,e,t)}actionHasDefaultShortcut(e){return this.devToolsDefaultShortcutActions.has(e)}getShortcutListener(e){const t=Object.keys(e).flatMap((e=>[...this.actionToShortcut.get(e)])),i=new Os(0,0);return t.forEach((e=>{i.addKeyMapping(e.descriptors.map((e=>e.key)),e.action)})),t=>{const n=Ke.makeKeyFromEvent(t),s=this.activePrefixKey?i.getNode(this.activePrefixKey.key()):i;s&&s.getNode(n)&&this.handleShortcut(t,e)}}addShortcutListener(e,t){const i=this.getShortcutListener(t);return e.addEventListener("keydown",i),i}async handleKey(e,t,i,n){const o=e>>8,r=Boolean(n)||Boolean(this.activePrefixKey),a=this.keyMap.getNode(e),l=this.applicableActions(e,n).length>0||a&&a.hasChords();if((r||!function(){if(!i||!On()||/^F\d+|Control|Shift|Alt|Meta|Escape|Win|U\+001B$/.test(t))return!1;if(!o)return!0;const n=qe;if(s.Platform.isMac()){if(Ke.makeKey("z",n.Meta)===e)return!0;if(Ke.makeKey("z",n.Meta|n.Shift)===e)return!0}else{if(Ke.makeKey("z",n.Ctrl)===e)return!0;if(Ke.makeKey("y",n.Ctrl)===e)return!0;if(!s.Platform.isWin()&&Ke.makeKey("z",n.Ctrl|n.Shift)===e)return!0}if((o&(n.Ctrl|n.Alt))==(n.Ctrl|n.Alt))return s.Platform.isWin();return!h(n.Ctrl)&&!h(n.Alt)&&!h(n.Meta)}())&&l&&!Ke.isModifier(Ke.keyCodeAndModifiersFromKey(e).keyCode)&&(i&&i.consume(!0),r||!Ds.hasInstance())){if(this.activePrefixTimeout){clearTimeout(this.activePrefixTimeout);const e=await d.call(this);if(this.activePrefixKey=null,this.activePrefixTimeout=null,e)return;this.consumePrefix&&await this.consumePrefix()}a&&a.hasChords()?(this.activePrefixKey=a,this.consumePrefix=async()=>{this.activePrefixKey=null,this.activePrefixTimeout=null,await d.call(this)},this.activePrefixTimeout=window.setTimeout(this.consumePrefix,Ws)):await d.call(this)}function h(e){return Boolean(o&e)}async function d(){const t=this.applicableActions(e,n);if(!t.length)return!1;for(const e of t){let t;if(n&&n[e.id()]&&(t=await n[e.id()]()),n||(t=await e.execute()),t)return s.userMetrics.keyboardShortcutFired(e.id()),!0}return!1}}registerUserShortcut(e){for(const t of this.disabledDefaultShortcutsForAction.get(e.action))if(t.descriptorsMatch(e.descriptors)&&t.hasKeybindSet(this.keybindSetSetting.get()))return void this.removeShortcut(t);for(const t of this.actionToShortcut.get(e.action))if(t.descriptorsMatch(e.descriptors))return;this.addShortcutToSetting(e)}removeShortcut(e){"DefaultShortcut"===e.type||"KeybindSetShortcut"===e.type?this.addShortcutToSetting(e.changeType("DisabledDefault")):this.removeShortcutFromSetting(e)}disabledDefaultsForAction(e){return this.disabledDefaultShortcutsForAction.get(e)}addShortcutToSetting(e){const t=this.userShortcutsSetting.get();t.push(e),this.userShortcutsSetting.set(t)}removeShortcutFromSetting(e){const t=this.userShortcutsSetting.get(),i=t.findIndex(e.equals,e);-1!==i&&(t.splice(i,1),this.userShortcutsSetting.set(t))}registerShortcut(e){this.actionToShortcut.set(e.action,e),this.keyMap.addKeyMapping(e.descriptors.map((e=>e.key)),e.action)}registerBindings(){this.actionToShortcut.clear(),this.keyMap.clear();const e=this.keybindSetSetting.get();this.disabledDefaultShortcutsForAction.clear(),this.devToolsDefaultShortcutActions.clear();const t=[],i=this.userShortcutsSetting.get();for(const e of i){const i=Ke.createShortcutFromSettingObject(e);"DisabledDefault"===i.type?this.disabledDefaultShortcutsForAction.set(i.action,i):(zs.has(i.action)&&t.push(...i.descriptors.map((e=>Ke.keyCodeAndModifiersFromKey(e.key)))),this.registerShortcut(i))}for(const e of C()){const i=e.id(),s=e.bindings();for(let e=0;s&&e<s.length;++e){const r=s[e].keybindSets;if(!n(s[e].platform)||!o(r))continue;const a=s[e].shortcut.split(/\s+/).map(Ke.makeDescriptorFromBindingShortcut);if(a.length>0){if(this.isDisabledDefault(a,i)){this.devToolsDefaultShortcutActions.add(i);continue}zs.has(i)&&t.push(...a.map((e=>Ke.keyCodeAndModifiersFromKey(e.key)))),r?(r.includes("devToolsDefault")&&this.devToolsDefaultShortcutActions.add(i),this.registerShortcut(new Ke(a,i,"KeybindSetShortcut",new Set(r)))):(this.devToolsDefaultShortcutActions.add(i),this.registerShortcut(new Ke(a,i,"DefaultShortcut")))}}}function n(e){if(!e)return!0;const t=e.split(",");let i=!1;const n=s.Platform.platform();for(let e=0;!i&&e<t.length;++e)i=t[e]===n;return i}function o(t){return!t||t.includes(e)}s.InspectorFrontendHost.InspectorFrontendHostInstance.setWhitelistedShortcuts(JSON.stringify(t))}isDisabledDefault(e,t){const i=this.disabledDefaultShortcutsForAction.get(t);for(const t of i)if(t.descriptorsMatch(e))return!0;return!1}}class Os{keyInternal;actionsInternal;chordsInternal;depth;constructor(e,t=0){this.keyInternal=e,this.actionsInternal=[],this.chordsInternal=new Map,this.depth=t}addAction(e){this.actionsInternal.push(e)}key(){return this.keyInternal}chords(){return this.chordsInternal}hasChords(){return this.chordsInternal.size>0}addKeyMapping(e,t){if(!(e.length<this.depth))if(e.length===this.depth)this.addAction(t);else{const i=e[this.depth];this.chordsInternal.has(i)||this.chordsInternal.set(i,new Os(i,this.depth+1)),this.chordsInternal.get(i).addKeyMapping(e,t)}}getNode(e){return this.chordsInternal.get(e)||null}actions(){return this.actionsInternal}clear(){this.actionsInternal=[],this.chordsInternal=new Map}}class Fs{static instance=new Fs}const zs=new Set(["main.toggle-dock","debugger.toggle-breakpoints-active","debugger.toggle-pause","quick-open.show-command-menu","console.toggle"]),Ws=1e3,Hs="devToolsDefault";var Ns=Object.freeze({__proto__:null,ShortcutRegistry:Bs,ShortcutTreeNode:Os,ForwardedShortcut:Fs,ForwardedActions:zs,KeyTimeout:Ws,DefaultShortcutSetting:Hs}),_s={cssContent:".soft-context-menu{overflow-y:auto;min-width:160px!important;padding:4px 0;border:1px solid var(--sys-color-neutral-outline);border-radius:7px;background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow)}:host-context(.-theme-with-dark-background) .soft-context-menu{border:none}.dockside-title{padding-right:13px}.soft-context-menu-item{display:flex;width:100%;font-size:12px;padding:3px 7px 3px 8px;white-space:nowrap;align-items:center;&.soft-context-menu-item-mouse-over{background-color:var(--sys-color-state-hover-on-subtle)}& devtools-icon{width:16px;height:16px;pointer-events:none;&.checkmark{margin:-1px 5px -1px 0;opacity:0%;.soft-context-menu-item[checked] &{opacity:100%}}}}.soft-context-menu-disabled{color:var(--sys-color-state-disabled);pointer-events:none}.soft-context-menu-separator{height:10px;margin:0 1px;& > .separator-line{margin:0;height:5px;border-bottom:1px solid var(--sys-color-divider);pointer-events:none}}.soft-context-menu-item-submenu-arrow{pointer-events:none;font-size:11px;text-align:right;align-self:center;margin-left:auto}.soft-context-menu-custom-item{display:inline-flex;justify-content:center;align-items:center;flex:auto}.soft-context-menu-shortcut{color:var(--sys-color-token-subtle);pointer-events:none;flex:1 1 auto;text-align:right;padding-left:10px;.soft-context-menu-item-mouse-over &{color:inherit}}@media (forced-colors: active){.soft-context-menu-item{color:canvastext}.soft-context-menu-item.soft-context-menu-item-mouse-over,\n  .-theme-with-dark-background .soft-context-menu-item.soft-context-menu-item-mouse-over,\n  :host-context(.-theme-with-dark-background) .soft-context-menu-item.soft-context-menu-item-mouse-over{background-color:Highlight;color:HighlightText;forced-color-adjust:none}.soft-context-menu .soft-context-menu-item devtools-icon,\n  .soft-context-menu .soft-context-menu-item .soft-context-menu-shortcut{color:ButtonText}.soft-context-menu .soft-context-menu-item.soft-context-menu-item-mouse-over devtools-icon,\n  .soft-context-menu .soft-context-menu-item.soft-context-menu-item-mouse-over .soft-context-menu-shortcut{color:HighlightText}.soft-context-menu:focus-visible{forced-color-adjust:none;background:canvas;border-color:Highlight}.soft-context-menu-separator > .separator-line{border-bottom-color:ButtonText}}"};const js={checked:"checked",unchecked:"unchecked",sSS:"{PH1}, {PH2}, {PH3}",sS:"{PH1}, {PH2}"},Vs=t.i18n.registerUIStrings("ui/legacy/SoftContextMenu.ts",js),Us=t.i18n.getLocalizedString.bind(void 0,Vs);class Ks{items;itemSelectedCallback;parentMenu;highlightedMenuItemElement;detailsForElementMap;document;glassPane;contextMenuElement;focusRestorer;hideOnUserMouseDownUnlessInMenu;activeSubMenuElement;subMenu;onMenuClosed;focusOnTheFirstItem=!0;keepOpen;loggableParent;constructor(e,t,i,n,s,o){this.items=e,this.itemSelectedCallback=t,this.parentMenu=n,this.highlightedMenuItemElement=null,this.detailsForElementMap=new WeakMap,this.onMenuClosed=s,this.keepOpen=i,this.loggableParent=o||null}getItems(){return this.items}show(e,t){if(!this.items.length)return;this.document=e,this.glassPane=new Ts,this.glassPane.setPointerEventsBehavior(this.parentMenu?"PierceGlassPane":"BlockedByGlassPane"),this.glassPane.registerRequiredCSS(_s),this.glassPane.setContentAnchorBox(t),this.glassPane.setSizeBehavior("MeasureContent"),this.glassPane.setMarginBehavior("NoMargin"),this.glassPane.setAnchorBehavior(this.parentMenu?"PreferRight":"PreferBottom"),this.contextMenuElement=this.glassPane.contentElement.createChild("div","soft-context-menu"),this.contextMenuElement.setAttribute("jslog",`${o.menu().track({resize:!0}).parent("mapped")}`),this.loggableParent&&o.setMappedParent(this.contextMenuElement,this.loggableParent),this.contextMenuElement.tabIndex=-1,q(this.contextMenuElement),this.contextMenuElement.addEventListener("mouseup",(e=>e.consume()),!1),this.contextMenuElement.addEventListener("keydown",this.menuKeyDown.bind(this),!1);const i=!!this.items.find((e=>"checkbox"===e.type));for(let e=0;e<this.items.length;++e)this.contextMenuElement.appendChild(this.createMenuItem(this.items[e],i));if(this.glassPane.show(e),this.focusRestorer=new Gn(this.contextMenuElement),!this.parentMenu){this.hideOnUserMouseDownUnlessInMenu=e=>{let t=this.subMenu;for(;t;){if(t.contextMenuElement===e.composedPath()[0])return;t=t.subMenu}this.discard(),e.consume(!0)},this.document.body.addEventListener("mousedown",this.hideOnUserMouseDownUnlessInMenu,!1);const e=Hi.maybeGetInspectorViewInstance()?.element;if(e){let t=!1;const i=new ResizeObserver((()=>{if(t)return i.disconnect(),void this.discard();t=!0}));i.observe(e)}if(this.contextMenuElement.children&&this.focusOnTheFirstItem){const e=this.contextMenuElement.children[0];this.highlightMenuItem(e,!1)}}}setContextMenuElementLabel(e){this.contextMenuElement&&be(this.contextMenuElement,e)}discard(){this.subMenu&&this.subMenu.discard(),this.focusRestorer&&this.focusRestorer.restore(),this.glassPane&&(this.glassPane.hide(),delete this.glassPane,this.hideOnUserMouseDownUnlessInMenu&&(this.document&&this.document.body.removeEventListener("mousedown",this.hideOnUserMouseDownUnlessInMenu,!1),delete this.hideOnUserMouseDownUnlessInMenu)),this.parentMenu&&(delete this.parentMenu.subMenu,this.parentMenu.activeSubMenuElement&&(ae(this.parentMenu.activeSubMenuElement,!1),delete this.parentMenu.activeSubMenuElement)),this.onMenuClosed?.()}createMenuItem(e,t){if("separator"===e.type)return this.createSeparator();if("subMenu"===e.type)return this.createSubMenu(e,t);const i=document.createElement("div");if(i.classList.add("soft-context-menu-item"),i.tabIndex=-1,$(i),e.checked&&i.setAttribute("checked",""),void 0!==e.id&&i.setAttribute("data-action-id",e.id.toString()),t){const e=a.Icon.create("checkmark","checkmark");i.appendChild(e)}e.tooltip&&Jt.install(i,e.tooltip);const n={actionId:void 0,isSeparator:void 0,customElement:void 0,subItems:void 0,subMenuTimer:void 0};if(e.jslogContext&&!e.element?.hasAttribute("jslog")&&("checkbox"===e.type?i.setAttribute("jslog",`${o.toggle().track({click:!0}).context(e.jslogContext)}`):i.setAttribute("jslog",`${o.action().track({click:!0}).context(e.jslogContext)}`)),e.element&&!e.label){if(i.createChild("div","soft-context-menu-custom-item").appendChild(e.element),e.element?.classList.contains("location-menu")){const t=e.element.ariaLabel||"";e.element.ariaLabel="",be(i,t)}return n.customElement=e.element,this.detailsForElementMap.set(i,n),i}e.enabled||i.classList.add("soft-context-menu-disabled"),ss(i,e.label||""),e.element&&i.appendChild(e.element),i.createChild("span","soft-context-menu-shortcut").textContent=e.shortcut||"",i.addEventListener("mousedown",this.menuItemMouseDown.bind(this),!1),i.addEventListener("mouseup",this.menuItemMouseUp.bind(this),!1),i.addEventListener("mouseover",this.menuItemMouseOver.bind(this),!1),i.addEventListener("mouseleave",this.menuItemMouseLeave.bind(this),!1),n.actionId=e.id;let s=e.label||"";if("checkbox"===e.type){const t=e.checked?Us(js.checked):Us(js.unchecked);s=e.shortcut?Us(js.sSS,{PH1:String(e.label),PH2:e.shortcut,PH3:t}):Us(js.sS,{PH1:String(e.label),PH2:t})}else e.shortcut&&(s=Us(js.sS,{PH1:String(e.label),PH2:e.shortcut}));return be(i,s),this.detailsForElementMap.set(i,n),i}createSubMenu(e,t){const i=document.createElement("div");if(i.classList.add("soft-context-menu-item"),i.tabIndex=-1,X(i),this.detailsForElementMap.set(i,{subItems:e.subItems,actionId:void 0,isSeparator:void 0,customElement:void 0,subMenuTimer:void 0}),t){const e=a.Icon.create("checkmark","checkmark soft-context-menu-item-checkmark");i.appendChild(e)}if(ss(i,e.label||""),ae(i,!1),s.Platform.isMac()&&!m.ThemeSupport.instance().hasTheme()){const e=i.createChild("span","soft-context-menu-item-submenu-arrow");te(e),e.textContent="▶"}else{const e=a.Icon.create("triangle-right","soft-context-menu-item-submenu-arrow");i.appendChild(e)}return i.addEventListener("mousedown",this.menuItemMouseDown.bind(this),!1),i.addEventListener("mouseup",this.menuItemMouseUp.bind(this),!1),i.addEventListener("mouseover",this.menuItemMouseOver.bind(this),!1),i.addEventListener("mouseleave",this.menuItemMouseLeave.bind(this),!1),e.jslogContext&&i.setAttribute("jslog",`${o.item().context(e.jslogContext)}`),i}createSeparator(){const e=document.createElement("div");return e.classList.add("soft-context-menu-separator"),this.detailsForElementMap.set(e,{subItems:void 0,actionId:void 0,isSeparator:!0,customElement:void 0,subMenuTimer:void 0}),e.createChild("div","separator-line"),e}menuItemMouseDown(e){e.consume(!0)}menuItemMouseUp(e){this.triggerAction(e.target,e),e.consume()}root(){let e=this;for(;e.parentMenu;)e=e.parentMenu;return e}setChecked(e,t){e.checked=t;const i=this.contextMenuElement?.querySelector(`[data-action-id="${e.id}"]`);if(!i)return;t?i.setAttribute("checked",""):i.removeAttribute("checked");const n=e.checked?Us(js.checked):Us(js.unchecked);be(i,e.shortcut?Us(js.sSS,{PH1:String(e.label),PH2:e.shortcut,PH3:n}):Us(js.sS,{PH1:String(e.label),PH2:n}))}triggerAction(e,t){const i=this.detailsForElementMap.get(e);if(!i||i.subItems)return this.showSubMenu(e),void t.consume();if(this.keepOpen){t.consume(!0);const e=this.items.find((e=>e.id===i.actionId));void 0!==e?.id&&(this.setChecked(e,!e.checked),this.itemSelectedCallback(e.id))}else this.root().discard(),t.consume(!0),void 0!==i.actionId&&(this.itemSelectedCallback(i.actionId),delete i.actionId)}showSubMenu(e){const t=this.detailsForElementMap.get(e);if(!t)return;if(t.subMenuTimer&&(window.clearTimeout(t.subMenuTimer),delete t.subMenuTimer),this.subMenu||!this.document)return;if(this.activeSubMenuElement=e,ae(e,!0),!t.subItems)return;this.subMenu=new Ks(t.subItems,this.itemSelectedCallback,!1,this);const i=e.boxInWindow();i.y-=5,i.x+=3,i.width-=6,i.height+=10,this.subMenu.show(this.document,i)}menuItemMouseOver(e){this.highlightMenuItem(e.target,!0)}menuItemMouseLeave(e){if(!this.subMenu||!e.relatedTarget)return void this.highlightMenuItem(null,!0);e.relatedTarget===this.contextMenuElement&&this.highlightMenuItem(null,!0)}highlightMenuItem(e,t){if(this.highlightedMenuItemElement!==e){if(this.subMenu&&this.subMenu.discard(),this.highlightedMenuItemElement){const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);this.highlightedMenuItemElement.classList.remove("force-white-icons"),this.highlightedMenuItemElement.classList.remove("soft-context-menu-item-mouse-over"),e&&e.subItems&&e.subMenuTimer&&(window.clearTimeout(e.subMenuTimer),delete e.subMenuTimer)}if(this.highlightedMenuItemElement=e,this.highlightedMenuItemElement){this.highlightedMenuItemElement.classList.add("force-white-icons"),this.highlightedMenuItemElement.classList.add("soft-context-menu-item-mouse-over");const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&e.customElement&&!e.customElement.classList.contains("location-menu")?e.customElement.focus():this.highlightedMenuItemElement.focus(),t&&e&&e.subItems&&!e.subMenuTimer&&(e.subMenuTimer=window.setTimeout(this.showSubMenu.bind(this,this.highlightedMenuItemElement),150))}this.contextMenuElement&&ve(this.contextMenuElement,e)}}highlightPrevious(){let e=this.highlightedMenuItemElement?this.highlightedMenuItemElement.previousSibling:this.contextMenuElement?this.contextMenuElement.lastChild:null,t=e?this.detailsForElementMap.get(e):void 0;for(;e&&t&&(t.isSeparator||e.classList.contains("soft-context-menu-disabled"));)e=e.previousSibling,t=e?this.detailsForElementMap.get(e):void 0;e&&this.highlightMenuItem(e,!1)}highlightNext(){let e=this.highlightedMenuItemElement?this.highlightedMenuItemElement.nextSibling:this.contextMenuElement?this.contextMenuElement.firstChild:null,t=e?this.detailsForElementMap.get(e):void 0;for(;e&&(t&&t.isSeparator||e.classList.contains("soft-context-menu-disabled"));)e=e.nextSibling,t=e?this.detailsForElementMap.get(e):void 0;e&&this.highlightMenuItem(e,!1)}menuKeyDown(e){const t=e;function i(){if(!this.highlightedMenuItemElement)return;const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&!e.customElement&&(this.triggerAction(this.highlightedMenuItemElement,t),e.subItems&&this.subMenu&&this.subMenu.highlightNext(),t.consume(!0))}switch(t.key){case"ArrowUp":this.highlightPrevious(),t.consume(!0);break;case"ArrowDown":this.highlightNext(),t.consume(!0);break;case"ArrowLeft":this.parentMenu&&(this.highlightMenuItem(null,!1),this.discard()),t.consume(!0);break;case"ArrowRight":{if(!this.highlightedMenuItemElement)break;const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&e.subItems&&(this.showSubMenu(this.highlightedMenuItemElement),this.subMenu&&this.subMenu.highlightNext()),e?.customElement?.classList.contains("location-menu")&&(e.customElement.dispatchEvent(new KeyboardEvent("keydown",{key:"ArrowRight"})),this.highlightMenuItem(null,!0)),t.consume(!0);break}case"Escape":this.discard(),t.consume(!0);break;case"Enter":if("Enter"!==t.key)return;i.call(this);break;case" ":i.call(this);break;default:t.consume(!0)}}markAsMenuItemCheckBox(){if(this.contextMenuElement)for(const e of this.contextMenuElement.children)"soft-context-menu-separator"!==e.className&&G(e)}setFocusOnTheFirstItem(e){this.focusOnTheFirstItem=e}}var qs=Object.freeze({__proto__:null,SoftContextMenu:Ks});class $s{typeInternal;label;disabled;checked;contextMenu;idInternal;customElement;shortcut;#s;jslogContext;constructor(e,t,i,n,s,o,r){this.typeInternal=t,this.label=i,this.disabled=n,this.checked=s,this.contextMenu=e,this.idInternal=void 0,this.#s=o,"item"!==t&&"checkbox"!==t||(this.idInternal=e?e.nextId():0),this.jslogContext=r}id(){if(void 0===this.idInternal)throw new Error("Tried to access a ContextMenu Item ID but none was set.");return this.idInternal}type(){return this.typeInternal}isEnabled(){return!this.disabled}setEnabled(e){this.disabled=!e}buildDescriptor(){switch(this.typeInternal){case"item":{const e={type:"item",id:this.idInternal,label:this.label,enabled:!this.disabled,checked:void 0,subItems:void 0,tooltip:this.#s,jslogContext:this.jslogContext};return this.customElement&&(e.element=this.customElement),this.shortcut&&(e.shortcut=this.shortcut),e}case"separator":return{type:"separator",id:void 0,label:void 0,enabled:void 0,checked:void 0,subItems:void 0};case"checkbox":{const e={type:"checkbox",id:this.idInternal,label:this.label,checked:Boolean(this.checked),enabled:!this.disabled,subItems:void 0,tooltip:this.#s,jslogContext:this.jslogContext};return this.customElement&&(e.element=this.customElement),e}}throw new Error("Invalid item type:"+this.typeInternal)}setShortcut(e){this.shortcut=e}}class Gs{contextMenu;items;constructor(e){this.contextMenu=e,this.items=[]}appendItem(e,t,i){const n=new $s(this.contextMenu,"item",e,i?.disabled,void 0,i?.tooltip,i?.jslogContext);return i?.additionalElement&&(n.customElement=i?.additionalElement),this.items.push(n),this.contextMenu&&this.contextMenu.setHandler(n.id(),t),n}appendCustomItem(e,t){const i=new $s(this.contextMenu,"item",void 0,void 0,void 0,void 0,t);return i.customElement=e,this.items.push(i),i}appendSeparator(){const e=new $s(this.contextMenu,"separator");return this.items.push(e),e}appendAction(e,t,i){if(i&&!M.instance().hasAction(e))return;const n=M.instance().getAction(e);t||(t=n.title());const s=this.appendItem(t,n.execute.bind(n),{disabled:!n.enabled(),jslogContext:e}),o=Bs.instance().shortcutTitleForAction(e);o&&s.setShortcut(o)}appendSubMenuItem(e,t,i){const n=new Xs(this.contextMenu,e,t,i);return n.init(),this.items.push(n),n}appendCheckboxItem(e,t,i){const n=new $s(this.contextMenu,"checkbox",e,i?.disabled,i?.checked,i?.tooltip,i?.jslogContext);return this.items.push(n),this.contextMenu&&this.contextMenu.setHandler(n.id(),t),i?.additionalElement&&(n.customElement=i?.additionalElement),n}}class Xs extends $s{sections;sectionList;constructor(e,t,i,n){super(e,"subMenu",t,i,void 0,void 0,n),this.sections=new Map,this.sectionList=[]}init(){Ys.groupWeights.forEach((e=>this.section(e)))}section(e){let t=e?this.sections.get(e):null;return t||(t=new Gs(this.contextMenu),e?(this.sections.set(e,t),this.sectionList.push(t)):this.sectionList.splice(Ys.groupWeights.indexOf("default"),0,t)),t}headerSection(){return this.section("header")}newSection(){return this.section("new")}revealSection(){return this.section("reveal")}clipboardSection(){return this.section("clipboard")}editSection(){return this.section("edit")}debugSection(){return this.section("debug")}viewSection(){return this.section("view")}defaultSection(){return this.section("default")}overrideSection(){return this.section("override")}saveSection(){return this.section("save")}footerSection(){return this.section("footer")}buildDescriptor(){const e={type:"subMenu",label:this.label,enabled:!this.disabled,subItems:[],id:void 0,checked:void 0,jslogContext:this.jslogContext},t=this.sectionList.filter((e=>Boolean(e.items.length)));for(const i of t){for(const t of i.items)e.subItems||(e.subItems=[]),e.subItems.push(t.buildDescriptor());i!==t[t.length-1]&&(e.subItems||(e.subItems=[]),e.subItems.push({type:"separator",id:void 0,subItems:void 0,checked:void 0,enabled:void 0,label:void 0}))}return e}appendItemsAtLocation(e){const t=Zs;t.sort(((e,t)=>(e.order||0)-(t.order||0)));for(const i of t){if(i.experiment&&!n.Runtime.experiments.isEnabled(i.experiment))continue;const t=i.location,s=i.actionId;if(!t||!t.startsWith(e+"/"))continue;const o=t.substr(e.length+1);o&&!o.includes("/")&&(s&&this.section(o).appendAction(s))}}static uniqueSectionName=0}class Ys extends Xs{contextMenu;pendingTargets;event;useSoftMenu;keepOpen;x;y;onSoftMenuClosed;jsLogContext;handlers;idInternal;softMenu;contextMenuLabel;openHostedMenu;eventTarget;loggableParent=null;constructor(e,t={}){super(null);const i=e;this.contextMenu=this,super.init(),this.pendingTargets=[],this.event=i,this.eventTarget=this.event.target,this.useSoftMenu=Boolean(t.useSoftMenu),this.keepOpen=Boolean(t.keepOpen),this.x=void 0===t.x?i.x:t.x,this.y=void 0===t.y?i.y:t.y,this.onSoftMenuClosed=t.onSoftMenuClosed,this.handlers=new Map,this.idInternal=0,this.openHostedMenu=null;let n=Is(e)||e.target;if(n){for(this.appendApplicableItems(n);n instanceof Element&&!n.hasAttribute("jslog");)n=n.parentElementOrShadowHost()??null;this.loggableParent=n}}static initialize(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.SetUseSoftMenu,(function(e){Ys.useSoftMenu=e.data}))}static installHandler(e){e.body.addEventListener("contextmenu",(function(e){new Ys(e).show()}),!1)}nextId(){return this.idInternal++}isHostedMenuOpen(){return Boolean(this.openHostedMenu)}getItems(){return this.softMenu?.getItems()||[]}setChecked(e,t){this.softMenu?.setChecked(e,t)}async show(){Ys.pendingMenu=this,this.event.consume(!0);const e=await Promise.all(this.pendingTargets.map((async e=>{const t=await async function(e){const t=[];for(const i of Qs)if(n.Runtime.Runtime.isDescriptorEnabled({experiment:i.experiment,condition:void 0})&&i.contextTypes)for(const n of i.contextTypes())e instanceof n&&t.push(await i.loadProvider());return t}(e);return{target:e,providers:t}})));if(Ys.pendingMenu===this){Ys.pendingMenu=null;for(const{target:t,providers:i}of e)for(const e of i)e.appendApplicableItems(this.event,this,t);this.pendingTargets=[],this.innerShow()}}discard(){this.softMenu&&this.softMenu.discard()}registerLoggablesWithin(e,t){for(const i of e)i.jslogContext&&("checkbox"===i.type?o.registerLoggable(i,`${o.toggle().track({click:!0}).context(i.jslogContext)}`,t||e):"item"===i.type?o.registerLoggable(i,`${o.action().track({click:!0}).context(i.jslogContext)}`,t||e):"subMenu"===i.type&&o.registerLoggable(i,`${o.item().context(i.jslogContext)}`,t||e),i.subItems&&this.registerLoggablesWithin(i.subItems,i))}innerShow(){const e=this.buildMenuDescriptors();if(!this.eventTarget)return;const t=this.eventTarget.ownerDocument;if(this.useSoftMenu||Ys.useSoftMenu||s.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()){this.softMenu=new Ks(e,this.itemSelected.bind(this),this.keepOpen,void 0,this.onSoftMenuClosed,this.loggableParent);const i="mouse"===this.event.pointerType&&this.event.button>=0;this.softMenu.setFocusOnTheFirstItem(!i),this.softMenu.show(t,new AnchorBox(this.x,this.y,0,0)),this.contextMenuLabel&&this.softMenu.setContextMenuElementLabel(this.contextMenuLabel)}else{function n(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.ContextMenuCleared,this.menuCleared,this),s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.ContextMenuItemSelected,this.onItemSelected,this)}s.InspectorFrontendHost.InspectorFrontendHostInstance.showContextMenuAtPoint(this.x,this.y,e,t),o.registerLoggable(e,`${o.menu()}`,this.loggableParent),this.registerLoggablesWithin(e),this.openHostedMenu=e,queueMicrotask(n.bind(this))}}setContextMenuLabel(e){this.contextMenuLabel=e}setX(e){this.x=e}setY(e){this.y=e}setHandler(e,t){t&&this.handlers.set(e,t)}buildMenuDescriptors(){return super.buildDescriptor().subItems}onItemSelected(e){this.itemSelected(e.data)}itemSelected(e){const t=this.handlers.get(e);if(t&&t.call(this),this.openHostedMenu){const t=(e,i)=>{for(const n of e){if(n.id===i)return n;const e=n.subItems&&t(n.subItems,i);if(e)return e}return null},i=t(this.openHostedMenu,e);i&&i.jslogContext&&o.logClick(i,new MouseEvent("click"))}this.menuCleared()}menuCleared(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.ContextMenuCleared,this.menuCleared,this),s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.ContextMenuItemSelected,this.onItemSelected,this),this.openHostedMenu&&o.logResize(this.openHostedMenu,new DOMRect(0,0,0,0)),this.openHostedMenu=null,this.keepOpen||this.onSoftMenuClosed?.()}appendApplicableItems(e){this.pendingTargets.includes(e)||this.pendingTargets.push(e)}markAsMenuItemCheckBox(){this.softMenu&&this.softMenu.markAsMenuItemCheckBox()}static pendingMenu=null;static useSoftMenu=!1;static groupWeights=["header","new","reveal","edit","clipboard","debug","view","default","override","save","footer"]}const Qs=[];const Zs=[];var Js=Object.freeze({__proto__:null,Item:$s,Section:Gs,SubMenu:Xs,ContextMenu:Ys,registerProvider:function(e){Qs.push(e)},registerItem:function(e){Zs.push(e)},maybeRemoveItem:function(e){const t=Zs.findIndex((t=>t.actionId===e.actionId&&t.location===e.location));return!(t<0)&&(Zs.splice(t,1),!0)}}),eo={cssContent:":host{position:absolute;top:0;bottom:0;left:0;right:0;display:flex;background-color:var(--color-background-opacity-80);z-index:1000}.drop-target-message{flex:auto;font-size:30px;color:var(--sys-color-token-subtle);display:flex;justify-content:center;align-items:center;margin:20px;border:4px dashed var(--sys-color-neutral-outline);pointer-events:none}"};var to=Object.freeze({__proto__:null,DropTarget:class{element;transferTypes;messageText;handleDrop;enabled;dragMaskElement;constructor(e,t,i,n){e.addEventListener("dragenter",this.onDragEnter.bind(this),!0),e.addEventListener("dragover",this.onDragOver.bind(this),!0),this.element=e,this.transferTypes=t,this.messageText=i,this.handleDrop=n,this.enabled=!0,this.dragMaskElement=null}setEnabled(e){this.enabled=e}onDragEnter(e){this.enabled&&this.hasMatchingType(e)&&e.consume(!0)}hasMatchingType(e){const t=e;if(!t.dataTransfer)return!1;for(const e of this.transferTypes){if(Array.from(t.dataTransfer.items).find((t=>e.kind===t.kind&&Boolean(e.type.exec(t.type)))))return!0}return!1}onDragOver(e){const t=e;if(!this.enabled||!this.hasMatchingType(t))return;if(t.dataTransfer&&(t.dataTransfer.dropEffect="copy"),t.consume(!0),this.dragMaskElement)return;this.dragMaskElement=this.element.createChild("div","");d.createShadowRootWithCoreStyles(this.dragMaskElement,{cssFile:eo,delegatesFocus:void 0}).createChild("div","drop-target-message").textContent=this.messageText,this.dragMaskElement.addEventListener("drop",this.onDrop.bind(this),!0),this.dragMaskElement.addEventListener("dragleave",this.onDragLeave.bind(this),!0)}onDrop(e){const t=e;t.consume(!0),this.removeMask(),this.enabled&&t.dataTransfer&&this.handleDrop(t.dataTransfer)}onDragLeave(e){e.consume(!0),this.removeMask()}removeMask(){this.dragMaskElement&&(this.dragMaskElement.remove(),this.dragMaskElement=null)}},Type:{URI:{kind:"string",type:/text\/uri-list/},Folder:{kind:"file",type:/$^/},File:{kind:"file",type:/.*/},WebFile:{kind:"file",type:/[\w]+/},ImageFile:{kind:"file",type:/image\/.*/}}}),io={cssContent:".empty-bold-text{display:block;font-size:1.5em;margin:0.83em 0;font-weight:bold}.empty-view{color:var(--sys-color-token-subtle);padding:30px;text-align:center;min-width:70px}.empty-view-scroller{justify-content:center;overflow:auto}.empty-view p{white-space:initial;line-height:18px;max-width:300px;flex-shrink:0}"};function no(e){return e.data}function so(e,t){e.data=t}class oo{elementInternal;elementsById;constructor(e){this.elementInternal=e,this.elementsById=new Map}element(){return this.elementInternal}$(e){return this.elementsById.get(e)}static build(e,...t){return oo.render(oo.template(e),t)}static cached(e,...t){let i=uo.get(e);return i||(i=oo.template(e),uo.set(e,i)),oo.render(i,t)}static template(e){let t="",i=!0;for(let n=0;n<e.length-1;n++){t+=e[n];const s=e[n].lastIndexOf(">"),o=e[n].indexOf("<",s+1);-1!==s&&-1===o?i=!0:-1!==o&&(i=!1),t+=i?ro:lo(n)}t+=e[e.length-1];const n=document.createElement("template");n.innerHTML=t;const s=n.ownerDocument.createTreeWalker(n.content,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,null);let o=0;const r=[],a=[],l=[];for(;s.nextNode();){const e=s.currentNode;if(e.nodeType===Node.ELEMENT_NODE&&e.hasAttributes()){e.hasAttribute("$")&&(l.push(e),a.push({replaceNodeIndex:void 0,attr:void 0,elementId:e.getAttribute("$")||""}),e.removeAttribute("$"));const t=[];for(let i=0;i<e.attributes.length;i++){const n=e.attributes[i].name;if(!ho.test(n)&&!ho.test(e.attributes[i].value))continue;t.push(n),l.push(e);const s={index:o,names:n.split(ho),values:e.attributes[i].value.split(ho)};o+=s.names.length-1,o+=s.values.length-1;const r={elementId:void 0,replaceNodeIndex:void 0,attr:s};a.push(r)}for(let i=0;i<t.length;i++)e.removeAttribute(t[i])}if(e.nodeType===Node.TEXT_NODE&&-1!==no(e).indexOf(ro)){const t=no(e).split(ao);so(e,t[t.length-1]);const i=e.parentNode;for(let n=0;n<t.length-1;n++){t[n]&&i.insertBefore(document.createTextNode(t[n]),e);const s=document.createElement("span");l.push(s),a.push({attr:void 0,elementId:void 0,replaceNodeIndex:o++}),i.insertBefore(s,e)}}e.nodeType!==Node.TEXT_NODE||e.previousSibling&&e.previousSibling.nodeType!==Node.ELEMENT_NODE||e.nextSibling&&e.nextSibling.nodeType!==Node.ELEMENT_NODE||!/^\s*$/.test(no(e))||r.push(e)}for(let e=0;e<l.length;e++)l[e].classList.add(co(e));for(const e of r)e.remove();return{template:n,binds:a}}static render(e,t){const i=e.template.ownerDocument.importNode(e.template.content,!0),n=i.firstChild===i.lastChild?i.firstChild:i,s=new oo(n),o=[];for(let t=0;t<e.binds.length;t++){const e=co(t),n=i.querySelector("."+e);n.classList.remove(e),o.push(n)}for(let i=0;i<e.binds.length;i++){const n=e.binds[i],r=o[i];if(void 0!==n.elementId)s.elementsById.set(n.elementId,r);else if(void 0!==n.replaceNodeIndex){const e=t[n.replaceNodeIndex];r.parentNode.replaceChild(this.nodeForValue(e),r)}else{if(void 0===n.attr)throw new Error("Unexpected bind");if(2===n.attr.names.length&&1===n.attr.values.length&&"function"==typeof t[n.attr.index])t[n.attr.index].call(null,r);else{let e=n.attr.names[0];for(let i=1;i<n.attr.names.length;i++)e+=t[n.attr.index+i-1],e+=n.attr.names[i];if(e){let i=n.attr.values[0];for(let e=1;e<n.attr.values.length;e++)i+=t[n.attr.index+n.attr.names.length-1+e-1],i+=n.attr.values[e];r.setAttribute(e,i)}}}}return s}static nodeForValue(e){if(e instanceof Node)return e;if(e instanceof oo)return e.elementInternal;if(Array.isArray(e)){const t=document.createDocumentFragment();for(const i of e)t.appendChild(this.nodeForValue(i));return t}return document.createTextNode(String(e))}}const ro="{{template-text}}",ao=/{{template-text}}/,lo=e=>"template-attribute"+e,ho=/template-attribute\d+/,co=e=>"template-class-"+e,uo=new Map,mo=(e,...t)=>oo.cached(e,...t).element();var po=Object.freeze({__proto__:null,Fragment:oo,textMarker:ro,attributeMarker:lo,html:mo});class go extends Lt{hrefInternal;clickable;onClick;onKeyDown;static create(e,t,n,s,r){t||(t=e);return mo`
  <x-link href='${e}' tabindex="0" class='${n=n||""} devtools-link' ${s?"no-click":""}
  jslog=${o.link().track({click:!0}).context(r)}>${i.StringUtilities.trimMiddle(t,150)}</x-link>`}constructor(){super(),this.style.setProperty("display","inline"),H(this),this.setAttribute("tabindex","0"),this.setAttribute("target","_blank"),this.setAttribute("rel","noopener"),this.hrefInternal=null,this.clickable=!0,this.onClick=e=>{e.consume(!0),this.hrefInternal&&s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.hrefInternal),this.dispatchEvent(new Event("x-link-invoke"))},this.onKeyDown=e=>{i.KeyboardUtilities.isEnterOrSpaceKey(e)&&(e.consume(!0),this.hrefInternal&&s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.hrefInternal)),this.dispatchEvent(new Event("x-link-invoke"))}}static get observedAttributes(){return Lt.observedAttributes.concat(["href","no-click","title"])}get href(){return this.hrefInternal}attributeChangedCallback(e,t,i){if("no-click"===e)return this.clickable=!i,void this.updateClick();if("href"===e){i||(i="");let e=null,t=null;try{t=new URL(ws(i)),e=t.toString()}catch{}return t&&"javascript:"===t.protocol&&(e=null),this.hrefInternal=e,this.hasAttribute("title")||Jt.install(this,i),void this.updateClick()}super.attributeChangedCallback(e,t,i)}updateClick(){null!==this.hrefInternal&&this.clickable?(this.addEventListener("click",this.onClick,!1),this.addEventListener("keydown",this.onKeyDown,!1),this.style.setProperty("cursor","pointer")):(this.removeEventListener("click",this.onClick,!1),this.removeEventListener("keydown",this.onKeyDown,!1),this.style.removeProperty("cursor"))}}customElements.define("x-link",go);const bo=p.html`<p>Hello, <x-link>world!</x-link></p>`;var fo=Object.freeze({__proto__:null,XLink:go,ContextMenuProvider:class{appendApplicableItems(e,t,i){let n=i;for(;n&&!(n instanceof go);)n=n.parentNodeOrShadowHost();if(!n||!n.href)return;const o=n;t.revealSection().appendItem(Un(),(()=>{o.href&&s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(o.href)}),{jslogContext:"open-in-new-tab"}),t.revealSection().appendItem(Kn(),(()=>{o.href&&s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(o.href)}),{jslogContext:"copy-link-address"})}},sample:bo});const vo={learnMore:"Learn more"},wo=t.i18n.registerUIStrings("ui/legacy/EmptyWidget.ts",vo),xo=t.i18n.getLocalizedString.bind(void 0,wo);var Eo=Object.freeze({__proto__:null,EmptyWidget:class extends Nt{textElement;constructor(e){super(),this.registerRequiredCSS(io),this.element.classList.add("empty-view-scroller"),this.contentElement=this.element.createChild("div","empty-view"),this.contentElement.setAttribute("jslog",`${o.section("empty-view")}`),this.textElement=this.contentElement.createChild("div","empty-bold-text"),this.textElement.textContent=e}appendParagraph(){return this.contentElement.createChild("p")}appendLink(e){const t=go.create(e,xo(vo.learnMore),void 0,void 0,"learn-more");return this.contentElement.appendChild(t)}set text(e){this.textElement.textContent=e}}}),Io={cssContent:'.filter-bar{background-color:var(--sys-color-cdt-base-container);flex:none;flex-wrap:wrap;align-items:center;border-bottom:1px solid var(--sys-color-divider);color:var(--sys-color-on-surface-subtle)}.filter-text-filter{display:inline-flex;margin-left:1px;margin-right:2px;min-width:40px;max-width:200px;height:24px;align-items:center}.filter-bitset-filter{padding:2px;display:inline-flex;overflow:hidden;height:24px;position:relative;margin:0}.filter-bitset-filter span{color:var(--sys-color-on-surface);outline:1px solid var(--sys-color-neutral-outline);outline-offset:-1px;box-sizing:border-box;display:inline-block;flex:none;margin:auto 2px;padding:3px 6px;background:transparent;border-radius:6px;overflow:hidden;cursor:pointer;font-weight:500;font-size:11px}.filter-bitset-filter span:focus-visible{outline:-webkit-focus-ring-color auto 5px}.filter-bitset-filter span:hover{outline:none;background:var(--sys-color-state-hover-on-subtle)}.filter-bitset-filter span.selected,\n.filter-bitset-filter span:active{color:var(--sys-color-on-tonal-container);outline:none;background-color:var(--sys-color-tonal-container)}.filter-bitset-filter-divider{background-color:var(--sys-color-divider);height:16px;width:1px;margin:auto 2px;display:inline-block}.filter-checkbox-filter{padding-left:4px;padding-right:7px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;display:inline-flex;vertical-align:middle;height:24px;position:relative}.filter-checkbox-filter > [is="dt-checkbox"]{display:flex;margin:auto 0}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.filter-divider{background-color:var(--sys-color-on-base-divider);width:1px;margin:5px 4px;height:16px}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0}.toolbar-button .active-filters-count{margin-right:5px;--override-adorner-background-color:var(--sys-color-tonal-container);--override-adorner-border-color:var(--sys-color-tonal-container);--override-adorner-text-color:var(--sys-color-primary);--override-adorner-font-size:10px;font-weight:700}.toolbar-text{margin:0 4px 0 0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.dropdown-filterbar{justify-content:space-between;padding:0 3px 0 5px;border:1px solid transparent;border-radius:7px;display:flex;background-color:transparent;color:var(--sys-color-on-surface-subtle)}.filter-input-field{padding-left:3px;width:163px;height:18px;line-height:20px;display:inline-block;overflow:hidden;white-space:nowrap;cursor:auto}.filter-input-container{margin:0 3px;display:flex;background:var(--sys-color-cdt-base-container);border:1px solid var(--sys-color-neutral-outline);border-radius:4px;width:163px;&:focus-within{border-color:var(--sys-color-state-focus-ring)}&:hover:not(:focus-within){background:var(--sys-color-state-hover-on-subtle)}}.filter-text-filter:not(.filter-text-empty) .filter-input-container{border-color:var(--sys-color-state-focus-ring)}.filter-input-clear-button{opacity:70%;height:16px;margin:0;cursor:pointer;border:none;background:none;padding:1px 2px;devtools-icon{display:block}&:hover{opacity:99%}}.filter-text-empty .filter-input-clear-button{display:none}button.toolbar-item:focus-visible{background:var(--sys-color-state-hover-on-subtle);border-radius:2px}@media (forced-colors: active){.filter-bitset-filter span:hover,\n  .filter-bitset-filter span.selected,\n  .filter-bitset-filter span:active{forced-color-adjust:none;background:Highlight;color:HighlightText}.filter-input-container{forced-color-adjust:none;background:ButtonFace;box-shadow:var(--legacy-focus-ring-inactive-shadow);color:fieldtext}.filter-input-container:hover,\n  .filter-input-container:focus-within,\n  .filter-text-filter:not(.filter-text-empty) .filter-input-container{box-shadow:var(--legacy-focus-ring-active-shadow)}}'};const yo={filter:"Filter",egSmalldUrlacomb:"e.g. `/small[d]+/ url:a.com/b`",sclickToSelectMultipleTypes:"{PH1}Click to select multiple types",allStrings:"All",clearFilter:"Clear input"},So=t.i18n.registerUIStrings("ui/legacy/FilterBar.ts",yo),Co=t.i18n.getLocalizedString.bind(void 0,So);class To extends(e.ObjectWrapper.eventMixin(_t)){enabled;stateSetting;filterButtonInternal;filters;alwaysShowFilters;showingWidget;constructor(t,i){super(),this.registerRequiredCSS(Io),this.enabled=!0,this.element.classList.add("filter-bar"),this.element.setAttribute("jslog",`${o.toolbar("filter-bar")}`),this.stateSetting=e.Settings.Settings.instance().createSetting("filter-bar-"+t+"-toggled",Boolean(i)),this.filterButtonInternal=new In(this.stateSetting,"filter",Co(yo.filter),"filter-filled","filter"),this.filters=[],this.updateFilterBar(),this.stateSetting.addChangeListener(this.updateFilterBar.bind(this))}filterButton(){return this.filterButtonInternal}addDivider(){const e=document.createElement("div");e.classList.add("filter-divider"),this.element.appendChild(e)}addFilter(e){this.filters.push(e),this.element.appendChild(e.element()),e.addEventListener("FilterChanged",this.filterChanged,this),this.updateFilterButton()}setEnabled(e){this.enabled=e,this.filterButtonInternal.setEnabled(e),this.updateFilterBar()}forceShowFilterBar(){this.alwaysShowFilters=!0,this.updateFilterBar()}showOnce(){this.stateSetting.set(!0)}filterChanged(){this.updateFilterButton(),this.dispatchEventToListeners("Changed")}wasShown(){super.wasShown(),this.updateFilterBar()}updateFilterBar(){this.parentWidget()&&!this.showingWidget&&(this.visible()?(this.showingWidget=!0,this.showWidget(),this.showingWidget=!1):this.hideWidget())}focus(){for(let e=0;e<this.filters.length;++e)if(this.filters[e]instanceof ko){this.filters[e].focus();break}}updateFilterButton(){let e=!1;for(const t of this.filters)e=e||t.isActive();this.filterButtonInternal.setDefaultWithRedColor(e),this.filterButtonInternal.setToggleWithRedColor(e)}clear(){this.element.removeChildren(),this.filters=[],this.updateFilterButton()}setting(){return this.stateSetting}visible(){return this.alwaysShowFilters||this.stateSetting.get()&&this.enabled}}class ko extends e.ObjectWrapper.ObjectWrapper{filterElement;filterInputElement;prompt;proxyElement;suggestionProvider;constructor(){super(),this.filterElement=document.createElement("div"),this.filterElement.className="filter-text-filter";const e=this.filterElement.createChild("div","filter-input-container");this.filterInputElement=e.createChild("span","filter-input-field"),this.prompt=new ln,this.prompt.initialize(this.completions.bind(this)," ",!0),this.proxyElement=this.prompt.attach(this.filterInputElement),Jt.install(this.proxyElement,Co(yo.egSmalldUrlacomb)),this.prompt.setPlaceholder(Co(yo.filter)),this.prompt.addEventListener("TextChanged",this.valueChanged.bind(this)),this.suggestionProvider=null;const t=e.createChild("button","filter-input-clear-button");Jt.install(t,Co(yo.clearFilter));const i=new a.Icon.Icon;i.data={color:"var(--icon-default)",width:"16px",height:"16px",iconName:"cross-circle-filled"},t.appendChild(i),t.addEventListener("click",(()=>{this.clear(),this.focus()})),t.setAttribute("jslog",`${o.action("clear-filter").track({click:!0})}`),this.updateEmptyStyles()}completions(e,t,i){return this.suggestionProvider?this.suggestionProvider(e,t,i):Promise.resolve([])}isActive(){return Boolean(this.prompt.text())}element(){return this.filterElement}value(){return this.prompt.text()}setValue(e){this.prompt.setText(e),this.valueChanged()}focus(){this.filterInputElement.focus()}setSuggestionProvider(e){this.prompt.clearAutocomplete(),this.suggestionProvider=e}valueChanged(){this.dispatchEventToListeners("FilterChanged"),this.updateEmptyStyles()}updateEmptyStyles(){this.filterElement.classList.toggle("filter-text-empty",!this.prompt.text())}clear(){this.setValue("")}}class Mo extends e.ObjectWrapper.ObjectWrapper{filtersElement;typeFilterElementTypeNames;allowedTypes;typeFilterElements;setting;constructor(e,t){super(),this.filtersElement=document.createElement("div"),this.filtersElement.classList.add("filter-bitset-filter"),this.filtersElement.setAttribute("jslog",`${o.section("filter-bitset")}`),Z(this.filtersElement),J(this.filtersElement),Jt.install(this.filtersElement,Co(yo.sclickToSelectMultipleTypes,{PH1:Ke.shortcutToString("",qe.CtrlOrMeta)})),this.typeFilterElementTypeNames=new WeakMap,this.allowedTypes=new Set,this.typeFilterElements=[],this.addBit(Mo.ALL_TYPES,Co(yo.allStrings)),this.typeFilterElements[0].tabIndex=0,this.filtersElement.createChild("div","filter-bitset-filter-divider");for(let t=0;t<e.length;++t)this.addBit(e[t].name,e[t].label(),e[t].title);t?(this.setting=t,t.addChangeListener(this.settingChanged.bind(this)),this.settingChanged()):this.toggleTypeFilter(Mo.ALL_TYPES,!1)}reset(){this.toggleTypeFilter(Mo.ALL_TYPES,!1)}isActive(){return!this.allowedTypes.has(Mo.ALL_TYPES)}element(){return this.filtersElement}accept(e){return this.allowedTypes.has(Mo.ALL_TYPES)||this.allowedTypes.has(e)}settingChanged(){const e=this.setting.get();this.allowedTypes=new Set;for(const t of this.typeFilterElements){const i=this.typeFilterElementTypeNames.get(t);i&&e[i]&&this.allowedTypes.add(i)}this.update()}update(){(0===this.allowedTypes.size||this.allowedTypes.has(Mo.ALL_TYPES))&&(this.allowedTypes=new Set,this.allowedTypes.add(Mo.ALL_TYPES));for(const e of this.typeFilterElements){const t=this.typeFilterElementTypeNames.get(e),i=this.allowedTypes.has(t||"");e.classList.toggle("selected",i),ue(e,i)}this.dispatchEventToListeners("FilterChanged")}addBit(e,t,i){const n=this.filtersElement.createChild("span",e);n.tabIndex=-1,this.typeFilterElementTypeNames.set(n,e),ss(n,t),ee(n),i&&(n.title=i),n.addEventListener("click",this.onTypeFilterClicked.bind(this),!1),n.addEventListener("keydown",this.onTypeFilterKeydown.bind(this),!1),n.setAttribute("jslog",`${o.item(e).track({click:!0})}`),this.typeFilterElements.push(n)}onTypeFilterClicked(e){const t=e;let i;if(i=s.Platform.isMac()?t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey:t.ctrlKey&&!t.metaKey&&!t.altKey&&!t.shiftKey,t.target){const e=t.target,n=this.typeFilterElementTypeNames.get(e);this.toggleTypeFilter(n,i)}}onTypeFilterKeydown(e){const t=e,n=t.target;n&&("ArrowLeft"===t.key||"ArrowUp"===t.key?this.keyFocusNextBit(n,!0)&&t.consume(!0):"ArrowRight"===t.key||"ArrowDown"===t.key?this.keyFocusNextBit(n,!1)&&t.consume(!0):i.KeyboardUtilities.isEnterOrSpaceKey(t)&&this.onTypeFilterClicked(t))}keyFocusNextBit(e,t){const i=this.typeFilterElements.indexOf(e);if(-1===i)return!1;const n=t?i-1:i+1;if(n<0||n>=this.typeFilterElements.length)return!1;const s=this.typeFilterElements[n];return s.tabIndex=0,e.tabIndex=-1,s.focus(),!0}toggleTypeFilter(e,t){if(t&&e!==Mo.ALL_TYPES?this.allowedTypes.delete(Mo.ALL_TYPES):this.allowedTypes=new Set,this.allowedTypes.has(e)?this.allowedTypes.delete(e):(this.allowedTypes.add(e),s.userMetrics.legacyResourceTypeFilterItemSelected(e)),0===this.allowedTypes.size&&this.allowedTypes.add(Mo.ALL_TYPES),s.userMetrics.legacyResourceTypeFilterNumberOfSelectedChanged(this.allowedTypes.size),this.setting){const e={};for(const t of this.allowedTypes)e[t]=!0;this.setting.set(e)}else this.update()}static ALL_TYPES="all"}class Lo extends e.ObjectWrapper.ObjectWrapper{filterElement;activeWhenChecked;label;checkboxElement;constructor(e,t,i,n,s){super(),this.filterElement=document.createElement("div"),this.filterElement.classList.add("filter-checkbox-filter"),this.activeWhenChecked=Boolean(i),this.label=ls.create(t),this.filterElement.appendChild(this.label),this.checkboxElement=this.label.checkboxElement,n?$i(this.checkboxElement,n):this.checkboxElement.checked=!0,this.checkboxElement.addEventListener("change",this.fireUpdated.bind(this),!1),s&&this.checkboxElement.setAttribute("jslog",`${o.toggle().track({change:!0}).context(s)}`)}isActive(){return this.activeWhenChecked===this.checkboxElement.checked}checked(){return this.checkboxElement.checked}setChecked(e){this.checkboxElement.checked=e}element(){return this.filterElement}labelElement(){return this.label}fireUpdated(){this.dispatchEventToListeners("FilterChanged")}}var Po=Object.freeze({__proto__:null,FilterBar:To,TextFilterUI:ko,NamedBitSetFilterUI:Mo,CheckboxFilterUI:Lo});var Do=Object.freeze({__proto__:null,FilterSuggestionBuilder:class{keys;valueSorter;valuesMap;constructor(e,t){this.keys=e,this.valueSorter=t||((e,t)=>t.sort()),this.valuesMap=new Map}completions(e,t,n){if(!t&&!n)return Promise.resolve([]);const s=t.startsWith("-");s&&(t=t.substring(1));const o=s?"-":"",r=t.indexOf(":"),a=[];if(-1===r){const e=new RegExp("^"+i.StringUtilities.escapeForRegExp(t),"i");for(const t of this.keys)e.test(t)&&a.push({text:o+t+":"})}else{const e=t.substring(0,r).toLowerCase(),n=t.substring(r+1),s=new RegExp("^"+i.StringUtilities.escapeForRegExp(n),"i"),l=Array.from(this.valuesMap.get(e)||new Set);this.valueSorter(e,l);for(const t of l)s.test(t)&&t!==n&&a.push({text:o+e+":"+t})}return Promise.resolve(a)}addItem(e,t){if(!t)return;let i=this.valuesMap.get(e);i||(i=new Set,this.valuesMap.set(e,i)),i.add(t)}clear(){this.valuesMap.clear()}}});class Ao{constructor(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.KeyEventUnhandled,this.onKeyEventUnhandled,this)}async onKeyEventUnhandled(e){const{type:t,key:i,keyCode:n,modifiers:s}=e.data;if("keydown"!==t)return;const o=b.instance(),r=Bs.instance();o.setFlavor(Fs,Fs.instance),await r.handleKey(Ke.makeKey(n,s),i),o.setFlavor(Fs,null)}}new Ao;var Ro=Object.freeze({__proto__:null,ForwardedInputEventHandler:Ao});let Bo=null;class Oo extends HTMLInputElement{history;historyPosition;constructor(){super(),this.history=[""],this.historyPosition=0,this.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.addEventListener("input",this.onInput.bind(this),!1)}static create(){return Bo||(Bo=d.registerCustomElement("input","history-input",Oo)),Bo()}onInput(e){this.history.length===this.historyPosition+1&&(this.history[this.history.length-1]=this.value)}onKeyDown(e){const t=e;t.keyCode===nt.Up.code?(this.historyPosition=Math.max(this.historyPosition-1,0),this.value=this.history[this.historyPosition],this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),t.consume(!0)):t.keyCode===nt.Down.code?(this.historyPosition=Math.min(this.historyPosition+1,this.history.length-1),this.value=this.history[this.historyPosition],this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),t.consume(!0)):t.keyCode===nt.Enter.code&&this.saveToHistory()}saveToHistory(){this.history.length>1&&this.history[this.history.length-2]===this.value||(this.history[this.history.length-1]=this.value,this.historyPosition=this.history.length-1,this.history.push(""))}}var Fo=Object.freeze({__proto__:null,HistoryInput:Oo});let zo=null;class Wo{focusRestorer;static startEditing(e,t){return zo||(zo=new Wo),zo.startEditing(e,t)}editorContent(e){const t=e.element;return"INPUT"===t.tagName&&"text"===t.type?t.value:t.textContent||""}setUpEditor(e){const t=e.element;t.classList.add("editing"),t.setAttribute("contenteditable","plaintext-only");const i=t.getAttribute("role");K(t),e.oldRole=i;const n=t.getAttribute("tabIndex");("number"!=typeof n||n<0)&&(t.tabIndex=0),this.focusRestorer=new Gn(t),e.oldTabIndex=n}closeEditor(e){const t=e.element;t.classList.remove("editing"),t.removeAttribute("contenteditable"),"string"!=typeof e.oldRole?t.removeAttribute("role"):t.setAttribute("role",e.oldRole),"number"!=typeof e.oldTabIndex?t.removeAttribute("tabIndex"):t.setAttribute("tabIndex",e.oldTabIndex),t.scrollTop=0,t.scrollLeft=0}cancelEditing(e){const t=e.element;"INPUT"===t.tagName&&"text"===t.type?t.value=e.oldText||"":t.textContent=e.oldText}startEditing(e,t){if(!Fn(e,!0))return null;const n=t||new Ho((function(){}),(function(){})),s={element:e,config:n,oldRole:null,oldTabIndex:null,oldText:null},o=n.commitHandler,r=n.cancelHandler,a=n.pasteHandler,l=n.context;let h="";const d=this;function c(t){n.blurHandler&&!n.blurHandler(e,t)||p.call(e)}function u(){Fn(e,!1),e.removeEventListener("blur",c,!1),e.removeEventListener("keydown",f,!0),a&&e.removeEventListener("paste",b,!0),d.focusRestorer&&d.focusRestorer.restore(),d.closeEditor(s)}function m(){d.cancelEditing(s),u(),r(this,l)}function p(){u(),o(this,d.editorContent(s),s.oldText||"",l,h)}function g(t,i){"commit"===t?(p.call(e),i.consume(!0)):"cancel"===t?(m.call(e),i.consume(!0)):t&&t.startsWith("move-")&&(h=t.substring(5),"Tab"===i.key&&i.consume(!0),c())}function b(e){if(!a)return;g(a(e),e)}function f(e){let t=function(e){return"Enter"===e.key?"commit":e.keyCode===nt.Esc.code||e.key===i.KeyboardUtilities.ESCAPE_KEY?"cancel":"Tab"===e.key?"move-"+(e.shiftKey?"backward":"forward"):""}(e);if(!t&&n.postKeydownFinishHandler){const i=n.postKeydownFinishHandler(e);i&&(t=i)}g(t,e)}this.setUpEditor(s),s.oldText=this.editorContent(s),e.addEventListener("blur",c,!1),e.addEventListener("keydown",f,!0),void 0!==a&&e.addEventListener("paste",b,!0);return{cancel:m.bind(e),commit:p.bind(e)}}}class Ho{commitHandler;cancelHandler;context;blurHandler;pasteHandler;postKeydownFinishHandler;constructor(e,t,i,n){this.commitHandler=e,this.cancelHandler=t,this.context=i,this.blurHandler=n}setPasteHandler(e){this.pasteHandler=e}setPostKeydownFinishHandler(e){this.postKeydownFinishHandler=e}}var No=Object.freeze({__proto__:null,InplaceEditor:Wo,Config:Ho}),_o={cssContent:".list{flex:auto 0 1;overflow-y:auto;border:1px solid var(--sys-color-divider);flex-direction:column;--override-background-list-item-color:hsl(0deg 0% 96%)}.-theme-with-dark-background .list,\n:host-context(.-theme-with-dark-background) .list{--override-background-list-item-color:hsl(0deg 0% 16%)}.list-separator{background:var(--sys-color-divider);height:1px}.list-item{flex:none;min-height:30px;display:flex;align-items:center;position:relative;overflow:hidden}.list-item:focus-within,\n.list-item:hover{background:var(--sys-color-state-hover-on-subtle)}.list-widget-input-validation-error{color:var(--sys-color-error);margin:0 5px}.controls-container{display:flex;flex-direction:row;justify-content:flex-end;align-items:stretch;pointer-events:none}.controls-gradient{flex:0 1 50px}.list-item:focus-within .controls-gradient,\n.list-item:hover .controls-gradient{background-image:linear-gradient(90deg,transparent,var(--override-background-list-item-color))}.controls-buttons{flex:none;display:flex;flex-direction:row;align-items:center;pointer-events:auto;visibility:hidden}.list-item:focus-within .controls-buttons,\n.list-item:hover .controls-buttons{background-color:var(--override-background-list-item-color);visibility:visible}.editor-container{display:flex;flex-direction:column;align-items:stretch;flex:none;background:var(--sys-color-surface3);overflow:hidden}.editor-content{flex:auto;display:flex;flex-direction:column;align-items:stretch}.editor-buttons{flex:none;display:flex;flex-direction:row;align-items:center;justify-content:flex-start;padding:5px}.editor-buttons > button{flex:none;margin-right:10px}.editor-content input{margin-right:10px}.editor-content input.error-input{background-color:var(--sys-color-cdt-base-container)}.text-prompt-container{padding:3px 6px;height:24px;border:none;box-shadow:var(--legacy-focus-ring-inactive-shadow);border-radius:2px;width:100%;background-color:var(--sys-color-cdt-base-container);&:focus{border:1px solid var(--sys-color-state-focus-ring)}& .text-prompt{width:100%}}@media (forced-colors: active){.list-item:focus-within .controls-buttons,\n  .list-item:hover .controls-buttons{background-color:canvas}.list-item:focus-within,\n  .list-item:hover{forced-color-adjust:none;background:Highlight}.list-item:focus-within *,\n  .list-item:hover *{color:HighlightText}.list-item:focus-within .controls-gradient,\n  .list-item:hover .controls-gradient{background-image:unset}}"};const jo={editString:"Edit",removeString:"Remove",saveString:"Save",addString:"Add",cancelString:"Cancel",changesSaved:"Changes to item have been saved",removedItem:"Item has been removed"},Vo=t.i18n.registerUIStrings("ui/legacy/ListWidget.ts",jo),Uo=t.i18n.getLocalizedString.bind(void 0,Vo);var Ko=Object.freeze({__proto__:null,ListWidget:class extends Nt{delegate;list;lastSeparator;focusRestorer;items;editable;elements;editor;editItem;editElement;emptyPlaceholder;isTable;constructor(e,t=!0,i=!1){super(!0,t),this.registerRequiredCSS(_o),this.delegate=e,this.list=this.contentElement.createChild("div","list"),this.lastSeparator=!1,this.focusRestorer=null,this.items=[],this.editable=[],this.elements=[],this.editor=null,this.editItem=null,this.editElement=null,this.emptyPlaceholder=null,this.isTable=i,i&&(this.list.role="table"),this.updatePlaceholder()}clear(){this.items=[],this.editable=[],this.elements=[],this.lastSeparator=!1,this.list.removeChildren(),this.updatePlaceholder(),this.stopEditing()}appendItem(e,t){if(this.lastSeparator&&this.items.length){const e=document.createElement("div");e.classList.add("list-separator"),this.isTable&&(e.role="rowgroup"),this.list.appendChild(e)}this.lastSeparator=!1,this.items.push(e),this.editable.push(t);const i=this.list.createChild("div","list-item");this.isTable&&(i.role="rowgroup");const n=this.delegate.renderItem(e,t);n.hasAttribute("jslog")||n.setAttribute("jslog",`${o.item()}`),i.appendChild(n),t&&(i.classList.add("editable"),i.tabIndex=0,i.appendChild(this.createControls(e,i))),this.elements.push(i),this.updatePlaceholder()}appendSeparator(){this.lastSeparator=!0}removeItem(e){this.editItem===this.items[e]&&this.stopEditing();const t=this.elements[e],i=t.previousElementSibling,n=i&&i.classList.contains("list-separator"),s=t.nextElementSibling,o=s&&s.classList.contains("list-separator");!n||!o&&s||i.remove(),o&&!i&&s.remove(),t.remove(),this.elements.splice(e,1),this.items.splice(e,1),this.editable.splice(e,1),this.updatePlaceholder()}addNewItem(e,t){this.startEditing(t,null,this.elements[e]||null)}setEmptyPlaceholder(e){this.emptyPlaceholder=e,this.updatePlaceholder()}createControls(e,t){const i=document.createElement("div");i.classList.add("controls-container"),i.classList.add("fill"),i.createChild("div","controls-gradient");const n=i.createChild("div","controls-buttons"),s=new gn("",n),o=new wn(Uo(jo.editString),"edit",void 0,"edit-item");o.addEventListener("Click",function(){const i=this.elements.indexOf(t),n=this.elements[i+1]||null;this.startEditing(e,t,n)}.bind(this)),s.appendToolbarItem(o);const r=new wn(Uo(jo.removeString),"bin",void 0,"remove-item");return r.addEventListener("Click",function(){const e=this.elements.indexOf(t);this.element.focus(),this.delegate.removeItemRequested(this.items[e],e),Se(Uo(jo.removedItem)),this.elements[Math.min(e,this.elements.length-1)].focus()}.bind(this)),s.appendToolbarItem(r),i}wasShown(){super.wasShown(),this.stopEditing()}updatePlaceholder(){this.emptyPlaceholder&&(this.elements.length||this.editor?this.emptyPlaceholder.remove():this.list.appendChild(this.emptyPlaceholder))}startEditing(e,t,i){if(t&&this.editElement===t)return;this.stopEditing(),this.focusRestorer=new Gn(this.element),this.list.classList.add("list-editing"),this.element.classList.add("list-editing"),this.editItem=e,this.editElement=t,t&&t.classList.add("hidden");const n=t?this.elements.indexOf(t):-1;this.editor=this.delegate.beginEdit(e),this.updatePlaceholder(),this.list.insertBefore(this.editor.element,i),this.editor.beginEdit(e,n,Uo(t?jo.saveString:jo.addString),this.commitEditing.bind(this),this.stopEditing.bind(this))}commitEditing(){const e=this.editItem,t=!this.editElement,i=this.editor,n=this.editElement?this.elements.indexOf(this.editElement):this.elements.length-1;this.stopEditing(),null!==e&&(this.delegate.commitEdit(e,i,t),Se(Uo(jo.changesSaved)),this.elements[n]&&this.elements[n].focus())}stopEditing(){this.list.classList.remove("list-editing"),this.element.classList.remove("list-editing"),this.focusRestorer&&this.focusRestorer.restore(),this.editElement&&this.editElement.classList.remove("hidden"),this.editor&&this.editor.element.parentElement&&this.editor.element.remove(),this.editor=null,this.editItem=null,this.editElement=null,this.updatePlaceholder()}},Editor:class{element;contentElementInternal;commitButton;cancelButton;errorMessageContainer;controls;controlByName;validators;commit;cancel;item;index;constructor(){this.element=document.createElement("div"),this.element.classList.add("editor-container"),this.element.addEventListener("keydown",t.bind(null,i.KeyboardUtilities.isEscKey,this.cancelClicked.bind(this)),!1),this.contentElementInternal=this.element.createChild("div","editor-content"),this.contentElementInternal.addEventListener("keydown",t.bind(null,(e=>"Enter"===e.key&&!(e.target instanceof HTMLSelectElement)),this.commitClicked.bind(this)),!1);const e=this.element.createChild("div","editor-buttons");function t(e,t,i){e(i)&&(i.consume(!0),t())}this.commitButton=os("",this.commitClicked.bind(this),{jslogContext:"commit",primary:!0}),e.appendChild(this.commitButton),this.cancelButton=os(Uo(jo.cancelString),this.cancelClicked.bind(this),{jslogContext:"cancel",primary:!0}),this.cancelButton.setAttribute("jslog",`${o.action("cancel").track({click:!0})}`),e.appendChild(this.cancelButton),this.errorMessageContainer=this.element.createChild("div","list-widget-input-validation-error"),O(this.errorMessageContainer),this.controls=[],this.controlByName=new Map,this.validators=[],this.commit=null,this.cancel=null,this.item=null,this.index=-1}contentElement(){return this.contentElementInternal}createInput(e,t,i,n){const s=rs("",t);return s.placeholder=i,s.addEventListener("input",this.validateControls.bind(this,!1),!1),s.setAttribute("jslog",`${o.textField().track({keydown:!0}).context(e)}`),be(s,i),this.controlByName.set(e,s),this.controls.push(s),this.validators.push(n),s}createSelect(e,t,n,s){const r=document.createElement("select");r.setAttribute("jslog",`${o.dropDown().track({change:!0}).context(e)}`),r.classList.add("chrome-select");for(let e=0;e<t.length;++e){const n=r.createChild("option");n.value=t[e],n.textContent=t[e],n.setAttribute("jslog",`${o.item(i.StringUtilities.toKebabCase(t[e])).track({click:!0})}`)}return s&&(Jt.install(r,s),be(r,s)),r.addEventListener("input",this.validateControls.bind(this,!1),!1),r.addEventListener("blur",this.validateControls.bind(this,!1),!1),this.controlByName.set(e,r),this.controls.push(r),this.validators.push(n),r}createCustomControl(e,t,i){const n=new t;return this.controlByName.set(e,n),this.controls.push(n),this.validators.push(i),n}control(e){const t=this.controlByName.get(e);if(!t)throw new Error(`Control with name ${e} does not exist, please verify.`);return t}validateControls(e){let t=!0;this.errorMessageContainer.textContent="";for(let i=0;i<this.controls.length;++i){const n=this.controls[i],{valid:s,errorMessage:o}=this.validators[i].call(null,this.item,this.index,n);if(n.classList.toggle("error-input",!s&&!e),pe(n,!s&&!e),!e&&o){if(this.errorMessageContainer.textContent){const e=document.createElement("br");this.errorMessageContainer.append(e)}this.errorMessageContainer.append(o)}t=t&&s}this.commitButton.disabled=!t}requestValidation(){this.validateControls(!1)}beginEdit(e,t,i,n,s){this.commit=n,this.cancel=s,this.item=e,this.index=t,this.commitButton.textContent=i,this.element.scrollIntoViewIfNeeded(!1),this.controls.length&&this.controls[0].focus(),this.validateControls(!0)}commitClicked(){if(this.commitButton.disabled)return;const e=this.commit;this.commit=null,this.cancel=null,this.item=null,this.index=-1,e&&e()}cancelClicked(){const e=this.cancel;this.commit=null,this.cancel=null,this.item=null,this.index=-1,e&&e()}}});class qo extends Nt{panelName;constructor(e){super(),this.element.setAttribute("jslog",`${o.panel().context(e).track({resize:!0})}`),this.element.classList.add("panel"),this.element.setAttribute("aria-label",e),this.element.classList.add(e),this.panelName=e,self.UI=self.UI||{},self.UI.panels=self.UI.panels||{},UI.panels[e]=this}get name(){return this.panelName}searchableView(){return null}elementsToRestoreScrollPositionsFor(){return[]}}var $o=Object.freeze({__proto__:null,Panel:qo,PanelWithSidebar:class extends qo{panelSplitWidget;mainWidget;sidebarWidget;constructor(e,t){super(e),this.panelSplitWidget=new Gt(!0,!1,this.panelName+"-panel-split-view-state",t||200),this.panelSplitWidget.show(this.element),this.mainWidget=new Nt,this.panelSplitWidget.setMainWidget(this.mainWidget),this.sidebarWidget=new Nt,this.sidebarWidget.setMinimumSize(100,25),this.panelSplitWidget.setSidebarWidget(this.sidebarWidget),this.sidebarWidget.element.classList.add("panel-sidebar"),this.sidebarWidget.element.setAttribute("jslog",`${o.pane("sidebar").track({resize:!0})}`)}panelSidebarElement(){return this.sidebarWidget.element}mainElement(){return this.mainWidget.element}splitWidget(){return this.panelSplitWidget}}}),Go={cssContent:".widget{display:flex;background:var(--sys-color-cdt-base-container);border:1px solid transparent;box-shadow:var(--drop-shadow);border-radius:2px;overflow:auto;user-select:text;line-height:11px;&.borderless-popover{border:0}}.widget.has-padding{padding:6px}"};class Xo{static createPopover=e=>{const t=new Ts(`${o.popover(e).parent("mapped")}`);return t.registerRequiredCSS(Go),t.setSizeBehavior("MeasureContent"),t.setMarginBehavior("Arrow"),t};disableOnClick;hasPadding;getRequest;scheduledRequest;hidePopoverCallback;container;showTimeout;hideTimeout;hidePopoverTimer;showPopoverTimer;boundMouseDown;boundMouseMove;boundMouseOut;#o;constructor(e,t,i){this.disableOnClick=!1,this.hasPadding=!1,this.getRequest=t,this.#o=i,this.scheduledRequest=null,this.hidePopoverCallback=null,this.container=e,this.showTimeout=0,this.hideTimeout=0,this.hidePopoverTimer=null,this.showPopoverTimer=null,this.boundMouseDown=this.mouseDown.bind(this),this.boundMouseMove=this.mouseMove.bind(this),this.boundMouseOut=this.mouseOut.bind(this),this.container.addEventListener("mousedown",this.boundMouseDown,!1),this.container.addEventListener("mousemove",this.boundMouseMove,!1),this.container.addEventListener("mouseout",this.boundMouseOut,!1),this.setTimeout(1e3)}setTimeout(e,t){this.showTimeout=e,this.hideTimeout="number"==typeof t?t:e/2}setHasPadding(e){this.hasPadding=e}setDisableOnClick(e){this.disableOnClick=e}eventInScheduledContent(e){const t=e;return!!this.scheduledRequest&&this.scheduledRequest.box.contains(t.clientX,t.clientY)}mouseDown(e){this.disableOnClick?this.hidePopover():this.eventInScheduledContent(e)||(this.startHidePopoverTimer(0),this.stopShowPopoverTimer(),this.startShowPopoverTimer(e,0))}mouseMove(e){const t=e;if(this.eventInScheduledContent(t))return this.stopShowPopoverTimer(),void this.startShowPopoverTimer(t,this.isPopoverVisible()?.6*this.showTimeout:this.showTimeout);this.startHidePopoverTimer(this.hideTimeout),this.stopShowPopoverTimer(),t.buttons&&this.disableOnClick||this.startShowPopoverTimer(t,this.isPopoverVisible()?.6*this.showTimeout:this.showTimeout)}popoverMouseMove(e){this.stopHidePopoverTimer()}popoverMouseOut(e,t){const i=t;if(!e.isShowing())return;const n=i.relatedTarget;n&&!n.isSelfOrDescendant(e.contentElement)&&this.startHidePopoverTimer(this.hideTimeout)}mouseOut(e){this.isPopoverVisible()&&(this.eventInScheduledContent(e)||this.startHidePopoverTimer(this.hideTimeout))}startHidePopoverTimer(e){this.hidePopoverCallback&&!this.hidePopoverTimer&&(this.hidePopoverTimer=window.setTimeout((()=>{this.hidePopoverInternal(),this.hidePopoverTimer=null}),e))}startShowPopoverTimer(e,t){this.scheduledRequest=this.getRequest.call(null,e),this.scheduledRequest&&(this.showPopoverTimer=window.setTimeout((()=>{this.showPopoverTimer=null,this.stopHidePopoverTimer(),this.hidePopoverInternal();const t=e.target.ownerDocument;this.showPopover(t)}),t))}stopShowPopoverTimer(){this.showPopoverTimer&&(clearTimeout(this.showPopoverTimer),this.showPopoverTimer=null)}isPopoverVisible(){return Boolean(this.hidePopoverCallback)}hidePopover(){this.stopShowPopoverTimer(),this.hidePopoverInternal()}hidePopoverInternal(){this.hidePopoverCallback&&(this.hidePopoverCallback.call(null),this.hidePopoverCallback=null)}showPopover(e){const t=Xo.createPopover(this.#o),i=this.scheduledRequest;i&&i.show.call(null,t).then((n=>{n&&(this.scheduledRequest===i?(Yo&&Yo.hidePopover(),Yo=this,o.setMappedParent(t.contentElement,this.container),t.contentElement.classList.toggle("has-padding",this.hasPadding),t.contentElement.addEventListener("mousemove",this.popoverMouseMove.bind(this),!0),t.contentElement.addEventListener("mouseout",this.popoverMouseOut.bind(this,t),!0),t.setContentAnchorBox(i.box),t.show(e),this.hidePopoverCallback=()=>{i.hide&&i.hide.call(null),t.hide(),Yo=null}):i.hide&&i.hide.call(null))}))}stopHidePopoverTimer(){this.hidePopoverTimer&&(clearTimeout(this.hidePopoverTimer),this.hidePopoverTimer=null,this.stopShowPopoverTimer())}dispose(){this.container.removeEventListener("mousedown",this.boundMouseDown,!1),this.container.removeEventListener("mousemove",this.boundMouseMove,!1),this.container.removeEventListener("mouseout",this.boundMouseOut,!1)}}let Yo=null;var Qo=Object.freeze({__proto__:null,PopoverHelper:Xo}),Zo={cssContent:".progress-indicator-shadow-stop-button{background-color:var(--sys-color-error-bright);border:0;width:10px;height:12px;border-radius:2px}.progress-indicator-shadow-container{display:flex;flex:1 0 auto;align-items:center}.progress-indicator-shadow-container .title{text-overflow:ellipsis;overflow:hidden;max-width:150px;margin-right:2px;color:var(--sys-color-token-subtle)}.progress-indicator-shadow-container progress{flex:auto;margin:0 2px;width:100px}"};var Jo=Object.freeze({__proto__:null,ProgressIndicator:class{element;shadowRoot;contentElement;labelElement;progressElement;stopButton;isCanceledInternal;worked;isDone;constructor(){this.element=document.createElement("div"),this.element.classList.add("progress-indicator"),this.shadowRoot=d.createShadowRootWithCoreStyles(this.element,{cssFile:Zo,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","progress-indicator-shadow-container"),this.labelElement=this.contentElement.createChild("div","title"),this.progressElement=this.contentElement.createChild("progress"),this.progressElement.value=0,this.stopButton=this.contentElement.createChild("button","progress-indicator-shadow-stop-button"),this.stopButton.addEventListener("click",this.cancel.bind(this)),this.isCanceledInternal=!1,this.worked=0}show(e){e.appendChild(this.element)}done(){this.isDone||(this.isDone=!0,this.element.remove())}cancel(){this.isCanceledInternal=!0}isCanceled(){return this.isCanceledInternal}setTitle(e){this.labelElement.textContent=e}setTotalWork(e){this.progressElement.max=e}setWorked(e,t){this.worked=e,this.progressElement.value=e,t&&this.setTitle(t)}incrementWorked(e){this.setWorked(this.worked+(e||1))}}}),er={cssContent:".widget{padding:20px}.message,\n.button{font-size:larger;white-space:pre;margin:5px}.button{text-align:center;margin-top:10px}.reason{--override-reason-color:#8b0000;color:var(--override-reason-color)}.-theme-with-dark-background .reason,\n:host-context(.-theme-with-dark-background) .reason{--override-reason-color:rgb(255 116 116)}"};const tr={debuggingConnectionWasClosed:"Debugging connection was closed. Reason: ",reconnectWhenReadyByReopening:"Reconnect when ready by reopening DevTools.",reconnectDevtools:"Reconnect `DevTools`",sendFeedback:"[FB-only] Send feedback"},ir=t.i18n.registerUIStrings("ui/legacy/RemoteDebuggingTerminatedScreen.ts",tr),nr=t.i18n.getLocalizedString.bind(void 0,ir);class sr extends Nt{constructor(e){super(!0),this.registerRequiredCSS(er);const t=this.contentElement.createChild("div","message").createChild("span");t.append(nr(tr.debuggingConnectionWasClosed));t.createChild("span","reason").textContent=e,this.contentElement.createChild("div","message").textContent=nr(tr.reconnectWhenReadyByReopening);const i=os(nr(tr.reconnectDevtools),(()=>window.location.reload()),{jslogContext:"reconnect"}),n=this.contentElement.createChild("div","button");if(n.appendChild(i),globalThis.FB_ONLY__reactNativeFeedbackLink){const e=globalThis.FB_ONLY__reactNativeFeedbackLink,t=os(nr(tr.sendFeedback),(()=>{s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(e)}),{className:"primary-button",jslogContext:"sendFeedback"});n.appendChild(t)}}static show(e){const t=new Ds("remote-debnugging-terminated");t.setSizeBehavior("MeasureContent"),t.addCloseButton(),t.setDimmed(!0),new sr(e).show(t.contentElement),t.show(),s.rnPerfMetrics.remoteDebuggingTerminated(e)}}var or=Object.freeze({__proto__:null,RemoteDebuggingTerminatedScreen:sr}),rr={cssContent:":host{background-color:var(--sys-color-cdt-base-container)}.report-content-box{background-color:var(--sys-color-cdt-base-container);overflow:auto}.report-content-box.no-scroll{overflow:visible}.report-header{border-bottom:1px solid var(--sys-color-divider);padding:12px 24px}.report-header .toolbar{margin-bottom:-8px;margin-top:5px;margin-left:-8px}.report-title{font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;user-select:none}.report-url,\n.report-subtitle{font-size:12px;margin-top:10px}.report-section{display:flex;padding:12px;border-bottom:1px solid var(--sys-color-divider);flex-direction:column}.report-section-header{margin-left:18px;display:flex;flex-direction:row;align-items:center}.report-section-title{flex:auto;text-overflow:ellipsis;overflow:hidden;font-weight:bold;color:var(--sys-color-on-surface)}.report-field{margin-top:8px;display:flex;line-height:28px}.report-row{margin:10px 0 2px 18px}.report-field-name{color:var(--sys-color-on-surface-subtle);flex:0 0 128px;text-align:right;padding:0 6px;white-space:pre-wrap}.report-field-value{flex:auto;padding:0 6px;white-space:pre;user-select:text}.report-field-value-is-flexed{display:flex;white-space:pre-wrap}.report-field-value-subtitle{color:var(--sys-color-state-disabled);line-height:14px}.report-row-selectable{user-select:text}.image-wrapper,\n.image-wrapper img{max-width:200px;max-height:200px;display:block;object-fit:contain}.image-wrapper{height:fit-content;margin-right:8px}.show-mask img{clip-path:circle(40% at 50% 50%)}.show-mask .image-wrapper{background:var(--image-file-checker)}@media (forced-colors: active){.report-field-value .inline-icon{color:ButtonText}.report-field-value .multiline-value{color:ButtonText}}"};class ar extends Nt{headerElement;titleElement;fieldList;fieldMap;constructor(e,t){super(),this.element.classList.add("report-section"),t&&this.element.classList.add(t),this.headerElement=this.element.createChild("div","report-section-header"),this.titleElement=this.headerElement.createChild("div","report-section-title"),this.setTitle(e),ie(this.titleElement,2),this.fieldList=this.element.createChild("div","vbox"),this.fieldMap=new Map}title(){return this.titleElement.textContent||""}getTitleElement(){return this.titleElement}getFieldElement(){return this.fieldList}appendFieldWithCustomView(e){this.fieldList.append(e)}setTitle(e,t){this.titleElement.textContent!==e&&(this.titleElement.textContent=e),Jt.install(this.titleElement,t||""),this.titleElement.classList.toggle("hidden",!this.titleElement.textContent)}setUiGroupTitle(e){W(this.element),be(this.element,e)}createToolbar(){const e=new gn("");return this.headerElement.appendChild(e.element),e}appendField(e,t){let i=this.fieldMap.get(e);return i||(i=this.fieldList.createChild("div","report-field"),i.createChild("div","report-field-name").textContent=e,this.fieldMap.set(e,i),i.createChild("div","report-field-value")),t&&i.lastElementChild&&(i.lastElementChild.textContent=t),i.lastElementChild}appendFlexedField(e,t){const i=this.appendField(e,t);return i.classList.add("report-field-value-is-flexed"),i}removeField(e){const t=this.fieldMap.get(e);t&&t.remove(),this.fieldMap.delete(e)}setFieldVisible(e,t){const i=this.fieldMap.get(e);i&&i.classList.toggle("hidden",!t)}fieldValue(e){const t=this.fieldMap.get(e);return t?t.lastElementChild:null}appendRow(){return this.fieldList.createChild("div","report-row")}appendSelectableRow(){return this.fieldList.createChild("div","report-row report-row-selectable")}clearContent(){this.fieldList.removeChildren(),this.fieldMap.clear()}markFieldListAsGroup(){W(this.fieldList),be(this.fieldList,this.title())}setIconMasked(e){this.element.classList.toggle("show-mask",e)}}var lr=Object.freeze({__proto__:null,ReportView:class extends Nt{contentBox;headerElement;titleElement;sectionList;subtitleElement;urlElement;constructor(e){super(!0),this.registerRequiredCSS(rr),this.contentBox=this.contentElement.createChild("div","report-content-box"),this.headerElement=this.contentBox.createChild("div","report-header vbox"),this.titleElement=this.headerElement.createChild("div","report-title"),e?this.titleElement.textContent=e:this.headerElement.classList.add("hidden"),ie(this.titleElement,1),this.sectionList=this.contentBox.createChild("div","vbox")}getHeaderElement(){return this.headerElement}setTitle(e){this.titleElement.textContent!==e&&(this.titleElement.textContent=e,this.headerElement.classList.toggle("hidden",Boolean(e)))}setSubtitle(e){this.subtitleElement&&this.subtitleElement.textContent===e||(this.subtitleElement||(this.subtitleElement=this.headerElement.createChild("div","report-subtitle")),this.subtitleElement.textContent=e)}setURL(e){this.urlElement||(this.urlElement=this.headerElement.createChild("div","report-url link")),this.urlElement.removeChildren(),e&&this.urlElement.appendChild(e),this.urlElement.setAttribute("jslog",`${o.link("source-location").track({click:!0})}`)}createToolbar(){const e=new gn("");return this.headerElement.appendChild(e.element),e}appendSection(e,t){const i=new ar(e,t);return i.show(this.sectionList),i}sortSections(e){const t=this.children().slice();if(!t.every(((t,i,n)=>!i||e(n[i-1],n[i])<=0))){this.detachChildWidgets(),t.sort(e);for(const e of t)e.show(this.sectionList)}}setHeaderVisible(e){this.headerElement.classList.toggle("hidden",!e)}setBodyScrollable(e){this.contentBox.classList.toggle("no-scroll",!e)}},Section:ar}),hr={cssContent:".root-view{background-color:var(--sys-color-cdt-base-container);overflow:hidden;position:absolute!important;left:0;top:0;right:0;bottom:0}"};var dr=Object.freeze({__proto__:null,RootView:class extends Nt{window;constructor(){super(),this.markAsRoot(),this.element.classList.add("root-view"),this.registerRequiredCSS(hr),this.element.setAttribute("spellcheck","false")}attachToDocument(e){e.defaultView&&e.defaultView.addEventListener("resize",this.doResize.bind(this),!1),this.window=e.defaultView,this.doResize(),this.show(e.body)}doResize(){if(this.window){const e=this.constraints().minimum,t=qt.instance().zoomFactor(),i=Math.min(0,this.window.innerWidth-e.width/t);this.element.style.marginRight=i+"px";const n=Math.min(0,this.window.innerHeight-e.height/t);this.element.style.marginBottom=n+"px"}super.doResize()}}}),cr={cssContent:".search-bar{flex:0 0 31px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);display:flex;overflow:hidden;z-index:0}.search-bar.replaceable{flex:0 0 57px}.search-replace{appearance:none;border:0;border-radius:4px;padding:0 3px;margin:0;flex:1}.search-replace:focus{outline:none}.toolbar-search{display:flex;width:100%}.toolbar-search > div{margin:2px;flex-shrink:0}.toolbar-search-control{display:flex;position:relative;background-color:var(--sys-color-cdt-base-container)}.toolbar-search-control,\n.toolbar-replace-control{border:1px solid var(--sys-color-neutral-outline);height:22px;border-radius:4px;width:100%;margin-top:2px;margin-bottom:2px;&:focus-within{border-color:var(--sys-color-state-focus-ring)}}.toolbar-search-inputs{flex-grow:1;min-width:150px;& input{line-height:17px}&:hover:not(:focus-within){& .toolbar-search-control{background-color:var(--sys-color-state-hover-on-subtle)}& input{background:none}}}.toolbar-search-navigation-controls{align-self:stretch}.toolbar-search-navigation{display:inline-block;width:20px;height:20px;background-repeat:no-repeat;border-left:1px solid var(--sys-color-divider);opacity:30%}.toolbar-search-navigation.enabled{opacity:100%}.toolbar-search button.search-action-button{font-weight:400;height:22px;width:87px}.toolbar-search-buttons{display:flex;flex-direction:column}.toolbar-search-navigation.enabled:active{background-position:4px 7px,0 0}.toolbar-search-navigation.toolbar-search-navigation-prev{background-image:var(--image-file-chevron-up);border-left:1px solid var(--sys-color-neutral-outline)}:host-context(.-theme-with-dark-background) .toolbar-search-navigation{filter:invert(90%)}.toolbar-search-navigation.toolbar-search-navigation-prev.enabled:active{background-image:var(--image-file-chevron-up),var(--sys-color-neutral-container)}.toolbar-search-navigation.toolbar-search-navigation-next{background-image:var(--image-file-chevron-down);border-left:1px solid var(--sys-color-neutral-outline)}.toolbar-search-navigation.toolbar-search-navigation-next.enabled:active{background-image:var(--image-file-chevron-down),var(--sys-color-neutral-container)}.search-results-matches{display:inline-block;text-align:right;padding:0 4px;color:var(--sys-color-token-subtle);align-self:center}.first-row-buttons{display:flex;justify-content:space-between}.toolbar-search > .replace-toggle-toolbar{margin:2px -2px 0 0}.toolbar-search-options{margin:0 auto}:host-context(#sources-panel-sources-view) .search-bar{flex-basis:auto}:host-context(#sources-panel-sources-view) .toolbar-search{flex-wrap:wrap}"};const ur={replace:"Replace",findString:"Find",searchPrevious:"Search previous",searchNext:"Search next",matchCase:"Match Case",useRegularExpression:"Use Regular Expression",cancel:"Cancel",replaceAll:"Replace all",dOfD:"{PH1} of {PH2}",matchString:"1 match",dMatches:"{PH1} matches"},mr=t.i18n.registerUIStrings("ui/legacy/SearchableView.ts",ur),pr=t.i18n.getLocalizedString.bind(void 0,mr);const gr=Symbol("searchableView"),br=new WeakMap;class fr{query;caseSensitive;isRegex;constructor(e,t,i){this.query=e,this.caseSensitive=t,this.isRegex=i}toSearchRegex(e){let t=this.caseSensitive?"":"i";e&&(t+="g");const n=this.isRegex?"/"+this.query+"/":this.query;let s,o=!1;try{/^\/.+\/$/.test(n)&&(s=new RegExp(n.substring(1,n.length-1),t),o=!0)}catch(e){}return s||(s=i.StringUtilities.createPlainTextSearchRegex(n,t)),{regex:s,fromQuery:o}}}var vr=Object.freeze({__proto__:null,SearchableView:class extends Nt{searchProvider;replaceProvider;setting;replaceable;footerElementContainer;footerElement;replaceToggleButton;searchInputElement;matchesElement;searchNavigationPrevElement;searchNavigationNextElement;replaceInputElement;buttonsContainer;caseSensitiveButton;regexButton;secondRowButtons;replaceButtonElement;replaceAllButtonElement;minimalSearchQuerySize;searchIsVisible;currentQuery;valueChangedTimeoutId;constructor(t,i,n){super(!0),this.registerRequiredCSS(cr),br.set(this.element,this),this.searchProvider=t,this.replaceProvider=i,this.setting=n?e.Settings.Settings.instance().createSetting(n,{}):null,this.replaceable=!1,this.contentElement.createChild("slot"),this.footerElementContainer=this.contentElement.createChild("div","search-bar hidden"),this.footerElementContainer.style.order="100",this.footerElement=this.footerElementContainer.createChild("div","toolbar-search"),this.footerElement.setAttribute("jslog",`${o.toolbar("search").track({resize:!0})}`);const s=new gn("replace-toggle-toolbar",this.footerElement);this.replaceToggleButton=new xn(pr(ur.replace),"replace",void 0,"repalce"),this.replaceToggleButton.addEventListener("Click",this.toggleReplace,this),s.appendToolbarItem(this.replaceToggleButton);const r=this.footerElement.createChild("div","toolbar-search-inputs"),a=r.createChild("div","toolbar-search-control");this.searchInputElement=Oo.create(),this.searchInputElement.type="search",this.searchInputElement.classList.add("search-replace","custom-search-input"),this.searchInputElement.id="search-input-field",this.searchInputElement.placeholder=pr(ur.findString),this.searchInputElement.setAttribute("jslog",`${o.textField("search").track({change:!0})}`),a.appendChild(this.searchInputElement),this.matchesElement=a.createChild("label","search-results-matches"),this.matchesElement.setAttribute("for","search-input-field");const l=a.createChild("div","toolbar-search-navigation-controls");this.searchNavigationPrevElement=l.createChild("div","toolbar-search-navigation toolbar-search-navigation-prev"),this.searchNavigationPrevElement.addEventListener("click",this.onPrevButtonSearch.bind(this),!1),Jt.install(this.searchNavigationPrevElement,pr(ur.searchPrevious)),be(this.searchNavigationPrevElement,pr(ur.searchPrevious)),this.searchNavigationPrevElement.setAttribute("jslog",`${o.action("select-previous").track({click:!0})}`),this.searchNavigationNextElement=l.createChild("div","toolbar-search-navigation toolbar-search-navigation-next"),this.searchNavigationNextElement.addEventListener("click",this.onNextButtonSearch.bind(this),!1),Jt.install(this.searchNavigationNextElement,pr(ur.searchNext)),be(this.searchNavigationNextElement,pr(ur.searchNext)),this.searchNavigationPrevElement.setAttribute("jslog",`${o.action("select-next").track({click:!0})}`),this.searchInputElement.addEventListener("keydown",this.onSearchKeyDown.bind(this),!0),this.searchInputElement.addEventListener("input",this.onInput.bind(this),!1),this.replaceInputElement=r.createChild("input","search-replace toolbar-replace-control hidden"),this.replaceInputElement.addEventListener("keydown",this.onReplaceKeyDown.bind(this),!0),this.replaceInputElement.placeholder=pr(ur.replace),this.replaceInputElement.setAttribute("jslog",`${o.textField("replace").track({change:!0})}`),this.buttonsContainer=this.footerElement.createChild("div","toolbar-search-buttons");const h=this.buttonsContainer.createChild("div","first-row-buttons"),d=new gn("toolbar-search-options",h);this.searchProvider.supportsCaseSensitiveSearch()&&(this.caseSensitiveButton=new xn(pr(ur.matchCase),void 0,void 0,"match-case"),this.caseSensitiveButton.setText("Aa"),this.caseSensitiveButton.addEventListener("Click",this.toggleCaseSensitiveSearch,this),d.appendToolbarItem(this.caseSensitiveButton)),this.searchProvider.supportsRegexSearch()&&(this.regexButton=new xn(pr(ur.useRegularExpression),void 0,void 0,"regex-search"),this.regexButton.setText(".*"),this.regexButton.addEventListener("Click",this.toggleRegexSearch,this),d.appendToolbarItem(this.regexButton));const c=os(pr(ur.cancel),this.closeSearch.bind(this),{className:"search-action-button",jslogContext:"close-search"});h.appendChild(c),this.secondRowButtons=this.buttonsContainer.createChild("div","second-row-buttons hidden"),this.replaceButtonElement=os(pr(ur.replace),this.replace.bind(this),{className:"search-action-button",jslogContext:"replace"}),this.replaceButtonElement.disabled=!0,this.secondRowButtons.appendChild(this.replaceButtonElement),this.replaceAllButtonElement=os(pr(ur.replaceAll),this.replaceAll.bind(this),{className:"search-action-button",jslogContext:"replace-all"}),this.secondRowButtons.appendChild(this.replaceAllButtonElement),this.replaceAllButtonElement.disabled=!0,this.minimalSearchQuerySize=3,this.loadSetting()}static fromElement(e){let t=null;for(;e&&!t;)t=br.get(e)||null,e=e.parentElementOrShadowHost();return t}toggleCaseSensitiveSearch(){this.caseSensitiveButton&&this.caseSensitiveButton.setToggled(!this.caseSensitiveButton.toggled()),this.saveSetting(),this.performSearch(!1,!0)}toggleRegexSearch(){this.regexButton&&this.regexButton.setToggled(!this.regexButton.toggled()),this.saveSetting(),this.performSearch(!1,!0)}toggleReplace(){this.replaceToggleButton.setToggled(!this.replaceToggleButton.toggled()),this.updateSecondRowVisibility()}saveSetting(){if(!this.setting)return;const e=this.setting.get()||{};this.caseSensitiveButton&&(e.caseSensitive=this.caseSensitiveButton.toggled()),this.regexButton&&(e.isRegex=this.regexButton.toggled()),this.setting.set(e)}loadSetting(){const e=this.setting&&this.setting.get()||{};this.searchProvider.supportsCaseSensitiveSearch()&&this.caseSensitiveButton&&this.caseSensitiveButton.setToggled(Boolean(e.caseSensitive)),this.searchProvider.supportsRegexSearch()&&this.regexButton&&this.regexButton.setToggled(Boolean(e.isRegex))}setMinimalSearchQuerySize(e){this.minimalSearchQuerySize=e}setPlaceholder(e,t){this.searchInputElement.placeholder=e,t&&be(this.searchInputElement,t)}setReplaceable(e){this.replaceable=e}updateSearchMatchesCount(e){const t=this.searchProvider;t.currentSearchMatches!==e&&(t.currentSearchMatches=e,this.updateSearchMatchesCountAndCurrentMatchIndex(t.currentQuery?e:0,-1))}updateCurrentMatchIndex(e){const t=this.searchProvider;this.updateSearchMatchesCountAndCurrentMatchIndex(t.currentSearchMatches,e)}isSearchVisible(){return Boolean(this.searchIsVisible)}closeSearch(){this.cancelSearch(),this.footerElementContainer.hasFocus()&&this.focus(),this.searchProvider.onSearchClosed?.()}toggleSearchBar(e){this.footerElementContainer.classList.toggle("hidden",!e),this.doResize()}cancelSearch(){this.searchIsVisible&&(this.resetSearch(),delete this.searchIsVisible,this.toggleSearchBar(!1))}resetSearch(){this.clearSearch(),this.updateReplaceVisibility(),this.matchesElement.textContent=""}refreshSearch(){this.searchIsVisible&&(this.resetSearch(),this.performSearch(!1,!1))}handleFindNextShortcut(){return!!this.searchIsVisible&&(this.searchProvider.jumpToNextSearchResult(),!0)}handleFindPreviousShortcut(){return!!this.searchIsVisible&&(this.searchProvider.jumpToPreviousSearchResult(),!0)}handleFindShortcut(){return this.showSearchField(),!0}handleCancelSearchShortcut(){return!!this.searchIsVisible&&(this.closeSearch(),!0)}updateSearchNavigationButtonState(e){this.replaceButtonElement.disabled=!e,this.replaceAllButtonElement.disabled=!e,this.searchNavigationPrevElement.classList.toggle("enabled",e),this.searchNavigationNextElement.classList.toggle("enabled",e)}updateSearchMatchesCountAndCurrentMatchIndex(e,t){this.currentQuery?this.matchesElement.textContent=0===e||t>=0?pr(ur.dOfD,{PH1:t+1,PH2:e}):1===e?pr(ur.matchString):pr(ur.dMatches,{PH1:e}):this.matchesElement.textContent="",this.updateSearchNavigationButtonState(e>0)}showSearchField(){let e;if(this.searchIsVisible&&this.cancelSearch(),!this.searchInputElement.hasFocus()){const t=Hi.instance().element.window().getSelection();t&&t.rangeCount&&(e=t.toString().replace(/\r?\n.*/,""))}this.toggleSearchBar(!0),this.updateReplaceVisibility(),e&&(this.searchInputElement.value=e),this.performSearch(!1,!1),this.searchInputElement.focus(),this.searchInputElement.select(),this.searchIsVisible=!0}updateReplaceVisibility(){this.replaceToggleButton.setVisible(this.replaceable),this.replaceable||(this.replaceToggleButton.setToggled(!1),this.updateSecondRowVisibility())}onSearchKeyDown(e){const t=e;if(i.KeyboardUtilities.isEscKey(t))return this.closeSearch(),void t.consume(!0);"Enter"===t.key&&(this.currentQuery?this.jumpToNextSearchResult(t.shiftKey):this.performSearch(!0,!0,t.shiftKey))}onReplaceKeyDown(e){"Enter"===e.key&&this.replace()}jumpToNextSearchResult(e){this.currentQuery&&(e?this.searchProvider.jumpToPreviousSearchResult():this.searchProvider.jumpToNextSearchResult())}onNextButtonSearch(e){this.searchNavigationNextElement.classList.contains("enabled")&&(this.jumpToNextSearchResult(),this.searchInputElement.focus())}onPrevButtonSearch(e){this.searchNavigationPrevElement.classList.contains("enabled")&&(this.jumpToNextSearchResult(!0),this.searchInputElement.focus())}clearSearch(){const e=this.searchProvider;delete this.currentQuery,Boolean(e.currentQuery)&&(delete e.currentQuery,this.searchProvider.onSearchCanceled()),this.updateSearchMatchesCountAndCurrentMatchIndex(0,-1)}performSearch(e,t,i){const n=this.searchInputElement.value;if(!n||!e&&n.length<this.minimalSearchQuerySize&&!this.currentQuery)return void this.clearSearch();this.currentQuery=n,this.searchProvider.currentQuery=n;const s=this.currentSearchConfig();this.searchProvider.performSearch(s,t,i)}currentSearchConfig(){const e=this.searchInputElement.value,t=!!this.caseSensitiveButton&&this.caseSensitiveButton.toggled(),i=!!this.regexButton&&this.regexButton.toggled();return new fr(e,t,i)}updateSecondRowVisibility(){const e=this.replaceToggleButton.toggled();this.footerElementContainer.classList.toggle("replaceable",e),this.secondRowButtons.classList.toggle("hidden",!e),this.replaceInputElement.classList.toggle("hidden",!e),e?this.replaceInputElement.focus():this.searchInputElement.focus(),this.doResize()}replace(){if(!this.replaceProvider)throw new Error("No 'replacable' provided to SearchableView!");const e=this.currentSearchConfig();this.replaceProvider.replaceSelectionWith(e,this.replaceInputElement.value),delete this.currentQuery,this.performSearch(!0,!0)}replaceAll(){if(!this.replaceProvider)throw new Error("No 'replacable' provided to SearchableView!");const e=this.currentSearchConfig();this.replaceProvider.replaceAllWith(e,this.replaceInputElement.value)}onInput(t){if(!e.Settings.Settings.instance().moduleSetting("search-as-you-type").get())return void this.clearSearch();this.valueChangedTimeoutId&&clearTimeout(this.valueChangedTimeoutId);const i=this.searchInputElement.value.length<3?200:0;this.valueChangedTimeoutId=window.setTimeout(this.onValueChanged.bind(this),i)}onValueChanged(){this.searchIsVisible&&(delete this.valueChangedTimeoutId,this.performSearch(!1,!0))}},_symbol:gr,SearchConfig:fr}),wr={cssContent:".item.disabled{opacity:50%}.item-list{background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);overflow-x:hidden;overflow-y:auto;width:100%}.item.highlighted{background-color:var(--sys-color-state-hover-on-subtle)}@media (forced-colors: active){.item.disabled{opacity:100%}.item-list{border:1px solid ButtonText;background-color:ButtonFace}.item.highlighted{forced-color-adjust:none;color:HighlightText;background-color:Highlight}}"},xr={cssContent:"button.soft-dropdown{height:26px;text-align:left;position:relative;border:none;background:none}button.soft-dropdown[disabled]{opacity:50%}button.soft-dropdown > .title{padding-right:5px;flex:0 1 120px;overflow:hidden;text-overflow:ellipsis}button.soft-dropdown:hover:not(:active) > .title{color:var(--sys-color-on-surface)}@media (forced-colors: active){button.soft-dropdown{border:1px solid ButtonText;background:ButtonFace;color:ButtonText}button.soft-dropdown[disabled]{opacity:100%}}"};const Er={noItemSelected:"(no item selected)"},Ir=t.i18n.registerUIStrings("ui/legacy/SoftDropDown.ts",Er),yr=t.i18n.getLocalizedString.bind(void 0,Ir);var Sr=Object.freeze({__proto__:null,SoftDropDown:class{delegate;selectedItem;model;placeholderText;element;titleElement;glassPane;list;rowHeight;width;listWasShowing200msAgo;constructor(e,t,i){this.delegate=t,this.selectedItem=null,this.model=e,this.placeholderText=yr(Er.noItemSelected),this.element=document.createElement("button"),i&&this.element.setAttribute("jslog",`${o.dropDown().track({click:!0,keydown:"ArrowUp|ArrowDown|Enter"}).context(i)}`),this.element.classList.add("soft-dropdown"),m.ThemeSupport.instance().appendStyle(this.element,xr),this.titleElement=this.element.createChild("span","title");const n=a.Icon.create("triangle-down");this.element.appendChild(n),ae(this.element,!1),this.glassPane=new Ts,this.glassPane.setMarginBehavior("NoMargin"),this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.setOutsideClickCallback(this.hide.bind(this)),this.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),this.list=new Yi(e,this,Gi.EqualHeightItems),this.list.element.classList.add("item-list"),this.rowHeight=36,this.width=315,d.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:wr,delegatesFocus:void 0}).appendChild(this.list.element),q(this.list.element),o.setMappedParent(this.list.element,this.element),this.list.element.setAttribute("jslog",`${o.menu().parent("mapped").track({resize:!0})}`),this.listWasShowing200msAgo=!1,this.element.addEventListener("mousedown",(e=>{this.listWasShowing200msAgo?this.hide(e):this.element.disabled||this.show(e)}),!1),this.element.addEventListener("keydown",this.onKeyDownButton.bind(this),!1),this.list.element.addEventListener("keydown",this.onKeyDownList.bind(this),!1),this.list.element.addEventListener("focusout",this.hide.bind(this),!1),this.list.element.addEventListener("mousedown",(e=>e.consume(!0)),!1),this.list.element.addEventListener("mouseup",(e=>{e.target!==this.list.element&&this.listWasShowing200msAgo&&(this.selectHighlightedItem(),this.hide(e))}),!1),e.addEventListener("ItemsReplaced",this.itemsReplaced,this)}show(e){this.glassPane.isShowing()||(this.glassPane.setContentAnchorBox(this.element.boxInWindow()),this.glassPane.show(this.element.ownerDocument),this.list.element.focus(),ae(this.element,!0),this.updateGlasspaneSize(),this.selectedItem&&this.list.selectItem(this.selectedItem),e.consume(!0),window.setTimeout((()=>{this.listWasShowing200msAgo=!0}),200))}updateGlasspaneSize(){const e=this.rowHeight*Math.min(this.model.length,9);this.glassPane.setMaxContentSize(new _e(this.width,e)),this.list.viewportResized()}hide(e){window.setTimeout((()=>{this.listWasShowing200msAgo=!1}),200),this.glassPane.hide(),this.list.selectItem(null),ae(this.element,!1),this.element.focus(),e.consume(!0)}onKeyDownButton(e){const t=e;let i=!1;switch(t.key){case"ArrowUp":this.show(t),this.list.selectItemNextPage(),i=!0;break;case"ArrowDown":this.show(t),this.list.selectItemPreviousPage(),i=!0;break;case"Enter":case" ":this.show(t),i=!0}i&&t.consume(!0)}onKeyDownList(e){const t=e;let i=!1;switch(t.key){case"ArrowLeft":i=this.list.selectPreviousItem(!1,!1);break;case"ArrowRight":i=this.list.selectNextItem(!1,!1);break;case"Home":for(let e=0;e<this.model.length;e++)if(this.isItemSelectable(this.model.at(e))){this.list.selectItem(this.model.at(e)),i=!0;break}break;case"End":for(let e=this.model.length-1;e>=0;e--)if(this.isItemSelectable(this.model.at(e))){this.list.selectItem(this.model.at(e)),i=!0;break}break;case"Escape":this.hide(t),i=!0;break;case"Tab":case"Enter":case" ":this.selectHighlightedItem(),this.hide(t),i=!0;break;default:if(1===t.key.length){const e=this.list.selectedIndex(),n=t.key.toUpperCase();for(let t=0;t<this.model.length;t++){const i=this.model.at((e+t+1)%this.model.length);if(this.delegate.titleFor(i).toUpperCase().startsWith(n)){this.list.selectItem(i);break}}i=!0}}i&&t.consume(!0)}setWidth(e){this.width=e,this.updateGlasspaneSize()}setRowHeight(e){this.rowHeight=e}setPlaceholderText(e){this.placeholderText=e,this.selectedItem||(this.titleElement.textContent=this.placeholderText)}itemsReplaced(e){const{removed:t}=e.data;this.selectedItem&&-1!==t.indexOf(this.selectedItem)&&(this.selectedItem=null,this.selectHighlightedItem()),this.updateGlasspaneSize()}getSelectedItem(){return this.selectedItem}selectItem(e){this.selectedItem=e,this.selectedItem?this.titleElement.textContent=this.delegate.titleFor(this.selectedItem):this.titleElement.textContent=this.placeholderText,this.delegate.itemSelected(this.selectedItem)}createElementForItem(e){const t=document.createElement("div");return t.classList.add("item"),t.addEventListener("mousemove",(t=>{(t.movementX||t.movementY)&&this.delegate.isItemSelectable(e)&&this.list.selectItem(e,!1,!0)})),t.classList.toggle("disabled",!this.delegate.isItemSelectable(e)),t.classList.toggle("highlighted",this.list.selectedItem()===e),$(t),t.appendChild(this.delegate.createElementForItem(e)),t}heightForItem(e){return this.rowHeight}isItemSelectable(e){return this.delegate.isItemSelectable(e)}selectedItemChanged(e,t,i,n){i&&i.classList.remove("highlighted"),n&&n.classList.add("highlighted"),ve(this.list.element,n),this.delegate.highlightedItemChanged(e,t,i&&i.firstElementChild,n&&n.firstElementChild)}updateSelectedItemARIA(e,t){return!1}selectHighlightedItem(){this.selectItem(this.list.selectedItem())}refreshItem(e){this.list.refreshItem(e)}}}),Cr={cssContent:".widget{padding:25px}.message{font-size:larger;white-space:pre;margin:5px}"};const Tr={devtoolsWasDisconnectedFromThe:"DevTools was disconnected from the page.",oncePageIsReloadedDevtoolsWill:"Once page is reloaded, DevTools will automatically reconnect."},kr=t.i18n.registerUIStrings("ui/legacy/TargetCrashedScreen.ts",Tr),Mr=t.i18n.getLocalizedString.bind(void 0,kr);var Lr=Object.freeze({__proto__:null,TargetCrashedScreen:class extends Nt{hideCallback;constructor(e){super(!0),this.registerRequiredCSS(Cr),this.contentElement.createChild("div","message").textContent=Mr(Tr.devtoolsWasDisconnectedFromThe),this.contentElement.createChild("div","message").textContent=Mr(Tr.oncePageIsReloadedDevtoolsWill),this.hideCallback=e}willHide(){this.hideCallback.call(null)}}});var Pr=Object.freeze({__proto__:null,ThrottledWidget:class extends Nt{updateThrottler;updateWhenVisible;constructor(t,i){super(t),this.updateThrottler=new e.Throttler.Throttler(void 0===i?100:i),this.updateWhenVisible=!1}doUpdate(){return Promise.resolve()}update(){this.updateWhenVisible=!this.isShowing(),this.updateWhenVisible||this.updateThrottler.schedule((()=>this.isShowing()?this.doUpdate():(this.updateWhenVisible=!0,Promise.resolve())))}wasShown(){super.wasShown(),this.updateWhenVisible&&this.update()}}}),Dr={cssContent:':host{flex:1 1 auto;padding:2px 0 0}.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow){min-width:100%;display:inline-block}.tree-outline{padding:0 0 4px 4px;margin:0;z-index:0;position:relative}.tree-outline:focus-visible{box-shadow:0 0 0 2px var(--sys-color-state-focus-ring) inset}.tree-outline li .selection{display:none;z-index:-1;margin-left:-10000px}.tree-outline:not(.hide-selection-when-blurred) li.selected{color:var(--sys-color-on-surface-subtle)}.tree-outline li:hover .selection{display:block;background-color:var(--sys-color-state-hover-on-subtle)}.tree-outline:not(.hide-selection-when-blurred) li.selected .selection{display:block;background-color:var(--sys-color-neutral-container)}.tree-outline:not(.hide-selection-when-blurred) li.elements-drag-over .selection{display:block;margin-top:-2px;border-top:2px solid;border-top-color:var(--sys-color-tonal-container)}.tree-outline:not(.hide-selection-when-blurred) li.hovered:not(.selected) .selection{display:block;left:3px;right:3px;background-color:var(--sys-color-state-hover-on-subtle);border-radius:5px}.tree-outline:not(.hide-selection-when-blurred) li.in-clipboard .highlight{outline:1px dotted var(--sys-color-neutral-outline)}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection{background-color:var(--sys-color-tonal-container)}ol.tree-outline,\n.tree-outline ol{list-style-type:none}.tree-outline ol{padding-left:12px}.tree-outline li{text-overflow:ellipsis;white-space:nowrap;position:relative;display:flex;align-items:center;min-height:16px}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus{color:var(--sys-color-on-tonal-container);& ::selection{background-color:var(--sys-color-state-focus-select)}& *:not(devtools-icon){color:inherit}}.tree-outline li .icons-container{align-self:center;display:flex;align-items:center}.tree-outline li .leading-icons{margin-right:4px}.tree-outline li .trailing-icons{margin-left:4px}.tree-outline li::before{user-select:none;mask-image:var(--image-file-triangle-right);background-color:var(--icon-default);content:"\\A0\\A0";text-shadow:none;margin-right:-2px;height:14px;width:14px;transition:transform 200ms}.tree-outline li:not(.parent)::before{background-color:transparent}.tree-outline li.parent.expanded::before{transform:rotate(90deg)}.tree-outline ol.children{display:none}.tree-outline ol.children.expanded{display:block}.tree-outline.tree-outline-dense li{margin-top:1px;min-height:12px}.tree-outline.tree-outline-dense li.parent{margin-top:0}.tree-outline.tree-outline-dense li.parent::before{top:0}.tree-outline.tree-outline-dense ol{padding-left:10px}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:var(--sys-color-state-focus-highlight);border-radius:2px}.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow) .tree-outline.hide-selection-when-blurred .selected:focus-visible{width:fit-content;padding-right:3px}@media (forced-colors: active){.tree-outline-disclosure li.parent::before,\n  .tree-outline:not(.hide-selection-when-blurred) li.parent:not(.selected)::before{forced-color-adjust:none;background-color:ButtonText}.tree-outline li devtools-icon{forced-color-adjust:none;color:ButtonText}.tree-outline-disclosure li.parent:hover:not(.selected)::before,\n  .tree-outline:not(.hide-selection-when-blurred) li.parent:hover:not(.selected)::before{background-color:ButtonText}.tree-outline:not(.hide-selection-when-blurred) li.selected .selection{forced-color-adjust:none;background-color:ButtonText}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible{forced-color-adjust:none;background-color:Highlight}ol.tree-outline:not(.hide-selection-when-blurred) li.parent.selected::before,\n  ol.tree-outline:not(.hide-selection-when-blurred) li.parent.selected:focus::before,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible::before{background-color:HighlightText}.tree-outline li:not(.parent)::before,\n  .tree-outline li:not(.parent):hover::before,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible:not(.parent)::before{forced-color-adjust:none;background-color:transparent}.tree-outline:not(.hide-selection-when-blurred) devtools-icon,\n  .tree-outline.hide-selection-when-blurred devtools-icon{color:ButtonText}.tree-outline li.selected devtools-icon,\n  .tree-outline li.selected:focus devtools-icon{color:HighlightText!important}ol.tree-outline:not(.hide-selection-when-blurred) li.selected,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected:focus,\n  .tree-outline:not(.hide-selection-when-blurred) li:focus-visible .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected:focus .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected span,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible span{forced-color-adjust:none;color:HighlightText}.tree-outline:not(.hide-selection-when-blurred) li.selected:focus-visible devtools-adorner,\n  .tree-outline.hide-selection-when-blurred li.selected:focus-visible devtools-adorner{--override-adorner-background-color:Highlight;--override-adorner-border-color:HighlightText}}'};const Ar=new WeakMap;var Rr;!function(e){e.ElementAttached="ElementAttached",e.ElementsDetached="ElementsDetached",e.ElementExpanded="ElementExpanded",e.ElementCollapsed="ElementCollapsed",e.ElementSelected="ElementSelected"}(Rr||(Rr={}));class Br extends e.ObjectWrapper.ObjectWrapper{rootElementInternal;renderSelection;selectedTreeElement;expandTreeElementsWhenArrowing;comparator;contentElement;preventTabOrder;showSelectionOnKeyboardFocus;focusable;element;useLightSelectionColor;treeElementToScrollIntoView;centerUponScrollIntoView;constructor(){super(),this.rootElementInternal=this.createRootElement(),this.renderSelection=!1,this.selectedTreeElement=null,this.expandTreeElementsWhenArrowing=!1,this.comparator=null,this.contentElement=this.rootElementInternal.childrenListNode,this.contentElement.addEventListener("keydown",this.treeKeyDown.bind(this),!1),this.preventTabOrder=!1,this.showSelectionOnKeyboardFocus=!1,this.focusable=!0,this.setFocusable(!0),this.element=this.contentElement,this.element.setAttribute("jslog",`${o.tree()}`),V(this.element),this.useLightSelectionColor=!1,this.treeElementToScrollIntoView=null,this.centerUponScrollIntoView=!1}setShowSelectionOnKeyboardFocus(e,t){this.contentElement.classList.toggle("hide-selection-when-blurred",e),this.preventTabOrder=Boolean(t),this.focusable&&(this.contentElement.tabIndex=Boolean(t)?-1:0),this.showSelectionOnKeyboardFocus=e}createRootElement(){const e=new Fr;return e.treeOutline=this,e.root=!0,e.selectable=!1,e.expanded=!0,e.childrenListNode.classList.remove("children"),e}rootElement(){return this.rootElementInternal}firstChild(){return this.rootElementInternal.firstChild()}lastDescendent(){let e=this.rootElementInternal.lastChild();for(;e&&e.expanded&&e.childCount();)e=e.lastChild();return e}appendChild(e,t){this.rootElementInternal.appendChild(e,t)}insertChild(e,t){this.rootElementInternal.insertChild(e,t)}removeChild(e){this.rootElementInternal.removeChild(e)}removeChildren(){this.rootElementInternal.removeChildren()}treeElementFromPoint(e,t){const i=Es(this.contentElement.ownerDocument,e,t);if(!i)return null;const n=xs(i,["ol","li"]);return n&&(Ar.get(n)||Or.get(n))||null}treeElementFromEvent(e){return e?this.treeElementFromPoint(e.pageX,e.pageY):null}setComparator(e){this.comparator=e}setFocusable(e){this.focusable=e,this.updateFocusable()}updateFocusable(){this.focusable?(this.contentElement.tabIndex=this.preventTabOrder||Boolean(this.selectedTreeElement)?-1:0,this.selectedTreeElement&&this.selectedTreeElement.setFocusable(!0)):(this.contentElement.removeAttribute("tabIndex"),this.selectedTreeElement&&this.selectedTreeElement.setFocusable(!1))}focus(){this.selectedTreeElement?this.selectedTreeElement.listItemElement.focus():this.contentElement.focus()}setUseLightSelectionColor(e){this.useLightSelectionColor=e}getUseLightSelectionColor(){return this.useLightSelectionColor}bindTreeElement(e){e.treeOutline&&console.error("Binding element for the second time: "+(new Error).stack),e.treeOutline=this,e.onbind()}unbindTreeElement(e){e.treeOutline||console.error("Unbinding element that was not bound: "+(new Error).stack),e.deselect(),e.onunbind(),e.treeOutline=null}selectPrevious(){let e=this.selectedTreeElement&&this.selectedTreeElement.traversePreviousTreeElement(!0);for(;e&&!e.selectable;)e=e.traversePreviousTreeElement(!this.expandTreeElementsWhenArrowing);return!!e&&(e.select(!1,!0),!0)}selectNext(){let e=this.selectedTreeElement&&this.selectedTreeElement.traverseNextTreeElement(!0);for(;e&&!e.selectable;)e=e.traverseNextTreeElement(!this.expandTreeElementsWhenArrowing);return!!e&&(e.select(!1,!0),!0)}forceSelect(e=!1,t=!0){this.selectedTreeElement&&this.selectedTreeElement.deselect(),this.selectFirst(e,t)}selectFirst(e=!1,t=!0){let i=this.firstChild();for(;i&&!i.selectable;)i=i.traverseNextTreeElement(!0);return!!i&&(i.select(e,t),!0)}selectLast(){let e=this.lastDescendent();for(;e&&!e.selectable;)e=e.traversePreviousTreeElement(!0);return!!e&&(e.select(!1,!0),!0)}treeKeyDown(e){if(e.shiftKey||e.metaKey||e.ctrlKey||On())return;let t=!1;this.selectedTreeElement?"ArrowUp"!==e.key||e.altKey?"ArrowDown"!==e.key||e.altKey?"ArrowLeft"===e.key?t=this.selectedTreeElement.collapseOrAscend(e.altKey):"ArrowRight"===e.key?this.selectedTreeElement.revealed()?t=this.selectedTreeElement.descendOrExpand(e.altKey):(this.selectedTreeElement.reveal(),t=!0):8===e.keyCode||46===e.keyCode?t=this.selectedTreeElement.ondelete():"Enter"===e.key?t=this.selectedTreeElement.onenter():e.keyCode===nt.Space.code?t=this.selectedTreeElement.onspace():"Home"===e.key?t=this.selectFirst():"End"===e.key&&(t=this.selectLast()):t=this.selectNext():t=this.selectPrevious():"ArrowUp"!==e.key||e.altKey?"ArrowDown"!==e.key||e.altKey||(t=this.selectFirst()):t=this.selectLast(),t&&e.consume(!0)}deferredScrollIntoView(e,t){const i=()=>{if(!this.treeElementToScrollIntoView)return;const e=this.treeElementToScrollIntoView.listItemElement.getBoundingClientRect(),t=this.contentElement.getBoundingClientRect();let i=this.element;for(;"visible"===getComputedStyle(i).overflow&&i.parentElement;)i=i.parentElement;const n=i.getBoundingClientRect(),s=n.left-t.left,o=n.top-t.top+this.contentElement.offsetTop;let r=e.left-t.left;r>s&&r<s+n.width?r=s:this.centerUponScrollIntoView&&(r-=n.width/2);let a=e.top-t.top;a>o&&a<o+n.height?a=o:this.centerUponScrollIntoView&&(a-=n.height/2),i.scrollTo(r,a),this.treeElementToScrollIntoView=null};this.treeElementToScrollIntoView||this.element.window().requestAnimationFrame(i),this.treeElementToScrollIntoView=e,this.centerUponScrollIntoView=t}onStartedEditingTitle(e){}}const Or=new WeakMap;class Fr{treeOutline;parent;previousSibling;nextSibling;boundOnFocus;boundOnBlur;listItemNode;titleElement;titleInternal;childrenInternal;childrenListNode;expandLoggable={};hiddenInternal;selectableInternal;expanded;selected;expandable;#r=!0;collapsible;toggleOnClick;button;root;tooltipInternal;leadingIconsElement;trailingIconsElement;selectionElementInternal;disableSelectFocus;constructor(e,t,i){this.treeOutline=null,this.parent=null,this.previousSibling=null,this.nextSibling=null,this.boundOnFocus=this.onFocus.bind(this),this.boundOnBlur=this.onBlur.bind(this),this.listItemNode=document.createElement("li"),this.titleElement=this.listItemNode.createChild("span","tree-element-title"),Or.set(this.listItemNode,this),this.titleInternal="",e&&(this.title=e),this.listItemNode.addEventListener("mousedown",this.handleMouseDown.bind(this),!1),this.listItemNode.addEventListener("click",this.treeElementToggled.bind(this),!1),this.listItemNode.addEventListener("dblclick",this.handleDoubleClick.bind(this),!1),this.listItemNode.setAttribute("jslog",`${o.treeItem().parent("parentTreeItem").context(i)}`),U(this.listItemNode),this.childrenInternal=null,this.childrenListNode=document.createElement("ol"),Ar.set(this.childrenListNode,this),this.childrenListNode.classList.add("children"),W(this.childrenListNode),this.hiddenInternal=!1,this.selectableInternal=!0,this.expanded=!1,this.selected=!1,this.setExpandable(t||!1),this.collapsible=!0,this.toggleOnClick=!1,this.button=null,this.root=!1,this.tooltipInternal="",this.leadingIconsElement=null,this.trailingIconsElement=null,this.selectionElementInternal=null,this.disableSelectFocus=!1}static getTreeElementBylistItemNode(e){return Or.get(e)}hasAncestor(e){if(!e)return!1;let t=this.parent;for(;t;){if(e===t)return!0;t=t.parent}return!1}hasAncestorOrSelf(e){return this===e||this.hasAncestor(e)}isHidden(){if(this.hidden)return!0;let e=this.parent;for(;e;){if(e.hidden)return!0;e=e.parent}return!1}children(){return this.childrenInternal||[]}childCount(){return this.childrenInternal?this.childrenInternal.length:0}firstChild(){return this.childrenInternal?this.childrenInternal[0]:null}lastChild(){return this.childrenInternal?this.childrenInternal[this.childrenInternal.length-1]:null}childAt(e){return this.childrenInternal?this.childrenInternal[e]:null}indexOfChild(e){return this.childrenInternal?this.childrenInternal.indexOf(e):-1}appendChild(e,t){let n;this.childrenInternal||(this.childrenInternal=[]),n=t?i.ArrayUtilities.lowerBound(this.childrenInternal,e,t):this.treeOutline&&this.treeOutline.comparator?i.ArrayUtilities.lowerBound(this.childrenInternal,e,this.treeOutline.comparator):this.childrenInternal.length,this.insertChild(e,n)}insertChild(e,t){if(this.childrenInternal||(this.childrenInternal=[]),!e)throw"child can't be undefined or null";console.assert(!e.parent,"Attempting to insert a child that is already in the tree, reparenting is not supported.");const i=t>0?this.childrenInternal[t-1]:null;i?(i.nextSibling=e,e.previousSibling=i):e.previousSibling=null;const n=this.childrenInternal[t];n?(n.previousSibling=e,e.nextSibling=n):e.nextSibling=null,this.childrenInternal.splice(t,0,e),this.setExpandable(!0),e.parent=this,this.treeOutline&&this.treeOutline.bindTreeElement(e);for(let t=e.firstChild();this.treeOutline&&t;t=t.traverseNextTreeElement(!1,e,!0))this.treeOutline.bindTreeElement(t);e.onattach(),e.ensureSelection(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Rr.ElementAttached,e);const s=e.nextSibling?e.nextSibling.listItemNode:null;this.childrenListNode.insertBefore(e.listItemNode,s),this.childrenListNode.insertBefore(e.childrenListNode,s),e.selected&&e.select(),e.expanded&&e.expand()}removeChildAtIndex(e){if(!this.childrenInternal||e<0||e>=this.childrenInternal.length)throw"childIndex out of range";const t=this.childrenInternal[e];this.childrenInternal.splice(e,1);const i=t.parent;this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(t)&&(t.nextSibling?t.nextSibling.select(!0):t.previousSibling?t.previousSibling.select(!0):i&&i.select(!0)),t.previousSibling&&(t.previousSibling.nextSibling=t.nextSibling),t.nextSibling&&(t.nextSibling.previousSibling=t.previousSibling),t.parent=null,this.treeOutline&&this.treeOutline.unbindTreeElement(t);for(let e=t.firstChild();this.treeOutline&&e;e=e.traverseNextTreeElement(!1,t,!0))this.treeOutline.unbindTreeElement(e);t.detach(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Rr.ElementsDetached)}removeChild(e){if(!e)throw"child can't be undefined or null";if(e.parent!==this)return;const t=this.childrenInternal?this.childrenInternal.indexOf(e):-1;if(-1===t)throw"child not found in this node's children";this.removeChildAtIndex(t)}removeChildren(){if(!this.root&&this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(this)&&this.select(!0),this.childrenInternal)for(const e of this.childrenInternal){e.previousSibling=null,e.nextSibling=null,e.parent=null,this.treeOutline&&this.treeOutline.unbindTreeElement(e);for(let t=e.firstChild();this.treeOutline&&t;t=t.traverseNextTreeElement(!1,e,!0))this.treeOutline.unbindTreeElement(t);e.detach()}this.childrenInternal=[],this.treeOutline&&this.treeOutline.dispatchEventToListeners(Rr.ElementsDetached)}get selectable(){return!this.isHidden()&&this.selectableInternal}set selectable(e){this.selectableInternal=e}get listItemElement(){return this.listItemNode}get childrenListElement(){return this.childrenListNode}get title(){return this.titleInternal}set title(e){this.titleInternal!==e&&(this.titleInternal=e,"string"==typeof e?(this.titleElement.textContent=e,this.tooltip=e):(this.titleElement=e,this.tooltip=""),this.listItemNode.removeChildren(),this.leadingIconsElement&&this.listItemNode.appendChild(this.leadingIconsElement),this.listItemNode.appendChild(this.titleElement),this.trailingIconsElement&&this.listItemNode.appendChild(this.trailingIconsElement),this.ensureSelection())}titleAsText(){return this.titleInternal?"string"==typeof this.titleInternal?this.titleInternal:this.titleInternal.textContent||"":""}startEditingTitle(e){Wo.startEditing(this.titleElement,e),this.treeOutline&&this.treeOutline.onStartedEditingTitle(this)}setLeadingIcons(e){if(this.leadingIconsElement||e.length){this.leadingIconsElement||(this.leadingIconsElement=document.createElement("div"),this.leadingIconsElement.classList.add("leading-icons"),this.leadingIconsElement.classList.add("icons-container"),this.listItemNode.insertBefore(this.leadingIconsElement,this.titleElement),this.ensureSelection()),this.leadingIconsElement.removeChildren();for(const t of e)this.leadingIconsElement.appendChild(t)}}setTrailingIcons(e){if(this.trailingIconsElement||e.length){this.trailingIconsElement||(this.trailingIconsElement=document.createElement("div"),this.trailingIconsElement.classList.add("trailing-icons"),this.trailingIconsElement.classList.add("icons-container"),this.listItemNode.appendChild(this.trailingIconsElement),this.ensureSelection()),this.trailingIconsElement.removeChildren();for(const t of e)this.trailingIconsElement.appendChild(t)}}get tooltip(){return this.tooltipInternal}set tooltip(e){this.tooltipInternal!==e&&(this.tooltipInternal=e,Jt.install(this.listItemNode,e))}isExpandable(){return this.expandable}setExpandable(e){this.expandable!==e&&(this.expandable=e,this.listItemNode.classList.toggle("parent",e),e?(o.registerLoggable(this.expandLoggable,`${o.expand()}`,this.listItemNode),ae(this.listItemNode,!1)):(this.collapse(),le(this.listItemNode)))}isExpandRecursively(){return this.#r}setExpandRecursively(e){this.#r=e}isCollapsible(){return this.collapsible}setCollapsible(e){this.collapsible!==e&&(this.collapsible=e,this.listItemNode.classList.toggle("always-parent",!e),e||this.expand())}get hidden(){return this.hiddenInternal}set hidden(e){if(this.hiddenInternal!==e&&(this.hiddenInternal=e,this.listItemNode.classList.toggle("hidden",e),this.childrenListNode.classList.toggle("hidden",e),e&&this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(this))){const e=this.treeOutline.selectedTreeElement.listItemElement.hasFocus();this.treeOutline.forceSelect(!e,!1)}}invalidateChildren(){this.childrenInternal&&(this.removeChildren(),this.childrenInternal=null)}ensureSelection(){this.treeOutline&&this.treeOutline.renderSelection&&(this.selectionElementInternal||(this.selectionElementInternal=document.createElement("div"),this.selectionElementInternal.classList.add("selection"),this.selectionElementInternal.classList.add("fill")),this.listItemNode.insertBefore(this.selectionElementInternal,this.listItemElement.firstChild))}treeElementToggled(e){const t=e.currentTarget;if(!t||Or.get(t)!==this||t.hasSelection())return;console.assert(Boolean(this.treeOutline));const i=!!this.treeOutline&&this.treeOutline.showSelectionOnKeyboardFocus,n=this.toggleOnClick&&(i||!this.selectable),s=this.isEventWithinDisclosureTriangle(e);(n||s)&&(this.expanded?e.altKey?this.collapseRecursively():this.collapse():e.altKey?this.expandRecursively():this.expand(),o.logClick(this.expandLoggable,e),e.consume())}handleMouseDown(e){const t=e.currentTarget;t&&this.selectable&&Or.get(t)===this&&(this.isEventWithinDisclosureTriangle(e)||this.selectOnMouseDown(e))}handleDoubleClick(e){const t=e.currentTarget;if(!t||Or.get(t)!==this)return;this.ondblclick(e)||this.expandable&&!this.expanded&&this.expand()}detach(){this.listItemNode.remove(),this.childrenListNode.remove()}collapse(){if(!this.expanded||!this.collapsible)return;this.listItemNode.classList.remove("expanded"),this.childrenListNode.classList.remove("expanded"),ae(this.listItemNode,!1),this.expanded=!1,this.oncollapse(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Rr.ElementCollapsed,this);const e=this.treeOutline&&this.treeOutline.selectedTreeElement;e&&e.hasAncestor(this)&&this.select(!0,!0)}collapseRecursively(){let e=this;for(;e;)e.expanded&&e.collapse(),e=e.traverseNextTreeElement(!1,this,!0)}collapseChildren(){if(this.childrenInternal)for(const e of this.childrenInternal)e.collapseRecursively()}expand(){!this.expandable||this.expanded&&this.childrenInternal||(this.expanded=!0,this.populateIfNeeded(),this.listItemNode.classList.add("expanded"),this.childrenListNode.classList.add("expanded"),ae(this.listItemNode,!0),this.treeOutline&&(this.onexpand(),this.treeOutline.dispatchEventToListeners(Rr.ElementExpanded,this)))}async expandRecursively(e){let t=this;const i={depthChange:0};let n=0;(void 0===e||isNaN(e))&&(e=3);do{t.isExpandRecursively()&&(await t.populateIfNeeded(),n<e&&t.expand()),t=t.traverseNextTreeElement(!t.isExpandRecursively(),this,!0,i),n+=i.depthChange}while(null!==t)}collapseOrAscend(e){if(this.expanded&&this.collapsible)return e?this.collapseRecursively():this.collapse(),!0;if(!this.parent||this.parent.root)return!1;if(!this.parent.selectable)return this.parent.collapse(),!0;let t=this.parent;for(;t&&!t.selectable;)t=t.parent;return!!t&&(t.select(!1,!0),!0)}descendOrExpand(e){if(!this.expandable)return!1;if(!this.expanded)return e?this.expandRecursively():this.expand(),!0;let t=this.firstChild();for(;t&&!t.selectable;)t=t.nextSibling;return!!t&&(t.select(!1,!0),!0)}reveal(e){let t=this.parent;for(;t&&!t.root;)t.expanded||t.expand(),t=t.parent;this.treeOutline&&this.treeOutline.deferredScrollIntoView(this,Boolean(e))}revealed(){let e=this.parent;for(;e&&!e.root;){if(!e.expanded)return!1;e=e.parent}return!0}selectOnMouseDown(e){if(this.select(!1,!0)&&e.consume(!0),this.listItemNode.draggable&&this.selectionElementInternal&&this.treeOutline){const e=this.treeOutline.element.getBoundingClientRect().left-this.listItemNode.getBoundingClientRect().left-this.treeOutline.element.scrollLeft;this.selectionElementInternal.style.setProperty("margin-left",e+"px")}}select(e,t){if(e=e||this.disableSelectFocus,!this.treeOutline||!this.selectable||this.selected)return e||this.listItemElement.focus(),!1;const i=this.treeOutline.selectedTreeElement;return this.treeOutline.selectedTreeElement=null,this.treeOutline.rootElementInternal===this?(i&&i.deselect(),e||this.listItemElement.focus(),!1):(this.selected=!0,this.treeOutline.selectedTreeElement=this,this.treeOutline.updateFocusable(),e&&!this.treeOutline.contentElement.hasFocus()||this.listItemElement.focus(),this.listItemNode.classList.add("selected"),ue(this.listItemNode,!0),this.treeOutline.dispatchEventToListeners(Rr.ElementSelected,this),i&&i.deselect(),this.onselect(t))}setFocusable(e){e?(this.listItemNode.setAttribute("tabIndex",this.treeOutline&&this.treeOutline.preventTabOrder?"-1":"0"),this.listItemNode.addEventListener("focus",this.boundOnFocus,!1),this.listItemNode.addEventListener("blur",this.boundOnBlur,!1)):(this.listItemNode.removeAttribute("tabIndex"),this.listItemNode.removeEventListener("focus",this.boundOnFocus,!1),this.listItemNode.removeEventListener("blur",this.boundOnBlur,!1))}onFocus(){this.treeOutline&&!this.treeOutline.getUseLightSelectionColor()&&(this.treeOutline.contentElement.classList.contains("hide-selection-when-blurred")||this.listItemNode.classList.add("force-white-icons"))}onBlur(){this.treeOutline&&!this.treeOutline.getUseLightSelectionColor()&&(this.treeOutline.contentElement.classList.contains("hide-selection-when-blurred")||this.listItemNode.classList.remove("force-white-icons"))}revealAndSelect(e){this.reveal(!0),this.select(e)}deselect(){const e=this.listItemNode.hasFocus();this.selected=!1,this.listItemNode.classList.remove("selected"),me(this.listItemNode),this.setFocusable(!1),this.treeOutline&&this.treeOutline.selectedTreeElement===this&&(this.treeOutline.selectedTreeElement=null,this.treeOutline.updateFocusable(),e&&this.treeOutline.focus())}async populateIfNeeded(){this.treeOutline&&this.expandable&&!this.childrenInternal&&(this.childrenInternal=[],await this.onpopulate())}async onpopulate(){}onenter(){return this.expandable&&!this.expanded?(this.expand(),!0):!(!this.collapsible||!this.expanded)&&(this.collapse(),!0)}ondelete(){return!1}onspace(){return!1}onbind(){}onunbind(){}onattach(){}onexpand(){}oncollapse(){}ondblclick(e){return!1}onselect(e){return!1}traverseNextTreeElement(e,t,i,n){i||this.populateIfNeeded(),n&&(n.depthChange=0);let s=e?this.revealed()?this.firstChild():null:this.firstChild();if(s&&(!e||e&&this.expanded))return n&&(n.depthChange=1),s;if(this===t)return null;if(s=e?this.revealed()?this.nextSibling:null:this.nextSibling,s)return s;for(s=this;s&&!s.root&&!(e?s.revealed()&&s.nextSibling:s.nextSibling)&&s.parent!==t;)n&&(n.depthChange-=1),s=s.parent;return!s||s.root?null:e?s.revealed()?s.nextSibling:null:s.nextSibling}traversePreviousTreeElement(e,t){let i=e?this.revealed()?this.previousSibling:null:this.previousSibling;for(!t&&i&&i.populateIfNeeded();i&&(e?i.revealed()&&i.expanded&&i.lastChild():i.lastChild());)t||i.populateIfNeeded(),i=e?i.revealed()&&i.expanded?i.lastChild():null:i.lastChild();return i||(!this.parent||this.parent.root?null:this.parent)}isEventWithinDisclosureTriangle(e){const t=window.getComputedStyle(this.listItemNode).paddingLeft;console.assert(t.endsWith("px"));const i=parseFloat(t),n=this.listItemNode.getBoundingClientRect().left+i;return e.pageX>=n&&e.pageX<=n+10&&this.expandable}setDisableSelectFocus(e){this.disableSelectFocus=e}}o.registerParentProvider("parentTreeItem",(function(e){const t=Fr.getTreeElementBylistItemNode(e),i=t?.parent?.listItemElement;return i?.isConnected&&i||t?.treeOutline?.contentElement}));var zr=Object.freeze({__proto__:null,get Events(){return Rr},TreeOutline:Br,TreeOutlineInShadow:class extends Br{element;shadowRoot;disclosureElement;renderSelection;constructor(){super(),this.contentElement.classList.add("tree-outline"),this.element=document.createElement("div"),this.shadowRoot=d.createShadowRootWithCoreStyles(this.element,{cssFile:Dr,delegatesFocus:void 0}),this.disclosureElement=this.shadowRoot.createChild("div","tree-outline-disclosure"),this.disclosureElement.appendChild(this.contentElement),this.renderSelection=!0}registerRequiredCSS(e){m.ThemeSupport.instance().appendStyle(this.shadowRoot,e)}registerCSSFiles(e){this.shadowRoot.adoptedStyleSheets=this.shadowRoot.adoptedStyleSheets.concat(e)}hideOverflow(){this.disclosureElement.classList.add("tree-outline-disclosure-hide-overflow")}makeDense(){this.contentElement.classList.add("tree-outline-dense")}onStartedEditingTitle(e){const t=this.shadowRoot.getSelection();t&&t.selectAllChildren(e.titleElement)}},treeElementBylistItemNode:Or,TreeElement:Fr});var Wr=Object.freeze({__proto__:null,SimpleView:class extends Nt{#a;#l;constructor(e,t,n){if(super(t),this.#a=e,n&&!i.StringUtilities.isExtendedKebabCase(n))throw new Error(`Invalid view ID '${n}'`);this.#l=n??i.StringUtilities.toKebabCase(e)}viewId(){return this.#l}title(){return this.#a}isCloseable(){return!1}isTransient(){return!1}toolbarItems(){return Promise.resolve([])}widget(){return Promise.resolve(this)}revealView(){return Ci.instance().revealView(this)}disposeView(){}isPreviewFeature(){return!1}}});export{Ce as ARIAUtils,T as ActionRegistration,L as ActionRegistry,v as Context,Te as ContextFlavorListener,Js as ContextMenu,As as Dialog,ft as DockController,to as DropTarget,Eo as EmptyWidget,Po as FilterBar,Do as FilterSuggestionBuilder,Ro as ForwardedInputEventHandler,po as Fragment,Ve as Geometry,Ps as GlassPane,Fo as HistoryInput,yt as Infobar,No as InplaceEditor,Vi as InspectorView,ot as KeyboardShortcut,Qi as ListControl,Ji as ListModel,Ko as ListWidget,$o as Panel,Qo as PopoverHelper,Jo as ProgressIndicator,or as RemoteDebuggingTerminatedScreen,lr as ReportView,kt as ResizerWidget,dr as RootView,vr as SearchableView,Xi as SettingsUI,Ns as ShortcutRegistry,qs as SoftContextMenu,Sr as SoftDropDown,Qt as SplitWidget,rn as SuggestBox,li as TabbedPane,Lr as TargetCrashedScreen,dn as TextPrompt,Pr as ThrottledWidget,kn as Toolbar,ei as Tooltip,zr as TreeOutline,Cs as UIUtils,Wr as View,Bi as ViewManager,Ut as Widget,Dt as XElement,fo as XLink,Ot as XWidget,$t as ZoomManager};
