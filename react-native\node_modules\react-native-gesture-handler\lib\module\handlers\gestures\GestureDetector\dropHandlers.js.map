{"version": 3, "sources": ["dropHandlers.ts"], "names": ["unregister<PERSON><PERSON><PERSON>", "RNGestureHandlerModule", "scheduleFlushOperations", "dropHandlers", "preparedGesture", "handler", "attachedGestures", "dropGestureHandler", "handlerTag", "config", "testId"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,wBAAlC;AACA,OAAOC,sBAAP,MAAmC,iCAAnC;AACA,SAASC,uBAAT,QAAwC,aAAxC;AAGA,OAAO,SAASC,YAAT,CAAsBC,eAAtB,EAA6D;AAClE,OAAK,MAAMC,OAAX,IAAsBD,eAAe,CAACE,gBAAtC,EAAwD;AACtDL,IAAAA,sBAAsB,CAACM,kBAAvB,CAA0CF,OAAO,CAACG,UAAlD;AAEAR,IAAAA,iBAAiB,CAACK,OAAO,CAACG,UAAT,EAAqBH,OAAO,CAACI,MAAR,CAAeC,MAApC,CAAjB;AACD;;AAEDR,EAAAA,uBAAuB;AACxB", "sourcesContent": ["import { unregisterHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { scheduleFlushOperations } from '../../utils';\nimport { AttachedGestureState } from './types';\n\nexport function dropHandlers(preparedGesture: AttachedGestureState) {\n  for (const handler of preparedGesture.attachedGestures) {\n    RNGestureHandlerModule.dropGestureHandler(handler.handlerTag);\n\n    unregisterHandler(handler.handlerTag, handler.config.testId);\n  }\n\n  scheduleFlushOperations();\n}\n"]}