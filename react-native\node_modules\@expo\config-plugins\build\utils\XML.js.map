{"version": 3, "file": "XML.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_os", "_path", "_xml2js", "e", "__esModule", "default", "writeXMLAsync", "options", "xml", "format", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "readXMLAsync", "contents", "readFile", "encoding", "flag", "parser", "<PERSON><PERSON><PERSON>", "manifest", "parseStringPromise", "fallback", "_processAndroidXML", "Array", "isArray", "resources", "string", "$", "translatable", "Error", "JSON", "stringify", "_", "unescapeAndroidString", "parseXMLAsync", "stringTimesN", "n", "char", "join", "indentLevel", "newline", "EOL", "xmlInput", "toString", "builder", "Builder", "headless", "escapeAndroidString", "buildObject", "indentString", "formatted", "regex", "replace", "pad", "split", "map", "line", "trim", "for<PERSON>ach", "indent", "match", "padding", "value", "m"], "sources": ["../../src/utils/XML.ts"], "sourcesContent": ["import fs from 'fs';\nimport { EOL } from 'os';\nimport path from 'path';\nimport { <PERSON><PERSON><PERSON>, Parse<PERSON> } from 'xml2js';\n\nexport type XMLValue = boolean | number | string | null | XMLArray | XMLObject;\n\nexport interface XMLArray extends Array<XMLValue> {}\n\nexport interface XMLObject {\n  [key: string]: XMLValue | undefined;\n}\n\nexport async function writeXMLAsync(options: { path: string; xml: any }): Promise<void> {\n  const xml = format(options.xml);\n  await fs.promises.mkdir(path.dirname(options.path), { recursive: true });\n  await fs.promises.writeFile(options.path, xml);\n}\n\nexport async function readXMLAsync(options: {\n  path: string;\n  fallback?: string | null;\n}): Promise<XMLObject> {\n  let contents: string = '';\n  try {\n    contents = await fs.promises.readFile(options.path, { encoding: 'utf8', flag: 'r' });\n  } catch {\n    // catch and use fallback\n  }\n  const parser = new Parser();\n  const manifest = await parser.parseStringPromise(contents || options.fallback || '');\n\n  return _processAndroidXML(manifest);\n}\n\nexport function _processAndroidXML(manifest: any): XMLObject {\n  // For strings.xml\n  if (Array.isArray(manifest?.resources?.string)) {\n    for (const string of manifest?.resources?.string) {\n      if (string.$.translatable === 'false' || string.$.translatable === false) {\n        continue;\n      }\n\n      if (!('_' in string)) {\n        throw new Error(`Empty string resource not supported: ${JSON.stringify(string)}`);\n      }\n\n      string._ = unescapeAndroidString(string._);\n    }\n  }\n\n  return manifest;\n}\n\nexport async function parseXMLAsync(contents: string): Promise<XMLObject> {\n  const xml = await new Parser().parseStringPromise(contents);\n  return xml;\n}\n\nconst stringTimesN = (n: number, char: string) => Array(n + 1).join(char);\n\nexport function format(manifest: any, { indentLevel = 2, newline = EOL } = {}): string {\n  let xmlInput: string;\n  if (typeof manifest === 'string') {\n    xmlInput = manifest;\n  } else if (manifest.toString) {\n    const builder = new Builder({\n      headless: true,\n    });\n\n    // For strings.xml\n    if (Array.isArray(manifest?.resources?.string)) {\n      for (const string of manifest?.resources?.string) {\n        if (string.$.translatable === 'false' || string.$.translatable === false) {\n          continue;\n        }\n        string._ = escapeAndroidString(string._);\n      }\n    }\n\n    xmlInput = builder.buildObject(manifest);\n\n    return xmlInput;\n  } else {\n    throw new Error(`Invalid XML value passed in: ${manifest}`);\n  }\n  const indentString = stringTimesN(indentLevel, ' ');\n\n  let formatted = '';\n  const regex = /(>)(<)(\\/*)/g;\n  const xml = xmlInput.replace(regex, `$1${newline}$2$3`);\n  let pad = 0;\n  xml\n    .split(/\\r?\\n/)\n    .map((line: string) => line.trim())\n    .forEach((line: string) => {\n      let indent = 0;\n      if (line.match(/.+<\\/\\w[^>]*>$/)) {\n        indent = 0;\n      } else if (line.match(/^<\\/\\w/)) {\n        if (pad !== 0) {\n          pad -= 1;\n        }\n      } else if (line.match(/^<\\w([^>]*[^/])?>.*$/)) {\n        indent = 1;\n      } else {\n        indent = 0;\n      }\n\n      const padding = stringTimesN(pad, indentString);\n      formatted += padding + line + newline;\n      pad += indent;\n    });\n\n  return formatted.trim();\n}\n\n/**\n * Escapes Android string literals, specifically characters `\"`, `'`, `\\`, `\\n`, `\\r`, `\\t`\n *\n * @param value unescaped Android XML string literal.\n */\nexport function escapeAndroidString(value: string): string {\n  value = value.replace(/[\\n\\r\\t'\"@]/g, (m) => {\n    switch (m) {\n      case '\"':\n      case \"'\":\n      case '@':\n        return '\\\\' + m;\n      case '\\n':\n        return '\\\\n';\n      case '\\r':\n        return '\\\\r';\n      case '\\t':\n        return '\\\\t';\n      default:\n        throw new Error(`Cannot escape unhandled XML character: ${m}`);\n    }\n  });\n  if (value.match(/(^\\s|\\s$)/)) {\n    value = '\"' + value + '\"';\n  }\n  return value;\n}\n\nexport function unescapeAndroidString(value: string): string {\n  return value.replace(/\\\\(.)/g, '$1');\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyC,SAAAC,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAUlC,eAAeG,aAAaA,CAACC,OAAmC,EAAiB;EACtF,MAAMC,GAAG,GAAGC,MAAM,CAACF,OAAO,CAACC,GAAG,CAAC;EAC/B,MAAME,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACC,OAAO,CAACP,OAAO,CAACM,IAAI,CAAC,EAAE;IAAEE,SAAS,EAAE;EAAK,CAAC,CAAC;EACxE,MAAML,aAAE,CAACC,QAAQ,CAACK,SAAS,CAACT,OAAO,CAACM,IAAI,EAAEL,GAAG,CAAC;AAChD;AAEO,eAAeS,YAAYA,CAACV,OAGlC,EAAsB;EACrB,IAAIW,QAAgB,GAAG,EAAE;EACzB,IAAI;IACFA,QAAQ,GAAG,MAAMR,aAAE,CAACC,QAAQ,CAACQ,QAAQ,CAACZ,OAAO,CAACM,IAAI,EAAE;MAAEO,QAAQ,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAC,CAAC;EACtF,CAAC,CAAC,MAAM;IACN;EAAA;EAEF,MAAMC,MAAM,GAAG,KAAIC,gBAAM,EAAC,CAAC;EAC3B,MAAMC,QAAQ,GAAG,MAAMF,MAAM,CAACG,kBAAkB,CAACP,QAAQ,IAAIX,OAAO,CAACmB,QAAQ,IAAI,EAAE,CAAC;EAEpF,OAAOC,kBAAkB,CAACH,QAAQ,CAAC;AACrC;AAEO,SAASG,kBAAkBA,CAACH,QAAa,EAAa;EAC3D;EACA,IAAII,KAAK,CAACC,OAAO,CAACL,QAAQ,EAAEM,SAAS,EAAEC,MAAM,CAAC,EAAE;IAC9C,KAAK,MAAMA,MAAM,IAAIP,QAAQ,EAAEM,SAAS,EAAEC,MAAM,EAAE;MAChD,IAAIA,MAAM,CAACC,CAAC,CAACC,YAAY,KAAK,OAAO,IAAIF,MAAM,CAACC,CAAC,CAACC,YAAY,KAAK,KAAK,EAAE;QACxE;MACF;MAEA,IAAI,EAAE,GAAG,IAAIF,MAAM,CAAC,EAAE;QACpB,MAAM,IAAIG,KAAK,CAAC,wCAAwCC,IAAI,CAACC,SAAS,CAACL,MAAM,CAAC,EAAE,CAAC;MACnF;MAEAA,MAAM,CAACM,CAAC,GAAGC,qBAAqB,CAACP,MAAM,CAACM,CAAC,CAAC;IAC5C;EACF;EAEA,OAAOb,QAAQ;AACjB;AAEO,eAAee,aAAaA,CAACrB,QAAgB,EAAsB;EACxE,MAAMV,GAAG,GAAG,MAAM,KAAIe,gBAAM,EAAC,CAAC,CAACE,kBAAkB,CAACP,QAAQ,CAAC;EAC3D,OAAOV,GAAG;AACZ;AAEA,MAAMgC,YAAY,GAAGA,CAACC,CAAS,EAAEC,IAAY,KAAKd,KAAK,CAACa,CAAC,GAAG,CAAC,CAAC,CAACE,IAAI,CAACD,IAAI,CAAC;AAElE,SAASjC,MAAMA,CAACe,QAAa,EAAE;EAAEoB,WAAW,GAAG,CAAC;EAAEC,OAAO,GAAGC;AAAI,CAAC,GAAG,CAAC,CAAC,EAAU;EACrF,IAAIC,QAAgB;EACpB,IAAI,OAAOvB,QAAQ,KAAK,QAAQ,EAAE;IAChCuB,QAAQ,GAAGvB,QAAQ;EACrB,CAAC,MAAM,IAAIA,QAAQ,CAACwB,QAAQ,EAAE;IAC5B,MAAMC,OAAO,GAAG,KAAIC,iBAAO,EAAC;MAC1BC,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,IAAIvB,KAAK,CAACC,OAAO,CAACL,QAAQ,EAAEM,SAAS,EAAEC,MAAM,CAAC,EAAE;MAC9C,KAAK,MAAMA,MAAM,IAAIP,QAAQ,EAAEM,SAAS,EAAEC,MAAM,EAAE;QAChD,IAAIA,MAAM,CAACC,CAAC,CAACC,YAAY,KAAK,OAAO,IAAIF,MAAM,CAACC,CAAC,CAACC,YAAY,KAAK,KAAK,EAAE;UACxE;QACF;QACAF,MAAM,CAACM,CAAC,GAAGe,mBAAmB,CAACrB,MAAM,CAACM,CAAC,CAAC;MAC1C;IACF;IAEAU,QAAQ,GAAGE,OAAO,CAACI,WAAW,CAAC7B,QAAQ,CAAC;IAExC,OAAOuB,QAAQ;EACjB,CAAC,MAAM;IACL,MAAM,IAAIb,KAAK,CAAC,gCAAgCV,QAAQ,EAAE,CAAC;EAC7D;EACA,MAAM8B,YAAY,GAAGd,YAAY,CAACI,WAAW,EAAE,GAAG,CAAC;EAEnD,IAAIW,SAAS,GAAG,EAAE;EAClB,MAAMC,KAAK,GAAG,cAAc;EAC5B,MAAMhD,GAAG,GAAGuC,QAAQ,CAACU,OAAO,CAACD,KAAK,EAAE,KAAKX,OAAO,MAAM,CAAC;EACvD,IAAIa,GAAG,GAAG,CAAC;EACXlD,GAAG,CACAmD,KAAK,CAAC,OAAO,CAAC,CACdC,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAClCC,OAAO,CAAEF,IAAY,IAAK;IACzB,IAAIG,MAAM,GAAG,CAAC;IACd,IAAIH,IAAI,CAACI,KAAK,CAAC,gBAAgB,CAAC,EAAE;MAChCD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAIH,IAAI,CAACI,KAAK,CAAC,QAAQ,CAAC,EAAE;MAC/B,IAAIP,GAAG,KAAK,CAAC,EAAE;QACbA,GAAG,IAAI,CAAC;MACV;IACF,CAAC,MAAM,IAAIG,IAAI,CAACI,KAAK,CAAC,sBAAsB,CAAC,EAAE;MAC7CD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,CAAC;IACZ;IAEA,MAAME,OAAO,GAAG1B,YAAY,CAACkB,GAAG,EAAEJ,YAAY,CAAC;IAC/CC,SAAS,IAAIW,OAAO,GAAGL,IAAI,GAAGhB,OAAO;IACrCa,GAAG,IAAIM,MAAM;EACf,CAAC,CAAC;EAEJ,OAAOT,SAAS,CAACO,IAAI,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASV,mBAAmBA,CAACe,KAAa,EAAU;EACzDA,KAAK,GAAGA,KAAK,CAACV,OAAO,CAAC,cAAc,EAAGW,CAAC,IAAK;IAC3C,QAAQA,CAAC;MACP,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,IAAI,GAAGA,CAAC;MACjB,KAAK,IAAI;QACP,OAAO,KAAK;MACd,KAAK,IAAI;QACP,OAAO,KAAK;MACd,KAAK,IAAI;QACP,OAAO,KAAK;MACd;QACE,MAAM,IAAIlC,KAAK,CAAC,0CAA0CkC,CAAC,EAAE,CAAC;IAClE;EACF,CAAC,CAAC;EACF,IAAID,KAAK,CAACF,KAAK,CAAC,WAAW,CAAC,EAAE;IAC5BE,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG;EAC3B;EACA,OAAOA,KAAK;AACd;AAEO,SAAS7B,qBAAqBA,CAAC6B,KAAa,EAAU;EAC3D,OAAOA,KAAK,CAACV,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;AACtC", "ignoreList": []}