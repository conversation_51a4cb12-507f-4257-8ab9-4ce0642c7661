import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import*as n from"../../models/text_utils/text_utils.js";import*as a from"../../ui/legacy/components/utils/utils.js";import*as r from"../../ui/legacy/legacy.js";import*as i from"../../core/host/host.js";import*as h from"../../models/workspace/workspace.js";import*as o from"../../ui/visual_logging/visual_logging.js";const c=new CSSStyleSheet;c.replaceSync(":host{padding:0;margin:0;overflow-y:auto}.tree-outline{padding:0}.tree-outline ol{padding:0}.tree-outline li{height:16px}li.search-result{cursor:pointer;font-size:12px;margin-top:8px;padding:2px 0 2px 4px;word-wrap:normal;white-space:pre}li.search-result:hover{background-color:var(--sys-color-state-hover-on-subtle)}li.search-result .search-result-file-name{color:var(--sys-color-on-surface);flex:1 1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}li.search-result .search-result-matches-count{color:var(--sys-color-token-subtle);margin:0 8px}li.search-result.expanded .search-result-matches-count{display:none}li.show-more-matches{color:var(--sys-color-on-surface);cursor:pointer;margin:8px 0 0 -4px}li.show-more-matches:hover{text-decoration:underline}li.search-match{margin:2px 0;word-wrap:normal;white-space:pre}li.search-match.selected:focus-visible{background:var(--sys-color-tonal-container)}li.search-match::before{display:none}li.search-match .search-match-line-number{color:var(--sys-color-token-subtle);text-align:right;vertical-align:top;word-break:normal;padding:2px 4px 2px 6px;margin-right:5px}.tree-outline .devtools-link{text-decoration:none;display:block;flex:auto}li.search-match .search-match-content{color:var(--sys-color-on-surface)}ol.children.expanded{padding-bottom:4px}.search-match-link{overflow:hidden;text-overflow:ellipsis;margin-left:9px}.search-result-qualifier{color:var(--sys-color-token-subtle)}.search-result-dash{color:var(--sys-color-surface-variant);margin:0 4px}\n/*# sourceURL=searchResultsPane.css */\n");const l={matchesCountS:"Matches Count {PH1}",lineS:"Line {PH1}",showDMore:"Show {PH1} more"},d=t.i18n.registerUIStrings("panels/search/SearchResultsPane.ts",l),u=t.i18n.getLocalizedString.bind(void 0,d);class g extends r.Widget.VBox{searchConfig;searchResults;treeElements;treeOutline;matchesExpandedCount;constructor(e){super(!0),this.searchConfig=e,this.searchResults=[],this.treeElements=[],this.treeOutline=new r.TreeOutline.TreeOutlineInShadow,this.treeOutline.hideOverflow(),this.contentElement.appendChild(this.treeOutline.element),this.matchesExpandedCount=0}addSearchResult(e){this.searchResults.push(e),this.addTreeElement(e)}showAllMatches(){this.treeElements.forEach((e=>{e.expand(),e.showAllMatches()}))}collapseAllResults(){this.treeElements.forEach((e=>{e.collapse()}))}addTreeElement(e){const t=new m(this.searchConfig,e);this.treeOutline.appendChild(t),this.treeOutline.selectedTreeElement||t.select(!0,!0),this.matchesExpandedCount<p&&t.expand(),this.matchesExpandedCount+=e.matchesCount(),this.treeElements.push(t)}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([c])}}const p=200;class m extends r.TreeOutline.TreeElement{searchConfig;searchResult;initialized;toggleOnClick;constructor(e,t){super("",!0),this.searchConfig=e,this.searchResult=t,this.initialized=!1,this.toggleOnClick=!0}onexpand(){this.initialized||(this.updateMatchesUI(),this.initialized=!0)}showAllMatches(){this.removeChildren(),this.appendSearchMatches(0,this.searchResult.matchesCount())}updateMatchesUI(){this.removeChildren();const e=Math.min(this.searchResult.matchesCount(),20);e<this.searchResult.matchesCount()?(this.appendSearchMatches(0,e-1),this.appendShowMoreMatchesElement(e-1)):this.appendSearchMatches(0,e)}onattach(){this.updateSearchMatches()}updateSearchMatches(){this.listItemElement.classList.add("search-result");const e=s(this.searchResult.label(),"search-result-file-name");e.appendChild(s("—","search-result-dash")),e.appendChild(s(this.searchResult.description(),"search-result-qualifier")),this.tooltip=this.searchResult.description(),this.listItemElement.appendChild(e);const t=document.createElement("span");function s(e,t){const s=document.createElement("span");return s.className=t,s.textContent=e,s}t.className="search-result-matches-count",t.textContent=`${this.searchResult.matchesCount()}`,r.ARIAUtils.setLabel(t,u(l.matchesCountS,{PH1:this.searchResult.matchesCount()})),this.listItemElement.appendChild(t),this.expanded&&this.updateMatchesUI()}appendSearchMatches(t,i){const h=this.searchResult,o=this.searchConfig.queries(),c=[];for(let e=0;e<o.length;++e)c.push(s.StringUtilities.createSearchRegex(o[e],!this.searchConfig.ignoreCase(),this.searchConfig.isRegex()));for(let s=t;s<i;++s){let t=h.matchLineContent(s),i=[];const o=h.matchColumn(s),d=h.matchLength(s);if(void 0!==o&&void 0!==d){const{matchRange:e,lineSegment:s}=f(t,new n.TextRange.SourceRange(o,d));t=s,i=[e]}else{t=t.trim();for(let e=0;e<c.length;++e)i=i.concat(this.regexMatchRanges(t,c[e]));({lineSegment:t,matchRanges:i}=C(t,i))}const g=a.Linkifier.Linkifier.linkifyRevealable(h.matchRevealable(s),"");g.classList.add("search-match-link"),g.tabIndex=0;const p=document.createElement("span");p.classList.add("search-match-line-number");const m=h.matchLabel(s);p.textContent=m,"number"!=typeof m||isNaN(m)?r.ARIAUtils.setLabel(p,m):r.ARIAUtils.setLabel(p,u(l.lineS,{PH1:m})),g.appendChild(p);const S=this.createContentSpan(t,i);g.appendChild(S);const x=new r.TreeOutline.TreeElement;this.appendChild(x),x.listItemElement.className="search-match",x.listItemElement.appendChild(g),x.listItemElement.addEventListener("keydown",(t=>{"Enter"===t.key&&(t.consume(!0),e.Revealer.reveal(h.matchRevealable(s)))})),x.tooltip=t}}appendShowMoreMatchesElement(e){const t=this.searchResult.matchesCount()-e,s=u(l.showDMore,{PH1:t}),n=new r.TreeOutline.TreeElement(s);this.appendChild(n),n.listItemElement.classList.add("show-more-matches"),n.onselect=this.showMoreMatchesElementSelected.bind(this,n,e)}createContentSpan(e,t){const s=document.createElement("span");return s.className="search-match-content",s.textContent=e,r.ARIAUtils.setLabel(s,`${e} line`),r.UIUtils.highlightRangesWithStyleClass(s,t,"highlighted-search-result"),s}regexMatchRanges(e,t){let s;t.lastIndex=0;const a=[];for(;t.lastIndex<e.length&&(s=t.exec(e));)a.push(new n.TextRange.SourceRange(s.index,s[0].length));return a}showMoreMatchesElementSelected(e,t){return this.removeChild(e),this.appendSearchMatches(t,this.searchResult.matchesCount()),!1}}const S={prefixLength:25,maxLength:1e3};function f(e,t,s=S){const a={...S,...s},r=e.trimStart(),i=e.length-r.length,h=Math.min(t.offset,i),o=Math.max(h,t.offset-a.prefixLength),c=Math.min(e.length,o+a.maxLength),l=o>h?"…":"",d=l+e.substring(o,c),u=t.offset-o+l.length,g=Math.min(t.length,d.length-u);return{lineSegment:d,matchRange:new n.TextRange.SourceRange(u,g)}}function C(e,t){let s=0,a=t;a.length>0&&a[0].offset>20&&(s=15);let r=e.substring(s,1e3+s);return s&&(a=a.map((e=>new n.TextRange.SourceRange(e.offset-s+1,e.length))),r="…"+r),{lineSegment:r,matchRanges:a}}var x=Object.freeze({__proto__:null,SearchResultsPane:g,matchesExpandedByDefault:p,matchesShownAtOnce:20,SearchResultsTreeElement:m,lineSegmentForMatch:f}),w=Object.freeze({__proto__:null});const R=new CSSStyleSheet;R.replaceSync(".search-drawer-header{align-items:center;flex-shrink:0;overflow:hidden}.search-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.search-toolbar-summary{background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);padding-left:5px;flex:0 0 19px;display:flex;padding-right:5px}.search-toolbar-summary .search-message{padding-top:2px;padding-left:1ex;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.search-view .search-results{overflow-y:auto;display:flex;flex:auto}.search-view .search-results > div{flex:auto}\n/*# sourceURL=searchView.css */\n");const E={search:"Search",searchQuery:"Search Query",matchCase:"Match Case",useRegularExpression:"Use Regular Expression",refresh:"Refresh",clear:"Clear",indexing:"Indexing…",searching:"Searching…",indexingInterrupted:"Indexing interrupted.",foundMatchingLineInFile:"Found 1 matching line in 1 file.",foundDMatchingLinesInFile:"Found {PH1} matching lines in 1 file.",foundDMatchingLinesInDFiles:"Found {PH1} matching lines in {PH2} files.",noMatchesFound:"No matches found.",searchFinished:"Search finished.",searchInterrupted:"Search interrupted."},I=t.i18n.registerUIStrings("panels/search/SearchView.ts",E),b=t.i18n.getLocalizedString.bind(void 0,I);class v extends r.Widget.VBox{focusOnShow;isIndexing;searchId;searchMatchesCount;searchResultsCount;nonEmptySearchResultsCount;searchingView;notFoundView;searchConfig;pendingSearchConfig;searchResultsPane;progressIndicator;visiblePane;searchPanelElement;searchResultsElement;search;matchCaseButton;regexButton;searchMessageElement;searchProgressPlaceholderElement;searchResultsMessageElement;advancedSearchConfig;searchScope;#e;#t=[];constructor(t,s){super(!0),this.setMinimumSize(0,40),this.focusOnShow=!1,this.isIndexing=!1,this.searchId=1,this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0,this.searchingView=null,this.notFoundView=null,this.searchConfig=null,this.pendingSearchConfig=null,this.searchResultsPane=null,this.progressIndicator=null,this.visiblePane=null,this.#e=s,this.element.setAttribute("jslog",`${o.panel("search").track({resize:!0})}`),this.contentElement.classList.add("search-view"),this.contentElement.addEventListener("keydown",(e=>{this.onKeyDownOnPanel(e)})),this.searchPanelElement=this.contentElement.createChild("div","search-drawer-header"),this.searchResultsElement=this.contentElement.createChild("div"),this.searchResultsElement.className="search-results";const n=document.createElement("div");n.classList.add("toolbar-item-search"),this.search=r.HistoryInput.HistoryInput.create(),this.search.addEventListener("keydown",(e=>{this.onKeyDown(e)})),this.search.setAttribute("jslog",`${o.textField().track({keydown:!0})}`),n.appendChild(this.search),this.search.placeholder=b(E.search),this.search.setAttribute("type","search"),this.search.setAttribute("results","0"),this.search.setAttribute("size","100"),this.search.classList.add("custom-search-input"),r.ARIAUtils.setLabel(this.search,b(E.searchQuery));const a=new r.Toolbar.ToolbarItem(n),i=new r.Toolbar.Toolbar("search-toolbar",this.searchPanelElement);i.element.setAttribute("jslog",`${o.toolbar()}`),this.matchCaseButton=v.appendToolbarToggle(i,"Aa",b(E.matchCase),"match-case"),this.regexButton=v.appendToolbarToggle(i,".*",b(E.useRegularExpression),"use-regex"),i.appendToolbarItem(a);const c=new r.Toolbar.ToolbarButton(b(E.refresh),"refresh",void 0,"search.refresh"),l=new r.Toolbar.ToolbarButton(b(E.clear),"clear",void 0,"search.clear");i.appendToolbarItem(c),i.appendToolbarItem(l),c.addEventListener("Click",(()=>this.onAction())),l.addEventListener("Click",(()=>{this.resetSearch(),this.onSearchInputClear()}));const d=this.contentElement.createChild("div","search-toolbar-summary");this.searchMessageElement=d.createChild("div","search-message"),this.searchProgressPlaceholderElement=d.createChild("div","flex-centered"),this.searchResultsMessageElement=d.createChild("div","search-message"),this.advancedSearchConfig=e.Settings.Settings.instance().createLocalSetting(t+"-search-config",new h.SearchConfig.SearchConfig("",!0,!1).toPlainObject()),this.load(),this.searchScope=null}static appendToolbarToggle(e,t,s,n){const a=new r.Toolbar.ToolbarToggle(s,void 0,void 0,n);return a.setText(t),a.addEventListener("Click",(()=>a.setToggled(!a.toggled()))),e.appendToolbarItem(a),a}buildSearchConfig(){return new h.SearchConfig.SearchConfig(this.search.value,!this.matchCaseButton.toggled(),this.regexButton.toggled())}toggle(e,t){this.search.value=e,this.isShowing()?this.focus():this.focusOnShow=!0,this.initScope(),t?this.onAction():this.startIndexing()}createScope(){throw new Error("Not implemented")}initScope(){this.searchScope=this.createScope()}wasShown(){this.focusOnShow&&(this.focus(),this.focusOnShow=!1),this.registerCSSFiles([R])}onIndexingFinished(){if(!this.progressIndicator)return;const e=!this.progressIndicator.isCanceled();if(this.progressIndicator.done(),this.progressIndicator=null,this.isIndexing=!1,this.searchMessageElement.textContent=e?"":b(E.indexingInterrupted),e||(this.pendingSearchConfig=null),!this.pendingSearchConfig)return;const t=this.pendingSearchConfig;this.pendingSearchConfig=null,this.innerStartSearch(t)}startIndexing(){this.isIndexing=!0,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new r.ProgressIndicator.ProgressIndicator,this.searchMessageElement.textContent=b(E.indexing),this.progressIndicator.show(this.searchProgressPlaceholderElement),this.searchScope&&this.searchScope.performIndexing(new e.Progress.ProgressProxy(this.progressIndicator,this.onIndexingFinished.bind(this)))}onSearchInputClear(){this.search.value="",this.save(),this.focus()}onSearchResult(e,t){e===this.searchId&&this.progressIndicator&&(this.progressIndicator&&this.progressIndicator.isCanceled()?this.onIndexingFinished():(this.searchResultsPane||(this.searchResultsPane=new g(this.searchConfig),this.showPane(this.searchResultsPane)),this.#t.push(t),this.#e.schedule((async()=>this.#s()))))}#s(){for(const e of this.#t)this.addSearchResult(e),e.matchesCount()&&this.searchResultsPane?.addSearchResult(e);this.#t=[]}onSearchFinished(e,t){e===this.searchId&&this.progressIndicator&&(this.searchResultsPane||this.nothingFound(),this.searchFinished(t),this.searchConfig=null,r.ARIAUtils.alert(this.searchMessageElement.textContent+" "+this.searchResultsMessageElement.textContent))}innerStartSearch(e){this.searchConfig=e,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new r.ProgressIndicator.ProgressIndicator,this.searchStarted(this.progressIndicator),this.searchScope&&this.searchScope.performSearch(e,this.progressIndicator,this.onSearchResult.bind(this,this.searchId),this.onSearchFinished.bind(this,this.searchId))}resetSearch(){this.stopSearch(),this.showPane(null),this.searchResultsPane=null,this.searchMessageElement.textContent="",this.searchResultsMessageElement.textContent=""}stopSearch(){this.progressIndicator&&!this.isIndexing&&this.progressIndicator.cancel(),this.searchScope&&this.searchScope.stopSearch(),this.searchConfig=null}searchStarted(e){this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0,this.searchingView||(this.searchingView=new r.EmptyWidget.EmptyWidget(b(E.searching))),this.showPane(this.searchingView),this.searchMessageElement.textContent=b(E.searching),e.show(this.searchProgressPlaceholderElement),this.updateSearchResultsMessage()}updateSearchResultsMessage(){this.searchMatchesCount&&this.searchResultsCount?1===this.searchMatchesCount&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=b(E.foundMatchingLineInFile):this.searchMatchesCount>1&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=b(E.foundDMatchingLinesInFile,{PH1:this.searchMatchesCount}):this.searchResultsMessageElement.textContent=b(E.foundDMatchingLinesInDFiles,{PH1:this.searchMatchesCount,PH2:this.nonEmptySearchResultsCount}):this.searchResultsMessageElement.textContent=""}showPane(e){this.visiblePane&&this.visiblePane.detach(),e&&e.show(this.searchResultsElement),this.visiblePane=e}nothingFound(){this.notFoundView||(this.notFoundView=new r.EmptyWidget.EmptyWidget(b(E.noMatchesFound))),this.showPane(this.notFoundView),this.searchResultsMessageElement.textContent=b(E.noMatchesFound)}addSearchResult(e){const t=e.matchesCount();this.searchMatchesCount+=t,this.searchResultsCount++,t&&this.nonEmptySearchResultsCount++,this.updateSearchResultsMessage()}searchFinished(e){this.searchMessageElement.textContent=b(e?E.searchFinished:E.searchInterrupted)}focus(){this.search.focus(),this.search.select()}willHide(){this.stopSearch()}onKeyDown(e){if(this.save(),e.keyCode===r.KeyboardShortcut.Keys.Enter.code)this.onAction()}onKeyDownOnPanel(e){const t=i.Platform.isMac(),s=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketRight"===e.code,n=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketRight"===e.code,a=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketLeft"===e.code,r=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketLeft"===e.code;s||n?this.searchResultsPane?.showAllMatches():(a||r)&&this.searchResultsPane?.collapseAllResults()}save(){this.advancedSearchConfig.set(this.buildSearchConfig().toPlainObject())}load(){const e=h.SearchConfig.SearchConfig.fromPlainObject(this.advancedSearchConfig.get());this.search.value=e.query(),this.matchCaseButton.setToggled(!e.ignoreCase()),this.regexButton.setToggled(e.isRegex())}onAction(){const e=this.buildSearchConfig();e.query()&&e.query().length&&(this.resetSearch(),++this.searchId,this.initScope(),this.isIndexing||this.startIndexing(),this.pendingSearchConfig=e)}get throttlerForTest(){return this.#e}}var y=Object.freeze({__proto__:null,SearchView:v});export{x as SearchResultsPane,w as SearchScope,y as SearchView};
