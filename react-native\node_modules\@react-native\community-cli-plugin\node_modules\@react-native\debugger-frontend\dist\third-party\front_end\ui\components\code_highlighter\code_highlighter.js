import*as t from"../../../third_party/codemirror.next/codemirror.next.js";const e=new CSSStyleSheet;e.replaceSync(".token-variable{color:var(--sys-color-token-variable)}.token-property{color:var(--sys-color-token-property)}.token-type{color:var(--sys-color-token-type)}.token-variable-special{color:var(--sys-color-token-variable-special)}.token-definition{color:var(--sys-color-token-definition)}.token-builtin{color:var(--sys-color-token-builtin)}.token-number{color:var(--sys-color-token-number)}.token-string{color:var(--sys-color-token-string)}.token-string-special{color:var(--sys-color-token-string-special)}.token-atom{color:var(--sys-color-token-atom)}.token-keyword{color:var(--sys-color-token-keyword)}.token-comment{color:var(--sys-color-token-comment)}.token-meta{color:var(--sys-color-token-meta)}.token-invalid{color:var(--sys-color-error)}.token-tag{color:var(--sys-color-token-tag)}.token-attribute{color:var(--sys-color-token-attribute)}.token-attribute-value{color:var(--sys-color-token-attribute-value)}.token-inserted{color:var(--sys-color-token-inserted)}.token-deleted{color:var(--sys-color-token-deleted)}.token-heading{color:var(--sys-color-token-variable-special);font-weight:bold}.token-link{color:var(--sys-color-token-variable-special);text-decoration:underline}.token-strikethrough{text-decoration:strike-through}.token-strong{font-weight:bold}.token-emphasis{font-style:italic}\n/*# sourceURL=codeHighlighter.css */\n");var a=Object.freeze({__proto__:null,default:e});const s=t.tags,r=t.HighlightStyle.define([{tag:s.variableName,class:"token-variable"},{tag:s.definition(s.variableName),class:"token-definition"},{tag:s.propertyName,class:"token-property"},{tag:[s.typeName,s.className,s.namespace,s.macroName],class:"token-type"},{tag:[s.special(s.name),s.constant(s.className)],class:"token-variable-special"},{tag:s.standard(s.variableName),class:"token-builtin"},{tag:[s.number,s.literal,s.unit],class:"token-number"},{tag:s.string,class:"token-string"},{tag:[s.special(s.string),s.regexp,s.escape],class:"token-string-special"},{tag:[s.atom,s.labelName,s.bool],class:"token-atom"},{tag:s.keyword,class:"token-keyword"},{tag:[s.comment,s.quote],class:"token-comment"},{tag:s.meta,class:"token-meta"},{tag:s.invalid,class:"token-invalid"},{tag:s.tagName,class:"token-tag"},{tag:s.attributeName,class:"token-attribute"},{tag:s.attributeValue,class:"token-attribute-value"},{tag:s.inserted,class:"token-inserted"},{tag:s.deleted,class:"token-deleted"},{tag:s.heading,class:"token-heading"},{tag:s.link,class:"token-link"},{tag:s.strikethrough,class:"token-strikethrough"},{tag:s.strong,class:"token-strong"},{tag:s.emphasis,class:"token-emphasis"}]);async function o(e,a){const s=await n(a);let r;return r=s?s.language.parser.parse(e):new t.Tree(t.NodeType.none,[],[],e.length),new c(e,r)}async function n(e){switch(e){case"application/javascript":case"application/ecmascript":case"application/x-ecmascript":case"application/x-javascript":case"text/ecmascript":case"text/javascript1.0":case"text/javascript1.1":case"text/javascript1.2":case"text/javascript1.3":case"text/javascript1.4":case"text/javascript1.5":case"text/jscript":case"text/livescript ":case"text/x-ecmascript":case"text/x-javascript":case"text/javascript":case"text/jsx":return t.javascript.javascript({jsx:!0});case"text/typescript":return t.javascript.javascript({typescript:!0});case"text/typescript-jsx":return t.javascript.javascript({typescript:!0,jsx:!0});case"text/css":return t.css.css();case"text/html":return t.html.html({selfClosingTags:!0});case"application/xml":case"image/svg+xml":return(await t.xml()).xml();case"application/wasm":return(await t.wast()).wast();case"text/x-c++src":return(await t.cpp()).cpp();case"text/x-go":return new t.LanguageSupport(await t.go());case"text/x-java":return(await t.java()).java();case"text/x-kotlin":return new t.LanguageSupport(await t.kotlin());case"application/json":case"application/manifest+json":{const e=t.javascript.javascriptLanguage.configure({top:"SingleExpression"});return new t.LanguageSupport(e)}case"application/x-httpd-php":return(await t.php()).php();case"text/x-python":return(await t.python()).python();case"text/markdown":return(await t.markdown()).markdown();case"text/x-sh":return new t.LanguageSupport(await t.shell());case"text/x-coffeescript":return new t.LanguageSupport(await t.coffeescript());case"text/x-clojure":return new t.LanguageSupport(await t.clojure());case"application/vnd.dart":return new t.LanguageSupport(await t.dart());case"text/x-gss":return new t.LanguageSupport(await t.gss());case"text/x-less":return(await t.less()).less();case"text/x-sass":return(await t.sass()).sass({indented:!0});case"text/x-scala":return new t.LanguageSupport(await t.scala());case"text/x-scss":return(await t.sass()).sass({indented:!1});case"text/x.angular":return(await t.angular()).angular();case"text/x.svelte":return(await t.svelte()).svelte();case"text/x.vue":return(await t.vue()).vue();default:return null}}class c{code;tree;constructor(t,e){this.code=t,this.tree=e}highlight(t){this.highlightRange(0,this.code.length,t)}highlightRange(e,a,s){let o=e;const n=(t,e)=>{t>o&&(s(this.code.slice(o,t),e),o=t)};t.highlightTree(this.tree,r,((t,e,a)=>{n(t,""),n(e,a)}),e,a),n(a,"")}}var i=Object.freeze({__proto__:null,highlightStyle:r,create:o,highlightNode:async function(t,e){const a=t.textContent||"",s=await o(a,e);t.removeChildren(),s.highlight(((e,a)=>{let s=document.createTextNode(e);if(a){const t=document.createElement("span");t.className=a,t.appendChild(s),s=t}t.appendChild(s)}))},languageFromMIME:n,CodeHighlighter:c});export{i as CodeHighlighter,a as Style};
