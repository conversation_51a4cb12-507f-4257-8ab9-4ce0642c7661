{"version": 3, "names": ["MISSING_CONTEXT_ERROR", "ScheduleUpdateContext", "React", "createContext", "scheduleUpdate", "Error", "flushUpdates", "useScheduleUpdate", "callback", "useContext", "useEffect"], "sourceRoot": "../../src", "sources": ["useScheduleUpdate.tsx"], "mappings": ";;;;;;;AAAA;AAA+B;AAAA;AAE/B,MAAMA,qBAAqB,GAAG,mCAAmC;AAE1D,MAAMC,qBAAqB,gBAAGC,KAAK,CAACC,aAAa,CAGrD;EACDC,cAAc,GAAG;IACf,MAAM,IAAIC,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACDM,YAAY,GAAG;IACb,MAAM,IAAID,KAAK,CAACL,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAOe,SAASO,iBAAiB,CAACC,QAAoB,EAAE;EAC9D,MAAM;IAAEJ,cAAc;IAAEE;EAAa,CAAC,GAAGJ,KAAK,CAACO,UAAU,CACvDR,qBAAqB,CACtB;EAEDG,cAAc,CAACI,QAAQ,CAAC;EAExBN,KAAK,CAACQ,SAAS,CAACJ,YAAY,CAAC;AAC/B"}