import*as e from"../../core/i18n/i18n.js";import*as r from"../../ui/legacy/legacy.js";const o={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},s=e.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",o),i=e.i18n.getLazilyComputedLocalizedString.bind(void 0,s);let c;r.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:i(o.developerResources),commandPrompt:i(o.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return c||(c=await import("./developer_resources.js")),c}()).DeveloperResourcesView.DeveloperResourcesView)});
