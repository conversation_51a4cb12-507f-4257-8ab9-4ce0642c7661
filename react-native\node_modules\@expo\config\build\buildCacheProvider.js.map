{"version": 3, "file": "buildCacheProvider.js", "names": [], "sources": ["../src/buildCacheProvider.ts"], "sourcesContent": ["type AndroidRunOptions = {\n  variant?: string;\n  device?: boolean | string;\n  port?: number;\n  bundler?: boolean;\n  install?: boolean;\n  buildCache?: boolean;\n  allArch?: boolean;\n  binary?: string;\n  appId?: string;\n};\n\ntype IosRunOptions = {\n  /** iOS device to target. */\n  device?: string | boolean;\n  /** Dev server port to use, ignored if `bundler` is `false`. */\n  port?: number;\n  /** Xcode scheme to build. */\n  scheme?: string | boolean;\n  /** Xcode configuration to build. Default `Debug` */\n  configuration?: 'Debug' | 'Release';\n  /** Should start the bundler dev server. */\n  bundler?: boolean;\n  /** Should install missing dependencies before building. */\n  install?: boolean;\n  /** Should use derived data for builds. */\n  buildCache?: boolean;\n  /** Path to an existing binary to install on the device. */\n  binary?: string;\n\n  /** Re-bundle JS and assets, then embed in existing app, and install again. */\n  rebundle?: boolean;\n};\n\nexport type RunOptions = AndroidRunOptions | IosRunOptions;\n\nexport type ResolveBuildCacheProps = {\n  projectRoot: string;\n  platform: 'android' | 'ios';\n  runOptions: RunOptions;\n  fingerprintHash: string;\n};\n\n/**\n * @deprecated Use `ResolveBuildCacheProps` instead.\n */\nexport type ResolveRemoteBuildCacheProps = ResolveBuildCacheProps;\n\nexport type UploadBuildCacheProps = {\n  projectRoot: string;\n  buildPath: string;\n  runOptions: RunOptions;\n  fingerprintHash: string;\n  platform: 'android' | 'ios';\n};\n/**\n * @deprecated Use `ResolveBuildCacheProps` instead.\n */\nexport type UploadRemoteBuildCacheProps = UploadBuildCacheProps;\n\nexport type CalculateFingerprintHashProps = {\n  projectRoot: string;\n  platform: 'android' | 'ios';\n  runOptions: RunOptions;\n};\n\nexport type BuildCacheProvider<T = any> = {\n  plugin: BuildCacheProviderPlugin<T>;\n  options: T;\n};\n\nexport type BuildCacheProviderPlugin<T = any> = {\n  calculateFingerprintHash?: (\n    props: CalculateFingerprintHashProps,\n    options: T\n  ) => Promise<string | null>;\n} & (\n  | {\n      resolveBuildCache(props: ResolveBuildCacheProps, options: T): Promise<string | null>;\n      uploadBuildCache(props: UploadBuildCacheProps, options: T): Promise<string | null>;\n    }\n  | {\n      /**\n       * @deprecated Use `resolveBuildCache` instead.\n       */\n      resolveRemoteBuildCache: (\n        props: ResolveRemoteBuildCacheProps,\n        options: T\n      ) => Promise<string | null>;\n      /**\n       * @deprecated Use `uploadBuildCache` instead.\n       */\n      uploadRemoteBuildCache: (\n        props: UploadRemoteBuildCacheProps,\n        options: T\n      ) => Promise<string | null>;\n    }\n);\n"], "mappings": "", "ignoreList": []}