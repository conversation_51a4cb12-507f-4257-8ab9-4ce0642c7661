{"version": 3, "sources": ["../../../../../src/start/doctor/typescript/TypeScriptProjectPrerequisite.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { updateTSConfigAsync } from './updateTSConfig';\nimport * as Log from '../../../log';\nimport { fileExistsAsync } from '../../../utils/dir';\nimport { env } from '../../../utils/env';\nimport { memoize } from '../../../utils/fn';\nimport { everyMatchAsync } from '../../../utils/glob';\nimport { ProjectPrerequisite } from '../Prerequisite';\nimport { ensureDependenciesAsync } from '../dependencies/ensureDependenciesAsync';\n\nconst debug = require('debug')('expo:doctor:typescriptSupport') as typeof console.log;\n\nconst warnDisabled = memoize(() => {\n  Log.warn('Skipping TypeScript setup: EXPO_NO_TYPESCRIPT_SETUP is enabled.');\n});\n\n/** Ensure the project has the required TypeScript support settings. */\nexport class TypeScriptProjectPrerequisite extends ProjectPrerequisite<boolean> {\n  /**\n   * Ensure a project that hasn't explicitly disabled typescript support has all the required packages for running in the browser.\n   *\n   * @returns `true` if the setup finished and no longer needs to be run again.\n   */\n  async assertImplementation(): Promise<boolean> {\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      warnDisabled();\n      return true;\n    }\n    debug('Ensuring TypeScript support is setup');\n\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n\n    // Ensure the project is TypeScript before continuing.\n    const intent = await this._getSetupRequirements();\n    if (!intent) {\n      return false;\n    }\n\n    // Ensure TypeScript packages are installed\n    await this._ensureDependenciesInstalledAsync();\n\n    // Update the config\n    await updateTSConfigAsync({ tsConfigPath });\n\n    return true;\n  }\n\n  async bootstrapAsync(): Promise<void> {\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      warnDisabled();\n      return;\n    }\n    // Ensure TypeScript packages are installed\n    await this._ensureDependenciesInstalledAsync({\n      skipPrompt: true,\n      isProjectMutable: true,\n    });\n\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n\n    // Update the config\n    await updateTSConfigAsync({ tsConfigPath });\n  }\n\n  /** Exposed for testing. */\n  async _getSetupRequirements(): Promise<{\n    /** Indicates that TypeScript support is being bootstrapped. */\n    isBootstrapping: boolean;\n  } | null> {\n    const tsConfigPath = await this._hasTSConfig();\n\n    // Enable TS setup if the project has a `tsconfig.json`\n    if (tsConfigPath) {\n      const content = await fs.readFile(tsConfigPath, { encoding: 'utf8' }).then(\n        (txt) => txt.trim(),\n        // null when the file doesn't exist.\n        () => null\n      );\n      const isBlankConfig = content === '' || content === '{}';\n      return { isBootstrapping: isBlankConfig };\n    }\n    // This is a somewhat heavy check in larger projects.\n    // Test that this is reasonably paced by running expo start in `expo/apps/native-component-list`\n    const typescriptFile = await this._queryFirstTypeScriptFileAsync();\n    if (typescriptFile) {\n      return { isBootstrapping: true };\n    }\n\n    return null;\n  }\n\n  /** Exposed for testing. */\n  async _ensureDependenciesInstalledAsync({\n    exp,\n    skipPrompt,\n    isProjectMutable,\n  }: {\n    exp?: ExpoConfig;\n    skipPrompt?: boolean;\n    isProjectMutable?: boolean;\n  } = {}): Promise<boolean> {\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        exp,\n        skipPrompt,\n        isProjectMutable,\n        installMessage: `It looks like you're trying to use TypeScript but don't have the required dependencies installed.`,\n        warningMessage:\n          \"If you're not using TypeScript, please remove the TypeScript files from your project\",\n        requiredPackages: [\n          // use typescript/package.json to skip node module cache issues when the user installs\n          // the package and attempts to resolve the module in the same process.\n          { file: 'typescript/package.json', pkg: 'typescript', dev: true },\n          { file: '@types/react/package.json', pkg: '@types/react', dev: true },\n        ],\n      });\n    } catch (error) {\n      // Reset the cached check so we can re-run the check if the user re-runs the command by pressing 'w' in the Terminal UI.\n      this.resetAssertion();\n      throw error;\n    }\n  }\n\n  /** Return the first TypeScript file in the project. */\n  async _queryFirstTypeScriptFileAsync(): Promise<null | string> {\n    try {\n      // TODO(Bacon): Use `everyMatch` since a bug causes `anyMatch` to return inaccurate results when used multiple times.\n      const results = await everyMatchAsync('**/*.@(ts|tsx)', {\n        cwd: this.projectRoot,\n        signal: AbortSignal.timeout(5000),\n        ignore: [\n          '**/@(Carthage|Pods|node_modules)/**',\n          '**/*.d.ts',\n          '@(ios|android|web|web-build|dist)/**',\n        ],\n      });\n\n      return results[0] ?? null;\n    } catch (error: any) {\n      if (error.name === 'TimeoutError') {\n        return null;\n      }\n\n      throw error;\n    }\n  }\n\n  async _hasTSConfig(): Promise<string | null> {\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n    if (await fileExistsAsync(tsConfigPath)) {\n      return tsConfigPath;\n    }\n    return null;\n  }\n}\n"], "names": ["TypeScriptProjectPrerequisite", "debug", "require", "warnDisabled", "memoize", "Log", "warn", "ProjectPrerequisite", "assertImplementation", "env", "EXPO_NO_TYPESCRIPT_SETUP", "tsConfigPath", "path", "join", "projectRoot", "intent", "_getSetupRequirements", "_ensureDependenciesInstalledAsync", "updateTSConfigAsync", "bootstrapAsync", "skip<PERSON>rompt", "isProjectMutable", "_hasTSConfig", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "isBlankConfig", "isBootstrapping", "typescriptFile", "_queryFirstTypeScriptFileAsync", "exp", "ensureDependenciesAsync", "installMessage", "warningMessage", "requiredPackages", "file", "pkg", "dev", "error", "resetAssertion", "results", "everyMatchAsync", "cwd", "signal", "AbortSignal", "timeout", "ignore", "name", "fileExistsAsync"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;;gEAnBE;;;;;;;gEACE;;;;;;gCAEmB;6DACf;qBACW;qBACZ;oBACI;sBACQ;8BACI;yCACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,eAAeC,IAAAA,WAAO,EAAC;IAC3BC,KAAIC,IAAI,CAAC;AACX;AAGO,MAAMN,sCAAsCO,iCAAmB;IACpE;;;;GAIC,GACD,MAAMC,uBAAyC;QAC7C,IAAIC,QAAG,CAACC,wBAAwB,EAAE;YAChCP;YACA,OAAO;QACT;QACAF,MAAM;QAEN,MAAMU,eAAeC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QAEjD,sDAAsD;QACtD,MAAMC,SAAS,MAAM,IAAI,CAACC,qBAAqB;QAC/C,IAAI,CAACD,QAAQ;YACX,OAAO;QACT;QAEA,2CAA2C;QAC3C,MAAM,IAAI,CAACE,iCAAiC;QAE5C,oBAAoB;QACpB,MAAMC,IAAAA,mCAAmB,EAAC;YAAEP;QAAa;QAEzC,OAAO;IACT;IAEA,MAAMQ,iBAAgC;QACpC,IAAIV,QAAG,CAACC,wBAAwB,EAAE;YAChCP;YACA;QACF;QACA,2CAA2C;QAC3C,MAAM,IAAI,CAACc,iCAAiC,CAAC;YAC3CG,YAAY;YACZC,kBAAkB;QACpB;QAEA,MAAMV,eAAeC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QAEjD,oBAAoB;QACpB,MAAMI,IAAAA,mCAAmB,EAAC;YAAEP;QAAa;IAC3C;IAEA,yBAAyB,GACzB,MAAMK,wBAGI;QACR,MAAML,eAAe,MAAM,IAAI,CAACW,YAAY;QAE5C,uDAAuD;QACvD,IAAIX,cAAc;YAChB,MAAMY,UAAU,MAAMC,mBAAE,CAACC,QAAQ,CAACd,cAAc;gBAAEe,UAAU;YAAO,GAAGC,IAAI,CACxE,CAACC,MAAQA,IAAIC,IAAI,IACjB,oCAAoC;YACpC,IAAM;YAER,MAAMC,gBAAgBP,YAAY,MAAMA,YAAY;YACpD,OAAO;gBAAEQ,iBAAiBD;YAAc;QAC1C;QACA,qDAAqD;QACrD,gGAAgG;QAChG,MAAME,iBAAiB,MAAM,IAAI,CAACC,8BAA8B;QAChE,IAAID,gBAAgB;YAClB,OAAO;gBAAED,iBAAiB;YAAK;QACjC;QAEA,OAAO;IACT;IAEA,yBAAyB,GACzB,MAAMd,kCAAkC,EACtCiB,GAAG,EACHd,UAAU,EACVC,gBAAgB,EAKjB,GAAG,CAAC,CAAC,EAAoB;QACxB,IAAI;YACF,OAAO,MAAMc,IAAAA,gDAAuB,EAAC,IAAI,CAACrB,WAAW,EAAE;gBACrDoB;gBACAd;gBACAC;gBACAe,gBAAgB,CAAC,iGAAiG,CAAC;gBACnHC,gBACE;gBACFC,kBAAkB;oBAChB,sFAAsF;oBACtF,sEAAsE;oBACtE;wBAAEC,MAAM;wBAA2BC,KAAK;wBAAcC,KAAK;oBAAK;oBAChE;wBAAEF,MAAM;wBAA6BC,KAAK;wBAAgBC,KAAK;oBAAK;iBACrE;YACH;QACF,EAAE,OAAOC,OAAO;YACd,wHAAwH;YACxH,IAAI,CAACC,cAAc;YACnB,MAAMD;QACR;IACF;IAEA,qDAAqD,GACrD,MAAMT,iCAAyD;QAC7D,IAAI;YACF,qHAAqH;YACrH,MAAMW,UAAU,MAAMC,IAAAA,qBAAe,EAAC,kBAAkB;gBACtDC,KAAK,IAAI,CAAChC,WAAW;gBACrBiC,QAAQC,YAAYC,OAAO,CAAC;gBAC5BC,QAAQ;oBACN;oBACA;oBACA;iBACD;YACH;YAEA,OAAON,OAAO,CAAC,EAAE,IAAI;QACvB,EAAE,OAAOF,OAAY;YACnB,IAAIA,MAAMS,IAAI,KAAK,gBAAgB;gBACjC,OAAO;YACT;YAEA,MAAMT;QACR;IACF;IAEA,MAAMpB,eAAuC;QAC3C,MAAMX,eAAeC,eAAI,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QACjD,IAAI,MAAMsC,IAAAA,oBAAe,EAACzC,eAAe;YACvC,OAAOA;QACT;QACA,OAAO;IACT;AACF"}