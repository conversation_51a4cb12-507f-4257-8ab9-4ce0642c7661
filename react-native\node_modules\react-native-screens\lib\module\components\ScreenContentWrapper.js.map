{"version": 3, "names": ["React", "ScreenContentWrapperNativeComponent", "ScreenContentWrapper", "props", "createElement", "_extends", "collapsable"], "sourceRoot": "../../../src", "sources": ["components/ScreenContentWrapper.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,mCAAmC,MAAM,+CAA+C;AAE/F,SAASC,oBAAoBA,CAACC,KAAgB,EAAE;EAC9C,oBAAOH,KAAA,CAAAI,aAAA,CAACH,mCAAmC,EAAAI,QAAA;IAACC,WAAW,EAAE;EAAM,GAAKH,KAAK,CAAG,CAAC;AAC/E;AAEA,eAAeD,oBAAoB"}