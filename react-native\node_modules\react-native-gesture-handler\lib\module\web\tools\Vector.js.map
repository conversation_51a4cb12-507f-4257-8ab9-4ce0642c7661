{"version": 3, "sources": ["Vector.ts"], "names": ["DiagonalDirections", "Directions", "MINIMAL_RECOGNIZABLE_MAGNITUDE", "Vector", "constructor", "x", "y", "_magnitude", "Math", "hypot", "isMagnitudeSufficient", "unitX", "unitY", "fromDirection", "direction", "DirectionToVectorMappings", "get", "fromVelocity", "tracker", "pointerId", "velocity", "getVelocity", "magnitude", "computeSimilarity", "vector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "Map", "LEFT", "RIGHT", "UP", "DOWN", "UP_RIGHT", "DOWN_RIGHT", "UP_LEFT", "DOWN_LEFT"], "mappings": ";;AAAA,SAASA,kBAAT,EAA6BC,UAA7B,QAA+C,kBAA/C;AACA,SAASC,8BAAT,QAA+C,cAA/C;AAGA,eAAe,MAAMC,MAAN,CAAa;AAO1BC,EAAAA,WAAW,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAChC,SAAKD,CAAL,GAASA,CAAT;AACA,SAAKC,CAAL,GAASA,CAAT;AAEA,SAAKC,UAAL,GAAkBC,IAAI,CAACC,KAAL,CAAW,KAAKJ,CAAhB,EAAmB,KAAKC,CAAxB,CAAlB;AACA,UAAMI,qBAAqB,GACzB,KAAKH,UAAL,GAAkBL,8BADpB;AAGA,SAAKS,KAAL,GAAaD,qBAAqB,GAAG,KAAKL,CAAL,GAAS,KAAKE,UAAjB,GAA8B,CAAhE;AACA,SAAKK,KAAL,GAAaF,qBAAqB,GAAG,KAAKJ,CAAL,GAAS,KAAKC,UAAjB,GAA8B,CAAhE;AACD;;AAEmB,SAAbM,aAAa,CAACC,SAAD,EAAqD;AAAA;;AACvE,oCAAOC,yBAAyB,CAACC,GAA1B,CAA8BF,SAA9B,CAAP,yEAAmD,IAAIX,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAnD;AACD;;AAEkB,SAAZc,YAAY,CAACC,OAAD,EAA0BC,SAA1B,EAA6C;AAC9D,UAAMC,QAAQ,GAAGF,OAAO,CAACG,WAAR,CAAoBF,SAApB,CAAjB;AACA,WAAO,IAAIhB,MAAJ,CAAWiB,QAAQ,CAACf,CAApB,EAAuBe,QAAQ,CAACd,CAAhC,CAAP;AACD;;AAEY,MAATgB,SAAS,GAAG;AACd,WAAO,KAAKf,UAAZ;AACD;;AAEDgB,EAAAA,iBAAiB,CAACC,MAAD,EAAiB;AAChC,WAAO,KAAKb,KAAL,GAAaa,MAAM,CAACb,KAApB,GAA4B,KAAKC,KAAL,GAAaY,MAAM,CAACZ,KAAvD;AACD;;AAEDa,EAAAA,SAAS,CAACD,MAAD,EAAiBE,SAAjB,EAAoC;AAC3C,WAAO,KAAKH,iBAAL,CAAuBC,MAAvB,IAAiCE,SAAxC;AACD;;AAtCyB;AAyC5B,MAAMX,yBAAyB,GAAG,IAAIY,GAAJ,CAGhC,CACA,CAAC1B,UAAU,CAAC2B,IAAZ,EAAkB,IAAIzB,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAf,CAAlB,CADA,EAEA,CAACF,UAAU,CAAC4B,KAAZ,EAAmB,IAAI1B,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAnB,CAFA,EAGA,CAACF,UAAU,CAAC6B,EAAZ,EAAgB,IAAI3B,MAAJ,CAAW,CAAX,EAAc,CAAC,CAAf,CAAhB,CAHA,EAIA,CAACF,UAAU,CAAC8B,IAAZ,EAAkB,IAAI5B,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAlB,CAJA,EAMA,CAACH,kBAAkB,CAACgC,QAApB,EAA8B,IAAI7B,MAAJ,CAAW,CAAX,EAAc,CAAC,CAAf,CAA9B,CANA,EAOA,CAACH,kBAAkB,CAACiC,UAApB,EAAgC,IAAI9B,MAAJ,CAAW,CAAX,EAAc,CAAd,CAAhC,CAPA,EAQA,CAACH,kBAAkB,CAACkC,OAApB,EAA6B,IAAI/B,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAC,CAAhB,CAA7B,CARA,EASA,CAACH,kBAAkB,CAACmC,SAApB,EAA+B,IAAIhC,MAAJ,CAAW,CAAC,CAAZ,EAAe,CAAf,CAA/B,CATA,CAHgC,CAAlC", "sourcesContent": ["import { DiagonalDirections, Directions } from '../../Directions';\nimport { MINIMAL_RECOGNIZABLE_MAGNITUDE } from '../constants';\nimport PointerTracker from './PointerTracker';\n\nexport default class Vector {\n  private readonly x;\n  private readonly y;\n  private readonly unitX;\n  private readonly unitY;\n  private readonly _magnitude;\n\n  constructor(x: number, y: number) {\n    this.x = x;\n    this.y = y;\n\n    this._magnitude = Math.hypot(this.x, this.y);\n    const isMagnitudeSufficient =\n      this._magnitude > MINIMAL_RECOGNIZABLE_MAGNITUDE;\n\n    this.unitX = isMagnitudeSufficient ? this.x / this._magnitude : 0;\n    this.unitY = isMagnitudeSufficient ? this.y / this._magnitude : 0;\n  }\n\n  static fromDirection(direction: Directions | DiagonalDirections): Vector {\n    return DirectionToVectorMappings.get(direction) ?? new Vector(0, 0);\n  }\n\n  static fromVelocity(tracker: PointerTracker, pointerId: number) {\n    const velocity = tracker.getVelocity(pointerId);\n    return new Vector(velocity.x, velocity.y);\n  }\n\n  get magnitude() {\n    return this._magnitude;\n  }\n\n  computeSimilarity(vector: Vector) {\n    return this.unitX * vector.unitX + this.unitY * vector.unitY;\n  }\n\n  isSimilar(vector: Vector, threshold: number) {\n    return this.computeSimilarity(vector) > threshold;\n  }\n}\n\nconst DirectionToVectorMappings = new Map<\n  Directions | DiagonalDirections,\n  Vector\n>([\n  [Directions.LEFT, new Vector(-1, 0)],\n  [Directions.RIGHT, new Vector(1, 0)],\n  [Directions.UP, new Vector(0, -1)],\n  [Directions.DOWN, new Vector(0, 1)],\n\n  [DiagonalDirections.UP_RIGHT, new Vector(1, -1)],\n  [DiagonalDirections.DOWN_RIGHT, new Vector(1, 1)],\n  [DiagonalDirections.UP_LEFT, new Vector(-1, -1)],\n  [DiagonalDirections.DOWN_LEFT, new Vector(-1, 1)],\n]);\n"]}