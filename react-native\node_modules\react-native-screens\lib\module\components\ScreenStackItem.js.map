{"version": 3, "names": ["React", "Platform", "StyleSheet", "warnOnce", "DebugContainer", "ScreenStackHeaderConfig", "Screen", "ScreenStack", "RNSScreensRefContext", "ScreenStackItem", "_ref", "ref", "children", "headerConfig", "activityState", "stackPresentation", "contentStyle", "style", "screenId", "rest", "currentScreenRef", "useRef", "screenRefs", "useContext", "useImperativeHandle", "current", "isHeaderInModal", "OS", "hidden", "headerHiddenPreviousRef", "useEffect", "content", "createElement", "Fragment", "styles", "absolute", "container", "internalScreenStyle", "flattenContentStyles", "flatten", "backgroundColor", "_extends", "node", "console", "warn", "currentRefs", "enabled", "isNativeStack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "largeTitle", "absoluteFill", "forwardRef", "create", "flex", "position", "top", "start", "end"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackItem.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,QAGL,cAAc;AACrB,OAAOC,QAAQ,MAAM,WAAW;AAEhC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,oBAAoB,QAAQ,aAAa;AAWlD,SAASC,eAAeA,CAAAC,IAAA,EAWtBC,GAA6B,EAC7B;EAAA,IAXA;IACEC,QAAQ;IACRC,YAAY;IACZC,aAAa;IACbC,iBAAiB;IACjBC,YAAY;IACZC,KAAK;IACLC,QAAQ;IACR,GAAGC;EACE,CAAC,GAAAT,IAAA;EAGR,MAAMU,gBAAgB,GAAGpB,KAAK,CAACqB,MAAM,CAAc,IAAI,CAAC;EACxD,MAAMC,UAAU,GAAGtB,KAAK,CAACuB,UAAU,CAACf,oBAAoB,CAAC;EAEzDR,KAAK,CAACwB,mBAAmB,CAACb,GAAG,EAAE,MAAMS,gBAAgB,CAACK,OAAQ,CAAC;EAE/D,MAAMC,eAAe,GACnBzB,QAAQ,CAAC0B,EAAE,KAAK,SAAS,GACrB,KAAK,GACLZ,iBAAiB,KAAK,MAAM,IAAIF,YAAY,EAAEe,MAAM,KAAK,KAAK;EAEpE,MAAMC,uBAAuB,GAAG7B,KAAK,CAACqB,MAAM,CAACR,YAAY,EAAEe,MAAM,CAAC;EAElE5B,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB3B,QAAQ,CACNF,QAAQ,CAAC0B,EAAE,KAAK,SAAS,IACvBZ,iBAAiB,KAAK,MAAM,IAC5Bc,uBAAuB,CAACJ,OAAO,KAAKZ,YAAY,EAAEe,MAAM,EACzD,qHACH,CAAC;IAEDC,uBAAuB,CAACJ,OAAO,GAAGZ,YAAY,EAAEe,MAAM;EACxD,CAAC,EAAE,CAACf,YAAY,EAAEe,MAAM,EAAEb,iBAAiB,CAAC,CAAC;EAE7C,MAAMgB,OAAO,gBACX/B,KAAA,CAAAgC,aAAA,CAAAhC,KAAA,CAAAiC,QAAA,qBACEjC,KAAA,CAAAgC,aAAA,CAAC5B,cAAc;IACba,KAAK,EAAE,CACLF,iBAAiB,KAAK,WAAW,GAC7Bd,QAAQ,CAAC0B,EAAE,KAAK,KAAK,GACnBO,MAAM,CAACC,QAAQ,GACf,IAAI,GACND,MAAM,CAACE,SAAS,EACpBpB,YAAY,CACZ;IACFD,iBAAiB,EAAEA,iBAAiB,IAAI;EAAO,GAC9CH,QACa,CAAC,eAYjBZ,KAAA,CAAAgC,aAAA,CAAC3B,uBAAuB,EAAKQ,YAAe,CAC5C,CACH;;EAED;EACA;EACA;EACA,IAAIwB,mBAAmB;EAEvB,IAAItB,iBAAiB,KAAK,WAAW,IAAIC,YAAY,EAAE;IACrD,MAAMsB,oBAAoB,GAAGpC,UAAU,CAACqC,OAAO,CAACvB,YAAY,CAAC;IAC7DqB,mBAAmB,GAAG;MACpBG,eAAe,EAAEF,oBAAoB,EAAEE;IACzC,CAAC;EACH;EAEA,oBACExC,KAAA,CAAAgC,aAAA,CAAC1B,MAAM,EAAAmC,QAAA;IACL9B,GAAG,EAAE+B,IAAI,IAAI;MACXtB,gBAAgB,CAACK,OAAO,GAAGiB,IAAI;MAE/B,IAAIpB,UAAU,KAAK,IAAI,EAAE;QACvBqB,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;QACD;MACF;MAEA,MAAMC,WAAW,GAAGvB,UAAU,CAACG,OAAO;MAEtC,IAAIiB,IAAI,KAAK,IAAI,EAAE;QACjB;QACA,OAAOG,WAAW,CAAC3B,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACL2B,WAAW,CAAC3B,QAAQ,CAAC,GAAG;UAAEO,OAAO,EAAEiB;QAAK,CAAC;MAC3C;IACF,CAAE;IACFI,OAAO;IACPC,aAAa;IACbjC,aAAa,EAAEA,aAAc;IAC7BC,iBAAiB,EAAEA,iBAAkB;IACrCiC,cAAc,EAAEnC,YAAY,EAAEoC,UAAU,IAAI,KAAM;IAClDhC,KAAK,EAAE,CAACA,KAAK,EAAEoB,mBAAmB;EAAE,GAChClB,IAAI,GACPO,eAAe,gBACd1B,KAAA,CAAAgC,aAAA,CAACzB,WAAW;IAACU,KAAK,EAAEiB,MAAM,CAACE;EAAU,gBACnCpC,KAAA,CAAAgC,aAAA,CAAC1B,MAAM;IACLwC,OAAO;IACPC,aAAa;IACbjC,aAAa,EAAEA,aAAc;IAC7BkC,cAAc,EAAEnC,YAAY,EAAEoC,UAAU,IAAI,KAAM;IAClDhC,KAAK,EAAEf,UAAU,CAACgD;EAAa,GAC9BnB,OACK,CACG,CAAC,GAEdA,OAEI,CAAC;AAEb;AAEA,4BAAe/B,KAAK,CAACmD,UAAU,CAAC1C,eAAe,CAAC;AAEhD,MAAMyB,MAAM,GAAGhC,UAAU,CAACkD,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTiB,IAAI,EAAE;EACR,CAAC;EACDlB,QAAQ,EAAE;IACRmB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC"}