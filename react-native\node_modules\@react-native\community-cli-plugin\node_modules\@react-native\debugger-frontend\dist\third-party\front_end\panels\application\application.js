import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import{assertNotNullOrUndefined as r}from"../../core/platform/platform.js";import*as a from"../../core/root/root.js";import*as o from"../../core/sdk/sdk.js";import*as n from"../../models/issues_manager/issues_manager.js";import*as d from"../../ui/components/icon_button/icon_button.js";import*as l from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as c from"../../ui/legacy/components/source_frame/source_frame.js";import*as h from"../../ui/legacy/legacy.js";import*as g from"../../ui/components/buttons/buttons.js";import*as u from"../../ui/legacy/components/inline_editor/inline_editor.js";import*as p from"../../ui/legacy/components/utils/utils.js";import*as m from"../../ui/visual_logging/visual_logging.js";import*as w from"./components/components.js";import{StorageMetadataView as v}from"./components/components.js";import*as S from"../../models/bindings/bindings.js";import*as b from"../../ui/legacy/components/data_grid/data_grid.js";import*as k from"../../ui/components/report_view/report_view.js";import*as y from"../../ui/legacy/components/object_ui/object_ui.js";import*as f from"../../ui/lit-html/lit-html.js";import*as C from"../../ui/components/split_view/split_view.js";import*as T from"./preloading/components/components.js";import*as I from"../../models/text_utils/text_utils.js";import*as E from"../network/components/components.js";import*as x from"../network/network.js";import*as M from"../../models/logs/logs.js";import*as R from"../network/forward/forward.js";import*as L from"../mobile_throttling/mobile_throttling.js";import*as B from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as A from"../../ui/legacy/components/cookie_table/cookie_table.js";class P extends h.TreeOutline.TreeElement{resourcesPanel;constructor(e,t,i){super(t,i),this.resourcesPanel=e,h.ARIAUtils.setLabel(this.listItemElement,t),this.listItemElement.tabIndex=-1}deselect(){super.deselect(),this.listItemElement.tabIndex=-1}get itemURL(){throw new Error("Unimplemented Method")}onselect(e){if(!e)return!1;const t=[];for(let e=this;e;e=e.parent){const i=e instanceof P&&e.itemURL;if(!i)break;t.push(i)}return this.resourcesPanel.setLastSelectedItemPath(t),!1}showView(e){this.resourcesPanel.showView(e)}}class D extends P{expandedSetting;categoryName;categoryLink;constructor(t,i,s,r=!1){super(t,i,!1),this.expandedSetting=e.Settings.Settings.instance().createSetting("resources-"+s+"-expanded",r),this.categoryName=i,this.categoryLink=null}get itemURL(){return"category://"+this.categoryName}setLink(e){this.categoryLink=e}onselect(e){return super.onselect(e),this.resourcesPanel.showCategoryView(this.categoryName,this.categoryLink),!1}onattach(){super.onattach(),this.expandedSetting.get()&&this.expand()}onexpand(){this.expandedSetting.set(!0)}oncollapse(){this.expandedSetting.set(!1)}}const F=new CSSStyleSheet;F.replaceSync('.report-field-name{flex-basis:152px}.manifest-view-header{min-width:600px;flex-shrink:0;flex-grow:0}.manifest-container{overflow:auto}.inline-icon{margin-inline:4px;width:16px;height:16px;&[name="check-circle"]{color:var(--icon-checkmark-green)}}.multiline-value{white-space:normal}.chrome-select{margin:4px}.inline-button{vertical-align:sub}\n/*# sourceURL=appManifestView.css */\n');const V={errorsAndWarnings:"Errors and warnings",installability:"Installability",identity:"Identity",presentation:"Presentation",protocolHandlers:"Protocol Handlers",icons:"Icons",windowControlsOverlay:"Window Controls Overlay",name:"Name",shortName:"Short name",url:"URL",computedAppId:"Computed App ID",appIdExplainer:"This is used by the browser to know whether the manifest should be updating an existing application, or whether it refers to a new web app that can be installed.",learnMore:"Learn more",appIdNote:"{PH1} {PH2} is not specified in the manifest, {PH3} is used instead. To specify an App ID that matches the current identity, set the {PH4} field to {PH5} {PH6}.",note:"Note:",copyToClipboard:"Copy suggested ID to clipboard",copiedToClipboard:"Copied suggested ID {PH1} to clipboard",description:"Description",startUrl:"Start URL",themeColor:"Theme color",backgroundColor:"Background color",darkThemeColor:"Dark theme color",darkBackgroundColor:"Dark background color",orientation:"Orientation",display:"Display",newNoteUrl:"New note URL",descriptionMayBeTruncated:"Description may be truncated.",shortcutsMayBeNotAvailable:"The maximum number of shortcuts is platform dependent. Some shortcuts may be not available.",showOnlyTheMinimumSafeAreaFor:"Show only the minimum safe area for maskable icons",documentationOnMaskableIcons:"documentation on maskable icons",needHelpReadOurS:"Need help? Read the {PH1}.",shortcutS:"Shortcut #{PH1}",shortcutSShouldIncludeAXPixel:"Shortcut #{PH1} should include a 96×96 pixel icon",screenshotS:"Screenshot #{PH1}",pageIsNotLoadedInTheMainFrame:"Page is not loaded in the main frame",pageIsNotServedFromASecureOrigin:"Page is not served from a secure origin",pageHasNoManifestLinkUrl:"Page has no manifest <link> `URL`",manifestCouldNotBeFetchedIsEmpty:"Manifest could not be fetched, is empty, or could not be parsed",manifestStartUrlIsNotValid:"Manifest '`start_url`' is not valid",manifestDoesNotContainANameOr:"Manifest does not contain a '`name`' or '`short_name`' field",manifestDisplayPropertyMustBeOne:"Manifest '`display`' property must be one of '`standalone`', '`fullscreen`', or '`minimal-ui`'",manifestDoesNotContainASuitable:"Manifest does not contain a suitable icon—PNG, SVG, or WebP format of at least {PH1}px is required, the '`sizes`' attribute must be set, and the '`purpose`' attribute, if set, must include '`any`'.",avoidPurposeAnyAndMaskable:"Declaring an icon with '`purpose`' of '`any maskable`' is discouraged. It is likely to look incorrect on some platforms due to too much or too little padding.",noSuppliedIconIsAtLeastSpxSquare:"No supplied icon is at least {PH1} pixels square in `PNG`, `SVG`, or `WebP` format, with the purpose attribute unset or set to '`any`'.",couldNotDownloadARequiredIcon:"Could not download a required icon from the manifest",downloadedIconWasEmptyOr:"Downloaded icon was empty or corrupted",theSpecifiedApplicationPlatform:"The specified application platform is not supported on Android",noPlayStoreIdProvided:"No Play store ID provided",thePlayStoreAppUrlAndPlayStoreId:"The Play Store app URL and Play Store ID do not match",theAppIsAlreadyInstalled:"The app is already installed",aUrlInTheManifestContainsA:"A URL in the manifest contains a username, password, or port",pageIsLoadedInAnIncognitoWindow:"Page is loaded in an incognito window",pageDoesNotWorkOffline:"Page does not work offline",couldNotCheckServiceWorker:"Could not check `service worker` without a '`start_url`' field in the manifest",manifestSpecifies:"Manifest specifies '`prefer_related_applications`: true'",preferrelatedapplicationsIsOnly:"'`prefer_related_applications`' is only supported on `Chrome` Beta and Stable channels on `Android`.",manifestContainsDisplayoverride:"Manifest contains '`display_override`' field, and the first supported display mode must be one of '`standalone`', '`fullscreen`', or '`minimal-ui`'",pageDoesNotWorkOfflineThePage:"Page does not work offline. Starting in Chrome 93, the installability criteria are changing, and this site will not be installable. See {PH1} for more information.",imageFromS:"Image from {PH1}",screenshot:"Screenshot",formFactor:"Form factor",label:"Label",platform:"Platform",icon:"Icon",sSrcIsNotSet:"{PH1} '`src`' is not set",sUrlSFailedToParse:"{PH1} URL ''{PH2}'' failed to parse",sSFailedToLoad:"{PH1} {PH2} failed to load",sSDoesNotSpecifyItsSizeInThe:"{PH1} {PH2} does not specify its size in the manifest",sSShouldSpecifyItsSizeAs:"{PH1} {PH2} should specify its size as `[width]x[height]`",sSShouldHaveSquareIcon:"Most operating systems require square icons. Please include at least one square icon in the array.",actualSizeSspxOfSSDoesNotMatch:"Actual size ({PH1}×{PH2})px of {PH3} {PH4} does not match specified size ({PH5}×{PH6}px)",actualWidthSpxOfSSDoesNotMatch:"Actual width ({PH1}px) of {PH2} {PH3} does not match specified width ({PH4}px)",actualHeightSpxOfSSDoesNotMatch:"Actual height ({PH1}px) of {PH2} {PH3} does not match specified height ({PH4}px)",sSSizeShouldBeAtLeast320:"{PH1} {PH2} size should be at least 320×320",sSSizeShouldBeAtMost3840:"{PH1} {PH2} size should be at most 3840×3840",sSWidthDoesNotComplyWithRatioRequirement:"{PH1} {PH2} width can't be more than 2.3 times as long as the height",sSHeightDoesNotComplyWithRatioRequirement:"{PH1} {PH2} height can't be more than 2.3 times as long as the width",screenshotPixelSize:"Screenshot {url} should specify a pixel size `[width]x[height]` instead of `any` as first size.",noScreenshotsForRicherPWAInstallOnDesktop:"Richer PWA Install UI won’t be available on desktop. Please add at least one screenshot with the `form_factor` set to `wide`.",noScreenshotsForRicherPWAInstallOnMobile:"Richer PWA Install UI won’t be available on mobile. Please add at least one screenshot for which `form_factor` is not set or set to a value other than `wide`.",tooManyScreenshotsForDesktop:"No more than 8 screenshots will be displayed on desktop. The rest will be ignored.",tooManyScreenshotsForMobile:"No more than 5 screenshots will be displayed on mobile. The rest will be ignored.",screenshotsMustHaveSameAspectRatio:"All screenshots with the same `form_factor` must have the same aspect ratio as the first screenshot with that `form_factor`. Some screenshots will be ignored.",wcoFound:"Chrome has successfully found the {PH1} value for the {PH2} field in the {PH3}.",wcoNotFound:"Define {PH1} in the manifest to use the Window Controls Overlay API and customize your app's title bar.",customizePwaTitleBar:"Customize the window controls overlay of your PWA's title bar",wcoNeedHelpReadMore:"Need help? Read {PH1}.",selectWindowControlsOverlayEmulationOs:"Emulate the Window Controls Overlay on"},O=i.i18n.registerUIStrings("panels/application/AppManifestView.ts",V),W=i.i18n.getLocalizedString.bind(void 0,O);class U extends(e.ObjectWrapper.eventMixin(h.Widget.VBox)){emptyView;reportView;errorsSection;installabilitySection;identitySection;presentationSection;iconsSection;windowControlsSection;protocolHandlersSection;shortcutSections;screenshotsSections;nameField;shortNameField;descriptionField;startURLField;themeColorSwatch;backgroundColorSwatch;darkThemeColorField;darkThemeColorSwatch;darkBackgroundColorField;darkBackgroundColorSwatch;orientationField;displayField;newNoteUrlField;throttler;registeredListeners;target;resourceTreeModel;serviceWorkerManager;overlayModel;protocolHandlersView;constructor(e,t,i){super(!0),this.contentElement.classList.add("manifest-container"),this.contentElement.setAttribute("jslog",`${m.pane("manifest")}`),this.emptyView=e,this.emptyView.appendLink("https://web.dev/add-manifest/"),this.emptyView.show(this.contentElement),this.emptyView.hideWidget(),this.reportView=t,this.reportView.element.classList.add("manifest-view-header"),this.reportView.show(this.contentElement),this.reportView.hideWidget(),this.errorsSection=this.reportView.appendSection(W(V.errorsAndWarnings)),this.errorsSection.element.setAttribute("jslog",`${m.section("errors-and-warnings")}`),this.installabilitySection=this.reportView.appendSection(W(V.installability)),this.installabilitySection.element.setAttribute("jslog",`${m.section("installability")}`),this.identitySection=this.reportView.appendSection(W(V.identity)),this.identitySection.element.setAttribute("jslog",`${m.section("identity")}`),this.presentationSection=this.reportView.appendSection(W(V.presentation)),this.presentationSection.element.setAttribute("jslog",`${m.section("presentation")}`),this.protocolHandlersSection=this.reportView.appendSection(W(V.protocolHandlers)),this.protocolHandlersSection.element.setAttribute("jslog",`${m.section("protocol-handlers")}`),this.protocolHandlersView=new w.ProtocolHandlersView.ProtocolHandlersView,this.protocolHandlersSection.appendFieldWithCustomView(this.protocolHandlersView),this.iconsSection=this.reportView.appendSection(W(V.icons),"report-section-icons"),this.iconsSection.element.setAttribute("jslog",`${m.section("icons")}`),this.windowControlsSection=this.reportView.appendSection(V.windowControlsOverlay),this.windowControlsSection.element.setAttribute("jslog",`${m.section("window-controls-overlay")}`),this.shortcutSections=[],this.screenshotsSections=[],this.nameField=this.identitySection.appendField(W(V.name)),this.shortNameField=this.identitySection.appendField(W(V.shortName)),this.descriptionField=this.identitySection.appendFlexedField(W(V.description)),this.startURLField=this.presentationSection.appendField(W(V.startUrl)),h.ARIAUtils.setLabel(this.startURLField,W(V.startUrl));const s=this.presentationSection.appendField(W(V.themeColor));this.themeColorSwatch=new u.ColorSwatch.ColorSwatch,s.appendChild(this.themeColorSwatch);const r=this.presentationSection.appendField(W(V.backgroundColor));this.backgroundColorSwatch=new u.ColorSwatch.ColorSwatch,r.appendChild(this.backgroundColorSwatch),this.darkThemeColorField=this.presentationSection.appendField(W(V.darkThemeColor)),this.darkThemeColorSwatch=new u.ColorSwatch.ColorSwatch,this.darkThemeColorField.appendChild(this.darkThemeColorSwatch),this.darkBackgroundColorField=this.presentationSection.appendField(W(V.darkBackgroundColor)),this.darkBackgroundColorSwatch=new u.ColorSwatch.ColorSwatch,this.darkBackgroundColorField.appendChild(this.darkBackgroundColorSwatch),this.orientationField=this.presentationSection.appendField(W(V.orientation)),this.displayField=this.presentationSection.appendField(W(V.display)),this.newNoteUrlField=this.presentationSection.appendField(W(V.newNoteUrl)),this.throttler=i,o.TargetManager.TargetManager.instance().observeTargets(this),this.registeredListeners=[]}getStaticSections(){return[this.identitySection,this.presentationSection,this.protocolHandlersSection,this.iconsSection,this.windowControlsSection]}getManifestElement(){return this.reportView.getHeaderElement()}targetAdded(e){e===o.TargetManager.TargetManager.instance().primaryPageTarget()&&(this.target=e,this.resourceTreeModel=e.model(o.ResourceTreeModel.ResourceTreeModel),this.serviceWorkerManager=e.model(o.ServiceWorkerManager.ServiceWorkerManager),this.overlayModel=e.model(o.OverlayModel.OverlayModel),this.resourceTreeModel&&this.serviceWorkerManager&&this.overlayModel&&(this.updateManifest(!0),this.registeredListeners=[this.resourceTreeModel.addEventListener(o.ResourceTreeModel.Events.DOMContentLoaded,(()=>{this.updateManifest(!0)})),this.serviceWorkerManager.addEventListener("RegistrationUpdated",(()=>{this.updateManifest(!1)}))]))}targetRemoved(t){this.target===t&&this.resourceTreeModel&&this.serviceWorkerManager&&this.overlayModel&&(delete this.resourceTreeModel,delete this.serviceWorkerManager,delete this.overlayModel,e.EventTarget.removeEventListeners(this.registeredListeners))}async updateManifest(e){if(!this.resourceTreeModel)return;const[{url:t,data:i,errors:s},r,a]=await Promise.all([this.resourceTreeModel.fetchAppManifest(),this.resourceTreeModel.getInstallabilityErrors(),this.resourceTreeModel.getAppId()]);this.throttler.schedule((()=>this.renderManifest(t,i,s,r,a)),e)}async renderManifest(s,r,a,o,n){const l=n?.appId||null,c=n?.recommendedId||null;if(!r&&!a.length)return this.emptyView.showWidget(),this.reportView.hideWidget(),void this.dispatchEventToListeners("ManifestDetected",!1);this.emptyView.hideWidget(),this.reportView.showWidget(),this.dispatchEventToListeners("ManifestDetected",!0);const u=p.Linkifier.Linkifier.linkifyURL(s);u.tabIndex=0,this.reportView.setURL(u),this.errorsSection.clearContent(),this.errorsSection.element.classList.toggle("hidden",!a.length);for(const e of a){const t=h.UIUtils.createIconLabel({title:e.message,iconName:e.critical?"cross-circle-filled":"warning-filled",color:e.critical?"var(--icon-error)":"var(--icon-warning)"});this.errorsSection.appendRow().appendChild(t)}if(!r)return;65279===r.charCodeAt(0)&&(r=r.slice(1));const w=JSON.parse(r);this.nameField.textContent=$("name"),this.shortNameField.textContent=$("short_name");const v=[],S=$("description");this.descriptionField.textContent=S,S.length>300&&v.push(W(V.descriptionMayBeTruncated));const b=$("start_url");if(l&&c){const e=this.identitySection.appendField(W(V.computedAppId));h.ARIAUtils.setLabel(e,"App Id"),e.textContent=l;const s=d.Icon.create("help","inline-icon");s.title=W(V.appIdExplainer),s.setAttribute("jslog",`${m.action("help").track({hover:!0})}`),e.appendChild(s);const r=h.XLink.XLink.create("https://developer.chrome.com/blog/pwa-manifest-id/",W(V.learnMore),void 0,void 0,"learn-more");if(e.appendChild(r),!$("id")){const s=e.createChild("div","multiline-value"),r=document.createElement("b");r.textContent=W(V.note);const a=document.createElement("code");a.textContent="id";const o=document.createElement("code");o.textContent="id";const n=document.createElement("code");n.textContent="start_url";const d=document.createElement("code");d.textContent=c;const l=new g.Button.Button;l.className="inline-button",l.variant="round",l.size="SMALL",l.iconName="copy",l.jslogContext="manifest.copy-id",l.title=W(V.copyToClipboard),l.addEventListener("click",(()=>{h.ARIAUtils.alert(W(V.copiedToClipboard,{PH1:c})),t.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(c)})),s.appendChild(i.i18n.getFormatLocalizedString(O,V.appIdNote,{PH1:r,PH2:a,PH3:n,PH4:o,PH5:d,PH6:l}))}}else this.identitySection.removeField(W(V.computedAppId));if(this.startURLField.removeChildren(),b){const t=e.ParsedURL.ParsedURL.completeURL(s,b);if(t){const e=p.Linkifier.Linkifier.linkifyURL(t,{text:b});e.tabIndex=0,e.setAttribute("jslog",`${m.link("start-url").track({click:!0})}`),this.startURLField.appendChild(e)}}this.themeColorSwatch.classList.toggle("hidden",!$("theme_color"));const k=e.Color.parse($("theme_color")||"white")||e.Color.parse("white");k&&this.themeColorSwatch.renderColor(k,!0),this.backgroundColorSwatch.classList.toggle("hidden",!$("background_color"));const y=e.Color.parse($("background_color")||"white")||e.Color.parse("white");y&&this.backgroundColorSwatch.renderColor(y,!0);const f=((w.user_preferences||{}).color_scheme||{}).dark||{},C=f.theme_color,T="string"==typeof C;if(this.darkThemeColorField.parentElement?.classList.toggle("hidden",!T),T){const t=e.Color.parse(C);t&&this.darkThemeColorSwatch.renderColor(t,!0)}const I=f.background_color,E="string"==typeof I;if(this.darkBackgroundColorField.parentElement?.classList.toggle("hidden",!E),E){const t=e.Color.parse(I);t&&this.darkBackgroundColorSwatch.renderColor(t,!0)}this.orientationField.textContent=$("orientation");const x=$("display");this.displayField.textContent=x;const M=(w.note_taking||{}).new_note_url,R="string"==typeof M;if(this.newNoteUrlField.parentElement?.classList.toggle("hidden",!R),this.newNoteUrlField.removeChildren(),R){const t=e.ParsedURL.ParsedURL.completeURL(s,M),i=p.Linkifier.Linkifier.linkifyURL(t,{text:M});i.tabIndex=0,this.newNoteUrlField.appendChild(i)}const L=w.protocol_handlers||[];this.protocolHandlersView.data={protocolHandlers:L,manifestLink:s};const B=w.icons||[];this.iconsSection.clearContent();const A=w.shortcuts||[];for(const e of this.shortcutSections)e.detach(!0);const P=w.screenshots||[];for(const e of this.screenshotsSections)e.detach(!0);const D=[],F=h.UIUtils.CheckboxLabel.create(W(V.showOnlyTheMinimumSafeAreaFor));F.classList.add("mask-checkbox"),F.setAttribute("jslog",`${m.toggle("show-minimal-safe-area-for-maskable-icons").track({change:!0})}`),F.addEventListener("click",(()=>{this.iconsSection.setIconMasked(F.checkboxElement.checked)})),this.iconsSection.appendRow().appendChild(F);const U=h.XLink.XLink.create("https://web.dev/maskable-icon/",W(V.documentationOnMaskableIcons),void 0,void 0,"learn-more");this.iconsSection.appendRow().appendChild(i.i18n.getFormatLocalizedString(O,V.needHelpReadOurS,{PH1:U}));let N=!1;for(const e of B){const t=await this.appendImageResourceToSection(s,e,this.iconsSection,!1);D.push(...t.imageResourceErrors),N=t.squareSizedIconAvailable||N}N||D.push(W(V.sSShouldHaveSquareIcon)),A.length>4&&v.push(W(V.shortcutsMayBeNotAvailable));let j=1;for(const t of A){const i=this.reportView.appendSection(W(V.shortcutS,{PH1:j}));i.element.setAttribute("jslog",`${m.section("shortcuts")}`),this.shortcutSections.push(i),i.appendFlexedField(W(V.name),t.name),t.short_name&&i.appendFlexedField(W(V.shortName),t.short_name),t.description&&i.appendFlexedField(W(V.description),t.description);const r=i.appendFlexedField(W(V.url)),a=e.ParsedURL.ParsedURL.completeURL(s,t.url),o=p.Linkifier.Linkifier.linkifyURL(a,{text:t.url});o.setAttribute("jslog",`${m.link("shortcut").track({click:!0})}`),o.tabIndex=0,r.appendChild(o);const n=t.icons||[];let d=!1;for(const e of n){const{imageResourceErrors:t}=await this.appendImageResourceToSection(s,e,i,!1);if(D.push(...t),!d&&e.sizes){const t=e.sizes.match(/^(\d+)x(\d+)$/);t&&t[1]>=96&&t[2]>=96&&(d=!0)}}d||D.push(W(V.shortcutSShouldIncludeAXPixel,{PH1:j})),j++}let H=1;const _=new Map;let G=!1;for(const e of P){const t=this.reportView.appendSection(W(V.screenshotS,{PH1:H}));this.screenshotsSections.push(t),e.form_factor&&t.appendFlexedField(W(V.formFactor),e.form_factor),e.label&&t.appendFlexedField(W(V.label),e.label),e.platform&&t.appendFlexedField(W(V.platform),e.platform);const{imageResourceErrors:i,naturalWidth:r,naturalHeight:a}=await this.appendImageResourceToSection(s,e,t,!0);if(D.push(...i),e.form_factor&&r&&a){_.has(e.form_factor)||_.set(e.form_factor,{width:r,height:a});const t=_.get(e.form_factor);t&&(G=G||r*t.height!=a*t.width)}H++}G&&v.push(W(V.screenshotsMustHaveSameAspectRatio));const K=P.filter((e=>"wide"===e.form_factor)),z=P.filter((e=>"wide"!==e.form_factor));K.length<1&&v.push(W(V.noScreenshotsForRicherPWAInstallOnDesktop)),z.length<1&&v.push(W(V.noScreenshotsForRicherPWAInstallOnMobile)),K.length>8&&v.push(W(V.tooManyScreenshotsForDesktop)),z.length>5&&v.push(W(V.tooManyScreenshotsForMobile)),this.installabilitySection.clearContent(),this.installabilitySection.element.classList.toggle("hidden",!o.length);const q=this.getInstallabilityErrorMessages(o);for(const e of q){const t=h.UIUtils.createIconLabel({title:e,iconName:"warning-filled",color:"var(--icon-warning)"});this.installabilitySection.appendRow().appendChild(t)}this.errorsSection.element.classList.toggle("hidden",!a.length&&!D.length&&!v.length);for(const e of v){const t=h.UIUtils.createIconLabel({title:e,iconName:"warning-filled",color:"var(--icon-warning)"});this.errorsSection.appendRow().appendChild(t)}for(const e of D){const t=h.UIUtils.createIconLabel({title:e,iconName:"warning-filled",color:"var(--icon-warning)"});this.errorsSection.appendRow().appendChild(t)}function $(e){const t=w[e];return"string"!=typeof t?"":t}this.windowControlsSection.clearContent();const J=(w.display_override||[]).includes("window-controls-overlay"),Q=h.XLink.XLink.create("https://developer.mozilla.org/en-US/docs/Web/Manifest/display_override","display-override",void 0,void 0,"display-override"),X=document.createElement("code");X.appendChild(Q);const Y=this.windowControlsSection.appendRow();if(J){const e=d.Icon.create("check-circle","inline-icon");Y.appendChild(e);const t=document.createElement("code");t.classList.add("wco"),t.textContent="window-controls-overlay",Y.appendChild(i.i18n.getFormatLocalizedString(O,V.wcoFound,{PH1:t,PH2:X,PH3:u})),this.overlayModel&&await this.appendWindowControlsToSection(this.overlayModel,s,$("theme_color"))}else{const e=d.Icon.create("info","inline-icon");Y.appendChild(e),Y.appendChild(i.i18n.getFormatLocalizedString(O,V.wcoNotFound,{PH1:X}))}const Z=h.XLink.XLink.create("https://learn.microsoft.com/en-us/microsoft-edge/progressive-web-apps-chromium/how-to/window-controls-overlay",W(V.customizePwaTitleBar),void 0,void 0,"customize-pwa-tittle-bar");this.windowControlsSection.appendRow().appendChild(i.i18n.getFormatLocalizedString(O,V.wcoNeedHelpReadMore,{PH1:Z})),this.dispatchEventToListeners("ManifestRendered")}getInstallabilityErrorMessages(e){const t=[];for(const i of e){let e;switch(i.errorId){case"not-in-main-frame":e=W(V.pageIsNotLoadedInTheMainFrame);break;case"not-from-secure-origin":e=W(V.pageIsNotServedFromASecureOrigin);break;case"no-manifest":e=W(V.pageHasNoManifestLinkUrl);break;case"manifest-empty":e=W(V.manifestCouldNotBeFetchedIsEmpty);break;case"start-url-not-valid":e=W(V.manifestStartUrlIsNotValid);break;case"manifest-missing-name-or-short-name":e=W(V.manifestDoesNotContainANameOr);break;case"manifest-display-not-supported":e=W(V.manifestDisplayPropertyMustBeOne);break;case"manifest-missing-suitable-icon":if(1!==i.errorArguments.length||"minimum-icon-size-in-pixels"!==i.errorArguments[0].name){console.error("Installability error does not have the correct errorArguments");break}e=W(V.manifestDoesNotContainASuitable,{PH1:i.errorArguments[0].value});break;case"no-acceptable-icon":if(1!==i.errorArguments.length||"minimum-icon-size-in-pixels"!==i.errorArguments[0].name){console.error("Installability error does not have the correct errorArguments");break}e=W(V.noSuppliedIconIsAtLeastSpxSquare,{PH1:i.errorArguments[0].value});break;case"cannot-download-icon":e=W(V.couldNotDownloadARequiredIcon);break;case"no-icon-available":e=W(V.downloadedIconWasEmptyOr);break;case"platform-not-supported-on-android":e=W(V.theSpecifiedApplicationPlatform);break;case"no-id-specified":e=W(V.noPlayStoreIdProvided);break;case"ids-do-not-match":e=W(V.thePlayStoreAppUrlAndPlayStoreId);break;case"already-installed":e=W(V.theAppIsAlreadyInstalled);break;case"url-not-supported-for-webapk":e=W(V.aUrlInTheManifestContainsA);break;case"in-incognito":e=W(V.pageIsLoadedInAnIncognitoWindow);break;case"not-offline-capable":e=W(V.pageDoesNotWorkOffline);break;case"no-url-for-service-worker":e=W(V.couldNotCheckServiceWorker);break;case"prefer-related-applications":e=W(V.manifestSpecifies);break;case"prefer-related-applications-only-beta-stable":e=W(V.preferrelatedapplicationsIsOnly);break;case"manifest-display-override-not-supported":e=W(V.manifestContainsDisplayoverride);break;case"warn-not-offline-capable":e=W(V.pageDoesNotWorkOfflineThePage,{PH1:"https://developer.chrome.com/blog/improved-pwa-offline-detection/"});break;default:console.error(`Installability error id '${i.errorId}' is not recognized`)}e&&t.push(e)}return t}async loadImage(e){const t=document.createElement("div");t.classList.add("image-wrapper");const i=document.createElement("img"),s=new Promise(((e,t)=>{i.onload=e,i.onerror=t}));i.src=e,i.alt=W(V.imageFromS,{PH1:e}),t.appendChild(i);try{return await s,{wrapper:t,image:i}}catch(e){}return null}parseSizes(e,t,i,s){const r=e?e.split(/\s+/):[],a=[];for(const e of r){if("any"===e){a.find((e=>"any"in e))||a.push({any:"any",formatted:"any"});continue}const r=e.match(/^(?<width>\d+)[xX](?<height>\d+)$/);if(r){const e=parseInt(r.groups?.width||"",10),t=parseInt(r.groups?.height||"",10),i=`${e}×${t}px`;a.push({width:e,height:t,formatted:i})}else s.push(W(V.sSShouldSpecifyItsSizeAs,{PH1:t,PH2:i}))}return a}checkSizeProblem(e,t,i,s,r){if("any"in e)return{hasSquareSize:i.naturalWidth===i.naturalHeight};const a=e.width===e.height;return i.naturalWidth!==e.width&&i.naturalHeight!==e.height?{error:W(V.actualSizeSspxOfSSDoesNotMatch,{PH1:i.naturalWidth,PH2:i.naturalHeight,PH3:s,PH4:r,PH5:e.width,PH6:e.height}),hasSquareSize:a}:i.naturalWidth!==e.width?{error:W(V.actualWidthSpxOfSSDoesNotMatch,{PH1:i.naturalWidth,PH2:s,PH3:r,PH4:e.width}),hasSquareSize:a}:i.naturalHeight!==e.height?{error:W(V.actualHeightSpxOfSSDoesNotMatch,{PH1:i.naturalHeight,PH2:s,PH3:r,PH4:e.height}),hasSquareSize:a}:{hasSquareSize:a}}async appendImageResourceToSection(t,i,s,r){const a=[],o=W(r?V.screenshot:V.icon);if(!i.src)return a.push(W(V.sSrcIsNotSet,{PH1:o})),{imageResourceErrors:a};const n=e.ParsedURL.ParsedURL.completeURL(t,i.src);if(!n)return a.push(W(V.sUrlSFailedToParse,{PH1:o,PH2:i.src})),{imageResourceErrors:a};const d=await this.loadImage(n);if(!d)return a.push(W(V.sSFailedToLoad,{PH1:o,PH2:n})),{imageResourceErrors:a};const{wrapper:l,image:c}=d,{naturalWidth:h,naturalHeight:g}=c,u=this.parseSizes(i.sizes,o,n,a),p=u.map((e=>e.formatted)).join(" ")+"\n"+(i.type||""),m=s.appendFlexedField(p);let w=!1;if(i.sizes){r&&u.length>0&&"any"in u[0]&&a.push(W(V.screenshotPixelSize,{url:n}));for(const e of u){const{error:t,hasSquareSize:s}=this.checkSizeProblem(e,i.type,c,o,n);if(w=w||s,t)a.push(t);else if(r){const t="any"in e?c.naturalWidth:e.width,i="any"in e?c.naturalHeight:e.height;t<320||i<320?a.push(W(V.sSSizeShouldBeAtLeast320,{PH1:o,PH2:n})):t>3840||i>3840?a.push(W(V.sSSizeShouldBeAtMost3840,{PH1:o,PH2:n})):t>2.3*i?a.push(W(V.sSWidthDoesNotComplyWithRatioRequirement,{PH1:o,PH2:n})):i>2.3*t&&a.push(W(V.sSHeightDoesNotComplyWithRatioRequirement,{PH1:o,PH2:n}))}}}else a.push(W(V.sSDoesNotSpecifyItsSizeInThe,{PH1:o,PH2:n}));c.width=c.naturalWidth;const v="string"==typeof i.purpose?i.purpose.toLowerCase():"";return v.includes("any")&&v.includes("maskable")&&a.push(W(V.avoidPurposeAnyAndMaskable)),m.appendChild(l),{imageResourceErrors:a,squareSizedIconAvailable:w,naturalWidth:h,naturalHeight:g}}wasShown(){super.wasShown(),this.reportView.registerCSSFiles([F]),this.registerCSSFiles([F])}async appendWindowControlsToSection(e,t,i){if(!await e.hasStyleSheetText(t))return;await e.toggleWindowControlsToolbar(!1);const s=h.UIUtils.CheckboxLabel.create(W(V.selectWindowControlsOverlayEmulationOs),!1);s.checkboxElement.addEventListener("click",(async()=>{await(this.overlayModel?.toggleWindowControlsToolbar(s.checkboxElement.checked))}));const r=s.createChild("select","chrome-select");r.appendChild(h.UIUtils.createOption("Windows","Windows","windows")),r.appendChild(h.UIUtils.createOption("macOS","Mac","macos")),r.appendChild(h.UIUtils.createOption("Linux","Linux","linux")),r.selectedIndex=0,this.overlayModel&&(r.value=this.overlayModel?.getWindowControlsConfig().selectedPlatform),r.addEventListener("change",(async()=>{const e=r.options[r.selectedIndex].value;this.overlayModel&&(this.overlayModel.setWindowControlsPlatform(e),await this.overlayModel.toggleWindowControlsToolbar(s.checkboxElement.checked))})),this.windowControlsSection.appendRow().appendChild(s),e.setWindowControlsThemeColor(i)}}var N=Object.freeze({__proto__:null,AppManifestView:U});const j={backForwardCache:"Back/forward cache"},H=i.i18n.registerUIStrings("panels/application/BackForwardCacheTreeElement.ts",j),_=i.i18n.getLocalizedString.bind(void 0,H);class G extends P{view;constructor(e){super(e,_(j.backForwardCache),!1);const t=d.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"bfcache://"}onselect(e){return super.onselect(e),this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new w.BackForwardCacheView.BackForwardCacheView)),this.showView(this.view),t.userMetrics.panelShown("back-forward-cache"),!1}}class K extends o.SDKModel.SDKModel{backgroundServiceAgent;events;constructor(e){super(e),this.backgroundServiceAgent=e.backgroundServiceAgent(),e.registerBackgroundServiceDispatcher(this),this.events=new Map}enable(e){this.events.set(e,[]),this.backgroundServiceAgent.invoke_startObserving({service:e})}setRecording(e,t){this.backgroundServiceAgent.invoke_setRecording({shouldRecord:e,service:t})}clearEvents(e){this.events.set(e,[]),this.backgroundServiceAgent.invoke_clearEvents({service:e})}getEvents(e){return this.events.get(e)||[]}recordingStateChanged({isRecording:e,service:t}){this.dispatchEventToListeners(z.RecordingStateChanged,{isRecording:e,serviceName:t})}backgroundServiceEventReceived({backgroundServiceEvent:e}){this.events.get(e.service).push(e),this.dispatchEventToListeners(z.BackgroundServiceEventReceived,e)}}var z;o.SDKModel.SDKModel.register(K,{capabilities:1,autostart:!1}),function(e){e.RecordingStateChanged="RecordingStateChanged",e.BackgroundServiceEventReceived="BackgroundServiceEventReceived"}(z||(z={}));var q=Object.freeze({__proto__:null,BackgroundServiceModel:K,get Events(){return z}});const $=new CSSStyleSheet;$.replaceSync(".empty-bold-text{display:block;font-size:1.5em;margin:0.83em 0;font-weight:bold}.empty-view{color:var(--sys-color-token-subtle);padding:30px;text-align:center;min-width:70px}.empty-view-scroller{justify-content:center;overflow:auto}.empty-view p{white-space:initial;line-height:18px;max-width:300px;flex-shrink:0}\n/*# sourceURL=emptyWidget.css */\n");const J=new CSSStyleSheet;J.replaceSync('.background-service-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:var(--sys-color-divider)}.data-grid{flex:auto;border:none}[slot="insertion-point-main"]{overflow:auto}.background-service-preview{position:absolute;background-color:var(--sys-color-cdt-base-container);justify-content:center;align-items:center;overflow:auto;font-size:13px;color:var(--sys-color-on-surface-subtle)}.background-service-preview > div{max-width:450px;margin:10px;text-align:center}.background-service-preview > div > p{flex:none;white-space:pre-line}.background-service-shortcut{color:var(--sys-color-on-surface-subtle)}.background-service-metadata{padding-left:5px;padding-top:10px}.background-service-metadata-entry{padding-left:10px;padding-bottom:5px}.background-service-metadata-name{color:var(--sys-color-on-surface-subtle);display:inline-block;margin-right:0.25em;font-weight:bold}.background-service-metadata-value{display:inline;margin-right:1em;white-space:pre-wrap;word-break:break-all;user-select:text}.background-service-empty-value{color:var(--sys-color-state-disabled);font-style:italic}.background-service-record-inline-button{margin-bottom:6px}\n/*# sourceURL=backgroundServiceView.css */\n');const Q={backgroundFetch:"Background fetch",backgroundSync:"Background sync",pushMessaging:"Push messaging",notifications:"Notifications",paymentHandler:"Payment handler",periodicBackgroundSync:"Periodic background sync",clear:"Clear",saveEvents:"Save events",showEventsFromOtherDomains:"Show events from other domains",showEventsForOtherStorageKeys:"Show events from other storage partitions",stopRecordingEvents:"Stop recording events",startRecordingEvents:"Start recording events",timestamp:"Timestamp",event:"Event",origin:"Origin",storageKey:"Storage Key",swScope:"Service Worker Scope",instanceId:"Instance ID",backgroundServices:"Background services",learnMore:"Learn more",selectAnEntryToViewMetadata:"Select an entry to view metadata",recordingSActivity:"Recording {PH1} activity...",devtoolsWillRecordAllSActivity:"DevTools will record all {PH1} activity for up to 3 days, even when closed.",clickTheRecordButtonSOrHitSTo:"Click the record button {PH1} or hit {PH2} to start recording.",empty:"empty",noMetadataForThisEvent:"No metadata for this event"},X=i.i18n.registerUIStrings("panels/application/BackgroundServiceView.ts",Q),Y=i.i18n.getLocalizedString.bind(void 0,X);class Z extends h.Widget.VBox{serviceName;model;serviceWorkerManager;securityOriginManager;storageKeyManager;recordAction;recordButton;originCheckbox;storageKeyCheckbox;saveButton;toolbar;splitWidget;dataGrid;previewPanel;selectedEventNode;preview;static getUIString(e){switch(e){case"backgroundFetch":return Y(Q.backgroundFetch);case"backgroundSync":return Y(Q.backgroundSync);case"pushMessaging":return Y(Q.pushMessaging);case"notifications":return Y(Q.notifications);case"paymentHandler":return Y(Q.paymentHandler);case"periodicBackgroundSync":return Y(Q.periodicBackgroundSync);default:return""}}constructor(e,t){super(!0),this.serviceName=e;const i=s.StringUtilities.toKebabCase(e);if(this.element.setAttribute("jslog",`${m.pane().context(i)}`),this.model=t,this.model.addEventListener(z.RecordingStateChanged,this.onRecordingStateChanged,this),this.model.addEventListener(z.BackgroundServiceEventReceived,this.onEventReceived,this),this.model.enable(this.serviceName),this.serviceWorkerManager=this.model.target().model(o.ServiceWorkerManager.ServiceWorkerManager),this.securityOriginManager=this.model.target().model(o.SecurityOriginManager.SecurityOriginManager),!this.securityOriginManager)throw new Error("SecurityOriginManager instance is missing");if(this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,(()=>this.onOriginChanged())),this.storageKeyManager=this.model.target().model(o.StorageKeyManager.StorageKeyManager),!this.storageKeyManager)throw new Error("StorageKeyManager instance is missing");this.storageKeyManager.addEventListener("MainStorageKeyChanged",(()=>this.onStorageKeyChanged())),this.recordAction=h.ActionRegistry.ActionRegistry.instance().getAction("background-service.toggle-recording"),this.toolbar=new h.Toolbar.Toolbar("background-service-toolbar",this.contentElement),this.toolbar.element.setAttribute("jslog",`${m.toolbar()}`),this.setupToolbar(),this.splitWidget=new h.SplitWidget.SplitWidget(!1,!0),this.splitWidget.show(this.contentElement),this.dataGrid=this.createDataGrid(),this.previewPanel=new h.Widget.VBox,this.previewPanel.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`),this.selectedEventNode=null,this.preview=null,this.splitWidget.setMainWidget(this.dataGrid.asWidget()),this.splitWidget.setSidebarWidget(this.previewPanel),this.showPreview(null)}getDataGrid(){return this.dataGrid}async setupToolbar(){this.toolbar.makeWrappable(!0),this.recordButton=h.Toolbar.Toolbar.createActionButton(this.recordAction),this.toolbar.appendToolbarItem(this.recordButton);const e=new h.Toolbar.ToolbarButton(Y(Q.clear),"clear",void 0,"background-service.clear");e.addEventListener("Click",(()=>this.clearEvents())),this.toolbar.appendToolbarItem(e),this.toolbar.appendSeparator(),this.saveButton=new h.Toolbar.ToolbarButton(Y(Q.saveEvents),"download",void 0,"background-service.save-events"),this.saveButton.addEventListener("Click",(e=>{this.saveToFile()})),this.saveButton.setEnabled(!1),this.toolbar.appendToolbarItem(this.saveButton),this.toolbar.appendSeparator(),this.originCheckbox=new h.Toolbar.ToolbarCheckbox(Y(Q.showEventsFromOtherDomains),Y(Q.showEventsFromOtherDomains),(()=>this.refreshView()),"show-events-from-other-domains"),this.toolbar.appendToolbarItem(this.originCheckbox),this.storageKeyCheckbox=new h.Toolbar.ToolbarCheckbox(Y(Q.showEventsForOtherStorageKeys),Y(Q.showEventsForOtherStorageKeys),(()=>this.refreshView()),"show-events-from-other-partitions"),this.toolbar.appendToolbarItem(this.storageKeyCheckbox)}refreshView(){this.clearView();const e=this.model.getEvents(this.serviceName).filter((e=>this.acceptEvent(e)));for(const t of e)this.addEvent(t)}clearView(){this.selectedEventNode=null,this.dataGrid.rootNode().removeChildren(),this.saveButton.setEnabled(!1),this.showPreview(null)}toggleRecording(){this.model.setRecording(!this.recordButton.toggled(),this.serviceName)}clearEvents(){this.model.clearEvents(this.serviceName),this.clearView()}onRecordingStateChanged({data:e}){e.serviceName===this.serviceName&&e.isRecording!==this.recordButton.toggled()&&(this.recordButton.setToggled(e.isRecording),this.updateRecordButtonTooltip(),this.showPreview(this.selectedEventNode))}updateRecordButtonTooltip(){const e=this.recordButton.toggled()?Y(Q.stopRecordingEvents):Y(Q.startRecordingEvents);this.recordButton.setTitle(e,"background-service.toggle-recording")}onEventReceived({data:e}){this.acceptEvent(e)&&this.addEvent(e)}onOriginChanged(){this.originCheckbox.checked()||this.refreshView()}onStorageKeyChanged(){this.storageKeyCheckbox.checked()||this.refreshView()}addEvent(e){const t=this.createEventData(e),i=new ee(t,e.eventMetadata);this.dataGrid.rootNode().appendChild(i),1===this.dataGrid.rootNode().children.length&&(this.saveButton.setEnabled(!0),this.showPreview(this.selectedEventNode))}createDataGrid(){const e=[{id:"id",title:"#",weight:1},{id:"timestamp",title:Y(Q.timestamp),weight:7},{id:"event-name",title:Y(Q.event),weight:8},{id:"origin",title:Y(Q.origin),weight:8},{id:"storage-key",title:Y(Q.storageKey),weight:8},{id:"sw-scope",title:Y(Q.swScope),weight:4},{id:"instance-id",title:Y(Q.instanceId),weight:8}],t=new b.DataGrid.DataGridImpl({displayName:Y(Q.backgroundServices),columns:e,editCallback:void 0,refreshCallback:void 0,deleteCallback:void 0});return t.setStriped(!0),t.addEventListener("SelectedNode",(e=>this.showPreview(e.data))),t}createEventData(e){let t="";const i=this.serviceWorkerManager?this.serviceWorkerManager.registrations().get(e.serviceWorkerRegistrationId):void 0;return i&&(t=i.scopeURL.substr(i.securityOrigin.length)),{id:this.dataGrid.rootNode().children.length+1,timestamp:h.UIUtils.formatTimestamp(1e3*e.timestamp,!0),origin:e.origin,"storage-key":e.storageKey,"sw-scope":t,"event-name":e.eventName,"instance-id":e.instanceId}}acceptEvent(e){if(e.service!==this.serviceName)return!1;if(this.originCheckbox.checked()||this.storageKeyCheckbox.checked())return!0;const t=e.origin.substr(0,e.origin.length-1),i=e.storageKey;return this.securityOriginManager.securityOrigins().includes(t)||this.storageKeyManager.storageKeys().includes(i)}createLearnMoreLink(){let e="https://developer.chrome.com/docs/devtools/javascript/background-services/?utm_source=devtools";switch(this.serviceName){case"backgroundFetch":e+="#fetch";break;case"backgroundSync":e+="#sync";break;case"pushMessaging":e+="#push";break;case"notifications":e+="#notifications"}return h.XLink.XLink.create(e,Y(Q.learnMore),void 0,void 0,"learn-more")}showPreview(e){if(this.selectedEventNode&&this.selectedEventNode===e)return;if(this.selectedEventNode=e,this.preview&&this.preview.detach(),this.selectedEventNode)return this.preview=this.selectedEventNode.createPreview(),void this.preview.show(this.previewPanel.contentElement);this.preview=new h.Widget.VBox,this.preview.contentElement.classList.add("background-service-preview","fill");const t=this.preview.contentElement.createChild("div");if(this.dataGrid.rootNode().children.length)t.createChild("p").textContent=Y(Q.selectAnEntryToViewMetadata);else if(this.recordButton.toggled()){const e=Z.getUIString(this.serviceName).toLowerCase();t.createChild("p").textContent=Y(Q.recordingSActivity,{PH1:e}),t.createChild("p").textContent=Y(Q.devtoolsWillRecordAllSActivity,{PH1:e})}else{const e=h.Toolbar.Toolbar.createActionButton(this.recordAction),s=document.createElement("b");s.classList.add("background-service-shortcut"),s.textContent=h.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("background-service.toggle-recording")[0].title();const r=h.UIUtils.createInlineButton(e);r.classList.add("background-service-record-inline-button"),t.createChild("p").appendChild(i.i18n.getFormatLocalizedString(X,Q.clickTheRecordButtonSOrHitSTo,{PH1:r,PH2:s})),t.appendChild(this.createLearnMoreLink())}this.preview.show(this.previewPanel.contentElement)}async saveToFile(){const e=`${this.serviceName}-${s.DateUtilities.toISO8601Compact(new Date)}.json`,t=new S.FileUtils.FileOutputStream;if(!await t.open(e))return;const i=this.model.getEvents(this.serviceName).filter((e=>this.acceptEvent(e)));await t.write(JSON.stringify(i,void 0,2)),t.close()}wasShown(){super.wasShown(),this.registerCSSFiles([$,J])}}class ee extends b.DataGrid.DataGridNode{eventMetadata;constructor(e,t){super(e),this.eventMetadata=t.sort(((e,t)=>s.StringUtilities.compare(e.key,t.key)))}createPreview(){const e=new h.Widget.VBox;e.element.classList.add("background-service-metadata"),e.element.setAttribute("jslog",`${m.section("metadata")}`);for(const t of this.eventMetadata){const i=document.createElement("div");i.classList.add("background-service-metadata-entry"),i.createChild("div","background-service-metadata-name").textContent=t.key+": ",t.value?i.createChild("div","background-service-metadata-value source-code").textContent=t.value:i.createChild("div","background-service-metadata-value background-service-empty-value").textContent=Y(Q.empty),e.element.appendChild(i)}if(!e.element.children.length){const t=document.createElement("div");t.classList.add("background-service-metadata-entry"),t.createChild("div","background-service-metadata-name background-service-empty-value").textContent=Y(Q.noMetadataForThisEvent),e.element.appendChild(t)}return e}}var te=Object.freeze({__proto__:null,BackgroundServiceView:Z,EventDataNode:ee,ActionDelegate:class{handleAction(e,t){const i=e.flavor(Z);if("background-service.toggle-recording"===t){if(!i)throw new Error("BackgroundServiceView instance is missing");return i.toggleRecording(),!0}return!1}}});const ie={bounceTrackingMitigations:"Bounce tracking mitigations"},se=i.i18n.registerUIStrings("panels/application/BounceTrackingMitigationsTreeElement.ts",ie),re=i.i18n.getLocalizedString.bind(void 0,se);class ae extends P{view;constructor(e){super(e,re(ie.bounceTrackingMitigations),!1);const t=d.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"bounce-tracking-mitigations://"}onselect(e){return super.onselect(e),this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new w.BounceTrackingMitigationsView.BounceTrackingMitigationsView)),this.showView(this.view),t.userMetrics.panelShown("bounce-tracking-mitigations"),!1}}var oe=Object.freeze({__proto__:null,i18nString:re,BounceTrackingMitigationsTreeElement:ae});class ne extends e.ObjectWrapper.ObjectWrapper{model;storageKeyInternal;isLocalStorageInternal;constructor(e,t,i){super(),this.model=e,this.storageKeyInternal=t,this.isLocalStorageInternal=i}static storageId(e,t){return{storageKey:e,isLocalStorage:t}}get id(){return ne.storageId(this.storageKeyInternal,this.isLocalStorageInternal)}get storageKey(){return this.storageKeyInternal}get isLocalStorage(){return this.isLocalStorageInternal}getItems(){return this.model.agent.invoke_getDOMStorageItems({storageId:this.id}).then((({entries:e})=>e))}setItem(e,t){this.model.agent.invoke_setDOMStorageItem({storageId:this.id,key:e,value:t})}removeItem(e){this.model.agent.invoke_removeDOMStorageItem({storageId:this.id,key:e})}clear(){this.model.agent.invoke_clear({storageId:this.id})}}class de extends o.SDKModel.SDKModel{storageKeyManagerInternal;storagesInternal;agent;enabled;constructor(e){super(e),this.storageKeyManagerInternal=e.model(o.StorageKeyManager.StorageKeyManager),this.storagesInternal={},this.agent=e.domstorageAgent()}get storageKeyManagerForTest(){return this.storageKeyManagerInternal}enable(){if(!this.enabled){if(this.target().registerDOMStorageDispatcher(new le(this)),this.storageKeyManagerInternal){this.storageKeyManagerInternal.addEventListener("StorageKeyAdded",this.storageKeyAdded,this),this.storageKeyManagerInternal.addEventListener("StorageKeyRemoved",this.storageKeyRemoved,this);for(const e of this.storageKeyManagerInternal.storageKeys())this.addStorageKey(e)}this.agent.invoke_enable(),this.enabled=!0}}clearForStorageKey(e){if(this.enabled){for(const t of[!0,!1]){const i=this.storageKey(e,t),s=this.storagesInternal[i];if(!s)return;s.clear()}this.removeStorageKey(e),this.addStorageKey(e)}}storageKeyAdded(e){this.addStorageKey(e.data)}addStorageKey(e){for(const t of[!0,!1]){const i=this.storageKey(e,t);console.assert(!this.storagesInternal[i]);const s=new ne(this,e,t);this.storagesInternal[i]=s,this.dispatchEventToListeners("DOMStorageAdded",s)}}storageKeyRemoved(e){this.removeStorageKey(e.data)}removeStorageKey(e){for(const t of[!0,!1]){const i=this.storageKey(e,t),s=this.storagesInternal[i];s&&(delete this.storagesInternal[i],this.dispatchEventToListeners("DOMStorageRemoved",s))}}storageKey(e,t){return JSON.stringify(ne.storageId(e,t))}domStorageItemsCleared(e){const t=this.storageForId(e);t&&t.dispatchEventToListeners("DOMStorageItemsCleared")}domStorageItemRemoved(e,t){const i=this.storageForId(e);if(!i)return;const s={key:t};i.dispatchEventToListeners("DOMStorageItemRemoved",s)}domStorageItemAdded(e,t,i){const s=this.storageForId(e);if(!s)return;const r={key:t,value:i};s.dispatchEventToListeners("DOMStorageItemAdded",r)}domStorageItemUpdated(e,t,i,s){const r=this.storageForId(e);if(!r)return;const a={key:t,oldValue:i,value:s};r.dispatchEventToListeners("DOMStorageItemUpdated",a)}storageForId(e){return console.assert(Boolean(e.storageKey)),this.storagesInternal[this.storageKey(e.storageKey||"",e.isLocalStorage)]}storages(){const e=[];for(const t in this.storagesInternal)e.push(this.storagesInternal[t]);return e}}o.SDKModel.SDKModel.register(de,{capabilities:2,autostart:!1});class le{model;constructor(e){this.model=e}domStorageItemsCleared({storageId:e}){this.model.domStorageItemsCleared(e)}domStorageItemRemoved({storageId:e,key:t}){this.model.domStorageItemRemoved(e,t)}domStorageItemAdded({storageId:e,key:t,newValue:i}){this.model.domStorageItemAdded(e,t,i)}domStorageItemUpdated({storageId:e,key:t,oldValue:i,newValue:s}){this.model.domStorageItemUpdated(e,t,i,s)}}var ce=Object.freeze({__proto__:null,DOMStorage:ne,DOMStorageModel:de,DOMStorageDispatcher:le});const he="";class ge extends o.SDKModel.SDKModel{storageBucketModel;indexedDBAgent;storageAgent;databasesInternal;databaseNamesByStorageKeyAndBucket;updatedStorageBuckets;throttler;enabled;constructor(t){super(t),t.registerStorageDispatcher(this),this.storageBucketModel=t.model(o.StorageBucketsModel.StorageBucketsModel),this.indexedDBAgent=t.indexedDBAgent(),this.storageAgent=t.storageAgent(),this.databasesInternal=new Map,this.databaseNamesByStorageKeyAndBucket=new Map,this.updatedStorageBuckets=new Set,this.throttler=new e.Throttler.Throttler(1e3)}static keyFromIDBKey(e){if(null==e)return;let t;switch(typeof e){case"number":t={type:"number",number:e};break;case"string":t={type:"string",string:e};break;case"object":if(e instanceof Date)t={type:"date",date:e.getTime()};else{if(!Array.isArray(e))return;{const i=[];for(let t=0;t<e.length;++t){const s=ge.keyFromIDBKey(e[t]);s&&i.push(s)}t={type:"array",array:i}}}break;default:return}return t}static keyRangeFromIDBKeyRange(e){return{lower:ge.keyFromIDBKey(e.lower),upper:ge.keyFromIDBKey(e.upper),lowerOpen:Boolean(e.lowerOpen),upperOpen:Boolean(e.upperOpen)}}static idbKeyPathFromKeyPath(e){let t;switch(e.type){case"null":t=null;break;case"string":t=e.string;break;case"array":t=e.array}return t}static keyPathStringFromIDBKeyPath(e){return"string"==typeof e?'"'+e+'"':e instanceof Array?'["'+e.join('", "')+'"]':null}enable(){if(!this.enabled){if(this.indexedDBAgent.invoke_enable(),this.storageBucketModel){this.storageBucketModel.addEventListener("BucketAdded",this.storageBucketAdded,this),this.storageBucketModel.addEventListener("BucketRemoved",this.storageBucketRemoved,this);for(const{bucket:e}of this.storageBucketModel.getBuckets())this.addStorageBucket(e)}this.enabled=!0}}clearForStorageKey(e){if(!this.enabled||!this.databaseNamesByStorageKeyAndBucket.has(e))return;for(const[t]of this.databaseNamesByStorageKeyAndBucket.get(e)||[]){const i=this.storageBucketModel?.getBucketByName(e,t??void 0)?.bucket;i&&this.removeStorageBucket(i)}this.databaseNamesByStorageKeyAndBucket.delete(e);const t=this.storageBucketModel?.getBucketsForStorageKey(e)||[];for(const{bucket:e}of t)this.addStorageBucket(e)}async deleteDatabase(e){this.enabled&&(await this.indexedDBAgent.invoke_deleteDatabase({storageBucket:e.storageBucket,databaseName:e.name}),this.loadDatabaseNamesByStorageBucket(e.storageBucket))}async refreshDatabaseNames(){for(const[e]of this.databaseNamesByStorageKeyAndBucket){const t=this.databaseNamesByStorageKeyAndBucket.get(e)?.keys()||[];for(const i of t){const t=this.storageBucketModel?.getBucketByName(e,i??void 0)?.bucket;t&&await this.loadDatabaseNamesByStorageBucket(t)}}this.dispatchEventToListeners(ue.DatabaseNamesRefreshed)}refreshDatabase(e){this.loadDatabase(e,!0)}async clearObjectStore(e,t){await this.indexedDBAgent.invoke_clearObjectStore({storageBucket:e.storageBucket,databaseName:e.name,objectStoreName:t})}async deleteEntries(e,t,i){const s=ge.keyRangeFromIDBKeyRange(i);await this.indexedDBAgent.invoke_deleteObjectStoreEntries({storageBucket:e.storageBucket,databaseName:e.name,objectStoreName:t,keyRange:s})}storageBucketAdded({data:{bucketInfo:{bucket:e}}}){this.addStorageBucket(e)}storageBucketRemoved({data:{bucketInfo:{bucket:e}}}){this.removeStorageBucket(e)}addStorageBucket(e){const{storageKey:t}=e;this.databaseNamesByStorageKeyAndBucket.has(t)||(this.databaseNamesByStorageKeyAndBucket.set(t,new Map),this.storageAgent.invoke_trackIndexedDBForStorageKey({storageKey:t}));const i=this.databaseNamesByStorageKeyAndBucket.get(t)||new Map;console.assert(!i.has(e.name??he)),i.set(e.name??he,new Set),this.loadDatabaseNamesByStorageBucket(e)}removeStorageBucket(e){const{storageKey:t}=e;console.assert(this.databaseNamesByStorageKeyAndBucket.has(t));const i=this.databaseNamesByStorageKeyAndBucket.get(t)||new Map;console.assert(i.has(e.name??he));const s=i.get(e.name??he)||new Map;for(const e of s)this.databaseRemovedForStorageBucket(e);i.delete(e.name??he),0===i.size&&(this.databaseNamesByStorageKeyAndBucket.delete(t),this.storageAgent.invoke_untrackIndexedDBForStorageKey({storageKey:t}))}updateStorageKeyDatabaseNames(e,t){const i=this.databaseNamesByStorageKeyAndBucket.get(e.storageKey);if(void 0===i)return;const s=new Set(t.map((t=>new me(e,t)))),r=new Set(i.get(e.name??he));i.set(e.name??he,s);for(const e of r)e.inSet(s)||this.databaseRemovedForStorageBucket(e);for(const e of s)e.inSet(r)||this.databaseAddedForStorageBucket(e)}databases(){const e=[];for(const[,t]of this.databaseNamesByStorageKeyAndBucket)for(const[,i]of t)for(const t of i)e.push(t);return e}databaseAddedForStorageBucket(e){this.dispatchEventToListeners(ue.DatabaseAdded,{model:this,databaseId:e})}databaseRemovedForStorageBucket(e){this.dispatchEventToListeners(ue.DatabaseRemoved,{model:this,databaseId:e})}async loadDatabaseNamesByStorageBucket(e){const{storageKey:t}=e,{databaseNames:i}=await this.indexedDBAgent.invoke_requestDatabaseNames({storageBucket:e});if(!i)return[];if(!this.databaseNamesByStorageKeyAndBucket.has(t))return[];return(this.databaseNamesByStorageKeyAndBucket.get(t)||new Map).has(e.name??he)?(this.updateStorageKeyDatabaseNames(e,i),i):[]}async loadDatabase(e,t){const i=(await this.indexedDBAgent.invoke_requestDatabase({storageBucket:e.storageBucket,databaseName:e.name})).databaseWithObjectStores;if(!this.databaseNamesByStorageKeyAndBucket.get(e.storageBucket.storageKey)?.has(e.storageBucket.name??he))return;if(!i)return;const s=new we(e,i.version);this.databasesInternal.set(e,s);for(const e of i.objectStores){const t=ge.idbKeyPathFromKeyPath(e.keyPath),i=new ve(e.name,t,e.autoIncrement);for(let t=0;t<e.indexes.length;++t){const s=e.indexes[t],r=ge.idbKeyPathFromKeyPath(s.keyPath),a=new Se(s.name,r,s.unique,s.multiEntry);i.indexes.set(a.name,a)}s.objectStores.set(i.name,i)}this.dispatchEventToListeners(ue.DatabaseLoaded,{model:this,database:s,entriesUpdated:t})}loadObjectStoreData(e,t,i,s,r,a){this.requestData(e,e.name,t,"",i,s,r,a)}loadIndexData(e,t,i,s,r,a,o){this.requestData(e,e.name,t,i,s,r,a,o)}async requestData(e,t,i,s,r,a,n,d){const l=r?ge.keyRangeFromIDBKeyRange(r):void 0,c=this.target().model(o.RuntimeModel.RuntimeModel),h=await this.indexedDBAgent.invoke_requestData({storageBucket:e.storageBucket,databaseName:t,objectStoreName:i,indexName:s,skipCount:a,pageSize:n,keyRange:l});if(!c||!this.databaseNamesByStorageKeyAndBucket.get(e.storageBucket.storageKey)?.has(e.storageBucket.name??he))return;if(h.getError())return void console.error("IndexedDBAgent error: "+h.getError());const g=h.objectStoreDataEntries,u=[];for(const e of g){const t=c?.createRemoteObject(e.key),i=c?.createRemoteObject(e.primaryKey),s=c?.createRemoteObject(e.value);if(!t||!i||!s)return;u.push(new pe(t,i,s))}d(u,h.hasMore)}async getMetadata(e,t){const i=e.name,s=t.name,r=await this.indexedDBAgent.invoke_getMetadata({storageBucket:e.storageBucket,databaseName:i,objectStoreName:s});return r.getError()?(console.error("IndexedDBAgent error: "+r.getError()),null):{entriesCount:r.entriesCount,keyGeneratorValue:r.keyGeneratorValue}}async refreshDatabaseListForStorageBucket(e){const t=await this.loadDatabaseNamesByStorageBucket(e);for(const i of t)this.loadDatabase(new me(e,i),!1)}indexedDBListUpdated({storageKey:e,bucketId:t}){const i=this.storageBucketModel?.getBucketById(t)?.bucket;e&&i&&(this.updatedStorageBuckets.add(i),this.throttler.schedule((()=>{const e=Array.from(this.updatedStorageBuckets,(e=>{this.refreshDatabaseListForStorageBucket(e)}));return this.updatedStorageBuckets.clear(),Promise.all(e)})))}indexedDBContentUpdated({bucketId:e,databaseName:t,objectStoreName:i}){const s=this.storageBucketModel?.getBucketById(e)?.bucket;if(s){const e=new me(s,t);this.dispatchEventToListeners(ue.IndexedDBContentUpdated,{databaseId:e,objectStoreName:i,model:this})}}attributionReportingTriggerRegistered(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}var ue;o.SDKModel.SDKModel.register(ge,{capabilities:8192,autostart:!1}),function(e){e.DatabaseAdded="DatabaseAdded",e.DatabaseRemoved="DatabaseRemoved",e.DatabaseLoaded="DatabaseLoaded",e.DatabaseNamesRefreshed="DatabaseNamesRefreshed",e.IndexedDBContentUpdated="IndexedDBContentUpdated"}(ue||(ue={}));class pe{key;primaryKey;value;constructor(e,t,i){this.key=e,this.primaryKey=t,this.value=i}}class me{storageBucket;name;constructor(e,t){this.storageBucket=e,this.name=t}inBucket(e){return this.storageBucket.name===e.name}equals(e){return this.name===e.name&&this.storageBucket.name===e.storageBucket.name&&this.storageBucket.storageKey===e.storageBucket.storageKey}inSet(e){for(const t of e)if(this.equals(t))return!0;return!1}}class we{databaseId;version;objectStores;constructor(e,t){this.databaseId=e,this.version=t,this.objectStores=new Map}}class ve{name;keyPath;autoIncrement;indexes;constructor(e,t,i){this.name=e,this.keyPath=t,this.autoIncrement=i,this.indexes=new Map}get keyPathString(){return ge.keyPathStringFromIDBKeyPath(this.keyPath)}}class Se{name;keyPath;unique;multiEntry;constructor(e,t,i,s){this.name=e,this.keyPath=t,this.unique=i,this.multiEntry=s}get keyPathString(){return ge.keyPathStringFromIDBKeyPath(this.keyPath)}}var be=Object.freeze({__proto__:null,IndexedDBModel:ge,get Events(){return ue},Entry:pe,DatabaseId:me,Database:we,ObjectStore:ve,Index:Se});const ke=new CSSStyleSheet;ke.replaceSync(".indexed-db-data-view .data-view-toolbar{position:relative;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.indexed-db-data-view .data-grid{flex:auto}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(1){background-color:var(--sys-color-cdt-base-container)}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(1) td{border:0}.indexed-db-data-view .data-grid .data-container tr:nth-last-child(2) td{border-bottom:1px solid var(--sys-color-divider)}.indexed-db-data-view .data-grid:focus .data-container tr.selected{background-color:var(--sys-color-tonal-container);color:inherit}.indexed-db-data-view .section,\n.indexed-db-data-view .section > .header,\n.indexed-db-data-view .section > .header .title{margin:0;min-height:inherit;line-height:inherit}.indexed-db-data-view .data-grid .data-container td .section .header .title{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.indexed-db-key-path{color:var(--sys-color-error);white-space:pre-wrap;unicode-bidi:-webkit-isolate}.indexed-db-container{overflow:auto}.indexed-db-header{min-width:400px;flex-shrink:0;flex-grow:0}.source-code.indexed-db-key-path{font-size:unset!important}.resources-toolbar{padding-right:10px}.object-store-summary-bar{flex:0 0 27px;line-height:27px;padding-left:5px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden}\n/*# sourceURL=indexedDBViews.css */\n");const ye={version:"Version",objectStores:"Object stores",deleteDatabase:"Delete database",refreshDatabase:"Refresh database",pleaseConfirmDeleteOfSDatabase:'Please confirm delete of "{PH1}" database.',idb:"IDB",refresh:"Refresh",deleteSelected:"Delete selected",clearObjectStore:"Clear object store",dataMayBeStale:"Data may be stale",someEntriesMayHaveBeenModified:"Some entries may have been modified",keyString:"Key",primaryKey:"Primary key",valueString:"Value",indexedDb:"Indexed DB",keyPath:"Key path: ",showPreviousPage:"Show previous page",showNextPage:"Show next page",startFromKey:"Start from key",expandRecursively:"Expand Recursively",collapse:"Collapse",totalEntriesS:"Total entries: {PH1}",keyGeneratorValueS:"Key generator value: {PH1}"},fe=i.i18n.registerUIStrings("panels/application/IndexedDBViews.ts",ye),Ce=i.i18n.getLocalizedString.bind(void 0,fe);class Te extends w.StorageMetadataView.StorageMetadataView{model;database;constructor(e,t){super(),this.model=e,t&&this.update(t)}getTitle(){return this.database?.databaseId.name}async renderReportContent(){return this.database?f.html`
      ${await super.renderReportContent()}
      ${this.key(Ce(ye.version))}
      ${this.value(this.database.version.toString())}
      ${this.key(Ce(ye.objectStores))}
      ${this.value(this.database.objectStores.size.toString())}
      <${k.ReportView.ReportSectionDivider.litTagName}></${k.ReportView.ReportSectionDivider.litTagName}>
      <${k.ReportView.ReportSection.litTagName}>
      <${g.Button.Button.litTagName}
          aria-label=${Ce(ye.deleteDatabase)}
          .variant=${"secondary"}
          @click=${this.deleteDatabase}
          jslog=${m.action("delete-database").track({click:!0})}>
        ${Ce(ye.deleteDatabase)}
      </${g.Button.Button.litTagName}>&nbsp;
      <${g.Button.Button.litTagName}
          aria-label=${Ce(ye.refreshDatabase)}
          .variant=${"secondary"}
          @click=${this.refreshDatabaseButtonClicked}
          jslog=${m.action("refresh-database").track({click:!0})}>
        ${Ce(ye.refreshDatabase)}
      </${g.Button.Button.litTagName}>
      </${k.ReportView.ReportSection.litTagName}>
      `:f.nothing}refreshDatabaseButtonClicked(){this.model.refreshDatabase(this.database.databaseId)}update(e){this.database=e;const t=this.model.target().model(o.StorageBucketsModel.StorageBucketsModel)?.getBucketByName(e.databaseId.storageBucket.storageKey,e.databaseId.storageBucket.name);t?this.setStorageBucket(t):this.setStorageKey(e.databaseId.storageBucket.storageKey),this.render().then((()=>this.updatedForTests()))}updatedForTests(){}async deleteDatabase(){await h.UIUtils.ConfirmDialog.show(Ce(ye.pleaseConfirmDeleteOfSDatabase,{PH1:this.database.databaseId.name}),this,{jslogContext:"delete-database-confirmation"})&&this.model.deleteDatabase(this.database.databaseId)}wasShown(){super.wasShown()}}customElements.define("devtools-idb-database-view",Te);class Ie extends h.View.SimpleView{model;databaseId;isIndex;refreshObjectStoreCallback;refreshButton;deleteSelectedButton;clearButton;needsRefresh;clearingObjectStore;pageSize;skipCount;entries;objectStore;index;keyInput;dataGrid;previouslySelectedNode;lastPageSize;lastSkipCount;pageBackButton;pageForwardButton;lastKey;summaryBarElement;constructor(e,t,i,s,r){super(Ce(ye.idb)),this.model=e,this.databaseId=t,this.isIndex=Boolean(s),this.refreshObjectStoreCallback=r,this.element.classList.add("indexed-db-data-view","storage-view"),this.element.setAttribute("jslog",`${m.pane("indexed-db-data-view")}`),this.refreshButton=new h.Toolbar.ToolbarButton(Ce(ye.refresh),"refresh"),this.refreshButton.addEventListener("Click",this.refreshButtonClicked,this),this.refreshButton.element.setAttribute("jslog",`${m.action("refresh").track({click:!0})}`),this.deleteSelectedButton=new h.Toolbar.ToolbarButton(Ce(ye.deleteSelected),"bin"),this.deleteSelectedButton.addEventListener("Click",(e=>{this.deleteButtonClicked(null)})),this.deleteSelectedButton.element.setAttribute("jslog",`${m.action("delete-selected").track({click:!0})}`),this.clearButton=new h.Toolbar.ToolbarButton(Ce(ye.clearObjectStore),"clear"),this.clearButton.addEventListener("Click",(()=>{this.clearButtonClicked()}),this),this.clearButton.element.setAttribute("jslog",`${m.action("clear-all").track({click:!0})}`);const a=h.UIUtils.createIconLabel({title:Ce(ye.dataMayBeStale),iconName:"warning",color:"var(--icon-warning)",width:"20px",height:"20px"});this.needsRefresh=new h.Toolbar.ToolbarItem(a),this.needsRefresh.setVisible(!1),this.needsRefresh.setTitle(Ce(ye.someEntriesMayHaveBeenModified)),this.clearingObjectStore=!1,this.createEditorToolbar(),this.pageSize=50,this.skipCount=0,this.update(i,s),this.entries=[]}createDataGrid(){const e=this.isIndex&&this.index?this.index.keyPath:this.objectStore.keyPath,t=[],i={title:void 0,titleDOMFragment:void 0,sortable:!1,sort:void 0,align:void 0,width:void 0,fixedWidth:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0};t.push({...i,id:"number",title:"#",sortable:!1,width:"50px"}),t.push({...i,id:"key",titleDOMFragment:this.keyColumnHeaderFragment(Ce(ye.keyString),e),sortable:!1}),this.isIndex&&t.push({...i,id:"primary-key",titleDOMFragment:this.keyColumnHeaderFragment(Ce(ye.primaryKey),this.objectStore.keyPath),sortable:!1});const s=Ce(ye.valueString);t.push({...i,id:"value",title:s,sortable:!1});const r=new b.DataGrid.DataGridImpl({displayName:Ce(ye.indexedDb),columns:t,deleteCallback:this.deleteButtonClicked.bind(this),refreshCallback:this.updateData.bind(this,!0),editCallback:void 0});return r.setStriped(!0),r.addEventListener("SelectedNode",(()=>{this.updateToolbarEnablement(),this.updateSelectionColor()}),this),r}keyColumnHeaderFragment(e,t){const i=document.createDocumentFragment();if(h.UIUtils.createTextChild(i,e),null===t)return i;if(h.UIUtils.createTextChild(i," ("+Ce(ye.keyPath)),Array.isArray(t)){h.UIUtils.createTextChild(i,"[");for(let e=0;e<t.length;++e)0!==e&&h.UIUtils.createTextChild(i,", "),i.appendChild(this.keyPathStringFragment(t[e]));h.UIUtils.createTextChild(i,"]")}else{const e=t;i.appendChild(this.keyPathStringFragment(e))}return h.UIUtils.createTextChild(i,")"),i}keyPathStringFragment(e){const t=document.createDocumentFragment();h.UIUtils.createTextChild(t,'"');return t.createChild("span","source-code indexed-db-key-path").textContent=e,h.UIUtils.createTextChild(t,'"'),t}createEditorToolbar(){const e=new h.Toolbar.Toolbar("data-view-toolbar",this.element);e.element.setAttribute("jslog",`${m.toolbar()}`),e.appendToolbarItem(this.refreshButton),e.appendToolbarItem(this.clearButton),e.appendToolbarItem(this.deleteSelectedButton),e.appendToolbarItem(new h.Toolbar.ToolbarSeparator),this.pageBackButton=new h.Toolbar.ToolbarButton(Ce(ye.showPreviousPage),"triangle-left",void 0,"prev-page"),this.pageBackButton.addEventListener("Click",this.pageBackButtonClicked,this),e.appendToolbarItem(this.pageBackButton),this.pageForwardButton=new h.Toolbar.ToolbarButton(Ce(ye.showNextPage),"triangle-right",void 0,"next-page"),this.pageForwardButton.setEnabled(!1),this.pageForwardButton.addEventListener("Click",this.pageForwardButtonClicked,this),e.appendToolbarItem(this.pageForwardButton),this.keyInput=new h.Toolbar.ToolbarInput(Ce(ye.startFromKey),"",.5),this.keyInput.addEventListener("TextChanged",this.updateData.bind(this,!1)),e.appendToolbarItem(this.keyInput),e.appendToolbarItem(new h.Toolbar.ToolbarSeparator),e.appendToolbarItem(this.needsRefresh)}pageBackButtonClicked(){this.skipCount=Math.max(0,this.skipCount-this.pageSize),this.updateData(!1)}pageForwardButtonClicked(){this.skipCount=this.skipCount+this.pageSize,this.updateData(!1)}populateContextMenu(e,t){const i=t;i.valueObjectPresentation&&(e.revealSection().appendItem(Ce(ye.expandRecursively),(()=>{i.valueObjectPresentation&&i.valueObjectPresentation.objectTreeElement().expandRecursively()}),{jslogContext:"expand-recursively"}),e.revealSection().appendItem(Ce(ye.collapse),(()=>{i.valueObjectPresentation&&i.valueObjectPresentation.objectTreeElement().collapse()}),{jslogContext:"collapse"}))}refreshData(){this.updateData(!0)}update(e,t){this.objectStore=e,this.index=t,this.dataGrid&&this.dataGrid.asWidget().detach(),this.dataGrid=this.createDataGrid(),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this)),this.dataGrid.asWidget().show(this.element),this.skipCount=0,this.updateData(!0)}parseKey(e){let t;try{t=JSON.parse(e)}catch(i){t=e}return t}updateData(e){const t=this.parseKey(this.keyInput.value()),i=this.pageSize;let s=this.skipCount,r=this.dataGrid.selectedNode?this.dataGrid.selectedNode.data.number:0;if(r=Math.max(r,this.skipCount),this.clearButton.setEnabled(!this.isIndex),!e&&this.lastKey===t&&this.lastPageSize===i&&this.lastSkipCount===s)return;function a(e,t){this.clear(),this.entries=e;let i=null;for(let t=0;t<e.length;++t){const a={};a.number=t+s,a.key=e[t].key,a["primary-key"]=e[t].primaryKey,a.value=e[t].value;const o=new Ee(a);this.dataGrid.rootNode().appendChild(o),a.number<=r&&(i=o)}i&&i.select(),this.pageBackButton.setEnabled(Boolean(s)),this.pageForwardButton.setEnabled(t),this.needsRefresh.setVisible(!1),this.updateToolbarEnablement(),this.updateSelectionColor(),this.updatedDataForTests()}this.lastKey===t&&this.lastPageSize===i||(s=0,this.skipCount=0),this.lastKey=t,this.lastPageSize=i,this.lastSkipCount=s;const o=t?window.IDBKeyRange.lowerBound(t):null;this.isIndex&&this.index?this.model.loadIndexData(this.databaseId,this.objectStore.name,this.index.name,o,s,i,a.bind(this)):this.model.loadObjectStoreData(this.databaseId,this.objectStore.name,o,s,i,a.bind(this)),this.model.getMetadata(this.databaseId,this.objectStore).then(this.updateSummaryBar.bind(this))}updateSummaryBar(e){if(this.summaryBarElement||(this.summaryBarElement=this.element.createChild("div","object-store-summary-bar")),this.summaryBarElement.removeChildren(),!e)return;const t=this.summaryBarElement.createChild("span");t.textContent=Ce(ye.totalEntriesS,{PH1:String(e.entriesCount)}),this.objectStore.autoIncrement&&(t.textContent+=" ❘ ",t.textContent+=Ce(ye.keyGeneratorValueS,{PH1:String(e.keyGeneratorValue)}))}updatedDataForTests(){}refreshButtonClicked(){this.updateData(!0)}async clearButtonClicked(){this.clearButton.setEnabled(!1),this.clearingObjectStore=!0,await this.model.clearObjectStore(this.databaseId,this.objectStore.name),this.clearingObjectStore=!1,this.clearButton.setEnabled(!0),this.updateData(!0)}markNeedsRefresh(){this.clearingObjectStore||this.needsRefresh.setVisible(!0)}async deleteButtonClicked(e){if(!e&&!(e=this.dataGrid.selectedNode))return;const t=(this.isIndex?e.data["primary-key"]:e.data.key).value;await this.model.deleteEntries(this.databaseId,this.objectStore.name,window.IDBKeyRange.only(t)),this.refreshObjectStoreCallback()}clear(){this.dataGrid.rootNode().removeChildren(),this.entries=[]}updateToolbarEnablement(){const e=!this.dataGrid||0===this.dataGrid.rootNode().children.length;this.deleteSelectedButton.setEnabled(!e&&null!==this.dataGrid.selectedNode)}updateSelectionColor(){this.previouslySelectedNode&&this.previouslySelectedNode.element().querySelectorAll(".source-code").forEach((e=>{const t=e.shadowRoot;t?.adoptedStyleSheets.pop()})),this.previouslySelectedNode=this.dataGrid.selectedNode??void 0,this.dataGrid.selectedNode?.element().querySelectorAll(".source-code").forEach((e=>{const t=e.shadowRoot,i=new CSSStyleSheet;i.replaceSync("::selection {background-color: var(--sys-color-state-focus-select);}"),t?.adoptedStyleSheets.push(i)}))}wasShown(){super.wasShown(),this.registerCSSFiles([ke])}}class Ee extends b.DataGrid.DataGridNode{selectable;valueObjectPresentation;constructor(e){super(e,!1),this.selectable=!0,this.valueObjectPresentation=null}createCell(e){const t=super.createCell(e),i=this.data[e];switch(e){case"value":{t.removeChildren();const e=y.ObjectPropertiesSection.ObjectPropertiesSection.defaultObjectPropertiesSection(i,void 0,!0,!0);t.appendChild(e.element),this.valueObjectPresentation=e;break}case"key":case"primary-key":{t.removeChildren();const e=y.ObjectPropertiesSection.ObjectPropertiesSection.defaultObjectPresentation(i,void 0,!0,!0);t.appendChild(e);break}}return t}}var xe=Object.freeze({__proto__:null,IDBDatabaseView:Te,IDBDataView:Ie,IDBDataGridNode:Ee});class Me extends o.SDKModel.SDKModel{storageAgent;enabled;constructor(e){super(e),e.registerStorageDispatcher(this),this.storageAgent=e.storageAgent(),this.enabled=!1}enable(){this.enabled||this.storageAgent.invoke_setInterestGroupTracking({enable:!0})}disable(){this.enabled&&this.storageAgent.invoke_setInterestGroupTracking({enable:!1})}interestGroupAccessed(e){this.dispatchEventToListeners("InterestGroupAccess",e)}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}o.SDKModel.SDKModel.register(Me,{capabilities:8192,autostart:!1});var Re=Object.freeze({__proto__:null,InterestGroupStorageModel:Me});const Le=new CSSStyleSheet;Le.replaceSync("devtools-interest-group-access-grid{overflow:auto}.placeholder{align-items:center;justify-content:center;font-size:13px;color:var(--sys-color-token-subtle);overflow:auto;text-align:center;& div{width:100%}}\n/*# sourceURL=interestGroupStorageView.css */\n");const Be={clickToDisplayBody:"Click on any interest group event to display the group's current state",noDataAvailable:"No details available for the selected interest group. The browser may have left the group."},Ae=i.i18n.registerUIStrings("panels/application/InterestGroupStorageView.ts",Be),Pe=i.i18n.getLocalizedString.bind(void 0,Ae);class De extends h.SplitWidget.SplitWidget{interestGroupGrid=new w.InterestGroupAccessGrid.InterestGroupAccessGrid;events=[];detailsGetter;noDataView;noDisplayView;constructor(e){super(!1,!0),this.element.setAttribute("jslog",`${m.pane("interest-groups")}`),this.detailsGetter=e;const t=new h.Widget.VBox;this.noDisplayView=new h.Widget.VBox,this.noDataView=new h.Widget.VBox,t.setMinimumSize(0,80),this.setMainWidget(t),this.noDisplayView.setMinimumSize(0,40),this.setSidebarWidget(this.noDisplayView),this.noDataView.setMinimumSize(0,40),t.contentElement.appendChild(this.interestGroupGrid),this.interestGroupGrid.addEventListener("cellfocused",this.onFocus.bind(this)),this.noDisplayView.contentElement.classList.add("placeholder"),this.noDisplayView.contentElement.setAttribute("jslog",`${m.pane("details").track({resize:!0})}`);this.noDisplayView.contentElement.createChild("div").textContent=Pe(Be.clickToDisplayBody),this.noDataView.contentElement.classList.add("placeholder"),this.noDataView.contentElement.setAttribute("jslog",`${m.pane("details").track({resize:!0})}`);this.noDataView.contentElement.createChild("div").textContent=Pe(Be.noDataAvailable)}wasShown(){super.wasShown();const e=this.sidebarWidget();e&&e.registerCSSFiles([Le])}addEvent(e){this.events.find((t=>{return s=e,(i=t).accessTime===s.accessTime&&i.type===s.type&&i.ownerOrigin===s.ownerOrigin&&i.name===s.name;var i,s}))||(this.events.push(e),this.interestGroupGrid.data=this.events)}clearEvents(){this.events=[],this.interestGroupGrid.data=this.events,this.setSidebarWidget(this.noDisplayView),this.sidebarUpdatedForTesting()}async onFocus(e){const t=e.data.row;if(!t)return;const i=t.cells.find((e=>"event-group-owner"===e.columnId))?.value,s=t.cells.find((e=>"event-group-name"===e.columnId))?.value,r=t.cells.find((e=>"event-type"===e.columnId))?.value;if(!i||!s)return;let a=null;if("additionalBid"!==r&&"additionalBidWin"!==r&&"topLevelAdditionalBid"!==r&&(a=await this.detailsGetter.getInterestGroupDetails(i,s)),a){const e=await c.JSONView.JSONView.createView(JSON.stringify(a));e?.setMinimumSize(0,40),e&&(e.contentElement.setAttribute("jslog",`${m.pane("details").track({resize:!0})}`),this.setSidebarWidget(e))}else this.setSidebarWidget(this.noDataView);this.sidebarUpdatedForTesting()}getEventsForTesting(){return this.events}getInterestGroupGridForTesting(){return this.interestGroupGrid}sidebarUpdatedForTesting(){}}var Fe=Object.freeze({__proto__:null,InterestGroupStorageView:De});const Ve={interestGroups:"Interest groups"},Oe=i.i18n.registerUIStrings("panels/application/InterestGroupTreeElement.ts",Ve),We=i.i18n.getLocalizedString.bind(void 0,Oe);class Ue extends P{view;constructor(e){super(e,We(Ve.interestGroups),!1);const t=d.Icon.create("database");this.setLeadingIcons([t]),this.view=new De(this)}get itemURL(){return"interest-groups://"}async getInterestGroupDetails(e,t){const i=o.TargetManager.TargetManager.instance().primaryPageTarget();if(!i)return null;return(await i.storageAgent().invoke_getInterestGroupDetails({ownerOrigin:e,name:t})).details}onselect(e){return super.onselect(e),this.showView(this.view),t.userMetrics.panelShown("interest-groups"),!1}addEvent(e){this.view.addEvent(e)}clearEvents(){this.view.clearEvents()}}var Ne=Object.freeze({__proto__:null,i18nString:We,InterestGroupTreeElement:Ue});const je=new CSSStyleSheet;je.replaceSync('.report-content-box{overflow:initial}.report-field-name{flex:0 0 200px}.report-field-value{user-select:text;display:flex}.report-field .inline-name{color:var(--sys-color-state-disabled);padding-left:2ex;user-select:none;white-space:pre-line}.report-field .inline-name::after{content:":\\A0"}.report-field .inline-comment{color:var(--sys-color-token-subtle);padding-left:1ex;white-space:pre-line}.report-field .inline-comment::before{content:"("}.report-field .inline-comment::after{content:")"}.report-field .inline-span{color:var(--sys-color-token-subtle);padding-left:1ex;white-space:pre-line}.report-field-value-link{display:inline-block}.icon-link.devtools-link{background-color:var(--sys-color-primary);vertical-align:sub}.frame-details-container{overflow:auto}.frame-details-report-container{min-width:550px}.text-ellipsis{overflow:hidden;text-overflow:ellipsis}\n/*# sourceURL=openedWindowDetailsView.css */\n');const He={yes:"Yes",no:"No",clickToRevealInElementsPanel:"Click to reveal in Elements panel",document:"Document",url:"URL",security:"Security",openerFrame:"Opener Frame",accessToOpener:"Access to opener",showsWhetherTheOpenedWindowIs:"Shows whether the opened window is able to access its opener and vice versa",windowWithoutTitle:"Window without title",closed:"closed",worker:"worker",type:"Type",securityIsolation:"Security & Isolation",crossoriginEmbedderPolicy:"Cross-Origin Embedder Policy",webWorker:"Web Worker",unknown:"Unknown",reportingTo:"reporting to"},_e=i.i18n.registerUIStrings("panels/application/OpenedWindowDetailsView.ts",He),Ge=i.i18n.getLocalizedString.bind(void 0,_e);async function Ke(t){let i=null;if(t instanceof o.ResourceTreeModel.ResourceTreeFrame?i=t:t&&(i=o.FrameManager.FrameManager.instance().getFrame(t)),!i)return null;const s=await i.getOwnerDOMNodeOrDocument();if(!s)return null;const r=function(e,t,i){const s=d.Icon.create(e,"icon-link devtools-link"),r=document.createElement("button");return h.Tooltip.Tooltip.install(r,t),r.classList.add("devtools-link","link-style","text-button"),r.appendChild(s),r.addEventListener("click",(e=>{e.consume(!0),i()})),r}("code-circle",Ge(He.clickToRevealInElementsPanel),(()=>e.Revealer.reveal(s))),a=document.createElement("span");return a.textContent=`<${s.nodeName().toLocaleLowerCase()}>`,r.insertBefore(a,r.firstChild),r.addEventListener("mouseenter",(()=>{i&&i.highlight()})),r.addEventListener("mouseleave",(()=>{o.OverlayModel.OverlayModel.hideDOMNodeHighlight()})),r}class ze extends h.ThrottledWidget.ThrottledWidget{targetInfo;isWindowClosed;reportView;documentSection;URLFieldValue;securitySection;openerElementField;hasDOMAccessValue;constructor(e,t){super(),this.targetInfo=e,this.isWindowClosed=t,this.contentElement.classList.add("frame-details-container"),this.reportView=new h.ReportView.ReportView(this.buildTitle()),this.reportView.show(this.contentElement),this.reportView.element.classList.add("frame-details-report-container"),this.documentSection=this.reportView.appendSection(Ge(He.document)),this.URLFieldValue=this.documentSection.appendField(Ge(He.url)).createChild("div","text-ellipsis"),this.securitySection=this.reportView.appendSection(Ge(He.security)),this.openerElementField=this.securitySection.appendField(Ge(He.openerFrame)),this.securitySection.setFieldVisible(Ge(He.openerFrame),!1),this.hasDOMAccessValue=this.securitySection.appendField(Ge(He.accessToOpener)),h.Tooltip.Tooltip.install(this.hasDOMAccessValue,Ge(He.showsWhetherTheOpenedWindowIs)),this.update()}async doUpdate(){var e;this.reportView.setTitle(this.buildTitle()),this.URLFieldValue.textContent=this.targetInfo.url,this.URLFieldValue.title=this.targetInfo.url,this.hasDOMAccessValue.textContent=(e=this.targetInfo.canAccessOpener,Ge(e?He.yes:He.no)),this.maybeDisplayOpenerFrame()}async maybeDisplayOpenerFrame(){this.openerElementField.removeChildren();const e=await Ke(this.targetInfo.openerFrameId);if(e)return this.openerElementField.append(e),void this.securitySection.setFieldVisible(Ge(He.openerFrame),!0);this.securitySection.setFieldVisible(Ge(He.openerFrame),!1)}buildTitle(){let e=this.targetInfo.title||Ge(He.windowWithoutTitle);return this.isWindowClosed&&(e+=` (${Ge(He.closed)})`),e}setIsWindowClosed(e){this.isWindowClosed=e}setTargetInfo(e){this.targetInfo=e}wasShown(){super.wasShown(),this.reportView.registerCSSFiles([je]),this.registerCSSFiles([je])}}class qe extends h.ThrottledWidget.ThrottledWidget{targetInfo;reportView;documentSection;isolationSection;coepPolicy;constructor(e){super(),this.targetInfo=e,this.contentElement.classList.add("frame-details-container"),this.reportView=new h.ReportView.ReportView(this.targetInfo.title||this.targetInfo.url||Ge(He.worker)),this.reportView.show(this.contentElement),this.reportView.element.classList.add("frame-details-report-container"),this.documentSection=this.reportView.appendSection(Ge(He.document));const t=this.documentSection.appendField(Ge(He.url)).createChild("div","text-ellipsis");t.textContent=this.targetInfo.url,t.title=this.targetInfo.url;this.documentSection.appendField(Ge(He.type)).textContent=this.workerTypeToString(this.targetInfo.type),this.isolationSection=this.reportView.appendSection(Ge(He.securityIsolation)),this.coepPolicy=this.isolationSection.appendField(Ge(He.crossoriginEmbedderPolicy)),this.update()}workerTypeToString(e){return"worker"===e?Ge(He.webWorker):"service_worker"===e?i.i18n.lockedString("Service Worker"):Ge(He.unknown)}async updateCoopCoepStatus(){const e=o.TargetManager.TargetManager.instance().targetById(this.targetInfo.targetId);if(!e)return;const t=e.model(o.NetworkManager.NetworkManager),i=t&&await t.getSecurityIsolationStatus(null);if(!i)return;this.fillCrossOriginPolicy(this.coepPolicy,(e=>"None"!==e),i.coep)}fillCrossOriginPolicy(e,t,i){if(!i)return void(e.textContent="");const s=t(i.value);if(e.textContent=s?i.value:i.reportOnlyValue,!s&&t(i.reportOnlyValue)){const t=document.createElement("span");t.classList.add("inline-comment"),t.textContent="report-only",e.appendChild(t)}const r=s?i.reportingEndpoint:i.reportOnlyReportingEndpoint;if(r){e.createChild("span","inline-name").textContent=Ge(He.reportingTo);e.createChild("span").textContent=r}}async doUpdate(){await this.updateCoopCoepStatus()}wasShown(){super.wasShown(),this.reportView.registerCSSFiles([je]),this.registerCSSFiles([je])}}var $e=Object.freeze({__proto__:null,OpenedWindowDetailsView:ze,WorkerDetailsView:qe});const Je=new CSSStyleSheet;Je.replaceSync(".preloading-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}\n/*# sourceURL=preloading/preloadingView.css */\n");const Qe=new CSSStyleSheet;Qe.replaceSync(":host{padding:2px 1px 2px 2px}.title{padding-left:8px}.subtitle{padding-left:8px}\n/*# sourceURL=preloading/preloadingViewDropDown.css */\n");const Xe={filterFilterByRuleSet:"Filter by rule set",filterAllPreloads:"All speculative loads",validityValid:"Valid",validityInvalid:"Invalid",validitySomeRulesInvalid:"Some rules invalid",statusNotTriggered:"Not triggered",statusPending:"Pending",statusRunning:"Running",statusReady:"Ready",statusSuccess:"Success",statusFailure:"Failure"},Ye=i.i18n.registerUIStrings("panels/application/preloading/PreloadingView.ts",Xe),Ze=i.i18n.getLocalizedString.bind(void 0,Ye),et=Symbol("AllRuleSetRootId");class tt{static status(e){switch(e){case"NotTriggered":return Ze(Xe.statusNotTriggered);case"Pending":return Ze(Xe.statusPending);case"Running":return Ze(Xe.statusRunning);case"Ready":return Ze(Xe.statusReady);case"Success":return Ze(Xe.statusSuccess);case"Failure":return Ze(Xe.statusFailure);case"NotSupported":return i.i18n.lockedString("Internal error")}}static preloadsStatusSummary(e){return["NotTriggered","Pending","Running","Ready","Success","Failure"].filter((t=>(e?.get(t)||0)>0)).map((t=>(e?.get(t)||0)+" "+this.status(t))).join(", ").toLocaleLowerCase()}static validity({errorType:e}){switch(e){case void 0:return Ze(Xe.validityValid);case"SourceIsNotJsonObject":return Ze(Xe.validityInvalid);case"InvalidRulesSkipped":return Ze(Xe.validitySomeRulesInvalid)}}static location(e){if(void 0!==e.backendNodeId)return i.i18n.lockedString("<script>");if(void 0!==e.url)return e.url;throw Error("unreachable")}static processLocalId(e){const t=e.indexOf(".");return-1===t?e:e.slice(t+1)}static ruleSetLocationShort(e,t){const i=void 0===e.url?t:e.url;return S.ResourceUtils.displayNameForURL(i)}}function it(){return o.TargetManager.TargetManager.instance().scopeTarget()?.inspectedURL()||""}class st extends h.Widget.VBox{model;focusedRuleSetId=null;focusedPreloadingAttemptId=null;warningsContainer;warningsView=new nt;hsplit;ruleSetGrid=new T.RuleSetGrid.RuleSetGrid;ruleSetDetails=new T.RuleSetDetailsView.RuleSetDetailsView;constructor(e){super(!0,!1),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer),this.ruleSetGrid.addEventListener("cellfocused",this.onRuleSetsGridCellFocused.bind(this)),f.render(f.html`
        <${C.SplitView.SplitView.litTagName} .horizontal=${!0} style="--min-sidebar-size: 0px">
          <div slot="main" class="overflow-auto" style="height: 100%">
            ${this.ruleSetGrid}
          </div>
          <div slot="sidebar" class="overflow-auto" style="height: 100%"
          jslog=${m.section("rule-set-details")}>
            ${this.ruleSetDetails}
          </div>
        </${C.SplitView.SplitView.litTagName}>`,this.contentElement,{host:this}),this.hsplit=this.contentElement.querySelector("devtools-split-view")}wasShown(){super.wasShown(),this.registerCSSFiles([$,Je]),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}revealRuleSet(e){this.focusedRuleSetId=e.ruleSetId,this.render()}updateRuleSetDetails(){const e=this.focusedRuleSetId,t=null===e?null:this.model.getRuleSetById(e);this.ruleSetDetails.data=t,null===t?this.hsplit.style.setProperty("--current-main-area-size","100%"):this.hsplit.style.setProperty("--current-main-area-size","60%")}render(){const e=this.model.getPreloadCountsByRuleSetId(),t=this.model.getAllRuleSets().map((({id:t,value:i})=>{const s=e.get(t)||new Map;return{ruleSet:i,preloadsStatusSummary:tt.preloadsStatusSummary(s)}}));this.ruleSetGrid.update({rows:t,pageURL:it()}),this.updateRuleSetDetails()}onRuleSetsGridCellFocused(e){const t=e;this.focusedRuleSetId=t.data.row.cells.find((e=>"id"===e.columnId))?.value,this.render()}getInfobarContainerForTest(){return this.warningsView.contentElement}getRuleSetGridForTest(){return this.ruleSetGrid}getRuleSetDetailsForTest(){return this.ruleSetDetails}}class rt extends h.Widget.VBox{model;focusedPreloadingAttemptId=null;warningsContainer;warningsView=new nt;preloadingGrid=new T.PreloadingGrid.PreloadingGrid;preloadingDetails=new T.PreloadingDetailsReportView.PreloadingDetailsReportView;ruleSetSelector;constructor(e){super(!0,!1),this.element.setAttribute("jslog",`${m.pane("preloading-speculations")}`),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer);const t=new h.Widget.VBox,i=new h.Toolbar.Toolbar("preloading-toolbar",t.contentElement);i.element.setAttribute("jslog",`${m.toolbar()}`),this.ruleSetSelector=new ot((()=>this.render())),i.appendToolbarItem(this.ruleSetSelector.item()),this.preloadingGrid.addEventListener("cellfocused",this.onPreloadingGridCellFocused.bind(this)),f.render(f.html`
        <${C.SplitView.SplitView.litTagName} .horizontal=${!0} style="--min-sidebar-size: 0px">
          <div slot="main" class="overflow-auto" style="height: 100%">
            ${this.preloadingGrid}
          </div>
          <div slot="sidebar" class="overflow-auto" style="height: 100%">
            ${this.preloadingDetails}
          </div>
        </${C.SplitView.SplitView.litTagName}>`,t.contentElement,{host:this}),t.show(this.contentElement)}wasShown(){super.wasShown(),this.registerCSSFiles([$,Je]),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}setFilter(e){let t=e.ruleSetId;null!==t&&void 0===this.model.getRuleSetById(t)&&(t=null),this.ruleSetSelector.select(t)}updatePreloadingDetails(){const e=this.focusedPreloadingAttemptId,t=null===e?null:this.model.getPreloadingAttemptById(e);if(null===t)this.preloadingDetails.data=null;else{const e=t.ruleSetIds.map((e=>this.model.getRuleSetById(e))).filter((e=>null!==e));this.preloadingDetails.data={preloadingAttempt:t,ruleSets:e,pageURL:it()}}}render(){const e=this.ruleSetSelector.getSelected(),t=this.model.getPreloadingAttempts(e).map((({id:e,value:t})=>{const i=t,s=i.ruleSetIds.flatMap((e=>{const t=this.model.getRuleSetById(e);return null===t?[]:[t]}));return{id:e,attempt:i,ruleSets:s}}));this.preloadingGrid.update({rows:t,pageURL:it()}),this.updatePreloadingDetails()}onPreloadingGridCellFocused(e){const t=e;this.focusedPreloadingAttemptId=t.data.row.cells.find((e=>"id"===e.columnId))?.value,this.render()}getRuleSetSelectorToolbarItemForTest(){return this.ruleSetSelector.item()}getPreloadingGridForTest(){return this.preloadingGrid}getPreloadingDetailsForTest(){return this.preloadingDetails}selectRuleSetOnFilterForTest(e){this.ruleSetSelector.select(e)}}class at extends h.Widget.VBox{model;warningsContainer;warningsView=new nt;usedPreloading=new T.UsedPreloadingView.UsedPreloadingView;constructor(e){super(!0,!1),this.element.setAttribute("jslog",`${m.pane("speculative-loads")}`),this.model=e,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.render,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"WarningsUpdated",this.warningsView.onWarningsUpdated,this.warningsView,{scoped:!0}),this.warningsContainer=document.createElement("div"),this.warningsContainer.classList.add("flex-none"),this.contentElement.insertBefore(this.warningsContainer,this.contentElement.firstChild),this.warningsView.show(this.warningsContainer);const t=new h.Widget.VBox;t.contentElement.appendChild(this.usedPreloading),t.show(this.contentElement)}wasShown(){super.wasShown(),this.registerCSSFiles([$,Je]),this.warningsView.wasShown(),this.render()}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.render()}render(){this.usedPreloading.data={pageURL:o.TargetManager.TargetManager.instance().scopeTarget()?.inspectedURL()||"",previousAttempts:this.model.getPreloadingAttemptsOfPreviousPage().map((({value:e})=>e)),currentAttempts:this.model.getPreloadingAttempts(null).map((({value:e})=>e))}}getUsedPreloadingForTest(){return this.usedPreloading}}class ot{model;onSelectionChanged=()=>{};toolbarItem;listModel;dropDown;constructor(e){const t=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(t),this.model=t,o.TargetManager.TargetManager.instance().addScopeChangeListener(this.onScopeChange.bind(this)),o.TargetManager.TargetManager.instance().addModelListener(o.PreloadingModel.PreloadingModel,"ModelUpdated",this.onModelUpdated,this,{scoped:!0}),this.listModel=new h.ListModel.ListModel,this.dropDown=new h.SoftDropDown.SoftDropDown(this.listModel,this),this.dropDown.setRowHeight(36),this.dropDown.setPlaceholderText(Ze(Xe.filterAllPreloads)),this.toolbarItem=new h.Toolbar.ToolbarItem(this.dropDown.element),this.toolbarItem.setTitle(Ze(Xe.filterFilterByRuleSet)),this.toolbarItem.element.classList.add("toolbar-has-dropdown"),this.toolbarItem.element.setAttribute("jslog",`${m.action("filter-by-rule-set").track({click:!0})}`),this.onModelUpdated(),this.onSelectionChanged=e}onScopeChange(){const e=o.TargetManager.TargetManager.instance().scopeTarget()?.model(o.PreloadingModel.PreloadingModel);r(e),this.model=e,this.onModelUpdated()}onModelUpdated(){const e=this.model.getAllRuleSets().map((({id:e})=>e)),t=[et,...e],i=this.dropDown.getSelectedItem();this.listModel.replaceAll(t),null===i?this.dropDown.selectItem(et):this.dropDown.selectItem(i),this.updateWidth(t)}updateWidth(e){const t=e.map((e=>this.titleFor(e).length)),i=Math.max(...t),s=Math.min(6*i+16,315);this.dropDown.setWidth(s)}translateItemIdToRuleSetId(e){return e===et?null:e}getSelected(){const e=this.dropDown.getSelectedItem();return null===e?null:this.translateItemIdToRuleSetId(e)}select(e){this.dropDown.selectItem(e)}item(){return this.toolbarItem}titleFor(e){const t=this.translateItemIdToRuleSetId(e);if(null===t)return Ze(Xe.filterAllPreloads);const s=this.model.getRuleSetById(t);return null===s?i.i18n.lockedString("Internal error"):tt.ruleSetLocationShort(s,it())}subtitleFor(e){const t=this.translateItemIdToRuleSetId(e),i=this.model.getPreloadCountsByRuleSetId().get(t)||new Map;return tt.preloadsStatusSummary(i)}createElementForItem(e){const t=document.createElement("div"),i=h.Utils.createShadowRootWithCoreStyles(t,{cssFile:[Qe],delegatesFocus:void 0}),r=i.createChild("div","title");h.UIUtils.createTextChild(r,s.StringUtilities.trimEndWithMaxLength(this.titleFor(e),100));const a=i.createChild("div","subtitle");return h.UIUtils.createTextChild(a,this.subtitleFor(e)),t}isItemSelectable(e){return!0}itemSelected(e){this.onSelectionChanged()}highlightedItemChanged(e,t,i,s){}}class nt extends h.Widget.VBox{infobar=new T.PreloadingDisabledInfobar.PreloadingDisabledInfobar;constructor(){super(!1,!1)}wasShown(){super.wasShown(),this.registerCSSFiles([$]),this.contentElement.append(this.infobar)}onWarningsUpdated(e){this.infobar.data=e.data}}var dt=Object.freeze({__proto__:null,PreloadingRuleSetView:st,PreloadingAttemptView:rt,PreloadingSummaryView:at,PreloadingWarningsView:nt});const lt={speculativeLoads:"Speculative loads",rules:"Rules",speculations:"Speculations"},ct=i.i18n.registerUIStrings("panels/application/PreloadingTreeElement.ts",lt),ht=i.i18n.getLocalizedString.bind(void 0,ct);class gt extends P{#e;#t;view;#i;#s;constructor(e,t,i,s){super(e,s,!1),this.#t=t,this.#i=i;const r=d.Icon.create("arrow-up-down");this.setLeadingIcons([r]),this.#s=!1}get itemURL(){return this.#i}initialize(e){this.#e=e,this.#s&&!this.view&&this.onselect(!1)}onselect(e){return super.onselect(e),this.#s=!0,!!this.#e&&(this.view||(this.view=new this.#t(this.#e)),this.showView(this.view),!1)}}class ut extends D{#e;#r;#s;#a=null;#o=null;constructor(e){super(e,ht(lt.speculativeLoads),"preloading");const t=d.Icon.create("arrow-up-down");this.setLeadingIcons([t]),this.#s=!1}constructChildren(e){this.#a=new pt(e),this.#o=new mt(e),this.appendChild(this.#a),this.appendChild(this.#o)}initialize(e){if(null===this.#a||null===this.#o)throw new Error("unreachable");this.#e=e,this.#a.initialize(e),this.#o.initialize(e),this.#s&&!this.#r&&this.onselect(!1)}onselect(e){return super.onselect(e),this.#s=!0,!!this.#e&&(this.#r||(this.#r=new at(this.#e)),this.showView(this.#r),!1)}expandAndRevealRuleSet(e){if(null===this.#a)throw new Error("unreachable");this.expand(),this.#a.revealRuleSet(e)}expandAndRevealAttempts(e){if(null===this.#o)throw new Error("unreachable");this.expand(),this.#o.revealAttempts(e)}}class pt extends gt{constructor(e){super(e,st,"preloading://rule-set",ht(lt.rules))}revealRuleSet(e){this.select(),void 0!==this.view&&this.view?.revealRuleSet(e)}}class mt extends gt{constructor(e){super(e,rt,"preloading://attempt",ht(lt.speculations))}revealAttempts(e){this.select(),this.view?.setFilter(e)}}var wt=Object.freeze({__proto__:null,PreloadingSummaryTreeElement:ut,PreloadingRuleSetTreeElement:pt});const vt=new CSSStyleSheet;vt.replaceSync(".placeholder{align-items:center;justify-content:center;font-size:13px;color:var(--sys-color-token-subtle);overflow:auto;text-align:center;& div{width:100%}}\n/*# sourceURL=reportingApiReportsView.css */\n");const St={clickToDisplayBody:"Click on any report to display its body"},bt=i.i18n.registerUIStrings("panels/application/ReportingApiReportsView.ts",St),kt=i.i18n.getLocalizedString.bind(void 0,bt);class yt extends h.SplitWidget.SplitWidget{reportsGrid=new w.ReportsGrid.ReportsGrid;reports=[];constructor(e){super(!1,!0);const t=new h.Widget.VBox,i=new h.Widget.VBox;t.setMinimumSize(0,80),this.setMainWidget(t),i.setMinimumSize(0,40),i.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`),this.setSidebarWidget(i),t.contentElement.appendChild(this.reportsGrid),this.reportsGrid.addEventListener("cellfocused",this.onFocus.bind(this)),i.contentElement.classList.add("placeholder");i.contentElement.createChild("div").textContent=kt(St.clickToDisplayBody),e.addEventListener(o.NetworkManager.Events.ReportingApiReportAdded,(e=>this.onReportAdded(e.data)),this),e.addEventListener(o.NetworkManager.Events.ReportingApiReportUpdated,(e=>this.onReportUpdated(e.data)),this)}wasShown(){super.wasShown();const e=this.sidebarWidget();e&&e.registerCSSFiles([vt])}onReportAdded(e){this.reports.push(e),this.reportsGrid.data={reports:this.reports}}onReportUpdated(e){const t=this.reports.findIndex((t=>t.id===e.id));this.reports[t]=e,this.reportsGrid.data={reports:this.reports}}async onFocus(e){const t=e.data.row.cells.find((e=>"id"===e.columnId)),i=t&&this.reports.find((e=>e.id===t.value));if(i){const e=await c.JSONView.JSONView.createView(JSON.stringify(i.body));e?.setMinimumSize(0,40),e&&this.setSidebarWidget(e)}}getReports(){return this.reports}getReportsGrid(){return this.reportsGrid}}var ft=Object.freeze({__proto__:null,i18nString:kt,ReportingApiReportsView:yt});class Ct extends h.SplitWidget.SplitWidget{endpointsGrid;endpoints;constructor(e){super(!1,!0),this.element.setAttribute("jslog",`${m.pane("reporting-api")}`),this.endpointsGrid=e,this.endpoints=new Map;const t=o.TargetManager.TargetManager.instance().primaryPageTarget(),i=t&&t.model(o.NetworkManager.NetworkManager);if(i){i.addEventListener(o.NetworkManager.Events.ReportingApiEndpointsChangedForOrigin,(e=>this.onEndpointsChangedForOrigin(e.data)),this);const e=new yt(i),t=new h.Widget.VBox;t.setMinimumSize(0,40),t.contentElement.appendChild(this.endpointsGrid),this.setMainWidget(e),this.setSidebarWidget(t),i.enableReportingApi()}}onEndpointsChangedForOrigin(e){this.endpoints.set(e.origin,e.endpoints),this.endpointsGrid.data={endpoints:this.endpoints}}}var Tt=Object.freeze({__proto__:null,ReportingApiView:Ct});const It={reportingApi:"Reporting API"},Et=i.i18n.registerUIStrings("panels/application/ReportingApiTreeElement.ts",It),xt=i.i18n.getLocalizedString.bind(void 0,Et);class Mt extends P{view;constructor(e){super(e,xt(It.reportingApi),!1);const t=d.Icon.create("document");this.setLeadingIcons([t])}get itemURL(){return"reportingApi://"}onselect(e){return super.onselect(e),this.view||(this.view=new Ct(new w.EndpointsGrid.EndpointsGrid)),this.showView(this.view),t.userMetrics.panelShown("reporting-api"),!1}}const Rt=new CSSStyleSheet;Rt.replaceSync('.tree-outline{padding-left:0;color:var(--sys-color-on-surface)}.tree-outline > ol{padding-bottom:10px}.tree-outline li{min-height:20px}.tree-outline li[role="heading"]{color:var(--sys-color-on-surface-subtle);font-weight:500}li.storage-group-list-item{padding:10px 8px 6px}li.storage-group-list-item:not(:first-child){border-top:1px solid var(--sys-color-divider)}li.storage-group-list-item::before{display:none}.icons-container devtools-icon.red-icon{color:var(--icon-error)}.icons-container devtools-icon.warn-icon{color:var(--icon-warning)}devtools-icon.navigator-file-tree-item{color:var(--icon-file-default)}devtools-icon.navigator-folder-tree-item{color:var(--icon-folder-primary)}devtools-icon.navigator-script-tree-item{color:var(--icon-file-script)}devtools-icon.navigator-stylesheet-tree-item{color:var(--icon-file-styles)}devtools-icon.navigator-image-tree-item,\ndevtools-icon.navigator-font-tree-item{color:var(--icon-file-image)}.window-closed .tree-element-title{text-decoration:line-through}\n/*# sourceURL=resourcesSidebar.css */\n');const Lt=new CSSStyleSheet;Lt.replaceSync(".service-worker-cache-data-view .data-view-toolbar{position:relative;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);padding-right:10px}.service-worker-cache-data-view .data-grid{flex:auto}.service-worker-cache-data-view .data-grid .data-container tr:nth-last-child(1) td{border:0}.service-worker-cache-data-view .data-grid .data-container tr:nth-last-child(2) td{border-bottom:1px solid var(--sys-color-divider)}.service-worker-cache-data-view .data-grid .data-container tr.selected{background-color:var(--sys-color-neutral-container);color:inherit}.service-worker-cache-data-view .data-grid:focus .data-container tr.selected{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.service-worker-cache-data-view .section,\n.service-worker-cache-data-view .section > .header,\n.service-worker-cache-data-view .section > .header .title{margin:0;min-height:inherit;line-height:inherit}.service-worker-cache-data-view .data-grid .data-container td .section .header .title{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.cache-preview-panel-resizer{background-color:var(--sys-color-surface1);height:4px;border-bottom:1px solid var(--sys-color-divider)}.cache-storage-summary-bar{flex:0 0 27px;line-height:27px;padding-left:5px;background-color:var(--sys-color-cdt-base-container);border-top:1px solid var(--sys-color-divider);white-space:nowrap;text-overflow:ellipsis;overflow:hidden}\n/*# sourceURL=serviceWorkerCacheViews.css */\n");const Bt={cache:"Cache",refresh:"Refresh",deleteSelected:"Delete Selected",filterByPath:"Filter by Path",selectACacheEntryAboveToPreview:"Select a cache entry above to preview",name:"Name",timeCached:"Time Cached",varyHeaderWarning:"⚠️ Set ignoreVary to true when matching this entry",serviceWorkerCache:"`Service Worker` Cache",matchingEntriesS:"Matching entries: {PH1}",totalEntriesS:"Total entries: {PH1}",headers:"Headers",preview:"Preview"},At=i.i18n.registerUIStrings("panels/application/ServiceWorkerCacheViews.ts",Bt),Pt=i.i18n.getLocalizedString.bind(void 0,At);class Dt extends h.View.SimpleView{model;entriesForTest;splitWidget;previewPanel;preview;cache;dataGrid;refreshThrottler;refreshButton;deleteSelectedButton;entryPathFilter;returnCount;summaryBarElement;loadingPromise;metadataView=new w.StorageMetadataView.StorageMetadataView;constructor(t,i){super(Pt(Bt.cache)),this.model=t,this.entriesForTest=null,this.element.classList.add("service-worker-cache-data-view"),this.element.classList.add("storage-view"),this.element.setAttribute("jslog",`${m.pane("cache-storage-data")}`);const s=new h.Toolbar.Toolbar("data-view-toolbar",this.element);s.element.setAttribute("jslog",`${m.toolbar()}`),this.element.appendChild(this.metadataView),this.splitWidget=new h.SplitWidget.SplitWidget(!1,!1),this.splitWidget.show(this.element),this.previewPanel=new h.Widget.VBox;const r=this.previewPanel.element.createChild("div","cache-preview-panel-resizer");this.splitWidget.setMainWidget(this.previewPanel),this.splitWidget.installResizer(r),this.preview=null,this.cache=i;const a=this.model.target().model(o.StorageBucketsModel.StorageBucketsModel)?.getBucketByName(i.storageBucket.storageKey,i.storageBucket.name);a?this.metadataView.setStorageBucket(a):i.storageKey&&this.metadataView.setStorageKey(i.storageKey),this.dataGrid=null,this.refreshThrottler=new e.Throttler.Throttler(300),this.refreshButton=new h.Toolbar.ToolbarButton(Pt(Bt.refresh),"refresh",void 0,"cache-storage.refresh"),this.refreshButton.addEventListener("Click",this.refreshButtonClicked,this),s.appendToolbarItem(this.refreshButton),this.deleteSelectedButton=new h.Toolbar.ToolbarButton(Pt(Bt.deleteSelected),"cross",void 0,"cache-storage.delete-selected"),this.deleteSelectedButton.addEventListener("Click",(e=>{this.deleteButtonClicked(null)})),s.appendToolbarItem(this.deleteSelectedButton);const n=new h.Toolbar.ToolbarInput(Pt(Bt.filterByPath),"",1);s.appendToolbarItem(n);const d=new e.Throttler.Throttler(300);this.entryPathFilter="",n.addEventListener("TextChanged",(()=>{d.schedule((()=>(this.entryPathFilter=n.value(),this.updateData(!0))))})),this.returnCount=null,this.summaryBarElement=null,this.loadingPromise=null,this.update(i)}resetDataGrid(){this.dataGrid&&this.dataGrid.asWidget().detach(),this.dataGrid=this.createDataGrid();const e=this.dataGrid.asWidget();this.splitWidget.setSidebarWidget(e),e.setMinimumSize(0,250)}wasShown(){this.model.addEventListener("CacheStorageContentUpdated",this.cacheContentUpdated,this),this.registerCSSFiles([Lt]),this.updateData(!0)}willHide(){this.model.removeEventListener("CacheStorageContentUpdated",this.cacheContentUpdated,this)}showPreview(e){e&&this.preview===e||(this.preview&&this.preview.detach(),e||(e=new h.EmptyWidget.EmptyWidget(Pt(Bt.selectACacheEntryAboveToPreview))),this.preview=e,this.preview.show(this.previewPanel.element))}createDataGrid(){const e=[{id:"number",title:"#",sortable:!1,width:"3px"},{id:"name",title:Pt(Bt.name),weight:4,sortable:!0},{id:"response-type",title:i.i18n.lockedString("Response-Type"),weight:1,align:"right",sortable:!0},{id:"content-type",title:i.i18n.lockedString("Content-Type"),weight:1,sortable:!0},{id:"content-length",title:i.i18n.lockedString("Content-Length"),weight:1,align:"right",sortable:!0},{id:"response-time",title:Pt(Bt.timeCached),width:"12em",weight:1,align:"right",sortable:!0},{id:"vary-header",title:i.i18n.lockedString("Vary Header"),weight:1,sortable:!0}],t=new b.DataGrid.DataGridImpl({displayName:Pt(Bt.serviceWorkerCache),columns:e,deleteCallback:this.deleteButtonClicked.bind(this),refreshCallback:this.updateData.bind(this,!0),editCallback:void 0});return t.addEventListener("SortingChanged",this.sortingChanged,this),t.addEventListener("SelectedNode",(e=>{this.previewCachedResponse(e.data.data)}),this),t.setStriped(!0),t}sortingChanged(){if(!this.dataGrid)return;const e=this.dataGrid,t=e.isSortOrderAscending(),i=e.sortColumnId();let s;"name"===i?s=(e,t)=>e.name.localeCompare(t.name):"content-type"===i?s=(e,t)=>e.data.mimeType.localeCompare(t.data.mimeType):"content-length"===i?s=(e,t)=>e.data.resourceSize-t.data.resourceSize:"response-time"===i?s=(e,t)=>e.data.endTime-t.data.endTime:"response-type"===i?s=(e,t)=>e.responseType.localeCompare(t.responseType):"vary-header"===i&&(s=(e,t)=>e.varyHeader.localeCompare(t.varyHeader));const r=e.rootNode().children.slice();e.rootNode().removeChildren(),r.sort(((e,i)=>{const r=s(e,i);return t?r:-r})),r.forEach((t=>e.rootNode().appendChild(t)))}async deleteButtonClicked(e){(e||(e=this.dataGrid&&this.dataGrid.selectedNode))&&(await this.model.deleteCacheEntry(this.cache,e.data.url()),e.remove())}update(e){this.cache=e,this.resetDataGrid(),this.updateData(!0)}updateSummaryBar(){this.summaryBarElement||(this.summaryBarElement=this.element.createChild("div","cache-storage-summary-bar")),this.summaryBarElement.removeChildren();const e=this.summaryBarElement.createChild("span");this.entryPathFilter?e.textContent=Pt(Bt.matchingEntriesS,{PH1:String(this.returnCount)}):e.textContent=Pt(Bt.totalEntriesS,{PH1:String(this.returnCount)})}updateDataCallback(e,t,i){if(!this.dataGrid)return;const s=this.dataGrid.selectedNode&&this.dataGrid.selectedNode.data.url();this.refreshButton.setEnabled(!0),this.entriesForTest=t,this.returnCount=i,this.updateSummaryBar();const r=new Map,a=this.dataGrid.rootNode();for(const e of a.children)r.set(e.data.url,e);a.removeChildren();let o=null;for(let e=0;e<t.length;++e){const i=t[e];let n=r.get(i.requestURL);n&&n.data.responseTime===i.responseTime?n.data.number=e:(n=new Vt(e,this.createRequest(i),i.responseType),n.selectable=!0),a.appendChild(n),i.requestURL===s&&(o=n)}o?o.revealAndSelect():this.showPreview(null),this.updatedForTest()}async updateData(e){if(!e&&this.loadingPromise)return this.loadingPromise;if(this.refreshButton.setEnabled(!1),this.loadingPromise)return this.loadingPromise;this.loadingPromise=new Promise((e=>{this.model.loadAllCacheData(this.cache,this.entryPathFilter,((t,i)=>{e({entries:t,returnCount:i})}))}));const{entries:t,returnCount:i}=await this.loadingPromise;this.updateDataCallback(0,t,i),this.loadingPromise=null}refreshButtonClicked(){this.updateData(!0)}cacheContentUpdated(e){const{cacheName:t,storageBucket:i}=e.data;this.cache.inBucket(i)&&this.cache.cacheName===t&&this.refreshThrottler.schedule((()=>Promise.resolve(this.updateData(!0))),!0)}async previewCachedResponse(e){let t=Ft.get(e);t||(t=new Ot(e),Ft.set(e,t)),this.dataGrid&&this.dataGrid.selectedNode&&e===this.dataGrid.selectedNode.data&&this.showPreview(t)}createRequest(t){const i=o.NetworkRequest.NetworkRequest.createWithoutBackendRequest("cache-storage-"+t.requestURL,t.requestURL,s.DevToolsPath.EmptyUrlString,null);i.requestMethod=t.requestMethod,i.setRequestHeaders(t.requestHeaders),i.statusCode=t.responseStatus,i.statusText=t.responseStatusText,i.protocol=new e.ParsedURL.ParsedURL(t.requestURL).scheme,i.responseHeaders=t.responseHeaders,i.setRequestHeadersText(""),i.endTime=t.responseTime;let r=t.responseHeaders.find((e=>"content-type"===e.name.toLowerCase())),a="text/plain";if(r){const e=s.MimeType.parseContentType(r.value);e.mimeType&&(a=e.mimeType)}i.mimeType=a,r=t.responseHeaders.find((e=>"content-length"===e.name.toLowerCase())),i.resourceSize=r&&Number(r.value)||0;let n=e.ResourceType.ResourceType.fromMimeType(a);return n||(n=e.ResourceType.ResourceType.fromURL(t.requestURL)||e.ResourceType.resourceTypes.Other),i.setResourceType(n),i.setContentDataProvider(this.requestContent.bind(this,i)),i}async requestContent(e){const t=await this.cache.requestCachedResponse(e.url(),e.requestHeaders());return t?new I.ContentData.ContentData(t.body,!0,e.mimeType,e.charset()??void 0):{error:"No cached response found"}}updatedForTest(){}}const Ft=new WeakMap;class Vt extends b.DataGrid.DataGridNode{number;name;request;responseType;varyHeader;constructor(t,i,r){super(i),this.number=t;const a=new e.ParsedURL.ParsedURL(i.url());a.isValid?this.name=s.StringUtilities.trimURL(i.url(),a.domain()):this.name=i.url(),this.request=i,this.responseType=r,this.varyHeader=i.responseHeaders.find((e=>"vary"===e.name.toLowerCase()))?.value||""}createCell(e){const t=this.createTD(e);let i,s=this.request.url();"number"===e?i=String(this.number):"name"===e?i=this.name:"response-type"===e?i="opaqueResponse"===this.responseType?"opaque":"opaqueRedirect"===this.responseType?"opaqueredirect":this.responseType:"content-type"===e?i=this.request.mimeType:"content-length"===e?i=(0|this.request.resourceSize).toLocaleString("en-US"):"response-time"===e?i=new Date(1e3*this.request.endTime).toLocaleString():"vary-header"===e&&(i=this.varyHeader,this.varyHeader&&(s=Pt(Bt.varyHeaderWarning)));const r=t.parentElement;let a;return r&&this.dataGrid&&(a=this.dataGrid.elementToDataGridNode.get(r)),b.DataGrid.DataGridImpl.setElementText(t,i||"",!0,a),h.Tooltip.Tooltip.install(t,s),t}}class Ot extends h.Widget.VBox{tabbedPane;resourceViewTabSetting;constructor(t){super(),this.tabbedPane=new h.TabbedPane.TabbedPane,this.tabbedPane.element.setAttribute("jslog",`${m.section("network-item-preview")}`),this.tabbedPane.addEventListener(h.TabbedPane.Events.TabSelected,this.tabSelected,this),this.resourceViewTabSetting=e.Settings.Settings.instance().createSetting("cache-storage-view-tab","preview"),this.tabbedPane.appendTab("headers",Pt(Bt.headers),l.LegacyWrapper.legacyWrapper(h.Widget.VBox,new E.RequestHeadersView.RequestHeadersView(t))),this.tabbedPane.appendTab("preview",Pt(Bt.preview),new x.RequestPreviewView.RequestPreviewView(t)),this.tabbedPane.show(this.element)}wasShown(){super.wasShown(),this.selectTab()}selectTab(e){e||(e=this.resourceViewTabSetting.get()),e&&!this.tabbedPane.selectTab(e)&&this.tabbedPane.selectTab("headers")}tabSelected(e){e.data.isUserGesture&&this.resourceViewTabSetting.set(e.data.tabId)}}var Wt=Object.freeze({__proto__:null,ServiceWorkerCacheView:Dt,DataGridNode:Vt,RequestView:Ot});const Ut={cacheStorage:"Cache storage",refreshCaches:"Refresh Caches",delete:"Delete"},Nt=i.i18n.registerUIStrings("panels/application/ServiceWorkerCacheTreeElement.ts",Ut),jt=i.i18n.getLocalizedString.bind(void 0,Nt);class Ht extends D{swCacheModels;swCacheTreeElements;storageBucket;constructor(e,t){super(e,jt(Ut.cacheStorage),"cache-storage");const i=d.Icon.create("database");this.setLink("https://developer.chrome.com/docs/devtools/storage/cache/?utm_source=devtools"),this.setLeadingIcons([i]),this.swCacheModels=new Set,this.swCacheTreeElements=new Set,this.storageBucket=t}initialize(){this.swCacheModels.clear(),this.swCacheTreeElements.clear(),o.TargetManager.TargetManager.instance().observeModels(o.ServiceWorkerCacheModel.ServiceWorkerCacheModel,{modelAdded:e=>this.serviceWorkerCacheModelAdded(e),modelRemoved:e=>this.serviceWorkerCacheModelRemoved(e)})}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(jt(Ut.refreshCaches),this.refreshCaches.bind(this),{jslogContext:"refresh-caches"}),t.show()}refreshCaches(){for(const e of this.swCacheModels)e.refreshCacheNames()}serviceWorkerCacheModelAdded(e){e.enable(),this.swCacheModels.add(e);for(const t of e.caches())this.addCache(e,t);e.addEventListener("CacheAdded",this.cacheAdded,this),e.addEventListener("CacheRemoved",this.cacheRemoved,this)}serviceWorkerCacheModelRemoved(e){for(const t of e.caches())this.removeCache(e,t);e.removeEventListener("CacheAdded",this.cacheAdded,this),e.removeEventListener("CacheRemoved",this.cacheRemoved,this),this.swCacheModels.delete(e)}cacheAdded(e){const{model:t,cache:i}=e.data;this.addCache(t,i)}cacheInTree(e){return!this.storageBucket||e.inBucket(this.storageBucket)}addCache(e,t){if(this.cacheInTree(t)){const i=new _t(this.resourcesPanel,e,t,void 0===this.storageBucket);this.swCacheTreeElements.add(i),this.appendChild(i)}}cacheRemoved(e){const{model:t,cache:i}=e.data;this.cacheInTree(i)&&this.removeCache(t,i)}removeCache(e,t){const i=this.cacheTreeElement(e,t);i&&(this.removeChild(i),this.swCacheTreeElements.delete(i),this.setExpandable(this.childCount()>0))}cacheTreeElement(e,t){for(const i of this.swCacheTreeElements)if(i.hasModelAndCache(e,t))return i;return null}}class _t extends P{model;cache;view;constructor(e,t,i,s){let r;r=s?i.cacheName+" - "+i.storageKey:i.cacheName,super(e,r,!1),this.model=t,this.cache=i,this.view=null;const a=d.Icon.create("table");this.setLeadingIcons([a])}get itemURL(){return"cache://"+this.cache.cacheId}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(jt(Ut.delete),this.clearCache.bind(this),{jslogContext:"delete"}),t.show()}clearCache(){this.model.deleteCache(this.cache)}update(e){this.cache=e,this.view&&this.view.update(e)}onselect(e){return super.onselect(e),this.view||(this.view=new Dt(this.model,this.cache)),this.showView(this.view),t.userMetrics.panelShown("service-worker-cache"),!1}hasModelAndCache(e,t){return this.cache.equals(t)&&this.model===e}}const Gt=new CSSStyleSheet;Gt.replaceSync('.service-worker-version{display:flex;flex-wrap:wrap}.service-worker-version-stack{position:relative}.service-worker-version-stack-bar{position:absolute;top:10px;bottom:20px;left:4px;content:"";border-left:1px solid var(--sys-color-divider);z-index:0}.service-worker-version:not(:last-child){margin-bottom:7px}.service-worker-version-string{flex-shrink:0}.service-worker-active-circle,\n.service-worker-redundant-circle,\n.service-worker-waiting-circle,\n.service-worker-installing-circle{position:relative;display:inline-block;width:10px;height:10px;z-index:10;margin-right:5px;border-radius:50%;border:1px solid var(--sys-color-token-subtle);align-self:center;flex-shrink:0}.service-worker-active-circle{background-color:var(--sys-color-green-bright)}.service-worker-waiting-circle{background-color:var(--sys-color-yellow-bright)}.service-worker-installing-circle{background-color:var(--sys-color-cdt-base-container)}.service-worker-redundant-circle{background-color:var(--sys-color-neutral-bright)}.service-worker-subtitle{padding-left:14px;line-height:14px;color:var(--sys-color-state-disabled)}.link{margin-left:7px}.service-worker-editor-with-button{align-items:baseline;display:flex}.service-worker-notification-editor{border:1px solid var(--sys-color-divider);display:flex;flex:auto;margin-right:4px;max-width:400px;min-width:80px}.report-field-value{white-space:normal}.report-field-value-filename,\n.service-worker-client-string{max-width:400px;overflow:hidden;text-overflow:ellipsis}.report-field-value-filename{padding-left:2px;margin-left:-2px}.report-field-value-subtitle{overflow:hidden;text-overflow:ellipsis}.service-worker-client{display:flex}.service-worker-client-focus-link{flex:none;margin-right:2px;align-self:center}.service-worker-notification-editor.source-code{padding:4px}.service-worker-list{background-color:var(--sys-color-cdt-base-container);overflow:auto}.service-workers-this-origin{flex-shrink:0;flex-grow:0}.service-workers-this-origin,\n.service-workers-other-origin{min-width:max-content}.service-worker-has-current .service-workers-other-origin{margin-top:16px;border-top:1px solid var(--sys-color-divider)}.devtools-link{line-height:14px;align-self:center;padding:1px}button.link{padding:1px}button.link:focus-visible{background-color:inherit}\n/*# sourceURL=serviceWorkersView.css */\n');const Kt=new CSSStyleSheet;Kt.replaceSync('.resource-service-worker-update-view{display:block;margin:6px;color:var(--sys-color-on-surface-subtle);overflow:auto}.service-worker-update-timing-table{border:1px solid var(--sys-color-divider);border-spacing:0;padding-left:10px;padding-right:10px;line-height:initial;table-layout:auto;overflow:hidden}.service-worker-update-timing-row{position:relative;height:20px;overflow:hidden;min-width:80px}.service-worker-update-timing-bar{position:absolute;min-width:1px;top:0;bottom:0}.service-worker-update-timing-bar-clickable::before{user-select:none;mask-image:var(--image-file-triangle-right);float:left;width:14px;height:14px;margin-right:2px;content:"";position:relative;background-color:var(--icon-default);transition:transform 200ms}.service-worker-update-timing-bar-clickable{position:relative;left:-12px}.service-worker-update-timing-bar-clickable:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.service-worker-update-timing-bar-clickable[aria-checked="true"]::before{transform:rotate(90deg)}.service-worker-update-timing-bar-details-collapsed{display:none}.service-worker-update-timing-bar-details-expanded{display:table-row}.service-worker-update-timing-bar-details:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.service-worker-update-timing-bar.activate{top:5px;height:10px;background-color:var(--sys-color-yellow-bright)}.service-worker-update-timing-bar.wait{top:5px;height:10px;background-color:var(--sys-color-purple-bright)}.service-worker-update-timing-bar.install{top:5px;height:10px;background-color:var(--sys-color-cyan-bright)}.service-worker-update-timing-table > tr > td{padding:4px 0;padding-right:10px}table.service-worker-update-timing-table > tr.service-worker-update-timing-table-header > td{border-top:5px solid transparent;color:var(--sys-color-token-subtle)}table.service-worker-update-timing-table > tr.service-worker-update-timing-bar-details > td:first-child{padding-left:12px}table.service-worker-update-timing-table > tr.service-worker-update-timeline > td:first-child{padding-left:12px}\n/*# sourceURL=serviceWorkerUpdateCycleView.css */\n');const zt={version:"Version",updateActivity:"Update Activity",timeline:"Timeline",startTimeS:"Start time: {PH1}",endTimeS:"End time: {PH1}"},qt=i.i18n.registerUIStrings("panels/application/ServiceWorkerUpdateCycleView.ts",zt),$t=i.i18n.getLocalizedString.bind(void 0,qt);class Jt{registration;rows;selectedRowIndex;tableElement;constructor(e){this.registration=e,this.rows=[],this.selectedRowIndex=-1,this.tableElement=document.createElement("table"),this.createTimingTable()}calculateServiceWorkerUpdateRanges(){function e(e,t){t.start<Number.MAX_VALUE&&t.start<=t.end&&e.push(t)}function t(t){let i=t.currentState,s=0,r=0,a=0,o=0;const n=i.status;if("new"===n)return[];for(;i;)"activated"===i.status?s=i.lastUpdatedTimestamp:"activating"===i.status?(0===s&&(s=i.lastUpdatedTimestamp),r=i.lastUpdatedTimestamp):"installed"===i.status?a=i.lastUpdatedTimestamp:"installing"===i.status&&(0===a&&(a=i.lastUpdatedTimestamp),o=i.lastUpdatedTimestamp),i=i.previousState;const d=[];return function(t,i,s,r,a,o,n){e(t,{id:i,phase:"Install",start:s,end:r}),"activating"!==n&&"activated"!==n&&"redundant"!==n||(e(t,{id:i,phase:"Wait",start:r,end:a}),e(t,{id:i,phase:"Activate",start:a,end:o}))}(d,t.id,o,a,r,s,n),d}const i=this.registration.versionsByMode(),s=["active","waiting","installing","redundant"];for(const e of s){const s=i.get(e);if(s){return t(s)}}return[]}createTimingTable(){this.tableElement.classList.add("service-worker-update-timing-table"),this.tableElement.setAttribute("jslog",`${m.tree("update-timing-table")}`);const e=this.calculateServiceWorkerUpdateRanges();this.updateTimingTable(e)}createTimingTableHead(){const e=this.tableElement.createChild("tr","service-worker-update-timing-table-header");h.UIUtils.createTextChild(e.createChild("td"),$t(zt.version)),h.UIUtils.createTextChild(e.createChild("td"),$t(zt.updateActivity)),h.UIUtils.createTextChild(e.createChild("td"),$t(zt.timeline))}removeRows(){const e=this.tableElement.getElementsByTagName("tr");for(;e[0];)e[0].parentNode&&e[0].parentNode.removeChild(e[0]);this.rows=[]}updateTimingTable(e){this.selectedRowIndex=-1,this.removeRows(),this.createTimingTableHead();const t=e;if(0===t.length)return;const i=t.map((e=>e.start)),s=t.map((e=>e.end)),r=i.reduce(((e,t)=>Math.min(e,t))),a=s.reduce(((e,t)=>Math.max(e,t))),o=100/(a-r);for(const e of t){const t=e.phase,i=o*(e.start-r),s=o*(a-e.end),n=this.tableElement.createChild("tr","service-worker-update-timeline");n.setAttribute("jslog",`${m.treeItem("update-timeline")}`),this.rows.push(n);const d=n.createChild("td");h.UIUtils.createTextChild(d,"#"+e.id),d.classList.add("service-worker-update-timing-bar-clickable"),d.setAttribute("tabindex","0"),d.setAttribute("role","switch"),d.addEventListener("focus",(e=>{this.onFocus(e)})),d.setAttribute("jslog",`${m.expand("timing-info").track({click:!0})}`),h.ARIAUtils.setChecked(d,!1);const l=n.createChild("td");h.UIUtils.createTextChild(l,t);const c=n.createChild("td").createChild("div","service-worker-update-timing-row").createChild("span","service-worker-update-timing-bar "+t.toLowerCase());c.style.left=i+"%",c.style.right=s+"%",c.textContent="​",this.constructUpdateDetails(n,e)}}constructUpdateDetails(e,t){const i=this.tableElement.createChild("tr","service-worker-update-timing-bar-details");i.classList.add("service-worker-update-timing-bar-details-collapsed");const s=i.createChild("td");s.colSpan=3;const r=new Date(t.start).toISOString();h.UIUtils.createTextChild(s.createChild("span"),$t(zt.startTimeS,{PH1:r})),i.tabIndex=0;const a=this.tableElement.createChild("tr","service-worker-update-timing-bar-details");a.classList.add("service-worker-update-timing-bar-details-collapsed");const o=a.createChild("td");o.colSpan=3;const n=new Date(t.end).toISOString();h.UIUtils.createTextChild(o.createChild("span"),$t(zt.endTimeS,{PH1:n})),a.tabIndex=0,e.addEventListener("keydown",(e=>{this.onKeydown(e,i,a)})),e.addEventListener("click",(e=>{this.onClick(e,i,a)}))}toggle(e,t,i,s){i.classList.contains("service-worker-update-timing-bar-clickable")&&(e.classList.toggle("service-worker-update-timing-bar-details-collapsed"),e.classList.toggle("service-worker-update-timing-bar-details-expanded"),t.classList.toggle("service-worker-update-timing-bar-details-collapsed"),t.classList.toggle("service-worker-update-timing-bar-details-expanded"),h.ARIAUtils.setChecked(i,!s))}onFocus(e){const t=e.target;if(!t)return;const i=t.parentElement;i&&(this.selectedRowIndex=this.rows.indexOf(i))}onKeydown(e,t,i){if(!e.target)return;const s=e.target,r=e,a="true"===s.getAttribute("aria-checked");return"Enter"===r.key||" "===r.key||!a&&"ArrowRight"===r.key||a&&"ArrowLeft"===r.key?(this.toggle(t,i,s,a),void e.preventDefault()):("ArrowDown"===r.key&&(this.selectedRowIndex>=0?this.selectNextRow():this.selectFirstRow(),e.preventDefault()),void("ArrowUp"===r.key&&(this.selectedRowIndex>=0?this.selectPreviousRow():this.selectLastRow(),e.preventDefault())))}focusRow(e){e.cells[0].focus()}blurRow(e){e.cells[0].blur()}selectFirstRow(){0!==this.rows.length&&(this.selectedRowIndex=0,this.focusRow(this.rows[0]))}selectLastRow(){0!==this.rows.length&&(this.selectedRowIndex=this.rows.length-1,this.focusRow(this.rows[this.selectedRowIndex]))}selectNextRow(){if(0===this.rows.length)return;const e=this.selectedRowIndex;this.selectedRowIndex++,this.selectedRowIndex>=this.rows.length&&(this.selectedRowIndex=0),this.blurRow(this.rows[e]),this.focusRow(this.rows[this.selectedRowIndex])}selectPreviousRow(){if(0===this.rows.length)return;const e=this.selectedRowIndex;this.selectedRowIndex--,this.selectedRowIndex<0&&(this.selectedRowIndex=this.rows.length-1),this.blurRow(this.rows[e]),this.focusRow(this.rows[this.selectedRowIndex])}onClick(e,t,i){const s=e.target;if(!s)return;const r="true"===s.getAttribute("aria-checked");this.toggle(t,i,s,r),e.preventDefault()}refresh(){const e=this.calculateServiceWorkerUpdateRanges();this.updateTimingTable(e)}}var Qt=Object.freeze({__proto__:null,ServiceWorkerUpdateCycleView:Jt});const Xt={serviceWorkersFromOtherOrigins:"Service workers from other origins",updateOnReload:"Update on reload",onPageReloadForceTheService:"On page reload, force the `service worker` to update, and activate it",bypassForNetwork:"Bypass for network",bypassTheServiceWorkerAndLoad:"Bypass the `service worker` and load resources from the network",serviceWorkerForS:"`Service worker` for {PH1}",testPushMessageFromDevtools:"Test push message from DevTools.",networkRequests:"Network requests",update:"Update",unregisterServiceWorker:"Unregister service worker",unregister:"Unregister",source:"Source",status:"Status",clients:"Clients",pushString:"Push",pushData:"Push data",syncString:"Sync",syncTag:"Sync tag",periodicSync:"Periodic Sync",periodicSyncTag:"Periodic Sync tag",sRegistrationErrors:"{PH1} registration errors",receivedS:"Received {PH1}",routers:"Routers",sDeleted:"{PH1} - deleted",sActivatedAndIsS:"#{PH1} activated and is {PH2}",stopString:"stop",inspect:"inspect",startString:"start",sIsRedundant:"#{PH1} is redundant",sWaitingToActivate:"#{PH1} waiting to activate",sTryingToInstall:"#{PH1} trying to install",updateCycle:"Update Cycle",workerS:"Worker: {PH1}",focus:"focus",seeAllRegistrations:"See all registrations"},Yt=i.i18n.registerUIStrings("panels/application/ServiceWorkersView.ts",Xt),Zt=i.i18n.getLocalizedString.bind(void 0,Yt);let ei=!1;class ti extends h.Widget.VBox{currentWorkersView;toolbar;sections;manager;securityOriginManager;sectionToRegistration;eventListeners;constructor(){super(!0),this.currentWorkersView=new h.ReportView.ReportView(i.i18n.lockedString("Service workers")),this.currentWorkersView.setBodyScrollable(!1),this.contentElement.classList.add("service-worker-list"),this.contentElement.setAttribute("jslog",`${m.pane("service-workers")}`),this.currentWorkersView.show(this.contentElement),this.currentWorkersView.element.classList.add("service-workers-this-origin"),this.currentWorkersView.element.setAttribute("jslog",`${m.section("this-origin")}`),this.toolbar=this.currentWorkersView.createToolbar(),this.sections=new Map,this.manager=null,this.securityOriginManager=null,this.sectionToRegistration=new WeakMap;const s=this.contentElement.createChild("div","service-workers-other-origin");s.setAttribute("jslog",`${m.section("other-origin")}`);const r=new h.ReportView.ReportView;r.setHeaderVisible(!1),r.show(s);const a=r.appendSection(Zt(Xt.serviceWorkersFromOtherOrigins)).appendRow(),n=h.Fragment.html`<a class="devtools-link" role="link" tabindex="0" href="chrome://serviceworker-internals" target="_blank" style="display: inline; cursor: pointer;">${Zt(Xt.seeAllRegistrations)}</a>`;n.setAttribute("jslog",`${m.link("view-all").track({click:!0})}`),self.onInvokeElement(n,(e=>{const t=o.TargetManager.TargetManager.instance().rootTarget();t&&t.targetAgent().invoke_createTarget({url:"chrome://serviceworker-internals?devtools"}),e.consume(!0)})),a.appendChild(n),this.toolbar.appendToolbarItem(L.ThrottlingManager.throttlingManager().createOfflineToolbarCheckbox());const d=e.Settings.Settings.instance().createSetting("service-worker-update-on-reload",!1);d.setTitle(Zt(Xt.updateOnReload));const l=new h.Toolbar.ToolbarSettingCheckbox(d,Zt(Xt.onPageReloadForceTheService));this.toolbar.appendToolbarItem(l);const c=e.Settings.Settings.instance().createSetting("bypass-service-worker",!1);c.setTitle(Zt(Xt.bypassForNetwork));const g=new h.Toolbar.ToolbarSettingCheckbox(c,Zt(Xt.bypassTheServiceWorkerAndLoad));this.toolbar.appendToolbarItem(g),this.eventListeners=new Map,o.TargetManager.TargetManager.instance().observeModels(o.ServiceWorkerManager.ServiceWorkerManager,this),this.updateListVisibility();document.body.addEventListener("drawerchange",(i=>{const s=i.detail&&i.detail.isDrawerOpen;if(this.manager&&!s){const{serviceWorkerNetworkRequestsPanelStatus:{isOpen:i,openedAt:s}}=this.manager;if(i){const i=h.ViewManager.ViewManager.instance().locationNameForViewId("network");h.ViewManager.ViewManager.instance().showViewInLocation("network",i,!1),e.Revealer.reveal(R.UIFilter.UIRequestFilter.filters([]));Date.now()-s<2e3&&t.userMetrics.actionTaken(t.UserMetrics.Action.ServiceWorkerNetworkRequestClosedQuickly),this.manager.serviceWorkerNetworkRequestsPanelStatus={isOpen:!1,openedAt:0}}}}))}modelAdded(e){if(e.target()===o.TargetManager.TargetManager.instance().primaryPageTarget()){this.manager=e,this.securityOriginManager=e.target().model(o.SecurityOriginManager.SecurityOriginManager);for(const e of this.manager.registrations().values())this.updateRegistration(e);this.eventListeners.set(e,[this.manager.addEventListener("RegistrationUpdated",this.registrationUpdated,this),this.manager.addEventListener("RegistrationDeleted",this.registrationDeleted,this),this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.updateSectionVisibility,this),this.securityOriginManager.addEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.updateSectionVisibility,this)])}}modelRemoved(t){this.manager&&this.manager===t&&(e.EventTarget.removeEventListeners(this.eventListeners.get(t)||[]),this.eventListeners.delete(t),this.manager=null,this.securityOriginManager=null)}getTimeStamp(e){const t=e.versionsByMode();let i=0;const s=t.get("active"),r=t.get("installing"),a=t.get("waiting"),o=t.get("redundant");return s?i=s.scriptResponseTime:a?i=a.scriptResponseTime:r?i=r.scriptResponseTime:o&&(i=o.scriptResponseTime),i||0}updateSectionVisibility(){let e=!1;const t=[];for(const i of this.sections.values()){const s=this.getReportViewForOrigin(i.registration.securityOrigin);e=e||s===this.currentWorkersView,i.section.parentWidget()!==s&&t.push(i)}for(const e of t){const t=e.registration;this.removeRegistrationFromList(t),this.updateRegistration(t,!0)}this.currentWorkersView.sortSections(((e,t)=>{const i=this.sectionToRegistration.get(e),s=this.sectionToRegistration.get(t),r=i?this.getTimeStamp(i):0;return(s?this.getTimeStamp(s):0)-r}));for(const e of this.sections.values())e.section.parentWidget()===this.currentWorkersView||this.isRegistrationVisible(e.registration)?e.section.showWidget():e.section.hideWidget();this.contentElement.classList.toggle("service-worker-has-current",Boolean(e)),this.updateListVisibility()}registrationUpdated(e){this.updateRegistration(e.data),this.gcRegistrations()}gcRegistrations(){if(!this.manager||!this.securityOriginManager)return;let e=!1;const t=new Set(this.securityOriginManager.securityOrigins());for(const i of this.manager.registrations().values())if((t.has(i.securityOrigin)||this.isRegistrationVisible(i))&&!i.canBeRemoved()){e=!0;break}if(e)for(const e of this.manager.registrations().values()){!(t.has(e.securityOrigin)||this.isRegistrationVisible(e))&&e.canBeRemoved()&&this.removeRegistrationFromList(e)}}getReportViewForOrigin(e){return this.securityOriginManager&&(this.securityOriginManager.securityOrigins().includes(e)||this.securityOriginManager.unreachableMainSecurityOrigin()===e)?this.currentWorkersView:null}updateRegistration(e,t){let i=this.sections.get(e);if(!i){const t=e.scopeURL,s=this.getReportViewForOrigin(e.securityOrigin);if(!s)return;const r=s.appendSection(t);r.setUiGroupTitle(Zt(Xt.serviceWorkerForS,{PH1:t})),this.sectionToRegistration.set(r,e),i=new ii(this.manager,r,e),this.sections.set(e,i)}t||(this.updateSectionVisibility(),i.scheduleUpdate())}registrationDeleted(e){this.removeRegistrationFromList(e.data)}removeRegistrationFromList(e){const t=this.sections.get(e);t&&t.section.detach(),this.sections.delete(e),this.updateSectionVisibility()}isRegistrationVisible(e){return!e.scopeURL}updateListVisibility(){this.contentElement.classList.toggle("service-worker-list-empty",0===this.sections.size)}wasShown(){super.wasShown(),this.registerCSSFiles([Gt])}}class ii{manager;section;registration;fingerprint;pushNotificationDataSetting;syncTagNameSetting;periodicSyncTagNameSetting;toolbar;updateCycleView;routerView;networkRequests;updateButton;deleteButton;sourceField;statusField;clientsField;linkifier;clientInfoCache;throttler;updateCycleField;routerField;constructor(t,i,s){this.manager=t,this.section=i,this.registration=s,this.fingerprint=null,this.pushNotificationDataSetting=e.Settings.Settings.instance().createLocalSetting("push-data",Zt(Xt.testPushMessageFromDevtools)),this.syncTagNameSetting=e.Settings.Settings.instance().createLocalSetting("sync-tag-name","test-tag-from-devtools"),this.periodicSyncTagNameSetting=e.Settings.Settings.instance().createLocalSetting("periodic-sync-tag-name","test-tag-from-devtools"),this.toolbar=i.createToolbar(),this.toolbar.renderAsLinks(),this.updateCycleView=new Jt(s),this.routerView=new w.ServiceWorkerRouterView.ServiceWorkerRouterView,this.networkRequests=new h.Toolbar.ToolbarButton(Zt(Xt.networkRequests),void 0,Zt(Xt.networkRequests)),this.networkRequests.addEventListener("Click",this.networkRequestsClicked,this),this.networkRequests.element.setAttribute("jslog",`${m.action("show-network-requests").track({click:!0})}`),this.toolbar.appendToolbarItem(this.networkRequests),this.updateButton=new h.Toolbar.ToolbarButton(Zt(Xt.update),void 0,Zt(Xt.update)),this.updateButton.addEventListener("Click",this.updateButtonClicked,this),this.updateButton.element.setAttribute("jslog",`${m.action("update").track({click:!0})}`),this.toolbar.appendToolbarItem(this.updateButton),this.deleteButton=new h.Toolbar.ToolbarButton(Zt(Xt.unregisterServiceWorker),void 0,Zt(Xt.unregister)),this.deleteButton.addEventListener("Click",this.unregisterButtonClicked,this),this.deleteButton.element.setAttribute("jslog",`${m.action("unregister").track({click:!0})}`),this.toolbar.appendToolbarItem(this.deleteButton),this.sourceField=this.wrapWidget(this.section.appendField(Zt(Xt.source))),this.statusField=this.wrapWidget(this.section.appendField(Zt(Xt.status))),this.clientsField=this.wrapWidget(this.section.appendField(Zt(Xt.clients))),this.createSyncNotificationField(Zt(Xt.pushString),this.pushNotificationDataSetting.get(),Zt(Xt.pushData),this.push.bind(this),"push-message"),this.createSyncNotificationField(Zt(Xt.syncString),this.syncTagNameSetting.get(),Zt(Xt.syncTag),this.sync.bind(this),"sync-tag"),this.createSyncNotificationField(Zt(Xt.periodicSync),this.periodicSyncTagNameSetting.get(),Zt(Xt.periodicSyncTag),(e=>this.periodicSync(e)),"periodic-sync-tag"),this.createUpdateCycleField(),this.maybeCreateRouterField(),this.linkifier=new p.Linkifier.Linkifier,this.clientInfoCache=new Map,this.throttler=new e.Throttler.Throttler(500)}createSyncNotificationField(e,t,i,s,r){const a=this.wrapWidget(this.section.appendField(e)).createChild("form","service-worker-editor-with-button"),o=h.UIUtils.createInput("source-code service-worker-notification-editor");o.setAttribute("jslog",`${m.textField().track({keydown:!0}).context(r)}`),a.appendChild(o);const n=h.UIUtils.createTextButton(e,void 0,{jslogContext:r});n.type="submit",a.appendChild(n),o.value=t,o.placeholder=i,h.ARIAUtils.setLabel(o,e),a.addEventListener("submit",(e=>{s(o.value||""),e.consume(!0)}))}scheduleUpdate(){ei?this.update():this.throttler.schedule(this.update.bind(this))}targetForVersionId(e){const t=this.manager.findVersion(e);return t&&t.targetId?o.TargetManager.TargetManager.instance().targetById(t.targetId):null}addVersion(e,t,i){const s=e.createChild("div","service-worker-version");s.createChild("div",t);const r=s.createChild("span","service-worker-version-string");return r.textContent=i,h.ARIAUtils.markAsAlert(r),s}updateClientsField(e){this.clientsField.removeChildren(),this.section.setFieldVisible(Zt(Xt.clients),Boolean(e.controlledClients.length));for(const t of e.controlledClients){const e=this.clientsField.createChild("div","service-worker-client"),i=this.clientInfoCache.get(t);i&&this.updateClientInfo(e,i),this.manager.target().targetAgent().invoke_getTargetInfo({targetId:t}).then(this.onClientInfo.bind(this,e))}}updateSourceField(t){this.sourceField.removeChildren();const i=e.ParsedURL.ParsedURL.extractName(t.scriptURL),s=this.sourceField.createChild("div","report-field-value-filename"),r=p.Linkifier.Linkifier.linkifyURL(t.scriptURL,{text:i});if(r.tabIndex=0,r.setAttribute("jslog",`${m.link("source-location").track({click:!0})}`),s.appendChild(r),this.registration.errors.length){const t=h.UIUtils.createIconLabel({title:String(this.registration.errors.length),iconName:"cross-circle-filled",color:"var(--icon-error)"});t.classList.add("devtools-link","link"),t.tabIndex=0,h.ARIAUtils.setLabel(t,Zt(Xt.sRegistrationErrors,{PH1:this.registration.errors.length})),self.onInvokeElement(t,(()=>e.Console.Console.instance().show())),s.appendChild(t)}void 0!==t.scriptResponseTime&&(this.sourceField.createChild("div","report-field-value-subtitle").textContent=Zt(Xt.receivedS,{PH1:new Date(1e3*t.scriptResponseTime).toLocaleString()}))}update(){const e=this.registration.fingerprint();if(e===this.fingerprint)return Promise.resolve();this.fingerprint=e,this.toolbar.setEnabled(!this.registration.isDeleted);const t=this.registration.versionsByMode(),s=this.registration.scopeURL,r=this.registration.isDeleted?Zt(Xt.sDeleted,{PH1:s}):s;this.section.setTitle(r);const a=t.get("active"),n=t.get("waiting"),d=t.get("installing"),l=t.get("redundant");this.statusField.removeChildren();const c=this.statusField.createChild("div","service-worker-version-stack");if(c.createChild("div","service-worker-version-stack-bar"),a){this.updateSourceField(a);const e=o.ServiceWorkerManager.ServiceWorkerVersion.RunningStatus[a.currentState.runningStatus](),t=this.addVersion(c,"service-worker-active-circle",Zt(Xt.sActivatedAndIsS,{PH1:a.id,PH2:e}));if(a.isRunning()||a.isStarting()){this.createLink(t,Zt(Xt.stopString),this.stopButtonClicked.bind(this,a.id)).setAttribute("jslog",`${m.action("stop").track({click:!0})}`),this.targetForVersionId(a.id)||this.createLink(t,Zt(Xt.inspect),this.inspectButtonClicked.bind(this,a.id))}else if(a.isStartable()){this.createLink(t,Zt(Xt.startString),this.startButtonClicked.bind(this)).setAttribute("jslog",`${m.action("start").track({click:!0})}`)}this.updateClientsField(a),this.maybeCreateRouterField()}else l&&(this.updateSourceField(l),this.addVersion(c,"service-worker-redundant-circle",Zt(Xt.sIsRedundant,{PH1:l.id})),this.updateClientsField(l));if(n){const e=this.addVersion(c,"service-worker-waiting-circle",Zt(Xt.sWaitingToActivate,{PH1:n.id}));this.createLink(e,i.i18n.lockedString("skipWaiting"),this.skipButtonClicked.bind(this)),void 0!==n.scriptResponseTime&&(e.createChild("div","service-worker-subtitle").textContent=Zt(Xt.receivedS,{PH1:new Date(1e3*n.scriptResponseTime).toLocaleString()})),this.targetForVersionId(n.id)||!n.isRunning()&&!n.isStarting()||this.createLink(e,Zt(Xt.inspect),this.inspectButtonClicked.bind(this,n.id))}if(d){const e=this.addVersion(c,"service-worker-installing-circle",Zt(Xt.sTryingToInstall,{PH1:d.id}));void 0!==d.scriptResponseTime&&(e.createChild("div","service-worker-subtitle").textContent=Zt(Xt.receivedS,{PH1:new Date(1e3*d.scriptResponseTime).toLocaleString()})),this.targetForVersionId(d.id)||!d.isRunning()&&!d.isStarting()||this.createLink(e,Zt(Xt.inspect),this.inspectButtonClicked.bind(this,d.id))}return this.updateCycleView.refresh(),Promise.resolve()}createLink(e,t,i,s,r){const a=document.createElement("button");return s&&(a.className=s),a.classList.add("link","devtools-link"),a.textContent=t,a.tabIndex=0,a.addEventListener("click",i,r),e.appendChild(a),a}unregisterButtonClicked(){this.manager.deleteRegistration(this.registration.id)}createUpdateCycleField(){this.updateCycleField=this.wrapWidget(this.section.appendField(Zt(Xt.updateCycle))),this.updateCycleField.appendChild(this.updateCycleView.tableElement)}maybeCreateRouterField(){const e=this.registration.versionsByMode().get("active"),t=Zt(Xt.routers);e&&e.routerRules&&e.routerRules.length>0?(this.routerField||(this.routerField=this.wrapWidget(this.section.appendField(t))),this.routerField.lastElementChild||this.routerField.appendChild(this.routerView),this.routerView.update(e.routerRules)):(this.section.removeField(t),this.routerField=void 0)}updateButtonClicked(){this.manager.updateRegistration(this.registration.id)}networkRequestsClicked(){const i="drawer-view"===h.ViewManager.ViewManager.instance().locationNameForViewId("resources")?"panel":"drawer-view";h.ViewManager.ViewManager.instance().showViewInLocation("network",i),e.Revealer.reveal(R.UIFilter.UIRequestFilter.filters([{filterType:R.UIFilter.FilterType.Is,filterValue:"service-worker-intercepted"}]));const s=M.NetworkLog.NetworkLog.instance().requests();let r=null;if(Array.isArray(s))for(const e of s)!r&&e.fetchedViaServiceWorker&&(r=e),e.fetchedViaServiceWorker&&r&&r.responseReceivedTime<e.responseReceivedTime&&(r=e);if(r){const t=R.UIRequestLocation.UIRequestLocation.tab(r,"timing",{clearFilter:!1});e.Revealer.reveal(t)}this.manager.serviceWorkerNetworkRequestsPanelStatus={isOpen:!0,openedAt:Date.now()},t.userMetrics.actionTaken(t.UserMetrics.Action.ServiceWorkerNetworkRequestClicked)}push(e){this.pushNotificationDataSetting.set(e),this.manager.deliverPushMessage(this.registration.id,e)}sync(e){this.syncTagNameSetting.set(e),this.manager.dispatchSyncEvent(this.registration.id,e,!0)}periodicSync(e){this.periodicSyncTagNameSetting.set(e),this.manager.dispatchPeriodicSyncEvent(this.registration.id,e)}onClientInfo(e,t){const i=t.targetInfo;i&&(this.clientInfoCache.set(i.targetId,i),this.updateClientInfo(e,i))}updateClientInfo(e,t){if("page"!==t.type&&"iframe"===t.type){const i=e.createChild("span","service-worker-client-string");return void h.UIUtils.createTextChild(i,Zt(Xt.workerS,{PH1:t.url}))}e.removeChildren();const i=e.createChild("span","service-worker-client-string");h.UIUtils.createTextChild(i,t.url);this.createLink(e,Zt(Xt.focus),this.activateTarget.bind(this,t.targetId),"service-worker-client-focus-link").setAttribute("jslog",`${m.action("client-focus").track({click:!0})}`)}activateTarget(e){this.manager.target().targetAgent().invoke_activateTarget({targetId:e})}startButtonClicked(){this.manager.startWorker(this.registration.scopeURL)}skipButtonClicked(){this.manager.skipWaiting(this.registration.scopeURL)}stopButtonClicked(e){this.manager.stopWorker(e)}inspectButtonClicked(e){this.manager.inspectWorker(e)}wrapWidget(e){const t=h.Utils.createShadowRootWithCoreStyles(e,{cssFile:[Gt,Kt],delegatesFocus:void 0}),i=document.createElement("div");return t.appendChild(i),i}}var si=Object.freeze({__proto__:null,setThrottleDisabledForDebugging:e=>{ei=e},ServiceWorkersView:ti,Section:ii});const ri=new CSSStyleSheet;ri.replaceSync("devtools-shared-storage-access-grid{overflow:auto}.placeholder{align-items:center;justify-content:center;font-size:13px;color:var(--sys-color-token-subtle);overflow:auto;text-align:center;& div{width:100%}}\n/*# sourceURL=sharedStorageEventsView.css */\n");const ai={clickToDisplayBody:"Click on any shared storage event to display the event parameters."},oi=i.i18n.registerUIStrings("panels/application/SharedStorageEventsView.ts",ai),ni=i.i18n.getLocalizedString.bind(void 0,oi);class di extends h.SplitWidget.SplitWidget{#n=new w.SharedStorageAccessGrid.SharedStorageAccessGrid;#d=[];#l;#c="";constructor(){super(!1,!0),this.element.setAttribute("jslog",`${m.pane("shared-storage-events")}`);const e=new h.Widget.VBox;this.#l=new h.Widget.VBox,e.setMinimumSize(0,80),this.setMainWidget(e),this.#l.setMinimumSize(0,40),this.setSidebarWidget(this.#l),e.contentElement.appendChild(this.#n),this.#n.addEventListener("cellfocused",this.#h.bind(this)),this.#n.setAttribute("jslog",`${m.section("events-table")}`),this.#g()?.addEventListener(o.ResourceTreeModel.Events.PrimaryPageChanged,this.clearEvents,this),this.#l.contentElement.classList.add("placeholder");this.#l.contentElement.createChild("div").textContent=ni(ai.clickToDisplayBody)}#g(){const e=o.TargetManager.TargetManager.instance().primaryPageTarget();return e?.model(o.ResourceTreeModel.ResourceTreeModel)||null}#u(){return this.#g()?.mainFrame||null}get id(){return this.#u()?.id||this.#c}wasShown(){super.wasShown();const e=this.sidebarWidget();e&&e.registerCSSFiles([ri])}addEvent(e){e.mainFrameId===this.id&&(this.#d.some((t=>{return i=t,s=e,JSON.stringify(i)===JSON.stringify(s);var i,s}))||(this.#d.push(e),this.#n.data=this.#d))}clearEvents(){this.#d=[],this.#n.data=this.#d,this.setSidebarWidget(this.#l)}async#h(e){const t=e.data.row;if(!t)return;const i={accessTime:t.cells.find((e=>"event-time"===e.columnId))?.value,accessType:t.cells.find((e=>"event-type"===e.columnId))?.value,ownerOrigin:t.cells.find((e=>"event-owner-origin"===e.columnId))?.value,eventParams:JSON.parse(t.cells.find((e=>"event-params"===e.columnId))?.value)},s=c.JSONView.JSONView.createViewSync(i);s.setMinimumSize(0,40),this.setSidebarWidget(s)}setDefaultIdForTesting(e){this.#c=e}getEventsForTesting(){return this.#d}getSharedStorageAccessGridForTesting(){return this.#n}}var li=Object.freeze({__proto__:null,SharedStorageEventsView:di});const ci={sharedStorage:"Shared storage"},hi=i.i18n.registerUIStrings("panels/application/SharedStorageListTreeElement.ts",ci),gi=i.i18n.getLocalizedString.bind(void 0,hi);class ui extends P{#p;view;constructor(t,i=!1){super(t,gi(ci.sharedStorage),!1),this.#p=e.Settings.Settings.instance().createSetting("resources-shared-storage-expanded",i);const s=d.Icon.create("database");this.setLeadingIcons([s]),this.view=new di}get itemURL(){return"shared-storage://"}onselect(e){return super.onselect(e),this.resourcesPanel.showView(this.view),!1}onattach(){super.onattach(),this.#p.get()&&this.expand()}onexpand(){this.#p.set(!0)}oncollapse(){this.#p.set(!1)}addEvent(e){this.view.addEvent(e)}}var pi=Object.freeze({__proto__:null,SharedStorageListTreeElement:ui});class mi extends e.ObjectWrapper.ObjectWrapper{#e;#m;constructor(e,t){super(),this.#e=e,this.#m=t}get securityOrigin(){return this.#m}async getMetadata(){return this.#e.storageAgent.invoke_getSharedStorageMetadata({ownerOrigin:this.securityOrigin}).then((({metadata:e})=>e))}async getEntries(){return this.#e.storageAgent.invoke_getSharedStorageEntries({ownerOrigin:this.securityOrigin}).then((({entries:e})=>e))}async setEntry(e,t,i){await this.#e.storageAgent.invoke_setSharedStorageEntry({ownerOrigin:this.securityOrigin,key:e,value:t,ignoreIfPresent:i})}async deleteEntry(e){await this.#e.storageAgent.invoke_deleteSharedStorageEntry({ownerOrigin:this.securityOrigin,key:e})}async clear(){await this.#e.storageAgent.invoke_clearSharedStorageEntries({ownerOrigin:this.securityOrigin})}async resetBudget(){await this.#e.storageAgent.invoke_resetSharedStorageBudget({ownerOrigin:this.securityOrigin})}}class wi extends o.SDKModel.SDKModel{#w;#v;storageAgent;#S;constructor(e){super(e),e.registerStorageDispatcher(this),this.#w=e.model(o.SecurityOriginManager.SecurityOriginManager),this.#v=new Map,this.storageAgent=e.storageAgent(),this.#S=!1}async enable(){this.#S||(this.#w.addEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.#b,this),this.#w.addEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.#k,this),await this.storageAgent.invoke_setSharedStorageTracking({enable:!0}),this.#y(),this.#S=!0)}disable(){this.#S&&(this.#w.removeEventListener(o.SecurityOriginManager.Events.SecurityOriginAdded,this.#b,this),this.#w.removeEventListener(o.SecurityOriginManager.Events.SecurityOriginRemoved,this.#k,this),this.storageAgent.invoke_setSharedStorageTracking({enable:!1}),this.#f(),this.#S=!1)}dispose(){this.disable()}#y(){for(const e of this.#w.securityOrigins())this.#C(e)}#f(){for(const e of this.#v.keys())this.#T(e)}#b(e){this.#C(e.data)}#C(t){const i=new e.ParsedURL.ParsedURL(t);if(!i.isValid||"data"===i.scheme||"about"===i.scheme||"javascript"===i.scheme)return;if(this.#v.has(t))return;const s=new mi(this,t);this.#v.set(t,s),this.dispatchEventToListeners("SharedStorageAdded",s)}#k(e){this.#T(e.data)}#T(e){const t=this.storageForOrigin(e);t&&(this.#v.delete(e),this.dispatchEventToListeners("SharedStorageRemoved",t))}storages(){return this.#v.values()}storageForOrigin(e){return this.#v.get(e)||null}numStoragesForTesting(){return this.#v.size}isChangeEvent(e){return["documentSet","documentAppend","documentDelete","documentClear","workletSet","workletAppend","workletDelete","workletClear"].includes(e.type)}sharedStorageAccessed(e){if(this.isChangeEvent(e)){const t=this.storageForOrigin(e.ownerOrigin);if(t){const i={accessTime:e.accessTime,type:e.type,mainFrameId:e.mainFrameId,params:e.params};t.dispatchEventToListeners("SharedStorageChanged",i)}else this.#C(e.ownerOrigin)}this.dispatchEventToListeners("SharedStorageAccess",e)}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}attributionReportingSourceRegistered(e){}}o.SDKModel.SDKModel.register(wi,{capabilities:8192,autostart:!1});var vi=Object.freeze({__proto__:null,SharedStorageForOrigin:mi,SharedStorageModel:wi});const Si={refresh:"Refresh",filter:"Filter",clearAll:"Clear All",deleteSelected:"Delete Selected",refreshedStatus:"Table refreshed"},bi=i.i18n.registerUIStrings("panels/application/StorageItemsView.ts",Si),ki=i.i18n.getLocalizedString.bind(void 0,bi);class yi extends h.Widget.VBox{filterRegex;refreshButton;mainToolbar;filterItem;deleteAllButton;deleteSelectedButton;metadataView=new w.StorageMetadataView.StorageMetadataView;constructor(e,t){super(!1),this.filterRegex=null,this.refreshButton=this.addButton(ki(Si.refresh),"refresh",(()=>{this.refreshItems(),h.ARIAUtils.alert(ki(Si.refreshedStatus))})),this.refreshButton.element.setAttribute("jslog",`${m.action("storage-items-view.refresh").track({click:!0})}`),this.mainToolbar=new h.Toolbar.Toolbar("top-resources-toolbar",this.element),this.mainToolbar.element.setAttribute("jslog",`${m.toolbar()}`),this.filterItem=new h.Toolbar.ToolbarInput(ki(Si.filter),"",.4),this.filterItem.addEventListener("TextChanged",this.filterChanged,this);const i=new h.Toolbar.ToolbarSeparator;this.deleteAllButton=this.addButton(ki(Si.clearAll),"clear",this.deleteAllItems),this.deleteSelectedButton=this.addButton(ki(Si.deleteSelected),"cross",this.deleteSelectedItem),this.deleteSelectedButton.element.setAttribute("jslog",`${m.action("storage-items-view.delete-selected").track({click:!0})}`),this.deleteAllButton.element.id="storage-items-delete-all",this.deleteAllButton.element.setAttribute("jslog",`${m.action("storage-items-view.clear-all").track({click:!0})}`);const s=[this.refreshButton,this.filterItem,i,this.deleteAllButton,this.deleteSelectedButton];for(const e of s)this.mainToolbar.appendToolbarItem(e);this.contentElement.appendChild(this.metadataView)}setDeleteAllTitle(e){this.deleteAllButton.setTitle(e)}setDeleteAllGlyph(e){this.deleteAllButton.setGlyph(e)}appendToolbarItem(e){this.mainToolbar.appendToolbarItem(e)}setStorageKey(e){this.metadataView.setStorageKey(e)}addButton(e,t,i){const s=new h.Toolbar.ToolbarButton(e,t);return s.addEventListener("Click",i,this),s}filterChanged({data:e}){this.filterRegex=e?new RegExp(s.StringUtilities.escapeForRegExp(e),"i"):null,this.refreshItems()}filter(e,t){if(this.filterRegex){const i=this.filterRegex;return e.filter((e=>i.test(t(e))))}return e}hasFilter(){return Boolean(this.filterRegex)}wasShown(){this.refreshItems()}setCanDeleteAll(e){this.deleteAllButton.setEnabled(e)}setCanDeleteSelected(e){this.deleteSelectedButton.setEnabled(e)}setCanRefresh(e){this.refreshButton.setEnabled(e)}setCanFilter(e){this.filterItem.setEnabled(e)}deleteAllItems(){}deleteSelectedItem(){}refreshItems(){}}var fi=Object.freeze({__proto__:null,StorageItemsView:yi});const Ci={sharedStorage:"Shared storage",key:"Key",value:"Value",sharedStorageItems:"Shared Storage Items",sharedStorageItemsCleared:"Shared Storage items cleared",sharedStorageFilteredItemsCleared:"Shared Storage filtered items cleared",selectAValueToPreview:"Select a value to preview",sharedStorageItemDeleted:"The storage item was deleted.",sharedStorageItemEdited:"The storage item was edited.",sharedStorageItemEditCanceled:"The storage item edit was canceled.",sharedStorageNumberEntries:"Number of entries shown in table: {PH1}"},Ti=i.i18n.registerUIStrings("panels/application/SharedStorageItemsView.ts",Ci),Ii=i.i18n.getLocalizedString.bind(void 0,Ti);class Ei extends yi{#I;outerSplitWidget;innerSplitWidget;#E;dataGrid;#l;#x;sharedStorageItemsDispatcher;constructor(t){super(Ii(Ci.sharedStorage),"sharedStoragePanel"),this.#I=t,this.element.classList.add("storage-view","table");const i=[{id:"key",title:Ii(Ci.key),sortable:!1,editable:!0,longText:!0,weight:50},{id:"value",title:Ii(Ci.value),sortable:!1,editable:!0,longText:!0,weight:50}];this.dataGrid=new b.DataGrid.DataGridImpl({displayName:Ii(Ci.sharedStorageItems),columns:i,editCallback:this.#M.bind(this),deleteCallback:this.#R.bind(this),refreshCallback:this.refreshItems.bind(this)}),this.dataGrid.addEventListener("SelectedNode",(e=>{this.#L(e.data)})),this.dataGrid.addEventListener("DeselectedNode",(()=>{this.#L(null)})),this.dataGrid.setStriped(!0),this.dataGrid.setName("shared-storage-items-view");const s=this.dataGrid.asWidget();s.setMinimumSize(0,100),this.#E=l.LegacyWrapper.legacyWrapper(h.Widget.VBox,new w.SharedStorageMetadataView.SharedStorageMetadataView(t,t.securityOrigin)),this.#E.setMinimumSize(0,275);const r=this.#E.element.createChild("div","metadata-view-resizer");this.innerSplitWidget=new h.SplitWidget.SplitWidget(!1,!1,"shared-storage-inner-split-view-state"),this.innerSplitWidget.setSidebarWidget(this.#E),this.innerSplitWidget.setMainWidget(s),this.innerSplitWidget.installResizer(r),this.#l=new h.Widget.VBox,this.#l.setMinimumSize(0,25),this.#l.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`);const a=this.#l.element.createChild("div","preview-panel-resizer");this.outerSplitWidget=new h.SplitWidget.SplitWidget(!1,!0,"shared-storage-outer-split-view-state"),this.outerSplitWidget.show(this.element),this.outerSplitWidget.setMainWidget(this.innerSplitWidget),this.outerSplitWidget.setSidebarWidget(this.#l),this.outerSplitWidget.installResizer(a),this.#l.contentElement.classList.add("placeholder");this.#l.contentElement.createChild("div").textContent=Ii(Ci.selectAValueToPreview),this.#x=[],e.EventTarget.removeEventListeners(this.#x),this.#I=t,this.#x=[this.#I.addEventListener("SharedStorageChanged",this.#B,this)],this.sharedStorageItemsDispatcher=new e.ObjectWrapper.ObjectWrapper}static async createView(e){const t=new Ei(e);return await t.updateEntriesOnly(),t}async updateEntriesOnly(){if(!this.isShowing())return;const e=await this.#I.getEntries();e&&this.#A(e)}async#B(){await this.refreshItems()}async refreshItems(){this.isShowing()&&(await this.#E.getComponent().render(),await this.updateEntriesOnly(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemsRefreshed"))}async deleteSelectedItem(){this.dataGrid.selectedNode&&await this.#R(this.dataGrid.selectedNode)}async deleteAllItems(){if(!this.hasFilter())return await this.#I.clear(),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemsCleared"),void h.ARIAUtils.alert(Ii(Ci.sharedStorageItemsCleared));await Promise.all(this.dataGrid.rootNode().children.filter((e=>e.data.key)).map((e=>this.#I.deleteEntry(e.data.key)))),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("FilteredItemsCleared"),h.ARIAUtils.alert(Ii(Ci.sharedStorageFilteredItemsCleared))}async#M(e,t,i,s){if("key"===t&&""===s)return await this.refreshItems(),void h.ARIAUtils.alert(Ii(Ci.sharedStorageItemEditCanceled));"key"===t?(await this.#I.deleteEntry(i),await this.#I.setEntry(s,e.data.value||"",!1)):await this.#I.setEntry(e.data.key||" ",s,!1),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemEdited",{columnIdentifier:t,oldText:i,newText:s}),h.ARIAUtils.alert(Ii(Ci.sharedStorageItemEdited))}#A(e){const t=this.dataGrid.rootNode(),[i]=t.children.filter((e=>e.selected)).map((e=>e.data.key));t.removeChildren();let s=null;const r=this.filter(e,(e=>`${e.key} ${e.value}`));for(const e of r){const r=new b.DataGrid.DataGridNode({key:e.key,value:e.value},!1);r.selectable=!0,t.appendChild(r),s&&e.key!==i||(s=r)}s&&(s.selected=!0),this.dataGrid.addCreationNode(!1),this.setCanDeleteSelected(Boolean(s)),h.ARIAUtils.alert(Ii(Ci.sharedStorageNumberEntries,{PH1:r.length}))}async#R(e){if(!e||e.isCreationNode||!this.#I)return;const t=e.data.key;await this.#I.deleteEntry(t),await this.refreshItems(),this.sharedStorageItemsDispatcher.dispatchEventToListeners("ItemDeleted",{key:t}),h.ARIAUtils.alert(Ii(Ci.sharedStorageItemDeleted))}async#L(e){const t=e?.data?.key,i=e?.data?.value,s=t&&{key:t,value:i||""};if(s){const t=c.JSONView.JSONView.createViewSync(s);e.selected&&(this.outerSplitWidget.setSidebarWidget(t),t.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`))}else this.outerSplitWidget.setSidebarWidget(this.#l)}getEntriesForTesting(){return this.dataGrid.rootNode().children.filter((e=>e.data.key)).map((e=>e.data))}}var xi=Object.freeze({__proto__:null,SharedStorageItemsView:Ei});class Mi extends P{view;constructor(e,t){super(e,t.securityOrigin,!1)}static async createElement(e,t){const i=new Mi(e,t);return i.view=await Ei.createView(t),i.view.element.setAttribute("jslog",`${m.pane("shared-storage-data")}`),i}get itemURL(){return"shared-storage://"}onselect(e){return super.onselect(e),this.resourcesPanel.showView(this.view),!1}}var Ri=Object.freeze({__proto__:null,SharedStorageTreeElement:Mi});const Li={storageBuckets:"Storage buckets"},Bi=i.i18n.registerUIStrings("panels/application/StorageBucketsTreeElement.ts",Li),Ai=i.i18n.getLocalizedString.bind(void 0,Bi);class Pi extends D{bucketTreeElements=new Set;constructor(e){super(e,Ai(Li.storageBuckets),"storage-buckets");const t=d.Icon.create("database");this.setLeadingIcons([t]),this.setLink("https://github.com/WICG/storage-buckets/blob/gh-pages/explainer.md")}initialize(){o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketAdded",this.bucketAdded,this),o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketRemoved",this.bucketRemoved,this),o.TargetManager.TargetManager.instance().addModelListener(o.StorageBucketsModel.StorageBucketsModel,"BucketChanged",this.bucketChanged,this);for(const e of o.TargetManager.TargetManager.instance().models(o.StorageBucketsModel.StorageBucketsModel)){const t=e.getBuckets();for(const i of t)this.addBucketTreeElement(e,i)}}removeBucketsForModel(e){for(const t of this.bucketTreeElements)t.model===e&&this.removeBucketTreeElement(t)}bucketAdded({data:{model:e,bucketInfo:t}}){this.addBucketTreeElement(e,t)}bucketRemoved({data:{model:e,bucketInfo:t}}){const i=this.getBucketTreeElement(e,t);i&&this.removeBucketTreeElement(i)}bucketChanged({data:{model:e,bucketInfo:t}}){const i=this.getBucketTreeElement(e,t);i&&(i.bucketInfo=t)}addBucketTreeElement(e,t){if(void 0===t.bucket.name)return;const i=new Di(this.resourcesPanel,e,t);this.bucketTreeElements.add(i),this.appendChild(i),i.initialize()}removeBucketTreeElement(e){this.removeChild(e),this.bucketTreeElements.delete(e),this.setExpandable(this.bucketTreeElements.size>0)}get itemURL(){return"storage-buckets-group://"}getBucketTreeElement(e,{bucket:{storageKey:t,name:i}}){for(const s of this.bucketTreeElements)if(s.model===e&&s.bucketInfo.bucket.storageKey===t&&s.bucketInfo.bucket.name===i)return s;return null}}class Di extends D{storageBucketInfo;bucketModel;view;constructor(e,t,i){const{bucket:s}=i,{origin:r}=o.StorageKeyManager.parseStorageKey(i.bucket.storageKey);super(e,`${s.name} - ${r}`,"storage-bucket"),this.bucketModel=t,this.storageBucketInfo=i;const a=d.Icon.create("database");this.setLeadingIcons([a])}initialize(){const{bucket:e}=this.bucketInfo,t=new rs(this.resourcesPanel,e);this.appendChild(t);const i=new Ht(this.resourcesPanel,e);this.appendChild(i),i.initialize()}get itemURL(){const{bucket:e}=this.bucketInfo;return`storage-buckets-group://${e.name}/${e.storageKey}`}get model(){return this.bucketModel}get bucketInfo(){return this.storageBucketInfo}set bucketInfo(e){this.storageBucketInfo=e,this.view&&this.view.getComponent().setStorageBucket(this.storageBucketInfo)}onselect(e){return super.onselect(e),this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new v.StorageMetadataView),this.view.getComponent().enableStorageBucketControls(this.model),this.view.getComponent().setStorageBucket(this.storageBucketInfo)),this.showView(this.view),!1}}var Fi=Object.freeze({__proto__:null,i18nString:Ai,StorageBucketsTreeParentElement:Pi,StorageBucketsTreeElement:Di});const Vi=new CSSStyleSheet;Vi.replaceSync(".report-row{display:flex;align-items:center;white-space:normal}.clear-storage-button .report-row{margin:0 0 0 17px;display:flex}.link{margin-left:10px;display:none}.report-row:hover .link{display:inline}.quota-override-editor-with-button{align-items:baseline;display:flex}.quota-override-notification-editor{border:solid 1px var(--sys-color-neutral-outline);border-radius:4px;display:flex;flex:auto;margin-right:4px;max-width:200px;min-width:50px;min-height:19px;padding-left:4px;&:focus{border-color:var(--sys-color-state-focus-ring)}&:hover:not(:focus){background-color:var(--sys-color-state-hover-on-subtle)}}.quota-override-error{padding-top:10px;color:var(--sys-color-error)}.usage-breakdown-row{min-width:fit-content}.clear-storage-container{overflow:auto}.clear-storage-header{min-width:400px}.report-content-box{overflow:initial}.include-third-party-cookies{flex:1;min-width:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-left:16px}\n/*# sourceURL=storageView.css */\n");const Oi={storageQuotaUsed:"{PH1} used out of {PH2} storage quota",storageQuotaUsedWithBytes:"{PH1} bytes used out of {PH2} bytes storage quota",storageWithCustomMarker:"{PH1} (custom)",storageTitle:"Storage",usage:"Usage",mb:"MB",learnMore:"Learn more",clearSiteData:"Clear site data",SiteDataCleared:"Site data cleared",application:"Application",unregisterServiceWorker:"Unregister service workers",localAndSessionStorage:"Local and session storage",indexDB:"IndexedDB",webSql:"Web SQL",cookies:"Cookies",cacheStorage:"Cache storage",includingThirdPartyCookies:"including third-party cookies",sFailedToLoad:"{PH1} (failed to load)",internalError:"Internal error",pleaseEnterANumber:"Please enter a number",numberMustBeNonNegative:"Number must be non-negative",numberMustBeSmaller:"Number must be smaller than {PH1}",clearing:"Clearing...",storageQuotaIsLimitedIn:"Storage quota is limited in Incognito mode",fileSystem:"File System",other:"Other",storageUsage:"Storage usage",serviceWorkers:"Service workers",simulateCustomStorage:"Simulate custom storage quota"},Wi=i.i18n.registerUIStrings("panels/application/StorageView.ts",Oi),Ui=i.i18n.getLocalizedString.bind(void 0,Wi);class Ni extends h.ThrottledWidget.ThrottledWidget{pieColors;reportView;target;securityOrigin;storageKey;settings;includeThirdPartyCookiesSetting;quotaRow;quotaUsage;pieChart;previousOverrideFieldValue;quotaOverrideCheckbox;quotaOverrideControlRow;quotaOverrideEditor;quotaOverrideErrorMessage;clearButton;constructor(){super(!0,1e3),this.contentElement.classList.add("clear-storage-container"),this.contentElement.setAttribute("jslog",`${m.pane("clear-storage")}`),this.pieColors=new Map([["appcache","rgb(110, 161, 226)"],["cache_storage","rgb(229, 113, 113)"],["cookies","rgb(239, 196, 87)"],["indexeddb","rgb(155, 127, 230)"],["local_storage","rgb(116, 178, 102)"],["service_workers","rgb(255, 167, 36)"],["websql","rgb(203, 220, 56)"]]),this.reportView=new h.ReportView.ReportView(Ui(Oi.storageTitle)),this.reportView.element.classList.add("clear-storage-header"),this.reportView.show(this.contentElement),this.target=null,this.securityOrigin=null,this.storageKey=null,this.settings=new Map;for(const t of ji)this.settings.set(t,e.Settings.Settings.instance().createSetting("clear-storage-"+s.StringUtilities.toKebabCase(t),!0));this.includeThirdPartyCookiesSetting=e.Settings.Settings.instance().createSetting("clear-storage-include-third-party-cookies",!1);const t=this.reportView.appendSection(Ui(Oi.usage));t.element.setAttribute("jslog",`${m.section("usage")}`),this.quotaRow=t.appendSelectableRow(),this.quotaRow.classList.add("quota-usage-row");const i=t.appendRow(),r=h.XLink.XLink.create("https://developer.chrome.com/docs/devtools/progressive-web-apps#opaque-responses",Ui(Oi.learnMore),void 0,void 0,"learn-more");i.appendChild(r),this.quotaUsage=null,this.pieChart=new B.PieChart.PieChart,this.populatePieChart(0,[]);const a=t.appendRow();a.classList.add("usage-breakdown-row"),a.appendChild(this.pieChart),this.previousOverrideFieldValue="";const n=t.appendRow();n.classList.add("quota-override-row"),this.quotaOverrideCheckbox=h.UIUtils.CheckboxLabel.create(Ui(Oi.simulateCustomStorage),!1,""),this.quotaOverrideCheckbox.setAttribute("jslog",`${m.toggle("simulate-custom-quota").track({change:!0})}`),n.appendChild(this.quotaOverrideCheckbox),this.quotaOverrideCheckbox.checkboxElement.addEventListener("click",this.onClickCheckbox.bind(this),!1),this.quotaOverrideControlRow=t.appendRow(),this.quotaOverrideEditor=this.quotaOverrideControlRow.createChild("input","quota-override-notification-editor"),this.quotaOverrideEditor.setAttribute("jslog",`${m.textField("quota-override").track({keydown:!0})}`),this.quotaOverrideControlRow.appendChild(h.UIUtils.createLabel(Ui(Oi.mb))),this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideEditor.addEventListener("keyup",(e=>{"Enter"===e.key&&(this.applyQuotaOverrideFromInputField(),e.consume(!0))})),this.quotaOverrideEditor.addEventListener("focusout",(e=>{this.applyQuotaOverrideFromInputField(),e.consume(!0)}));const d=t.appendRow();this.quotaOverrideErrorMessage=d.createChild("div","quota-override-error");const l=this.reportView.appendSection("","clear-storage-button").appendRow();this.clearButton=h.UIUtils.createTextButton(Ui(Oi.clearSiteData),this.clear.bind(this),{jslogContext:"storage.clear-site-data"}),this.clearButton.id="storage-view-clear-button",l.appendChild(this.clearButton);const c=h.SettingsUI.createSettingCheckbox(Ui(Oi.includingThirdPartyCookies),this.includeThirdPartyCookiesSetting,!0);c.classList.add("include-third-party-cookies"),l.appendChild(c);const g=this.reportView.appendSection(Ui(Oi.application));g.element.setAttribute("jslog",`${m.section("application")}`),this.appendItem(g,Ui(Oi.unregisterServiceWorker),"service_workers"),g.markFieldListAsGroup();const u=this.reportView.appendSection(Ui(Oi.storageTitle));u.element.setAttribute("jslog",`${m.section("storage")}`),this.appendItem(u,Ui(Oi.localAndSessionStorage),"local_storage"),this.appendItem(u,Ui(Oi.indexDB),"indexeddb"),this.appendItem(u,Ui(Oi.webSql),"websql"),this.appendItem(u,Ui(Oi.cookies),"cookies"),this.appendItem(u,Ui(Oi.cacheStorage),"cache_storage"),u.markFieldListAsGroup(),o.TargetManager.TargetManager.instance().observeTargets(this)}appendItem(e,t,i){const s=e.appendRow(),r=this.settings.get(i);r&&s.appendChild(h.SettingsUI.createSettingCheckbox(t,r,!0))}targetAdded(e){if(e!==o.TargetManager.TargetManager.instance().primaryPageTarget())return;this.target=e;const t=e.model(o.SecurityOriginManager.SecurityOriginManager);this.updateOrigin(t.mainSecurityOrigin(),t.unreachableMainSecurityOrigin()),t.addEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,this.originChanged,this);const i=e.model(o.StorageKeyManager.StorageKeyManager);this.updateStorageKey(i.mainStorageKey()),i.addEventListener("MainStorageKeyChanged",this.storageKeyChanged,this)}targetRemoved(e){if(this.target!==e)return;e.model(o.SecurityOriginManager.SecurityOriginManager).removeEventListener(o.SecurityOriginManager.Events.MainSecurityOriginChanged,this.originChanged,this);e.model(o.StorageKeyManager.StorageKeyManager).removeEventListener("MainStorageKeyChanged",this.storageKeyChanged,this)}originChanged(e){const{mainSecurityOrigin:t,unreachableMainSecurityOrigin:i}=e.data;this.updateOrigin(t,i)}storageKeyChanged(e){const{mainStorageKey:t}=e.data;this.updateStorageKey(t)}updateOrigin(e,t){const i=this.securityOrigin;t?(this.securityOrigin=t,this.reportView.setSubtitle(Ui(Oi.sFailedToLoad,{PH1:t}))):(this.securityOrigin=e,this.reportView.setSubtitle(e)),i!==this.securityOrigin&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,this.quotaOverrideErrorMessage.textContent=""),this.doUpdate()}updateStorageKey(e){const t=this.storageKey;this.storageKey=e,this.reportView.setSubtitle(e),t!==this.storageKey&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,this.quotaOverrideErrorMessage.textContent=""),this.doUpdate()}async applyQuotaOverrideFromInputField(){if(!this.target||!this.securityOrigin)return void(this.quotaOverrideErrorMessage.textContent=Ui(Oi.internalError));this.quotaOverrideErrorMessage.textContent="";const e=this.quotaOverrideEditor.value;if(""===e)return await this.clearQuotaForOrigin(this.target,this.securityOrigin),void(this.previousOverrideFieldValue="");const t=parseFloat(e);if(!Number.isFinite(t))return void(this.quotaOverrideErrorMessage.textContent=Ui(Oi.pleaseEnterANumber));if(t<0)return void(this.quotaOverrideErrorMessage.textContent=Ui(Oi.numberMustBeNonNegative));const i=9e12;if(t>=i)return void(this.quotaOverrideErrorMessage.textContent=Ui(Oi.numberMustBeSmaller,{PH1:i.toLocaleString()}));const s=1e6,r=Math.round(t*s),a=""+r/s;this.quotaOverrideEditor.value=a,this.previousOverrideFieldValue=a,await this.target.storageAgent().invoke_overrideQuotaForOrigin({origin:this.securityOrigin,quotaSize:r})}async clearQuotaForOrigin(e,t){await e.storageAgent().invoke_overrideQuotaForOrigin({origin:t})}async onClickCheckbox(){this.quotaOverrideControlRow.classList.contains("hidden")?(this.quotaOverrideControlRow.classList.remove("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!0,this.quotaOverrideEditor.value=this.previousOverrideFieldValue,this.quotaOverrideEditor.focus()):this.target&&this.securityOrigin&&(this.quotaOverrideControlRow.classList.add("hidden"),this.quotaOverrideCheckbox.checkboxElement.checked=!1,await this.clearQuotaForOrigin(this.target,this.securityOrigin),this.quotaOverrideErrorMessage.textContent="")}clear(){if(!this.securityOrigin)return;const e=[];for(const t of this.settings.keys()){const i=this.settings.get(t);i&&i.get()&&e.push(t)}if(this.target){const t=this.includeThirdPartyCookiesSetting.get();Ni.clear(this.target,this.storageKey,this.securityOrigin,e,t)}this.clearButton.disabled=!0;const t=this.clearButton.textContent;this.clearButton.textContent=Ui(Oi.clearing),window.setTimeout((()=>{this.clearButton.disabled=!1,this.clearButton.textContent=t,this.clearButton.focus()}),500),h.ARIAUtils.alert(Ui(Oi.SiteDataCleared))}static clear(e,t,i,s,r){if(console.assert(Boolean(t)),!t)return;e.storageAgent().invoke_clearDataForStorageKey({storageKey:t,storageTypes:s.join(",")});const a=new Set(s),n=a.has("all");if(a.has("local_storage")||n){const i=e.model(de);i&&i.clearForStorageKey(t)}if(a.has("indexeddb")||n)for(const e of o.TargetManager.TargetManager.instance().targets()){const i=e.model(ge);i&&i.clearForStorageKey(t)}if(i&&(a.has("cookies")||n)){e.storageAgent().invoke_clearDataForOrigin({origin:i,storageTypes:"cookies"});const t=e.model(o.CookieModel.CookieModel);t&&t.clear(void 0,r?void 0:i)}if(a.has("cache_storage")||n){const e=o.TargetManager.TargetManager.instance().primaryPageTarget(),i=e&&e.model(o.ServiceWorkerCacheModel.ServiceWorkerCacheModel);i&&i.clearForStorageKey(t)}}async doUpdate(){if(!this.securityOrigin||!this.target)return this.quotaRow.textContent="",void this.populatePieChart(0,[]);const e=this.securityOrigin,t=await this.target.storageAgent().invoke_getUsageAndQuota({origin:e});if(this.quotaRow.textContent="",t.getError())return void this.populatePieChart(0,[]);const r=t.overrideActive,a=s.NumberUtilities.bytesToString(t.quota),o=s.NumberUtilities.bytesToString(t.usage),n=Ui(Oi.storageWithCustomMarker,{PH1:a}),l=r?h.Fragment.Fragment.build`<b>${n}</b>`.element():a,c=i.i18n.getFormatLocalizedString(Wi,Oi.storageQuotaUsed,{PH1:o,PH2:l});if(this.quotaRow.appendChild(c),h.Tooltip.Tooltip.install(this.quotaRow,Ui(Oi.storageQuotaUsedWithBytes,{PH1:t.usage.toLocaleString(),PH2:t.quota.toLocaleString()})),!t.overrideActive&&t.quota<125829120){const e=new d.Icon.Icon;e.data={iconName:"info",color:"var(--icon-info)",width:"14px",height:"14px"},h.Tooltip.Tooltip.install(this.quotaRow,Ui(Oi.storageQuotaIsLimitedIn)),this.quotaRow.appendChild(e)}if(null===this.quotaUsage||this.quotaUsage!==t.usage){this.quotaUsage=t.usage;const e=[];for(const i of t.usageBreakdown.sort(((e,t)=>t.usage-e.usage))){const t=i.usage;if(!t)continue;const s=this.getStorageTypeName(i.storageType),r=this.pieColors.get(i.storageType)||"#ccc";e.push({value:t,color:r,title:s})}this.populatePieChart(t.usage,e)}this.update()}populatePieChart(e,t){this.pieChart.data={chartName:Ui(Oi.storageUsage),size:110,formatter:s.NumberUtilities.bytesToString,showLegend:!0,total:e,slices:t}}getStorageTypeName(e){switch(e){case"file_systems":return Ui(Oi.fileSystem);case"websql":return Ui(Oi.webSql);case"appcache":return Ui(Oi.application);case"indexeddb":return Ui(Oi.indexDB);case"cache_storage":return Ui(Oi.cacheStorage);case"service_workers":return Ui(Oi.serviceWorkers);default:return Ui(Oi.other)}}wasShown(){super.wasShown(),this.reportView.registerCSSFiles([Vi]),this.registerCSSFiles([Vi])}}const ji=["appcache","cache_storage","cookies","indexeddb","local_storage","service_workers","websql"];var Hi=Object.freeze({__proto__:null,StorageView:Ni,AllStorageTypes:ji,ActionDelegate:class{handleAction(e,t){switch(t){case"resources.clear":return this.handleClear(!1);case"resources.clear-incl-third-party-cookies":return this.handleClear(!0)}return!1}handleClear(e){const t=o.TargetManager.TargetManager.instance().primaryPageTarget();if(!t)return!1;const i=t.model(o.ResourceTreeModel.ResourceTreeModel);if(!i)return!1;const s=i.getMainSecurityOrigin();return i.getMainStorageKey().then((i=>{Ni.clear(t,i,s,ji,e)}),(e=>{})),!0}}});const _i={trustTokens:"Private state tokens"},Gi=i.i18n.registerUIStrings("panels/application/TrustTokensTreeElement.ts",_i),Ki=i.i18n.getLocalizedString.bind(void 0,Gi);class zi extends P{view;constructor(e){super(e,Ki(_i.trustTokens),!1);const t=d.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"trustTokens://"}onselect(e){return super.onselect(e),this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new w.TrustTokensView.TrustTokensView,"trust-tokens")),this.showView(this.view),t.userMetrics.panelShown("trust-tokens"),!1}}var qi=Object.freeze({__proto__:null,i18nString:Ki,TrustTokensTreeElement:zi});const $i={application:"Application",storage:"Storage",localStorage:"Local storage",sessionStorage:"Session storage",cookies:"Cookies",backgroundServices:"Background services",frames:"Frames",manifest:"Manifest",noManifestDetected:"No manifest detected",appManifest:"App Manifest",indexeddb:"IndexedDB",refreshIndexeddb:"Refresh IndexedDB",versionSEmpty:"Version: {PH1} (empty)",versionS:"Version: {PH1}",clear:"Clear",keyPathS:"Key path: {PH1}",localFiles:"Local Files",cookiesUsedByFramesFromS:"Cookies used by frames from {PH1}",openedWindows:"Opened Windows",webWorkers:"Web Workers",documentNotAvailable:"Document not available",theContentOfThisDocumentHasBeen:"The content of this document has been generated dynamically via 'document.write()'.",windowWithoutTitle:"Window without title",worker:"worker",onInvokeManifestAlert:"Manifest: Invoke to scroll to the top of manifest",beforeInvokeAlert:"{PH1}: Invoke to scroll to this section in manifest",onInvokeAlert:"Scrolled to {PH1}",applicationSidebarPanel:"Application panel sidebar",thirdPartyPhaseout:"Cookies from {PH1} may have been blocked due to third-party cookie phaseout."},Ji=i.i18n.registerUIStrings("panels/application/ApplicationPanelSidebar.ts",$i),Qi=i.i18n.getLocalizedString.bind(void 0,Ji);function Xi(e){if("main"===e)throw new Error("Unexpected main target id")}class Yi extends h.Widget.VBox{panel;sidebarTree;applicationTreeElement;serviceWorkersTreeElement;localStorageListTreeElement;sessionStorageListTreeElement;indexedDBListTreeElement;interestGroupTreeElement;cookieListTreeElement;trustTokensTreeElement;cacheStorageListTreeElement;sharedStorageListTreeElement;storageBucketsTreeElement;backForwardCacheListTreeElement;backgroundFetchTreeElement;backgroundSyncTreeElement;bounceTrackingMitigationsTreeElement;notificationsTreeElement;paymentHandlerTreeElement;periodicBackgroundSyncTreeElement;pushMessagingTreeElement;reportingApiTreeElement;preloadingSummaryTreeElement;resourcesSection;domStorageTreeElements;sharedStorageTreeElements;domains;target;previousHoveredElement;sharedStorageTreeElementDispatcher;constructor(t){super(),this.panel=t,this.sidebarTree=new h.TreeOutline.TreeOutlineInShadow,this.sidebarTree.element.classList.add("resources-sidebar"),this.sidebarTree.element.classList.add("filter-all"),this.sidebarTree.addEventListener(h.TreeOutline.Events.ElementAttached,this.treeElementAdded,this),this.contentElement.appendChild(this.sidebarTree.element);const i=Qi($i.application);this.applicationTreeElement=this.addSidebarSection(i);const s=this.applicationTreeElement.treeOutline?.contentElement;s&&(s.ariaLabel=Qi($i.applicationSidebarPanel));const r=new ts(t);this.applicationTreeElement.appendChild(r),r.generateChildren(),this.serviceWorkersTreeElement=new es(t),this.applicationTreeElement.appendChild(this.serviceWorkersTreeElement);const n=new ss(t);this.applicationTreeElement.appendChild(n);const l=Qi($i.storage),c=this.addSidebarSection(l);this.localStorageListTreeElement=new D(t,Qi($i.localStorage),"local-storage"),this.localStorageListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/localstorage/?utm_source=devtools");const g=d.Icon.create("table");this.localStorageListTreeElement.setLeadingIcons([g]),c.appendChild(this.localStorageListTreeElement),this.sessionStorageListTreeElement=new D(t,Qi($i.sessionStorage),"session-storage"),this.sessionStorageListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/sessionstorage/?utm_source=devtools");const u=d.Icon.create("table");this.sessionStorageListTreeElement.setLeadingIcons([u]),c.appendChild(this.sessionStorageListTreeElement),this.indexedDBListTreeElement=new rs(t),this.indexedDBListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/indexeddb/?utm_source=devtools"),c.appendChild(this.indexedDBListTreeElement),this.cookieListTreeElement=new D(t,Qi($i.cookies),"cookies"),this.cookieListTreeElement.setLink("https://developer.chrome.com/docs/devtools/storage/cookies/?utm_source=devtools");const p=d.Icon.create("cookie");this.cookieListTreeElement.setLeadingIcons([p]),c.appendChild(this.cookieListTreeElement),this.trustTokensTreeElement=new zi(t),c.appendChild(this.trustTokensTreeElement),this.interestGroupTreeElement=new Ue(t),c.appendChild(this.interestGroupTreeElement),this.sharedStorageListTreeElement=new ui(t),c.appendChild(this.sharedStorageListTreeElement),this.cacheStorageListTreeElement=new Ht(t),c.appendChild(this.cacheStorageListTreeElement),a.Runtime.experiments.isEnabled("storage-buckets-tree")&&(this.storageBucketsTreeElement=new Pi(t),c.appendChild(this.storageBucketsTreeElement));const m=Qi($i.backgroundServices),w=this.addSidebarSection(m);this.backForwardCacheListTreeElement=new G(t),w.appendChild(this.backForwardCacheListTreeElement),this.backgroundFetchTreeElement=new Zi(t,"backgroundFetch"),w.appendChild(this.backgroundFetchTreeElement),this.backgroundSyncTreeElement=new Zi(t,"backgroundSync"),w.appendChild(this.backgroundSyncTreeElement),this.bounceTrackingMitigationsTreeElement=new ae(t),w.appendChild(this.bounceTrackingMitigationsTreeElement),this.notificationsTreeElement=new Zi(t,"notifications"),w.appendChild(this.notificationsTreeElement),this.paymentHandlerTreeElement=new Zi(t,"paymentHandler"),w.appendChild(this.paymentHandlerTreeElement),this.periodicBackgroundSyncTreeElement=new Zi(t,"periodicBackgroundSync"),w.appendChild(this.periodicBackgroundSyncTreeElement),a.Runtime.experiments.isEnabled("preloading-status-panel")&&(this.preloadingSummaryTreeElement=new ut(t),w.appendChild(this.preloadingSummaryTreeElement),this.preloadingSummaryTreeElement.constructChildren(t)),this.pushMessagingTreeElement=new Zi(t,"pushMessaging"),w.appendChild(this.pushMessagingTreeElement),this.reportingApiTreeElement=new Mt(t),w.appendChild(this.reportingApiTreeElement);const v=Qi($i.frames),S=this.addSidebarSection(v);this.resourcesSection=new hs(t,S),this.domStorageTreeElements=new Map,this.sharedStorageTreeElements=new Map,this.domains={},this.sidebarTree.contentElement.addEventListener("mousemove",this.onmousemove.bind(this),!1),this.sidebarTree.contentElement.addEventListener("mouseleave",this.onmouseleave.bind(this),!1),o.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ResourceTreeModel.ResourceTreeModel,o.ResourceTreeModel.Events.FrameNavigated,this.frameNavigated,this,{scoped:!0});this.panel.lastSelectedItemPath().length||r.select(),o.TargetManager.TargetManager.instance().observeModels(de,{modelAdded:e=>this.domStorageModelAdded(e),modelRemoved:e=>this.domStorageModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(ge,{modelAdded:e=>this.indexedDBModelAdded(e),modelRemoved:e=>this.indexedDBModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(Me,{modelAdded:e=>this.interestGroupModelAdded(e),modelRemoved:e=>this.interestGroupModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(wi,{modelAdded:e=>this.sharedStorageModelAdded(e).catch((e=>{console.error(e)})),modelRemoved:e=>this.sharedStorageModelRemoved(e)},{scoped:!0}),o.TargetManager.TargetManager.instance().observeModels(o.StorageBucketsModel.StorageBucketsModel,{modelAdded:e=>this.storageBucketsModelAdded(e),modelRemoved:e=>this.storageBucketsModelRemoved(e)},{scoped:!0}),this.sharedStorageTreeElementDispatcher=new e.ObjectWrapper.ObjectWrapper,this.contentElement.style.contain="layout style"}addSidebarSection(e){const t=new h.TreeOutline.TreeElement(e,!0);return t.listItemElement.classList.add("storage-group-list-item"),t.setCollapsible(!1),t.selectable=!1,this.sidebarTree.appendChild(t),h.ARIAUtils.markAsHeading(t.listItemElement,3),h.ARIAUtils.setLabel(t.childrenListElement,e),t}targetAdded(e){if(e!==e.outermostTarget())return;this.target=e;const t=e.model(Me);t&&t.addEventListener("InterestGroupAccess",this.interestGroupAccess,this);const i=e.model(o.ResourceTreeModel.ResourceTreeModel);i&&(i.cachedResourcesLoaded()&&this.initialize(),i.addEventListener(o.ResourceTreeModel.Events.CachedResourcesLoaded,this.initialize,this),i.addEventListener(o.ResourceTreeModel.Events.WillLoadCachedResources,this.resetWithFrames,this))}targetRemoved(e){if(e!==this.target)return;delete this.target;const t=e.model(o.ResourceTreeModel.ResourceTreeModel);t&&(t.removeEventListener(o.ResourceTreeModel.Events.CachedResourcesLoaded,this.initialize,this),t.removeEventListener(o.ResourceTreeModel.Events.WillLoadCachedResources,this.resetWithFrames,this));const i=e.model(Me);i&&i.removeEventListener("InterestGroupAccess",this.interestGroupAccess,this),this.resetWithFrames()}focus(){this.sidebarTree.focus()}initialize(){for(const e of o.ResourceTreeModel.ResourceTreeModel.frames())this.addCookieDocument(e);const e=this.target&&this.target.model(Me);e&&e.enable(),this.cacheStorageListTreeElement.initialize();const t=this.target&&this.target.model(K)||null;if(this.backgroundFetchTreeElement&&this.backgroundFetchTreeElement.initialize(t),this.backgroundSyncTreeElement&&this.backgroundSyncTreeElement.initialize(t),this.notificationsTreeElement.initialize(t),this.paymentHandlerTreeElement.initialize(t),this.periodicBackgroundSyncTreeElement.initialize(t),this.pushMessagingTreeElement.initialize(t),this.storageBucketsTreeElement?.initialize(),a.Runtime.experiments.isEnabled("preloading-status-panel")){const e=this.target?.model(o.PreloadingModel.PreloadingModel);e&&this.preloadingSummaryTreeElement?.initialize(e)}}domStorageModelAdded(e){e.enable(),e.storages().forEach(this.addDOMStorage.bind(this)),e.addEventListener("DOMStorageAdded",this.domStorageAdded,this),e.addEventListener("DOMStorageRemoved",this.domStorageRemoved,this)}domStorageModelRemoved(e){e.storages().forEach(this.removeDOMStorage.bind(this)),e.removeEventListener("DOMStorageAdded",this.domStorageAdded,this),e.removeEventListener("DOMStorageRemoved",this.domStorageRemoved,this)}indexedDBModelAdded(e){e.enable(),this.indexedDBListTreeElement.addIndexedDBForModel(e)}indexedDBModelRemoved(e){this.indexedDBListTreeElement.removeIndexedDBForModel(e)}interestGroupModelAdded(e){e.enable(),e.addEventListener("InterestGroupAccess",this.interestGroupAccess,this)}interestGroupModelRemoved(e){e.disable(),e.removeEventListener("InterestGroupAccess",this.interestGroupAccess,this)}async sharedStorageModelAdded(e){await e.enable();for(const t of e.storages())await this.addSharedStorage(t);e.addEventListener("SharedStorageAdded",this.sharedStorageAdded,this),e.addEventListener("SharedStorageRemoved",this.sharedStorageRemoved,this),e.addEventListener("SharedStorageAccess",this.sharedStorageAccess,this)}sharedStorageModelRemoved(e){e.disable();for(const t of e.storages())this.removeSharedStorage(t);e.removeEventListener("SharedStorageAdded",this.sharedStorageAdded,this),e.removeEventListener("SharedStorageRemoved",this.sharedStorageRemoved,this),e.removeEventListener("SharedStorageAccess",this.sharedStorageAccess,this)}storageBucketsModelAdded(e){e.enable()}storageBucketsModelRemoved(e){this.storageBucketsTreeElement?.removeBucketsForModel(e)}resetWithFrames(){this.resourcesSection.reset(),this.reset()}treeElementAdded(e){const t=this.panel.lastSelectedItemPath();if(!t.length)return;const i=e.data,s=[i];for(let e=i.parent;e&&"itemURL"in e&&e.itemURL;e=e.parent)s.push(e);let r=t.length-1,a=s.length-1;for(;r>=0&&a>=0&&t[r]===s[a].itemURL;)s[a].expanded||(r>0&&s[a].expand(),s[a].selected||s[a].select()),r--,a--}reset(){this.domains={},this.cookieListTreeElement.removeChildren(),this.interestGroupTreeElement.clearEvents()}frameNavigated(e){const t=e.data;t.isOutermostFrame()&&this.reset(),this.addCookieDocument(t)}interestGroupAccess(e){this.interestGroupTreeElement.addEvent(e.data)}addCookieDocument(t){const i=t.unreachableUrl()||t.url,s=e.ParsedURL.ParsedURL.fromString(i);if(!s||"http"!==s.scheme&&"https"!==s.scheme&&"file"!==s.scheme)return;const r=s.securityOrigin();if(!this.domains[r]){this.domains[r]=!0;const e=new ls(this.panel,t,s);this.cookieListTreeElement.appendChild(e)}}domStorageAdded(e){const t=e.data;this.addDOMStorage(t)}addDOMStorage(e){console.assert(!this.domStorageTreeElements.get(e)),console.assert(Boolean(e.storageKey));const t=new ds(this.panel,e);function i(e,t){const i=e.titleAsText().toLocaleLowerCase(),s=t.titleAsText().toLocaleUpperCase();return i.localeCompare(s)}this.domStorageTreeElements.set(e,t),e.isLocalStorage?this.localStorageListTreeElement.appendChild(t,i):this.sessionStorageListTreeElement.appendChild(t,i)}domStorageRemoved(e){const t=e.data;this.removeDOMStorage(t)}removeDOMStorage(e){const t=this.domStorageTreeElements.get(e);if(!t)return;const i=t.selected,s=t.parent;s&&(s.removeChild(t),i&&s.select()),this.domStorageTreeElements.delete(e)}async sharedStorageAdded(e){await this.addSharedStorage(e.data)}async addSharedStorage(e){const t=await Mi.createElement(this.panel,e);this.sharedStorageTreeElements.has(e.securityOrigin)||(this.sharedStorageTreeElements.set(e.securityOrigin,t),this.sharedStorageListTreeElement.appendChild(t),this.sharedStorageTreeElementDispatcher.dispatchEventToListeners("SharedStorageTreeElementAdded",{origin:e.securityOrigin}))}sharedStorageRemoved(e){this.removeSharedStorage(e.data)}removeSharedStorage(e){const t=this.sharedStorageTreeElements.get(e.securityOrigin);if(!t)return;const i=t.selected,s=t.parent;s&&(s.removeChild(t),s.setExpandable(s.childCount()>0),i&&s.select()),this.sharedStorageTreeElements.delete(e.securityOrigin)}sharedStorageAccess(e){this.sharedStorageListTreeElement.addEvent(e.data)}async showResource(e,t,i){await this.resourcesSection.revealResource(e,t,i)}showFrame(e){this.resourcesSection.revealAndSelectFrame(e)}showFileSystem(e){this.innerShowView(e)}innerShowView(e){this.panel.showView(e)}showPreloadingRuleSetView(e){this.preloadingSummaryTreeElement&&this.preloadingSummaryTreeElement.expandAndRevealRuleSet(e)}showPreloadingAttemptViewWithFilter(e){this.preloadingSummaryTreeElement&&this.preloadingSummaryTreeElement.expandAndRevealAttempts(e)}onmousemove(e){const t=e.target;if(!t)return;const i=h.UIUtils.enclosingNodeOrSelfWithNodeName(t,"li");if(!i)return;const s=h.TreeOutline.TreeElement.getTreeElementBylistItemNode(i);this.previousHoveredElement!==s&&(this.previousHoveredElement&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement),s instanceof gs&&(this.previousHoveredElement=s,s.hovered=!0))}onmouseleave(e){this.previousHoveredElement&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement)}wasShown(){super.wasShown(),this.sidebarTree.registerCSSFiles([Rt])}}class Zi extends P{serviceName;view;model;selectedInternal;constructor(e,t){super(e,Z.getUIString(t),!1),this.serviceName=t,this.selectedInternal=!1,this.view=null,this.model=null;const i=d.Icon.create(this.getIconType());this.setLeadingIcons([i])}getIconType(){switch(this.serviceName){case"backgroundFetch":return"arrow-up-down";case"backgroundSync":return"sync";case"pushMessaging":return"cloud";case"notifications":return"bell";case"paymentHandler":return"credit-card";case"periodicBackgroundSync":return"watch";default:return console.error(`Service ${this.serviceName} does not have a dedicated icon`),"table"}}initialize(e){this.model=e,this.selectedInternal&&!this.view&&this.onselect(!1)}get itemURL(){return`background-service://${this.serviceName}`}get selectable(){return!!this.model&&super.selectable}onselect(e){return super.onselect(e),this.selectedInternal=!0,!!this.model&&(this.view||(this.view=new Z(this.serviceName,this.model)),this.showView(this.view),h.Context.Context.instance().setFlavor(Z,this.view),t.userMetrics.panelShown("background_service_"+this.serviceName),!1)}}class es extends P{view;constructor(e){super(e,i.i18n.lockedString("Service workers"),!1);const t=d.Icon.create("gears");this.setLeadingIcons([t])}get itemURL(){return"service-workers://"}onselect(e){return super.onselect(e),this.view||(this.view=new ti),this.showView(this.view),t.userMetrics.panelShown("service-workers"),!1}}class ts extends P{view;constructor(t){super(t,Qi($i.manifest),!0);const i=d.Icon.create("document");this.setLeadingIcons([i]),self.onInvokeElement(this.listItemElement,this.onInvoke.bind(this));const s=new h.EmptyWidget.EmptyWidget(Qi($i.noManifestDetected)),r=new h.ReportView.ReportView(Qi($i.appManifest));this.view=new U(s,r,new e.Throttler.Throttler(1e3)),h.ARIAUtils.setLabel(this.listItemElement,Qi($i.onInvokeManifestAlert));const a=e=>{this.setExpandable(e)};this.view.addEventListener("ManifestDetected",(e=>a(e.data)))}get itemURL(){return"manifest://"}onselect(e){return super.onselect(e),this.showView(this.view),t.userMetrics.panelShown("app-manifest"),!1}generateChildren(){const e=this.view.getStaticSections();for(const t of e){const e=t.getTitleElement(),i=t.title(),s=t.getFieldElement(),r=new is(this.resourcesPanel,e,i,s);this.appendChild(r)}}onInvoke(){this.view.getManifestElement().scrollIntoView(),h.ARIAUtils.alert(Qi($i.onInvokeAlert,{PH1:this.listItemElement.title}))}showManifestView(){this.showView(this.view)}}class is extends P{#P;#D;constructor(e,t,i,s){super(e,i,!1);const r=d.Icon.create("document");this.setLeadingIcons([r]),this.#P=t,this.#D=s,self.onInvokeElement(this.listItemElement,this.onInvoke.bind(this)),this.listItemElement.addEventListener("keydown",this.onInvokeElementKeydown.bind(this)),h.ARIAUtils.setLabel(this.listItemElement,Qi($i.beforeInvokeAlert,{PH1:this.listItemElement.title}))}get itemURL(){return"manifest://"+this.title}onInvoke(){this.parent?.showManifestView(),this.#P.scrollIntoView(),h.ARIAUtils.alert(Qi($i.onInvokeAlert,{PH1:this.listItemElement.title})),t.userMetrics.manifestSectionSelected(this.listItemElement.title)}onInvokeElementKeydown(e){if("Tab"!==e.key||e.shiftKey)return;const t=this.#D.querySelector(".mask-checkbox");let i=this.#D.querySelector('[tabindex="0"]');t&&t.shadowRoot?i=t.shadowRoot.querySelector("input")||null:i||(i=this.#D.querySelector("devtools-protocol-handlers-view")?.shadowRoot?.querySelector('[tabindex="0"]')||null),i&&(i?.focus(),e.consume(!0))}}class ss extends P{view;constructor(e){super(e,Qi($i.storage),!1);const t=d.Icon.create("database");this.setLeadingIcons([t])}get itemURL(){return"clear-storage://"}onselect(e){return super.onselect(e),this.view||(this.view=new Ni),this.showView(this.view),t.userMetrics.panelShown(t.UserMetrics.PanelCodes[t.UserMetrics.PanelCodes.storage]),!1}}class rs extends D{idbDatabaseTreeElements;storageBucket;constructor(e,t){super(e,Qi($i.indexeddb),"indexed-db");const i=d.Icon.create("database");this.setLeadingIcons([i]),this.idbDatabaseTreeElements=[],this.storageBucket=t,this.initialize()}initialize(){o.TargetManager.TargetManager.instance().addModelListener(ge,ue.DatabaseAdded,this.indexedDBAdded,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(ge,ue.DatabaseRemoved,this.indexedDBRemoved,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(ge,ue.DatabaseLoaded,this.indexedDBLoaded,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(ge,ue.IndexedDBContentUpdated,this.indexedDBContentUpdated,this,{scoped:!0}),this.idbDatabaseTreeElements=[];for(const e of o.TargetManager.TargetManager.instance().models(ge,{scoped:!0})){const t=e.databases();for(let i=0;i<t.length;++i)this.addIndexedDB(e,t[i])}}addIndexedDBForModel(e){for(const t of e.databases())this.addIndexedDB(e,t)}removeIndexedDBForModel(e){const t=this.idbDatabaseTreeElements.filter((t=>t.model===e));for(const e of t)this.removeIDBDatabaseTreeElement(e)}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Qi($i.refreshIndexeddb),this.refreshIndexedDB.bind(this),{jslogContext:"refresh-indexeddb"}),t.show()}refreshIndexedDB(){for(const e of o.TargetManager.TargetManager.instance().models(ge,{scoped:!0}))e.refreshDatabaseNames()}databaseInTree(e){return!this.storageBucket||e.inBucket(this.storageBucket)}indexedDBAdded({data:{databaseId:e,model:t}}){this.addIndexedDB(t,e)}addIndexedDB(e,t){if(!this.databaseInTree(t))return;const i=new as(this.resourcesPanel,e,t);this.idbDatabaseTreeElements.push(i),this.appendChild(i),e.refreshDatabase(t)}indexedDBRemoved({data:{databaseId:e,model:t}}){const i=this.idbDatabaseTreeElement(t,e);i&&this.removeIDBDatabaseTreeElement(i)}removeIDBDatabaseTreeElement(e){e.clear(),this.removeChild(e),s.ArrayUtilities.removeElement(this.idbDatabaseTreeElements,e),this.setExpandable(this.childCount()>0)}indexedDBLoaded({data:{database:e,model:t,entriesUpdated:i}}){const s=this.idbDatabaseTreeElement(t,e.databaseId);s&&(s.update(e,i),this.indexedDBLoadedForTest())}indexedDBLoadedForTest(){}indexedDBContentUpdated({data:{databaseId:e,objectStoreName:t,model:i}}){const s=this.idbDatabaseTreeElement(i,e);s&&s.indexedDBContentUpdated(t)}idbDatabaseTreeElement(e,t){return this.idbDatabaseTreeElements.find((i=>i.databaseId.equals(t)&&i.model===e))||null}}class as extends P{model;databaseId;idbObjectStoreTreeElements;database;view;constructor(e,t,i){super(e,i.name,!1),this.model=t,this.databaseId=i,this.idbObjectStoreTreeElements=new Map;const s=d.Icon.create("database");this.setLeadingIcons([s]),this.model.addEventListener(ue.DatabaseNamesRefreshed,this.refreshIndexedDB,this)}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Qi($i.refreshIndexeddb),this.refreshIndexedDB.bind(this),{jslogContext:"refresh-indexeddb"}),t.show()}refreshIndexedDB(){this.model.refreshDatabase(this.databaseId)}indexedDBContentUpdated(e){const t=this.idbObjectStoreTreeElements.get(e);t&&t.markNeedsRefresh()}update(e,t){this.database=e;const i=new Set;for(const e of[...this.database.objectStores.keys()].sort()){const s=this.database.objectStores.get(e);if(!s)continue;i.add(s.name);let r=this.idbObjectStoreTreeElements.get(s.name);r||(r=new os(this.resourcesPanel,this.model,this.databaseId,s),this.idbObjectStoreTreeElements.set(s.name,r),this.appendChild(r)),r.update(s,t)}for(const e of this.idbObjectStoreTreeElements.keys())i.has(e)||this.objectStoreRemoved(e);this.view&&this.view.getComponent().update(e),this.updateTooltip()}updateTooltip(){const e=this.database?this.database.version:"-";0===Object.keys(this.idbObjectStoreTreeElements).length?this.tooltip=Qi($i.versionSEmpty,{PH1:e}):this.tooltip=Qi($i.versionS,{PH1:e})}get selectable(){return!!this.database&&super.selectable}onselect(e){return super.onselect(e),!!this.database&&(this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.VBox,new Te(this.model,this.database),"indexeddb-data")),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1)}objectStoreRemoved(e){const t=this.idbObjectStoreTreeElements.get(e);t&&(t.clear(),this.removeChild(t)),this.idbObjectStoreTreeElements.delete(e),this.updateTooltip()}clear(){for(const e of this.idbObjectStoreTreeElements.keys())this.objectStoreRemoved(e)}}class os extends P{model;databaseId;idbIndexTreeElements;objectStore;view;constructor(e,t,i,s){super(e,s.name,!1),this.model=t,this.databaseId=i,this.idbIndexTreeElements=new Map,this.objectStore=s,this.view=null;const r=d.Icon.create("table");this.setLeadingIcons([r])}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name+"/"+this.objectStore.name}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}markNeedsRefresh(){this.view&&this.view.markNeedsRefresh();for(const e of this.idbIndexTreeElements.values())e.markNeedsRefresh()}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Qi($i.clear),this.clearObjectStore.bind(this),{jslogContext:"clear"}),t.show()}refreshObjectStore(){this.view&&this.view.refreshData();for(const e of this.idbIndexTreeElements.values())e.refreshIndex()}async clearObjectStore(){await this.model.clearObjectStore(this.databaseId,this.objectStore.name),this.update(this.objectStore,!0)}update(e,t){this.objectStore=e;const i=new Set;for(const e of this.objectStore.indexes.values()){i.add(e.name);let s=this.idbIndexTreeElements.get(e.name);s||(s=new ns(this.resourcesPanel,this.model,this.databaseId,this.objectStore,e,this.refreshObjectStore.bind(this)),this.idbIndexTreeElements.set(e.name,s),this.appendChild(s)),s.update(this.objectStore,e,t)}for(const e of this.idbIndexTreeElements.keys())i.has(e)||this.indexRemoved(e);for(const[e,t]of this.idbIndexTreeElements.entries())i.has(e)||(this.removeChild(t),this.idbIndexTreeElements.delete(e));this.childCount()&&this.expand(),this.view&&t&&this.view.update(this.objectStore,null),this.updateTooltip()}updateTooltip(){const e=this.objectStore.keyPathString;let t=null!==e?Qi($i.keyPathS,{PH1:e}):"";this.objectStore.autoIncrement&&(t+="\n"+i.i18n.lockedString("autoIncrement")),this.tooltip=t}onselect(e){return super.onselect(e),this.view||(this.view=new Ie(this.model,this.databaseId,this.objectStore,null,this.refreshObjectStore.bind(this))),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1}indexRemoved(e){const t=this.idbIndexTreeElements.get(e);t&&(t.clear(),this.removeChild(t)),this.idbIndexTreeElements.delete(e)}clear(){for(const e of this.idbIndexTreeElements.keys())this.indexRemoved(e);this.view&&this.view.clear()}}class ns extends P{model;databaseId;objectStore;index;refreshObjectStore;view;constructor(e,t,i,s,r,a){super(e,r.name,!1),this.model=t,this.databaseId=i,this.objectStore=s,this.index=r,this.refreshObjectStore=a}get itemURL(){return"indexedDB://"+this.databaseId.storageBucket.storageKey+"/"+(this.databaseId.storageBucket.name??"")+"/"+this.databaseId.name+"/"+this.objectStore.name+"/"+this.index.name}markNeedsRefresh(){this.view&&this.view.markNeedsRefresh()}refreshIndex(){this.view&&this.view.refreshData()}update(e,t,i){this.objectStore=e,this.index=t,this.view&&i&&this.view.update(this.objectStore,this.index),this.updateTooltip()}updateTooltip(){const e=[],t=this.index.keyPathString;e.push(Qi($i.keyPathS,{PH1:t})),this.index.unique&&e.push(i.i18n.lockedString("unique")),this.index.multiEntry&&e.push(i.i18n.lockedString("multiEntry")),this.tooltip=e.join("\n")}onselect(e){return super.onselect(e),this.view||(this.view=new Ie(this.model,this.databaseId,this.objectStore,this.index,this.refreshObjectStore)),this.showView(this.view),t.userMetrics.panelShown("indexed-db"),!1}clear(){this.view&&this.view.clear()}}class ds extends P{domStorage;constructor(e,t){super(e,t.storageKey?o.StorageKeyManager.parseStorageKey(t.storageKey).origin:Qi($i.localFiles),!1),this.domStorage=t;const i=d.Icon.create("table");this.setLeadingIcons([i])}get itemURL(){return"storage://"+this.domStorage.storageKey+"/"+(this.domStorage.isLocalStorage?"local":"session")}onselect(e){return super.onselect(e),t.userMetrics.panelShown("dom-storage"),this.resourcesPanel.showDOMStorage(this.domStorage),!1}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Qi($i.clear),(()=>this.domStorage.clear()),{jslogContext:"clear"}),t.show()}}class ls extends P{target;cookieDomainInternal;constructor(e,t,i){super(e,i.securityOrigin()||Qi($i.localFiles),!1),this.target=t.resourceTreeModel().target(),this.cookieDomainInternal=i.securityOrigin(),this.tooltip=Qi($i.cookiesUsedByFramesFromS,{PH1:this.cookieDomainInternal});const s=d.Icon.create("cookie");n.RelatedIssue.hasThirdPartyPhaseoutCookieIssueForDomain(i.domain())&&(s.name="warning-filled",s.classList.add("warn-icon"),this.tooltip=Qi($i.thirdPartyPhaseout,{PH1:this.cookieDomainInternal})),this.setLeadingIcons([s])}get itemURL(){return"cookies://"+this.cookieDomainInternal}cookieDomain(){return this.cookieDomainInternal}onattach(){super.onattach(),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.defaultSection().appendItem(Qi($i.clear),(()=>this.resourcesPanel.clearCookies(this.target,this.cookieDomainInternal)),{jslogContext:"clear"}),t.show()}onselect(e){return super.onselect(e),this.resourcesPanel.showCookies(this.target,this.cookieDomainInternal),t.userMetrics.panelShown(t.UserMetrics.PanelCodes[t.UserMetrics.PanelCodes.cookies]),!1}}class cs extends h.Widget.VBox{emptyWidget;linkElement;constructor(){super(),this.element.classList.add("storage-view"),this.emptyWidget=new h.EmptyWidget.EmptyWidget(""),this.linkElement=null,this.emptyWidget.show(this.element)}setText(e){this.emptyWidget.text=e}setLink(e){e&&!this.linkElement&&(this.linkElement=this.emptyWidget.appendLink(e)),!e&&this.linkElement&&this.linkElement.classList.add("hidden"),e&&this.linkElement&&(this.linkElement.setAttribute("href",e),this.linkElement.classList.remove("hidden"))}}class hs{panel;treeElement;treeElementForFrameId;treeElementForTargetId;constructor(e,t){this.panel=e,this.treeElement=t,h.ARIAUtils.setLabel(this.treeElement.listItemNode,"Resources Section"),this.treeElementForFrameId=new Map,this.treeElementForTargetId=new Map;const i=o.FrameManager.FrameManager.instance();i.addEventListener("FrameAddedToTarget",(e=>this.frameAdded(e.data.frame)),this),i.addEventListener("FrameRemoved",(e=>this.frameDetached(e.data.frameId)),this),i.addEventListener("FrameNavigated",(e=>this.frameNavigated(e.data.frame)),this),i.addEventListener("ResourceAdded",(e=>this.resourceAdded(e.data.resource)),this),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetCreated",this.windowOpened,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetInfoChanged",this.windowChanged,this,{scoped:!0}),o.TargetManager.TargetManager.instance().addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetDestroyed",this.windowDestroyed,this,{scoped:!0}),o.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0})}initialize(){const e=o.FrameManager.FrameManager.instance();for(const t of e.getAllFrames()){this.treeElementForFrameId.get(t.id)||this.addFrameAndParents(t);const e=t.resourceTreeModel().target().model(o.ChildTargetManager.ChildTargetManager);if(e)for(const t of e.targetInfos())this.windowOpened({data:t})}}targetAdded(e){e.type()!==o.Target.Type.Worker&&e.type()!==o.Target.Type.ServiceWorker||this.workerAdded(e),e.type()===o.Target.Type.Frame&&e===e.outermostTarget()&&this.initialize()}async workerAdded(e){const t=e.parentTarget();if(!t)return;const i=t.id(),s=this.treeElementForTargetId.get(i),r=e.id();Xi(r);const{targetInfo:a}=await t.targetAgent().invoke_getTargetInfo({targetId:r});s&&a&&s.workerCreated(a)}targetRemoved(e){}addFrameAndParents(e){const t=e.parentFrame();t&&!this.treeElementForFrameId.get(t.id)&&this.addFrameAndParents(t),this.frameAdded(e)}expandFrame(e){if(!e)return!1;let t=this.treeElementForFrameId.get(e.id);return!(!t&&!this.expandFrame(e.parentFrame()))&&(t=this.treeElementForFrameId.get(e.id),!!t&&(t.expand(),!0))}async revealResource(e,t,i){if(!this.expandFrame(e.frame()))return;const s=ps.forResource(e);s&&await s.revealResource(t,i)}revealAndSelectFrame(e){const t=this.treeElementForFrameId.get(e.id);t?.reveal(),t?.select()}frameAdded(e){if(!o.TargetManager.TargetManager.instance().isInScope(e.resourceTreeModel()))return;const t=e.parentFrame(),i=t?this.treeElementForFrameId.get(t.id):this.treeElement;if(!i)return;const s=this.treeElementForFrameId.get(e.id);s&&(this.treeElementForFrameId.delete(e.id),s.parent&&s.parent.removeChild(s));const r=new gs(this,e);this.treeElementForFrameId.set(e.id,r);const a=e.resourceTreeModel().target().id();this.treeElementForTargetId.get(a)||this.treeElementForTargetId.set(a,r),i.appendChild(r);for(const t of e.resources())this.resourceAdded(t)}frameDetached(e){const t=this.treeElementForFrameId.get(e);t&&(this.treeElementForFrameId.delete(e),t.parent&&t.parent.removeChild(t))}frameNavigated(e){if(!o.TargetManager.TargetManager.instance().isInScope(e.resourceTreeModel()))return;const t=this.treeElementForFrameId.get(e.id);t&&t.frameNavigated(e)}resourceAdded(e){const t=e.frame();if(!t)return;if(!o.TargetManager.TargetManager.instance().isInScope(t.resourceTreeModel()))return;const i=this.treeElementForFrameId.get(t.id);i&&i.appendResource(e)}windowOpened(e){const t=e.data;if(t.openerId&&"page"===t.type){const e=this.treeElementForFrameId.get(t.openerId);e&&(this.treeElementForTargetId.set(t.targetId,e),e.windowOpened(t))}}windowDestroyed(e){const t=e.data,i=this.treeElementForTargetId.get(t);i&&(i.windowDestroyed(t),this.treeElementForTargetId.delete(t))}windowChanged(e){const t=e.data;if(t.openerId&&"page"===t.type){const e=this.treeElementForFrameId.get(t.openerId);e&&e.windowChanged(t)}}reset(){this.treeElement.removeChildren(),this.treeElementForFrameId.clear(),this.treeElementForTargetId.clear()}}class gs extends P{section;frame;frameId;categoryElements;treeElementForResource;treeElementForWindow;treeElementForWorker;view;constructor(e,t){super(e.panel,"",!1),this.section=e,this.frame=t,this.frameId=t.id,this.categoryElements=new Map,this.treeElementForResource=new Map,this.treeElementForWindow=new Map,this.treeElementForWorker=new Map,this.frameNavigated(t),this.view=null}getIconTypeForFrame(e){return e.isOutermostFrame()?e.unreachableUrl()?"frame-crossed":"frame":e.unreachableUrl()?"iframe-crossed":"iframe"}async frameNavigated(e){const t=d.Icon.create(this.getIconTypeForFrame(e));if(e.unreachableUrl()&&t.classList.add("red-icon"),this.setLeadingIcons([t]),this.invalidateChildren(),this.frameId=e.id,this.title!==e.displayName()&&(this.title=e.displayName(),h.ARIAUtils.setLabel(this.listItemElement,this.title),this.parent)){const e=this.parent;e.removeChild(this),e.appendChild(this)}if(this.categoryElements.clear(),this.treeElementForResource.clear(),this.treeElementForWorker.clear(),this.selected?(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new w.FrameDetailsView.FrameDetailsReportView(this.frame)),this.showView(this.view)):this.view=null,e.isOutermostFrame()){const t=o.TargetManager.TargetManager.instance().targets();for(const i of t)if(i.type()===o.Target.Type.ServiceWorker&&o.TargetManager.TargetManager.instance().isInScope(i)){const t=i.id();Xi(t);const s=e.resourceTreeModel().target().targetAgent(),r=(await s.invoke_getTargetInfo({targetId:t})).targetInfo;this.workerCreated(r)}}}get itemURL(){return this.frame.isOutermostFrame()?"frame://":"frame://"+encodeURI(this.frame.url)}onselect(e){return super.onselect(e),this.view||(this.view=l.LegacyWrapper.legacyWrapper(h.Widget.Widget,new w.FrameDetailsView.FrameDetailsReportView(this.frame))),t.userMetrics.panelShown("frame-details"),this.showView(this.view),this.listItemElement.classList.remove("hovered"),o.OverlayModel.OverlayModel.hideDOMNodeHighlight(),!1}set hovered(e){e?(this.listItemElement.classList.add("hovered"),this.frame.highlight()):(this.listItemElement.classList.remove("hovered"),o.OverlayModel.OverlayModel.hideDOMNodeHighlight())}appendResource(t){const i=t.statusCode();if(i>=301&&i<=303)return;const s=t.resourceType(),r=s.name();let a=s===e.ResourceType.resourceTypes.Document?this:this.categoryElements.get(r);a||(a=new D(this.section.panel,t.resourceType().category().title(),r,"Frames"===r),this.categoryElements.set(s.name(),a),this.appendChild(a,gs.presentationOrderCompare));const o=new ps(this.section.panel,t);a.appendChild(o,gs.presentationOrderCompare),this.treeElementForResource.set(t.url,o)}windowOpened(e){const t="opened-windows";let i=this.categoryElements.get(t);if(i||(i=new D(this.section.panel,Qi($i.openedWindows),t),this.categoryElements.set(t,i),this.appendChild(i,gs.presentationOrderCompare)),!this.treeElementForWindow.get(e.targetId)){const t=new ms(this.section.panel,e);i.appendChild(t),this.treeElementForWindow.set(e.targetId,t)}}workerCreated(e){const t="service_worker"===e.type?"service-workers":"web-workers",s="service_worker"===e.type?i.i18n.lockedString("Service workers"):Qi($i.webWorkers);let r=this.categoryElements.get(t);if(r||(r=new D(this.section.panel,s,t),this.categoryElements.set(t,r),this.appendChild(r,gs.presentationOrderCompare)),!this.treeElementForWorker.get(e.targetId)){const t=new ws(this.section.panel,e);r.appendChild(t),this.treeElementForWorker.set(e.targetId,t)}}windowChanged(e){const t=this.treeElementForWindow.get(e.targetId);t&&(t.title!==e.title&&(t.title=e.title),t.update(e))}windowDestroyed(e){const t=this.treeElementForWindow.get(e);t&&t.windowClosed()}appendChild(e,t=gs.presentationOrderCompare){super.appendChild(e,t)}static presentationOrderCompare(e,t){function i(e){return e instanceof D?2:e instanceof gs?1:3}return i(e)-i(t)||e.titleAsText().localeCompare(t.titleAsText())}}const us=new WeakMap;class ps extends P{panel;resource;previewPromise;constructor(e,t){super(e,t.isGenerated?Qi($i.documentNotAvailable):t.displayName,!1),this.panel=e,this.resource=t,this.previewPromise=null,this.tooltip=t.url,us.set(this.resource,this);const i=d.Icon.create("document","navigator-file-tree-item");i.classList.add("navigator-"+t.resourceType().name()+"-tree-item"),this.setLeadingIcons([i])}static forResource(e){return us.get(e)}get itemURL(){return this.resource.url}preparePreview(){if(this.previewPromise)return this.previewPromise;const e=c.PreviewFactory.PreviewFactory.createPreview(this.resource,this.resource.mimeType);return this.previewPromise=e.then((e=>e||new h.EmptyWidget.EmptyWidget(this.resource.url))),this.previewPromise}onselect(e){return super.onselect(e),this.resource.isGenerated?this.panel.showCategoryView(Qi($i.theContentOfThisDocumentHasBeen),null):this.panel.scheduleShowView(this.preparePreview()),t.userMetrics.panelShown("frame-resource"),!1}ondblclick(e){return t.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.resource.url),!1}onattach(){super.onattach(),this.listItemElement.draggable=!0,this.listItemElement.addEventListener("dragstart",this.ondragstart.bind(this),!1),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0)}ondragstart(e){return!!e.dataTransfer&&(e.dataTransfer.setData("text/plain",this.resource.content||""),e.dataTransfer.effectAllowed="copy",!0)}handleContextMenuEvent(e){const t=new h.ContextMenu.ContextMenu(e);t.appendApplicableItems(this.resource),t.show()}async revealResource(e,t){this.revealAndSelect(!0);const i=await this.panel.scheduleShowView(this.preparePreview());i instanceof c.ResourceSourceFrame.ResourceSourceFrame&&"number"==typeof e&&i.revealPosition({lineNumber:e,columnNumber:t},!0)}}class ms extends P{targetInfo;isWindowClosed;view;constructor(e,t){super(e,t.title||Qi($i.windowWithoutTitle),!1),this.targetInfo=t,this.isWindowClosed=!1,this.view=null,this.updateIcon(t.canAccessOpener)}updateIcon(e){const t=e?"popup":"frame",i=d.Icon.create(t);this.setLeadingIcons([i])}update(e){e.canAccessOpener!==this.targetInfo.canAccessOpener&&this.updateIcon(e.canAccessOpener),this.targetInfo=e,this.view&&(this.view.setTargetInfo(e),this.view.update())}windowClosed(){this.listItemElement.classList.add("window-closed"),this.isWindowClosed=!0,this.view&&(this.view.setIsWindowClosed(!0),this.view.update())}onselect(e){return super.onselect(e),this.view?this.view.update():this.view=new ze(this.targetInfo,this.isWindowClosed),this.showView(this.view),t.userMetrics.panelShown("frame-window"),!1}get itemURL(){return this.targetInfo.url}}class ws extends P{targetInfo;view;constructor(e,t){super(e,t.title||t.url||Qi($i.worker),!1),this.targetInfo=t,this.view=null;const i=d.Icon.create("gears","navigator-file-tree-item");this.setLeadingIcons([i])}onselect(e){return super.onselect(e),this.view?this.view.update():this.view=new qe(this.targetInfo),this.showView(this.view),t.userMetrics.panelShown("frame-worker"),!1}get itemURL(){return this.targetInfo.url}}var vs=Object.freeze({__proto__:null,ApplicationPanelSidebar:Yi,BackgroundServiceTreeElement:Zi,ServiceWorkersTreeElement:es,AppManifestTreeElement:ts,ManifestChildTreeElement:is,ClearStorageTreeElement:ss,IndexedDBTreeElement:rs,IDBDatabaseTreeElement:as,IDBObjectStoreTreeElement:os,IDBIndexTreeElement:ns,DOMStorageTreeElement:ds,CookieTreeElement:ls,StorageCategoryView:cs,ResourcesSection:hs,FrameTreeElement:gs,FrameResourceTreeElement:ps});const Ss=new CSSStyleSheet;Ss.replaceSync(".cookie-preview-widget{padding:2px 6px}.cookie-preview-widget-header{font-weight:bold;user-select:none;white-space:nowrap;margin-bottom:4px;flex:0 0 18px;display:flex;align-items:center}.cookie-preview-widget-header-label{line-height:18px}.cookie-preview-widget-cookie-value{user-select:text;word-break:break-all;flex:1;overflow:auto}.cookie-preview-widget-toggle{margin-left:12px;font-weight:normal}\n/*# sourceURL=cookieItemsView.css */\n");const bs={showUrlDecoded:"Show URL-decoded",cookies:"Cookies",selectACookieToPreviewItsValue:"Select a cookie to preview its value",onlyShowCookiesWithAnIssue:"Only show cookies with an issue",onlyShowCookiesWhichHaveAn:"Only show cookies that have an associated issue",clearFilteredCookies:"Clear filtered cookies",clearAllCookies:"Clear all cookies",numberOfCookiesShownInTableS:"Number of cookies shown in table: {PH1}"},ks=i.i18n.registerUIStrings("panels/application/CookieItemsView.ts",bs),ys=i.i18n.getLocalizedString.bind(void 0,ks);class fs extends h.Widget.VBox{cookie;showDecodedSetting;toggle;value;constructor(){super(),this.setMinimumSize(230,45),this.cookie=null,this.showDecodedSetting=e.Settings.Settings.instance().createSetting("cookie-view-show-decoded",!1);const t=document.createElement("div");t.classList.add("cookie-preview-widget-header");const i=document.createElement("span");i.classList.add("cookie-preview-widget-header-label"),i.textContent="Cookie Value",t.appendChild(i),this.contentElement.appendChild(t);const s=h.UIUtils.CheckboxLabel.create(ys(bs.showUrlDecoded),this.showDecodedSetting.get(),void 0,"show-url-decoded");s.classList.add("cookie-preview-widget-toggle"),s.checkboxElement.addEventListener("click",(()=>this.showDecoded(!this.showDecodedSetting.get()))),t.appendChild(s),this.toggle=s;const r=document.createElement("div");r.classList.add("cookie-preview-widget-cookie-value"),r.textContent="",r.addEventListener("dblclick",this.handleDblClickOnCookieValue.bind(this)),this.value=r,this.contentElement.classList.add("cookie-preview-widget"),this.contentElement.setAttribute("jslog",`${m.section("cookie-preview")}`),this.contentElement.appendChild(r)}showDecoded(e){this.cookie&&(this.showDecodedSetting.set(e),this.toggle.checkboxElement.checked=e,this.updatePreview())}updatePreview(){this.cookie?this.value.textContent=this.showDecodedSetting.get()?decodeURIComponent(this.cookie.value()):this.cookie.value():this.value.textContent=""}setCookie(e){this.cookie=e,this.updatePreview()}handleDblClickOnCookieValue(e){e.preventDefault();const t=document.createRange();t.selectNode(this.value);const i=window.getSelection();i&&(i.removeAllRanges(),i.addRange(t))}}class Cs extends yi{model;cookieDomain;totalSize;cookiesTable;splitWidget;previewPanel;previewWidget;emptyWidget;onlyIssuesFilterUI;refreshThrottler;eventDescriptors;allCookies;shownCookies;selectedCookie;constructor(t,i){super(ys(bs.cookies),"cookiesPanel"),this.element.classList.add("storage-view"),this.element.setAttribute("jslog",`${m.pane("cookies-data")}`),this.model=t,this.cookieDomain=i,this.totalSize=0,this.cookiesTable=new A.CookiesTable.CookiesTable(!1,this.saveCookie.bind(this),this.refreshItems.bind(this),this.handleCookieSelected.bind(this),this.deleteCookie.bind(this)),this.cookiesTable.setMinimumSize(0,50),this.splitWidget=new h.SplitWidget.SplitWidget(!1,!0,"cookie-items-split-view-state"),this.splitWidget.show(this.element),this.previewPanel=new h.Widget.VBox,this.previewPanel.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`);const s=this.previewPanel.element.createChild("div","preview-panel-resizer");this.splitWidget.setMainWidget(this.cookiesTable),this.splitWidget.setSidebarWidget(this.previewPanel),this.splitWidget.installResizer(s),this.previewWidget=new fs,this.emptyWidget=new h.EmptyWidget.EmptyWidget(ys(bs.selectACookieToPreviewItsValue)),this.emptyWidget.show(this.previewPanel.contentElement),this.onlyIssuesFilterUI=new h.Toolbar.ToolbarCheckbox(ys(bs.onlyShowCookiesWithAnIssue),ys(bs.onlyShowCookiesWhichHaveAn),(()=>{this.updateWithCookies(this.allCookies)}),"only-show-cookies-with-issues"),this.appendToolbarItem(this.onlyIssuesFilterUI),this.refreshThrottler=new e.Throttler.Throttler(300),this.eventDescriptors=[],this.allCookies=[],this.shownCookies=[],this.selectedCookie=null,this.setCookiesDomain(t,i)}setCookiesDomain(t,i){this.model=t,this.cookieDomain=i,this.refreshItems(),e.EventTarget.removeEventListeners(this.eventDescriptors);const s=t.target().model(o.NetworkManager.NetworkManager);s&&(this.eventDescriptors=[s.addEventListener(o.NetworkManager.Events.ResponseReceived,this.onResponseReceived,this),s.addEventListener(o.NetworkManager.Events.LoadingFinished,this.onLoadingFinished,this)])}showPreview(e){e!==this.selectedCookie&&(this.selectedCookie=e,e?(this.emptyWidget.detach(),this.previewWidget.setCookie(e),this.previewWidget.show(this.previewPanel.contentElement)):(this.previewWidget.detach(),this.emptyWidget.show(this.previewPanel.contentElement)))}handleCookieSelected(){const e=this.cookiesTable.selectedCookie();this.setCanDeleteSelected(Boolean(e)),this.showPreview(e)}async saveCookie(e,t){return t&&e.key()!==t.key()&&await this.model.deleteCookie(t),this.model.saveCookie(e)}deleteCookie(e,t){this.model.deleteCookie(e).then(t)}updateWithCookies(t){this.allCookies=t,this.totalSize=t.reduce(((e,t)=>e+t.size()),0);const i=e.ParsedURL.ParsedURL.fromString(this.cookieDomain),s=i?i.host:"";this.cookiesTable.setCookieDomain(s),this.shownCookies=this.filter(t,(e=>`${e.name()} ${e.value()} ${e.domain()}`)),this.hasFilter()?(this.setDeleteAllTitle(ys(bs.clearFilteredCookies)),this.setDeleteAllGlyph("filter-clear")):(this.setDeleteAllTitle(ys(bs.clearAllCookies)),this.setDeleteAllGlyph("clear-list")),this.cookiesTable.setCookies(this.shownCookies,this.model.getCookieToBlockedReasonsMap()),h.ARIAUtils.alert(ys(bs.numberOfCookiesShownInTableS,{PH1:this.shownCookies.length})),this.setCanFilter(!0),this.setCanDeleteAll(this.shownCookies.length>0),this.setCanDeleteSelected(Boolean(this.cookiesTable.selectedCookie())),this.cookiesTable.selectedCookie()||this.showPreview(null)}filter(e,t){return super.filter(e,t).filter((e=>!this.onlyIssuesFilterUI.checked()||e instanceof o.Cookie.Cookie&&n.RelatedIssue.hasIssues(e)))}deleteAllItems(){this.showPreview(null),this.model.deleteCookies(this.shownCookies).then((()=>this.refreshItems()))}deleteSelectedItem(){const e=this.cookiesTable.selectedCookie();e&&(this.showPreview(null),this.model.deleteCookie(e).then((()=>this.refreshItems())))}refreshItems(){this.model.getCookiesForDomain(this.cookieDomain).then(this.updateWithCookies.bind(this))}refreshItemsThrottled(){this.refreshThrottler.schedule((()=>Promise.resolve(this.refreshItems())))}onResponseReceived(){this.refreshItemsThrottled()}onLoadingFinished(){this.refreshItemsThrottled()}wasShown(){super.wasShown(),this.registerCSSFiles([Ss])}}var Ts=Object.freeze({__proto__:null,CookieItemsView:Cs});const Is={domStorage:"DOM Storage",key:"Key",value:"Value",domStorageItems:"DOM Storage Items",domStorageItemsCleared:"DOM Storage Items cleared",selectAValueToPreview:"Select a value to preview",domStorageItemDeleted:"The storage item was deleted.",domStorageNumberEntries:"Number of entries shown in table: {PH1}"},Es=i.i18n.registerUIStrings("panels/application/DOMStorageItemsView.ts",Is),xs=i.i18n.getLocalizedString.bind(void 0,Es);class Ms extends yi{domStorage;dataGrid;splitWidget;previewPanel;preview;previewValue;eventListeners;constructor(e){super(xs(Is.domStorage),"domStoragePanel"),this.domStorage=e,e.storageKey&&this.setStorageKey(e.storageKey),this.element.classList.add("storage-view","table");const t=[{id:"key",title:xs(Is.key),sortable:!1,editable:!0,longText:!0,weight:50},{id:"value",title:xs(Is.value),sortable:!1,editable:!0,longText:!0,weight:50}];this.dataGrid=new b.DataGrid.DataGridImpl({displayName:xs(Is.domStorageItems),columns:t,editCallback:this.editingCallback.bind(this),deleteCallback:this.deleteCallback.bind(this),refreshCallback:this.refreshItems.bind(this)}),this.dataGrid.addEventListener("SelectedNode",(e=>{this.previewEntry(e.data)})),this.dataGrid.addEventListener("DeselectedNode",(()=>{this.previewEntry(null)})),this.dataGrid.setStriped(!0),this.dataGrid.setName("dom-storage-items-view"),this.splitWidget=new h.SplitWidget.SplitWidget(!1,!0,"dom-storage-split-view-state"),this.splitWidget.show(this.element),this.previewPanel=new h.Widget.VBox,this.previewPanel.setMinimumSize(0,50),this.previewPanel.element.setAttribute("jslog",`${m.pane("preview").track({resize:!0})}`);const i=this.previewPanel.element.createChild("div","preview-panel-resizer"),s=this.dataGrid.asWidget();s.setMinimumSize(0,50),this.splitWidget.setMainWidget(s),this.splitWidget.setSidebarWidget(this.previewPanel),this.splitWidget.installResizer(i),this.preview=null,this.previewValue=null,this.showPreview(null,null),this.eventListeners=[],this.setStorage(e)}setStorage(t){e.EventTarget.removeEventListeners(this.eventListeners),this.domStorage=t;const i=t.isLocalStorage?"local-storage-data":"session-storage-data";this.element.setAttribute("jslog",`${m.pane().context(i)}`),t.storageKey&&this.setStorageKey(t.storageKey),this.eventListeners=[this.domStorage.addEventListener("DOMStorageItemsCleared",this.domStorageItemsCleared,this),this.domStorage.addEventListener("DOMStorageItemRemoved",this.domStorageItemRemoved,this),this.domStorage.addEventListener("DOMStorageItemAdded",this.domStorageItemAdded,this),this.domStorage.addEventListener("DOMStorageItemUpdated",this.domStorageItemUpdated,this)],this.refreshItems()}domStorageItemsCleared(){this.isShowing()&&this.dataGrid&&(this.dataGrid.rootNode().removeChildren(),this.dataGrid.addCreationNode(!1),h.ARIAUtils.alert(xs(Is.domStorageItemsCleared)),this.setCanDeleteSelected(!1))}domStorageItemRemoved(e){if(!this.isShowing()||!this.dataGrid)return;const t=e.data,i=this.dataGrid.rootNode(),s=i.children;for(let e=0;e<s.length;++e){const r=s[e];if(r.data.key===t.key)return i.removeChild(r),void this.setCanDeleteSelected(s.length>1)}}domStorageItemAdded(e){if(!this.isShowing()||!this.dataGrid)return;const t=e.data,i=this.dataGrid.rootNode(),s=i.children;for(let e=0;e<s.length;++e)if(s[e].data.key===t.key)return;const r=new b.DataGrid.DataGridNode({key:t.key,value:t.value},!1);i.insertChild(r,s.length-1)}domStorageItemUpdated(e){if(!this.isShowing()||!this.dataGrid)return;const t=e.data,i=this.dataGrid.rootNode().children.find((e=>e.data.key===t.key));i&&(i.data.value!==t.value&&(i.data.value=t.value,i.refresh()),i.selected&&(this.previewValue!==t.value&&this.previewEntry(i),this.setCanDeleteSelected(!0)))}showDOMStorageItems(e){const t=this.dataGrid.rootNode();let i=null;for(const e of t.children)if(e.selected){i=e.data.key;break}t.removeChildren();let s=null;const r=this.filter(e,(e=>`${e[0]} ${e[1]}`));for(const e of r){const r=e[0],a=e[1],o=new b.DataGrid.DataGridNode({key:r,value:a},!1);o.selectable=!0,t.appendChild(o),s&&r!==i||(s=o)}s&&(s.selected=!0),this.dataGrid.addCreationNode(!1),this.setCanDeleteSelected(Boolean(s)),h.ARIAUtils.alert(xs(Is.domStorageNumberEntries,{PH1:r.length}))}deleteSelectedItem(){this.dataGrid&&this.dataGrid.selectedNode&&this.deleteCallback(this.dataGrid.selectedNode)}refreshItems(){this.domStorage.getItems().then((e=>e&&this.showDOMStorageItems(e)))}deleteAllItems(){this.domStorage.clear(),this.domStorageItemsCleared()}editingCallback(e,t,i,s){const r=this.domStorage;"key"===t?("string"==typeof i&&r.removeItem(i),r.setItem(s,e.data.value||""),this.removeDupes(e)):r.setItem(e.data.key||"",s)}removeDupes(e){const t=this.dataGrid.rootNode(),i=t.children;for(let s=i.length-1;s>=0;--s){const r=i[s];r.data.key===e.data.key&&e!==r&&t.removeChild(r)}}deleteCallback(e){e&&!e.isCreationNode&&(this.domStorage&&this.domStorage.removeItem(e.data.key),h.ARIAUtils.alert(xs(Is.domStorageItemDeleted)))}showPreview(e,t){this.preview&&this.previewValue===t||(this.preview&&this.preview.detach(),e||(e=new h.EmptyWidget.EmptyWidget(xs(Is.selectAValueToPreview))),this.previewValue=t,this.preview=e,e.show(this.previewPanel.contentElement))}async previewEntry(t){const i=t&&t.data&&t.data.value;if(t&&t.data&&t.data.value){const s=`${this.domStorage.isLocalStorage?"localstorage":"sessionstorage"}://${t.key}`,r=I.StaticContentProvider.StaticContentProvider.fromString(s,e.ResourceType.resourceTypes.XHR,i),a=await c.PreviewFactory.PreviewFactory.createPreview(r,"text/plain");t.selected&&this.showPreview(a,i)}else this.showPreview(null,i)}}var Rs=Object.freeze({__proto__:null,DOMStorageItemsView:Ms});const Ls=new CSSStyleSheet;let Bs;Ls.replaceSync(".resources-toolbar{border-top:1px solid var(--sys-color-divider);background-color:var(--sys-color-cdt-base-container)}.top-resources-toolbar{border-bottom:1px solid var(--sys-color-divider);background-color:var(--sys-color-cdt-base-container)}.resources.panel .status{float:right;height:16px;margin-top:1px;margin-left:4px;line-height:1em}.storage-view{display:flex;overflow:hidden}.storage-view .data-grid:not(.inline){border:none;flex:auto}.storage-view .storage-table-error{color:var(--sys-color-error);font-size:24px;font-weight:bold;padding:10px;display:flex;align-items:center;justify-content:center}.storage-view.query{padding:2px 0;overflow-y:overlay;overflow-x:hidden}.storage-view .filter-bar{border-top:none;border-bottom:1px solid var(--sys-color-divider)}.database-query-group-messages{overflow-y:auto}.database-query-prompt-container{position:relative;padding:1px 22px 1px 24px;min-height:16px}.database-query-prompt{white-space:pre-wrap}.prompt-icon{position:absolute;display:block;left:7px;top:9px;margin-top:-7px;user-select:none}.database-user-query .prompt-icon{margin-top:-10px}.database-query-prompt-container .prompt-icon{top:6px}.database-user-query{position:relative;border-bottom:1px solid var(--sys-color-divider);padding:1px 22px 1px 24px;min-height:16px;flex-shrink:0}.database-user-query:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.database-query-text{color:var(--sys-color-primary-bright);user-select:text}.database-query-result{position:relative;padding:1px 22px;min-height:16px;margin-left:-22px;padding-right:0}.database-query-result.error{color:var(--sys-color-token-property-special);user-select:text}.database-query-result.error .prompt-icon{margin-top:-9px}.resources-sidebar{padding:0;overflow-x:auto;background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=resourcesPanel.css */\n");class As extends h.Panel.PanelWithSidebar{resourcesLastSelectedItemSetting;visibleView;pendingViewPromise;categoryView;storageViews;storageViewToolbar;domStorageView;cookieView;emptyWidget;sidebar;constructor(){super("resources"),this.resourcesLastSelectedItemSetting=e.Settings.Settings.instance().createSetting("resources-last-selected-element-path",[]),this.visibleView=null,this.pendingViewPromise=null,this.categoryView=null;const t=new h.Widget.VBox;t.setMinimumSize(100,0),this.storageViews=t.element.createChild("div","vbox flex-auto"),this.storageViewToolbar=new h.Toolbar.Toolbar("resources-toolbar",t.element),this.splitWidget().setMainWidget(t),this.domStorageView=null,this.cookieView=null,this.emptyWidget=null,this.sidebar=new Yi(this),this.sidebar.show(this.panelSidebarElement())}static instance(e={forceNew:null}){const{forceNew:t}=e;return Bs&&!t||(Bs=new As),Bs}static shouldCloseOnReset(e){return[c.ResourceSourceFrame.ResourceSourceFrame,c.ImageView.ImageView,c.FontView.FontView,yi].some((t=>e instanceof t))}static async showAndGetSidebar(){return await h.ViewManager.ViewManager.instance().showView("resources"),As.instance().sidebar}focus(){this.sidebar.focus()}lastSelectedItemPath(){return this.resourcesLastSelectedItemSetting.get()}setLastSelectedItemPath(e){this.resourcesLastSelectedItemSetting.set(e)}resetView(){this.visibleView&&As.shouldCloseOnReset(this.visibleView)&&this.showView(null)}showView(e){this.pendingViewPromise=null,this.visibleView!==e&&(this.visibleView&&this.visibleView.detach(),e&&e.show(this.storageViews),this.visibleView=e,this.storageViewToolbar.removeToolbarItems(),this.storageViewToolbar.element.classList.toggle("hidden",!0),e instanceof h.View.SimpleView&&e.toolbarItems().then((e=>{e.map((e=>this.storageViewToolbar.appendToolbarItem(e))),this.storageViewToolbar.element.classList.toggle("hidden",!e.length)})))}async scheduleShowView(e){this.pendingViewPromise=e;const t=await e;return this.pendingViewPromise!==e?null:(this.showView(t),t)}showCategoryView(e,t){this.categoryView||(this.categoryView=new cs),this.categoryView.element.setAttribute("jslog",`${m.pane().context(s.StringUtilities.toKebabCase(e))}`),this.categoryView.setText(e),this.categoryView.setLink(t),this.showView(this.categoryView)}showDOMStorage(e){e&&(this.domStorageView?this.domStorageView.setStorage(e):this.domStorageView=new Ms(e),this.showView(this.domStorageView))}showCookies(e,t){const i=e.model(o.CookieModel.CookieModel);i&&(this.cookieView?this.cookieView.setCookiesDomain(i,t):this.cookieView=new Cs(i,t),this.showView(this.cookieView))}clearCookies(e,t){const i=e.model(o.CookieModel.CookieModel);i&&i.clear(t).then((()=>{this.cookieView&&this.cookieView.refreshItems()}))}wasShown(){super.wasShown(),this.registerCSSFiles([Ls])}}var Ps=Object.freeze({__proto__:null,ResourcesPanel:As,ResourceRevealer:class{async reveal(e){const t=await As.showAndGetSidebar();await t.showResource(e)}},FrameDetailsRevealer:class{async reveal(e){(await As.showAndGetSidebar()).showFrame(e)}},RuleSetViewRevealer:class{async reveal(e){(await As.showAndGetSidebar()).showPreloadingRuleSetView(e)}},AttemptViewWithFilterRevealer:class{async reveal(e){(await As.showAndGetSidebar()).showPreloadingAttemptViewWithFilter(e)}}});export{N as AppManifestView,vs as ApplicationPanelSidebar,q as BackgroundServiceModel,te as BackgroundServiceView,oe as BounceTrackingMitigationsTreeElement,Ts as CookieItemsView,Rs as DOMStorageItemsView,ce as DOMStorageModel,be as IndexedDBModel,xe as IndexedDBViews,Re as InterestGroupStorageModel,Fe as InterestGroupStorageView,Ne as InterestGroupTreeElement,$e as OpenedWindowDetailsView,wt as PreloadingTreeElement,dt as PreloadingView,ft as ReportingApiReportsView,Tt as ReportingApiView,Ps as ResourcesPanel,Wt as ServiceWorkerCacheViews,Qt as ServiceWorkerUpdateCycleView,si as ServiceWorkersView,li as SharedStorageEventsView,xi as SharedStorageItemsView,pi as SharedStorageListTreeElement,vi as SharedStorageModel,Ri as SharedStorageTreeElement,Fi as StorageBucketsTreeElement,fi as StorageItemsView,Hi as StorageView,qi as TrustTokensTreeElement};
