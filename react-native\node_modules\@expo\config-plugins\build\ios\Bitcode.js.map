{"version": 3, "file": "Bitcode.js", "names": ["_Xcodeproj", "data", "require", "_iosPlugins", "_warnings", "withBitcode", "config", "withXcodeProject", "modResults", "setBitcodeWithConfig", "project", "exports", "withCustomBitcode", "bitcode", "setBitcode", "getBitcode", "ios", "isDefaultBehavior", "targetName", "undefined", "isBitcodeEnabled", "configs", "Object", "entries", "pbxXCBuildConfigurationSection", "filter", "isNotComment", "hasConfiguration", "find", "configuration", "name", "addBuildProperty", "names", "Set", "map", "sort", "addWarningIOS", "join"], "sources": ["../../src/ios/Bitcode.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { XcodeProject } from 'xcode';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { isNotComment } from './utils/Xcodeproj';\nimport { withXcodeProject } from '../plugins/ios-plugins';\nimport { addWarningIOS } from '../utils/warnings';\n\ntype Bitcode = NonNullable<ExpoConfig['ios']>['bitcode'];\n\n/**\n * Plugin to set a bitcode preference for the Xcode project\n * based on the project's Expo config `ios.bitcode` value.\n */\nexport const withBitcode: ConfigPlugin = (config) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setBitcodeWithConfig(config, {\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\n/**\n * Plugin to set a custom bitcode preference for the Xcode project.\n * Does not read from the Expo config `ios.bitcode`.\n *\n * @param bitcode custom bitcode setting.\n */\nexport const withCustomBitcode: ConfigPlugin<Bitcode> = (config, bitcode) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setBitcode(bitcode, {\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\n/**\n * Get the bitcode preference from the Expo config.\n */\nexport function getBitcode(config: Pick<ExpoConfig, 'ios'>): Bitcode {\n  return config.ios?.bitcode;\n}\n\n/**\n * Enable or disable the `ENABLE_BITCODE` property of the project configurations.\n */\nexport function setBitcodeWithConfig(\n  config: Pick<ExpoConfig, 'ios'>,\n  { project }: { project: XcodeProject }\n): XcodeProject {\n  const bitcode = getBitcode(config);\n  return setBitcode(bitcode, { project });\n}\n\n/**\n * Enable or disable the `ENABLE_BITCODE` property.\n */\nexport function setBitcode(bitcode: Bitcode, { project }: { project: XcodeProject }): XcodeProject {\n  const isDefaultBehavior = bitcode == null;\n  // If the value is undefined, then do nothing.\n  if (isDefaultBehavior) {\n    return project;\n  }\n\n  const targetName = typeof bitcode === 'string' ? bitcode : undefined;\n  const isBitcodeEnabled = !!bitcode;\n  if (targetName) {\n    // Assert if missing\n    const configs = Object.entries(project.pbxXCBuildConfigurationSection()).filter(isNotComment);\n    const hasConfiguration = configs.find(([, configuration]) => configuration.name === targetName);\n    if (hasConfiguration) {\n      // If targetName is defined then disable bitcode everywhere.\n      project.addBuildProperty('ENABLE_BITCODE', 'NO');\n    } else {\n      const names = [\n        // Remove duplicates, wrap in double quotes, and sort alphabetically.\n        ...new Set(configs.map(([, configuration]) => `\"${configuration.name}\"`)),\n      ].sort();\n      addWarningIOS(\n        'ios.bitcode',\n        `No configuration named \"${targetName}\". Expected one of: ${names.join(', ')}.`\n      );\n    }\n  }\n\n  project.addBuildProperty('ENABLE_BITCODE', isBitcodeEnabled ? 'YES' : 'NO', targetName);\n\n  return project;\n}\n"], "mappings": ";;;;;;;;;AAIA,SAAAA,WAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,UAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,UAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA;AACA;AACA;AACA;AACO,MAAMI,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMC,oBAAoB,CAACH,MAAM,EAAE;MACrDI,OAAO,EAAEJ,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAK,OAAA,CAAAN,WAAA,GAAAA,WAAA;AAMO,MAAMO,iBAAwC,GAAGA,CAACN,MAAM,EAAEO,OAAO,KAAK;EAC3E,OAAO,IAAAN,8BAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMM,UAAU,CAACD,OAAO,EAAE;MAC5CH,OAAO,EAAEJ,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAK,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAGO,SAASG,UAAUA,CAACT,MAA+B,EAAW;EACnE,OAAOA,MAAM,CAACU,GAAG,EAAEH,OAAO;AAC5B;;AAEA;AACA;AACA;AACO,SAASJ,oBAAoBA,CAClCH,MAA+B,EAC/B;EAAEI;AAAmC,CAAC,EACxB;EACd,MAAMG,OAAO,GAAGE,UAAU,CAACT,MAAM,CAAC;EAClC,OAAOQ,UAAU,CAACD,OAAO,EAAE;IAAEH;EAAQ,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACO,SAASI,UAAUA,CAACD,OAAgB,EAAE;EAAEH;AAAmC,CAAC,EAAgB;EACjG,MAAMO,iBAAiB,GAAGJ,OAAO,IAAI,IAAI;EACzC;EACA,IAAII,iBAAiB,EAAE;IACrB,OAAOP,OAAO;EAChB;EAEA,MAAMQ,UAAU,GAAG,OAAOL,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGM,SAAS;EACpE,MAAMC,gBAAgB,GAAG,CAAC,CAACP,OAAO;EAClC,IAAIK,UAAU,EAAE;IACd;IACA,MAAMG,OAAO,GAAGC,MAAM,CAACC,OAAO,CAACb,OAAO,CAACc,8BAA8B,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,yBAAY,CAAC;IAC7F,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,IAAI,CAAC,CAAC,GAAGC,aAAa,CAAC,KAAKA,aAAa,CAACC,IAAI,KAAKZ,UAAU,CAAC;IAC/F,IAAIS,gBAAgB,EAAE;MACpB;MACAjB,OAAO,CAACqB,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC;IAClD,CAAC,MAAM;MACL,MAAMC,KAAK,GAAG;MACZ;MACA,GAAG,IAAIC,GAAG,CAACZ,OAAO,CAACa,GAAG,CAAC,CAAC,GAAGL,aAAa,CAAC,KAAK,IAAIA,aAAa,CAACC,IAAI,GAAG,CAAC,CAAC,CAC1E,CAACK,IAAI,CAAC,CAAC;MACR,IAAAC,yBAAa,EACX,aAAa,EACb,2BAA2BlB,UAAU,uBAAuBc,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC,GAC9E,CAAC;IACH;EACF;EAEA3B,OAAO,CAACqB,gBAAgB,CAAC,gBAAgB,EAAEX,gBAAgB,GAAG,KAAK,GAAG,IAAI,EAAEF,UAAU,CAAC;EAEvF,OAAOR,OAAO;AAChB", "ignoreList": []}