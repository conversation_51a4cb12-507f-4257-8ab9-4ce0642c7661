{"version": 3, "sources": ["../../../../../src/start/server/metro/createServerRouteMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type { ProjectConfig } from '@expo/config';\nimport resolve from 'resolve';\nimport resolveFrom from 'resolve-from';\nimport { promisify } from 'util';\n\nimport { fetchManifest } from './fetchRouterManifest';\nimport { getErrorOverlayHtmlAsync, logMetroError } from './metroErrorInterface';\nimport { warnInvalidWebOutput } from './router';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\nconst resolveAsync = promisify(resolve) as any as (\n  id: string,\n  opts: resolve.AsyncOpts\n) => Promise<string | null>;\n\nexport function createRouteHandlerMiddleware(\n  projectRoot: string,\n  options: {\n    appDir: string;\n    routerRoot: string;\n    getStaticPageAsync: (pathname: string) => Promise<{ content: string }>;\n    bundleApiRoute: (\n      functionFilePath: string\n    ) => Promise<null | Record<string, Function> | Response>;\n    config: ProjectConfig;\n  } & import('expo-router/build/routes-manifest').Options\n) {\n  if (!resolveFrom.silent(projectRoot, 'expo-router')) {\n    throw new CommandError(\n      `static and server rendering requires the expo-router package to be installed in your project. Either install the expo-router package or change 'web.output' to 'static' in your app.json.`\n    );\n  }\n\n  const { createRequestHandler } =\n    require('@expo/server/build/vendor/http') as typeof import('@expo/server/build/vendor/http');\n\n  return createRequestHandler(\n    { build: '' },\n    {\n      async getRoutesManifest() {\n        const manifest = await fetchManifest<RegExp>(projectRoot, options);\n        debug('manifest', manifest);\n        // NOTE: no app dir if null\n        // TODO: Redirect to 404 page\n        return (\n          manifest ?? {\n            // Support the onboarding screen if there's no manifest\n            htmlRoutes: [\n              {\n                file: 'index.js',\n                page: '/index',\n                routeKeys: {},\n                namedRegex: /^\\/(?:index)?\\/?$/i,\n              },\n            ],\n            apiRoutes: [],\n            notFoundRoutes: [],\n            redirects: [],\n            rewrites: [],\n          }\n        );\n      },\n      async getHtml(request) {\n        try {\n          const { content } = await options.getStaticPageAsync(request.url);\n          return content;\n        } catch (error: any) {\n          // Forward the Metro server response as-is. It won't be pretty, but at least it will be accurate.\n\n          try {\n            return new Response(\n              await getErrorOverlayHtmlAsync({\n                error,\n                projectRoot,\n                routerRoot: options.routerRoot,\n              }),\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          } catch (staticError: any) {\n            debug('Failed to render static error overlay:', staticError);\n            // Fallback error for when Expo Router is misconfigured in the project.\n            return new Response(\n              '<span><h3>Internal Error:</h3><b>Project is not setup correctly for static rendering (check terminal for more info):</b><br/>' +\n                error.message +\n                '<br/><br/>' +\n                staticError.message +\n                '</span>',\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          }\n        }\n      },\n      logApiRouteExecutionError(error) {\n        logMetroError(projectRoot, { error });\n      },\n      async handleApiRouteError(error) {\n        const htmlServerError = await getErrorOverlayHtmlAsync({\n          error,\n          projectRoot,\n          routerRoot: options.routerRoot!,\n        });\n\n        return new Response(htmlServerError, {\n          status: 500,\n          headers: {\n            'Content-Type': 'text/html',\n          },\n        });\n      },\n      async getApiRoute(route) {\n        const { exp } = options.config;\n        if (exp.web?.output !== 'server') {\n          warnInvalidWebOutput();\n        }\n\n        const resolvedFunctionPath = await resolveAsync(route.file, {\n          extensions: ['.js', '.jsx', '.ts', '.tsx'],\n          basedir: options.appDir,\n        })!;\n\n        try {\n          debug(`Bundling middleware at: ${resolvedFunctionPath}`);\n          return await options.bundleApiRoute(resolvedFunctionPath!);\n        } catch (error: any) {\n          return new Response(\n            'Failed to load API Route: ' + resolvedFunctionPath + '\\n\\n' + error.message,\n            {\n              status: 500,\n              headers: {\n                'Content-Type': 'text/html',\n              },\n            }\n          );\n        }\n      },\n    }\n  );\n}\n"], "names": ["createRouteHandlerMiddleware", "debug", "require", "resolveAsync", "promisify", "resolve", "projectRoot", "options", "resolveFrom", "silent", "CommandError", "createRequestHandler", "build", "getRoutesManifest", "manifest", "fetchManifest", "htmlRoutes", "file", "page", "routeKeys", "namedRegex", "apiRoutes", "notFoundRoutes", "redirects", "rewrites", "getHtml", "request", "content", "getStaticPageAsync", "url", "error", "Response", "getErrorOverlayHtmlAsync", "routerRoot", "status", "headers", "staticError", "message", "logApiRouteExecutionError", "logMetroError", "handleApiRouteError", "htmlServerError", "getApiRoute", "route", "exp", "config", "web", "output", "warnInvalidWebOutput", "resolvedFunctionPath", "extensions", "basedir", "appDir", "bundleApiRoute"], "mappings": "AAAA;;;;;CAKC;;;;+BAmBeA;;;eAAAA;;;;gEAhBI;;;;;;;gEACI;;;;;;;yBACE;;;;;;qCAEI;qCAC0B;wBACnB;wBACR;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,eAAeC,IAAAA,iBAAS,EAACC,kBAAO;AAK/B,SAASL,6BACdM,WAAmB,EACnBC,OAQuD;IAEvD,IAAI,CAACC,sBAAW,CAACC,MAAM,CAACH,aAAa,gBAAgB;QACnD,MAAM,IAAII,oBAAY,CACpB,CAAC,yLAAyL,CAAC;IAE/L;IAEA,MAAM,EAAEC,oBAAoB,EAAE,GAC5BT,QAAQ;IAEV,OAAOS,qBACL;QAAEC,OAAO;IAAG,GACZ;QACE,MAAMC;YACJ,MAAMC,WAAW,MAAMC,IAAAA,kCAAa,EAAST,aAAaC;YAC1DN,MAAM,YAAYa;YAClB,2BAA2B;YAC3B,6BAA6B;YAC7B,OACEA,YAAY;gBACV,uDAAuD;gBACvDE,YAAY;oBACV;wBACEC,MAAM;wBACNC,MAAM;wBACNC,WAAW,CAAC;wBACZC,YAAY;oBACd;iBACD;gBACDC,WAAW,EAAE;gBACbC,gBAAgB,EAAE;gBAClBC,WAAW,EAAE;gBACbC,UAAU,EAAE;YACd;QAEJ;QACA,MAAMC,SAAQC,OAAO;YACnB,IAAI;gBACF,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAMpB,QAAQqB,kBAAkB,CAACF,QAAQG,GAAG;gBAChE,OAAOF;YACT,EAAE,OAAOG,OAAY;gBACnB,iGAAiG;gBAEjG,IAAI;oBACF,OAAO,IAAIC,SACT,MAAMC,IAAAA,6CAAwB,EAAC;wBAC7BF;wBACAxB;wBACA2B,YAAY1B,QAAQ0B,UAAU;oBAChC,IACA;wBACEC,QAAQ;wBACRC,SAAS;4BACP,gBAAgB;wBAClB;oBACF;gBAEJ,EAAE,OAAOC,aAAkB;oBACzBnC,MAAM,0CAA0CmC;oBAChD,uEAAuE;oBACvE,OAAO,IAAIL,SACT,kIACED,MAAMO,OAAO,GACb,eACAD,YAAYC,OAAO,GACnB,WACF;wBACEH,QAAQ;wBACRC,SAAS;4BACP,gBAAgB;wBAClB;oBACF;gBAEJ;YACF;QACF;QACAG,2BAA0BR,KAAK;YAC7BS,IAAAA,kCAAa,EAACjC,aAAa;gBAAEwB;YAAM;QACrC;QACA,MAAMU,qBAAoBV,KAAK;YAC7B,MAAMW,kBAAkB,MAAMT,IAAAA,6CAAwB,EAAC;gBACrDF;gBACAxB;gBACA2B,YAAY1B,QAAQ0B,UAAU;YAChC;YAEA,OAAO,IAAIF,SAASU,iBAAiB;gBACnCP,QAAQ;gBACRC,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF;QACA,MAAMO,aAAYC,KAAK;gBAEjBC;YADJ,MAAM,EAAEA,GAAG,EAAE,GAAGrC,QAAQsC,MAAM;YAC9B,IAAID,EAAAA,WAAAA,IAAIE,GAAG,qBAAPF,SAASG,MAAM,MAAK,UAAU;gBAChCC,IAAAA,4BAAoB;YACtB;YAEA,MAAMC,uBAAuB,MAAM9C,aAAawC,MAAM1B,IAAI,EAAE;gBAC1DiC,YAAY;oBAAC;oBAAO;oBAAQ;oBAAO;iBAAO;gBAC1CC,SAAS5C,QAAQ6C,MAAM;YACzB;YAEA,IAAI;gBACFnD,MAAM,CAAC,wBAAwB,EAAEgD,sBAAsB;gBACvD,OAAO,MAAM1C,QAAQ8C,cAAc,CAACJ;YACtC,EAAE,OAAOnB,OAAY;gBACnB,OAAO,IAAIC,SACT,+BAA+BkB,uBAAuB,SAASnB,MAAMO,OAAO,EAC5E;oBACEH,QAAQ;oBACRC,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YAEJ;QACF;IACF;AAEJ"}