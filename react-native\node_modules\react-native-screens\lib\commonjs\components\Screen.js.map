{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "InnerScreen", "_react", "_interopRequireDefault", "require", "_reactNative", "_TransitionProgressContext", "_DelayedFreeze", "_core", "_ScreenNativeComponent", "_ModalScreenNativeComponent", "_usePrevious", "obj", "__esModule", "_extends", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "AnimatedNativeScreen", "Animated", "createAnimatedComponent", "ScreenNativeComponent", "AnimatedNativeModalScreen", "ModalScreenNativeComponent", "SHEET_FIT_TO_CONTENTS", "SHEET_COMPAT_LARGE", "SHEET_COMPAT_MEDIUM", "SHEET_COMPAT_ALL", "SHEET_DIMMED_ALWAYS", "assertDetentsArrayIsSorted", "array", "Error", "resolveSheetAllowedDetents", "allowedDetentsCompat", "Array", "isArray", "Platform", "OS", "__DEV__", "console", "warn", "slice", "resolveSheetLargestUndimmedDetent", "lud", "lastDetentIndex", "isIndexInClosedRange", "resolveSheetInitialDetentIndex", "index", "lowerBound", "upperBound", "Number", "isInteger", "React", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "usePrevious", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "screensEnabled", "freezeOnBlur", "freezeEnabled", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "isNativePlatformSupported", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "AnimatedScreen", "undefined", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "createElement", "freeze", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,WAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAOA,IAAAK,sBAAA,GAAAN,sBAAA,CAAAC,OAAA;AAGA,IAAAM,2BAAA,GAAAP,sBAAA,CAAAC,OAAA;AAGA,IAAAO,YAAA,GAAAP,OAAA;AAAoD,SAAAD,uBAAAS,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAb,OAAA,EAAAa,GAAA;AAAA,SAAAE,SAAA,IAAAA,QAAA,GAAAnB,MAAA,CAAAoB,MAAA,GAAApB,MAAA,CAAAoB,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAA1B,MAAA,CAAA4B,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAH,QAAA,CAAAY,KAAA,OAAAP,SAAA,KAPpD;AAUA,MAAMQ,oBAAoB,GAAGC,qBAAQ,CAACC,uBAAuB,CAC3DC,8BACF,CAAC;AACD,MAAMC,yBAAyB,GAAGH,qBAAQ,CAACC,uBAAuB,CAChEG,mCACF,CAAC;;AAED;AACA;AAkBA;AACA,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,MAAMC,kBAAkB,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMC,mBAAmB,GAAG,CAAC,GAAG,CAAC;AACjC,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAEnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;AAC9B;;AAEA,SAASC,0BAA0BA,CAACC,KAAe,EAAE;EACnD,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,KAAK,CAACnB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIqB,KAAK,CAACrB,CAAC,GAAG,CAAC,CAAC,GAAGqB,KAAK,CAACrB,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAIsB,KAAK,CACb,gEACF,CAAC;IACH;EACF;AACF;;AAEA;AACA;AACA,SAASC,0BAA0BA,CACjCC,oBAAwD,EAC9C;EACV,IAAIC,KAAK,CAACC,OAAO,CAACF,oBAAoB,CAAC,EAAE;IACvC,IAAIG,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAIJ,oBAAoB,CAACtB,MAAM,GAAG,CAAC,EAAE;MAChE,IAAI2B,OAAO,EAAE;QACXC,OAAO,CAACC,IAAI,CACV,iGACF,CAAC;MACH;MACAP,oBAAoB,GAAGA,oBAAoB,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzD;IACA,IAAIH,OAAO,EAAE;MACXT,0BAA0B,CAACI,oBAAoB,CAAC;IAClD;IACA,OAAOA,oBAAoB;EAC7B,CAAC,MAAM,IAAIA,oBAAoB,KAAK,eAAe,EAAE;IACnD,OAAOT,qBAAqB;EAC9B,CAAC,MAAM,IAAIS,oBAAoB,KAAK,OAAO,EAAE;IAC3C,OAAOR,kBAAkB;EAC3B,CAAC,MAAM,IAAIQ,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,OAAOP,mBAAmB;EAC5B,CAAC,MAAM,IAAIO,oBAAoB,KAAK,KAAK,EAAE;IACzC,OAAON,gBAAgB;EACzB,CAAC,MAAM;IACL;IACA,OAAO,CAAC,GAAG,CAAC;EACd;AACF;AAEA,SAASe,iCAAiCA,CACxCC,GAAmD,EACnDC,eAAuB,EACf;EACR,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,CAACE,oBAAoB,CAACF,GAAG,EAAEf,mBAAmB,EAAEgB,eAAe,CAAC,EAAE;MACpE,IAAIN,OAAO,EAAE;QACX,MAAM,IAAIP,KAAK,CACb,uHACF,CAAC;MACH;MACA;MACA,OAAOH,mBAAmB;IAC5B;IACA,OAAOe,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,KAAK,MAAM,EAAE;IACzB,OAAOC,eAAe;EACxB,CAAC,MAAM,IAAID,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC1C,OAAOf,mBAAmB;EAC5B,CAAC,MAAM,IAAIe,GAAG,KAAK,OAAO,EAAE;IAC1B,OAAO,CAAC;EACV,CAAC,MAAM,IAAIA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAC;EACV,CAAC,MAAM;IACL;IACA,OAAOf,mBAAmB;EAC5B;AACF;AAEA,SAASkB,8BAA8BA,CACrCC,KAA6C,EAC7CH,eAAuB,EACf;EACR,IAAIG,KAAK,KAAK,MAAM,EAAE;IACpBA,KAAK,GAAGH,eAAe;EACzB,CAAC,MAAM,IAAIG,KAAK,IAAI,IAAI,EAAE;IACxB;IACAA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,CAACF,oBAAoB,CAACE,KAAK,EAAE,CAAC,EAAEH,eAAe,CAAC,EAAE;IACpD,IAAIN,OAAO,EAAE;MACX,MAAM,IAAIP,KAAK,CACb,+GACF,CAAC;IACH;IACA;IACA,OAAO,CAAC;EACV;EACA,OAAOgB,KAAK;AACd;AAEA,SAASF,oBAAoBA,CAC3BxD,KAAa,EACb2D,UAAkB,EAClBC,UAAkB,EACT;EACT,OAAOC,MAAM,CAACC,SAAS,CAAC9D,KAAK,CAAC,IAAIA,KAAK,IAAI2D,UAAU,IAAI3D,KAAK,IAAI4D,UAAU;AAC9E;AAEO,MAAMzD,WAAW,GAAAJ,OAAA,CAAAI,WAAA,gBAAG4D,cAAK,CAACC,UAAU,CACzC,SAAS7D,WAAWA,CAAC8D,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGJ,cAAK,CAACK,MAAM,CAAoB,IAAI,CAAC;EACtDL,cAAK,CAACM,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAG,IAAAC,wBAAW,EAACP,KAAK,CAACQ,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIR,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACU,cAAc,GAAGT,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMU,OAAO,GAAGb,cAAK,CAACK,MAAM,CAAC,IAAItC,qBAAQ,CAAC+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC3D,MAAMQ,QAAQ,GAAGf,cAAK,CAACK,MAAM,CAAC,IAAItC,qBAAQ,CAAC+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC5D,MAAMS,YAAY,GAAGhB,cAAK,CAACK,MAAM,CAAC,IAAItC,qBAAQ,CAAC+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAEhE,MAAM;IACJU,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAC1BC,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;IAC9B,GAAGC;EACL,CAAC,GAAGnB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAoB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAG/C,mBAAmB;IACrDgD,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGZ,IAAI;EAER,IAAIJ,OAAO,IAAIiB,+BAAyB,EAAE;IACxC,MAAMC,2BAA2B,GAC/BvD,0BAA0B,CAAC0C,mBAAmB,CAAC;IACjD,MAAMc,kCAAkC,GACtC9C,iCAAiC,CAC/BiC,+BAA+B,EAC/BY,2BAA2B,CAAC5E,MAAM,GAAG,CACvC,CAAC;IACH,MAAM8E,+BAA+B,GAAG3C,8BAA8B,CACpEkC,uBAAuB,EACvBO,2BAA2B,CAAC5E,MAAM,GAAG,CACvC,CAAC;IACD;IACA,MAAM+E,cAAc,GAClBtD,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzB4C,iBAAiB,KAAKU,SAAS,IAC/BV,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,GAC7C/D,oBAAoB,GACpBI,yBAAyB;IAE/B,IAAI;MACF;MACA;MACA;MACAsE,MAAM;MACN9B,aAAa;MACb+B,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAG3C;IACL,CAAC,GAAGmB,IAAI;IAER,IAAImB,MAAM,KAAKD,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvDpD,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDsB,aAAa,GAAG8B,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;;IAEA,IACEE,aAAa,IACblC,iBAAiB,KAAK+B,SAAS,IAC/B7B,aAAa,KAAK6B,SAAS,EAC3B;MACA,IAAI/B,iBAAiB,GAAGE,aAAa,EAAE;QACrC,MAAM,IAAI/B,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMmE,SAAS,GAAI3C,GAAe,IAAK;MACrC,IAAIA,GAAG,EAAE4C,UAAU,EAAEC,eAAe,EAAEH,KAAK,EAAE;QAC3C1C,GAAG,CAAC4C,UAAU,CAACC,eAAe,CAACH,KAAK,GAAG;UACrC,GAAG1C,GAAG,CAAC4C,UAAU,CAACC,eAAe,CAACH,KAAK;UACvCI,OAAO,EAAE;QACX,CAAC;QACDtC,MAAM,CAACR,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAE+C,WAAW,EAAEF,eAAe,EAAEH,KAAK,EAAE;QACnD1C,GAAG,CAAC+C,WAAW,CAACF,eAAe,CAACH,KAAK,GAAG;UACtC,GAAG1C,GAAG,CAAC+C,WAAW,CAACF,eAAe,CAACH,KAAK;UACxCI,OAAO,EAAE;QACX,CAAC;QACDtC,MAAM,CAACR,GAAG,CAAC;MACb;IACF,CAAC;IAED,oBACE9D,MAAA,CAAAH,OAAA,CAAAiH,aAAA,CAACzG,cAAA,CAAAR,OAAa;MAACkH,MAAM,EAAEjC,YAAY,IAAIT,aAAa,KAAK;IAAE,gBACzDrE,MAAA,CAAAH,OAAA,CAAAiH,aAAA,CAACb,cAAc,EAAArF,QAAA,KACTiD,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY4B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEW,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEQ,MAAM,EAAEd;MAAU,CAAC,CAAE;MACtC7B,aAAa,EAAEA,aAAc;MAC7BY,mBAAmB,EAAEa,2BAA4B;MACjDmB,0BAA0B,EAAElB,kCAAmC;MAC/DT,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/D6B,kBAAkB,EAAElB,+BAAgC;MACpDM,uBAAuB,EAAE;QACvBa,KAAK,EAAEb,uBAAuB,EAAEa,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAEf,uBAAuB,EAAEe,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEhB,uBAAuB,EAAEgB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACAxD,GAAG,EAAE2C,SAAU;MACfc,oBAAoB,EAClB,CAAClB,aAAa,GACVH,SAAS,GACTxE,qBAAQ,CAAC8F,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACX/C,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAE+C,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACrB,aAAa;IAAK;IAClBD,QAAQ,gBAERpG,MAAA,CAAAH,OAAA,CAAAiH,aAAA,CAAC1G,0BAAA,CAAAP,OAAyB,CAAC8H,QAAQ;MACjC/H,KAAK,EAAE;QACL8E,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACDyB,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACN9B,aAAa;MACbmC,KAAK;MACL;MACAjC,cAAc;MACd,GAAGV;IACL,CAAC,GAAGmB,IAAI;IAER,IAAImB,MAAM,KAAKD,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvD7B,aAAa,GAAG8B,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACEnG,MAAA,CAAAH,OAAA,CAAAiH,aAAA,CAAC3G,YAAA,CAAAuB,QAAQ,CAACkG,IAAI,EAAAhH,QAAA;MACZ4F,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEI,OAAO,EAAEvC,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEP,GAAG,EAAEQ;IAAO,GACRT,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACO,MAAM/D,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAG6D,cAAK,CAACkE,aAAa,CAAC9H,WAAW,CAAC;AAE7D,MAAM+H,MAAM,gBAAGnE,cAAK,CAACC,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAMiE,aAAa,GAAGpE,cAAK,CAACqE,UAAU,CAAClI,aAAa,CAAC,IAAIC,WAAW;EAEpE,oBAAOC,MAAA,CAAAH,OAAA,CAAAiH,aAAA,CAACiB,aAAa,EAAAnH,QAAA,KAAKiD,KAAK;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC/C,CAAC,CAAC;AAEFgE,MAAM,CAACG,WAAW,GAAG,QAAQ;AAAC,IAAAC,QAAA,GAAAvI,OAAA,CAAAE,OAAA,GAEfiI,MAAM"}