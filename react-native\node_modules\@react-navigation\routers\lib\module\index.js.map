{"version": 3, "names": ["CommonActions", "default", "BaseRouter", "DrawerActions", "DrawerRouter", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA,OAAO,KAAKA,aAAa,MAAM,iBAAiB;AAEhD,SAASA,aAAa;AAEtB,SAASC,OAAO,IAAIC,UAAU,QAAQ,cAAc;AAQpD,SAASC,aAAa,EAAEF,OAAO,IAAIG,YAAY,QAAQ,gBAAgB;AAOvE,SAASC,YAAY,EAAEJ,OAAO,IAAIK,WAAW,QAAQ,eAAe;AAOpE,SAASC,UAAU,EAAEN,OAAO,IAAIO,SAAS,QAAQ,aAAa;AAC9D,cAAc,SAAS"}