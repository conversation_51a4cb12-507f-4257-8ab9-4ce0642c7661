const t=1024;let e=0;class i{constructor(t,e){this.from=t,this.to=e}}class n{constructor(t={}){this.id=e++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof t&&(t=o.match(t)),e=>{let i=t(e);return void 0===i?null:[this,i]}}}n.closedBy=new n({deserialize:t=>t.split(" ")}),n.openedBy=new n({deserialize:t=>t.split(" ")}),n.group=new n({deserialize:t=>t.split(" ")}),n.isolate=new n({deserialize:t=>{if(t&&"rtl"!=t&&"ltr"!=t&&"auto"!=t)throw new RangeError("Invalid value for isolate: "+t);return t||"auto"}}),n.contextHash=new n({perNode:!0}),n.lookAhead=new n({perNode:!0}),n.mounted=new n({perNode:!0});class s{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[n.mounted.id]}}const r=Object.create(null);class o{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):r,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(null==t.name?8:0),n=new o(t.name||"",e,t.id,i);if(t.props)for(let i of t.props)if(Array.isArray(i)||(i=i(n)),i){if(i[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[i[0].id]=i[1]}return n}prop(t){return this.props[t.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(t){if("string"==typeof t){if(this.name==t)return!0;let e=this.prop(n.group);return!!e&&e.indexOf(t)>-1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return t=>{for(let i=t.prop(n.group),s=-1;s<(i?i.length:0);s++){let n=e[s<0?t.name:i[s]];if(n)return n}}}}o.none=new o("",Object.create(null),0,8);class a{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let e of t){let t=e(i);t&&(n||(n=Object.assign({},i.props)),n[t[0].id]=t[1])}e.push(n?new o(i.name,n,i.id,i.flags):i)}return new a(e)}}const l=new WeakMap,h=new WeakMap;var c;!function(t){t[t.ExcludeBuffers=1]="ExcludeBuffers",t[t.IncludeAnonymous=2]="IncludeAnonymous",t[t.IgnoreMounts=4]="IgnoreMounts",t[t.IgnoreOverlays=8]="IgnoreOverlays"}(c||(c={}));class O{constructor(t,e,i,n,s){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,s&&s.length){this.props=Object.create(null);for(let[t,e]of s)this.props["number"==typeof t?t:t.id]=e}}toString(){let t=s.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let t of this.children){let i=t.toString();i&&(e&&(e+=","),e+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new x(this.topNode,t)}cursorAt(t,e=0,i=0){let n=l.get(this)||this.topNode,s=new x(n);return s.moveTo(t,e),l.set(this,s._tree),s}get topNode(){return new g(this,0,0,null)}resolve(t,e=0){let i=p(l.get(this)||this.topNode,t,e,!1);return l.set(this,i),i}resolveInner(t,e=0){let i=p(h.get(this)||this.topNode,t,e,!0);return h.set(this,i),i}resolveStack(t,e=0){return function(t,e,i){let n=t.resolveInner(e,i),r=null;for(let t=n instanceof g?n:n.context.parent;t;t=t.parent)if(t.index<0){let s=t.parent;(r||(r=[n])).push(s.resolve(e,i)),t=s}else{let o=s.get(t.tree);if(o&&o.overlay&&o.overlay[0].from<=e&&o.overlay[o.overlay.length-1].to>=e){let s=new g(o.tree,o.overlay[0].from+t.from,-1,t);(r||(r=[n])).push(p(s,e,i,!1))}}return r?Q(r):n}(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:s=this.length}=t,r=t.mode||0,o=(r&c.IncludeAnonymous)>0;for(let t=this.cursor(r|c.IncludeAnonymous);;){let r=!1;if(t.from<=s&&t.to>=n&&(!o&&t.type.isAnonymous||!1!==e(t))){if(t.firstChild())continue;r=!0}for(;r&&i&&(o||!t.type.isAnonymous)&&i(t),!t.nextSibling();){if(!t.parent())return;r=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:Z(o.none,this.children,this.positions,0,this.children.length,0,this.length,((t,e,i)=>new O(this.type,t,e,i,this.propValues)),t.makeTree||((t,e,i)=>new O(o.none,t,e,i)))}static build(e){return function(e){var i;let{buffer:s,nodeSet:r,maxBufferLength:o=t,reused:a=[],minRepeatType:l=r.types.length}=e,h=Array.isArray(s)?new u(s,s.length):s,c=r.types,d=0,p=0;function m(t,e,i,n,s,O){let{id:u,start:v,end:x,size:k}=h,$=p;for(;k<0;){if(h.next(),-1==k){let e=a[u];return i.push(e),void n.push(v-t)}if(-3==k)return void(d=u);if(-4==k)return void(p=u);throw new RangeError(`Unrecognized record size: ${k}`)}let P,T,X=c[u],C=v-t;if(x-v<=o&&(T=y(h.pos-e,s))){let e=new Uint16Array(T.size-T.skip),i=h.pos-T.size,n=e.length;for(;h.pos>i;)n=Q(T.start,e,n);P=new f(e,x-T.start,r),C=T.start-t}else{let t=h.pos-k;h.next();let e=[],i=[],n=u>=l?u:-1,s=0,r=x;for(;h.pos>t;)n>=0&&h.id==n&&h.size>=0?(h.end<=r-o&&(b(e,i,v,s,h.end,r,n,$),s=e.length,r=h.end),h.next()):O>2500?g(v,t,e,i):m(v,t,e,i,n,O+1);if(n>=0&&s>0&&s<e.length&&b(e,i,v,s,v,r,n,$),e.reverse(),i.reverse(),n>-1&&s>0){let t=w(X);P=Z(X,e,i,0,e.length,0,x-v,t,t)}else P=S(X,e,i,x-v,$-x)}i.push(P),n.push(C)}function g(t,e,i,n){let s=[],a=0,l=-1;for(;h.pos>e;){let{id:t,start:e,end:i,size:n}=h;if(n>4)h.next();else{if(l>-1&&e<l)break;l<0&&(l=i-o),s.push(t,e,i),a++,h.next()}}if(a){let e=new Uint16Array(4*a),o=s[s.length-2];for(let t=s.length-3,i=0;t>=0;t-=3)e[i++]=s[t],e[i++]=s[t+1]-o,e[i++]=s[t+2]-o,e[i++]=i;i.push(new f(e,s[2]-o,r)),n.push(o-t)}}function w(t){return(e,i,s)=>{let r,o,a=0,l=e.length-1;if(l>=0&&(r=e[l])instanceof O){if(!l&&r.type==t&&r.length==s)return r;(o=r.prop(n.lookAhead))&&(a=i[l]+r.length+o)}return S(t,e,i,s,a)}}function b(t,e,i,n,s,o,a,l){let h=[],c=[];for(;t.length>n;)h.push(t.pop()),c.push(e.pop()+i-s);t.push(S(r.types[a],h,c,o-s,l-o)),e.push(s-i)}function S(t,e,i,s,r=0,o){if(d){let t=[n.contextHash,d];o=o?[t].concat(o):[t]}if(r>25){let t=[n.lookAhead,r];o=o?[t].concat(o):[t]}return new O(t,e,i,s,o)}function y(t,e){let i=h.fork(),n=0,s=0,r=0,a=i.end-o,c={size:0,start:0,skip:0};t:for(let o=i.pos-t;i.pos>o;){let t=i.size;if(i.id==e&&t>=0){c.size=n,c.start=s,c.skip=r,r+=4,n+=4,i.next();continue}let h=i.pos-t;if(t<0||h<o||i.start<a)break;let O=i.id>=l?4:0,u=i.start;for(i.next();i.pos>h;){if(i.size<0){if(-3!=i.size)break t;O+=4}else i.id>=l&&(O+=4);i.next()}s=u,n+=t,r+=O}return(e<0||n==t)&&(c.size=n,c.start=s,c.skip=r),c.size>4?c:void 0}function Q(t,e,i){let{id:n,start:s,end:r,size:o}=h;if(h.next(),o>=0&&n<l){let a=i;if(o>4){let n=h.pos-(o-4);for(;h.pos>n;)i=Q(t,e,i)}e[--i]=a,e[--i]=r-t,e[--i]=s-t,e[--i]=n}else-3==o?d=n:-4==o&&(p=n);return i}let v=[],x=[];for(;h.pos>0;)m(e.start||0,e.bufferStart||0,v,x,-1,0);let k=null!==(i=e.length)&&void 0!==i?i:v.length?x[0]+v[0].length:0;return new O(c[e.topID],v.reverse(),x.reverse(),k)}(e)}}O.empty=new O(o.none,[],[],0);class u{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new u(this.buffer,this.index)}}class f{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return o.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],s=n.name;if(/\W/.test(s)&&!n.isError&&(s=JSON.stringify(s)),i==(t+=4))return s;let r=[];for(;t<i;)r.push(this.childString(t)),t=this.buffer[t+3];return s+"("+r.join(",")+")"}findChild(t,e,i,n,s){let{buffer:r}=this,o=-1;for(let a=t;a!=e&&!(d(s,n,r[a+1],r[a+2])&&(o=a,i>0));a=r[a+3]);return o}slice(t,e,i){let n=this.buffer,s=new Uint16Array(e-t),r=0;for(let o=t,a=0;o<e;){s[a++]=n[o++],s[a++]=n[o++]-i;let e=s[a++]=n[o++]-i;s[a++]=n[o++]-t,r=Math.max(r,e)}return new f(s,r,this.set)}}function d(t,e,i,n){switch(t){case-2:return i<e;case-1:return n>=e&&i<e;case 0:return i<e&&n>e;case 1:return i<=e&&n>e;case 2:return n>e;case 4:return!0}}function p(t,e,i,n){for(var s;t.from==t.to||(i<1?t.from>=e:t.from>e)||(i>-1?t.to<=e:t.to<e);){let e=!n&&t instanceof g&&t.index<0?null:t.parent;if(!e)return t;t=e}let r=n?0:c.IgnoreOverlays;if(n)for(let n=t,o=n.parent;o;n=o,o=n.parent)n instanceof g&&n.index<0&&(null===(s=o.enter(e,i,r))||void 0===s?void 0:s.from)!=n.from&&(t=o);for(;;){let n=t.enter(e,i,r);if(!n)return t;t=n}}class m{cursor(t=0){return new x(this,t)}getChild(t,e=null,i=null){let n=w(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return w(this,t,e,i)}resolve(t,e=0){return p(this,t,e,!1)}resolveInner(t,e=0){return p(this,t,e,!0)}matchContext(t){return b(this,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let t=e.lastChild;if(!t||t.to!=e.to)break;t.type.isError&&t.from==t.to?(i=e,e=t.prevSibling):e=t}return i}get node(){return this}get next(){return this.parent}}class g extends m{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:a,positions:l}=o._tree,h=e>0?a.length:-1;t!=h;t+=e){let h=a[t],O=l[t]+o.from;if(d(n,i,O,O+h.length))if(h instanceof f){if(r&c.ExcludeBuffers)continue;let s=h.findChild(0,h.buffer.length,e,i-O,n);if(s>-1)return new y(new S(o,h,t,O),null,s)}else if(r&c.IncludeAnonymous||!h.type.isAnonymous||k(h)){let a;if(!(r&c.IgnoreMounts)&&(a=s.get(h))&&!a.overlay)return new g(a.tree,O,t,o);let l=new g(h,O,t,o);return r&c.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(e<0?h.children.length-1:0,e,i,n)}}if(r&c.IncludeAnonymous||!o.type.isAnonymous)return null;if(t=o.index>=0?o.index+e:e<0?-1:o._parent._tree.children.length,o=o._parent,!o)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&c.IgnoreOverlays)&&(n=s.get(this._tree))&&n.overlay){let i=t-this.from;for(let{from:t,to:s}of n.overlay)if((e>0?t<=i:t<i)&&(e<0?s>=i:s>i))return new g(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function w(t,e,i,n){let s=t.cursor(),r=[];if(!s.firstChild())return r;if(null!=i)for(;!s.type.is(i);)if(!s.nextSibling())return r;for(;;){if(null!=n&&s.type.is(n))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return null==n?r:[]}}function b(t,e,i=e.length-1){for(let n=t.parent;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(e[i]&&e[i]!=n.name)return!1;i--}}return!0}class S{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class y extends m{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return s<0?null:new y(this.context,this,s)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&c.ExcludeBuffers)return null;let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return s<0?null:new y(this.context,this,s)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new y(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new y(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,s=i.buffer[this.index+3];if(s>n){let r=i.buffer[this.index+1];t.push(i.slice(n,s,r)),e.push(0)}return new O(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function Q(t){if(!t.length)return null;let e=0,i=t[0];for(let n=1;n<t.length;n++){let s=t[n];(s.from>i.from||s.to<i.to)&&(i=s,e=n)}let n=i instanceof g&&i.index<0?null:i.parent,s=t.slice();return n?s[e]=n:s.splice(e,1),new v(s,i)}class v{constructor(t,e){this.heads=t,this.node=e}get next(){return Q(this.heads)}}class x{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof g)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let e=t._parent;e;e=e._parent)this.stack.unshift(e.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return!!t&&(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0)}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return!!t&&(t instanceof g?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return!(s<0)&&(this.stack.push(this.index),this.yieldBuf(s))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?!(i&c.ExcludeBuffers)&&this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&c.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&c.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode));let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let t=i<0?0:this.stack[i]+4;if(this.index!=t)return this.yieldBuf(e.findChild(t,this.index,-1,0,4))}else{let t=e.buffer[this.index+3];if(t<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(t)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let t=0;t<this.index;t++)if(n.buffer.buffer[t+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;({index:e,_parent:i}=i))if(e>-1)for(let n=e+t,s=t<0?-1:i._tree.children.length;n!=s;n+=t){let t=i._tree.children[n];if(this.mode&c.IncludeAnonymous||t instanceof f||!t.type.isAnonymous||k(t))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,s=this.stack.length;s>=0;){for(let r=t;r;r=r._parent)if(r.index==n){if(n==this.index)return r;e=r,i=s+1;break t}n=this.stack[--s]}for(let t=i;t<this.stack.length;t++)e=new y(this.buffer,e,this.stack[t]);return this.bufferNode=new y(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==t(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;n&&e&&e(this),n=this.type.isAnonymous,!this.nextSibling();){if(!i)return;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return b(this.node,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,s=this.stack.length-1;n>=0;s--){if(s<0)return b(this.node,t,n);let r=i[e.buffer[this.stack[s]]];if(!r.isAnonymous){if(t[n]&&t[n]!=r.name)return!1;n--}}return!0}}function k(t){return t.children.some((t=>t instanceof f||!t.type.isAnonymous||k(t)))}const $=new WeakMap;function P(t,e){if(!t.isAnonymous||e instanceof f||e.type!=t)return 1;let i=$.get(e);if(null==i){i=1;for(let n of e.children){if(n.type!=t||!(n instanceof O)){i=1;break}i+=P(t,n)}$.set(e,i)}return i}function Z(t,e,i,n,s,r,o,a,l){let h=0;for(let i=n;i<s;i++)h+=P(t,e[i]);let c=Math.ceil(1.5*h/8),O=[],u=[];return function e(i,n,s,o,a){for(let h=s;h<o;){let s=h,f=n[h],d=P(t,i[h]);for(h++;h<o;h++){let e=P(t,i[h]);if(d+e>=c)break;d+=e}if(h==s+1){if(d>c){let t=i[s];e(t.children,t.positions,0,t.children.length,n[s]+a);continue}O.push(i[s])}else{let e=n[h-1]+i[h-1].length-f;O.push(Z(t,i,n,s,h,f,e,null,l))}u.push(f+a-r)}}(e,i,n,s,0),(a||l)(O,u,o)}class T{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof y?this.setBuffer(t.context.buffer,t.index,e):t instanceof g&&this.map.set(t.tree,e)}get(t){return t instanceof y?this.getBuffer(t.context.buffer,t.index):t instanceof g?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class X{constructor(t,e,i,n,s=!1,r=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(s?1:0)|(r?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(t,e=[],i=!1){let n=[new X(0,t.length,t,0,!1,i)];for(let i of e)i.to>t.length&&n.push(i);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],s=1,r=t.length?t[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<e.length?e[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;r&&r.from<c;){let e=r;if(a>=e.from||c<=e.to||l){let t=Math.max(e.from,a)-l,i=Math.min(e.to,c)-l;e=t>=i?null:new X(t,i,e.tree,e.offset+l,o>0,!!h)}if(e&&n.push(e),r.to>c)break;r=s<t.length?t[s++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return n}}class C{startParse(t,e,n){return"string"==typeof t&&(t=new Y(t)),n=n?n.length?n.map((t=>new i(t.from,t.to))):[new i(0,0)]:[new i(0,t.length)],this.createParse(t,e||[],n)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let t=n.advance();if(t)return t}}}class Y{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function A(t){return(e,i,n,s)=>new q(e,t,i,n,s)}class R{constructor(t,e,i,n,s){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=s}}function W(t){if(!t.length||t.some((t=>t.from>=t.to)))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(t))}class V{constructor(t,e,i,n,s,r,o){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=s,this.target=r,this.prev=o,this.depth=0,this.ranges=[]}}const j=new n({perNode:!0});class q{constructor(t,e,i,n,s){this.nest=e,this.input=i,this.fragments=n,this.ranges=s,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let t=this.baseParse.advance();if(!t)return null;if(this.baseParse=null,this.baseTree=t,this.startInner(),null!=this.stoppedAt)for(let t of this.inner)t.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let t=this.baseTree;return null!=this.stoppedAt&&(t=new O(t.type,t.children,t.positions,t.length,t.propValues.concat([[j,this.stoppedAt]]))),t}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[n.mounted.id]=new s(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new z(this.fragments),e=null,n=null,s=new x(new g(this.baseTree,this.ranges[0].from,0,null),c.IncludeAnonymous|c.IgnoreMounts);t:for(let r,o;;){let a,l=!0;if(null!=this.stoppedAt&&s.from>=this.stoppedAt)l=!1;else if(t.hasNode(s)){if(e){let t=e.mounts.find((t=>t.frag.from<=s.from&&t.frag.to>=s.to&&t.mount.overlay));if(t)for(let i of t.mount.overlay){let n=i.from+t.pos,r=i.to+t.pos;n>=s.from&&r<=s.to&&!e.ranges.some((t=>t.from<r&&t.to>n))&&e.ranges.push({from:n,to:r})}}l=!1}else if(n&&(o=_(n.ranges,s.from,s.to)))l=2!=o;else if(!s.type.isAnonymous&&(r=this.nest(s,this.input))&&(s.from<s.to||!r.overlay)){s.tree||M(s);let o=t.findMounts(s.from,r.parser);if("function"==typeof r.overlay)e=new V(r.parser,r.overlay,o,this.inner.length,s.from,s.tree,e);else{let t=G(this.ranges,r.overlay||(s.from<s.to?[new i(s.from,s.to)]:[]));t.length&&W(t),!t.length&&r.overlay||this.inner.push(new R(r.parser,t.length?r.parser.startParse(this.input,B(o,t),t):r.parser.startParse(""),r.overlay?r.overlay.map((t=>new i(t.from-s.from,t.to-s.from))):null,s.tree,t.length?t[0].from:s.from)),r.overlay?t.length&&(n={ranges:t,depth:0,prev:n}):l=!1}}else e&&(a=e.predicate(s))&&(!0===a&&(a=new i(s.from,s.to)),a.from<a.to&&e.ranges.push(a));if(l&&s.firstChild())e&&e.depth++,n&&n.depth++;else for(;!s.nextSibling();){if(!s.parent())break t;if(e&&! --e.depth){let t=G(this.ranges,e.ranges);t.length&&(W(t),this.inner.splice(e.index,0,new R(e.parser,e.parser.startParse(this.input,B(e.mounts,t),t),e.ranges.map((t=>new i(t.from-e.start,t.to-e.start))),e.target,t[0].from))),e=e.prev}n&&! --n.depth&&(n=n.prev)}}}}function _(t,e,i){for(let n of t){if(n.from>=i)break;if(n.to>e)return n.from<=e&&n.to>=i?2:1}return 0}function E(t,e,i,n,s,r){if(e<i){let o=t.buffer[e+1];n.push(t.slice(e,i,o)),s.push(o-r)}}function M(t){let{node:e}=t,i=[],n=e.context.buffer;do{i.push(t.index),t.parent()}while(!t.tree);let s=t.tree,r=s.children.indexOf(n),a=s.children[r],l=a.buffer,h=[r];s.children[r]=function t(n,s,r,o,c,u){let f=i[u],d=[],p=[];E(a,n,f,d,p,o);let m=l[f+1],g=l[f+2];h.push(d.length);let w=u?t(f+4,l[f+3],a.set.types[l[f]],m,g-m,u-1):e.toTree();return d.push(w),p.push(m-o),E(a,l[f+3],s,d,p,o),new O(r,d,p,c)}(0,l.length,o.none,0,a.length,i.length-1);for(let e of h){let i=t.tree.children[e],n=t.tree.positions[e];t.yield(new g(i,n+t.from,e,t._tree))}}class D{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(c.IncludeAnonymous|c.IgnoreMounts)}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,c.IgnoreOverlays|c.ExcludeBuffers)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(!(e.children.length&&0==e.positions[0]&&e.children[0]instanceof O))break;e=e.children[0]}return!1}}class z{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=null!==(e=i.tree.prop(j))&&void 0!==e?e:i.to,this.inner=new D(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=null!==(t=e.tree.prop(j))&&void 0!==t?t:e.to,this.inner=new D(e.tree,-e.offset)}}findMounts(t,e){var i;let s=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let t=this.inner.cursor.node;t;t=t.parent){let r=null===(i=t.tree)||void 0===i?void 0:i.prop(n.mounted);if(r&&r.parser==e)for(let e=this.fragI;e<this.fragments.length;e++){let i=this.fragments[e];if(i.from>=t.to)break;i.tree==this.curFrag.tree&&s.push({frag:i,pos:t.from-i.offset,mount:r})}}}return s}}function G(t,e){let n=null,s=e;for(let r=1,o=0;r<t.length;r++){let a=t[r-1].to,l=t[r].from;for(;o<s.length;o++){let t=s[o];if(t.from>=l)break;t.to<=a||(n||(s=n=e.slice()),t.from<a?(n[o]=new i(t.from,a),t.to>l&&n.splice(o+1,0,new i(l,t.to))):t.to>l?n[o--]=new i(l,t.to):n.splice(o--,1))}}return s}function I(t,e,n,s){let r=0,o=0,a=!1,l=!1,h=-1e9,c=[];for(;;){let O=r==t.length?1e9:a?t[r].to:t[r].from,u=o==e.length?1e9:l?e[o].to:e[o].from;if(a!=l){let t=Math.max(h,n),e=Math.min(O,u,s);t<e&&c.push(new i(t,e))}if(h=Math.min(O,u),1e9==h)break;O==h&&(a?(a=!1,r++):a=!0),u==h&&(l?(l=!1,o++):l=!0)}return c}function B(t,e){let n=[];for(let{pos:s,mount:r,frag:o}of t){let t=s+(r.overlay?r.overlay[0].from:0),a=t+r.tree.length,l=Math.max(o.from,t),h=Math.min(o.to,a);if(r.overlay){let a=I(e,r.overlay.map((t=>new i(t.from+s,t.to+s))),l,h);for(let e=0,i=l;;e++){let s=e==a.length,l=s?h:a[e].from;if(l>i&&n.push(new X(i,l,r.tree,-t,o.from>=i||o.openStart,o.to<=l||o.openEnd)),s)break;i=a[e].to}}else n.push(new X(l,h,r.tree,-t,o.from>=t||o.openStart,o.to<=a||o.openEnd))}return n}class N{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=it(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),L.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=it(this,t,e);let i=[];return this.decompose(t,e,i,0),L.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new J(this),s=new J(t);for(let t=e,r=e;;){if(n.next(t),s.next(t),t=0,n.lineBreak!=s.lineBreak||n.done!=s.done||n.value!=s.value)return!1;if(r+=n.value.length,n.done||r>=i)return!0}}iter(t=1){return new J(this,t)}iterRange(t,e=this.length){return new K(this,t,e)}iterLines(t,e){let i;if(null==t)i=this.iter();else{null==e&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new tt(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new U(t):L.from(U.split(t,[])):N.empty}}class U extends N{constructor(t,e=function(t){let e=-1;for(let i of t)e+=i.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.text[s],o=n+r.length;if((e?i:o)>=t)return new et(n,o,i,r);n=o+1,i++}}decompose(t,e,i,n){let s=t<=0&&e>=this.length?this:new U(H(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&n){let t=i.pop(),e=F(s.text,t.text.slice(),0,s.length);if(e.length<=32)i.push(new U(e,t.length+s.length));else{let t=e.length>>1;i.push(new U(e.slice(0,t)),new U(e.slice(t)))}}else i.push(s)}replace(t,e,i){if(!(i instanceof U))return super.replace(t,e,i);[t,e]=it(this,t,e);let n=F(this.text,F(i.text,H(this.text,0,t)),e),s=this.length+i.length-(e-t);return n.length<=32?new U(n,s):L.from(U.split(n,[]),s)}sliceString(t,e=this.length,i="\n"){[t,e]=it(this,t,e);let n="";for(let s=0,r=0;s<=e&&r<this.text.length;r++){let o=this.text[r],a=s+o.length;s>t&&r&&(n+=i),t<a&&e>s&&(n+=o.slice(Math.max(0,t-s),e-s)),s=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let s of t)i.push(s),n+=s.length+1,32==i.length&&(e.push(new U(i,n)),i=[],n=-1);return n>-1&&e.push(new U(i,n)),e}}class L extends N{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let e of t)this.lines+=e.lines}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.children[s],o=n+r.length,a=i+r.lines-1;if((e?a:o)>=t)return r.lineInner(t,e,i,n);n=o+1,i=a+1}}decompose(t,e,i,n){for(let s=0,r=0;r<=e&&s<this.children.length;s++){let o=this.children[s],a=r+o.length;if(t<=a&&e>=r){let s=n&((r<=t?1:0)|(a>=e?2:0));r>=t&&a<=e&&!s?i.push(o):o.decompose(t-r,e-r,i,s)}r=a+1}}replace(t,e,i){if([t,e]=it(this,t,e),i.lines<this.lines)for(let n=0,s=0;n<this.children.length;n++){let r=this.children[n],o=s+r.length;if(t>=s&&e<=o){let a=r.replace(t-s,e-s,i),l=this.lines-r.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let s=this.children.slice();return s[n]=a,new L(s,this.length-(e-t)+i.length)}return super.replace(s,o,a)}s=o+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i="\n"){[t,e]=it(this,t,e);let n="";for(let s=0,r=0;s<this.children.length&&r<=e;s++){let o=this.children[s],a=r+o.length;r>t&&s&&(n+=i),t<a&&e>r&&(n+=o.sliceString(t-r,e-r,i)),r=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof L))return 0;let i=0,[n,s,r,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,s+=e){if(n==r||s==o)return i;let a=this.children[n],l=t.children[s];if(a!=l)return i+a.scanIdentical(l,e);i+=a.length+1}}static from(t,e=t.reduce(((t,e)=>t+e.length+1),-1)){let i=0;for(let e of t)i+=e.lines;if(i<32){let i=[];for(let e of t)e.flatten(i);return new U(i,e)}let n=Math.max(32,i>>5),s=n<<1,r=n>>1,o=[],a=0,l=-1,h=[];function c(t){let e;if(t.lines>s&&t instanceof L)for(let e of t.children)c(e);else t.lines>r&&(a>r||!a)?(O(),o.push(t)):t instanceof U&&a&&(e=h[h.length-1])instanceof U&&t.lines+e.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new U(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>n&&O(),a+=t.lines,l+=t.length+1,h.push(t))}function O(){0!=a&&(o.push(1==h.length?h[0]:L.from(h,l)),l=-1,a=h.length=0)}for(let e of t)c(e);return O(),1==o.length?o[0]:new L(o,e)}}function F(t,e,i=0,n=1e9){for(let s=0,r=0,o=!0;r<t.length&&s<=n;r++){let a=t[r],l=s+a.length;l>=i&&(l>n&&(a=a.slice(0,n-s)),s<i&&(a=a.slice(i-s)),o?(e[e.length-1]+=a,o=!1):e.push(a)),s=l+1}return e}function H(t,e,i){return F(t,[""],e,i)}N.empty=new U([""],0);class J{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof U?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],s=this.offsets[i],r=s>>1,o=n instanceof U?n.text.length:n.children.length;if(r==(e>0?o:0)){if(0==i)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&s)==(e>0?0:1)){if(this.offsets[i]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(n instanceof U){let s=n.text[r+(e<0?-1:0)];if(this.offsets[i]+=e,s.length>Math.max(0,t))return this.value=0==t?s:e>0?s.slice(t):s.slice(0,s.length-t),this;t-=s.length}else{let s=n.children[r+(e<0?-1:0)];t>s.length?(t-=s.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(s),this.offsets.push(e>0?1:(s instanceof U?s.text.length:s.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class K{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new J(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class tt{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(N.prototype[Symbol.iterator]=function(){return this.iter()},J.prototype[Symbol.iterator]=K.prototype[Symbol.iterator]=tt.prototype[Symbol.iterator]=function(){return this});class et{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function it(t,e,i){return[e=Math.max(0,Math.min(t.length,e)),Math.max(e,Math.min(t.length,i))]}let nt="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map((t=>t?parseInt(t,36):1));for(let t=1;t<nt.length;t++)nt[t]+=nt[t-1];function st(t){for(let e=1;e<nt.length;e+=2)if(nt[e]>t)return nt[e-1]<=t;return!1}function rt(t){return t>=127462&&t<=127487}const ot=8205;function at(t,e,i=!0,n=!0){return(i?lt:ht)(t,e,n)}function lt(t,e,i){if(e==t.length)return e;e&&ct(t.charCodeAt(e))&&Ot(t.charCodeAt(e-1))&&e--;let n=ut(t,e);for(e+=dt(n);e<t.length;){let s=ut(t,e);if(n==ot||s==ot||i&&st(s))e+=dt(s),n=s;else{if(!rt(s))break;{let i=0,n=e-2;for(;n>=0&&rt(ut(t,n));)i++,n-=2;if(i%2==0)break;e+=2}}}return e}function ht(t,e,i){for(;e>0;){let n=lt(t,e-2,i);if(n<e)return n;e--}return 0}function ct(t){return t>=56320&&t<57344}function Ot(t){return t>=55296&&t<56320}function ut(t,e){let i=t.charCodeAt(e);if(!Ot(i)||e+1==t.length)return i;let n=t.charCodeAt(e+1);return ct(n)?n-56320+(i-55296<<10)+65536:i}function ft(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function dt(t){return t<65536?1:2}const pt=/\r\n?|\n/;var mt=function(t){return t[t.Simple=0]="Simple",t[t.TrackDel=1]="TrackDel",t[t.TrackBefore=2]="TrackBefore",t[t.TrackAfter=3]="TrackAfter",t}(mt||(mt={}));class gt{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let s=this.sections[e++],r=this.sections[e++];r<0?(t(i,n,s),n+=s):n+=r,i+=s}}iterChangedRanges(t,e=!1){yt(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new gt(t)}composeDesc(t){return this.empty?t:t.empty?this:vt(this,t)}mapDesc(t,e=!1){return t.empty?this:Qt(this,t,e)}mapPos(t,e=-1,i=mt.Simple){let n=0,s=0;for(let r=0;r<this.sections.length;){let o=this.sections[r++],a=this.sections[r++],l=n+o;if(a<0){if(l>t)return s+(t-n);s+=o}else{if(i!=mt.Simple&&l>=t&&(i==mt.TrackDel&&n<t&&l>t||i==mt.TrackBefore&&n<t||i==mt.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!o)return t==n||e<0?s:s+a;s+=a}n=l}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return s}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let s=n+this.sections[i++];if(this.sections[i++]>=0&&n<=e&&s>=t)return!(n<t&&s>e)||"cover";n=s}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some((t=>"number"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeDesc");return new gt(t)}static create(t){return new gt(t)}}class wt extends gt{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return yt(this,((e,i,n,s,r)=>t=t.replace(n,n+(i-e),r)),!1),t}mapDesc(t,e=!1){return Qt(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,s=0;n<e.length;n+=2){let r=e[n],o=e[n+1];if(o>=0){e[n]=o,e[n+1]=r;let a=n>>1;for(;i.length<a;)i.push(N.empty);i.push(r?t.slice(s,s+r):N.empty)}s+=r}return new wt(e,i)}compose(t){return this.empty?t:t.empty?this:vt(this,t,!0)}map(t,e=!1){return t.empty?this:Qt(this,t,e,!0)}iterChanges(t,e=!1){yt(this,t,e)}get desc(){return gt.create(this.sections)}filter(t){let e=[],i=[],n=[],s=new xt(this);t:for(let r=0,o=0;;){let a=r==t.length?1e9:t[r++];for(;o<a||o==a&&0==s.len;){if(s.done)break t;let t=Math.min(s.len,a-o);bt(n,t,-1);let r=-1==s.ins?-1:0==s.off?s.ins:0;bt(e,t,r),r>0&&St(i,e,s.text),s.forward(t),o+=t}let l=t[r++];for(;o<l;){if(s.done)break t;let t=Math.min(s.len,l-o);bt(e,t,-1),bt(n,t,-1==s.ins?-1:0==s.off?s.ins:0),s.forward(t),o+=t}}return{changes:new wt(e,i),filtered:gt.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):0==n?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],s=[],r=0,o=null;function a(t=!1){if(!t&&!n.length)return;r<e&&bt(n,e-r,-1);let i=new wt(n,s);o=o?o.compose(i.map(o)):i,n=[],s=[],r=0}return function t(l){if(Array.isArray(l))for(let e of l)t(e);else if(l instanceof wt){if(l.length!=e)throw new RangeError(`Mismatched change set length (got ${l.length}, expected ${e})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:t,to:o=t,insert:h}=l;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let c=h?"string"==typeof h?N.of(h.split(i||pt)):h:N.empty,O=c.length;if(t==o&&0==O)return;t<r&&a(),t>r&&bt(n,t-r,-1),bt(n,o-t,O),St(s,n,c),r=o}}(t),a(!o),o}static empty(t){return new wt(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let s=t[n];if("number"==typeof s)e.push(s,-1);else{if(!Array.isArray(s)||"number"!=typeof s[0]||s.some(((t,e)=>e&&"string"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==s.length)e.push(s[0],0);else{for(;i.length<n;)i.push(N.empty);i[n]=N.of(s.slice(1)),e.push(s[0],i[n].length)}}}return new wt(e,i)}static createSet(t,e){return new wt(t,e)}}function bt(t,e,i,n=!1){if(0==e&&i<=0)return;let s=t.length-2;s>=0&&i<=0&&i==t[s+1]?t[s]+=e:0==e&&0==t[s]?t[s+1]+=i:n?(t[s]+=e,t[s+1]+=i):t.push(e,i)}function St(t,e,i){if(0==i.length)return;let n=e.length-2>>1;if(n<t.length)t[t.length-1]=t[t.length-1].append(i);else{for(;t.length<n;)t.push(N.empty);t.push(i)}}function yt(t,e,i){let n=t.inserted;for(let s=0,r=0,o=0;o<t.sections.length;){let a=t.sections[o++],l=t.sections[o++];if(l<0)s+=a,r+=a;else{let h=s,c=r,O=N.empty;for(;h+=a,c+=l,l&&n&&(O=O.append(n[o-2>>1])),!(i||o==t.sections.length||t.sections[o+1]<0);)a=t.sections[o++],l=t.sections[o++];e(s,h,r,c,O),s=h,r=c}}}function Qt(t,e,i,n=!1){let s=[],r=n?[]:null,o=new xt(t),a=new xt(e);for(let t=-1;;)if(-1==o.ins&&-1==a.ins){let t=Math.min(o.len,a.len);bt(s,t,-1),o.forward(t),a.forward(t)}else if(a.ins>=0&&(o.ins<0||t==o.i||0==o.off&&(a.len<o.len||a.len==o.len&&!i))){let e=a.len;for(bt(s,a.ins,-1);e;){let i=Math.min(o.len,e);o.ins>=0&&t<o.i&&o.len<=i&&(bt(s,0,o.ins),r&&St(r,s,o.text),t=o.i),o.forward(i),e-=i}a.next()}else{if(!(o.ins>=0)){if(o.done&&a.done)return r?wt.createSet(s,r):gt.create(s);throw new Error("Mismatched change set lengths")}{let e=0,i=o.len;for(;i;)if(-1==a.ins){let t=Math.min(i,a.len);e+=t,i-=t,a.forward(t)}else{if(!(0==a.ins&&a.len<i))break;i-=a.len,a.next()}bt(s,e,t<o.i?o.ins:0),r&&t<o.i&&St(r,s,o.text),t=o.i,o.forward(o.len-i)}}}function vt(t,e,i=!1){let n=[],s=i?[]:null,r=new xt(t),o=new xt(e);for(let t=!1;;){if(r.done&&o.done)return s?wt.createSet(n,s):gt.create(n);if(0==r.ins)bt(n,r.len,0,t),r.next();else if(0!=o.len||o.done){if(r.done||o.done)throw new Error("Mismatched change set lengths");{let e=Math.min(r.len2,o.len),i=n.length;if(-1==r.ins){let i=-1==o.ins?-1:o.off?0:o.ins;bt(n,e,i,t),s&&i&&St(s,n,o.text)}else-1==o.ins?(bt(n,r.off?0:r.len,e,t),s&&St(s,n,r.textBit(e))):(bt(n,r.off?0:r.len,o.off?0:o.ins,t),s&&!o.off&&St(s,n,o.text));t=(r.ins>e||o.ins>=0&&o.len>e)&&(t||n.length>i),r.forward2(e),o.forward(e)}}else bt(n,0,o.ins,t),s&&St(s,n,o.text),o.next()}}class xt{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?N.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?N.empty:e[i].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class kt{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return 32&this.flags?this.to:this.from}get head(){return 32&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 8&this.flags?-1:16&this.flags?1:0}get bidiLevel(){let t=7&this.flags;return 7==t?null:t}get goalColumn(){let t=this.flags>>6;return 16777215==t?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new kt(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return $t.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return $t.range(this.anchor,i)}eq(t,e=!1){return!(this.anchor!=t.anchor||this.head!=t.head||e&&this.empty&&this.assoc!=t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return $t.range(t.anchor,t.head)}static create(t,e,i){return new kt(t,e,i)}}class $t{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:$t.create(this.ranges.map((i=>i.map(t,e))),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new $t([this.main],0)}addRange(t,e=!0){return $t.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,$t.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map((t=>t.toJSON())),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new $t(t.ranges.map((t=>kt.fromJSON(t))),t.main)}static single(t,e=t){return new $t([$t.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let s=t[n];if(s.empty?s.from<=i:s.from<i)return $t.normalized(t.slice(),e);i=s.to}return new $t(t,e)}static cursor(t,e=0,i,n){return kt.create(t,t,(0==e?0:e<0?8:16)|(null==i?7:Math.min(6,i))|(null!=n?n:16777215)<<6)}static range(t,e,i,n){let s=(null!=i?i:16777215)<<6|(null==n?7:Math.min(6,n));return e<t?kt.create(e,t,48|s):kt.create(t,e,(e>t?8:0)|s)}static normalized(t,e=0){let i=t[e];t.sort(((t,e)=>t.from-e.from)),e=t.indexOf(i);for(let i=1;i<t.length;i++){let n=t[i],s=t[i-1];if(n.empty?n.from<=s.to:n.from<s.to){let r=s.from,o=Math.max(n.to,s.to);i<=e&&e--,t.splice(--i,2,n.anchor>n.head?$t.range(o,r):$t.range(r,o))}}return new $t(t,e)}}function Pt(t,e){for(let i of t.ranges)if(i.to>e)throw new RangeError("Selection points outside of document")}let Zt=0;class Tt{constructor(t,e,i,n,s){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Zt++,this.default=t([]),this.extensions="function"==typeof s?s(this):s}get reader(){return this}static define(t={}){return new Tt(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:Xt),!!t.static,t.enables)}of(t){return new Ct([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Ct(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Ct(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],(i=>e(i.field(t))))}}function Xt(t,e){return t==e||t.length==e.length&&t.every(((t,i)=>t===e[i]))}class Ct{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Zt++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,s=this.id,r=t[s]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let i of this.dependencies)"doc"==i?a=!0:"selection"==i?l=!0:0==(1&(null!==(e=t[i.id])&&void 0!==e?e:1))&&h.push(t[i.id]);return{create:t=>(t.values[r]=i(t),1),update(t,e){if(a&&e.docChanged||l&&(e.docChanged||e.selection)||At(t,h)){let e=i(t);if(o?!Yt(e,t.values[r],n):!n(e,t.values[r]))return t.values[r]=e,1}return 0},reconfigure:(t,e)=>{let a,l=e.config.address[s];if(null!=l){let s=Ut(e,l);if(this.dependencies.every((i=>i instanceof Tt?e.facet(i)===t.facet(i):!(i instanceof Vt)||e.field(i,!1)==t.field(i,!1)))||(o?Yt(a=i(t),s,n):n(a=i(t),s)))return t.values[r]=s,0}else a=i(t);return t.values[r]=a,1}}}}function Yt(t,e,i){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!i(t[n],e[n]))return!1;return!0}function At(t,e){let i=!1;for(let n of e)1&Nt(t,n)&&(i=!0);return i}function Rt(t,e,i){let n=i.map((e=>t[e.id])),s=i.map((t=>t.type)),r=n.filter((t=>!(1&t))),o=t[e.id]>>1;function a(t){let i=[];for(let e=0;e<n.length;e++){let r=Ut(t,n[e]);if(2==s[e])for(let t of r)i.push(t);else i.push(r)}return e.combine(i)}return{create(t){for(let e of n)Nt(t,e);return t.values[o]=a(t),1},update(t,i){if(!At(t,r))return 0;let n=a(t);return e.compare(n,t.values[o])?0:(t.values[o]=n,1)},reconfigure(t,s){let r=At(t,n),l=s.config.facets[e.id],h=s.facet(e);if(l&&!r&&Xt(i,l))return t.values[o]=h,0;let c=a(t);return e.compare(c,h)?(t.values[o]=h,0):(t.values[o]=c,1)}}}const Wt=Tt.define({static:!0});class Vt{constructor(t,e,i,n,s){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=s,this.provides=void 0}static define(t){let e=new Vt(Zt++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(Wt).find((t=>t.field==this));return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,i)=>{let n=t.values[e],s=this.updateF(n,i);return this.compareF(n,s)?0:(t.values[e]=s,1)},reconfigure:(t,i)=>null!=i.config.address[this.id]?(t.values[e]=i.field(this),0):(t.values[e]=this.create(t),1)}}init(t){return[this,Wt.of({field:this,create:t})]}get extension(){return this}}const jt=4,qt=3,_t=2,Et=1;function Mt(t){return e=>new zt(e,t)}const Dt={highest:Mt(0),high:Mt(Et),default:Mt(_t),low:Mt(qt),lowest:Mt(jt)};class zt{constructor(t,e){this.inner=t,this.prec=e}}class Gt{of(t){return new It(this,t)}reconfigure(t){return Gt.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class It{constructor(t,e){this.compartment=t,this.inner=e}}class Bt{constructor(t,e,i,n,s,r){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=s,this.facets=r,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],s=Object.create(null),r=new Map;for(let i of function(t,e,i){let n=[[],[],[],[],[]],s=new Map;function r(t,o){let a=s.get(t);if(null!=a){if(a<=o)return;let e=n[a].indexOf(t);e>-1&&n[a].splice(e,1),t instanceof It&&i.delete(t.compartment)}if(s.set(t,o),Array.isArray(t))for(let e of t)r(e,o);else if(t instanceof It){if(i.has(t.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=e.get(t.compartment)||t.inner;i.set(t.compartment,n),r(n,o)}else if(t instanceof zt)r(t.inner,t.prec);else if(t instanceof Vt)n[o].push(t),t.provides&&r(t.provides,o);else if(t instanceof Ct)n[o].push(t),t.facet.extensions&&r(t.facet.extensions,_t);else{let e=t.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${t}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(e,o)}}return r(t,_t),n.reduce(((t,e)=>t.concat(e)))}(t,e,r))i instanceof Vt?n.push(i):(s[i.facet.id]||(s[i.facet.id]=[])).push(i);let o=Object.create(null),a=[],l=[];for(let t of n)o[t.id]=l.length<<1,l.push((e=>t.slot(e)));let h=null==i?void 0:i.config.facets;for(let t in s){let e=s[t],n=e[0].facet,r=h&&h[t]||[];if(e.every((t=>0==t.type)))if(o[n.id]=a.length<<1|1,Xt(r,e))a.push(i.facet(n));else{let t=n.combine(e.map((t=>t.value)));a.push(i&&n.compare(t,i.facet(n))?i.facet(n):t)}else{for(let t of e)0==t.type?(o[t.id]=a.length<<1|1,a.push(t.value)):(o[t.id]=l.length<<1,l.push((e=>t.dynamicSlot(e))));o[n.id]=l.length<<1,l.push((t=>Rt(t,n,e)))}}let c=l.map((t=>t(o)));return new Bt(t,r,c,o,a,s)}}function Nt(t,e){if(1&e)return 2;let i=e>>1,n=t.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;t.status[i]=4;let s=t.computeSlot(t,t.config.dynamicSlots[i]);return t.status[i]=2|s}function Ut(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const Lt=Tt.define(),Ft=Tt.define({combine:t=>t.some((t=>t)),static:!0}),Ht=Tt.define({combine:t=>t.length?t[0]:void 0,static:!0}),Jt=Tt.define(),Kt=Tt.define(),te=Tt.define(),ee=Tt.define({combine:t=>!!t.length&&t[0]});class ie{constructor(t,e){this.type=t,this.value=e}static define(){return new ne}}class ne{of(t){return new ie(this,t)}}class se{constructor(t){this.map=t}of(t){return new re(this,t)}}class re{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new re(this.type,e)}is(t){return this.type==t}static define(t={}){return new se(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let t=n.map(e);t&&i.push(t)}return i}}re.reconfigure=re.define(),re.appendConfig=re.define();class oe{constructor(t,e,i,n,s,r){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=s,this.scrollIntoView=r,this._doc=null,this._state=null,i&&Pt(i,e.newLength),s.some((t=>t.type==oe.time))||(this.annotations=s.concat(oe.time.of(Date.now())))}static create(t,e,i,n,s,r){return new oe(t,e,i,n,s,r)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(oe.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function ae(t,e){let i=[];for(let n=0,s=0;;){let r,o;if(n<t.length&&(s==e.length||e[s]>=t[n]))r=t[n++],o=t[n++];else{if(!(s<e.length))return i;r=e[s++],o=e[s++]}!i.length||i[i.length-1]<r?i.push(r,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function le(t,e,i){var n;let s,r,o;return i?(s=e.changes,r=wt.empty(e.changes.length),o=t.changes.compose(e.changes)):(s=e.changes.map(t.changes),r=t.changes.mapDesc(e.changes,!0),o=t.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):null===(n=t.selection)||void 0===n?void 0:n.map(s),effects:re.mapEffects(t.effects,s).concat(re.mapEffects(e.effects,r)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function he(t,e,i){let n=e.selection,s=ue(e.annotations);return e.userEvent&&(s=s.concat(oe.userEvent.of(e.userEvent))),{changes:e.changes instanceof wt?e.changes:wt.of(e.changes||[],i,t.facet(Ht)),selection:n&&(n instanceof $t?n:$t.single(n.anchor,n.head)),effects:ue(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function ce(t,e,i){let n=he(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(i=!1);for(let s=1;s<e.length;s++){!1===e[s].filter&&(i=!1);let r=!!e[s].sequential;n=le(n,he(t,e[s],r?n.changes.newLength:t.doc.length),r)}let s=oe.create(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(t){let e=t.startState,i=e.facet(te),n=t;for(let s=i.length-1;s>=0;s--){let r=i[s](t);r&&Object.keys(r).length&&(n=le(n,he(e,r,t.changes.newLength),!0))}return n==t?t:oe.create(e,t.changes,t.selection,n.effects,n.annotations,n.scrollIntoView)}(i?function(t){let e=t.startState,i=!0;for(let n of e.facet(Jt)){let e=n(t);if(!1===e){i=!1;break}Array.isArray(e)&&(i=!0===i?e:ae(i,e))}if(!0!==i){let n,s;if(!1===i)s=t.changes.invertedDesc,n=wt.empty(e.doc.length);else{let e=t.changes.filter(i);n=e.changes,s=e.filtered.mapDesc(e.changes).invertedDesc}t=oe.create(e,n,t.selection&&t.selection.map(s),re.mapEffects(t.effects,s),t.annotations,t.scrollIntoView)}let n=e.facet(Kt);for(let i=n.length-1;i>=0;i--){let s=n[i](t);t=s instanceof oe?s:Array.isArray(s)&&1==s.length&&s[0]instanceof oe?s[0]:ce(e,ue(s),!1)}return t}(s):s)}oe.time=ie.define(),oe.userEvent=ie.define(),oe.addToHistory=ie.define(),oe.remote=ie.define();const Oe=[];function ue(t){return null==t?Oe:Array.isArray(t)?t:[t]}var fe=function(t){return t[t.Word=0]="Word",t[t.Space=1]="Space",t[t.Other=2]="Other",t}(fe||(fe={}));const de=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let pe;try{pe=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(t){}function me(t){return e=>{if(!/\S/.test(e))return fe.Space;if(function(t){if(pe)return pe.test(t);for(let e=0;e<t.length;e++){let i=t[e];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||de.test(i)))return!0}return!1}(e))return fe.Word;for(let i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return fe.Word;return fe.Other}}class ge{constructor(t,e,i,n,s,r){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=s,r&&(r._state=this);for(let t=0;t<this.config.dynamicSlots.length;t++)Nt(this,t<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(null!=i)return Nt(this,i),Ut(this,i);if(e)throw new RangeError("Field is not present in this state")}update(...t){return ce(this,t,!0)}applyTransaction(t){let e,i=this.config,{base:n,compartments:s}=i;for(let e of t.effects)e.is(Gt.reconfigure)?(i&&(s=new Map,i.compartments.forEach(((t,e)=>s.set(e,t))),i=null),s.set(e.value.compartment,e.value.extension)):e.is(re.reconfigure)?(i=null,n=e.value):e.is(re.appendConfig)&&(i=null,n=ue(n).concat(e.value));if(i)e=t.startState.values.slice();else{i=Bt.resolve(n,s,this),e=new ge(i,this.doc,this.selection,i.dynamicSlots.map((()=>null)),((t,e)=>e.reconfigure(t,this)),null).values}let r=t.startState.facet(Ft)?t.newSelection:t.newSelection.asSingle();new ge(i,t.newDoc,r,e,((e,i)=>i.update(e,t)),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:t},range:$t.cursor(e.from+t.length)})))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),s=[i.range],r=ue(i.effects);for(let i=1;i<e.ranges.length;i++){let o=t(e.ranges[i]),a=this.changes(o.changes),l=a.map(n);for(let t=0;t<i;t++)s[t]=s[t].map(l);let h=n.mapDesc(a,!0);s.push(o.range.map(h)),n=n.compose(l),r=re.mapEffects(r,l).concat(re.mapEffects(ue(o.effects),h))}return{changes:n,selection:$t.create(s,e.mainIndex),effects:r}}changes(t=[]){return t instanceof wt?t:wt.of(t,this.doc.length,this.facet(ge.lineSeparator))}toText(t){return N.of(t.split(this.facet(ge.lineSeparator)||pt))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(Nt(this,e),Ut(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof Vt&&null!=this.config.address[n.id]&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let e in i)if(Object.prototype.hasOwnProperty.call(t,e)){let s=i[e],r=t[e];n.push(s.init((t=>s.spec.fromJSON(r,t))))}return ge.create({doc:t.doc,selection:$t.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=Bt.resolve(t.extensions||[],new Map),i=t.doc instanceof N?t.doc:N.of((t.doc||"").split(e.staticFacet(ge.lineSeparator)||pt)),n=t.selection?t.selection instanceof $t?t.selection:$t.single(t.selection.anchor,t.selection.head):$t.single(0);return Pt(n,i.length),e.staticFacet(Ft)||(n=n.asSingle()),new ge(e,i,n,e.dynamicSlots.map((()=>null)),((t,e)=>e.create(t)),null)}get tabSize(){return this.facet(ge.tabSize)}get lineBreak(){return this.facet(ge.lineSeparator)||"\n"}get readOnly(){return this.facet(ee)}phrase(t,...e){for(let e of this.facet(ge.phrases))if(Object.prototype.hasOwnProperty.call(e,t)){t=e[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,((t,i)=>{if("$"==i)return"$";let n=+(i||1);return!n||n>e.length?t:e[n-1]}))),t}languageDataAt(t,e,i=-1){let n=[];for(let s of this.facet(Lt))for(let r of s(this,e,i))Object.prototype.hasOwnProperty.call(r,t)&&n.push(r[t]);return n}charCategorizer(t){return me(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),s=this.charCategorizer(t),r=t-i,o=t-i;for(;r>0;){let t=at(e,r,!1);if(s(e.slice(t,r))!=fe.Word)break;r=t}for(;o<n;){let t=at(e,o);if(s(e.slice(o,t))!=fe.Word)break;o=t}return r==o?null:$t.range(r+i,o+i)}}function we(t,e,i={}){let n={};for(let e of t)for(let t of Object.keys(e)){let s=e[t],r=n[t];if(void 0===r)n[t]=s;else if(r===s||void 0===s);else{if(!Object.hasOwnProperty.call(i,t))throw new Error("Config merge conflict for field "+t);n[t]=i[t](r,s)}}for(let t in e)void 0===n[t]&&(n[t]=e[t]);return n}ge.allowMultipleSelections=Ft,ge.tabSize=Tt.define({combine:t=>t.length?t[0]:4}),ge.lineSeparator=Ht,ge.readOnly=ee,ge.phrases=Tt.define({compare(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length==n.length&&i.every((i=>t[i]==e[i]))}}),ge.languageData=Lt,ge.changeFilter=Jt,ge.transactionFilter=Kt,ge.transactionExtender=te,Gt.reconfigure=re.define();class be{eq(t){return this==t}range(t,e=t){return Se.create(t,e,this)}}be.prototype.startSide=be.prototype.endSide=0,be.prototype.point=!1,be.prototype.mapMode=mt.TrackDel;class Se{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new Se(t,e,i)}}function ye(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class Qe{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let s=i?this.to:this.from;for(let r=n,o=s.length;;){if(r==o)return r;let n=r+o>>1,a=s[n]-t||(i?this.value[n].endSide:this.value[n].startSide)-e;if(n==r)return a>=0?r:o;a>=0?o=n:r=n+1}}between(t,e,i,n){for(let s=this.findIndex(e,-1e9,!0),r=this.findIndex(i,1e9,!1,s);s<r;s++)if(!1===n(this.from[s]+t,this.to[s]+t,this.value[s]))return!1}map(t,e){let i=[],n=[],s=[],r=-1,o=-1;for(let a=0;a<this.value.length;a++){let l,h,c=this.value[a],O=this.from[a]+t,u=this.to[a]+t;if(O==u){let t=e.mapPos(O,c.startSide,c.mapMode);if(null==t)continue;if(l=h=t,c.startSide!=c.endSide&&(h=e.mapPos(O,c.endSide),h<l))continue}else if(l=e.mapPos(O,c.startSide),h=e.mapPos(u,c.endSide),l>h||l==h&&c.startSide>0&&c.endSide<=0)continue;(h-l||c.endSide-c.startSide)<0||(r<0&&(r=l),c.point&&(o=Math.max(o,h-l)),i.push(c),n.push(l-r),s.push(h-r))}return{mapped:i.length?new Qe(n,s,i,o):null,pos:r}}}class ve{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new ve(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:s=this.length}=t,r=t.filter;if(0==e.length&&!r)return this;if(i&&(e=e.slice().sort(ye)),this.isEmpty)return e.length?ve.of(e):this;let o=new $e(this,null,-1).goto(0),a=0,l=[],h=new xe;for(;o.value||a<e.length;)if(a<e.length&&(o.from-e[a].from||o.startSide-e[a].value.startSide)>=0){let t=e[a++];h.addInner(t.from,t.to,t.value)||l.push(t)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(o.chunkIndex)<e[a].from)&&(!r||n>this.chunkEnd(o.chunkIndex)||s<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!r||n>o.to||s<o.from||r(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||l.push(Se.create(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?ve.empty:this.nextLayer.update({add:l,filter:r,filterFrom:n,filterTo:s}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s],a=t.touchesRange(r,r+o.length);if(!1===a)n=Math.max(n,o.maxPoint),e.push(o),i.push(t.mapPos(r));else if(!0===a){let{mapped:s,pos:a}=o.map(r,t);s&&(n=Math.max(n,s.maxPoint),e.push(s),i.push(a))}}let s=this.nextLayer.map(t);return 0==e.length?s:new ve(i,e,s||ve.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let s=this.chunkPos[n],r=this.chunk[n];if(e>=s&&t<=s+r.length&&!1===r.between(s,t-s,e-s,i))return}this.nextLayer.between(t,e,i)}}iter(t=0){return Pe.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Pe.from(t).goto(e)}static compare(t,e,i,n,s=-1){let r=t.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),o=e.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),a=ke(r,o,i),l=new Te(r,a,s),h=new Te(o,a,s);i.iterGaps(((t,e,i)=>Xe(l,t,h,e,i,n))),i.empty&&0==i.length&&Xe(l,0,h,0,0,n)}static eq(t,e,i=0,n){null==n&&(n=999999999);let s=t.filter((t=>!t.isEmpty&&e.indexOf(t)<0)),r=e.filter((e=>!e.isEmpty&&t.indexOf(e)<0));if(s.length!=r.length)return!1;if(!s.length)return!0;let o=ke(s,r),a=new Te(s,o,0).goto(i),l=new Te(r,o,0).goto(i);for(;;){if(a.to!=l.to||!Ce(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>n)return!0;a.next(),l.next()}}static spans(t,e,i,n,s=-1){let r=new Te(t,null,s).goto(e),o=e,a=r.openStart;for(;;){let t=Math.min(r.to,i);if(r.point){let i=r.activeForPoint(r.to),s=r.pointFrom<e?i.length+1:Math.min(i.length,a);n.point(o,t,r.point,i,s,r.pointRank),a=Math.min(r.openEnd(t),i.length)}else t>o&&(n.span(o,t,r.active,a),a=r.openEnd(t));if(r.to>i)return a+(r.point&&r.to>i?1:0);o=r.to,r.next()}}static of(t,e=!1){let i=new xe;for(let n of t instanceof Se?[t]:e?function(t){if(t.length>1)for(let e=t[0],i=1;i<t.length;i++){let n=t[i];if(ye(e,n)>0)return t.slice().sort(ye);e=n}return t}(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return ve.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=ve.empty;n=n.nextLayer)e=new ve(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}ve.empty=new ve([],[],null,-1),ve.empty.nextLayer=ve.empty;class xe{finishChunk(t){this.chunks.push(new Qe(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new xe)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0)&&(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(ve.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=ve.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function ke(t,e,i){let n=new Map;for(let e of t)for(let t=0;t<e.chunk.length;t++)e.chunk[t].maxPoint<=0&&n.set(e.chunk[t],e.chunkPos[t]);let s=new Set;for(let t of e)for(let e=0;e<t.chunk.length;e++){let r=n.get(t.chunk[e]);null==r||(i?i.mapPos(r):r)!=t.chunkPos[e]||(null==i?void 0:i.touchesRange(r,r+t.chunk[e].length))||s.add(t.chunk[e])}return s}class $e{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Pe{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let s=0;s<t.length;s++)for(let r=t[s];!r.isEmpty;r=r.nextLayer)r.maxPoint>=i&&n.push(new $e(r,e,i,s));return 1==n.length?n[0]:new Pe(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let t=this.heap.length>>1;t>=0;t--)Ze(this.heap,t);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let t=this.heap.length>>1;t>=0;t--)Ze(this.heap,t);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Ze(this.heap,0)}}}function Ze(t,e){for(let i=t[e];;){let n=1+(e<<1);if(n>=t.length)break;let s=t[n];if(n+1<t.length&&s.compare(t[n+1])>=0&&(s=t[n+1],n++),i.compare(s)<0)break;t[n]=i,t[e]=s,e=n}}class Te{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Pe.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){Ye(this.active,t),Ye(this.activeTo,t),Ye(this.activeRank,t),this.minActive=Re(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:s}=this.cursor;for(;e<this.activeRank.length&&(s-this.activeRank[e]||n-this.activeTo[e])>0;)e++;Ae(this.active,e,i),Ae(this.activeTo,e,n),Ae(this.activeRank,e,s),t&&Ae(t,e,this.cursor.from),this.minActive=Re(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&Ye(i,n)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let t=this.cursor.value;if(t.point){if(!(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=t,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=t.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){this.openStart=0;for(let e=i.length-1;e>=0&&i[e]<t;e--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Xe(t,e,i,n,s,r){t.goto(e),i.goto(n);let o=n+s,a=n,l=n-e;for(;;){let e=t.to+l-i.to||t.endSide-i.endSide,n=e<0?t.to+l:i.to,s=Math.min(n,o);if(t.point||i.point?t.point&&i.point&&(t.point==i.point||t.point.eq(i.point))&&Ce(t.activeForPoint(t.to),i.activeForPoint(i.to))||r.comparePoint(a,s,t.point,i.point):s>a&&!Ce(t.active,i.active)&&r.compareRange(a,s,t.active,i.active),n>o)break;a=n,e<=0&&t.next(),e>=0&&i.next()}}function Ce(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!=e[i]&&!t[i].eq(e[i]))return!1;return!0}function Ye(t,e){for(let i=e,n=t.length-1;i<n;i++)t[i]=t[i+1];t.pop()}function Ae(t,e,i){for(let i=t.length-1;i>=e;i--)t[i+1]=t[i];t[e]=i}function Re(t,e){let i=-1,n=1e9;for(let s=0;s<e.length;s++)(e[s]-n||t[s].endSide-t[i].endSide)<0&&(i=s,n=e[s]);return i}function We(t,e,i=t.length){let n=0;for(let s=0;s<i;)9==t.charCodeAt(s)?(n+=e-n%e,s++):(n++,s=at(t,s));return n}const Ve="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),je="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),qe="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class _e{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function s(t,e,r,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return r.push(t[0]+";");for(let i in e){let o=e[i];if(/&/.test(i))s(i.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),o,r);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+i+") should be a primitive value.");s(n(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+o+";")}(a.length||h)&&r.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}for(let e in t)s(n(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=qe[Ve]||1;return qe[Ve]=t+1,"ͼ"+t.toString(36)}static mount(t,e,i){let n=t[je],s=i&&i.nonce;n?s&&n.setNonce(s):n=new Me(t,s),n.mount(Array.isArray(e)?e:[e])}}let Ee=new Map;class Me{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let e=Ee.get(i);if(e)return t.adoptedStyleSheets=[e.sheet,...t.adoptedStyleSheets],t[je]=e;this.sheet=new n.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],Ee.set(i,this)}else{this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let n=t.head||t;n.insertBefore(this.styleTag,n.firstChild)}this.modules=[],t[je]=this}mount(t){let e=this.sheet,i=0,n=0;for(let s=0;s<t.length;s++){let r=t[s],o=this.modules.indexOf(r);if(o<n&&o>-1&&(this.modules.splice(o,1),n--,o=-1),-1==o){if(this.modules.splice(n++,0,r),e)for(let t=0;t<r.rules.length;t++)e.insertRule(r.rules[t],i++)}else{for(;n<o;)i+=this.modules[n++].rules.length;i+=r.rules.length,n++}}if(!e){let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}for(var De={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},ze={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Ge="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Ie="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Be=0;Be<10;Be++)De[48+Be]=De[96+Be]=String(Be);for(Be=1;Be<=24;Be++)De[Be+111]="F"+Be;for(Be=65;Be<=90;Be++)De[Be]=String.fromCharCode(Be+32),ze[Be]=String.fromCharCode(Be);for(var Ne in De)ze.hasOwnProperty(Ne)||(ze[Ne]=De[Ne]);function Ue(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function Le(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function Fe(t,e){if(!e.anchorNode)return!1;try{return Le(t,e.anchorNode)}catch(t){return!1}}function He(t){return 3==t.nodeType?hi(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function Je(t,e,i,n){return!!i&&(ti(t,e,i,n,-1)||ti(t,e,i,n,1))}function Ke(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function ti(t,e,i,n,s){for(;;){if(t==i&&e==n)return!0;if(e==(s<0?0:ei(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=Ke(t)+(s<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(s<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=s<0?ei(t):0}}}function ei(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function ii(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function ni(t){return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}function si(t,e){let i=e.width/t.offsetWidth,n=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.width-t.offsetWidth)<1)&&(i=1),(n>.995&&n<1.005||!isFinite(n)||Math.abs(e.height-t.offsetHeight)<1)&&(n=1),{scaleX:i,scaleY:n}}class ri{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?ei(e):0),i,Math.min(t.focusOffset,i?ei(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let oi,ai=null;function li(t){if(t.setActive)return t.setActive();if(ai)return t.focus(ai);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==ai?{get preventScroll(){return ai={preventScroll:!0},!0}}:void 0),!ai){ai=!1;for(let t=0;t<e.length;){let i=e[t++],n=e[t++],s=e[t++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=s&&(i.scrollLeft=s)}}}function hi(t,e,i=e){let n=oi||(oi=document.createRange());return n.setEnd(t,i),n.setStart(t,e),n}function ci(t,e,i){let n={key:e,code:e,keyCode:i,which:i,cancelable:!0},s=new KeyboardEvent("keydown",n);s.synthetic=!0,t.dispatchEvent(s);let r=new KeyboardEvent("keyup",n);return r.synthetic=!0,t.dispatchEvent(r),s.defaultPrevented||r.defaultPrevented}function Oi(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function ui(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}class fi{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new fi(t.parentNode,Ke(t),e)}static after(t,e){return new fi(t.parentNode,Ke(t)+1,e)}}const di=[];class pi{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i,n=this.dom,s=null;for(let r of this.children){if(7&r.flags){if(!r.dom&&(i=s?s.nextSibling:n.firstChild)){let t=pi.get(i);(!t||!t.parent&&t.canReuseDOM(r))&&r.reuseDOM(i)}r.sync(t,e),r.flags&=-8}if(i=s?s.nextSibling:n.firstChild,e&&!e.written&&e.node==n&&i!=r.dom&&(e.written=!0),r.dom.parentNode==n)for(;i&&i!=r.dom;)i=mi(i);else n.insertBefore(r.dom,i);s=r.dom}for(i=s?s.nextSibling:n.firstChild,i&&e&&e.node==n&&(e.written=!0);i;)i=mi(i)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=0==ei(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==n&&e.firstChild!=e.lastChild&&(n=t==e.firstChild?-1:1),t=e}i=n<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!pi.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let n=this.children[t];if(n.dom==i)return e;e+=n.length+n.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,s=-1,r=-1,o=-1;for(let a=0,l=i,h=i;a<this.children.length;a++){let i=this.children[a],c=l+i.length;if(l<t&&c>e)return i.domBoundsAround(t,e,l);if(c>=t&&-1==n&&(n=a,s=l),l>e&&i.dom.parentNode==this.dom){r=a,o=h;break}h=c,l=c+i.breakAfter}return{from:s,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:r<this.children.length&&r>=0?this.children[r].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=di){this.markDirty();for(let n=t;n<e;n++){let t=this.children[n];t.parent==this&&i.indexOf(t)<0&&t.destroy()}this.children.splice(t,e-t,...i);for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new gi(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,s,r){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!(8&(this.flags|t.flags))}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function mi(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}pi.prototype.breakAfter=0;class gi{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function wi(t,e,i,n,s,r,o,a,l){let{children:h}=t,c=h.length?h[e]:null,O=r.length?r[r.length-1]:null,u=O?O.breakAfter:o;if(!(e==n&&c&&!o&&!u&&r.length<2&&c.merge(i,s,r.length?O:null,0==i,a,l))){if(n<h.length){let t=h[n];t&&(s<t.length||t.breakAfter&&(null==O?void 0:O.breakAfter))?(e==n&&(t=t.split(s),s=0),!u&&O&&t.merge(0,s,O,!0,0,l)?r[r.length-1]=t:((s||t.children.length&&!t.children[0].length)&&t.merge(0,s,null,!1,0,l),r.push(t))):(null==t?void 0:t.breakAfter)&&(O?O.breakAfter=1:o=1),n++}for(c&&(c.breakAfter=o,i>0&&(!o&&r.length&&c.merge(i,c.length,r[0],!1,a,0)?c.breakAfter=r.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,a,0),e++));e<n&&r.length;)if(h[n-1].become(r[r.length-1]))n--,r.pop(),l=r.length?0:a;else{if(!h[e].become(r[0]))break;e++,r.shift(),a=r.length?0:l}!r.length&&e&&n<h.length&&!h[e-1].breakAfter&&h[n].merge(0,0,h[e-1],!1,a,l)&&e--,(e<n||r.length)&&t.replaceChildren(e,n,r)}}function bi(t,e,i,n,s,r){let o=t.childCursor(),{i:a,off:l}=o.findPos(i,1),{i:h,off:c}=o.findPos(e,-1),O=e-i;for(let t of n)O+=t.length;t.length+=O,wi(t,h,c,a,l,n,0,s,r)}let Si="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},yi="undefined"!=typeof document?document:{documentElement:{style:{}}};const Qi=/Edge\/(\d+)/.exec(Si.userAgent),vi=/MSIE \d/.test(Si.userAgent),xi=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Si.userAgent),ki=!!(vi||xi||Qi),$i=!ki&&/gecko\/(\d+)/i.test(Si.userAgent),Pi=!ki&&/Chrome\/(\d+)/.exec(Si.userAgent),Zi="webkitFontSmoothing"in yi.documentElement.style,Ti=!ki&&/Apple Computer/.test(Si.vendor),Xi=Ti&&(/Mobile\/\w+/.test(Si.userAgent)||Si.maxTouchPoints>2);var Ci={mac:Xi||/Mac/.test(Si.platform),windows:/Win/.test(Si.platform),linux:/Linux|X11/.test(Si.platform),ie:ki,ie_version:vi?yi.documentMode||6:xi?+xi[1]:Qi?+Qi[1]:0,gecko:$i,gecko_version:$i?+(/Firefox\/(\d+)/.exec(Si.userAgent)||[0,0])[1]:0,chrome:!!Pi,chrome_version:Pi?+Pi[1]:0,ios:Xi,android:/Android\b/.test(Si.userAgent),webkit:Zi,safari:Ti,webkit_version:Zi?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=yi.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class Yi extends pi{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags||i&&(!(i instanceof Yi)||this.length-(e-t)+i.length>256||8&i.flags))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new Yi(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new fi(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let n=t.nodeValue.length;e>n&&(e=n);let s=e,r=e,o=0;0==e&&i<0||e==n&&i>=0?Ci.chrome||Ci.gecko||(e?(s--,o=1):r<n&&(r++,o=-1)):i<0?s--:r<n&&r++;let a=hi(t,s,r).getClientRects();if(!a.length)return null;let l=a[(o?o<0:i>=0)?0:a.length-1];Ci.safari&&!o&&0==l.width&&(l=Array.prototype.find.call(a,(t=>t.width))||l);return o?ii(l,o<0):l||null}(this.dom,t,e)}}class Ai extends pi{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let t of e)t.setParent(this)}setAttrs(t){if(Oi(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!(8&(this.flags|t.flags))}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,s,r){return(!i||!(!(i instanceof Ai&&i.mark.eq(this.mark))||t&&s<=0||e<this.length&&r<=0))&&(bi(this,t,e,i?i.children.slice():[],s-1,r-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,s=0;for(let r of this.children){let o=i+r.length;o>t&&e.push(i<t?r.split(t-i):r),n<0&&i>=t&&(n=s),i=o,s++}let r=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new Ai(this.mark,e,r)}domAtPos(t){return Vi(this,t)}coordsAt(t,e){return qi(this,t,e)}}class Ri extends pi{static create(t,e,i){return new Ri(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=Ri.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof Ri&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof Ri&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return N.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):N.empty}domAtPos(t){return(this.length?0==t:this.side>0)?fi.before(this.dom):fi.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),s=null;if(!n.length)return null;let r=this.side?this.side<0:t>0;for(let e=r?n.length-1:0;s=n[e],!(t>0?0==e:e==n.length-1||s.top<s.bottom);e+=r?-1:1);return ii(s,!r)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Wi extends pi{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof Wi&&t.side==this.side}split(){return new Wi(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?fi.before(this.dom):fi.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return N.empty}get isHidden(){return!0}}function Vi(t,e){let i=t.dom,{children:n}=t,s=0;for(let t=0;s<n.length;s++){let r=n[s],o=t+r.length;if(!(o==t&&r.getSide()<=0)){if(e>t&&e<o&&r.dom.parentNode==i)return r.domAtPos(e-t);if(e<=t)break;t=o}}for(let t=s;t>0;t--){let e=n[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=s;t<n.length;t++){let e=n[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new fi(i,0)}function ji(t,e,i){let n,{children:s}=t;i>0&&e instanceof Ai&&s.length&&(n=s[s.length-1])instanceof Ai&&n.mark.eq(e.mark)?ji(n,e.children[0],i-1):(s.push(e),e.setParent(t)),t.length+=e.length}function qi(t,e,i){let n=null,s=-1,r=null,o=-1;!function t(e,a){for(let l=0,h=0;l<e.children.length&&h<=a;l++){let c=e.children[l],O=h+c.length;O>=a&&(c.children.length?t(c,a-h):(!r||r.isHidden&&i>0)&&(O>a||h==O&&c.getSide()>0)?(r=c,o=a-h):(h<a||h==O&&c.getSide()<0&&!c.isHidden)&&(n=c,s=a-h)),h=O}}(t,e);let a=(i<0?n:r)||n||r;return a?a.coordsAt(Math.max(0,a==n?s:o),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=He(e);return i[i.length-1]||null}(t)}function _i(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}Yi.prototype.children=Ri.prototype.children=Wi.prototype.children=di;const Ei=Object.create(null);function Mi(t,e,i){if(t==e)return!0;t||(t=Ei),e||(e=Ei);let n=Object.keys(t),s=Object.keys(e);if(n.length-(i&&n.indexOf(i)>-1?1:0)!=s.length-(i&&s.indexOf(i)>-1?1:0))return!1;for(let r of n)if(r!=i&&(-1==s.indexOf(r)||t[r]!==e[r]))return!1;return!0}function Di(t,e,i){let n=!1;if(e)for(let s in e)i&&s in i||(n=!0,"style"==s?t.style.cssText="":t.removeAttribute(s));if(i)for(let s in i)e&&e[s]==i[s]||(n=!0,"style"==s?t.style.cssText=i[s]:t.setAttribute(s,i[s]));return n}function zi(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let n=t.attributes[i];e[n.name]=n.value}return e}class Gi extends pi{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,s,r){if(i){if(!(i instanceof Gi))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),bi(this,t,e,i?i.children.slice():[],s,r),!0}split(t){let e=new Gi;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i:i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Mi(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){ji(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=_i(e,this.attrs||{})),i&&(this.attrs=_i({class:i},this.attrs||{}))}domAtPos(t){return Vi(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(Oi(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(Di(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&pi.get(n)instanceof Ai;)n=n.lastChild;if(!(n&&this.length&&("BR"==n.nodeName||0!=(null===(i=pi.get(n))||void 0===i?void 0:i.isEditable)||Ci.ios&&this.children.some((t=>t instanceof Yi))))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t,e=0;for(let i of this.children){if(!(i instanceof Yi)||/[^ -~]/.test(i.text))return null;let n=He(i.dom);if(1!=n.length)return null;e+=n[0].width,t=n[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(t,e){let i=qi(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let n=(e-t.textHeight)/2;return{top:i.top+n,bottom:i.bottom-n,left:i.left,right:i.left}}}return i}become(t){return!1}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let s=t.children[i],r=n+s.length;if(r>=e){if(s instanceof Gi)return s;if(r>e)break}n=r+s.breakAfter}return null}}class Ii extends pi{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof Ii&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return 0==t?fi.before(this.dom):fi.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new Ii(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return di}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):N.empty}domBoundsAround(){return null}become(t){return t instanceof Ii&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class Bi{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var Ni=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(Ni||(Ni={}));class Ui extends be{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new Li(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new Hi(t,e,e,i,t.widget||null,!1)}static replace(t){let e,i,n=!!t.block;if(t.isBlockGap)e=-5e8,i=4e8;else{let{start:s,end:r}=Ji(t,n);e=(s?n?-3e8:-1:5e8)-1,i=1+(r?n?2e8:1:-6e8)}return new Hi(t,e,i,n,t.widget||null,!0)}static line(t){return new Fi(t)}static set(t,e=!1){return ve.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}Ui.none=ve.empty;class Li extends Ui{constructor(t){let{start:e,end:i}=Ji(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof Li&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&Mi(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Li.prototype.point=!1;class Fi extends Ui{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof Fi&&this.spec.class==t.spec.class&&Mi(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}Fi.prototype.mapMode=mt.TrackBefore,Fi.prototype.point=!0;class Hi extends Ui{constructor(t,e,i,n,s,r){super(e,i,s,t),this.block=n,this.isReplace=r,this.mapMode=n?e<=0?mt.TrackBefore:mt.TrackAfter:mt.TrackDel}get type(){return this.startSide!=this.endSide?Ni.WidgetRange:this.startSide<=0?Ni.WidgetBefore:Ni.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof Hi&&(e=this.widget,i=t.widget,e==i||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide;var e,i}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function Ji(t,e=!1){let{inclusiveStart:i,inclusiveEnd:n}=t;return null==i&&(i=t.inclusive),null==n&&(n=t.inclusive),{start:null!=i?i:e,end:null!=n?n:e}}function Ki(t,e,i,n=0){let s=i.length-1;s>=0&&i[s]+n>=t?i[s]=Math.max(i[s],e):i.push(t,e)}Hi.prototype.point=!0;class tn{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof Ii&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new Gi),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(en(new Wi(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof Ii||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(en(new Yi(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,s,r){if(this.disallowBlockEffectsFor[r]&&i instanceof Hi){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let o=e-t;if(i instanceof Hi)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new Ii(i.widget||new nn("div"),o,i));else{let r=Ri.create(i.widget||new nn("span"),o,o?0:i.startSide),a=this.atCursorPos&&!r.isEditable&&s<=n.length&&(t<e||i.startSide>0),l=!r.isEditable&&(t<e||s>n.length||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||a||r.isEditable||(this.pendingBuffer=0),this.flushBuffer(n),a&&(h.append(en(new Wi(1),n),s),s=n.length+Math.max(0,s-n.length)),h.append(en(r,n),s),this.atCursorPos=l,this.pendingBuffer=l?t<e||s>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);o&&(this.textOff+o<=this.text.length?this.textOff+=o:(this.skip+=o-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=s)}static build(t,e,i,n,s){let r=new tn(t,e,i,s);return r.openEnd=ve.spans(n,e,i,r),r.openStart<0&&(r.openStart=r.openEnd),r.finish(r.openEnd),r}}function en(t,e){for(let i of e)t=new Ai(i,[t],t.length);return t}class nn extends Bi{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}var sn=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(sn||(sn={}));const rn=sn.LTR,on=sn.RTL;function an(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const ln=an("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),hn=an("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),cn=Object.create(null),On=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);cn[e]=i,cn[i]=-e}function un(t){return t<=247?ln[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?hn[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}const fn=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class dn{get dir(){return this.level%2?on:rn}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let s=-1;for(let r=0;r<t.length;r++){let o=t[r];if(o.from<=e&&o.to>=e){if(o.level==i)return r;(s<0||(0!=n?n<0?o.from<e:o.to>e:t[s].level>o.level))&&(s=r)}}if(s<0)throw new RangeError("Index out of range");return s}}function pn(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.direction!=s.direction||!pn(n.inner,s.inner))return!1}return!0}const mn=[];function gn(t,e,i,n,s,r,o){let a=n%2?2:1;if(n%2==s%2)for(let l=e,h=0;l<i;){let e=!0,c=!1;if(h==r.length||l<r[h].from){let t=mn[l];t!=a&&(e=!1,c=16==t)}let O=e||1!=a?null:[],u=e?n:n+1,f=l;t:for(;;)if(h<r.length&&f==r[h].from){if(c)break t;let d=r[h];if(!e)for(let t=d.to,e=h+1;;){if(t==i)break t;if(!(e<r.length&&r[e].from==t)){if(mn[t]==a)break t;break}t=r[e++].to}if(h++,O)O.push(d);else{d.from>l&&o.push(new dn(l,d.from,u)),wn(t,d.direction==rn!=!(u%2)?n+1:n,s,d.inner,d.from,d.to,o),l=d.to}f=d.to}else{if(f==i||(e?mn[f]!=a:mn[f]==a))break;f++}O?gn(t,l,f,n+1,s,O,o):l<f&&o.push(new dn(l,f,u)),l=f}else for(let l=i,h=r.length;l>e;){let i=!0,c=!1;if(!h||l>r[h-1].to){let t=mn[l-1];t!=a&&(i=!1,c=16==t)}let O=i||1!=a?null:[],u=i?n:n+1,f=l;t:for(;;)if(h&&f==r[h-1].to){if(c)break t;let d=r[--h];if(!i)for(let t=d.from,i=h;;){if(t==e)break t;if(!i||r[i-1].to!=t){if(mn[t-1]==a)break t;break}t=r[--i].from}if(O)O.push(d);else{d.to<l&&o.push(new dn(d.to,l,u)),wn(t,d.direction==rn!=!(u%2)?n+1:n,s,d.inner,d.from,d.to,o),l=d.from}f=d.from}else{if(f==e||(i?mn[f-1]!=a:mn[f-1]==a))break;f--}O?gn(t,f,l,n+1,s,O,o):f<l&&o.push(new dn(f,l,u)),l=f}}function wn(t,e,i,n,s,r,o){let a=e%2?2:1;!function(t,e,i,n,s){for(let r=0;r<=n.length;r++){let o=r?n[r-1].to:e,a=r<n.length?n[r].from:i,l=r?256:s;for(let e=o,i=l,n=l;e<a;e++){let s=un(t.charCodeAt(e));512==s?s=i:8==s&&4==n&&(s=16),mn[e]=4==s?2:s,7&s&&(n=s),i=s}for(let t=o,e=l,n=l;t<a;t++){let s=mn[t];if(128==s)t<a-1&&e==mn[t+1]&&24&e?s=mn[t]=e:mn[t]=256;else if(64==s){let s=t+1;for(;s<a&&64==mn[s];)s++;let r=t&&8==e||s<i&&8==mn[s]?1==n?1:8:256;for(let e=t;e<s;e++)mn[e]=r;t=s-1}else 8==s&&1==n&&(mn[t]=1);e=s,7&s&&(n=s)}}}(t,s,r,n,a),function(t,e,i,n,s){let r=1==s?2:1;for(let o=0,a=0,l=0;o<=n.length;o++){let h=o?n[o-1].to:e,c=o<n.length?n[o].from:i;for(let e,i,n,o=h;o<c;o++)if(i=cn[e=t.charCodeAt(o)])if(i<0){for(let t=a-3;t>=0;t-=3)if(On[t+1]==-i){let e=On[t+2],i=2&e?s:4&e?1&e?r:s:0;i&&(mn[o]=mn[On[t]]=i),a=t;break}}else{if(189==On.length)break;On[a++]=o,On[a++]=e,On[a++]=l}else if(2==(n=mn[o])||1==n){let t=n==s;l=t?0:1;for(let e=a-3;e>=0;e-=3){let i=On[e+2];if(2&i)break;if(t)On[e+2]|=2;else{if(4&i)break;On[e+2]|=4}}}}}(t,s,r,n,a),function(t,e,i,n){for(let s=0,r=n;s<=i.length;s++){let o=s?i[s-1].to:t,a=s<i.length?i[s].from:e;for(let l=o;l<a;){let o=mn[l];if(256==o){let o=l+1;for(;;)if(o==a){if(s==i.length)break;o=i[s++].to,a=s<i.length?i[s].from:e}else{if(256!=mn[o])break;o++}let h=1==r,c=h==(1==(o<e?mn[o]:n))?h?1:2:n;for(let e=o,n=s,r=n?i[n-1].to:t;e>l;)e==r&&(e=i[--n].from,r=n?i[n-1].to:t),mn[--e]=c;l=o}else r=o,l++}}}(s,r,n,a),gn(t,s,r,e,i,n,o)}function bn(t){return[new dn(0,t,0)]}let Sn="";function yn(t,e,i,n,s){var r;let o=n.head-t.from,a=dn.find(e,o,null!==(r=n.bidiLevel)&&void 0!==r?r:-1,n.assoc),l=e[a],h=l.side(s,i);if(o==h){let t=a+=s?1:-1;if(t<0||t>=e.length)return null;l=e[a=t],o=l.side(!s,i),h=l.side(s,i)}let c=at(t.text,o,l.forward(s,i));(c<l.from||c>l.to)&&(c=h),Sn=t.text.slice(Math.min(o,c),Math.max(o,c));let O=a==(s?e.length-1:0)?null:e[a+(s?1:-1)];return O&&c==h&&O.level+(s?0:1)<l.level?$t.cursor(O.side(!s,i)+t.from,O.forward(s,i)?1:-1,O.level):$t.cursor(c+t.from,l.forward(s,i)?-1:1,l.level)}function Qn(t,e,i){for(let n=e;n<i;n++){let e=un(t.charCodeAt(n));if(1==e)return rn;if(2==e||4==e)return on}return rn}const vn=Tt.define(),xn=Tt.define(),kn=Tt.define(),$n=Tt.define(),Pn=Tt.define(),Zn=Tt.define(),Tn=Tt.define(),Xn=Tt.define({combine:t=>t.some((t=>t))}),Cn=Tt.define({combine:t=>t.some((t=>t))});class Yn{constructor(t,e="nearest",i="nearest",n=5,s=5,r=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=s,this.isSnapshot=r}map(t){return t.empty?this:new Yn(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Yn($t.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const An=re.define({map:(t,e)=>t.map(e)});function Rn(t,e,i){let n=t.facet($n);n.length?n[0](e):window.onerror?window.onerror(String(e),i,void 0,void 0,e):i?console.error(i+":",e):console.error(e)}const Wn=Tt.define({combine:t=>!t.length||t[0]});let Vn=0;const jn=Tt.define();class qn{constructor(t,e,i,n,s){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=s(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:s,decorations:r}=e||{};return new qn(Vn++,t,i,n,(t=>{let e=[jn.of(t)];return r&&e.push(Dn.of((e=>{let i=e.plugin(t);return i?r(i):Ui.none}))),s&&e.push(s(t)),e}))}static fromClass(t,e){return qn.define((e=>new t(e)),e)}}class _n{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(Rn(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){Rn(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){Rn(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const En=Tt.define(),Mn=Tt.define(),Dn=Tt.define(),zn=Tt.define(),Gn=Tt.define(),In=Tt.define();function Bn(t,e){let i=t.state.facet(In);if(!i.length)return i;let n=i.map((e=>e instanceof Function?e(t):e)),s=[];return ve.spans(n,e.from,e.to,{point(){},span(t,i,n,r){let o=t-e.from,a=i-e.from,l=s;for(let t=n.length-1;t>=0;t--,r--){let i,s=n[t].spec.bidiIsolate;if(null==s&&(s=Qn(e.text,o,a)),r>0&&l.length&&(i=l[l.length-1]).to==o&&i.direction==s)i.to=a,l=i.inner;else{let t={from:o,to:a,direction:s,inner:[]};l.push(t),l=t.inner}}}}),s}const Nn=Tt.define();function Un(t){let e=0,i=0,n=0,s=0;for(let r of t.state.facet(Nn)){let o=r(t);o&&(null!=o.left&&(e=Math.max(e,o.left)),null!=o.right&&(i=Math.max(i,o.right)),null!=o.top&&(n=Math.max(n,o.top)),null!=o.bottom&&(s=Math.max(s,o.bottom)))}return{left:e,right:i,top:n,bottom:s}}const Ln=Tt.define();class Fn{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new Fn(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let n=0,s=0,r=0,o=0;;n++){let a=n==t.length?null:t[n],l=r-o,h=a?a.fromB:1e9;for(;s<e.length&&e[s]<h;){let t=e[s],n=e[s+1],r=Math.max(o,t),a=Math.min(h,n);if(r<=a&&new Fn(r+l,a+l,r,a).addToSet(i),n>h)break;s+=2}if(!a)return i;new Fn(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),r=a.toA,o=a.toB}}}class Hn{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=wt.empty(this.startState.doc.length);for(let t of i)this.changes=this.changes.compose(t.changes);let n=[];this.changes.iterChangedRanges(((t,e,i,s)=>n.push(new Fn(t,e,i,s)))),this.changedRanges=n}static create(t,e,i){return new Hn(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return 0==this.flags&&0==this.transactions.length}}class Jn extends pi{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Gi],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Fn(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0);let n=-1;this.view.inputState.composing>=0&&((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?n=this.domChanged.newSel.head:function(t,e){let i=!1;e&&t.iterChangedRanges(((t,n)=>{t<e.to&&n>e.from&&(i=!0)}));return i}(t.changes,this.hasComposition)||t.selectionSet||(n=t.state.selection.main.head));let s=n>-1?function(t,e,i){let n=ts(t,i);if(!n)return null;let{node:s,from:r,to:o}=n,a=s.nodeValue;if(/[\n\r]/.test(a))return null;if(t.state.doc.sliceString(n.from,n.to)!=a)return null;let l=e.invertedDesc,h=new Fn(l.mapPos(r),l.mapPos(o),r,o),c=[];for(let e=s.parentNode;;e=e.parentNode){let i=pi.get(e);if(i instanceof Ai)c.push({node:e,deco:i.mark});else{if(i instanceof Gi||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:h,text:s,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new Li({inclusive:!0,attributes:zi(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:n}=this.hasComposition;i=new Fn(e,n,t.changes.mapPos(e,-1),t.changes.mapPos(n,1)).addToSet(i.slice())}this.hasComposition=s?{from:s.range.fromB,to:s.range.toB}:null,(Ci.ie||Ci.chrome)&&!s&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let r=function(t,e,i){let n=new is;return ve.compare(t,e,i,n),n.changes}(this.decorations,this.updateDeco(),t.changes);return i=Fn.extendWithRanges(i,r),!!(7&this.flags||0!=i.length)&&(this.updateInner(i,t.startState.doc.length,s),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=Ci.chrome||Ci.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||n.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""})),this.markedForComposition.forEach((t=>t.flags&=-9));let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof Ii&&t.widget instanceof Kn&&s.push(t.dom);n.updateGaps(s)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,s=this.childCursor(e);for(let t=n.length-1;;t--){let e=t>=0?n[t]:null;if(!e)break;let r,o,a,l,{fromA:h,toA:c,fromB:O,toB:u}=e;if(i&&i.range.fromB<u&&i.range.toB>O){let t=tn.build(this.view.state.doc,O,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=tn.build(this.view.state.doc,i.range.toB,u,this.decorations,this.dynamicDecorationMap);o=t.breakAtStart,a=t.openStart,l=e.openEnd;let n=this.compositionView(i);e.breakAtStart?n.breakAfter=1:e.content.length&&n.merge(n.length,n.length,e.content[0],!1,e.openStart,0)&&(n.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&n.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),r=t.content.concat(n).concat(e.content)}else({content:r,breakAtStart:o,openStart:a,openEnd:l}=tn.build(this.view.state.doc,O,u,this.decorations,this.dynamicDecorationMap));let{i:f,off:d}=s.findPos(c,1),{i:p,off:m}=s.findPos(h,-1);wi(this,p,m,f,d,r,o,a,l)}i&&this.fixCompositionDOM(i)}compositionView(t){let e=new Yi(t.text.nodeValue);e.flags|=8;for(let{deco:i}of t.marks)e=new Ai(i,[e],e.length);let i=new Gi;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some((t=>7&t.flags))?1:0),this.markedForComposition.add(e);let i=pi.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let s=t.marks.length-1;s>=-1;s--)i=n.childPos(i.off,1),n=n.children[i.i],e(s>=0?t.marks[s].node:t.text,n)}updateSelection(t=!1,e=!1){!t&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,s=!n&&Fe(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||s))return;let r=this.forceSelection;this.forceSelection=!1;let o=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(o.anchor)),l=o.empty?a:this.moveToLine(this.domAtPos(o.head));if(Ci.gecko&&o.empty&&!this.hasComposition&&(1==(h=a).node.nodeType&&h.node.firstChild&&(0==h.offset||"false"==h.node.childNodes[h.offset-1].contentEditable)&&(h.offset==h.node.childNodes.length||"false"==h.node.childNodes[h.offset].contentEditable))){let t=document.createTextNode("");this.view.observer.ignore((()=>a.node.insertBefore(t,a.node.childNodes[a.offset]||null))),a=l=new fi(t,0),r=!0}var h;let c=this.view.observer.selectionRange;!r&&c.focusNode&&(Je(a.node,a.offset,c.anchorNode,c.anchorOffset)&&Je(l.node,l.offset,c.focusNode,c.focusOffset)||this.suppressWidgetCursorChange(c,o))||(this.view.observer.ignore((()=>{Ci.android&&Ci.chrome&&this.dom.contains(c.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=Ue(this.view.root);if(t)if(o.empty){if(Ci.gecko){let t=(e=a.node,n=a.offset,1!=e.nodeType?0:(n&&"false"==e.childNodes[n-1].contentEditable?1:0)|(n<e.childNodes.length&&"false"==e.childNodes[n].contentEditable?2:0));if(t&&3!=t){let e=es(a.node,a.offset,1==t?1:-1);e&&(a=new fi(e.node,e.offset))}}t.collapse(a.node,a.offset),null!=o.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=o.bidiLevel)}else if(t.extend){t.collapse(a.node,a.offset);try{t.extend(l.node,l.offset)}catch(t){}}else{let e=document.createRange();o.anchor>o.head&&([a,l]=[l,a]),e.setEnd(l.node,l.offset),e.setStart(a.node,a.offset),t.removeAllRanges(),t.addRange(e)}else;var e,n;s&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())})),this.view.observer.setSelectionRange(a,l)),this.impreciseAnchor=a.precise?null:new fi(c.anchorNode,c.anchorOffset),this.impreciseHead=l.precise?null:new fi(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&Je(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Ue(t.root),{anchorNode:n,anchorOffset:s}=t.observer.selectionRange;if(!(i&&e.empty&&e.assoc&&i.modify))return;let r=Gi.find(this,e.head);if(!r)return;let o=r.posAtStart;if(e.head==o||e.head==o+r.length)return;let a=this.coordsAt(e.head,-1),l=this.coordsAt(e.head,1);if(!a||!l||a.bottom>l.top)return;let h=this.domAtPos(e.head+e.assoc);i.collapse(h.node,h.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(n,s)}moveToLine(t){let e,i=this.dom;if(t.node!=i)return t;for(let n=t.offset;!e&&n<i.childNodes.length;n++){let t=pi.get(i.childNodes[n]);t instanceof Gi&&(e=t.domAtPos(0))}for(let n=t.offset-1;!e&&n>=0;n--){let t=pi.get(i.childNodes[n]);t instanceof Gi&&(e=t.domAtPos(t.length))}return e?new fi(e.node,e.offset,!0):t}nearest(t){for(let e=t;e;){let t=pi.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof Gi)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let s=this.length,r=this.children.length-1;r>=0;r--){let o=this.children[r],a=s-o.breakAfter,l=a-o.length;if(a<t)break;l<=t&&(l<t||o.covers(-1))&&(a>t||o.covers(1))&&(!i||o instanceof Gi&&!(i instanceof Gi&&e>=0))&&(i=o,n=l),s=l}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof Gi))return null;for(;n.children.length;){let{i:t,off:e}=n.childPos(i,1);for(;;t++){if(t==n.children.length)return null;if((n=n.children[t]).length)break}i=e}if(!(n instanceof Yi))return null;let s=at(n.text,i);if(s==i)return null;let r=hi(n.dom,i,s).getClientRects();for(let t=0;t<r.length;t++){let e=r[t];if(t==r.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,s=this.view.contentDOM.clientWidth,r=s>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,o=-1,a=this.view.textDirection==sn.LTR;for(let t=0,l=0;l<this.children.length;l++){let h=this.children[l],c=t+h.length;if(c>n)break;if(t>=i){let i=h.dom.getBoundingClientRect();if(e.push(i.height),r){let e=h.dom.lastChild,n=e?He(e):[];if(n.length){let e=n[n.length-1],r=a?e.right-i.left:i.right-e.left;r>o&&(o=r,this.minWidth=s,this.minWidthFrom=t,this.minWidthTo=c)}}}t=c+h.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?sn.RTL:sn.LTR}measureTextSize(){for(let t of this.children)if(t instanceof Gi){let e=t.measureTextSize();if(e)return e}let t,e,i,n=document.createElement("div");return n.className="cm-line",n.style.width="99999px",n.style.position="absolute",n.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore((()=>{this.dom.appendChild(n);let s=He(n.firstChild)[0];t=n.getBoundingClientRect().height,e=s?s.width/27:7,i=s?s.height:t,n.remove()})),{lineHeight:t,charWidth:e,textHeight:i}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new gi(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let s=n==e.viewports.length?null:e.viewports[n],r=s?s.from-1:this.length;if(r>i){let n=(e.lineBlockAt(r).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(Ui.replace({widget:new Kn(n),block:!0,inclusive:!0,isBlockGap:!0}).range(i,r))}if(!s)break;i=s.to+1}return Ui.set(t)}updateDeco(){let t=this.view.state.facet(Dn).map(((t,e)=>(this.dynamicDecorationMap[e]="function"==typeof t)?t(this.view):t)),e=!1,i=this.view.state.facet(zn).map(((t,i)=>{let n="function"==typeof t;return n&&(e=!0),n?t(this.view):t}));i.length&&(this.dynamicDecorationMap[t.length]=e,t.push(ve.join(i)));for(let e=t.length;e<t.length+3;e++)this.dynamicDecorationMap[e]=!1;return this.decorations=[...t,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);return this.view.scrollDOM.scrollTop=e.top-t.yMargin,void(this.view.scrollDOM.scrollLeft=t.xMargin)}let e,{range:i}=t,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,e.left),top:Math.min(n.top,e.top),right:Math.max(n.right,e.right),bottom:Math.max(n.bottom,e.bottom)});let s=Un(this.view),r={left:n.left-s.left,top:n.top-s.top,right:n.right+s.right,bottom:n.bottom+s.bottom},{offsetWidth:o,offsetHeight:a}=this.view.scrollDOM;!function(t,e,i,n,s,r,o,a){let l=t.ownerDocument,h=l.defaultView||window;for(let c=t,O=!1;c&&!O;)if(1==c.nodeType){let t,u=c==l.body,f=1,d=1;if(u)t=ni(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(O=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:f,scaleY:d}=si(c,e)),t={left:e.left,right:e.left+c.clientWidth*f,top:e.top,bottom:e.top+c.clientHeight*d}}let p=0,m=0;if("nearest"==s)e.top<t.top?(m=-(t.top-e.top+o),i>0&&e.bottom>t.bottom+m&&(m=e.bottom-t.bottom+m+o)):e.bottom>t.bottom&&(m=e.bottom-t.bottom+o,i<0&&e.top-m<t.top&&(m=-(t.top+m-e.top+o)));else{let n=e.bottom-e.top,r=t.bottom-t.top;m=("center"==s&&n<=r?e.top+n/2-r/2:"start"==s||"center"==s&&i<0?e.top-o:e.bottom-r+o)-t.top}if("nearest"==n?e.left<t.left?(p=-(t.left-e.left+r),i>0&&e.right>t.right+p&&(p=e.right-t.right+p+r)):e.right>t.right&&(p=e.right-t.right+r,i<0&&e.left<t.left+p&&(p=-(t.left+p-e.left+r))):p=("center"==n?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==n==a?e.left-r:e.right-(t.right-t.left)+r)-t.left,p||m)if(u)h.scrollBy(p,m);else{let t=0,i=0;if(m){let t=c.scrollTop;c.scrollTop+=m/d,i=(c.scrollTop-t)*d}if(p){let e=c.scrollLeft;c.scrollLeft+=p/f,t=(c.scrollLeft-e)*f}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&Math.abs(t-p)<1&&(n="nearest"),i&&Math.abs(i-m)<1&&(s="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else{if(11!=c.nodeType)break;c=c.host}}(this.view.scrollDOM,r,i.head<i.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,o),-o),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==sn.LTR)}}class Kn extends Bi{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}}function ts(t,e){let i=t.observer.selectionRange,n=i.focusNode&&es(i.focusNode,i.focusOffset,0);if(!n)return null;let s=e-n.offset;return{from:s,to:s+n.node.nodeValue.length,node:n.node}}function es(t,e,i){if(i<=0)for(let i=t,n=e;;){if(3==i.nodeType)return{node:i,offset:n};if(!(1==i.nodeType&&n>0))break;i=i.childNodes[n-1],n=ei(i)}if(i>=0)for(let n=t,s=e;;){if(3==n.nodeType)return{node:n,offset:s};if(!(1==n.nodeType&&s<n.childNodes.length&&i>=0))break;n=n.childNodes[s],s=0}return null}let is=class{constructor(){this.changes=[]}compareRange(t,e){Ki(t,e,this.changes)}comparePoint(t,e){Ki(t,e,this.changes)}};function ns(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function ss(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function rs(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function os(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function as(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function ls(t,e,i){let n,s,r,o,a,l,h,c,O=!1;for(let u=t.firstChild;u;u=u.nextSibling){let t=He(u);for(let f=0;f<t.length;f++){let d=t[f];s&&rs(s,d)&&(d=os(as(d,s.bottom),s.top));let p=ns(e,d),m=ss(i,d);if(0==p&&0==m)return 3==u.nodeType?hs(u,e,i):ls(u,e,i);if(!n||o>m||o==m&&r>p){n=u,s=d,r=p,o=m;let a=m?i<d.top?-1:1:p?e<d.left?-1:1:0;O=!a||(a>0?f<t.length-1:f>0)}0==p?i>d.bottom&&(!h||h.bottom<d.bottom)?(a=u,h=d):i<d.top&&(!c||c.top>d.top)&&(l=u,c=d):h&&rs(h,d)?h=as(h,d.bottom):c&&rs(c,d)&&(c=os(c,d.top))}}if(h&&h.bottom>=i?(n=a,s=h):c&&c.top<=i&&(n=l,s=c),!n)return{node:t,offset:0};let u=Math.max(s.left,Math.min(s.right,e));return 3==n.nodeType?hs(n,u,i):O&&"false"!=n.contentEditable?ls(n,u,i):{node:t,offset:Array.prototype.indexOf.call(t.childNodes,n)+(e>=(s.left+s.right)/2?1:0)}}function hs(t,e,i){let n=t.nodeValue.length,s=-1,r=1e9,o=0;for(let a=0;a<n;a++){let n=hi(t,a,a+1).getClientRects();for(let l=0;l<n.length;l++){let h=n[l];if(h.top==h.bottom)continue;o||(o=e-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=e&&h.right+1>=e&&c<r){let i=e>=(h.left+h.right)/2,n=i;if(Ci.chrome||Ci.gecko){hi(t,a).getBoundingClientRect().left==h.right&&(n=!i)}if(c<=0)return{node:t,offset:a+(n?1:0)};s=a+(n?1:0),r=c}}}return{node:t,offset:s>-1?s:o>0?t.nodeValue.length:0}}function cs(t,e,i,n=-1){var s,r;let o,a=t.contentDOM.getBoundingClientRect(),l=a.top+t.viewState.paddingTop,{docHeight:h}=t.viewState,{x:c,y:O}=e,u=O-l;if(u<0)return 0;if(u>h)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,s=!1;o=t.elementAtHeight(u),o.type!=Ni.Text;)for(;u=n>0?o.bottom+e:o.top-e,!(u>=0&&u<=h);){if(s)return i?null:0;s=!0,n=-n}O=l+u;let f=o.from;if(f<t.viewport.from)return 0==t.viewport.from?0:i?null:Os(t,a,o,c,O);if(f>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:Os(t,a,o,c,O);let d=t.dom.ownerDocument,p=t.root.elementFromPoint?t.root:d,m=p.elementFromPoint(c,O);m&&!t.contentDOM.contains(m)&&(m=null),m||(c=Math.max(a.left+1,Math.min(a.right-1,c)),m=p.elementFromPoint(c,O),m&&!t.contentDOM.contains(m)&&(m=null));let g,w=-1;if(m&&0!=(null===(s=t.docView.nearest(m))||void 0===s?void 0:s.isEditable))if(d.caretPositionFromPoint){let t=d.caretPositionFromPoint(c,O);t&&({offsetNode:g,offset:w}=t)}else if(d.caretRangeFromPoint){let e=d.caretRangeFromPoint(c,O);e&&(({startContainer:g,startOffset:w}=e),(!t.contentDOM.contains(g)||Ci.safari&&function(t,e,i){let n;if(3!=t.nodeType||e!=(n=t.nodeValue.length))return!1;for(let e=t.nextSibling;e;e=e.nextSibling)if(1!=e.nodeType||"BR"!=e.nodeName)return!1;return hi(t,n-1,n).getBoundingClientRect().left>i}(g,w,c)||Ci.chrome&&function(t,e,i){if(0!=e)return!1;for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}let n=1==t.nodeType?t.getBoundingClientRect():hi(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect();return i-n.left>5}(g,w,c))&&(g=void 0))}if(!g||!t.docView.dom.contains(g)){let e=Gi.find(t.docView,f);if(!e)return u>o.top+o.height/2?o.to:o.from;({node:g,offset:w}=ls(e.dom,c,O))}let b=t.docView.nearest(g);if(!b)return null;if(b.isWidget&&1==(null===(r=b.dom)||void 0===r?void 0:r.nodeType)){let t=b.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?b.posAtStart:b.posAtEnd}return b.localPosFromDOM(g,w)+b.posAtStart}function Os(t,e,i,n,s){let r=Math.round((n-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;r+=Math.floor((s-i.top-.5*(t.defaultLineHeight-e))/e)*t.viewState.heightOracle.lineLength}let o=t.state.sliceDoc(i.from,i.to);return i.from+function(t,e,i,n){for(let n=0,s=0;;){if(s>=e)return n;if(n==t.length)break;s+=9==t.charCodeAt(n)?i-s%i:1,n=at(t,n)}return!0===n?-1:t.length}(o,r,t.state.tabSize)}function us(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let t of i.type)if(t.to>e||t.to==e&&(t.to==i.to||t.type==Ni.Text))return t;return i}function fs(t,e,i,n){let s=t.state.doc.lineAt(e.head),r=t.bidiSpans(s),o=t.textDirectionAt(s.from);for(let a=e,l=null;;){let e=yn(s,r,o,a,i),h=Sn;if(!e){if(s.number==(i?t.state.doc.lines:1))return a;h="\n",s=t.state.doc.line(s.number+(i?1:-1)),r=t.bidiSpans(s),e=t.visualLineSide(s,!i)}if(l){if(!l(h))return a}else{if(!n)return e;l=n(h)}a=e}}function ds(t,e,i){for(;;){let n=0;for(let s of t)s.between(e-1,e+1,((t,s,r)=>{if(e>t&&e<s){let r=n||i||(e-t<s-e?-1:1);e=r<0?t:s,n=r}}));if(!n)return e}}function ps(t,e,i){let n=ds(t.state.facet(Gn).map((e=>e(t))),i.from,e.head>i.from?-1:1);return n==i.from?i:$t.cursor(n,n<i.from?1:-1)}class ms{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Ci.safari&&t.contentDOM.addEventListener("input",(()=>null)),Ci.gecko&&function(t){zs.has(t)||(zs.add(t),t.addEventListener("copy",(()=>{})),t.addEventListener("cut",(()=>{})))}(t.contentDOM.ownerDocument)}handleEvent(t){(function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,n=e.target;n!=t.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=pi.get(n))&&i.ignoreEvent(e))return!1;return!0})(this.view,t)&&!this.ignoreDuringComposition(t)&&("keydown"==t.type&&this.keydown(t)||this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=ws(t),i=this.handlers,n=this.view.contentDOM;for(let t in e)if("scroll"!=t){let s=!e[t].handlers.length,r=i[t];r&&s!=!r.handlers.length&&(n.removeEventListener(t,this.handleEvent),r=null),r||n.addEventListener(t,this.handleEvent,{passive:s})}for(let t in i)"scroll"==t||e[t]||n.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&Date.now()<this.lastEscPress+2e3)return!0;if(27!=t.keyCode&&ys.indexOf(t.keyCode)<0&&(this.view.inputState.lastEscPress=0),Ci.android&&Ci.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return!Ci.ios||t.synthetic||t.altKey||t.metaKey||!((e=bs.find((e=>e.keyCode==t.keyCode)))&&!t.ctrlKey||Ss.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(229!=t.keyCode&&this.view.observer.forceFlush(),!1):(this.pendingIOSKey=e||t,setTimeout((()=>this.flushIOSKey()),250),!0)}flushIOSKey(){let t=this.pendingIOSKey;return!!t&&(this.pendingIOSKey=void 0,ci(this.view.contentDOM,t.key,t.keyCode))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Ci.safari&&!Ci.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function gs(t,e){return(i,n)=>{try{return e.call(t,n,i)}catch(t){Rn(i.state,t)}}}function ws(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec;if(t&&t.domEventHandlers)for(let n in t.domEventHandlers){let s=t.domEventHandlers[n];s&&i(n).handlers.push(gs(e.value,s))}if(t&&t.domEventObservers)for(let n in t.domEventObservers){let s=t.domEventObservers[n];s&&i(n).observers.push(gs(e.value,s))}}for(let t in xs)i(t).handlers.push(xs[t]);for(let t in ks)i(t).observers.push(ks[t]);return e}const bs=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Ss="dthko",ys=[16,17,18,20,91,92,224,225];function Qs(t){return.7*Math.max(0,t)+8}class vs{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParent=function(t){let e=t.ownerDocument;for(let i=t.parentNode;i&&i!=e.body;)if(1==i.nodeType){if(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)return i;i=i.assignedSlot||i.parentNode}else{if(11!=i.nodeType)break;i=i.host}return null}(t.contentDOM),this.atoms=t.state.facet(Gn).map((e=>e(t)));let s=t.contentDOM.ownerDocument;s.addEventListener("mousemove",this.move=this.move.bind(this)),s.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(ge.allowMultipleSelections)&&function(t,e){let i=t.state.facet(vn);return i.length?i[0](e):Ci.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let n=Ue(t.root);if(!n||0==n.rangeCount)return!0;let s=n.getRangeAt(0).getClientRects();for(let t=0;t<s.length;t++){let i=s[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)||1!=js(e))&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e,i,n;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&(i=this.startEvent,n=t,Math.max(Math.abs(i.clientX-n.clientX),Math.abs(i.clientY-n.clientY))<10))return;this.select(this.lastEvent=t);let s=0,r=0,o=(null===(e=this.scrollParent)||void 0===e?void 0:e.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight},a=Un(this.view);t.clientX-a.left<=o.left+6?s=-Qs(o.left-t.clientX):t.clientX+a.right>=o.right-6&&(s=Qs(t.clientX-o.right)),t.clientY-a.top<=o.top+6?r=-Qs(o.top-t.clientY):t.clientY+a.bottom>=o.bottom-6&&(r=Qs(t.clientY-o.bottom)),this.setScrollSpeed(s,r)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval((()=>this.scroll()),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){this.scrollParent?(this.scrollParent.scrollLeft+=this.scrollSpeed.x,this.scrollParent.scrollTop+=this.scrollSpeed.y):this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],s=null;if(n.empty){let t=ds(this.atoms,n.from,0);t!=n.from&&(s=$t.cursor(t,-1))}else{let t=ds(this.atoms,n.from,-1),e=ds(this.atoms,n.to,1);t==n.from&&e==n.to||(s=$t.range(n.from==n.anchor?t:e,n.from==n.head?t:e))}s&&(e||(e=t.ranges.slice()),e[i]=s)}return e?$t.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));!this.mustSelect&&i.eq(e.state.selection,!1===this.dragging)||this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){this.style.update(t)&&setTimeout((()=>this.select(this.lastEvent)),20)}}const xs=Object.create(null),ks=Object.create(null),$s=Ci.ie&&Ci.ie_version<15||Ci.ios&&Ci.webkit_version<604;function Ps(t,e){let i,{state:n}=t,s=1,r=n.toText(e),o=r.lines==n.selection.ranges.length;if(null!=_s&&n.selection.ranges.every((t=>t.empty))&&_s==r.toString()){let t=-1;i=n.changeByRange((i=>{let a=n.doc.lineAt(i.from);if(a.from==t)return{range:i};t=a.from;let l=n.toText((o?r.line(s++).text:e)+n.lineBreak);return{changes:{from:a.from,insert:l},range:$t.cursor(i.from+l.length)}}))}else i=o?n.changeByRange((t=>{let e=r.line(s++);return{changes:{from:t.from,to:t.to,insert:e.text},range:$t.cursor(t.from+e.length)}})):n.replaceSelection(r);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}function Zs(t,e,i,n){if(1==n)return $t.cursor(e,i);if(2==n)return function(t,e,i=1){let n=t.charCategorizer(e),s=t.doc.lineAt(e),r=e-s.from;if(0==s.length)return $t.cursor(e);0==r?i=1:r==s.length&&(i=-1);let o=r,a=r;i<0?o=at(s.text,r,!1):a=at(s.text,r);let l=n(s.text.slice(o,a));for(;o>0;){let t=at(s.text,o,!1);if(n(s.text.slice(t,o))!=l)break;o=t}for(;a<s.length;){let t=at(s.text,a);if(n(s.text.slice(a,t))!=l)break;a=t}return $t.range(o+s.from,a+s.from)}(t.state,e,i);{let i=Gi.find(t.docView,e),n=t.state.doc.lineAt(i?i.posAtEnd:e),s=i?i.posAtStart:n.from,r=i?i.posAtEnd:n.to;return r<t.state.doc.length&&r==n.to&&r++,$t.range(s,r)}}ks.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},xs.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&(t.inputState.lastEscPress=Date.now()),!1),ks.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},ks.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},xs.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let n of t.state.facet(kn))if(i=n(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=Ys(t,e),n=js(e),s=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),s=s.map(t.changes))},get(e,r,o){let a,l=Ys(t,e),h=Zs(t,l.pos,l.bias,n);if(i.pos!=l.pos&&!r){let e=Zs(t,i.pos,i.bias,n),s=Math.min(e.from,h.from),r=Math.max(e.to,h.to);h=s<h.from?$t.range(s,r):$t.range(r,s)}return r?s.replaceRange(s.main.extend(h.from,h.to)):o&&1==n&&s.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:n,to:s}=t.ranges[i];if(n<=e&&s>=e)return $t.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(s,l.pos))?a:o?s.addRange(h):$t.create([h])}}}(t,e)),i){let n=!t.hasFocus;t.inputState.startMouseSelection(new vs(t,e,i,n)),n&&t.observer.ignore((()=>li(t.contentDOM)));let s=t.inputState.mouseSelection;if(s)return s.start(e),!1===s.dragging}return!1};let Ts=(t,e)=>t>=e.top&&t<=e.bottom,Xs=(t,e,i)=>Ts(e,i)&&t>=i.left&&t<=i.right;function Cs(t,e,i,n){let s=Gi.find(t.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(0==r)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&Xs(i,n,o))return-1;let a=s.coordsAt(r,1);return a&&Xs(i,n,a)?1:o&&Ts(n,o)?-1:1}function Ys(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:Cs(t,i,e.clientX,e.clientY)}}const As=Ci.ie&&Ci.ie_version<=11;let Rs=null,Ws=0,Vs=0;function js(t){if(!As)return t.detail;let e=Rs,i=Vs;return Rs=t,Vs=Date.now(),Ws=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(Ws+1)%3:1}function qs(t,e,i,n){if(!i)return;let s=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=t.inputState,o=n&&r&&function(t,e){let i=t.state.facet(xn);return i.length?i[0](e):Ci.mac?!e.altKey:!e.ctrlKey}(t,e)?{from:r.from,to:r.to}:null,a={from:s,insert:i},l=t.state.changes(o?[o,a]:a);t.focus(),t.dispatch({changes:l,selection:{anchor:l.mapPos(s,-1),head:l.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"}),t.inputState.draggedContent=null}xs.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let n=t.docView.nearest(e.target);if(n&&n.isWidget){let t=n.posAtStart,e=t+n.length;(t>=i.to||e<=i.from)&&(i=$t.range(t,e))}}let{inputState:n}=t;return n.mouseSelection&&(n.mouseSelection.dragging=!0),n.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to)),e.dataTransfer.effectAllowed="copyMove"),!1},xs.dragend=t=>(t.inputState.draggedContent=null,!1),xs.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let n=Array(i.length),s=0,r=()=>{++s==i.length&&qs(t,e,n.filter((t=>null!=t)).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=r,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(n[t]=e.result),r()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return qs(t,e,i,!0),!0}return!1},xs.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=$s?null:e.clipboardData;return i?(Ps(t,i.getData("text/plain")||i.getData("text/uri-text")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout((()=>{t.focus(),i.remove(),Ps(t,i.value)}),50)}(t),!1)};let _s=null;xs.copy=xs.cut=(t,e)=>{let{text:i,ranges:n,linewise:s}=function(t){let e=[],i=[],n=!1;for(let n of t.selection.ranges)n.empty||(e.push(t.sliceDoc(n.from,n.to)),i.push(n));if(!e.length){let s=-1;for(let{from:n}of t.selection.ranges){let r=t.doc.lineAt(n);r.number>s&&(e.push(r.text),i.push({from:r.from,to:Math.min(t.doc.length,r.to+1)})),s=r.number}n=!0}return{text:e.join(t.lineBreak),ranges:i,linewise:n}}(t.state);if(!i&&!s)return!1;_s=s?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"});let r=$s?null:e.clipboardData;return r?(r.clearData(),r.setData("text/plain",i),!0):(function(t,e){let i=t.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=e,n.focus(),n.selectionEnd=e.length,n.selectionStart=0,setTimeout((()=>{n.remove(),t.focus()}),50)}(t,i),!1)};const Es=ie.define();function Ms(t,e){let i=[];for(let n of t.facet(Tn)){let s=n(t,e);s&&i.push(s)}return i?t.update({effects:i,annotations:Es.of(!0)}):null}function Ds(t){setTimeout((()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=Ms(t.state,e);i?t.dispatch(i):t.update([])}}),10)}ks.focus=t=>{t.inputState.lastFocusTime=Date.now(),t.scrollDOM.scrollTop||!t.inputState.lastScrollTop&&!t.inputState.lastScrollLeft||(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),Ds(t)},ks.blur=t=>{t.observer.clearSelectionRange(),Ds(t)},ks.compositionstart=ks.compositionupdate=t=>{null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0)},ks.compositionend=t=>{t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,Ci.chrome&&Ci.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then((()=>t.observer.flush())):setTimeout((()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])}),50)},ks.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},xs.beforeinput=(t,e)=>{var i;let n;if(Ci.chrome&&Ci.android&&(n=bs.find((t=>t.inputType==e.inputType)))&&(t.observer.delayAndroidKey(n.key,n.keyCode),"Backspace"==n.key||"Delete"==n.key)){let e=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout((()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())}),100)}return!1};const zs=new Set;const Gs=["pre-wrap","normal","pre-line","break-spaces"];class Is{constructor(t){this.lineWrapping=t,this.doc=N.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30,this.heightChanged=!1}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){if(!this.lineWrapping)return this.lineHeight;return(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return Gs.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(e=!0,this.heightSamples[Math.floor(10*n)]=!0)}return e}refresh(t,e,i,n,s,r){let o=Gs.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=s,a){this.heightSamples={};for(let t=0;t<r.length;t++){let e=r[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return a}}class Bs{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class Ns{constructor(t,e,i,n,s){this.from=t,this.length=e,this.top=i,this.height=n,this._content=s}get type(){return"number"==typeof this._content?Ni.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof Hi?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new Ns(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var Us=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(Us||(Us={}));const Ls=.001;class Fs{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t,e){this.height!=e&&(Math.abs(this.height-e)>Ls&&(t.heightChanged=!0),this.height=e)}replace(t,e,i){return Fs.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let s=this,r=i.doc;for(let o=n.length-1;o>=0;o--){let{fromA:a,toA:l,fromB:h,toB:c}=n[o],O=s.lineAt(a,Us.ByPosNoHeight,i.setDoc(e),0,0),u=O.to>=l?O:s.lineAt(l,Us.ByPosNoHeight,i,0,0);for(c+=u.to-l,l=u.to;o>0&&O.from<=n[o-1].toA;)a=n[o-1].fromA,h=n[o-1].fromB,o--,a<O.from&&(O=s.lineAt(a,Us.ByPosNoHeight,i,0,0));h+=O.from-a,a=O.from;let f=ir.build(i.setDoc(r),t,h,c);s=s.replace(a,l,f)}return s.updateHeight(i,0)}static empty(){return new Js(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,n=0,s=0;for(;;)if(e==i)if(n>2*s){let s=t[e-1];s.break?t.splice(--e,1,s.left,null,s.right):t.splice(--e,1,s.left,s.right),i+=1+s.break,n-=s.size}else{if(!(s>2*n))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,s-=e.size}}else if(n<s){let i=t[e++];i&&(n+=i.size)}else{let e=t[--i];e&&(s+=e.size)}let r=0;return null==t[e-1]?(r=1,e--):null==t[e]&&(r=1,i++),new tr(Fs.of(t.slice(0,e)),r,Fs.of(t.slice(i)))}}Fs.prototype.size=1;class Hs extends Fs{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new Ns(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,s){return this.blockAt(0,i,n,s)}forEachLine(t,e,i,n,s,r){t<=s+this.length&&e>=s&&r(this.blockAt(0,i,n,s))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(t,n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Js extends Hs{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new Ns(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return 1==i.length&&(n instanceof Js||n instanceof Ks&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof Ks?n=new Js(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):Fs.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(t,n.heights[n.index++]):(i||this.outdated)&&this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class Ks extends Fs{constructor(t){super(t,0)}heightMetrics(t,e){let i,n=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number,r=s-n+1,o=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*r);i=e/r,this.length>r+1&&(o=(this.height-e)/(this.length-r-1))}else i=this.height/r;return{firstLine:n,lastLine:s,perLine:i,perChar:o}}blockAt(t,e,i,n){let{firstLine:s,lastLine:r,perLine:o,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let s=n+Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length),r=e.doc.lineAt(s),l=o+r.length*a,h=Math.max(i,t-l/2);return new Ns(r.from,r.length,h,l,0)}{let n=Math.max(0,Math.min(r-s,Math.floor((t-i)/o))),{from:a,length:l}=e.doc.line(s+n);return new Ns(a,l,i+o*n,o,0)}}lineAt(t,e,i,n,s){if(e==Us.ByHeight)return this.blockAt(t,i,n,s);if(e==Us.ByPosNoHeight){let{from:e,to:n}=i.doc.lineAt(t);return new Ns(e,n-e,0,0,0)}let{firstLine:r,perLine:o,perChar:a}=this.heightMetrics(i,s),l=i.doc.lineAt(t),h=o+l.length*a,c=l.number-r,O=n+o*c+a*(l.from-s-c);return new Ns(l.from,l.length,Math.max(n,Math.min(O,n+this.height-h)),h,0)}forEachLine(t,e,i,n,s,r){t=Math.max(t,s),e=Math.min(e,s+this.length);let{firstLine:o,perLine:a,perChar:l}=this.heightMetrics(i,s);for(let h=t,c=n;h<=e;){let e=i.doc.lineAt(h);if(h==t){let i=e.number-o;c+=a*i+l*(t-s-i)}let n=a+l*e.length;r(new Ns(e.from,e.length,c,n,0)),c+=n,h=e.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let t=i[i.length-1];t instanceof Ks?i[i.length-1]=new Ks(t.length+n):i.push(null,new Ks(n-1))}if(t>0){let e=i[0];e instanceof Ks?i[0]=new Ks(t+e.length):i.unshift(new Ks(t-1),null)}return Fs.of(i)}decomposeLeft(t,e){e.push(new Ks(t-1),null)}decomposeRight(t,e){e.push(null,new Ks(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let s=e+this.length;if(n&&n.from<=e+this.length&&n.more){let i=[],r=Math.max(e,n.from),o=-1;for(n.from>e&&i.push(new Ks(n.from-e-1).updateHeight(t,e));r<=s&&n.more;){let e=t.doc.lineAt(r).length;i.length&&i.push(null);let s=n.heights[n.index++];-1==o?o=s:Math.abs(s-o)>=Ls&&(o=-2);let a=new Js(e,s);a.outdated=!1,i.push(a),r+=e+1}r<=s&&i.push(null,new Ks(s-r).updateHeight(t,r));let a=Fs.of(i);return(o<0||Math.abs(a.height-this.height)>=Ls||Math.abs(o-this.heightMetrics(t,e).perLine)>=Ls)&&(t.heightChanged=!0),a}return(i||this.outdated)&&(this.setHeight(t,t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class tr extends Fs{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,n){let s=i+this.left.height;return t<s?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,s,n+this.left.length+this.break)}lineAt(t,e,i,n,s){let r=n+this.left.height,o=s+this.left.length+this.break,a=e==Us.ByHeight?t<r:t<o,l=a?this.left.lineAt(t,e,i,n,s):this.right.lineAt(t,e,i,r,o);if(this.break||(a?l.to<o:l.from>o))return l;let h=e==Us.ByPosNoHeight?Us.ByPosNoHeight:Us.ByPos;return a?l.join(this.right.lineAt(o,h,i,r,o)):this.left.lineAt(o,h,i,n,s).join(l)}forEachLine(t,e,i,n,s,r){let o=n+this.left.height,a=s+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,s,r),e>=a&&this.right.forEachLine(t,e,i,o,a,r);else{let l=this.lineAt(a,Us.ByPos,i,n,s);t<l.from&&this.left.forEachLine(t,l.from-1,i,n,s,r),l.to>=t&&l.from<=e&&r(l),e>l.to&&this.right.forEachLine(l.to+1,e,i,o,a,r)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let s=[];t>0&&this.decomposeLeft(t,s);let r=s.length;for(let t of i)s.push(t);if(t>0&&er(s,r-1),e<this.length){let t=s.length;this.decomposeRight(e,s),er(s,t)}return Fs.of(s)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?Fs.of(this.break?[t,null,e]:[t,e]):(this.left=t,this.right=e,this.height=t.height+e.height,this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:s,right:r}=this,o=e+s.length+this.break,a=null;return n&&n.from<=e+s.length&&n.more?a=s=s.updateHeight(t,e,i,n):s.updateHeight(t,e,i),n&&n.from<=o+r.length&&n.more?a=r=r.updateHeight(t,o,i,n):r.updateHeight(t,o,i),a?this.balanced(s,r):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function er(t,e){let i,n;null==t[e]&&(i=t[e-1])instanceof Ks&&(n=t[e+1])instanceof Ks&&t.splice(e-1,3,new Ks(i.length+1+n.length))}class ir{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof Js?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new Js(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,s=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let r=e-t;i.block?this.addBlock(new Hs(r,n,i)):(r||s||n>=5)&&this.addLineDeco(n,s,r)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Js(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new Ks(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Js)return t;let e=new Js(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof Js||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new Js(0,-1));let i=t;for(let t of this.nodes)t instanceof Js&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,n){let s=new ir(i,t);return ve.spans(e,i,n,s,0),s.finish(i)}}class nr{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&Ki(t,e,this.changes,5)}}function sr(t,e){let i=t.getBoundingClientRect(),n=t.ownerDocument,s=n.defaultView||window,r=Math.max(0,i.left),o=Math.min(s.innerWidth,i.right),a=Math.max(0,i.top),l=Math.min(s.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=n.body;)if(1==e.nodeType){let i=e,n=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=n.overflow){let n=i.getBoundingClientRect();r=Math.max(r,n.left),o=Math.min(o,n.right),a=Math.max(a,n.top),l=e==t.parentNode?n.bottom:Math.min(l,n.bottom)}e="absolute"==n.position||"fixed"==n.position?i.offsetParent:i.parentNode}else{if(11!=e.nodeType)break;e=e.host}return{left:r-i.left,right:Math.max(r,o)-i.left,top:a-(i.top+e),bottom:Math.max(a,l)-(i.top+e)}}function rr(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class or{constructor(t,e,i){this.from=t,this.to=e,this.size=i}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.size!=s.size)return!1}return!0}draw(t,e){return Ui.replace({widget:new ar(this.size*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class ar extends Bi{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class lr{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!0,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=fr,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=sn.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(Mn).some((t=>"function"!=typeof t&&"cm-lineWrapping"==t.class));this.heightOracle=new Is(e),this.stateDeco=t.facet(Dn).filter((t=>"function"!=typeof t)),this.heightMap=Fs.empty().applyChanges(this.stateDeco,N.empty,this.heightOracle.setDoc(t.doc),[new Fn(0,0,0,t.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=Ui.set(this.lineGaps.map((t=>t.draw(this,!1)))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>n>=t&&n<=e))){let{from:e,to:i}=this.lineBlockAt(n);t.push(new hr(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from)),this.scaler=this.heightMap.height<=7e6?fr:new dr(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,(t=>{this.viewportLines.push(1==this.scaler.scale?t:pr(t,this.scaler))}))}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Dn).filter((t=>"function"!=typeof t));let n=t.changedRanges,s=Fn.extendWithRanges(n,function(t,e,i){let n=new nr;return ve.compare(t,e,i,n,0),n.changes}(i,this.stateDeco,t?t.changes:wt.empty(this.state.doc.length))),r=this.heightMap.height,o=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),s),this.heightMap.height!=r&&(t.flags|=2),o?(this.scrollAnchorPos=t.changes.mapPos(o.from,-1),this.scrollAnchorHeight=o.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=s.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let l=!t.changes.empty||2&t.flags||a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,this.updateForViewport(),l&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Cn)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,s=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?sn.RTL:sn.LTR;let r=this.heightOracle.mustRefreshForWrapping(s),o=e.getBoundingClientRect(),a=r||this.mustMeasureContent||this.contentDOMHeight!=o.height;this.contentDOMHeight=o.height,this.mustMeasureContent=!1;let l=0,h=0;if(o.width&&o.height){let{scaleX:t,scaleY:i}=si(e,o);this.scaleX==t&&this.scaleY==i||(this.scaleX=t,this.scaleY=i,l|=8,r=a=!0)}let c=(parseInt(i.paddingTop)||0)*this.scaleY,O=(parseInt(i.paddingBottom)||0)*this.scaleY;this.paddingTop==c&&this.paddingBottom==O||(this.paddingTop=c,this.paddingBottom=O,l|=10),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,l|=8);let u=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=u&&(this.scrollAnchorHeight=-1,this.scrollTop=u),this.scrolledToBottom=ui(t.scrollDOM);let f=(this.printing?rr:sr)(e,this.paddingTop),d=f.top-this.pixelViewport.top,p=f.bottom-this.pixelViewport.bottom;this.pixelViewport=f;let m=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(m!=this.inView&&(this.inView=m,m&&(a=!0)),!this.inView&&!this.scrollTarget)return 0;let g=o.width;if(this.contentDOMWidth==g&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=o.width,this.editorHeight=t.scrollDOM.clientHeight,l|=8),a){let e=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(e)&&(r=!0),r||n.lineWrapping&&Math.abs(g-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:o,textHeight:a}=t.docView.measureTextSize();r=i>0&&n.refresh(s,i,o,a,g/o,e),r&&(t.docView.minWidth=0,l|=8)}d>0&&p>0?h=Math.max(d,p):d<0&&p<0&&(h=Math.min(d,p)),n.heightChanged=!1;for(let i of this.viewports){let s=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(r?Fs.empty().applyChanges(this.stateDeco,N.empty,this.heightOracle,[new Fn(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,r,new Bs(i.from,s))}n.heightChanged&&(l|=2)}let w=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return w&&(this.viewport=this.getViewport(h,this.scrollTarget)),this.updateForViewport(),(2&l||w)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(r?[]:this.lineGaps,t)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,s=this.heightOracle,{visibleTop:r,visibleBottom:o}=this,a=new hr(n.lineAt(r-1e3*i,Us.ByHeight,s,0,0).from,n.lineAt(o+1e3*(1-i),Us.ByHeight,s,0,0).to);if(e){let{head:t}=e.range;if(t<a.from||t>a.to){let i,r=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(t,Us.ByPos,s,0,0);i="center"==e.y?(o.top+o.bottom)/2-r/2:"start"==e.y||"nearest"==e.y&&t<a.from?o.top:o.bottom-r,a=new hr(n.lineAt(i-500,Us.ByHeight,s,0,0).from,n.lineAt(i+r+500,Us.ByHeight,s,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new hr(this.heightMap.lineAt(i,Us.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,Us.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,Us.ByPos,this.heightOracle,0,0),{bottom:s}=this.heightMap.lineAt(e,Us.ByPos,this.heightOracle,0,0),{visibleTop:r,visibleBottom:o}=this;return(0==t||n<=r-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||s>=o+Math.max(10,Math.min(i,250)))&&n>r-2e3&&s<o+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new or(e.mapPos(n.from),e.mapPos(n.to),n.size));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,s=n>>1,r=n<<1;if(this.defaultTextDirection!=sn.LTR&&!i)return[];let o=[],a=(n,r,l,h)=>{if(r-n<s)return;let c=this.state.selection.main,O=[c.from];c.empty||O.push(c.to);for(let t of O)if(t>n&&t<r)return a(n,t-10,l,h),void a(t+10,r,l,h);let u=function(t,e){for(let i of t)if(e(i))return i;return}(t,(t=>t.from>=l.from&&t.to<=l.to&&Math.abs(t.from-n)<s&&Math.abs(t.to-r)<s&&!O.some((e=>t.from<e&&t.to>e))));if(!u){if(r<l.to&&e&&i&&e.visibleRanges.some((t=>t.from<=r&&t.to>=r))){let t=e.moveToLineBoundary($t.cursor(r),!1,!0).head;t>n&&(r=t)}u=new or(n,r,this.gapSize(l,n,r,h))}o.push(u)};for(let t of this.viewportLines){if(t.length<r)continue;let e=cr(t.from,t.to,this.stateDeco);if(e.total<r)continue;let s,o,l=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let i,r,a=n/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=l){let n=ur(e,l),s=((this.visibleBottom-this.visibleTop)/2+a)/t.height;i=n-s,r=n+s}else i=(this.visibleTop-t.top-a)/t.height,r=(this.visibleBottom-t.top+a)/t.height;s=Or(e,i),o=Or(e,r)}else{let t,i,r=e.total*this.heightOracle.charWidth,a=n*this.heightOracle.charWidth;if(null!=l){let n=ur(e,l),s=((this.pixelViewport.right-this.pixelViewport.left)/2+a)/r;t=n-s,i=n+s}else t=(this.pixelViewport.left-a)/r,i=(this.pixelViewport.right+a)/r;s=Or(e,t),o=Or(e,i)}s>t.from&&a(t.from,s,t,e),o<t.to&&a(o,t.to,t,e)}return o}gapSize(t,e,i,n){let s=ur(n,i)-ur(n,e);return this.heightOracle.lineWrapping?t.height*s:n.total*this.heightOracle.charWidth*s}updateLineGaps(t){or.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=Ui.set(t.map((t=>t.draw(this,this.heightOracle.lineWrapping)))))}computeVisibleRanges(){let t=this.stateDeco;this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let e=[];ve.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));return this.visibleRanges=e,i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||pr(this.heightMap.lineAt(t,Us.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return pr(this.heightMap.lineAt(this.scaler.fromDOM(t),Us.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return pr(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class hr{constructor(t,e){this.from=t,this.to=e}}function cr(t,e,i){let n=[],s=t,r=0;return ve.spans(i,t,e,{span(){},point(t,e){t>s&&(n.push({from:s,to:t}),r+=t-s),s=e}},20),s<e&&(n.push({from:s,to:e}),r+=e-s),{total:r,ranges:n}}function Or({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let n=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:s}=e[t],r=s-i;if(n<=r)return i+n;n-=r}}function ur(t,e){let i=0;for(let{from:n,to:s}of t.ranges){if(e<=s){i+=e-n;break}i+=s-n}return i/t.total}const fr={toDOM:t=>t,fromDOM:t=>t,scale:1};class dr{constructor(t,e,i){let n=0,s=0,r=0;this.viewports=i.map((({from:i,to:s})=>{let r=e.lineAt(i,Us.ByPos,t,0,0).top,o=e.lineAt(s,Us.ByPos,t,0,0).bottom;return n+=o-r,{from:i,to:s,top:r,bottom:o,domTop:0,domBottom:0}})),this.scale=(7e6-n)/(e.height-n);for(let t of this.viewports)t.domTop=r+(t.top-s)*this.scale,r=t.domBottom=t.domTop+(t.bottom-t.top),s=t.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.top)return n+(t-i)*this.scale;if(t<=s.bottom)return s.domTop+(t-s.top);i=s.bottom,n=s.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.domTop)return i+(t-n)/this.scale;if(t<=s.domBottom)return s.top+(t-s.domTop);i=s.bottom,n=s.domBottom}}}function pr(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),n=e.toDOM(t.bottom);return new Ns(t.from,t.length,i,n-i,Array.isArray(t._content)?t._content.map((t=>pr(t,e))):t._content)}const mr=Tt.define({combine:t=>t.join(" ")}),gr=Tt.define({combine:t=>t.indexOf(!0)>-1}),wr=_e.newName(),br=_e.newName(),Sr=_e.newName(),yr={"&light":"."+br,"&dark":"."+Sr};function Qr(t,e,i){return new _e(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,(e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e})}const vr=Qr("."+wr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},yr),xr="￿";class kr{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(ge.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=xr}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let t=this.text.length;this.readNode(n);let s=n.nextSibling;if(s==e)break;let r=pi.get(n),o=pi.get(s);(r&&o?r.breakAfter:(r?r.breakAfter:Pr(n))||Pr(s)&&("BR"!=n.nodeName||n.cmIgnore)&&this.text.length>t)&&this.lineBreak(),n=s}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let s,r=-1,o=1;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(s=n.exec(e))&&(r=s.index,o=s[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=pi.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+($r(t,i.node,i.offset)?e:0))}}function $r(t,e,i){for(;;){if(!e||i<ei(e))return!1;if(e==t)return!0;i=Ke(e)+1,e=e.parentNode}}function Pr(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}class Zr{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Tr{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="";let{impreciseHead:s,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=s||r?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r}=t.observer.selectionRange;i&&(e.push(new Zr(i,n)),s==i&&r==n||e.push(new Zr(s,r)));return e}(t),i=new kr(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,n=2==t.length?t[1].pos:i;return i>-1&&n>-1?$t.single(i+e,n+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=s&&s.node==e.focusNode&&s.offset==e.focusOffset||!Le(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),n=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!Le(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),o=t.viewport;if(Ci.ios&&t.state.selection.main.empty&&i!=n&&(o.from>0||o.to<t.state.doc.length)){let e=o.from-Math.min(i,n),s=o.to-Math.max(i,n);0!=e&&1!=e||0!=s&&-1!=s||(i=0,n=t.state.doc.length)}this.newSel=$t.single(n,i)}}}function Xr(t,e){let i,{newSel:n}=e,s=t.state.selection.main,r=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:n,to:o}=e.bounds,a=s.from,l=null;(8===r||Ci.android&&e.text.length<o-n)&&(a=s.to,l="end");let h=function(t,e,i,n){let s=Math.min(t.length,e.length),r=0;for(;r<s&&t.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&t.length==e.length)return null;let o=t.length,a=e.length;for(;o>0&&a>0&&t.charCodeAt(o-1)==e.charCodeAt(a-1);)o--,a--;if("end"==n){i-=o+Math.max(0,r-Math.min(o,a))-r}if(o<r&&t.length<e.length){r-=i<=r&&i>=o?r-i:0,a=r+(a-o),o=r}else if(a<r){r-=i<=r&&i>=a?r-i:0,o=r+(o-a),a=r}return{from:r,toA:o,toB:a}}(t.state.doc.sliceString(n,o,xr),e.text,a-n,l);h&&(Ci.chrome&&13==r&&h.toB==h.from+2&&e.text.slice(h.from,h.toB)==xr+xr&&h.toB--,i={from:n+h.from,to:n+h.toA,insert:N.of(e.text.slice(h.from,h.toB).split(xr))})}else n&&(!t.hasFocus&&t.state.facet(Wn)||n.main.eq(s))&&(n=null);if(!i&&!n)return!1;if(!i&&e.typeOver&&!s.empty&&n&&n.main.empty?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,s.to)}:i&&i.from>=s.from&&i.to<=s.to&&(i.from!=s.from||i.to!=s.to)&&s.to-s.from-(i.to-i.from)<=4?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,s.to))}:(Ci.mac||Ci.android)&&i&&i.from==i.to&&i.from==s.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(n&&2==i.insert.length&&(n=$t.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:N.of([" "])}):Ci.chrome&&i&&i.from==i.to&&i.from==s.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(n&&(n=$t.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:N.of([" "])}),i){if(Ci.ios&&t.inputState.flushIOSKey())return!0;if(Ci.android&&(i.from==s.from&&i.to==s.to&&1==i.insert.length&&2==i.insert.lines&&ci(t.contentDOM,"Enter",13)||(i.from==s.from-1&&i.to==s.to&&0==i.insert.length||8==r&&i.insert.length<i.to-i.from&&i.to>s.head)&&ci(t.contentDOM,"Backspace",8)||i.from==s.from&&i.to==s.to+1&&0==i.insert.length&&ci(t.contentDOM,"Delete",46)))return!0;let e,o=i.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let a=()=>e||(e=function(t,e,i){let n,s=t.state,r=s.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=r.from<e.from?s.sliceDoc(r.from,e.from):"",o=r.to>e.to?s.sliceDoc(e.to,r.to):"";n=s.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+o))}else{let o=s.changes(e),a=i&&i.main.to<=o.newLength?i.main:void 0;if(s.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let l,h=t.state.sliceDoc(e.from,e.to),c=i&&ts(t,i.main.head);if(c){let t=e.insert.length-(e.to-e.from);l={from:c.from,to:c.to-t}}else l=t.state.doc.lineAt(r.head);let O=r.to-e.to,u=r.to-r.from;n=s.changeByRange((i=>{if(i.from==r.from&&i.to==r.to)return{changes:o,range:a||i.map(o)};let n=i.to-O,c=n-h.length;if(i.to-i.from!=u||t.state.sliceDoc(c,n)!=h||i.to>=l.from&&i.from<=l.to)return{range:i};let f=s.changes({from:c,to:n,insert:e.insert}),d=i.to-r.to;return{changes:f,range:a?$t.range(Math.max(0,a.anchor+d),Math.max(0,a.head+d)):i.map(f)}}))}else n={changes:o,selection:a&&s.selection.replaceRange(a)}}let o="input.type";(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,o+=".compose",t.inputState.compositionFirstChange&&(o+=".start",t.inputState.compositionFirstChange=!1));return s.update(n,{userEvent:o,scrollIntoView:!0})}(t,i,n));return t.state.facet(Zn).some((e=>e(t,i.from,i.to,o,a)))||t.dispatch(a()),!0}if(n&&!n.main.eq(s)){let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:n,scrollIntoView:e,userEvent:i}),!0}return!1}const Cr={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Yr=Ci.ie&&Ci.ie_version<=11;class Ar{constructor(t){this.view=t,this.active=!1,this.selectionRange=new ri,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);(Ci.ie&&Ci.ie_version<=11||Ci.ios&&t.composing)&&e.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),Yr&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver((()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()})),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver((t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))}),{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver((t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))}),{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1,this.view.requestMeasure()}),50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout((()=>{this.view.viewState.printing=!1,this.view.requestMeasure()}),500)}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(Wn)?i.root.activeElement!=this.dom:!Fe(i.dom,n))return;let s=n.anchorNode&&i.docView.nearest(n.anchorNode);s&&s.ignoreEvent(t)?e||(this.selectionChanged=!1):(Ci.ie&&Ci.ie_version<=11||Ci.android&&Ci.chrome)&&!i.state.selection.main.empty&&n.focusNode&&Je(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Ci.safari&&11==t.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t){let e=null;function i(t){t.preventDefault(),t.stopImmediatePropagation(),e=t.getTargetRanges()[0]}if(t.contentDOM.addEventListener("beforeinput",i,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",i,!0),!e)return null;let n=e.startContainer,s=e.startOffset,r=e.endContainer,o=e.endOffset,a=t.docView.domAtPos(t.state.selection.main.anchor);Je(a.node,a.offset,r,o)&&([n,s,r,o]=[r,o,n,s]);return{anchorNode:n,anchorOffset:s,focusNode:r,focusOffset:o}}(this.view)||Ue(t.root);if(!e||this.selectionRange.eq(e))return!1;let i=Fe(this.dom,e);return i&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,n=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=n)return!1;for(n=Math.min(n,ei(i));;)if(n){if(1!=i.nodeType)return!1;let t=i.childNodes[n-1];"false"==t.contentEditable?n--:(i=t,n=ei(i))}else{if(i==t)return!0;n=Ke(i),i=i.parentNode}}(this.dom,e)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(e),i&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,Cr),Yr&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Yr&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;if(t){this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&ci(this.dom,t.key,t.keyCode)}};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame((()=>{this.delayedFlush=-1,this.flush()})))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let s of t){let t=this.readMutation(s);t&&(t.typeOver&&(n=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&Fe(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let s=new Tr(this.view,t,e,i);return this.view.docView.domChanged={newSel:s.newSel?s.newSel.main:null},s}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Xr(this.view,e);return this.view.state==i&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=Rr(e,t.previousSibling||t.target.previousSibling,-1),n=Rr(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var t,e,i;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect();for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function Rr(t,e,i){for(;e;){let n=pi.get(e);if(n&&n.parent==t)return n;let s=e.parentNode;e=s!=t.dom?s:i>0?e.nextSibling:e.previousSibling}return null}class Wr{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:e}=t;this.dispatchTransactions=t.dispatchTransactions||e&&(t=>t.forEach((t=>e(t,this))))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new lr(t.state||ge.create(t)),t.scrollTo&&t.scrollTo.is(An)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(jn).map((t=>new _n(t)));for(let t of this.plugins)t.update(this);this.observer=new Ar(this),this.inputState=new ms(this),this.inputState.ensureHandlers(this.plugins),this.docView=new Jn(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure()}dispatch(...t){let e=1==t.length&&t[0]instanceof oe?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,n=!1,s=this.state;for(let e of t){if(e.startState!=s)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");s=e.state}if(this.destroyed)return void(this.viewState.state=s);let r=this.hasFocus,o=0,a=null;t.some((t=>t.annotation(Es)))?(this.inputState.notifiedFocused=r,o=1):r!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=r,a=Ms(s,r),a||(o=1));let l=this.observer.delayedAndroidKey,h=null;if(l?(this.observer.clearDelayedAndroidKey(),h=this.observer.readChange(),(h&&!this.state.doc.eq(s.doc)||!this.state.selection.eq(s.selection))&&(h=null)):this.observer.clear(),s.facet(ge.phrases)!=this.state.facet(ge.phrases))return this.setState(s);e=Hn.create(this,s,t),e.flags|=o;let c=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(c&&(c=c.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;c=new Yn(t.empty?t:$t.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(An)&&(c=t.value.clip(this.state))}this.viewState.update(e,c),this.bidiCache=qr.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(Ln)!=this.styleModules&&this.mountStyles(),n=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(e.startState.facet(mr)!=e.state.facet(mr)&&(this.viewState.mustMeasureContent=!0),(i||n||c||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!e.empty)for(let t of this.state.facet(Pn))try{t(e)}catch(t){Rn(this.state,t,"update listener")}(a||h)&&Promise.resolve().then((()=>{a&&this.state==a.startState&&this.dispatch(a),h&&!Xr(this,h)&&l.force&&ci(this.contentDOM,l.key,l.keyCode)}))}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new lr(t),this.plugins=t.facet(jn).map((t=>new _n(t))),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView.destroy(),this.docView=new Jn(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(jn),i=t.state.facet(jn);if(e!=i){let n=[];for(let s of i){let i=e.indexOf(s);if(i<0)n.push(new _n(s));else{let e=this.plugins[i];e.mustUpdate=t,n.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey)return this.measureScheduled=-1,void this.requestMeasure();this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:s,scrollAnchorHeight:r}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(r=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(r<0)if(ui(i))s=-1,r=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(n);s=t.from,r=t.top}this.updateState=1;let o=this.viewState.measure(this);if(!o&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let a=[];4&o||([this.measureRequests,a]=[a,this.measureRequests]);let l=a.map((t=>{try{return t.read(this)}catch(t){return Rn(this.state,t),jr}})),h=Hn.create(this,this.state,[]),c=!1;h.flags|=o,e?e.flags|=o:e=h,this.updateState=2,h.empty||(this.updatePlugins(h),this.inputState.update(h),this.updateAttrs(),c=this.docView.update(h));for(let t=0;t<a.length;t++)if(l[t]!=jr)try{let e=a[t];e.write&&e.write(l[t],this)}catch(t){Rn(this.state,t)}if(c&&this.docView.updateSelection(!0),!h.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,r=-1;continue}{let t=(s<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(s).top)-r;if(t>1||t<-1){n+=t,i.scrollTop=n/this.scaleY,r=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(Pn))t(e)}get themeClasses(){return wr+" "+(this.state.facet(gr)?Sr:br)+" "+this.state.facet(mr)}updateAttrs(){let t=_r(this,En,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(Wn)?"true":"false",class:"cm-content",style:`${Ci.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),_r(this,Mn,e);let i=this.observer.ignore((()=>{let i=Di(this.contentDOM,this.contentAttrs,e),n=Di(this.dom,this.editorAttrs,t);return i||n}));return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)if(t.is(Wr.announce)){e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value}}mountStyles(){this.styleModules=this.state.facet(Ln);let t=this.state.facet(Wr.cspNonce);_e.mount(this.root,this.styleModules.concat(vr).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame((()=>this.measure()))),t){if(this.measureRequests.indexOf(t)>-1)return;if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return ps(this,t,fs(this,t,e,i))}moveByGroup(t,e){return ps(this,t,fs(this,t,e,(e=>function(t,e,i){let n=t.state.charCategorizer(e),s=n(i);return t=>{let e=n(t);return s==fe.Space&&(s=e),s==e}}(this,t.head,e))))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),s=i[e?i.length-1:0];return $t.cursor(s.side(e,n)+t.from,s.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,n){let s=us(t,e.head),r=n&&s.type==Ni.Text&&(t.lineWrapping||s.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head):null;if(r){let e=t.dom.getBoundingClientRect(),n=t.textDirectionAt(s.from),o=t.posAtCoords({x:i==(n==sn.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(null!=o)return $t.cursor(o,i?-1:1)}return $t.cursor(i?s.to:s.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return ps(this,t,function(t,e,i,n){let s=e.head,r=i?1:-1;if(s==(i?t.state.doc.length:0))return $t.cursor(s,e.assoc);let o,a=e.goalColumn,l=t.contentDOM.getBoundingClientRect(),h=t.coordsAtPos(s,e.assoc||-1),c=t.documentTop;if(h)null==a&&(a=h.left-l.left),o=r<0?h.top:h.bottom;else{let e=t.viewState.lineBlockAt(s);null==a&&(a=Math.min(l.right-l.left,t.defaultCharacterWidth*(s-e.from))),o=(r<0?e.top:e.bottom)+c}let O=l.left+a,u=null!=n?n:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=o+(u+e)*r,n=cs(t,{x:O,y:i},!1,r);if(i<l.top||i>l.bottom||(r<0?n<s:n>s)){let e=t.docView.coordsForChar(n),s=!e||i<e.top?-1:1;return $t.cursor(n,s,void 0,a)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),cs(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),s=this.bidiSpans(n);return ii(i,s[dn.find(s,t-n.from,-1,e)].dir==sn.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Xn)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Vr)return bn(t.length);let e,i=this.textDirectionAt(t.from);for(let n of this.bidiCache)if(n.from==t.from&&n.dir==i&&(n.fresh||pn(n.isolates,e=Bn(this,t))))return n.order;e||(e=Bn(this,t));let n=function(t,e,i){if(!t)return[new dn(0,0,e==on?1:0)];if(e==rn&&!i.length&&!fn.test(t))return bn(t.length);if(i.length)for(;t.length>mn.length;)mn[mn.length]=256;let n=[],s=e==rn?0:1;return wn(t,s,s,i,0,t.length,n),n}(t.text,i,e);return this.bidiCache.push(new qr(t.from,t.to,i,e,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Ci.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{li(this.contentDOM),this.docView.updateSelection()}))}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return An.of(new Yn("number"==typeof t?$t.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return An.of(new Yn($t.cursor(i.from),"start","start",i.top-t,e,!0))}static domEventHandlers(t){return qn.define((()=>({})),{eventHandlers:t})}static domEventObservers(t){return qn.define((()=>({})),{eventObservers:t})}static theme(t,e){let i=_e.newName(),n=[mr.of(i),Ln.of(Qr(`.${i}`,t))];return e&&e.dark&&n.push(gr.of(!0)),n}static baseTheme(t){return Dt.lowest(Ln.of(Qr("."+wr,t,yr)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&pi.get(i)||pi.get(t);return(null===(e=null==n?void 0:n.rootView)||void 0===e?void 0:e.view)||null}}Wr.styleModule=Ln,Wr.inputHandler=Zn,Wr.focusChangeEffect=Tn,Wr.perLineTextDirection=Xn,Wr.exceptionSink=$n,Wr.updateListener=Pn,Wr.editable=Wn,Wr.mouseSelectionStyle=kn,Wr.dragMovesSelection=xn,Wr.clickAddsSelectionRange=vn,Wr.decorations=Dn,Wr.outerDecorations=zn,Wr.atomicRanges=Gn,Wr.bidiIsolatedRanges=In,Wr.scrollMargins=Nn,Wr.darkTheme=gr,Wr.cspNonce=Tt.define({combine:t=>t.length?t[0]:""}),Wr.contentAttributes=Mn,Wr.editorAttributes=En,Wr.lineWrapping=Wr.contentAttributes.of({class:"cm-lineWrapping"}),Wr.announce=re.define();const Vr=4096,jr={};class qr{constructor(t,e,i,n,s,r){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=s,this.order=r}static update(t,e){if(e.empty&&!t.some((t=>t.fresh)))return t;let i=[],n=t.length?t[t.length-1].dir:sn.LTR;for(let s=Math.max(0,t.length-10);s<t.length;s++){let r=t[s];r.dir!=n||e.touchesRange(r.from,r.to)||i.push(new qr(e.mapPos(r.from,1),e.mapPos(r.to,-1),r.dir,r.isolates,!1,r.order))}return i}}function _r(t,e,i){for(let n=t.state.facet(e),s=n.length-1;s>=0;s--){let e=n[s],r="function"==typeof e?e(t):e;r&&_i(r,i)}return i}const Er=Ci.mac?"mac":Ci.windows?"win":Ci.linux?"linux":"key";function Mr(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const Dr=Dt.default(Wr.domEventHandlers({keydown:(t,e)=>function(t,e,i,n){let s=function(t){var e=!(Ge&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||Ie&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?ze:De)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(e),r=ut(s,0),o=dt(r)==s.length&&" "!=s,a="",l=!1,h=!1,c=!1;Ir&&Ir.view==i&&Ir.scope==n&&(a=Ir.prefix+" ",ys.indexOf(e.keyCode)<0&&(h=!0,Ir=null));let O,u,f=new Set,d=t=>{if(t){for(let n of t.run)if(!f.has(n)&&(f.add(n),n(i,e)))return t.stopPropagation&&(c=!0),!0;t.preventDefault&&(t.stopPropagation&&(c=!0),h=!0)}return!1},p=t[n];p&&(d(p[a+Mr(s,e,!o)])?l=!0:o&&(e.altKey||e.metaKey||e.ctrlKey)&&!(Ci.windows&&e.ctrlKey&&e.altKey)&&(O=De[e.keyCode])&&O!=s?(d(p[a+Mr(O,e,!0)])||e.shiftKey&&(u=ze[e.keyCode])!=s&&u!=O&&d(p[a+Mr(u,e,!1)]))&&(l=!0):o&&e.shiftKey&&d(p[a+Mr(s,e,!0)])&&(l=!0),!l&&d(p._any)&&(l=!0));h&&(l=!0);l&&c&&e.stopPropagation();return l}(function(t){let e=t.facet(zr),i=Gr.get(e);i||Gr.set(e,i=function(t,e=Er){let i=Object.create(null),n=Object.create(null),s=(t,e)=>{let i=n[t];if(null==i)n[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},r=(t,n,r,o,a)=>{var l,h;let c=i[t]||(i[t]=Object.create(null)),O=n.split(/ (?!$)/).map((t=>function(t,e){const i=t.split(/-(?!$)/);let n,s,r,o,a=i[i.length-1];"Space"==a&&(a=" ");for(let t=0;t<i.length-1;++t){const a=i[t];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))s=!0;else if(/^s(hift)?$/i.test(a))r=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);"mac"==e?o=!0:s=!0}}return n&&(a="Alt-"+a),s&&(a="Ctrl-"+a),o&&(a="Meta-"+a),r&&(a="Shift-"+a),a}(t,e)));for(let e=1;e<O.length;e++){let i=O.slice(0,e).join(" ");s(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let n=Ir={view:e,prefix:i,scope:t};return setTimeout((()=>{Ir==n&&(Ir=null)}),Br),!0}]})}let u=O.join(" ");s(u,!1);let f=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(h=null===(l=c._any)||void 0===l?void 0:l.run)||void 0===h?void 0:h.slice())||[]});r&&f.run.push(r),o&&(f.preventDefault=!0),a&&(f.stopPropagation=!0)};for(let n of t){let t=n.scope?n.scope.split(" "):["editor"];if(n.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});for(let e in t)t[e].run.push(n.any)}let s=n[e]||n.key;if(s)for(let e of t)r(e,s,n.run,n.preventDefault,n.stopPropagation),n.shift&&r(e,"Shift-"+s,n.shift,n.preventDefault,n.stopPropagation)}return i}(e.reduce(((t,e)=>t.concat(e)),[])));return i}(e.state),t,e,"editor")})),zr=Tt.define({enables:Dr}),Gr=new WeakMap;let Ir=null;const Br=4e3;class Nr{constructor(t,e,i,n,s){this.className=t,this.left=e,this.top=i,this.width=n,this.height=s}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let s=Ur(t);return[new Nr(e,n.left-s.left,n.top-s.top,null,n.bottom-n.top)]}return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let n=Math.max(i.from,t.viewport.from),s=Math.min(i.to,t.viewport.to),r=t.textDirection==sn.LTR,o=t.contentDOM,a=o.getBoundingClientRect(),l=Ur(t),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),O=a.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=a.right-(c?parseInt(c.paddingRight):0),f=us(t,n),d=us(t,s),p=f.type==Ni.Text?f:null,m=d.type==Ni.Text?d:null;p&&(t.lineWrapping||f.widgetLineBreaks)&&(p=Lr(t,n,p));m&&(t.lineWrapping||d.widgetLineBreaks)&&(m=Lr(t,s,m));if(p&&m&&p.from==m.from)return w(b(i.from,i.to,p));{let e=p?b(i.from,null,p):S(f,!1),n=m?b(null,i.to,m):S(d,!0),s=[];return(p||f).to<(m||d).from-(p&&m?1:0)||f.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<n.top?s.push(g(O,e.bottom,u,n.top)):e.bottom<n.top&&t.elementAtHeight((e.bottom+n.top)/2).type==Ni.Text&&(e.bottom=n.top=(e.bottom+n.top)/2),w(e).concat(s).concat(w(n))}function g(t,i,n,s){return new Nr(e,t-l.left,i-l.top-.01,n-t,s-i+.01)}function w({top:t,bottom:e,horizontal:i}){let n=[];for(let s=0;s<i.length;s+=2)n.push(g(i[s],t,i[s+1],e));return n}function b(e,i,n){let s=1e9,o=-1e9,a=[];function l(e,i,l,h,c){let f=t.coordsAtPos(e,e==n.to?-2:2),d=t.coordsAtPos(l,l==n.from?2:-2);f&&d&&(s=Math.min(f.top,d.top,s),o=Math.max(f.bottom,d.bottom,o),c==sn.LTR?a.push(r&&i?O:f.left,r&&h?u:d.right):a.push(!r&&h?O:d.left,!r&&i?u:f.right))}let h=null!=e?e:n.from,c=null!=i?i:n.to;for(let n of t.visibleRanges)if(n.to>h&&n.from<c)for(let s=Math.max(n.from,h),r=Math.min(n.to,c);;){let n=t.state.doc.lineAt(s);for(let o of t.bidiSpans(n)){let t=o.from+n.from,a=o.to+n.from;if(t>=r)break;a>s&&l(Math.max(t,s),null==e&&t<=h,Math.min(a,r),null==i&&a>=c,o.dir)}if(s=n.to+1,s>=r)break}return 0==a.length&&l(h,null==e,c,null==i,t.textDirection),{top:s,bottom:o,horizontal:a}}function S(t,e){let i=a.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i)}}function Ur(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==sn.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function Lr(t,e,i){let n=$t.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(n,!1,!0).from),to:Math.min(i.to,t.moveToLineBoundary(n,!0,!0).from),type:Ni.Text}}class Fr{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(Hr)!=t.state.facet(Hr)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}setOrder(t){let e=0,i=t.facet(Hr);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;t==this.scaleX&&e==this.scaleY||(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some(((t,e)=>{return i=t,n=this.drawn[e],!(i.constructor==n.constructor&&i.eq(n));var i,n}))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const Hr=Tt.define();function Jr(t){return[qn.define((e=>new Fr(e,t))),Hr.of(t)]}const Kr=!Ci.ios,to=Tt.define({combine:t=>we(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function eo(t={}){return[to.of(t),no,ro,ao,Cn.of(!0)]}function io(t){return t.startState.facet(to)!=t.state.facet(to)}const no=Jr({above:!0,markers(t){let{state:e}=t,i=e.facet(to),n=[];for(let s of e.selection.ranges){let r=s==e.selection.main;if(s.empty?!r||Kr:i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=s.empty?s:$t.cursor(s.head,s.head>s.anchor?-1:1);for(let s of Nr.forRange(t,e,i))n.push(s)}}return n},update(t,e){t.transactions.some((t=>t.selection))&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=io(t);return i&&so(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){so(e.state,t)},class:"cm-cursorLayer"});function so(t,e){e.style.animationDuration=t.facet(to).cursorBlinkRate+"ms"}const ro=Jr({above:!1,markers:t=>t.state.selection.ranges.map((e=>e.empty?[]:Nr.forRange(t,"cm-selectionBackground",e))).reduce(((t,e)=>t.concat(e))),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||io(t),class:"cm-selectionLayer"}),oo={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};Kr&&(oo[".cm-line"].caretColor="transparent !important",oo[".cm-content"]={caretColor:"transparent !important"});const ao=Dt.highest(Wr.theme(oo));function lo(t,e,i,n,s){e.lastIndex=0;for(let r,o=t.iterRange(i,n),a=i;!o.next().done;a+=o.value.length)if(!o.lineBreak)for(;r=e.exec(o.value);)s(a+r.index,r)}class ho{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:s,maxLength:r=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(t,e,i,s)=>n(s,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,n,s)=>{let r=i(t,e,n);r&&s(n,n+t[0].length,r)};else{if(!i)throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.addMatch=(t,e,n,s)=>s(n,n+t[0].length,i)}this.boundary=s,this.maxLength=r}createDeco(t){let e=new xe,i=e.add.bind(e);for(let{from:e,to:n}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let n=[];for(let{from:s,to:r}of i)s=Math.max(t.state.doc.lineAt(s).from,s-e),r=Math.min(t.state.doc.lineAt(r).to,r+e),n.length&&n[n.length-1].to>=s?n[n.length-1].to=r:n.push({from:s,to:r});return n}(t,this.maxLength))lo(t.state.doc,this.regexp,e,n,((e,n)=>this.addMatch(n,t,e,i)));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges(((e,s,r,o)=>{o>t.view.viewport.from&&r<t.view.viewport.to&&(i=Math.min(r,i),n=Math.max(o,n))})),t.viewportChanged||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let s of t.visibleRanges){let r=Math.max(s.from,i),o=Math.min(s.to,n);if(o>r){let i=t.state.doc.lineAt(r),n=i.to<o?t.state.doc.lineAt(o):i,a=Math.max(s.from,i.from),l=Math.min(s.to,n.to);if(this.boundary){for(;r>i.from;r--)if(this.boundary.test(i.text[r-1-i.from])){a=r;break}for(;o<n.to;o++)if(this.boundary.test(n.text[o-n.from])){l=o;break}}let h,c=[],O=(t,e,i)=>c.push(i.range(t,e));if(i==n)for(this.regexp.lastIndex=a-i.from;(h=this.regexp.exec(i.text))&&h.index<l-i.from;)this.addMatch(h,t,h.index+i.from,O);else lo(t.state.doc,this.regexp,a,l,((e,i)=>this.addMatch(i,t,e,O)));e=e.update({filterFrom:a,filterTo:l,filter:(t,e)=>t<a||e>l,add:c})}}return e}}const co=null!=/x/.unicode?"gu":"g",Oo=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\ufeff￹-￼]",co),uo={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let fo=null;const po=Tt.define({combine(t){let e=we(t,{render:null,specialChars:Oo,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==fo&&"undefined"!=typeof document&&document.body){let e=document.body.style;fo=null!=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)}return fo||!1}())&&(e.specialChars=new RegExp("\t|"+e.specialChars.source,co)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,co)),e}});function mo(t={}){return[po.of(t),go||(go=qn.fromClass(class{constructor(t){this.view=t,this.decorations=Ui.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(po)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new ho({regexp:t.specialChars,decoration:(e,i,n)=>{let{doc:s}=i.state,r=ut(e[0],0);if(9==r){let t=s.lineAt(n),e=i.state.tabSize,r=We(t.text,e,n-t.from);return Ui.replace({widget:new bo((e-r%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=Ui.replace({widget:new wo(t,r)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(po);t.startState.facet(po)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let go=null;class wo extends Bi{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=function(t){return t>=32?"•":10==t?"␤":String.fromCharCode(9216+t)}(this.code),i=t.state.phrase("Control character")+" "+(uo[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let s=document.createElement("span");return s.textContent=e,s.title=i,s.setAttribute("aria-label",i),s.className="cm-specialChar",s}ignoreEvent(){return!1}}class bo extends Bi{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="\t",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}const So=qn.fromClass(class{constructor(){this.height=1e3,this.attrs={style:"padding-bottom: 1000px"}}update(t){let{view:e}=t,i=e.viewState.editorHeight*e.scaleY-e.defaultLineHeight-e.documentPadding.top-.5;i>=0&&i!=this.height&&(this.height=i,this.attrs={style:`padding-bottom: ${i}px`})}});function yo(){return[So,Mn.of((t=>{var e;return(null===(e=t.plugin(So))||void 0===e?void 0:e.attrs)||null}))]}class Qo extends Bi{constructor(t){super(),this.content=t}toDOM(){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild("string"==typeof this.content?document.createTextNode(this.content):this.content),"string"==typeof this.content?t.setAttribute("aria-label","placeholder "+this.content):t.setAttribute("aria-hidden","true"),t}coordsAt(t){let e=t.firstChild?He(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),n=ii(e[0],"rtl"!=i.direction),s=parseInt(i.lineHeight);return n.bottom-n.top>1.5*s?{left:n.left,right:n.right,top:n.top,bottom:n.top+s}:n}ignoreEvent(){return!1}}function vo(t){return qn.fromClass(class{constructor(e){this.view=e,this.placeholder=t?Ui.set([Ui.widget({widget:new Qo(t),side:1}).range(0)]):Ui.none}get decorations(){return this.view.state.doc.length?Ui.none:this.placeholder}},{decorations:t=>t.decorations})}const xo="-10000px";class ko{constructor(t,e,i){this.facet=e,this.createTooltipView=i,this.input=t.state.facet(e),this.tooltips=this.input.filter((t=>t)),this.tooltipViews=this.tooltips.map(i)}update(t,e){var i;let n=t.state.facet(this.facet),s=n.filter((t=>t));if(n===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let r=[],o=e?[]:null;for(let i=0;i<s.length;i++){let n=s[i],a=-1;if(n){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==n.create&&(a=t)}if(a<0)r[i]=this.createTooltipView(n),o&&(o[i]=!!n.above);else{let n=r[i]=this.tooltipViews[a];o&&(o[i]=e[a]),n.update&&n.update(t)}}}for(let t of this.tooltipViews)r.indexOf(t)<0&&(t.dom.remove(),null===(i=t.destroy)||void 0===i||i.call(t));return e&&(o.forEach(((t,i)=>e[i]=t)),e.length=o.length),this.input=n,this.tooltips=s,this.tooltipViews=r,!0}}function $o(t={}){return Zo.of(t)}function Po(t){let{win:e}=t;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const Zo=Tt.define({combine:t=>{var e,i,n;return{position:Ci.ios?"absolute":(null===(e=t.find((t=>t.position)))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find((t=>t.parent)))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(n=t.find((t=>t.tooltipSpace)))||void 0===n?void 0:n.tooltipSpace)||Po}}}),To=new WeakMap,Xo=qn.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(Zo);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new ko(t,Ao,(t=>this.createTooltip(t))),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver((t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()}),{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1,this.maybeMeasure()}),50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,n=t.state.facet(Zo);if(n.position!=this.position&&!this.madeAbsolute){this.position=n.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=!0}if(n.parent!=this.parent){this.parent&&this.container.remove(),this.parent=n.parent,this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t){let e=t.create(this.view);if(e.dom.classList.add("cm-tooltip"),t.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top=xo,e.dom.style.left="0px",this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let e of this.manager.tooltipViews)e.dom.remove(),null===(t=e.destroy)||void 0===t||t.call(e);this.parent&&this.container.remove(),null===(e=this.intersectionObserver)||void 0===e||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect(),e=1,i=1,n=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{dom:t}=this.manager.tooltipViews[0];if(Ci.gecko)n=t.offsetParent!=this.container.ownerDocument.body;else if(t.style.top==xo&&"0px"==t.style.left){let e=t.getBoundingClientRect();n=Math.abs(e.top+1e4)>1||Math.abs(e.left)>1}}if(n||"absolute"==this.position)if(this.parent){let t=this.parent.getBoundingClientRect();t.width&&t.height&&(e=t.width/this.parent.offsetWidth,i=t.height/this.parent.offsetHeight)}else({scaleX:e,scaleY:i}=this.view.viewState);return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map(((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)})),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(Zo).tooltipSpace(this.view),scaleX:e,scaleY:i,makeAbsolute:n}}writeMeasure(t){var e;if(t.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let t of this.manager.tooltipViews)t.dom.style.position="absolute"}let{editor:i,space:n,scaleX:s,scaleY:r}=t,o=[];for(let a=0;a<this.manager.tooltips.length;a++){let l=this.manager.tooltips[a],h=this.manager.tooltipViews[a],{dom:c}=h,O=t.pos[a],u=t.size[a];if(!O||O.bottom<=Math.max(i.top,n.top)||O.top>=Math.min(i.bottom,n.bottom)||O.right<Math.max(i.left,n.left)-.1||O.left>Math.min(i.right,n.right)+.1){c.style.top=xo;continue}let f=l.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,d=f?7:0,p=u.right-u.left,m=null!==(e=To.get(h))&&void 0!==e?e:u.bottom-u.top,g=h.offset||Yo,w=this.view.textDirection==sn.LTR,b=u.width>n.right-n.left?w?n.left:n.right-u.width:w?Math.min(O.left-(f?14:0)+g.x,n.right-p):Math.max(n.left,O.left-p+(f?14:0)-g.x),S=this.above[a];!l.strictSide&&(S?O.top-(u.bottom-u.top)-g.y<n.top:O.bottom+(u.bottom-u.top)+g.y>n.bottom)&&S==n.bottom-O.bottom>O.top-n.top&&(S=this.above[a]=!S);let y=(S?O.top-n.top:n.bottom-O.bottom)-d;if(y<m&&!1!==h.resize){if(y<this.view.defaultLineHeight){c.style.top=xo;continue}To.set(h,m),c.style.height=(m=y)/r+"px"}else c.style.height&&(c.style.height="");let Q=S?O.top-m-d-g.y:O.bottom+d+g.y,v=b+p;if(!0!==h.overlap)for(let t of o)t.left<v&&t.right>b&&t.top<Q+m&&t.bottom>Q&&(Q=S?t.top-m-2-d:t.bottom+d+2);if("absolute"==this.position?(c.style.top=(Q-t.parent.top)/r+"px",c.style.left=(b-t.parent.left)/s+"px"):(c.style.top=Q/r+"px",c.style.left=b/s+"px"),f){let t=O.left+(w?g.x:-g.x)-(b+14-7);f.style.left=t/s+"px"}!0!==h.overlap&&o.push({left:b,top:Q,right:v,bottom:Q+m}),c.classList.toggle("cm-tooltip-above",S),c.classList.toggle("cm-tooltip-below",!S),h.positioned&&h.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top=xo}},{eventObservers:{scroll(){this.maybeMeasure()}}}),Co=Wr.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Yo={x:0,y:0},Ao=Tt.define({enables:[Xo,Co]});function Ro(t,e){let i=t.plugin(Xo);if(!i)return null;let n=i.manager.tooltips.indexOf(e);return n<0?null:i.manager.tooltipViews[n]}function Wo(t){let e=t.plugin(Xo);e&&e.maybeMeasure()}const Vo=Tt.define({combine(t){let e,i;for(let n of t)e=e||n.topContainer,i=i||n.bottomContainer;return{topContainer:e,bottomContainer:i}}}),jo=qn.fromClass(class{constructor(t){this.input=t.state.facet(Eo),this.specs=this.input.filter((t=>t)),this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(Vo);this.top=new qo(t,!0,e.topContainer),this.bottom=new qo(t,!1,e.bottomContainer),this.top.sync(this.panels.filter((t=>t.top))),this.bottom.sync(this.panels.filter((t=>!t.top)));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(t){let e=t.state.facet(Vo);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new qo(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new qo(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(Eo);if(i!=this.input){let e=i.filter((t=>t)),n=[],s=[],r=[],o=[];for(let i of e){let e,a=this.specs.indexOf(i);a<0?(e=i(t.view),o.push(e)):(e=this.panels[a],e.update&&e.update(t)),n.push(e),(e.top?s:r).push(e)}this.specs=e,this.panels=n,this.top.sync(s),this.bottom.sync(r);for(let t of o)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>Wr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}}))});class qo{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=_o(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=_o(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function _o(t){let e=t.nextSibling;return t.remove(),e}const Eo=Tt.define({enables:jo});class Mo extends be{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}Mo.prototype.elementClass="",Mo.prototype.toDOM=void 0,Mo.prototype.mapMode=mt.TrackBefore,Mo.prototype.startSide=Mo.prototype.endSide=-1,Mo.prototype.point=!0;const Do=Tt.define(),zo={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>ve.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Go=Tt.define();function Io(t){return[No(),Go.of(Object.assign(Object.assign({},zo),t))]}const Bo=Tt.define({combine:t=>t.some((t=>t))});function No(t){let e=[Uo];return t&&!1===t.fixed&&e.push(Bo.of(!0)),e}const Uo=qn.fromClass(class{constructor(t){this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(Go).map((e=>new Jo(t,e)));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!t.state.facet(Bo),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,n=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Bo)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=ve.iter(this.view.state.facet(Do),this.view.viewport.from),n=[],s=this.gutters.map((t=>new Ho(t,this.view.viewport,-this.view.documentPadding.top)));for(let t of this.view.viewportLineBlocks)if(n.length&&(n=[]),Array.isArray(t.type)){let e=!0;for(let r of t.type)if(r.type==Ni.Text&&e){Fo(i,n,r.from);for(let t of s)t.line(this.view,r,n);e=!1}else if(r.widget)for(let t of s)t.widget(this.view,r)}else if(t.type==Ni.Text){Fo(i,n,t.from);for(let e of s)e.line(this.view,t,n)}else if(t.widget)for(let e of s)e.widget(this.view,t);for(let t of s)t.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(Go),i=t.state.facet(Go),n=t.docChanged||t.heightChanged||t.viewportChanged||!ve.eq(t.startState.facet(Do),t.state.facet(Do),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(n=!0);else{n=!0;let s=[];for(let n of i){let i=e.indexOf(n);i<0?s.push(new Jo(this.view,n)):(this.gutters[i].update(t),s.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),s.indexOf(t)<0&&t.destroy();for(let t of s)this.dom.appendChild(t.dom);this.gutters=s}return n}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>Wr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&0!=i.gutters.length&&i.fixed?e.textDirection==sn.LTR?{left:i.dom.offsetWidth*e.scaleX}:{right:i.dom.offsetWidth*e.scaleX}:null}))});function Lo(t){return Array.isArray(t)?t:[t]}function Fo(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class Ho{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=ve.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,s=(e.top-this.height)/t.scaleY,r=e.height/t.scaleY;if(this.i==n.elements.length){let e=new Ko(t,r,s,i);n.elements.push(e),n.dom.appendChild(e.dom)}else n.elements[this.i].update(t,r,s,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Fo(this.cursor,n,e.from),i.length&&(n=n.concat(i));let s=this.gutter.config.lineMarker(t,e,n);s&&n.unshift(s);let r=this.gutter;(0!=n.length||r.config.renderEmptyElements)&&this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e);i&&this.addElement(t,e,[i])}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Jo{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,(n=>{let s,r=n.target;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let t=r.getBoundingClientRect();s=(t.top+t.bottom)/2}else s=n.clientY;let o=t.lineBlockAtHeight(s-t.documentTop);e.domEventHandlers[i](t,o,n)&&n.preventDefault()}));this.markers=Lo(e.markers(t)),e.initialSpacer&&(this.spacer=new Ko(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Lo(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!ve.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class Ko{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let s=0,r=0;;){let o=r,a=s<e.length?e[s++]:null,l=!1;if(a){let t=a.elementClass;t&&(i+=" "+t);for(let t=r;t<this.markers.length;t++)if(this.markers[t].compare(a)){o=t,l=!0;break}}else o=this.markers.length;for(;r<o;){let t=this.markers[r++];if(t.toDOM){t.destroy(n);let e=n.nextSibling;n.remove(),n=e}}if(!a)break;a.toDOM&&(l?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),l&&r++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const ta=Tt.define(),ea=Tt.define({combine:t=>we(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let n=i[t],s=e[t];i[t]=n?(t,e,i)=>n(t,e,i)||s(t,e,i):s}return i}})});class ia extends Mo{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function na(t,e){return t.state.facet(ea).formatNumber(e,t.state)}const sa=Go.compute([ea],(t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(ta),lineMarker:(t,e,i)=>i.some((t=>t.toDOM))?null:new ia(na(t,t.state.doc.lineAt(e.from).number)),widgetMarker:()=>null,lineMarkerChange:t=>t.startState.facet(ea)!=t.state.facet(ea),initialSpacer:t=>new ia(na(t,oa(t.state.doc.lines))),updateSpacer(t,e){let i=na(e.view,oa(e.view.state.doc.lines));return i==t.number?t:new ia(i)},domEventHandlers:t.facet(ea).domEventHandlers})));function ra(t={}){return[ea.of(t),No(),sa]}function oa(t){let e=9;for(;e<t;)e=10*e+9;return e}let aa=0;class la{constructor(t,e,i){this.set=t,this.base=e,this.modified=i,this.id=aa++}static define(t){if(null==t?void 0:t.base)throw new Error("Can not derive from a modified tag");let e=new la([],null,[]);if(e.set.push(e),t)for(let i of t.set)e.set.push(i);return e}static defineModifier(){let t=new ca;return e=>e.modified.indexOf(t)>-1?e:ca.get(e.base||e,e.modified.concat(t).sort(((t,e)=>t.id-e.id)))}}let ha=0;class ca{constructor(){this.instances=[],this.id=ha++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find((i=>{return i.base==t&&(n=e,s=i.modified,n.length==s.length&&n.every(((t,e)=>t==s[e])));var n,s}));if(i)return i;let n=[],s=new la(n,t,e);for(let t of e)t.instances.push(s);let r=function(t){let e=[[]];for(let i=0;i<t.length;i++)for(let n=0,s=e.length;n<s;n++)e.push(e[n].concat(t[i]));return e.sort(((t,e)=>e.length-t.length))}(e);for(let e of t.set)if(!e.modified.length)for(let t of r)n.push(ca.get(e,t));return s}}function Oa(t){let e=Object.create(null);for(let i in t){let n=t[i];Array.isArray(n)||(n=[n]);for(let t of i.split(" "))if(t){let i=[],s=2,r=t;for(let e=0;;){if("..."==r&&e>0&&e+3==t.length){s=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(r);if(!n)throw new RangeError("Invalid path: "+t);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),e+=n[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){s=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);r=t.slice(e)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+t);let l=new fa(n,s,o>0?i.slice(0,o):null);e[a]=l.sort(e[a])}}return ua.add(e)}const ua=new n;class fa{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}function da(t,e){let i=Object.create(null);for(let e of t)if(Array.isArray(e.tag))for(let t of e.tag)i[t.id]=e.class;else i[e.tag.id]=e.class;let{scope:n,all:s=null}=e||{};return{style:t=>{let e=s;for(let n of t)for(let t of n.set){let n=i[t.id];if(n){e=e?e+" "+n:n;break}}return e},scope:n}}function pa(t,e,i,n=0,s=t.length){let r=new ma(n,Array.isArray(e)?e:[e],i);r.highlightRange(t.cursor(),n,s,"",r.highlighters),r.flush(s)}fa.empty=new fa([],2,null);class ma{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,s,r){let{type:o,from:a,to:l}=t;if(a>=i||l<=e)return;o.isTop&&(r=this.highlighters.filter((t=>!t.scope||t.scope(o))));let h=s,c=function(t){let e=t.type.prop(ua);for(;e&&e.context&&!t.matchContext(e.context);)e=e.next;return e||null}(t)||fa.empty,O=function(t,e){let i=null;for(let n of t){let t=n.style(e);t&&(i=i?i+" "+t:t)}return i}(r,c.tags);if(O&&(h&&(h+=" "),h+=O,1==c.mode&&(s+=(s?" ":"")+O)),this.startSpan(Math.max(e,a),h),c.opaque)return;let u=t.tree&&t.tree.prop(n.mounted);if(u&&u.overlay){let n=t.node.enter(u.overlay[0].from+a,1),o=this.highlighters.filter((t=>!t.scope||t.scope(u.tree.type))),c=t.firstChild();for(let O=0,f=a;;O++){let d=O<u.overlay.length?u.overlay[O]:null,p=d?d.from+a:l,m=Math.max(e,f),g=Math.min(i,p);if(m<g&&c)for(;t.from<g&&(this.highlightRange(t,m,g,s,r),this.startSpan(Math.min(g,t.to),h),!(t.to>=p)&&t.nextSibling()););if(!d||p>i)break;f=d.to+a,f>e&&(this.highlightRange(n.cursor(),Math.max(e,d.from+a),Math.min(i,f),"",o),this.startSpan(Math.min(i,f),h))}c&&t.parent()}else if(t.firstChild()){u&&(s="");do{if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,s,r),this.startSpan(Math.min(i,t.to),h)}}while(t.nextSibling());t.parent()}}}const ga=la.define,wa=ga(),ba=ga(),Sa=ga(ba),ya=ga(ba),Qa=ga(),va=ga(Qa),xa=ga(Qa),ka=ga(),$a=ga(ka),Pa=ga(),Za=ga(),Ta=ga(),Xa=ga(Ta),Ca=ga(),Ya={comment:wa,lineComment:ga(wa),blockComment:ga(wa),docComment:ga(wa),name:ba,variableName:ga(ba),typeName:Sa,tagName:ga(Sa),propertyName:ya,attributeName:ga(ya),className:ga(ba),labelName:ga(ba),namespace:ga(ba),macroName:ga(ba),literal:Qa,string:va,docString:ga(va),character:ga(va),attributeValue:ga(va),number:xa,integer:ga(xa),float:ga(xa),bool:ga(Qa),regexp:ga(Qa),escape:ga(Qa),color:ga(Qa),url:ga(Qa),keyword:Pa,self:ga(Pa),null:ga(Pa),atom:ga(Pa),unit:ga(Pa),modifier:ga(Pa),operatorKeyword:ga(Pa),controlKeyword:ga(Pa),definitionKeyword:ga(Pa),moduleKeyword:ga(Pa),operator:Za,derefOperator:ga(Za),arithmeticOperator:ga(Za),logicOperator:ga(Za),bitwiseOperator:ga(Za),compareOperator:ga(Za),updateOperator:ga(Za),definitionOperator:ga(Za),typeOperator:ga(Za),controlOperator:ga(Za),punctuation:Ta,separator:ga(Ta),bracket:Xa,angleBracket:ga(Xa),squareBracket:ga(Xa),paren:ga(Xa),brace:ga(Xa),content:ka,heading:$a,heading1:ga($a),heading2:ga($a),heading3:ga($a),heading4:ga($a),heading5:ga($a),heading6:ga($a),contentSeparator:ga(ka),list:ga(ka),quote:ga(ka),emphasis:ga(ka),strong:ga(ka),link:ga(ka),monospace:ga(ka),strikethrough:ga(ka),inserted:ga(),deleted:ga(),changed:ga(),invalid:ga(),meta:Ca,documentMeta:ga(Ca),annotation:ga(Ca),processingInstruction:ga(Ca),definition:la.defineModifier(),constant:la.defineModifier(),function:la.defineModifier(),standard:la.defineModifier(),local:la.defineModifier(),special:la.defineModifier()};var Aa;da([{tag:Ya.link,class:"tok-link"},{tag:Ya.heading,class:"tok-heading"},{tag:Ya.emphasis,class:"tok-emphasis"},{tag:Ya.strong,class:"tok-strong"},{tag:Ya.keyword,class:"tok-keyword"},{tag:Ya.atom,class:"tok-atom"},{tag:Ya.bool,class:"tok-bool"},{tag:Ya.url,class:"tok-url"},{tag:Ya.labelName,class:"tok-labelName"},{tag:Ya.inserted,class:"tok-inserted"},{tag:Ya.deleted,class:"tok-deleted"},{tag:Ya.literal,class:"tok-literal"},{tag:Ya.string,class:"tok-string"},{tag:Ya.number,class:"tok-number"},{tag:[Ya.regexp,Ya.escape,Ya.special(Ya.string)],class:"tok-string2"},{tag:Ya.variableName,class:"tok-variableName"},{tag:Ya.local(Ya.variableName),class:"tok-variableName tok-local"},{tag:Ya.definition(Ya.variableName),class:"tok-variableName tok-definition"},{tag:Ya.special(Ya.variableName),class:"tok-variableName2"},{tag:Ya.definition(Ya.propertyName),class:"tok-propertyName tok-definition"},{tag:Ya.typeName,class:"tok-typeName"},{tag:Ya.namespace,class:"tok-namespace"},{tag:Ya.className,class:"tok-className"},{tag:Ya.macroName,class:"tok-macroName"},{tag:Ya.propertyName,class:"tok-propertyName"},{tag:Ya.operator,class:"tok-operator"},{tag:Ya.comment,class:"tok-comment"},{tag:Ya.meta,class:"tok-meta"},{tag:Ya.invalid,class:"tok-invalid"},{tag:Ya.punctuation,class:"tok-punctuation"}]);const Ra=new n;function Wa(t){return Tt.define({combine:t?e=>e.concat(t):void 0})}const Va=new n;class ja{constructor(t,e,i=[],n=""){this.data=t,this.name=n,ge.prototype.hasOwnProperty("tree")||Object.defineProperty(ge.prototype,"tree",{get(){return Ea(this)}}),this.parser=e,this.extension=[Fa.of(this),ge.languageData.of(((t,e,i)=>{let n=qa(t,e,i),s=n.type.prop(Ra);if(!s)return[];let r=t.facet(s),o=n.type.prop(Va);if(o){let s=n.resolve(e-n.from,i);for(let e of o)if(e.test(s,t)){let i=t.facet(e.facet);return"replace"==e.type?i:i.concat(r)}}return r}))].concat(i)}isActiveAt(t,e,i=-1){return qa(t,e,i).type.prop(Ra)==this.data}findRegions(t){let e=t.facet(Fa);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],s=(t,e)=>{if(t.prop(Ra)==this.data)return void i.push({from:e,to:e+t.length});let r=t.prop(n.mounted);if(r){if(r.tree.prop(Ra)==this.data){if(r.overlay)for(let t of r.overlay)i.push({from:t.from+e,to:t.to+e});else i.push({from:e,to:e+t.length});return}if(r.overlay){let t=i.length;if(s(r.tree,r.overlay[0].from+e),i.length>t)return}}for(let i=0;i<t.children.length;i++){let n=t.children[i];n instanceof O&&s(n,t.positions[i]+e)}};return s(Ea(t),0),i}get allowsNesting(){return!0}}function qa(t,e,i){let n=t.facet(Fa),s=Ea(t).topNode;if(!n||n.allowsNesting)for(let t=s;t;t=t.enter(e,i,c.ExcludeBuffers))t.type.isTop&&(s=t);return s}ja.setState=re.define();class _a extends ja{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=Wa(t.languageData);return new _a(e,t.parser.configure({props:[Ra.add((t=>t.isTop?e:void 0))]}),t.name)}configure(t,e){return new _a(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function Ea(t){let e=t.field(ja.state,!1);return e?e.tree:O.empty}function Ma(t,e,i=50){var n;let s=null===(n=t.field(ja.state,!1))||void 0===n?void 0:n.context;if(!s)return null;let r=s.viewport;s.updateViewport({from:0,to:e});let o=s.isDone(e)||s.work(i,e)?s.tree:null;return s.updateViewport(r),o}class Da{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let za=null;class Ga{constructor(t,e,i=[],n,s,r,o,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=s,this.viewport=r,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new Ga(t,e,[],O.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Da(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=O.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var i;if("number"==typeof t){let e=Date.now()+t;t=()=>Date.now()>e}for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n){if(this.fragments=this.withoutTempSkipped(X.addTree(n,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=n,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(t())return!1}}))}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext((()=>{for(;!(e=this.parse.advance()););})),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(X.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=za;za=this;try{return t()}finally{za=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=Ia(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:s,viewport:r,skipped:o}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges(((t,i,n,s)=>e.push({fromA:t,toA:i,fromB:n,toB:s}))),i=X.applyChanges(i,e),n=O.empty,s=0,r={from:t.mapPos(r.from,-1),to:t.mapPos(r.to,1)},this.skipped.length){o=[];for(let e of this.skipped){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);i<n&&o.push({from:i,to:n})}}}return new Ga(this.parser,e,i,n,s,r,o,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let e=0;e<this.skipped.length;e++){let{from:i,to:n}=this.skipped[e];i<t.to&&n>t.from&&(this.fragments=Ia(this.fragments,i,n),this.skipped.splice(e--,1))}return!(this.skipped.length>=e)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends C{createParse(e,i,n){let s=n[0].from,r=n[n.length-1].to;return{parsedPos:s,advance(){let e=za;if(e){for(let t of n)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=r,new O(o.none,[],[],r-s)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return za}}function Ia(t,e,i){return X.applyChanges(t,[{fromA:e,toA:i,fromB:e,toB:i}])}class Ba{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new Ba(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=Ga.create(t.facet(Fa).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new Ba(i)}}ja.state=Vt.define({create:Ba.init,update(t,e){for(let t of e.effects)if(t.is(ja.setState))return t.value;return e.startState.facet(Fa)!=e.state.facet(Fa)?Ba.init(e.state):t.apply(e)}});let Na=t=>{let e=setTimeout((()=>t()),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(Na=t=>{let e=-1,i=setTimeout((()=>{e=requestIdleCallback(t,{timeout:400})}),100);return()=>e<0?clearTimeout(i):cancelIdleCallback(e)});const Ua="undefined"!=typeof navigator&&(null===(Aa=navigator.scheduling)||void 0===Aa?void 0:Aa.isInputPending)?()=>navigator.scheduling.isInputPending():null,La=qn.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(ja.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(ja.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=Na(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,s=i.field(ja.state);if(s.tree==s.context.tree&&s.context.isDone(n+1e5))return;let r=Date.now()+Math.min(this.chunkBudget,100,t&&!Ua?Math.max(25,t.timeRemaining()-5):1e9),o=s.context.treeLen<n&&i.doc.length>n+1e3,a=s.context.work((()=>Ua&&Ua()||Date.now()>r),n+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(s.context.takeTree(),this.view.dispatch({effects:ja.setState.of(new Ba(s.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(s.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then((()=>this.scheduleWork())).catch((t=>Rn(this.view.state,t))).then((()=>this.workScheduled--)),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),Fa=Tt.define({combine:t=>t.length?t[0]:null,enables:t=>[ja.state,La,Wr.contentAttributes.compute([t],(e=>{let i=e.facet(t);return i&&i.name?{"data-language":i.name}:{}}))]});class Ha{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class Ja{constructor(t,e,i,n,s,r=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=s,this.support=r,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then((t=>this.support=t),(t=>{throw this.loading=null,t})))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new Ja(t.name,(t.alias||[]).concat(t.name).map((t=>t.toLowerCase())),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let i of t)if(i.filename&&i.filename.test(e))return i;let i=/\.([^.]+)$/.exec(e);if(i)for(let e of t)if(e.extensions.indexOf(i[1])>-1)return e;return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let i of t)if(i.alias.some((t=>t==e)))return i;if(i)for(let i of t)for(let t of i.alias){let n=e.indexOf(t);if(n>-1&&(t.length>2||!/\w/.test(e[n-1])&&!/\w/.test(e[n+t.length])))return i}return null}}const Ka=Tt.define(),tl=Tt.define({combine:t=>{if(!t.length)return"  ";let e=t[0];if(!e||/\S/.test(e)||Array.from(e).some((t=>t!=e[0])))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return e}});function el(t){let e=t.facet(tl);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function il(t,e){let i="",n=t.tabSize,s=t.facet(tl)[0];if("\t"==s){for(;e>=n;)i+="\t",e-=n;s=" "}for(let t=0;t<e;t++)i+=s;return i}function nl(t,e){t instanceof ge&&(t=new sl(t));for(let i of t.state.facet(Ka)){let n=i(t,e);if(void 0!==n)return n}let i=Ea(t.state);return i.length>=e?function(t,e,i){let n=e.resolveStack(i),s=n.node.enterUnfinishedNodesBefore(i);if(s!=n.node){let t=[];for(let e=s;e!=n.node;e=e.parent)t.push(e);for(let e=t.length-1;e>=0;e--)n={node:t[e],next:n}}return ol(n,t,i)}(t,i,e):null}class sl{constructor(t,e={}){this.state=t,this.options=e,this.unit=el(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:s}=this.options;return null!=n&&n>=i.from&&n<=i.to?s&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.countColumn(i,t-n),r=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return r>-1&&(s+=r-this.countColumn(i,i.search(/\S|$/))),s}countColumn(t,e=t.length){return We(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.options.overrideIndentation;if(s){let t=s(n);if(t>-1)return t}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const rl=new n;function ol(t,e,i){for(let n=t;n;n=n.next){let t=al(n.node);if(t)return t(hl.create(e,i,n))}return 0}function al(t){let e=t.type.prop(rl);if(e)return e;let i,s=t.firstChild;if(s&&(i=s.type.prop(n.closedBy))){let e=t.lastChild,n=e&&i.indexOf(e.name)>-1;return t=>ul(t,!0,1,void 0,n&&!function(t){return t.pos==t.options.simulateBreak&&t.options.simulateDoubleBreak}(t)?e.from:void 0)}return null==t.parent?ll:null}function ll(){return 0}class hl extends sl{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new hl(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(cl(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return ol(this.context.next,this.base,this.pos)}}function cl(t,e){for(let i=e;i;i=i.parent)if(t==i)return!0;return!1}function Ol({closing:t,align:e=!0,units:i=1}){return n=>ul(n,e,i,t)}function ul(t,e,i,n,s){let r=t.textAfter,o=r.match(/^\s*/)[0].length,a=n&&r.slice(o,o+n.length)==n||s==t.pos+o,l=e?function(t){let e=t.node,i=e.childAfter(e.from),n=e.lastChild;if(!i)return null;let s=t.options.simulateBreak,r=t.state.doc.lineAt(i.from),o=null==s||s<=r.from?r.to:Math.min(r.to,s);for(let t=i.to;;){let s=e.childAfter(t);if(!s||s==n)return null;if(!s.type.isSkipped)return s.from<o?i:null;t=s.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*i)}const fl=t=>t.baseIndent;function dl({except:t,units:e=1}={}){return i=>{let n=t&&t.test(i.textAfter);return i.baseIndent+(n?0:e*i.unit)}}function pl(){return ge.transactionFilter.of((t=>{if(!t.docChanged||!t.isUserEvent("input.type")&&!t.isUserEvent("input.complete"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let i=t.newDoc,{head:n}=t.newSelection.main,s=i.lineAt(n);if(n>s.from+200)return t;let r=i.sliceString(s.from,n);if(!e.some((t=>t.test(r))))return t;let{state:o}=t,a=-1,l=[];for(let{head:t}of o.selection.ranges){let e=o.doc.lineAt(t);if(e.from==a)continue;a=e.from;let i=nl(o,e.from);if(null==i)continue;let n=/^\s*/.exec(e.text)[0],s=il(o,i);n!=s&&l.push({from:e.from,to:e.from+n.length,insert:s})}return l.length?[t,{changes:l,sequential:!0}]:t}))}const ml=Tt.define(),gl=new n;function wl(t){let e=t.firstChild,i=t.lastChild;return e&&e.to<i.from?{from:e.to,to:i.type.isError?t.to:i.from}:null}function bl(t){let e=t.lastChild;return e&&e.to==t.to&&e.type.isError}function Sl(t,e,i){for(let n of t.facet(ml)){let s=n(t,e,i);if(s)return s}return function(t,e,i){let n=Ea(t);if(n.length<i)return null;let s=null;for(let r=n.resolveStack(i,1);r;r=r.next){let o=r.node;if(o.to<=i||o.from>i)continue;if(s&&o.from<e)break;let a=o.type.prop(gl);if(a&&(o.to<n.length-50||n.length==t.doc.length||!bl(o))){let n=a(o,t);n&&n.from<=i&&n.from>=e&&n.to>i&&(s=n)}}return s}(t,e,i)}function yl(t,e){let i=e.mapPos(t.from,1),n=e.mapPos(t.to,-1);return i>=n?void 0:{from:i,to:n}}const Ql=re.define({map:yl}),vl=re.define({map:yl});function xl(t){let e=[];for(let{head:i}of t.state.selection.ranges)e.some((t=>t.from<=i&&t.to>=i))||e.push(t.lineBlockAt(i));return e}const kl=Vt.define({create:()=>Ui.none,update(t,e){t=t.map(e.changes);for(let i of e.effects)if(i.is(Ql)&&!Pl(t,i.value.from,i.value.to)){let{preparePlaceholder:n}=e.state.facet(Yl),s=n?Ui.replace({widget:new Vl(n(e.state,i.value))}):Wl;t=t.update({add:[s.range(i.value.from,i.value.to)]})}else i.is(vl)&&(t=t.update({filter:(t,e)=>i.value.from!=t||i.value.to!=e,filterFrom:i.value.from,filterTo:i.value.to}));if(e.selection){let i=!1,{head:n}=e.selection.main;t.between(n,n,((t,e)=>{t<n&&e>n&&(i=!0)})),i&&(t=t.update({filterFrom:n,filterTo:n,filter:(t,e)=>e<=n||t>=n}))}return t},provide:t=>Wr.decorations.from(t),toJSON(t,e){let i=[];return t.between(0,e.doc.length,((t,e)=>{i.push(t,e)})),i},fromJSON(t){if(!Array.isArray(t)||t.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let i=0;i<t.length;){let n=t[i++],s=t[i++];if("number"!=typeof n||"number"!=typeof s)throw new RangeError("Invalid JSON for fold state");e.push(Wl.range(n,s))}return Ui.set(e,!0)}});function $l(t,e,i){var n;let s=null;return null===(n=t.field(kl,!1))||void 0===n||n.between(e,i,((t,e)=>{(!s||s.from>t)&&(s={from:t,to:e})})),s}function Pl(t,e,i){let n=!1;return t.between(e,e,((t,s)=>{t==e&&s==i&&(n=!0)})),n}function Zl(t,e){return t.field(kl,!1)?e:e.concat(re.appendConfig.of(Al()))}function Tl(t,e,i=!0){let n=t.state.doc.lineAt(e.from).number,s=t.state.doc.lineAt(e.to).number;return Wr.announce.of(`${t.state.phrase(i?"Folded lines":"Unfolded lines")} ${n} ${t.state.phrase("to")} ${s}.`)}const Xl=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:t=>{for(let e of xl(t)){let i=Sl(t.state,e.from,e.to);if(i)return t.dispatch({effects:Zl(t.state,[Ql.of(i),Tl(t,i)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:t=>{if(!t.state.field(kl,!1))return!1;let e=[];for(let i of xl(t)){let n=$l(t.state,i.from,i.to);n&&e.push(vl.of(n),Tl(t,n,!1))}return e.length&&t.dispatch({effects:e}),e.length>0}},{key:"Ctrl-Alt-[",run:t=>{let{state:e}=t,i=[];for(let n=0;n<e.doc.length;){let s=t.lineBlockAt(n),r=Sl(e,s.from,s.to);r&&i.push(Ql.of(r)),n=(r?t.lineBlockAt(r.to):s).to+1}return i.length&&t.dispatch({effects:Zl(t.state,i)}),!!i.length}},{key:"Ctrl-Alt-]",run:t=>{let e=t.state.field(kl,!1);if(!e||!e.size)return!1;let i=[];return e.between(0,t.state.doc.length,((t,e)=>{i.push(vl.of({from:t,to:e}))})),t.dispatch({effects:i}),!0}}],Cl={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},Yl=Tt.define({combine:t=>we(t,Cl)});function Al(t){let e=[kl,El];return t&&e.push(Yl.of(t)),e}function Rl(t,e){let{state:i}=t,n=i.facet(Yl),s=e=>{let i=t.lineBlockAt(t.posAtDOM(e.target)),n=$l(t.state,i.from,i.to);n&&t.dispatch({effects:vl.of(n)}),e.preventDefault()};if(n.placeholderDOM)return n.placeholderDOM(t,s,e);let r=document.createElement("span");return r.textContent=n.placeholderText,r.setAttribute("aria-label",i.phrase("folded code")),r.title=i.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=s,r}const Wl=Ui.replace({widget:new class extends Bi{toDOM(t){return Rl(t,null)}}});class Vl extends Bi{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return Rl(t,this.value)}}const jl={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class ql extends Mo{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function _l(t={}){let e=Object.assign(Object.assign({},jl),t),i=new ql(e,!0),n=new ql(e,!1),s=qn.fromClass(class{constructor(t){this.from=t.viewport.from,this.markers=this.buildMarkers(t)}update(t){(t.docChanged||t.viewportChanged||t.startState.facet(Fa)!=t.state.facet(Fa)||t.startState.field(kl,!1)!=t.state.field(kl,!1)||Ea(t.startState)!=Ea(t.state)||e.foldingChanged(t))&&(this.markers=this.buildMarkers(t.view))}buildMarkers(t){let e=new xe;for(let s of t.viewportLineBlocks){let r=$l(t.state,s.from,s.to)?n:Sl(t.state,s.from,s.to)?i:null;r&&e.add(s.from,s.from,r)}return e.finish()}}),{domEventHandlers:r}=e;return[s,Io({class:"cm-foldGutter",markers(t){var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.markers)||ve.empty},initialSpacer:()=>new ql(e,!1),domEventHandlers:Object.assign(Object.assign({},r),{click:(t,e,i)=>{if(r.click&&r.click(t,e,i))return!0;let n=$l(t.state,e.from,e.to);if(n)return t.dispatch({effects:vl.of(n)}),!0;let s=Sl(t.state,e.from,e.to);return!!s&&(t.dispatch({effects:Ql.of(s)}),!0)}})}),Al()]}const El=Wr.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Ml{constructor(t,e){let i;function n(t){let e=_e.newName();return(i||(i=Object.create(null)))["."+e]=t,e}this.specs=t;const s="string"==typeof e.all?e.all:e.all?n(e.all):void 0,r=e.scope;this.scope=r instanceof ja?t=>t.prop(Ra)==r.data:r?t=>t==r:void 0,this.style=da(t.map((t=>({tag:t.tag,class:t.class||n(Object.assign({},t,{tag:null}))}))),{all:s}).style,this.module=i?new _e(i):null,this.themeType=e.themeType}static define(t,e){return new Ml(t,e||{})}}const Dl=Tt.define(),zl=Tt.define({combine:t=>t.length?[t[0]]:null});function Gl(t){let e=t.facet(Dl);return e.length?e:t.facet(zl)}function Il(t,e){let i,n=[Nl];return t instanceof Ml&&(t.module&&n.push(Wr.styleModule.of(t.module)),i=t.themeType),(null==e?void 0:e.fallback)?n.push(zl.of(t)):i?n.push(Dl.computeN([Wr.darkTheme],(e=>e.facet(Wr.darkTheme)==("dark"==i)?[t]:[]))):n.push(Dl.of(t)),n}class Bl{constructor(t){this.markCache=Object.create(null),this.tree=Ea(t.state),this.decorations=this.buildDeco(t,Gl(t.state))}update(t){let e=Ea(t.state),i=Gl(t.state),n=i!=Gl(t.startState);e.length<t.view.viewport.to&&!n&&e.type==this.tree.type?this.decorations=this.decorations.map(t.changes):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i))}buildDeco(t,e){if(!e||!this.tree.length)return Ui.none;let i=new xe;for(let{from:n,to:s}of t.visibleRanges)pa(this.tree,e,((t,e,n)=>{i.add(t,e,this.markCache[n]||(this.markCache[n]=Ui.mark({class:n})))}),n,s);return i.finish()}}const Nl=Dt.high(qn.fromClass(Bl,{decorations:t=>t.decorations}));Ya.meta,Ya.link,Ya.heading,Ya.emphasis,Ya.strong,Ya.strikethrough,Ya.keyword,Ya.atom,Ya.bool,Ya.url,Ya.contentSeparator,Ya.labelName,Ya.literal,Ya.inserted,Ya.string,Ya.deleted,Ya.regexp,Ya.escape,Ya.string,Ya.variableName,Ya.variableName,Ya.typeName,Ya.namespace,Ya.className,Ya.variableName,Ya.macroName,Ya.propertyName,Ya.comment,Ya.invalid;const Ul=Wr.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),Ll=1e4,Fl="()[]{}",Hl=Tt.define({combine:t=>we(t,{afterCursor:!0,brackets:Fl,maxScanDistance:Ll,renderMatch:th})}),Jl=Ui.mark({class:"cm-matchingBracket"}),Kl=Ui.mark({class:"cm-nonmatchingBracket"});function th(t){let e=[],i=t.matched?Jl:Kl;return e.push(i.range(t.start.from,t.start.to)),t.end&&e.push(i.range(t.end.from,t.end.to)),e}const eh=Vt.define({create:()=>Ui.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let i=[],n=e.state.facet(Hl);for(let t of e.state.selection.ranges){if(!t.empty)continue;let s=ah(e.state,t.head,-1,n)||t.head>0&&ah(e.state,t.head-1,1,n)||n.afterCursor&&(ah(e.state,t.head,1,n)||t.head<e.state.doc.length&&ah(e.state,t.head+1,-1,n));s&&(i=i.concat(n.renderMatch(s,e.state)))}return Ui.set(i,!0)},provide:t=>Wr.decorations.from(t)}),ih=[eh,Ul];function nh(t={}){return[Hl.of(t),ih]}const sh=new n;function rh(t,e,i){let s=t.prop(e<0?n.openedBy:n.closedBy);if(s)return s;if(1==t.name.length){let n=i.indexOf(t.name);if(n>-1&&n%2==(e<0?1:0))return[i[n+e]]}return null}function oh(t){let e=t.type.prop(sh);return e?e(t.node):t}function ah(t,e,i,n={}){let s=n.maxScanDistance||Ll,r=n.brackets||Fl,o=Ea(t),a=o.resolveInner(e,i);for(let n=a;n;n=n.parent){let s=rh(n.type,i,r);if(s&&n.from<n.to){let o=oh(n);if(o&&(i>0?e>=o.from&&e<o.to:e>o.from&&e<=o.to))return lh(t,e,i,n,o,s,r)}}return function(t,e,i,n,s,r,o){let a=i<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?e-1:e,to:i>0?e+1:e},c=t.doc.iterRange(e,i>0?t.doc.length:0),O=0;for(let t=0;!c.next().done&&t<=r;){let r=c.value;i<0&&(t+=r.length);let a=e+t*i;for(let t=i>0?0:r.length-1,e=i>0?r.length:-1;t!=e;t+=i){let e=o.indexOf(r[t]);if(!(e<0||n.resolveInner(a+t,1).type!=s))if(e%2==0==i>0)O++;else{if(1==O)return{start:h,end:{from:a+t,to:a+t+1},matched:e>>1==l>>1};O--}}i>0&&(t+=r.length)}return c.done?{start:h,matched:!1}:null}(t,e,i,o,a.type,s,r)}function lh(t,e,i,n,s,r,o){let a=n.parent,l={from:s.from,to:s.to},h=0,c=null==a?void 0:a.cursor();if(c&&(i<0?c.childBefore(n.from):c.childAfter(n.to)))do{if(i<0?c.to<=n.from:c.from>=n.to){if(0==h&&r.indexOf(c.type.name)>-1&&c.from<c.to){let t=oh(c);return{start:l,end:t?{from:t.from,to:t.to}:void 0,matched:!0}}if(rh(c.type,i,o))h++;else if(rh(c.type,-i,o)){if(0==h){let t=oh(c);return{start:l,end:t&&t.from<t.to?{from:t.from,to:t.to}:void 0,matched:!1}}h--}}}while(i<0?c.prevSibling():c.nextSibling());return{start:l,matched:!1}}function hh(t,e,i,n=0,s=0){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);let r=s;for(let s=n;s<e;s++)9==t.charCodeAt(s)?r+=i-r%i:r++;return r}class ch{constructor(t,e,i,n){this.string=t,this.tabSize=e,this.indentUnit=i,this.overrideIndent=n,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return 0==this.pos}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e,i=this.string.charAt(this.pos);if(e="string"==typeof t?i==t:i&&(t instanceof RegExp?t.test(i):t(i)),e)return++this.pos,i}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=hh(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return null!==(t=this.overrideIndent)&&void 0!==t?t:hh(this.string,null,this.tabSize)}match(t,e,i){if("string"==typeof t){let n=t=>i?t.toLowerCase():t;return n(this.string.substr(this.pos,t.length))==n(t)?(!1!==e&&(this.pos+=t.length),!0):null}{let i=this.string.slice(this.pos).match(t);return i&&i.index>0?null:(i&&!1!==e&&(this.pos+=i[0].length),i)}}current(){return this.string.slice(this.start,this.pos)}}function Oh(t){if("object"!=typeof t)return t;let e={};for(let i in t){let n=t[i];e[i]=n instanceof Array?n.slice():n}return e}const uh=new WeakMap;class fh extends ja{constructor(t){let e,i=Wa(t.languageData),s={name:(r=t).name||"",token:r.token,blankLine:r.blankLine||(()=>{}),startState:r.startState||(()=>!0),copyState:r.copyState||Oh,indent:r.indent||(()=>null),languageData:r.languageData||{},tokenTable:r.tokenTable||wh};var r;super(i,new class extends C{createParse(t,i,n){return new mh(e,t,i,n)}},[Ka.of(((t,e)=>this.getIndent(t,e)))],t.name),this.topNode=function(t){let e=o.define({id:bh.length,name:"Document",props:[Ra.add((()=>t))],top:!0});return bh.push(e),e}(i),e=this,this.streamParser=s,this.stateAfter=new n({perNode:!0}),this.tokenTable=t.tokenTable?new xh(s.tokenTable):kh}static define(t){return new fh(t)}getIndent(t,e){let i,n=Ea(t.state),s=n.resolve(e);for(;s&&s.type!=this.topNode;)s=s.parent;if(!s)return null;let{overrideIndentation:r}=t.options;r&&(i=uh.get(t.state),null!=i&&i<e-1e4&&(i=void 0));let o,a,l=dh(this,n,0,s.from,null!=i?i:e);if(l?(a=l.state,o=l.pos+1):(a=this.streamParser.startState(t.unit),o=0),e-o>1e4)return null;for(;o<e;){let i=t.state.doc.lineAt(o),n=Math.min(e,i.to);if(i.length){let e=r?r(i.from):-1,s=new ch(i.text,t.state.tabSize,t.unit,e<0?void 0:e);for(;s.pos<n-i.from;)gh(this.streamParser.token,s,a)}else this.streamParser.blankLine(a,t.unit);if(n==e)break;o=i.to+1}let h=t.lineAt(e);return r&&null==i&&uh.set(t.state,h.from),this.streamParser.indent(a,/^\s*(.*)/.exec(h.text)[1],t)}get allowsNesting(){return!1}}function dh(t,e,i,n,s){let r=i>=n&&i+e.length<=s&&e.prop(t.stateAfter);if(r)return{state:t.streamParser.copyState(r),pos:i+e.length};for(let r=e.children.length-1;r>=0;r--){let o=e.children[r],a=i+e.positions[r],l=o instanceof O&&a<s&&dh(t,o,a,n,s);if(l)return l}return null}function ph(t,e,i,n,s){if(s&&i<=0&&n>=e.length)return e;s||e.type!=t.topNode||(s=!0);for(let r=e.children.length-1;r>=0;r--){let o,a=e.positions[r],l=e.children[r];if(a<n&&l instanceof O){if(!(o=ph(t,l,i-a,n-a,s)))break;return s?new O(e.type,e.children.slice(0,r).concat(o),e.positions.slice(0,r+1),a+o.length):o}}return null}class mh{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let s=Ga.get(),r=n[0].from,{state:o,tree:a}=function(t,e,i,n){for(let n of e){let e,s=n.from+(n.openStart?25:0),r=n.to-(n.openEnd?25:0),o=s<=i&&r>i&&dh(t,n.tree,0-n.offset,i,r);if(o&&(e=ph(t,n.tree,i+n.offset,o.pos+n.offset,!1)))return{state:o.state,tree:e}}return{state:t.streamParser.startState(n?el(n):4),tree:O.empty}}(t,i,r,null==s?void 0:s.state);this.state=o,this.parsedPos=this.chunkStart=r+a.length;for(let t=0;t<a.children.length;t++)this.chunks.push(a.children[t]),this.chunkPos.push(a.positions[t]);s&&this.parsedPos<s.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(el(s.state)),s.skipUntilInView(this.parsedPos,s.viewport.from),this.parsedPos=s.viewport.from),this.moveRangeIndex()}advance(){let t=Ga.get(),e=null==this.stoppedAt?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)"\n"==e&&(e="");else{let t=e.indexOf("\n");t>-1&&(e=e.slice(0,t))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let t=this.rangeIndex;;){let n=this.ranges[t].to;if(n>=i)break;if(e=e.slice(0,n-(i-e.length)),t++,t==this.ranges.length)break;let s=this.ranges[t].from,r=this.lineAfter(s);e+=r,i=s+r.length}return{line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,s=t+e;if(i>0?n>s:n>=s)break;e+=this.ranges[++this.rangeIndex].from-n}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(t,e,i,n,s){if(this.ranges.length>1){e+=s=this.skipGapsTo(e,s,1);let t=this.chunk.length;i+=s=this.skipGapsTo(i,s,-1),n+=this.chunk.length-t}return this.chunk.push(t,e,i,n),s}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:s}=this.lang,r=new ch(e,t?t.state.tabSize:4,t?el(t.state):2);if(r.eol())s.blankLine(this.state,r.indentUnit);else for(;!r.eol();){let t=gh(s.token,r,this.state);if(t&&(n=this.emitToken(this.lang.tokenTable.resolve(t),this.parsedPos+r.start,this.parsedPos+r.pos,4,n)),r.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=O.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:Sh,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new O(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new O(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function gh(t,e,i){e.start=e.pos;for(let n=0;n<10;n++){let n=t(e,i);if(e.pos>e.start)return n}throw new Error("Stream parser failed to advance stream.")}const wh=Object.create(null),bh=[o.none],Sh=new a(bh),yh=[],Qh=Object.create(null),vh=Object.create(null);for(let[t,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])vh[t]=Ph(wh,e);class xh{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),vh)}resolve(t){return t?this.table[t]||(this.table[t]=Ph(this.extra,t)):0}}const kh=new xh(wh);function $h(t,e){yh.indexOf(t)>-1||(yh.push(t),console.warn(e))}function Ph(t,e){let i=[];for(let n of e.split(" ")){let e=[];for(let i of n.split(".")){let n=t[i]||Ya[i];n?"function"==typeof n?e.length?e=e.map(n):$h(i,`Modifier ${i} used at start of tag`):e.length?$h(i,`Tag ${i} used as modifier`):e=Array.isArray(n)?n:[n]:$h(i,`Unknown highlighting tag ${i}`)}for(let t of e)i.push(t)}if(!i.length)return 0;let n=e.replace(/ /g,"_"),s=n+" "+i.map((t=>t.id)),r=Qh[s];if(r)return r.id;let a=Qh[s]=o.define({id:bh.length,name:n,props:[Oa({[n]:i})]});return bh.push(a),a.id}function Zh(t){return t.length<=4096&&/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/.test(t)}function Th(t){for(let e=t.iter();!e.next().done;)if(Zh(e.value))return!0;return!1}const Xh=Tt.define({combine:t=>t.some((t=>t))});function Ch(t={}){let e=[Yh];return t.alwaysIsolate&&e.push(Xh.of(!0)),e}const Yh=qn.fromClass(class{constructor(t){this.always=t.state.facet(Xh)||t.textDirection!=sn.LTR||t.state.facet(Wr.perLineTextDirection),this.hasRTL=!this.always&&Th(t.state.doc),this.tree=Ea(t.state),this.decorations=Ah(t,this.tree,this.always)}update(t){let e=t.state.facet(Xh)||t.view.textDirection!=sn.LTR||t.state.facet(Wr.perLineTextDirection);if(e||this.hasRTL||!function(t){let e=!1;return t.iterChanges(((t,i,n,s,r)=>{!e&&Th(r)&&(e=!0)})),e}(t.changes)||(this.hasRTL=!0),!e&&!this.hasRTL)return;let i=Ea(t.state);(e!=this.always||i!=this.tree||t.docChanged||t.viewportChanged)&&(this.tree=i,this.always=e,this.decorations=Ah(t.view,i,e))}},{provide:t=>{function e(e){var i,n;return null!==(n=null===(i=e.plugin(t))||void 0===i?void 0:i.decorations)&&void 0!==n?n:Ui.none}return[Wr.outerDecorations.of(e),Dt.lowest(Wr.bidiIsolatedRanges.of(e))]}});function Ah(t,e,i){let s=new xe,r=t.visibleRanges;i||(r=function(t,e){let i=e.iter(),n=0,s=[],r=null;for(let{from:e,to:o}of t)for(e!=n&&(n<e&&i.next(e-n),n=e);;){let t=n,e=n+i.value.length;if(!i.lineBreak&&Zh(i.value)&&(r&&r.to>t-10?r.to=Math.min(o,e):s.push(r={from:t,to:Math.min(o,e)})),n>=o)break;n=e,i.next()}return s}(r,t.state.doc));for(let{from:t,to:i}of r)e.iterate({enter:t=>{let e=t.type.prop(n.isolate);e&&s.add(t.from,t.to,Rh[e])},from:t,to:i});return s.finish()}const Rh={rtl:Ui.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"rtl"},bidiIsolate:sn.RTL}),ltr:Ui.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"ltr"},bidiIsolate:sn.LTR}),auto:Ui.mark({class:"cm-iso",inclusive:!0,attributes:{dir:"auto"},bidiIsolate:null})};class Wh{constructor(t,e,i,n,s,r,o,a,l,h=0,c){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=s,this.score=r,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter(((t,e)=>e%3==0)).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new Wh(t,[],e,i,i,0,[],0,n?new Vh(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var e;let i=t>>19,n=65535&t,{parser:s}=this.p,r=s.dynamicPrecedence(n);if(r&&(this.score+=r),0==i)return this.pushState(s.getGoto(this.state,n,!0),this.reducePos),n<s.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,4,!0),void this.reduceContext(n,this.reducePos);let o=this.stack.length-3*(i-1)-(262144&t?6:0),a=o?this.stack[o-2]:this.p.ranges[0].from,l=this.reducePos-a;l>=2e3&&!(null===(e=this.p.parser.nodeSet.types[n])||void 0===e?void 0:e.isAnonymous)&&(a==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=l):this.p.lastBigReductionSize<l&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=a,this.p.lastBigReductionSize=l));let h=o?this.stack[o-1]:0,c=this.bufferBase+this.buffer.length-h;if(n<s.minRepeatTerm||131072&t){let t=s.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,a,t,c+4,!0)}if(262144&t)this.state=this.stack[o];else{let t=this.stack[o-3];this.state=s.getGoto(t,n,!0)}for(;this.stack.length>o;)this.stack.pop();this.reduceContext(n,a)}storeNode(t,e,i,n=4,s=!1){if(0==t&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let t=this,n=this.buffer.length;if(0==n&&t.parent&&(n=t.bufferBase-t.parent.bufferBase,t=t.parent),n>0&&0==t.buffer[n-4]&&t.buffer[n-1]>-1){if(e==i)return;if(t.buffer[n-2]>=e)return void(t.buffer[n-2]=i)}}if(s&&this.pos!=i){let s=this.buffer.length;if(s>0&&0!=this.buffer[s-4])for(;s>0&&this.buffer[s-2]>i;)this.buffer[s]=this.buffer[s-4],this.buffer[s+1]=this.buffer[s-3],this.buffer[s+2]=this.buffer[s-2],this.buffer[s+3]=this.buffer[s-1],s-=4,n>4&&(n-=4);this.buffer[s]=t,this.buffer[s+1]=e,this.buffer[s+2]=i,this.buffer[s+3]=n}else this.buffer.push(t,e,i,n)}shift(t,e,i,n){if(131072&t)this.pushState(65535&t,this.pos);else if(0==(262144&t)){let s=t,{parser:r}=this.p;(n>this.pos||e<=r.maxNode)&&(this.pos=n,r.stateFlag(s,1)||(this.reducePos=n)),this.pushState(s,i),this.shiftContext(e,i),e<=r.maxNode&&this.buffer.push(e,i,n,4)}else this.pos=n,this.shiftContext(e,i),e<=this.p.parser.maxNode&&this.buffer.push(e,i,n,4)}apply(t,e,i,n){65536&t?this.reduce(t):this.shift(t,e,i,n)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new Wh(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new jh(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(0==i)return!1;if(0==(65536&i))return!0;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let i=[];for(let n,s=0;s<e.length;s+=2)(n=e[s+1])!=this.state&&this.p.parser.hasAction(n,t)&&i.push(e[s],n);if(this.stack.length<120)for(let t=0;i.length<8&&t<e.length;t+=2){let n=e[t+1];i.some(((t,e)=>1&e&&t==n))||i.push(e[t],n)}e=i}let i=[];for(let t=0;t<e.length&&i.length<4;t+=2){let n=e[t+1];if(n==this.state)continue;let s=this.split();s.pushState(n,this.pos),s.storeNode(0,s.pos,s.pos,4,!0),s.shiftContext(e[t],this.pos),s.reducePos=this.pos,s.score-=200,i.push(s)}return i}forceReduce(){let{parser:t}=this.p,e=t.stateSlot(this.state,5);if(0==(65536&e))return!1;if(!t.validAction(this.state,e)){let i=e>>19,n=65535&e,s=this.stack.length-3*i;if(s<0||t.getGoto(this.stack[s],n,!1)<0){let t=this.findForcedReduction();if(null==t)return!1;e=t}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(e),!0}findForcedReduction(){let{parser:t}=this.p,e=[],i=(n,s)=>{if(!e.includes(n))return e.push(n),t.allActions(n,(e=>{if(393216&e);else if(65536&e){let i=(e>>19)-s;if(i>1){let n=65535&e,s=this.stack.length-3*i;if(s>=0&&t.getGoto(this.stack[s],n,!1)>=0)return i<<19|65536|n}}else{let t=i(e,s+1);if(null!=t)return t}}))};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:t}=this.p;return 65535==t.data[t.stateSlot(this.state,1)]&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||-3!=this.buffer[t])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||-4!=this.buffer[t])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new Vh(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class Vh{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}class jh{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=65535&t,i=t>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class qh{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,0==this.index&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new qh(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;null!=t&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new qh(this.stack,this.pos,this.index)}}function _h(t,e=Uint16Array){if("string"!=typeof t)return t;let i=null;for(let n=0,s=0;n<t.length;){let r=0;for(;;){let e=t.charCodeAt(n++),i=!1;if(126==e){r=65535;break}e>=92&&e--,e>=34&&e--;let s=e-32;if(s>=46&&(s-=46,i=!0),r+=s,i)break;r*=46}i?i[s++]=r:i=new e(r)}return i}class Eh{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const Mh=new Eh;class Dh{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=Mh,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,s=this.pos+t;for(;s<i.from;){if(!n)return null;let t=this.ranges[--n];s-=i.from-t.to,i=t}for(;e<0?s>i.to:s>=i.to;){if(n==this.ranges.length-1)return null;let t=this.ranges[++n];s+=t.from-i.to,i=t}return s}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let e of this.ranges)if(e.to>t)return Math.max(t,e.from);return this.end}peek(t){let e,i,n=this.chunkOff+t;if(n>=0&&n<this.chunk.length)e=this.pos+t,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(t,1);if(null==n)return-1;if(e=n,e>=this.chunk2Pos&&e<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(e-this.chunk2Pos);else{let t=this.rangeIndex,n=this.range;for(;n.to<=e;)n=this.ranges[++t];this.chunk2=this.input.chunk(this.chunk2Pos=e),e+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-e)),i=this.chunk2.charCodeAt(0)}}return e>=this.token.lookAhead&&(this.token.lookAhead=e+1),i}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=Mh,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class zh{constructor(t,e){this.data=t,this.id=e}token(t,e){let{parser:i}=e.p;Bh(this.data,t,e,this.id,i.data,i.tokenPrecTable)}}zh.prototype.contextual=zh.prototype.fallback=zh.prototype.extend=!1;class Gh{constructor(t,e,i){this.precTable=e,this.elseToken=i,this.data="string"==typeof t?_h(t):t}token(t,e){let i=t.pos,n=0;for(;;){let i=t.next<0,s=t.resolveOffset(1,1);if(Bh(this.data,t,e,0,this.data,this.precTable),t.token.value>-1)break;if(null==this.elseToken)return;if(i||n++,null==s)break;t.reset(s,t.token)}n&&(t.reset(i,t.token),t.acceptToken(this.elseToken,n))}}Gh.prototype.contextual=zh.prototype.fallback=zh.prototype.extend=!1;class Ih{constructor(t,e={}){this.token=t,this.contextual=!!e.contextual,this.fallback=!!e.fallback,this.extend=!!e.extend}}function Bh(t,e,i,n,s,r){let o=0,a=1<<n,{dialect:l}=i.p.parser;t:for(;0!=(a&t[o]);){let i=t[o+1];for(let n=o+3;n<i;n+=2)if((t[n+1]&a)>0){let i=t[n];if(l.allows(i)&&(-1==e.token.value||e.token.value==i||Uh(i,e.token.value,s,r))){e.acceptToken(i);break}}let n=e.next,h=0,c=t[o+2];if(!(e.next<0&&c>h&&65535==t[i+3*c-3]&&65535==t[i+3*c-3])){for(;h<c;){let s=h+c>>1,r=i+s+(s<<1),a=t[r],l=t[r+1]||65536;if(n<a)c=s;else{if(!(n>=l)){o=t[r+2],e.advance();continue t}h=s+1}}break}o=t[i+3*c-1]}}function Nh(t,e,i){for(let n,s=e;65535!=(n=t[s]);s++)if(n==i)return s-e;return-1}function Uh(t,e,i,n){let s=Nh(i,n,e);return s<0||Nh(i,n,t)<s}const Lh="undefined"!=typeof process&&process.env&&/\bparse\b/.test(process.env.LOG);let Fh=null;function Hh(t,e,i){let n=t.cursor(c.IncludeAnonymous);for(n.moveTo(e);;)if(!(i<0?n.childBefore(e):n.childAfter(e)))for(;;){if((i<0?n.to<e:n.from>e)&&!n.type.isError)return i<0?Math.max(0,Math.min(n.to-1,e-25)):Math.min(t.length,Math.max(n.from+1,e+25));if(i<0?n.prevSibling():n.nextSibling())break;if(!n.parent())return i<0?0:t.length}}class Jh{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?Hh(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?Hh(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],s=this.index[e];if(s==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[s],o=this.start[e]+i.positions[s];if(o>t)return this.nextStart=o,null;if(r instanceof O){if(o==t){if(o<this.safeFrom)return null;let t=o+r.length;if(t<=this.safeTo){let e=r.prop(n.lookAhead);if(!e||t+e<this.fragment.to)return r}}this.index[e]++,o+r.length>=Math.max(this.safeFrom,t)&&(this.trees.push(r),this.start.push(o),this.index.push(0))}else this.index[e]++,this.nextStart=o+r.length}}}class Kh{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map((t=>new Eh))}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:s}=n,r=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,a=0;for(let n=0;n<s.length;n++){if(0==(1<<n&r))continue;let l=s[n],h=this.tokens[n];if((!i||l.fallback)&&((l.contextual||h.start!=t.pos||h.mask!=r||h.context!=o)&&(this.updateCachedToken(h,l,t),h.mask=r,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let n=e;if(h.extended>-1&&(e=this.addActions(t,h.extended,h.end,e)),e=this.addActions(t,h.value,h.end,e),!l.extend&&(i=h,e>n))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),i||t.pos!=this.stream.end||(i=new Eh,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new Eh,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){let n=this.stream.clipPos(i.pos);if(e.token(this.stream.reset(n,t),i),t.value>-1){let{parser:e}=i.p;for(let n=0;n<e.specialized.length;n++)if(e.specialized[n]==t.value){let s=e.specializers[n](this.stream.read(t.start,t.end),i);if(s>=0&&i.p.parser.dialect.allows(s>>1)){0==(1&s)?t.value=s>>1:t.extended=s>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,e,i,n){for(let e=0;e<n;e+=3)if(this.actions[e]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:s}=t,{parser:r}=t.p,{data:o}=r;for(let t=0;t<2;t++)for(let a=r.stateSlot(s,t?2:1);;a+=3){if(65535==o[a]){if(1!=o[a+1]){0==n&&2==o[a+1]&&(n=this.putAction(oc(o,a+2),e,i,n));break}a=oc(o,a+2)}o[a]==e&&(n=this.putAction(oc(o,a+1),e,i,n))}return n}}class tc{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new Dh(e,n),this.tokens=new Kh(t,this.stream),this.topTerm=t.top[1];let{from:s}=n[0];this.stacks=[Wh.start(this,t.top[0],s)],this.fragments=i.length&&this.stream.end-s>4*t.bufferLength?new Jh(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t,e,i=this.stacks,n=this.minStackPos,s=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[t]=i;for(;t.forceReduce()&&t.stack.length&&t.stack[t.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let r=0;r<i.length;r++){let o=i[r];for(;;){if(this.tokens.mainToken=null,o.pos>n)s.push(o);else{if(this.advanceStack(o,s,i))continue;{t||(t=[],e=[]),t.push(o);let i=this.tokens.getMainToken(o);e.push(i.value,i.end)}}break}}if(!s.length){let e=t&&function(t){let e=null;for(let i of t){let t=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=t&&i.pos>t)&&i.p.parser.stateFlag(i.state,2)&&(!e||e.score<i.score)&&(e=i)}return e}(t);if(e)return Lh&&console.log("Finish with "+this.stackID(e)),this.stackToTree(e);if(this.parser.strict)throw Lh&&t&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&t){let i=null!=this.stoppedAt&&t[0].pos>this.stoppedAt?t[0]:this.runRecovery(t,e,s);if(i)return Lh&&console.log("Force-finish "+this.stackID(i)),this.stackToTree(i.forceAll())}if(this.recovering){let t=1==this.recovering?1:3*this.recovering;if(s.length>t)for(s.sort(((t,e)=>e.score-t.score));s.length>t;)s.pop();s.some((t=>t.reducePos>n))&&this.recovering--}else if(s.length>1){t:for(let t=0;t<s.length-1;t++){let e=s[t];for(let i=t+1;i<s.length;i++){let n=s[i];if(e.sameState(n)||e.buffer.length>500&&n.buffer.length>500){if(!((e.score-n.score||e.buffer.length-n.buffer.length)>0)){s.splice(t--,1);continue t}s.splice(i--,1)}}}s.length>12&&s.splice(12,s.length-12)}this.minStackPos=s[0].pos;for(let t=1;t<s.length;t++)s[t].pos<this.minStackPos&&(this.minStackPos=s[t].pos);return null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let s=t.pos,{parser:r}=this,o=Lh?this.stackID(t)+" -> ":"";if(null!=this.stoppedAt&&s>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let e=t.curContext&&t.curContext.tracker.strict,i=e?t.curContext.hash:0;for(let a=this.fragments.nodeAt(s);a;){let s=this.parser.nodeSet.types[a.type.id]==a.type?r.getGoto(t.state,a.type.id):-1;if(s>-1&&a.length&&(!e||(a.prop(n.contextHash)||0)==i))return t.useNode(a,s),Lh&&console.log(o+this.stackID(t)+` (via reuse of ${r.getName(a.type.id)})`),!0;if(!(a instanceof O)||0==a.children.length||a.positions[0]>0)break;let l=a.children[0];if(!(l instanceof O&&0==a.positions[0]))break;a=l}}let a=r.stateSlot(t.state,4);if(a>0)return t.reduce(a),Lh&&console.log(o+this.stackID(t)+` (via always-reduce ${r.getName(65535&a)})`),!0;if(t.stack.length>=9e3)for(;t.stack.length>6e3&&t.forceReduce(););let l=this.tokens.getActions(t);for(let n=0;n<l.length;){let a=l[n++],h=l[n++],c=l[n++],O=n==l.length||!i,u=O?t:t.split(),f=this.tokens.mainToken;if(u.apply(a,h,f?f.start:u.pos,c),Lh&&console.log(o+this.stackID(u)+` (via ${0==(65536&a)?"shift":`reduce of ${r.getName(65535&a)}`} for ${r.getName(h)} @ ${s}${u==t?"":", split"})`),O)return!0;u.pos>s?e.push(u):i.push(u)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return ec(t,e),!0}}runRecovery(t,e,i){let n=null,s=!1;for(let r=0;r<t.length;r++){let o=t[r],a=e[r<<1],l=e[1+(r<<1)],h=Lh?this.stackID(o)+" -> ":"";if(o.deadEnd){if(s)continue;if(s=!0,o.restart(),Lh&&console.log(h+this.stackID(o)+" (restarted)"),this.advanceFully(o,i))continue}let c=o.split(),O=h;for(let t=0;c.forceReduce()&&t<10;t++){if(Lh&&console.log(O+this.stackID(c)+" (via force-reduce)"),this.advanceFully(c,i))break;Lh&&(O=this.stackID(c)+" -> ")}for(let t of o.recoverByInsert(a))Lh&&console.log(h+this.stackID(t)+" (via recover-insert)"),this.advanceFully(t,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),Lh&&console.log(h+this.stackID(o)+` (via recover-delete ${this.parser.getName(a)})`),ec(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),O.build({buffer:qh.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(Fh||(Fh=new WeakMap)).get(t);return e||Fh.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function ec(t,e){for(let i=0;i<e.length;i++){let n=e[i];if(n.pos==t.pos&&n.sameState(t))return void(e[i].score<t.score&&(e[i]=t))}e.push(t)}class ic{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||0==this.disabled[t]}}const nc=t=>t;class sc{constructor(t){this.start=t.start,this.shift=t.shift||nc,this.reduce=t.reduce||nc,this.reuse=t.reuse||nc,this.hash=t.hash||(()=>0),this.strict=!1!==t.strict}}class rc extends C{constructor(e){if(super(),this.wrappers=[],14!=e.version)throw new RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let i=e.nodeNames.split(" ");this.minRepeatTerm=i.length;for(let t=0;t<e.repeatNodeCount;t++)i.push("");let s=Object.keys(e.topRules).map((t=>e.topRules[t][1])),r=[];for(let t=0;t<i.length;t++)r.push([]);function l(t,e,i){r[t].push([e,e.deserialize(String(i))])}if(e.nodeProps)for(let t of e.nodeProps){let e=t[0];"string"==typeof e&&(e=n[e]);for(let i=1;i<t.length;){let n=t[i++];if(n>=0)l(n,e,t[i++]);else{let s=t[i+-n];for(let r=-n;r>0;r--)l(t[i++],e,s);i++}}}this.nodeSet=new a(i.map(((t,i)=>o.define({name:i>=this.minRepeatTerm?void 0:t,id:i,props:r[i],top:s.indexOf(i)>-1,error:0==i,skipped:e.skippedNodes&&e.skippedNodes.indexOf(i)>-1})))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=t;let h=_h(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let t=0;t<this.specializerSpecs.length;t++)this.specialized[t]=this.specializerSpecs[t].term;this.specializers=this.specializerSpecs.map(ac),this.states=_h(e.states,Uint32Array),this.data=_h(e.stateData),this.goto=_h(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map((t=>"number"==typeof t?new zh(h,t):t)),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new tc(this,t,e,i);for(let s of this.wrappers)n=s(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let s=n[e+1];;){let e=n[s++],r=1&e,o=n[s++];if(r&&i)return o;for(let i=s+(e>>1);s<i;s++)if(n[s]==t)return o;if(r)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let s,r=this.stateSlot(t,n?2:1);;r+=3){if(65535==(s=i[r])){if(1!=i[r+1]){if(2==i[r+1])return oc(i,r+2);break}s=i[r=oc(i,r+2)]}if(s==e||0==s)return oc(i,r+1)}return 0}stateSlot(t,e){return this.states[6*t+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){return!!this.allActions(t,(t=>t==e||null))}allActions(t,e){let i=this.stateSlot(t,4),n=i?e(i):void 0;for(let i=this.stateSlot(t,1);null==n;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=oc(this.data,i+2)}n=e(oc(this.data,i+1))}return n}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=oc(this.data,i+2)}if(0==(1&this.data[i+2])){let t=this.data[i+1];e.some(((e,i)=>1&i&&e==t))||e.push(this.data[i],t)}}return e}configure(t){let e=Object.assign(Object.create(rc.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map((e=>{let i=t.tokenizers.find((t=>t.from==e));return i?i.to:e}))),t.specializers&&(e.specializers=this.specializers.slice(),e.specializerSpecs=this.specializerSpecs.map(((i,n)=>{let s=t.specializers.find((t=>t.from==i.external));if(!s)return i;let r=Object.assign(Object.assign({},i),{external:s.to});return e.specializers[n]=ac(r),r}))),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),null!=t.strict&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),null!=t.bufferLength&&(e.bufferLength=t.bufferLength),e}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return null==e?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map((()=>!1));if(t)for(let n of t.split(" ")){let t=e.indexOf(n);t>=0&&(i[t]=!0)}let n=null;for(let t=0;t<e.length;t++)if(!i[t])for(let i,s=this.dialects[e[t]];65535!=(i=this.data[s++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[i]=1;return new ic(t,i,n)}static deserialize(t){return new rc(t)}}function oc(t,e){return t[e]|t[e+1]<<16}function ac(t){if(t.external){let e=t.extend?1:0;return(i,n)=>t.external(i,n)<<1|e}return t.get}const lc=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288];function hc(t){return t>=65&&t<=90||t>=97&&t<=122||t>=161}const cc=new Ih(((t,e)=>{for(let n=!1,s=0,r=0;;r++){let{next:o}=t;if(hc(o)||45==o||95==o||n&&((i=o)>=48&&i<=57))!n&&(45!=o||r>0)&&(n=!0),s===r&&45==o&&s++,t.advance();else{if(92!=o||10==t.peek(1)){n&&t.acceptToken(40==o?100:2==s&&e.canShift(2)?2:101);break}t.advance(),t.next>-1&&t.advance(),n=!0}}var i})),Oc=new Ih((t=>{if(lc.includes(t.peek(-1))){let{next:e}=t;(hc(e)||95==e||35==e||46==e||91==e||58==e||45==e||38==e)&&t.acceptToken(99)}})),uc=new Ih((t=>{if(!lc.includes(t.peek(-1))){let{next:e}=t;if(37==e&&(t.advance(),t.acceptToken(1)),hc(e)){do{t.advance()}while(hc(t.next));t.acceptToken(1)}}})),fc=Oa({"AtKeyword import charset namespace keyframes media supports":Ya.definitionKeyword,"from to selector":Ya.keyword,NamespaceName:Ya.namespace,KeyframeName:Ya.labelName,KeyframeRangeName:Ya.operatorKeyword,TagName:Ya.tagName,ClassName:Ya.className,PseudoClassName:Ya.constant(Ya.className),IdName:Ya.labelName,"FeatureName PropertyName":Ya.propertyName,AttributeName:Ya.attributeName,NumberLiteral:Ya.number,KeywordQuery:Ya.keyword,UnaryQueryOp:Ya.operatorKeyword,"CallTag ValueName":Ya.atom,VariableName:Ya.variableName,Callee:Ya.operatorKeyword,Unit:Ya.unit,"UniversalSelector NestingSelector":Ya.definitionOperator,MatchOp:Ya.compareOperator,"ChildOp SiblingOp, LogicOp":Ya.logicOperator,BinOp:Ya.arithmeticOperator,Important:Ya.modifier,Comment:Ya.blockComment,ColorLiteral:Ya.color,"ParenthesizedContent StringLiteral":Ya.string,":":Ya.punctuation,"PseudoOp #":Ya.derefOperator,"; ,":Ya.separator,"( )":Ya.paren,"[ ]":Ya.squareBracket,"{ }":Ya.brace}),dc={__proto__:null,lang:32,"nth-child":32,"nth-last-child":32,"nth-of-type":32,"nth-last-of-type":32,dir:32,"host-context":32,url:60,"url-prefix":60,domain:60,regexp:60,selector:138},pc={__proto__:null,"@import":118,"@media":142,"@charset":146,"@namespace":150,"@keyframes":156,"@supports":168},mc={__proto__:null,not:132,only:132},gc=rc.deserialize({version:14,states:":^QYQ[OOO#_Q[OOP#fOWOOOOQP'#Cd'#CdOOQP'#Cc'#CcO#kQ[O'#CfO$_QXO'#CaO$fQ[O'#ChO$qQ[O'#DTO$vQ[O'#DWOOQP'#Em'#EmO${QdO'#DgO%jQ[O'#DtO${QdO'#DvO%{Q[O'#DxO&WQ[O'#D{O&`Q[O'#ERO&nQ[O'#ETOOQS'#El'#ElOOQS'#EW'#EWQYQ[OOO&uQXO'#CdO'jQWO'#DcO'oQWO'#EsO'zQ[O'#EsQOQWOOP(UO#tO'#C_POOO)C@[)C@[OOQP'#Cg'#CgOOQP,59Q,59QO#kQ[O,59QO(aQ[O'#E[O({QWO,58{O)TQ[O,59SO$qQ[O,59oO$vQ[O,59rO(aQ[O,59uO(aQ[O,59wO(aQ[O,59xO)`Q[O'#DbOOQS,58{,58{OOQP'#Ck'#CkOOQO'#DR'#DROOQP,59S,59SO)gQWO,59SO)lQWO,59SOOQP'#DV'#DVOOQP,59o,59oOOQO'#DX'#DXO)qQ`O,59rOOQS'#Cp'#CpO${QdO'#CqO)yQvO'#CsO+ZQtO,5:ROOQO'#Cx'#CxO)lQWO'#CwO+oQWO'#CyO+tQ[O'#DOOOQS'#Ep'#EpOOQO'#Dj'#DjO+|Q[O'#DqO,[QWO'#EtO&`Q[O'#DoO,jQWO'#DrOOQO'#Eu'#EuO)OQWO,5:`O,oQpO,5:bOOQS'#Dz'#DzO,wQWO,5:dO,|Q[O,5:dOOQO'#D}'#D}O-UQWO,5:gO-ZQWO,5:mO-cQWO,5:oOOQS-E8U-E8UO${QdO,59}O-kQ[O'#E^O-xQWO,5;_O-xQWO,5;_POOO'#EV'#EVP.TO#tO,58yPOOO,58y,58yOOQP1G.l1G.lO.zQXO,5:vOOQO-E8Y-E8YOOQS1G.g1G.gOOQP1G.n1G.nO)gQWO1G.nO)lQWO1G.nOOQP1G/Z1G/ZO/XQ`O1G/^O/rQXO1G/aO0YQXO1G/cO0pQXO1G/dO1WQWO,59|O1]Q[O'#DSO1dQdO'#CoOOQP1G/^1G/^O${QdO1G/^O1kQpO,59]OOQS,59_,59_O${QdO,59aO1sQWO1G/mOOQS,59c,59cO1xQ!bO,59eOOQS'#DP'#DPOOQS'#EY'#EYO2QQ[O,59jOOQS,59j,59jO2YQWO'#DjO2eQWO,5:VO2jQWO,5:]O&`Q[O,5:XO&`Q[O'#E_O2rQWO,5;`O2}QWO,5:ZO(aQ[O,5:^OOQS1G/z1G/zOOQS1G/|1G/|OOQS1G0O1G0OO3`QWO1G0OO3eQdO'#EOOOQS1G0R1G0ROOQS1G0X1G0XOOQS1G0Z1G0ZO3pQtO1G/iOOQO,5:x,5:xO4WQ[O,5:xOOQO-E8[-E8[O4eQWO1G0yPOOO-E8T-E8TPOOO1G.e1G.eOOQP7+$Y7+$YOOQP7+$x7+$xO${QdO7+$xOOQS1G/h1G/hO4pQXO'#ErO4wQWO,59nO4|QtO'#EXO5tQdO'#EoO6OQWO,59ZO6TQpO7+$xOOQS1G.w1G.wOOQS1G.{1G.{OOQS7+%X7+%XO6]QWO1G/POOQS-E8W-E8WOOQS1G/U1G/UO${QdO1G/qOOQO1G/w1G/wOOQO1G/s1G/sO6bQWO,5:yOOQO-E8]-E8]O6pQXO1G/xOOQS7+%j7+%jO6wQYO'#CsOOQO'#EQ'#EQO7SQ`O'#EPOOQO'#EP'#EPO7_QWO'#E`O7gQdO,5:jOOQS,5:j,5:jO7rQtO'#E]O${QdO'#E]O8sQdO7+%TOOQO7+%T7+%TOOQO1G0d1G0dO9WQpO<<HdO9`QWO,5;^OOQP1G/Y1G/YOOQS-E8V-E8VO${QdO'#EZO9hQWO,5;ZOOQT1G.u1G.uOOQP<<Hd<<HdOOQS7+$k7+$kO9pQdO7+%]OOQO7+%d7+%dOOQO,5:k,5:kO3hQdO'#EaO7_QWO,5:zOOQS,5:z,5:zOOQS-E8^-E8^OOQS1G0U1G0UO9wQtO,5:wOOQS-E8Z-E8ZOOQO<<Ho<<HoOOQPAN>OAN>OO:xQdO,5:uOOQO-E8X-E8XOOQO<<Hw<<HwOOQO,5:{,5:{OOQO-E8_-E8_OOQS1G0f1G0f",stateData:";[~O#ZOS#[QQ~OUYOXYO]VO^VOqXOxWO![aO!]ZO!i[O!k]O!m^O!p_O!v`O#XRO#bTO~OQfOUYOXYO]VO^VOqXOxWO![aO!]ZO!i[O!k]O!m^O!p_O!v`O#XeO#bTO~O#U#gP~P!ZO#[jO~O#XlO~O]qO^qOqsOtoOxrO!OtO!RvO#VuO#bnO~O!TwO~P#pO`}O#WzO#XyO~O#X!OO~O#X!QO~OQ![Ob!TOf![Oh![On!YOq!ZO#W!WO#X!SO#e!UO~Ob!^O!d!`O!g!aO#X!]O!T#hP~Oh!fOn!YO#X!eO~Oh!hO#X!hO~Ob!^O!d!`O!g!aO#X!]O~O!Y#hP~P%jO]WX]!WX^WXqWXtWXxWX!OWX!RWX!TWX#VWX#bWX~O]!mO~O!Y!nO#U#gX!S#gX~O#U#gX!S#gX~P!ZO#]!qO#^!qO#_!sO~OUYOXYO]VO^VOqXOxWO#XRO#bTO~OtoO!TwO~O`!zO#WzO#XyO~O!S#gP~P!ZOb#RO~Ob#SO~Op#TO|#UO~OP#WObgXjgX!YgX!dgX!ggX#XgXagXQgXfgXhgXngXqgXtgX!XgX#UgX#WgX#egXpgX!SgX~Ob!^Oj#XO!d!`O!g!aO#X!]O!Y#hP~Ob#[O~Op#`O#X#]O~Ob!^O!d!`O!g!aO#X#aO~Ot#eO!b#dO!T#hX!Y#hX~Ob#hO~Oj#XO!Y#jO~O!Y#kO~Oh#lOn!YO~O!T#mO~O!TwO!b#dO~O!TwO!Y#pO~O!Y#QX#U#QX!S#QX~P!ZO!Y!nO#U#ga!S#ga~O#]!qO#^!qO#_#wO~O]qO^qOqsOxrO!OtO!RvO#VuO#bnO~Ot#Oa!T#Oaa#Oa~P.`Op#yO|#zO~O]qO^qOqsOxrO#bnO~Ot}i!O}i!R}i!T}i#V}ia}i~P/aOt!Pi!O!Pi!R!Pi!T!Pi#V!Pia!Pi~P/aOt!Qi!O!Qi!R!Qi!T!Qi#V!Qia!Qi~P/aO!S#{O~Oa#fP~P(aOa#cP~P${Oa$SOj#XO~O!Y$UO~Oh$VOo$VO~Op$XO#X#]O~O]!`Xa!^X!b!^X~O]$YO~Oa$ZO!b#dO~Ot#eO!T#ha!Y#ha~O!b#dOt!ca!T!ca!Y!caa!ca~O!Y$`O~O!S$gO#X$bO#e$aO~Oj#XOt$iO!X$kO!Y!Vi#U!Vi!S!Vi~P${O!Y#Qa#U#Qa!S#Qa~P!ZO!Y!nO#U#gi!S#gi~Oa#fX~P#pOa$oO~Oj#XOQ!{Xa!{Xb!{Xf!{Xh!{Xn!{Xq!{Xt!{X#W!{X#X!{X#e!{X~Ot$qOa#cX~P${Oa$sO~Oj#XOp$tO~Oa$uO~O!b#dOt#Ra!T#Ra!Y#Ra~Oa$wO~P.`OP#WOtgX!TgX~O#e$aOt!sX!T!sX~Ot$yO!TwO~O!S$}O#X$bO#e$aO~Oj#XOQ#PXb#PXf#PXh#PXn#PXq#PXt#PX!X#PX!Y#PX#U#PX#W#PX#X#PX#e#PX!S#PX~Ot$iO!X%QO!Y!Vq#U!Vq!S!Vq~P${Oj#XOp%RO~OtoOa#fa~Ot$qOa#ca~Oa%UO~P${Oj#XOQ#Pab#Paf#Pah#Pan#Paq#Pat#Pa!X#Pa!Y#Pa#U#Pa#W#Pa#X#Pa#e#Pa!S#Pa~Oa!}at!}a~P${O#Zo#[#ej!R#e~",goto:"-g#jPPP#kP#nP#w$WP#w$g#wPP$mPPP$s$|$|P%`P$|P$|%z&^PPPP$|&vP&z'Q#wP'W#w'^P#wP#w#wPPP'd'y(WPP#nPP(_(_(i(_P(_P(_(_P#nP#nP#nP(l#nP(o(r(u(|#nP#nP)R)X)h)v)|*S*^*d*n*t*zPPPPPPPPPP+Q+ZP+v+yP,o,r,x-RRkQ_bOPdhw!n#skYOPdhotuvw!n#R#h#skSOPdhotuvw!n#R#h#sQmTR!tnQ{VR!xqQ!x}Q#Z!XR#x!zq![Z]!T!m#S#U#X#q#z$P$Y$i$j$q$v%Sp![Z]!T!m#S#U#X#q#z$P$Y$i$j$q$v%SU$d#m$f$yR$x$cq!XZ]!T!m#S#U#X#q#z$P$Y$i$j$q$v%Sp![Z]!T!m#S#U#X#q#z$P$Y$i$j$q$v%SQ!f^R#l!gT#^!Z#_Q|VR!yqQ!x|R#x!yQ!PWR!{rQ!RXR!|sQxUQ!wpQ#i!cQ#o!jQ#p!kQ${$eR%X$zSgPwQ!phQ#r!nR$l#sZfPhw!n#sa!b[`a!V!^!`#d#eR#b!^R!g^R!i_R#n!iS$e#m$fR%V$yV$c#m$f$yQ!rjR#v!rQdOShPwU!ldh#sR#s!nQ$P#SU$p$P$v%SQ$v$YR%S$qQ#_!ZR$W#_Q$r$PR%T$rQpUS!vp$nR$n#|Q$j#qR%P$jQ!ogS#t!o#uR#u!pQ#f!_R$^#fQ$f#mR$|$fQ$z$eR%W$z_cOPdhw!n#s^UOPdhw!n#sQ!uoQ!}tQ#OuQ#PvQ#|#RR$_#hR$Q#SQ!VZQ!d]Q#V!TQ#q!m[$O#S$P$Y$q$v%SQ$R#UQ$T#XS$h#q$jQ$m#zR%O$iR#}#RQiPR#QwQ!c[Q!kaR#Y!VU!_[a!VQ!j`Q#c!^Q#g!`Q$[#dR$]#e",nodeNames:"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName , PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports AtRule Styles",maxTerm:117,nodeProps:[["isolate",-2,3,24,""],["openedBy",17,"(",32,"[",50,"{"],["closedBy",18,")",33,"]",51,"}"]],propSources:[fc],skippedNodes:[0,3,87],repeatNodeCount:11,tokenData:"J^~R!^OX$}X^%u^p$}pq%uqr)Xrs.Rst/utu6duv$}vw7^wx7oxy9^yz9oz{9t{|:_|}?Q}!O?c!O!P@Q!P!Q@i!Q![Ab![!]B]!]!^CX!^!_$}!_!`Cj!`!aC{!a!b$}!b!cDw!c!}$}!}#OFa#O#P$}#P#QFr#Q#R6d#R#T$}#T#UGT#U#c$}#c#dHf#d#o$}#o#pH{#p#q6d#q#rI^#r#sIo#s#y$}#y#z%u#z$f$}$f$g%u$g#BY$}#BY#BZ%u#BZ$IS$}$IS$I_%u$I_$I|$}$I|$JO%u$JO$JT$}$JT$JU%u$JU$KV$}$KV$KW%u$KW&FU$}&FU&FV%u&FV;'S$};'S;=`JW<%lO$}`%QSOy%^z;'S%^;'S;=`%o<%lO%^`%cSo`Oy%^z;'S%^;'S;=`%o<%lO%^`%rP;=`<%l%^~%zh#Z~OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^~'mh#Z~o`OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^l)[UOy%^z#]%^#]#^)n#^;'S%^;'S;=`%o<%lO%^l)sUo`Oy%^z#a%^#a#b*V#b;'S%^;'S;=`%o<%lO%^l*[Uo`Oy%^z#d%^#d#e*n#e;'S%^;'S;=`%o<%lO%^l*sUo`Oy%^z#c%^#c#d+V#d;'S%^;'S;=`%o<%lO%^l+[Uo`Oy%^z#f%^#f#g+n#g;'S%^;'S;=`%o<%lO%^l+sUo`Oy%^z#h%^#h#i,V#i;'S%^;'S;=`%o<%lO%^l,[Uo`Oy%^z#T%^#T#U,n#U;'S%^;'S;=`%o<%lO%^l,sUo`Oy%^z#b%^#b#c-V#c;'S%^;'S;=`%o<%lO%^l-[Uo`Oy%^z#h%^#h#i-n#i;'S%^;'S;=`%o<%lO%^l-uS!X[o`Oy%^z;'S%^;'S;=`%o<%lO%^~.UWOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o<%lO.R~.sOh~~.vRO;'S.R;'S;=`/P;=`O.R~/SXOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o;=`<%l.R<%lO.R~/rP;=`<%l.Rn/zYxQOy%^z!Q%^!Q![0j![!c%^!c!i0j!i#T%^#T#Z0j#Z;'S%^;'S;=`%o<%lO%^l0oYo`Oy%^z!Q%^!Q![1_![!c%^!c!i1_!i#T%^#T#Z1_#Z;'S%^;'S;=`%o<%lO%^l1dYo`Oy%^z!Q%^!Q![2S![!c%^!c!i2S!i#T%^#T#Z2S#Z;'S%^;'S;=`%o<%lO%^l2ZYf[o`Oy%^z!Q%^!Q![2y![!c%^!c!i2y!i#T%^#T#Z2y#Z;'S%^;'S;=`%o<%lO%^l3QYf[o`Oy%^z!Q%^!Q![3p![!c%^!c!i3p!i#T%^#T#Z3p#Z;'S%^;'S;=`%o<%lO%^l3uYo`Oy%^z!Q%^!Q![4e![!c%^!c!i4e!i#T%^#T#Z4e#Z;'S%^;'S;=`%o<%lO%^l4lYf[o`Oy%^z!Q%^!Q![5[![!c%^!c!i5[!i#T%^#T#Z5[#Z;'S%^;'S;=`%o<%lO%^l5aYo`Oy%^z!Q%^!Q![6P![!c%^!c!i6P!i#T%^#T#Z6P#Z;'S%^;'S;=`%o<%lO%^l6WSf[o`Oy%^z;'S%^;'S;=`%o<%lO%^d6gUOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^d7QS|So`Oy%^z;'S%^;'S;=`%o<%lO%^b7cSXQOy%^z;'S%^;'S;=`%o<%lO%^~7rWOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W<%lO7o~8_RO;'S7o;'S;=`8h;=`O7o~8kXOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W;=`<%l7o<%lO7o~9ZP;=`<%l7on9cSb^Oy%^z;'S%^;'S;=`%o<%lO%^~9tOa~n9{UUQjWOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^n:fWjW!RQOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^l;TUo`Oy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^l;nYo`#e[Oy%^z!Q%^!Q![;g![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^l<cYo`Oy%^z{%^{|=R|}%^}!O=R!O!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=WUo`Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=qUo`#e[Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l>[[o`#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^n?VSt^Oy%^z;'S%^;'S;=`%o<%lO%^l?hWjWOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^n@VU#bQOy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^~@nTjWOy%^z{@}{;'S%^;'S;=`%o<%lO%^~AUSo`#[~Oy%^z;'S%^;'S;=`%o<%lO%^lAg[#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^bBbU]QOy%^z![%^![!]Bt!];'S%^;'S;=`%o<%lO%^bB{S^Qo`Oy%^z;'S%^;'S;=`%o<%lO%^nC^S!Y^Oy%^z;'S%^;'S;=`%o<%lO%^dCoS|SOy%^z;'S%^;'S;=`%o<%lO%^bDQU!OQOy%^z!`%^!`!aDd!a;'S%^;'S;=`%o<%lO%^bDkS!OQo`Oy%^z;'S%^;'S;=`%o<%lO%^bDzWOy%^z!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^bEk[![Qo`Oy%^z}%^}!OEd!O!Q%^!Q![Ed![!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^nFfSq^Oy%^z;'S%^;'S;=`%o<%lO%^nFwSp^Oy%^z;'S%^;'S;=`%o<%lO%^bGWUOy%^z#b%^#b#cGj#c;'S%^;'S;=`%o<%lO%^bGoUo`Oy%^z#W%^#W#XHR#X;'S%^;'S;=`%o<%lO%^bHYS!bQo`Oy%^z;'S%^;'S;=`%o<%lO%^bHiUOy%^z#f%^#f#gHR#g;'S%^;'S;=`%o<%lO%^fIQS!TUOy%^z;'S%^;'S;=`%o<%lO%^nIcS!S^Oy%^z;'S%^;'S;=`%o<%lO%^fItU!RQOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^`JZP;=`<%l$}",tokenizers:[Oc,uc,cc,1,2,3,4,new Gh("m~RRYZ[z{a~~g~aO#^~~dP!P!Qg~lO#_~~",28,105)],topRules:{StyleSheet:[0,4],Styles:[1,86]},specialized:[{term:100,get:t=>dc[t]||-1},{term:58,get:t=>pc[t]||-1},{term:101,get:t=>mc[t]||-1}],tokenPrec:1200});let wc=null;function bc(){if(!wc&&"object"==typeof document&&document.body){let{style:t}=document.body,e=[],i=new Set;for(let n in t)"cssText"!=n&&"cssFloat"!=n&&"string"==typeof t[n]&&(/[A-Z]/.test(n)&&(n=n.replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))),i.has(n)||(e.push(n),i.add(n)));wc=e.sort().map((t=>({type:"property",label:t})))}return wc||[]}const Sc=["active","after","any-link","autofill","backdrop","before","checked","cue","default","defined","disabled","empty","enabled","file-selector-button","first","first-child","first-letter","first-line","first-of-type","focus","focus-visible","focus-within","fullscreen","has","host","host-context","hover","in-range","indeterminate","invalid","is","lang","last-child","last-of-type","left","link","marker","modal","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","part","placeholder","placeholder-shown","read-only","read-write","required","right","root","scope","selection","slotted","target","target-text","valid","visited","where"].map((t=>({type:"class",label:t}))),yc=["above","absolute","activeborder","additive","activecaption","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","antialiased","appworkspace","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic-abegede-gez","ethiopic-halehame-aa-er","ethiopic-halehame-gez","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","graytext","grid","groove","hand","hard-light","help","hidden","hide","higher","highlight","highlighttext","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","justify","keep-all","landscape","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-hexadecimal","lower-latin","lower-norwegian","lowercase","ltr","luminosity","manipulation","match","matrix","matrix3d","medium","menu","menutext","message-box","middle","min-intrinsic","mix","monospace","move","multiple","multiple_mask_images","multiply","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","opacity","open-quote","optimizeLegibility","optimizeSpeed","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","text","text-bottom","text-top","textarea","textfield","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","to","top","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-latin","uppercase","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"].map((t=>({type:"keyword",label:t}))).concat(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"].map((t=>({type:"constant",label:t})))),Qc=["a","abbr","address","article","aside","b","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","dd","del","details","dfn","dialog","div","dl","dt","em","figcaption","figure","footer","form","header","hgroup","h1","h2","h3","h4","h5","h6","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","main","meter","nav","ol","output","p","pre","ruby","section","select","small","source","span","strong","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","tr","u","ul"].map((t=>({type:"type",label:t}))),vc=/^(\w[\w-]*|-\w[\w-]*|)$/,xc=/^-(-[\w-]*)?$/;const kc=new T,$c=["Declaration"];function Pc(t){for(let e=t;;){if(e.type.isTop)return e;if(!(e=e.parent))return t}}function Zc(t,e,i){if(e.to-e.from>4096){let n=kc.get(e);if(n)return n;let s=[],r=new Set,o=e.cursor(c.IncludeAnonymous);if(o.firstChild())do{for(let e of Zc(t,o.node,i))r.has(e.label)||(r.add(e.label),s.push(e))}while(o.nextSibling());return kc.set(e,s),s}{let n=[],s=new Set;return e.cursor().iterate((e=>{var r;if(i(e)&&e.matchContext($c)&&":"==(null===(r=e.node.nextSibling)||void 0===r?void 0:r.name)){let i=t.sliceString(e.from,e.to);s.has(i)||(s.add(i),n.push({label:i,type:"variable"}))}})),n}}const Tc=t=>e=>{let{state:i,pos:n}=e,s=Ea(i).resolveInner(n,-1),r=s.type.isError&&s.from==s.to-1&&"-"==i.doc.sliceString(s.from,s.to);if("PropertyName"==s.name||(r||"TagName"==s.name)&&/^(Block|Styles)$/.test(s.resolve(s.to).name))return{from:s.from,options:bc(),validFor:vc};if("ValueName"==s.name)return{from:s.from,options:yc,validFor:vc};if("PseudoClassName"==s.name)return{from:s.from,options:Sc,validFor:vc};if(t(s)||(e.explicit||r)&&function(t,e){var i;if(("("==t.name||t.type.isError)&&(t=t.parent||t),"ArgList"!=t.name)return!1;let n=null===(i=t.parent)||void 0===i?void 0:i.firstChild;return"Callee"==(null==n?void 0:n.name)&&"var"==e.sliceString(n.from,n.to)}(s,i.doc))return{from:t(s)||r?s.from:n,options:Zc(i.doc,Pc(s),t),validFor:xc};if("TagName"==s.name){for(let{parent:t}=s;t;t=t.parent)if("Block"==t.name)return{from:s.from,options:bc(),validFor:vc};return{from:s.from,options:Qc,validFor:vc}}if(!e.explicit)return null;let o=s.resolve(n),a=o.childBefore(n);return a&&":"==a.name&&"PseudoClassSelector"==o.name?{from:n,options:Sc,validFor:vc}:a&&":"==a.name&&"Declaration"==o.name||"ArgList"==o.name?{from:n,options:yc,validFor:vc}:"Block"==o.name||"Styles"==o.name?{from:n,options:bc(),validFor:vc}:null},Xc=Tc((t=>"VariableName"==t.name)),Cc=_a.define({name:"css",parser:gc.configure({props:[rl.add({Declaration:dl()}),gl.add({"Block KeyframeList":wl})]}),languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:"-"}});function Yc(){return new Ha(Cc,Cc.data.of({autocomplete:Xc}))}class Ac{constructor(t,e,i){this.state=t,this.pos=e,this.explicit=i,this.abortListeners=[]}tokenBefore(t){let e=Ea(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),s=n.search(_c(t,!1));return s<0?null:{from:i+s,to:this.pos,text:n.slice(s)}}get aborted(){return null==this.abortListeners}addEventListener(t,e){"abort"==t&&this.abortListeners&&this.abortListeners.push(e)}}function Rc(t){let e=Object.keys(t).join(""),i=/\w/.test(e);return i&&(e=e.replace(/\w/g,"")),`[${i?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function Wc(t){let e=t.map((t=>"string"==typeof t?{label:t}:t)),[i,n]=e.every((t=>/^\w+$/.test(t.label)))?[/\w*$/,/\w+$/]:function(t){let e=Object.create(null),i=Object.create(null);for(let{label:n}of t){e[n[0]]=!0;for(let t=1;t<n.length;t++)i[n[t]]=!0}let n=Rc(e)+Rc(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}(e);return t=>{let s=t.matchBefore(n);return s||t.explicit?{from:s?s.from:t.pos,options:e,validFor:i}:null}}function Vc(t,e){return i=>{for(let e=Ea(i.state).resolveInner(i.pos,-1);e;e=e.parent){if(t.indexOf(e.name)>-1)return null;if(e.type.isTop)break}return e(i)}}class jc{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function qc(t){return t.selection.main.from}function _c(t,e){var i;let{source:n}=t,s=e&&"^"!=n[0],r="$"!=n[n.length-1];return s||r?new RegExp(`${s?"^":""}(?:${n})${r?"$":""}`,null!==(i=t.flags)&&void 0!==i?i:t.ignoreCase?"i":""):t}const Ec=ie.define();const Mc=new WeakMap;function Dc(t){if(!Array.isArray(t))return t;let e=Mc.get(t);return e||Mc.set(t,e=Wc(t)),e}const zc=re.define(),Gc=re.define();class Ic{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=ut(t,e),n=dt(i);this.chars.push(i);let s=t.slice(e,e+n),r=s.toUpperCase();this.folded.push(ut(r==s?s.toLowerCase():r,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,!0}match(t){if(0==this.pattern.length)return this.ret(-100,[]);if(t.length<this.pattern.length)return!1;let{chars:e,folded:i,any:n,precise:s,byWord:r}=this;if(1==e.length){let n=ut(t,0),s=dt(n),r=s==t.length?0:-100;if(n==e[0]);else{if(n!=i[0])return!1;r+=-200}return this.ret(r,[0,s])}let o=t.indexOf(this.pattern);if(0==o)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,l=0;if(o<0){for(let s=0,r=Math.min(t.length,200);s<r&&l<a;){let r=ut(t,s);r!=e[l]&&r!=i[l]||(n[l++]=s),s+=dt(r)}if(l<a)return!1}let h=0,c=0,O=!1,u=0,f=-1,d=-1,p=/[a-z]/.test(t),m=!0;for(let n=0,l=Math.min(t.length,200),g=0;n<l&&c<a;){let l=ut(t,n);o<0&&(h<a&&l==e[h]&&(s[h++]=n),u<a&&(l==e[u]||l==i[u]?(0==u&&(f=n),d=n+1,u++):u=0));let w,b=l<255?l>=48&&l<=57||l>=97&&l<=122?2:l>=65&&l<=90?1:0:(w=ft(l))!=w.toLowerCase()?1:w!=w.toUpperCase()?2:0;(!n||1==b&&p||0==g&&0!=b)&&(e[c]==l||i[c]==l&&(O=!0)?r[c++]=n:r.length&&(m=!1)),g=b,n+=dt(l)}return c==a&&0==r[0]&&m?this.result((O?-200:0)-100,r,t):u==a&&0==f?this.ret(-200-t.length+(d==t.length?0:-100),[0,d]):o>-1?this.ret(-700-t.length,[o,o+this.pattern.length]):u==a?this.ret(-900-t.length,[f,d]):c==a?this.result((O?-200:0)-100-700+(m?0:-1100),r,t):2!=e.length&&this.result((n[0]?-700:0)-200-1100,n,t)}result(t,e,i){let n=[],s=0;for(let t of e){let e=t+(this.astral?dt(ut(i,t)):1);s&&n[s-1]==t?n[s-1]=e:(n[s++]=t,n[s++]=e)}return this.ret(t-i.length,n)}}const Bc=Tt.define({combine:t=>we(t,{activateOnTyping:!0,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:Uc,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>Nc(t(i),e(i)),optionClass:(t,e)=>i=>Nc(t(i),e(i)),addToOptions:(t,e)=>t.concat(e)})});function Nc(t,e){return t?e?t+" "+e:t:e}function Uc(t,e,i,n,s,r){let o,a,l=t.textDirection==sn.RTL,h=l,c=!1,O="top",u=e.left-s.left,f=s.right-e.right,d=n.right-n.left,p=n.bottom-n.top;if(h&&u<Math.min(d,f)?h=!1:!h&&f<Math.min(d,u)&&(h=!0),d<=(h?u:f))o=Math.max(s.top,Math.min(i.top,s.bottom-p))-e.top,a=Math.min(400,h?u:f);else{c=!0,a=Math.min(400,(l?e.right:s.right-e.left)-30);let t=s.bottom-e.bottom;t>=p||t>e.top?o=i.bottom-e.top:(O="bottom",o=e.bottom-i.top)}return{style:`${O}: ${o/((e.bottom-e.top)/r.offsetHeight)}px; max-width: ${a/((e.right-e.left)/r.offsetWidth)}px`,class:"cm-completionInfo-"+(c?l?"left-narrow":"right-narrow":h?"left":"right")}}function Lc(t,e,i){if(t<=i)return{from:0,to:t};if(e<0&&(e=0),e<=t>>1){let t=Math.floor(e/i);return{from:t*i,to:(t+1)*i}}let n=Math.floor((t-e)/i);return{from:t-(n+1)*i,to:t-n*i}}class Fc{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:t=>this.placeInfo(t),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:s,selected:r}=n.open,o=t.state.facet(Bc);this.optionContent=function(t){let e=t.addToOptions.slice();return t.icons&&e.push({render(t){let e=document.createElement("div");return e.classList.add("cm-completionIcon"),t.type&&e.classList.add(...t.type.split(/\s+/g).map((t=>"cm-completionIcon-"+t))),e.setAttribute("aria-hidden","true"),e},position:20}),e.push({render(t,e,i){let n=document.createElement("span");n.className="cm-completionLabel";let s=t.displayLabel||t.label,r=0;for(let t=0;t<i.length;){let e=i[t++],o=i[t++];e>r&&n.appendChild(document.createTextNode(s.slice(r,e)));let a=n.appendChild(document.createElement("span"));a.appendChild(document.createTextNode(s.slice(e,o))),a.className="cm-completionMatchedText",r=o}return r<s.length&&n.appendChild(document.createTextNode(s.slice(r))),n},position:50},{render(t){if(!t.detail)return null;let e=document.createElement("span");return e.className="cm-completionDetail",e.textContent=t.detail,e},position:80}),e.sort(((t,e)=>t.position-e.position)).map((t=>t.render))}(o),this.optionClass=o.optionClass,this.tooltipClass=o.tooltipClass,this.range=Lc(s.length,r,o.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",(i=>{let{options:n}=t.state.field(e).open;for(let e,s=i.target;s&&s!=this.dom;s=s.parentNode)if("LI"==s.nodeName&&(e=/-(\d+)$/.exec(s.id))&&+e[1]<n.length)return this.applyCompletion(t,n[+e[1]]),void i.preventDefault()})),this.dom.addEventListener("focusout",(e=>{let i=t.state.field(this.stateField,!1);i&&i.tooltip&&t.state.facet(Bc).closeOnBlur&&e.relatedTarget!=t.contentDOM&&t.dispatch({effects:Gc.of(null)})})),this.showOptions(s,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)}))}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:s,selected:r,disabled:o}=i.open;n.open&&n.open.options==s||(this.range=Lc(s.length,r,t.state.facet(Bc).maxRenderedOptions),this.showOptions(s,i.id)),this.updateSel(),o!=(null===(e=n.open)||void 0===e?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!o)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let t of this.currentClass.split(" "))t&&this.dom.classList.remove(t);for(let t of e.split(" "))t&&this.dom.classList.add(t);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=Lc(e.options.length,e.selected,this.view.state.facet(Bc).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let s="string"==typeof n?document.createTextNode(n):n(i);if(!s)return;"then"in s?s.then((e=>{e&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(e,i)})).catch((t=>Rn(this.view.state,t,"completion info"))):this.addInfoPane(s,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",null!=t.nodeType)i.appendChild(t),this.infoDestroy=null;else{let{dom:e,destroy:n}=t;i.appendChild(e),this.infoDestroy=n||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)"LI"==i.nodeName&&i.id?n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected"):n--;return e&&function(t,e){let i=t.getBoundingClientRect(),n=e.getBoundingClientRect(),s=i.height/t.offsetHeight;n.top<i.top?t.scrollTop-=(i.top-n.top)/s:n.bottom>i.bottom&&(t.scrollTop+=(n.bottom-i.bottom)/s)}(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),s=this.space;if(!s){let t=this.dom.ownerDocument.defaultView||window;s={left:0,top:0,right:t.innerWidth,bottom:t.innerHeight}}return n.top>Math.min(s.bottom,e.bottom)-10||n.bottom<Math.max(s.top,e.top)+10?null:this.view.state.facet(Bc).positionInfo(this.view,e,n,i,s,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions"));let s=null;for(let r=i.from;r<i.to;r++){let{completion:o,match:a}=t[r],{section:l}=o;if(l){let t="string"==typeof l?l:l.name;if(t!=s&&(r>i.from||0==i.from))if(s=t,"string"!=typeof l&&l.header)n.appendChild(l.header(l));else{n.appendChild(document.createElement("completion-section")).textContent=t}}const h=n.appendChild(document.createElement("li"));h.id=e+"-"+r,h.setAttribute("role","option");let c=this.optionClass(o);c&&(h.className=c);for(let t of this.optionContent){let e=t(o,this.view.state,a);e&&h.appendChild(e)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function Hc(t,e){return i=>new Fc(i,t,e)}function Jc(t){return 100*(t.boost||0)+(t.apply?10:0)+(t.info?5:0)+(t.type?1:0)}class Kc{constructor(t,e,i,n,s,r){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=s,this.disabled=r}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new Kc(this.options,iO(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,s){let r=function(t,e){let i=[],n=null,s=t=>{i.push(t);let{section:e}=t.completion;if(e){n||(n=[]);let t="string"==typeof e?e:e.name;n.some((e=>e.name==t))||n.push("string"==typeof e?{name:t}:e)}};for(let n of t)if(n.hasResult()){let t=n.result.getMatch;if(!1===n.result.filter)for(let e of n.result.options)s(new jc(e,n.source,t?t(e):[],1e9-i.length));else{let i=new Ic(e.sliceDoc(n.from,n.to));for(let e of n.result.options)if(i.match(e.label)){let r=e.displayLabel?t?t(e,i.matched):[]:i.matched;s(new jc(e,n.source,r,i.score+(e.boost||0)))}}}if(n){let t=Object.create(null),e=0,s=(t,e)=>{var i,n;return(null!==(i=t.rank)&&void 0!==i?i:1e9)-(null!==(n=e.rank)&&void 0!==n?n:1e9)||(t.name<e.name?-1:1)};for(let i of n.sort(s))e-=1e5,t[i.name]=e;for(let e of i){let{section:i}=e.completion;i&&(e.score+=t["string"==typeof i?i:i.name])}}let r=[],o=null,a=e.facet(Bc).compareCompletions;for(let t of i.sort(((t,e)=>e.score-t.score||a(t.completion,e.completion)))){let e=t.completion;!o||o.label!=e.label||o.detail!=e.detail||null!=o.type&&null!=e.type&&o.type!=e.type||o.apply!=e.apply||o.boost!=e.boost?r.push(t):Jc(t.completion)>Jc(o)&&(r[r.length-1]=t),o=t.completion}return r}(t,e);if(!r.length)return n&&t.some((t=>1==t.state))?new Kc(n.options,n.attrs,n.tooltip,n.timestamp,n.selected,!0):null;let o=e.facet(Bc).selectOnOpen?0:-1;if(n&&n.selected!=o&&-1!=n.selected){let t=n.options[n.selected].completion;for(let e=0;e<r.length;e++)if(r[e].completion==t){o=e;break}}return new Kc(r,iO(i,o),{pos:t.reduce(((t,e)=>e.hasResult()?Math.min(t,e.from):t),1e8),create:OO,above:s.aboveCursor},n?n.timestamp:Date.now(),o,!1)}map(t){return new Kc(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}}class tO{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new tO(nO,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(t){let{state:e}=t,i=e.facet(Bc),n=(i.override||e.languageDataAt("autocomplete",qc(e)).map(Dc)).map((e=>(this.active.find((t=>t.source==e))||new rO(e,this.active.some((t=>0!=t.state))?1:0)).update(t,i)));n.length==this.active.length&&n.every(((t,e)=>t==this.active[e]))&&(n=this.active);let s=this.open;s&&t.docChanged&&(s=s.map(t.changes)),t.selection||n.some((e=>e.hasResult()&&t.changes.touchesRange(e.from,e.to)))||!function(t,e){if(t==e)return!0;for(let i=0,n=0;;){for(;i<t.length&&!t[i].hasResult;)i++;for(;n<e.length&&!e[n].hasResult;)n++;let s=i==t.length,r=n==e.length;if(s||r)return s==r;if(t[i++].result!=e[n++].result)return!1}}(n,this.active)?s=Kc.build(n,e,this.id,s,i):s&&s.disabled&&!n.some((t=>1==t.state))&&(s=null),!s&&n.every((t=>1!=t.state))&&n.some((t=>t.hasResult()))&&(n=n.map((t=>t.hasResult()?new rO(t.source,0):t)));for(let e of t.effects)e.is(lO)&&(s=s&&s.setSelected(e.value,this.id));return n==this.active&&s==this.open?this:new tO(n,this.id,s)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:eO}}const eO={"aria-autocomplete":"list"};function iO(t,e){let i={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":t};return e>-1&&(i["aria-activedescendant"]=t+"-"+e),i}const nO=[];function sO(t){return t.isUserEvent("input.type")?"input":t.isUserEvent("delete.backward")?"delete":null}class rO{constructor(t,e,i=-1){this.source=t,this.state=e,this.explicitPos=i}hasResult(){return!1}update(t,e){let i=sO(t),n=this;i?n=n.handleUserEvent(t,i,e):t.docChanged?n=n.handleChange(t):t.selection&&0!=n.state&&(n=new rO(n.source,0));for(let e of t.effects)if(e.is(zc))n=new rO(n.source,1,e.value?qc(t.state):-1);else if(e.is(Gc))n=new rO(n.source,0);else if(e.is(aO))for(let t of e.value)t.source==n.source&&(n=t);return n}handleUserEvent(t,e,i){return"delete"!=e&&i.activateOnTyping?new rO(this.source,1):this.map(t.changes)}handleChange(t){return t.changes.touchesRange(qc(t.startState))?new rO(this.source,0):this.map(t.changes)}map(t){return t.empty||this.explicitPos<0?this:new rO(this.source,this.state,t.mapPos(this.explicitPos))}}class oO extends rO{constructor(t,e,i,n,s){super(t,2,e),this.result=i,this.from=n,this.to=s}hasResult(){return!0}handleUserEvent(t,e,i){var n;let s=t.changes.mapPos(this.from),r=t.changes.mapPos(this.to,1),o=qc(t.state);if((this.explicitPos<0?o<=s:o<this.from)||o>r||"delete"==e&&qc(t.startState)==this.from)return new rO(this.source,"input"==e&&i.activateOnTyping?1:0);let a,l=this.explicitPos<0?-1:t.changes.mapPos(this.explicitPos);return function(t,e,i,n){if(!t)return!1;let s=e.sliceDoc(i,n);return"function"==typeof t?t(s,i,n,e):_c(t,!0).test(s)}(this.result.validFor,t.state,s,r)?new oO(this.source,l,this.result,s,r):this.result.update&&(a=this.result.update(this.result,s,r,new Ac(t.state,o,l>=0)))?new oO(this.source,l,a,a.from,null!==(n=a.to)&&void 0!==n?n:qc(t.state)):new rO(this.source,1,l)}handleChange(t){return t.changes.touchesRange(this.from,this.to)?new rO(this.source,0):this.map(t.changes)}map(t){return t.empty?this:new oO(this.source,this.explicitPos<0?-1:t.mapPos(this.explicitPos),this.result,t.mapPos(this.from),t.mapPos(this.to,1))}}const aO=re.define({map:(t,e)=>t.map((t=>t.map(e)))}),lO=re.define(),hO=Vt.define({create:()=>tO.start(),update:(t,e)=>t.update(e),provide:t=>[Ao.from(t,(t=>t.tooltip)),Wr.contentAttributes.from(t,(t=>t.attrs))]});function cO(t,e){const i=e.completion.apply||e.completion.label;let n=t.state.field(hO).active.find((t=>t.source==e.source));return n instanceof oO&&("string"==typeof i?t.dispatch(Object.assign(Object.assign({},function(t,e,i,n){let{main:s}=t.selection,r=i-s.from,o=n-s.from;return Object.assign(Object.assign({},t.changeByRange((a=>a!=s&&i!=n&&t.sliceDoc(a.from+r,a.from+o)!=t.sliceDoc(i,n)?{range:a}:{changes:{from:a.from+r,to:n==s.from?a.to:a.from+o,insert:e},range:$t.cursor(a.from+r+e.length)}))),{scrollIntoView:!0,userEvent:"input.complete"})}(t.state,i,n.from,n.to)),{annotations:Ec.of(e.completion)})):i(t,e.completion,n.from,n.to),!0)}const OO=Hc(hO,cO);function uO(t,e="option"){return i=>{let n=i.state.field(hO,!1);if(!n||!n.open||n.open.disabled||Date.now()-n.open.timestamp<i.state.facet(Bc).interactionDelay)return!1;let s,r=1;"page"==e&&(s=Ro(i,n.open.tooltip))&&(r=Math.max(2,Math.floor(s.dom.offsetHeight/s.dom.querySelector("li").offsetHeight)-1));let{length:o}=n.open.options,a=n.open.selected>-1?n.open.selected+r*(t?1:-1):t?0:o-1;return a<0?a="page"==e?0:o-1:a>=o&&(a="page"==e?o-1:0),i.dispatch({effects:lO.of(a)}),!0}}const fO=t=>{let e=t.state.field(hO,!1);return!(t.state.readOnly||!e||!e.open||e.open.selected<0||e.open.disabled||Date.now()-e.open.timestamp<t.state.facet(Bc).interactionDelay)&&cO(t,e.open.options[e.open.selected])},dO=t=>!!t.state.field(hO,!1)&&(t.dispatch({effects:zc.of(!0)}),!0),pO=t=>{let e=t.state.field(hO,!1);return!(!e||!e.active.some((t=>0!=t.state)))&&(t.dispatch({effects:Gc.of(null)}),!0)};class mO{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const gO=qn.fromClass(class{constructor(t){this.view=t,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.composing=0;for(let e of t.state.field(hO).active)1==e.state&&this.startQuery(e)}update(t){let e=t.state.field(hO);if(!t.selectionSet&&!t.docChanged&&t.startState.field(hO)==e)return;let i=t.transactions.some((t=>(t.selection||t.docChanged)&&!sO(t)));for(let e=0;e<this.running.length;e++){let n=this.running[e];if(i||n.updates.length+t.transactions.length>50&&Date.now()-n.time>1e3){for(let t of n.context.abortListeners)try{t()}catch(t){Rn(this.view.state,t)}n.context.abortListeners=null,this.running.splice(e--,1)}else n.updates.push(...t.transactions)}if(this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),this.debounceUpdate=e.active.some((t=>1==t.state&&!this.running.some((e=>e.active.source==t.source))))?setTimeout((()=>this.startUpdate()),50):-1,0!=this.composing)for(let e of t.transactions)"input"==sO(e)?this.composing=2:2==this.composing&&e.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1;let{state:t}=this.view,e=t.field(hO);for(let t of e.active)1!=t.state||this.running.some((e=>e.active.source==t.source))||this.startQuery(t)}startQuery(t){let{state:e}=this.view,i=qc(e),n=new Ac(e,i,t.explicitPos==i),s=new mO(t,n);this.running.push(s),Promise.resolve(t.source(n)).then((t=>{s.context.aborted||(s.done=t||null,this.scheduleAccept())}),(t=>{this.view.dispatch({effects:Gc.of(null)}),Rn(this.view.state,t)}))}scheduleAccept(){this.running.every((t=>void 0!==t.done))?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Bc).updateSyncTime))}accept(){var t;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],i=this.view.state.facet(Bc);for(let n=0;n<this.running.length;n++){let s=this.running[n];if(void 0===s.done)continue;if(this.running.splice(n--,1),s.done){let n=new oO(s.active.source,s.active.explicitPos,s.done,s.done.from,null!==(t=s.done.to)&&void 0!==t?t:qc(s.updates.length?s.updates[0].startState:this.view.state));for(let t of s.updates)n=n.update(t,i);if(n.hasResult()){e.push(n);continue}}let r=this.view.state.field(hO).active.find((t=>t.source==s.active.source));if(r&&1==r.state)if(null==s.done){let t=new rO(s.active.source,0);for(let e of s.updates)t=t.update(e,i);1!=t.state&&e.push(t)}else this.startQuery(r)}e.length&&this.view.dispatch({effects:aO.of(e)})}},{eventHandlers:{blur(t){let e=this.view.state.field(hO,!1);if(e&&e.tooltip&&this.view.state.facet(Bc).closeOnBlur){let i=e.open&&Ro(this.view,e.open.tooltip);i&&i.dom.contains(t.relatedTarget)||this.view.dispatch({effects:Gc.of(null)})}},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout((()=>this.view.dispatch({effects:zc.of(!1)})),20),this.composing=0}}}),wO=Wr.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class bO{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class SO{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,mt.TrackDel),i=t.mapPos(this.to,1,mt.TrackDel);return null==e||null==i?null:new SO(this.field,e,i)}}class yO{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],s=t.doc.lineAt(e),r=/^\s*/.exec(s.text)[0];for(let s of this.lines){if(i.length){let i=r,o=/^\t*/.exec(s)[0].length;for(let e=0;e<o;e++)i+=t.facet(tl);n.push(e+i.length-o),s=i+s.slice(o)}i.push(s),e+=s.length+1}let o=this.fieldPositions.map((t=>new SO(t.field,n[t.line]+t.from,n[t.line]+t.to)));return{text:i,ranges:o}}static parse(t){let e,i=[],n=[],s=[];for(let r of t.split(/\r\n?|\n/)){for(;e=/[#$]\{(?:(\d+)(?::([^}]*))?|([^}]*))\}/.exec(r);){let t=e[1]?+e[1]:null,o=e[2]||e[3]||"",a=-1;for(let e=0;e<i.length;e++)(null!=t?i[e].seq==t:o&&i[e].name==o)&&(a=e);if(a<0){let e=0;for(;e<i.length&&(null==t||null!=i[e].seq&&i[e].seq<t);)e++;i.splice(e,0,{seq:t,name:o}),a=e;for(let t of s)t.field>=a&&t.field++}s.push(new bO(a,n.length,e.index,e.index+o.length)),r=r.slice(0,e.index)+o+r.slice(e.index+e[0].length)}for(let t;t=/\\([{}])/.exec(r);){r=r.slice(0,t.index)+t[1]+r.slice(t.index+t[0].length);for(let e of s)e.line==n.length&&e.from>t.index&&(e.from--,e.to--)}n.push(r)}return new yO(n,s)}}let QO=Ui.widget({widget:new class extends Bi{toDOM(){let t=document.createElement("span");return t.className="cm-snippetFieldPosition",t}ignoreEvent(){return!1}}}),vO=Ui.mark({class:"cm-snippetField"});class xO{constructor(t,e){this.ranges=t,this.active=e,this.deco=Ui.set(t.map((t=>(t.from==t.to?QO:vO).range(t.from,t.to))))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new xO(e,this.active)}selectionInsideField(t){return t.ranges.every((t=>this.ranges.some((e=>e.field==this.active&&e.from<=t.from&&e.to>=t.to))))}}const kO=re.define({map:(t,e)=>t&&t.map(e)}),$O=re.define(),PO=Vt.define({create:()=>null,update(t,e){for(let i of e.effects){if(i.is(kO))return i.value;if(i.is($O)&&t)return new xO(t.ranges,i.value)}return t&&e.docChanged&&(t=t.map(e.changes)),t&&e.selection&&!t.selectionInsideField(e.selection)&&(t=null),t},provide:t=>Wr.decorations.from(t,(t=>t?t.deco:Ui.none))});function ZO(t,e){return $t.create(t.filter((t=>t.field==e)).map((t=>$t.range(t.from,t.to))))}function TO(t){let e=yO.parse(t);return(t,i,n,s)=>{let{text:r,ranges:o}=e.instantiate(t.state,n),a={changes:{from:n,to:s,insert:N.of(r)},scrollIntoView:!0,annotations:i?Ec.of(i):void 0};if(o.length&&(a.selection=ZO(o,0)),o.length>1){let e=new xO(o,0),i=a.effects=[kO.of(e)];void 0===t.state.field(PO,!1)&&i.push(re.appendConfig.of([PO,AO,WO,wO]))}t.dispatch(t.state.update(a))}}function XO(t){return({state:e,dispatch:i})=>{let n=e.field(PO,!1);if(!n||t<0&&0==n.active)return!1;let s=n.active+t,r=t>0&&!n.ranges.some((e=>e.field==s+t));return i(e.update({selection:ZO(n.ranges,s),effects:kO.of(r?null:new xO(n.ranges,s)),scrollIntoView:!0})),!0}}const CO=[{key:"Tab",run:XO(1),shift:XO(-1)},{key:"Escape",run:({state:t,dispatch:e})=>!!t.field(PO,!1)&&(e(t.update({effects:kO.of(null)})),!0)}],YO=Tt.define({combine:t=>t.length?t[0]:CO}),AO=Dt.highest(zr.compute([YO],(t=>t.facet(YO))));function RO(t,e){return Object.assign(Object.assign({},e),{apply:TO(t)})}const WO=Wr.domEventHandlers({mousedown(t,e){let i,n=e.state.field(PO,!1);if(!n||null==(i=e.posAtCoords({x:t.clientX,y:t.clientY})))return!1;let s=n.ranges.find((t=>t.from<=i&&t.to>=i));return!(!s||s.field==n.active)&&(e.dispatch({selection:ZO(n.ranges,s.field),effects:kO.of(n.ranges.some((t=>t.field>s.field))?new xO(n.ranges,s.field):null),scrollIntoView:!0}),!0)}});function VO(t,e){return new RegExp(e(t.source),t.unicode?"u":"")}const jO=Object.create(null);function qO(t,e,i,n,s){for(let r=t.iterLines(),o=0;!r.next().done;){let t,{value:a}=r;for(e.lastIndex=0;t=e.exec(a);)if(!n[t[0]]&&o+t.index!=s&&(i.push({type:"text",label:t[0]}),n[t[0]]=!0,i.length>=2e3))return;o+=a.length+1}}function _O(t,e,i,n,s){let r=t.length>=1e3,o=r&&e.get(t);if(o)return o;let a=[],l=Object.create(null);if(t.children){let r=0;for(let o of t.children){if(o.length>=1e3)for(let t of _O(o,e,i,n-r,s-r))l[t.label]||(l[t.label]=!0,a.push(t));else qO(o,i,a,l,s-r);r+=o.length+1}}else qO(t,i,a,l,s);return r&&a.length<2e3&&e.set(t,a),a}const EO=t=>{let e=t.state.languageDataAt("wordChars",t.pos).join(""),i=function(t){let e=t.replace(/[\]\-\\]/g,"\\$&");try{return new RegExp(`[\\p{Alphabetic}\\p{Number}_${e}]+`,"ug")}catch(t){return new RegExp(`[w${e}]`,"g")}}(e),n=t.matchBefore(VO(i,(t=>t+"$")));if(!n&&!t.explicit)return null;let s=n?n.from:t.pos,r=_O(t.state.doc,function(t){return jO[t]||(jO[t]=new WeakMap)}(e),i,5e4,s);return{from:s,options:r,validFor:VO(i,(t=>"^"+t))}},MO={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},DO=re.define({map(t,e){let i=e.mapPos(t,-1,mt.TrackAfter);return null==i?void 0:i}}),zO=new class extends be{};zO.startSide=1,zO.endSide=-1;const GO=Vt.define({create:()=>ve.empty,update(t,e){if(e.selection){let i=e.state.doc.lineAt(e.selection.main.head).from,n=e.startState.doc.lineAt(e.startState.selection.main.head).from;i!=e.changes.mapPos(n,-1)&&(t=ve.empty)}t=t.map(e.changes);for(let i of e.effects)i.is(DO)&&(t=t.update({add:[zO.range(i.value,i.value+1)]}));return t}});function IO(){return[FO,GO]}const BO="()[]{}<>";function NO(t){for(let e=0;e<BO.length;e+=2)if(BO.charCodeAt(e)==t)return BO.charAt(e+1);return ft(t<128?t:t+1)}function UO(t,e){return t.languageDataAt("closeBrackets",e)[0]||MO}const LO="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),FO=Wr.inputHandler.of(((t,e,i,n)=>{if((LO?t.composing:t.compositionStarted)||t.state.readOnly)return!1;let s=t.state.selection.main;if(n.length>2||2==n.length&&1==dt(ut(n,0))||e!=s.from||i!=s.to)return!1;let r=function(t,e){let i=UO(t,t.selection.main.head),n=i.brackets||MO.brackets;for(let s of n){let r=NO(ut(s,0));if(e==s)return r==s?iu(t,s,n.indexOf(s+s+s)>-1,i):tu(t,s,r,i.before||MO.before);if(e==r&&JO(t,t.selection.main.from))return eu(t,s,r)}return null}(t.state,n);return!!r&&(t.dispatch(r),!0)})),HO=[{key:"Backspace",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=UO(t,t.selection.main.head).brackets||MO.brackets,n=null,s=t.changeByRange((e=>{if(e.empty){let n=function(t,e){let i=t.sliceString(e-2,e);return dt(ut(i,0))==i.length?i:i.slice(1)}(t.doc,e.head);for(let s of i)if(s==n&&KO(t.doc,e.head)==NO(ut(s,0)))return{changes:{from:e.head-s.length,to:e.head+s.length},range:$t.cursor(e.head-s.length)}}return{range:n=e}}));return n||e(t.update(s,{scrollIntoView:!0,userEvent:"delete.backward"})),!n}}];function JO(t,e){let i=!1;return t.field(GO).between(0,t.doc.length,(t=>{t==e&&(i=!0)})),i}function KO(t,e){let i=t.sliceString(e,e+2);return i.slice(0,dt(ut(i,0)))}function tu(t,e,i,n){let s=null,r=t.changeByRange((r=>{if(!r.empty)return{changes:[{insert:e,from:r.from},{insert:i,from:r.to}],effects:DO.of(r.to+e.length),range:$t.range(r.anchor+e.length,r.head+e.length)};let o=KO(t.doc,r.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:e+i,from:r.head},effects:DO.of(r.head+e.length),range:$t.cursor(r.head+e.length)}:{range:s=r}}));return s?null:t.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function eu(t,e,i){let n=null,s=t.changeByRange((e=>e.empty&&KO(t.doc,e.head)==i?{changes:{from:e.head,to:e.head+i.length,insert:i},range:$t.cursor(e.head+i.length)}:n={range:e}));return n?null:t.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function iu(t,e,i,n){let s=n.stringPrefixes||MO.stringPrefixes,r=null,o=t.changeByRange((n=>{if(!n.empty)return{changes:[{insert:e,from:n.from},{insert:e,from:n.to}],effects:DO.of(n.to+e.length),range:$t.range(n.anchor+e.length,n.head+e.length)};let o,a=n.head,l=KO(t.doc,a);if(l==e){if(nu(t,a))return{changes:{insert:e+e,from:a},effects:DO.of(a+e.length),range:$t.cursor(a+e.length)};if(JO(t,a)){let n=i&&t.sliceDoc(a,a+3*e.length)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+n.length,insert:n},range:$t.cursor(a+n.length)}}}else{if(i&&t.sliceDoc(a-2*e.length,a)==e+e&&(o=su(t,a-2*e.length,s))>-1&&nu(t,o))return{changes:{insert:e+e+e+e,from:a},effects:DO.of(a+e.length),range:$t.cursor(a+e.length)};if(t.charCategorizer(a)(l)!=fe.Word&&su(t,a,s)>-1&&!function(t,e,i,n){let s=Ea(t).resolveInner(e,-1),r=n.reduce(((t,e)=>Math.max(t,e.length)),0);for(let o=0;o<5;o++){let o=t.sliceDoc(s.from,Math.min(s.to,s.from+i.length+r)),a=o.indexOf(i);if(!a||a>-1&&n.indexOf(o.slice(0,a))>-1){let e=s.firstChild;for(;e&&e.from==s.from&&e.to-e.from>i.length+a;){if(t.sliceDoc(e.to-i.length,e.to)==i)return!1;e=e.firstChild}return!0}let l=s.to==e&&s.parent;if(!l)break;s=l}return!1}(t,a,e,s))return{changes:{insert:e+e,from:a},effects:DO.of(a+e.length),range:$t.cursor(a+e.length)}}return{range:r=n}}));return r?null:t.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function nu(t,e){let i=Ea(t).resolveInner(e+1);return i.parent&&i.from==e}function su(t,e,i){let n=t.charCategorizer(e);if(n(t.sliceDoc(e-1,e))!=fe.Word)return e;for(let s of i){let i=e-s.length;if(t.sliceDoc(i,e)==s&&n(t.sliceDoc(i-1,i))!=fe.Word)return i}return-1}function ru(t={}){return[hO,Bc.of(t),gO,au,wO]}const ou=[{key:"Ctrl-Space",run:dO},{key:"Escape",run:pO},{key:"ArrowDown",run:uO(!0)},{key:"ArrowUp",run:uO(!1)},{key:"PageDown",run:uO(!0,"page")},{key:"PageUp",run:uO(!1,"page")},{key:"Enter",run:fO}],au=Dt.highest(zr.computeN([Bc],(t=>t.facet(Bc).defaultKeymap?[ou]:[])));function lu(t){let e=t.field(hO,!1);return e&&e.active.some((t=>1==t.state))?"pending":e&&e.active.some((t=>0!=t.state))?"active":null}const hu=new WeakMap;function cu(t){var e;let i=null===(e=t.field(hO,!1))||void 0===e?void 0:e.open;if(!i||i.disabled)return[];let n=hu.get(i.options);return n||hu.set(i.options,n=i.options.map((t=>t.completion))),n}function Ou(t){var e;let i=null===(e=t.field(hO,!1))||void 0===e?void 0:e.open;return i&&!i.disabled&&i.selected>=0?i.options[i.selected].completion:null}function uu(t){var e;let i=null===(e=t.field(hO,!1))||void 0===e?void 0:e.open;return i&&!i.disabled&&i.selected>=0?i.selected:null}const fu=t=>{let{state:e}=t,i=e.doc.lineAt(e.selection.main.from),n=gu(t.state,i.from);return n.line?pu(t):!!n.block&&mu(t)};function du(t,e){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let s=t(e,i);return!!s&&(n(i.update(s)),!0)}}const pu=du(bu,0),mu=du(((t,e)=>function(t,e,i=e.selection.ranges){let n=i.map((t=>gu(e,t.from).block));if(!n.every((t=>t)))return null;let s=i.map(((t,i)=>function(t,{open:e,close:i},n,s){let r,o,a=t.sliceDoc(n-wu,n),l=t.sliceDoc(s,s+wu),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,O=a.length-h;if(a.slice(O-e.length,O)==e&&l.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:s+c,margin:c&&1}};s-n<=2*wu?r=o=t.sliceDoc(n,s):(r=t.sliceDoc(n,n+wu),o=t.sliceDoc(s-wu,s));let u=/^\s*/.exec(r)[0].length,f=/\s*$/.exec(o)[0].length,d=o.length-f-i.length;if(r.slice(u,u+e.length)==e&&o.slice(d,d+i.length)==i)return{open:{pos:n+u+e.length,margin:/\s/.test(r.charAt(u+e.length))?1:0},close:{pos:s-f-i.length,margin:/\s/.test(o.charAt(d-1))?1:0}};return null}(e,n[i],t.from,t.to)));if(2!=t&&!s.every((t=>t)))return{changes:e.changes(i.map(((t,e)=>s[e]?[]:[{from:t.from,insert:n[e].open+" "},{from:t.to,insert:" "+n[e].close}])))};if(1!=t&&s.some((t=>t))){let t=[];for(let e,i=0;i<s.length;i++)if(e=s[i]){let s=n[i],{open:r,close:o}=e;t.push({from:r.pos-s.open.length,to:r.pos+r.margin},{from:o.pos-o.margin,to:o.pos+s.close.length})}return{changes:t}}return null}(t,e,function(t){let e=[];for(let i of t.selection.ranges){let n=t.doc.lineAt(i.from),s=i.to<=n.to?n:t.doc.lineAt(i.to),r=e.length-1;r>=0&&e[r].to>n.from?e[r].to=s.to:e.push({from:n.from+/^\s*/.exec(n.text)[0].length,to:s.to})}return e}(e))),0);function gu(t,e){let i=t.languageDataAt("commentTokens",e);return i.length?i[0]:{}}const wu=50;function bu(t,e,i=e.selection.ranges){let n=[],s=-1;for(let{from:t,to:r}of i){let i=n.length,o=1e9,a=gu(e,t).line;if(a){for(let i=t;i<=r;){let l=e.doc.lineAt(i);if(l.from>s&&(t==r||r>l.from)){s=l.from;let t=/^\s*/.exec(l.text)[0].length,e=t==l.length,i=l.text.slice(t,t+a.length)==a?t:-1;t<l.text.length&&t<o&&(o=t),n.push({line:l,comment:i,token:a,indent:t,empty:e,single:!1})}i=l.to+1}if(o<1e9)for(let t=i;t<n.length;t++)n[t].indent<n[t].line.text.length&&(n[t].indent=o);n.length==i+1&&(n[i].single=!0)}}if(2!=t&&n.some((t=>t.comment<0&&(!t.empty||t.single)))){let t=[];for(let{line:e,token:i,indent:s,empty:r,single:o}of n)!o&&r||t.push({from:e.from+s,insert:i+" "});let i=e.changes(t);return{changes:i,selection:e.selection.map(i,1)}}if(1!=t&&n.some((t=>t.comment>=0))){let t=[];for(let{line:e,comment:i,token:s}of n)if(i>=0){let n=e.from+i,r=n+s.length;" "==e.text[r-e.from]&&r++,t.push({from:n,to:r})}return{changes:t}}return null}const Su=ie.define(),yu=ie.define(),Qu=Tt.define(),vu=Tt.define({combine:t=>we(t,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})});const xu=Vt.define({create:()=>Mu.empty,update(t,e){let i=e.state.facet(vu),n=e.annotation(Su);if(n){let s=e.docChanged?$t.single(function(t){let e=0;return t.iterChangedRanges(((t,i)=>e=i)),e}(e.changes)):void 0,r=Cu.fromTransaction(e,s),o=n.side,a=0==o?t.undone:t.done;return a=r?Yu(a,a.length,i.minDepth,r):Vu(a,e.startState.selection),new Mu(0==o?n.rest:a,0==o?a:n.rest)}let s=e.annotation(yu);if("full"!=s&&"before"!=s||(t=t.isolate()),!1===e.annotation(oe.addToHistory))return e.changes.empty?t:t.addMapping(e.changes.desc);let r=Cu.fromTransaction(e),o=e.annotation(oe.time),a=e.annotation(oe.userEvent);return r?t=t.addChanges(r,o,a,i,e):e.selection&&(t=t.addSelection(e.startState.selection,o,a,i.newGroupDelay)),"full"!=s&&"after"!=s||(t=t.isolate()),t},toJSON:t=>({done:t.done.map((t=>t.toJSON())),undone:t.undone.map((t=>t.toJSON()))}),fromJSON:t=>new Mu(t.done.map(Cu.fromJSON),t.undone.map(Cu.fromJSON))});function ku(t={}){return[xu,vu.of(t),Wr.domEventHandlers({beforeinput(t,e){let i="historyUndo"==t.inputType?Pu:"historyRedo"==t.inputType?Zu:null;return!!i&&(t.preventDefault(),i(e))}})]}function $u(t,e){return function({state:i,dispatch:n}){if(!e&&i.readOnly)return!1;let s=i.field(xu,!1);if(!s)return!1;let r=s.pop(t,i,e);return!!r&&(n(r),!0)}}const Pu=$u(0,!1),Zu=$u(1,!1),Tu=$u(0,!0),Xu=$u(1,!0);class Cu{constructor(t,e,i,n,s){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=s}setSelAfter(t){return new Cu(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:null===(t=this.changes)||void 0===t?void 0:t.toJSON(),mapped:null===(e=this.mapped)||void 0===e?void 0:e.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map((t=>t.toJSON()))}}static fromJSON(t){return new Cu(t.changes&&wt.fromJSON(t.changes),[],t.mapped&&gt.fromJSON(t.mapped),t.startSelection&&$t.fromJSON(t.startSelection),t.selectionsAfter.map($t.fromJSON))}static fromTransaction(t,e){let i=Ru;for(let e of t.startState.facet(Qu)){let n=e(t);n.length&&(i=i.concat(n))}return!i.length&&t.changes.empty?null:new Cu(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Ru)}static selection(t){return new Cu(void 0,Ru,void 0,void 0,t)}}function Yu(t,e,i,n){let s=e+1>i+20?e-i-1:0,r=t.slice(s,e);return r.push(n),r}function Au(t,e){return t.length?e.length?t.concat(e):t:e}const Ru=[],Wu=200;function Vu(t,e){if(t.length){let i=t[t.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-Wu));return n.length&&n[n.length-1].eq(e)?t:(n.push(e),Yu(t,t.length-1,1e9,i.setSelAfter(n)))}return[Cu.selection([e])]}function ju(t){let e=t[t.length-1],i=t.slice();return i[t.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),i}function qu(t,e){if(!t.length)return t;let i=t.length,n=Ru;for(;i;){let s=_u(t[i-1],e,n);if(s.changes&&!s.changes.empty||s.effects.length){let e=t.slice(0,i);return e[i-1]=s,e}e=s.mapped,i--,n=s.selectionsAfter}return n.length?[Cu.selection(n)]:Ru}function _u(t,e,i){let n=Au(t.selectionsAfter.length?t.selectionsAfter.map((t=>t.map(e))):Ru,i);if(!t.changes)return Cu.selection(n);let s=t.changes.map(e),r=e.mapDesc(t.changes,!0),o=t.mapped?t.mapped.composeDesc(r):r;return new Cu(s,re.mapEffects(t.effects,e),o,t.startSelection.map(r),n)}const Eu=/^(input\.type|delete)($|\.)/;class Mu{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Mu(this.done,this.undone):this}addChanges(t,e,i,n,s){let r=this.done,o=r[r.length-1];return r=o&&o.changes&&!o.changes.empty&&t.changes&&(!i||Eu.test(i))&&(!o.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(s,function(t,e){let i=[],n=!1;return t.iterChangedRanges(((t,e)=>i.push(t,e))),e.iterChangedRanges(((t,e,s,r)=>{for(let t=0;t<i.length;){let e=i[t++],o=i[t++];r>=e&&s<=o&&(n=!0)}})),n}(o.changes,t.changes))||"input.type.compose"==i)?Yu(r,r.length-1,n.minDepth,new Cu(t.changes.compose(o.changes),Au(t.effects,o.effects),o.mapped,o.startSelection,Ru)):Yu(r,r.length,n.minDepth,t),new Mu(r,Ru,e,i)}addSelection(t,e,i,n){let s=this.done.length?this.done[this.done.length-1].selectionsAfter:Ru;return s.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(r=s[s.length-1],o=t,r.ranges.length==o.ranges.length&&0===r.ranges.filter(((t,e)=>t.empty!=o.ranges[e].empty)).length)?this:new Mu(Vu(this.done,t),this.undone,e,i);var r,o}addMapping(t){return new Mu(qu(this.done,t),qu(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=0==t?this.done:this.undone;if(0==n.length)return null;let s=n[n.length-1];if(i&&s.selectionsAfter.length)return e.update({selection:s.selectionsAfter[s.selectionsAfter.length-1],annotations:Su.of({side:t,rest:ju(n)}),userEvent:0==t?"select.undo":"select.redo",scrollIntoView:!0});if(s.changes){let i=1==n.length?Ru:n.slice(0,n.length-1);return s.mapped&&(i=qu(i,s.mapped)),e.update({changes:s.changes,selection:s.startSelection,effects:s.effects,annotations:Su.of({side:t,rest:i}),filter:!1,userEvent:0==t?"undo":"redo",scrollIntoView:!0})}return null}}Mu.empty=new Mu(Ru,Ru);const Du=[{key:"Mod-z",run:Pu,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Zu,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Zu,preventDefault:!0},{key:"Mod-u",run:Tu,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Xu,preventDefault:!0}];function zu(t,e){return $t.create(t.ranges.map(e),t.mainIndex)}function Gu(t,e){return t.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Iu({state:t,dispatch:e},i){let n=zu(t.selection,i);return!n.eq(t.selection)&&(e(Gu(t,n)),!0)}function Bu(t,e){return $t.cursor(e?t.to:t.from)}function Nu(t,e){return Iu(t,(i=>i.empty?t.moveByChar(i,e):Bu(i,e)))}function Uu(t){return t.textDirectionAt(t.state.selection.main.head)==sn.LTR}const Lu=t=>Nu(t,!Uu(t)),Fu=t=>Nu(t,Uu(t));function Hu(t,e){return Iu(t,(i=>i.empty?t.moveByGroup(i,e):Bu(i,e)))}const Ju=t=>Hu(t,!Uu(t)),Ku=t=>Hu(t,Uu(t));function tf(t,e,i){if(e.type.prop(i))return!0;let n=e.to-e.from;return n&&(n>2||/[^\s,.;:]/.test(t.sliceDoc(e.from,e.to)))||e.firstChild}function ef(t,e,i){let s,r,o=Ea(t).resolveInner(e.head),a=i?n.closedBy:n.openedBy;for(let n=e.head;;){let e=i?o.childAfter(n):o.childBefore(n);if(!e)break;tf(t,e,a)?o=e:n=i?e.to:e.from}return r=o.type.prop(a)&&(s=i?ah(t,o.from,1):ah(t,o.to,-1))&&s.matched?i?s.end.to:s.end.from:i?o.to:o.from,$t.cursor(r,i?-1:1)}const nf=t=>Iu(t,(e=>ef(t.state,e,!Uu(t)))),sf=t=>Iu(t,(e=>ef(t.state,e,Uu(t))));function rf(t,e){return Iu(t,(i=>{if(!i.empty)return Bu(i,e);let n=t.moveVertically(i,e);return n.head!=i.head?n:t.moveToLineBoundary(i,e)}))}const of=t=>rf(t,!1),af=t=>rf(t,!0);function lf(t){let e,i=t.scrollDOM.clientHeight<t.scrollDOM.scrollHeight-2,n=0,s=0;if(i){for(let e of t.state.facet(Wr.scrollMargins)){let i=e(t);(null==i?void 0:i.top)&&(n=Math.max(null==i?void 0:i.top,n)),(null==i?void 0:i.bottom)&&(s=Math.max(null==i?void 0:i.bottom,s))}e=t.scrollDOM.clientHeight-n-s}else e=(t.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:n,marginBottom:s,selfScroll:i,height:Math.max(t.defaultLineHeight,e-5)}}function hf(t,e){let i,n=lf(t),{state:s}=t,r=zu(s.selection,(i=>i.empty?t.moveVertically(i,e,n.height):Bu(i,e)));if(r.eq(s.selection))return!1;if(n.selfScroll){let e=t.coordsAtPos(s.selection.main.head),o=t.scrollDOM.getBoundingClientRect(),a=o.top+n.marginTop,l=o.bottom-n.marginBottom;e&&e.top>a&&e.bottom<l&&(i=Wr.scrollIntoView(r.main.head,{y:"start",yMargin:e.top-a}))}return t.dispatch(Gu(s,r),{effects:i}),!0}const cf=t=>hf(t,!1),Of=t=>hf(t,!0);function uf(t,e,i){let n=t.lineBlockAt(e.head),s=t.moveToLineBoundary(e,i);if(s.head==e.head&&s.head!=(i?n.to:n.from)&&(s=t.moveToLineBoundary(e,i,!1)),!i&&s.head==n.from&&n.length){let i=/^\s*/.exec(t.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&e.head!=n.from+i&&(s=$t.cursor(n.from+i))}return s}function ff(t,e,i){let n=!1,s=zu(t.selection,(e=>{let s=ah(t,e.head,-1)||ah(t,e.head,1)||e.head>0&&ah(t,e.head-1,1)||e.head<t.doc.length&&ah(t,e.head+1,-1);if(!s||!s.end)return e;n=!0;let r=s.start.from==e.head?s.end.to:s.end.from;return i?$t.range(e.anchor,r):$t.cursor(r)}));return!!n&&(e(Gu(t,s)),!0)}const df=({state:t,dispatch:e})=>ff(t,e,!1),pf=({state:t,dispatch:e})=>ff(t,e,!0);function mf(t,e){let i=zu(t.state.selection,(t=>{let i=e(t);return $t.range(t.anchor,i.head,i.goalColumn,i.bidiLevel||void 0)}));return!i.eq(t.state.selection)&&(t.dispatch(Gu(t.state,i)),!0)}function gf(t,e){return mf(t,(i=>t.moveByChar(i,e)))}const wf=t=>gf(t,!Uu(t)),bf=t=>gf(t,Uu(t));function Sf(t,e){return mf(t,(i=>t.moveByGroup(i,e)))}const yf=t=>Sf(t,!Uu(t)),Qf=t=>Sf(t,Uu(t)),vf=t=>mf(t,(e=>ef(t.state,e,!Uu(t)))),xf=t=>mf(t,(e=>ef(t.state,e,Uu(t))));function kf(t,e){return mf(t,(i=>t.moveVertically(i,e)))}const $f=t=>kf(t,!1),Pf=t=>kf(t,!0);function Zf(t,e){return mf(t,(i=>t.moveVertically(i,e,lf(t).height)))}const Tf=t=>Zf(t,!1),Xf=t=>Zf(t,!0),Cf=({state:t,dispatch:e})=>(e(Gu(t,{anchor:0})),!0),Yf=({state:t,dispatch:e})=>(e(Gu(t,{anchor:t.doc.length})),!0),Af=({state:t,dispatch:e})=>(e(Gu(t,{anchor:t.selection.main.anchor,head:0})),!0),Rf=({state:t,dispatch:e})=>(e(Gu(t,{anchor:t.selection.main.anchor,head:t.doc.length})),!0);function Wf(t,e){if(t.state.readOnly)return!1;let i="delete.selection",{state:n}=t,s=n.changeByRange((n=>{let{from:s,to:r}=n;if(s==r){let o=e(n);o<s?(i="delete.backward",o=Vf(t,o,!1)):o>s&&(i="delete.forward",o=Vf(t,o,!0)),s=Math.min(s,o),r=Math.max(r,o)}else s=Vf(t,s,!1),r=Vf(t,r,!0);return s==r?{range:n}:{changes:{from:s,to:r},range:$t.cursor(s,s<n.head?-1:1)}}));return!s.changes.empty&&(t.dispatch(n.update(s,{scrollIntoView:!0,userEvent:i,effects:"delete.selection"==i?Wr.announce.of(n.phrase("Selection deleted")):void 0})),!0)}function Vf(t,e,i){if(t instanceof Wr)for(let n of t.state.facet(Wr.atomicRanges).map((e=>e(t))))n.between(e,e,((t,n)=>{t<e&&n>e&&(e=i?n:t)}));return e}const jf=(t,e)=>Wf(t,(i=>{let n,s,r=i.from,{state:o}=t,a=o.doc.lineAt(r);if(!e&&r>a.from&&r<a.from+200&&!/[^ \t]/.test(n=a.text.slice(0,r-a.from))){if("\t"==n[n.length-1])return r-1;let t=We(n,o.tabSize)%el(o)||el(o);for(let e=0;e<t&&" "==n[n.length-1-e];e++)r--;s=r}else s=at(a.text,r-a.from,e,e)+a.from,s==r&&a.number!=(e?o.doc.lines:1)&&(s+=e?1:-1);return s})),qf=t=>jf(t,!1),_f=t=>jf(t,!0),Ef=(t,e)=>Wf(t,(i=>{let n=i.head,{state:s}=t,r=s.doc.lineAt(n),o=s.charCategorizer(n);for(let t=null;;){if(n==(e?r.to:r.from)){n==i.head&&r.number!=(e?s.doc.lines:1)&&(n+=e?1:-1);break}let a=at(r.text,n-r.from,e)+r.from,l=r.text.slice(Math.min(n,a)-r.from,Math.max(n,a)-r.from),h=o(l);if(null!=t&&h!=t)break;" "==l&&n==i.head||(t=h),n=a}return n})),Mf=t=>Ef(t,!1);const Df=zf(!1);function zf(t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let s=e.changeByRange((i=>{let{from:s,to:r}=i,o=e.doc.lineAt(s),a=!t&&s==r&&function(t,e){if(/\(\)|\[\]|\{\}/.test(t.sliceDoc(e-1,e+1)))return{from:e,to:e};let i,s=Ea(t).resolveInner(e),r=s.childBefore(e),o=s.childAfter(e);return r&&o&&r.to<=e&&o.from>=e&&(i=r.type.prop(n.closedBy))&&i.indexOf(o.name)>-1&&t.doc.lineAt(r.to).from==t.doc.lineAt(o.from).from&&!/\S/.test(t.sliceDoc(r.to,o.from))?{from:r.to,to:o.from}:null}(e,s);t&&(s=r=(r<=o.to?o:e.doc.lineAt(r)).to);let l=new sl(e,{simulateBreak:s,simulateDoubleBreak:!!a}),h=nl(l,s);for(null==h&&(h=We(/^\s*/.exec(e.doc.lineAt(s).text)[0],e.tabSize));r<o.to&&/\s/.test(o.text[r-o.from]);)r++;a?({from:s,to:r}=a):s>o.from&&s<o.from+100&&!/\S/.test(o.text.slice(0,s))&&(s=o.from);let c=["",il(e,h)];return a&&c.push(il(e,l.lineIndent(o.from,-1))),{changes:{from:s,to:r,insert:N.of(c)},range:$t.cursor(s+1+c[1].length)}}));return i(e.update(s,{scrollIntoView:!0,userEvent:"input"})),!0}}function Gf(t,e){let i=-1;return t.changeByRange((n=>{let s=[];for(let r=n.from;r<=n.to;){let o=t.doc.lineAt(r);o.number>i&&(n.empty||n.to>o.from)&&(e(o,s,n),i=o.number),r=o.to+1}let r=t.changes(s);return{changes:s,range:$t.range(r.mapPos(n.anchor,1),r.mapPos(n.head,1))}}))}const If=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Gf(t,((e,i)=>{i.push({from:e.from,insert:t.facet(tl)})})),{userEvent:"input.indent"})),!0),Bf=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Gf(t,((e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let s=We(n,t.tabSize),r=0,o=il(t,Math.max(0,s-el(t)));for(;r<n.length&&r<o.length&&n.charCodeAt(r)==o.charCodeAt(r);)r++;i.push({from:e.from+r,to:e.from+n.length,insert:o.slice(r)})})),{userEvent:"delete.dedent"})),!0),Nf=[{key:"ArrowLeft",run:Lu,shift:wf,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:Ju,shift:yf,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:t=>Iu(t,(e=>uf(t,e,!Uu(t)))),shift:t=>mf(t,(e=>uf(t,e,!Uu(t)))),preventDefault:!0},{key:"ArrowRight",run:Fu,shift:bf,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Ku,shift:Qf,preventDefault:!0},{mac:"Cmd-ArrowRight",run:t=>Iu(t,(e=>uf(t,e,Uu(t)))),shift:t=>mf(t,(e=>uf(t,e,Uu(t)))),preventDefault:!0},{key:"ArrowUp",run:of,shift:$f,preventDefault:!0},{mac:"Cmd-ArrowUp",run:Cf,shift:Af},{mac:"Ctrl-ArrowUp",run:cf,shift:Tf},{key:"ArrowDown",run:af,shift:Pf,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Yf,shift:Rf},{mac:"Ctrl-ArrowDown",run:Of,shift:Xf},{key:"PageUp",run:cf,shift:Tf},{key:"PageDown",run:Of,shift:Xf},{key:"Home",run:t=>Iu(t,(e=>uf(t,e,!1))),shift:t=>mf(t,(e=>uf(t,e,!1))),preventDefault:!0},{key:"Mod-Home",run:Cf,shift:Af},{key:"End",run:t=>Iu(t,(e=>uf(t,e,!0))),shift:t=>mf(t,(e=>uf(t,e,!0))),preventDefault:!0},{key:"Mod-End",run:Yf,shift:Rf},{key:"Enter",run:Df},{key:"Mod-a",run:({state:t,dispatch:e})=>(e(t.update({selection:{anchor:0,head:t.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:qf,shift:qf},{key:"Delete",run:_f},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Mf},{key:"Mod-Delete",mac:"Alt-Delete",run:t=>Ef(t,!0)},{mac:"Mod-Backspace",run:t=>Wf(t,(e=>{let i=t.moveToLineBoundary(e,!1).head;return e.head>i?i:Math.max(0,e.head-1)}))},{mac:"Mod-Delete",run:t=>Wf(t,(e=>{let i=t.moveToLineBoundary(e,!0).head;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))}].concat([{key:"Ctrl-b",run:Lu,shift:wf,preventDefault:!0},{key:"Ctrl-f",run:Fu,shift:bf},{key:"Ctrl-p",run:of,shift:$f},{key:"Ctrl-n",run:af,shift:Pf},{key:"Ctrl-a",run:t=>Iu(t,(e=>$t.cursor(t.lineBlockAt(e.head).from,1))),shift:t=>mf(t,(e=>$t.cursor(t.lineBlockAt(e.head).from)))},{key:"Ctrl-e",run:t=>Iu(t,(e=>$t.cursor(t.lineBlockAt(e.head).to,-1))),shift:t=>mf(t,(e=>$t.cursor(t.lineBlockAt(e.head).to)))},{key:"Ctrl-d",run:_f},{key:"Ctrl-h",run:qf},{key:"Ctrl-k",run:t=>Wf(t,(e=>{let i=t.lineBlockAt(e.head).to;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))},{key:"Ctrl-Alt-h",run:Mf},{key:"Ctrl-o",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((t=>({changes:{from:t.from,to:t.to,insert:N.of(["",""])},range:$t.cursor(t.from)})));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((e=>{if(!e.empty||0==e.from||e.from==t.doc.length)return{range:e};let i=e.from,n=t.doc.lineAt(i),s=i==n.from?i-1:at(n.text,i-n.from,!1)+n.from,r=i==n.to?i+1:at(n.text,i-n.from,!0)+n.from;return{changes:{from:s,to:r,insert:t.doc.slice(i,r).append(t.doc.slice(s,i))},range:$t.cursor(r)}}));return!i.changes.empty&&(e(t.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:Of}].map((t=>({mac:t.key,run:t.run,shift:t.shift})))),Uf=20,Lf=22,Ff=23,Hf=24,Jf=26,Kf=27,td=28,ed=31,id=34,nd=37,sd={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},rd={dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},od={dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}};function ad(t){return 9==t||10==t||13==t||32==t}let ld=null,hd=null,cd=0;function Od(t,e){let i=t.pos+e;if(cd==i&&hd==t)return ld;let n=t.peek(e);for(;ad(n);)n=t.peek(++e);let s="";for(;45==(r=n)||46==r||58==r||r>=65&&r<=90||95==r||r>=97&&r<=122||r>=161;)s+=String.fromCharCode(n),n=t.peek(++e);var r;return hd=t,cd=i,ld=s?s.toLowerCase():n==ud||n==fd?void 0:null}const ud=63,fd=33;function dd(t,e){this.name=t,this.parent=e,this.hash=e?e.hash:0;for(let e=0;e<t.length;e++)this.hash+=(this.hash<<4)+t.charCodeAt(e)+(t.charCodeAt(e)<<8)}const pd=[6,10,7,8,9],md=new sc({start:null,shift:(t,e,i,n)=>pd.indexOf(e)>-1?new dd(Od(n,1)||"",t):t,reduce:(t,e)=>e==Uf&&t?t.parent:t,reuse(t,e,i,n){let s=e.type.id;return 6==s||36==s?new dd(Od(n,1)||"",t):t},hash:t=>t?t.hash:0,strict:!1}),gd=new Ih(((t,e)=>{if(60!=t.next)return void(t.next<0&&e.context&&t.acceptToken(57));t.advance();let i=47==t.next;i&&t.advance();let n=Od(t,0);if(void 0===n)return;if(!n)return t.acceptToken(i?14:6);let s=e.context?e.context.name:null;if(i){if(n==s)return t.acceptToken(11);if(s&&rd[s])return t.acceptToken(57,-2);if(e.dialectEnabled(0))return t.acceptToken(12);for(let t=e.context;t;t=t.parent)if(t.name==n)return;t.acceptToken(13)}else{if("script"==n)return t.acceptToken(7);if("style"==n)return t.acceptToken(8);if("textarea"==n)return t.acceptToken(9);if(sd.hasOwnProperty(n))return t.acceptToken(10);s&&od[s]&&od[s][n]?t.acceptToken(57,-1):t.acceptToken(6)}}),{contextual:!0}),wd=new Ih((t=>{for(let e=0,i=0;;i++){if(t.next<0){i&&t.acceptToken(58);break}if(45==t.next)e++;else{if(62==t.next&&e>=2){i>=3&&t.acceptToken(58,-2);break}e=0}t.advance()}}));const bd=new Ih(((t,e)=>{if(47==t.next&&62==t.peek(1)){let i=e.dialectEnabled(1)||function(t){for(;t;t=t.parent)if("svg"==t.name||"math"==t.name)return!0;return!1}(e.context);t.acceptToken(i?5:4,2)}else 62==t.next&&t.acceptToken(4,1)}));function Sd(t,e,i){let n=2+t.length;return new Ih((s=>{for(let r=0,o=0,a=0;;a++){if(s.next<0){a&&s.acceptToken(e);break}if(0==r&&60==s.next||1==r&&47==s.next||r>=2&&r<n&&s.next==t.charCodeAt(r-2))r++,o++;else if(2!=r&&r!=n||!ad(s.next)){if(r==n&&62==s.next){a>o?s.acceptToken(e,-o):s.acceptToken(i,-(o-2));break}if((10==s.next||13==s.next)&&a){s.acceptToken(e,1);break}r=o=0}else o++;s.advance()}}))}const yd=Sd("script",54,1),Qd=Sd("style",55,2),vd=Sd("textarea",56,3),xd=Oa({"Text RawText":Ya.content,"StartTag StartCloseTag SelfClosingEndTag EndTag":Ya.angleBracket,TagName:Ya.tagName,"MismatchedCloseTag/TagName":[Ya.tagName,Ya.invalid],AttributeName:Ya.attributeName,"AttributeValue UnquotedAttributeValue":Ya.attributeValue,Is:Ya.definitionOperator,"EntityReference CharacterReference":Ya.character,Comment:Ya.blockComment,ProcessingInst:Ya.processingInstruction,DoctypeDecl:Ya.documentMeta}),kd=rc.deserialize({version:14,states:",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%WQ&rO,59fO%`Q&rO,59iO%hQ&rO,59lO%sQ&rO,59nOOOa'#D^'#D^O%{OaO'#CxO&WOaO,59[OOOb'#D_'#D_O&`ObO'#C{O&kObO,59[OOOd'#D`'#D`O&sOdO'#DOO'OOdO,59[OOO`'#Da'#DaO'WO!rO,59[O'_Q#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'dO$fO,59oOOO`,59o,59oO'lQ#|O,59qO'qQ#|O,59rOOO`-E7W-E7WO'vQ&rO'#CsOOQW'#DZ'#DZO(UQ&rO1G.wOOOa1G.w1G.wO(^Q&rO1G/QOOOb1G/Q1G/QO(fQ&rO1G/TOOOd1G/T1G/TO(nQ&rO1G/WOOO`1G/W1G/WOOO`1G/Y1G/YO(yQ&rO1G/YOOOa-E7[-E7[O)RQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)WQ#tO'#C|OOOd-E7^-E7^O)]Q#tO'#DPOOO`-E7_-E7_O)bQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O)gQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rOOO`7+$t7+$tO)rQ#|O,59eO)wQ#|O,59hO)|Q#|O,59kOOO`1G/X1G/XO*RO7[O'#CvO*dOMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O*uO7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+WOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z",stateData:"+s~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OhyO~OS!OOhyO~OS!QOhyO~OS!SOT!TOhyO~OS!TOhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXhgXTgX~OS!fOhyO~OS!gOhyO~OS!hOhyO~OS!iOT!jOhyO~OS!jOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~",goto:"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{}!P!R!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ}bQ!PcQ!RdQ!UeZ!e{}!P!R!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",nodeNames:"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",maxTerm:67,context:md,nodeProps:[["closedBy",-10,1,2,3,7,8,9,10,11,12,13,"EndTag",6,"EndTag SelfClosingEndTag",-4,21,30,33,36,"CloseTag"],["openedBy",4,"StartTag StartCloseTag",5,"StartTag",-4,29,32,35,37,"OpenTag"],["group",-9,14,17,18,19,20,39,40,41,42,"Entity",16,"Entity TextContent",-3,28,31,34,"TextContent Entity"],["isolate",-11,21,29,30,32,33,35,36,37,38,41,42,"ltr",-3,26,27,39,""]],propSources:[xd],skippedNodes:[0],repeatNodeCount:9,tokenData:"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X",tokenizers:[yd,Qd,vd,bd,gd,wd,0,1,2,3,4,5],topRules:{Document:[0,15]},dialects:{noMatch:0,selfClosing:485},tokenPrec:487});function $d(t,e){let i=Object.create(null);for(let n of t.getChildren(Ff)){let t=n.getChild(Hf),s=n.getChild(Jf)||n.getChild(Kf);t&&(i[e.read(t.from,t.to)]=s?s.type.id==Jf?e.read(s.from+1,s.to-1):e.read(s.from,s.to):"")}return i}function Pd(t,e){let i=t.getChild(Lf);return i?e.read(i.from,i.to):" "}function Zd(t,e,i){let n;for(let s of i)if(!s.attrs||s.attrs(n||(n=$d(t.node.parent.firstChild,e))))return{parser:s.parser};return null}function Td(t=[],e=[]){let i=[],n=[],s=[],r=[];for(let e of t){("script"==e.tag?i:"style"==e.tag?n:"textarea"==e.tag?s:r).push(e)}let o=e.length?Object.create(null):null;for(let t of e)(o[t.name]||(o[t.name]=[])).push(t);return A(((t,e)=>{let a=t.type.id;if(a==td)return Zd(t,e,i);if(a==ed)return Zd(t,e,n);if(a==id)return Zd(t,e,s);if(a==Uf&&r.length){let i,n=t.node,s=n.firstChild,o=s&&Pd(s,e);if(o)for(let t of r)if(t.tag==o&&(!t.attrs||t.attrs(i||(i=$d(n,e))))){let e=n.lastChild,i=e.type.id==nd?e.from:n.to;if(i>s.to)return{parser:t.parser,overlay:[{from:s.to,to:i}]}}}if(o&&a==Ff){let i,n=t.node;if(i=n.firstChild){let t=o[e.read(i.from,i.to)];if(t)for(let i of t){if(i.tagName&&i.tagName!=Pd(n.parent,e))continue;let t=n.lastChild;if(t.type.id==Jf){let e=t.from+1,n=t.lastChild,s=t.to-(n&&n.isError?0:1);if(s>e)return{parser:i.parser,overlay:[{from:e,to:s}]}}else if(t.type.id==Kf)return{parser:i.parser,overlay:[{from:t.from,to:t.to}]}}}}return null}))}const Xd=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],Cd=new sc({start:!1,shift:(t,e)=>4==e||5==e||312==e?t:313==e,strict:!1}),Yd=new Ih(((t,e)=>{let{next:i}=t;(125==i||-1==i||e.context)&&t.acceptToken(310)}),{contextual:!0,fallback:!0}),Ad=new Ih(((t,e)=>{let i,{next:n}=t;Xd.indexOf(n)>-1||(47!=n||47!=(i=t.peek(1))&&42!=i)&&(125==n||59==n||-1==n||e.context||t.acceptToken(309))}),{contextual:!0}),Rd=new Ih(((t,e)=>{let{next:i}=t;if((43==i||45==i)&&(t.advance(),i==t.next)){t.advance();let i=!e.context&&e.canShift(1);t.acceptToken(i?1:2)}}),{contextual:!0});function Wd(t,e){return t>=65&&t<=90||t>=97&&t<=122||95==t||t>=192||!e&&t>=48&&t<=57}const Vd=new Ih(((t,e)=>{if(60!=t.next||!e.dialectEnabled(0))return;if(t.advance(),47==t.next)return;let i=0;for(;Xd.indexOf(t.next)>-1;)t.advance(),i++;if(Wd(t.next,!0)){for(t.advance(),i++;Wd(t.next,!1);)t.advance(),i++;for(;Xd.indexOf(t.next)>-1;)t.advance(),i++;if(44==t.next)return;for(let e=0;;e++){if(7==e){if(!Wd(t.next,!0))return;break}if(t.next!="extends".charCodeAt(e))break;t.advance(),i++}}t.acceptToken(3,-i)})),jd=Oa({"get set async static":Ya.modifier,"for while do if else switch try catch finally return throw break continue default case":Ya.controlKeyword,"in of await yield void typeof delete instanceof":Ya.operatorKeyword,"let var const using function class extends":Ya.definitionKeyword,"import export from":Ya.moduleKeyword,"with debugger as new":Ya.keyword,TemplateString:Ya.special(Ya.string),super:Ya.atom,BooleanLiteral:Ya.bool,this:Ya.self,null:Ya.null,Star:Ya.modifier,VariableName:Ya.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":Ya.function(Ya.variableName),VariableDefinition:Ya.definition(Ya.variableName),Label:Ya.labelName,PropertyName:Ya.propertyName,PrivatePropertyName:Ya.special(Ya.propertyName),"CallExpression/MemberExpression/PropertyName":Ya.function(Ya.propertyName),"FunctionDeclaration/VariableDefinition":Ya.function(Ya.definition(Ya.variableName)),"ClassDeclaration/VariableDefinition":Ya.definition(Ya.className),PropertyDefinition:Ya.definition(Ya.propertyName),PrivatePropertyDefinition:Ya.definition(Ya.special(Ya.propertyName)),UpdateOp:Ya.updateOperator,"LineComment Hashbang":Ya.lineComment,BlockComment:Ya.blockComment,Number:Ya.number,String:Ya.string,Escape:Ya.escape,ArithOp:Ya.arithmeticOperator,LogicOp:Ya.logicOperator,BitOp:Ya.bitwiseOperator,CompareOp:Ya.compareOperator,RegExp:Ya.regexp,Equals:Ya.definitionOperator,Arrow:Ya.function(Ya.punctuation),": Spread":Ya.punctuation,"( )":Ya.paren,"[ ]":Ya.squareBracket,"{ }":Ya.brace,"InterpolationStart InterpolationEnd":Ya.special(Ya.brace),".":Ya.derefOperator,", ;":Ya.separator,"@":Ya.meta,TypeName:Ya.typeName,TypeDefinition:Ya.definition(Ya.typeName),"type enum interface implements namespace module declare":Ya.definitionKeyword,"abstract global Privacy readonly override":Ya.modifier,"is keyof unique infer":Ya.operatorKeyword,JSXAttributeValue:Ya.attributeValue,JSXText:Ya.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":Ya.angleBracket,"JSXIdentifier JSXNameSpacedName":Ya.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":Ya.attributeName,"JSXBuiltin/JSXIdentifier":Ya.standard(Ya.tagName)}),qd={__proto__:null,export:18,as:23,from:31,default:34,async:39,function:40,extends:52,this:56,true:64,false:64,null:76,void:80,typeof:84,super:102,new:136,delete:152,yield:161,await:165,class:170,public:227,private:227,protected:227,readonly:229,instanceof:248,satisfies:251,in:252,const:254,import:286,keyof:339,unique:343,infer:349,is:385,abstract:405,implements:407,type:409,let:412,var:414,using:417,interface:423,enum:427,namespace:433,module:435,declare:439,global:443,for:462,of:471,while:474,with:478,do:482,if:486,else:488,switch:492,case:498,try:504,catch:508,finally:512,return:516,throw:520,break:524,continue:528,debugger:532},_d={__proto__:null,async:123,get:125,set:127,declare:187,public:189,private:189,protected:189,static:191,abstract:193,override:195,readonly:201,accessor:203,new:389},Ed={__proto__:null,"<":143},Md=rc.deserialize({version:14,states:"$<UO%TQ^OOO%[Q^OOO'_Q`OOP(lOWOOO*zQ08SO'#ChO+RO!bO'#CiO+aO#tO'#CiO+oO?MpO'#D^O.QQ^O'#DdO.bQ^O'#DoO%[Q^O'#DyO0fQ^O'#EROOQ07b'#EZ'#EZO1PQWO'#EWOOQO'#El'#ElOOQO'#Ie'#IeO1XQWO'#GmO1dQWO'#EkO1iQWO'#EkO3kQ08SO'#JiO6[Q08SO'#JjO6xQWO'#FZO6}Q&jO'#FqOOQ07b'#Fc'#FcO7YO,YO'#FcO7hQ7[O'#FxO9UQWO'#FwOOQ07b'#Jj'#JjOOQ07`'#Ji'#JiO9ZQWO'#GqOOQU'#KU'#KUO9fQWO'#IRO9kQ07hO'#ISOOQU'#JW'#JWOOQU'#IW'#IWQ`Q^OOO`Q^OOO%[Q^O'#DqO9sQ^O'#D}O9zQ^O'#EPO9aQWO'#GmO:RQ7[O'#CnO:aQWO'#EjO:lQWO'#EuO:qQ7[O'#FbO;`QWO'#GmOOQO'#KV'#KVO;eQWO'#KVO;sQWO'#GuO;sQWO'#GvO;sQWO'#GxO9aQWO'#G{O<jQWO'#HOO>RQWO'#CdO>cQWO'#H[O>kQWO'#HbO>kQWO'#HdO`Q^O'#HfO>kQWO'#HhO>kQWO'#HkO>pQWO'#HqO>uQ07iO'#HwO%[Q^O'#HyO?QQ07iO'#H{O?]Q07iO'#H}O9kQ07hO'#IPO?hQ08SO'#ChO@jQ`O'#DiQOQWOOO%[Q^O'#EPOAQQWO'#ESO:RQ7[O'#EjOA]QWO'#EjOAhQpO'#FbOOQU'#Cf'#CfOOQ07`'#Dn'#DnOOQ07`'#Jm'#JmO%[Q^O'#JmOOQO'#Jq'#JqOOQO'#Ib'#IbOBhQ`O'#EcOOQ07`'#Eb'#EbOCdQ07pO'#EcOCnQ`O'#EVOOQO'#Jp'#JpODSQ`O'#JqOEaQ`O'#EVOCnQ`O'#EcPEnO!0LbO'#CaPOOO)CDu)CDuOOOO'#IX'#IXOEyO!bO,59TOOQ07b,59T,59TOOOO'#IY'#IYOFXO#tO,59TO%[Q^O'#D`OOOO'#I['#I[OFgO?MpO,59xOOQ07b,59x,59xOFuQ^O'#I]OGYQWO'#JkOI[QrO'#JkO+}Q^O'#JkOIcQWO,5:OOIyQWO'#ElOJWQWO'#JyOJcQWO'#JxOJcQWO'#JxOJkQWO,5;YOJpQWO'#JwOOQ07f,5:Z,5:ZOJwQ^O,5:ZOLxQ08SO,5:eOMiQWO,5:mONSQ07hO'#JvONZQWO'#JuO9ZQWO'#JuONoQWO'#JuONwQWO,5;XON|QWO'#JuO!#UQrO'#JjOOQ07b'#Ch'#ChO%[Q^O'#ERO!#tQpO,5:rOOQO'#Jr'#JrOOQO-E<c-E<cO9aQWO,5=XO!$[QWO,5=XO!$aQ^O,5;VO!&dQ7[O'#EgO!'}QWO,5;VO!)mQ7[O'#DsO!)tQ^O'#DxO!*OQ`O,5;`O!*WQ`O,5;`O%[Q^O,5;`OOQU'#FR'#FROOQU'#FT'#FTO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aOOQU'#FX'#FXO!*fQ^O,5;rOOQ07b,5;w,5;wOOQ07b,5;x,5;xO!,iQWO,5;xOOQ07b,5;y,5;yO%[Q^O'#IiO!,qQ07hO,5<eO!&dQ7[O,5;aO!-`Q7[O,5;aO%[Q^O,5;uO!-gQ&jO'#FgO!.dQ&jO'#J}O!.OQ&jO'#J}O!.kQ&jO'#J}OOQO'#J}'#J}O!/PQ&jO,5<POOOS,5<],5<]O!/bQ^O'#FsOOOS'#Ih'#IhO7YO,YO,5;}O!/iQ&jO'#FuOOQ07b,5;},5;}O!0YQMhO'#CuOOQ07b'#Cy'#CyO!0mQWO'#CyO!0rO?MpO'#C}O!1`Q7[O,5<bO!1gQWO,5<dO!3SQ!LQO'#GSO!3aQWO'#GTO!3fQWO'#GTO!3kQ!LQO'#GXO!4jQ`O'#G]OOQO'#Gh'#GhO!(SQ7[O'#GgOOQO'#Gj'#GjO!(SQ7[O'#GiO!5]QMhO'#JdOOQ07b'#Jd'#JdO!5gQWO'#JcO!5uQWO'#JbO!5}QWO'#CtOOQ07b'#Cw'#CwOOQ07b'#DR'#DROOQ07b'#DT'#DTO1SQWO'#DVO!(SQ7[O'#FzO!(SQ7[O'#F|O!6VQWO'#GOO!6[QWO'#GPO!3fQWO'#GVO!(SQ7[O'#G[O!6aQWO'#EmO!7OQWO,5<cOOQ07`'#Cq'#CqO!7WQWO'#EnO!8QQ`O'#EoOOQ07`'#Jw'#JwO!8XQ07hO'#KWO9kQ07hO,5=]O`Q^O,5>mOOQU'#J`'#J`OOQU,5>n,5>nOOQU-E<U-E<UO!:ZQ08SO,5:]O!<wQ08SO,5:iO%[Q^O,5:iO!?bQ08SO,5:kOOQO,5@q,5@qO!@RQ7[O,5=XO!@aQ07hO'#JaO9UQWO'#JaO!@rQ07hO,59YO!@}Q`O,59YO!AVQ7[O,59YO:RQ7[O,59YO!AbQWO,5;VO!AjQWO'#HZO!BOQWO'#KZO%[Q^O,5;zO!7{Q`O,5;|O!BWQWO,5=tO!B]QWO,5=tO!BbQWO,5=tO9kQ07hO,5=tO;sQWO,5=dOOQO'#Cu'#CuO!BpQ`O,5=aO!BxQ7[O,5=bO!CTQWO,5=dO!CYQpO,5=gO!CbQWO'#KVO>pQWO'#HQO9aQWO'#HSO!CgQWO'#HSO:RQ7[O'#HUO!ClQWO'#HUOOQU,5=j,5=jO!CqQWO'#HVO!DSQWO'#CnO!DXQWO,59OO!DcQWO,59OO!FhQ^O,59OOOQU,59O,59OO!FxQ07hO,59OO%[Q^O,59OO!ITQ^O'#H^OOQU'#H_'#H_OOQU'#H`'#H`O`Q^O,5=vO!IkQWO,5=vO`Q^O,5=|O`Q^O,5>OO!IpQWO,5>QO`Q^O,5>SO!IuQWO,5>VO!IzQ^O,5>]OOQU,5>c,5>cO%[Q^O,5>cO9kQ07hO,5>eOOQU,5>g,5>gO!NUQWO,5>gOOQU,5>i,5>iO!NUQWO,5>iOOQU,5>k,5>kO!NZQ`O'#D[O%[Q^O'#JmO!NxQ`O'#JmO# gQ`O'#DjO# xQ`O'#DjO#$ZQ^O'#DjO#$bQWO'#JlO#$jQWO,5:TO#$oQWO'#EpO#$}QWO'#JzO#%VQWO,5;ZO#%[Q`O'#DjO#%iQ`O'#EUOOQ07b,5:n,5:nO%[Q^O,5:nO#%pQWO,5:nO>pQWO,5;UO!@}Q`O,5;UO!AVQ7[O,5;UO:RQ7[O,5;UO#%xQWO,5@XO#%}Q$ISO,5:rOOQO-E<`-E<`O#'TQ07pO,5:}OCnQ`O,5:qO#'_Q`O,5:qOCnQ`O,5:}O!@rQ07hO,5:qOOQ07`'#Ef'#EfOOQO,5:},5:}O%[Q^O,5:}O#'lQ07hO,5:}O#'wQ07hO,5:}O!@}Q`O,5:qOOQO,5;T,5;TO#(VQ07hO,5:}POOO'#IV'#IVP#(kO!0LbO,58{POOO,58{,58{OOOO-E<V-E<VOOQ07b1G.o1G.oOOOO-E<W-E<WO#(vQpO,59zOOOO-E<Y-E<YOOQ07b1G/d1G/dO#({QrO,5>wO+}Q^O,5>wOOQO,5>},5>}O#)VQ^O'#I]OOQO-E<Z-E<ZO#)dQWO,5@VO#)lQrO,5@VO#)sQWO,5@dOOQ07b1G/j1G/jO%[Q^O,5@eO#){QWO'#IcOOQO-E<a-E<aO#)sQWO,5@dOOQ07`1G0t1G0tOOQ07f1G/u1G/uOOQ07f1G0X1G0XO%[Q^O,5@bO#*aQ07hO,5@bO#*rQ07hO,5@bO#*yQWO,5@aO9ZQWO,5@aO#+RQWO,5@aO#+aQWO'#IfO#*yQWO,5@aOOQ07`1G0s1G0sO!*OQ`O,5:tO!*ZQ`O,5:tOOQO,5:v,5:vO#,RQWO,5:vO#,ZQ7[O1G2sO9aQWO1G2sOOQ07b1G0q1G0qO#,iQ08SO1G0qO#-nQ08QO,5;ROOQ07b'#GR'#GRO#.[Q08SO'#JdO!$aQ^O1G0qO#0dQ7[O'#JnO#0nQWO,5:_O#0sQrO'#JoO%[Q^O'#JoO#0}QWO,5:dOOQ07b'#D['#D[OOQ07b1G0z1G0zO%[Q^O1G0zOOQ07b1G1d1G1dO#1SQWO1G0zO#3kQ08SO1G0{O#3rQ08SO1G0{O#6]Q08SO1G0{O#6dQ08SO1G0{O#8nQ08SO1G0{O#9UQ08SO1G0{O#<OQ08SO1G0{O#<VQ08SO1G0{O#>pQ08SO1G0{O#>wQ08SO1G0{O#@oQ08SO1G0{O#CoQ(CYO'#ChO#EmQ(CYO1G1^O#EtQ(CYO'#JjO!,lQWO1G1dO#FUQ08SO,5?TOOQ07`-E<g-E<gO#FxQ08SO1G0{OOQ07b1G0{1G0{O#ITQ08SO1G1aO#IwQ&jO,5<TO#JPQ&jO,5<UO#JXQ&jO'#FlO#JpQWO'#FkOOQO'#KO'#KOOOQO'#Ig'#IgO#JuQ&jO1G1kOOQ07b1G1k1G1kOOOS1G1v1G1vO#KWQ(CYO'#JiO#KbQWO,5<_O!*fQ^O,5<_OOOS-E<f-E<fOOQ07b1G1i1G1iO#KgQ`O'#J}OOQ07b,5<a,5<aO#KoQ`O,5<aOOQ07b,59e,59eO!&dQ7[O'#DPOOOO'#IZ'#IZO#KtO?MpO,59iOOQ07b,59i,59iO%[Q^O1G1|O!6[QWO'#IkO#LPQ7[O,5<uOOQ07b,5<r,5<rO!(SQ7[O'#InO#LoQ7[O,5=RO!(SQ7[O'#IpO#MbQ7[O,5=TO!&dQ7[O,5=VOOQO1G2O1G2OO#MlQpO'#CqO#NPQpO,5<nO#NWQWO'#KRO9aQWO'#KRO#NfQWO,5<pO!(SQ7[O,5<oO#NkQWO'#GUO#NvQWO,5<oO#N{QpO'#GRO$ YQpO'#KSO$ dQWO'#KSO!&dQ7[O'#KSO$ iQWO,5<sO$ nQ`O'#G^O!4eQ`O'#G^O$!PQWO'#G`O$!UQWO'#GbO!3fQWO'#GeO$!ZQ07hO'#ImO$!fQ`O,5<wOOQ07f,5<w,5<wO$!mQ`O'#G^O$!{Q`O'#G_O$#TQ`O'#G_O$#YQ7[O,5=RO$#jQ7[O,5=TOOQ07b,5=W,5=WO!(SQ7[O,5?}O!(SQ7[O,5?}O$#zQWO'#IrO$$VQWO,5?|O$$_QWO,59`O$%OQ7[O,59qOOQ07b,59q,59qO$%qQ7[O,5<fO$&dQ7[O,5<hO@bQWO,5<jOOQ07b,5<k,5<kO$&nQWO,5<qO$&sQ7[O,5<vO$'TQWO'#JuO!$aQ^O1G1}O$'YQWO1G1}O9ZQWO'#JxO9ZQWO'#EpO%[Q^O'#EpO9ZQWO'#ItO$'_Q07hO,5@rOOQU1G2w1G2wOOQU1G4X1G4XOOQ07b1G/w1G/wO!,iQWO1G/wO$)dQ08SO1G0TOOQU1G2s1G2sO!&dQ7[O1G2sO%[Q^O1G2sO#,^QWO1G2sO$+hQ7[O'#EgOOQ07`,5?{,5?{O$+rQ07hO,5?{OOQU1G.t1G.tO!@rQ07hO1G.tO!@}Q`O1G.tO!AVQ7[O1G.tO$,TQWO1G0qO$,YQWO'#ChO$,eQWO'#K[O$,mQWO,5=uO$,rQWO'#K[O$,wQWO'#K[O$-VQWO'#IzO$-eQWO,5@uO$-mQrO1G1fOOQ07b1G1h1G1hO9aQWO1G3`O@bQWO1G3`O$-tQWO1G3`O$-yQWO1G3`OOQU1G3`1G3`O!CTQWO1G3OO!&dQ7[O1G2{O$.OQWO1G2{OOQU1G2|1G2|O!&dQ7[O1G2|O$.TQWO1G2|O$.]Q`O'#GzOOQU1G3O1G3OO!4eQ`O'#IvO!CYQpO1G3ROOQU1G3R1G3ROOQU,5=l,5=lO$.eQ7[O,5=nO9aQWO,5=nO$!UQWO,5=pO9UQWO,5=pO!@}Q`O,5=pO!AVQ7[O,5=pO:RQ7[O,5=pO$.sQWO'#KYO$/OQWO,5=qOOQU1G.j1G.jO$/<EMAIL>$/`QWO1G.jO9kQ07hO1G.jO$1eQrO,5@wO$1uQWO,5@wO9ZQWO,5@wO$2QQ^O,5=xO$2XQWO,5=xOOQU1G3b1G3bO`Q^O1G3bOOQU1G3h1G3hOOQU1G3j1G3jO>kQWO1G3lO$2^Q^O1G3nO$6bQ^O'#HmOOQU1G3q1G3qO$6oQWO'#HsO>pQWO'#HuOOQU1G3w1G3wO$6wQ^O1G3wO9kQ07hO1G3}OOQU1G4P1G4POOQ07`'#GY'#GYO9kQ07hO1G4RO9kQ07hO1G4TO$;OQWO,5@XO!*fQ^O,5;[O9ZQWO,5;[O>pQWO,5:UO!*fQ^O,5:UO!@}Q`O,5:UO$;TQ(CYO,5:UOOQO,5;[,5;[O$;_Q`O'#I^O$;uQWO,5@WOOQ07b1G/o1G/oO$;}Q`O'#IdO$<XQWO,5@fOOQ07`1G0u1G0uO# xQ`O,5:UOOQO'#Ia'#IaO$<aQ`O,5:pOOQ07f,5:p,5:pO#%sQWO1G0YOOQ07b1G0Y1G0YO%[Q^O1G0YOOQ07b1G0p1G0pO>pQWO1G0pO!@}Q`O1G0pO!AVQ7[O1G0pOOQ07`1G5s1G5sO!@rQ07hO1G0]OOQO1G0i1G0iO%[Q^O1G0iO$<hQ07hO1G0iO$<sQ07hO1G0iO!@}Q`O1G0]OCnQ`O1G0]O$=RQ07hO1G0iOOQO1G0]1G0]O$=gQ08SO1G0iPOOO-E<T-E<TPOOO1G.g1G.gOOOO1G/f1G/fO$=qQpO,5<eO$=yQrO1G4cOOQO1G4i1G4iO%[Q^O,5>wO$>TQWO1G5qO$>]QWO1G6OO$>eQrO1G6PO9ZQWO,5>}O$>oQ08SO1G5|O%[Q^O1G5|O$?PQ07hO1G5|O$?bQWO1G5{O$?bQWO1G5{O9ZQWO1G5{O$?jQWO,5?QO9ZQWO,5?QOOQO,5?Q,5?QO$@OQWO,5?QO$'TQWO,5?QOOQO-E<d-E<dOOQO1G0`1G0`OOQO1G0b1G0bO!,lQWO1G0bOOQU7+(_7+(_O!&dQ7[O7+(_O%[Q^O7+(_O$@^QWO7+(_O$@iQ7[O7+(_O$@wQ08SO,5=RO$CSQ08SO,5=TO$E_Q08SO,5=RO$GpQ08SO,5=TO$JRQ08SO,59qO$LZQ08SO,5<fO$NfQ08SO,5<hO%!qQ08SO,5<vOOQ07b7+&]7+&]O%%SQ08SO7+&]O%%vQ7[O'#I_O%&QQWO,5@YOOQ07b1G/y1G/yO%&YQ^O'#I`O%&gQWO,5@ZO%&oQrO,5@ZOOQ07b1G0O1G0OO%&yQWO7+&fOOQ07b7+&f7+&fO%'OQ(CYO,5:eO%[Q^O7+&xO%'YQ(CYO,5:]O%'gQ(CYO,5:iO%'qQ(CYO,5:kOOQ07b7+'O7+'OOOQO1G1o1G1oOOQO1G1p1G1pO%'{QtO,5<WO!*fQ^O,5<VOOQO-E<e-E<eOOQ07b7+'V7+'VOOOS7+'b7+'bOOOS1G1y1G1yO%(WQWO1G1yOOQ07b1G1{1G1{O%(]QpO,59kOOOO-E<X-E<XOOQ07b1G/T1G/TO%(dQ08SO7+'hOOQ07b,5?V,5?VO%)WQpO,5?VOOQ07b1G2a1G2aP!&dQ7[O'#IkPOQ07b-E<i-E<iO%)vQ7[O,5?YOOQ07b-E<l-E<lO%*iQ7[O,5?[OOQ07b-E<n-E<nO%*sQpO1G2qOOQ07b1G2Y1G2YO%*zQWO'#IjO%+YQWO,5@mO%+YQWO,5@mO%+bQWO,5@mO%+mQWO,5@mOOQO1G2[1G2[O%+{Q7[O1G2ZO!(SQ7[O1G2ZO%,]Q!LQO'#IlO%,mQWO,5@nO!&dQ7[O,5@nO%,uQpO,5@nOOQ07b1G2_1G2_OOQ07`,5<x,5<xOOQ07`,5<y,5<yO$'TQWO,5<yOC_QWO,5<yO!@}Q`O,5<xOOQO'#Ga'#GaO%-PQWO,5<zOOQ07`,5<|,5<|O$'TQWO,5=POOQO,5?X,5?XOOQO-E<k-E<kOOQ07f1G2c1G2cO!4eQ`O,5<xO%-XQWO,5<yO$!PQWO,5<zO!4eQ`O,5<yO!(SQ7[O'#InO%-{Q7[O1G2mO!(SQ7[O'#IpO%.nQ7[O1G2oO%.xQ7[O1G5iO%/SQ7[O1G5iOOQO,5?^,5?^OOQO-E<p-E<pOOQO1G.z1G.zO!7{Q`O,59sO%[Q^O,59sO%/aQWO1G2UO!(SQ7[O1G2]O%/fQ08SO7+'iOOQ07b7+'i7+'iO!$aQ^O7+'iO%0YQWO,5;[OOQ07`,5?`,5?`OOQ07`-E<r-E<rOOQ07b7+%c7+%cO%0_QpO'#KTO#%sQWO7+(_O%0iQrO7+(_O$@aQWO7+(_O%0pQ08QO'#ChO%1TQ08QO,5<}O%1uQWO,5<}OOQ07`1G5g1G5gOOQU7+$`7+$`O!@rQ07hO7+$`O!@}Q`O7+$`O!$aQ^O7+&]O%1zQWO'#IyO%2cQWO,5@vOOQO1G3a1G3aO9aQWO,5@vO%2cQWO,5@vO%2kQWO,5@vOOQO,5?f,5?fOOQO-E<x-E<xOOQ07b7+'Q7+'QO%2pQWO7+(zO9kQ07hO7+(zO9aQWO7+(zO@bQWO7+(zOOQU7+(j7+(jO%2uQ08QO7+(gO!&dQ7[O7+(gO%3PQpO7+(hOOQU7+(h7+(hO!&dQ7[O7+(hO%3WQWO'#KXO%3cQWO,5=fOOQO,5?b,5?bOOQO-E<t-E<tOOQU7+(m7+(mO%4rQ`O'#HTOOQU1G3Y1G3YO!&dQ7[O1G3YO%[Q^O1G3YO%4yQWO1G3YO%5UQ7[O1G3YO9kQ07hO1G3[O$!UQWO1G3[O9UQWO1G3[O!@}Q`O1G3[O!AVQ7[O1G3[O%5dQWO'#IxO%5xQWO,5@tO%6QQ`O,5@tOOQ07`1G3]1G3]OOQU7+$U7+$UO@bQWO7+$UO9kQ07hO7+$UO%6]QWO7+$UO%[Q^O1G6cO%[Q^O1G6dO%6bQ07hO1G6cO%6lQ^O1G3dO%6sQWO1G3dO%6xQ^O1G3dOOQU7+(|7+(|O9kQ07hO7+)WO`Q^O7+)YOOQU'#K_'#K_OOQU'#I{'#I{O%7PQ^O,5>XOOQU,5>X,5>XO%[Q^O'#HnO%7^QWO'#HpOOQU,5>_,5>_O9ZQWO,5>_OOQU,5>a,5>aOOQU7+)c7+)cOOQU7+)i7+)iOOQU7+)m7+)mOOQU7+)o7+)oO%7cQ`O1G5sO%7wQ(CYO1G0vO%8RQWO1G0vOOQO1G/p1G/pO%8^Q(CYO1G/pO>pQWO1G/pO!*fQ^O'#DjOOQO,5>x,5>xOOQO-E<[-E<[OOQO,5?O,5?OOOQO-E<b-E<bO!@}Q`O1G/pOOQO-E<_-E<_OOQ07f1G0[1G0[OOQ07b7+%t7+%tO#%sQWO7+%tOOQ07b7+&[7+&[O>pQWO7+&[O!@}Q`O7+&[OOQO7+%w7+%wO$=gQ08SO7+&TOOQO7+&T7+&TO%[Q^O7+&TO%8hQ07hO7+&TO!@rQ07hO7+%wO!@}Q`O7+%wO%8sQ07hO7+&TO%9RQ08SO7++hO%[Q^O7++hO%9cQWO7++gO%9cQWO7++gOOQO1G4l1G4lO9ZQWO1G4lO%9kQWO1G4lOOQO7+%|7+%|O#%sQWO<<KyO%0iQrO<<KyO%9yQWO<<KyOOQU<<Ky<<KyO!&dQ7[O<<KyO%[Q^O<<KyO%:RQWO<<KyO%:^Q08SO,5?YO%<iQ08SO,5?[O%>tQ08SO1G2ZO%AVQ08SO1G2mO%CbQ08SO1G2oO%EmQ7[O,5>yOOQO-E<]-E<]O%EwQrO,5>zO%[Q^O,5>zOOQO-E<^-E<^O%FRQWO1G5uOOQ07b<<JQ<<JQO%FZQ(CYO1G0qO%HeQ(CYO1G0{O%HlQ(CYO1G0{O%JpQ(CYO1G0{O%JwQ(CYO1G0{O%LlQ(CYO1G0{O%MSQ(CYO1G0{O& gQ(CYO1G0{O& nQ(CYO1G0{O&#rQ(CYO1G0{O&#yQ(CYO1G0{O&%qQ(CYO1G0{O&&UQ08SO<<JdO&'ZQ(CYO1G0{O&)PQ(CYO'#JdO&+SQ(CYO1G1aO&+aQ(CYO1G0TO!*fQ^O'#FnOOQO'#KP'#KPOOQO1G1r1G1rO&+kQWO1G1qO&+pQ(CYO,5?TOOOS7+'e7+'eOOOO1G/V1G/VOOQ07b1G4q1G4qO!(SQ7[O7+(]O&+zQWO,5?UO9aQWO,5?UOOQO-E<h-E<hO&,YQWO1G6XO&,YQWO1G6XO&,bQWO1G6XO&,mQ7[O7+'uO&,}QpO,5?WO&-XQWO,5?WO!&dQ7[O,5?WOOQO-E<j-E<jO&-^QpO1G6YO&-hQWO1G6YOOQ07`1G2e1G2eO$'TQWO1G2eOOQ07`1G2d1G2dO&-pQWO1G2fO!&dQ7[O1G2fOOQ07`1G2k1G2kO!@}Q`O1G2dOC_QWO1G2eO&-uQWO1G2fO&-}QWO1G2eO&.qQ7[O,5?YOOQ07b-E<m-E<mO&/dQ7[O,5?[OOQ07b-E<o-E<oO!(SQ7[O7++TOOQ07b1G/_1G/_O&/nQWO1G/_OOQ07b7+'p7+'pO&/sQ7[O7+'wO&0TQ08SO<<KTOOQ07b<<KT<<KTO&0wQWO1G0vO!&dQ7[O'#IsO&0|QWO,5@oO!&dQ7[O1G2iOOQU<<Gz<<GzO!@rQ07hO<<GzO&1UQ08SO<<IwOOQ07b<<Iw<<IwOOQO,5?e,5?eO&1xQWO,5?eO&1}QWO,5?eOOQO-E<w-E<wO&2]QWO1G6bO&2]QWO1G6bO9aQWO1G6bO@bQWO<<LfOOQU<<Lf<<LfO&2eQWO<<LfO9kQ07hO<<LfOOQU<<LR<<LRO%2uQ08QO<<LROOQU<<LS<<LSO%3PQpO<<LSO&2jQ`O'#IuO&2uQWO,5@sO!*fQ^O,5@sOOQU1G3Q1G3QO&2}Q^O'#JmOOQO'#Iw'#IwO9kQ07hO'#IwO&3XQ`O,5=oOOQU,5=o,5=oO&3`Q`O'#EcO&3tQWO7+(tO&3yQWO7+(tOOQU7+(t7+(tO!&dQ7[O7+(tO%[Q^O7+(tO&4RQWO7+(tOOQU7+(v7+(vO9kQ07hO7+(vO$!UQWO7+(vO9UQWO7+(vO!@}Q`O7+(vO&4^QWO,5?dOOQO-E<v-E<vOOQO'#HW'#HWO&4iQWO1G6`O9kQ07hO<<GpOOQU<<Gp<<GpO@bQWO<<GpO&4qQWO7++}O&4vQWO7+,OO%[Q^O7++}O%[Q^O7+,OOOQU7+)O7+)OO&4{QWO7+)OO&5QQ^O7+)OO&5XQWO7+)OOOQU<<Lr<<LrOOQU<<Lt<<LtOOQU-E<y-E<yOOQU1G3s1G3sO&5^QWO,5>YOOQU,5>[,5>[O&5cQWO1G3yO9ZQWO7+&bO!*fQ^O7+&bOOQO7+%[7+%[O&5hQ(CYO1G6PO>pQWO7+%[OOQ07b<<I`<<I`OOQ07b<<Iv<<IvO>pQWO<<IvOOQO<<Io<<IoO$=gQ08SO<<IoO%[Q^O<<IoOOQO<<Ic<<IcO!@rQ07hO<<IcO&5rQ07hO<<IoO&5}Q08SO<= SO&6_QWO<= ROOQO7+*W7+*WO9ZQWO7+*WOOQUANAeANAeO&6gQWOANAeO!&dQ7[OANAeO#%sQWOANAeO%0iQrOANAeO%[Q^OANAeO&6oQ08SO7+'uO&9QQ08SO,5?YO&;]Q08SO,5?[O&=hQ08SO7+'wO&?yQrO1G4fO&@TQ(CYO7+&]O&BXQ(CYO,5=RO&D`Q(CYO,5=TO&DpQ(CYO,5=RO&EQQ(CYO,5=TO&EbQ(CYO,59qO&GeQ(CYO,5<fO&IhQ(CYO,5<hO&KkQ(CYO,5<vO&MaQ(CYO7+'hO&MnQ(CYO7+'iO&M{QWO,5<YOOQO7+']7+']O&NQQ7[O<<KwOOQO1G4p1G4pO&NXQWO1G4pO&NdQWO1G4pO&NrQWO7++sO&NrQWO7++sO!&dQ7[O1G4rO&NzQpO1G4rO' UQWO7++tOOQ07`7+(P7+(PO$'TQWO7+(QO' ^QpO7+(QOOQ07`7+(O7+(OO$'TQWO7+(PO' eQWO7+(QO!&dQ7[O7+(QOC_QWO7+(PO' jQ7[O<<NoOOQ07b7+$y7+$yO' tQpO,5?_OOQO-E<q-E<qO'!OQ08QO7+(TOOQUAN=fAN=fO9aQWO1G5POOQO1G5P1G5PO'!`QWO1G5PO'!eQWO7++|O'!eQWO7++|O9kQ07hOANBQO@bQWOANBQOOQUANBQANBQOOQUANAmANAmOOQUANAnANAnO'!mQWO,5?aOOQO-E<s-E<sO'!xQ(CYO1G6_O'%YQrO'#ChOOQO,5?c,5?cOOQO-E<u-E<uOOQU1G3Z1G3ZO&2}Q^O,5<zOOQU<<L`<<L`O!&dQ7[O<<L`O&3tQWO<<L`O'%dQWO<<L`O%[Q^O<<L`OOQU<<Lb<<LbO9kQ07hO<<LbO$!UQWO<<LbO9UQWO<<LbO'%lQ`O1G5OO'%wQWO7++zOOQUAN=[AN=[O9kQ07hOAN=[OOQU<= i<= iOOQU<= j<= jO'&PQWO<= iO'&UQWO<= jOOQU<<Lj<<LjO'&ZQWO<<LjO'&`Q^O<<LjOOQU1G3t1G3tO>pQWO7+)eO'&gQWO<<I|O'&rQ(CYO<<I|OOQO<<Hv<<HvOOQ07bAN?bAN?bOOQOAN?ZAN?ZO$=gQ08SOAN?ZOOQOAN>}AN>}O%[Q^OAN?ZOOQO<<Mr<<MrOOQUG27PG27PO!&dQ7[OG27PO#%sQWOG27PO'&|QWOG27PO%0iQrOG27PO''UQ(CYO<<JdO''cQ(CYO1G2ZO')XQ(CYO,5?YO'+[Q(CYO,5?[O'-_Q(CYO1G2mO'/bQ(CYO1G2oO'1eQ(CYO<<KTO'1rQ(CYO<<IwOOQO1G1t1G1tO!(SQ7[OANAcOOQO7+*[7+*[O'2PQWO7+*[O'2[QWO<= _O'2dQpO7+*^OOQ07`<<Kl<<KlO$'TQWO<<KlOOQ07`<<Kk<<KkO'2nQpO<<KlO$'TQWO<<KkOOQO7+*k7+*kO9aQWO7+*kO'2uQWO<= hOOQUG27lG27lO9kQ07hOG27lO!*fQ^O1G4{O'2}QWO7++yO&3tQWOANAzOOQUANAzANAzO!&dQ7[OANAzO'3VQWOANAzOOQUANA|ANA|O9kQ07hOANA|O$!UQWOANA|OOQO'#HX'#HXOOQO7+*j7+*jOOQUG22vG22vOOQUANETANETOOQUANEUANEUOOQUANBUANBUO'3_QWOANBUOOQU<<MP<<MPO!*fQ^OAN?hOOQOG24uG24uO$=gQ08SOG24uO#%sQWOLD,kOOQULD,kLD,kO!&dQ7[OLD,kO'3dQWOLD,kO'3lQ(CYO7+'uO'5bQ(CYO,5?YO'7eQ(CYO,5?[O'9hQ(CYO7+'wO';^Q7[OG26}OOQO<<Mv<<MvOOQ07`ANAWANAWO$'TQWOANAWOOQ07`ANAVANAVOOQO<<NV<<NVOOQULD-WLD-WO';nQ(CYO7+*gOOQUG27fG27fO&3tQWOG27fO!&dQ7[OG27fOOQUG27hG27hO9kQ07hOG27hOOQUG27pG27pO';xQ(CYOG25SOOQOLD*aLD*aOOQU!$(!V!$(!VO#%sQWO!$(!VO!&dQ7[O!$(!VO'<SQ08SOG26}OOQ07`G26rG26rOOQULD-QLD-QO&3tQWOLD-QOOQULD-SLD-SOOQU!)9Eq!)9EqO#%sQWO!)9EqOOQU!$(!l!$(!lOOQU!.K;]!.K;]O'>eQ(CYOG26}O!*fQ^O'#DyO1PQWO'#EWO'@ZQrO'#JiO!*fQ^O'#DqO'@bQ^O'#D}O'@iQrO'#ChO'CPQrO'#ChO!*fQ^O'#EPO'CaQ^O,5;VO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O'#IiO'EdQWO,5<eO'ElQ7[O,5;aO'GVQ7[O,5;aO!*fQ^O,5;uO!&dQ7[O'#GgO'ElQ7[O'#GgO!&dQ7[O'#GiO'ElQ7[O'#GiO1SQWO'#DVO1SQWO'#DVO!&dQ7[O'#FzO'ElQ7[O'#FzO!&dQ7[O'#F|O'ElQ7[O'#F|O!&dQ7[O'#G[O'ElQ7[O'#G[O!*fQ^O,5:iO!*fQ^O,5@eO'CaQ^O1G0qO'G^Q(CYO'#ChO!*fQ^O1G1|O!&dQ7[O'#InO'ElQ7[O'#InO!&dQ7[O'#IpO'ElQ7[O'#IpO!&dQ7[O,5<oO'ElQ7[O,5<oO'CaQ^O1G1}O!*fQ^O7+&xO!&dQ7[O1G2ZO'ElQ7[O1G2ZO!&dQ7[O'#InO'ElQ7[O'#InO!&dQ7[O'#IpO'ElQ7[O'#IpO!&dQ7[O1G2]O'ElQ7[O1G2]O'CaQ^O7+'iO'CaQ^O7+&]O!&dQ7[OANAcO'ElQ7[OANAcO'GhQWO'#EkO'GmQWO'#EkO'GuQWO'#FZO'GzQWO'#EuO'HPQWO'#JyO'H[QWO'#JwO'HgQWO,5;VO'HlQ7[O,5<bO'HsQWO'#GTO'HxQWO'#GTO'H}QWO,5<cO'IVQWO,5;VO'I_Q(CYO1G1^O'IfQWO,5<oO'IkQWO,5<oO'IpQWO,5<qO'IuQWO,5<qO'IzQWO1G1}O'JPQWO1G0qO'JUQ7[O<<KwO'J]Q7[O<<KwO7hQ7[O'#FxO9UQWO'#FwOA]QWO'#EjO!*fQ^O,5;rO!3fQWO'#GTO!3fQWO'#GTO!3fQWO'#GVO!3fQWO'#GVO!(SQ7[O7+(]O!(SQ7[O7+(]O%*sQpO1G2qO%*sQpO1G2qO!&dQ7[O,5=VO!&dQ7[O,5=V",stateData:"'Ka~O'tOS'uOSSOS'vRQ~OPYOQYORfOX!VO`qOczOdyOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![XO!fuO!kZO!nYO!oYO!pYO!rvO!twO!wxO!{]O#s!PO$T|O%b}O%d!QO%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO%s!UO&P!WO&V!XO&X!YO&Z!ZO&]![O&`!]O&f!^O&l!_O&n!`O&p!aO&r!bO&t!cO'{SO'}TO(QUO(XVO(g[O(tiO~OVtO~P`OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(tiO~O`!vOo!nO!P!oO!_!xO!`!uO!a!uO!{:dO#P!pO#Q!pO#R!wO#S!pO#T!pO#W!yO#X!yO'|!lO'}TO(QUO([!mO(g!sO~O'v!zO~OP[XZ[X`[Xn[X|[X}[X!P[X!Y[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X'r[X(X[X(h[X(o[X(p[X~O!d$|X~P(qO^!|O'}#OO(O!|O(P#OO~O^#PO(P#OO(Q#OO(R#PO~Ot#RO!R#SO(Y#SO(Z#UO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{:hO'}TO(QUO(XVO(g[O(tiO~O!X#YO!Y#VO!V(_P!V(lP~P+}O!Z#bO~P`OPYOQYORfOc!jOd!iOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'}TO(QUO(XVO(g[O(tiO~Ol#lO!X#hO!{]O#e#kO#f#hO'{:iO!j(iP~P.iO!k#nO'{#mO~O!w#rO!{]O%b#sO~O#g#tO~O!d#uO#g#tO~OP$]OZ$dOn$QO|#yO}#zO!P#{O!Y$aO!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO#v$VO#x$XO#y$YO(XVO(h$ZO(o#|O(p#}O~O`(]X'r(]X'p(]X!j(]X!V(]X![(]X%c(]X!d(]X~P1qO#[$eO$O$eOP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#r(^X#t(^X#v(^X#x(^X#y(^X(X(^X(h(^X(o(^X(p(^X![(^X%c(^X~O`(^X!i(^X'r(^X'p(^X!V(^X!j(^Xr(^X!d(^X~P4XO#[$eO~O$Y$gO$[$fO$c$lO~ORfO![$mO$f$nO$h$pO~Og%VOl%WOn$tOo$sOp$sOv%XOx%YOz%ZO!P${O![$|O!f%`O!k$xO#f%aO$T%^O$o%[O$q%]O$t%_O'{$rO'}TO(QUO(X$uO(o$}O(p%POf(UP~O!k%bO~O!P%eO![%fO'{%dO~O!d%jO~O`%kO'r%kO~O'|!lO~P%[O%h%rO~P%[Og%VO!k%bO'{%dO'|!lO~Od%yO!k%bO'{%dO~O#r$SO~O|&OO![%{O!k%}O%d&RO'{%dO'|!lO'}TO(QUO_(}P~O!w#rO~O%m&TO!P(yX![(yX'{(yX~O'{&UO~O!t&ZO#s!PO%d!QO%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO~Oc&`Od&_O!w&]O%b&^O%u&[O~P;xOc&cOdyO![&bO!t&ZO!wxO!{]O#s!PO%b}O%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO%s!UO~Oa&fO#[&iO%d&dO'|!lO~P<}O!k&jO!t&nO~O!k#nO~O![XO~O`%kO'q&vO'r%kO~O`%kO'q&yO'r%kO~O`%kO'q&{O'r%kO~O'p[X!V[Xr[X!j[X&T[X![[X%c[X!d[X~P(qO!_'YO!`'RO!a'RO'|!lO'}TO(QUO~Oo'PO!P'OO!X'SO([&}O!Z(`P!Z(nP~P@UOj']O!['ZO'{%dO~Od'bO!k%bO'{%dO~O|&OO!k%}O~Oo!nO!P!oO!{:dO#P!pO#Q!pO#S!pO#T!pO'|!lO'}TO(QUO([!mO(g!sO~O!_'hO!`'gO!a'gO#R!pO#W'iO#X'iO~PApO`%kOg%VO!d#uO!k%bO'r%kO(h'kO~O!o'oO#['mO~PCOOo!nO!P!oO'}TO(QUO([!mO(g!sO~O![XOo(eX!P(eX!_(eX!`(eX!a(eX!{(eX#P(eX#Q(eX#R(eX#S(eX#T(eX#W(eX#X(eX'|(eX'}(eX(Q(eX([(eX(g(eX~O!`'gO!a'gO'|!lO~PCnO'w'sO'x'sO'y'uO~O^!|O'}'wO(O!|O(P'wO~O^#PO(P'wO(Q'wO(R#PO~Ot#RO!R#SO(Y#SO(Z'{O~O!X'}O!V'PX!V'VX!Y'PX!Y'VX~P+}O!Y(PO!V(_X~OP$]OZ$dOn$QO|#yO}#zO!P#{O!Y(PO!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO#v$VO#x$XO#y$YO(XVO(h$ZO(o#|O(p#}O~O!V(_X~PGbO!V(UO~O!V(kX!Y(kX!d(kX!j(kX(h(kX~O#[(kX#g#`X!Z(kX~PIhO#[(VO!V(mX!Y(mX~O!Y(WO!V(lX~O!V(ZO~O#[$eO~PIhO!Z([O~P`O|#yO}#zO!P#{O!i#wO!k#xO(XVOP!maZ!man!ma!Y!ma!h!ma!o!ma#j!ma#k!ma#l!ma#m!ma#n!ma#o!ma#p!ma#q!ma#r!ma#t!ma#v!ma#x!ma#y!ma(h!ma(o!ma(p!ma~O`!ma'r!ma'p!ma!V!ma!j!mar!ma![!ma%c!ma!d!ma~PKOO!j(]O~O!d#uO#[(^O(h'kO!Y(jX`(jX'r(jX~O!j(jX~PMnO!P%eO![%fO!{]O#e(cO#f(bO'{%dO~O!Y(dO!j(iX~O!j(fO~O!P%eO![%fO#f(bO'{%dO~OP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!i(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#r(^X#t(^X#v(^X#x(^X#y(^X(X(^X(h(^X(o(^X(p(^X~O!d#uO!j(^X~P! [O|(gO}(hO!i#wO!k#xO!{!za!P!za~O!w!za%b!za![!za#e!za#f!za'{!za~P!#`O!w(lO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![XO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(tiO~Og%VOl%WOn$tOo$sOp$sOv%XOx%YOz;QO!P${O![$|O!f<`O!k$xO#f;WO$T%^O$o;SO$q;UO$t%_O'{(pO'}TO(QUO(X$uO(o$}O(p%PO~O#g(rO~Og%VOl%WOn$tOo$sOp$sOv%XOx%YOz%ZO!P${O![$|O!f%`O!k$xO#f%aO$T%^O$o%[O$q%]O$t%_O'{(pO'}TO(QUO(X$uO(o$}O(p%PO~Of(bP~P!(SO!X(vO!j(cP~P%[O([(xO(g[O~O!P(zO!k#xO([(xO(g[O~OP:cOQ:cORfOc<[Od!iOlkOn:cOokOpkOvkOx:cOz:cO!PWO!TkO!UkO![!eO!f:fO!kZO!n:cO!o:cO!p:cO!r:gO!t:jO!w!hO$T!kO'{)YO'}TO(QUO(XVO(g[O(t<YO~O})]O!k#xO~O!Y$aO`$ma'r$ma'p$ma!j$ma!V$ma![$ma%c$ma!d$ma~O#s)aO~P!&dO|)dO!d)cO![$ZX$W$ZX$Y$ZX$[$ZX$c$ZX~O!d)cO![(qX$W(qX$Y(qX$[(qX$c(qX~O|)dO~P!.OO|)dO![(qX$W(qX$Y(qX$[(qX$c(qX~O![)fO$W)jO$Y)eO$[)eO$c)kO~O!X)nO~P!*fO$Y$gO$[$fO$c)rO~Oj$uX|$uX!P$uX!i$uX(o$uX(p$uX~OfiXf$uXjiX!YiX#[iX~P!/tOo)tO~Ot)uO(Y)vO(Z)xO~Oj*RO|)zO!P){O(o$}O(p%PO~Of)yO~P!0}Of*SO~Og%VOl%WOn$tOo$sOp$sOv%XOx%YOz;QO!P${O![$|O!f<`O!k$xO#f;WO$T%^O$o;SO$q;UO$t%_O'}TO(QUO(X$uO(o$}O(p%PO~O!X*WO'{*TO!j(uP~P!1lO#g*YO~O!k*ZO~O!X*`O'{*]O!V(vP~P!1lOn*lO!P*dO!_*jO!`*cO!a*cO!k*ZO#W*kO%Y*fO'|!lO([!mO~O!Z*iO~P!3xO!i#wOj(WX|(WX!P(WX(o(WX(p(WX!Y(WX#[(WX~Of(WX#|(WX~P!4qOj*qO#[*pOf(VX!Y(VX~O!Y*rOf(UX~O'{&UOf(UP~O!k*yO~O'{(pO~Ol*}O!P%eO!X#hO![%fO!{]O#e#kO#f#hO'{%dO!j(iP~O!d#uO#g+OO~O!P%eO!X+QO!Y(WO![%fO'{%dO!V(lP~Oo'VO!P+SO!X+RO'}TO(QUO([(xO~O!Z(nP~P!7lO!Y+TO`(zX'r(zX~OP$]OZ$dOn$QO|#yO}#zO!P#{O!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO#v$VO#x$XO#y$YO(XVO(h$ZO(o#|O(p#}O~O`!ea!Y!ea'r!ea'p!ea!V!ea!j!ear!ea![!ea%c!ea!d!ea~P!8dO|#yO}#zO!P#{O!i#wO!k#xO(XVOP!qaZ!qan!qa!Y!qa!h!qa!o!qa#j!qa#k!qa#l!qa#m!qa#n!qa#o!qa#p!qa#q!qa#r!qa#t!qa#v!qa#x!qa#y!qa(h!qa(o!qa(p!qa~O`!qa'r!qa'p!qa!V!qa!j!qar!qa![!qa%c!qa!d!qa~P!:}O|#yO}#zO!P#{O!i#wO!k#xO(XVOP!saZ!san!sa!Y!sa!h!sa!o!sa#j!sa#k!sa#l!sa#m!sa#n!sa#o!sa#p!sa#q!sa#r!sa#t!sa#v!sa#x!sa#y!sa(h!sa(o!sa(p!sa~O`!sa'r!sa'p!sa!V!sa!j!sar!sa![!sa%c!sa!d!sa~P!=hOg%VOj+^O!['ZO%c+]O~O!d+`O`(TX![(TX'r(TX!Y(TX~O`%kO![XO'r%kO~Og%VO!k%bO~Og%VO!k%bO'{%dO~O!d#uO#g(rO~Oa+kO%d+lO'{+hO'}TO(QUO!Z)OP~O!Y+mO_(}X~OZ+qO~O_+rO~O![%{O'{%dO'|!lO_(}P~Og%VO#[+wO~Og%VOj+zO![$|O~O![+|O~O|,OO![XO~O%h%rO~O!w,TO~Od,YO~Oa,ZO'{#mO'}TO(QUO!Z(|P~Od%yO~O%d!QO'{&UO~P<}OZ,`O_,_O~OPYOQYORfOczOdyOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO!fuO!kZO!nYO!oYO!pYO!rvO!wxO!{]O%b}O'}TO(QUO(XVO(g[O(tiO~O![!eO!t!gO$T!kO'{!dO~P!DkO_,_O`%kO'r%kO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(tiO~O`,eO!twO#s!OO%f!OO%g!OO%h!OO~P!GTO!k&jO~O&V,kO~O![,mO~O&h,oO&j,pOP&eaQ&eaR&eaX&ea`&eac&ead&eal&ean&eao&eap&eav&eax&eaz&ea!P&ea!T&ea!U&ea![&ea!f&ea!k&ea!n&ea!o&ea!p&ea!r&ea!t&ea!w&ea!{&ea#s&ea$T&ea%b&ea%d&ea%f&ea%g&ea%h&ea%k&ea%m&ea%p&ea%q&ea%s&ea&P&ea&V&ea&X&ea&Z&ea&]&ea&`&ea&f&ea&l&ea&n&ea&p&ea&r&ea&t&ea'p&ea'{&ea'}&ea(Q&ea(X&ea(g&ea(t&ea!Z&ea&^&eaa&ea&c&ea~O'{,uO~Og!bX!Y!OX!Y!bX!Z!OX!Z!bX!d!OX!d!bX!k!bX#[!OX~O!d,zO#[,yOg(aX!Y#dX!Y(aX!Z#dX!Z(aX!d(aX!k(aX~Og%VO!d,|O!k%bO!Y!^X!Z!^X~Oo!nO!P!oO'}TO(QUO([!mO~OP:cOQ:cORfOc<[Od!iOlkOn:cOokOpkOvkOx:cOz:cO!PWO!TkO!UkO![!eO!f:fO!kZO!n:cO!o:cO!p:cO!r:gO!t:jO!w!hO$T!kO'}TO(QUO(XVO(g[O(t<YO~O'{;]O~P#!ZO!Y-QO!Z(`X~O!Z-SO~O!d,zO#[,yO!Y#dX!Z#dX~O!Y-TO!Z(nX~O!Z-VO~O!`-WO!a-WO'|!lO~P# xO!Z-ZO~P'_Oj-^O!['ZO~O!V-cO~Oo!za!_!za!`!za!a!za#P!za#Q!za#R!za#S!za#T!za#W!za#X!za'|!za'}!za(Q!za([!za(g!za~P!#`O!o-hO#[-fO~PCOO!`-jO!a-jO'|!lO~PCnO`%kO#[-fO'r%kO~O`%kO!d#uO#[-fO'r%kO~O`%kO!d#uO!o-hO#[-fO'r%kO(h'kO~O'w'sO'x'sO'y-oO~Or-pO~O!V'Pa!Y'Pa~P!8dO!X-tO!V'PX!Y'PX~P%[O!Y(PO!V(_a~O!V(_a~PGbO!Y(WO!V(la~O!P%eO!X-xO![%fO'{%dO!V'VX!Y'VX~O#[-zO!Y(ja!j(ja`(ja'r(ja~O!d#uO~P#*aO!Y(dO!j(ia~O!P%eO![%fO#f.OO'{%dO~Ol.TO!P%eO!X.QO![%fO!{]O#e.SO#f.QO'{%dO!Y'YX!j'YX~O}.XO!k#xO~Og%VOj.[O!['ZO%c.ZO~O`#_i!Y#_i'r#_i'p#_i!V#_i!j#_ir#_i![#_i%c#_i!d#_i~P!8dOj<fO|)zO!P){O(o$}O(p%PO~O#g#Za`#Za#[#Za'r#Za!Y#Za!j#Za![#Za!V#Za~P#-]O#g(WXP(WXZ(WX`(WXn(WX}(WX!h(WX!k(WX!o(WX#j(WX#k(WX#l(WX#m(WX#n(WX#o(WX#p(WX#q(WX#r(WX#t(WX#v(WX#x(WX#y(WX'r(WX(X(WX(h(WX!j(WX!V(WX'p(WXr(WX![(WX%c(WX!d(WX~P!4qO!Y.iOf(bX~P!0}Of.kO~O!Y.lO!j(cX~P!8dO!j.oO~O!V.qO~OP$]O|#yO}#zO!P#{O!i#wO!k#xO!o$]O(XVOZ#ii`#iin#ii!Y#ii!h#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(h#ii(o#ii(p#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O#j#ii~P#1XO#j$OO~P#1XOP$]O|#yO}#zO!P#{O!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO(XVOZ#ii`#ii!Y#ii!h#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(h#ii(o#ii(p#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~On#ii~P#3yOn$QO~P#3yOP$]On$QO|#yO}#zO!P#{O!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO(XVO`#ii!Y#ii#t#ii#v#ii#x#ii#y#ii'r#ii(h#ii(o#ii(p#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~OZ#ii!h#ii#o#ii#p#ii#q#ii#r#ii~P#6kOZ$dO!h$SO#o$SO#p$SO#q$cO#r$SO~P#6kOP$]OZ$dOn$QO|#yO}#zO!P#{O!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO(XVO`#ii!Y#ii#v#ii#x#ii#y#ii'r#ii(h#ii(p#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O(o#ii~P#9lO(o#|O~P#9lOP$]OZ$dOn$QO|#yO}#zO!P#{O!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO#v$VO(XVO(o#|O`#ii!Y#ii#x#ii#y#ii'r#ii(h#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O(p#ii~P#<^O(p#}O~P#<^OP$]OZ$dOn$QO|#yO}#zO!P#{O!h$SO!i#wO!k#xO!o$]O#j$OO#k$PO#l$PO#m$PO#n$RO#o$SO#p$SO#q$cO#r$SO#t$TO#v$VO#x$XO(XVO(o#|O(p#}O~O`#ii!Y#ii#y#ii'r#ii(h#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~P#?OOP[XZ[Xn[X|[X}[X!P[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(h[X(o[X(p[X!Y[X!Z[X~O#|[X~P#AiOP$]OZ:zOn:nO|#yO}#zO!P#{O!h:pO!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO#n:oO#o:pO#p:pO#q:yO#r:pO#t:qO#v:sO#x:uO#y:vO(XVO(h$ZO(o#|O(p#}O~O#|.sO~P#CvO#[:{O$O:{O#|(^X!Z(^X~P! [O`']a!Y']a'r']a'p']a!j']a!V']ar']a![']a%c']a!d']a~P!8dOP#iiZ#ii`#iin#ii}#ii!Y#ii!h#ii!i#ii!k#ii!o#ii#j#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(X#ii(h#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~P#-]O`#}i!Y#}i'r#}i'p#}i!V#}i!j#}ir#}i![#}i%c#}i!d#}i~P!8dO$Y.xO$[.xO~O$Y.yO$[.yO~O!d)cO#[.zO![$`X$W$`X$Y$`X$[$`X$c$`X~O!X.{O~O![)fO$W.}O$Y)eO$[)eO$c/OO~O!Y:wO!Z(]X~P#CvO!Z/PO~O!d)cO$c(qX~O$c/RO~Ot)uO(Y)vO(Z/UO~O!V/YO~P!&dO(o$}Oj%Za|%Za!P%Za(p%Za!Y%Za#[%Za~Of%Za#|%Za~P#LWO(p%POj%]a|%]a!P%]a(o%]a!Y%]a#[%]a~Of%]a#|%]a~P#LyO!YeX!deX!jeX!j$uX(heX~P!/tO!j/bO~P#-]O!Y/cO!d#uO(h'kO!j(uX~O!j/hO~O!X*WO'{%dO!j(uP~O#g/jO~O!V$uX!Y$uX!d$|X~P!/tO!Y/kO!V(vX~P#-]O!d/mO~O!V/oO~Og%VOn/sO!d#uO!k%bO(h'kO~O'{/uO~O!d+`O~O`%kO!Y/yO'r%kO~O!Z/{O~P!3xO!`/|O!a/|O'|!lO([!mO~O!P0OO([!mO~O#W0PO~Of%Za!Y%Za#[%Za#|%Za~P!0}Of%]a!Y%]a#[%]a#|%]a~P!0}O'{&UOf'fX!Y'fX~O!Y*rOf(Ua~Of0YO~O|0ZO}0ZO!P0[Ojya(oya(pya!Yya#[ya~Ofya#|ya~P$$dO|)zO!P){Oj$na(o$na(p$na!Y$na#[$na~Of$na#|$na~P$%YO|)zO!P){Oj$pa(o$pa(p$pa!Y$pa#[$pa~Of$pa#|$pa~P$%{O#g0^O~Of%Oa!Y%Oa#[%Oa#|%Oa~P!0}O!d#uO~O#g0aO~O!Y+TO`(za'r(za~O|#yO}#zO!P#{O!i#wO!k#xO(XVOP!qiZ!qin!qi!Y!qi!h!qi!o!qi#j!qi#k!qi#l!qi#m!qi#n!qi#o!qi#p!qi#q!qi#r!qi#t!qi#v!qi#x!qi#y!qi(h!qi(o!qi(p!qi~O`!qi'r!qi'p!qi!V!qi!j!qir!qi![!qi%c!qi!d!qi~P$'jOg%VOn$tOo$sOp$sOv%XOx%YOz;QO!P${O![$|O!f<`O!k$xO#f;WO$T%^O$o;SO$q;UO$t%_O'}TO(QUO(X$uO(o$}O(p%PO~Ol0kO'{0jO~P$*TO!d+`O`(Ta![(Ta'r(Ta!Y(Ta~O#g0qO~OZ[X!YeX!ZeX~O!Y0rO!Z)OX~O!Z0tO~OZ0uO~Oa0wO'{+hO'}TO(QUO~O![%{O'{%dO_'nX!Y'nX~O!Y+mO_(}a~O!j0zO~P!8dOZ0}O~O_1OO~O#[1RO~Oj1UO![$|O~O([(xO!Z({P~Og%VOj1_O![1[O%c1^O~OZ1iO!Y1gO!Z(|X~O!Z1jO~O_1lO`%kO'r%kO~O'{#mO'}TO(QUO~O#[$eO$O$eOP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#t(^X#v(^X#x(^X#y(^X(X(^X(h(^X(o(^X(p(^X~O#r1oO&T1pO`(^X!i(^X~P$/kO#[$eO#r1oO&T1pO~O`1rO~P%[O`1tO~O&^1wOP&[iQ&[iR&[iX&[i`&[ic&[id&[il&[in&[io&[ip&[iv&[ix&[iz&[i!P&[i!T&[i!U&[i![&[i!f&[i!k&[i!n&[i!o&[i!p&[i!r&[i!t&[i!w&[i!{&[i#s&[i$T&[i%b&[i%d&[i%f&[i%g&[i%h&[i%k&[i%m&[i%p&[i%q&[i%s&[i&P&[i&V&[i&X&[i&Z&[i&]&[i&`&[i&f&[i&l&[i&n&[i&p&[i&r&[i&t&[i'p&[i'{&[i'}&[i(Q&[i(X&[i(g&[i(t&[i!Z&[ia&[i&c&[i~Oa1}O!Z1{O&c1|O~P`O![XO!k2PO~O&j,pOP&eiQ&eiR&eiX&ei`&eic&eid&eil&ein&eio&eip&eiv&eix&eiz&ei!P&ei!T&ei!U&ei![&ei!f&ei!k&ei!n&ei!o&ei!p&ei!r&ei!t&ei!w&ei!{&ei#s&ei$T&ei%b&ei%d&ei%f&ei%g&ei%h&ei%k&ei%m&ei%p&ei%q&ei%s&ei&P&ei&V&ei&X&ei&Z&ei&]&ei&`&ei&f&ei&l&ei&n&ei&p&ei&r&ei&t&ei'p&ei'{&ei'}&ei(Q&ei(X&ei(g&ei(t&ei!Z&ei&^&eia&ei&c&ei~O!V2VO~O!Y!^a!Z!^a~P#CvOo!nO!P!oO!X2]O([!mO!Y'QX!Z'QX~P@UO!Y-QO!Z(`a~O!Y'WX!Z'WX~P!7lO!Y-TO!Z(na~O!Z2dO~P'_O`%kO#[2mO'r%kO~O`%kO!d#uO#[2mO'r%kO~O`%kO!d#uO!o2qO#[2mO'r%kO(h'kO~O`%kO'r%kO~P!8dO!Y$aOr$ma~O!V'Pi!Y'Pi~P!8dO!Y(PO!V(_i~O!Y(WO!V(li~O!V(mi!Y(mi~P!8dO!Y(ji!j(ji`(ji'r(ji~P!8dO#[2sO!Y(ji!j(ji`(ji'r(ji~O!Y(dO!j(ii~O!P%eO![%fO!{]O#e2xO#f2wO'{%dO~O!P%eO![%fO#f2wO'{%dO~Oj3PO!['ZO%c3OO~Og%VOj3PO!['ZO%c3OO~O#g%ZaP%ZaZ%Za`%Zan%Za}%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za'r%Za(X%Za(h%Za!j%Za!V%Za'p%Zar%Za![%Za%c%Za!d%Za~P#LWO#g%]aP%]aZ%]a`%]an%]a}%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a'r%]a(X%]a(h%]a!j%]a!V%]a'p%]ar%]a![%]a%c%]a!d%]a~P#LyO#g%ZaP%ZaZ%Za`%Zan%Za}%Za!Y%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za'r%Za(X%Za(h%Za!j%Za!V%Za'p%Za#[%Zar%Za![%Za%c%Za!d%Za~P#-]O#g%]aP%]aZ%]a`%]an%]a}%]a!Y%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a'r%]a(X%]a(h%]a!j%]a!V%]a'p%]a#[%]ar%]a![%]a%c%]a!d%]a~P#-]O#gyaPyaZya`yanya!hya!iya!kya!oya#jya#kya#lya#mya#nya#oya#pya#qya#rya#tya#vya#xya#yya'rya(Xya(hya!jya!Vya'pyarya![ya%cya!dya~P$$dO#g$naP$naZ$na`$nan$na}$na!h$na!i$na!k$na!o$na#j$na#k$na#l$na#m$na#n$na#o$na#p$na#q$na#r$na#t$na#v$na#x$na#y$na'r$na(X$na(h$na!j$na!V$na'p$nar$na![$na%c$na!d$na~P$%YO#g$paP$paZ$pa`$pan$pa}$pa!h$pa!i$pa!k$pa!o$pa#j$pa#k$pa#l$pa#m$pa#n$pa#o$pa#p$pa#q$pa#r$pa#t$pa#v$pa#x$pa#y$pa'r$pa(X$pa(h$pa!j$pa!V$pa'p$par$pa![$pa%c$pa!d$pa~P$%{O#g%OaP%OaZ%Oa`%Oan%Oa}%Oa!Y%Oa!h%Oa!i%Oa!k%Oa!o%Oa#j%Oa#k%Oa#l%Oa#m%Oa#n%Oa#o%Oa#p%Oa#q%Oa#r%Oa#t%Oa#v%Oa#x%Oa#y%Oa'r%Oa(X%Oa(h%Oa!j%Oa!V%Oa'p%Oa#[%Oar%Oa![%Oa%c%Oa!d%Oa~P#-]O`#_q!Y#_q'r#_q'p#_q!V#_q!j#_qr#_q![#_q%c#_q!d#_q~P!8dOf'RX!Y'RX~P!(SO!Y.iOf(ba~O!X3ZO!Y'SX!j'SX~P%[O!Y.lO!j(ca~O!Y.lO!j(ca~P!8dO!V3^O~O#|!ma!Z!ma~PKOO#|!ea!Y!ea!Z!ea~P#CvO#|!qa!Z!qa~P!:}O#|!sa!Z!sa~P!=hORfO![3pO$a3qO~O!Z3uO~Or3vO~P#-]O`$jq!Y$jq'r$jq'p$jq!V$jq!j$jqr$jq![$jq%c$jq!d$jq~P!8dO!V3wO~P#-]O|)zO!P){O(p%POj'ba(o'ba!Y'ba#['ba~Of'ba#|'ba~P%)_O|)zO!P){Oj'da(o'da(p'da!Y'da#['da~Of'da#|'da~P%*QO(h$ZO~P#-]O!X3zO'{%dO!Y'^X!j'^X~O!Y/cO!j(ua~O!Y/cO!d#uO!j(ua~O!Y/cO!d#uO(h'kO!j(ua~Of$wi!Y$wi#[$wi#|$wi~P!0}O!X4SO'{*]O!V'`X!Y'`X~P!1lO!Y/kO!V(va~O!Y/kO!V(va~P#-]O!d#uO#r4[O~On4_O!d#uO(h'kO~O(o$}Oj%Zi|%Zi!P%Zi(p%Zi!Y%Zi#[%Zi~Of%Zi#|%Zi~P%-dO(p%POj%]i|%]i!P%]i(o%]i!Y%]i#[%]i~Of%]i#|%]i~P%.VOf(Vi!Y(Vi~P!0}O#[4fOf(Vi!Y(Vi~P!0}O!j4iO~O`$kq!Y$kq'r$kq'p$kq!V$kq!j$kqr$kq![$kq%c$kq!d$kq~P!8dO!V4mO~O!Y4nO![(wX~P#-]O!i#wO~P4XO`$uX![$uX%W[X'r$uX!Y$uX~P!/tO%W4pO`kXjkX|kX!PkX![kX'rkX(okX(pkX!YkX~O%W4pO~Oa4vO%d4wO'{+hO'}TO(QUO!Y'mX!Z'mX~O!Y0rO!Z)Oa~OZ4{O~O_4|O~O`%kO'r%kO~P#-]O![$|O~P#-]O!Y5UO#[5WO!Z({X~O!Z5XO~Oo!nO!P5YO!_!xO!`!uO!a!uO!{:dO#P!pO#Q!pO#R!pO#S!pO#T!pO#W5_O#X!yO'|!lO'}TO(QUO([!mO(g!sO~O!Z5^O~P%3hOj5dO![1[O%c5cO~Og%VOj5dO![1[O%c5cO~Oa5kO'{#mO'}TO(QUO!Y'lX!Z'lX~O!Y1gO!Z(|a~O'}TO(QUO([5mO~O_5qO~O#r5tO&T5uO~PMnO!j5vO~P%[O`5xO~O`5xO~P%[Oa1}O!Z5}O&c1|O~P`O!d6PO~O!d6ROg(ai!Y(ai!Z(ai!d(ai!k(ai~O!Y#di!Z#di~P#CvO#[6SO!Y#di!Z#di~O!Y!^i!Z!^i~P#CvO`%kO#[6]O'r%kO~O`%kO!d#uO#[6]O'r%kO~O!Y(jq!j(jq`(jq'r(jq~P!8dO!Y(dO!j(iq~O!P%eO![%fO#f6dO'{%dO~O!['ZO%c6gO~Oj6jO!['ZO%c6gO~O#g'baP'baZ'ba`'ban'ba}'ba!h'ba!i'ba!k'ba!o'ba#j'ba#k'ba#l'ba#m'ba#n'ba#o'ba#p'ba#q'ba#r'ba#t'ba#v'ba#x'ba#y'ba'r'ba(X'ba(h'ba!j'ba!V'ba'p'bar'ba!['ba%c'ba!d'ba~P%)_O#g'daP'daZ'da`'dan'da}'da!h'da!i'da!k'da!o'da#j'da#k'da#l'da#m'da#n'da#o'da#p'da#q'da#r'da#t'da#v'da#x'da#y'da'r'da(X'da(h'da!j'da!V'da'p'dar'da!['da%c'da!d'da~P%*QO#g$wiP$wiZ$wi`$win$wi}$wi!Y$wi!h$wi!i$wi!k$wi!o$wi#j$wi#k$wi#l$wi#m$wi#n$wi#o$wi#p$wi#q$wi#r$wi#t$wi#v$wi#x$wi#y$wi'r$wi(X$wi(h$wi!j$wi!V$wi'p$wi#[$wir$wi![$wi%c$wi!d$wi~P#-]O#g%ZiP%ZiZ%Zi`%Zin%Zi}%Zi!h%Zi!i%Zi!k%Zi!o%Zi#j%Zi#k%Zi#l%Zi#m%Zi#n%Zi#o%Zi#p%Zi#q%Zi#r%Zi#t%Zi#v%Zi#x%Zi#y%Zi'r%Zi(X%Zi(h%Zi!j%Zi!V%Zi'p%Zir%Zi![%Zi%c%Zi!d%Zi~P%-dO#g%]iP%]iZ%]i`%]in%]i}%]i!h%]i!i%]i!k%]i!o%]i#j%]i#k%]i#l%]i#m%]i#n%]i#o%]i#p%]i#q%]i#r%]i#t%]i#v%]i#x%]i#y%]i'r%]i(X%]i(h%]i!j%]i!V%]i'p%]ir%]i![%]i%c%]i!d%]i~P%.VOf'Ra!Y'Ra~P!0}O!Y'Sa!j'Sa~P!8dO!Y.lO!j(ci~O#|#_i!Y#_i!Z#_i~P#CvOP$]O|#yO}#zO!P#{O!i#wO!k#xO!o$]O(XVOZ#iin#ii!h#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(h#ii(o#ii(p#ii!Y#ii!Z#ii~O#j#ii~P%FhO#j:lO~P%FhOP$]O|#yO}#zO!P#{O!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO(XVOZ#ii!h#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(h#ii(o#ii(p#ii!Y#ii!Z#ii~On#ii~P%HsOn:nO~P%HsOP$]On:nO|#yO}#zO!P#{O!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO#n:oO(XVO#t#ii#v#ii#x#ii#y#ii#|#ii(h#ii(o#ii(p#ii!Y#ii!Z#ii~OZ#ii!h#ii#o#ii#p#ii#q#ii#r#ii~P%KOOZ:zO!h:pO#o:pO#p:pO#q:yO#r:pO~P%KOOP$]OZ:zOn:nO|#yO}#zO!P#{O!h:pO!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO#n:oO#o:pO#p:pO#q:yO#r:pO#t:qO(XVO#v#ii#x#ii#y#ii#|#ii(h#ii(p#ii!Y#ii!Z#ii~O(o#ii~P%MjO(o#|O~P%MjOP$]OZ:zOn:nO|#yO}#zO!P#{O!h:pO!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO#n:oO#o:pO#p:pO#q:yO#r:pO#t:qO#v:sO(XVO(o#|O#x#ii#y#ii#|#ii(h#ii!Y#ii!Z#ii~O(p#ii~P& uO(p#}O~P& uOP$]OZ:zOn:nO|#yO}#zO!P#{O!h:pO!i#wO!k#xO!o$]O#j:lO#k:mO#l:mO#m:mO#n:oO#o:pO#p:pO#q:yO#r:pO#t:qO#v:sO#x:uO(XVO(o#|O(p#}O~O#y#ii#|#ii(h#ii!Y#ii!Z#ii~P&$QO`#zy!Y#zy'r#zy'p#zy!V#zy!j#zyr#zy![#zy%c#zy!d#zy~P!8dOj<gO|)zO!P){O(o$}O(p%PO~OP#iiZ#iin#ii}#ii!h#ii!i#ii!k#ii!o#ii#j#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(X#ii(h#ii!Y#ii!Z#ii~P&&xO!i#wOP(WXZ(WXj(WXn(WX|(WX}(WX!P(WX!h(WX!k(WX!o(WX#j(WX#k(WX#l(WX#m(WX#n(WX#o(WX#p(WX#q(WX#r(WX#t(WX#v(WX#x(WX#y(WX#|(WX(X(WX(h(WX(o(WX(p(WX!Y(WX!Z(WX~O#|#}i!Y#}i!Z#}i~P#CvO#|!qi!Z!qi~P$'jO!Z6|O~O!Y']a!Z']a~P#CvO!d#uO(h'kO!Y'^a!j'^a~O!Y/cO!j(ui~O!Y/cO!d#uO!j(ui~Of$wq!Y$wq#[$wq#|$wq~P!0}O!V'`a!Y'`a~P#-]O!d7TO~O!Y/kO!V(vi~P#-]O!Y/kO!V(vi~O!V7XO~O!d#uO#r7^O~On7_O!d#uO(h'kO~O|)zO!P){O(p%POj'ca(o'ca!Y'ca#['ca~Of'ca#|'ca~P&.YO|)zO!P){Oj'ea(o'ea(p'ea!Y'ea#['ea~Of'ea#|'ea~P&.{O!V7aO~Of$yq!Y$yq#[$yq#|$yq~P!0}O`$ky!Y$ky'r$ky'p$ky!V$ky!j$kyr$ky![$ky%c$ky!d$ky~P!8dO!d6RO~O!Y4nO![(wa~O`#_y!Y#_y'r#_y'p#_y!V#_y!j#_yr#_y![#_y%c#_y!d#_y~P!8dOZ7fO~Oa7hO'{+hO'}TO(QUO~O!Y0rO!Z)Oi~O_7lO~O([(xO!Y'iX!Z'iX~O!Y5UO!Z({a~OlkO'{7sO~P.iO!Z7vO~P%3hOo!nO!P7wO'}TO(QUO([!mO(g!sO~O![1[O~O![1[O%c7yO~Oj7|O![1[O%c7yO~OZ8RO!Y'la!Z'la~O!Y1gO!Z(|i~O!j8VO~O!j8WO~O!j8ZO~O!j8ZO~P%[O`8]O~O!d8^O~O!j8_O~O!Y(mi!Z(mi~P#CvO`%kO#[8gO'r%kO~O!Y(jy!j(jy`(jy'r(jy~P!8dO!Y(dO!j(iy~O!['ZO%c8jO~O#g$wqP$wqZ$wq`$wqn$wq}$wq!Y$wq!h$wq!i$wq!k$wq!o$wq#j$wq#k$wq#l$wq#m$wq#n$wq#o$wq#p$wq#q$wq#r$wq#t$wq#v$wq#x$wq#y$wq'r$wq(X$wq(h$wq!j$wq!V$wq'p$wq#[$wqr$wq![$wq%c$wq!d$wq~P#-]O#g'caP'caZ'ca`'can'ca}'ca!h'ca!i'ca!k'ca!o'ca#j'ca#k'ca#l'ca#m'ca#n'ca#o'ca#p'ca#q'ca#r'ca#t'ca#v'ca#x'ca#y'ca'r'ca(X'ca(h'ca!j'ca!V'ca'p'car'ca!['ca%c'ca!d'ca~P&.YO#g'eaP'eaZ'ea`'ean'ea}'ea!h'ea!i'ea!k'ea!o'ea#j'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#t'ea#v'ea#x'ea#y'ea'r'ea(X'ea(h'ea!j'ea!V'ea'p'ear'ea!['ea%c'ea!d'ea~P&.{O#g$yqP$yqZ$yq`$yqn$yq}$yq!Y$yq!h$yq!i$yq!k$yq!o$yq#j$yq#k$yq#l$yq#m$yq#n$yq#o$yq#p$yq#q$yq#r$yq#t$yq#v$yq#x$yq#y$yq'r$yq(X$yq(h$yq!j$yq!V$yq'p$yq#[$yqr$yq![$yq%c$yq!d$yq~P#-]O!Y'Si!j'Si~P!8dO#|#_q!Y#_q!Z#_q~P#CvO(o$}OP%ZaZ%Zan%Za}%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za#|%Za(X%Za(h%Za!Y%Za!Z%Za~Oj%Za|%Za!P%Za(p%Za~P&@bO(p%POP%]aZ%]an%]a}%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a#|%]a(X%]a(h%]a!Y%]a!Z%]a~Oj%]a|%]a!P%]a(o%]a~P&BiOj<gO|)zO!P){O(p%PO~P&@bOj<gO|)zO!P){O(o$}O~P&BiO|0ZO}0ZO!P0[OPyaZyajyanya!hya!iya!kya!oya#jya#kya#lya#mya#nya#oya#pya#qya#rya#tya#vya#xya#yya#|ya(Xya(hya(oya(pya!Yya!Zya~O|)zO!P){OP$naZ$naj$nan$na}$na!h$na!i$na!k$na!o$na#j$na#k$na#l$na#m$na#n$na#o$na#p$na#q$na#r$na#t$na#v$na#x$na#y$na#|$na(X$na(h$na(o$na(p$na!Y$na!Z$na~O|)zO!P){OP$paZ$paj$pan$pa}$pa!h$pa!i$pa!k$pa!o$pa#j$pa#k$pa#l$pa#m$pa#n$pa#o$pa#p$pa#q$pa#r$pa#t$pa#v$pa#x$pa#y$pa#|$pa(X$pa(h$pa(o$pa(p$pa!Y$pa!Z$pa~OP%OaZ%Oan%Oa}%Oa!h%Oa!i%Oa!k%Oa!o%Oa#j%Oa#k%Oa#l%Oa#m%Oa#n%Oa#o%Oa#p%Oa#q%Oa#r%Oa#t%Oa#v%Oa#x%Oa#y%Oa#|%Oa(X%Oa(h%Oa!Y%Oa!Z%Oa~P&&xO#|$jq!Y$jq!Z$jq~P#CvO#|$kq!Y$kq!Z$kq~P#CvO!Z8vO~O#|8wO~P!0}O!d#uO!Y'^i!j'^i~O!d#uO(h'kO!Y'^i!j'^i~O!Y/cO!j(uq~O!V'`i!Y'`i~P#-]O!Y/kO!V(vq~O!V8}O~P#-]O!V8}O~Of(Vy!Y(Vy~P!0}O!Y'ga!['ga~P#-]O`%Vq![%Vq'r%Vq!Y%Vq~P#-]OZ9SO~O!Y0rO!Z)Oq~O#[9WO!Y'ia!Z'ia~O!Y5UO!Z({i~P#CvOP[XZ[Xn[X|[X}[X!P[X!V[X!Y[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(h[X(o[X(p[X~O!d%TX#r%TX~P'#SO![1[O%c9[O~O'}TO(QUO([9aO~O!Y1gO!Z(|q~O!j9dO~O!j9eO~O!j9fO~O!j9fO~P%[O#[9iO!Y#dy!Z#dy~O!Y#dy!Z#dy~P#CvO!['ZO%c9nO~O#|#zy!Y#zy!Z#zy~P#CvOP$wiZ$win$wi}$wi!h$wi!i$wi!k$wi!o$wi#j$wi#k$wi#l$wi#m$wi#n$wi#o$wi#p$wi#q$wi#r$wi#t$wi#v$wi#x$wi#y$wi#|$wi(X$wi(h$wi!Y$wi!Z$wi~P&&xO|)zO!P){O(p%POP'baZ'baj'ban'ba}'ba!h'ba!i'ba!k'ba!o'ba#j'ba#k'ba#l'ba#m'ba#n'ba#o'ba#p'ba#q'ba#r'ba#t'ba#v'ba#x'ba#y'ba#|'ba(X'ba(h'ba(o'ba!Y'ba!Z'ba~O|)zO!P){OP'daZ'daj'dan'da}'da!h'da!i'da!k'da!o'da#j'da#k'da#l'da#m'da#n'da#o'da#p'da#q'da#r'da#t'da#v'da#x'da#y'da#|'da(X'da(h'da(o'da(p'da!Y'da!Z'da~O(o$}OP%ZiZ%Zij%Zin%Zi|%Zi}%Zi!P%Zi!h%Zi!i%Zi!k%Zi!o%Zi#j%Zi#k%Zi#l%Zi#m%Zi#n%Zi#o%Zi#p%Zi#q%Zi#r%Zi#t%Zi#v%Zi#x%Zi#y%Zi#|%Zi(X%Zi(h%Zi(p%Zi!Y%Zi!Z%Zi~O(p%POP%]iZ%]ij%]in%]i|%]i}%]i!P%]i!h%]i!i%]i!k%]i!o%]i#j%]i#k%]i#l%]i#m%]i#n%]i#o%]i#p%]i#q%]i#r%]i#t%]i#v%]i#x%]i#y%]i#|%]i(X%]i(h%]i(o%]i!Y%]i!Z%]i~O#|$ky!Y$ky!Z$ky~P#CvO#|#_y!Y#_y!Z#_y~P#CvO!d#uO!Y'^q!j'^q~O!Y/cO!j(uy~O!V'`q!Y'`q~P#-]O!V9wO~P#-]O!Y0rO!Z)Oy~O!Y5UO!Z({q~O![1[O%c:OO~O!j:RO~O!['ZO%c:WO~OP$wqZ$wqn$wq}$wq!h$wq!i$wq!k$wq!o$wq#j$wq#k$wq#l$wq#m$wq#n$wq#o$wq#p$wq#q$wq#r$wq#t$wq#v$wq#x$wq#y$wq#|$wq(X$wq(h$wq!Y$wq!Z$wq~P&&xO|)zO!P){O(p%POP'caZ'caj'can'ca}'ca!h'ca!i'ca!k'ca!o'ca#j'ca#k'ca#l'ca#m'ca#n'ca#o'ca#p'ca#q'ca#r'ca#t'ca#v'ca#x'ca#y'ca#|'ca(X'ca(h'ca(o'ca!Y'ca!Z'ca~O|)zO!P){OP'eaZ'eaj'ean'ea}'ea!h'ea!i'ea!k'ea!o'ea#j'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#t'ea#v'ea#x'ea#y'ea#|'ea(X'ea(h'ea(o'ea(p'ea!Y'ea!Z'ea~OP$yqZ$yqn$yq}$yq!h$yq!i$yq!k$yq!o$yq#j$yq#k$yq#l$yq#m$yq#n$yq#o$yq#p$yq#q$yq#r$yq#t$yq#v$yq#x$yq#y$yq#|$yq(X$yq(h$yq!Y$yq!Z$yq~P&&xOf%_!Z!Y%_!Z#[%_!Z#|%_!Z~P!0}O!Y'iq!Z'iq~P#CvO!Y#d!Z!Z#d!Z~P#CvO#g%_!ZP%_!ZZ%_!Z`%_!Zn%_!Z}%_!Z!Y%_!Z!h%_!Z!i%_!Z!k%_!Z!o%_!Z#j%_!Z#k%_!Z#l%_!Z#m%_!Z#n%_!Z#o%_!Z#p%_!Z#q%_!Z#r%_!Z#t%_!Z#v%_!Z#x%_!Z#y%_!Z'r%_!Z(X%_!Z(h%_!Z!j%_!Z!V%_!Z'p%_!Z#[%_!Zr%_!Z![%_!Z%c%_!Z!d%_!Z~P#-]OP%_!ZZ%_!Zn%_!Z}%_!Z!h%_!Z!i%_!Z!k%_!Z!o%_!Z#j%_!Z#k%_!Z#l%_!Z#m%_!Z#n%_!Z#o%_!Z#p%_!Z#q%_!Z#r%_!Z#t%_!Z#v%_!Z#x%_!Z#y%_!Z#|%_!Z(X%_!Z(h%_!Z!Y%_!Z!Z%_!Z~P&&xOr(]X~P1qO'|!lO~P!*fO!VeX!YeX#[eX~P'#SOP[XZ[Xn[X|[X}[X!P[X!Y[X!YeX!h[X!i[X!k[X!o[X#[[X#[eX#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(h[X(o[X(p[X~O!deX!j[X!jeX(heX~P'@vOP:cOQ:cORfOc<[Od!iOlkOn:cOokOpkOvkOx:cOz:cO!PWO!TkO!UkO![XO!f:fO!kZO!n:cO!o:cO!p:cO!r:gO!t:jO!w!hO$T!kO'{)YO'}TO(QUO(XVO(g[O(t<YO~O!Y:wO!Z$ma~Og%VOl%WOn$tOo$sOp$sOv%XOx%YOz;RO!P${O![$|O!f<aO!k$xO#f;XO$T%^O$o;TO$q;VO$t%_O'{(pO'}TO(QUO(X$uO(o$}O(p%PO~O#s)aO~P'ElO!Z[X!ZeX~P'@vO#g:kO~O!d#uO#g:kO~O#[:{O~O#r:pO~O#[;ZO!Y(mX!Z(mX~O#[:{O!Y(kX!Z(kX~O#g;[O~Of;^O~P!0}O#g;cO~O#g;dO~O!d#uO#g;eO~O!d#uO#g;[O~O#|;fO~P#CvO#g;gO~O#g;hO~O#g;mO~O#g;nO~O#g;oO~O#g;pO~O#|;qO~P!0}O#|;rO~P!0}O!i#P#Q#S#T#W#e#f#q(t$o$q$t%W%b%c%d%k%m%p%q%s%u~'vS#k!U't'|#lo#j#mn|'u$Y'u'{$[([~",goto:"$2p)SPPPPP)TPP)WP)iP*x.|PPPP5pPP6WPP<S?gP?zP?zPPP?zPAxP?zP?zP?zPA|PPBRPBlPGdPPPGhPPPPGhJiPPPJoKjPGhPMxPPPP!!WGhPPPGhPGhP!$fGhP!'z!(|!)VP!)y!)}!)yPPPPP!-Y!(|PP!-v!.pP!1dGhGh!1i!4s!9Y!9Y!=OPPP!=VGhPPPPPPPPPPP!@dP!AuPPGh!CSPGhPGhGhGhGhPGh!DfP!GnP!JrP!Jv!KQ!KU!KUP!GkP!KY!KYP!N^P!NbGhGh!Nh##k?zP?zP?z?zP#$v?z?z#'O?z#)k?z#+m?z?z#,[#.f#.f#.j#.r#.f#.zP#.fP?z#/d?z#3R?z?z5pPPP#6vPPP#7a#7aP#7aP#7w#7aPP#7}P#7tP#7t#8b#7t#8|#9S5m)W#9V)WP#9^#9^#9^P)WP)WP)WP)WPP)WP#9d#9gP#9g)WP#9kP#9nP)WP)WP)WP)WP)WP)W)WPP#9t#9z#:V#:]#:c#:i#:o#:}#;T#;Z#;e#;k#;u#<U#<[#<|#=`#=f#=l#=z#>a#@O#@^#@d#Ax#BW#Cr#DQ#DW#D^#Dd#Dn#Dt#Dz#EU#Eh#EnPPPPPPPPPP#EtPPPPPPP#Fi#Ip#KP#KW#K`PPPP$!d$%Z$+r$+u$+x$,q$,t$,w$-O$-WPP$-^$-b$.Y$/X$/]$/qPP$/u$/{$0PP$0S$0W$0Z$1P$1h$2P$2T$2W$2Z$2a$2d$2h$2lR!{RoqOXst!Z#c%j&m&o&p&r,h,m1w1zY!uQ'Z-Y1[5]Q%pvQ%xyQ&P|Q&e!VS'R!e-QQ'a!iS'g!r!xS*c$|*hQ+f%yQ+s&RQ,X&_Q-W'YQ-b'bQ-j'hQ/|*jQ1f,YR;Y:g%OdOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S,e,h,m-^-f-t-z.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3Z5Y5d5t5u5x6]7w7|8]8gS#p]:d!r)[$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]Q*u%ZQ+k%{Q,Z&bQ,b&jQ.c;QQ0h+^Q0l+`Q0w+lQ1n,`Q2{.[Q4v0rQ5k1gQ6i3PQ6u;RQ7h4wR8m6j&|kOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]t!nQ!r!u!x!y'R'Y'Z'g'h'i-Q-W-Y-j1[5]5_$v$si#u#w$c$d$x${%O%Q%[%]%a)u){)}*P*R*Y*`*p*q+]+`+w+z.Z.i/Z/j/k/m0Q0S0^1R1U1^3O3x4S4[4f4n4p5c6g7T7^7y8j8w9[9n:O:W:y:z:|:};O;P;S;T;U;V;W;X;_;`;a;b;c;d;g;h;i;j;k;l;m;n;q;r<Y<b<c<f<gQ&S|Q'P!eS'V%f-TQ+k%{Q,Z&bQ0]*yQ0w+lQ0|+rQ1m,_Q1n,`Q4v0rQ5P1OQ5k1gQ5n1iQ5o1lQ7h4wQ7k4|Q8U5qQ9V7lR9b8RrnOXst!V!Z#c%j&d&m&o&p&r,h,m1w1zR,]&f&v^OPXYstuvwz!Z!`!g!j!o#R#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O']'m(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<[<][#[WZ#V#Y'S'}!S%gm#g#h#k%b%e(W(b(c(d+Q+R+T,d,z-x.O.P.Q.S2P2w2x6R6dQ%sxQ%wyS%||&RQ&Y!TQ'^!hQ'`!iQ(k#rS*V$x*ZS+e%x%yQ+i%{Q,S&]Q,W&_S-a'a'bQ.^(lQ/g*WQ0p+fQ0v+lQ0x+mQ0{+qQ1a,TS1e,X,YQ2i-bQ3y/cQ4u0rQ4y0uQ5O0}Q5j1fQ7Q3zQ7g4wQ7j4{Q9R7fR9y9S!O$zi#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<c!S%uy!i!t%w%x%y'Q'`'a'b'f'p*b+e+f,}-a-b-i/t0p2b2i2p4^Q+_%sQ+x&VQ+{&WQ,V&_Q.](kQ1`,SU1d,W,X,YQ3Q.^Q5e1aS5i1e1fQ8Q5j#W<^#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<go<_:y:z:};P;T;V;X;`;b;d;h;j;l;n;rW%Ti%V*r<YS&V!Q&dQ&W!RQ&X!SR+v&T$w%Si#u#w$c$d$x${%O%Q%[%]%a)u){)}*P*R*Y*`*p*q+]+`+w+z.Z.i/Z/j/k/m0Q0S0^1R1U1^3O3x4S4[4f4n4p5c6g7T7^7y8j8w9[9n:O:W:y:z:|:};O;P;S;T;U;V;W;X;_;`;a;b;c;d;g;h;i;j;k;l;m;n;q;r<Y<b<c<f<gT)v$u)wV*v%Z;Q;RU'V!e%f-TS(y#y#zQ+p&OS.V(g(hQ1V+|Q4g0ZR7p5U&|kOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]$i$`c#X#d%n%o%q'|(S(n(u(})O)P)Q)R)S)T)U)V)W)X)Z)^)b)l+Z+o-O-m-r-w-y.h.n.r.t.u.v/V0_2W2Z2k2r3Y3_3`3a3b3c3d3e3f3g3h3i3j3k3n3o3t4k4s6U6[6a6o6p6y6z7r8a8e8n8t8u9k9{:S:e<PT#SV#T&}kOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]Q'T!eR2^-Qv!nQ!e!r!u!x!y'R'Y'Z'g'h'i-Q-W-Y-j1[5]5_S*b$|*hS/t*c*jQ/}*kQ1X,OQ4^/|R4a0PnqOXst!Z#c%j&m&o&p&r,h,m1w1zQ&t!^Q'q!wS(m#t:kQ+c%vQ,Q&YQ,R&[Q-_'_Q-l'jS.g(r;[S0`+O;eQ0n+dQ1Z,PQ2O,oQ2Q,pQ2Y,{Q2g-`Q2j-dS4l0a;oQ4q0oS4t0q;pQ6T2[Q6X2hQ6^2oQ7e4rQ8b6VQ8c6YQ8f6_R9h8_$d$_c#X#d%o%q'|(S(n(u(})O)P)Q)R)S)T)U)V)W)X)Z)^)b)l+Z+o-O-m-r-w-y.h.n.r.u.v/V0_2W2Z2k2r3Y3_3`3a3b3c3d3e3f3g3h3i3j3k3n3o3t4k4s6U6[6a6o6p6y6z7r8a8e8n8t8u9k9{:S:e<PS(j#o'dU*o%R(q3mS+Y%n.tQ2|0hQ6f2{Q8l6iR9o8m$d$^c#X#d%o%q'|(S(n(u(})O)P)Q)R)S)T)U)V)W)X)Z)^)b)l+Z+o-O-m-r-w-y.h.n.r.u.v/V0_2W2Z2k2r3Y3_3`3a3b3c3d3e3f3g3h3i3j3k3n3o3t4k4s6U6[6a6o6p6y6z7r8a8e8n8t8u9k9{:S:e<PS(i#o'dS({#z$_S+X%n.tS.W(h(jQ.w)]Q0e+YR2y.X&|kOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]S#p]:dQ&o!XQ&p!YQ&r![Q&s!]R1v,kQ'[!hQ+[%sQ-]'^S.Y(k+_Q2e-[W2}.].^0g0iQ6W2fU6e2z2|3QS8i6f6hS9m8k8lS:U9l9oQ:^:VR:a:_U!vQ'Z-YT5Z1[5]!Q_OXZ`st!V!Z#c#g%b%j&d&f&m&o&p&r(d,h,m.P1w1z]!pQ!r'Z-Y1[5]T#p]:d%Y{OPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&j&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S+^,e,h,m-^-f-t-z.[.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3P3Z5Y5d5t5u5x6]6j7w7|8]8gS(y#y#zS.V(g(h!s;v$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]Y!tQ'Z-Y1[5]Q'f!rS'p!u!xS'r!y5_S-i'g'hQ-k'iR2p-jQ'o!tS(`#f1qS-h'f'rQ/f*VQ/r*bQ2q-kQ4O/gS4X/s/}Q7P3yS7[4_4aQ8y7QR9Q7_Q#vbQ'n!tS(_#f1qS(a#l*}Q+P%cQ+a%tQ+g%zU-g'f'o'rQ-{(`Q/e*VQ/q*bQ/w*eQ0m+bQ1b,US2n-h-kQ2v.TS3}/f/gS4W/r/}Q4Z/vQ4]/xQ5g1cQ6`2qQ7O3yQ7S4OS7W4X4aQ7]4`Q8O5hS8x7P7QQ8|7XQ9O7[Q9_8PQ9u8yQ9v8}Q9x9QQ:Q9`Q:Y9wQ;y;tQ<U;}R<V<OV!vQ'Z-Y%YaOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&j&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S+^,e,h,m-^-f-t-z.[.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3P3Z5Y5d5t5u5x6]6j7w7|8]8gS#vz!j!r;s$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]R;y<[%YbOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&j&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S+^,e,h,m-^-f-t-z.[.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3P3Z5Y5d5t5u5x6]6j7w7|8]8gQ%cj!S%ty!i!t%w%x%y'Q'`'a'b'f'p*b+e+f,}-a-b-i/t0p2b2i2p4^S%zz!jQ+b%uQ,U&_W1c,V,W,X,YU5h1d1e1fS8P5i5jQ9`8Q!r;t$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]Q;}<ZR<O<[$|eOPXYstuvw!Z!`!g!o#R#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&m&o&p&r&v'O']'m(P(V(^(r(v(z)y+O+S+^,e,h,m-^-f-t-z.[.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3P3Z5Y5d5t5u5x6]6j7w7|8]8gY#aWZ#V#Y'}!S%gm#g#h#k%b%e(W(b(c(d+Q+R+T,d,z-x.O.P.Q.S2P2w2x6R6dQ,c&j!p;u$[$m)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]R;x'SS'W!e%fR2`-T%OdOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S,e,h,m-^-f-t-z.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3Z5Y5d5t5u5x6]7w7|8]8g!r)[$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]Q,b&jQ0h+^Q2{.[Q6i3PR8m6j!f$Uc#X%n'|(S(n(u)U)V)W)X)^)b+o-m-r-w-y.h.n/V0_2k2r3Y3k4k4s6[6a6o8e9k:e!T:r)Z)l-O.t2W2Z3_3g3h3i3j3n3t6U6p6y6z7r8a8n8t8u9{:S<P!b$Wc#X%n'|(S(n(u)W)X)^)b+o-m-r-w-y.h.n/V0_2k2r3Y3k4k4s6[6a6o8e9k:e!P:t)Z)l-O.t2W2Z3_3i3j3n3t6U6p6y6z7r8a8n8t8u9{:S<P!^$[c#X%n'|(S(n(u)^)b+o-m-r-w-y.h.n/V0_2k2r3Y3k4k4s6[6a6o8e9k:eQ3x/az<])Z)l-O.t2W2Z3_3n3t6U6p6y6z7r8a8n8t8u9{:S<PQ<b<dR<c<e&|kOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]S$nh$oR3q.z'TgOPWXYZhstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m$o%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.z.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]T$jf$pQ$hfS)e$k)iR)q$pT$if$pT)g$k)i'ThOPWXYZhstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$[$a$e$m$o%j%p%}&f&i&j&m&o&p&r&v'O'S']'m'}(P(V(^(r(v(z)n)y+O+S+^,e,h,m,y,|-^-f-t-z.[.l.s.z.{0[0a0q1_1o1p1r1t1w1z1|2]2m2s3P3Z3p5W5Y5d5t5u5x6S6]6j7w7|8]8g9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]T$nh$oQ$qhR)p$o%YjOPWXYZstuvw!Z!`!g!o#R#V#Y#c#n#t#x#{$O$P$Q$R$S$T$U$V$W$X$Y$a$e%j%p%}&f&i&j&m&o&p&r&v'O']'m'}(P(V(^(r(v(z)y+O+S+^,e,h,m-^-f-t-z.[.l.s0[0a0q1_1o1p1r1t1w1z1|2m2s3P3Z5Y5d5t5u5x6]6j7w7|8]8g!s<Z$[$m'S)n,y,|.{2]3p5W6S9W9i:c:f:g:j:k:l:m:n:o:p:q:r:s:t:u:v:w:{;Y;Z;[;^;e;f;o;p<]#clOPXZst!Z!`!o#R#c#n#{$m%j&f&i&j&m&o&p&r&v'O'](z)n+S+^,e,h,m-^.[.{0[1_1o1p1r1t1w1z1|3P3p5Y5d5t5u5x6j7w7|8]!O%Ri#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<c#W(q#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<gQ*z%_Q/W)zo3m:y:z:};P;T;V;X;`;b;d;h;j;l;n;r!O$yi#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<cQ*[$zS*e$|*hQ*{%`Q/x*f#W;{#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<gn;|:y:z:};P;T;V;X;`;b;d;h;j;l;n;rQ<Q<^Q<R<_Q<S<`R<T<a!O%Ri#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<c#W(q#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<go3m:y:z:};P;T;V;X;`;b;d;h;j;l;n;rnoOXst!Z#c%j&m&o&p&r,h,m1w1zQ*_${Q,v&yQ,w&{R4R/k$v%Si#u#w$c$d$x${%O%Q%[%]%a)u){)}*P*R*Y*`*p*q+]+`+w+z.Z.i/Z/j/k/m0Q0S0^1R1U1^3O3x4S4[4f4n4p5c6g7T7^7y8j8w9[9n:O:W:y:z:|:};O;P;S;T;U;V;W;X;_;`;a;b;c;d;g;h;i;j;k;l;m;n;q;r<Y<b<c<f<gQ+y&WQ1T+{Q5S1SR7o5TT*g$|*hS*g$|*hT5[1[5]S/v*d5YT4`0O7wQ+a%tQ/w*eQ0m+bQ1b,UQ5g1cQ8O5hQ9_8PR:Q9`!O%Oi#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<cr)}$v(s*O*n*|/i0U0V3W4P4j6}7`9t;z<W<XS0Q*m0R#W:|#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<gn:}:y:z:};P;T;V;X;`;b;d;h;j;l;n;r!^;_(o)`*U*^._.b.f/S/X/a/n0f1Q1S3T4Q4U5R5T6k6n7U7Y7b7d8{9P:X<d<e`;`3l6q6t6x8o9p9s:bS;i.a3UT;j6s8r!O%Qi#w%O%Q%[%]%a)}*P*Y*p*q.i/j0Q0S0^3x4f8w<Y<b<cv*P$v(s*Q*m*|/]/i0U0V3W4P4b4j6}7`9t;z<W<XS0S*n0T#W;O#u$c$d$x${)u){*R*`+]+`+w+z.Z/Z/k/m1R1U1^3O4S4[4n4p5c6g7T7^7y8j9[9n:O:W:|;O;S;U;W;_;a;c;g;i;k;m;q<f<gn;P:y:z:};P;T;V;X;`;b;d;h;j;l;n;r!b;a(o)`*U*^.`.a.f/S/X/a/n0f1Q1S3R3T4Q4U5R5T6k6l6n7U7Y7b7d8{9P:X<d<ed;b3l6r6s6x8o8p9p9q9s:bS;k.b3VT;l6t8srnOXst!V!Z#c%j&d&m&o&p&r,h,m1w1zQ&a!UR,e&jrnOXst!V!Z#c%j&d&m&o&p&r,h,m1w1zR&a!UQ+}&XR1P+vsnOXst!V!Z#c%j&d&m&o&p&r,h,m1w1zQ1],SS5b1`1aU7x5`5a5eS9Z7z7{S9|9Y9]Q:Z9}R:`:[Q&h!VR,^&dR5n1iS%||&RR0x+mQ&m!WR,h&nR,n&sT1x,m1zR,r&tQ,q&tR2R,rQ't!zR-n'tSsOtQ#cXT%ms#cQ!}TR'v!}Q#QUR'x#QQ)w$uR/T)wQ#TVR'z#TQ#WWU(Q#W(R-uQ(R#XR-u(SQ-R'TR2_-RQ.j(sR3X.jQ.m(uS3[.m3]R3].nQ-Y'ZR2c-YY!rQ'Z-Y1[5]R'e!rS#^W%eU(X#^(Y-vQ(Y#_R-v(TQ-U'WR2a-Ut`OXst!V!Z#c%j&d&f&m&o&p&r,h,m1w1zS#gZ%bU#q`#g.PR.P(dQ(e#iQ-|(aW.U(e-|2t6bQ2t-}R6b2uQ)i$kR.|)iQ$ohR)o$oQ$bcU)_$b-q:xQ-q:eR:x)lQ/d*VW3{/d3|7R8zU3|/e/f/gS7R3}4OR8z7S$X)|$v(o(s)`*U*^*m*n*w*x*|.a.b.d.e.f/S/X/]/_/a/i/n0U0V0f1Q1S3R3S3T3W3l4P4Q4U4b4d4j5R5T6k6l6m6n6s6t6v6w6x6}7U7Y7`7b7d8o8p8q8{9P9p9q9r9s9t:X:b;z<W<X<d<eQ/l*^U4T/l4V7VQ4V/nR7V4UQ*h$|R/z*hr*O$v(s*m*n*|/i0U0V3W4P4j6}7`9t;z<W<X!^._(o)`*U*^.a.b.f/S/X/a/n0f1Q1S3T4Q4U5R5T6k6n7U7Y7b7d8{9P:X<d<eU/^*O._6qa6q3l6s6t6x8o9p9s:bQ0R*mQ3U.aU4c0R3U8rR8r6sv*Q$v(s*m*n*|/]/i0U0V3W4P4b4j6}7`9t;z<W<X!b.`(o)`*U*^.a.b.f/S/X/a/n0f1Q1S3R3T4Q4U5R5T6k6l6n7U7Y7b7d8{9P:X<d<eU/`*Q.`6re6r3l6s6t6x8o8p9p9q9s:bQ0T*nQ3V.bU4e0T3V8sR8s6tQ*s%UR0X*sQ4o0fR7c4oQ+U%hR0d+UQ5V1VS7q5V9XR9X7rQ,P&YR1Y,PQ5]1[R7u5]Q1h,ZS5l1h8SR8S5nQ0s+iW4x0s4z7i9TQ4z0vQ7i4yR9T7jQ+n%|R0y+nQ1z,mR5|1zYrOXst#cQ&q!ZQ+W%jQ,g&mQ,i&oQ,j&pQ,l&rQ1u,hS1x,m1zR5{1wQ%lpQ&u!_Q&x!aQ&z!bQ&|!cQ'l!tQ+V%iQ+c%vQ+u&SQ,]&hQ,t&wW-e'f'n'o'rQ-l'jQ/y*gQ0n+dS1k,^,aQ2S,sQ2T,vQ2U,wQ2j-dW2l-g-h-k-mQ4q0oQ4}0|Q5Q1QQ5f1bQ5p1mQ5z1vU6Z2k2n2qQ6^2oQ7e4rQ7m5PQ7n5RQ7t5[Q7}5gQ8T5oS8d6[6`Q8f6_Q9U7kQ9^8OQ9c8UQ9j8eQ9z9VQ:P9_Q:T9kR:]:QQ%vyQ'_!iQ'j!tU+d%w%x%yQ,{'QU-`'`'a'bS-d'f'pQ/p*bS0o+e+fQ2[,}S2h-a-bQ2o-iQ4Y/tQ4r0pQ6V2bQ6Y2iQ6_2pR7Z4^S$wi<YR*t%VU%Ui%V<YR0W*rQ$viS(o#u+`Q(s#wS)`$c$dQ*U$xQ*^${Q*m%OQ*n%QQ*w%[Q*x%]Q*|%aQ.a:|Q.b;OQ.d;SQ.e;UQ.f;WQ/S)uS/X){/ZQ/])}Q/_*PQ/a*RQ/i*YQ/n*`Q0U*pQ0V*qh0f+].Z1^3O5c6g7y8j9[9n:O:WQ1Q+wQ1S+zQ3R;_Q3S;aQ3T;cQ3W.iS3l:y:zQ4P/jQ4Q/kQ4U/mQ4b0QQ4d0SQ4j0^Q5R1RQ5T1UQ6k;gQ6l;iQ6m;kQ6n;mQ6s:}Q6t;PQ6v;TQ6w;VQ6x;XQ6}3xQ7U4SQ7Y4[Q7`4fQ7b4nQ7d4pQ8o;dQ8p;`Q8q;bQ8{7TQ9P7^Q9p;hQ9q;jQ9r;lQ9s;nQ9t8wQ:X;qQ:b;rQ;z<YQ<W<bQ<X<cQ<d<fR<e<gnpOXst!Z#c%j&m&o&p&r,h,m1w1zQ!fPS#eZ#nQ&w!`U'c!o5Y7wQ'y#RQ(|#{Q)m$mS,a&f&iQ,f&jQ,s&vQ,x'OQ-[']Q.p(zQ/Q)nQ0b+SQ0i+^Q1s,eQ2f-^Q2|.[Q3s.{Q4h0[Q5a1_Q5r1oQ5s1pQ5w1rQ5y1tQ6O1|Q6f3PQ6{3pQ7{5dQ8X5tQ8Y5uQ8[5xQ8l6jQ9]7|R9g8]#WcOPXZst!Z!`!o#c#n#{%j&f&i&j&m&o&p&r&v'O'](z+S+^,e,h,m-^.[0[1_1o1p1r1t1w1z1|3P5Y5d5t5u5x6j7w7|8]Q#XWQ#dYQ%nuQ%ovS%qw!gS'|#V(PQ(S#YQ(n#tQ(u#xQ(}$OQ)O$PQ)P$QQ)Q$RQ)R$SQ)S$TQ)T$UQ)U$VQ)V$WQ)W$XQ)X$YQ)Z$[Q)^$aQ)b$eW)l$m)n.{3pQ+Z%pQ+o%}S-O'S2]Q-m'mS-r'}-tQ-w(VQ-y(^Q.h(rQ.n(vQ.r:cQ.t:fQ.u:gQ.v:jQ/V)yQ0_+OQ2W,yQ2Z,|Q2k-fQ2r-zQ3Y.lQ3_:kQ3`:lQ3a:mQ3b:nQ3c:oQ3d:pQ3e:qQ3f:rQ3g:sQ3h:tQ3i:uQ3j:vQ3k.sQ3n:{Q3o;YQ3t:wQ4k0aQ4s0qQ6U;ZQ6[2mQ6a2sQ6o3ZQ6p;[Q6y;^Q6z;eQ7r5WQ8a6SQ8e6]Q8n;fQ8t;oQ8u;pQ9k8gQ9{9WQ:S9iQ:e#RR<P<]R#ZWR'U!eY!tQ'Z-Y1[5]S'Q!e-QQ'f!rS'p!u!xS'r!y5_S,}'R'YS-i'g'hQ-k'iQ2b-WR2p-jR(t#wR(w#xQ!fQT-X'Z-Y]!qQ!r'Z-Y1[5]Q#o]R'd:dT#jZ%bS#iZ%bS%hm,dU(a#g#h#kS-}(b(cQ.R(dQ0c+TQ2u.OU2v.P.Q.SS6c2w2xR8h6d`#]W#V#Y%e'}(W+Q-xr#fZm#g#h#k%b(b(c(d+T.O.P.Q.S2w2x6dQ1q,dQ2X,zQ6Q2PQ8`6RT;w'S+RT#`W%eS#_W%eS(O#V(WS(T#Y+QS-P'S+RT-s'}-xT'X!e%fQ$kfR)s$pT)h$k)iR3r.zT*X$x*ZR*a${Q0g+]Q2z.ZQ5`1^Q6h3OQ7z5cQ8k6gQ9Y7yQ9l8jQ9}9[Q:V9nQ:[:OR:_:WnqOXst!Z#c%j&m&o&p&r,h,m1w1zQ&g!VR,]&dtmOXst!U!V!Z#c%j&d&m&o&p&r,h,m1w1zR,d&jT%im,dR1W+|R,[&bQ&Q|R+t&RR+j%{T&k!W&nT&l!W&nT1y,m1z",nodeNames:"⚠ ArithOp ArithOp JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . ?. PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewExpression new TypeArgList CompareOp < ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies in const CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast ArrowFunction TypeParamList SequenceExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:371,context:Cd,nodeProps:[["isolate",-8,4,5,13,33,35,48,50,52,""],["group",-26,8,16,18,65,201,205,209,210,212,215,218,228,230,236,238,240,242,245,251,257,259,261,263,265,267,268,"Statement",-32,12,13,28,31,32,38,48,51,52,54,59,67,75,79,81,83,84,106,107,116,117,134,137,139,140,141,142,144,145,164,165,167,"Expression",-23,27,29,33,37,39,41,168,170,172,173,175,176,177,179,180,181,183,184,185,195,197,199,200,"Type",-3,87,99,105,"ClassItem"],["openedBy",22,"<",34,"InterpolationStart",53,"[",57,"{",72,"(",157,"JSXStartCloseTag"],["closedBy",23,">",36,"InterpolationEnd",47,"]",58,"}",73,")",162,"JSXEndTag"]],propSources:[jd],skippedNodes:[0,4,5,271],repeatNodeCount:37,tokenData:"$Fj(CSR!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#8g!R![#:v![!]#Gv!]!^#IS!^!_#J^!_!`#Ns!`!a$#_!a!b$(l!b!c$,k!c!}Er!}#O$-u#O#P$/P#P#Q$4h#Q#R$5r#R#SEr#S#T$7P#T#o$8Z#o#p$<k#p#q$=a#q#r$>q#r#s$?}#s$f%Z$f$g+g$g#BYEr#BY#BZ$AX#BZ$ISEr$IS$I_$AX$I_$I|Er$I|$I}$Dd$I}$JO$Dd$JO$JTEr$JT$JU$AX$JU$KVEr$KV$KW$AX$KW&FUEr&FU&FV$AX&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$AX?HUOEr(n%d_$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$f&j(R!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(R!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$f&j(OpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(OpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z(CS+rq$f&j(Op(R!b't(;dOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z(CS.ST(P#S$f&j'u(;dO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c(CS.n_$f&j(Op(R!b'u(;dOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`/x`$f&j!o$Ip(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S1V`#t$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S2d_#t$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/|3l_'}$(n$f&j(R!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$f&j(R!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$f&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$a`$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$a``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$a`$f&j(R!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(R!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$a`(R!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k#%|:hh$f&j(Op(R!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__VS$f&j(Op(R!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]VS$f&j(R!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXVS$f&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSVSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWVS(R!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]VS$f&j(OpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWVS(OpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYVS(Op(R!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S#%|C}i$f&j(g!L^(Op(R!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr#%|EoP;=`<%lCr(CSFRk$f&j(Op(R!b$Y#t'{&;d([!LYOY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$f&j(Op(R!b$Y#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv(CSJPP;=`<%lEr%#SJ_`$f&j(Op(R!b#l$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#SKl_$f&j$O$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&COLva(p&;`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#SNW`$f&j#x$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/|! c_(Q$)`$f&j(OpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$f&j(OpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$f&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$a`$f&j(OpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(OpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$a`(OpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b(*Q!'t_!k(!b$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'l!)O_!jM|$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+h!*[b$f&j(Op(R!b'|#)d#m$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S!+o`$f&j(Op(R!b#j$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&-O!,|`$f&j(Op(R!bn&%`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&C[!.Z_!Y&;l$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS!/ec$f&j(Op(R!b|'<nOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'d!0ya$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'d!2Z_!XMt$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!3eg$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!5Vg$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!6wc$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!8_c$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS!9uf$f&j(Op(R!b#k$IdOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Kpxz!;Zz{#,f{!P!;Z!P!Q#-{!Q!^!;Z!^!_#'Z!_!`#5k!`!a#7Q!a!}!;Z!}#O#*}#O#P!Dj#P#o!;Z#o#p#'Z#p;'S!;Z;'S;=`#,`<%lO!;Z(r!;fb$f&j(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Kpx!P!;Z!P!Q#%Z!Q!^!;Z!^!_#'Z!_!}!;Z!}#O#*}#O#P!Dj#P#o!;Z#o#p#'Z#p;'S!;Z;'S;=`#,`<%lO!;Z(Q!<w`$f&j(R!b!USOY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eb!Q!^!<n!^!_!GY!_!}!<n!}#O!Ja#O#P!Dj#P#o!<n#o#p!GY#p;'S!<n;'S;=`!Kj<%lO!<n&n!>Q^$f&j!USOY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@Y!_!}!=y!}#O!Bw#O#P!Dj#P#o!=y#o#p!@Y#p;'S!=y;'S;=`!E[<%lO!=y&n!?Ta$f&j!USO!^&c!_#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&cS!@_X!USOY!@YZ!P!@Y!P!Q!@z!Q!}!@Y!}#O!Ac#O#P!Bb#P;'S!@Y;'S;=`!Bq<%lO!@YS!APU!US#Z#[!@z#]#^!@z#a#b!@z#g#h!@z#i#j!@z#m#n!@zS!AfVOY!AcZ#O!Ac#O#P!A{#P#Q!@Y#Q;'S!Ac;'S;=`!B[<%lO!AcS!BOSOY!AcZ;'S!Ac;'S;=`!B[<%lO!AcS!B_P;=`<%l!AcS!BeSOY!@YZ;'S!@Y;'S;=`!Bq<%lO!@YS!BtP;=`<%l!@Y&n!B|[$f&jOY!BwYZ&cZ!^!Bw!^!_!Ac!_#O!Bw#O#P!Cr#P#Q!=y#Q#o!Bw#o#p!Ac#p;'S!Bw;'S;=`!Dd<%lO!Bw&n!CwX$f&jOY!BwYZ&cZ!^!Bw!^!_!Ac!_#o!Bw#o#p!Ac#p;'S!Bw;'S;=`!Dd<%lO!Bw&n!DgP;=`<%l!Bw&n!DoX$f&jOY!=yYZ&cZ!^!=y!^!_!@Y!_#o!=y#o#p!@Y#p;'S!=y;'S;=`!E[<%lO!=y&n!E_P;=`<%l!=y(Q!Eki$f&j(R!b!USOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#Z&}#Z#[!Eb#[#]&}#]#^!Eb#^#a&}#a#b!Eb#b#g&}#g#h!Eb#h#i&}#i#j!Eb#j#m&}#m#n!Eb#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!f!GaZ(R!b!USOY!GYZw!GYwx!@Yx!P!GY!P!Q!HS!Q!}!GY!}#O!Ic#O#P!Bb#P;'S!GY;'S;=`!JZ<%lO!GY!f!HZb(R!b!USOY'}Zw'}x#O'}#P#Z'}#Z#[!HS#[#]'}#]#^!HS#^#a'}#a#b!HS#b#g'}#g#h!HS#h#i'}#i#j!HS#j#m'}#m#n!HS#n;'S'};'S;=`(f<%lO'}!f!IhX(R!bOY!IcZw!Icwx!Acx#O!Ic#O#P!A{#P#Q!GY#Q;'S!Ic;'S;=`!JT<%lO!Ic!f!JWP;=`<%l!Ic!f!J^P;=`<%l!GY(Q!Jh^$f&j(R!bOY!JaYZ&cZw!Jawx!Bwx!^!Ja!^!_!Ic!_#O!Ja#O#P!Cr#P#Q!<n#Q#o!Ja#o#p!Ic#p;'S!Ja;'S;=`!Kd<%lO!Ja(Q!KgP;=`<%l!Ja(Q!KmP;=`<%l!<n'`!Ky`$f&j(Op!USOY!KpYZ&cZr!Kprs!=ys!P!Kp!P!Q!L{!Q!^!Kp!^!_!Ns!_!}!Kp!}#O##z#O#P!Dj#P#o!Kp#o#p!Ns#p;'S!Kp;'S;=`#%T<%lO!Kp'`!MUi$f&j(Op!USOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#Z(r#Z#[!L{#[#](r#]#^!L{#^#a(r#a#b!L{#b#g(r#g#h!L{#h#i(r#i#j!L{#j#m(r#m#n!L{#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rt!NzZ(Op!USOY!NsZr!Nsrs!@Ys!P!Ns!P!Q# m!Q!}!Ns!}#O#!|#O#P!Bb#P;'S!Ns;'S;=`##t<%lO!Nst# tb(Op!USOY)rZr)rs#O)r#P#Z)r#Z#[# m#[#])r#]#^# m#^#a)r#a#b# m#b#g)r#g#h# m#h#i)r#i#j# m#j#m)r#m#n# m#n;'S)r;'S;=`*Z<%lO)rt##RX(OpOY#!|Zr#!|rs!Acs#O#!|#O#P!A{#P#Q!Ns#Q;'S#!|;'S;=`##n<%lO#!|t##qP;=`<%l#!|t##wP;=`<%l!Ns'`#$R^$f&j(OpOY##zYZ&cZr##zrs!Bws!^##z!^!_#!|!_#O##z#O#P!Cr#P#Q!Kp#Q#o##z#o#p#!|#p;'S##z;'S;=`#$}<%lO##z'`#%QP;=`<%l##z'`#%WP;=`<%l!Kp(r#%fk$f&j(Op(R!b!USOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#Z%Z#Z#[#%Z#[#]%Z#]#^#%Z#^#a%Z#a#b#%Z#b#g%Z#g#h#%Z#h#i%Z#i#j#%Z#j#m%Z#m#n#%Z#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#W#'d](Op(R!b!USOY#'ZZr#'Zrs!GYsw#'Zwx!Nsx!P#'Z!P!Q#(]!Q!}#'Z!}#O#)w#O#P!Bb#P;'S#'Z;'S;=`#*w<%lO#'Z#W#(fe(Op(R!b!USOY*gZr*grs'}sw*gwx)rx#O*g#P#Z*g#Z#[#(]#[#]*g#]#^#(]#^#a*g#a#b#(]#b#g*g#g#h#(]#h#i*g#i#j#(]#j#m*g#m#n#(]#n;'S*g;'S;=`+Z<%lO*g#W#*OZ(Op(R!bOY#)wZr#)wrs!Icsw#)wwx#!|x#O#)w#O#P!A{#P#Q#'Z#Q;'S#)w;'S;=`#*q<%lO#)w#W#*tP;=`<%l#)w#W#*zP;=`<%l#'Z(r#+W`$f&j(Op(R!bOY#*}YZ&cZr#*}rs!Jasw#*}wx##zx!^#*}!^!_#)w!_#O#*}#O#P!Cr#P#Q!;Z#Q#o#*}#o#p#)w#p;'S#*};'S;=`#,Y<%lO#*}(r#,]P;=`<%l#*}(r#,cP;=`<%l!;Z(CS#,sb$f&j(Op(R!b'v(;d!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Kpx!P!;Z!P!Q#%Z!Q!^!;Z!^!_#'Z!_!}!;Z!}#O#*}#O#P!Dj#P#o!;Z#o#p#'Z#p;'S!;Z;'S;=`#,`<%lO!;Z(CS#.W_$f&j(Op(R!bS(;dOY#-{YZ&cZr#-{rs#/Vsw#-{wx#2gx!^#-{!^!_#4f!_#O#-{#O#P#0X#P#o#-{#o#p#4f#p;'S#-{;'S;=`#5e<%lO#-{(Bb#/`]$f&j(R!bS(;dOY#/VYZ&cZw#/Vwx#0Xx!^#/V!^!_#1j!_#O#/V#O#P#0X#P#o#/V#o#p#1j#p;'S#/V;'S;=`#2a<%lO#/V(AO#0`X$f&jS(;dOY#0XYZ&cZ!^#0X!^!_#0{!_#o#0X#o#p#0{#p;'S#0X;'S;=`#1d<%lO#0X(;d#1QSS(;dOY#0{Z;'S#0{;'S;=`#1^<%lO#0{(;d#1aP;=`<%l#0{(AO#1gP;=`<%l#0X(<v#1qW(R!bS(;dOY#1jZw#1jwx#0{x#O#1j#O#P#0{#P;'S#1j;'S;=`#2Z<%lO#1j(<v#2^P;=`<%l#1j(Bb#2dP;=`<%l#/V(Ap#2p]$f&j(OpS(;dOY#2gYZ&cZr#2grs#0Xs!^#2g!^!_#3i!_#O#2g#O#P#0X#P#o#2g#o#p#3i#p;'S#2g;'S;=`#4`<%lO#2g(<U#3pW(OpS(;dOY#3iZr#3irs#0{s#O#3i#O#P#0{#P;'S#3i;'S;=`#4Y<%lO#3i(<U#4]P;=`<%l#3i(Ap#4cP;=`<%l#2g(=h#4oY(Op(R!bS(;dOY#4fZr#4frs#1jsw#4fwx#3ix#O#4f#O#P#0{#P;'S#4f;'S;=`#5_<%lO#4f(=h#5bP;=`<%l#4f(CS#5hP;=`<%l#-{%#W#5xb$f&j$O$Id(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Kpx!P!;Z!P!Q#%Z!Q!^!;Z!^!_#'Z!_!}!;Z!}#O#*}#O#P!Dj#P#o!;Z#o#p#'Z#p;'S!;Z;'S;=`#,`<%lO!;Z+h#7_b$W#t$f&j(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Kpx!P!;Z!P!Q#%Z!Q!^!;Z!^!_#'Z!_!}!;Z!}#O#*}#O#P!Dj#P#o!;Z#o#p#'Z#p;'S!;Z;'S;=`#,`<%lO!;Z$/l#8rp$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#:v![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#:v#S#U%Z#U#V#>Q#V#X%Z#X#Y!4|#Y#b%Z#b#c#<v#c#d#AY#d#l%Z#l#m#D[#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#;Rk$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#:v![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#:v#S#X%Z#X#Y!4|#Y#b%Z#b#c#<v#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#=R_$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#>Zd$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#?i!R!S#?i!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#?i#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#?tf$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#?i!R!S#?i!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#?i#S#b%Z#b#c#<v#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Acc$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#Bn!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#Bn#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Bye$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#Bn!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#Bn#S#b%Z#b#c#<v#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Deg$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#E|![!^%Z!^!_*g!_!c%Z!c!i#E|!i#O%Z#O#P&c#P#R%Z#R#S#E|#S#T%Z#T#Z#E|#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#FXi$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#E|![!^%Z!^!_*g!_!c%Z!c!i#E|!i#O%Z#O#P&c#P#R%Z#R#S#E|#S#T%Z#T#Z#E|#Z#b%Z#b#c#<v#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%Gh#HT_!d$b$f&j#|%<f(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#I__`l$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(@^#Jk^g!*v!h'.r(Op(R!b(tSOY*gZr*grs'}sw*gwx)rx!P*g!P!Q#Kg!Q!^*g!^!_#L]!_!`#M}!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#KpX$h&j(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh#LfZ#n$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#MX!`#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh#MbX$O$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh#NWX#o$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g%Gh$ Oa#[%?x$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$!T!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#W$!`_#g$Ih$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%Gh$#nafBf#o$Id$c#|$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$$s!`!a$%}!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$%O_#o$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$&Ya#n$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$'_!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$'j`#n$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+h$(wc(h$Ip$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P$*S!P!^%Z!^!_*g!_!a%Z!a!b$+^!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+`$*__}'#p$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$+i`$f&j#y$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&^$,v_!{!Ln$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(@^$.Q_!P(8n$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/UZ$f&jO!^$/w!^!_$0_!_#i$/w#i#j$0d#j#l$/w#l#m$2V#m#o$/w#o#p$0_#p;'S$/w;'S;=`$4b<%lO$/w(n$0OT^#S$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0dO^#S(n$0i[$f&jO!Q&c!Q![$1_![!^&c!_!c&c!c!i$1_!i#T&c#T#Z$1_#Z#o&c#o#p$3u#p;'S&c;'S;=`&w<%lO&c(n$1dZ$f&jO!Q&c!Q![$2V![!^&c!_!c&c!c!i$2V!i#T&c#T#Z$2V#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2[Z$f&jO!Q&c!Q![$2}![!^&c!_!c&c!c!i$2}!i#T&c#T#Z$2}#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3SZ$f&jO!Q&c!Q![$/w![!^&c!_!c&c!c!i$/w!i#T&c#T#Z$/w#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$3xR!Q![$4R!c!i$4R#T#Z$4R#S$4US!Q![$4R!c!i$4R#T#Z$4R#q#r$0_(n$4eP;=`<%l$/w!2r$4s_!V!+S$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$5}`#v$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&,v$7[_$f&j(Op(R!b(X&%WOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS$8jk$f&j(Op(R!b'{&;d$[#t([!LYOY%ZYZ&cZr%Zrs&}st%Ztu$8Zuw%Zwx(rx}%Z}!O$:_!O!Q%Z!Q![$8Z![!^%Z!^!_*g!_!c%Z!c!}$8Z!}#O%Z#O#P&c#P#R%Z#R#S$8Z#S#T%Z#T#o$8Z#o#p*g#p$g%Z$g;'S$8Z;'S;=`$<e<%lO$8Z+d$:jk$f&j(Op(R!b$[#tOY%ZYZ&cZr%Zrs&}st%Ztu$:_uw%Zwx(rx}%Z}!O$:_!O!Q%Z!Q![$:_![!^%Z!^!_*g!_!c%Z!c!}$:_!}#O%Z#O#P&c#P#R%Z#R#S$:_#S#T%Z#T#o$:_#o#p*g#p$g%Z$g;'S$:_;'S;=`$<_<%lO$:_+d$<bP;=`<%l$:_(CS$<hP;=`<%l$8Z!5p$<tX![!3l(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g&CO$=la(o&;`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+^#q;'S%Z;'S;=`+a<%lO%Z%#`$?O_!Z$I`r`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(r$@Y_!pS$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS$Aj|$f&j(Op(R!b't(;d$Y#t'{&;d([!LYOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$AX#BZ$ISEr$IS$I_$AX$I_$JTEr$JT$JU$AX$JU$KVEr$KV$KW$AX$KW&FUEr&FU&FV$AX&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$AX?HUOEr(CS$Duk$f&j(Op(R!b'u(;d$Y#t'{&;d([!LYOY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[Ad,Rd,Vd,2,3,4,5,6,7,8,9,10,11,12,13,Yd,new Gh("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOt~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!R~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(Z~~",141,332),new Gh("j~RQYZXz{^~^O'x~~aP!P!Qd~iO'y~~",25,315)],topRules:{Script:[0,6],SingleExpression:[1,269],SingleClassItem:[2,270]},dialects:{jsx:0,ts:14602},dynamicPrecedences:{69:1,79:1,81:1,165:1,193:1},specialized:[{term:319,get:t=>qd[t]||-1},{term:334,get:t=>_d[t]||-1},{term:70,get:t=>Ed[t]||-1}],tokenPrec:14626}),Dd=[RO("function ${name}(${params}) {\n\t${}\n}",{label:"function",detail:"definition",type:"keyword"}),RO("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}",{label:"for",detail:"loop",type:"keyword"}),RO("for (let ${name} of ${collection}) {\n\t${}\n}",{label:"for",detail:"of loop",type:"keyword"}),RO("do {\n\t${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),RO("while (${}) {\n\t${}\n}",{label:"while",detail:"loop",type:"keyword"}),RO("try {\n\t${}\n} catch (${error}) {\n\t${}\n}",{label:"try",detail:"/ catch block",type:"keyword"}),RO("if (${}) {\n\t${}\n}",{label:"if",detail:"block",type:"keyword"}),RO("if (${}) {\n\t${}\n} else {\n\t${}\n}",{label:"if",detail:"/ else block",type:"keyword"}),RO("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}",{label:"class",detail:"definition",type:"keyword"}),RO('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),RO('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],zd=Dd.concat([RO("interface ${name} {\n\t${}\n}",{label:"interface",detail:"definition",type:"keyword"}),RO("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),RO("enum ${name} {\n\t${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),Gd=new T,Id=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function Bd(t){return(e,i)=>{let n=e.node.getChild("VariableDefinition");return n&&i(n,t),!0}}const Nd=["FunctionDeclaration"],Ud={FunctionDeclaration:Bd("function"),ClassDeclaration:Bd("class"),ClassExpression:()=>!0,EnumDeclaration:Bd("constant"),TypeAliasDeclaration:Bd("type"),NamespaceDeclaration:Bd("namespace"),VariableDefinition(t,e){t.matchContext(Nd)||e(t,"variable")},TypeDefinition(t,e){e(t,"type")},__proto__:null};function Ld(t,e){let i=Gd.get(e);if(i)return i;let n=[],s=!0;function r(e,i){let s=t.sliceString(e.from,e.to);n.push({label:s,type:i})}return e.cursor(c.IncludeAnonymous).iterate((e=>{if(s)s=!1;else if(e.name){let t=Ud[e.name];if(t&&t(e,r)||Id.has(e.name))return!1}else if(e.to-e.from>8192){for(let i of Ld(t,e.node))n.push(i);return!1}})),Gd.set(e,n),n}const Fd=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,Hd=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName",".","?."];function Jd(t){let e=Ea(t.state).resolveInner(t.pos,-1);if(Hd.indexOf(e.name)>-1)return null;let i="VariableName"==e.name||e.to-e.from<20&&Fd.test(t.state.sliceDoc(e.from,e.to));if(!i&&!t.explicit)return null;let n=[];for(let i=e;i;i=i.parent)Id.has(i.name)&&(n=n.concat(Ld(t.state.doc,i)));return{options:n,from:i?e.from:t.pos,validFor:Fd}}function Kd(t,e,i){var n;let s=[];for(;;){let r,o=e.firstChild;if("VariableName"==(null==o?void 0:o.name))return s.push(t(o)),{path:s.reverse(),name:i};if("MemberExpression"!=(null==o?void 0:o.name)||"PropertyName"!=(null===(n=r=o.lastChild)||void 0===n?void 0:n.name))return null;s.push(t(r)),e=o}}function tp(t){let e=e=>t.state.doc.sliceString(e.from,e.to),i=Ea(t.state).resolveInner(t.pos,-1);return"PropertyName"==i.name?Kd(e,i.parent,e(i)):"."!=i.name&&"?."!=i.name||"MemberExpression"!=i.parent.name?Hd.indexOf(i.name)>-1?null:"VariableName"==i.name||i.to-i.from<20&&Fd.test(e(i))?{path:[],name:e(i)}:"MemberExpression"==i.name?Kd(e,i,""):t.explicit?{path:[],name:""}:null:Kd(e,i.parent,"")}const ep=_a.define({name:"javascript",parser:Md.configure({props:[rl.add({IfStatement:dl({except:/^\s*({|else\b)/}),TryStatement:dl({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:fl,SwitchBody:t=>{let e=t.textAfter,i=/^\s*\}/.test(e),n=/^\s*(case|default)\b/.test(e);return t.baseIndent+(i?0:n?1:2)*t.unit},Block:Ol({closing:"}"}),ArrowFunction:t=>t.baseIndent+t.unit,"TemplateString BlockComment":()=>null,"Statement Property":dl({except:/^{/}),JSXElement(t){let e=/^\s*<\//.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},JSXEscape(t){let e=/\s*\}/.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},"JSXOpenTag JSXSelfClosingTag":t=>t.column(t.node.from)+t.unit}),gl.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":wl,BlockComment:t=>({from:t.from+2,to:t.to-2})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),ip={test:t=>/^JSX/.test(t.name),facet:Wa({commentTokens:{block:{open:"{/*",close:"*/}"}}})},np=ep.configure({dialect:"ts"},"typescript"),sp=ep.configure({dialect:"jsx",props:[Va.add((t=>t.isTop?[ip]:void 0))]}),rp=ep.configure({dialect:"jsx ts",props:[Va.add((t=>t.isTop?[ip]:void 0))]},"typescript");let op=t=>({label:t,type:"keyword"});const ap="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(op),lp=ap.concat(["declare","implements","private","protected","public"].map(op));function hp(t={}){let e=t.jsx?t.typescript?rp:sp:t.typescript?np:ep,i=t.typescript?zd.concat(lp):Dd.concat(ap);return new Ha(e,[ep.data.of({autocomplete:Vc(Hd,Wc(i))}),ep.data.of({autocomplete:Jd}),t.jsx?up:[]])}function cp(t,e,i=t.length){for(let n=null==e?void 0:e.firstChild;n;n=n.nextSibling)if("JSXIdentifier"==n.name||"JSXBuiltin"==n.name||"JSXNamespacedName"==n.name||"JSXMemberExpression"==n.name)return t.sliceString(n.from,Math.min(n.to,i));return""}const Op="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),up=Wr.inputHandler.of(((t,e,i,n,s)=>{if((Op?t.composing:t.compositionStarted)||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!ep.isActiveAt(t.state,e,-1))return!1;let r=s(),{state:o}=r,a=o.changeByRange((t=>{var e;let i,{head:s}=t,r=Ea(o).resolveInner(s-1,-1);if("JSXStartTag"==r.name&&(r=r.parent),o.doc.sliceString(s-1,s)!=n||"JSXAttributeValue"==r.name&&r.to>s);else{if(">"==n&&"JSXFragmentTag"==r.name)return{range:t,changes:{from:s,insert:"</>"}};if("/"==n&&"JSXStartCloseTag"==r.name){let t=r.parent,n=t.parent;if(n&&t.from==s-2&&((i=cp(o.doc,n.firstChild,s))||"JSXFragmentTag"==(null===(e=n.firstChild)||void 0===e?void 0:e.name))){let t=`${i}>`;return{range:$t.cursor(s+t.length,-1),changes:{from:s,insert:t}}}}else if(">"==n){let e=function(t){for(;;){if("JSXOpenTag"==t.name||"JSXSelfClosingTag"==t.name||"JSXFragmentTag"==t.name)return t;if("JSXEscape"==t.name||!t.parent)return null;t=t.parent}}(r);if(e&&!/^\/?>|^<\//.test(o.doc.sliceString(s,s+2))&&(i=cp(o.doc,e,s)))return{range:t,changes:{from:s,insert:`</${i}>`}}}}return{range:t}}));return!a.changes.empty&&(t.dispatch([r,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}));function fp(t,e,i,n){return i.line(t+n.line).from+e+(1==t?n.col-1:-1)}function dp(t,e,i){let n=fp(t.line,t.column,e,i),s={from:n,to:null!=t.endLine&&1!=t.endColumn?fp(t.endLine,t.endColumn,e,i):n,message:t.message,source:t.ruleId?"eslint:"+t.ruleId:"eslint",severity:1==t.severity?"warning":"error"};if(t.fix){let{range:e,text:r}=t.fix,o=e[0]+i.pos-n,a=e[1]+i.pos-n;s.actions=[{name:"fix",apply(t,e){t.dispatch({changes:{from:e+o,to:e+a,insert:r},scrollIntoView:!0})}}]}return s}var pp=Object.freeze({__proto__:null,autoCloseTags:up,completionPath:tp,esLint:function(t,e){return e||(e={parserOptions:{ecmaVersion:2019,sourceType:"module"},env:{browser:!0,node:!0,es6:!0,es2015:!0,es2017:!0,es2020:!0},rules:{}},t.getRules().forEach(((t,i)=>{t.meta.docs.recommended&&(e.rules[i]=2)}))),i=>{let{state:n}=i,s=[];for(let{from:i,to:r}of ep.findRegions(n)){let o=n.doc.lineAt(i),a={line:o.number-1,col:i-o.from,pos:i};for(let o of t.verify(n.sliceDoc(i,r),e))s.push(dp(o,n.doc,a))}return s}},javascript:hp,javascriptLanguage:ep,jsxLanguage:sp,localCompletionSource:Jd,scopeCompletionSource:function(t){let e=new Map;return i=>{let n=tp(i);if(!n)return null;let s=t;for(let t of n.path)if(s=s[t],!s)return null;let r=e.get(s);return r||e.set(s,r=function(t,e){let i=[],n=new Set;for(let s=0;;s++){for(let r of(Object.getOwnPropertyNames||Object.keys)(t)){if(!/^[a-zA-Z_$\xaa-\uffdc][\w$\xaa-\uffdc]*$/.test(r)||n.has(r))continue;let o;n.add(r);try{o=t[r]}catch(t){continue}i.push({label:r,type:"function"==typeof o?/^[A-Z]/.test(r)?"class":e?"function":"method":e?"variable":"property",boost:-s})}let r=Object.getPrototypeOf(t);if(!r)return i;t=r}}(s,!n.path.length)),{from:i.pos-n.name.length,options:r,validFor:Fd}}},snippets:Dd,tsxLanguage:rp,typescriptLanguage:np,typescriptSnippets:zd});const mp=["_blank","_self","_top","_parent"],gp=["ascii","utf-8","utf-16","latin1","latin1"],wp=["get","post","put","delete"],bp=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],Sp=["true","false"],yp={},Qp={a:{attrs:{href:null,ping:null,type:null,media:null,target:mp,hreflang:null}},abbr:yp,address:yp,area:{attrs:{alt:null,coords:null,href:null,target:null,ping:null,media:null,hreflang:null,type:null,shape:["default","rect","circle","poly"]}},article:yp,aside:yp,audio:{attrs:{src:null,mediagroup:null,crossorigin:["anonymous","use-credentials"],preload:["none","metadata","auto"],autoplay:["autoplay"],loop:["loop"],controls:["controls"]}},b:yp,base:{attrs:{href:null,target:mp}},bdi:yp,bdo:yp,blockquote:{attrs:{cite:null}},body:yp,br:yp,button:{attrs:{form:null,formaction:null,name:null,value:null,autofocus:["autofocus"],disabled:["autofocus"],formenctype:bp,formmethod:wp,formnovalidate:["novalidate"],formtarget:mp,type:["submit","reset","button"]}},canvas:{attrs:{width:null,height:null}},caption:yp,center:yp,cite:yp,code:yp,col:{attrs:{span:null}},colgroup:{attrs:{span:null}},command:{attrs:{type:["command","checkbox","radio"],label:null,icon:null,radiogroup:null,command:null,title:null,disabled:["disabled"],checked:["checked"]}},data:{attrs:{value:null}},datagrid:{attrs:{disabled:["disabled"],multiple:["multiple"]}},datalist:{attrs:{data:null}},dd:yp,del:{attrs:{cite:null,datetime:null}},details:{attrs:{open:["open"]}},dfn:yp,div:yp,dl:yp,dt:yp,em:yp,embed:{attrs:{src:null,type:null,width:null,height:null}},eventsource:{attrs:{src:null}},fieldset:{attrs:{disabled:["disabled"],form:null,name:null}},figcaption:yp,figure:yp,footer:yp,form:{attrs:{action:null,name:null,"accept-charset":gp,autocomplete:["on","off"],enctype:bp,method:wp,novalidate:["novalidate"],target:mp}},h1:yp,h2:yp,h3:yp,h4:yp,h5:yp,h6:yp,head:{children:["title","base","link","style","meta","script","noscript","command"]},header:yp,hgroup:yp,hr:yp,html:{attrs:{manifest:null}},i:yp,iframe:{attrs:{src:null,srcdoc:null,name:null,width:null,height:null,sandbox:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"],seamless:["seamless"]}},img:{attrs:{alt:null,src:null,ismap:null,usemap:null,width:null,height:null,crossorigin:["anonymous","use-credentials"]}},input:{attrs:{alt:null,dirname:null,form:null,formaction:null,height:null,list:null,max:null,maxlength:null,min:null,name:null,pattern:null,placeholder:null,size:null,src:null,step:null,value:null,width:null,accept:["audio/*","video/*","image/*"],autocomplete:["on","off"],autofocus:["autofocus"],checked:["checked"],disabled:["disabled"],formenctype:bp,formmethod:wp,formnovalidate:["novalidate"],formtarget:mp,multiple:["multiple"],readonly:["readonly"],required:["required"],type:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]}},ins:{attrs:{cite:null,datetime:null}},kbd:yp,keygen:{attrs:{challenge:null,form:null,name:null,autofocus:["autofocus"],disabled:["disabled"],keytype:["RSA"]}},label:{attrs:{for:null,form:null}},legend:yp,li:{attrs:{value:null}},link:{attrs:{href:null,type:null,hreflang:null,media:null,sizes:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}},map:{attrs:{name:null}},mark:yp,menu:{attrs:{label:null,type:["list","context","toolbar"]}},meta:{attrs:{content:null,charset:gp,name:["viewport","application-name","author","description","generator","keywords"],"http-equiv":["content-language","content-type","default-style","refresh"]}},meter:{attrs:{value:null,min:null,low:null,high:null,max:null,optimum:null}},nav:yp,noscript:yp,object:{attrs:{data:null,type:null,name:null,usemap:null,form:null,width:null,height:null,typemustmatch:["typemustmatch"]}},ol:{attrs:{reversed:["reversed"],start:null,type:["1","a","A","i","I"]},children:["li","script","template","ul","ol"]},optgroup:{attrs:{disabled:["disabled"],label:null}},option:{attrs:{disabled:["disabled"],label:null,selected:["selected"],value:null}},output:{attrs:{for:null,form:null,name:null}},p:yp,param:{attrs:{name:null,value:null}},pre:yp,progress:{attrs:{value:null,max:null}},q:{attrs:{cite:null}},rp:yp,rt:yp,ruby:yp,samp:yp,script:{attrs:{type:["text/javascript"],src:null,async:["async"],defer:["defer"],charset:gp}},section:yp,select:{attrs:{form:null,name:null,size:null,autofocus:["autofocus"],disabled:["disabled"],multiple:["multiple"]}},slot:{attrs:{name:null}},small:yp,source:{attrs:{src:null,type:null,media:null}},span:yp,strong:yp,style:{attrs:{type:["text/css"],media:null,scoped:null}},sub:yp,summary:yp,sup:yp,table:yp,tbody:yp,td:{attrs:{colspan:null,rowspan:null,headers:null}},template:yp,textarea:{attrs:{dirname:null,form:null,maxlength:null,name:null,placeholder:null,rows:null,cols:null,autofocus:["autofocus"],disabled:["disabled"],readonly:["readonly"],required:["required"],wrap:["soft","hard"]}},tfoot:yp,th:{attrs:{colspan:null,rowspan:null,headers:null,scope:["row","col","rowgroup","colgroup"]}},thead:yp,time:{attrs:{datetime:null}},title:yp,tr:yp,track:{attrs:{src:null,label:null,default:null,kind:["subtitles","captions","descriptions","chapters","metadata"],srclang:null}},ul:{children:["li","script","template","ul","ol"]},var:yp,video:{attrs:{src:null,poster:null,width:null,height:null,crossorigin:["anonymous","use-credentials"],preload:["auto","metadata","none"],autoplay:["autoplay"],mediagroup:["movie"],muted:["muted"],controls:["controls"]}},wbr:yp},vp={accesskey:null,class:null,contenteditable:Sp,contextmenu:null,dir:["ltr","rtl","auto"],draggable:["true","false","auto"],dropzone:["copy","move","link","string:","file:"],hidden:["hidden"],id:null,inert:["inert"],itemid:null,itemprop:null,itemref:null,itemscope:["itemscope"],itemtype:null,lang:["ar","bn","de","en-GB","en-US","es","fr","hi","id","ja","pa","pt","ru","tr","zh"],spellcheck:Sp,autocorrect:Sp,autocapitalize:Sp,style:null,tabindex:null,title:null,translate:["yes","no"],rel:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"],role:"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),"aria-activedescendant":null,"aria-atomic":Sp,"aria-autocomplete":["inline","list","both","none"],"aria-busy":Sp,"aria-checked":["true","false","mixed","undefined"],"aria-controls":null,"aria-describedby":null,"aria-disabled":Sp,"aria-dropeffect":null,"aria-expanded":["true","false","undefined"],"aria-flowto":null,"aria-grabbed":["true","false","undefined"],"aria-haspopup":Sp,"aria-hidden":Sp,"aria-invalid":["true","false","grammar","spelling"],"aria-label":null,"aria-labelledby":null,"aria-level":null,"aria-live":["off","polite","assertive"],"aria-multiline":Sp,"aria-multiselectable":Sp,"aria-owns":null,"aria-posinset":null,"aria-pressed":["true","false","mixed","undefined"],"aria-readonly":Sp,"aria-relevant":null,"aria-required":Sp,"aria-selected":["true","false","undefined"],"aria-setsize":null,"aria-sort":["ascending","descending","none","other"],"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null,"aria-valuetext":null},xp="beforeunload copy cut dragstart dragover dragleave dragenter dragend drag paste focus blur change click load mousedown mouseenter mouseleave mouseup keydown keyup resize scroll unload".split(" ").map((t=>"on"+t));for(let t of xp)vp[t]=null;class kp{constructor(t,e){this.tags=Object.assign(Object.assign({},Qp),t),this.globalAttrs=Object.assign(Object.assign({},vp),e),this.allTags=Object.keys(this.tags),this.globalAttrNames=Object.keys(this.globalAttrs)}}function $p(t,e,i=t.length){if(!e)return"";let n=e.firstChild,s=n&&n.getChild("TagName");return s?t.sliceString(s.from,Math.min(s.to,i)):""}function Pp(t,e=!1){for(;t;t=t.parent)if("Element"==t.name){if(!e)return t;e=!1}return null}function Zp(t,e,i){let n=i.tags[$p(t,Pp(e))];return(null==n?void 0:n.children)||i.allTags}function Tp(t,e){let i=[];for(let n=Pp(e);n&&!n.type.isTop;n=Pp(n.parent)){let s=$p(t,n);if(s&&"CloseTag"==n.lastChild.name)break;s&&i.indexOf(s)<0&&("EndTag"==e.name||e.from>=n.firstChild.to)&&i.push(s)}return i}kp.default=new kp;const Xp=/^[:\-\.\w\u00b7-\uffff]*$/;function Cp(t,e,i,n,s){let r=/\s*>/.test(t.sliceDoc(s,s+5))?"":">",o=Pp(i,!0);return{from:n,to:s,options:Zp(t.doc,o,e).map((t=>({label:t,type:"type"}))).concat(Tp(t.doc,i).map(((t,e)=>({label:"/"+t,apply:"/"+t+r,type:"type",boost:99-e})))),validFor:/^\/?[:\-\.\w\u00b7-\uffff]*$/}}function Yp(t,e,i,n){let s=/\s*>/.test(t.sliceDoc(n,n+5))?"":">";return{from:i,to:n,options:Tp(t.doc,e).map(((t,e)=>({label:t,apply:t+s,type:"type",boost:99-e}))),validFor:Xp}}function Ap(t,e){let{state:i,pos:n}=e,s=Ea(i).resolveInner(n,-1),r=s.resolve(n);for(let t,e=n;r==s&&(t=s.childBefore(e));){let i=t.lastChild;if(!i||!i.type.isError||i.from<i.to)break;r=s=t,e=i.from}return"TagName"==s.name?s.parent&&/CloseTag$/.test(s.parent.name)?Yp(i,s,s.from,n):Cp(i,t,s,s.from,n):"StartTag"==s.name?Cp(i,t,s,n,n):"StartCloseTag"==s.name||"IncompleteCloseTag"==s.name?Yp(i,s,n,n):e.explicit&&("OpenTag"==s.name||"SelfClosingTag"==s.name)||"AttributeName"==s.name?function(t,e,i,n,s){let r=Pp(i),o=r?e.tags[$p(t.doc,r)]:null,a=o&&o.attrs?Object.keys(o.attrs):[];return{from:n,to:s,options:(o&&!1===o.globalAttrs?a:a.length?a.concat(e.globalAttrNames):e.globalAttrNames).map((t=>({label:t,type:"property"}))),validFor:Xp}}(i,t,s,"AttributeName"==s.name?s.from:n,n):"Is"==s.name||"AttributeValue"==s.name||"UnquotedAttributeValue"==s.name?function(t,e,i,n,s){var r;let o,a=null===(r=i.parent)||void 0===r?void 0:r.getChild("AttributeName"),l=[];if(a){let r=t.sliceDoc(a.from,a.to),h=e.globalAttrs[r];if(!h){let n=Pp(i),s=n?e.tags[$p(t.doc,n)]:null;h=(null==s?void 0:s.attrs)&&s.attrs[r]}if(h){let e=t.sliceDoc(n,s).toLowerCase(),i='"',r='"';/^['"]/.test(e)?(o='"'==e[0]?/^[^"]*$/:/^[^']*$/,i="",r=t.sliceDoc(s,s+1)==e[0]?"":e[0],e=e.slice(1),n++):o=/^[^\s<>='"]*$/;for(let t of h)l.push({label:t,apply:i+t+r,type:"constant"})}}return{from:n,to:s,options:l,validFor:o}}(i,t,s,"Is"==s.name?n:s.from,n):!e.explicit||"Element"!=r.name&&"Text"!=r.name&&"Document"!=r.name?null:function(t,e,i,n){let s=[],r=0;for(let n of Zp(t.doc,i,e))s.push({label:"<"+n,type:"type"});for(let e of Tp(t.doc,i))s.push({label:"</"+e+">",type:"type",boost:99-r++});return{from:n,to:n,options:s,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}(i,t,s,n)}function Rp(t){return Ap(kp.default,t)}function Wp(t){let{extraTags:e,extraGlobalAttributes:i}=t,n=i||e?new kp(e,i):kp.default;return t=>Ap(n,t)}const Vp=ep.parser.configure({top:"SingleExpression"}),jp=[{tag:"script",attrs:t=>"text/typescript"==t.type||"ts"==t.lang,parser:np.parser},{tag:"script",attrs:t=>"text/babel"==t.type||"text/jsx"==t.type,parser:sp.parser},{tag:"script",attrs:t=>"text/typescript-jsx"==t.type,parser:rp.parser},{tag:"script",attrs:t=>/^(importmap|speculationrules|application\/(.+\+)?json)$/i.test(t.type),parser:Vp},{tag:"script",attrs:t=>!t.type||/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(t.type),parser:ep.parser},{tag:"style",attrs:t=>(!t.lang||"css"==t.lang)&&(!t.type||/^(text\/)?(x-)?(stylesheet|css)$/i.test(t.type)),parser:Cc.parser}],qp=[{name:"style",parser:Cc.parser.configure({top:"Styles"})}].concat(xp.map((t=>({name:t,parser:ep.parser})))),_p=_a.define({name:"html",parser:kd.configure({props:[rl.add({Element(t){let e=/^(\s*)(<\/)?/.exec(t.textAfter);return t.node.to<=t.pos+e[0].length?t.continue():t.lineIndent(t.node.from)+(e[2]?0:t.unit)},"OpenTag CloseTag SelfClosingTag":t=>t.column(t.node.from)+t.unit,Document(t){if(t.pos+/\s*/.exec(t.textAfter)[0].length<t.node.to)return t.continue();let e,i=null;for(let e=t.node;;){let t=e.lastChild;if(!t||"Element"!=t.name||t.to!=e.to)break;i=e=t}return i&&(!(e=i.lastChild)||"CloseTag"!=e.name&&"SelfClosingTag"!=e.name)?t.lineIndent(i.from)+t.unit:null}}),gl.add({Element(t){let e=t.firstChild,i=t.lastChild;return e&&"OpenTag"==e.name?{from:e.to,to:"CloseTag"==i.name?i.from:t.to}:null}}),sh.add({"OpenTag CloseTag":t=>t.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}},indentOnInput:/^\s*<\/\w+\W$/,wordChars:"-._"}}),Ep=_p.configure({wrap:Td(jp,qp)});function Mp(t={}){let e,i="";!1===t.matchClosingTags&&(i="noMatch"),!0===t.selfClosingTags&&(i=(i?i+" ":"")+"selfClosing"),(t.nestedLanguages&&t.nestedLanguages.length||t.nestedAttributes&&t.nestedAttributes.length)&&(e=Td((t.nestedLanguages||[]).concat(jp),(t.nestedAttributes||[]).concat(qp)));let n=e?_p.configure({wrap:e,dialect:i}):i?Ep.configure({dialect:i}):Ep;return new Ha(n,[Ep.data.of({autocomplete:Wp(t)}),!1!==t.autoCloseTags?zp:[],hp().support,Yc().support])}const Dp=new Set("area base br col command embed frame hr img input keygen link meta param source track wbr menuitem".split(" ")),zp=Wr.inputHandler.of(((t,e,i,n,s)=>{if(t.composing||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!Ep.isActiveAt(t.state,e,-1))return!1;let r=s(),{state:o}=r,a=o.changeByRange((t=>{var e,i,s;let r,a=o.doc.sliceString(t.from-1,t.to)==n,{head:l}=t,h=Ea(o).resolveInner(l-1,-1);if("TagName"!=h.name&&"StartTag"!=h.name||(h=h.parent),a&&">"==n&&"OpenTag"==h.name){if("CloseTag"!=(null===(i=null===(e=h.parent)||void 0===e?void 0:e.lastChild)||void 0===i?void 0:i.name)&&(r=$p(o.doc,h.parent,l))&&!Dp.has(r)){return{range:t,changes:{from:l,to:l+(">"===o.doc.sliceString(l,l+1)?1:0),insert:`</${r}>`}}}}else if(a&&"/"==n&&"IncompleteCloseTag"==h.name){let t=h.parent;if(h.from==l-2&&"CloseTag"!=(null===(s=t.lastChild)||void 0===s?void 0:s.name)&&(r=$p(o.doc,t,l))&&!Dp.has(r)){let t=l+(">"===o.doc.sliceString(l,l+1)?1:0),e=`${r}>`;return{range:$t.cursor(l+e.length,-1),changes:{from:l,to:t,insert:e}}}}return{range:t}}));return!a.changes.empty&&(t.dispatch([r,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}));var Gp=Object.freeze({__proto__:null,autoCloseTags:zp,html:Mp,htmlCompletionSource:Rp,htmlCompletionSourceWith:Wp,htmlLanguage:Ep,htmlPlain:_p});const Ip="function"==typeof String.prototype.normalize?t=>t.normalize("NFKD"):t=>t;class Bp{constructor(t,e,i=0,n=t.length,s,r){this.test=r,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=t.iterRange(i,n),this.bufferStart=i,this.normalize=s?t=>s(Ip(t)):Ip,this.query=this.normalize(e)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return ut(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let t=this.peek();if(t<0)return this.done=!0,this;let e=ft(t),i=this.bufferStart+this.bufferPos;this.bufferPos+=dt(t);let n=this.normalize(e);for(let t=0,s=i;;t++){let r=n.charCodeAt(t),o=this.match(r,s);if(t==n.length-1){if(o)return this.value=o,this;break}s==i&&t<e.length&&e.charCodeAt(t)==r&&s++}}}match(t,e){let i=null;for(let n=0;n<this.matches.length;n+=2){let s=this.matches[n],r=!1;this.query.charCodeAt(s)==t&&(s==this.query.length-1?i={from:this.matches[n+1],to:e+1}:(this.matches[n]++,r=!0)),r||(this.matches.splice(n,2),n-=2)}return this.query.charCodeAt(0)==t&&(1==this.query.length?i={from:e,to:e+1}:this.matches.push(1,e)),i&&this.test&&!this.test(i.from,i.to,this.buffer,this.bufferStart)&&(i=null),i}}"undefined"!=typeof Symbol&&(Bp.prototype[Symbol.iterator]=function(){return this});const Np={from:-1,to:-1,match:/.*/.exec("")},Up="gm"+(null==/x/.unicode?"":"u");class Lp{constructor(t,e,i,n=0,s=t.length){if(this.text=t,this.to=s,this.curLine="",this.done=!1,this.value=Np,/\\[sWDnr]|\n|\r|\[\^/.test(e))return new Jp(t,e,i,n,s);this.re=new RegExp(e,Up+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.iter=t.iter();let r=t.lineAt(n);this.curLineStart=r.from,this.matchPos=Kp(t,n),this.getLine(this.curLineStart)}getLine(t){this.iter.next(t),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let t=this.matchPos-this.curLineStart;;){this.re.lastIndex=t;let e=this.matchPos<=this.to&&this.re.exec(this.curLine);if(e){let i=this.curLineStart+e.index,n=i+e[0].length;if(this.matchPos=Kp(this.text,n+(i==n?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<n||i>this.value.to)&&(!this.test||this.test(i,n,e)))return this.value={from:i,to:n,match:e},this;t=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),t=0}}}}const Fp=new WeakMap;class Hp{constructor(t,e){this.from=t,this.text=e}get to(){return this.from+this.text.length}static get(t,e,i){let n=Fp.get(t);if(!n||n.from>=i||n.to<=e){let n=new Hp(e,t.sliceString(e,i));return Fp.set(t,n),n}if(n.from==e&&n.to==i)return n;let{text:s,from:r}=n;return r>e&&(s=t.sliceString(e,r)+s,r=e),n.to<i&&(s+=t.sliceString(n.to,i)),Fp.set(t,new Hp(r,s)),new Hp(e,s.slice(e-r,i-r))}}class Jp{constructor(t,e,i,n,s){this.text=t,this.to=s,this.done=!1,this.value=Np,this.matchPos=Kp(t,n),this.re=new RegExp(e,Up+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.flat=Hp.get(t,n,this.chunkEnd(n+5e3))}chunkEnd(t){return t>=this.to?this.to:this.text.lineAt(t).to}next(){for(;;){let t=this.re.lastIndex=this.matchPos-this.flat.from,e=this.re.exec(this.flat.text);if(e&&!e[0]&&e.index==t&&(this.re.lastIndex=t+1,e=this.re.exec(this.flat.text)),e){let t=this.flat.from+e.index,i=t+e[0].length;if((this.flat.to>=this.to||e.index+e[0].length<=this.flat.text.length-10)&&(!this.test||this.test(t,i,e)))return this.value={from:t,to:i,match:e},this.matchPos=Kp(this.text,i+(t==i?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=Hp.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}function Kp(t,e){if(e>=t.length)return e;let i,n=t.lineAt(e);for(;e<n.to&&(i=n.text.charCodeAt(e-n.from))>=56320&&i<57344;)e++;return e}"undefined"!=typeof Symbol&&(Lp.prototype[Symbol.iterator]=Jp.prototype[Symbol.iterator]=function(){return this});const tm={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},em=Tt.define({combine:t=>we(t,tm,{highlightWordAroundCursor:(t,e)=>t||e,minSelectionLength:Math.min,maxMatches:Math.min})});function im(t){let e=[am,om];return t&&e.push(em.of(t)),e}const nm=Ui.mark({class:"cm-selectionMatch"}),sm=Ui.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function rm(t,e,i,n){return!(0!=i&&t(e.sliceDoc(i-1,i))==fe.Word||n!=e.doc.length&&t(e.sliceDoc(n,n+1))==fe.Word)}const om=qn.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.selectionSet||t.docChanged||t.viewportChanged)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=t.state.facet(em),{state:i}=t,n=i.selection;if(n.ranges.length>1)return Ui.none;let s,r=n.main,o=null;if(r.empty){if(!e.highlightWordAroundCursor)return Ui.none;let t=i.wordAt(r.head);if(!t)return Ui.none;o=i.charCategorizer(r.head),s=i.sliceDoc(t.from,t.to)}else{let t=r.to-r.from;if(t<e.minSelectionLength||t>200)return Ui.none;if(e.wholeWords){if(s=i.sliceDoc(r.from,r.to),o=i.charCategorizer(r.head),!rm(o,i,r.from,r.to)||!function(t,e,i,n){return t(e.sliceDoc(i,i+1))==fe.Word&&t(e.sliceDoc(n-1,n))==fe.Word}(o,i,r.from,r.to))return Ui.none}else if(s=i.sliceDoc(r.from,r.to).trim(),!s)return Ui.none}let a=[];for(let n of t.visibleRanges){let t=new Bp(i.doc,s,n.from,n.to);for(;!t.next().done;){let{from:n,to:s}=t.value;if((!o||rm(o,i,n,s))&&(r.empty&&n<=r.from&&s>=r.to?a.push(sm.range(n,s)):(n>=r.to||s<=r.from)&&a.push(nm.range(n,s)),a.length>e.maxMatches))return Ui.none}}return Ui.set(a)}},{decorations:t=>t.decorations}),am=Wr.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}});const lm=({state:t,dispatch:e})=>{let{ranges:i}=t.selection;if(i.some((t=>t.from===t.to)))return(({state:t,dispatch:e})=>{let{selection:i}=t,n=$t.create(i.ranges.map((e=>t.wordAt(e.head)||$t.cursor(e.head))),i.mainIndex);return!n.eq(i)&&(e(t.update({selection:n})),!0)})({state:t,dispatch:e});let n=t.sliceDoc(i[0].from,i[0].to);if(t.selection.ranges.some((e=>t.sliceDoc(e.from,e.to)!=n)))return!1;let s=function(t,e){let{main:i,ranges:n}=t.selection,s=t.wordAt(i.head),r=s&&s.from==i.from&&s.to==i.to;for(let i=!1,s=new Bp(t.doc,e,n[n.length-1].to);;){if(s.next(),!s.done){if(i&&n.some((t=>t.from==s.value.from)))continue;if(r){let e=t.wordAt(s.value.from);if(!e||e.from!=s.value.from||e.to!=s.value.to)continue}return s.value}if(i)return null;s=new Bp(t.doc,e,0,Math.max(0,n[n.length-1].from-1)),i=!0}}(t,n);return!!s&&(e(t.update({selection:t.selection.addRange($t.range(s.from,s.to),!1),effects:Wr.scrollIntoView(s.to)})),!0)};function hm(t){const e=t.selection.main.head;return t.doc.lineAt(e)}function cm(t,e){let i=0;t:for(let n=0;n<t.length;n++)switch(t[n]){case" ":i+=1;continue t;case"\t":i+=e-i%e;continue t;case"\r":continue t;default:break t}return i}const Om=Tt.define({combine:t=>we(t,{highlightActiveBlock:!0,hideFirstIndent:!1,markerType:"fullScope",thickness:1})});class um{constructor(t,e,i,n){this.lines=t,this.state=e,this.map=new Map,this.unitWidth=i,this.markerType=n;for(const t of this.lines)this.add(t);this.state.facet(Om).highlightActiveBlock&&this.findAndSetActiveLines()}has(t){return this.map.has("number"==typeof t?t:t.number)}get(t){const e=this.map.get("number"==typeof t?t:t.number);if(!e)throw new Error("Line not found in indentation map");return e}set(t,e,i){const n=!t.text.trim().length,s={line:t,col:e,level:i,empty:n};return this.map.set(s.line.number,s),s}add(t){if(this.has(t))return this.get(t);if(!t.length||!t.text.trim().length){if(1===t.number)return this.set(t,0,0);if(t.number===this.state.doc.lines){const e=this.closestNonEmpty(t,-1);return this.set(t,0,e.level)}const e=this.closestNonEmpty(t,-1),i=this.closestNonEmpty(t,1);return e.level>=i.level&&"codeOnly"!==this.markerType?this.set(t,0,e.level):e.empty&&0===e.level&&0!==i.level?this.set(t,0,0):i.level>e.level?this.set(t,0,e.level+1):this.set(t,0,i.level)}const e=cm(t.text,this.state.tabSize),i=Math.floor(e/this.unitWidth);return this.set(t,e,i)}closestNonEmpty(t,e){let i=t.number+e;for(;-1===e?i>=1:i<=this.state.doc.lines;){if(this.has(i)){const t=this.get(i);if(!t.empty)return t}const t=this.state.doc.line(i);if(t.text.trim().length){const e=cm(t.text,this.state.tabSize),i=Math.floor(e/this.unitWidth);return this.set(t,e,i)}i+=e}const n=this.state.doc.line(-1===e?1:this.state.doc.lines);return this.set(n,0,0)}findAndSetActiveLines(){const t=hm(this.state);if(!this.has(t))return;let e,i,n=this.get(t);if(this.has(n.line.number+1)){const t=this.get(n.line.number+1);t.level>n.level&&(n=t)}if(this.has(n.line.number-1)){const t=this.get(n.line.number-1);t.level>n.level&&(n=t)}if(0!==n.level){for(n.active=n.level,e=n.line.number;e>1;e--){if(!this.has(e-1))continue;const t=this.get(e-1);if(t.level<n.level)break;t.active=n.level}for(i=n.line.number;i<this.state.doc.lines;i++){if(!this.has(i+1))continue;const t=this.get(i+1);if(t.level<n.level)break;t.active=n.level}}}}function fm(t){const e={light:"#F0F1F2",dark:"#2B3245",activeLight:"#E4E5E6",activeDark:"#3C445C"};let i=e;return t&&(i=Object.assign(Object.assign({},e),t)),Wr.baseTheme({"&light":{"--indent-marker-bg-color":i.light,"--indent-marker-active-bg-color":i.activeLight},"&dark":{"--indent-marker-bg-color":i.dark,"--indent-marker-active-bg-color":i.activeDark},".cm-line":{position:"relative"},".cm-indent-markers::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:"var(--indent-markers)",pointerEvents:"none",zIndex:"-1"}})}function dm(t,e,i,n,s){return`${`repeating-linear-gradient(to right, var(${t}) 0 ${e}px, transparent ${e}px ${i}ch)`} ${n*i}.5ch/calc(${i*s}ch - 1px) no-repeat`}function pm(t,e,i,n){const{level:s,active:r}=t;if(i&&0===s)return[];const o=i?1:0,a=[];if(void 0!==r){const t=r-o-1;t>0&&a.push(dm("--indent-marker-bg-color",n,e,o,t)),a.push(dm("--indent-marker-active-bg-color",n,e,r-1,1)),r!==s&&a.push(dm("--indent-marker-bg-color",n,e,r,s-r))}else a.push(dm("--indent-marker-bg-color",n,e,o,s-o));return a.join(",")}class mm{constructor(t){this.view=t,this.unitWidth=el(t.state),this.currentLineNumber=hm(t.state).number,this.generate(t.state)}update(t){const e=el(t.state),i=e!==this.unitWidth;i&&(this.unitWidth=e);const n=hm(t.state).number,s=n!==this.currentLineNumber;this.currentLineNumber=n;const r=t.state.facet(Om).highlightActiveBlock&&s;(t.docChanged||t.viewportChanged||i||r)&&this.generate(t.state)}generate(t){const e=new xe,i=function(t,e=t.state){const i=new Set;for(const{from:n,to:s}of t.visibleRanges){let t=n;for(;t<=s;){const n=e.doc.lineAt(t);i.has(n)||i.add(n),t=n.to+1}}return i}(this.view,t),{hideFirstIndent:n,markerType:s,thickness:r}=t.facet(Om),o=new um(i,t,this.unitWidth,s);for(const t of i){const i=o.get(t.number);if(!(null==i?void 0:i.level))continue;const s=pm(i,this.unitWidth,n,r);e.add(t.from,t.from,Ui.line({class:"cm-indent-markers",attributes:{style:`--indent-markers: ${s}`}}))}this.decorations=e.finish()}}function gm(t={}){return[Om.of(t),fm(t.colors),qn.fromClass(mm,{decorations:t=>t.decorations})]}function wm(){return import("./angular.js")}async function bm(){return fh.define((await import("./legacy.js").then((function(t){return t.c}))).clojure)}async function Sm(){return fh.define((await import("./legacy.js").then((function(t){return t.a}))).coffeeScript)}function ym(){return import("./cpp.js")}const Qm=_a.define({name:Cc.name,parser:Cc.parser,languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:""}}),vm={cssCompletionSource:Xc,cssLanguage:Qm,css:()=>new Ha(Qm,Qm.data.of({autocomplete:Xc}))};async function xm(){return fh.define((await import("./legacy.js").then((function(t){return t.b}))).dart)}async function km(){return fh.define((await import("./legacy.js").then((function(t){return t.d}))).gss)}async function $m(){return fh.define((await import("./legacy.js").then((function(t){return t.g}))).go)}function Pm(){return import("./java.js")}async function Zm(){return fh.define((await import("./legacy.js").then((function(t){return t.b}))).kotlin)}function Tm(){return import("./less.js")}function Xm(){return import("./markdown.js")}function Cm(){return import("./php.js")}function Ym(){return import("./python.js")}function Am(){return import("./sass.js")}async function Rm(){return fh.define((await import("./legacy.js").then((function(t){return t.b}))).scala)}async function Wm(){return fh.define((await import("./legacy.js").then((function(t){return t.s}))).shell)}async function Vm(){return import("./svelte.js")}async function jm(){return(await import("./legacy.js").then((function(t){return t.d}))).css}function qm(){return import("./vue.js")}function _m(){return import("./wast.js")}function Em(){return import("./xml.js")}export{Sm as $,ml as A,tl as B,Ac as C,Ja as D,Ih as E,Ga as F,Ra as G,sc as H,Vc as I,Wc as J,c as K,_a as L,T as M,o as N,RO as O,C as P,np as Q,Cc as R,hp as S,la as T,Yc as U,Wr as V,Md as W,Gh as X,sh as Y,wm as Z,bm as _,Ha as a,ch as a$,ym as a0,vm as a1,xm as a2,km as a3,$m as a4,Pm as a5,Zm as a6,Tm as a7,Xm as a8,Cm as a9,ku as aA,Du as aB,Bf as aC,If as aD,Df as aE,Zu as aF,Xu as aG,pf as aH,yf as aI,Qf as aJ,vf as aK,xf as aL,Nf as aM,fu as aN,Pu as aO,Tu as aP,Gp as aQ,pp as aR,nh as aS,Al as aT,Ch as aU,Ma as aV,_l as aW,Xl as aX,Ml as aY,pl as aZ,fh as a_,Ym as aa,Am as ab,Rm as ac,Wm as ad,Vm as ae,jm as af,qm as ag,_m as ah,Em as ai,fO as aj,ru as ak,IO as al,HO as am,pO as an,EO as ao,lu as ap,cu as aq,uO as ar,Ou as as,uu as at,dO as au,df as av,Ju as aw,Ku as ax,nf as ay,sf as az,rc as b,Il as b0,im as b1,lm as b2,ie as b3,ne as b4,gt as b5,wt as b6,Gt as b7,Tt as b8,et as b9,Hn as bA,Bi as bB,x as bC,pa as bD,_e as bE,gm as bF,mt as ba,Se as bb,ve as bc,xe as bd,kt as be,re as bf,se as bg,Vt as bh,N as bi,oe as bj,Ui as bk,eo as bl,Io as bm,Mo as bn,No as bo,mo as bp,ta as bq,ra as br,ho as bs,vo as bt,Wo as bu,yo as bv,Eo as bw,Ao as bx,$o as by,qn as bz,dl as c,Ol as d,gl as e,fl as f,wl as g,Mp as h,rl as i,ep as j,Tc as k,n as l,a as m,O as n,ja as o,A as p,Ea as q,$t as r,Oa as s,Ya as t,We as u,Dt as v,zr as w,Rp as x,ge as y,Wa as z};
//# sourceMappingURL=codemirror.js.map
