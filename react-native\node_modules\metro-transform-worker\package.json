{"name": "metro-transform-worker", "version": "0.81.5", "description": "🚇 Transform worker for Metro.", "main": "src/index.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src": "./src/index.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "metro": "0.81.5", "metro-babel-transformer": "0.81.5", "metro-cache": "0.81.5", "metro-cache-key": "0.81.5", "metro-minify-terser": "0.81.5", "metro-source-map": "0.81.5", "metro-transform-plugins": "0.81.5", "nullthrows": "^1.1.1"}, "devDependencies": {"@react-native/metro-babel-transformer": "0.73.11", "metro-memory-fs": "0.81.5"}, "engines": {"node": ">=18.18"}}