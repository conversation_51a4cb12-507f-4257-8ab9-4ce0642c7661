{"version": 3, "file": "FontAwesome5.js", "sourceRoot": "", "sources": ["../src/FontAwesome5.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,QAAQ,MAAM,oEAAoE,CAAC;AAC1F,OAAO,QAAQ,MAAM,yEAAyE,CAAC;AAE/F,MAAM,OAAO,GAAG;IACd,OAAO,EAAE,OAAO,CAAC,mEAAmE,CAAC;IACrF,KAAK,EAAE,OAAO,CAAC,mEAAmE,CAAC;IACnF,KAAK,EAAE,OAAO,CAAC,iEAAiE,CAAC;IACjF,KAAK,EAAE,OAAO,CAAC,kEAAkE,CAAC;CACnF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;CACf,CAAC;AAEF,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAErE,eAAe,OAAO,CAAC", "sourcesContent": ["\"use client\";\n\nimport { createFA5iconSet } from './createIconSetFromFontAwesome5';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free.json';\nimport metadata from './vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free_meta.json';\n\nconst fontMap = {\n  Regular: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf'),\n  Light: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf'),\n  Solid: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf'),\n  Brand: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf'),\n};\n\nexport const FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n};\n\nconst iconSet = createFA5iconSet(glyphMap, metadata, fontMap, false);\n\nexport default iconSet;\n"]}