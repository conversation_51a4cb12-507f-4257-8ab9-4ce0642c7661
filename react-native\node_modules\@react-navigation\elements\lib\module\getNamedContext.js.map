{"version": 3, "names": ["React", "contexts", "global", "Map", "getNamedContext", "name", "initialValue", "context", "get", "createContext", "displayName", "set"], "sourceRoot": "../../src", "sources": ["getNamedContext.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,QAAQ,GAAG,uCAAuC;AAMxD;AACAC,MAAM,CAACD,QAAQ,CAAC,GAAGC,MAAM,CAACD,QAAQ,CAAC,IAAI,IAAIE,GAAG,EAA8B;AAE5E,eAAe,SAASC,eAAe,CACrCC,IAAY,EACZC,YAAe,EACG;EAClB,IAAIC,OAAO,GAAGL,MAAM,CAACD,QAAQ,CAAC,CAACO,GAAG,CAACH,IAAI,CAAC;EAExC,IAAIE,OAAO,EAAE;IACX,OAAOA,OAAO;EAChB;EAEAA,OAAO,gBAAGP,KAAK,CAACS,aAAa,CAAIH,YAAY,CAAC;EAC9CC,OAAO,CAACG,WAAW,GAAGL,IAAI;EAE1BH,MAAM,CAACD,QAAQ,CAAC,CAACU,GAAG,CAACN,IAAI,EAAEE,OAAO,CAAC;EAEnC,OAAOA,OAAO;AAChB"}