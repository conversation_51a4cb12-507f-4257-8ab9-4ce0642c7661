{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperModuleImports", "_core", "_helpers", "_index", "_index2", "_default", "exports", "default", "declare", "api", "options", "dirname", "assertVersion", "version", "runtimeVersion", "absoluteRuntime", "moduleName", "Error", "DUAL_MODE_RUNTIME", "supportsCJSDefault", "hasMinVersion", "hasOwnProperty", "call", "useBuiltIns", "polyfill", "useESModules", "esModules", "caller", "supportsStaticESM", "helpers", "useRuntimeHelpers", "HEADER_HELPERS", "Set", "name", "inherits", "babel7", "createPolyfillPlugins", "pre", "file", "modulePath", "set", "_ref", "getRuntimePath", "get", "availableHelper", "t", "arrowFunctionExpression", "identifier", "blockHoist", "has", "isModule", "path", "undefined", "help<PERSON><PERSON><PERSON>", "node", "sourceType", "resolveFSPath", "addDefaultImport", "cache", "Map", "source", "nameHint", "is<PERSON><PERSON>per", "cache<PERSON>ey", "key", "cached", "cloneNode", "addDefault", "importedInterop"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { addDefault, isModule } from \"@babel/helper-module-imports\";\nimport { types as t } from \"@babel/core\";\n\nimport { hasMinVersion } from \"./helpers.ts\";\nimport getRuntimePath, { resolveFSPath } from \"./get-runtime-path/index.ts\";\n\n// TODO(Babel 8): Remove this\nimport babel7 from \"./babel-7/index.cjs\" with { if: \"!process.env.BABEL_8_BREAKING\" };\n\nexport interface Options {\n  absoluteRuntime?: boolean;\n  corejs?: string | number | { version: string | number; proposals?: boolean };\n  helpers?: boolean;\n  version?: string;\n  moduleName?: null | string;\n}\n\nexport default declare((api, options: Options, dirname) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const {\n    version: runtimeVersion = \"7.0.0-beta.0\",\n    absoluteRuntime = false,\n    moduleName = null,\n  } = options;\n\n  if (\n    typeof absoluteRuntime !== \"boolean\" &&\n    typeof absoluteRuntime !== \"string\"\n  ) {\n    throw new Error(\n      \"The 'absoluteRuntime' option must be undefined, a boolean, or a string.\",\n    );\n  }\n\n  if (typeof runtimeVersion !== \"string\") {\n    throw new Error(`The 'version' option must be a version string.`);\n  }\n\n  if (moduleName !== null && typeof moduleName !== \"string\") {\n    throw new Error(\"The 'moduleName' option must be null or a string.\");\n  }\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // In recent @babel/runtime versions, we can use require(\"helper\").default\n    // instead of require(\"helper\") so that it has the same interface as the\n    // ESM helper, and bundlers can better exchange one format for the other.\n    const DUAL_MODE_RUNTIME = \"7.13.0\";\n    // eslint-disable-next-line no-var\n    var supportsCJSDefault = hasMinVersion(DUAL_MODE_RUNTIME, runtimeVersion);\n  }\n\n  if (Object.hasOwn(options, \"useBuiltIns\")) {\n    // @ts-expect-error deprecated options\n    if (options.useBuiltIns) {\n      throw new Error(\n        \"The 'useBuiltIns' option has been removed. The @babel/runtime \" +\n          \"module now uses builtins by default.\",\n      );\n    } else {\n      throw new Error(\n        \"The 'useBuiltIns' option has been removed. Use the 'corejs'\" +\n          \"option to polyfill with `core-js` via @babel/runtime.\",\n      );\n    }\n  }\n\n  if (Object.hasOwn(options, \"polyfill\")) {\n    // @ts-expect-error deprecated options\n    if (options.polyfill === false) {\n      throw new Error(\n        \"The 'polyfill' option has been removed. The @babel/runtime \" +\n          \"module now skips polyfilling by default.\",\n      );\n    } else {\n      throw new Error(\n        \"The 'polyfill' option has been removed. Use the 'corejs'\" +\n          \"option to polyfill with `core-js` via @babel/runtime.\",\n      );\n    }\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (Object.hasOwn(options, \"regenerator\")) {\n      throw new Error(\n        \"The 'regenerator' option has been removed. The generators transform \" +\n          \"no longers relies on a 'regeneratorRuntime' global. \" +\n          \"If you still need to replace imports to the 'regeneratorRuntime' \" +\n          \"global, you can use babel-plugin-polyfill-regenerator.\",\n      );\n    }\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (Object.hasOwn(options, \"useESModules\")) {\n      throw new Error(\n        \"The 'useESModules' option has been removed. @babel/runtime now uses \" +\n          \"package.json#exports to support both CommonJS and ESM helpers.\",\n      );\n    }\n  } else {\n    // @ts-expect-error(Babel 7 vs Babel 8)\n    const { useESModules = false } = options;\n    if (typeof useESModules !== \"boolean\" && useESModules !== \"auto\") {\n      throw new Error(\n        \"The 'useESModules' option must be undefined, or a boolean, or 'auto'.\",\n      );\n    }\n\n    // eslint-disable-next-line no-var\n    var esModules =\n      useESModules === \"auto\"\n        ? api.caller(\n            // @ts-expect-error CallerMetadata does not define supportsStaticESM\n            caller => !!caller?.supportsStaticESM,\n          )\n        : useESModules;\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (Object.hasOwn(options, \"helpers\")) {\n      throw new Error(\n        \"The 'helpers' option has been removed. \" +\n          \"Remove the plugin from your config if \" +\n          \"you want to disable helpers import injection.\",\n      );\n    }\n  } else {\n    // eslint-disable-next-line no-var\n    var { helpers: useRuntimeHelpers = true } = options;\n\n    if (typeof useRuntimeHelpers !== \"boolean\") {\n      throw new Error(\"The 'helpers' option must be undefined, or a boolean.\");\n    }\n  }\n\n  const HEADER_HELPERS = new Set([\n    \"interopRequireWildcard\",\n    \"interopRequireDefault\",\n  ]);\n\n  return {\n    name: \"transform-runtime\",\n\n    inherits: process.env.BABEL_8_BREAKING\n      ? undefined\n      : babel7.createPolyfillPlugins(options, runtimeVersion, absoluteRuntime),\n\n    pre(file) {\n      if (!process.env.BABEL_8_BREAKING && !useRuntimeHelpers) return;\n\n      let modulePath: string;\n\n      file.set(\"helperGenerator\", (name: string) => {\n        modulePath ??= getRuntimePath(\n          moduleName ??\n            file.get(\"runtimeHelpersModuleName\") ??\n            \"@babel/runtime\",\n          dirname,\n          absoluteRuntime,\n        );\n\n        // If the helper didn't exist yet at the version given, we bail\n        // out and let Babel either insert it directly, or throw an error\n        // so that plugins can handle that case properly.\n        if (!process.env.BABEL_8_BREAKING) {\n          if (!file.availableHelper?.(name, runtimeVersion)) {\n            if (name === \"regeneratorRuntime\") {\n              // For regeneratorRuntime, we can fallback to the old behavior of\n              // relying on the regeneratorRuntime global. If the 'regenerator'\n              // option is not disabled, babel-plugin-polyfill-regenerator will\n              // then replace it with a @babel/helpers/regenerator import.\n              //\n              // We must wrap it in a function, because built-in Babel helpers\n              // are functions.\n              return t.arrowFunctionExpression(\n                [],\n                t.identifier(\"regeneratorRuntime\"),\n              );\n            }\n            if (\n              name === \"regenerator\" ||\n              name === \"regeneratorKeys\" ||\n              name === \"regeneratorAsync\" ||\n              name === \"regeneratorAsyncGen\"\n            ) {\n              // See the `newHelpersAvailable` function in\n              // babel-plugin-transform-regenerator/src/regenerator/util.ts\n              return t.identifier(\"__interal_marker_fallback_regenerator__\");\n            }\n            return;\n          }\n        } else {\n          if (!file.availableHelper(name, runtimeVersion)) return;\n        }\n\n        // Explicitly set the CommonJS interop helpers to their reserve\n        // blockHoist of 4 so they are guaranteed to exist\n        // when other things used them to import.\n        const blockHoist =\n          HEADER_HELPERS.has(name) && !isModule(file.path) ? 4 : undefined;\n\n        let helperPath = `${modulePath}/helpers/${\n          !process.env.BABEL_8_BREAKING &&\n          esModules &&\n          file.path.node.sourceType === \"module\"\n            ? \"esm/\" + name\n            : name\n        }`;\n        if (absoluteRuntime) helperPath = resolveFSPath(helperPath);\n\n        return addDefaultImport(helperPath, name, blockHoist, true);\n      });\n\n      const cache = new Map();\n\n      function addDefaultImport(\n        source: string,\n        nameHint: string,\n        blockHoist: number,\n        isHelper = false,\n      ) {\n        // If something on the page adds a helper when the file is an ES6\n        // file, we can't reused the cached helper name after things have been\n        // transformed because it has almost certainly been renamed.\n        const cacheKey = isModule(file.path);\n        const key = `${source}:${nameHint}:${cacheKey || \"\"}`;\n\n        let cached = cache.get(key);\n        if (cached) {\n          cached = t.cloneNode(cached);\n        } else {\n          cached = addDefault(file.path, source, {\n            importedInterop: (\n              process.env.BABEL_8_BREAKING\n                ? isHelper\n                : isHelper && supportsCJSDefault\n            )\n              ? \"compiled\"\n              : \"uncompiled\",\n            nameHint,\n            blockHoist,\n          });\n\n          cache.set(key, cached);\n        }\n        return cached;\n      }\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAGA,IAAAK,OAAA,GAAAL,OAAA;AAAsF,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAUvE,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,EAAEC,OAAO,KAAK;EACzDF,GAAG,CAACG,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IACJC,OAAO,EAAEC,cAAc,GAAG,cAAc;IACxCC,eAAe,GAAG,KAAK;IACvBC,UAAU,GAAG;EACf,CAAC,GAAGN,OAAO;EAEX,IACE,OAAOK,eAAe,KAAK,SAAS,IACpC,OAAOA,eAAe,KAAK,QAAQ,EACnC;IACA,MAAM,IAAIE,KAAK,CACb,yEACF,CAAC;EACH;EAEA,IAAI,OAAOH,cAAc,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIG,KAAK,CAAC,gDAAgD,CAAC;EACnE;EAEA,IAAID,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACzD,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEmC;IAIjC,MAAMC,iBAAiB,GAAG,QAAQ;IAElC,IAAIC,kBAAkB,GAAG,IAAAC,sBAAa,EAACF,iBAAiB,EAAEJ,cAAc,CAAC;EAC3E;EAEA,IAAIO,cAAA,CAAAC,IAAA,CAAcZ,OAAO,EAAE,aAAa,CAAC,EAAE;IAEzC,IAAIA,OAAO,CAACa,WAAW,EAAE;MACvB,MAAM,IAAIN,KAAK,CACb,gEAAgE,GAC9D,sCACJ,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIA,KAAK,CACb,6DAA6D,GAC3D,uDACJ,CAAC;IACH;EACF;EAEA,IAAII,cAAA,CAAAC,IAAA,CAAcZ,OAAO,EAAE,UAAU,CAAC,EAAE;IAEtC,IAAIA,OAAO,CAACc,QAAQ,KAAK,KAAK,EAAE;MAC9B,MAAM,IAAIP,KAAK,CACb,6DAA6D,GAC3D,0CACJ,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIA,KAAK,CACb,0DAA0D,GACxD,uDACJ,CAAC;IACH;EACF;EAAC;EAoBM;IAEL,MAAM;MAAEQ,YAAY,GAAG;IAAM,CAAC,GAAGf,OAAO;IACxC,IAAI,OAAOe,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,MAAM,EAAE;MAChE,MAAM,IAAIR,KAAK,CACb,uEACF,CAAC;IACH;IAGA,IAAIS,SAAS,GACXD,YAAY,KAAK,MAAM,GACnBhB,GAAG,CAACkB,MAAM,CAERA,MAAM,IAAI,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEC,iBAAiB,CACvC,CAAC,GACDH,YAAY;EACpB;EAUO;IAEL,IAAI;MAAEI,OAAO,EAAEC,iBAAiB,GAAG;IAAK,CAAC,GAAGpB,OAAO;IAEnD,IAAI,OAAOoB,iBAAiB,KAAK,SAAS,EAAE;MAC1C,MAAM,IAAIb,KAAK,CAAC,uDAAuD,CAAC;IAC1E;EACF;EAEA,MAAMc,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC7B,wBAAwB,EACxB,uBAAuB,CACxB,CAAC;EAEF,OAAO;IACLC,IAAI,EAAE,mBAAmB;IAEzBC,QAAQ,EAEJC,OAAM,CAACC,qBAAqB,CAAC1B,OAAO,EAAEI,cAAc,EAAEC,eAAe,CAAC;IAE1EsB,GAAGA,CAACC,IAAI,EAAE;MACR,IAAqC,CAACR,iBAAiB,EAAE;MAEzD,IAAIS,UAAkB;MAEtBD,IAAI,CAACE,GAAG,CAAC,iBAAiB,EAAGP,IAAY,IAAK;QAAA,IAAAQ,IAAA;QAC5CF,UAAU,WAAVA,UAAU,GAAVA,UAAU,GAAK,IAAAG,cAAc,GAAAD,IAAA,GAC3BzB,UAAU,WAAVA,UAAU,GACRsB,IAAI,CAACK,GAAG,CAAC,0BAA0B,CAAC,YAAAF,IAAA,GACpC,gBAAgB,EAClB9B,OAAO,EACPI,eACF,CAAC;QAKkC;UACjC,IAAI,EAACuB,IAAI,CAACM,eAAe,YAApBN,IAAI,CAACM,eAAe,CAAGX,IAAI,EAAEnB,cAAc,CAAC,GAAE;YACjD,IAAImB,IAAI,KAAK,oBAAoB,EAAE;cAQjC,OAAOY,WAAC,CAACC,uBAAuB,CAC9B,EAAE,EACFD,WAAC,CAACE,UAAU,CAAC,oBAAoB,CACnC,CAAC;YACH;YACA,IACEd,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,qBAAqB,EAC9B;cAGA,OAAOY,WAAC,CAACE,UAAU,CAAC,yCAAyC,CAAC;YAChE;YACA;UACF;QACF;QAOA,MAAMC,UAAU,GACdjB,cAAc,CAACkB,GAAG,CAAChB,IAAI,CAAC,IAAI,CAAC,IAAAiB,6BAAQ,EAACZ,IAAI,CAACa,IAAI,CAAC,GAAG,CAAC,GAAGC,SAAS;QAElE,IAAIC,UAAU,GAAG,GAAGd,UAAU,YAE5Bb,SAAS,IACTY,IAAI,CAACa,IAAI,CAACG,IAAI,CAACC,UAAU,KAAK,QAAQ,GAClC,MAAM,GAAGtB,IAAI,GACbA,IAAI,EACR;QACF,IAAIlB,eAAe,EAAEsC,UAAU,GAAG,IAAAG,oBAAa,EAACH,UAAU,CAAC;QAE3D,OAAOI,gBAAgB,CAACJ,UAAU,EAAEpB,IAAI,EAAEe,UAAU,EAAE,IAAI,CAAC;MAC7D,CAAC,CAAC;MAEF,MAAMU,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;MAEvB,SAASF,gBAAgBA,CACvBG,MAAc,EACdC,QAAgB,EAChBb,UAAkB,EAClBc,QAAQ,GAAG,KAAK,EAChB;QAIA,MAAMC,QAAQ,GAAG,IAAAb,6BAAQ,EAACZ,IAAI,CAACa,IAAI,CAAC;QACpC,MAAMa,GAAG,GAAG,GAAGJ,MAAM,IAAIC,QAAQ,IAAIE,QAAQ,IAAI,EAAE,EAAE;QAErD,IAAIE,MAAM,GAAGP,KAAK,CAACf,GAAG,CAACqB,GAAG,CAAC;QAC3B,IAAIC,MAAM,EAAE;UACVA,MAAM,GAAGpB,WAAC,CAACqB,SAAS,CAACD,MAAM,CAAC;QAC9B,CAAC,MAAM;UACLA,MAAM,GAAG,IAAAE,+BAAU,EAAC7B,IAAI,CAACa,IAAI,EAAES,MAAM,EAAE;YACrCQ,eAAe,EAGTN,QAAQ,IAAI3C,kBAAkB,GAEhC,UAAU,GACV,YAAY;YAChB0C,QAAQ;YACRb;UACF,CAAC,CAAC;UAEFU,KAAK,CAAClB,GAAG,CAACwB,GAAG,EAAEC,MAAM,CAAC;QACxB;QACA,OAAOA,MAAM;MACf;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}