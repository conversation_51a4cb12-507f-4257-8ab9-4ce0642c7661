import*as e from"../../core/common/common.js";import*as s from"../../core/root/root.js";import*as l from"../../core/sdk/sdk.js";import*as t from"../../ui/legacy/legacy.js";let i;class d extends e.ObjectWrapper.ObjectWrapper{#e;#s="";#l=[];#t=[];#i=null;constructor(){super(),l.TargetManager.TargetManager.instance().addModelListener(l.AutofillModel.AutofillModel,"AddressFormFilled",this.#d,this,{scoped:!0}),this.#e=e.Settings.Settings.instance().createSetting("auto-open-autofill-view-on-event",!0)}static instance(e={forceNew:null}){const{forceNew:s}=e;return i&&!s||(i=new d),i}async#d({data:e}){s.Runtime.experiments.isEnabled("autofill-view")&&this.#e.get()&&await t.ViewManager.ViewManager.instance().showView("autofill-view"),this.#i=e.autofillModel,this.#a(e.event),this.#s&&this.dispatchEventToListeners("AddressFormFilled",{address:this.#s,filledFields:this.#l,matches:this.#t,autofillModel:this.#i})}getLastFilledAddressForm(){return this.#s&&this.#i?{address:this.#s,filledFields:this.#l,matches:this.#t,autofillModel:this.#i}:null}#a({addressUi:e,filledFields:s}){this.#s=e.addressFields.map((e=>(e=>e.fields.filter((e=>e.value.length)).map((e=>e.value)).join(" "))(e))).filter((e=>e.length)).join("\n"),this.#l=s,this.#t=[];for(let e=0;e<this.#l.length;e++){if(""===this.#l[e].value)continue;const s=this.#l[e].value.replaceAll(/[.,]*\s+/g," "),l=this.#s.replaceAll(/\s/g," ").matchAll(new RegExp(s,"g"));for(const s of l)void 0!==s.index&&this.#t.push({startIndex:s.index,endIndex:s.index+s[0].length,filledFieldIndex:e})}}}var a=Object.freeze({__proto__:null,AutofillManager:d});export{a as AutofillManager};
