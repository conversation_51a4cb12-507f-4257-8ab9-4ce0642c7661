{"author": {"name": "<PERSON>", "url": "http://github.com/broofa", "email": "<EMAIL>"}, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}, "contributors": [{"name": "<PERSON>", "url": "http://github.com/bentomas", "email": "<EMAIL>"}], "description": "A comprehensive library for mime-type mapping", "license": "MIT", "dependencies": {}, "devDependencies": {"github-release-notes": "0.13.1", "mime-db": "1.31.0", "mime-score": "1.1.0"}, "scripts": {"prepare": "node src/build.js", "changelog": "gren changelog --tags=all --generate --override", "test": "node src/test.js"}, "keywords": ["util", "mime"], "main": "mime.js", "name": "mime", "repository": {"url": "https://github.com/broofa/node-mime", "type": "git"}, "version": "1.6.0"}