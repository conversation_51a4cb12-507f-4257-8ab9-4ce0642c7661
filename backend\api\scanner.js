const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration', // Nom de votre base de données
  password: '123456', // Mot de passe correct
  port: 5432,
});

// Route pour scanner un QR code et récupérer les informations du client
router.get('/scan/:qrCode', async (req, res) => {
  const { qrCode } = req.params;
  
  try {
    console.log('🔍 Scan QR Code:', qrCode);

    // Requête pour récupérer les informations du client à partir du QR code
    // Le QR code est stocké dans la table Contract (champ codeQr)
    const clientQuery = `
      SELECT 
        c.idClient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        cont.idContract,
        cont.codeQr,
        cont.dateContract,
        cont.marqueCompteur,
        cont.numSerieCompteur,
        cont.posX,
        cont.posY
      FROM Client c
      LEFT JOIN Secteur s ON c.idS = s.idS
      LEFT JOIN Contract cont ON c.idClient = cont.idClient
      WHERE cont.codeQr = $1
    `;

    const clientResult = await pool.query(clientQuery, [qrCode]);

    if (clientResult.rows.length === 0) {
      return res.json({
        success: false,
        message: 'QR Code non trouvé dans la base de données'
      });
    }

    const clientData = clientResult.rows[0];

    // Récupérer la dernière consommation du client
    const consommationQuery = `
      SELECT 
        cons.idCons,
        cons.consommationPre,
        cons.consommationActuelle,
        cons.jours,
        cons.periode,
        cons.status
      FROM Consommation cons
      WHERE cons.idCont = $1
      ORDER BY cons.periode DESC
      LIMIT 1
    `;

    const consommationResult = await pool.query(consommationQuery, [clientData.idcontract]);
    const lastConsommation = consommationResult.rows.length > 0 ? consommationResult.rows[0] : null;

    // Récupérer toutes les factures du client
    const facturesQuery = `
      SELECT 
        f.idFact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status
      FROM Facture f
      LEFT JOIN Consommation cons ON f.idConst = cons.idCons
      WHERE cons.idCont = $1
      ORDER BY f.date DESC
    `;

    const facturesResult = await pool.query(facturesQuery, [clientData.idcontract]);

    // Préparer la réponse avec toutes les informations
    const response = {
      success: true,
      data: {
        client: {
          idClient: clientData.idclient,
          nom: clientData.nom,
          prenom: clientData.prenom,
          adresse: clientData.adresse,
          ville: clientData.ville,
          tel: clientData.tel,
          email: clientData.email,
          secteur_nom: clientData.secteur_nom
        },
        contract: {
          idContract: clientData.idcontract,
          codeQr: clientData.codeqr,
          dateContract: clientData.datecontract,
          marqueCompteur: clientData.marquecompteur,
          numSerieCompteur: clientData.numseriecompteur,
          posX: clientData.posx,
          posY: clientData.posy
        },
        lastConsommation: lastConsommation ? {
          idCons: lastConsommation.idcons,
          consommationPre: lastConsommation.consommationpre,
          consommationActuelle: lastConsommation.consommationactuelle,
          jours: lastConsommation.jours,
          periode: lastConsommation.periode,
          status: lastConsommation.status
        } : null,
        factures: facturesResult.rows.map(facture => ({
          idFact: facture.idfact,
          date: facture.date,
          montant: facture.montant,
          periode: facture.periode,
          reference: facture.reference,
          status: facture.status
        }))
      }
    };

    console.log('✅ Client trouvé:', response.data.client.nom, response.data.client.prenom);
    res.json(response);

  } catch (error) {
    console.error('❌ Erreur lors du scan:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du scan du QR code',
      error: error.message
    });
  }
});

// Route pour tester la connexion à la base de données
router.get('/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW()');
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Route pour lister tous les QR codes disponibles (pour les tests)
router.get('/qr-codes', async (req, res) => {
  try {
    const query = `
      SELECT 
        cont.codeQr,
        c.nom,
        c.prenom,
        cont.marqueCompteur,
        cont.numSerieCompteur
      FROM Contract cont
      LEFT JOIN Client c ON cont.idClient = c.idClient
      WHERE cont.codeQr IS NOT NULL
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    
    res.json({
      success: true,
      qrCodes: result.rows.map(row => ({
        codeQr: row.codeqr,
        client: `${row.nom} ${row.prenom}`,
        compteur: `${row.marquecompteur} - ${row.numseriecompteur}`
      }))
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des QR codes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des QR codes',
      error: error.message
    });
  }
});

module.exports = router;
