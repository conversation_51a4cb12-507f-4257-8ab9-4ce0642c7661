{"version": 3, "sources": ["../../../src/prebuild/updatePackageJson.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport path from 'path';\nimport { intersects as semverIntersects, Range as SemverRange } from 'semver';\n\nimport * as Log from '../log';\nimport { isModuleSymlinked } from '../utils/isModuleSymlinked';\nimport { logNewSection } from '../utils/ora';\n\nexport type DependenciesMap = { [key: string]: string | number };\n\nexport type DependenciesModificationResults = {\n  /** A list of new values were added to the `dependencies` object in the `package.json`. */\n  changedDependencies: string[];\n};\n\n/** Modifies the `package.json` with `modifyPackageJson` and format/displays the results. */\nexport async function updatePackageJSONAsync(\n  projectRoot: string,\n  {\n    templateDirectory,\n    templatePkg = getPackageJson(templateDirectory),\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templateDirectory: string;\n    templatePkg?: PackageJSONConfig;\n    pkg: PackageJSONConfig;\n    skipDependencyUpdate?: string[];\n  }\n): Promise<DependenciesModificationResults> {\n  const updatingPackageJsonStep = logNewSection('Updating package.json');\n\n  const results = modifyPackageJson(projectRoot, {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  const hasChanges = results.changedDependencies.length || results.scriptsChanged;\n\n  // NOTE: This is effectively bundler caching and subject to breakage if the inputs don't match the mutations.\n  if (hasChanges) {\n    await fs.promises.writeFile(\n      path.resolve(projectRoot, 'package.json'),\n      // Add new line to match the format of running yarn.\n      // This prevents the `package.json` from changing when running `prebuild --no-install` multiple times.\n      JSON.stringify(pkg, null, 2) + '\\n'\n    );\n  }\n\n  updatingPackageJsonStep.succeed(\n    'Updated package.json' + (hasChanges ? '' : chalk.dim(` | no changes`))\n  );\n\n  return results;\n}\n\n/**\n * Make required modifications to the `package.json` file as a JSON object.\n *\n * 1. Update `package.json` `scripts`.\n * 2. Update `package.json` `dependencies` (not `devDependencies`).\n * 3. Update `package.json` `main`.\n *\n * @param projectRoot The root directory of the project.\n * @param templatePkg Template project package.json as JSON.\n * @param pkg Current package.json as JSON.\n * @param skipDependencyUpdate Array of dependencies to skip updating.\n * @returns\n */\nfunction modifyPackageJson(\n  projectRoot: string,\n  {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templatePkg: PackageJSONConfig;\n    pkg: PackageJSONConfig;\n    /** @deprecated Required packages are not overwritten, only added when missing */\n    skipDependencyUpdate?: string[];\n  }\n) {\n  const scriptsChanged = updatePkgScripts({ pkg });\n\n  // TODO: Move to `npx expo-doctor`\n  return {\n    scriptsChanged,\n    ...updatePkgDependencies(projectRoot, {\n      pkg,\n      templatePkg,\n      skipDependencyUpdate,\n    }),\n  };\n}\n\n/**\n * Update `package.json` dependencies by combining the `dependencies` in the\n * project we are creating with the dependencies in the template project.\n *\n * > Exposed for testing.\n */\nexport function updatePkgDependencies(\n  projectRoot: string,\n  {\n    pkg,\n    templatePkg,\n    skipDependencyUpdate = [],\n  }: {\n    pkg: PackageJSONConfig;\n    templatePkg: PackageJSONConfig;\n    /** @deprecated Required packages are not overwritten, only added when missing */\n    skipDependencyUpdate?: string[];\n  }\n): DependenciesModificationResults {\n  const { dependencies } = templatePkg;\n  // The default values come from the bare-minimum template's package.json.\n  // Users can change this by using different templates with the `--template` flag.\n  // The main reason for allowing the changing of dependencies would be to include\n  // dependencies that are required for the native project to build. For example,\n  // it does not need to include dependencies that are used in the JS-code only.\n  const defaultDependencies = createDependenciesMap(dependencies);\n\n  // NOTE: This is a hack to ensure this doesn't trigger an extraneous change in the `package.json`\n  // it isn't required for anything in the `ios` and `android` folders.\n  delete defaultDependencies['expo-status-bar'];\n  // NOTE: Expo splash screen is installed by default in the template but the config plugin also lives in prebuild-config\n  // so we can delete it to prevent an extraneous change in the `package.json`.\n  delete defaultDependencies['expo-splash-screen'];\n\n  const combinedDependencies: DependenciesMap = createDependenciesMap({\n    ...defaultDependencies,\n    ...pkg.dependencies,\n  });\n\n  // These dependencies are only added, not overwritten from the project\n  const requiredDependencies = [\n    // TODO: This is no longer required because it's this same package.\n    'expo',\n    // TODO: Drop this somehow.\n    'react-native',\n  ].filter((depKey) => !!defaultDependencies[depKey]);\n\n  const symlinkedPackages: [string, string][] = [];\n  const nonRecommendedPackages: [string, string][] = [];\n\n  for (const dependencyKey of requiredDependencies) {\n    // If the local package.json defined the dependency that we want to overwrite...\n    if (pkg.dependencies?.[dependencyKey]) {\n      // Then ensure it isn't symlinked (i.e. the user has a custom version in their yarn workspace).\n      if (isModuleSymlinked(projectRoot, { moduleId: dependencyKey, isSilent: true })) {\n        // If the package is in the project's package.json and it's symlinked, then skip overwriting it.\n        symlinkedPackages.push([\n          `${dependencyKey}`,\n          `${dependencyKey}@${defaultDependencies[dependencyKey]}`,\n        ]);\n        continue;\n      }\n\n      // Do not modify manually skipped dependencies\n      if (skipDependencyUpdate.includes(dependencyKey)) {\n        continue;\n      }\n\n      // Warn users for outdated dependencies when prebuilding\n      const hasRecommendedVersion = versionRangesIntersect(\n        pkg.dependencies[dependencyKey],\n        String(defaultDependencies[dependencyKey])\n      );\n      if (!hasRecommendedVersion) {\n        nonRecommendedPackages.push([\n          `${dependencyKey}@${pkg.dependencies[dependencyKey]}`,\n          `${dependencyKey}@${defaultDependencies[dependencyKey]}`,\n        ]);\n      }\n    }\n  }\n\n  if (symlinkedPackages.length) {\n    symlinkedPackages.forEach(([current, recommended]) => {\n      Log.log(\n        `\\u203A Using symlinked ${chalk.bold(current)} instead of recommended ${chalk.bold(recommended)}.`\n      );\n    });\n  }\n\n  if (nonRecommendedPackages.length) {\n    nonRecommendedPackages.forEach(([current, recommended]) => {\n      Log.warn(\n        `\\u203A Using ${chalk.bold(current)} instead of recommended ${chalk.bold(recommended)}.`\n      );\n    });\n  }\n\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies =\n    hashForDependencyMap(pkg.dependencies) !== hashForDependencyMap(combinedDependencies);\n  // Save the dependencies\n  let changedDependencies: string[] = [];\n  if (hasNewDependencies) {\n    changedDependencies = diffKeys(combinedDependencies, pkg.dependencies ?? {}).sort();\n    // Use Object.assign to preserve the original order of dependencies, this makes it easier to see what changed in the git diff.\n    pkg.dependencies = Object.assign(pkg.dependencies ?? {}, combinedDependencies);\n  }\n\n  return {\n    changedDependencies,\n  };\n}\n\nfunction diffKeys(a: Record<string, any>, b: Record<string, any>): string[] {\n  return Object.keys(a).filter((key) => a[key] !== b[key]);\n}\n\n/**\n * Create an object of type DependenciesMap a dependencies object or throw if not valid.\n *\n * @param dependencies - ideally an object of type {[key]: string} - if not then this will error.\n */\nexport function createDependenciesMap(dependencies: any): DependenciesMap {\n  if (typeof dependencies !== 'object') {\n    throw new Error(`Dependency map is invalid, expected object but got ${typeof dependencies}`);\n  } else if (!dependencies) {\n    return {};\n  }\n\n  const outputMap: DependenciesMap = {};\n\n  for (const key of Object.keys(dependencies)) {\n    const value = dependencies[key];\n    if (typeof value === 'string') {\n      outputMap[key] = value;\n    } else {\n      throw new Error(\n        `Dependency for key \\`${key}\\` should be a \\`string\\`, instead got: \\`{ ${key}: ${JSON.stringify(\n          value\n        )} }\\``\n      );\n    }\n  }\n  return outputMap;\n}\n\n/**\n * Updates the package.json scripts for prebuild if the scripts match\n * the default values used in project templates.\n */\nexport function updatePkgScripts({ pkg }: { pkg: PackageJSONConfig }) {\n  let hasChanged = false;\n  if (!pkg.scripts) {\n    pkg.scripts = {};\n  }\n  if (\n    !pkg.scripts.android ||\n    pkg.scripts.android === 'expo start --android' ||\n    pkg.scripts.android === 'react-native run-android'\n  ) {\n    pkg.scripts.android = 'expo run:android';\n    hasChanged = true;\n  }\n  if (\n    !pkg.scripts.ios ||\n    pkg.scripts.ios === 'expo start --ios' ||\n    pkg.scripts.ios === 'react-native run-ios'\n  ) {\n    pkg.scripts.ios = 'expo run:ios';\n    hasChanged = true;\n  }\n  return hasChanged;\n}\n\nfunction normalizeDependencyMap(deps: DependenciesMap): string[] {\n  return Object.keys(deps)\n    .map((dependency) => `${dependency}@${deps[dependency]}`)\n    .sort();\n}\n\nexport function hashForDependencyMap(deps: DependenciesMap = {}): string {\n  const depsList = normalizeDependencyMap(deps);\n  const depsString = depsList.join('\\n');\n  return createFileHash(depsString);\n}\n\nexport function createFileHash(contents: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  return crypto.createHash('sha1').update(contents).digest('hex');\n}\n\n/**\n * Determine if two semver ranges are overlapping or intersecting.\n * This is a safe version of `semver.intersects` that does not throw.\n */\nfunction versionRangesIntersect(rangeA: string | SemverRange, rangeB: string | SemverRange) {\n  try {\n    return semverIntersects(rangeA, rangeB);\n  } catch {\n    return false;\n  }\n}\n"], "names": ["createDependenciesMap", "createFileHash", "hashForDependencyMap", "updatePackageJSONAsync", "updatePkgDependencies", "updatePkgScripts", "projectRoot", "templateDirectory", "templatePkg", "getPackageJson", "pkg", "skipDependencyUpdate", "updatingPackageJsonStep", "logNewSection", "results", "modifyPackageJson", "has<PERSON><PERSON><PERSON>", "changedDependencies", "length", "scriptsChanged", "fs", "promises", "writeFile", "path", "resolve", "JSON", "stringify", "succeed", "chalk", "dim", "dependencies", "defaultDependencies", "combinedDependencies", "requiredDependencies", "filter", "<PERSON><PERSON><PERSON><PERSON>", "symlinkedPackages", "nonRecommendedPackages", "dependencyKey", "isModuleSymlinked", "moduleId", "isSilent", "push", "includes", "hasRecommendedVersion", "versionRangesIntersect", "String", "for<PERSON>ach", "current", "recommended", "Log", "log", "bold", "warn", "hasNewDependencies", "diff<PERSON>eys", "sort", "Object", "assign", "a", "b", "keys", "key", "Error", "outputMap", "value", "has<PERSON><PERSON>ed", "scripts", "android", "ios", "normalizeDependencyMap", "deps", "map", "dependency", "depsList", "depsString", "join", "contents", "crypto", "createHash", "update", "digest", "rangeA", "rangeB", "semverIntersects"], "mappings": ";;;;;;;;;;;IA8NgBA,qBAAqB;eAArBA;;IAgEAC,cAAc;eAAdA;;IANAC,oBAAoB;eAApBA;;IArQMC,sBAAsB;eAAtBA;;IAsFNC,qBAAqB;eAArBA;;IAiJAC,gBAAgB;eAAhBA;;;;yBA1PkC;;;;;;;gEAChC;;;;;;;gEACC;;;;;;;gEACJ;;;;;;;gEACE;;;;;;;yBACoD;;;;;;6DAEhD;mCACa;qBACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB,eAAeF,uBACpBG,WAAmB,EACnB,EACEC,iBAAiB,EACjBC,cAAcC,IAAAA,wBAAc,EAACF,kBAAkB,EAC/CG,GAAG,EACHC,oBAAoB,EAMrB;IAED,MAAMC,0BAA0BC,IAAAA,kBAAa,EAAC;IAE9C,MAAMC,UAAUC,kBAAkBT,aAAa;QAC7CE;QACAE;QACAC;IACF;IAEA,MAAMK,aAAaF,QAAQG,mBAAmB,CAACC,MAAM,IAAIJ,QAAQK,cAAc;IAE/E,6GAA6G;IAC7G,IAAIH,YAAY;QACd,MAAMI,aAAE,CAACC,QAAQ,CAACC,SAAS,CACzBC,eAAI,CAACC,OAAO,CAAClB,aAAa,iBAC1B,oDAAoD;QACpD,sGAAsG;QACtGmB,KAAKC,SAAS,CAAChB,KAAK,MAAM,KAAK;IAEnC;IAEAE,wBAAwBe,OAAO,CAC7B,yBAA0BX,CAAAA,aAAa,KAAKY,gBAAK,CAACC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAA;IAGvE,OAAOf;AACT;AAEA;;;;;;;;;;;;CAYC,GACD,SAASC,kBACPT,WAAmB,EACnB,EACEE,WAAW,EACXE,GAAG,EACHC,oBAAoB,EAMrB;IAED,MAAMQ,iBAAiBd,iBAAiB;QAAEK;IAAI;IAE9C,kCAAkC;IAClC,OAAO;QACLS;QACA,GAAGf,sBAAsBE,aAAa;YACpCI;YACAF;YACAG;QACF,EAAE;IACJ;AACF;AAQO,SAASP,sBACdE,WAAmB,EACnB,EACEI,GAAG,EACHF,WAAW,EACXG,uBAAuB,EAAE,EAM1B;IAED,MAAM,EAAEmB,YAAY,EAAE,GAAGtB;IACzB,yEAAyE;IACzE,iFAAiF;IACjF,gFAAgF;IAChF,+EAA+E;IAC/E,8EAA8E;IAC9E,MAAMuB,sBAAsB/B,sBAAsB8B;IAElD,iGAAiG;IACjG,qEAAqE;IACrE,OAAOC,mBAAmB,CAAC,kBAAkB;IAC7C,uHAAuH;IACvH,6EAA6E;IAC7E,OAAOA,mBAAmB,CAAC,qBAAqB;IAEhD,MAAMC,uBAAwChC,sBAAsB;QAClE,GAAG+B,mBAAmB;QACtB,GAAGrB,IAAIoB,YAAY;IACrB;IAEA,sEAAsE;IACtE,MAAMG,uBAAuB;QAC3B,mEAAmE;QACnE;QACA,2BAA2B;QAC3B;KACD,CAACC,MAAM,CAAC,CAACC,SAAW,CAAC,CAACJ,mBAAmB,CAACI,OAAO;IAElD,MAAMC,oBAAwC,EAAE;IAChD,MAAMC,yBAA6C,EAAE;IAErD,KAAK,MAAMC,iBAAiBL,qBAAsB;YAE5CvB;QADJ,gFAAgF;QAChF,KAAIA,oBAAAA,IAAIoB,YAAY,qBAAhBpB,iBAAkB,CAAC4B,cAAc,EAAE;YACrC,+FAA+F;YAC/F,IAAIC,IAAAA,oCAAiB,EAACjC,aAAa;gBAAEkC,UAAUF;gBAAeG,UAAU;YAAK,IAAI;gBAC/E,gGAAgG;gBAChGL,kBAAkBM,IAAI,CAAC;oBACrB,GAAGJ,eAAe;oBAClB,GAAGA,cAAc,CAAC,EAAEP,mBAAmB,CAACO,cAAc,EAAE;iBACzD;gBACD;YACF;YAEA,8CAA8C;YAC9C,IAAI3B,qBAAqBgC,QAAQ,CAACL,gBAAgB;gBAChD;YACF;YAEA,wDAAwD;YACxD,MAAMM,wBAAwBC,uBAC5BnC,IAAIoB,YAAY,CAACQ,cAAc,EAC/BQ,OAAOf,mBAAmB,CAACO,cAAc;YAE3C,IAAI,CAACM,uBAAuB;gBAC1BP,uBAAuBK,IAAI,CAAC;oBAC1B,GAAGJ,cAAc,CAAC,EAAE5B,IAAIoB,YAAY,CAACQ,cAAc,EAAE;oBACrD,GAAGA,cAAc,CAAC,EAAEP,mBAAmB,CAACO,cAAc,EAAE;iBACzD;YACH;QACF;IACF;IAEA,IAAIF,kBAAkBlB,MAAM,EAAE;QAC5BkB,kBAAkBW,OAAO,CAAC,CAAC,CAACC,SAASC,YAAY;YAC/CC,KAAIC,GAAG,CACL,CAAC,uBAAuB,EAAEvB,gBAAK,CAACwB,IAAI,CAACJ,SAAS,wBAAwB,EAAEpB,gBAAK,CAACwB,IAAI,CAACH,aAAa,CAAC,CAAC;QAEtG;IACF;IAEA,IAAIZ,uBAAuBnB,MAAM,EAAE;QACjCmB,uBAAuBU,OAAO,CAAC,CAAC,CAACC,SAASC,YAAY;YACpDC,KAAIG,IAAI,CACN,CAAC,aAAa,EAAEzB,gBAAK,CAACwB,IAAI,CAACJ,SAAS,wBAAwB,EAAEpB,gBAAK,CAACwB,IAAI,CAACH,aAAa,CAAC,CAAC;QAE5F;IACF;IAEA,yGAAyG;IACzG,MAAMK,qBACJpD,qBAAqBQ,IAAIoB,YAAY,MAAM5B,qBAAqB8B;IAClE,wBAAwB;IACxB,IAAIf,sBAAgC,EAAE;IACtC,IAAIqC,oBAAoB;QACtBrC,sBAAsBsC,SAASvB,sBAAsBtB,IAAIoB,YAAY,IAAI,CAAC,GAAG0B,IAAI;QACjF,8HAA8H;QAC9H9C,IAAIoB,YAAY,GAAG2B,OAAOC,MAAM,CAAChD,IAAIoB,YAAY,IAAI,CAAC,GAAGE;IAC3D;IAEA,OAAO;QACLf;IACF;AACF;AAEA,SAASsC,SAASI,CAAsB,EAAEC,CAAsB;IAC9D,OAAOH,OAAOI,IAAI,CAACF,GAAGzB,MAAM,CAAC,CAAC4B,MAAQH,CAAC,CAACG,IAAI,KAAKF,CAAC,CAACE,IAAI;AACzD;AAOO,SAAS9D,sBAAsB8B,YAAiB;IACrD,IAAI,OAAOA,iBAAiB,UAAU;QACpC,MAAM,IAAIiC,MAAM,CAAC,mDAAmD,EAAE,OAAOjC,cAAc;IAC7F,OAAO,IAAI,CAACA,cAAc;QACxB,OAAO,CAAC;IACV;IAEA,MAAMkC,YAA6B,CAAC;IAEpC,KAAK,MAAMF,OAAOL,OAAOI,IAAI,CAAC/B,cAAe;QAC3C,MAAMmC,QAAQnC,YAAY,CAACgC,IAAI;QAC/B,IAAI,OAAOG,UAAU,UAAU;YAC7BD,SAAS,CAACF,IAAI,GAAGG;QACnB,OAAO;YACL,MAAM,IAAIF,MACR,CAAC,qBAAqB,EAAED,IAAI,4CAA4C,EAAEA,IAAI,EAAE,EAAErC,KAAKC,SAAS,CAC9FuC,OACA,IAAI,CAAC;QAEX;IACF;IACA,OAAOD;AACT;AAMO,SAAS3D,iBAAiB,EAAEK,GAAG,EAA8B;IAClE,IAAIwD,aAAa;IACjB,IAAI,CAACxD,IAAIyD,OAAO,EAAE;QAChBzD,IAAIyD,OAAO,GAAG,CAAC;IACjB;IACA,IACE,CAACzD,IAAIyD,OAAO,CAACC,OAAO,IACpB1D,IAAIyD,OAAO,CAACC,OAAO,KAAK,0BACxB1D,IAAIyD,OAAO,CAACC,OAAO,KAAK,4BACxB;QACA1D,IAAIyD,OAAO,CAACC,OAAO,GAAG;QACtBF,aAAa;IACf;IACA,IACE,CAACxD,IAAIyD,OAAO,CAACE,GAAG,IAChB3D,IAAIyD,OAAO,CAACE,GAAG,KAAK,sBACpB3D,IAAIyD,OAAO,CAACE,GAAG,KAAK,wBACpB;QACA3D,IAAIyD,OAAO,CAACE,GAAG,GAAG;QAClBH,aAAa;IACf;IACA,OAAOA;AACT;AAEA,SAASI,uBAAuBC,IAAqB;IACnD,OAAOd,OAAOI,IAAI,CAACU,MAChBC,GAAG,CAAC,CAACC,aAAe,GAAGA,WAAW,CAAC,EAAEF,IAAI,CAACE,WAAW,EAAE,EACvDjB,IAAI;AACT;AAEO,SAAStD,qBAAqBqE,OAAwB,CAAC,CAAC;IAC7D,MAAMG,WAAWJ,uBAAuBC;IACxC,MAAMI,aAAaD,SAASE,IAAI,CAAC;IACjC,OAAO3E,eAAe0E;AACxB;AAEO,SAAS1E,eAAe4E,QAAgB;IAC7C,0DAA0D;IAC1D,OAAOC,iBAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACH,UAAUI,MAAM,CAAC;AAC3D;AAEA;;;CAGC,GACD,SAASpC,uBAAuBqC,MAA4B,EAAEC,MAA4B;IACxF,IAAI;QACF,OAAOC,IAAAA,oBAAgB,EAACF,QAAQC;IAClC,EAAE,OAAM;QACN,OAAO;IACT;AACF"}