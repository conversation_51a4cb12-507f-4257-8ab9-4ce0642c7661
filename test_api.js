// Test simple pour vérifier l'API
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';

async function testAPI() {
  console.log('🧪 Test de l\'API Facturation');
  console.log('================================');

  try {
    // Test 1: Récupérer tous les clients
    console.log('\n1️⃣ Test: Récupération des clients');
    const clientsResponse = await fetch(`${API_BASE_URL}/api/clients`);
    const clientsData = await clientsResponse.json();
    
    if (clientsData.success) {
      console.log(`✅ ${clientsData.data.length} clients trouvés`);
      
      if (clientsData.data.length > 0) {
        const firstClient = clientsData.data[0];
        console.log(`📋 Premier client: ${firstClient.nom} ${firstClient.prenom} (ID: ${firstClient.idclient})`);
        
        // Test 2: Récupérer les contrats du premier client
        console.log(`\n2️⃣ Test: Contrats du client ${firstClient.idclient}`);
        const contractsResponse = await fetch(`${API_BASE_URL}/api/clients/${firstClient.idclient}/contracts`);
        const contractsData = await contractsResponse.json();
        
        if (contractsData.success) {
          console.log(`✅ ${contractsData.data.length} contrat(s) trouvé(s)`);
          
          if (contractsData.data.length > 0) {
            const firstContract = contractsData.data[0];
            console.log(`📋 Premier contrat: ID ${firstContract.idcontract}, QR: ${firstContract.codeqr}`);
            
            // Test 3: Récupérer la dernière consommation du contrat
            console.log(`\n3️⃣ Test: Dernière consommation du contrat ${firstContract.idcontract}`);
            const consommationResponse = await fetch(`${API_BASE_URL}/api/contracts/${firstContract.idcontract}/last-consommation`);
            const consommationData = await consommationResponse.json();
            
            if (consommationData.success) {
              console.log(`✅ Dernière consommation trouvée: ${consommationData.data.consommationactuelle} m³`);
              console.log(`📅 Période: ${consommationData.data.periode}`);
            } else {
              console.log(`ℹ️ Aucune consommation trouvée pour ce contrat`);
            }
          }
        } else {
          console.log(`❌ Erreur contrats: ${contractsData.message}`);
        }
      }
    } else {
      console.log(`❌ Erreur clients: ${clientsData.message}`);
    }

  } catch (error) {
    console.error('❌ Erreur de test:', error.message);
  }
}

// Exécuter le test
testAPI();
