{"version": 3, "file": "BuildProperties.js", "names": ["_androidPlugins", "data", "require", "createBuildGradlePropsConfigPlugin", "configToPropertyRules", "name", "withUnknown", "config", "sourceConfig", "withGradleProperties", "modResults", "updateAndroidBuildPropertiesFromConfig", "Object", "defineProperty", "value", "withJsEngineGradleProps", "exports", "propName", "propValueGetter", "android", "jsEngine", "toString", "withNewArchEnabledGradleProps", "newArchEnabled", "gradleProperties", "configToProperty", "updateAndroidBuildProperty", "options", "oldPropIndex", "findIndex", "prop", "type", "key", "oldProp", "newProp", "prevValue", "JSON", "parse", "newValue", "Array", "isArray", "prevArrayWithStringifiedValues", "map", "v", "stringify", "newArrayWithStringifiedValues", "mergedValues", "Set", "push", "removePropWhenValueIsNull", "splice"], "sources": ["../../src/android/BuildProperties.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\n\nimport type { PropertiesItem } from './Properties';\nimport type { ConfigPlugin } from '../Plugin.types';\nimport { withGradleProperties } from '../plugins/android-plugins';\nimport { BuildPropertiesConfig, ConfigToPropertyRuleType } from '../utils/BuildProperties.types';\n\n/**\n * Creates a `withGradleProperties` config-plugin based on given config to property mapping rules.\n *\n * The factory supports two modes from generic type inference\n * ```ts\n * // config-plugin without `props`, it will implicitly use the expo config as source config.\n * createBuildGradlePropsConfigPlugin<ExpoConfig>(): ConfigPlugin<void>;\n *\n * // config-plugin with a parameter `props: CustomType`, it will use the `props` as source config.\n * createBuildGradlePropsConfigPlugin<CustomType>(): ConfigPlugin<CustomType>;\n * ```\n *\n * @param configToPropertyRules config to property mapping rules\n * @param name the config plugin name\n */\nexport function createBuildGradlePropsConfigPlugin<SourceConfigType extends BuildPropertiesConfig>(\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[],\n  name?: string\n) {\n  const withUnknown: ConfigPlugin<SourceConfigType extends ExpoConfig ? void : SourceConfigType> = (\n    config,\n    sourceConfig\n  ) =>\n    withGradleProperties(config, (config) => {\n      config.modResults = updateAndroidBuildPropertiesFromConfig(\n        (sourceConfig ?? config) as SourceConfigType,\n        config.modResults,\n        configToPropertyRules\n      );\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * A config-plugin to update `android/gradle.properties` from the `jsEngine` in expo config\n */\nexport const withJsEngineGradleProps = createBuildGradlePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'hermesEnabled',\n      propValueGetter: (config) =>\n        ((config.android?.jsEngine ?? config.jsEngine ?? 'hermes') === 'hermes').toString(),\n    },\n  ],\n  'withJsEngineGradleProps'\n);\n\n/**\n * A config-plugin to update `android/gradle.properties` from the `newArchEnabled` in expo config\n */\nexport const withNewArchEnabledGradleProps = createBuildGradlePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'newArchEnabled',\n      propValueGetter: (config) =>\n        (config.android?.newArchEnabled ?? config.newArchEnabled)?.toString(),\n    },\n  ],\n  'withNewArchEnabledGradleProps'\n);\n\nexport function updateAndroidBuildPropertiesFromConfig<\n  SourceConfigType extends BuildPropertiesConfig,\n>(\n  config: SourceConfigType,\n  gradleProperties: PropertiesItem[],\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[]\n) {\n  for (const configToProperty of configToPropertyRules) {\n    const value = configToProperty.propValueGetter(config);\n    updateAndroidBuildProperty(gradleProperties, configToProperty.propName, value);\n  }\n\n  return gradleProperties;\n}\n\nexport function updateAndroidBuildProperty(\n  gradleProperties: PropertiesItem[],\n  name: string,\n  value: string | null | undefined,\n  options?: { removePropWhenValueIsNull?: boolean }\n) {\n  const oldPropIndex = gradleProperties.findIndex(\n    (prop) => prop.type === 'property' && prop.key === name\n  );\n  const oldProp = oldPropIndex >= 0 ? gradleProperties[oldPropIndex] : null;\n  if (value) {\n    // found the matched value, add or merge new property\n    const newProp: PropertiesItem = {\n      type: 'property',\n      key: name,\n      value,\n    };\n\n    if (oldProp && oldProp.type === 'property') {\n      try {\n        const prevValue = JSON.parse(oldProp.value);\n        const newValue = JSON.parse(value);\n        if (Array.isArray(prevValue) && Array.isArray(newValue)) {\n          const prevArrayWithStringifiedValues = prevValue.map((v) => JSON.stringify(v));\n          const newArrayWithStringifiedValues = newValue.map((v) => JSON.stringify(v));\n          const mergedValues = [\n            ...new Set([...prevArrayWithStringifiedValues, ...newArrayWithStringifiedValues]),\n          ].map((v) => JSON.parse(v));\n          oldProp.value = JSON.stringify(mergedValues);\n          return gradleProperties;\n        }\n      } catch {}\n      oldProp.value = value;\n      return gradleProperties;\n    }\n\n    gradleProperties.push(newProp);\n    return gradleProperties;\n  }\n  if (options?.removePropWhenValueIsNull && oldPropIndex >= 0) {\n    gradleProperties.splice(oldPropIndex, 1);\n    return gradleProperties;\n  }\n\n  return gradleProperties;\n}\n"], "mappings": ";;;;;;;;;AAIA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,kCAAkCA,CAChDC,qBAAmE,EACnEC,IAAa,EACb;EACA,MAAMC,WAAwF,GAAGA,CAC/FC,MAAM,EACNC,YAAY,KAEZ,IAAAC,sCAAoB,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACG,UAAU,GAAGC,sCAAsC,CACvDH,YAAY,IAAID,MAAM,EACvBA,MAAM,CAACG,UAAU,EACjBN,qBACF,CAAC;IACD,OAAOG,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRO,MAAM,CAACC,cAAc,CAACP,WAAW,EAAE,MAAM,EAAE;MACzCQ,KAAK,EAAET;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACO,MAAMS,uBAAuB,GAAAC,OAAA,CAAAD,uBAAA,GAAGZ,kCAAkC,CACvE,CACE;EACEc,QAAQ,EAAE,eAAe;EACzBC,eAAe,EAAGX,MAAM,IACtB,CAAC,CAACA,MAAM,CAACY,OAAO,EAAEC,QAAQ,IAAIb,MAAM,CAACa,QAAQ,IAAI,QAAQ,MAAM,QAAQ,EAAEC,QAAQ,CAAC;AACtF,CAAC,CACF,EACD,yBACF,CAAC;;AAED;AACA;AACA;AACO,MAAMC,6BAA6B,GAAAN,OAAA,CAAAM,6BAAA,GAAGnB,kCAAkC,CAC7E,CACE;EACEc,QAAQ,EAAE,gBAAgB;EAC1BC,eAAe,EAAGX,MAAM,IACtB,CAACA,MAAM,CAACY,OAAO,EAAEI,cAAc,IAAIhB,MAAM,CAACgB,cAAc,GAAGF,QAAQ,CAAC;AACxE,CAAC,CACF,EACD,+BACF,CAAC;AAEM,SAASV,sCAAsCA,CAGpDJ,MAAwB,EACxBiB,gBAAkC,EAClCpB,qBAAmE,EACnE;EACA,KAAK,MAAMqB,gBAAgB,IAAIrB,qBAAqB,EAAE;IACpD,MAAMU,KAAK,GAAGW,gBAAgB,CAACP,eAAe,CAACX,MAAM,CAAC;IACtDmB,0BAA0B,CAACF,gBAAgB,EAAEC,gBAAgB,CAACR,QAAQ,EAAEH,KAAK,CAAC;EAChF;EAEA,OAAOU,gBAAgB;AACzB;AAEO,SAASE,0BAA0BA,CACxCF,gBAAkC,EAClCnB,IAAY,EACZS,KAAgC,EAChCa,OAAiD,EACjD;EACA,MAAMC,YAAY,GAAGJ,gBAAgB,CAACK,SAAS,CAC5CC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,UAAU,IAAID,IAAI,CAACE,GAAG,KAAK3B,IACrD,CAAC;EACD,MAAM4B,OAAO,GAAGL,YAAY,IAAI,CAAC,GAAGJ,gBAAgB,CAACI,YAAY,CAAC,GAAG,IAAI;EACzE,IAAId,KAAK,EAAE;IACT;IACA,MAAMoB,OAAuB,GAAG;MAC9BH,IAAI,EAAE,UAAU;MAChBC,GAAG,EAAE3B,IAAI;MACTS;IACF,CAAC;IAED,IAAImB,OAAO,IAAIA,OAAO,CAACF,IAAI,KAAK,UAAU,EAAE;MAC1C,IAAI;QACF,MAAMI,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAACnB,KAAK,CAAC;QAC3C,MAAMwB,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACvB,KAAK,CAAC;QAClC,IAAIyB,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,IAAII,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;UACvD,MAAMG,8BAA8B,GAAGN,SAAS,CAACO,GAAG,CAAEC,CAAC,IAAKP,IAAI,CAACQ,SAAS,CAACD,CAAC,CAAC,CAAC;UAC9E,MAAME,6BAA6B,GAAGP,QAAQ,CAACI,GAAG,CAAEC,CAAC,IAAKP,IAAI,CAACQ,SAAS,CAACD,CAAC,CAAC,CAAC;UAC5E,MAAMG,YAAY,GAAG,CACnB,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGN,8BAA8B,EAAE,GAAGI,6BAA6B,CAAC,CAAC,CAClF,CAACH,GAAG,CAAEC,CAAC,IAAKP,IAAI,CAACC,KAAK,CAACM,CAAC,CAAC,CAAC;UAC3BV,OAAO,CAACnB,KAAK,GAAGsB,IAAI,CAACQ,SAAS,CAACE,YAAY,CAAC;UAC5C,OAAOtB,gBAAgB;QACzB;MACF,CAAC,CAAC,MAAM,CAAC;MACTS,OAAO,CAACnB,KAAK,GAAGA,KAAK;MACrB,OAAOU,gBAAgB;IACzB;IAEAA,gBAAgB,CAACwB,IAAI,CAACd,OAAO,CAAC;IAC9B,OAAOV,gBAAgB;EACzB;EACA,IAAIG,OAAO,EAAEsB,yBAAyB,IAAIrB,YAAY,IAAI,CAAC,EAAE;IAC3DJ,gBAAgB,CAAC0B,MAAM,CAACtB,YAAY,EAAE,CAAC,CAAC;IACxC,OAAOJ,gBAAgB;EACzB;EAEA,OAAOA,gBAAgB;AACzB", "ignoreList": []}