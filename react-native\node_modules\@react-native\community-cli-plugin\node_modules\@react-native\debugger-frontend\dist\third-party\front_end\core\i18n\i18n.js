import*as e from"../../third_party/i18n/i18n.js";import*as t from"../root/root.js";let o=null;class n{locale;lookupClosestDevToolsLocale;constructor(e){this.lookupClosestDevToolsLocale=e.lookupClosestDevToolsLocale,"browserLanguage"===e.settingLanguage?this.locale=e.navigatorLanguage||"en-US":this.locale=e.settingLanguage,this.locale=this.lookupClosestDevToolsLocale(this.locale)}static instance(e={create:!1}){if(!o&&!e.create)throw new Error("No LanguageSelector instance exists yet.");return e.create&&(o=new n(e.data)),o}static removeInstance(){o=null}forceFallbackLocale(){this.locale="en-US"}languageIsSupportedByDevTools(e){return r(e,this.lookupClosestDevToolsLocale(e))}}function r(e,t){const o=new Intl.Locale(e),n=new Intl.Locale(t);return o.language===n.language}var s=Object.freeze({__proto__:null,DevToolsLocale:n,localeLanguagesMatch:r});const i="@HOST@/remote/serve_file/@VERSION@/core/i18n/locales/@LOCALE@.json",l="./locales/@LOCALE@.json",a=new e.I18n.I18n(["af","am","ar","as","az","be","bg","bn","bs","ca","cs","cy","da","de","el","en-GB","es-419","es","et","eu","fa","fi","fil","fr-CA","fr","gl","gu","he","hi","hr","hu","hy","id","is","it","ja","ka","kk","km","kn","ko","ky","lo","lt","lv","mk","ml","mn","mr","ms","my","ne","nl","no","or","pa","pl","pt-PT","pt","ro","ru","si","sk","sl","sq","sr-Latn","sr","sv","sw","ta","te","th","tr","uk","ur","uz","vi","zh-HK","zh-TW","zu","en-US","zh"],"en-US"),c=new Set(["en-US","zh"]);function u(e,t,o={}){return e.getLocalizedStringSetFor(n.instance().locale).getLocalizedString(t,o)}function g(e,t){return a.registerFileStrings(e,t)}var f=Object.freeze({__proto__:null,lookupClosestSupportedDevToolsLocale:function(e){return a.lookupClosestSupportedLocale(e)},getAllSupportedDevToolsLocales:function(){return[...a.supportedLocales]},fetchAndRegisterLocaleData:async function(e,o=self.location.toString()){const n=fetch(function(e,o){const n=t.Runtime.getRemoteBase(o);if(n&&n.version&&!c.has(e))return i.replace("@HOST@","devtools://devtools").replace("@VERSION@",n.version).replace("@LOCALE@",e);const r=l.replace("@LOCALE@",e);return new URL(r,import.meta.url).toString()}(e,o)).then((e=>e.json())),r=new Promise(((e,t)=>window.setTimeout((()=>t(new Error("timed out fetching locale"))),5e3))),s=await Promise.race([r,n]);a.registerLocaleData(e,s)},getLazilyComputedLocalizedString:function(e,t,o={}){return()=>u(e,t,o)},getLocalizedString:u,registerUIStrings:g,getFormatLocalizedString:function(e,t,o){const r=e.getLocalizedStringSetFor(n.instance().locale).getMessageFormatterFor(t),s=document.createElement("span");for(const e of r.getAst())if(1===e.type){const t=o[e.value];t&&s.append(t)}else"value"in e&&s.append(String(e.value));return s},serializeUIString:function(e,t={}){const o={string:e,values:t};return JSON.stringify(o)},deserializeUIString:function(e){return e?JSON.parse(e):{string:"",values:{}}},lockedString:function(e){return e},lockedLazyString:function(e){return()=>e},getLocalizedLanguageRegion:function(e,t){const o=new Intl.Locale(e),{language:n,baseName:r}=o,s=n===new Intl.Locale(t.locale).language?"en":r,i=new Intl.DisplayNames([t.locale],{type:"language"}).of(n),l=new Intl.DisplayNames([s],{type:"language"}).of(n);let a="",c="";if(o.region){a=` (${new Intl.DisplayNames([t.locale],{type:"region",style:"short"}).of(o.region)})`,c=` (${new Intl.DisplayNames([s],{type:"region",style:"short"}).of(o.region)})`}return`${i}${a} - ${l}${c}`}});const p={fmms:"{PH1} μs",fms:"{PH1} ms",fs:"{PH1} s",fmin:"{PH1} min",fhrs:"{PH1} hrs",fdays:"{PH1} days"},m=g("core/i18n/time-utilities.ts",p),L=u.bind(void 0,m),d=function(e,t){if(!isFinite(e))return"-";if(0===e)return"0";if(t&&e<.1)return L(p.fmms,{PH1:(1e3*e).toFixed(0)});if(t&&e<1e3)return L(p.fms,{PH1:e.toFixed(2)});if(e<1e3)return L(p.fms,{PH1:e.toFixed(0)});const o=e/1e3;if(o<60)return L(p.fs,{PH1:o.toFixed(2)});const n=o/60;if(n<60)return L(p.fmin,{PH1:n.toFixed(1)});const r=n/60;if(r<24)return L(p.fhrs,{PH1:r.toFixed(1)});return L(p.fdays,{PH1:(r/24).toFixed(1)})};var S=Object.freeze({__proto__:null,preciseMillisToString:function(e,t){return t=t||0,L(p.fms,{PH1:e.toFixed(t)})},millisToString:d,secondsToString:function(e,t){return isFinite(e)?d(1e3*e,t):"-"}});export{s as DevToolsLocale,S as TimeUtilities,f as i18n};
