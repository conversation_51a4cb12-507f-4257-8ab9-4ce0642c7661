import { API_CONFIG } from '../config/constants';

/**
 * Diagnostic de connexion pour identifier les problèmes
 */
export const runDiagnostics = async () => {
  const results = {
    serverConnection: false,
    databaseConnection: false,
    apiEndpoints: {},
    errors: [],
    recommendations: []
  };

  const API_BASE_URL = API_CONFIG.BASE_URL;
  
  console.log('🔍 Démarrage du diagnostic...');
  console.log('🌐 URL du serveur:', API_BASE_URL);

  try {
    // Test 1: Connexion au serveur
    console.log('📡 Test 1: Connexion au serveur...');
    
    const serverResponse = await fetch(`${API_BASE_URL}/api/database/schema`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 5000,
    });

    if (serverResponse.ok) {
      results.serverConnection = true;
      console.log('✅ Serveur accessible');
      
      const serverData = await serverResponse.json();
      if (serverData.success) {
        results.databaseConnection = true;
        console.log('✅ Base de données accessible');
      }
    } else {
      throw new Error(`Serveur retourne ${serverResponse.status}`);
    }

    // Test 2: Endpoints API
    console.log('📡 Test 2: Endpoints API...');
    
    const endpoints = [
      { name: 'clients', url: '/api/clients' },
      { name: 'contracts', url: '/api/contracts' },
      { name: 'consommations', url: '/api/consommations' },
      { name: 'factures', url: '/api/factures' }
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${API_BASE_URL}${endpoint.url}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        });

        results.apiEndpoints[endpoint.name] = {
          status: response.status,
          ok: response.ok,
          accessible: response.ok
        };

        if (response.ok) {
          console.log(`✅ ${endpoint.name}: OK`);
        } else {
          console.log(`❌ ${endpoint.name}: ${response.status}`);
        }
      } catch (error) {
        results.apiEndpoints[endpoint.name] = {
          status: 'ERROR',
          ok: false,
          accessible: false,
          error: error.message
        };
        console.log(`❌ ${endpoint.name}: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Erreur de diagnostic:', error);
    results.errors.push(error.message);

    // Recommandations basées sur l'erreur
    if (error.message.includes('Failed to fetch') || error.message.includes('Network request failed')) {
      results.recommendations.push('Vérifiez que le serveur Node.js est démarré');
      results.recommendations.push('Vérifiez que votre téléphone et ordinateur sont sur le même réseau WiFi');
      results.recommendations.push(`Testez l'URL dans votre navigateur: ${API_BASE_URL}/api/clients`);
      results.recommendations.push('Vérifiez que le firewall Windows autorise le port 3007');
    } else if (error.message.includes('timeout')) {
      results.recommendations.push('Le serveur met trop de temps à répondre');
      results.recommendations.push('Redémarrez le serveur Node.js');
    } else {
      results.recommendations.push('Erreur inconnue - vérifiez les logs du serveur');
    }
  }

  return results;
};

/**
 * Affiche les résultats du diagnostic dans une alerte
 */
export const showDiagnosticResults = (results) => {
  let message = '🔍 DIAGNOSTIC DE CONNEXION\n\n';

  // État du serveur
  message += `🌐 Serveur: ${results.serverConnection ? '✅ Accessible' : '❌ Inaccessible'}\n`;
  message += `💾 Base de données: ${results.databaseConnection ? '✅ Connectée' : '❌ Déconnectée'}\n\n`;

  // État des endpoints
  message += '📡 ENDPOINTS API:\n';
  Object.entries(results.apiEndpoints).forEach(([name, status]) => {
    message += `• ${name}: ${status.accessible ? '✅' : '❌'} (${status.status})\n`;
  });

  // Erreurs
  if (results.errors.length > 0) {
    message += '\n❌ ERREURS:\n';
    results.errors.forEach(error => {
      message += `• ${error}\n`;
    });
  }

  // Recommandations
  if (results.recommendations.length > 0) {
    message += '\n💡 RECOMMANDATIONS:\n';
    results.recommendations.forEach(rec => {
      message += `• ${rec}\n`;
    });
  }

  return message;
};

/**
 * Test rapide de connexion
 */
export const quickConnectionTest = async () => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/api/database/schema`, {
      method: 'GET',
      timeout: 3000,
    });
    
    return {
      success: response.ok,
      status: response.status,
      message: response.ok ? 'Connexion OK' : `Erreur ${response.status}`
    };
  } catch (error) {
    return {
      success: false,
      status: 'ERROR',
      message: error.message
    };
  }
};
