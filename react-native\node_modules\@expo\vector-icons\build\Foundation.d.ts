declare const _default: import("./createIconSet").Icon<"link" | "alert" | "checkbox" | "minus" | "plus" | "info" | "check" | "book" | "pause" | "mail" | "home" | "laptop" | "star" | "filter" | "save" | "paperclip" | "lock" | "cloud" | "eye" | "camera" | "heart" | "upload" | "download" | "unlock" | "play" | "calendar" | "database" | "key" | "flag" | "layout" | "sound" | "align-left" | "align-right" | "archive" | "arrow-down" | "arrow-left" | "arrow-right" | "arrow-up" | "bookmark" | "clipboard" | "clock" | "compass" | "credit-card" | "crop" | "folder" | "list" | "loop" | "magnifying-glass" | "map" | "megaphone" | "mobile" | "music" | "paypal" | "pencil" | "price-tag" | "print" | "quote" | "rss" | "share" | "shield" | "shopping-bag" | "shopping-cart" | "shuffle" | "ticket" | "trash" | "trophy" | "video" | "comment" | "like" | "refresh" | "align-center" | "align-justify" | "anchor" | "at-sign" | "bluetooth" | "bold" | "fast-forward" | "italic" | "minus-circle" | "monitor" | "play-circle" | "power" | "rewind" | "target" | "underline" | "upload-cloud" | "volume" | "x" | "x-circle" | "zoom-in" | "zoom-out" | "photo" | "stop" | "eject" | "asterisk" | "comments" | "wrench" | "strikethrough" | "unlink" | "superscript" | "subscript" | "microphone" | "html5" | "css3" | "euro" | "dollar" | "yen" | "bitcoin" | "female" | "male" | "wheelchair" | "paw" | "battery-full" | "battery-half" | "battery-empty" | "usb" | "universal-access" | "blind" | "braille" | "address-book" | "marker" | "web" | "lightbulb" | "crown" | "first-aid" | "skull" | "book-bookmark" | "burst" | "elevator" | "record" | "dislike" | "annotate" | "arrows-compress" | "arrows-expand" | "arrows-in" | "arrows-out" | "asl" | "background-color" | "bitcoin-circle" | "burst-new" | "burst-sale" | "clipboard-notes" | "clipboard-pencil" | "closed-caption" | "comment-minus" | "comment-quotes" | "comment-video" | "contrast" | "die-five" | "die-four" | "die-one" | "die-six" | "die-three" | "die-two" | "dollar-bill" | "female-symbol" | "folder-add" | "folder-lock" | "foot" | "foundation" | "graph-bar" | "graph-horizontal" | "graph-pie" | "graph-trend" | "guide-dog" | "hearing-aid" | "indent-less" | "indent-more" | "list-bullet" | "list-number" | "list-thumbnails" | "male-female" | "male-symbol" | "mobile-signal" | "mountains" | "next" | "no-dogs" | "no-smoking" | "page-add" | "page-copy" | "page-csv" | "page-delete" | "page-doc" | "page-edit" | "page-export-csv" | "page-export-doc" | "page-export-pdf" | "page-export" | "page-filled" | "page-multiple" | "page-pdf" | "page-remove" | "page-search" | "page" | "paint-bucket" | "play-video" | "pound" | "previous" | "pricetag-multiple" | "prohibited" | "projection-screen" | "puzzle" | "results-demographics" | "results" | "rewind-ten" | "safety-cone" | "sheriff-badge" | "social-500px" | "social-adobe" | "social-amazon" | "social-android" | "social-apple" | "social-behance" | "social-bing" | "social-blogger" | "social-delicious" | "social-designer-news" | "social-deviant-art" | "social-digg" | "social-dribbble" | "social-drive" | "social-dropbox" | "social-evernote" | "social-facebook" | "social-flickr" | "social-forrst" | "social-foursquare" | "social-game-center" | "social-github" | "social-google-plus" | "social-hacker-news" | "social-hi5" | "social-instagram" | "social-joomla" | "social-lastfm" | "social-linkedin" | "social-medium" | "social-myspace" | "social-orkut" | "social-path" | "social-picasa" | "social-pinterest" | "social-rdio" | "social-reddit" | "social-skillshare" | "social-skype" | "social-smashing-mag" | "social-snapchat" | "social-spotify" | "social-squidoo" | "social-stack-overflow" | "social-steam" | "social-stumbleupon" | "social-treehouse" | "social-tumblr" | "social-twitter" | "social-vimeo" | "social-windows" | "social-xbox" | "social-yahoo" | "social-yelp" | "social-youtube" | "social-zerply" | "social-zurb" | "tablet-landscape" | "tablet-portrait" | "target-two" | "telephone-accessible" | "telephone" | "text-color" | "thumbnails" | "torso-business" | "torso-female" | "torso" | "torsos-all-female" | "torsos-all" | "torsos-female-male" | "torsos-male-female" | "torsos" | "trees" | "volume-none" | "volume-strike" | "widget", "foundation">;
export default _default;
//# sourceMappingURL=Foundation.d.ts.map