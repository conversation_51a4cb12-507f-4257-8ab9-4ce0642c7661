{"version": 3, "names": ["React", "Platform", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ScreenContentWrapper", "DebugContainer", "props", "createElement", "process", "env", "NODE_ENV", "stackPresentation", "rest", "OS", "displayName"], "sourceRoot": "../../../src", "sources": ["components/DebugContainer.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAwB,cAAc;AACvD;AACA;AACA,OAAOC,YAAY,MAAM,iDAAiD;AAC1E,OAAOC,oBAAoB,MAAM,wBAAwB;AAQzD;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAmD,GAAGC,KAAK,IAAI;EACjE,oBAAOL,KAAA,CAAAM,aAAA,CAACH,oBAAoB,EAAKE,KAAQ,CAAC;AAC5C,CAAC;AAED,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAL,cAAc,GAAIC,KAAqB,IAAK;IAC1C,MAAM;MAAEK,iBAAiB;MAAE,GAAGC;IAAK,CAAC,GAAGN,KAAK;IAE5C,IACEJ,QAAQ,CAACW,EAAE,KAAK,KAAK,IACrBF,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,WAAW,EACjC;MACA;MACA,oBACEV,KAAA,CAAAM,aAAA,CAACJ,YAAY,qBACXF,KAAA,CAAAM,aAAA,CAACH,oBAAoB,EAAKQ,IAAO,CACrB,CAAC;IAEnB;IAEA,oBAAOX,KAAA,CAAAM,aAAA,CAACH,oBAAoB,EAAKQ,IAAO,CAAC;EAC3C,CAAC;EAEDP,cAAc,CAACS,WAAW,GAAG,gBAAgB;AAC/C;AAEA,eAAeT,cAAc"}