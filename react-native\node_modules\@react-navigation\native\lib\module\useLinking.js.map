{"version": 3, "names": ["findFocusedRoute", "getActionFromState", "getActionFromStateDefault", "getPathFromState", "getPathFromStateDefault", "getStateFromPath", "getStateFromPathDefault", "isEqual", "React", "createMemoryHistory", "ServerContext", "findMatchingState", "a", "b", "undefined", "key", "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "history", "length", "routes", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aRoute", "index", "bRoute", "aChildState", "state", "bChildState", "series", "cb", "queue", "Promise", "resolve", "callback", "then", "linkingHandlers", "useLinking", "ref", "independent", "enabled", "config", "useEffect", "process", "env", "NODE_ENV", "console", "error", "join", "trim", "handler", "Symbol", "push", "indexOf", "splice", "useState", "enabledRef", "useRef", "configRef", "getStateFromPathRef", "getPathFromStateRef", "getActionFromStateRef", "current", "server", "useContext", "getInitialState", "useCallback", "value", "location", "window", "path", "pathname", "search", "thenable", "onfulfilled", "catch", "previousIndexRef", "previousStateRef", "pendingPopStatePathRef", "listen", "navigation", "previousIndex", "record", "get", "resetRoot", "rootState", "getRootState", "some", "r", "routeNames", "includes", "name", "warn", "action", "dispatch", "e", "message", "getPathForRoute", "route", "stateForPath", "focusedRoute", "params", "replace", "onStateChange", "previousState", "pending<PERSON><PERSON>", "previousFocusedState", "focusedState", "history<PERSON><PERSON><PERSON>", "nextIndex", "backIndex", "currentIndex", "go", "addListener"], "sourceRoot": "../../src", "sources": ["useLinking.tsx"], "mappings": "AAAA,SACEA,gBAAgB,EAChBC,kBAAkB,IAAIC,yBAAyB,EAC/CC,gBAAgB,IAAIC,uBAAuB,EAC3CC,gBAAgB,IAAIC,uBAAuB,QAItC,wBAAwB;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAK3C;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,CACxBC,CAAgB,EAChBC,CAAgB,KACmB;EACnC,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,IAAIF,CAAC,CAACG,GAAG,KAAKF,CAAC,CAACE,GAAG,EAAE;IACzD,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;EAC/B;;EAEA;EACA,MAAME,cAAc,GAAGJ,CAAC,CAACK,OAAO,GAAGL,CAAC,CAACK,OAAO,CAACC,MAAM,GAAGN,CAAC,CAACO,MAAM,CAACD,MAAM;EACrE,MAAME,cAAc,GAAGP,CAAC,CAACI,OAAO,GAAGJ,CAAC,CAACI,OAAO,CAACC,MAAM,GAAGL,CAAC,CAACM,MAAM,CAACD,MAAM;EAErE,MAAMG,MAAM,GAAGT,CAAC,CAACO,MAAM,CAACP,CAAC,CAACU,KAAK,CAAC;EAChC,MAAMC,MAAM,GAAGV,CAAC,CAACM,MAAM,CAACN,CAAC,CAACS,KAAK,CAAC;EAEhC,MAAME,WAAW,GAAGH,MAAM,CAACI,KAAsB;EACjD,MAAMC,WAAW,GAAGH,MAAM,CAACE,KAAsB;;EAEjD;EACA;EACA;EACA;EACA;EACA,IACET,cAAc,KAAKI,cAAc,IACjCC,MAAM,CAACN,GAAG,KAAKQ,MAAM,CAACR,GAAG,IACzBS,WAAW,KAAKV,SAAS,IACzBY,WAAW,KAAKZ,SAAS,IACzBU,WAAW,CAACT,GAAG,KAAKW,WAAW,CAACX,GAAG,EACnC;IACA,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACf;EAEA,OAAOF,iBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAIC,EAAuB,IAAK;EACjD,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,EAAE;EAC7B,MAAMC,QAAQ,GAAG,MAAM;IACrBH,KAAK,GAAGA,KAAK,CAACI,IAAI,CAACL,EAAE,CAAC;EACxB,CAAC;EACD,OAAOI,QAAQ;AACjB,CAAC;AAED,IAAIE,eAAyB,GAAG,EAAE;AAMlC,eAAe,SAASC,UAAU,CAChCC,GAA2D,QAS3D;EAAA,IARA;IACEC,WAAW;IACXC,OAAO,GAAG,IAAI;IACdC,MAAM;IACNlC,gBAAgB,GAAGC,uBAAuB;IAC1CH,gBAAgB,GAAGC,uBAAuB;IAC1CH,kBAAkB,GAAGC;EACd,CAAC;EAEVM,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAO7B,SAAS;IAClB;IAEA,IAAIuB,WAAW,EAAE;MACf,OAAOvB,SAAS;IAClB;IAEA,IAAIwB,OAAO,KAAK,KAAK,IAAIJ,eAAe,CAAChB,MAAM,EAAE;MAC/C0B,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,CAC7D,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,EAAE,CACV;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,EAAE;IAExB,IAAIX,OAAO,KAAK,KAAK,EAAE;MACrBJ,eAAe,CAACgB,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAM1B,KAAK,GAAGY,eAAe,CAACiB,OAAO,CAACH,OAAO,CAAC;MAE9C,IAAI1B,KAAK,GAAG,CAAC,CAAC,EAAE;QACdY,eAAe,CAACkB,MAAM,CAAC9B,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAACgB,OAAO,EAAED,WAAW,CAAC,CAAC;EAE1B,MAAM,CAACpB,OAAO,CAAC,GAAGT,KAAK,CAAC6C,QAAQ,CAAC5C,mBAAmB,CAAC;;EAErD;EACA;EACA;EACA,MAAM6C,UAAU,GAAG9C,KAAK,CAAC+C,MAAM,CAACjB,OAAO,CAAC;EACxC,MAAMkB,SAAS,GAAGhD,KAAK,CAAC+C,MAAM,CAAChB,MAAM,CAAC;EACtC,MAAMkB,mBAAmB,GAAGjD,KAAK,CAAC+C,MAAM,CAAClD,gBAAgB,CAAC;EAC1D,MAAMqD,mBAAmB,GAAGlD,KAAK,CAAC+C,MAAM,CAACpD,gBAAgB,CAAC;EAC1D,MAAMwD,qBAAqB,GAAGnD,KAAK,CAAC+C,MAAM,CAACtD,kBAAkB,CAAC;EAE9DO,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpBc,UAAU,CAACM,OAAO,GAAGtB,OAAO;IAC5BkB,SAAS,CAACI,OAAO,GAAGrB,MAAM;IAC1BkB,mBAAmB,CAACG,OAAO,GAAGvD,gBAAgB;IAC9CqD,mBAAmB,CAACE,OAAO,GAAGzD,gBAAgB;IAC9CwD,qBAAqB,CAACC,OAAO,GAAG3D,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAM4D,MAAM,GAAGrD,KAAK,CAACsD,UAAU,CAACpD,aAAa,CAAC;EAE9C,MAAMqD,eAAe,GAAGvD,KAAK,CAACwD,WAAW,CAAC,MAAM;IAC9C,IAAIC,KAA8B;IAElC,IAAIX,UAAU,CAACM,OAAO,EAAE;MACtB,MAAMM,QAAQ,GACZ,CAAAL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,QAAQ,MACf,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,QAAQ,GAAGpD,SAAS,CAAC;MAE/D,MAAMsD,IAAI,GAAGF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM,GAAGxD,SAAS;MAEvE,IAAIsD,IAAI,EAAE;QACRH,KAAK,GAAGR,mBAAmB,CAACG,OAAO,CAACQ,IAAI,EAAEZ,SAAS,CAACI,OAAO,CAAC;MAC9D;IACF;IAEA,MAAMW,QAAQ,GAAG;MACftC,IAAI,CAACuC,WAAsD,EAAE;QAC3D,OAAO1C,OAAO,CAACC,OAAO,CAACyC,WAAW,GAAGA,WAAW,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDQ,KAAK,GAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAGlE,KAAK,CAAC+C,MAAM,CAAqBzC,SAAS,CAAC;EACpE,MAAM6D,gBAAgB,GAAGnE,KAAK,CAAC+C,MAAM,CAA8BzC,SAAS,CAAC;EAC7E,MAAM8D,sBAAsB,GAAGpE,KAAK,CAAC+C,MAAM,CAAqBzC,SAAS,CAAC;EAE1EN,KAAK,CAACgC,SAAS,CAAC,MAAM;IACpBkC,gBAAgB,CAACd,OAAO,GAAG3C,OAAO,CAACK,KAAK;IAExC,OAAOL,OAAO,CAAC4D,MAAM,CAAC,MAAM;MAC1B,MAAMC,UAAU,GAAG1C,GAAG,CAACwB,OAAO;MAE9B,IAAI,CAACkB,UAAU,IAAI,CAACxC,OAAO,EAAE;QAC3B;MACF;MAEA,MAAM;QAAE4B;MAAS,CAAC,GAAGC,MAAM;MAE3B,MAAMC,IAAI,GAAGF,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM;MAChD,MAAMhD,KAAK,GAAGL,OAAO,CAACK,KAAK;MAE3B,MAAMyD,aAAa,GAAGL,gBAAgB,CAACd,OAAO,IAAI,CAAC;MAEnDc,gBAAgB,CAACd,OAAO,GAAGtC,KAAK;MAChCsD,sBAAsB,CAAChB,OAAO,GAAGQ,IAAI;;MAErC;MACA;MACA;MACA,MAAMY,MAAM,GAAG/D,OAAO,CAACgE,GAAG,CAAC3D,KAAK,CAAC;MAEjC,IAAI,CAAA0D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEZ,IAAI,MAAKA,IAAI,IAAIY,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEvD,KAAK,EAAE;QAC1CqD,UAAU,CAACI,SAAS,CAACF,MAAM,CAACvD,KAAK,CAAC;QAClC;MACF;MAEA,MAAMA,KAAK,GAAGgC,mBAAmB,CAACG,OAAO,CAACQ,IAAI,EAAEZ,SAAS,CAACI,OAAO,CAAC;;MAElE;MACA;MACA,IAAInC,KAAK,EAAE;QACT;QACA;QACA,MAAM0D,SAAS,GAAGL,UAAU,CAACM,YAAY,EAAE;QAE3C,IAAI3D,KAAK,CAACN,MAAM,CAACkE,IAAI,CAAEC,CAAC,IAAK,EAACH,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEI,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,EAAC,EAAE;UACrE7C,OAAO,CAAC8C,IAAI,CACV,0SAA0S,CAC3S;UACD;QACF;QAEA,IAAIpE,KAAK,GAAGyD,aAAa,EAAE;UACzB,MAAMY,MAAM,GAAGhC,qBAAqB,CAACC,OAAO,CAC1CnC,KAAK,EACL+B,SAAS,CAACI,OAAO,CAClB;UAED,IAAI+B,MAAM,KAAK7E,SAAS,EAAE;YACxB,IAAI;cACFgE,UAAU,CAACc,QAAQ,CAACD,MAAM,CAAC;YAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;cACV;cACA;cACAjD,OAAO,CAAC8C,IAAI,CACT,qDAAoDtB,IAAK,MACxD,OAAOyB,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACC,OAAO,GACTD,CACL,EAAC,CACH;YACH;UACF,CAAC,MAAM;YACLf,UAAU,CAACI,SAAS,CAACzD,KAAK,CAAC;UAC7B;QACF,CAAC,MAAM;UACLqD,UAAU,CAACI,SAAS,CAACzD,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QACL;QACAqD,UAAU,CAACI,SAAS,CAACzD,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,OAAO,EAAErB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B5B,KAAK,CAACgC,SAAS,CAAC,MAAM;IAAA;IACpB,IAAI,CAACF,OAAO,EAAE;MACZ;IACF;IAEA,MAAMyD,eAAe,GAAG,CACtBC,KAA0C,EAC1CvE,KAAsB,KACX;MACX;MACA;MACA,IAAIuE,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE5B,IAAI,EAAE;QACf,MAAM6B,YAAY,GAAGxC,mBAAmB,CAACG,OAAO,CAC9CoC,KAAK,CAAC5B,IAAI,EACVZ,SAAS,CAACI,OAAO,CAClB;QAED,IAAIqC,YAAY,EAAE;UAChB,MAAMC,YAAY,GAAGlG,gBAAgB,CAACiG,YAAY,CAAC;UAEnD,IACEC,YAAY,IACZA,YAAY,CAACT,IAAI,KAAKO,KAAK,CAACP,IAAI,IAChClF,OAAO,CAAC2F,YAAY,CAACC,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC,EAC1C;YACA,OAAOH,KAAK,CAAC5B,IAAI;UACnB;QACF;MACF;MAEA,OAAOV,mBAAmB,CAACE,OAAO,CAACnC,KAAK,EAAE+B,SAAS,CAACI,OAAO,CAAC;IAC9D,CAAC;IAED,IAAIxB,GAAG,CAACwB,OAAO,EAAE;MACf;MACA;MACA,MAAMnC,KAAK,GAAGW,GAAG,CAACwB,OAAO,CAACwB,YAAY,EAAE;MAExC,IAAI3D,KAAK,EAAE;QACT,MAAMuE,KAAK,GAAGhG,gBAAgB,CAACyB,KAAK,CAAC;QACrC,MAAM2C,IAAI,GAAG2B,eAAe,CAACC,KAAK,EAAEvE,KAAK,CAAC;QAE1C,IAAIkD,gBAAgB,CAACf,OAAO,KAAK9C,SAAS,EAAE;UAC1C6D,gBAAgB,CAACf,OAAO,GAAGnC,KAAK;QAClC;QAEAR,OAAO,CAACmF,OAAO,CAAC;UAAEhC,IAAI;UAAE3C;QAAM,CAAC,CAAC;MAClC;IACF;IAEA,MAAM4E,aAAa,GAAG,YAAY;MAChC,MAAMvB,UAAU,GAAG1C,GAAG,CAACwB,OAAO;MAE9B,IAAI,CAACkB,UAAU,IAAI,CAACxC,OAAO,EAAE;QAC3B;MACF;MAEA,MAAMgE,aAAa,GAAG3B,gBAAgB,CAACf,OAAO;MAC9C,MAAMnC,KAAK,GAAGqD,UAAU,CAACM,YAAY,EAAE;;MAEvC;MACA,IAAI,CAAC3D,KAAK,EAAE;QACV;MACF;MAEA,MAAM8E,WAAW,GAAG3B,sBAAsB,CAAChB,OAAO;MAClD,MAAMoC,KAAK,GAAGhG,gBAAgB,CAACyB,KAAK,CAAC;MACrC,MAAM2C,IAAI,GAAG2B,eAAe,CAACC,KAAK,EAAEvE,KAAK,CAAC;MAE1CkD,gBAAgB,CAACf,OAAO,GAAGnC,KAAK;MAChCmD,sBAAsB,CAAChB,OAAO,GAAG9C,SAAS;;MAE1C;MACA;MACA;MACA;MACA,MAAM,CAAC0F,oBAAoB,EAAEC,YAAY,CAAC,GAAG9F,iBAAiB,CAC5D2F,aAAa,EACb7E,KAAK,CACN;MAED,IACE+E,oBAAoB,IACpBC,YAAY;MACZ;MACA;MACArC,IAAI,KAAKmC,WAAW,EACpB;QACA,MAAMG,YAAY,GAChB,CAACD,YAAY,CAACxF,OAAO,GACjBwF,YAAY,CAACxF,OAAO,CAACC,MAAM,GAC3BuF,YAAY,CAACtF,MAAM,CAACD,MAAM,KAC7BsF,oBAAoB,CAACvF,OAAO,GACzBuF,oBAAoB,CAACvF,OAAO,CAACC,MAAM,GACnCsF,oBAAoB,CAACrF,MAAM,CAACD,MAAM,CAAC;QAEzC,IAAIwF,YAAY,GAAG,CAAC,EAAE;UACpB;UACA;UACAzF,OAAO,CAACiC,IAAI,CAAC;YAAEkB,IAAI;YAAE3C;UAAM,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAIiF,YAAY,GAAG,CAAC,EAAE;UAC3B;;UAEA,MAAMC,SAAS,GAAG1F,OAAO,CAAC2F,SAAS,CAAC;YAAExC;UAAK,CAAC,CAAC;UAC7C,MAAMyC,YAAY,GAAG5F,OAAO,CAACK,KAAK;UAElC,IAAI;YACF,IACEqF,SAAS,KAAK,CAAC,CAAC,IAChBA,SAAS,GAAGE,YAAY;YACxB;YACA5F,OAAO,CAACgE,GAAG,CAAC0B,SAAS,GAAGE,YAAY,CAAC,EACrC;cACA;cACA,MAAM5F,OAAO,CAAC6F,EAAE,CAACH,SAAS,GAAGE,YAAY,CAAC;YAC5C,CAAC,MAAM;cACL;cACA;cACA;cACA,MAAM5F,OAAO,CAAC6F,EAAE,CAACJ,YAAY,CAAC;YAChC;;YAEA;YACAzF,OAAO,CAACmF,OAAO,CAAC;cAAEhC,IAAI;cAAE3C;YAAM,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOoE,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,MAAM;UACL;UACA5E,OAAO,CAACmF,OAAO,CAAC;YAAEhC,IAAI;YAAE3C;UAAM,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL;QACA;QACAR,OAAO,CAACmF,OAAO,CAAC;UAAEhC,IAAI;UAAE3C;QAAM,CAAC,CAAC;MAClC;IACF,CAAC;;IAED;IACA;IACA;IACA,uBAAOW,GAAG,CAACwB,OAAO,iDAAX,aAAamD,WAAW,CAAC,OAAO,EAAEpF,MAAM,CAAC0E,aAAa,CAAC,CAAC;EACjE,CAAC,EAAE,CAAC/D,OAAO,EAAErB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B,OAAO;IACL2B;EACF,CAAC;AACH"}