{"version": 3, "names": ["ReanimatedErrorConstructor", "ReanimatedError", "message", "prefix", "errorInstance", "Error", "name", "registerReanimatedError", "_WORKLET", "global", "_workletStackDetails", "Map", "registerWorkletStackDetails", "hash", "stackDetails", "set", "getBundleOffset", "error", "frame", "stack", "split", "parsedFrame", "exec", "file", "line", "col", "Number", "processStack", "workletStackEntries", "match", "result", "for<PERSON>ach", "origLine", "origCol", "map", "errorDetails", "get", "lineOffset", "colOffset", "bundleFile", "bundleLine", "bundleCol", "replace", "reportFatalErrorOnJS", "undefined", "jsEngine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportFatalError"], "sourceRoot": "../../src", "sources": ["errors.ts"], "mappings": "AAAA;AACA,YAAY;;AAGsC;;AAQlD,MAAMA,0BAAsD,GAC1D,SAASC,eAAeA,CAACC,OAAgB,EAAE;EACzC,SAAS;;EACT,MAAMC,MAAM,GAAG,cAAc;EAC7B,MAAMC,aAAa,GAAG,IAAIC,KAAK,CAACH,OAAO,GAAG,GAAGC,MAAM,IAAID,OAAO,EAAE,GAAGC,MAAM,CAAC;EAC1EC,aAAa,CAACE,IAAI,GAAG,iBAAiB;EACtC,OAAOF,aAAa;AACtB,CAA+B;AAEjC,SAASJ,0BAA0B,IAAIC,eAAe;;AAEtD;AACA;AACA;AACA;AACA,OAAO,SAASM,uBAAuBA,CAAA,EAAG;EACxC,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,MAAM,IAAIH,KAAK,CACb,0EACF,CAAC;EACH;EACCI,MAAM,CAA6BR,eAAe,GACjDD,0BAA0B;AAC9B;AAEA,MAAMU,oBAAoB,GAAG,IAAIC,GAAG,CAA8B,CAAC;AAEnE,OAAO,SAASC,2BAA2BA,CACzCC,IAAY,EACZC,YAAiC,EACjC;EACAJ,oBAAoB,CAACK,GAAG,CAACF,IAAI,EAAEC,YAAY,CAAC;AAC9C;AAEA,SAASE,eAAeA,CAACC,KAAY,EAA4B;EAC/D,MAAMC,KAAK,GAAGD,KAAK,CAACE,KAAK,EAAEC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACT,MAAMG,WAAW,GAAG,sBAAsB,CAACC,IAAI,CAACJ,KAAK,CAAC;IACtD,IAAIG,WAAW,EAAE;MACf,MAAM,GAAGE,IAAI,EAAEC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,WAAW;MACvC,OAAO,CAACE,IAAI,EAAEG,MAAM,CAACF,IAAI,CAAC,EAAEE,MAAM,CAACD,GAAG,CAAC,CAAC;IAC1C;EACF;EACA,OAAO,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1B;AAEA,SAASE,YAAYA,CAACR,KAAa,EAAU;EAC3C,MAAMS,mBAAmB,GAAGT,KAAK,CAACU,KAAK,CAAC,4BAA4B,CAAC;EACrE,IAAIC,MAAM,GAAGX,KAAK;EAClBS,mBAAmB,EAAEG,OAAO,CAAEF,KAAK,IAAK;IACtC,MAAM,GAAGhB,IAAI,EAAEmB,QAAQ,EAAEC,OAAO,CAAC,GAAGJ,KAAK,CAACT,KAAK,CAAC,KAAK,CAAC,CAACc,GAAG,CAACR,MAAM,CAAC;IAClE,MAAMS,YAAY,GAAGzB,oBAAoB,CAAC0B,GAAG,CAACvB,IAAI,CAAC;IACnD,IAAI,CAACsB,YAAY,EAAE;MACjB;IACF;IACA,MAAM,CAAClB,KAAK,EAAEoB,UAAU,EAAEC,SAAS,CAAC,GAAGH,YAAY;IACnD,MAAM,CAACI,UAAU,EAAEC,UAAU,EAAEC,SAAS,CAAC,GAAGzB,eAAe,CAACC,KAAK,CAAC;IAClE,MAAMO,IAAI,GAAGQ,QAAQ,GAAGQ,UAAU,GAAGH,UAAU;IAC/C,MAAMZ,GAAG,GAAGQ,OAAO,GAAGQ,SAAS,GAAGH,SAAS;IAE3CR,MAAM,GAAGA,MAAM,CAACY,OAAO,CAACb,KAAK,EAAE,GAAGU,UAAU,IAAIf,IAAI,IAAIC,GAAG,EAAE,CAAC;EAChE,CAAC,CAAC;EACF,OAAOK,MAAM;AACf;AAEA,OAAO,SAASa,oBAAoBA,CAAC;EACnCzC,OAAO;EACPiB;AAIF,CAAC,EAAE;EACD,MAAMF,KAAK,GAAG,IAAIZ,KAAK,CAAC,CAAC;EACzBY,KAAK,CAACf,OAAO,GAAGA,OAAO;EACvBe,KAAK,CAACE,KAAK,GAAGA,KAAK,GAAGQ,YAAY,CAACR,KAAK,CAAC,GAAGyB,SAAS;EACrD3B,KAAK,CAACX,IAAI,GAAG,iBAAiB;EAC9B;EACAW,KAAK,CAAC4B,QAAQ,GAAG,YAAY;EAC7B;EACApC,MAAM,CAACqC,UAAU,CAACC,gBAAgB,CAAC9B,KAAK,CAAC;AAC3C", "ignoreList": []}