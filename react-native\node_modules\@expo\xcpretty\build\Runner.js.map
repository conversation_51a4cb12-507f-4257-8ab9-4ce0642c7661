{"version": 3, "file": "Runner.js", "sourceRoot": "", "sources": ["../src/Runner.ts"], "names": [], "mappings": ";;;;;AAOA,4EAYC;AAED,sDAuFC;AAED,wCAMC;AAED,kDAIC;AA1HD,kDAA0B;AAC1B,4CAAoB;AACpB,4CAAoB;AACpB,gDAAwB;AAExB,yDAAsD;AAEtD,SAAgB,gCAAgC,CAC9C,WAAmB,EACnB,EAAE,gBAAgB,KAAoC,EAAE;IAExD,OAAO,IAAI,OAAO,CAAS,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;QACnD,MAAM,KAAK,GAAG,qBAAqB,CAAC,WAAW,EAAE,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAExF,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QACvC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,qBAAqB,CACnC,WAAmB,EACnB,EACE,gBAAgB,EAChB,OAAO,EACP,MAAM,GAKP;IAED,MAAM,SAAS,GAAG,mCAAgB,CAAC,MAAM,CAAC,WAAW,EAAE;QACrD,YAAY,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,SAAS;QACvE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;KAC1C,CAAC,CAAC;IAEH,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,IAAI,WAAW,GAAG,EAAE,CAAC;IAErB,IAAI,aAAa,GAAG,EAAE,CAAC;IAEvB,uEAAuE;IACvE,6GAA6G;IAC7G,SAAS,WAAW;QAClB,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,aAAa,CAAC;QAC3B,aAAa,GAAG,EAAE,CAAC;QACnB,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,EAAE;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnC,WAAW,IAAI,UAAU,CAAC;QAC1B,aAAa,IAAI,UAAU,CAAC;QAC5B,IAAI,aAAa,CAAC,QAAQ,CAAC,YAAE,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,WAAW,EAAE,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG,CAAC,IAAY,EAAE,EAAE;QAC7B,WAAW,EAAE,CAAC;QACd,MAAM,UAAU,GAAG,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACnE,WAAW,IAAI,UAAU,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG,CAAC,IAAY,EAAE,EAAE;QAC7B,WAAW,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC;QACzC,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC1E,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,4CAA4C;YAC5C,MAAM,iBAAiB,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;YAEpD,MAAM,UAAU,GAAG,oEAAoE,IAAI,GAAG,CAAC;YAE/F,IAAI,iBAAiB,EAAE,CAAC;gBACtB,0HAA0H;gBAC1H,8EAA8E;gBAC9E,wEAAwE;gBACxE,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9B,OAAO;YACT,CAAC;YAED,qFAAqF;YACrF,wEAAwE;YACxE,MAAM,CACJ,IAAI,KAAK,CACP,GAAG,UAAU,mFAAmF,SAAS,OAAO;gBAC9G,WAAW;gBACX,MAAM;gBACN,WAAW;gBACX,yBAAyB,eAAK,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAC1D,CACF,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO,CAAC,WAAW,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAClC,CAAC;AAED,SAAgB,cAAc,CAAC,WAAmB,EAAE,WAAmB,EAAE,WAAmB;IAC1F,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IAEtE,YAAE,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC3C,YAAE,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAC7C,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAgB,mBAAmB,CAAC,WAAmB;IACrD,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC/C,YAAE,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1C,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAAE,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC,CAAC;AAC1F,CAAC;AAED,SAAS,QAAQ,CAAC,KAAU;IAC1B,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;IACxC,OAAO,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC;AACvC,CAAC", "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nimport { ExpoRunFormatter } from './ExpoRunFormatter';\n\nexport function formatXcodeBuildPipeProcessAsync(\n  projectRoot: string,\n  { xcodeProjectName }: { xcodeProjectName?: string } = {}\n) {\n  return new Promise<string>(async (resolve, reject) => {\n    const hooks = createXcodeBuildHooks(projectRoot, { xcodeProjectName, resolve, reject });\n\n    process.stdin.on('data', hooks.onData);\n    process.stdin.on('end', () => {\n      hooks.onEnd(0);\n    });\n  });\n}\n\nexport function createXcodeBuildHooks(\n  projectRoot: string,\n  {\n    xcodeProjectName,\n    resolve,\n    reject,\n  }: {\n    xcodeProjectName?: string;\n    resolve: (buildOutput: string) => void;\n    reject: (error: Error) => void;\n  }\n) {\n  const formatter = ExpoRunFormatter.create(projectRoot, {\n    xcodeProject: xcodeProjectName ? { name: xcodeProjectName } : undefined,\n    isDebug: isTruthy(process.env.EXPO_DEBUG),\n  });\n\n  let buildOutput = '';\n  let errorOutput = '';\n\n  let currentBuffer = '';\n\n  // Data can be sent in chunks that would have no relevance to our regex\n  // this can cause massive slowdowns, so we need to ensure the data is complete before attempting to parse it.\n  function flushBuffer() {\n    if (!currentBuffer) {\n      return;\n    }\n\n    const data = currentBuffer;\n    currentBuffer = '';\n    const lines = formatter.pipe(data);\n    for (const line of lines) {\n      console.log(line);\n    }\n  }\n\n  const onData = (data: Buffer) => {\n    const stringData = data.toString();\n    buildOutput += stringData;\n    currentBuffer += stringData;\n    if (currentBuffer.endsWith(os.EOL)) {\n      flushBuffer();\n    }\n  };\n\n  const onErr = (data: Buffer) => {\n    flushBuffer();\n    const stringData = data instanceof Buffer ? data.toString() : data;\n    errorOutput += stringData;\n  };\n\n  const onEnd = (code: number) => {\n    flushBuffer();\n    console.log(formatter.getBuildSummary());\n    const logFilePath = writeBuildLogs(projectRoot, buildOutput, errorOutput);\n    if (code !== 0) {\n      // Determine if the logger found any errors;\n      const wasErrorPresented = !!formatter.errors.length;\n\n      const errorTitle = `Failed to build iOS project. \"xcodebuild\" exited with error code ${code}.`;\n\n      if (wasErrorPresented) {\n        // This has a flaw, if the user is missing a file, and there is a script error, only the missing file error will be shown.\n        // They will only see the script error if they fix the missing file and rerun.\n        // The flaw can be fixed by catching script errors in the custom logger.\n        reject(new Error(errorTitle));\n        return;\n      }\n\n      // Show all the log info because often times the error is coming from a shell script,\n      // that invoked a node script, that started metro, which threw an error.\n      reject(\n        new Error(\n          `${errorTitle}\\nTo view more error logs, try building the app with Xcode directly, by opening ${'unknown'}.\\n\\n` +\n            buildOutput +\n            '\\n\\n' +\n            errorOutput +\n            `Build logs written to ${chalk.underline(logFilePath)}`\n        )\n      );\n      return;\n    }\n    resolve(buildOutput);\n  };\n\n  return { onData, onErr, onEnd };\n}\n\nexport function writeBuildLogs(projectRoot: string, buildOutput: string, errorOutput: string) {\n  const [logFilePath, errorFilePath] = getErrorLogFilePath(projectRoot);\n\n  fs.writeFileSync(logFilePath, buildOutput);\n  fs.writeFileSync(errorFilePath, errorOutput);\n  return logFilePath;\n}\n\nexport function getErrorLogFilePath(projectRoot: string): [string, string] {\n  const folder = path.join(projectRoot, '.expo');\n  fs.mkdirSync(folder, { recursive: true });\n  return [path.join(folder, 'xcodebuild.log'), path.join(folder, 'xcodebuild-error.log')];\n}\n\nfunction isTruthy(value: any): boolean {\n  const str = String(value).toLowerCase();\n  return str === 'true' || str === '1';\n}\n"]}