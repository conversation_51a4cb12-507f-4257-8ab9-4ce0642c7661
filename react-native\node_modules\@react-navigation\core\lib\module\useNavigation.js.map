{"version": 3, "names": ["React", "NavigationContainerRefContext", "NavigationContext", "useNavigation", "root", "useContext", "navigation", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useNavigation.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,6BAA6B,MAAM,iCAAiC;AAC3E,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAa,GAI9B;EACL,MAAMC,IAAI,GAAGJ,KAAK,CAACK,UAAU,CAACJ,6BAA6B,CAAC;EAC5D,MAAMK,UAAU,GAAGN,KAAK,CAACK,UAAU,CAACH,iBAAiB,CAAC;EAEtD,IAAII,UAAU,KAAKC,SAAS,IAAIH,IAAI,KAAKG,SAAS,EAAE;IAClD,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;EACH;;EAEA;EACA,OAAQF,UAAU,IAAIF,IAAI;AAC5B"}