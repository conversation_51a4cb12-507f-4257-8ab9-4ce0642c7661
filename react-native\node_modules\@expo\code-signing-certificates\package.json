{"name": "@expo/code-signing-certificates", "version": "0.0.5", "description": "A library for working with expo-updates code signing certificates", "main": "build/main.js", "types": "build/main.d.ts", "scripts": {"tsc": "tsc", "clean": "rm -rf build coverage coverage-integration", "lint": "eslint src", "lint-fix": "eslint src --fix", "test": "jest --rootDir . --config jest.config.js", "generate-example-certificates": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/generateExampleCertificates.ts", "generate-example-self-signed": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/generateExampleSelfSignedCertificate.ts", "generate-certificates-for-tests": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/generateCertificatesForTests.ts", "oidgen": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/oidgen/oidgen.ts", "sign-manifest-for-testing": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/signManifestForTesting.ts", "sanity-check-signature": "ts-node -r tsconfig-paths/register --project tsconfig.scripts.json ./scripts/sanityCheckSignature.ts"}, "repository": {"type": "git", "url": "https://github.com/expo/code-signing-certificates.git"}, "keywords": ["code signing"], "license": "MIT", "bugs": {"url": "https://github.com/expo/code-signing-certificates/issues"}, "author": "Expo <<EMAIL>>", "homepage": "https://github.com/expo/code-signing-certificates/tree/main#readme", "files": ["build"], "dependencies": {"node-forge": "^1.2.1", "nullthrows": "^1.1.1"}, "devDependencies": {"@tsconfig/node14": "^1.0.1", "@types/jest": "^27.4.0", "@types/jsbn": "^1.2.30", "@types/node-forge": "^1.0.0", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.11.0", "eslint": "^8.8.0", "eslint-config-universe": "^10.0.0", "jest": "^27.5.0", "prettier": "^2.5.1", "ts-jest": "^27.1.3", "ts-node-dev": "^1.1.8", "typescript": "^4.5.5", "uuid": "^8.3.2"}}