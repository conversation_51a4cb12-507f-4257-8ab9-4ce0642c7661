{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenFooter", "obj", "__esModule", "default", "FooterComponent", "_ref", "children", "createElement", "collapsable"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/FooterComponent.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAM1C,SAASG,eAAeA,CAAAC,IAAA,EAA4B;EAAA,IAA3B;IAAEC;EAAsB,CAAC,GAAAD,IAAA;EAC/D,oBAAOR,MAAA,CAAAM,OAAA,CAAAI,aAAA,CAACP,aAAA,CAAAG,OAAY;IAACK,WAAW,EAAE;EAAM,GAAEF,QAAuB,CAAC;AACpE"}