import*as e from"../../models/heap_snapshot_model/heap_snapshot_model.js";import*as t from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import*as n from"../../models/text_utils/text_utils.js";class i{#e;#t;#s;#n;#i;#o;#r;constructor(e,t){this.#e=e.strings,this.#t=1,this.#s=[],this.#n={},this.#i={},this.#o={},this.#r=null,this.#a(e),this.#d(e,t)}#a(e){const t=this.#e,s=e.snapshot.meta.trace_function_info_fields,n=s.indexOf("name"),i=s.indexOf("script_name"),o=s.indexOf("script_id"),r=s.indexOf("line"),d=s.indexOf("column"),h=s.length,l=e.trace_function_infos,c=l.length,p=this.#s=new Array(c/h);let u=0;for(let e=0;e<c;e+=h)p[u++]=new a(t[l[e+n]],t[l[e+i]],l[e+o],l[e+r],l[e+d])}#d(e,t){const s=e.trace_tree,n=this.#s,i=this.#i,r=e.snapshot.meta.trace_node_fields,a=r.indexOf("id"),d=r.indexOf("function_info_index"),h=r.indexOf("count"),l=r.indexOf("size"),c=r.indexOf("children"),p=r.length;return function e(s,r,u){const f=n[s[r+d]],g=s[r+a],I=t[g],m=I?I.count:0,x=I?I.size:0,S=new o(g,f,s[r+h],s[r+l],m,x,u);i[g]=S,f.addTraceTopNode(S);const N=s[r+c];for(let t=0;t<N.length;t+=p)S.children.push(e(N,t,S));return S}(s,0,null)}serializeTraceTops(){if(this.#r)return this.#r;const e=this.#r=[],t=this.#s;for(let s=0;s<t.length;s++){const n=t[s];if(0===n.totalCount)continue;const i=this.#t++,o=0===s;e.push(this.#h(i,n,n.totalCount,n.totalSize,n.totalLiveCount,n.totalLiveSize,!o)),this.#o[i]=n}return e.sort((function(e,t){return t.size-e.size})),e}serializeCallers(t){let s=this.#l(t);const n=[];for(;1===s.callers().length;)s=s.callers()[0],n.push(this.#c(s));const i=[],o=s.callers();for(let e=0;e<o.length;e++)i.push(this.#c(o[e]));return new e.HeapSnapshotModel.AllocationNodeCallers(n,i)}serializeAllocationStack(t){let s=this.#i[t];const n=[];for(;s;){const t=s.functionInfo;n.push(new e.HeapSnapshotModel.AllocationStackFrame(t.functionName,t.scriptName,t.scriptId,t.line,t.column)),s=s.parent}return n}traceIds(e){return this.#l(e).traceTopIds}#l(e){let t=this.#n[e];if(!t){t=this.#o[e].bottomUpRoot(),delete this.#o[e],this.#n[e]=t}return t}#c(e){const t=this.#t++;return this.#n[t]=e,this.#h(t,e.functionInfo,e.allocationCount,e.allocationSize,e.liveCount,e.liveSize,e.hasCallers())}#h(t,s,n,i,o,r,a){return new e.HeapSnapshotModel.SerializedAllocationNode(t,s.functionName,s.scriptName,s.scriptId,s.line,s.column,n,i,o,r,a)}}class o{id;functionInfo;allocationCount;allocationSize;liveCount;liveSize;parent;children;constructor(e,t,s,n,i,o,r){this.id=e,this.functionInfo=t,this.allocationCount=s,this.allocationSize=n,this.liveCount=i,this.liveSize=o,this.parent=r,this.children=[]}}class r{functionInfo;allocationCount;allocationSize;liveCount;liveSize;traceTopIds;#p;constructor(e){this.functionInfo=e,this.allocationCount=0,this.allocationSize=0,this.liveCount=0,this.liveSize=0,this.traceTopIds=[],this.#p=[]}addCaller(e){const t=e.functionInfo;let s;for(let e=0;e<this.#p.length;e++){const n=this.#p[e];if(n.functionInfo===t){s=n;break}}return s||(s=new r(t),this.#p.push(s)),s}callers(){return this.#p}hasCallers(){return this.#p.length>0}}class a{functionName;scriptName;scriptId;line;column;totalCount;totalSize;totalLiveCount;totalLiveSize;#r;#u;constructor(e,t,s,n,i){this.functionName=e,this.scriptName=t,this.scriptId=s,this.line=n,this.column=i,this.totalCount=0,this.totalSize=0,this.totalLiveCount=0,this.totalLiveSize=0,this.#r=[]}addTraceTopNode(e){0!==e.allocationCount&&(this.#r.push(e),this.totalCount+=e.allocationCount,this.totalSize+=e.allocationSize,this.totalLiveCount+=e.liveCount,this.totalLiveSize+=e.liveSize)}bottomUpRoot(){return this.#r.length?(this.#u||this.#f(),this.#u):null}#f(){this.#u=new r(this);for(let e=0;e<this.#r.length;e++){let t=this.#r[e],s=this.#u;const n=t.allocationCount,i=t.allocationSize,o=t.liveCount,r=t.liveSize,a=t.id;for(;s.allocationCount+=n,s.allocationSize+=i,s.liveCount+=o,s.liveSize+=r,s.traceTopIds.push(a),t=t.parent,null!==t;)s=s.addCaller(t)}}}var d=Object.freeze({__proto__:null,AllocationProfile:i,TopDownAllocationNode:o,BottomUpAllocationNode:r,FunctionAllocationInfo:a});class h{snapshot;edges;edgeIndex;constructor(e,t){this.snapshot=e,this.edges=e.containmentEdges,this.edgeIndex=t||0}clone(){return new h(this.snapshot,this.edgeIndex)}hasStringName(){throw new Error("Not implemented")}name(){throw new Error("Not implemented")}node(){return this.snapshot.createNode(this.nodeIndex())}nodeIndex(){if(void 0===this.snapshot.edgeToNodeOffset)throw new Error("edgeToNodeOffset is undefined");return this.edges[this.edgeIndex+this.snapshot.edgeToNodeOffset]}toString(){return"HeapSnapshotEdge: "+this.name()}type(){return this.snapshot.edgeTypes[this.rawType()]}itemIndex(){return this.edgeIndex}serialize(){return new e.HeapSnapshotModel.Edge(this.name(),this.node().serialize(),this.type(),this.edgeIndex)}rawType(){if(void 0===this.snapshot.edgeTypeOffset)throw new Error("edgeTypeOffset is undefined");return this.edges[this.edgeIndex+this.snapshot.edgeTypeOffset]}isInternal(){throw new Error("Not implemented")}isInvisible(){throw new Error("Not implemented")}isWeak(){throw new Error("Not implemented")}}class l{#g;constructor(e){this.#g=e.createNode()}itemForIndex(e){return this.#g.nodeIndex=e,this.#g}}class c{#I;constructor(e){this.#I=e.createEdge(0)}itemForIndex(e){return this.#I.edgeIndex=e,this.#I}}class p{#m;constructor(e){this.#m=e.createRetainingEdge(0)}itemForIndex(e){return this.#m.setRetainerIndex(e),this.#m}}class u{#x;edge;constructor(e){this.#x=e,this.edge=e.snapshot.createEdge(e.edgeIndexesStart())}hasNext(){return this.edge.edgeIndex<this.#x.edgeIndexesEnd()}item(){return this.edge}next(){if(void 0===this.edge.snapshot.edgeFieldsCount)throw new Error("edgeFieldsCount is undefined");this.edge.edgeIndex+=this.edge.snapshot.edgeFieldsCount}}class f{snapshot;#S;#N;#y;#T;#O;constructor(e,t){this.snapshot=e,this.setRetainerIndex(t)}clone(){return new f(this.snapshot,this.retainerIndex())}hasStringName(){return this.edge().hasStringName()}name(){return this.edge().name()}node(){return this.nodeInternal()}nodeIndex(){if(void 0===this.#y)throw new Error("retainingNodeIndex is undefined");return this.#y}retainerIndex(){return this.#S}setRetainerIndex(e){if(e!==this.#S){if(!this.snapshot.retainingEdges||!this.snapshot.retainingNodes)throw new Error("Snapshot does not contain retaining edges or retaining nodes");this.#S=e,this.#N=this.snapshot.retainingEdges[e],this.#y=this.snapshot.retainingNodes[e],this.#T=null,this.#O=null}}set edgeIndex(e){this.setRetainerIndex(e)}nodeInternal(){return this.#O||(this.#O=this.snapshot.createNode(this.#y)),this.#O}edge(){return this.#T||(this.#T=this.snapshot.createEdge(this.#N)),this.#T}toString(){return this.edge().toString()}itemIndex(){return this.#S}serialize(){return new e.HeapSnapshotModel.Edge(this.name(),this.node().serialize(),this.type(),this.#N)}type(){return this.edge().type()}}class g{#w;retainer;constructor(e){const t=e.snapshot,s=e.ordinal();if(!t.firstRetainerIndex)throw new Error("Snapshot does not contain firstRetainerIndex");const n=t.firstRetainerIndex[s];this.#w=t.firstRetainerIndex[s+1],this.retainer=t.createRetainingEdge(n)}hasNext(){return this.retainer.retainerIndex()<this.#w}item(){return this.retainer}next(){this.retainer.setRetainerIndex(this.retainer.retainerIndex()+1)}}class I{snapshot;nodeIndex;constructor(e,t){this.snapshot=e,this.nodeIndex=t||0}distance(){return this.snapshot.nodeDistances[this.nodeIndex/this.snapshot.nodeFieldCount]}className(){throw new Error("Not implemented")}classIndex(){throw new Error("Not implemented")}dominatorIndex(){const e=this.snapshot.nodeFieldCount;return this.snapshot.dominatorsTree[this.nodeIndex/this.snapshot.nodeFieldCount]*e}edges(){return new u(this)}edgesCount(){return(this.edgeIndexesEnd()-this.edgeIndexesStart())/this.snapshot.edgeFieldsCount}id(){throw new Error("Not implemented")}rawName(){throw new Error("Not implemented")}isRoot(){return this.nodeIndex===this.snapshot.rootNodeIndex}isUserRoot(){throw new Error("Not implemented")}isHidden(){throw new Error("Not implemented")}isArray(){throw new Error("Not implemented")}isDocumentDOMTreesRoot(){throw new Error("Not implemented")}name(){return this.snapshot.strings[this.nameInternal()]}retainedSize(){return this.snapshot.retainedSizes[this.ordinal()]}retainers(){return new g(this)}retainersCount(){const e=this.snapshot,t=this.ordinal();return e.firstRetainerIndex[t+1]-e.firstRetainerIndex[t]}selfSize(){const e=this.snapshot;return e.nodes[this.nodeIndex+e.nodeSelfSizeOffset]}type(){return this.snapshot.nodeTypes[this.rawType()]}traceNodeId(){const e=this.snapshot;return e.nodes[this.nodeIndex+e.nodeTraceNodeIdOffset]}itemIndex(){return this.nodeIndex}serialize(){return new e.HeapSnapshotModel.Node(this.id(),this.name(),this.distance(),this.nodeIndex,this.retainedSize(),this.selfSize(),this.type())}nameInternal(){const e=this.snapshot;return e.nodes[this.nodeIndex+e.nodeNameOffset]}edgeIndexesStart(){return this.snapshot.firstEdgeIndexes[this.ordinal()]}edgeIndexesEnd(){return this.snapshot.firstEdgeIndexes[this.ordinal()+1]}ordinal(){return this.nodeIndex/this.snapshot.nodeFieldCount}nextNodeIndex(){return this.nodeIndex+this.snapshot.nodeFieldCount}rawType(){const e=this.snapshot;return e.nodes[this.nodeIndex+e.nodeTypeOffset]}}class m{node;#C;constructor(e){this.node=e,this.#C=e.snapshot.nodes.length}hasNext(){return this.node.nodeIndex<this.#C}item(){return this.node}next(){this.node.nodeIndex=this.node.nextNodeIndex()}}class x{#E;#b;#F;constructor(e,t){this.#E=e,this.#b=t,this.#F=0}hasNext(){return this.#F<this.#b.length}item(){const e=this.#b[this.#F];return this.#E.itemForIndex(e)}next(){++this.#F}}class S{#z;#_;constructor(e,t){this.#z=e,this.#_=t,this.skipFilteredItems()}hasNext(){return this.#z.hasNext()}item(){return this.#z.item()}next(){this.#z.next(),this.skipFilteredItems()}skipFilteredItems(){for(;this.#z.hasNext()&&this.#_&&!this.#_(this.#z.item());)this.#z.next()}}class N{#v;constructor(e){this.#v=e}updateStatus(e){this.sendUpdateEvent(t.i18n.serializeUIString(e))}updateProgress(e,s,n){const i=(100*(n?s/n:0)).toFixed(0);this.sendUpdateEvent(t.i18n.serializeUIString(e,{PH1:i}))}reportProblem(t){this.#v&&this.#v.sendEvent(e.HeapSnapshotModel.HeapSnapshotProgressEvent.BrokenSnapshot,t)}sendUpdateEvent(t){this.#v&&this.#v.sendEvent(e.HeapSnapshotModel.HeapSnapshotProgressEvent.Update,t)}}class y{#A;constructor(e){this.#A=[e]}addError(e){this.#A.length>100||this.#A.push(e)}toString(){return this.#A.join("\n  ")}}class T{nodes;containmentEdges;#k;#D;#j;strings;#H;#R;#P;rootNodeIndexInternal;#M;#U;#L;#B;#W;nodeTypeOffset;nodeNameOffset;nodeIdOffset;nodeSelfSizeOffset;#J;nodeTraceNodeIdOffset;nodeFieldCount;nodeTypes;nodeArrayType;nodeHiddenType;nodeObjectType;nodeNativeType;nodeConsStringType;nodeSlicedStringType;nodeCodeType;nodeSyntheticType;edgeFieldsCount;edgeTypeOffset;edgeNameOffset;edgeToNodeOffset;edgeTypes;edgeElementType;edgeHiddenType;edgeInternalType;edgeShortcutType;edgeWeakType;edgeInvisibleType;#$;#Q;#q;#G;#K;nodeCount;#V;retainedSizes;firstEdgeIndexes;retainingNodes;retainingEdges;firstRetainerIndex;nodeDistances;firstDominatedNodeIndex;dominatedNodes;dominatorsTree;#X;#Y;#Z;lazyStringCache;constructor(e,t){this.nodes=e.nodes,this.containmentEdges=e.edges,this.#k=e.snapshot.meta,this.#D=e.samples,this.#j=null,this.strings=e.strings,this.#H=e.locations,this.#R=t,this.#P=-5,this.rootNodeIndexInternal=0,e.snapshot.root_index&&(this.rootNodeIndexInternal=e.snapshot.root_index),this.#M={},this.#L={},this.#B={},this.#W=e}initialize(){const e=this.#k;this.nodeTypeOffset=e.node_fields.indexOf("type"),this.nodeNameOffset=e.node_fields.indexOf("name"),this.nodeIdOffset=e.node_fields.indexOf("id"),this.nodeSelfSizeOffset=e.node_fields.indexOf("self_size"),this.#J=e.node_fields.indexOf("edge_count"),this.nodeTraceNodeIdOffset=e.node_fields.indexOf("trace_node_id"),this.#Y=e.node_fields.indexOf("detachedness"),this.nodeFieldCount=e.node_fields.length,this.nodeTypes=e.node_types[this.nodeTypeOffset],this.nodeArrayType=this.nodeTypes.indexOf("array"),this.nodeHiddenType=this.nodeTypes.indexOf("hidden"),this.nodeObjectType=this.nodeTypes.indexOf("object"),this.nodeNativeType=this.nodeTypes.indexOf("native"),this.nodeConsStringType=this.nodeTypes.indexOf("concatenated string"),this.nodeSlicedStringType=this.nodeTypes.indexOf("sliced string"),this.nodeCodeType=this.nodeTypes.indexOf("code"),this.nodeSyntheticType=this.nodeTypes.indexOf("synthetic"),this.edgeFieldsCount=e.edge_fields.length,this.edgeTypeOffset=e.edge_fields.indexOf("type"),this.edgeNameOffset=e.edge_fields.indexOf("name_or_index"),this.edgeToNodeOffset=e.edge_fields.indexOf("to_node"),this.edgeTypes=e.edge_types[this.edgeTypeOffset],this.edgeTypes.push("invisible"),this.edgeElementType=this.edgeTypes.indexOf("element"),this.edgeHiddenType=this.edgeTypes.indexOf("hidden"),this.edgeInternalType=this.edgeTypes.indexOf("internal"),this.edgeShortcutType=this.edgeTypes.indexOf("shortcut"),this.edgeWeakType=this.edgeTypes.indexOf("weak"),this.edgeInvisibleType=this.edgeTypes.indexOf("invisible");const t=e.location_fields||[];this.#$=t.indexOf("object_index"),this.#Q=t.indexOf("script_id"),this.#q=t.indexOf("line"),this.#G=t.indexOf("column"),this.#K=t.length,this.nodeCount=this.nodes.length/this.nodeFieldCount,this.#V=this.containmentEdges.length/this.edgeFieldsCount,this.retainedSizes=new Float64Array(this.nodeCount),this.firstEdgeIndexes=new Uint32Array(this.nodeCount+1),this.retainingNodes=new Uint32Array(this.#V),this.retainingEdges=new Uint32Array(this.#V),this.firstRetainerIndex=new Uint32Array(this.nodeCount+1),this.nodeDistances=new Int32Array(this.nodeCount),this.firstDominatedNodeIndex=new Uint32Array(this.nodeCount+1),this.dominatedNodes=new Uint32Array(this.nodeCount-1),this.#R.updateStatus("Building edge indexes…"),this.buildEdgeIndexes(),this.#R.updateStatus("Building retainers…"),this.buildRetainers(),this.#R.updateStatus("Propagating DOM state…"),this.propagateDOMState(),this.#R.updateStatus("Calculating node flags…"),this.calculateFlags(),this.#R.updateStatus("Calculating distances…"),this.calculateDistances(),this.#R.updateStatus("Building postorder index…");const s=this.buildPostOrderIndex();if(this.#R.updateStatus("Building dominator tree…"),this.dominatorsTree=this.buildDominatorTree(s.postOrderIndex2NodeOrdinal,s.nodeOrdinal2PostOrderIndex),this.#R.updateStatus("Calculating shallow sizes…"),this.calculateShallowSizes(),this.#R.updateStatus("Calculating retained sizes…"),this.calculateRetainedSizes(s.postOrderIndex2NodeOrdinal),this.#R.updateStatus("Building dominated nodes…"),this.buildDominatedNodes(),this.#R.updateStatus("Calculating statistics…"),this.calculateStatistics(),this.#R.updateStatus("Calculating samples…"),this.buildSamples(),this.#R.updateStatus("Building locations…"),this.buildLocationMap(),this.#R.updateStatus("Finished processing."),this.#W.snapshot.trace_function_count){this.#R.updateStatus("Building allocation statistics…");const e=this.nodes.length,t=this.nodeFieldCount,s=this.rootNode(),n={};for(let i=0;i<e;i+=t){s.nodeIndex=i;const e=s.traceNodeId();let t=n[e];t||(n[e]=t={count:0,size:0,ids:[]}),t.count++,t.size+=s.selfSize(),t.ids.push(s.id())}this.#X=new i(this.#W,n),this.#R.updateStatus("done")}}buildEdgeIndexes(){const e=this.nodes,t=this.nodeCount,s=this.firstEdgeIndexes,n=this.nodeFieldCount,i=this.edgeFieldsCount,o=this.#J;s[t]=this.containmentEdges.length;for(let r=0,a=0;r<t;++r)s[r]=a,a+=e[r*n+o]*i}buildRetainers(){const e=this.retainingNodes,t=this.retainingEdges,s=this.firstRetainerIndex,n=this.containmentEdges,i=this.edgeFieldsCount,o=this.nodeFieldCount,r=this.edgeToNodeOffset,a=this.firstEdgeIndexes,d=this.nodeCount;for(let e=r,t=n.length;e<t;e+=i){const t=n[e];if(t%o)throw new Error("Invalid toNodeIndex "+t);++s[t/o]}for(let t=0,n=0;t<d;t++){const i=s[t];s[t]=n,e[n]=i,n+=i}s[d]=e.length;let h=a[0];for(let l=0;l<d;++l){const d=h;h=a[l+1];const c=l*o;for(let a=d;a<h;a+=i){const i=n[a+r];if(i%o)throw new Error("Invalid toNodeIndex "+i);const d=s[i/o],h=d+--e[d];e[h]=c,t[h]=a}}}allNodes(){return new m(this.rootNode())}rootNode(){return this.createNode(this.rootNodeIndexInternal)}get rootNodeIndex(){return this.rootNodeIndexInternal}get totalSize(){return this.rootNode().retainedSize()}getDominatedIndex(e){if(e%this.nodeFieldCount)throw new Error("Invalid nodeIndex: "+e);return this.firstDominatedNodeIndex[e/this.nodeFieldCount]}createFilter(e){const t=e.minNodeId,s=e.maxNodeId,n=e.allocationNodeId;let i;if("number"==typeof n){if(i=this.createAllocationStackFilter(n),!i)throw new Error("Unable to create filter");i.key="AllocationNodeId: "+n}else"number"==typeof t&&"number"==typeof s&&(i=this.createNodeIdFilter(t,s),i.key="NodeIdRange: "+t+".."+s);return i}search(e,t){const n=e.query;const i=e.isRegex?new RegExp(n):s.StringUtilities.createPlainTextSearchRegex(n,"i");const o=e.isRegex||!e.caseSensitive?function(e,t,s){return i.test(t)&&e.add(s),e}:function(e,t,s){return-1!==t.indexOf(n)&&e.add(s),e},r=this.strings.reduce(o,new Set);if(!r.size)return[];const a=this.createFilter(t),d=[],h=this.nodes.length,l=this.nodes,c=this.nodeNameOffset,p=this.nodeIdOffset,u=this.nodeFieldCount,f=this.rootNode();for(let e=0;e<h;e+=u)f.nodeIndex=e,a&&!a(f)||r.has(l[e+c])&&d.push(l[e+p]);return d}aggregatesWithFilter(e){const t=this.createFilter(e),s=t?t.key:"allObjects";return this.getAggregatesByClassName(!1,s,t)}createNodeIdFilter(e,t){return function(s){const n=s.id();return n>e&&n<=t}}createAllocationStackFilter(e){if(!this.#X)throw new Error("No Allocation Profile provided");const t=this.#X.traceIds(e);if(!t.length)return;const s={};for(let e=0;e<t.length;e++)s[t[e]]=!0;return function(e){return Boolean(s[e.traceNodeId()])}}getAggregatesByClassName(e,t,s){const n=this.buildAggregates(s);let i;return t&&this.#L[t]?i=this.#L[t]:(this.calculateClassesRetainedSize(n.aggregatesByClassIndex,s),i=n.aggregatesByClassName,t&&(this.#L[t]=i)),!e||t&&this.#B[t]||(this.sortAggregateIndexes(i),t&&(this.#B[t]=e)),i}allocationTracesTops(){return this.#X.serializeTraceTops()}allocationNodeCallers(e){return this.#X.serializeCallers(e)}allocationStack(e){const t=this.createNode(e).traceNodeId();return t?this.#X.serializeAllocationStack(t):null}aggregatesForDiff(){if(this.#U)return this.#U;const e=this.getAggregatesByClassName(!0,"allObjects");this.#U={};const t=this.createNode();for(const s in e){const n=e[s].idxs,i=new Array(n.length),o=new Array(n.length);for(let e=0;e<n.length;e++)t.nodeIndex=n[e],i[e]=t.id(),o[e]=t.selfSize();this.#U[s]={indexes:n,ids:i,selfSizes:o}}return this.#U}isUserRoot(e){return!0}calculateShallowSizes(){}calculateDistances(t){const s=this.nodeCount,n=this.nodeDistances,i=this.#P;for(let e=0;e<s;++e)n[e]=i;const o=new Uint32Array(this.nodeCount);let r=0;for(let e=this.rootNode().edges();e.hasNext();e.next()){const t=e.edge.node();this.isUserRoot(t)&&(n[t.ordinal()]=1,o[r++]=t.nodeIndex)}this.bfs(o,r,n,t),n[this.rootNode().ordinal()]=r>0?e.HeapSnapshotModel.baseSystemDistance:0,o[0]=this.rootNode().nodeIndex,r=1,this.bfs(o,r,n,t)}bfs(e,t,s,n){const i=this.edgeFieldsCount,o=this.nodeFieldCount,r=this.containmentEdges,a=this.firstEdgeIndexes,d=this.edgeToNodeOffset,h=this.edgeTypeOffset,l=this.nodeCount,c=this.edgeWeakType,p=this.#P;let u=0;const f=this.createEdge(0),g=this.createNode(0);for(;u<t;){const l=e[u++],I=l/o,m=s[I]+1,x=a[I],S=a[I+1];g.nodeIndex=l;for(let a=x;a<S;a+=i){if(r[a+h]===c)continue;const i=r[a+d],l=i/o;s[l]===p&&(f.edgeIndex=a,n&&!n(g,f)||(s[l]=m,e[t++]=i))}}if(t>l)throw new Error("BFS failed. Nodes to visit ("+t+") is more than nodes count ("+l+")")}buildAggregates(e){const t={},s={},n=[],i=this.nodes,o=i.length,r=this.nodeNativeType,a=this.nodeFieldCount,d=this.nodeSelfSizeOffset,h=this.nodeTypeOffset,l=this.rootNode(),c=this.nodeDistances;for(let p=0;p<o;p+=a){if(l.nodeIndex=p,e&&!e(l))continue;const o=i[p+d];if(!o&&i[p+h]!==r)continue;const u=l.classIndex(),f=c[p/a];if(u in t){const e=t[u];if(!e)continue;e.distance=Math.min(e.distance,f),++e.count,e.self+=o,e.idxs.push(p)}else{const e=l.type(),i={count:1,distance:f,self:o,maxRet:0,type:e,name:"object"===e||"native"===e?l.name():null,idxs:[p]};t[u]=i,n.push(u),s[l.className()]=i}}for(let e=0,s=n.length;e<s;++e){const s=t[n[e]];s&&(s.idxs=s.idxs.slice())}return{aggregatesByClassName:s,aggregatesByClassIndex:t}}calculateClassesRetainedSize(e,t){const s=this.rootNodeIndexInternal,n=this.createNode(s),i=[s],o=[-1],r=[],a=new Map,d=this.nodeFieldCount,h=this.nodeTypeOffset,l=this.nodeNativeType,c=this.dominatedNodes,p=this.nodes,u=this.firstDominatedNodeIndex;for(;i.length;){const s=i.pop();n.nodeIndex=s;let f=n.classIndex();const g=Boolean(a.get(f)),I=s/d,m=u[I],x=u[I+1];g||t&&!t(n)||!n.selfSize()&&p[s+h]!==l||(e[f].maxRet+=n.retainedSize(),m!==x&&(a.set(f,!0),o.push(i.length),r.push(f)));for(let e=m;e<x;e++)i.push(c[e]);const S=i.length;for(;o[o.length-1]===S;)o.pop(),f=r.pop(),a.set(f,!1)}}sortAggregateIndexes(e){const t=this.createNode(),s=this.createNode();for(const n in e)e[n].idxs.sort(((e,n)=>(t.nodeIndex=e,s.nodeIndex=n,t.id()<s.id()?-1:1)))}isEssentialEdge(e,t){const s=this.containmentEdges[t+this.edgeTypeOffset];if(s===this.edgeInternalType){const s=this.strings[this.containmentEdges[t+this.edgeNameOffset]].match(/^\d+ \/ part of key \(.*? @\d+\) -> value \(.*? @\d+\) pair in WeakMap \(table @(?<tableId>\d+)\)$/);if(s){return this.nodes[e+this.nodeIdOffset]!==parseInt(s.groups.tableId,10)}}return s!==this.edgeWeakType&&(s!==this.edgeShortcutType||e===this.rootNodeIndexInternal)}buildPostOrderIndex(){const e=this.nodeFieldCount,t=this.nodeCount,s=this.rootNodeIndexInternal/e,n=this.edgeFieldsCount,i=this.edgeToNodeOffset,o=this.firstEdgeIndexes,r=this.containmentEdges,a=this.userObjectsMapAndFlag(),d=a?a.map:null,h=a?a.flag:0,l=new Uint32Array(t),c=new Uint32Array(t),p=new Uint32Array(t),u=new Uint32Array(t),f=new Uint8Array(t);let g=0,I=0;l[0]=s,c[0]=o[s],f[s]=1;let m=0;for(;;){for(++m;I>=0;){const t=l[I],a=c[I];if(a<o[t+1]){if(c[I]+=n,!this.isEssentialEdge(t*e,a))continue;const p=r[a+i]/e;if(f[p])continue;const u=!d||d[t]&h,g=!d||d[p]&h;if(t!==s&&g&&!u)continue;++I,l[I]=p,c[I]=o[p],f[p]=1}else u[t]=g,p[g++]=t,--I}if(g===t||m>1)break;const a=new y(`Heap snapshot: ${t-g} nodes are unreachable from the root. Following nodes have only weak retainers:`),x=this.rootNode();--g,I=0,l[0]=s,c[0]=o[s+1];for(let s=0;s<t;++s){if(f[s]||!this.hasOnlyWeakRetainers(s))continue;l[++I]=s,c[I]=o[s],f[s]=1,x.nodeIndex=s*e;const t=[];for(let e=x.retainers();e.hasNext();e.next())t.push(`${e.item().node().name()}@${e.item().node().id()}.${e.item().name()}`);a.addError(`${x.name()} @${x.id()}  weak retainers: ${t.join(", ")}`)}console.warn(a.toString())}if(g!==t){const n=new y("Still found "+(t-g)+" unreachable nodes in heap snapshot:"),i=this.rootNode();--g;for(let s=0;s<t;++s)f[s]||(i.nodeIndex=s*e,n.addError(i.name()+" @"+i.id()),u[s]=g,p[g++]=s);u[s]=g,p[g++]=s,console.warn(n.toString())}return{postOrderIndex2NodeOrdinal:p,nodeOrdinal2PostOrderIndex:u}}hasOnlyWeakRetainers(e){const t=this.edgeTypeOffset,s=this.edgeWeakType,n=this.edgeShortcutType,i=this.containmentEdges,o=this.retainingEdges,r=this.firstRetainerIndex[e],a=this.firstRetainerIndex[e+1];for(let e=r;e<a;++e){const r=i[o[e]+t];if(r!==s&&r!==n)return!1}return!0}buildDominatorTree(e,t){const s=this.nodeFieldCount,n=this.firstRetainerIndex,i=this.retainingNodes,o=this.retainingEdges,r=this.edgeFieldsCount,a=this.edgeToNodeOffset,d=this.firstEdgeIndexes,h=this.containmentEdges,l=this.rootNodeIndexInternal,c=this.userObjectsMapAndFlag(),p=c?c.map:null,u=c?c.flag:0,f=e.length,g=f-1,I=f,m=new Uint32Array(f);for(let e=0;e<g;++e)m[e]=I;m[g]=g;const x=new Uint8Array(f);let S;{S=this.rootNodeIndexInternal/s;const e=d[S+1];for(let n=d[S];n<e;n+=r){if(!this.isEssentialEdge(this.rootNodeIndexInternal,n))continue;x[t[h[n+a]/s]]=1}}let N=!0;for(;N;){N=!1;for(let c=g-1;c>=0;--c){if(0===x[c])continue;if(x[c]=0,m[c]===g)continue;S=e[c];const f=!p||p[S]&u;let y=I;const T=n[S],O=n[S+1];let w=!0;for(let e=T;e<O;++e){const n=o[e],r=i[e];if(!this.isEssentialEdge(r,n))continue;w=!1;const a=r/s,d=!p||p[a]&u;if(r!==l&&f&&!d)continue;let h=t[a];if(m[h]!==I){if(y===I)y=h;else for(;h!==y;){for(;h<y;)h=m[h];for(;y<h;)y=m[y]}if(y===g)break}}if(w&&(y=g),y!==I&&m[c]!==y){m[c]=y,N=!0,S=e[c];const n=d[S]+a,i=d[S+1];for(let e=n;e<i;e+=r){x[t[h[e]/s]]=1}}}}const y=new Uint32Array(f);for(let t=0,s=m.length;t<s;++t)S=e[t],y[S]=e[m[t]];return y}calculateRetainedSizes(e){const t=this.nodeCount,s=this.nodes,n=this.nodeSelfSizeOffset,i=this.nodeFieldCount,o=this.dominatorsTree,r=this.retainedSizes;for(let e=0;e<t;++e)r[e]=s[e*i+n];for(let s=0;s<t-1;++s){const t=e[s];r[o[t]]+=r[t]}}buildDominatedNodes(){const e=this.firstDominatedNodeIndex,t=this.dominatedNodes,s=this.nodeFieldCount,n=this.dominatorsTree;let i=0,o=this.nodeCount;const r=this.rootNodeIndexInternal/s;if(r===i)i=1;else{if(r!==o-1)throw new Error("Root node is expected to be either first or last");o-=1}for(let t=i;t<o;++t)++e[n[t]];let a=0;for(let s=0,n=this.nodeCount;s<n;++s){const n=t[a]=e[s];e[s]=a,a+=n}e[this.nodeCount]=t.length;for(let r=i;r<o;++r){let i=e[n[r]];i+=--t[i],t[i]=r*s}}iterateFilteredChildren(e,t,s){const n=this.firstEdgeIndexes[e],i=this.firstEdgeIndexes[e+1];for(let e=n;e<i;e+=this.edgeFieldsCount){const n=this.containmentEdges[e+this.edgeToNodeOffset]/this.nodeFieldCount;t(this.containmentEdges[e+this.edgeTypeOffset])&&s(n)}}addString(e){return this.strings.push(e),this.strings.length-1}propagateDOMState(){if(-1===this.#Y)return;console.time("propagateDOMState");const e=new Uint8Array(this.nodeCount),t=[],s=[],n=new Map,i=function(i,o,r){if(e[o])return;const a=o*i.nodeFieldCount;i.nodes[a+i.nodeTypeOffset]===i.nodeNativeType?(i.nodes[a+i.#Y]=r,1===r?t.push(o):2===r&&(!function(e,t){const s=e.nodes[t+e.nodeNameOffset];let i=n.get(s);void 0===i&&(i=e.addString("Detached "+e.strings[s]),n.set(s,i)),e.nodes[t+e.nodeNameOffset]=i}(i,a),s.push(o)),e[o]=1):e[o]=1},o=function(e,t,s){e.iterateFilteredChildren(t,(t=>![e.edgeHiddenType,e.edgeInvisibleType,e.edgeWeakType].includes(t)),(t=>i(e,t,s)))};for(let e=0;e<this.nodeCount;++e){const t=this.nodes[e*this.nodeFieldCount+this.#Y];0!==t&&i(this,e,t)}for(;0!==t.length;){o(this,t.pop(),1)}for(;0!==s.length;){const e=s.pop();1!==this.nodes[e*this.nodeFieldCount+this.#Y]&&o(this,e,2)}console.timeEnd("propagateDOMState")}buildSamples(){const t=this.#D;if(!t||!t.length)return;const n=t.length/2,i=new Array(n),o=new Array(n),r=new Array(n),a=this.#k.sample_fields.indexOf("timestamp_us"),d=this.#k.sample_fields.indexOf("last_assigned_id");for(let e=0;e<n;e++)i[e]=0,o[e]=t[2*e+a]/1e3,r[e]=t[2*e+d];const h=this.nodes.length,l=this.nodeFieldCount,c=this.rootNode();for(let e=0;e<h;e+=l){c.nodeIndex=e;const t=c.id();if(t%2==0)continue;const o=s.ArrayUtilities.lowerBound(r,t,s.ArrayUtilities.DEFAULT_COMPARATOR);o!==n&&(i[o]+=c.selfSize())}this.#j=new e.HeapSnapshotModel.Samples(o,r,i)}buildLocationMap(){const t=new Map,s=this.#H;for(let n=0;n<s.length;n+=this.#K){const i=s[n+this.#$],o=s[n+this.#Q],r=s[n+this.#q],a=s[n+this.#G];t.set(i,new e.HeapSnapshotModel.Location(o,r,a))}this.#Z=t}getLocation(e){return this.#Z.get(e)||null}getSamples(){return this.#j}calculateFlags(){throw new Error("Not implemented")}calculateStatistics(){throw new Error("Not implemented")}userObjectsMapAndFlag(){throw new Error("Not implemented")}calculateSnapshotDiff(t,s){let n=this.#M[t];if(n)return n;n={};const i=this.getAggregatesByClassName(!0,"allObjects");for(const e in s){const t=s[e],o=this.calculateDiffForClass(t,i[e]);o&&(n[e]=o)}const o=new e.HeapSnapshotModel.AggregateForDiff;for(const e in i){if(e in s)continue;const t=this.calculateDiffForClass(o,i[e]);t&&(n[e]=t)}return this.#M[t]=n,n}calculateDiffForClass(t,s){const n=t.ids,i=t.indexes,o=t.selfSizes,r=s?s.idxs:[];let a=0,d=0;const h=n.length,l=r.length,c=new e.HeapSnapshotModel.Diff,p=this.createNode(r[d]);for(;a<h&&d<l;){const e=n[a];e<p.id()?(c.deletedIndexes.push(i[a]),c.removedCount++,c.removedSize+=o[a],++a):e>p.id()?(c.addedIndexes.push(r[d]),c.addedCount++,c.addedSize+=p.selfSize(),p.nodeIndex=r[++d]):(++a,p.nodeIndex=r[++d])}for(;a<h;)c.deletedIndexes.push(i[a]),c.removedCount++,c.removedSize+=o[a],++a;for(;d<l;)c.addedIndexes.push(r[d]),c.addedCount++,c.addedSize+=p.selfSize(),p.nodeIndex=r[++d];return c.countDelta=c.addedCount-c.removedCount,c.sizeDelta=c.addedSize-c.removedSize,c.addedCount||c.removedCount?c:null}nodeForSnapshotObjectId(e){for(let t=this.allNodes();t.hasNext();t.next())if(t.node.id()===e)return t.node;return null}nodeClassName(e){const t=this.nodeForSnapshotObjectId(e);return t?t.className():null}idsOfObjectsWithName(e){const t=[];for(let s=this.allNodes();s.hasNext();s.next())s.item().name()===e&&t.push(s.item().id());return t}createEdgesProvider(e){const t=this.createNode(e),s=this.containmentEdgesFilter(),n=new c(this);return new C(this,s,t.edges(),n)}createEdgesProviderForTest(e,t){const s=this.createNode(e),n=new c(this);return new C(this,t,s.edges(),n)}retainingEdgesFilter(){return null}containmentEdgesFilter(){return null}createRetainingEdgesProvider(e){const t=this.createNode(e),s=this.retainingEdgesFilter(),n=new p(this);return new C(this,s,t.retainers(),n)}createAddedNodesProvider(e,t){const s=this.#M[e][t];return new E(this,s.addedIndexes)}createDeletedNodesProvider(e){return new E(this,e)}createNodesProviderForClass(e,t){return new E(this,this.aggregatesWithFilter(t)[e].idxs)}maxJsNodeId(){const e=this.nodeFieldCount,t=this.nodes,s=t.length;let n=0;for(let i=this.nodeIdOffset;i<s;i+=e){const e=t[i];e%2!=0&&(n<e&&(n=e))}return n}updateStaticData(){return new e.HeapSnapshotModel.StaticData(this.nodeCount,this.rootNodeIndexInternal,this.totalSize,this.maxJsNodeId())}}class O{location_fields=[];node_fields=[];node_types=[];edge_fields=[];edge_types=[];trace_function_info_fields=[];trace_node_fields=[];sample_fields=[];type_strings={}}class w{iterator;#ee;#te;iterationOrder;currentComparator;#se;#ne;constructor(e,t){this.iterator=e,this.#ee=t,this.#te=!e.hasNext(),this.iterationOrder=null,this.currentComparator=null,this.#se=0,this.#ne=0}createIterationOrder(){if(!this.iterationOrder){this.iterationOrder=[];for(let e=this.iterator;e.hasNext();e.next())this.iterationOrder.push(e.item().itemIndex())}}isEmpty(){return this.#te}serializeItemsRange(t,s){if(this.createIterationOrder(),t>s)throw new Error("Start position > end position: "+t+" > "+s);if(!this.iterationOrder)throw new Error("Iteration order undefined");if(s>this.iterationOrder.length&&(s=this.iterationOrder.length),this.#se<s&&t<this.iterationOrder.length-this.#ne&&this.currentComparator){const e=this.currentComparator;this.sort(e,this.#se,this.iterationOrder.length-1-this.#ne,t,s-1),t<=this.#se&&(this.#se=s),s>=this.iterationOrder.length-this.#ne&&(this.#ne=this.iterationOrder.length-t)}let n=t;const i=s-t,o=new Array(i);for(let e=0;e<i;++e){const t=this.iterationOrder[n++],s=this.#ee.itemForIndex(t);o[e]=s.serialize()}return new e.HeapSnapshotModel.ItemsRange(t,s,this.iterationOrder.length,o)}sortAndRewind(e){this.currentComparator=e,this.#se=0,this.#ne=0}}class C extends w{snapshot;constructor(e,t,s,n){super(t?new S(s,t):s,n),this.snapshot=e}sort(e,t,n,i,o){const r=e.fieldName1,a=e.fieldName2,d=e.ascending1,h=e.ascending2,l=this.iterator.item().clone(),c=l.clone(),p=this.snapshot.createNode(),u=this.snapshot.createNode();function f(e,t,s){if(l.edgeIndex=t,c.edgeIndex=s,"__proto__"===c.name())return-1;if("__proto__"===l.name())return 1;const n=l.hasStringName()===c.hasStringName()?l.name()<c.name()?-1:l.name()>c.name()?1:0:l.hasStringName()?-1:1;return e?n:-n}function g(e,t,s,n){l.edgeIndex=s,p.nodeIndex=l.nodeIndex();const i=p[e]();c.edgeIndex=n,u.nodeIndex=c.nodeIndex();const o=u[e](),r=i<o?-1:i>o?1:0;return t?r:-r}if(!this.iterationOrder)throw new Error("Iteration order not defined");"!edgeName"===r?s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=f(d,e,t);return 0===s&&(s=g(a,h,e,t)),0===s?e-t:s}),t,n,i,o):"!edgeName"===a?s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=g(r,d,e,t);return 0===s&&(s=f(h,e,t)),0===s?e-t:s}),t,n,i,o):s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=g(r,d,e,t);return 0===s&&(s=g(a,h,e,t)),0===s?e-t:s}),t,n,i,o)}}class E extends w{snapshot;constructor(e,t){const s=new l(e);super(new x(s,t),s),this.snapshot=e}nodePosition(e){this.createIterationOrder();const t=this.snapshot.createNode();let s=0;if(!this.iterationOrder)throw new Error("Iteration order not defined");for(;s<this.iterationOrder.length&&(t.nodeIndex=this.iterationOrder[s],t.id()!==e);s++);if(s===this.iterationOrder.length)return-1;const n=this.iterationOrder[s];let i=0;const o=this.currentComparator,r=this.buildCompareFunction(o);for(let e=0;e<this.iterationOrder.length;e++)r(this.iterationOrder[e],n)<0&&++i;return i}buildCompareFunction(e){const t=this.snapshot.createNode(),s=this.snapshot.createNode(),n=t[e.fieldName1],i=t[e.fieldName2],o=e.ascending1?1:-1,r=e.ascending2?1:-1;function a(e,n){const i=e.call(t),o=e.call(s);return i<o?-n:i>o?n:0}return function(e,d){t.nodeIndex=e,s.nodeIndex=d;let h=a(n,o);return 0===h&&(h=a(i,r)),h||e-d}}sort(e,t,n,i,o){if(!this.iterationOrder)throw new Error("Iteration order not defined");s.ArrayUtilities.sortRange(this.iterationOrder,this.buildCompareFunction(e),t,n,i,o)}}class b extends T{nodeFlags;lazyStringCache;flags;#ie;#oe;constructor(e,t,s){super(e,t),this.nodeFlags={canBeQueried:1,detachedDOMTreeNode:2,pageObject:4},this.lazyStringCache={},this.#oe=s??{heapSnapshotTreatBackingStoreAsContainingObject:!1},this.initialize()}createNode(e){return new F(this,void 0===e?-1:e)}createEdge(e){return new z(this,e)}createRetainingEdge(e){return new _(this,e)}containmentEdgesFilter(){return e=>!e.isInvisible()}retainingEdgesFilter(){const e=this.containmentEdgesFilter();return function(t){return e(t)&&!t.node().isRoot()&&!t.isWeak()}}calculateFlags(){this.flags=new Uint32Array(this.nodeCount),this.markDetachedDOMTreeNodes(),this.markQueriableHeapObjects(),this.markPageOwnedNodes()}calculateShallowSizes(){if(!this.#oe.heapSnapshotTreatBackingStoreAsContainingObject)return;const{nodeCount:e,nodes:t,nodeFieldCount:s,nodeSelfSizeOffset:n}=this,i=4294967295,o=4294967294;if(e>=o)throw new Error("Too many nodes for calculateShallowSizes");const r=new Uint32Array(e),a=[],d=this.createNode(0);for(let t=0;t<e;++t)d.isHidden()||d.isArray()?r[t]=i:(r[t]=t,a.push(t)),d.nodeIndex=d.nextNodeIndex();for(;0!==a.length;){const e=a.pop(),t=r[e];d.nodeIndex=e*s;for(let e=d.edges();e.hasNext();e.next()){const n=e.edge;if(n.isWeak())continue;const d=n.nodeIndex()/s;switch(r[d]){case i:r[d]=t,a.push(d);break;case d:case t:case o:break;default:r[d]=o,a.push(d)}}}for(let a=0;a<e;++a){const e=r[a];switch(e){case i:case o:case a:break;default:{const i=a*s,o=e*s;if(d.nodeIndex=o,d.isSynthetic())break;const r=t[i+n];t[i+n]=0,t[o+n]+=r;break}}}}calculateDistances(){const e=new Set,t=/^\d+ \/ part of key \(.*? @(?<keyId>\d+)\) -> value \(.*? @\d+\) pair in WeakMap \(table @(?<tableId>\d+)\)$/;super.calculateDistances((function(s,n){if(s.isHidden()&&"sloppy_function_map"===n.name()&&"system / NativeContext"===s.rawName())return!1;if(s.isArray()&&"(map descriptors)"===s.rawName()){const e=parseInt(n.name(),10);return e<2||e%3!=1}if(n.isInternal()){const i=n.name().match(t);if(i){const t=s.id().toString(10),o=n.node().id().toString(10);if(!e.has(t+" "+o)){const{keyId:s,tableId:n}=i.groups;let r="";if(s===t)r=n;else{if(n!==t)throw new Error("Invalid IDs in WeakMap edge");r=s}return e.add(r+" "+o),!1}}}return!0}))}isUserRoot(e){return e.isUserRoot()||e.isDocumentDOMTreesRoot()}userObjectsMapAndFlag(){return{map:this.flags,flag:this.nodeFlags.pageObject}}flagsOfNode(e){return this.flags[e.nodeIndex/this.nodeFieldCount]}markDetachedDOMTreeNodes(){const e=this.nodes,t=e.length,s=this.nodeFieldCount,n=this.nodeNativeType,i=this.nodeTypeOffset,o=this.nodeFlags.detachedDOMTreeNode,r=this.rootNode();for(let a=0,d=0;a<t;a+=s,d++){e[a+i]===n&&(r.nodeIndex=a,r.name().startsWith("Detached ")&&(this.flags[d]|=o))}}markQueriableHeapObjects(){const e=this.nodeFlags.canBeQueried,t=this.edgeHiddenType,s=this.edgeInternalType,n=this.edgeInvisibleType,i=this.edgeWeakType,o=this.edgeToNodeOffset,r=this.edgeTypeOffset,a=this.edgeFieldsCount,d=this.containmentEdges,h=this.nodeFieldCount,l=this.firstEdgeIndexes,c=this.flags,p=[];for(let e=this.rootNode().edges();e.hasNext();e.next())e.edge.node().isUserRoot()&&p.push(e.edge.node().nodeIndex/h);for(;p.length;){const u=p.pop();if(c[u]&e)continue;c[u]|=e;const f=l[u],g=l[u+1];for(let l=f;l<g;l+=a){const a=d[l+o]/h;if(c[a]&e)continue;const u=d[l+r];u!==t&&u!==n&&u!==s&&u!==i&&p.push(a)}}}markPageOwnedNodes(){const e=this.edgeShortcutType,t=this.edgeElementType,s=this.edgeToNodeOffset,n=this.edgeTypeOffset,i=this.edgeFieldsCount,o=this.edgeWeakType,r=this.firstEdgeIndexes,a=this.containmentEdges,d=this.nodeFieldCount,h=this.nodeCount,l=this.flags,c=this.nodeFlags.pageObject,p=new Uint32Array(h);let u=0;const f=this.rootNodeIndexInternal/d,g=this.rootNode();for(let o=r[f],h=r[f+1];o<h;o+=i){const i=a[o+n],r=a[o+s];if(i===t){if(g.nodeIndex=r,!g.isDocumentDOMTreesRoot())continue}else if(i!==e)continue;const h=r/d;p[u++]=h,l[h]|=c}for(;u;){const e=p[--u],t=r[e],h=r[e+1];for(let e=t;e<h;e+=i){const t=a[e+s]/d;if(l[t]&c)continue;a[e+n]!==o&&(p[u++]=t,l[t]|=c)}}}calculateStatistics(){const t=this.nodeFieldCount,s=this.nodes,n=s.length,i=this.nodeTypeOffset,o=this.nodeSelfSizeOffset,r=this.nodeNativeType,a=this.nodeCodeType,d=this.nodeConsStringType,h=this.nodeSlicedStringType,l=this.nodeDistances;let c=0,p=0,u=0,f=0,g=0;const I=this.rootNode();for(let m=0;m<n;m+=t){const n=s[m+o];if(l[m/t]>=e.HeapSnapshotModel.baseSystemDistance){g+=n;continue}const x=s[m+i];I.nodeIndex=m,x===r?c+=n:x===a?p+=n:x===d||x===h||"string"===I.type()?u+=n:"Array"===I.name()&&(f+=this.calculateArraySize(I))}this.#ie=new e.HeapSnapshotModel.Statistics,this.#ie.total=this.totalSize,this.#ie.v8heap=this.totalSize-c,this.#ie.native=c,this.#ie.code=p,this.#ie.jsArrays=f,this.#ie.strings=u,this.#ie.system=g}calculateArraySize(e){let t=e.selfSize();const s=e.edgeIndexesStart(),n=e.edgeIndexesEnd(),i=this.containmentEdges,o=this.strings,r=this.edgeToNodeOffset,a=this.edgeTypeOffset,d=this.edgeNameOffset,h=this.edgeFieldsCount,l=this.edgeInternalType;for(let c=s;c<n;c+=h){if(i[c+a]!==l)continue;if("elements"!==o[i[c+d]])continue;const s=i[c+r];e.nodeIndex=s,1===e.retainersCount()&&(t+=e.selfSize());break}return t}getStatistics(){return this.#ie}}class F extends I{constructor(e,t){super(e,t)}canBeQueried(){const e=this.snapshot,t=e.flagsOfNode(this);return Boolean(t&e.nodeFlags.canBeQueried)}rawName(){return super.name()}name(){const e=this.snapshot;if(this.rawType()===e.nodeConsStringType){let t=e.lazyStringCache[this.nodeIndex];return void 0===t&&(t=this.consStringName(),e.lazyStringCache[this.nodeIndex]=t),t}return this.rawName()}consStringName(){const e=this.snapshot,t=e.nodeConsStringType,s=e.edgeInternalType,n=e.edgeFieldsCount,i=e.edgeToNodeOffset,o=e.edgeTypeOffset,r=e.edgeNameOffset,a=e.strings,d=e.containmentEdges,h=e.firstEdgeIndexes,l=e.nodeFieldCount,c=e.nodeTypeOffset,p=e.nodeNameOffset,u=e.nodes,f=[];f.push(this.nodeIndex);let g="";for(;f.length&&g.length<1024;){const e=f.pop();if(u[e+c]!==t){g+=a[u[e+p]];continue}const I=e/l,m=h[I],x=h[I+1];let S=0,N=0;for(let e=m;e<x&&(!S||!N);e+=n){if(d[e+o]===s){const t=a[d[e+r]];"first"===t?S=d[e+i]:"second"===t&&(N=d[e+i])}}f.push(N),f.push(S)}return g}className(){const e=this.type();switch(e){case"hidden":return"(system)";case"object":case"native":return this.name();case"code":return"(compiled code)";case"closure":return"Function";case"regexp":return"RegExp";default:return"("+e+")"}}classIndex(){const e=this.snapshot,t=e.nodes,s=t[this.nodeIndex+e.nodeTypeOffset];return s===e.nodeObjectType||s===e.nodeNativeType?t[this.nodeIndex+e.nodeNameOffset]:-1-s}id(){const e=this.snapshot;return e.nodes[this.nodeIndex+e.nodeIdOffset]}isHidden(){return this.rawType()===this.snapshot.nodeHiddenType}isArray(){return this.rawType()===this.snapshot.nodeArrayType}isSynthetic(){return this.rawType()===this.snapshot.nodeSyntheticType}isUserRoot(){return!this.isSynthetic()}isDocumentDOMTreesRoot(){return this.isSynthetic()&&"(Document DOM trees)"===this.name()}serialize(){const e=super.serialize(),t=this.snapshot,s=t.flagsOfNode(this);return s&t.nodeFlags.canBeQueried&&(e.canBeQueried=!0),s&t.nodeFlags.detachedDOMTreeNode&&(e.detachedDOMTreeNode=!0),e}}class z extends h{constructor(e,t){super(e,t)}clone(){const e=this.snapshot;return new z(e,this.edgeIndex)}hasStringName(){return this.isShortcut()?isNaN(parseInt(this.nameInternal(),10)):this.hasStringNameInternal()}isElement(){return this.rawType()===this.snapshot.edgeElementType}isHidden(){return this.rawType()===this.snapshot.edgeHiddenType}isWeak(){return this.rawType()===this.snapshot.edgeWeakType}isInternal(){return this.rawType()===this.snapshot.edgeInternalType}isInvisible(){return this.rawType()===this.snapshot.edgeInvisibleType}isShortcut(){return this.rawType()===this.snapshot.edgeShortcutType}name(){const e=this.nameInternal();if(!this.isShortcut())return String(e);const t=parseInt(e,10);return String(isNaN(t)?e:t)}toString(){const e=this.name();switch(this.type()){case"context":return"->"+e;case"element":return"["+e+"]";case"weak":return"[["+e+"]]";case"property":return-1===e.indexOf(" ")?"."+e:'["'+e+'"]';case"shortcut":return"string"==typeof e?-1===e.indexOf(" ")?"."+e:'["'+e+'"]':"["+e+"]";case"internal":case"hidden":case"invisible":return"{"+e+"}"}return"?"+e+"?"}hasStringNameInternal(){const e=this.rawType(),t=this.snapshot;return e!==t.edgeElementType&&e!==t.edgeHiddenType}nameInternal(){return this.hasStringNameInternal()?this.snapshot.strings[this.nameOrIndex()]:this.nameOrIndex()}nameOrIndex(){return this.edges[this.edgeIndex+this.snapshot.edgeNameOffset]}rawType(){return this.edges[this.edgeIndex+this.snapshot.edgeTypeOffset]}}class _ extends f{constructor(e,t){super(e,t)}clone(){const e=this.snapshot;return new _(e,this.retainerIndex())}isHidden(){return this.edge().isHidden()}isInternal(){return this.edge().isInternal()}isInvisible(){return this.edge().isInvisible()}isShortcut(){return this.edge().isShortcut()}isWeak(){return this.edge().isWeak()}}var v=Object.freeze({__proto__:null,HeapSnapshotEdge:h,HeapSnapshotNodeIndexProvider:l,HeapSnapshotEdgeIndexProvider:c,HeapSnapshotRetainerEdgeIndexProvider:p,HeapSnapshotEdgeIterator:u,HeapSnapshotRetainerEdge:f,HeapSnapshotRetainerEdgeIterator:g,HeapSnapshotNode:I,HeapSnapshotNodeIterator:m,HeapSnapshotIndexRangeIterator:x,HeapSnapshotFilteredIterator:S,HeapSnapshotProgress:N,HeapSnapshotProblemReport:y,HeapSnapshot:T,HeapSnapshotHeader:class{title;meta;node_count;edge_count;trace_function_count;root_index;constructor(){this.title="",this.meta=new O,this.node_count=0,this.edge_count=0,this.trace_function_count=0,this.root_index=0}},HeapSnapshotItemProvider:w,HeapSnapshotEdgesProvider:C,HeapSnapshotNodesProvider:E,JSHeapSnapshot:b,JSHeapSnapshotNode:F,JSHeapSnapshotEdge:z,JSHeapSnapshotRetainerEdge:_});class A{#R;#re;#ae;#de;#he;#le;#ce;#pe;#ue;constructor(e){this.#fe(),this.#R=new N(e),this.#re=[],this.#ae=null,this.#de=!1,this.#ge()}dispose(){this.#fe()}#fe(){this.#pe="",this.#he=void 0}close(){this.#de=!0,this.#ae&&this.#ae("")}buildSnapshot(e){this.#he=this.#he||{},this.#R.updateStatus("Processing snapshot…");const t=new b(this.#he,this.#R,e);return this.#fe(),t}#Ie(){let e=0;const t="0".charCodeAt(0),s="9".charCodeAt(0),n="]".charCodeAt(0),i=this.#pe.length;for(;;){for(;e<i;){const i=this.#pe.charCodeAt(e);if(t<=i&&i<=s)break;if(i===n)return this.#pe=this.#pe.slice(e+1),!1;++e}if(e===i)return this.#pe="",!0;let o=0;const r=e;for(;e<i;){const n=this.#pe.charCodeAt(e);if(t>n||n>s)break;o*=10,o+=n-t,++e}if(e===i)return this.#pe=this.#pe.slice(r),!0;if(!this.#le)throw new Error("Array not instantiated");this.#le[this.#ce++]=o}}#me(){this.#R.updateStatus("Parsing strings…");const e=this.#pe.lastIndexOf("]");if(-1===e)throw new Error("Incomplete JSON");if(this.#pe=this.#pe.slice(0,e+1),!this.#he)throw new Error("No snapshot in parseStringsArray");this.#he.strings=JSON.parse(this.#pe)}write(e){this.#re.push(e),this.#ae&&(this.#ae(this.#re.shift()),this.#ae=null)}#xe(){if(this.#re.length>0)return Promise.resolve(this.#re.shift());const{promise:e,resolve:t}=s.PromiseUtilities.promiseWithResolvers();return this.#ae=t,e}async#Se(e,t){for(;;){const s=this.#pe.indexOf(e,t||0);if(-1!==s)return s;t=this.#pe.length-e.length+1,this.#pe+=await this.#xe()}}async#Ne(e,t,s){const n=await this.#Se(e),i=await this.#Se("[",n);for(this.#pe=this.#pe.slice(i+1),this.#le=s?new Uint32Array(s):[],this.#ce=0;this.#Ie();)s?this.#R.updateProgress(t,this.#ce,this.#le.length):this.#R.updateStatus(t),this.#pe+=await this.#xe();const o=this.#le;return this.#le=null,o}async#ge(){const e='"snapshot"',t=await this.#Se(e);if(-1===t)throw new Error("Snapshot token not found");this.#R.updateStatus("Loading snapshot info…");const s=this.#pe.slice(t+10+1);for(this.#ue=new n.TextUtils.BalancedJSONTokenizer((e=>{this.#pe=this.#ue.remainder(),this.#ue=null,this.#he=this.#he||{},this.#he.snapshot=JSON.parse(e)})),this.#ue.write(s);this.#ue;)this.#ue.write(await this.#xe());this.#he=this.#he||{};const i=await this.#Ne('"nodes"',"Loading nodes… {PH1}%",this.#he.snapshot.meta.node_fields.length*this.#he.snapshot.node_count);this.#he.nodes=i;const o=await this.#Ne('"edges"',"Loading edges… {PH1}%",this.#he.snapshot.meta.edge_fields.length*this.#he.snapshot.edge_count);if(this.#he.edges=o,this.#he.snapshot.trace_function_count){const e=await this.#Ne('"trace_function_infos"',"Loading allocation traces… {PH1}%",this.#he.snapshot.meta.trace_function_info_fields.length*this.#he.snapshot.trace_function_count);this.#he.trace_function_infos=e;const t=await this.#Se(":"),s=await this.#Se('"',t),n=this.#pe.indexOf("["),i=this.#pe.lastIndexOf("]",s);this.#he.trace_tree=JSON.parse(this.#pe.substring(n,i+1)),this.#pe=this.#pe.slice(i+1)}if(this.#he.snapshot.meta.sample_fields){const e=await this.#Ne('"samples"',"Loading samples…");this.#he.samples=e}if(this.#he.snapshot.meta.location_fields){const e=await this.#Ne('"locations"',"Loading locations…");this.#he.locations=e}else this.#he.locations=[];this.#R.updateStatus("Loading strings…");const r=await this.#Se('"strings"'),a=await this.#Se("[",r);for(this.#pe=this.#pe.slice(a);!this.#de;)this.#pe+=await this.#xe();this.#me()}}var k=Object.freeze({__proto__:null,HeapSnapshotLoader:A});var D=Object.freeze({__proto__:null,HeapSnapshotWorkerDispatcher:class{#ye;#Te;constructor(e,t){this.#ye=[],this.#Te=t}sendEvent(e,t){this.#Te({eventName:e,data:t})}dispatchMessage({data:t}){const s={callId:t.callId,result:null,error:void 0,errorCallStack:void 0,errorMethodName:void 0};try{switch(t.disposition){case"createLoader":this.#ye[t.objectId]=new A(this);break;case"dispose":delete this.#ye[t.objectId];break;case"getter":{const e=this.#ye[t.objectId][t.methodName];s.result=e;break}case"factory":{const e=this.#ye[t.objectId],n=e[t.methodName].apply(e,t.methodArguments);n&&(this.#ye[t.newObjectId]=n),s.result=Boolean(n);break}case"method":{const e=this.#ye[t.objectId];s.result=e[t.methodName].apply(e,t.methodArguments);break}case"evaluateForTest":try{globalThis.HeapSnapshotWorker={AllocationProfile:d,HeapSnapshot:v,HeapSnapshotLoader:k},globalThis.HeapSnapshotModel=e,s.result=self.eval(t.source)}catch(e){s.result=e.toString()}}}catch(e){s.error=e.toString(),s.errorCallStack=e.stack,t.methodName&&(s.errorMethodName=t.methodName)}this.#Te(s)}}});export{d as AllocationProfile,v as HeapSnapshot,k as HeapSnapshotLoader,D as HeapSnapshotWorkerDispatcher};
