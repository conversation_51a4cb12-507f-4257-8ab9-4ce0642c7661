import groovy.json.JsonSlurper

buildscript {
    def kotlin_version = rootProject.ext.has('kotlinVersion') ? rootProject.ext.get('kotlinVersion') : project.properties['RNGH_kotlinVersion']

    repositories {
        mavenCentral()
        google()
    }

    dependencies {
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
        classpath("com.android.tools.build:gradle:7.2.1")
        classpath("com.diffplug.spotless:spotless-plugin-gradle:6.7.2")
    }
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def resolveReactNativeDirectory() {
    def reactNativeLocation = safeExtGet("REACT_NATIVE_NODE_MODULES_DIR", null)
    if (reactNativeLocation != null) {
        return file(reactNativeLocation)
    }

    // Fallback to node resolver for custom directory structures like monorepos.
    def reactNativePackage = file(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim())
    if (reactNativePackage.exists()) {
        return reactNativePackage.parentFile
    }

    throw new Exception(
        "[react-native-gesture-handler] Unable to resolve react-native location in " +
            "node_modules. You should add project extension property (in app/build.gradle) " +
            "`REACT_NATIVE_NODE_MODULES_DIR` with path to react-native."
    )
}

if (isNewArchitectureEnabled()) {
    apply plugin: 'com.facebook.react'
}
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

if (project == rootProject) {
    apply from: "spotless.gradle"
}

// Check whether Reanimated 2.3 or higher is installed alongside Gesture Handler
def shouldUseCommonInterfaceFromReanimated() {
    def reanimated = rootProject.subprojects.find { it.name == 'react-native-reanimated' }
    if (reanimated != null) {
        def inputFile = new File(reanimated.projectDir, '../package.json')
        def json = new JsonSlurper().parseText(inputFile.text)
        def reanimatedVersion = json.version as String
        def (major, minor, patch) = reanimatedVersion.tokenize('.')
        return (Integer.parseInt(major) == 2 && Integer.parseInt(minor) >= 3) || Integer.parseInt(major) == 3
    } else {
        return false
    }
}

def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

def REACT_NATIVE_DIR = resolveReactNativeDirectory()

def reactProperties = new Properties()
file("$REACT_NATIVE_DIR/ReactAndroid/gradle.properties").withInputStream { reactProperties.load(it) }

def REACT_NATIVE_VERSION = reactProperties.getProperty("VERSION_NAME")
def REACT_NATIVE_MINOR_VERSION = REACT_NATIVE_VERSION.startsWith("0.0.0-") ? 1000 : REACT_NATIVE_VERSION.split("\\.")[1].toInteger()

repositories {
    mavenCentral()
}

android {
    compileSdkVersion safeExtGet("compileSdkVersion", 33)

    namespace "com.swmansion.gesturehandler"
    buildFeatures {
        buildConfig = true
        prefab = true
    }

    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 21)
        targetSdkVersion safeExtGet('targetSdkVersion', 33)
        versionCode 1
        versionName "1.0"
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
        buildConfigField "int", "REACT_NATIVE_MINOR_VERSION", REACT_NATIVE_MINOR_VERSION.toString()

        if (isNewArchitectureEnabled()) {
            var appProject = rootProject.allprojects.find {it.plugins.hasPlugin('com.android.application')}
            externalNativeBuild {
                cmake {
                    cppFlags "-O2", "-frtti", "-fexceptions", "-Wall", "-Werror", "-std=c++20", "-DANDROID"
                    arguments "-DREACT_NATIVE_DIR=${REACT_NATIVE_DIR}",
                        "-DREACT_NATIVE_MINOR_VERSION=${REACT_NATIVE_MINOR_VERSION}",
                        "-DANDROID_STL=c++_shared"
                    abiFilters (*reactNativeArchitectures())
                }
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    if (isNewArchitectureEnabled()) {
        externalNativeBuild {
            cmake {
                path "src/main/jni/CMakeLists.txt"
            }
        }
    }

    packagingOptions {
        // For some reason gradle only complains about the duplicated version of libreact_render libraries
        // while there are more libraries copied in intermediates folder of the lib build directory, we exclude
        // only the ones that make the build fail (ideally we should only include libgesturehandler but we
        // are only allowed to specify exclude patterns)
        exclude "**/libreact_render*.so"
        exclude "**/libreactnative.so"
        exclude "**/libjsi.so"
    }

    sourceSets.main {
        java {
            // Include "common/" only when it's not provided by Reanimated to mitigate
            // multiple definitions of the same class preventing build
            if (shouldUseCommonInterfaceFromReanimated()) {
                srcDirs += 'reanimated/src/main/java'
            } else {
                srcDirs += 'common/src/main/java'
                srcDirs += 'noreanimated/src/main/java'
            }

            if (isNewArchitectureEnabled()) {
                srcDirs += 'fabric/src/main/java'
            } else {
                // this folder also includes files from codegen so the library can compile with
                // codegen turned off
                srcDirs += 'paper/src/main/java'
            }
        }
    }
}

def kotlin_version = safeExtGet('kotlinVersion', project.properties['RNGH_kotlinVersion'])

dependencies {
    implementation 'com.facebook.react:react-native:+' // from node_modules
    

    if (shouldUseCommonInterfaceFromReanimated()) {
        // Include Reanimated as dependency to load the common interface
        implementation (rootProject.subprojects.find { it.name == 'react-native-reanimated' }) {
            exclude group:'com.facebook.fbjni' // resolves "Duplicate class com.facebook.jni.CppException"
        }
    }

    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation "androidx.core:core-ktx:1.6.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
}
