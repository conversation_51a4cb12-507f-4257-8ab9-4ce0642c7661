var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};function t(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}function r(e,t,r,i){return new(r||(r=Promise))((function(n,s){function a(e){try{c(i.next(e))}catch(e){s(e)}}function o(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){e.done?n(e.value):function(e){return e instanceof r?e:new r((function(t){t(e)}))}(e.value).then(a,o)}c((i=i.apply(e,t||[])).next())}))}function i(e,t){var r,i,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(o){return function(c){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,o[0]&&(a=0)),a;)try{if(r=1,i&&(n=2&o[0]?i.return:o[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,o[1])).done)return n;switch(i=0,n&&(o=[2&o[0],n.value]),o[0]){case 0:case 1:n=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(n=(n=a.trys).length>0&&n[n.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!n||o[1]>n[0]&&o[1]<n[3])){a.label=o[1];break}if(6===o[0]&&a.label<n[1]){a.label=n[1],n=o;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(o);break}n[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],i=0}finally{r=n=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}}function n(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function s(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a}function a(e,t,r){if(r||2===arguments.length)for(var i,n=0,s=t.length;n<s;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}function o(e){return this instanceof o?(this.v=e,this):new o(e)}function c(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=r.apply(e,t||[]),s=[];return i={},a("next"),a("throw"),a("return"),i[Symbol.asyncIterator]=function(){return this},i;function a(e){n[e]&&(i[e]=function(t){return new Promise((function(r,i){s.push([e,t,r,i])>1||c(e,t)}))})}function c(e,t){try{!function(e){e.value instanceof o?Promise.resolve(e.value.v).then(l,d):u(s[0][2],e)}(n[e](t))}catch(e){u(s[0][3],e)}}function l(e){c("next",e)}function d(e){c("throw",e)}function u(e,t){e(t),s.shift(),s.length&&c(s[0][0],s[0][1])}}function l(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=n(e),t={},i("next"),i("throw"),i("return"),t[Symbol.asyncIterator]=function(){return this},t);function i(r){t[r]=e[r]&&function(t){return new Promise((function(i,n){(function(e,t,r,i){Promise.resolve(i).then((function(t){e({value:t,done:r})}),t)})(i,n,(t=e[r](t)).done,t.value)}))}}}function d(e){return"function"==typeof e}function u(e){var t=e((function(e){Error.call(e),e.stack=(new Error).stack}));return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var h=u((function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}));function p(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var f=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var e,t,r,i,o;if(!this.closed){this.closed=!0;var c=this._parentage;if(c)if(this._parentage=null,Array.isArray(c))try{for(var l=n(c),u=l.next();!u.done;u=l.next()){u.value.remove(this)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=l.return)&&t.call(l)}finally{if(e)throw e.error}}else c.remove(this);var p=this.initialTeardown;if(d(p))try{p()}catch(e){o=e instanceof h?e.errors:[e]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var y=n(f),m=y.next();!m.done;m=y.next()){var w=m.value;try{g(w)}catch(e){o=o??[],e instanceof h?o=a(a([],s(o)),s(e.errors)):o.push(e)}}}catch(e){r={error:e}}finally{try{m&&!m.done&&(i=y.return)&&i.call(y)}finally{if(r)throw r.error}}}if(o)throw new h(o)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)g(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&p(t,e)},e.prototype.remove=function(t){var r=this._finalizers;r&&p(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e;var t}(),y=f.EMPTY;function m(e){return e instanceof f||e&&"closed"in e&&d(e.remove)&&d(e.add)&&d(e.unsubscribe)}function g(e){d(e)?e():e.unsubscribe()}var w={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},v={setTimeout:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var n=v.delegate;return null!=n&&n.setTimeout?n.setTimeout.apply(n,a([e,t],s(r))):setTimeout.apply(void 0,a([e,t],s(r)))},clearTimeout:function(e){var t=v.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function b(e){v.setTimeout((function(){var t=w.onUnhandledError;if(!t)throw e;t(e)}))}function k(){}var S=C("C",void 0,void 0);function C(e,t,r){return{kind:e,value:t,error:r}}var T=null;function E(e){if(w.useDeprecatedSynchronousErrorHandling){var t=!T;if(t&&(T={errorThrown:!1,error:null}),e(),t){var r=T,i=r.errorThrown,n=r.error;if(T=null,i)throw n}}else e()}var I=function(e){function r(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,m(t)&&t.add(r)):r.destination=M,r}return t(r,e),r.create=function(e,t,r){return new _(e,t,r)},r.prototype.next=function(e){this.isStopped?D(function(e){return C("N",e,void 0)}(e),this):this._next(e)},r.prototype.error=function(e){this.isStopped?D(function(e){return C("E",void 0,e)}(e),this):(this.isStopped=!0,this._error(e))},r.prototype.complete=function(){this.isStopped?D(S,this):(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(e){this.destination.next(e)},r.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r}(f),x=Function.prototype.bind;function F(e,t){return x.call(e,t)}var R=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){P(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){P(e)}else P(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){P(e)}},e}(),_=function(e){function r(t,r,i){var n,s,a=e.call(this)||this;d(t)||!t?n={next:t??void 0,error:r??void 0,complete:i??void 0}:a&&w.useDeprecatedNextContext?((s=Object.create(t)).unsubscribe=function(){return a.unsubscribe()},n={next:t.next&&F(t.next,s),error:t.error&&F(t.error,s),complete:t.complete&&F(t.complete,s)}):n=t;return a.destination=new R(n),a}return t(r,e),r}(I);function P(e){w.useDeprecatedSynchronousErrorHandling?function(e){w.useDeprecatedSynchronousErrorHandling&&T&&(T.errorThrown=!0,T.error=e)}(e):b(e)}function D(e,t){var r=w.onStoppedNotification;r&&v.setTimeout((function(){return r(e,t)}))}var M={closed:!0,next:k,error:function(e){throw e},complete:k},A="function"==typeof Symbol&&Symbol.observable||"@@observable";function q(e){return e}function O(e){return 0===e.length?q:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var N=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var i=this,n=function(e){return e&&e instanceof I||function(e){return e&&d(e.next)&&d(e.error)&&d(e.complete)}(e)&&m(e)}(e)?e:new _(e,t,r);return E((function(){var e=i,t=e.operator,r=e.source;n.add(t?t.call(n,r):r?i._subscribe(n):i._trySubscribe(n))})),n},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=L(t))((function(t,i){var n=new _({next:function(t){try{e(t)}catch(e){i(e),n.unsubscribe()}},error:i,complete:t});r.subscribe(n)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[A]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return O(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=L(e))((function(e,r){var i;t.subscribe((function(e){return i=e}),(function(e){return r(e)}),(function(){return e(i)}))}))},e.create=function(t){return new e(t)},e}();function L(e){var t;return null!==(t=e??w.Promise)&&void 0!==t?t:Promise}function B(e){return function(t){if(function(e){return d(null==e?void 0:e.lift)}(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}function j(e,t,r,i,n){return new H(e,t,r,i,n)}var H=function(e){function r(t,r,i,n,s,a){var o=e.call(this,t)||this;return o.onFinalize=s,o.shouldUnsubscribe=a,o._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,o._error=n?function(e){try{n(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,o._complete=i?function(){try{i()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,o}return t(r,e),r.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}},r}(I),$=u((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),K=function(e){function r(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return t(r,e),r.prototype.lift=function(e){var t=new V(this,this);return t.operator=e,t},r.prototype._throwIfClosed=function(){if(this.closed)throw new $},r.prototype.next=function(e){var t=this;E((function(){var r,i;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var s=n(t.currentObservers),a=s.next();!a.done;a=s.next()){a.value.next(e)}}catch(e){r={error:e}}finally{try{a&&!a.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}}}))},r.prototype.error=function(e){var t=this;E((function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}}))},r.prototype.complete=function(){var e=this;E((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}}))},r.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(r.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),r.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},r.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},r.prototype._innerSubscribe=function(e){var t=this,r=this,i=r.hasError,n=r.isStopped,s=r.observers;return i||n?y:(this.currentObservers=null,s.push(e),new f((function(){t.currentObservers=null,p(s,e)})))},r.prototype._checkFinalizedStatuses=function(e){var t=this,r=t.hasError,i=t.thrownError,n=t.isStopped;r?e.error(i):n&&e.complete()},r.prototype.asObservable=function(){var e=new N;return e.source=this,e},r.create=function(e,t){return new V(e,t)},r}(N),V=function(e){function r(t,r){var i=e.call(this)||this;return i.destination=t,i.source=r,i}return t(r,e),r.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},r.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},r.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},r.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:y},r}(K),W={now:function(){return(W.delegate||Date).now()},delegate:void 0},z=function(e){function r(t,r,i){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===i&&(i=W);var n=e.call(this)||this;return n._bufferSize=t,n._windowTime=r,n._timestampProvider=i,n._buffer=[],n._infiniteTimeWindow=!0,n._infiniteTimeWindow=r===1/0,n._bufferSize=Math.max(1,t),n._windowTime=Math.max(1,r),n}return t(r,e),r.prototype.next=function(t){var r=this,i=r.isStopped,n=r._buffer,s=r._infiniteTimeWindow,a=r._timestampProvider,o=r._windowTime;i||(n.push(t),!s&&n.push(a.now()+o)),this._trimBuffer(),e.prototype.next.call(this,t)},r.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,i=this._buffer.slice(),n=0;n<i.length&&!e.closed;n+=r?1:2)e.next(i[n]);return this._checkFinalizedStatuses(e),t},r.prototype._trimBuffer=function(){var e=this,t=e._bufferSize,r=e._timestampProvider,i=e._buffer,n=e._infiniteTimeWindow,s=(n?1:2)*t;if(t<1/0&&s<i.length&&i.splice(0,i.length-s),!n){for(var a=r.now(),o=0,c=1;c<i.length&&i[c]<=a;c+=2)o=c;o&&i.splice(0,o+1)}},r}(K),U=function(e){function r(t,r){return e.call(this)||this}return t(r,e),r.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},r}(f),G={setInterval:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var n=G.delegate;return null!=n&&n.setInterval?n.setInterval.apply(n,a([e,t],s(r))):setInterval.apply(void 0,a([e,t],s(r)))},clearInterval:function(e){var t=G.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},Q=function(e){function r(t,r){var i=e.call(this,t,r)||this;return i.scheduler=t,i.work=r,i.pending=!1,i}return t(r,e),r.prototype.schedule=function(e,t){var r;if(void 0===t&&(t=0),this.closed)return this;this.state=e;var i=this.id,n=this.scheduler;return null!=i&&(this.id=this.recycleAsyncId(n,i,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(n,this.id,t),this},r.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),G.setInterval(e.flush.bind(e,this),r)},r.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&G.clearInterval(t)},r.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},r.prototype._execute=function(e,t){var r,i=!1;try{this.work(e)}catch(e){i=!0,r=e||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),r},r.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,i=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,p(i,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},r}(U),J=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=W.now,e}(),X=new(function(e){function r(t,r){void 0===r&&(r=J.now);var i=e.call(this,t,r)||this;return i.actions=[],i._active=!1,i}return t(r,e),r.prototype.flush=function(e){var t=this.actions;if(this._active)t.push(e);else{var r;this._active=!0;do{if(r=e.execute(e.state,e.delay))break}while(e=t.shift());if(this._active=!1,r){for(;e=t.shift();)e.unsubscribe();throw r}}},r}(J))(Q),Y=X,Z=new N((function(e){return e.complete()}));function ee(e){return e&&d(e.schedule)}function te(e){return e[e.length-1]}function re(e){return ee(te(e))?e.pop():void 0}var ie=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function ne(e){return d(null==e?void 0:e.then)}function se(e){return d(e[A])}function ae(e){return Symbol.asyncIterator&&d(null==e?void 0:e[Symbol.asyncIterator])}function oe(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var ce="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function le(e){return d(null==e?void 0:e[ce])}function de(e){return c(this,arguments,(function(){var t,r,n;return i(this,(function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,o(t.read())];case 3:return r=i.sent(),n=r.value,r.done?[4,o(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,o(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}}))}))}function ue(e){return d(null==e?void 0:e.getReader)}function he(e){if(e instanceof N)return e;if(null!=e){if(se(e))return function(e){return new N((function(t){var r=e[A]();if(d(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")}))}(e);if(ie(e))return function(e){return new N((function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()}))}(e);if(ne(e))return function(e){return new N((function(t){e.then((function(e){t.closed||(t.next(e),t.complete())}),(function(e){return t.error(e)})).then(null,b)}))}(e);if(ae(e))return pe(e);if(le(e))return function(e){return new N((function(t){var r,i;try{for(var s=n(e),a=s.next();!a.done;a=s.next()){var o=a.value;if(t.next(o),t.closed)return}}catch(e){r={error:e}}finally{try{a&&!a.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}t.complete()}))}(e);if(ue(e))return function(e){return pe(de(e))}(e)}throw oe(e)}function pe(e){return new N((function(t){(function(e,t){var n,s,a,o;return r(this,void 0,void 0,(function(){var r,c;return i(this,(function(i){switch(i.label){case 0:i.trys.push([0,5,6,11]),n=l(e),i.label=1;case 1:return[4,n.next()];case 2:if((s=i.sent()).done)return[3,4];if(r=s.value,t.next(r),t.closed)return[2];i.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return c=i.sent(),a={error:c},[3,11];case 6:return i.trys.push([6,,9,10]),s&&!s.done&&(o=n.return)?[4,o.call(n)]:[3,8];case 7:i.sent(),i.label=8;case 8:return[3,10];case 9:if(a)throw a.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}}))}))})(e,t).catch((function(e){return t.error(e)}))}))}function fe(e,t,r,i,n){void 0===i&&(i=0),void 0===n&&(n=!1);var s=t.schedule((function(){r(),n?e.add(this.schedule(null,i)):this.unsubscribe()}),i);if(e.add(s),!n)return s}function ye(e,t){return void 0===t&&(t=0),B((function(r,i){r.subscribe(j(i,(function(r){return fe(i,e,(function(){return i.next(r)}),t)}),(function(){return fe(i,e,(function(){return i.complete()}),t)}),(function(r){return fe(i,e,(function(){return i.error(r)}),t)})))}))}function me(e,t){return void 0===t&&(t=0),B((function(r,i){i.add(e.schedule((function(){return r.subscribe(i)}),t))}))}function ge(e,t){if(!e)throw new Error("Iterable cannot be null");return new N((function(r){fe(r,t,(function(){var i=e[Symbol.asyncIterator]();fe(r,t,(function(){i.next().then((function(e){e.done?r.complete():r.next(e.value)}))}),0,!0)}))}))}function we(e,t){if(null!=e){if(se(e))return function(e,t){return he(e).pipe(me(t),ye(t))}(e,t);if(ie(e))return function(e,t){return new N((function(r){var i=0;return t.schedule((function(){i===e.length?r.complete():(r.next(e[i++]),r.closed||this.schedule())}))}))}(e,t);if(ne(e))return function(e,t){return he(e).pipe(me(t),ye(t))}(e,t);if(ae(e))return ge(e,t);if(le(e))return function(e,t){return new N((function(r){var i;return fe(r,t,(function(){i=e[ce](),fe(r,t,(function(){var e,t,n;try{t=(e=i.next()).value,n=e.done}catch(e){return void r.error(e)}n?r.complete():r.next(t)}),0,!0)})),function(){return d(null==i?void 0:i.return)&&i.return()}}))}(e,t);if(ue(e))return function(e,t){return ge(de(e),t)}(e,t)}throw oe(e)}function ve(e,t){return t?we(e,t):he(e)}function be(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ve(e,re(e))}var ke=u((function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}}));function Se(e,t){var r="object"==typeof t;return new Promise((function(i,n){var s=new _({next:function(e){i(e),s.unsubscribe()},error:n,complete:function(){r?i(t.defaultValue):n(new ke)}});e.subscribe(s)}))}function Ce(e,t){return B((function(r,i){var n=0;r.subscribe(j(i,(function(r){i.next(e.call(t,r,n++))})))}))}var Te=Array.isArray;function Ee(e){return Ce((function(t){return function(e,t){return Te(t)?e.apply(void 0,a([],s(t))):e(t)}(e,t)}))}Array.isArray,Object.getPrototypeOf,Object.prototype,Object.keys;function Ie(e,t,r,i,n,s,a,o){var c=[],l=0,d=0,u=!1,h=function(){u&&!c.length&&!l&&t.complete()},p=function(e){return l<i?f(e):c.push(e)},f=function(e){s&&t.next(e),l++;var o=!1;he(r(e,d++)).subscribe(j(t,(function(e){null==n||n(e),s?p(e):t.next(e)}),(function(){o=!0}),void 0,(function(){if(o)try{l--;for(var e=function(){var e=c.shift();a?fe(t,a,(function(){return f(e)})):f(e)};c.length&&l<i;)e();h()}catch(e){t.error(e)}})))};return e.subscribe(j(t,p,(function(){u=!0,h()}))),function(){null==o||o()}}function xe(e,t,r){return void 0===r&&(r=1/0),d(t)?xe((function(r,i){return Ce((function(e,n){return t(r,e,i,n)}))(he(e(r,i)))}),r):("number"==typeof t&&(r=t),B((function(t,i){return Ie(t,i,e,r)})))}function Fe(e){return void 0===e&&(e=1/0),xe(q,e)}function Re(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Fe(1)(ve(e,re(e)))}function _e(e){return new N((function(t){he(e()).subscribe(t)}))}var Pe=["addListener","removeListener"],De=["addEventListener","removeEventListener"],Me=["on","off"];function Ae(e,t,r,i){if(d(r)&&(i=r,r=void 0),i)return Ae(e,t,r).pipe(Ee(i));var n=s(function(e){return d(e.addEventListener)&&d(e.removeEventListener)}(e)?De.map((function(i){return function(n){return e[i](t,n,r)}})):function(e){return d(e.addListener)&&d(e.removeListener)}(e)?Pe.map(qe(e,t)):function(e){return d(e.on)&&d(e.off)}(e)?Me.map(qe(e,t)):[],2),a=n[0],o=n[1];if(!a&&ie(e))return xe((function(e){return Ae(e,t,r)}))(he(e));if(!a)throw new TypeError("Invalid event target");return new N((function(e){var t=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.next(1<t.length?t:t[0])};return a(t),function(){return o(t)}}))}function qe(e,t){return function(r){return function(i){return e[r](t,i)}}}function Oe(e,t,r){void 0===e&&(e=0),void 0===r&&(r=Y);var i=-1;return null!=t&&(ee(t)?r=t:i=t),new N((function(t){var n=function(e){return e instanceof Date&&!isNaN(e)}(e)?+e-r.now():e;n<0&&(n=0);var s=0;return r.schedule((function(){t.closed||(t.next(s++),0<=i?this.schedule(void 0,i):t.complete())}),n)}))}function Ne(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=re(e),i=function(e,t){return"number"==typeof te(e)?e.pop():t}(e,1/0),n=e;return n.length?1===n.length?he(n[0]):Fe(i)(ve(n,r)):Z}var Le=new N(k),Be=Array.isArray;function je(e){return 1===e.length&&Be(e[0])?e[0]:e}function He(e,t){return B((function(r,i){var n=0;r.subscribe(j(i,(function(r){return e.call(t,r,n++)&&i.next(r)})))}))}function $e(e){return function(t){for(var r=[],i=function(i){r.push(he(e[i]).subscribe(j(t,(function(e){if(r){for(var n=0;n<r.length;n++)n!==i&&r[n].unsubscribe();r=null}t.next(e)}))))},n=0;r&&!t.closed&&n<e.length;n++)i(n)}}function Ke(e){return B((function(t,r){var i,n=null,s=!1;n=t.subscribe(j(r,void 0,void 0,(function(a){i=he(e(a,Ke(e)(t))),n?(n.unsubscribe(),n=null,i.subscribe(r)):s=!0}))),s&&(n.unsubscribe(),n=null,i.subscribe(r))}))}function Ve(e){return B((function(t,r){var i=!1;t.subscribe(j(r,(function(e){i=!0,r.next(e)}),(function(){i||r.next(e),r.complete()})))}))}function We(e){return e<=0?function(){return Z}:B((function(t,r){var i=0;t.subscribe(j(r,(function(t){++i<=e&&(r.next(t),e<=i&&r.complete())})))}))}function ze(){return B((function(e,t){e.subscribe(j(t,k))}))}function Ue(e){return void 0===e&&(e=Ge),B((function(t,r){var i=!1;t.subscribe(j(r,(function(e){i=!0,r.next(e)}),(function(){return i?r.complete():r.error(e())})))}))}function Ge(){return new ke}function Qe(e,t){var r=arguments.length>=2;return function(i){return i.pipe(e?He((function(t,r){return e(t,r,i)})):q,We(1),r?Ve(t):Ue((function(){return new ke})))}}function Je(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?B((function(t,r){$e(a([t],s(e)))(r)})):q}function Xe(e){var t;void 0===e&&(e=1/0);var r=(t=e&&"object"==typeof e?e:{count:e}).count,i=void 0===r?1/0:r,n=t.delay,s=t.resetOnSuccess,a=void 0!==s&&s;return i<=0?q:B((function(e,t){var r,s=0,o=function(){var c=!1;r=e.subscribe(j(t,(function(e){a&&(s=0),t.next(e)}),void 0,(function(e){if(s++<i){var a=function(){r?(r.unsubscribe(),r=null,o()):c=!0};if(null!=n){var l="number"==typeof n?Oe(n):he(n(e,s)),d=j(t,(function(){d.unsubscribe(),a()}),(function(){t.complete()}));l.subscribe(d)}else a()}else t.error(e)}))),c&&(r.unsubscribe(),r=null,o())};o()}))}function Ye(e,t,r){var i=d(e)||t||r?{next:e,error:t,complete:r}:e;return i?B((function(e,t){var r;null===(r=i.subscribe)||void 0===r||r.call(i);var n=!0;e.subscribe(j(t,(function(e){var r;null===(r=i.next)||void 0===r||r.call(i,e),t.next(e)}),(function(){var e;n=!1,null===(e=i.complete)||void 0===e||e.call(i),t.complete()}),(function(e){var r;n=!1,null===(r=i.error)||void 0===r||r.call(i,e),t.error(e)}),(function(){var e,t;n&&(null===(e=i.unsubscribe)||void 0===e||e.call(i)),null===(t=i.finalize)||void 0===t||t.call(i)})))})):q}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Symbol.dispose??=Symbol("dispose"),Symbol.asyncDispose??=Symbol("asyncDispose");const Ze=Symbol.dispose,et=Symbol.asyncDispose;class tt{#e=!1;#t=[];get disposed(){return this.#e}dispose(){if(!this.#e){this.#e=!0;for(const e of this.#t.reverse())e[Ze]()}}use(e){return e&&this.#t.push(e),e}adopt(e,t){return this.#t.push({[Ze](){t(e)}}),e}defer(e){this.#t.push({[Ze](){e()}})}move(){if(this.#e)throw new ReferenceError("a disposed stack can not use anything new");const e=new tt;return e.#t=this.#t,this.#e=!0,e}[Ze]=this.dispose;[Symbol.toStringTag]="DisposableStack"}class rt{#e=!1;#t=[];get disposed(){return this.#e}async dispose(){if(!this.#e){this.#e=!0;for(const e of this.#t.reverse())await e[et]()}}use(e){return e&&this.#t.push(e),e}adopt(e,t){return this.#t.push({[et]:()=>t(e)}),e}defer(e){this.#t.push({[et]:()=>e()})}move(){if(this.#e)throw new ReferenceError("a disposed stack can not use anything new");const e=new rt;return e.#t=this.#t,this.#e=!0,e}[et]=this.dispose;[Symbol.toStringTag]="AsyncDisposableStack"}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class it{#r;#i=new Map;constructor(e=function(e){return{all:e=e||new Map,on:function(t,r){var i=e.get(t);i?i.push(r):e.set(t,[r])},off:function(t,r){var i=e.get(t);i&&(r?i.splice(i.indexOf(r)>>>0,1):e.set(t,[]))},emit:function(t,r){var i=e.get(t);i&&i.slice().map((function(e){e(r)})),(i=e.get("*"))&&i.slice().map((function(e){e(t,r)}))}}}(new Map)){this.#r=e}on(e,t){const r=this.#i.get(e);return void 0===r?this.#i.set(e,[t]):r.push(t),this.#r.on(e,t),this}off(e,t){const r=this.#i.get(e)??[];if(void 0===t){for(const t of r)this.#r.off(e,t);return this.#i.delete(e),this}const i=r.lastIndexOf(t);return i>-1&&this.#r.off(e,...r.splice(i,1)),this}emit(e,t){return this.#r.emit(e,t),this.listenerCount(e)>0}once(e,t){const r=i=>{t(i),this.off(e,r)};return this.on(e,r)}listenerCount(e){return this.#i.get(e)?.length||0}removeAllListeners(e){return void 0!==e?this.off(e):(this[Ze](),this)}[Ze](){for(const[e,t]of this.#i)for(const r of t)this.#r.off(e,r);this.#i.clear()}}class nt{#n;#s;#a;constructor(e,t,r){this.#n=e,this.#s=t,this.#a=r,this.#n.on(this.#s,this.#a)}[Ze](){this.#n.off(this.#s,this.#a)}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const st=(e,t)=>{if(!e)throw new Error(t)},at=!("undefined"==typeof process||!process.version);
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
let ot=null;const ct=e=>at?async(...t)=>{dt&&lt.push(e+t),(await async function(){return ot||(ot=(await import("debug")).default),ot}())(e)(t)}:(...t)=>{const r=globalThis.__PUPPETEER_DEBUG;if(!r)return;("*"===r||(r.endsWith("*")?e.startsWith(r):e===r))&&console.log(`${e}:`,...t)};let lt=[],dt=!1;
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class ut extends Error{constructor(e){super(e),this.name=this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class ht extends ut{}class pt extends ut{#o;#c="";set code(e){this.#o=e}get code(){return this.#o}set originalMessage(e){this.#c=e}get originalMessage(){return this.#c}}class ft extends ut{}class yt extends pt{}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const mt={letter:{width:8.5,height:11},legal:{width:8.5,height:14},tabloid:{width:11,height:17},ledger:{width:17,height:11},a0:{width:33.1,height:46.8},a1:{width:23.4,height:33.1},a2:{width:16.54,height:23.4},a3:{width:11.7,height:16.54},a4:{width:8.27,height:11.7},a5:{width:5.83,height:8.27},a6:{width:4.13,height:5.83}},gt=ct("puppeteer:error"),wt=(Object.freeze({width:800,height:600}),Symbol("Source URL for Puppeteer evaluation scripts"));
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class vt{static INTERNAL_URL="pptr:internal";static fromCallSite(e,t){const r=new vt;return r.#l=e,r.#d=t.toString(),r}static parse=e=>{e=e.slice(5);const[t="",r=""]=e.split(";"),i=new vt;return i.#l=t,i.#d=decodeURIComponent(r),i};static isPuppeteerURL=e=>e.startsWith("pptr:");#l;#d;get functionName(){return this.#l}get siteString(){return this.#d}toString(){return`pptr:${[this.#l,encodeURIComponent(this.#d)].join(";")}`}}const bt=(e,t)=>{if(Object.prototype.hasOwnProperty.call(t,wt))return t;const r=Error.prepareStackTrace;Error.prepareStackTrace=(e,t)=>t[2];const i=(new Error).stack;return Error.prepareStackTrace=r,Object.assign(t,{[wt]:vt.fromCallSite(e,i)})},kt=e=>"string"==typeof e||e instanceof String,St=e=>"number"==typeof e||e instanceof Number;function Ct(e,...t){if(kt(e))return st(0===t.length,"Cannot evaluate a string with arguments"),e;return`(${e})(${t.map((function(e){return Object.is(e,void 0)?"undefined":JSON.stringify(e)})).join(",")})`}let Tt=null;async function Et(){if(!Tt)try{Tt=await import("fs/promises")}catch(e){if(e instanceof TypeError)throw new Error("Cannot write to a path outside of a Node-like environment.");throw e}return Tt}async function It(e,t){const r=[],i=e.getReader();if(t){const e=await Et(),n=await e.open(t,"w+");try{for(;;){const{done:e,value:t}=await i.read();if(e)break;r.push(t),await n.writeFile(t)}}finally{await n.close()}}else for(;;){const{done:e,value:t}=await i.read();if(e)break;r.push(t)}try{return Buffer.concat(r)}catch(e){return gt(e),null}}async function xt(e,t){return new ReadableStream({async pull(r){const{data:i,base64Encoded:n,eof:s}=await e.send("IO.read",{handle:t});r.enqueue(function(e,t){return t?Uint8Array.from(atob(e),(e=>e.codePointAt(0))):(new TextEncoder).encode(e)}(i,n??!1)),s&&(await e.send("IO.close",{handle:t}),r.close())}})}function Ft(e){return 0===e?Le:Oe(e).pipe(Ce((()=>{throw new ht(`Timed out after waiting ${e}ms`)})))}const Rt="__puppeteer_utility_world__",_t=/^[\040\t]*\/\/[@#] sourceURL=\s*(\S*?)\s*$/m;const Pt=500;const Dt={px:1,in:96,cm:37.8,mm:3.78};function Mt(e,t="in"){if(void 0===e)return;let r;if(St(e))r=e;else{if(!kt(e))throw new Error("page.pdf() Cannot handle parameter type: "+typeof e);{const t=e;let i=t.substring(t.length-2).toLowerCase(),n="";i in Dt?n=t.substring(0,t.length-2):(i="px",n=t);const s=Number(n);st(!isNaN(s),"Failed to parse parameter value: "+t),r=s*Dt[i]}}return r/Dt[t]}function At(e,t){return new N((r=>{const i=e=>{r.next(e)};return e.on(t,i),()=>{e.off(t,i)}}))}function qt(e){return xe((t=>ve(Promise.resolve(e(t))).pipe(He((e=>e)),Ce((()=>t)))))}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Ot=new Map([["geolocation","geolocation"],["midi","midi"],["notifications","notifications"],["camera","videoCapture"],["microphone","audioCapture"],["background-sync","backgroundSync"],["ambient-light-sensor","sensors"],["accelerometer","sensors"],["gyroscope","sensors"],["magnetometer","sensors"],["accessibility-events","accessibilityEvents"],["clipboard-read","clipboardReadWrite"],["clipboard-write","clipboardReadWrite"],["clipboard-sanitized-write","clipboardSanitizedWrite"],["payment-handler","paymentHandler"],["persistent-storage","durableStorage"],["idle-detection","idleDetection"],["midi-sysex","midiSysex"]]);class Nt extends it{constructor(){super()}async waitForTarget(e,t={}){const{timeout:r=3e4}=t;return await Se(Ne(At(this,"targetcreated"),At(this,"targetchanged"),ve(this.targets())).pipe(qt(e),Je(Ft(r))))}async pages(){return(await Promise.all(this.browserContexts().map((e=>e.pages())))).reduce(((e,t)=>e.concat(t)),[])}isConnected(){return this.connected}[Ze](){this.close().catch(gt)}[et](){return this.close()}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Lt extends it{constructor(){super()}async waitForTarget(e,t={}){const{timeout:r=3e4}=t;return await Se(Ne(At(this,"targetcreated"),At(this,"targetchanged"),ve(this.targets())).pipe(qt(e),Je(Ft(r))))}get closed(){return!this.browser().browserContexts().includes(this)}get id(){}[Ze](){this.close().catch(gt)}[et](){return this.close()}}var Bt,jt;!function(e){e.Disconnected=Symbol("CDPSession.Disconnected"),e.Swapped=Symbol("CDPSession.Swapped"),e.Ready=Symbol("CDPSession.Ready"),e.SessionAttached="sessionattached",e.SessionDetached="sessiondetached"}(Bt||(Bt={}));class Ht extends it{constructor(){super()}parentSession(){}}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class $t{static create(e){return new $t(e)}static async race(e){const t=new Set;try{const r=e.map((e=>e instanceof $t?(e.#u&&t.add(e),e.valueOrThrow()):e));return await Promise.race(r)}finally{for(const e of t)e.reject(new Error("Timeout cleared"))}}#h=!1;#p=!1;#f;#y;#m=new Promise((e=>{this.#y=e}));#u;#g;constructor(e){e&&e.timeout>0&&(this.#g=new ht(e.message),this.#u=setTimeout((()=>{this.reject(this.#g)}),e.timeout))}#w(e){clearTimeout(this.#u),this.#f=e,this.#y()}resolve(e){this.#p||this.#h||(this.#h=!0,this.#w(e))}reject(e){this.#p||this.#h||(this.#p=!0,this.#w(e))}resolved(){return this.#h}finished(){return this.#h||this.#p}value(){return this.#f}#v;valueOrThrow(){return this.#v||(this.#v=(async()=>{if(await this.#m,this.#p)throw this.#f;return this.#f})()),this.#v}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */!function(e){e.PAGE="page",e.BACKGROUND_PAGE="background_page",e.SERVICE_WORKER="service_worker",e.SHARED_WORKER="shared_worker",e.BROWSER="browser",e.WEBVIEW="webview",e.OTHER="other",e.TAB="tab"}(jt||(jt={}));class Kt{constructor(){}async worker(){return null}async page(){return null}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */function Vt(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function Wt(e,t,r){return e.message=t,e.originalMessage=r??e.originalMessage,e}function zt(e){let t=e.error.message;return e.error&&"object"==typeof e.error&&"data"in e.error&&(t+=` ${e.error.data}`),t}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ut{#b=new Map;#k=function(){let e=0;return()=>++e}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */();create(e,t,r){const i=new Gt(this.#k(),e,t);this.#b.set(i.id,i);try{r(i.id)}catch(e){throw i.promise.valueOrThrow().catch(gt).finally((()=>{this.#b.delete(i.id)})),i.reject(e),e}return i.promise.valueOrThrow().finally((()=>{this.#b.delete(i.id)}))}reject(e,t,r){const i=this.#b.get(e);i&&this._reject(i,t,r)}_reject(e,t,r){let i,n;t instanceof pt?(i=t,i.cause=e.error,n=t.message):(i=e.error,n=t),e.reject(Wt(i,`Protocol error (${e.label}): ${n}`,r))}resolve(e,t){const r=this.#b.get(e);r&&r.resolve(t)}clear(){for(const e of this.#b.values())this._reject(e,new yt("Target closed"));this.#b.clear()}getPendingProtocolErrors(){const e=[];for(const t of this.#b.values())e.push(new Error(`${t.label} timed out. Trace: ${t.error.stack}`));return e}}class Gt{#S;#C=new pt;#T=$t.create();#E;#I;constructor(e,t,r){this.#S=e,this.#I=t,r&&(this.#E=setTimeout((()=>{this.#T.reject(Wt(this.#C,`${t} timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.`))}),r))}resolve(e){clearTimeout(this.#E),this.#T.resolve(e)}reject(e){clearTimeout(this.#E),this.#T.reject(e)}get id(){return this.#S}get promise(){return this.#T}get error(){return this.#C}get label(){return this.#I}}class Qt extends Ht{#x;#F;#b=new Ut;#R;#_;#n;constructor(e,t,r,i){super(),this.#R=e,this.#F=t,this.#x=r,this.#_=i}_setTarget(e){this.#n=e}_target(){return st(this.#n,"Target must exist"),this.#n}connection(){return this.#R}parentSession(){if(!this.#_)return this;const e=this.#R?.session(this.#_);return e??void 0}send(e,t,r){return this.#R?this.#R._rawSend(this.#b,e,t,this.#x,r):Promise.reject(new yt(`Protocol error (${e}): Session closed. Most likely the ${this.#F} has been closed.`))}_onMessage(e){e.id?e.error?this.#b.reject(e.id,zt(e),e.error.message):this.#b.resolve(e.id,e.result):(st(!e.id),this.emit(e.method,e.params))}async detach(){if(!this.#R)throw new Error(`Session already detached. Most likely the ${this.#F} has been closed.`);await this.#R.send("Target.detachFromTarget",{sessionId:this.#x})}_onClosed(){this.#b.clear(),this.#R=void 0,this.emit(Bt.Disconnected,void 0)}id(){return this.#x}getPendingProtocolErrors(){return this.#b.getPendingProtocolErrors()}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Jt{#P;#D;constructor(){this.#P=null,this.#D=null}setDefaultTimeout(e){this.#P=e}setDefaultNavigationTimeout(e){this.#D=e}navigationTimeout(){return null!==this.#D?this.#D:null!==this.#P?this.#P:3e4}timeout(){return null!==this.#P?this.#P:3e4}}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Xt{static Guard=class{#M;constructor(e){this.#M=e}[Ze](){return this.#M.release()}};#A=!1;#q=[];async acquire(){if(!this.#A)return this.#A=!0,new Xt.Guard(this);const e=$t.create();return this.#q.push(e.resolve.bind(e)),await e.valueOrThrow(),new Xt.Guard(this)}release(){const e=this.#q.shift();e?e():this.#A=!1}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Yt=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Zt=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});const er=new WeakSet;function tr(e,t){let r=!1;if(e.prototype[Ze]){const t=e.prototype[Ze];e.prototype[Ze]=function(){if(!er.has(this))return t.call(this);er.delete(this)},r=!0}if(e.prototype[et]){const t=e.prototype[et];e.prototype[et]=function(){if(!er.has(this))return t.call(this);er.delete(this)},r=!0}return r&&(e.prototype.move=function(){return er.add(this),this}),e}function rr(e=(e=>`Attempted to use disposed ${e.constructor.name}.`)){return(t,r)=>function(...r){if(this.disposed)throw new Error(e(this));return t.call(this,...r)}}function ir(e,t){const r=new WeakMap;let i=-1;return function(...t){if(-1===i&&(i=t.length),i!==t.length)throw new Error("Memoized method was called with the wrong number of arguments");let n=!1,s=r;for(const e of t)s.has(e)||(n=!0,s.set(e,new WeakMap)),s=s.get(e);if(n)return e.call(this,...t)}}function nr(e=function(){return this}){return(t,r)=>{const i=new WeakMap;return async function(...r){const n={stack:[],error:void 0,hasError:!1};try{const s=e.call(this);let a=i.get(s);a||(a=new Xt,i.set(s,a));Yt(n,await a.acquire(),!0);return await t.call(this,...r)}catch(e){n.error=e,n.hasError=!0}finally{const e=Zt(n);e&&await e}}}}new WeakMap;var sr,ar=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},or=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});!function(e){e.Action="action"}(sr||(sr={}));class cr extends it{static race(e){return fr.create(e)}visibility=null;_timeout=3e4;#O=!0;#N=!0;#L=!0;operators={conditions:(e,t)=>xe((r=>Ne(...e.map((e=>e(r,t)))).pipe(Ve(r)))),retryAndRaceWithSignalAndTimer:e=>{const t=[];return e&&t.push(Ae(e,"abort").pipe(Ce((()=>{throw e.reason})))),t.push(Ft(this._timeout)),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return O(e)}(Xe({delay:yr}),Je(...t))}};get timeout(){return this._timeout}setTimeout(e){const t=this._clone();return t._timeout=e,t}setVisibility(e){const t=this._clone();return t.visibility=e,t}setWaitForEnabled(e){const t=this._clone();return t.#N=e,t}setEnsureElementIsInTheViewport(e){const t=this._clone();return t.#O=e,t}setWaitForStableBoundingBox(e){const t=this._clone();return t.#L=e,t}copyOptions(e){return this._timeout=e._timeout,this.visibility=e.visibility,this.#N=e.#N,this.#O=e.#O,this.#L=e.#L,this}#B=(e,t)=>this.#N?ve(e.frame.waitForFunction((e=>{if(!(e instanceof HTMLElement))return!0;return!["BUTTON","INPUT","SELECT","TEXTAREA","OPTION","OPTGROUP"].includes(e.nodeName)||!e.hasAttribute("disabled")}),{timeout:this._timeout,signal:t},e)).pipe(ze()):Z;#j=e=>this.#L?_e((()=>ve(e.evaluate((e=>new Promise((t=>{window.requestAnimationFrame((()=>{const r=e.getBoundingClientRect();window.requestAnimationFrame((()=>{const i=e.getBoundingClientRect();t([{x:r.x,y:r.y,width:r.width,height:r.height},{x:i.x,y:i.y,width:i.width,height:i.height}])}))}))}))))))).pipe(Qe((([e,t])=>e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height)),Xe({delay:yr}),ze()):Z;#H=e=>this.#O?ve(e.isIntersectingViewport({threshold:0})).pipe(He((e=>!e)),xe((()=>ve(e.scrollIntoView()))),xe((()=>_e((()=>ve(e.isIntersectingViewport({threshold:0})))).pipe(Qe(q),Xe({delay:yr}),ze())))):Z;#$(e){const t=e?.signal;return this._wait(e).pipe(this.operators.conditions([this.#H,this.#j,this.#B],t),Ye((()=>this.emit(sr.Action,void 0))),xe((t=>ve(t.click(e)).pipe(Ke((e=>{throw t.dispose().catch(gt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(t))}#K(e,t){const r=t?.signal;return this._wait(t).pipe(this.operators.conditions([this.#H,this.#j,this.#B],r),Ye((()=>this.emit(sr.Action,void 0))),xe((t=>ve(t.evaluate((e=>e instanceof HTMLSelectElement?"select":e instanceof HTMLTextAreaElement?"typeable-input":e instanceof HTMLInputElement?new Set(["textarea","text","url","tel","search","password","number","email"]).has(e.type)?"typeable-input":"other-input":e.isContentEditable?"contenteditable":"unknown"))).pipe(xe((r=>{switch(r){case"select":return ve(t.select(e).then(k));case"contenteditable":case"typeable-input":return ve(t.evaluate(((e,t)=>{const r=e.isContentEditable?e.innerText:e.value;if(t.length<=r.length||!t.startsWith(e.value))return e.isContentEditable?e.innerText="":e.value="",t;const i=e.isContentEditable?e.innerText:e.value;return e.isContentEditable?(e.innerText="",e.innerText=i):(e.value="",e.value=i),t.substring(i.length)}),e)).pipe(xe((e=>ve(t.type(e)))));case"other-input":return ve(t.focus()).pipe(xe((()=>ve(t.evaluate(((e,t)=>{e.value=t,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))}),e)))));case"unknown":throw new Error("Element cannot be filled out.")}}))).pipe(Ke((e=>{throw t.dispose().catch(gt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(r))}#V(e){const t=e?.signal;return this._wait(e).pipe(this.operators.conditions([this.#H,this.#j],t),Ye((()=>this.emit(sr.Action,void 0))),xe((e=>ve(e.hover()).pipe(Ke((t=>{throw e.dispose().catch(gt),t}))))),this.operators.retryAndRaceWithSignalAndTimer(t))}#W(e){const t=e?.signal;return this._wait(e).pipe(this.operators.conditions([this.#H,this.#j],t),Ye((()=>this.emit(sr.Action,void 0))),xe((t=>ve(t.evaluate(((e,t,r)=>{void 0!==t&&(e.scrollTop=t),void 0!==r&&(e.scrollLeft=r)}),e?.scrollTop,e?.scrollLeft)).pipe(Ke((e=>{throw t.dispose().catch(gt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(t))}clone(){return this._clone()}async waitHandle(e){return await Se(this._wait(e).pipe(this.operators.retryAndRaceWithSignalAndTimer(e?.signal)))}async wait(e){const t={stack:[],error:void 0,hasError:!1};try{const r=ar(t,await this.waitHandle(e),!1);return await r.jsonValue()}catch(e){t.error=e,t.hasError=!0}finally{or(t)}}map(e){return new hr(this._clone(),(t=>t.evaluateHandle(e)))}filter(e){return new ur(this._clone(),(async(t,r)=>(await t.frame.waitForFunction(e,{signal:r,timeout:this._timeout},t),!0)))}filterHandle(e){return new ur(this._clone(),e)}mapHandle(e){return new hr(this._clone(),e)}click(e){return Se(this.#$(e))}fill(e,t){return Se(this.#K(e,t))}hover(e){return Se(this.#V(e))}scroll(e){return Se(this.#W(e))}}class lr extends cr{static create(e,t){return new lr(e,t).setTimeout("getDefaultTimeout"in e?e.getDefaultTimeout():e.page().getDefaultTimeout())}#z;#U;constructor(e,t){super(),this.#z=e,this.#U=t}_clone(){return new lr(this.#z,this.#U)}_wait(e){const t=e?.signal;return _e((()=>ve(this.#z.waitForFunction(this.#U,{timeout:this.timeout,signal:t})))).pipe(Ue())}}class dr extends cr{#G;constructor(e){super(),this.#G=e,this.copyOptions(this.#G)}get delegate(){return this.#G}setTimeout(e){const t=super.setTimeout(e);return t.#G=this.#G.setTimeout(e),t}setVisibility(e){const t=super.setVisibility(e);return t.#G=t.#G.setVisibility(e),t}setWaitForEnabled(e){const t=super.setWaitForEnabled(e);return t.#G=this.#G.setWaitForEnabled(e),t}setEnsureElementIsInTheViewport(e){const t=super.setEnsureElementIsInTheViewport(e);return t.#G=this.#G.setEnsureElementIsInTheViewport(e),t}setWaitForStableBoundingBox(e){const t=super.setWaitForStableBoundingBox(e);return t.#G=this.#G.setWaitForStableBoundingBox(e),t}}class ur extends dr{#Q;constructor(e,t){super(e),this.#Q=t}_clone(){return new ur(this.delegate.clone(),this.#Q).copyOptions(this)}_wait(e){return this.delegate._wait(e).pipe(xe((t=>ve(Promise.resolve(this.#Q(t,e?.signal))).pipe(He((e=>e)),Ce((()=>t))))),Ue())}}class hr extends dr{#J;constructor(e,t){super(e),this.#J=t}_clone(){return new hr(this.delegate.clone(),this.#J).copyOptions(this)}_wait(e){return this.delegate._wait(e).pipe(xe((t=>ve(Promise.resolve(this.#J(t,e?.signal))))))}}class pr extends cr{static create(e,t){return new pr(e,t).setTimeout("getDefaultTimeout"in e?e.getDefaultTimeout():e.page().getDefaultTimeout())}#z;#X;constructor(e,t){super(),this.#z=e,this.#X=t}#Y=e=>this.visibility?(()=>{switch(this.visibility){case"hidden":return _e((()=>ve(e.isHidden())));case"visible":return _e((()=>ve(e.isVisible())))}})().pipe(Qe(q),Xe({delay:yr}),ze()):Z;_clone(){return new pr(this.#z,this.#X).copyOptions(this)}_wait(e){const t=e?.signal;return _e((()=>ve(this.#z.waitForSelector(this.#X,{visible:!1,timeout:this._timeout,signal:t})))).pipe(He((e=>null!==e)),Ue(),this.operators.conditions([this.#Y],t))}}class fr extends cr{static create(e){const t=function(e){for(const t of e)if(!(t instanceof cr))throw new Error("Unknown locator for race candidate");return e}(e);return new fr(t)}#Z;constructor(e){super(),this.#Z=e}_clone(){return new fr(this.#Z.map((e=>e.clone()))).copyOptions(this)}_wait(e){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===(e=je(e)).length?he(e[0]):new N($e(e))}(...this.#Z.map((t=>t._wait(e))))}}const yr=100;
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var mr=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},gr=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},wr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},vr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});let br=(()=>{let e,t=it,r=[];return class extends t{static{const i="function"==typeof Symbol&&Symbol.metadata?Object.create(t[Symbol.metadata]??null):void 0;gr(this,null,e,{kind:"method",name:"screenshot",static:!1,private:!1,access:{has:e=>"screenshot"in e,get:e=>e.screenshot},metadata:i},null,r),i&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:i})}_isDragging=(mr(this,r),!1);_timeoutSettings=new Jt;#ee=new WeakMap;#te=new z(1);constructor(){var e,t,r;super(),At(this,"request").pipe(xe((e=>Re(be(1),Ne(At(this,"requestfailed"),At(this,"requestfinished"),At(this,"response").pipe(Ce((e=>e.request())))).pipe(He((t=>t.id===e.id)),We(1),Ce((()=>-1)))))),(e=(e,t)=>be(e+t),t=0,void 0===r&&(r=1/0),B((function(i,n){var s=t;return Ie(i,n,(function(t,r){return e(s,t,r)}),r,(function(e){s=e}),!1,void 0,(function(){return s=null}))}))),function(e){return B((function(t,r){he(e).subscribe(j(r,(function(){return r.complete()}),k)),!r.closed&&t.subscribe(r)}))}(At(this,"close")),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=re(e);return B((function(t,i){(r?Re(e,t,r):Re(e,t)).subscribe(i)}))}(0)).subscribe(this.#te)}on(e,t){if("request"!==e)return super.on(e,t);let r=this.#ee.get(t);return void 0===r&&(r=e=>{e.enqueueInterceptAction((()=>t(e)))},this.#ee.set(t,r)),super.on(e,r)}off(e,t){return"request"===e&&(t=this.#ee.get(t)||t),super.off(e,t)}locator(e){return"string"==typeof e?pr.create(this,e):lr.create(this,e)}locatorRace(e){return cr.race(e)}async $(e){return await this.mainFrame().$(e)}async $$(e){return await this.mainFrame().$$(e)}async evaluateHandle(e,...t){return e=bt(this.evaluateHandle.name,e),await this.mainFrame().evaluateHandle(e,...t)}async $eval(e,t,...r){return t=bt(this.$eval.name,t),await this.mainFrame().$eval(e,t,...r)}async $$eval(e,t,...r){return t=bt(this.$$eval.name,t),await this.mainFrame().$$eval(e,t,...r)}async addScriptTag(e){return await this.mainFrame().addScriptTag(e)}async addStyleTag(e){return await this.mainFrame().addStyleTag(e)}url(){return this.mainFrame().url()}async content(){return await this.mainFrame().content()}async setContent(e,t){await this.mainFrame().setContent(e,t)}async goto(e,t){return await this.mainFrame().goto(e,t)}async waitForNavigation(e={}){return await this.mainFrame().waitForNavigation(e)}waitForRequest(e,t={}){const{timeout:r=this._timeoutSettings.timeout()}=t;if("string"==typeof e){const t=e;e=e=>e.url()===t}return Se(At(this,"request").pipe(qt(e),Je(Ft(r),At(this,"close").pipe(Ce((()=>{throw new yt("Page closed!")}))))))}waitForResponse(e,t={}){const{timeout:r=this._timeoutSettings.timeout()}=t;if("string"==typeof e){const t=e;e=e=>e.url()===t}return Se(At(this,"response").pipe(qt(e),Je(Ft(r),At(this,"close").pipe(Ce((()=>{throw new yt("Page closed!")}))))))}waitForNetworkIdle(e={}){return Se(this.waitForNetworkIdle$(e))}waitForNetworkIdle$(e={}){const{timeout:t=this._timeoutSettings.timeout(),idleTime:r=Pt,concurrency:i=0}=e;return this.#te.pipe((n=e=>e>i?Z:Oe(r),B((function(e,t){var r=null,i=0,a=!1,o=function(){return a&&!r&&t.complete()};e.subscribe(j(t,(function(e){null==r||r.unsubscribe();var a=0,c=i++;he(n(e,c)).subscribe(r=j(t,(function(r){return t.next(s?s(e,r,c,a++):r)}),(function(){r=null,o()})))}),(function(){a=!0,o()})))}))),Ce((()=>{})),Je(Ft(t),At(this,"close").pipe(Ce((()=>{throw new yt("Page closed!")})))));var n,s}async waitForFrame(e,t={}){const{timeout:r=this.getDefaultTimeout()}=t;return kt(e)&&(e=t=>e===t.url()),await Se(Ne(At(this,"frameattached"),At(this,"framenavigated"),ve(this.frames())).pipe(qt(e),Qe(),Je(Ft(r),At(this,"close").pipe(Ce((()=>{throw new yt("Page closed.")}))))))}async emulate(e){await Promise.all([this.setUserAgent(e.userAgent),this.setViewport(e.viewport)])}async evaluate(e,...t){return e=bt(this.evaluate.name,e),await this.mainFrame().evaluate(e,...t)}async _maybeWriteBufferToFile(e,t){if(!e)return;const r=await Et();await r.writeFile(e,t)}async screencast(e={}){const[{ScreenRecorder:t},[r,i,n]]=await Promise.all([import("./package/lib/esm/puppeteer/node/ScreenRecorder.js"),this.#re()]);let s;if(e.crop){const{x:t,y:a,width:o,height:c}=Sr(kr(e.crop));if(t<0||a<0)throw new Error("`crop.x` and `crop.y` must be greater than or equal to 0.");if(o<=0||c<=0)throw new Error("`crop.height` and `crop.width` must be greater than or equal to 0.");const l=r/n,d=i/n;if(t+o>l)throw new Error(`\`crop.width\` cannot be larger than the viewport width (${l}).`);if(a+c>d)throw new Error(`\`crop.height\` cannot be larger than the viewport height (${d}).`);s={x:t*n,y:a*n,width:o*n,height:c*n}}if(void 0!==e.speed&&e.speed<=0)throw new Error("`speed` must be greater than 0.");if(void 0!==e.scale&&e.scale<=0)throw new Error("`scale` must be greater than 0.");const a=new t(this,r,i,{...e,path:e.ffmpegPath,crop:s});try{await this._startScreencast()}catch(e){throw a.stop(),e}if(e.path){const{createWriteStream:t}=await import("fs"),r=t(e.path,"binary");a.pipe(r)}return a}#ie=0;#ne;async _startScreencast(){++this.#ie,this.#ne||(this.#ne=this.mainFrame().client.send("Page.startScreencast",{format:"png"}).then((()=>new Promise((e=>this.mainFrame().client.once("Page.screencastFrame",(()=>e()))))))),await this.#ne}async _stopScreencast(){--this.#ie,this.#ne&&(this.#ne=void 0,0===this.#ie&&await this.mainFrame().client.send("Page.stopScreencast"))}async#re(){const e={stack:[],error:void 0,hasError:!1};try{const t=this.viewport(),r=wr(e,new tt,!1);return t&&0!==t.deviceScaleFactor&&(await this.setViewport({...t,deviceScaleFactor:0}),r.defer((()=>{this.setViewport(t).catch(gt)}))),await this.mainFrame().isolatedRealm().evaluate((()=>[window.visualViewport.width*window.devicePixelRatio,window.visualViewport.height*window.devicePixelRatio,window.devicePixelRatio]))}catch(t){e.error=t,e.hasError=!0}finally{vr(e)}}async screenshot(e={}){const t={stack:[],error:void 0,hasError:!1};try{await this.bringToFront();const r={...e,clip:e.clip?{...e.clip}:void 0};if(void 0===r.type&&void 0!==r.path){const e=r.path;switch(e.slice(e.lastIndexOf(".")+1).toLowerCase()){case"png":r.type="png";break;case"jpeg":case"jpg":r.type="jpeg";break;case"webp":r.type="webp"}}if(void 0!==r.quality){if(r.quality<0&&r.quality>100)throw new Error(`Expected 'quality' (${r.quality}) to be between 0 and 100, inclusive.`);if(void 0===r.type||!["jpeg","webp"].includes(r.type))throw new Error(`${r.type??"png"} screenshots do not support 'quality'.`)}if(r.clip){if(r.clip.width<=0)throw new Error("'width' in 'clip' must be positive.");if(r.clip.height<=0)throw new Error("'height' in 'clip' must be positive.")}!function(e){e.optimizeForSpeed??=!1,e.type??="png",e.fromSurface??=!0,e.fullPage??=!1,e.omitBackground??=!1,e.encoding??="binary",e.captureBeyondViewport??=!0}(r);const i=wr(t,new rt,!0);if(r.clip){if(r.fullPage)throw new Error("'clip' and 'fullPage' are mutually exclusive");r.clip=Sr(kr(r.clip))}else if(r.fullPage){if(!r.captureBeyondViewport){const e=await this.mainFrame().isolatedRealm().evaluate((()=>{const e=document.documentElement;return{width:e.scrollWidth,height:e.scrollHeight}})),t=this.viewport();await this.setViewport({...t,...e}),i.defer((async()=>{t?await this.setViewport(t).catch(gt):await this.setViewport({width:0,height:0}).catch(gt)}))}}else r.captureBeyondViewport=!1;const n=await this._screenshot(r);if("base64"===r.encoding)return n;const s=Buffer.from(n,"base64");return await this._maybeWriteBufferToFile(r.path,s),s}catch(e){t.error=e,t.hasError=!0}finally{const e=vr(t);e&&await e}}async title(){return await this.mainFrame().title()}click(e,t){return this.mainFrame().click(e,t)}focus(e){return this.mainFrame().focus(e)}hover(e){return this.mainFrame().hover(e)}select(e,...t){return this.mainFrame().select(e,...t)}tap(e){return this.mainFrame().tap(e)}type(e,t,r){return this.mainFrame().type(e,t,r)}async waitForSelector(e,t={}){return await this.mainFrame().waitForSelector(e,t)}waitForFunction(e,t,...r){return this.mainFrame().waitForFunction(e,t,...r)}[(e=[nr((function(){return this.browser()}))],Ze)](){this.close().catch(gt)}[et](){return this.close()}}})();new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]);function kr(e){return{...e,...e.width<0?{x:e.x+e.width,width:-e.width}:{x:e.x,width:e.width},...e.height<0?{y:e.y+e.height,height:-e.height}:{y:e.y,height:e.height}}}function Sr(e){const t=Math.round(e.x),r=Math.round(e.y),i=Math.round(e.width+e.x-t),n=Math.round(e.height+e.y-r);return{...e,x:t,y:r,width:i,height:n}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Cr{#s;#se;#ae;#oe;constructor(e,t,r,i){this.#s=e,this.#se=t,this.#ae=r,this.#oe=i}type(){return this.#s}text(){return this.#se}args(){return this.#ae}location(){return this.#oe[0]??{}}stackTrace(){return this.#oe}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Tr{#ce;#le;#de=!1;constructor(e,t){this.#ce=e,this.#le="selectSingle"!==t.mode}isMultiple(){return this.#le}async accept(e){st(!this.#de,"Cannot accept FileChooser which is already handled!"),this.#de=!0,await this.#ce.uploadFile(...e)}async cancel(){st(!this.#de,"Cannot cancel FileChooser which is already handled!"),this.#de=!0,await this.#ce.evaluate((e=>{e.dispatchEvent(new Event("cancel",{bubbles:!0}))}))}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Er;!function(e){e.Request=Symbol("NetworkManager.Request"),e.RequestServedFromCache=Symbol("NetworkManager.RequestServedFromCache"),e.Response=Symbol("NetworkManager.Response"),e.RequestFailed=Symbol("NetworkManager.RequestFailed"),e.RequestFinished=Symbol("NetworkManager.RequestFinished")}(Er||(Er={}));
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class Ir{#ue;constructor(e){this.#ue=e}updateClient(e){this.#ue=e}async snapshot(e={}){const{interestingOnly:t=!0,root:r=null}=e,{nodes:i}=await this.#ue.send("Accessibility.getFullAXTree");let n;if(r){const{node:e}=await this.#ue.send("DOM.describeNode",{objectId:r.id});n=e.backendNodeId}const s=xr.createTree(i);let a=s;if(n&&(a=s.find((e=>e.payload.backendDOMNodeId===n)),!a))return null;if(!t)return this.serializeTree(a)[0]??null;const o=new Set;return this.collectInterestingNodes(o,s,!1),o.has(a)?this.serializeTree(a,o)[0]??null:null}serializeTree(e,t){const r=[];for(const i of e.children)r.push(...this.serializeTree(i,t));if(t&&!t.has(e))return r;const i=e.serialize();return r.length&&(i.children=r),[i]}collectInterestingNodes(e,t,r){if(t.isInteresting(r)&&e.add(t),!t.isLeafNode()){r=r||t.isControl();for(const i of t.children)this.collectInterestingNodes(e,i,r)}}}class xr{payload;children=[];#he=!1;#pe=!1;#fe=!1;#ye=!1;#me;#ge;#we;#ve;constructor(e){this.payload=e,this.#me=this.payload.name?this.payload.name.value:"",this.#ge=this.payload.role?this.payload.role.value:"Unknown",this.#we=this.payload.ignored;for(const e of this.payload.properties||[])"editable"===e.name&&(this.#he="richtext"===e.value.value,this.#pe=!0),"focusable"===e.name&&(this.#fe=e.value.value),"hidden"===e.name&&(this.#ye=e.value.value)}#be(){return!this.#he&&(!!this.#pe||("textbox"===this.#ge||"searchbox"===this.#ge))}#ke(){const e=this.#ge;return"LineBreak"===e||"text"===e||"InlineTextBox"===e||"StaticText"===e}#Se(){if(void 0===this.#ve){this.#ve=!1;for(const e of this.children)if(e.#fe||e.#Se()){this.#ve=!0;break}}return this.#ve}find(e){if(e(this))return this;for(const t of this.children){const r=t.find(e);if(r)return r}return null}isLeafNode(){if(!this.children.length)return!0;if(this.#be()||this.#ke())return!0;switch(this.#ge){case"doc-cover":case"graphics-symbol":case"img":case"image":case"Meter":case"scrollbar":case"slider":case"separator":case"progressbar":return!0}return!this.#Se()&&(!(!this.#fe||!this.#me)||!("heading"!==this.#ge||!this.#me))}isControl(){switch(this.#ge){case"button":case"checkbox":case"ColorWell":case"combobox":case"DisclosureTriangle":case"listbox":case"menu":case"menubar":case"menuitem":case"menuitemcheckbox":case"menuitemradio":case"radio":case"scrollbar":case"searchbox":case"slider":case"spinbutton":case"switch":case"tab":case"textbox":case"tree":case"treeitem":return!0;default:return!1}}isInteresting(e){return"Ignored"!==this.#ge&&!this.#ye&&!this.#we&&(!(!this.#fe&&!this.#he)||(!!this.isControl()||!e&&(this.isLeafNode()&&!!this.#me)))}serialize(){const e=new Map;for(const t of this.payload.properties||[])e.set(t.name.toLowerCase(),t.value.value);this.payload.name&&e.set("name",this.payload.name.value),this.payload.value&&e.set("value",this.payload.value.value),this.payload.description&&e.set("description",this.payload.description.value);const t={role:this.#ge},r=["name","value","description","keyshortcuts","roledescription","valuetext"];for(const n of r)e.has(n)&&(t[n]=(i=n,e.get(i)));var i;const n=["disabled","expanded","focused","modal","multiline","multiselectable","readonly","required","selected"],s=t=>e.get(t);for(const e of n){if("focused"===e&&"RootWebArea"===this.#ge)continue;s(e)&&(t[e]=s(e))}const a=["checked","pressed"];for(const r of a){if(!e.has(r))continue;const i=e.get(r);t[r]="mixed"===i?"mixed":"true"===i}const o=["level","valuemax","valuemin"],c=t=>e.get(t);for(const r of o)e.has(r)&&(t[r]=c(r));const l=["autocomplete","haspopup","invalid","orientation"],d=t=>e.get(t);for(const e of l){const r=d(e);r&&"false"!==r&&(t[e]=d(e))}return t}static createTree(e){const t=new Map;for(const r of e)t.set(r.nodeId,new xr(r));for(const e of t.values())for(const r of e.payload.childIds||[]){const i=t.get(r);i&&e.children.push(i)}return t.values().next().value}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Fr=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Rr=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},_r=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Pr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});let Dr=(()=>{let e,t,r,i,n=[tr],s=[],a=[];(class{static{t=this}static{const o="function"==typeof Symbol&&Symbol.metadata?Object.create(null):void 0;Rr(this,null,r,{kind:"method",name:"getProperty",static:!1,private:!1,access:{has:e=>"getProperty"in e,get:e=>e.getProperty},metadata:o},null,a),Rr(this,null,i,{kind:"method",name:"getProperties",static:!1,private:!1,access:{has:e=>"getProperties"in e,get:e=>e.getProperties},metadata:o},null,a),Rr(null,e={value:t},n,{kind:"class",name:t.name,metadata:o},null,s),t=e.value,o&&Object.defineProperty(t,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:o}),Fr(t,s)}constructor(){Fr(this,a)}async evaluate(e,...t){return e=bt(this.evaluate.name,e),await this.realm.evaluate(e,this,...t)}async evaluateHandle(e,...t){return e=bt(this.evaluateHandle.name,e),await this.realm.evaluateHandle(e,this,...t)}async getProperty(e){return await this.evaluateHandle(((e,t)=>e[t]),e)}async getProperties(){const e=await this.evaluate((e=>{const t=[],r=Object.getOwnPropertyDescriptors(e);for(const e in r)r[e]?.enumerable&&t.push(e);return t})),t=new Map,r=await Promise.all(e.map((e=>this.getProperty(e))));for(const[i,n]of Object.entries(e)){const e={stack:[],error:void 0,hasError:!1};try{const s=_r(e,r[i],!1);s&&t.set(n,s.move())}catch(t){e.error=t,e.hasError=!0}finally{Pr(e)}}return t}[(r=[rr()],i=[rr()],Ze)](){this.dispose().catch(gt)}[et](){return this.dispose()}});return t})();var Mr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Ar=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class qr{#me;#Ce;constructor(e,t){this.#me=e,this.#Ce=t}get name(){return this.#me}async run(e,t,r,i){const n=new tt;try{if(!i){const i={stack:[],error:void 0,hasError:!1};try{const s=Mr(i,await e.evaluateHandle(((e,t)=>globalThis[e].args.get(t)),this.#me,t),!1),a=await s.getProperties();for(const[e,t]of a)if(e in r)if("node"===t.remoteObject().subtype)r[+e]=t;else n.use(t);else n.use(t)}catch(e){i.error=e,i.hasError=!0}finally{Ar(i)}}await e.evaluate(((e,t,r)=>{const i=globalThis[e].callbacks;i.get(t).resolve(r),i.delete(t)}),this.#me,t,await this.#Ce(...r));for(const e of r)e instanceof Dr&&n.use(e)}catch(r){Vt(r)?await e.evaluate(((e,t,r,i)=>{const n=new Error(r);n.stack=i;const s=globalThis[e].callbacks;s.get(t).reject(n),s.delete(t)}),this.#me,t,r.message,r.stack).catch(gt):await e.evaluate(((e,t,r)=>{const i=globalThis[e].callbacks;i.get(t).reject(r),i.delete(t)}),this.#me,t,r).catch(gt)}}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Or=ct("puppeteer:protocol:SEND ►"),Nr=ct("puppeteer:protocol:RECV ◀");class Lr extends it{#Te;#Ee;#Ie;#xe;#Fe=new Map;#Re=!1;#_e=new Set;#b=new Ut;constructor(e,t,r=0,i){super(),this.#Te=e,this.#Ie=r,this.#xe=i??18e4,this.#Ee=t,this.#Ee.onmessage=this.onMessage.bind(this),this.#Ee.onclose=this.#Pe.bind(this)}static fromSession(e){return e.connection()}get timeout(){return this.#xe}get _closed(){return this.#Re}get _sessions(){return this.#Fe}session(e){return this.#Fe.get(e)||null}url(){return this.#Te}send(e,t,r){return this._rawSend(this.#b,e,t,void 0,r)}_rawSend(e,t,r,i,n){return e.create(t,n?.timeout??this.#xe,(e=>{const n=JSON.stringify({method:t,params:r,id:e,sessionId:i});Or(n),this.#Ee.send(n)}))}async closeBrowser(){await this.send("Browser.close")}async onMessage(e){this.#Ie&&await new Promise((e=>setTimeout(e,this.#Ie))),Nr(e);const t=JSON.parse(e);if("Target.attachedToTarget"===t.method){const e=t.params.sessionId,r=new Qt(this,t.params.targetInfo.type,e,t.sessionId);this.#Fe.set(e,r),this.emit(Bt.SessionAttached,r);const i=this.#Fe.get(t.sessionId);i&&i.emit(Bt.SessionAttached,r)}else if("Target.detachedFromTarget"===t.method){const e=this.#Fe.get(t.params.sessionId);if(e){e._onClosed(),this.#Fe.delete(t.params.sessionId),this.emit(Bt.SessionDetached,e);const r=this.#Fe.get(t.sessionId);r&&r.emit(Bt.SessionDetached,e)}}if(t.sessionId){const e=this.#Fe.get(t.sessionId);e&&e._onMessage(t)}else t.id?t.error?this.#b.reject(t.id,zt(t),t.error.message):this.#b.resolve(t.id,t.result):this.emit(t.method,t.params)}#Pe(){if(!this.#Re){this.#Re=!0,this.#Ee.onmessage=void 0,this.#Ee.onclose=void 0,this.#b.clear();for(const e of this.#Fe.values())e._onClosed();this.#Fe.clear(),this.emit(Bt.Disconnected,void 0)}}dispose(){this.#Pe(),this.#Ee.close()}isAutoAttached(e){return!this.#_e.has(e)}async _createSession(e,t=!0){t||this.#_e.add(e.targetId);const{sessionId:r}=await this.send("Target.attachToTarget",{targetId:e.targetId,flatten:!0});this.#_e.delete(e.targetId);const i=this.#Fe.get(r);if(!i)throw new Error("CDPSession creation failed.");return i}async createSession(e){return await this._createSession(e,!1)}getPendingProtocolErrors(){const e=[];e.push(...this.#b.getPendingProtocolErrors());for(const t of this.#Fe.values())e.push(...t.getPendingProtocolErrors());return e}}function Br(e){return e instanceof yt}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class jr{#De;#Me;constructor(e){this.#De=new Hr(e),this.#Me=new $r(e)}updateClient(e){this.#De.updateClient(e),this.#Me.updateClient(e)}async startJSCoverage(e={}){return await this.#De.start(e)}async stopJSCoverage(){return await this.#De.stop()}async startCSSCoverage(e={}){return await this.#Me.start(e)}async stopCSSCoverage(){return await this.#Me.stop()}}class Hr{#ue;#Ae=!1;#qe=new Map;#Oe=new Map;#Ne;#Le=!1;#Be=!1;#je=!1;constructor(e){this.#ue=e}updateClient(e){this.#ue=e}async start(e={}){st(!this.#Ae,"JSCoverage is already enabled");const{resetOnNavigation:t=!0,reportAnonymousScripts:r=!1,includeRawScriptCoverage:i=!1,useBlockCoverage:n=!0}=e;this.#Le=t,this.#Be=r,this.#je=i,this.#Ae=!0,this.#qe.clear(),this.#Oe.clear(),this.#Ne=new tt,this.#Ne.use(new nt(this.#ue,"Debugger.scriptParsed",this.#He.bind(this))),this.#Ne.use(new nt(this.#ue,"Runtime.executionContextsCleared",this.#$e.bind(this))),await Promise.all([this.#ue.send("Profiler.enable"),this.#ue.send("Profiler.startPreciseCoverage",{callCount:this.#je,detailed:n}),this.#ue.send("Debugger.enable"),this.#ue.send("Debugger.setSkipAllPauses",{skip:!0})])}#$e(){this.#Le&&(this.#qe.clear(),this.#Oe.clear())}async#He(e){if(!vt.isPuppeteerURL(e.url)&&(e.url||this.#Be))try{const t=await this.#ue.send("Debugger.getScriptSource",{scriptId:e.scriptId});this.#qe.set(e.scriptId,e.url),this.#Oe.set(e.scriptId,t.scriptSource)}catch(e){gt(e)}}async stop(){st(this.#Ae,"JSCoverage is not enabled"),this.#Ae=!1;const e=await Promise.all([this.#ue.send("Profiler.takePreciseCoverage"),this.#ue.send("Profiler.stopPreciseCoverage"),this.#ue.send("Profiler.disable"),this.#ue.send("Debugger.disable")]);this.#Ne?.dispose();const t=[],r=e[0];for(const e of r.result){let r=this.#qe.get(e.scriptId);!r&&this.#Be&&(r="debugger://VM"+e.scriptId);const i=this.#Oe.get(e.scriptId);if(void 0===i||void 0===r)continue;const n=[];for(const t of e.functions)n.push(...t.ranges);const s=Kr(n);this.#je?t.push({url:r,ranges:s,text:i,rawScriptCoverage:e}):t.push({url:r,ranges:s,text:i})}return t}}class $r{#ue;#Ae=!1;#Ke=new Map;#Ve=new Map;#We;#Le=!1;constructor(e){this.#ue=e}updateClient(e){this.#ue=e}async start(e={}){st(!this.#Ae,"CSSCoverage is already enabled");const{resetOnNavigation:t=!0}=e;this.#Le=t,this.#Ae=!0,this.#Ke.clear(),this.#Ve.clear(),this.#We=new tt,this.#We.use(new nt(this.#ue,"CSS.styleSheetAdded",this.#ze.bind(this))),this.#We.use(new nt(this.#ue,"Runtime.executionContextsCleared",this.#$e.bind(this))),await Promise.all([this.#ue.send("DOM.enable"),this.#ue.send("CSS.enable"),this.#ue.send("CSS.startRuleUsageTracking")])}#$e(){this.#Le&&(this.#Ke.clear(),this.#Ve.clear())}async#ze(e){const t=e.header;if(t.sourceURL)try{const e=await this.#ue.send("CSS.getStyleSheetText",{styleSheetId:t.styleSheetId});this.#Ke.set(t.styleSheetId,t.sourceURL),this.#Ve.set(t.styleSheetId,e.text)}catch(e){gt(e)}}async stop(){st(this.#Ae,"CSSCoverage is not enabled"),this.#Ae=!1;const e=await this.#ue.send("CSS.stopRuleUsageTracking");await Promise.all([this.#ue.send("CSS.disable"),this.#ue.send("DOM.disable")]),this.#We?.dispose();const t=new Map;for(const r of e.ruleUsage){let e=t.get(r.styleSheetId);e||(e=[],t.set(r.styleSheetId,e)),e.push({startOffset:r.startOffset,endOffset:r.endOffset,count:r.used?1:0})}const r=[];for(const e of this.#Ke.keys()){const i=this.#Ke.get(e);st(void 0!==i,`Stylesheet URL is undefined (styleSheetId=${e})`);const n=this.#Ve.get(e);st(void 0!==n,`Stylesheet text is undefined (styleSheetId=${e})`);const s=Kr(t.get(e)||[]);r.push({url:i,ranges:s,text:n})}return r}}function Kr(e){const t=[];for(const r of e)t.push({offset:r.startOffset,type:0,range:r}),t.push({offset:r.endOffset,type:1,range:r});t.sort(((e,t)=>{if(e.offset!==t.offset)return e.offset-t.offset;if(e.type!==t.type)return t.type-e.type;const r=e.range.endOffset-e.range.startOffset,i=t.range.endOffset-t.range.startOffset;return 0===e.type?i-r:r-i}));const r=[],i=[];let n=0;for(const e of t){if(r.length&&n<e.offset&&r[r.length-1]>0){const t=i[i.length-1];t&&t.end===n?t.end=e.offset:i.push({start:n,end:e.offset})}n=e.offset,0===e.type?r.push(e.range.count):r.pop()}return i.filter((e=>e.end-e.start>0))}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Vr{#s;#Ue;#Ge;#de=!1;constructor(e,t,r=""){this.#s=e,this.#Ue=t,this.#Ge=r}type(){return this.#s}message(){return this.#Ue}defaultValue(){return this.#Ge}async accept(e){st(!this.#de,"Cannot accept dialog which is already handled!"),this.#de=!0,await this.handle({accept:!0,text:e})}async dismiss(){st(!this.#de,"Cannot dismiss dialog which is already handled!"),this.#de=!0,await this.handle({accept:!1})}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Wr extends Vr{#ue;constructor(e,t,r,i=""){super(t,r,i),this.#ue=e}async handle(e){await this.#ue.send("Page.handleJavaScriptDialog",{accept:e.accept,promptText:e.text})}}var zr=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Ur=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Gr=self&&self.__setFunctionName||function(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})};class Qr{#Qe;#Je;#Xe;constructor(e,t,r){this.#Qe=e,this.#Je=t,this.#Xe=r,this.#Je.registerState(this)}async setState(e){this.#Qe=e,await this.sync()}get state(){return this.#Qe}async sync(){await Promise.all(this.#Je.clients().map((e=>this.#Xe(e,this.#Qe))))}}let Jr=(()=>{let e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,y,m,g,w,v,b=[];return class{static{const k="function"==typeof Symbol&&Symbol.metadata?Object.create(null):void 0;e=[ir],r=[ir],n=[ir],a=[ir],c=[ir],d=[ir],h=[ir],f=[ir],m=[ir],w=[ir],Ur(this,t={value:Gr((async function(e,t){if(!t.viewport)return;const{viewport:r}=t,i=r.isMobile||!1,n=r.width,s=r.height,a=r.deviceScaleFactor??1,o=r.isLandscape?{angle:90,type:"landscapePrimary"}:{angle:0,type:"portraitPrimary"},c=r.hasTouch||!1;await Promise.all([e.send("Emulation.setDeviceMetricsOverride",{mobile:i,width:n,height:s,deviceScaleFactor:a,screenOrientation:o}).catch((e=>{if(!e.message.includes("Target does not support metrics override"))throw e;gt(e)})),e.send("Emulation.setTouchEmulationEnabled",{enabled:c})])}),"#applyViewport")},e,{kind:"method",name:"#applyViewport",static:!1,private:!0,access:{has:e=>#Ye in e,get:e=>e.#Ye},metadata:k},null,b),Ur(this,i={value:Gr((async function(e,t){t.active&&(t.overrides?await e.send("Emulation.setIdleOverride",{isUserActive:t.overrides.isUserActive,isScreenUnlocked:t.overrides.isScreenUnlocked}):await e.send("Emulation.clearIdleOverride"))}),"#emulateIdleState")},r,{kind:"method",name:"#emulateIdleState",static:!1,private:!0,access:{has:e=>#Ze in e,get:e=>e.#Ze},metadata:k},null,b),Ur(this,s={value:Gr((async function(e,t){if(t.active)try{await e.send("Emulation.setTimezoneOverride",{timezoneId:t.timezoneId||""})}catch(e){if(Vt(e)&&e.message.includes("Invalid timezone"))throw new Error(`Invalid timezone ID: ${t.timezoneId}`);throw e}}),"#emulateTimezone")},n,{kind:"method",name:"#emulateTimezone",static:!1,private:!0,access:{has:e=>#et in e,get:e=>e.#et},metadata:k},null,b),Ur(this,o={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedVisionDeficiency",{type:t.visionDeficiency||"none"})}),"#emulateVisionDeficiency")},a,{kind:"method",name:"#emulateVisionDeficiency",static:!1,private:!0,access:{has:e=>#tt in e,get:e=>e.#tt},metadata:k},null,b),Ur(this,l={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setCPUThrottlingRate",{rate:t.factor??1})}),"#emulateCpuThrottling")},c,{kind:"method",name:"#emulateCpuThrottling",static:!1,private:!0,access:{has:e=>#rt in e,get:e=>e.#rt},metadata:k},null,b),Ur(this,u={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedMedia",{features:t.mediaFeatures})}),"#emulateMediaFeatures")},d,{kind:"method",name:"#emulateMediaFeatures",static:!1,private:!0,access:{has:e=>#it in e,get:e=>e.#it},metadata:k},null,b),Ur(this,p={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedMedia",{media:t.type||""})}),"#emulateMediaType")},h,{kind:"method",name:"#emulateMediaType",static:!1,private:!0,access:{has:e=>#nt in e,get:e=>e.#nt},metadata:k},null,b),Ur(this,y={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setGeolocationOverride",t.geoLocation?{longitude:t.geoLocation.longitude,latitude:t.geoLocation.latitude,accuracy:t.geoLocation.accuracy}:void 0)}),"#setGeolocation")},f,{kind:"method",name:"#setGeolocation",static:!1,private:!0,access:{has:e=>#st in e,get:e=>e.#st},metadata:k},null,b),Ur(this,g={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setDefaultBackgroundColorOverride",{color:t.color})}),"#setDefaultBackgroundColor")},m,{kind:"method",name:"#setDefaultBackgroundColor",static:!1,private:!0,access:{has:e=>#at in e,get:e=>e.#at},metadata:k},null,b),Ur(this,v={value:Gr((async function(e,t){t.active&&await e.send("Emulation.setScriptExecutionDisabled",{value:!t.javaScriptEnabled})}),"#setJavaScriptEnabled")},w,{kind:"method",name:"#setJavaScriptEnabled",static:!1,private:!0,access:{has:e=>#ot in e,get:e=>e.#ot},metadata:k},null,b),k&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:k})}#ue=void zr(this,b);#ct=!1;#lt=!1;#dt=[];#ut=new Qr({active:!1},this,this.#Ye);#ht=new Qr({active:!1},this,this.#Ze);#pt=new Qr({active:!1},this,this.#et);#ft=new Qr({active:!1},this,this.#tt);#yt=new Qr({active:!1},this,this.#rt);#mt=new Qr({active:!1},this,this.#it);#gt=new Qr({active:!1},this,this.#nt);#wt=new Qr({active:!1},this,this.#st);#vt=new Qr({active:!1},this,this.#at);#bt=new Qr({javaScriptEnabled:!0,active:!1},this,this.#ot);#kt=new Set;constructor(e){this.#ue=e}updateClient(e){this.#ue=e,this.#kt.delete(e)}registerState(e){this.#dt.push(e)}clients(){return[this.#ue,...Array.from(this.#kt)]}async registerSpeculativeSession(e){this.#kt.add(e),e.once(Bt.Disconnected,(()=>{this.#kt.delete(e)})),Promise.all(this.#dt.map((e=>e.sync().catch(gt))))}get javascriptEnabled(){return this.#bt.state.javaScriptEnabled}async emulateViewport(e){await this.#ut.setState({viewport:e,active:!0});const t=e.isMobile||!1,r=e.hasTouch||!1,i=this.#ct!==t||this.#lt!==r;return this.#ct=t,this.#lt=r,i}get#Ye(){return t.value}async emulateIdleState(e){await this.#ht.setState({active:!0,overrides:e})}get#Ze(){return i.value}get#et(){return s.value}async emulateTimezone(e){await this.#pt.setState({timezoneId:e,active:!0})}get#tt(){return o.value}async emulateVisionDeficiency(e){const t=new Set(["none","achromatopsia","blurredVision","deuteranopia","protanopia","tritanopia"]);st(!e||t.has(e),`Unsupported vision deficiency: ${e}`),await this.#ft.setState({active:!0,visionDeficiency:e})}get#rt(){return l.value}async emulateCPUThrottling(e){st(null===e||e>=1,"Throttling rate should be greater or equal to 1"),await this.#yt.setState({active:!0,factor:e??void 0})}get#it(){return u.value}async emulateMediaFeatures(e){if(Array.isArray(e))for(const t of e){const e=t.name;st(/^(?:prefers-(?:color-scheme|reduced-motion)|color-gamut)$/.test(e),"Unsupported media feature: "+e)}await this.#mt.setState({active:!0,mediaFeatures:e})}get#nt(){return p.value}async emulateMediaType(e){st("screen"===e||"print"===e||void 0===(e??void 0),"Unsupported media type: "+e),await this.#gt.setState({type:e,active:!0})}get#st(){return y.value}async setGeolocation(e){const{longitude:t,latitude:r,accuracy:i=0}=e;if(t<-180||t>180)throw new Error(`Invalid longitude "${t}": precondition -180 <= LONGITUDE <= 180 failed.`);if(r<-90||r>90)throw new Error(`Invalid latitude "${r}": precondition -90 <= LATITUDE <= 90 failed.`);if(i<0)throw new Error(`Invalid accuracy "${i}": precondition 0 <= ACCURACY failed.`);await this.#wt.setState({active:!0,geoLocation:{longitude:t,latitude:r,accuracy:i}})}get#at(){return g.value}async resetDefaultBackgroundColor(){await this.#vt.setState({active:!0,color:void 0})}async setTransparentBackgroundColor(){await this.#vt.setState({active:!0,color:{r:0,g:0,b:0,a:0}})}get#ot(){return v.value}async setJavaScriptEnabled(e){await this.#bt.setState({active:!0,javaScriptEnabled:e})}}})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Xr{static create=e=>new Xr(e);#St;constructor(e){this.#St=e}async get(e){return await this.#St(e)}}const Yr=new
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class{#Ct=!1;#Tt=new Set;append(e){this.#Et((()=>{this.#Tt.add(e)}))}pop(e){this.#Et((()=>{this.#Tt.delete(e)}))}inject(e,t=!1){(this.#Ct||t)&&e(this.#St()),this.#Ct=!1}#Et(e){e(),this.#Ct=!0}#St(){return`(() => {\n      const module = {};\n      "use strict";var v=Object.defineProperty;var re=Object.getOwnPropertyDescriptor;var ne=Object.getOwnPropertyNames;var oe=Object.prototype.hasOwnProperty;var u=(t,e)=>{for(var n in e)v(t,n,{get:e[n],enumerable:!0})},se=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of ne(e))!oe.call(t,o)&&o!==n&&v(t,o,{get:()=>e[o],enumerable:!(r=re(e,o))||r.enumerable});return t};var ie=t=>se(v({},"__esModule",{value:!0}),t);var Re={};u(Re,{default:()=>ke});module.exports=ie(Re);var C=class extends Error{constructor(e){super(e),this.name=this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},b=class extends C{};var f=class t{static create(e){return new t(e)}static async race(e){let n=new Set;try{let r=e.map(o=>o instanceof t?(o.#s&&n.add(o),o.valueOrThrow()):o);return await Promise.race(r)}finally{for(let r of n)r.reject(new Error("Timeout cleared"))}}#e=!1;#r=!1;#n;#t;#o=new Promise(e=>{this.#t=e});#s;#l;constructor(e){e&&e.timeout>0&&(this.#l=new b(e.message),this.#s=setTimeout(()=>{this.reject(this.#l)},e.timeout))}#a(e){clearTimeout(this.#s),this.#n=e,this.#t()}resolve(e){this.#r||this.#e||(this.#e=!0,this.#a(e))}reject(e){this.#r||this.#e||(this.#r=!0,this.#a(e))}resolved(){return this.#e}finished(){return this.#e||this.#r}value(){return this.#n}#i;valueOrThrow(){return this.#i||(this.#i=(async()=>{if(await this.#o,this.#r)throw this.#n;return this.#n})()),this.#i}};var X=new Map,z=t=>{let e=X.get(t);return e||(e=new Function(\`return \${t}\`)(),X.set(t,e),e)};var k={};u(k,{ariaQuerySelector:()=>le,ariaQuerySelectorAll:()=>I});var le=(t,e)=>window.__ariaQuerySelector(t,e),I=async function*(t,e){yield*await window.__ariaQuerySelectorAll(t,e)};var M={};u(M,{customQuerySelectors:()=>O});var R=class{#e=new Map;register(e,n){if(!n.queryOne&&n.queryAll){let r=n.queryAll;n.queryOne=(o,i)=>{for(let s of r(o,i))return s;return null}}else if(n.queryOne&&!n.queryAll){let r=n.queryOne;n.queryAll=(o,i)=>{let s=r(o,i);return s?[s]:[]}}else if(!n.queryOne||!n.queryAll)throw new Error("At least one query method must be defined.");this.#e.set(e,{querySelector:n.queryOne,querySelectorAll:n.queryAll})}unregister(e){this.#e.delete(e)}get(e){return this.#e.get(e)}clear(){this.#e.clear()}},O=new R;var q={};u(q,{pierceQuerySelector:()=>ae,pierceQuerySelectorAll:()=>ce});var ae=(t,e)=>{let n=null,r=o=>{let i=document.createTreeWalker(o,NodeFilter.SHOW_ELEMENT);do{let s=i.currentNode;s.shadowRoot&&r(s.shadowRoot),!(s instanceof ShadowRoot)&&s!==o&&!n&&s.matches(e)&&(n=s)}while(!n&&i.nextNode())};return t instanceof Document&&(t=t.documentElement),r(t),n},ce=(t,e)=>{let n=[],r=o=>{let i=document.createTreeWalker(o,NodeFilter.SHOW_ELEMENT);do{let s=i.currentNode;s.shadowRoot&&r(s.shadowRoot),!(s instanceof ShadowRoot)&&s!==o&&s.matches(e)&&n.push(s)}while(i.nextNode())};return t instanceof Document&&(t=t.documentElement),r(t),n};var m=(t,e)=>{if(!t)throw new Error(e)};var T=class{#e;#r;#n;#t;constructor(e,n){this.#e=e,this.#r=n}async start(){let e=this.#t=f.create(),n=await this.#e();if(n){e.resolve(n);return}this.#n=new MutationObserver(async()=>{let r=await this.#e();r&&(e.resolve(r),await this.stop())}),this.#n.observe(this.#r,{childList:!0,subtree:!0,attributes:!0})}async stop(){m(this.#t,"Polling never started."),this.#t.finished()||this.#t.reject(new Error("Polling stopped")),this.#n&&(this.#n.disconnect(),this.#n=void 0)}result(){return m(this.#t,"Polling never started."),this.#t.valueOrThrow()}},E=class{#e;#r;constructor(e){this.#e=e}async start(){let e=this.#r=f.create(),n=await this.#e();if(n){e.resolve(n);return}let r=async()=>{if(e.finished())return;let o=await this.#e();if(!o){window.requestAnimationFrame(r);return}e.resolve(o),await this.stop()};window.requestAnimationFrame(r)}async stop(){m(this.#r,"Polling never started."),this.#r.finished()||this.#r.reject(new Error("Polling stopped"))}result(){return m(this.#r,"Polling never started."),this.#r.valueOrThrow()}},P=class{#e;#r;#n;#t;constructor(e,n){this.#e=e,this.#r=n}async start(){let e=this.#t=f.create(),n=await this.#e();if(n){e.resolve(n);return}this.#n=setInterval(async()=>{let r=await this.#e();r&&(e.resolve(r),await this.stop())},this.#r)}async stop(){m(this.#t,"Polling never started."),this.#t.finished()||this.#t.reject(new Error("Polling stopped")),this.#n&&(clearInterval(this.#n),this.#n=void 0)}result(){return m(this.#t,"Polling never started."),this.#t.valueOrThrow()}};var V={};u(V,{pQuerySelector:()=>Ce,pQuerySelectorAll:()=>te});var c=class{static async*map(e,n){for await(let r of e)yield await n(r)}static async*flatMap(e,n){for await(let r of e)yield*n(r)}static async collect(e){let n=[];for await(let r of e)n.push(r);return n}static async first(e){for await(let n of e)return n}};var p={attribute:/\\[\\s*(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)\\s*(?:(?<operator>\\W?=)\\s*(?<value>.+?)\\s*(\\s(?<caseSensitive>[iIsS]))?\\s*)?\\]/gu,id:/#(?<name>[-\\w\\P{ASCII}]+)/gu,class:/\\.(?<name>[-\\w\\P{ASCII}]+)/gu,comma:/\\s*,\\s*/g,combinator:/\\s*[\\s>+~]\\s*/g,"pseudo-element":/::(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,"pseudo-class":/:(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,universal:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?\\*/gu,type:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)/gu},ue=new Set(["combinator","comma"]);var fe=t=>{switch(t){case"pseudo-element":case"pseudo-class":return new RegExp(p[t].source.replace("(?<argument>\\xB6*)","(?<argument>.*)"),"gu");default:return p[t]}};function de(t,e){let n=0,r="";for(;e<t.length;e++){let o=t[e];switch(o){case"(":++n;break;case")":--n;break}if(r+=o,n===0)return r}return r}function me(t,e=p){if(!t)return[];let n=[t];for(let[o,i]of Object.entries(e))for(let s=0;s<n.length;s++){let l=n[s];if(typeof l!="string")continue;i.lastIndex=0;let a=i.exec(l);if(!a)continue;let h=a.index-1,d=[],W=a[0],H=l.slice(0,h+1);H&&d.push(H),d.push({...a.groups,type:o,content:W});let B=l.slice(h+W.length+1);B&&d.push(B),n.splice(s,1,...d)}let r=0;for(let o of n)switch(typeof o){case"string":throw new Error(\`Unexpected sequence \${o} found at index \${r}\`);case"object":r+=o.content.length,o.pos=[r-o.content.length,r],ue.has(o.type)&&(o.content=o.content.trim()||" ");break}return n}var he=/(['"])([^\\\\\\n]+?)\\1/g,pe=/\\\\./g;function G(t,e=p){if(t=t.trim(),t==="")return[];let n=[];t=t.replace(pe,(i,s)=>(n.push({value:i,offset:s}),"\\uE000".repeat(i.length))),t=t.replace(he,(i,s,l,a)=>(n.push({value:i,offset:a}),\`\${s}\${"\\uE001".repeat(l.length)}\${s}\`));{let i=0,s;for(;(s=t.indexOf("(",i))>-1;){let l=de(t,s);n.push({value:l,offset:s}),t=\`\${t.substring(0,s)}(\${"\\xB6".repeat(l.length-2)})\${t.substring(s+l.length)}\`,i=s+l.length}}let r=me(t,e),o=new Set;for(let i of n.reverse())for(let s of r){let{offset:l,value:a}=i;if(!(s.pos[0]<=l&&l+a.length<=s.pos[1]))continue;let{content:h}=s,d=l-s.pos[0];s.content=h.slice(0,d)+a+h.slice(d+a.length),s.content!==h&&o.add(s)}for(let i of o){let s=fe(i.type);if(!s)throw new Error(\`Unknown token type: \${i.type}\`);s.lastIndex=0;let l=s.exec(i.content);if(!l)throw new Error(\`Unable to parse content for \${i.type}: \${i.content}\`);Object.assign(i,l.groups)}return r}function*x(t,e){switch(t.type){case"list":for(let n of t.list)yield*x(n,t);break;case"complex":yield*x(t.left,t),yield*x(t.right,t);break;case"compound":yield*t.list.map(n=>[n,t]);break;default:yield[t,e]}}function y(t){let e;return Array.isArray(t)?e=t:e=[...x(t)].map(([n])=>n),e.map(n=>n.content).join("")}p.combinator=/\\s*(>>>>?|[\\s>+~])\\s*/g;var ye=/\\\\[\\s\\S]/g,ge=t=>t.length<=1?t:((t[0]==='"'||t[0]==="'")&&t.endsWith(t[0])&&(t=t.slice(1,-1)),t.replace(ye,e=>e[1]));function K(t){let e=!0,n=G(t);if(n.length===0)return[[],e];let r=[],o=[r],i=[o],s=[];for(let l of n){switch(l.type){case"combinator":switch(l.content){case">>>":e=!1,s.length&&(r.push(y(s)),s.splice(0)),r=[],o.push(">>>"),o.push(r);continue;case">>>>":e=!1,s.length&&(r.push(y(s)),s.splice(0)),r=[],o.push(">>>>"),o.push(r);continue}break;case"pseudo-element":if(!l.name.startsWith("-p-"))break;e=!1,s.length&&(r.push(y(s)),s.splice(0)),r.push({name:l.name.slice(3),value:ge(l.argument??"")});continue;case"comma":s.length&&(r.push(y(s)),s.splice(0)),r=[],o=[r],i.push(o);continue}s.push(l)}return s.length&&r.push(y(s)),[i,e]}var _={};u(_,{textQuerySelectorAll:()=>S});var we=new Set(["checkbox","image","radio"]),Se=t=>t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLInputElement&&!we.has(t.type),be=new Set(["SCRIPT","STYLE"]),w=t=>!be.has(t.nodeName)&&!document.head?.contains(t),D=new WeakMap,J=t=>{for(;t;)D.delete(t),t instanceof ShadowRoot?t=t.host:t=t.parentNode},Y=new WeakSet,Te=new MutationObserver(t=>{for(let e of t)J(e.target)}),g=t=>{let e=D.get(t);if(e||(e={full:"",immediate:[]},!w(t)))return e;let n="";if(Se(t))e.full=t.value,e.immediate.push(t.value),t.addEventListener("input",r=>{J(r.target)},{once:!0,capture:!0});else{for(let r=t.firstChild;r;r=r.nextSibling){if(r.nodeType===Node.TEXT_NODE){e.full+=r.nodeValue??"",n+=r.nodeValue??"";continue}n&&e.immediate.push(n),n="",r.nodeType===Node.ELEMENT_NODE&&(e.full+=g(r).full)}n&&e.immediate.push(n),t instanceof Element&&t.shadowRoot&&(e.full+=g(t.shadowRoot).full),Y.has(t)||(Te.observe(t,{childList:!0,characterData:!0,subtree:!0}),Y.add(t))}return D.set(t,e),e};var S=function*(t,e){let n=!1;for(let r of t.childNodes)if(r instanceof Element&&w(r)){let o;r.shadowRoot?o=S(r.shadowRoot,e):o=S(r,e);for(let i of o)yield i,n=!0}n||t instanceof Element&&w(t)&&g(t).full.includes(e)&&(yield t)};var L={};u(L,{checkVisibility:()=>Pe,pierce:()=>N,pierceAll:()=>Q});var Ee=["hidden","collapse"],Pe=(t,e)=>{if(!t)return e===!1;if(e===void 0)return t;let n=t.nodeType===Node.TEXT_NODE?t.parentElement:t,r=window.getComputedStyle(n),o=r&&!Ee.includes(r.visibility)&&!xe(n);return e===o?t:!1};function xe(t){let e=t.getBoundingClientRect();return e.width===0||e.height===0}var Ne=t=>"shadowRoot"in t&&t.shadowRoot instanceof ShadowRoot;function*N(t){Ne(t)?yield t.shadowRoot:yield t}function*Q(t){t=N(t).next().value,yield t;let e=[document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT)];for(let n of e){let r;for(;r=n.nextNode();)r.shadowRoot&&(yield r.shadowRoot,e.push(document.createTreeWalker(r.shadowRoot,NodeFilter.SHOW_ELEMENT)))}}var j={};u(j,{xpathQuerySelectorAll:()=>$});var $=function*(t,e,n=-1){let o=(t.ownerDocument||document).evaluate(e,t,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE),i=[],s;for(;(s=o.iterateNext())&&(i.push(s),!(n&&i.length===n)););for(let l=0;l<i.length;l++)s=i[l],yield s,delete i[l]};var Ae=/[-\\w\\P{ASCII}*]/,Z=t=>"querySelectorAll"in t,A=class extends Error{constructor(e,n){super(\`\${e} is not a valid selector: \${n}\`)}},U=class{#e;#r;#n=[];#t=void 0;elements;constructor(e,n,r){this.elements=[e],this.#e=n,this.#r=r,this.#o()}async run(){if(typeof this.#t=="string")switch(this.#t.trimStart()){case":scope":this.#o();break}for(;this.#t!==void 0;this.#o()){let e=this.#t,n=this.#e;typeof e=="string"?e[0]&&Ae.test(e[0])?this.elements=c.flatMap(this.elements,async function*(r){Z(r)&&(yield*r.querySelectorAll(e))}):this.elements=c.flatMap(this.elements,async function*(r){if(!r.parentElement){if(!Z(r))return;yield*r.querySelectorAll(e);return}let o=0;for(let i of r.parentElement.children)if(++o,i===r)break;yield*r.parentElement.querySelectorAll(\`:scope>:nth-child(\${o})\${e}\`)}):this.elements=c.flatMap(this.elements,async function*(r){switch(e.name){case"text":yield*S(r,e.value);break;case"xpath":yield*$(r,e.value);break;case"aria":yield*I(r,e.value);break;default:let o=O.get(e.name);if(!o)throw new A(n,\`Unknown selector type: \${e.name}\`);yield*o.querySelectorAll(r,e.value)}})}}#o(){if(this.#n.length!==0){this.#t=this.#n.shift();return}if(this.#r.length===0){this.#t=void 0;return}let e=this.#r.shift();switch(e){case">>>>":{this.elements=c.flatMap(this.elements,N),this.#o();break}case">>>":{this.elements=c.flatMap(this.elements,Q),this.#o();break}default:this.#n=e,this.#o();break}}},F=class{#e=new WeakMap;calculate(e,n=[]){if(e===null)return n;e instanceof ShadowRoot&&(e=e.host);let r=this.#e.get(e);if(r)return[...r,...n];let o=0;for(let s=e.previousSibling;s;s=s.previousSibling)++o;let i=this.calculate(e.parentNode,[o]);return this.#e.set(e,i),[...i,...n]}},ee=(t,e)=>{if(t.length+e.length===0)return 0;let[n=-1,...r]=t,[o=-1,...i]=e;return n===o?ee(r,i):n<o?-1:1},ve=async function*(t){let e=new Set;for await(let r of t)e.add(r);let n=new F;yield*[...e.values()].map(r=>[r,n.calculate(r)]).sort(([,r],[,o])=>ee(r,o)).map(([r])=>r)},te=function(t,e){let n,r;try{[n,r]=K(e)}catch{return t.querySelectorAll(e)}if(r)return t.querySelectorAll(e);if(n.some(o=>{let i=0;return o.some(s=>(typeof s=="string"?++i:i=0,i>1))}))throw new A(e,"Multiple deep combinators found in sequence.");return ve(c.flatMap(n,o=>{let i=new U(t,e,o);return i.run(),i.elements}))},Ce=async function(t,e){for await(let n of te(t,e))return n;return null};var Ie=Object.freeze({...k,...M,...q,...V,..._,...L,...j,Deferred:f,createFunction:z,createTextContent:g,IntervalPoller:P,isSuitableNodeForTextMatching:w,MutationPoller:T,RAFPoller:E}),ke=Ie;\n/**\n * @license\n * Copyright 2018 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n/**\n * @license\n * Copyright 2020 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\n\n      ${[...this.#Tt].map((e=>`(${e})(module.exports.default);`)).join("")}\n      return module.exports.default;\n    })()`}};class Zr{static async*map(e,t){for await(const r of e)yield await t(r)}static async*flatMap(e,t){for await(const r of e)yield*t(r)}static async collect(e){const t=[];for await(const r of e)t.push(r);return t}static async first(e){for await(const t of e)return t}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const ei=new Map;function ti(e){let t=e.toString();try{new Function(`(${t})`)}catch{let e="function ";t.startsWith("async ")&&(e=`async ${e}`,t=t.substring(6)),t=`${e}${t}`;try{new Function(`(${t})`)}catch{throw new Error("Passed function cannot be serialized!")}}return t}const ri=(e,t)=>{let r=ti(e);for(const[e,i]of Object.entries(t))r=r.replace(new RegExp(`PLACEHOLDER\\(\\s*(?:'${e}'|"${e}")\\s*\\)`,"g"),`(${i})`);return(e=>{let t=ei.get(e);return t||(t=new Function(`return ${e}`)(),ei.set(e,t),t)})(r)},ii=Symbol("_isElementHandle");
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var ni=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},si=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});const ai=20;async function*oi(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=ni(r,await e.evaluateHandle((async(e,t)=>{const r=[];for(;r.length<t;){const t=await e.next();if(t.done)break;r.push(t.value)}return r}),t),!1),n=await i.getProperties(),s=n.values();return ni(r,new tt,!1).defer((()=>{for(const e of s){const t={stack:[],error:void 0,hasError:!1};try{ni(t,e,!1)[Ze]()}catch(e){t.error=e,t.hasError=!0}finally{si(t)}}})),yield*s,0===n.size}catch(e){r.error=e,r.hasError=!0}finally{si(r)}}async function*ci(e){const t={stack:[],error:void 0,hasError:!1};try{const r=ni(t,await e.evaluateHandle((e=>async function*(){yield*e}())),!1);yield*async function*(e){let t=ai;for(;!(yield*oi(e,t));)t<<=1}(r)}catch(e){t.error=e,t.hasError=!0}finally{si(t)}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var li=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},di=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class ui{static querySelectorAll;static querySelector;static get _querySelector(){if(this.querySelector)return this.querySelector;if(!this.querySelectorAll)throw new Error("Cannot create default `querySelector`.");return this.querySelector=ri((async(e,t,r)=>{const i=PLACEHOLDER("querySelectorAll")(e,t,r);for await(const e of i)return e;return null}),{querySelectorAll:ti(this.querySelectorAll)})}static get _querySelectorAll(){if(this.querySelectorAll)return this.querySelectorAll;if(!this.querySelector)throw new Error("Cannot create default `querySelectorAll`.");return this.querySelectorAll=ri((async function*(e,t,r){const i=PLACEHOLDER("querySelector"),n=await i(e,t,r);n&&(yield n)}),{querySelector:ti(this.querySelector)})}static async*queryAll(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=li(r,await e.evaluateHandle(this._querySelectorAll,t,Xr.create((e=>e.puppeteerUtil))),!1);yield*ci(i)}catch(e){r.error=e,r.hasError=!0}finally{di(r)}}static async queryOne(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=li(r,await e.evaluateHandle(this._querySelector,t,Xr.create((e=>e.puppeteerUtil))),!1);return ii in i?i.move():null}catch(e){r.error=e,r.hasError=!0}finally{di(r)}}static async waitFor(e,t,r){const i={stack:[],error:void 0,hasError:!1};try{let n;const s=li(i,await(async()=>{if(ii in e)return n=e.frame,await n.isolatedRealm().adoptHandle(e);n=e})(),!1),{visible:a=!1,hidden:o=!1,timeout:c,signal:l}=r;try{const e={stack:[],error:void 0,hasError:!1};try{l?.throwIfAborted();const r=li(e,await n.isolatedRealm().waitForFunction((async(e,t,r,i,n)=>{const s=e.createFunction(t),a=await s(i??document,r,e);return e.checkVisibility(a,n)}),{polling:a||o?"raf":"mutation",root:s,timeout:c,signal:l},Xr.create((e=>e.puppeteerUtil)),ti(this._querySelector),t,s,!!a||!o&&void 0),!1);if(l?.aborted)throw l.reason;return ii in r?await n.mainRealm().transferHandle(r):null}catch(t){e.error=t,e.hasError=!0}finally{di(e)}}catch(e){if(!Vt(e))throw e;if("AbortError"===e.name)throw e;throw e.message=`Waiting for selector \`${t}\` failed: ${e.message}`,e}}catch(e){i.error=e,i.hasError=!0}finally{di(i)}}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const hi=new Set(["StaticText","InlineTextBox"]),pi=e=>e.replace(/ +/g," ").trim(),fi=/\[\s*(?<attribute>\w+)\s*=\s*(?<quote>"|')(?<value>\\.|.*?(?=\k<quote>))\k<quote>\s*\]/g;class yi extends ui{static querySelector=async(e,t,{ariaQuerySelector:r})=>await r(e,t);static async*queryAll(e,t){const{name:r,role:i}=(e=>{const t={},r=e.replace(fi,((e,r,i,n)=>(r=r.trim(),st((e=>["name","role"].includes(e))(r),`Unknown aria attribute "${r}" in selector`),t[r]=pi(n),"")));return r&&!t.name&&(t.name=pi(r)),t})(t),n=await(async(e,t,r,i)=>{const{nodes:n}=await e.send("Accessibility.queryAXTree",{objectId:t.id,accessibleName:r,role:i});return n.filter((e=>!e.role||!hi.has(e.role.value)))})(e.realm.environment.client,e,r,i);yield*Zr.map(n,(t=>e.realm.adoptBackendNode(t.backendDOMNodeId)))}static queryOne=async(e,t)=>await Zr.first(this.queryAll(e,t))??null}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const mi=new class{#i=new Map;get(e){const t=this.#i.get(e);return t?t[1]:void 0}register(e,t){st(!this.#i.has(e),`Cannot register over existing handler: ${e}`),st(/^[a-zA-Z]+$/.test(e),"Custom query handler names may only contain [a-zA-Z]"),st(t.queryAll||t.queryOne,"At least one query method must be implemented.");const r=class extends ui{static querySelectorAll=ri(((e,t,r)=>r.customQuerySelectors.get(PLACEHOLDER("name")).querySelectorAll(e,t)),{name:JSON.stringify(e)});static querySelector=ri(((e,t,r)=>r.customQuerySelectors.get(PLACEHOLDER("name")).querySelector(e,t)),{name:JSON.stringify(e)})},i=ri((e=>{e.customQuerySelectors.register(PLACEHOLDER("name"),{queryAll:PLACEHOLDER("queryAll"),queryOne:PLACEHOLDER("queryOne")})}),{name:JSON.stringify(e),queryAll:t.queryAll?ti(t.queryAll):String(void 0),queryOne:t.queryOne?ti(t.queryOne):String(void 0)}).toString();this.#i.set(e,[i,r]),Yr.append(i)}unregister(e){const t=this.#i.get(e);if(!t)throw new Error(`Cannot unregister unknown handler: ${e}`);Yr.pop(t[0]),this.#i.delete(e)}names(){return[...this.#i.keys()]}clear(){for(const[e]of this.#i)Yr.pop(e);this.#i.clear()}};
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class gi extends ui{static querySelectorAll=(e,t,{pQuerySelectorAll:r})=>r(e,t);static querySelector=(e,t,{pQuerySelector:r})=>r(e,t)}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const wi={aria:yi,pierce:
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class extends ui{static querySelector=(e,t,{pierceQuerySelector:r})=>r(e,t);static querySelectorAll=(e,t,{pierceQuerySelectorAll:r})=>r(e,t)},xpath:
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class extends ui{static querySelectorAll=(e,t,{xpathQuerySelectorAll:r})=>r(e,t);static querySelector=(e,t,{xpathQuerySelectorAll:r})=>{for(const i of r(e,t,1))return i;return null}},text:class extends ui{static querySelectorAll=(e,t,{textQuerySelectorAll:r})=>r(e,t)}},vi=["=","/"];function bi(e){for(const t of[mi.names().map((e=>[e,mi.get(e)])),Object.entries(wi)])for(const[r,i]of t)for(const t of vi){const n=`${r}${t}`;if(e.startsWith(n))return{updatedSelector:e=e.slice(n.length),QueryHandler:i}}return{updatedSelector:e,QueryHandler:gi}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var ki=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Si=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Ci=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Ti=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});let Ei=(()=>{var e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,y,m,g,w,v,b,k,S,C,T,E,I,x,F,R;let _,P,D,M,A,q,O,N,L,B,j,H,$,K,V,W,z,U,G,Q,J,X,Y,Z,ee,te,re,ie,ne,se,ae=Dr,oe=[];return class ce extends ae{static{const le="function"==typeof Symbol&&Symbol.metadata?Object.create(ae[Symbol.metadata]??null):void 0;_=[rr(),(e=ce).bindIsolatedHandle.bind(e)],P=[rr(),(t=ce).bindIsolatedHandle.bind(t)],D=[rr(),(r=ce).bindIsolatedHandle.bind(r)],M=[rr(),(i=ce).bindIsolatedHandle.bind(i)],A=[rr(),(n=ce).bindIsolatedHandle.bind(n)],q=[rr(),(s=ce).bindIsolatedHandle.bind(s)],O=[rr(),(a=ce).bindIsolatedHandle.bind(a)],N=[rr(),(o=ce).bindIsolatedHandle.bind(o)],L=[rr(),(c=ce).bindIsolatedHandle.bind(c)],B=[rr(),(l=ce).bindIsolatedHandle.bind(l)],j=[rr(),(d=ce).bindIsolatedHandle.bind(d)],H=[rr(),(u=ce).bindIsolatedHandle.bind(u)],$=[rr(),(h=ce).bindIsolatedHandle.bind(h)],K=[rr(),(p=ce).bindIsolatedHandle.bind(p)],V=[rr(),(f=ce).bindIsolatedHandle.bind(f)],W=[rr(),(y=ce).bindIsolatedHandle.bind(y)],z=[rr(),(m=ce).bindIsolatedHandle.bind(m)],U=[rr(),(g=ce).bindIsolatedHandle.bind(g)],G=[rr(),(w=ce).bindIsolatedHandle.bind(w)],Q=[rr(),(v=ce).bindIsolatedHandle.bind(v)],J=[rr(),(b=ce).bindIsolatedHandle.bind(b)],X=[rr(),(k=ce).bindIsolatedHandle.bind(k)],Y=[rr(),(S=ce).bindIsolatedHandle.bind(S)],Z=[rr(),(C=ce).bindIsolatedHandle.bind(C)],ee=[rr(),(T=ce).bindIsolatedHandle.bind(T)],te=[rr(),(E=ce).bindIsolatedHandle.bind(E)],re=[rr(),(I=ce).bindIsolatedHandle.bind(I)],ie=[rr(),(x=ce).bindIsolatedHandle.bind(x)],ne=[rr(),(F=ce).bindIsolatedHandle.bind(F)],se=[rr(),(R=ce).bindIsolatedHandle.bind(R)],Si(this,null,_,{kind:"method",name:"getProperty",static:!1,private:!1,access:{has:e=>"getProperty"in e,get:e=>e.getProperty},metadata:le},null,oe),Si(this,null,P,{kind:"method",name:"getProperties",static:!1,private:!1,access:{has:e=>"getProperties"in e,get:e=>e.getProperties},metadata:le},null,oe),Si(this,null,D,{kind:"method",name:"jsonValue",static:!1,private:!1,access:{has:e=>"jsonValue"in e,get:e=>e.jsonValue},metadata:le},null,oe),Si(this,null,M,{kind:"method",name:"$",static:!1,private:!1,access:{has:e=>"$"in e,get:e=>e.$},metadata:le},null,oe),Si(this,null,A,{kind:"method",name:"$$",static:!1,private:!1,access:{has:e=>"$$"in e,get:e=>e.$$},metadata:le},null,oe),Si(this,null,q,{kind:"method",name:"waitForSelector",static:!1,private:!1,access:{has:e=>"waitForSelector"in e,get:e=>e.waitForSelector},metadata:le},null,oe),Si(this,null,O,{kind:"method",name:"isVisible",static:!1,private:!1,access:{has:e=>"isVisible"in e,get:e=>e.isVisible},metadata:le},null,oe),Si(this,null,N,{kind:"method",name:"isHidden",static:!1,private:!1,access:{has:e=>"isHidden"in e,get:e=>e.isHidden},metadata:le},null,oe),Si(this,null,L,{kind:"method",name:"toElement",static:!1,private:!1,access:{has:e=>"toElement"in e,get:e=>e.toElement},metadata:le},null,oe),Si(this,null,B,{kind:"method",name:"clickablePoint",static:!1,private:!1,access:{has:e=>"clickablePoint"in e,get:e=>e.clickablePoint},metadata:le},null,oe),Si(this,null,j,{kind:"method",name:"hover",static:!1,private:!1,access:{has:e=>"hover"in e,get:e=>e.hover},metadata:le},null,oe),Si(this,null,H,{kind:"method",name:"click",static:!1,private:!1,access:{has:e=>"click"in e,get:e=>e.click},metadata:le},null,oe),Si(this,null,$,{kind:"method",name:"drag",static:!1,private:!1,access:{has:e=>"drag"in e,get:e=>e.drag},metadata:le},null,oe),Si(this,null,K,{kind:"method",name:"dragEnter",static:!1,private:!1,access:{has:e=>"dragEnter"in e,get:e=>e.dragEnter},metadata:le},null,oe),Si(this,null,V,{kind:"method",name:"dragOver",static:!1,private:!1,access:{has:e=>"dragOver"in e,get:e=>e.dragOver},metadata:le},null,oe),Si(this,null,W,{kind:"method",name:"drop",static:!1,private:!1,access:{has:e=>"drop"in e,get:e=>e.drop},metadata:le},null,oe),Si(this,null,z,{kind:"method",name:"dragAndDrop",static:!1,private:!1,access:{has:e=>"dragAndDrop"in e,get:e=>e.dragAndDrop},metadata:le},null,oe),Si(this,null,U,{kind:"method",name:"select",static:!1,private:!1,access:{has:e=>"select"in e,get:e=>e.select},metadata:le},null,oe),Si(this,null,G,{kind:"method",name:"tap",static:!1,private:!1,access:{has:e=>"tap"in e,get:e=>e.tap},metadata:le},null,oe),Si(this,null,Q,{kind:"method",name:"touchStart",static:!1,private:!1,access:{has:e=>"touchStart"in e,get:e=>e.touchStart},metadata:le},null,oe),Si(this,null,J,{kind:"method",name:"touchMove",static:!1,private:!1,access:{has:e=>"touchMove"in e,get:e=>e.touchMove},metadata:le},null,oe),Si(this,null,X,{kind:"method",name:"touchEnd",static:!1,private:!1,access:{has:e=>"touchEnd"in e,get:e=>e.touchEnd},metadata:le},null,oe),Si(this,null,Y,{kind:"method",name:"focus",static:!1,private:!1,access:{has:e=>"focus"in e,get:e=>e.focus},metadata:le},null,oe),Si(this,null,Z,{kind:"method",name:"type",static:!1,private:!1,access:{has:e=>"type"in e,get:e=>e.type},metadata:le},null,oe),Si(this,null,ee,{kind:"method",name:"press",static:!1,private:!1,access:{has:e=>"press"in e,get:e=>e.press},metadata:le},null,oe),Si(this,null,te,{kind:"method",name:"boundingBox",static:!1,private:!1,access:{has:e=>"boundingBox"in e,get:e=>e.boundingBox},metadata:le},null,oe),Si(this,null,re,{kind:"method",name:"boxModel",static:!1,private:!1,access:{has:e=>"boxModel"in e,get:e=>e.boxModel},metadata:le},null,oe),Si(this,null,ie,{kind:"method",name:"screenshot",static:!1,private:!1,access:{has:e=>"screenshot"in e,get:e=>e.screenshot},metadata:le},null,oe),Si(this,null,ne,{kind:"method",name:"isIntersectingViewport",static:!1,private:!1,access:{has:e=>"isIntersectingViewport"in e,get:e=>e.isIntersectingViewport},metadata:le},null,oe),Si(this,null,se,{kind:"method",name:"scrollIntoView",static:!1,private:!1,access:{has:e=>"scrollIntoView"in e,get:e=>e.scrollIntoView},metadata:le},null,oe),le&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:le})}static bindIsolatedHandle(e,t){return async function(...t){const r={stack:[],error:void 0,hasError:!1};try{if(this.realm===this.frame.isolatedRealm())return await e.call(this,...t);const i=Ci(r,await this.frame.isolatedRealm().adoptHandle(this),!1),n=await e.call(i,...t);return n===i?this:n instanceof Dr?await this.realm.transferHandle(n):(Array.isArray(n)&&await Promise.all(n.map((async(e,t,r)=>{e instanceof Dr&&(r[t]=await this.realm.transferHandle(e))}))),n instanceof Map&&await Promise.all([...n.entries()].map((async([e,t])=>{t instanceof Dr&&n.set(e,await this.realm.transferHandle(t))}))),n)}catch(e){r.error=e,r.hasError=!0}finally{Ti(r)}}}handle=void ki(this,oe);constructor(e){super(),this.handle=e,this[ii]=!0}get id(){return this.handle.id}get disposed(){return this.handle.disposed}async getProperty(e){return await this.handle.getProperty(e)}async getProperties(){return await this.handle.getProperties()}async evaluate(e,...t){return e=bt(this.evaluate.name,e),await this.handle.evaluate(e,...t)}async evaluateHandle(e,...t){return e=bt(this.evaluateHandle.name,e),await this.handle.evaluateHandle(e,...t)}async jsonValue(){return await this.handle.jsonValue()}toString(){return this.handle.toString()}remoteObject(){return this.handle.remoteObject()}dispose(){return this.handle.dispose()}asElement(){return this}async $(e){const{updatedSelector:t,QueryHandler:r}=bi(e);return await r.queryOne(this,t)}async $$(e){const{updatedSelector:t,QueryHandler:r}=bi(e);return await Zr.collect(r.queryAll(this,t))}async $eval(e,t,...r){const i={stack:[],error:void 0,hasError:!1};try{t=bt(this.$eval.name,t);const n=Ci(i,await this.$(e),!1);if(!n)throw new Error(`Error: failed to find element matching selector "${e}"`);return await n.evaluate(t,...r)}catch(e){i.error=e,i.hasError=!0}finally{Ti(i)}}async $$eval(e,t,...r){const i={stack:[],error:void 0,hasError:!1};try{t=bt(this.$$eval.name,t);const n=await this.$$(e),s=Ci(i,await this.evaluateHandle(((e,...t)=>t),...n),!1),[a]=await Promise.all([s.evaluate(t,...r),...n.map((e=>e.dispose()))]);return a}catch(e){i.error=e,i.hasError=!0}finally{Ti(i)}}async waitForSelector(e,t={}){const{updatedSelector:r,QueryHandler:i}=bi(e);return await i.waitFor(this,r,t)}async#It(e){return await this.evaluate((async(e,t,r)=>Boolean(t.checkVisibility(e,r))),Xr.create((e=>e.puppeteerUtil)),e)}async isVisible(){return await this.#It(!0)}async isHidden(){return await this.#It(!1)}async toElement(e){const t=await this.evaluate(((e,t)=>e.nodeName===t.toUpperCase()),e);if(!t)throw new Error(`Element is not a(n) \`${e}\` element`);return this}async clickablePoint(e){const t=await this.#xt();if(!t)throw new Error("Node is either not clickable or not an Element");return void 0!==e?{x:t.x+e.x,y:t.y+e.y}:{x:t.x+t.width/2,y:t.y+t.height/2}}async hover(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().mouse.move(e,t)}async click(e={}){await this.scrollIntoViewIfNeeded();const{x:t,y:r}=await this.clickablePoint(e.offset);await this.frame.page().mouse.click(t,r,e)}async drag(e){await this.scrollIntoViewIfNeeded();const t=this.frame.page();if(t.isDragInterceptionEnabled()){const r=await this.clickablePoint();return e instanceof ce&&(e=await e.clickablePoint()),await t.mouse.drag(r,e)}try{t._isDragging||(t._isDragging=!0,await this.hover(),await t.mouse.down()),e instanceof ce?await e.hover():await t.mouse.move(e.x,e.y)}catch(e){throw t._isDragging=!1,e}}async dragEnter(e={items:[],dragOperationsMask:1}){const t=this.frame.page();await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.dragEnter(r,e)}async dragOver(e={items:[],dragOperationsMask:1}){const t=this.frame.page();await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.dragOver(r,e)}async drop(e={items:[],dragOperationsMask:1}){const t=this.frame.page();if("items"in e){await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.drop(r,e)}else await e.drag(this),t._isDragging=!1,await t.mouse.up()}async dragAndDrop(e,t){const r=this.frame.page();st(r.isDragInterceptionEnabled(),"Drag Interception is not enabled!"),await this.scrollIntoViewIfNeeded();const i=await this.clickablePoint(),n=await e.clickablePoint();await r.mouse.dragAndDrop(i,n,t)}async select(...e){for(const t of e)st(kt(t),'Values must be strings. Found value "'+t+'" of type "'+typeof t+'"');return await this.evaluate(((e,t)=>{const r=new Set(t);if(!(e instanceof HTMLSelectElement))throw new Error("Element is not a <select> element.");const i=new Set;if(e.multiple)for(const t of e.options)t.selected=r.has(t.value),t.selected&&i.add(t.value);else{for(const t of e.options)t.selected=!1;for(const t of e.options)if(r.has(t.value)){t.selected=!0,i.add(t.value);break}}return e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0})),[...i.values()]}),e)}async tap(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.tap(e,t)}async touchStart(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.touchStart(e,t)}async touchMove(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.touchMove(e,t)}async touchEnd(){await this.scrollIntoViewIfNeeded(),await this.frame.page().touchscreen.touchEnd()}async focus(){await this.evaluate((e=>{if(!(e instanceof HTMLElement))throw new Error("Cannot focus non-HTMLElement");return e.focus()}))}async type(e,t){await this.focus(),await this.frame.page().keyboard.type(e,t)}async press(e,t){await this.focus(),await this.frame.page().keyboard.press(e,t)}async#xt(){const e=await this.evaluate((e=>e instanceof Element?[...e.getClientRects()].map((e=>({x:e.x,y:e.y,width:e.width,height:e.height}))):null));if(!e?.length)return null;await this.#Ft(e);let t,r=this.frame;for(;t=r?.parentFrame();){const i={stack:[],error:void 0,hasError:!1};try{const n=Ci(i,await r.frameElement(),!1);if(!n)throw new Error("Unsupported frame type");const s=await n.evaluate((e=>{if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return{left:t.left+parseInt(r.paddingLeft,10)+parseInt(r.borderLeftWidth,10),top:t.top+parseInt(r.paddingTop,10)+parseInt(r.borderTopWidth,10)}}));if(!s)return null;for(const t of e)t.x+=s.left,t.y+=s.top;await n.#Ft(e),r=t}catch(e){i.error=e,i.hasError=!0}finally{Ti(i)}}const i=e.find((e=>e.width>=1&&e.height>=1));return i?{x:i.x,y:i.y,height:i.height,width:i.width}:null}async#Ft(e){const{documentWidth:t,documentHeight:r}=await this.frame.isolatedRealm().evaluate((()=>({documentWidth:document.documentElement.clientWidth,documentHeight:document.documentElement.clientHeight})));for(const i of e)Ii(i,t,r)}async boundingBox(){const e=await this.evaluate((e=>{if(!(e instanceof Element))return null;if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect();return{x:t.x,y:t.y,width:t.width,height:t.height}}));if(!e)return null;const t=await this.#Rt();return t?{x:e.x+t.x,y:e.y+t.y,height:e.height,width:e.width}:null}async boxModel(){const e=await this.evaluate((e=>{if(!(e instanceof Element))return null;if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e),i={padding:{left:parseInt(r.paddingLeft,10),top:parseInt(r.paddingTop,10),right:parseInt(r.paddingRight,10),bottom:parseInt(r.paddingBottom,10)},margin:{left:-parseInt(r.marginLeft,10),top:-parseInt(r.marginTop,10),right:-parseInt(r.marginRight,10),bottom:-parseInt(r.marginBottom,10)},border:{left:parseInt(r.borderLeft,10),top:parseInt(r.borderTop,10),right:parseInt(r.borderRight,10),bottom:parseInt(r.borderBottom,10)}},n=[{x:t.left,y:t.top},{x:t.left+t.width,y:t.top},{x:t.left+t.width,y:t.top+t.bottom},{x:t.left,y:t.top+t.bottom}],s=a(n,i.border);return{content:a(s,i.padding),padding:s,border:n,margin:a(n,i.margin),width:t.width,height:t.height};function a(e,t){return[{x:e[0].x+t.left,y:e[0].y+t.top},{x:e[1].x-t.right,y:e[1].y+t.top},{x:e[2].x-t.right,y:e[2].y-t.bottom},{x:e[3].x+t.left,y:e[3].y-t.bottom}]}}));if(!e)return null;const t=await this.#Rt();if(!t)return null;for(const r of["content","padding","border","margin"])for(const i of e[r])i.x+=t.x,i.y+=t.y;return e}async#Rt(){const e={x:0,y:0};let t,r=this.frame;for(;t=r?.parentFrame();){const i={stack:[],error:void 0,hasError:!1};try{const n=Ci(i,await r.frameElement(),!1);if(!n)throw new Error("Unsupported frame type");const s=await n.evaluate((e=>{if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return{left:t.left+parseInt(r.paddingLeft,10)+parseInt(r.borderLeftWidth,10),top:t.top+parseInt(r.paddingTop,10)+parseInt(r.borderTopWidth,10)}}));if(!s)return null;e.x+=s.left,e.y+=s.top,r=t}catch(e){i.error=e,i.hasError=!0}finally{Ti(i)}}return e}async screenshot(e={}){const{scrollIntoView:t=!0}=e;let r=await this.#_t();const i=this.frame.page();t&&(await this.scrollIntoViewIfNeeded(),r=await this.#_t());const[n,s]=await this.evaluate((()=>{if(!window.visualViewport)throw new Error("window.visualViewport is not supported.");return[window.visualViewport.pageLeft,window.visualViewport.pageTop]}));return r.x+=n,r.y+=s,await i.screenshot({...e,clip:r})}async#_t(){const e=await this.boundingBox();return st(e,"Node is either not visible or not an HTMLElement"),st(0!==e.width,"Node has 0 width."),st(0!==e.height,"Node has 0 height."),e}async assertConnectedElement(){const e=await this.evaluate((async e=>e.isConnected?e.nodeType!==Node.ELEMENT_NODE?"Node is not of type HTMLElement":void 0:"Node is detached from document"));if(e)throw new Error(e)}async scrollIntoViewIfNeeded(){await this.isIntersectingViewport({threshold:1})||await this.scrollIntoView()}async isIntersectingViewport(e={}){const t={stack:[],error:void 0,hasError:!1};try{await this.assertConnectedElement();const r=await this.#Pt(),i=Ci(t,r&&await r.#Dt(),!1);return await(i??this).evaluate((async(e,t)=>{const r=await new Promise((t=>{const r=new IntersectionObserver((e=>{t(e[0].intersectionRatio),r.disconnect()}));r.observe(e)}));return 1===t?1===r:r>t}),e.threshold??0)}catch(e){t.error=e,t.hasError=!0}finally{Ti(t)}}async scrollIntoView(){await this.assertConnectedElement(),await this.evaluate((async e=>{e.scrollIntoView({block:"center",inline:"center",behavior:"instant"})}))}async#Pt(){return await this.evaluate((e=>e instanceof SVGElement))?this:null}async#Dt(){return await this.evaluateHandle((e=>e instanceof SVGSVGElement?e:e.ownerSVGElement))}}})();function Ii(e,t,r){e.width=Math.max(e.x>=0?Math.min(t-e.x,e.width):Math.min(t,e.width+e.x),0),e.height=Math.max(e.y>=0?Math.min(r-e.y,e.height):Math.min(r,e.height+e.y),0)}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */function xi(e){let t,r;if(e.exception){if(!("object"===e.exception.type&&"error"===e.exception.subtype||e.exception.objectId))return Ri(e.exception);{const i=Fi(e);t=i.name,r=i.message}}else t="Error",r=e.text;const i=r.split("\n").length,n=new Error(r);n.name=t;const s=n.stack.split("\n"),a=s.splice(0,i);if(s.shift(),e.stackTrace&&s.length<Error.stackTraceLimit)for(const t of e.stackTrace.callFrames.reverse()){if(vt.isPuppeteerURL(t.url)&&t.url!==vt.INTERNAL_URL){const e=vt.parse(t.url);s.unshift(`    at ${t.functionName||e.functionName} (${e.functionName} at ${e.siteString}, <anonymous>:${t.lineNumber}:${t.columnNumber})`)}else s.push(`    at ${t.functionName||"<anonymous>"} (${t.url}:${t.lineNumber}:${t.columnNumber})`);if(s.length>=Error.stackTraceLimit)break}return n.stack=[...a,...s].join("\n"),n}const Fi=e=>{let t,r="";const i=e.exception?.description?.split("\n    at ")??[],n=Math.min(e.stackTrace?.callFrames.length??0,i.length-1);return i.splice(-n,n),e.exception?.className&&(r=e.exception.className),t=i.join("\n"),r&&t.startsWith(`${r}: `)&&(t=t.slice(r.length+2)),{message:t,name:r}};function Ri(e){if(st(!e.objectId,"Cannot extract value when objectId is given"),e.unserializableValue){if("bigint"===e.type)return BigInt(e.unserializableValue.replace("n",""));switch(e.unserializableValue){case"-0":return-0;case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error("Unsupported unserializable value: "+e.unserializableValue)}}return e.value}function _i(e,t){const r=globalThis[t];"PuppeteerBinding"!==r[Symbol.toStringTag]&&(Object.assign(globalThis,{[t](...i){const n=globalThis[t];n.args??=new Map,n.callbacks??=new Map;const s=(n.lastSeq??0)+1;return n.lastSeq=s,n.args.set(s,i),r(JSON.stringify({type:e,name:t,seq:s,args:i,isTrivial:!i.some((e=>e instanceof Node))})),new Promise(((e,t)=>{n.callbacks.set(s,{resolve(t){n.args.delete(s),e(t)},reject(e){n.args.delete(s),t(e)}})}))}}),globalThis[t][Symbol.toStringTag]="PuppeteerBinding")}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class Pi extends Dr{#e=!1;#Mt;#At;constructor(e,t){super(),this.#At=e,this.#Mt=t}get disposed(){return this.#e}get realm(){return this.#At}get client(){return this.realm.environment.client}async jsonValue(){if(!this.#Mt.objectId)return Ri(this.#Mt);const e=await this.evaluate((e=>e));if(void 0===e)throw new Error("Could not serialize referenced object");return e}asElement(){return null}async dispose(){this.#e||(this.#e=!0,await Di(this.client,this.#Mt))}toString(){if(!this.#Mt.objectId)return"JSHandle:"+Ri(this.#Mt);return"JSHandle@"+(this.#Mt.subtype||this.#Mt.type)}get id(){return this.#Mt.objectId}remoteObject(){return this.#Mt}}async function Di(e,t){t.objectId&&await e.send("Runtime.releaseObject",{objectId:t.objectId}).catch((e=>{gt(e)}))}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Mi=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Ai=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0};let qi=(()=>{var e,t;let r,i,n,s,a=Ei,o=[];return class extends a{static{const c="function"==typeof Symbol&&Symbol.metadata?Object.create(a[Symbol.metadata]??null):void 0;r=[rr()],i=[rr(),(e=Ei).bindIsolatedHandle.bind(e)],n=[rr(),(t=Ei).bindIsolatedHandle.bind(t)],s=[rr()],Ai(this,null,r,{kind:"method",name:"contentFrame",static:!1,private:!1,access:{has:e=>"contentFrame"in e,get:e=>e.contentFrame},metadata:c},null,o),Ai(this,null,i,{kind:"method",name:"scrollIntoView",static:!1,private:!1,access:{has:e=>"scrollIntoView"in e,get:e=>e.scrollIntoView},metadata:c},null,o),Ai(this,null,n,{kind:"method",name:"uploadFile",static:!1,private:!1,access:{has:e=>"uploadFile"in e,get:e=>e.uploadFile},metadata:c},null,o),Ai(this,null,s,{kind:"method",name:"autofill",static:!1,private:!1,access:{has:e=>"autofill"in e,get:e=>e.autofill},metadata:c},null,o),c&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:c})}constructor(e,t){super(new Pi(e,t)),Mi(this,o)}get realm(){return this.handle.realm}get client(){return this.handle.client}remoteObject(){return this.handle.remoteObject()}get#qt(){return this.frame._frameManager}get frame(){return this.realm.environment}async contentFrame(){const e=await this.client.send("DOM.describeNode",{objectId:this.id});return"string"!=typeof e.node.frameId?null:this.#qt.frame(e.node.frameId)}async scrollIntoView(){await this.assertConnectedElement();try{await this.client.send("DOM.scrollIntoViewIfNeeded",{objectId:this.id})}catch(e){gt(e),await super.scrollIntoView()}}async uploadFile(...e){const t=await this.evaluate((e=>e.multiple));let r;st(e.length<=1||t,"Multiple file uploads only work with <input type=file multiple>");try{r=await import("path")}catch(e){if(e instanceof TypeError)throw new Error("JSHandle#uploadFile can only be used in Node-like environments.");throw e}const i=e.map((e=>r.win32.isAbsolute(e)||r.posix.isAbsolute(e)?e:r.resolve(e)));if(0===i.length)return void await this.evaluate((e=>{e.files=(new DataTransfer).files,e.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))}));const{node:{backendNodeId:n}}=await this.client.send("DOM.describeNode",{objectId:this.id});await this.client.send("DOM.setFileInputFiles",{objectId:this.id,files:i,backendNodeId:n})}async autofill(e){const t=(await this.client.send("DOM.describeNode",{objectId:this.handle.id})).node.backendNodeId,r=this.frame._id;await this.client.send("Autofill.trigger",{fieldId:t,frameId:r,card:e.creditCard})}}})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Oi{_client;_world;_contextId;_contextName;constructor(e,t,r){this._client=e,this._world=r,this._contextId=t.id,t.name&&(this._contextName=t.name)}#Ot=!1;#Nt;get puppeteerUtil(){let e=Promise.resolve();return this.#Ot||(e=Promise.all([this.#Lt(new qr("__ariaQuerySelector",yi.queryOne)),this.#Lt(new qr("__ariaQuerySelectorAll",(async(e,t)=>{const r=yi.queryAll(e,t);return await e.realm.evaluateHandle(((...e)=>e),...await Zr.collect(r))})))]),this.#Ot=!0),Yr.inject((t=>{this.#Nt&&this.#Nt.then((e=>{e.dispose()})),this.#Nt=e.then((()=>this.evaluateHandle(t)))}),!this.#Nt),this.#Nt}async#Lt(e){try{this._world&&(this._world._bindings.set(e.name,e),await this._world._addBindingToContext(this,e.name))}catch{}}async evaluate(e,...t){return await this.#Bt(!0,e,...t)}async evaluateHandle(e,...t){return await this.#Bt(!1,e,...t)}async#Bt(e,t,...r){const i=`//# sourceURL=${(e=>{if(Object.prototype.hasOwnProperty.call(e,wt))return e[wt]})(t)?.toString()??vt.INTERNAL_URL}`;if(kt(t)){const r=this._contextId,n=t,s=_t.test(n)?n:`${n}\n${i}\n`,{exceptionDetails:a,result:o}=await this._client.send("Runtime.evaluate",{expression:s,contextId:r,returnByValue:e,awaitPromise:!0,userGesture:!0}).catch(Ni);if(a)throw xi(a);return e?Ri(o):Li(this._world,o)}const n=ti(t),s=_t.test(n)?n:`${n}\n${i}\n`;let a;try{a=this._client.send("Runtime.callFunctionOn",{functionDeclaration:s,executionContextId:this._contextId,arguments:r.length?await Promise.all(r.map(async function(e){e instanceof Xr&&(e=await e.get(this));if("bigint"==typeof e)return{unserializableValue:`${e.toString()}n`};if(Object.is(e,-0))return{unserializableValue:"-0"};if(Object.is(e,1/0))return{unserializableValue:"Infinity"};if(Object.is(e,-1/0))return{unserializableValue:"-Infinity"};if(Object.is(e,NaN))return{unserializableValue:"NaN"};const t=e&&(e instanceof Pi||e instanceof qi)?e:null;if(t){if(t.realm!==this._world)throw new Error("JSHandles can be evaluated only in the context they were created!");if(t.disposed)throw new Error("JSHandle is disposed!");return t.remoteObject().unserializableValue?{unserializableValue:t.remoteObject().unserializableValue}:t.remoteObject().objectId?{objectId:t.remoteObject().objectId}:{value:t.remoteObject().value}}return{value:e}}.bind(this))):[],returnByValue:e,awaitPromise:!0,userGesture:!0})}catch(e){throw e instanceof TypeError&&e.message.startsWith("Converting circular structure to JSON")&&(e.message+=" Recursive objects are not allowed."),e}const{exceptionDetails:o,result:c}=await a.catch(Ni);if(o)throw xi(o);return e?Ri(c):Li(this._world,c)}}const Ni=e=>{if(e.message.includes("Object reference chain is too long"))return{result:{type:"undefined"}};if(e.message.includes("Object couldn't be returned by value"))return{result:{type:"undefined"}};if(e.message.endsWith("Cannot find context with specified id")||e.message.endsWith("Inspected target navigated or closed"))throw new Error("Execution context was destroyed, most likely because of a navigation.");throw e};function Li(e,t){return"node"===t.subtype?new qi(e,t):new Pi(e,t)}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Bi extends it{#R;#jt=new Map;#Ht=new Map;#$t=new Map;#Kt;#Vt;#Wt=new WeakMap;#zt=$t.create();#Ut=new Set;constructor(e,t,r){super(),this.#R=e,this.#Kt=r,this.#Vt=t,this.#R.on("Target.targetCreated",this.#Gt),this.#R.on("Target.targetDestroyed",this.#Qt),this.#R.on(Bt.SessionDetached,this.#Jt),this.setupAttachmentListeners(this.#R)}setupAttachmentListeners(e){const t=t=>this.#Xt(e,t);st(!this.#Wt.has(e)),this.#Wt.set(e,t),e.on("Target.attachedToTarget",t)}#Jt=e=>{this.removeSessionListeners(e),this.#$t.delete(e.id())};removeSessionListeners(e){this.#Wt.has(e)&&(e.off("Target.attachedToTarget",this.#Wt.get(e)),this.#Wt.delete(e))}getAvailableTargets(){return this.#Ht}dispose(){this.#R.off("Target.targetCreated",this.#Gt),this.#R.off("Target.targetDestroyed",this.#Qt)}async initialize(){await this.#R.send("Target.setDiscoverTargets",{discover:!0,filter:[{}]}),this.#Ut=new Set(this.#jt.keys()),await this.#zt.valueOrThrow()}#Gt=async e=>{if(this.#jt.has(e.targetInfo.targetId))return;if(this.#jt.set(e.targetInfo.targetId,e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){const t=this.#Vt(e.targetInfo,void 0);return t._initialize(),this.#Ht.set(e.targetInfo.targetId,t),void this.#Yt(t._targetId)}const t=this.#Vt(e.targetInfo,void 0);!this.#Kt||this.#Kt(t)?(t._initialize(),this.#Ht.set(e.targetInfo.targetId,t),this.emit("targetAvailable",t),this.#Yt(t._targetId)):this.#Yt(e.targetInfo.targetId)};#Qt=e=>{this.#jt.delete(e.targetId),this.#Yt(e.targetId);const t=this.#Ht.get(e.targetId);t&&(this.emit("targetGone",t),this.#Ht.delete(e.targetId))};#Xt=async(e,t)=>{const r=t.targetInfo,i=this.#R.session(t.sessionId);if(!i)throw new Error(`Session ${t.sessionId} was not created.`);const n=this.#Ht.get(r.targetId);st(n,`Target ${r.targetId} is missing`),i._setTarget(n),this.setupAttachmentListeners(i),this.#$t.set(i.id(),this.#Ht.get(r.targetId)),e.emit(Bt.Ready,i)};#Yt(e){this.#Ut.delete(e),0===this.#Ut.size&&this.#zt.resolve()}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var ji,Hi=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},$i=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Ki=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Vi=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});!function(e){e.FrameNavigated=Symbol("Frame.FrameNavigated"),e.FrameSwapped=Symbol("Frame.FrameSwapped"),e.LifecycleEvent=Symbol("Frame.LifecycleEvent"),e.FrameNavigatedWithinDocument=Symbol("Frame.FrameNavigatedWithinDocument"),e.FrameDetached=Symbol("Frame.FrameDetached"),e.FrameSwappedByActivation=Symbol("Frame.FrameSwappedByActivation")}(ji||(ji={}));const Wi=rr((e=>`Attempted to use detached Frame '${e._id}'.`));let zi=(()=>{let e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,y,m,g,w,v,b=it,k=[];return class extends b{static{const S="function"==typeof Symbol&&Symbol.metadata?Object.create(b[Symbol.metadata]??null):void 0;e=[Wi],t=[Wi],r=[Wi],i=[Wi],n=[Wi],s=[Wi],a=[Wi],o=[Wi],c=[Wi],l=[Wi],d=[Wi],u=[Wi],h=[Wi],p=[Wi],f=[Wi],y=[Wi],m=[Wi],g=[Wi],w=[Wi],v=[Wi],$i(this,null,e,{kind:"method",name:"frameElement",static:!1,private:!1,access:{has:e=>"frameElement"in e,get:e=>e.frameElement},metadata:S},null,k),$i(this,null,t,{kind:"method",name:"evaluateHandle",static:!1,private:!1,access:{has:e=>"evaluateHandle"in e,get:e=>e.evaluateHandle},metadata:S},null,k),$i(this,null,r,{kind:"method",name:"evaluate",static:!1,private:!1,access:{has:e=>"evaluate"in e,get:e=>e.evaluate},metadata:S},null,k),$i(this,null,i,{kind:"method",name:"locator",static:!1,private:!1,access:{has:e=>"locator"in e,get:e=>e.locator},metadata:S},null,k),$i(this,null,n,{kind:"method",name:"$",static:!1,private:!1,access:{has:e=>"$"in e,get:e=>e.$},metadata:S},null,k),$i(this,null,s,{kind:"method",name:"$$",static:!1,private:!1,access:{has:e=>"$$"in e,get:e=>e.$$},metadata:S},null,k),$i(this,null,a,{kind:"method",name:"$eval",static:!1,private:!1,access:{has:e=>"$eval"in e,get:e=>e.$eval},metadata:S},null,k),$i(this,null,o,{kind:"method",name:"$$eval",static:!1,private:!1,access:{has:e=>"$$eval"in e,get:e=>e.$$eval},metadata:S},null,k),$i(this,null,c,{kind:"method",name:"waitForSelector",static:!1,private:!1,access:{has:e=>"waitForSelector"in e,get:e=>e.waitForSelector},metadata:S},null,k),$i(this,null,l,{kind:"method",name:"waitForFunction",static:!1,private:!1,access:{has:e=>"waitForFunction"in e,get:e=>e.waitForFunction},metadata:S},null,k),$i(this,null,d,{kind:"method",name:"content",static:!1,private:!1,access:{has:e=>"content"in e,get:e=>e.content},metadata:S},null,k),$i(this,null,u,{kind:"method",name:"addScriptTag",static:!1,private:!1,access:{has:e=>"addScriptTag"in e,get:e=>e.addScriptTag},metadata:S},null,k),$i(this,null,h,{kind:"method",name:"addStyleTag",static:!1,private:!1,access:{has:e=>"addStyleTag"in e,get:e=>e.addStyleTag},metadata:S},null,k),$i(this,null,p,{kind:"method",name:"click",static:!1,private:!1,access:{has:e=>"click"in e,get:e=>e.click},metadata:S},null,k),$i(this,null,f,{kind:"method",name:"focus",static:!1,private:!1,access:{has:e=>"focus"in e,get:e=>e.focus},metadata:S},null,k),$i(this,null,y,{kind:"method",name:"hover",static:!1,private:!1,access:{has:e=>"hover"in e,get:e=>e.hover},metadata:S},null,k),$i(this,null,m,{kind:"method",name:"select",static:!1,private:!1,access:{has:e=>"select"in e,get:e=>e.select},metadata:S},null,k),$i(this,null,g,{kind:"method",name:"tap",static:!1,private:!1,access:{has:e=>"tap"in e,get:e=>e.tap},metadata:S},null,k),$i(this,null,w,{kind:"method",name:"type",static:!1,private:!1,access:{has:e=>"type"in e,get:e=>e.type},metadata:S},null,k),$i(this,null,v,{kind:"method",name:"title",static:!1,private:!1,access:{has:e=>"title"in e,get:e=>e.title},metadata:S},null,k),S&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:S})}_id=void Hi(this,k);_parentId;_name;_hasStartedLoading=!1;constructor(){super()}#Zt;#er(){return this.#Zt||(this.#Zt=this.isolatedRealm().evaluateHandle((()=>document)).then((e=>this.mainRealm().transferHandle(e)))),this.#Zt}clearDocumentHandle(){this.#Zt=void 0}async frameElement(){const e={stack:[],error:void 0,hasError:!1};try{const t=this.parentFrame();if(!t)return null;const r=Ki(e,await t.isolatedRealm().evaluateHandle((()=>document.querySelectorAll("iframe,frame"))),!1);for await(const e of ci(r)){const t={stack:[],error:void 0,hasError:!1};try{const r=Ki(t,e,!1),i=await r.contentFrame();if(i?._id===this._id)return r.move()}catch(e){t.error=e,t.hasError=!0}finally{Vi(t)}}return null}catch(t){e.error=t,e.hasError=!0}finally{Vi(e)}}async evaluateHandle(e,...t){return e=bt(this.evaluateHandle.name,e),await this.mainRealm().evaluateHandle(e,...t)}async evaluate(e,...t){return e=bt(this.evaluate.name,e),await this.mainRealm().evaluate(e,...t)}locator(e){return"string"==typeof e?pr.create(this,e):lr.create(this,e)}async $(e){const t=await this.#er();return await t.$(e)}async $$(e){const t=await this.#er();return await t.$$(e)}async $eval(e,t,...r){t=bt(this.$eval.name,t);const i=await this.#er();return await i.$eval(e,t,...r)}async $$eval(e,t,...r){t=bt(this.$$eval.name,t);const i=await this.#er();return await i.$$eval(e,t,...r)}async waitForSelector(e,t={}){const{updatedSelector:r,QueryHandler:i}=bi(e);return await i.waitFor(this,r,t)}async waitForFunction(e,t={},...r){return await this.mainRealm().waitForFunction(e,t,...r)}async content(){return await this.evaluate((()=>{let e="";for(const t of document.childNodes)if(t===document.documentElement)e+=document.documentElement.outerHTML;else e+=(new XMLSerializer).serializeToString(t);return e}))}async setFrameContent(e){return await this.evaluate((e=>{document.open(),document.write(e),document.close()}),e)}name(){return this._name||""}isDetached(){return this.detached}get disposed(){return this.detached}async addScriptTag(e){let{content:t="",type:r}=e;const{path:i}=e;if(+!!e.url+ +!!i+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(i){const e=await Et();t=await e.readFile(i,"utf8"),t+=`//# sourceURL=${i.replace(/\n/g,"")}`}return r=r??"text/javascript",await this.mainRealm().transferHandle(await this.isolatedRealm().evaluateHandle((async({Deferred:e},{url:t,id:r,type:i,content:n})=>{const s=e.create(),a=document.createElement("script");return a.type=i,a.text=n,t?(a.src=t,a.addEventListener("load",(()=>s.resolve()),{once:!0}),a.addEventListener("error",(e=>{s.reject(new Error(e.message??"Could not load script"))}),{once:!0})):s.resolve(),r&&(a.id=r),document.head.appendChild(a),await s.valueOrThrow(),a}),Xr.create((e=>e.puppeteerUtil)),{...e,type:r,content:t}))}async addStyleTag(e){let{content:t=""}=e;const{path:r}=e;if(+!!e.url+ +!!r+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(r){const i=await Et();t=await i.readFile(r,"utf8"),t+="/*# sourceURL="+r.replace(/\n/g,"")+"*/",e.content=t}return await this.mainRealm().transferHandle(await this.isolatedRealm().evaluateHandle((async({Deferred:e},{url:t,content:r})=>{const i=e.create();let n;if(t){const e=document.createElement("link");e.rel="stylesheet",e.href=t,n=e}else n=document.createElement("style"),n.appendChild(document.createTextNode(r));return n.addEventListener("load",(()=>{i.resolve()}),{once:!0}),n.addEventListener("error",(e=>{i.reject(new Error(e.message??"Could not load style"))}),{once:!0}),document.head.appendChild(n),await i.valueOrThrow(),n}),Xr.create((e=>e.puppeteerUtil)),e))}async click(e,t={}){const r={stack:[],error:void 0,hasError:!1};try{const i=Ki(r,await this.$(e),!1);st(i,`No element found for selector: ${e}`),await i.click(t),await i.dispose()}catch(e){r.error=e,r.hasError=!0}finally{Vi(r)}}async focus(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ki(t,await this.$(e),!1);st(r,`No element found for selector: ${e}`),await r.focus()}catch(e){t.error=e,t.hasError=!0}finally{Vi(t)}}async hover(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ki(t,await this.$(e),!1);st(r,`No element found for selector: ${e}`),await r.hover()}catch(e){t.error=e,t.hasError=!0}finally{Vi(t)}}async select(e,...t){const r={stack:[],error:void 0,hasError:!1};try{const i=Ki(r,await this.$(e),!1);return st(i,`No element found for selector: ${e}`),await i.select(...t)}catch(e){r.error=e,r.hasError=!0}finally{Vi(r)}}async tap(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ki(t,await this.$(e),!1);st(r,`No element found for selector: ${e}`),await r.tap()}catch(e){t.error=e,t.hasError=!0}finally{Vi(t)}}async type(e,t,r){const i={stack:[],error:void 0,hasError:!1};try{const n=Ki(i,await this.$(e),!1);st(n,`No element found for selector: ${e}`),await n.type(t,r)}catch(e){i.error=e,i.hasError=!0}finally{Vi(i)}}async title(){return await this.isolatedRealm().evaluate((()=>document.title))}}})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ui{id;name;constructor(e,t){this.id=e,this.name=t}}class Gi{#ue;#tr;#S;#de=!1;#rr=this.#ir.bind(this);#nr=new Set;devices=[];constructor(e,t,r){this.#ue=e,this.#tr=t,this.#S=r.id,this.#ue.on("DeviceAccess.deviceRequestPrompted",this.#rr),this.#ue.on("Target.detachedFromTarget",(()=>{this.#ue=null})),this.#ir(r)}#ir(e){if(e.id===this.#S)for(const t of e.devices){if(this.devices.some((e=>e.id===t.id)))continue;const e=new Ui(t.id,t.name);this.devices.push(e);for(const t of this.#nr)t.filter(e)&&t.promise.resolve(e)}}async waitForDevice(e,t={}){for(const t of this.devices)if(e(t))return t;const{timeout:r=this.#tr.timeout()}=t,i=$t.create({message:`Waiting for \`DeviceRequestPromptDevice\` failed: ${r}ms exceeded`,timeout:r}),n={filter:e,promise:i};this.#nr.add(n);try{return await i.valueOrThrow()}finally{this.#nr.delete(n)}}async select(e){return st(null!==this.#ue,"Cannot select device through detached session!"),st(this.devices.includes(e),"Cannot select unknown device!"),st(!this.#de,"Cannot select DeviceRequestPrompt which is already handled!"),this.#ue.off("DeviceAccess.deviceRequestPrompted",this.#rr),this.#de=!0,await this.#ue.send("DeviceAccess.selectPrompt",{id:this.#S,deviceId:e.id})}async cancel(){return st(null!==this.#ue,"Cannot cancel prompt through detached session!"),st(!this.#de,"Cannot cancel DeviceRequestPrompt which is already handled!"),this.#ue.off("DeviceAccess.deviceRequestPrompted",this.#rr),this.#de=!0,await this.#ue.send("DeviceAccess.cancelPrompt",{id:this.#S})}}class Qi{#ue;#tr;#sr=new Set;constructor(e,t){this.#ue=e,this.#tr=t,this.#ue.on("DeviceAccess.deviceRequestPrompted",(e=>{this.#ar(e)})),this.#ue.on("Target.detachedFromTarget",(()=>{this.#ue=null}))}async waitForDevicePrompt(e={}){st(null!==this.#ue,"Cannot wait for device prompt through detached session!");let t;0===this.#sr.size&&(t=this.#ue.send("DeviceAccess.enable"));const{timeout:r=this.#tr.timeout()}=e,i=$t.create({message:`Waiting for \`DeviceRequestPrompt\` failed: ${r}ms exceeded`,timeout:r});this.#sr.add(i);try{const[e]=await Promise.all([i.valueOrThrow(),t]);return e}finally{this.#sr.delete(i)}}#ar(e){if(!this.#sr.size)return;st(null!==this.#ue);const t=new Gi(this.#ue,this.#tr,e);for(const e of this.#sr)e.resolve(t);this.#sr.clear()}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ji{#At;#or;#cr;#Ce;#ae;#xe;#g;#lr=$t.create();#dr;#ur;#hr=[];constructor(e,t,r,...i){if(this.#At=e,this.#or=t.polling,this.#cr=t.root,this.#ur=t.signal,this.#ur?.addEventListener("abort",(()=>{this.terminate(this.#ur?.reason)}),{once:!0}),"string"==typeof r)this.#Ce=`() => {return (${r});}`;else this.#Ce=ti(r);this.#ae=i,this.#At.taskManager.add(this),t.timeout&&(this.#g=new ht(`Waiting failed: ${t.timeout}ms exceeded`),this.#xe=setTimeout((()=>{this.terminate(this.#g)}),t.timeout)),this.rerun()}get result(){return this.#lr.valueOrThrow()}async rerun(){for(const e of this.#hr)e.abort();this.#hr.length=0;const e=new AbortController;this.#hr.push(e);try{switch(this.#or){case"raf":this.#dr=await this.#At.evaluateHandle((({RAFPoller:e,createFunction:t},r,...i)=>{const n=t(r);return new e((()=>n(...i)))}),Xr.create((e=>e.puppeteerUtil)),this.#Ce,...this.#ae);break;case"mutation":this.#dr=await this.#At.evaluateHandle((({MutationPoller:e,createFunction:t},r,i,...n)=>{const s=t(i);return new e((()=>s(...n)),r||document)}),Xr.create((e=>e.puppeteerUtil)),this.#cr,this.#Ce,...this.#ae);break;default:this.#dr=await this.#At.evaluateHandle((({IntervalPoller:e,createFunction:t},r,i,...n)=>{const s=t(i);return new e((()=>s(...n)),r)}),Xr.create((e=>e.puppeteerUtil)),this.#or,this.#Ce,...this.#ae)}await this.#dr.evaluate((e=>{e.start()}));const e=await this.#dr.evaluateHandle((e=>e.result()));this.#lr.resolve(e),await this.terminate()}catch(t){if(e.signal.aborted)return;const r=this.getBadError(t);r&&await this.terminate(r)}}async terminate(e){if(this.#At.taskManager.delete(this),clearTimeout(this.#xe),e&&!this.#lr.finished()&&this.#lr.reject(e),this.#dr)try{await this.#dr.evaluateHandle((async e=>{await e.stop()})),this.#dr&&(await this.#dr.dispose(),this.#dr=void 0)}catch{}}getBadError(e){if(Vt(e)){if(e.message.includes("Execution context is not available in detached frame"))return new Error("Waiting failed: Frame detached");if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return;if(e.message.includes("AbortError: Actor 'MessageHandlerFrame' destroyed"))return;return e}return new Error("WaitTask failed with an error",{cause:e})}}class Xi{#pr=new Set;add(e){this.#pr.add(e)}delete(e){this.#pr.delete(e)}terminateAll(e){for(const t of this.#pr)t.terminate(e);this.#pr.clear()}async rerunAll(){await Promise.all([...this.#pr].map((e=>e.rerun())))}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Yi{timeoutSettings;taskManager=new Xi;constructor(e){this.timeoutSettings=e}async waitForFunction(e,t={},...r){const{polling:i="raf",timeout:n=this.timeoutSettings.timeout(),root:s,signal:a}=t;if("number"==typeof i&&i<0)throw new Error("Cannot poll with non-positive interval");const o=new Ji(this,{polling:i,root:s,timeout:n,signal:a},e,...r);return await o.result}get disposed(){return this.#e}#e=!1;[Ze](){this.#e=!0,this.taskManager.terminateAll(new Error("waitForFunction failed: frame got detached."))}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Zi=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},en=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class tn extends Yi{#fr=$t.create();#yr=new Set;#mr=new Map;get _bindings(){return this.#mr}#gr;constructor(e,t){super(t),this.#gr=e,this.frameUpdated()}get environment(){return this.#gr}frameUpdated(){this.client.on("Runtime.bindingCalled",this.#wr)}get client(){return this.#gr.client}clearContext(){this.#fr?.reject(new Error("Execution context was destroyed")),this.#fr=$t.create(),"clearDocumentHandle"in this.#gr&&this.#gr.clearDocumentHandle()}setContext(e){this.#yr.clear(),this.#fr.resolve(e),this.taskManager.rerunAll()}hasContext(){return this.#fr.resolved()}#vr(){if(this.disposed)throw new Error(`Execution context is not available in detached frame "${this.environment.url()}" (are you trying to evaluate?)`);if(null===this.#fr)throw new Error("Execution content promise is missing");return this.#fr.valueOrThrow()}async evaluateHandle(e,...t){e=bt(this.evaluateHandle.name,e);const r=await this.#vr();return await r.evaluateHandle(e,...t)}async evaluate(e,...t){e=bt(this.evaluate.name,e);let r=this.#fr.value();return r&&r instanceof Oi||(r=await this.#vr()),await r.evaluate(e,...t)}#M=new Xt;async _addBindingToContext(e,t){const r={stack:[],error:void 0,hasError:!1};try{if(this.#yr.has(t))return;Zi(r,await this.#M.acquire(),!1);try{await e._client.send("Runtime.addBinding",e._contextName?{name:t,executionContextName:e._contextName}:{name:t,executionContextId:e._contextId}),await e.evaluate(_i,"internal",t),this.#yr.add(t)}catch(e){if(e instanceof Error){if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return}gt(e)}}catch(e){r.error=e,r.hasError=!0}finally{en(r)}}#wr=async e=>{let t;try{t=JSON.parse(e.payload)}catch{return}const{type:r,name:i,seq:n,args:s,isTrivial:a}=t;if("internal"===r&&this.#yr.has(i))try{const t=await this.#fr.valueOrThrow();if(e.executionContextId!==t._contextId)return;const r=this._bindings.get(i);await(r?.run(t,n,s,a))}catch(e){gt(e)}};async adoptBackendNode(e){const t=await this.#vr(),{object:r}=await this.client.send("DOM.resolveNode",{backendNodeId:e,executionContextId:t._contextId});return Li(this,r)}async adoptHandle(e){if(e.realm===this)return await e.evaluateHandle((e=>e));const t=await this.client.send("DOM.describeNode",{objectId:e.id});return await this.adoptBackendNode(t.node.backendNodeId)}async transferHandle(e){if(e.realm===this)return e;if(void 0===e.remoteObject().objectId)return e;const t=await this.client.send("DOM.describeNode",{objectId:e.remoteObject().objectId}),r=await this.adoptBackendNode(t.node.backendNodeId);return await e.dispose(),r}[Ze](){super[Ze](),this.client.off("Runtime.bindingCalled",this.#wr)}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const rn=Symbol("mainWorld"),nn=Symbol("puppeteerWorld");
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var sn;!function(e){e.FrameAttached=Symbol("FrameManager.FrameAttached"),e.FrameNavigated=Symbol("FrameManager.FrameNavigated"),e.FrameDetached=Symbol("FrameManager.FrameDetached"),e.FrameSwapped=Symbol("FrameManager.FrameSwapped"),e.LifecycleEvent=Symbol("FrameManager.LifecycleEvent"),e.FrameNavigatedWithinDocument=Symbol("FrameManager.FrameNavigatedWithinDocument")}(sn||(sn={}));
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const an=new Map([["load","load"],["domcontentloaded","DOMContentLoaded"],["networkidle0","networkIdle"],["networkidle2","networkAlmostIdle"]]);class on{#br;#kr;#xe;#Sr=null;#Ne=new tt;#Cr;#Tr;#Er=$t.create();#Ir=$t.create();#xr=$t.create();#Fr;#Rr;#_r;constructor(e,t,r,i){Array.isArray(r)?r=r.slice():"string"==typeof r&&(r=[r]),this.#Cr=t._loaderId,this.#br=r.map((e=>{const t=an.get(e);return st(t,"Unknown value for options.waitUntil: "+e),t})),this.#kr=t,this.#xe=i,this.#Ne.use(new nt(t._frameManager,sn.LifecycleEvent,this.#Pr.bind(this))),this.#Ne.use(new nt(t,ji.FrameNavigatedWithinDocument,this.#Dr.bind(this))),this.#Ne.use(new nt(t,ji.FrameNavigated,this.#Mr.bind(this))),this.#Ne.use(new nt(t,ji.FrameSwapped,this.#Ar.bind(this))),this.#Ne.use(new nt(t,ji.FrameSwappedByActivation,this.#Ar.bind(this))),this.#Ne.use(new nt(t,ji.FrameDetached,this.#qr.bind(this))),this.#Ne.use(new nt(e,Er.Request,this.#Or.bind(this))),this.#Ne.use(new nt(e,Er.Response,this.#Nr.bind(this))),this.#Ne.use(new nt(e,Er.RequestFailed,this.#Lr.bind(this))),this.#Tr=$t.create({timeout:this.#xe,message:`Navigation timeout of ${this.#xe} ms exceeded`}),this.#Pr()}#Or(e){e.frame()===this.#kr&&e.isNavigationRequest()&&(this.#Sr=e,this.#_r?.resolve(),this.#_r=$t.create(),null!==e.response()&&this.#_r?.resolve())}#Lr(e){this.#Sr?.id===e.id&&this.#_r?.resolve()}#Nr(e){this.#Sr?.id===e.request().id&&this.#_r?.resolve()}#qr(e){this.#kr!==e?this.#Pr():this.#Tr.resolve(new Error("Navigating frame was detached"))}async navigationResponse(){return await(this.#_r?.valueOrThrow()),this.#Sr?this.#Sr.response():null}sameDocumentNavigationPromise(){return this.#Er.valueOrThrow()}newDocumentNavigationPromise(){return this.#xr.valueOrThrow()}lifecyclePromise(){return this.#Ir.valueOrThrow()}terminationPromise(){return this.#Tr.valueOrThrow()}#Dr(){this.#Fr=!0,this.#Pr()}#Mr(e){if("BackForwardCacheRestore"===e)return this.#Ar();this.#Pr()}#Ar(){this.#Rr=!0,this.#Pr()}#Pr(){(function e(t,r){for(const e of r)if(!t._lifecycleEvents.has(e))return!1;for(const i of t.childFrames())if(i._hasStartedLoading&&!e(i,r))return!1;return!0})(this.#kr,this.#br)&&(this.#Ir.resolve(),this.#Fr&&this.#Er.resolve(void 0),(this.#Rr||this.#kr._loaderId!==this.#Cr)&&this.#xr.resolve(void 0))}dispose(){this.#Ne.dispose(),this.#Tr.resolve(new Error("LifecycleWatcher disposed"))}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var cn=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},ln=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var y in i)f[y]="access"===y?{}:i[y];for(var y in i.access)f.access[y]=i.access[y];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var m=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw new TypeError("Object expected");(o=a(m.get))&&(u.get=o),(o=a(m.set))&&(u.set=o),(o=a(m.init))&&n.unshift(o)}else(o=a(m))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0};let dn=(()=>{let e,t,r,i,n=zi,s=[];return class extends n{static{const a="function"==typeof Symbol&&Symbol.metadata?Object.create(n[Symbol.metadata]??null):void 0;ln(this,null,e,{kind:"method",name:"goto",static:!1,private:!1,access:{has:e=>"goto"in e,get:e=>e.goto},metadata:a},null,s),ln(this,null,t,{kind:"method",name:"waitForNavigation",static:!1,private:!1,access:{has:e=>"waitForNavigation"in e,get:e=>e.waitForNavigation},metadata:a},null,s),ln(this,null,r,{kind:"method",name:"setContent",static:!1,private:!1,access:{has:e=>"setContent"in e,get:e=>e.setContent},metadata:a},null,s),ln(this,null,i,{kind:"method",name:"waitForDevicePrompt",static:!1,private:!1,access:{has:e=>"waitForDevicePrompt"in e,get:e=>e.waitForDevicePrompt},metadata:a},null,s),a&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:a})}#Te=(cn(this,s),"");#Br=!1;#ue;worlds;_frameManager;_id;_loaderId="";_lifecycleEvents=new Set;_parentId;constructor(e,t,r,i){super(),this._frameManager=e,this.#Te="",this._id=t,this._parentId=r,this.#Br=!1,this._loaderId="",this.updateClient(i),this.on(ji.FrameSwappedByActivation,(()=>{this._onLoadingStarted(),this._onLoadingStopped()}))}_client(){return this.#ue}updateId(e){this._id=e}updateClient(e,t=!1){this.#ue=e,t?(this.worlds[rn].frameUpdated(),this.worlds[nn].frameUpdated()):(this.worlds&&(this.worlds[rn].clearContext(),this.worlds[nn].clearContext()),this.worlds={[rn]:new tn(this,this._frameManager.timeoutSettings),[nn]:new tn(this,this._frameManager.timeoutSettings)})}page(){return this._frameManager.page()}isOOPFrame(){return this.#ue!==this._frameManager.client}async goto(e,t={}){const{referer:r=this._frameManager.networkManager.extraHTTPHeaders().referer,referrerPolicy:i=this._frameManager.networkManager.extraHTTPHeaders()["referer-policy"],waitUntil:n=["load"],timeout:s=this._frameManager.timeoutSettings.navigationTimeout()}=t;let a=!1;const o=new on(this._frameManager.networkManager,this,n,s);let c=await $t.race([async function(e,t,r,i,n){try{const s=await e.send("Page.navigate",{url:t,referrer:r,frameId:n,referrerPolicy:i});return a=!!s.loaderId,"net::ERR_HTTP_RESPONSE_CODE_FAILURE"===s.errorText?null:s.errorText?new Error(`${s.errorText} at ${t}`):null}catch(e){if(Vt(e))return e;throw e}}(this.#ue,e,r,i,this._id),o.terminationPromise()]);c||(c=await $t.race([o.terminationPromise(),a?o.newDocumentNavigationPromise():o.sameDocumentNavigationPromise()]));try{if(c)throw c;return await o.navigationResponse()}finally{o.dispose()}}async waitForNavigation(e={}){const{waitUntil:t=["load"],timeout:r=this._frameManager.timeoutSettings.navigationTimeout()}=e,i=new on(this._frameManager.networkManager,this,t,r),n=await $t.race([i.terminationPromise(),i.sameDocumentNavigationPromise(),i.newDocumentNavigationPromise()]);try{if(n)throw n;return await i.navigationResponse()}finally{i.dispose()}}get client(){return this.#ue}mainRealm(){return this.worlds[rn]}isolatedRealm(){return this.worlds[nn]}async setContent(e,t={}){const{waitUntil:r=["load"],timeout:i=this._frameManager.timeoutSettings.navigationTimeout()}=t;await this.setFrameContent(e);const n=new on(this._frameManager.networkManager,this,r,i),s=await $t.race([n.terminationPromise(),n.lifecyclePromise()]);if(n.dispose(),s)throw s}url(){return this.#Te}parentFrame(){return this._frameManager._frameTree.parentFrame(this._id)||null}childFrames(){return this._frameManager._frameTree.childFrames(this._id)}#jr(){const e=this.page().mainFrame();return this.isOOPFrame()||null===e?this._frameManager._deviceRequestPromptManager(this.#ue):e._frameManager._deviceRequestPromptManager(this.#ue)}async waitForDevicePrompt(e={}){return await this.#jr().waitForDevicePrompt(e)}_navigated(e){this._name=e.name,this.#Te=`${e.url}${e.urlFragment||""}`}_navigatedWithinDocument(e){this.#Te=e}_onLifecycleEvent(e,t){"init"===t&&(this._loaderId=e,this._lifecycleEvents.clear()),this._lifecycleEvents.add(t)}_onLoadingStopped(){this._lifecycleEvents.add("DOMContentLoaded"),this._lifecycleEvents.add("load")}_onLoadingStarted(){this._hasStartedLoading=!0}get detached(){return this.#Br}[(e=[Wi],t=[Wi],r=[Wi],i=[Wi],Ze)](){this.#Br||(this.#Br=!0,this.worlds[rn][Ze](),this.worlds[nn][Ze]())}exposeFunction(){throw new ft}}})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class un{#Hr=new Map;#$r=new Map;#Kr=new Map;#Vr;#Wr=new Map;getMainFrame(){return this.#Vr}getById(e){return this.#Hr.get(e)}waitForFrame(e){const t=this.getById(e);if(t)return Promise.resolve(t);const r=$t.create();return(this.#Wr.get(e)||new Set).add(r),r.valueOrThrow()}frames(){return Array.from(this.#Hr.values())}addFrame(e){this.#Hr.set(e._id,e),e._parentId?(this.#$r.set(e._id,e._parentId),this.#Kr.has(e._parentId)||this.#Kr.set(e._parentId,new Set),this.#Kr.get(e._parentId).add(e._id)):this.#Vr||(this.#Vr=e),this.#Wr.get(e._id)?.forEach((t=>t.resolve(e)))}removeFrame(e){this.#Hr.delete(e._id),this.#$r.delete(e._id),e._parentId?this.#Kr.get(e._parentId)?.delete(e._id):this.#Vr=void 0}childFrames(e){const t=this.#Kr.get(e);return t?Array.from(t).map((e=>this.getById(e))).filter((e=>void 0!==e)):[]}parentFrame(e){const t=this.#$r.get(e);return t?this.getById(t):void 0}}class hn{_interceptionId;_failureText=null;_response=null;_fromMemoryCache=!1;_redirectChain=[];constructor(){}}var pn;function fn(e){const t=[];for(const r in e){const i=e[r];if(!Object.is(i,void 0)){const e=Array.isArray(i)?i:[i];t.push(...e.map((e=>({name:r,value:e+""}))))}}return t}!function(e){e.Abort="abort",e.Respond="respond",e.Continue="continue",e.Disabled="disabled",e.None="none",e.AlreadyHandled="already-handled"}(pn||(pn={}));const yn={100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Switch Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"};class mn extends hn{id;#ue;#zr;#Ur;#Gr=!1;#Te;#Qr;#Jr;#Xr=!1;#Yr;#Zr={};#kr;#ei;#ti=null;#ri=null;#ii={action:pn.None};#ni;#si;get client(){return this.#ue}constructor(e,t,r,i,n,s){super(),this.#ue=e,this.id=n.requestId,this.#zr=n.requestId===n.loaderId&&"Document"===n.type,this._interceptionId=r,this.#Ur=i,this.#Te=n.request.url,this.#Qr=(n.type||"other").toLowerCase(),this.#Jr=n.request.method,this.#Yr=n.request.postData,this.#Xr=n.request.hasPostData??!1,this.#kr=t,this._redirectChain=s,this.#ei={},this.#ni=[],this.#si=n.initiator;for(const[e,t]of Object.entries(n.request.headers))this.#Zr[e.toLowerCase()]=t}url(){return this.#Te}continueRequestOverrides(){return st(this.#Ur,"Request Interception is not enabled!"),this.#ei}responseForRequest(){return st(this.#Ur,"Request Interception is not enabled!"),this.#ti}abortErrorReason(){return st(this.#Ur,"Request Interception is not enabled!"),this.#ri}interceptResolutionState(){return this.#Ur?this.#Gr?{action:pn.AlreadyHandled}:{...this.#ii}:{action:pn.Disabled}}isInterceptResolutionHandled(){return this.#Gr}enqueueInterceptAction(e){this.#ni.push(e)}async finalizeInterceptions(){await this.#ni.reduce(((e,t)=>e.then(t)),Promise.resolve());const{action:e}=this.interceptResolutionState();switch(e){case"abort":return await this.#ai(this.#ri);case"respond":if(null===this.#ti)throw new Error("Response is missing for the interception");return await this.#oi(this.#ti);case"continue":return await this.#ci(this.#ei)}}resourceType(){return this.#Qr}method(){return this.#Jr}postData(){return this.#Yr}hasPostData(){return this.#Xr}async fetchPostData(){try{return(await this.#ue.send("Network.getRequestPostData",{requestId:this.id})).postData}catch(e){return void gt(e)}}headers(){return this.#Zr}response(){return this._response}frame(){return this.#kr}isNavigationRequest(){return this.#zr}initiator(){return this.#si}redirectChain(){return this._redirectChain.slice()}failure(){return this._failureText?{errorText:this._failureText}:null}async continue(e={},t){if(!this.#Te.startsWith("data:")){if(st(this.#Ur,"Request Interception is not enabled!"),st(!this.#Gr,"Request is already handled!"),void 0===t)return await this.#ci(e);if(this.#ei=e,void 0===this.#ii.priority||t>this.#ii.priority)this.#ii={action:pn.Continue,priority:t};else if(t===this.#ii.priority){if("abort"===this.#ii.action||"respond"===this.#ii.action)return;this.#ii.action=pn.Continue}}}async#ci(e={}){const{url:t,method:r,postData:i,headers:n}=e;this.#Gr=!0;const s=i?Buffer.from(i).toString("base64"):void 0;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.continueRequest");await this.#ue.send("Fetch.continueRequest",{requestId:this._interceptionId,url:t,method:r,postData:s,headers:n?fn(n):void 0}).catch((e=>(this.#Gr=!1,wn(e))))}async respond(e,t){if(!this.#Te.startsWith("data:")){if(st(this.#Ur,"Request Interception is not enabled!"),st(!this.#Gr,"Request is already handled!"),void 0===t)return await this.#oi(e);if(this.#ti=e,void 0===this.#ii.priority||t>this.#ii.priority)this.#ii={action:pn.Respond,priority:t};else if(t===this.#ii.priority){if("abort"===this.#ii.action)return;this.#ii.action=pn.Respond}}}async#oi(e){this.#Gr=!0;const t=e.body&&kt(e.body)?Buffer.from(e.body):e.body||null,r={};if(e.headers)for(const t of Object.keys(e.headers)){const i=e.headers[t];r[t.toLowerCase()]=Array.isArray(i)?i.map((e=>String(e))):String(i)}e.contentType&&(r["content-type"]=e.contentType),t&&!("content-length"in r)&&(r["content-length"]=String(Buffer.byteLength(t)));const i=e.status||200;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.fulfillRequest");await this.#ue.send("Fetch.fulfillRequest",{requestId:this._interceptionId,responseCode:i,responsePhrase:yn[i],responseHeaders:fn(r),body:t?t.toString("base64"):void 0}).catch((e=>(this.#Gr=!1,wn(e))))}async abort(e="failed",t){if(this.#Te.startsWith("data:"))return;const r=gn[e];if(st(r,"Unknown error code: "+e),st(this.#Ur,"Request Interception is not enabled!"),st(!this.#Gr,"Request is already handled!"),void 0===t)return await this.#ai(r);this.#ri=r,(void 0===this.#ii.priority||t>=this.#ii.priority)&&(this.#ii={action:pn.Abort,priority:t})}async#ai(e){if(this.#Gr=!0,void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.failRequest");await this.#ue.send("Fetch.failRequest",{requestId:this._interceptionId,errorReason:e||"Failed"}).catch(wn)}}const gn={aborted:"Aborted",accessdenied:"AccessDenied",addressunreachable:"AddressUnreachable",blockedbyclient:"BlockedByClient",blockedbyresponse:"BlockedByResponse",connectionaborted:"ConnectionAborted",connectionclosed:"ConnectionClosed",connectionfailed:"ConnectionFailed",connectionrefused:"ConnectionRefused",connectionreset:"ConnectionReset",internetdisconnected:"InternetDisconnected",namenotresolved:"NameNotResolved",timedout:"TimedOut",failed:"Failed"};async function wn(e){if(["Invalid header"].includes(e.originalMessage))throw e;gt(e)}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class vn{constructor(){}ok(){const e=this.status();return 0===e||e>=200&&e<=299}async text(){return(await this.buffer()).toString("utf8")}async json(){const e=await this.text();return JSON.parse(e)}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class bn{#li;#di;#ui;#hi;#pi;#fi;constructor(e){this.#li=e.subjectName,this.#di=e.issuer,this.#ui=e.validFrom,this.#hi=e.validTo,this.#pi=e.protocol,this.#fi=e.sanList}issuer(){return this.#di}validFrom(){return this.#ui}validTo(){return this.#hi}protocol(){return this.#pi}subjectName(){return this.#li}subjectAlternativeNames(){return this.#fi}}class kn extends vn{#ue;#yi;#mi=null;#gi=$t.create();#wi;#vi;#bi;#Te;#ki;#Si;#Zr={};#Ci;#Ti;constructor(e,t,r,i){super(),this.#ue=e,this.#yi=t,this.#wi={ip:r.remoteIPAddress,port:r.remotePort},this.#bi=this.#Ei(i)||r.statusText,this.#Te=t.url(),this.#ki=!!r.fromDiskCache,this.#Si=!!r.fromServiceWorker,this.#vi=i?i.statusCode:r.status;const n=i?i.headers:r.headers;for(const[e,t]of Object.entries(n))this.#Zr[e.toLowerCase()]=t;this.#Ci=r.securityDetails?new bn(r.securityDetails):null,this.#Ti=r.timing||null}#Ei(e){if(!e||!e.headersText)return;const t=e.headersText.split("\r",1)[0];if(!t)return;const r=t.match(/[^ ]* [^ ]* (.*)/);if(!r)return;const i=r[1];return i||void 0}_resolveBody(e){return e?this.#gi.reject(e):this.#gi.resolve()}remoteAddress(){return this.#wi}url(){return this.#Te}status(){return this.#vi}statusText(){return this.#bi}headers(){return this.#Zr}securityDetails(){return this.#Ci}timing(){return this.#Ti}buffer(){return this.#mi||(this.#mi=this.#gi.valueOrThrow().then((async()=>{try{const e=await this.#ue.send("Network.getResponseBody",{requestId:this.#yi.id});return Buffer.from(e.body,e.base64Encoded?"base64":"utf8")}catch(e){if(e instanceof pt&&"No resource with given identifier found"===e.originalMessage)throw new pt("Could not load body for this request. This might happen if the request is a preflight request.");throw e}}))),this.#mi}request(){return this.#yi}fromCache(){return this.#ki||this.#yi._fromMemoryCache}fromServiceWorker(){return this.#Si}frame(){return this.#yi.frame()}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Sn{#Ii=new Map;#xi=new Map;#Fi=new Map;#Ri=new Map;#_i=new Map;#Pi=new Map;forget(e){this.#Ii.delete(e),this.#xi.delete(e),this.#Pi.delete(e),this.#_i.delete(e),this.#Ri.delete(e)}responseExtraInfo(e){return this.#Ri.has(e)||this.#Ri.set(e,[]),this.#Ri.get(e)}queuedRedirectInfo(e){return this.#_i.has(e)||this.#_i.set(e,[]),this.#_i.get(e)}queueRedirectInfo(e,t){this.queuedRedirectInfo(e).push(t)}takeQueuedRedirectInfo(e){return this.queuedRedirectInfo(e).shift()}inFlightRequestsCount(){let e=0;for(const t of this.#Fi.values())t.response()||e++;return e}storeRequestWillBeSent(e,t){this.#Ii.set(e,t)}getRequestWillBeSent(e){return this.#Ii.get(e)}forgetRequestWillBeSent(e){this.#Ii.delete(e)}getRequestPaused(e){return this.#xi.get(e)}forgetRequestPaused(e){this.#xi.delete(e)}storeRequestPaused(e,t){this.#xi.set(e,t)}getRequest(e){return this.#Fi.get(e)}storeRequest(e,t){this.#Fi.set(e,t)}forgetRequest(e){this.#Fi.delete(e)}getQueuedEventGroup(e){return this.#Pi.get(e)}queueEventGroup(e,t){this.#Pi.set(e,t)}forgetQueuedEventGroup(e){this.#Pi.delete(e)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Cn extends it{#Di;#qt;#Mi=new Sn;#Ai;#qi;#Oi=new Set;#Ni=!1;#Li=!1;#Bi;#ji;#Hi;#$i;#i=[["Fetch.requestPaused",this.#Ki],["Fetch.authRequired",this.#Vi],["Network.requestWillBeSent",this.#Wi],["Network.requestServedFromCache",this.#zi],["Network.responseReceived",this.#Ui],["Network.loadingFinished",this.#Gi],["Network.loadingFailed",this.#Qi],["Network.responseReceivedExtraInfo",this.#Ji],[Bt.Disconnected,this.#Xi]];#Yi=new Map;constructor(e,t){super(),this.#Di=e,this.#qt=t}async addClient(e){if(this.#Yi.has(e))return;const t=new tt;this.#Yi.set(e,t);for(const[r,i]of this.#i)t.use(new nt(e,r,(t=>i.bind(this)(e,t))));await Promise.all([this.#Di?e.send("Security.setIgnoreCertificateErrors",{ignore:!0}):null,e.send("Network.enable"),this.#Zi(e),this.#en(e),this.#tn(e),this.#rn(e),this.#in(e)])}async#Xi(e){this.#Yi.get(e)?.dispose(),this.#Yi.delete(e)}async authenticate(e){this.#qi=e;const t=this.#Ni||!!this.#qi;t!==this.#Li&&(this.#Li=t,await this.#nn(this.#rn.bind(this)))}async setExtraHTTPHeaders(e){this.#Ai={};for(const t of Object.keys(e)){const r=e[t];st(kt(r),`Expected value of header "${t}" to be String, but "${typeof r}" is found.`),this.#Ai[t.toLowerCase()]=r}await this.#nn(this.#Zi.bind(this))}async#Zi(e){void 0!==this.#Ai&&await e.send("Network.setExtraHTTPHeaders",{headers:this.#Ai})}extraHTTPHeaders(){return Object.assign({},this.#Ai)}inFlightRequestsCount(){return this.#Mi.inFlightRequestsCount()}async setOfflineMode(e){this.#ji||(this.#ji={offline:!1,upload:-1,download:-1,latency:0}),this.#ji.offline=e,await this.#nn(this.#en.bind(this))}async emulateNetworkConditions(e){this.#ji||(this.#ji={offline:!1,upload:-1,download:-1,latency:0}),this.#ji.upload=e?e.upload:-1,this.#ji.download=e?e.download:-1,this.#ji.latency=e?e.latency:0,await this.#nn(this.#en.bind(this))}async#nn(e){await Promise.all(Array.from(this.#Yi.keys()).map((t=>e(t))))}async#en(e){void 0!==this.#ji&&await e.send("Network.emulateNetworkConditions",{offline:this.#ji.offline,latency:this.#ji.latency,uploadThroughput:this.#ji.upload,downloadThroughput:this.#ji.download})}async setUserAgent(e,t){this.#Hi=e,this.#$i=t,await this.#nn(this.#in.bind(this))}async#in(e){void 0!==this.#Hi&&await e.send("Network.setUserAgentOverride",{userAgent:this.#Hi,userAgentMetadata:this.#$i})}async setCacheEnabled(e){this.#Bi=!e,await this.#nn(this.#tn.bind(this))}async setRequestInterception(e){this.#Ni=e;const t=this.#Ni||!!this.#qi;t!==this.#Li&&(this.#Li=t,await this.#nn(this.#rn.bind(this)))}async#rn(e){void 0===this.#Bi&&(this.#Bi=!1),this.#Li?await Promise.all([this.#tn(e),e.send("Fetch.enable",{handleAuthRequests:!0,patterns:[{urlPattern:"*"}]})]):await Promise.all([this.#tn(e),e.send("Fetch.disable")])}async#tn(e){void 0!==this.#Bi&&await e.send("Network.setCacheDisabled",{cacheDisabled:this.#Bi})}#Wi(e,t){if(!this.#Ni||t.request.url.startsWith("data:"))this.#Or(e,t,void 0);else{const{requestId:r}=t;this.#Mi.storeRequestWillBeSent(r,t);const i=this.#Mi.getRequestPaused(r);if(i){const{requestId:n}=i;this.#sn(t,i),this.#Or(e,t,n),this.#Mi.forgetRequestPaused(r)}}}#Vi(e,t){let r="Default";this.#Oi.has(t.requestId)?r="CancelAuth":this.#qi&&(r="ProvideCredentials",this.#Oi.add(t.requestId));const{username:i,password:n}=this.#qi||{username:void 0,password:void 0};e.send("Fetch.continueWithAuth",{requestId:t.requestId,authChallengeResponse:{response:r,username:i,password:n}}).catch(gt)}#Ki(e,t){!this.#Ni&&this.#Li&&e.send("Fetch.continueRequest",{requestId:t.requestId}).catch(gt);const{networkId:r,requestId:i}=t;if(!r)return void this.#an(e,t);const n=(()=>{const e=this.#Mi.getRequestWillBeSent(r);if(!e||e.request.url===t.request.url&&e.request.method===t.request.method)return e;this.#Mi.forgetRequestWillBeSent(r)})();n?(this.#sn(n,t),this.#Or(e,n,i)):this.#Mi.storeRequestPaused(r,t)}#sn(e,t){e.request.headers={...e.request.headers,...t.request.headers}}#an(e,t){const r=t.frameId?this.#qt.frame(t.frameId):null,i=new mn(e,r,t.requestId,this.#Ni,t,[]);this.emit(Er.Request,i),i.finalizeInterceptions()}#Or(e,t,r){let i=[];if(t.redirectResponse){let n=null;if(t.redirectHasExtraInfo&&(n=this.#Mi.responseExtraInfo(t.requestId).shift(),!n))return void this.#Mi.queueRedirectInfo(t.requestId,{event:t,fetchRequestId:r});const s=this.#Mi.getRequest(t.requestId);s&&(this.#on(e,s,t.redirectResponse,n),i=s._redirectChain)}const n=t.frameId?this.#qt.frame(t.frameId):null,s=new mn(e,n,r,this.#Ni,t,i);this.#Mi.storeRequest(t.requestId,s),this.emit(Er.Request,s),s.finalizeInterceptions()}#zi(e,t){const r=this.#Mi.getRequest(t.requestId);r&&(r._fromMemoryCache=!0),this.emit(Er.RequestServedFromCache,r)}#on(e,t,r,i){const n=new kn(e,t,r,i);t._response=n,t._redirectChain.push(t),n._resolveBody(new Error("Response body is unavailable for redirect responses")),this.#cn(t,!1),this.emit(Er.Response,n),this.emit(Er.RequestFinished,t)}#ln(e,t,r){const i=this.#Mi.getRequest(t.requestId);if(!i)return;this.#Mi.responseExtraInfo(t.requestId).length&&gt(new Error("Unexpected extraInfo events for request "+t.requestId)),t.response.fromDiskCache&&(r=null);const n=new kn(e,i,t.response,r);i._response=n,this.emit(Er.Response,n)}#Ui(e,t){const r=this.#Mi.getRequest(t.requestId);let i=null;!r||r._fromMemoryCache||!t.hasExtraInfo||(i=this.#Mi.responseExtraInfo(t.requestId).shift(),i)?this.#ln(e,t,i):this.#Mi.queueEventGroup(t.requestId,{responseReceivedEvent:t})}#Ji(e,t){const r=this.#Mi.takeQueuedRedirectInfo(t.requestId);if(r)return this.#Mi.responseExtraInfo(t.requestId).push(t),void this.#Or(e,r.event,r.fetchRequestId);const i=this.#Mi.getQueuedEventGroup(t.requestId);if(i)return this.#Mi.forgetQueuedEventGroup(t.requestId),this.#ln(e,i.responseReceivedEvent,t),i.loadingFinishedEvent&&this.#dn(i.loadingFinishedEvent),void(i.loadingFailedEvent&&this.#un(i.loadingFailedEvent));this.#Mi.responseExtraInfo(t.requestId).push(t)}#cn(e,t){const r=e.id,i=e._interceptionId;this.#Mi.forgetRequest(r),void 0!==i&&this.#Oi.delete(i),t&&this.#Mi.forget(r)}#Gi(e,t){const r=this.#Mi.getQueuedEventGroup(t.requestId);r?r.loadingFinishedEvent=t:this.#dn(t)}#dn(e){const t=this.#Mi.getRequest(e.requestId);t&&(t.response()&&t.response()?._resolveBody(),this.#cn(t,!0),this.emit(Er.RequestFinished,t))}#Qi(e,t){const r=this.#Mi.getQueuedEventGroup(t.requestId);r?r.loadingFailedEvent=t:this.#un(t)}#un(e){const t=this.#Mi.getRequest(e.requestId);if(!t)return;t._failureText=e.errorText;const r=t.response();r&&r._resolveBody(),this.#cn(t,!0),this.emit(Er.RequestFailed,t)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Tn extends it{#hn;#pn;#tr;#fn=new Map;#yn=new Set;#ue;_frameTree=new un;#mn=new Set;#gn=new WeakMap;#wn;get timeoutSettings(){return this.#tr}get networkManager(){return this.#pn}get client(){return this.#ue}constructor(e,t,r,i){super(),this.#ue=e,this.#hn=t,this.#pn=new Cn(r,this),this.#tr=i,this.setupEventListeners(this.#ue),e.once(Bt.Disconnected,(()=>{this.#vn().catch(gt)}))}async#vn(){const e=this._frameTree.getMainFrame();if(!e)return;for(const t of e.childFrames())this.#bn(t);const t=$t.create({timeout:100,message:"Frame was not swapped"});e.once(ji.FrameSwappedByActivation,(()=>{t.resolve()}));try{await t.valueOrThrow()}catch(t){this.#bn(e)}}async swapFrameTree(e){this.#$e(this.#ue),this.#ue=e,st(this.#ue instanceof Qt,"CDPSession is not an instance of CDPSessionImpl.");const t=this._frameTree.getMainFrame();t&&(this.#mn.add(this.#ue._target()._targetId),this._frameTree.removeFrame(t),t.updateId(this.#ue._target()._targetId),t.mainRealm().clearContext(),t.isolatedRealm().clearContext(),this._frameTree.addFrame(t),t.updateClient(e,!0)),this.setupEventListeners(e),e.once(Bt.Disconnected,(()=>{this.#vn().catch(gt)})),await this.initialize(e),await this.#pn.addClient(e),t&&t.emit(ji.FrameSwappedByActivation,void 0)}async registerSpeculativeSession(e){await this.#pn.addClient(e)}setupEventListeners(e){e.on("Page.frameAttached",(async t=>{await(this.#wn?.valueOrThrow()),this.#kn(e,t.frameId,t.parentFrameId)})),e.on("Page.frameNavigated",(async e=>{this.#mn.add(e.frame.id),await(this.#wn?.valueOrThrow()),this.#Sn(e.frame,e.type)})),e.on("Page.navigatedWithinDocument",(async e=>{await(this.#wn?.valueOrThrow()),this.#Cn(e.frameId,e.url)})),e.on("Page.frameDetached",(async e=>{await(this.#wn?.valueOrThrow()),this.#qr(e.frameId,e.reason)})),e.on("Page.frameStartedLoading",(async e=>{await(this.#wn?.valueOrThrow()),this.#Tn(e.frameId)})),e.on("Page.frameStoppedLoading",(async e=>{await(this.#wn?.valueOrThrow()),this.#En(e.frameId)})),e.on("Runtime.executionContextCreated",(async t=>{await(this.#wn?.valueOrThrow()),this.#In(t.context,e)})),e.on("Runtime.executionContextDestroyed",(async t=>{await(this.#wn?.valueOrThrow()),this.#xn(t.executionContextId,e)})),e.on("Runtime.executionContextsCleared",(async()=>{await(this.#wn?.valueOrThrow()),this.#$e(e)})),e.on("Page.lifecycleEvent",(async e=>{await(this.#wn?.valueOrThrow()),this.#Fn(e)}))}async initialize(e){try{this.#wn?.resolve(),this.#wn=$t.create(),await Promise.all([this.#pn.addClient(e),e.send("Page.enable"),e.send("Page.getFrameTree").then((({frameTree:t})=>{this.#Rn(e,t),this.#wn?.resolve()})),e.send("Page.setLifecycleEventsEnabled",{enabled:!0}),e.send("Runtime.enable").then((()=>this.#_n(e,Rt)))])}catch(e){if(this.#wn?.resolve(),Vt(e)&&Br(e))return;throw e}}executionContextById(e,t=this.#ue){const r=this.getExecutionContextById(e,t);return st(r,"INTERNAL ERROR: missing context with id = "+e),r}getExecutionContextById(e,t=this.#ue){return this.#fn.get(`${t.id()}:${e}`)}page(){return this.#hn}mainFrame(){const e=this._frameTree.getMainFrame();return st(e,"Requesting main frame too early!"),e}frames(){return Array.from(this._frameTree.frames())}frame(e){return this._frameTree.getById(e)||null}onAttachedToTarget(e){if("iframe"!==e._getTargetInfo().type)return;const t=this.frame(e._getTargetInfo().targetId);t&&t.updateClient(e._session()),this.setupEventListeners(e._session()),this.initialize(e._session())}_deviceRequestPromptManager(e){let t=this.#gn.get(e);return void 0===t&&(t=new Qi(e,this.#tr),this.#gn.set(e,t)),t}#Fn(e){const t=this.frame(e.frameId);t&&(t._onLifecycleEvent(e.loaderId,e.name),this.emit(sn.LifecycleEvent,t),t.emit(ji.LifecycleEvent,void 0))}#Tn(e){const t=this.frame(e);t&&t._onLoadingStarted()}#En(e){const t=this.frame(e);t&&(t._onLoadingStopped(),this.emit(sn.LifecycleEvent,t),t.emit(ji.LifecycleEvent,void 0))}#Rn(e,t){if(t.frame.parentId&&this.#kn(e,t.frame.id,t.frame.parentId),this.#mn.has(t.frame.id)?this.#mn.delete(t.frame.id):this.#Sn(t.frame,"Navigation"),t.childFrames)for(const r of t.childFrames)this.#Rn(e,r)}#kn(e,t,r){let i=this.frame(t);i?e&&i.isOOPFrame()&&i.updateClient(e):(i=new dn(this,t,r,e),this._frameTree.addFrame(i),this.emit(sn.FrameAttached,i))}async#Sn(e,t){const r=e.id,i=!e.parentId;let n=this._frameTree.getById(r);if(n)for(const e of n.childFrames())this.#bn(e);i&&(n?(this._frameTree.removeFrame(n),n._id=r):n=new dn(this,r,void 0,this.#ue),this._frameTree.addFrame(n)),n=await this._frameTree.waitForFrame(r),n._navigated(e),this.emit(sn.FrameNavigated,n),n.emit(ji.FrameNavigated,t)}async#_n(e,t){const r=`${e.id()}:${t}`;this.#yn.has(r)||(await e.send("Page.addScriptToEvaluateOnNewDocument",{source:`//# sourceURL=${vt.INTERNAL_URL}`,worldName:t}),await Promise.all(this.frames().filter((t=>t.client===e)).map((r=>e.send("Page.createIsolatedWorld",{frameId:r._id,worldName:t,grantUniveralAccess:!0}).catch(gt)))),this.#yn.add(r))}#Cn(e,t){const r=this.frame(e);r&&(r._navigatedWithinDocument(t),this.emit(sn.FrameNavigatedWithinDocument,r),r.emit(ji.FrameNavigatedWithinDocument,void 0),this.emit(sn.FrameNavigated,r),r.emit(ji.FrameNavigated,"Navigation"))}#qr(e,t){const r=this.frame(e);if(r)switch(t){case"remove":this.#bn(r);break;case"swap":this.emit(sn.FrameSwapped,r),r.emit(ji.FrameSwapped,void 0)}}#In(e,t){const r=e.auxData,i=r&&r.frameId,n="string"==typeof i?this.frame(i):void 0;let s;if(n){if(n.client!==t)return;e.auxData&&e.auxData.isDefault?s=n.worlds[rn]:e.name!==Rt||n.worlds[nn].hasContext()||(s=n.worlds[nn])}if(!s)return;const a=new Oi(n?.client||this.#ue,e,s);s&&s.setContext(a);const o=`${t.id()}:${e.id}`;this.#fn.set(o,a)}#xn(e,t){const r=`${t.id()}:${e}`,i=this.#fn.get(r);i&&(this.#fn.delete(r),i._world&&i._world.clearContext())}#$e(e){for(const[t,r]of this.#fn.entries())r._client===e&&(r._world&&r._world.clearContext(),this.#fn.delete(t))}#bn(e){for(const t of e.childFrames())this.#bn(t);e[Ze](),this._frameTree.removeFrame(e),this.emit(sn.FrameDetached,e),e.emit(ji.FrameDetached,e)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class En{constructor(){}}const In=Object.freeze({Left:"left",Right:"right",Middle:"middle",Back:"back",Forward:"forward"});class xn{constructor(){}}class Fn{constructor(){}async tap(e,t){await this.touchStart(e,t),await this.touchEnd()}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Rn={0:{keyCode:48,key:"0",code:"Digit0"},1:{keyCode:49,key:"1",code:"Digit1"},2:{keyCode:50,key:"2",code:"Digit2"},3:{keyCode:51,key:"3",code:"Digit3"},4:{keyCode:52,key:"4",code:"Digit4"},5:{keyCode:53,key:"5",code:"Digit5"},6:{keyCode:54,key:"6",code:"Digit6"},7:{keyCode:55,key:"7",code:"Digit7"},8:{keyCode:56,key:"8",code:"Digit8"},9:{keyCode:57,key:"9",code:"Digit9"},Power:{key:"Power",code:"Power"},Eject:{key:"Eject",code:"Eject"},Abort:{keyCode:3,code:"Abort",key:"Cancel"},Help:{keyCode:6,code:"Help",key:"Help"},Backspace:{keyCode:8,code:"Backspace",key:"Backspace"},Tab:{keyCode:9,code:"Tab",key:"Tab"},Numpad5:{keyCode:12,shiftKeyCode:101,key:"Clear",code:"Numpad5",shiftKey:"5",location:3},NumpadEnter:{keyCode:13,code:"NumpadEnter",key:"Enter",text:"\r",location:3},Enter:{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\r":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\n":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},ShiftLeft:{keyCode:16,code:"ShiftLeft",key:"Shift",location:1},ShiftRight:{keyCode:16,code:"ShiftRight",key:"Shift",location:2},ControlLeft:{keyCode:17,code:"ControlLeft",key:"Control",location:1},ControlRight:{keyCode:17,code:"ControlRight",key:"Control",location:2},AltLeft:{keyCode:18,code:"AltLeft",key:"Alt",location:1},AltRight:{keyCode:18,code:"AltRight",key:"Alt",location:2},Pause:{keyCode:19,code:"Pause",key:"Pause"},CapsLock:{keyCode:20,code:"CapsLock",key:"CapsLock"},Escape:{keyCode:27,code:"Escape",key:"Escape"},Convert:{keyCode:28,code:"Convert",key:"Convert"},NonConvert:{keyCode:29,code:"NonConvert",key:"NonConvert"},Space:{keyCode:32,code:"Space",key:" "},Numpad9:{keyCode:33,shiftKeyCode:105,key:"PageUp",code:"Numpad9",shiftKey:"9",location:3},PageUp:{keyCode:33,code:"PageUp",key:"PageUp"},Numpad3:{keyCode:34,shiftKeyCode:99,key:"PageDown",code:"Numpad3",shiftKey:"3",location:3},PageDown:{keyCode:34,code:"PageDown",key:"PageDown"},End:{keyCode:35,code:"End",key:"End"},Numpad1:{keyCode:35,shiftKeyCode:97,key:"End",code:"Numpad1",shiftKey:"1",location:3},Home:{keyCode:36,code:"Home",key:"Home"},Numpad7:{keyCode:36,shiftKeyCode:103,key:"Home",code:"Numpad7",shiftKey:"7",location:3},ArrowLeft:{keyCode:37,code:"ArrowLeft",key:"ArrowLeft"},Numpad4:{keyCode:37,shiftKeyCode:100,key:"ArrowLeft",code:"Numpad4",shiftKey:"4",location:3},Numpad8:{keyCode:38,shiftKeyCode:104,key:"ArrowUp",code:"Numpad8",shiftKey:"8",location:3},ArrowUp:{keyCode:38,code:"ArrowUp",key:"ArrowUp"},ArrowRight:{keyCode:39,code:"ArrowRight",key:"ArrowRight"},Numpad6:{keyCode:39,shiftKeyCode:102,key:"ArrowRight",code:"Numpad6",shiftKey:"6",location:3},Numpad2:{keyCode:40,shiftKeyCode:98,key:"ArrowDown",code:"Numpad2",shiftKey:"2",location:3},ArrowDown:{keyCode:40,code:"ArrowDown",key:"ArrowDown"},Select:{keyCode:41,code:"Select",key:"Select"},Open:{keyCode:43,code:"Open",key:"Execute"},PrintScreen:{keyCode:44,code:"PrintScreen",key:"PrintScreen"},Insert:{keyCode:45,code:"Insert",key:"Insert"},Numpad0:{keyCode:45,shiftKeyCode:96,key:"Insert",code:"Numpad0",shiftKey:"0",location:3},Delete:{keyCode:46,code:"Delete",key:"Delete"},NumpadDecimal:{keyCode:46,shiftKeyCode:110,code:"NumpadDecimal",key:"\0",shiftKey:".",location:3},Digit0:{keyCode:48,code:"Digit0",shiftKey:")",key:"0"},Digit1:{keyCode:49,code:"Digit1",shiftKey:"!",key:"1"},Digit2:{keyCode:50,code:"Digit2",shiftKey:"@",key:"2"},Digit3:{keyCode:51,code:"Digit3",shiftKey:"#",key:"3"},Digit4:{keyCode:52,code:"Digit4",shiftKey:"$",key:"4"},Digit5:{keyCode:53,code:"Digit5",shiftKey:"%",key:"5"},Digit6:{keyCode:54,code:"Digit6",shiftKey:"^",key:"6"},Digit7:{keyCode:55,code:"Digit7",shiftKey:"&",key:"7"},Digit8:{keyCode:56,code:"Digit8",shiftKey:"*",key:"8"},Digit9:{keyCode:57,code:"Digit9",shiftKey:"(",key:"9"},KeyA:{keyCode:65,code:"KeyA",shiftKey:"A",key:"a"},KeyB:{keyCode:66,code:"KeyB",shiftKey:"B",key:"b"},KeyC:{keyCode:67,code:"KeyC",shiftKey:"C",key:"c"},KeyD:{keyCode:68,code:"KeyD",shiftKey:"D",key:"d"},KeyE:{keyCode:69,code:"KeyE",shiftKey:"E",key:"e"},KeyF:{keyCode:70,code:"KeyF",shiftKey:"F",key:"f"},KeyG:{keyCode:71,code:"KeyG",shiftKey:"G",key:"g"},KeyH:{keyCode:72,code:"KeyH",shiftKey:"H",key:"h"},KeyI:{keyCode:73,code:"KeyI",shiftKey:"I",key:"i"},KeyJ:{keyCode:74,code:"KeyJ",shiftKey:"J",key:"j"},KeyK:{keyCode:75,code:"KeyK",shiftKey:"K",key:"k"},KeyL:{keyCode:76,code:"KeyL",shiftKey:"L",key:"l"},KeyM:{keyCode:77,code:"KeyM",shiftKey:"M",key:"m"},KeyN:{keyCode:78,code:"KeyN",shiftKey:"N",key:"n"},KeyO:{keyCode:79,code:"KeyO",shiftKey:"O",key:"o"},KeyP:{keyCode:80,code:"KeyP",shiftKey:"P",key:"p"},KeyQ:{keyCode:81,code:"KeyQ",shiftKey:"Q",key:"q"},KeyR:{keyCode:82,code:"KeyR",shiftKey:"R",key:"r"},KeyS:{keyCode:83,code:"KeyS",shiftKey:"S",key:"s"},KeyT:{keyCode:84,code:"KeyT",shiftKey:"T",key:"t"},KeyU:{keyCode:85,code:"KeyU",shiftKey:"U",key:"u"},KeyV:{keyCode:86,code:"KeyV",shiftKey:"V",key:"v"},KeyW:{keyCode:87,code:"KeyW",shiftKey:"W",key:"w"},KeyX:{keyCode:88,code:"KeyX",shiftKey:"X",key:"x"},KeyY:{keyCode:89,code:"KeyY",shiftKey:"Y",key:"y"},KeyZ:{keyCode:90,code:"KeyZ",shiftKey:"Z",key:"z"},MetaLeft:{keyCode:91,code:"MetaLeft",key:"Meta",location:1},MetaRight:{keyCode:92,code:"MetaRight",key:"Meta",location:2},ContextMenu:{keyCode:93,code:"ContextMenu",key:"ContextMenu"},NumpadMultiply:{keyCode:106,code:"NumpadMultiply",key:"*",location:3},NumpadAdd:{keyCode:107,code:"NumpadAdd",key:"+",location:3},NumpadSubtract:{keyCode:109,code:"NumpadSubtract",key:"-",location:3},NumpadDivide:{keyCode:111,code:"NumpadDivide",key:"/",location:3},F1:{keyCode:112,code:"F1",key:"F1"},F2:{keyCode:113,code:"F2",key:"F2"},F3:{keyCode:114,code:"F3",key:"F3"},F4:{keyCode:115,code:"F4",key:"F4"},F5:{keyCode:116,code:"F5",key:"F5"},F6:{keyCode:117,code:"F6",key:"F6"},F7:{keyCode:118,code:"F7",key:"F7"},F8:{keyCode:119,code:"F8",key:"F8"},F9:{keyCode:120,code:"F9",key:"F9"},F10:{keyCode:121,code:"F10",key:"F10"},F11:{keyCode:122,code:"F11",key:"F11"},F12:{keyCode:123,code:"F12",key:"F12"},F13:{keyCode:124,code:"F13",key:"F13"},F14:{keyCode:125,code:"F14",key:"F14"},F15:{keyCode:126,code:"F15",key:"F15"},F16:{keyCode:127,code:"F16",key:"F16"},F17:{keyCode:128,code:"F17",key:"F17"},F18:{keyCode:129,code:"F18",key:"F18"},F19:{keyCode:130,code:"F19",key:"F19"},F20:{keyCode:131,code:"F20",key:"F20"},F21:{keyCode:132,code:"F21",key:"F21"},F22:{keyCode:133,code:"F22",key:"F22"},F23:{keyCode:134,code:"F23",key:"F23"},F24:{keyCode:135,code:"F24",key:"F24"},NumLock:{keyCode:144,code:"NumLock",key:"NumLock"},ScrollLock:{keyCode:145,code:"ScrollLock",key:"ScrollLock"},AudioVolumeMute:{keyCode:173,code:"AudioVolumeMute",key:"AudioVolumeMute"},AudioVolumeDown:{keyCode:174,code:"AudioVolumeDown",key:"AudioVolumeDown"},AudioVolumeUp:{keyCode:175,code:"AudioVolumeUp",key:"AudioVolumeUp"},MediaTrackNext:{keyCode:176,code:"MediaTrackNext",key:"MediaTrackNext"},MediaTrackPrevious:{keyCode:177,code:"MediaTrackPrevious",key:"MediaTrackPrevious"},MediaStop:{keyCode:178,code:"MediaStop",key:"MediaStop"},MediaPlayPause:{keyCode:179,code:"MediaPlayPause",key:"MediaPlayPause"},Semicolon:{keyCode:186,code:"Semicolon",shiftKey:":",key:";"},Equal:{keyCode:187,code:"Equal",shiftKey:"+",key:"="},NumpadEqual:{keyCode:187,code:"NumpadEqual",key:"=",location:3},Comma:{keyCode:188,code:"Comma",shiftKey:"<",key:","},Minus:{keyCode:189,code:"Minus",shiftKey:"_",key:"-"},Period:{keyCode:190,code:"Period",shiftKey:">",key:"."},Slash:{keyCode:191,code:"Slash",shiftKey:"?",key:"/"},Backquote:{keyCode:192,code:"Backquote",shiftKey:"~",key:"`"},BracketLeft:{keyCode:219,code:"BracketLeft",shiftKey:"{",key:"["},Backslash:{keyCode:220,code:"Backslash",shiftKey:"|",key:"\\"},BracketRight:{keyCode:221,code:"BracketRight",shiftKey:"}",key:"]"},Quote:{keyCode:222,code:"Quote",shiftKey:'"',key:"'"},AltGraph:{keyCode:225,code:"AltGraph",key:"AltGraph"},Props:{keyCode:247,code:"Props",key:"CrSel"},Cancel:{keyCode:3,key:"Cancel",code:"Abort"},Clear:{keyCode:12,key:"Clear",code:"Numpad5",location:3},Shift:{keyCode:16,key:"Shift",code:"ShiftLeft",location:1},Control:{keyCode:17,key:"Control",code:"ControlLeft",location:1},Alt:{keyCode:18,key:"Alt",code:"AltLeft",location:1},Accept:{keyCode:30,key:"Accept"},ModeChange:{keyCode:31,key:"ModeChange"}," ":{keyCode:32,key:" ",code:"Space"},Print:{keyCode:42,key:"Print"},Execute:{keyCode:43,key:"Execute",code:"Open"},"\0":{keyCode:46,key:"\0",code:"NumpadDecimal",location:3},a:{keyCode:65,key:"a",code:"KeyA"},b:{keyCode:66,key:"b",code:"KeyB"},c:{keyCode:67,key:"c",code:"KeyC"},d:{keyCode:68,key:"d",code:"KeyD"},e:{keyCode:69,key:"e",code:"KeyE"},f:{keyCode:70,key:"f",code:"KeyF"},g:{keyCode:71,key:"g",code:"KeyG"},h:{keyCode:72,key:"h",code:"KeyH"},i:{keyCode:73,key:"i",code:"KeyI"},j:{keyCode:74,key:"j",code:"KeyJ"},k:{keyCode:75,key:"k",code:"KeyK"},l:{keyCode:76,key:"l",code:"KeyL"},m:{keyCode:77,key:"m",code:"KeyM"},n:{keyCode:78,key:"n",code:"KeyN"},o:{keyCode:79,key:"o",code:"KeyO"},p:{keyCode:80,key:"p",code:"KeyP"},q:{keyCode:81,key:"q",code:"KeyQ"},r:{keyCode:82,key:"r",code:"KeyR"},s:{keyCode:83,key:"s",code:"KeyS"},t:{keyCode:84,key:"t",code:"KeyT"},u:{keyCode:85,key:"u",code:"KeyU"},v:{keyCode:86,key:"v",code:"KeyV"},w:{keyCode:87,key:"w",code:"KeyW"},x:{keyCode:88,key:"x",code:"KeyX"},y:{keyCode:89,key:"y",code:"KeyY"},z:{keyCode:90,key:"z",code:"KeyZ"},Meta:{keyCode:91,key:"Meta",code:"MetaLeft",location:1},"*":{keyCode:106,key:"*",code:"NumpadMultiply",location:3},"+":{keyCode:107,key:"+",code:"NumpadAdd",location:3},"-":{keyCode:109,key:"-",code:"NumpadSubtract",location:3},"/":{keyCode:111,key:"/",code:"NumpadDivide",location:3},";":{keyCode:186,key:";",code:"Semicolon"},"=":{keyCode:187,key:"=",code:"Equal"},",":{keyCode:188,key:",",code:"Comma"},".":{keyCode:190,key:".",code:"Period"},"`":{keyCode:192,key:"`",code:"Backquote"},"[":{keyCode:219,key:"[",code:"BracketLeft"},"\\":{keyCode:220,key:"\\",code:"Backslash"},"]":{keyCode:221,key:"]",code:"BracketRight"},"'":{keyCode:222,key:"'",code:"Quote"},Attn:{keyCode:246,key:"Attn"},CrSel:{keyCode:247,key:"CrSel",code:"Props"},ExSel:{keyCode:248,key:"ExSel"},EraseEof:{keyCode:249,key:"EraseEof"},Play:{keyCode:250,key:"Play"},ZoomOut:{keyCode:251,key:"ZoomOut"},")":{keyCode:48,key:")",code:"Digit0"},"!":{keyCode:49,key:"!",code:"Digit1"},"@":{keyCode:50,key:"@",code:"Digit2"},"#":{keyCode:51,key:"#",code:"Digit3"},$:{keyCode:52,key:"$",code:"Digit4"},"%":{keyCode:53,key:"%",code:"Digit5"},"^":{keyCode:54,key:"^",code:"Digit6"},"&":{keyCode:55,key:"&",code:"Digit7"},"(":{keyCode:57,key:"(",code:"Digit9"},A:{keyCode:65,key:"A",code:"KeyA"},B:{keyCode:66,key:"B",code:"KeyB"},C:{keyCode:67,key:"C",code:"KeyC"},D:{keyCode:68,key:"D",code:"KeyD"},E:{keyCode:69,key:"E",code:"KeyE"},F:{keyCode:70,key:"F",code:"KeyF"},G:{keyCode:71,key:"G",code:"KeyG"},H:{keyCode:72,key:"H",code:"KeyH"},I:{keyCode:73,key:"I",code:"KeyI"},J:{keyCode:74,key:"J",code:"KeyJ"},K:{keyCode:75,key:"K",code:"KeyK"},L:{keyCode:76,key:"L",code:"KeyL"},M:{keyCode:77,key:"M",code:"KeyM"},N:{keyCode:78,key:"N",code:"KeyN"},O:{keyCode:79,key:"O",code:"KeyO"},P:{keyCode:80,key:"P",code:"KeyP"},Q:{keyCode:81,key:"Q",code:"KeyQ"},R:{keyCode:82,key:"R",code:"KeyR"},S:{keyCode:83,key:"S",code:"KeyS"},T:{keyCode:84,key:"T",code:"KeyT"},U:{keyCode:85,key:"U",code:"KeyU"},V:{keyCode:86,key:"V",code:"KeyV"},W:{keyCode:87,key:"W",code:"KeyW"},X:{keyCode:88,key:"X",code:"KeyX"},Y:{keyCode:89,key:"Y",code:"KeyY"},Z:{keyCode:90,key:"Z",code:"KeyZ"},":":{keyCode:186,key:":",code:"Semicolon"},"<":{keyCode:188,key:"<",code:"Comma"},_:{keyCode:189,key:"_",code:"Minus"},">":{keyCode:190,key:">",code:"Period"},"?":{keyCode:191,key:"?",code:"Slash"},"~":{keyCode:192,key:"~",code:"Backquote"},"{":{keyCode:219,key:"{",code:"BracketLeft"},"|":{keyCode:220,key:"|",code:"Backslash"},"}":{keyCode:221,key:"}",code:"BracketRight"},'"':{keyCode:222,key:'"',code:"Quote"},SoftLeft:{key:"SoftLeft",code:"SoftLeft",location:4},SoftRight:{key:"SoftRight",code:"SoftRight",location:4},Camera:{keyCode:44,key:"Camera",code:"Camera",location:4},Call:{key:"Call",code:"Call",location:4},EndCall:{keyCode:95,key:"EndCall",code:"EndCall",location:4},VolumeDown:{keyCode:182,key:"VolumeDown",code:"VolumeDown",location:4},VolumeUp:{keyCode:183,key:"VolumeUp",code:"VolumeUp",location:4}};
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class _n extends En{#ue;#Pn=new Set;_modifiers=0;constructor(e){super(),this.#ue=e}updateClient(e){this.#ue=e}async down(e,t={text:void 0,commands:[]}){const r=this.#Dn(e),i=this.#Pn.has(r.code);this.#Pn.add(r.code),this._modifiers|=this.#Mn(r.key);const n=void 0===t.text?r.text:t.text;await this.#ue.send("Input.dispatchKeyEvent",{type:n?"keyDown":"rawKeyDown",modifiers:this._modifiers,windowsVirtualKeyCode:r.keyCode,code:r.code,key:r.key,text:n,unmodifiedText:n,autoRepeat:i,location:r.location,isKeypad:3===r.location,commands:t.commands})}#Mn(e){return"Alt"===e?1:"Control"===e?2:"Meta"===e?4:"Shift"===e?8:0}#Dn(e){const t=8&this._modifiers,r={key:"",keyCode:0,code:"",text:"",location:0},i=Rn[e];return st(i,`Unknown key: "${e}"`),i.key&&(r.key=i.key),t&&i.shiftKey&&(r.key=i.shiftKey),i.keyCode&&(r.keyCode=i.keyCode),t&&i.shiftKeyCode&&(r.keyCode=i.shiftKeyCode),i.code&&(r.code=i.code),i.location&&(r.location=i.location),1===r.key.length&&(r.text=r.key),i.text&&(r.text=i.text),t&&i.shiftText&&(r.text=i.shiftText),-9&this._modifiers&&(r.text=""),r}async up(e){const t=this.#Dn(e);this._modifiers&=~this.#Mn(t.key),this.#Pn.delete(t.code),await this.#ue.send("Input.dispatchKeyEvent",{type:"keyUp",modifiers:this._modifiers,key:t.key,windowsVirtualKeyCode:t.keyCode,code:t.code,location:t.location})}async sendCharacter(e){await this.#ue.send("Input.insertText",{text:e})}charIsKey(e){return!!Rn[e]}async type(e,t={}){const r=t.delay||void 0;for(const t of e)this.charIsKey(t)?await this.press(t,{delay:r}):(r&&await new Promise((e=>setTimeout(e,r))),await this.sendCharacter(t))}async press(e,t={}){const{delay:r=null}=t;await this.down(e,t),r&&await new Promise((e=>setTimeout(e,t.delay))),await this.up(e)}}const Pn=e=>{switch(e){case In.Left:return 1;case In.Right:return 2;case In.Middle:return 4;case In.Back:return 8;case In.Forward:return 16}},Dn=e=>1&e?In.Left:2&e?In.Right:4&e?In.Middle:8&e?In.Back:16&e?In.Forward:"none";class Mn extends xn{#ue;#An;constructor(e,t){super(),this.#ue=e,this.#An=t}updateClient(e){this.#ue=e}#qn={position:{x:0,y:0},buttons:0};get#Qe(){return Object.assign({...this.#qn},...this.#On)}#On=[];#Nn(){const e={};this.#On.push(e);const t=()=>{this.#On.splice(this.#On.indexOf(e),1)};return{update:t=>{Object.assign(e,t)},commit:()=>{this.#qn={...this.#qn,...e},t()},rollback:t}}async#Ln(e){const{update:t,commit:r,rollback:i}=this.#Nn();try{await e(t),r()}catch(e){throw i(),e}}async reset(){const e=[];for(const[t,r]of[[1,In.Left],[4,In.Middle],[2,In.Right],[16,In.Forward],[8,In.Back]])this.#Qe.buttons&t&&e.push(this.up({button:r}));0===this.#Qe.position.x&&0===this.#Qe.position.y||e.push(this.move(0,0)),await Promise.all(e)}async move(e,t,r={}){const{steps:i=1}=r,n=this.#Qe.position,s=e,a=t;for(let e=1;e<=i;e++)await this.#Ln((t=>{t({position:{x:n.x+(s-n.x)*(e/i),y:n.y+(a-n.y)*(e/i)}});const{buttons:r,position:o}=this.#Qe;return this.#ue.send("Input.dispatchMouseEvent",{type:"mouseMoved",modifiers:this.#An._modifiers,buttons:r,button:Dn(r),...o})}))}async down(e={}){const{button:t=In.Left,clickCount:r=1}=e,i=Pn(t);if(!i)throw new Error(`Unsupported mouse button: ${t}`);if(this.#Qe.buttons&i)throw new Error(`'${t}' is already pressed.`);await this.#Ln((e=>{e({buttons:this.#Qe.buttons|i});const{buttons:n,position:s}=this.#Qe;return this.#ue.send("Input.dispatchMouseEvent",{type:"mousePressed",modifiers:this.#An._modifiers,clickCount:r,buttons:n,button:t,...s})}))}async up(e={}){const{button:t=In.Left,clickCount:r=1}=e,i=Pn(t);if(!i)throw new Error(`Unsupported mouse button: ${t}`);if(!(this.#Qe.buttons&i))throw new Error(`'${t}' is not pressed.`);await this.#Ln((e=>{e({buttons:this.#Qe.buttons&~i});const{buttons:n,position:s}=this.#Qe;return this.#ue.send("Input.dispatchMouseEvent",{type:"mouseReleased",modifiers:this.#An._modifiers,clickCount:r,buttons:n,button:t,...s})}))}async click(e,t,r={}){const{delay:i,count:n=1,clickCount:s=n}=r;if(n<1)throw new Error("Click must occur a positive number of times.");const a=[this.move(e,t)];if(s===n)for(let e=1;e<n;++e)a.push(this.down({...r,clickCount:e}),this.up({...r,clickCount:e}));a.push(this.down({...r,clickCount:s})),"number"==typeof i&&(await Promise.all(a),a.length=0,await new Promise((e=>{setTimeout(e,i)}))),a.push(this.up({...r,clickCount:s})),await Promise.all(a)}async wheel(e={}){const{deltaX:t=0,deltaY:r=0}=e,{position:i,buttons:n}=this.#Qe;await this.#ue.send("Input.dispatchMouseEvent",{type:"mouseWheel",pointerType:"mouse",modifiers:this.#An._modifiers,deltaY:r,deltaX:t,buttons:n,...i})}async drag(e,t){const r=new Promise((e=>{this.#ue.once("Input.dragIntercepted",(t=>e(t.data)))}));return await this.move(e.x,e.y),await this.down(),await this.move(t.x,t.y),await r}async dragEnter(e,t){await this.#ue.send("Input.dispatchDragEvent",{type:"dragEnter",x:e.x,y:e.y,modifiers:this.#An._modifiers,data:t})}async dragOver(e,t){await this.#ue.send("Input.dispatchDragEvent",{type:"dragOver",x:e.x,y:e.y,modifiers:this.#An._modifiers,data:t})}async drop(e,t){await this.#ue.send("Input.dispatchDragEvent",{type:"drop",x:e.x,y:e.y,modifiers:this.#An._modifiers,data:t})}async dragAndDrop(e,t,r={}){const{delay:i=null}=r,n=await this.drag(e,t);await this.dragEnter(t,n),await this.dragOver(t,n),i&&await new Promise((e=>setTimeout(e,i))),await this.drop(t,n),await this.up()}}class An extends Fn{#ue;#An;constructor(e,t){super(),this.#ue=e,this.#An=t}updateClient(e){this.#ue=e}async touchStart(e,t){await this.#ue.send("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:[{x:Math.round(e),y:Math.round(t),radiusX:.5,radiusY:.5,force:.5}],modifiers:this.#An._modifiers})}async touchMove(e,t){await this.#ue.send("Input.dispatchTouchEvent",{type:"touchMove",touchPoints:[{x:Math.round(e),y:Math.round(t),radiusX:.5,radiusY:.5,force:.5}],modifiers:this.#An._modifiers})}async touchEnd(){await this.#ue.send("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[],modifiers:this.#An._modifiers})}}class qn{#ue;#Bn=!1;#jn;constructor(e){this.#ue=e}updateClient(e){this.#ue=e}async start(e={}){st(!this.#Bn,"Cannot start recording trace while already recording trace.");const t=["-*","devtools.timeline","v8.execute","disabled-by-default-devtools.timeline","disabled-by-default-devtools.timeline.frame","toplevel","blink.console","blink.user_timing","latencyInfo","disabled-by-default-devtools.timeline.stack","disabled-by-default-v8.cpu_profiler"],{path:r,screenshots:i=!1,categories:n=t}=e;i&&n.push("disabled-by-default-devtools.screenshot");const s=n.filter((e=>e.startsWith("-"))).map((e=>e.slice(1))),a=n.filter((e=>!e.startsWith("-")));this.#jn=r,this.#Bn=!0,await this.#ue.send("Tracing.start",{transferMode:"ReturnAsStream",traceConfig:{excludedCategories:s,includedCategories:a}})}async stop(){const e=$t.create();return this.#ue.once("Tracing.tracingComplete",(async t=>{try{st(t.stream,'Missing "stream"');const r=await xt(this.#ue,t.stream),i=await It(r,this.#jn);e.resolve(i??void 0)}catch(t){Vt(t)?e.reject(t):e.reject(new Error(`Unknown error: ${t}`))}})),await this.#ue.send("Tracing.end"),this.#Bn=!1,await e.valueOrThrow()}}
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class On extends it{timeoutSettings=new Jt;#Te;constructor(e){super(),this.#Te=e}url(){return this.#Te}async evaluate(e,...t){return e=bt(this.evaluate.name,e),await this.mainRealm().evaluate(e,...t)}async evaluateHandle(e,...t){return e=bt(this.evaluateHandle.name,e),await this.mainRealm().evaluateHandle(e,...t)}async close(){throw new ft("WebWorker.close() is not supported")}}class Nn extends On{#At;#ue;#S;#F;constructor(e,t,r,i,n,s){super(t),this.#S=r,this.#ue=e,this.#F=i,this.#At=new tn(this,new Jt),this.#ue.once("Runtime.executionContextCreated",(async t=>{this.#At.setContext(new Oi(e,t.context,this.#At))})),this.#ue.on("Runtime.consoleAPICalled",(async e=>{try{return n(e.type,e.args.map((e=>new Pi(this.#At,e))),e.stackTrace)}catch(e){gt(e)}})),this.#ue.on("Runtime.exceptionThrown",s),this.#ue.send("Runtime.enable").catch(gt)}mainRealm(){return this.#At}get client(){return this.#ue}async close(){switch(this.#F){case jt.SERVICE_WORKER:case jt.SHARED_WORKER:await(this.client.connection()?.send("Target.closeTarget",{targetId:this.#S})),await(this.client.connection()?.send("Target.detachFromTarget",{sessionId:this.client.id()}));break;default:await this.evaluate((()=>{self.close()}))}}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Ln=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Bn=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});function jn(e){return"warning"===e?"warn":e}class Hn extends br{static async _create(e,t,r,i){const n=new Hn(e,t,r);if(await n.#Hn(),i)try{await n.setViewport(i)}catch(e){if(!Vt(e)||!Br(e))throw e;gt(e)}return n}#Re=!1;#$n;#Kn;#Vn;#Wn;#zn;#An;#Un;#Gn;#Qn;#qt;#Jn;#Xn;#mr=new Map;#Yn=new Map;#Zn;#es;#ts=new Map;#rs=new Set;#is=$t.create();#ns=!1;#ss=!1;#as=[[sn.FrameAttached,e=>{this.emit("frameattached",e)}],[sn.FrameDetached,e=>{this.emit("framedetached",e)}],[sn.FrameNavigated,e=>{this.emit("framenavigated",e)}]];#os=[[Er.Request,e=>{this.emit("request",e)}],[Er.RequestServedFromCache,e=>{this.emit("requestservedfromcache",e)}],[Er.Response,e=>{this.emit("response",e)}],[Er.RequestFailed,e=>{this.emit("requestfailed",e)}],[Er.RequestFinished,e=>{this.emit("requestfinished",e)}]];#cs=[[Bt.Disconnected,()=>{this.#is.reject(new yt("Target closed"))}],["Page.domContentEventFired",()=>this.emit("domcontentloaded",void 0)],["Page.loadEventFired",()=>this.emit("load",void 0)],["Runtime.consoleAPICalled",this.#ls.bind(this)],["Runtime.bindingCalled",this.#wr.bind(this)],["Page.javascriptDialogOpening",this.#ds.bind(this)],["Runtime.exceptionThrown",this.#us.bind(this)],["Inspector.targetCrashed",this.#hs.bind(this)],["Performance.metrics",this.#ps.bind(this)],["Log.entryAdded",this.#fs.bind(this)],["Page.fileChooserOpened",this.#ys.bind(this)]];constructor(e,t,r){super(),this.#Kn=e,this.#Wn=e.parentSession(),st(this.#Wn,"Tab target session is not defined."),this.#zn=this.#Wn._target(),st(this.#zn,"Tab target is not defined."),this.#Vn=t,this.#$n=t._targetManager(),this.#An=new _n(e),this.#Un=new Mn(e,this.#An),this.#Gn=new An(e,this.#An),this.#Qn=new Ir(e),this.#qt=new Tn(e,this,r,this._timeoutSettings),this.#Jn=new Jr(e),this.#Xn=new qn(e),this.#Zn=new jr(e),this.#es=null;for(const[e,t]of this.#as)this.#qt.on(e,t);for(const[e,t]of this.#os)this.#qt.networkManager.on(e,t);this.#Wn.on(Bt.Swapped,this.#ms.bind(this)),this.#Wn.on(Bt.Ready,this.#gs.bind(this)),this.#$n.on("targetGone",this.#ws),this.#zn._isClosedDeferred.valueOrThrow().then((()=>{this.#$n.off("targetGone",this.#ws),this.emit("close",void 0),this.#Re=!0})).catch(gt),this.#vs()}async#ms(e){this.#Kn=e,st(this.#Kn instanceof Qt,"CDPSession is not instance of CDPSessionImpl"),this.#Vn=this.#Kn._target(),st(this.#Vn,"Missing target on swap"),this.#An.updateClient(e),this.#Un.updateClient(e),this.#Gn.updateClient(e),this.#Qn.updateClient(e),this.#Jn.updateClient(e),this.#Xn.updateClient(e),this.#Zn.updateClient(e),await this.#qt.swapFrameTree(e),this.#vs()}async#gs(e){st(e instanceof Qt),"prerender"===e._target()._subtype()&&(this.#qt.registerSpeculativeSession(e).catch(gt),this.#Jn.registerSpeculativeSession(e).catch(gt))}#vs(){this.#Kn.on(Bt.Ready,this.#Xt);for(const[e,t]of this.#cs)this.#Kn.on(e,t)}#ws=e=>{const t=e._session()?.id(),r=this.#ts.get(t);r&&(this.#ts.delete(t),this.emit("workerdestroyed",r))};#Xt=e=>{if(st(e instanceof Qt),this.#qt.onAttachedToTarget(e._target()),"worker"===e._target()._getTargetInfo().type){const t=new Nn(e,e._target().url(),e._target()._targetId,e._target().type(),this.#bs.bind(this),this.#us.bind(this));this.#ts.set(e.id(),t),this.emit("workercreated",t)}e.on(Bt.Ready,this.#Xt)};async#Hn(){try{await Promise.all([this.#qt.initialize(this.#Kn),this.#Kn.send("Performance.enable"),this.#Kn.send("Log.enable")])}catch(e){if(!Vt(e)||!Br(e))throw e;gt(e)}}async#ys(e){const t={stack:[],error:void 0,hasError:!1};try{if(!this.#rs.size)return;const r=this.#qt.frame(e.frameId);st(r,"This should never happen.");const i=Ln(t,await r.worlds[rn].adoptBackendNode(e.backendNodeId),!1),n=new Tr(i.move(),e);for(const e of this.#rs)e.resolve(n);this.#rs.clear()}catch(e){t.error=e,t.hasError=!0}finally{Bn(t)}}_client(){return this.#Kn}isServiceWorkerBypassed(){return this.#ns}isDragInterceptionEnabled(){return this.#ss}isJavaScriptEnabled(){return this.#Jn.javascriptEnabled}async waitForFileChooser(e={}){const t=0===this.#rs.size,{timeout:r=this._timeoutSettings.timeout()}=e,i=$t.create({message:`Waiting for \`FileChooser\` failed: ${r}ms exceeded`,timeout:r});let n;this.#rs.add(i),t&&(n=this.#Kn.send("Page.setInterceptFileChooserDialog",{enabled:!0}));try{const[e]=await Promise.all([i.valueOrThrow(),n]);return e}catch(e){throw this.#rs.delete(i),e}}async setGeolocation(e){return await this.#Jn.setGeolocation(e)}target(){return this.#Vn}browser(){return this.#Vn.browser()}browserContext(){return this.#Vn.browserContext()}#hs(){this.emit("error",new Error("Page crashed!"))}#fs(e){const{level:t,text:r,args:i,source:n,url:s,lineNumber:a}=e.entry;i&&i.map((e=>{Di(this.#Kn,e)})),"worker"!==n&&this.emit("console",new Cr(jn(t),r,[],[{url:s,lineNumber:a}]))}mainFrame(){return this.#qt.mainFrame()}get keyboard(){return this.#An}get touchscreen(){return this.#Gn}get coverage(){return this.#Zn}get tracing(){return this.#Xn}get accessibility(){return this.#Qn}frames(){return this.#qt.frames()}workers(){return Array.from(this.#ts.values())}async setRequestInterception(e){return await this.#qt.networkManager.setRequestInterception(e)}async setBypassServiceWorker(e){return this.#ns=e,await this.#Kn.send("Network.setBypassServiceWorker",{bypass:e})}async setDragInterception(e){return this.#ss=e,await this.#Kn.send("Input.setInterceptDrags",{enabled:e})}async setOfflineMode(e){return await this.#qt.networkManager.setOfflineMode(e)}async emulateNetworkConditions(e){return await this.#qt.networkManager.emulateNetworkConditions(e)}setDefaultNavigationTimeout(e){this._timeoutSettings.setDefaultNavigationTimeout(e)}setDefaultTimeout(e){this._timeoutSettings.setDefaultTimeout(e)}getDefaultTimeout(){return this._timeoutSettings.timeout()}async queryObjects(e){st(!e.disposed,"Prototype JSHandle is disposed!"),st(e.id,"Prototype JSHandle must not be referencing primitive value");const t=await this.mainFrame().client.send("Runtime.queryObjects",{prototypeObjectId:e.id});return Li(this.mainFrame().mainRealm(),t.objects)}async cookies(...e){const t=(await this.#Kn.send("Network.getCookies",{urls:e.length?e:[this.url()]})).cookies,r=["sourcePort"];return t.map((e=>{for(const t of r)delete e[t];return e}))}async deleteCookie(...e){const t=this.url();for(const r of e){const e=Object.assign({},r);!r.url&&t.startsWith("http")&&(e.url=t),await this.#Kn.send("Network.deleteCookies",e)}}async setCookie(...e){const t=this.url(),r=t.startsWith("http"),i=e.map((e=>{const i=Object.assign({},e);return!i.url&&r&&(i.url=t),st("about:blank"!==i.url,`Blank page can not have cookie "${i.name}"`),st(!String.prototype.startsWith.call(i.url||"","data:"),`Data URL page can not have cookie "${i.name}"`),i}));await this.deleteCookie(...i),i.length&&await this.#Kn.send("Network.setCookies",{cookies:i})}async exposeFunction(e,t){if(this.#mr.has(e))throw new Error(`Failed to add page binding with name ${e}: window['${e}'] already exists!`);let r;if("function"==typeof t)r=new qr(e,t);else r=new qr(e,t.default);this.#mr.set(e,r);const i=function(e,t){return Ct(_i,e,t)}("exposedFun",e);await this.#Kn.send("Runtime.addBinding",{name:e});const{identifier:n}=await this.#Kn.send("Page.addScriptToEvaluateOnNewDocument",{source:i});this.#Yn.set(e,n),await Promise.all(this.frames().map((e=>{if(e===this.mainFrame()||e._hasStartedLoading)return e.evaluate(i).catch(gt)})))}async removeExposedFunction(e){const t=this.#Yn.get(e);if(!t)throw new Error(`Failed to remove page binding with name ${e}: window['${e}'] does not exists!`);await this.#Kn.send("Runtime.removeBinding",{name:e}),await this.removeScriptToEvaluateOnNewDocument(t),await Promise.all(this.frames().map((t=>{if(t===this.mainFrame()||t._hasStartedLoading)return t.evaluate((e=>{globalThis[e]=void 0}),e).catch(gt)}))),this.#Yn.delete(e),this.#mr.delete(e)}async authenticate(e){return await this.#qt.networkManager.authenticate(e)}async setExtraHTTPHeaders(e){return await this.#qt.networkManager.setExtraHTTPHeaders(e)}async setUserAgent(e,t){return await this.#qt.networkManager.setUserAgent(e,t)}async metrics(){const e=await this.#Kn.send("Performance.getMetrics");return this.#ks(e.metrics)}#ps(e){this.emit("metrics",{title:e.title,metrics:this.#ks(e.metrics)})}#ks(e){const t={};for(const r of e||[])$n.has(r.name)&&(t[r.name]=r.value);return t}#us(e){this.emit("pageerror",function(e){let t,r;if(e.exception){if(!("object"===e.exception.type&&"error"===e.exception.subtype||e.exception.objectId))return Ri(e.exception);{const i=Fi(e);t=i.name,r=i.message}}else t="Error",r=e.text;const i=new Error(r);i.name=t;const n=i.message.split("\n").length,s=i.stack.split("\n").splice(0,n),a=[];if(e.stackTrace)for(const t of e.stackTrace.callFrames)if(a.push(`    at ${t.functionName||"<anonymous>"} (${t.url}:${t.lineNumber+1}:${t.columnNumber+1})`),a.length>=Error.stackTraceLimit)break;return i.stack=[...s,...a].join("\n"),i}(e.exceptionDetails))}async#ls(e){if(0===e.executionContextId)return;const t=this.#qt.getExecutionContextById(e.executionContextId,this.#Kn);if(!t)return void gt(new Error(`ExecutionContext not found for a console message: ${JSON.stringify(e)}`));const r=e.args.map((e=>Li(t._world,e)));this.#bs(jn(e.type),r,e.stackTrace)}async#wr(e){let t;try{t=JSON.parse(e.payload)}catch{return}const{type:r,name:i,seq:n,args:s,isTrivial:a}=t;if("exposedFun"!==r)return;const o=this.#qt.executionContextById(e.executionContextId,this.#Kn);if(!o)return;const c=this.#mr.get(i);await(c?.run(o,n,s,a))}#bs(e,t,r){if(!this.listenerCount("console"))return void t.forEach((e=>e.dispose()));const i=[];for(const e of t){const t=e.remoteObject();t.objectId?i.push(e.toString()):i.push(Ri(t))}const n=[];if(r)for(const e of r.callFrames)n.push({url:e.url,lineNumber:e.lineNumber,columnNumber:e.columnNumber});const s=new Cr(jn(e),i.join(" "),t,n);this.emit("console",s)}#ds(e){const t=function(e){let t=null;return new Set(["alert","confirm","prompt","beforeunload"]).has(e)&&(t=e),st(t,`Unknown javascript dialog type: ${e}`),t}(e.type),r=new Wr(this.#Kn,t,e.message,e.defaultPrompt);this.emit("dialog",r)}async reload(e){const[t]=await Promise.all([this.waitForNavigation(e),this.#Kn.send("Page.reload")]);return t}async createCDPSession(){return await this.target().createCDPSession()}async goBack(e={}){return await this.#Ss(-1,e)}async goForward(e={}){return await this.#Ss(1,e)}async#Ss(e,t){const r=await this.#Kn.send("Page.getNavigationHistory"),i=r.entries[r.currentIndex+e];if(!i)return null;return(await Promise.all([this.waitForNavigation(t),this.#Kn.send("Page.navigateToHistoryEntry",{entryId:i.id})]))[0]}async bringToFront(){await this.#Kn.send("Page.bringToFront")}async setJavaScriptEnabled(e){return await this.#Jn.setJavaScriptEnabled(e)}async setBypassCSP(e){await this.#Kn.send("Page.setBypassCSP",{enabled:e})}async emulateMediaType(e){return await this.#Jn.emulateMediaType(e)}async emulateCPUThrottling(e){return await this.#Jn.emulateCPUThrottling(e)}async emulateMediaFeatures(e){return await this.#Jn.emulateMediaFeatures(e)}async emulateTimezone(e){return await this.#Jn.emulateTimezone(e)}async emulateIdleState(e){return await this.#Jn.emulateIdleState(e)}async emulateVisionDeficiency(e){return await this.#Jn.emulateVisionDeficiency(e)}async setViewport(e){const t=await this.#Jn.emulateViewport(e);this.#es=e,t&&await this.reload()}viewport(){return this.#es}async evaluateOnNewDocument(e,...t){const r=Ct(e,...t),{identifier:i}=await this.#Kn.send("Page.addScriptToEvaluateOnNewDocument",{source:r});return{identifier:i}}async removeScriptToEvaluateOnNewDocument(e){await this.#Kn.send("Page.removeScriptToEvaluateOnNewDocument",{identifier:e})}async setCacheEnabled(e=!0){await this.#qt.networkManager.setCacheEnabled(e)}async _screenshot(e){const t={stack:[],error:void 0,hasError:!1};try{const{fromSurface:r,omitBackground:i,optimizeForSpeed:n,quality:s,clip:a,type:o,captureBeyondViewport:c}=e,l=this.target()._targetManager()instanceof Bi,d=Ln(t,new rt,!0);l||!i||"png"!==o&&"webp"!==o||(await this.#Jn.setTransparentBackgroundColor(),d.defer((async()=>{await this.#Jn.resetDefaultBackgroundColor().catch(gt)})));let u=a;if(u&&!c){const e=await this.mainFrame().isolatedRealm().evaluate((()=>{const{height:e,pageLeft:t,pageTop:r,width:i}=window.visualViewport;return{x:t,y:r,height:e,width:i}}));u=function(e,t){const r=Math.max(e.x,t.x),i=Math.max(e.y,t.y);return{x:r,y:i,width:Math.max(Math.min(e.x+e.width,t.x+t.width)-r,0),height:Math.max(Math.min(e.y+e.height,t.y+t.height)-i,0)}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */(u,e)}const{data:h}=await this.#Kn.send("Page.captureScreenshot",{format:o,...n?{optimizeForSpeed:n}:{},...void 0!==s?{quality:Math.round(s)}:{},...u?{clip:{...u,scale:u.scale??1}}:{},...r?{}:{fromSurface:r},captureBeyondViewport:c});return h}catch(e){t.error=e,t.hasError=!0}finally{const e=Bn(t);e&&await e}}async createPDFStream(e={}){const{timeout:t=this._timeoutSettings.timeout()}=e,{landscape:r,displayHeaderFooter:i,headerTemplate:n,footerTemplate:s,printBackground:a,scale:o,width:c,height:l,margin:d,pageRanges:u,preferCSSPageSize:h,omitBackground:p,tagged:f,outline:y}=function(e={},t="in"){let r=8.5,i=11;if(e.format){const t=mt[e.format.toLowerCase()];st(t,"Unknown paper format: "+e.format),r=t.width,i=t.height}else r=Mt(e.width,t)??r,i=Mt(e.height,t)??i;const n={top:Mt(e.margin?.top,t)||0,left:Mt(e.margin?.left,t)||0,bottom:Mt(e.margin?.bottom,t)||0,right:Mt(e.margin?.right,t)||0};return e.outline&&(e.tagged=!0),{scale:1,displayHeaderFooter:!1,headerTemplate:"",footerTemplate:"",printBackground:!1,landscape:!1,pageRanges:"",preferCSSPageSize:!1,omitBackground:!1,outline:!1,tagged:!0,...e,width:r,height:i,margin:n}}(e);p&&await this.#Jn.setTransparentBackgroundColor();const m=this.#Kn.send("Page.printToPDF",{transferMode:"ReturnAsStream",landscape:r,displayHeaderFooter:i,headerTemplate:n,footerTemplate:s,printBackground:a,scale:o,paperWidth:c,paperHeight:l,marginTop:d.top,marginBottom:d.bottom,marginLeft:d.left,marginRight:d.right,pageRanges:u,preferCSSPageSize:h,generateTaggedPDF:f,generateDocumentOutline:y}),g=await Se(ve(m).pipe(Je(Ft(t))));return p&&await this.#Jn.resetDefaultBackgroundColor(),st(g.stream,"`stream` is missing from `Page.printToPDF"),await xt(this.#Kn,g.stream)}async pdf(e={}){const{path:t}=e,r=await this.createPDFStream(e),i=await It(r,t);return st(i,"Could not create buffer"),i}async close(e={runBeforeUnload:void 0}){const t=this.#Kn.connection();st(t,"Protocol error: Connection closed. Most likely the page has been closed.");!!e.runBeforeUnload?await this.#Kn.send("Page.close"):(await t.send("Target.closeTarget",{targetId:this.#Vn._targetId}),await this.#zn._isClosedDeferred.valueOrThrow())}isClosed(){return this.#Re}get mouse(){return this.#Un}async waitForDevicePrompt(e={}){return await this.mainFrame().waitForDevicePrompt(e)}}const $n=new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]);var Kn;!function(e){e.SUCCESS="success",e.ABORTED="aborted"}(Kn||(Kn={}));class Vn extends Kt{#Cs;#Ts;#Es;#$n;#Is;_initializedDeferred=$t.create();_isClosedDeferred=$t.create();_targetId;constructor(e,t,r,i,n){super(),this.#Ts=t,this.#$n=i,this.#Es=e,this.#Cs=r,this._targetId=e.targetId,this.#Is=n,this.#Ts&&this.#Ts instanceof Qt&&this.#Ts._setTarget(this)}async asPage(){const e=this._session();return e?await Hn._create(e,this,!1,null):await this.createCDPSession().then((e=>Hn._create(e,this,!1,null)))}_subtype(){return this.#Es.subtype}_session(){return this.#Ts}_sessionFactory(){if(!this.#Is)throw new Error("sessionFactory is not initialized");return this.#Is}createCDPSession(){if(!this.#Is)throw new Error("sessionFactory is not initialized");return this.#Is(!1).then((e=>(e._setTarget(this),e)))}url(){return this.#Es.url}type(){switch(this.#Es.type){case"page":return jt.PAGE;case"background_page":return jt.BACKGROUND_PAGE;case"service_worker":return jt.SERVICE_WORKER;case"shared_worker":return jt.SHARED_WORKER;case"browser":return jt.BROWSER;case"webview":return jt.WEBVIEW;case"tab":return jt.TAB;default:return jt.OTHER}}_targetManager(){if(!this.#$n)throw new Error("targetManager is not initialized");return this.#$n}_getTargetInfo(){return this.#Es}browser(){if(!this.#Cs)throw new Error("browserContext is not initialized");return this.#Cs.browser()}browserContext(){if(!this.#Cs)throw new Error("browserContext is not initialized");return this.#Cs}opener(){const{openerId:e}=this.#Es;if(e)return this.browser().targets().find((t=>t._targetId===e))}_targetInfoChanged(e){this.#Es=e,this._checkIfInitialized()}_initialize(){this._initializedDeferred.resolve(Kn.SUCCESS)}_isTargetExposed(){return this.type()!==jt.TAB&&!this._subtype()}_checkIfInitialized(){this._initializedDeferred.resolved()||this._initializedDeferred.resolve(Kn.SUCCESS)}}class Wn extends Vn{#xs;pagePromise;#Di;constructor(e,t,r,i,n,s,a){super(e,t,r,i,n),this.#Di=s,this.#xs=a??void 0}_initialize(){this._initializedDeferred.valueOrThrow().then((async e=>{if(e===Kn.ABORTED)return;const t=this.opener();if(!(t instanceof Wn))return;if(!t||!t.pagePromise||"page"!==this.type())return!0;const r=await t.pagePromise;if(!r.listenerCount("popup"))return!0;const i=await this.page();return r.emit("popup",i),!0})).catch(gt),this._checkIfInitialized()}async page(){if(!this.pagePromise){const e=this._session();this.pagePromise=(e?Promise.resolve(e):this._sessionFactory()(!1)).then((e=>Hn._create(e,this,this.#Di,this.#xs??null)))}return await this.pagePromise??null}_checkIfInitialized(){this._initializedDeferred.resolved()||""!==this._getTargetInfo().url&&this._initializedDeferred.resolve(Kn.SUCCESS)}}class zn extends Wn{}class Un extends Vn{#Fs;async worker(){if(!this.#Fs){const e=this._session();this.#Fs=(e?Promise.resolve(e):this._sessionFactory()(!1)).then((e=>new Nn(e,this._getTargetInfo().url,this._targetId,this.type(),(()=>{}),(()=>{}))))}return await this.#Fs}}class Gn extends Vn{}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Qn extends it{#R;#jt=new Map;#Rs=new Map;#_s=new Map;#Ps=new Set;#Kt;#Vt;#Wt=new WeakMap;#Ds=new WeakMap;#zt=$t.create();#Ut=new Set;#Ms=!0;#As=[{}];constructor(e,t,r,i=!0){super(),this.#R=e,this.#Kt=r,this.#Vt=t,this.#Ms=i,this.#R.on("Target.targetCreated",this.#Gt),this.#R.on("Target.targetDestroyed",this.#Qt),this.#R.on("Target.targetInfoChanged",this.#qs),this.#R.on(Bt.SessionDetached,this.#Jt),this.#Os(this.#R)}#Ns=()=>{if(this.#Ms)for(const[e,t]of this.#jt.entries()){const r=new Vn(t,void 0,void 0,this,void 0);this.#Kt&&!this.#Kt(r)||"browser"===t.type||this.#Ut.add(e)}};async initialize(){await this.#R.send("Target.setDiscoverTargets",{discover:!0,filter:this.#As}),this.#Ns(),await this.#R.send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0,filter:[{type:"page",exclude:!0},...this.#As]}),this.#Yt(),await this.#zt.valueOrThrow()}dispose(){this.#R.off("Target.targetCreated",this.#Gt),this.#R.off("Target.targetDestroyed",this.#Qt),this.#R.off("Target.targetInfoChanged",this.#qs),this.#R.off(Bt.SessionDetached,this.#Jt),this.#Ls(this.#R)}getAvailableTargets(){return this.#Rs}#Os(e){const t=t=>{this.#Xt(e,t)};st(!this.#Wt.has(e)),this.#Wt.set(e,t),e.on("Target.attachedToTarget",t);const r=t=>this.#ws(e,t);st(!this.#Ds.has(e)),this.#Ds.set(e,r),e.on("Target.detachedFromTarget",r)}#Ls(e){const t=this.#Wt.get(e);t&&(e.off("Target.attachedToTarget",t),this.#Wt.delete(e)),this.#Ds.has(e)&&(e.off("Target.detachedFromTarget",this.#Ds.get(e)),this.#Ds.delete(e))}#Jt=e=>{this.#Ls(e)};#Gt=async e=>{if(this.#jt.set(e.targetInfo.targetId,e.targetInfo),this.emit("targetDiscovered",e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){if(this.#Rs.has(e.targetInfo.targetId))return;const t=this.#Vt(e.targetInfo,void 0);t._initialize(),this.#Rs.set(e.targetInfo.targetId,t)}};#Qt=e=>{const t=this.#jt.get(e.targetId);if(this.#jt.delete(e.targetId),this.#Yt(e.targetId),"service_worker"===t?.type&&this.#Rs.has(e.targetId)){const t=this.#Rs.get(e.targetId);t&&(this.emit("targetGone",t),this.#Rs.delete(e.targetId))}};#qs=e=>{if(this.#jt.set(e.targetInfo.targetId,e.targetInfo),this.#Ps.has(e.targetInfo.targetId)||!this.#Rs.has(e.targetInfo.targetId)||!e.targetInfo.attached)return;const t=this.#Rs.get(e.targetInfo.targetId);if(!t)return;const r=t.url(),i=t._initializedDeferred.value()===Kn.SUCCESS;if(function(e,t){return Boolean(e._subtype())&&!t.subtype}(t,e.targetInfo)){const e=t?._session();st(e,"Target that is being activated is missing a CDPSession."),e.parentSession()?.emit(Bt.Swapped,e)}t._targetInfoChanged(e.targetInfo),i&&r!==t.url()&&this.emit("targetChanged",{target:t,wasInitialized:i,previousURL:r})};#Xt=async(e,t)=>{const r=t.targetInfo,i=this.#R.session(t.sessionId);if(!i)throw new Error(`Session ${t.sessionId} was not created.`);const n=async()=>{await i.send("Runtime.runIfWaitingForDebugger").catch(gt),await e.send("Target.detachFromTarget",{sessionId:i.id()}).catch(gt)};if(!this.#R.isAutoAttached(r.targetId))return;if("service_worker"===r.type){if(this.#Yt(r.targetId),await n(),this.#Rs.has(r.targetId))return;const e=this.#Vt(r);return e._initialize(),this.#Rs.set(r.targetId,e),void this.emit("targetAvailable",e)}const s=this.#Rs.has(r.targetId),a=s?this.#Rs.get(r.targetId):this.#Vt(r,i,e instanceof Ht?e:void 0);if(this.#Kt&&!this.#Kt(a))return this.#Ps.add(r.targetId),this.#Yt(r.targetId),void await n();this.#Os(i),s?(i._setTarget(a),this.#_s.set(i.id(),this.#Rs.get(r.targetId))):(a._initialize(),this.#Rs.set(r.targetId,a),this.#_s.set(i.id(),a)),e.emit(Bt.Ready,i),this.#Ut.delete(a._targetId),s||this.emit("targetAvailable",a),this.#Yt(),await Promise.all([i.send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0,filter:this.#As}),i.send("Runtime.runIfWaitingForDebugger")]).catch(gt)};#Yt(e){void 0!==e&&this.#Ut.delete(e),0===this.#Ut.size&&this.#zt.resolve()}#ws=(e,t)=>{const r=this.#_s.get(t.sessionId);this.#_s.delete(t.sessionId),r&&(this.#Rs.delete(r._targetId),this.emit("targetGone",r))}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Jn extends Nt{protocol="cdp";static async _create(e,t,r,i,n,s,a,o,c,l=!0){const d=new Jn(e,t,r,i,n,s,a,o,c,l);return await d._attach(),d}#Di;#xs;#Bs;#R;#js;#Kt;#Hs;#$s;#Ks=new Map;#$n;constructor(e,t,r,i,n,s,a,o,c,l=!0){super(),e=e||"chrome",this.#Di=i,this.#xs=n,this.#Bs=s,this.#R=t,this.#js=a||function(){},this.#Kt=o||(()=>!0),this.#Vs(c),this.#$n="firefox"===e?new Bi(t,this.#Ws,this.#Kt):new Qn(t,this.#Ws,this.#Kt,l),this.#$s=new Xn(this.#R,this);for(const e of r)this.#Ks.set(e,new Xn(this.#R,this,e))}#zs=()=>{this.emit("disconnected",void 0)};async _attach(){this.#R.on(Bt.Disconnected,this.#zs),this.#$n.on("targetAvailable",this.#Xt),this.#$n.on("targetGone",this.#ws),this.#$n.on("targetChanged",this.#Us),this.#$n.on("targetDiscovered",this.#Gs),await this.#$n.initialize()}_detach(){this.#R.off(Bt.Disconnected,this.#zs),this.#$n.off("targetAvailable",this.#Xt),this.#$n.off("targetGone",this.#ws),this.#$n.off("targetChanged",this.#Us),this.#$n.off("targetDiscovered",this.#Gs)}process(){return this.#Bs??null}_targetManager(){return this.#$n}#Vs(e){this.#Hs=e||(e=>"page"===e.type()||"background_page"===e.type()||"webview"===e.type())}_getIsPageTargetCallback(){return this.#Hs}async createBrowserContext(e={}){const{proxyServer:t,proxyBypassList:r}=e,{browserContextId:i}=await this.#R.send("Target.createBrowserContext",{proxyServer:t,proxyBypassList:r&&r.join(",")}),n=new Xn(this.#R,this,i);return this.#Ks.set(i,n),n}browserContexts(){return[this.#$s,...Array.from(this.#Ks.values())]}defaultBrowserContext(){return this.#$s}async _disposeContext(e){e&&(await this.#R.send("Target.disposeBrowserContext",{browserContextId:e}),this.#Ks.delete(e))}#Ws=(e,t)=>{const{browserContextId:r}=e,i=r&&this.#Ks.has(r)?this.#Ks.get(r):this.#$s;if(!i)throw new Error("Missing browser context");const n=t=>this.#R._createSession(e,t),s=new Gn(e,t,i,this.#$n,n);return e.url?.startsWith("devtools://")?new zn(e,t,i,this.#$n,n,this.#Di,this.#xs??null):this.#Hs(s)?new Wn(e,t,i,this.#$n,n,this.#Di,this.#xs??null):"service_worker"===e.type||"shared_worker"===e.type?new Un(e,t,i,this.#$n,n):s};#Xt=async e=>{e._isTargetExposed()&&await e._initializedDeferred.valueOrThrow()===Kn.SUCCESS&&(this.emit("targetcreated",e),e.browserContext().emit("targetcreated",e))};#ws=async e=>{e._initializedDeferred.resolve(Kn.ABORTED),e._isClosedDeferred.resolve(),e._isTargetExposed()&&await e._initializedDeferred.valueOrThrow()===Kn.SUCCESS&&(this.emit("targetdestroyed",e),e.browserContext().emit("targetdestroyed",e))};#Us=({target:e})=>{this.emit("targetchanged",e),e.browserContext().emit("targetchanged",e)};#Gs=e=>{this.emit("targetdiscovered",e)};wsEndpoint(){return this.#R.url()}async newPage(){return await this.#$s.newPage()}async _createPageInContext(e){const{targetId:t}=await this.#R.send("Target.createTarget",{url:"about:blank",browserContextId:e||void 0}),r=await this.waitForTarget((e=>e._targetId===t));if(!r)throw new Error(`Missing target for page (id = ${t})`);if(!(await r._initializedDeferred.valueOrThrow()===Kn.SUCCESS))throw new Error(`Failed to create target for page (id = ${t})`);const i=await r.page();if(!i)throw new Error(`Failed to create a page for context (id = ${e})`);return i}targets(){return Array.from(this.#$n.getAvailableTargets().values()).filter((e=>e._isTargetExposed()&&e._initializedDeferred.value()===Kn.SUCCESS))}target(){const e=this.targets().find((e=>"browser"===e.type()));if(!e)throw new Error("Browser target is not found");return e}async version(){return(await this.#Qs()).product}async userAgent(){return(await this.#Qs()).userAgent}async close(){await this.#js.call(null),await this.disconnect()}disconnect(){return this.#$n.dispose(),this.#R.dispose(),this._detach(),Promise.resolve()}get connected(){return!this.#R._closed}#Qs(){return this.#R.send("Browser.getVersion")}get debugInfo(){return{pendingProtocolErrors:this.#R.getPendingProtocolErrors()}}}class Xn extends Lt{#R;#Js;#S;constructor(e,t,r){super(),this.#R=e,this.#Js=t,this.#S=r}get id(){return this.#S}targets(){return this.#Js.targets().filter((e=>e.browserContext()===this))}async pages(){return(await Promise.all(this.targets().filter((e=>"page"===e.type()||"other"===e.type()&&this.#Js._getIsPageTargetCallback()?.(e))).map((e=>e.page())))).filter((e=>!!e))}isIncognito(){return!!this.#S}async overridePermissions(e,t){const r=t.map((e=>{const t=Ot.get(e);if(!t)throw new Error("Unknown permission: "+e);return t}));await this.#R.send("Browser.grantPermissions",{origin:e,browserContextId:this.#S||void 0,permissions:r})}async clearPermissionOverrides(){await this.#R.send("Browser.resetPermissions",{browserContextId:this.#S||void 0})}newPage(){return this.#Js._createPageInContext(this.#S)}browser(){return this.#Js}async close(){st(this.#S,"Non-incognito profiles cannot be closed!"),await this.#Js._disposeContext(this.#S)}}export{Jn as Browser,Lr as Connection,qi as ElementHandle,dn as Frame,Hn as Page,Vn as Target};
