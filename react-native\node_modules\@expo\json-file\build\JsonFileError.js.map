{"version": 3, "file": "JsonFileError.js", "sourceRoot": "", "sources": ["../src/JsonFileError.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAqB,aAAc,SAAQ,KAAK;IAC9C,KAAK,CAAoB;IACzB,IAAI,CAAqB;IACzB,QAAQ,CAAqB;IAC7B,eAAe,CAAO;IAEtB,YAAY,OAAe,EAAE,KAAa,EAAE,IAAa,EAAE,QAAiB;QAC1E,IAAI,WAAW,GAAG,OAAO,CAAC;QAC1B,IAAI,QAAQ,EAAE,CAAC;YACb,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,QAAQ,EAAE,CAAC;QAC7D,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACV,WAAW,IAAI,eAAe,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;QAC/D,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;CACF;AArBD,gCAqBC;AAED,MAAa,kBAAmB,SAAQ,aAAa;IACnD,YAAY,QAAiB;QAC3B,KAAK,CAAC,mCAAmC,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAChF,CAAC;CACF;AAJD,gDAIC"}