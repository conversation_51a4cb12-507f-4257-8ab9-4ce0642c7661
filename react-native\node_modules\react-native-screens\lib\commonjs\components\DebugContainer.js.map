{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_AppContainer", "_interopRequireDefault", "_ScreenContentWrapper", "obj", "__esModule", "default", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "DebugContainer", "props", "createElement", "process", "env", "NODE_ENV", "stackPresentation", "rest", "Platform", "OS", "displayName", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/DebugContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,aAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAD,sBAAA,CAAAH,OAAA;AAA0D,SAAAG,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAH,UAAA,SAAAG,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAF,OAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAJ,CAAA,UAAAG,CAAA,CAAAE,GAAA,CAAAL,CAAA,OAAAM,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAY,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAN,CAAA,CAAAR,OAAA,GAAAE,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAc,GAAA,CAAAjB,CAAA,EAAAM,CAAA,GAAAA,CAAA;AAH1D;AACA;AAUA;AACA;AACA;AACA;AACA;AACA,IAAIY,cAAmD,GAAGC,KAAK,IAAI;EACjE,oBAAO9B,KAAA,CAAA+B,aAAA,CAACzB,qBAAA,CAAAG,OAAoB,EAAKqB,KAAQ,CAAC;AAC5C,CAAC;AAED,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAL,cAAc,GAAIC,KAAqB,IAAK;IAC1C,MAAM;MAAEK,iBAAiB;MAAE,GAAGC;IAAK,CAAC,GAAGN,KAAK;IAE5C,IACEO,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBH,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,WAAW,EACjC;MACA;MACA,oBACEnC,KAAA,CAAA+B,aAAA,CAAC3B,aAAA,CAAAK,OAAY,qBACXT,KAAA,CAAA+B,aAAA,CAACzB,qBAAA,CAAAG,OAAoB,EAAK2B,IAAO,CACrB,CAAC;IAEnB;IAEA,oBAAOpC,KAAA,CAAA+B,aAAA,CAACzB,qBAAA,CAAAG,OAAoB,EAAK2B,IAAO,CAAC;EAC3C,CAAC;EAEDP,cAAc,CAACU,WAAW,GAAG,gBAAgB;AAC/C;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAhC,OAAA,GAEcoB,cAAc"}