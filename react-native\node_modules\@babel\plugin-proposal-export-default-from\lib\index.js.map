{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "ExportNamedDeclaration", "path", "node", "specifiers", "source", "t", "isExportDefaultSpecifier", "exported", "shift", "every", "s", "isExportSpecifier", "unshift", "exportSpecifier", "identifier", "insertBefore", "exportNamedDeclaration", "cloneNode"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"proposal-export-default-from\",\n    manipulateOptions: (_, parser) => parser.plugins.push(\"exportDefaultFrom\"),\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const { node } = path;\n        const { specifiers, source } = node;\n        if (!t.isExportDefaultSpecifier(specifiers[0])) return;\n\n        const { exported } = specifiers.shift();\n\n        if (specifiers.every(s => t.isExportSpecifier(s))) {\n          specifiers.unshift(\n            t.exportSpecifier(t.identifier(\"default\"), exported),\n          );\n          return;\n        }\n\n        path.insertBefore(\n          t.exportNamedDeclaration(\n            null,\n            [t.exportSpecifier(t.identifier(\"default\"), exported)],\n            t.cloneNode(source),\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,8BAA8B;IACpCC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;IAE1EC,OAAO,EAAE;MACPC,sBAAsBA,CAACC,IAAI,EAAE;QAC3B,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,MAAM;UAAEE,UAAU;UAAEC;QAAO,CAAC,GAAGF,IAAI;QACnC,IAAI,CAACG,WAAC,CAACC,wBAAwB,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAEhD,MAAM;UAAEI;QAAS,CAAC,GAAGJ,UAAU,CAACK,KAAK,CAAC,CAAC;QAEvC,IAAIL,UAAU,CAACM,KAAK,CAACC,CAAC,IAAIL,WAAC,CAACM,iBAAiB,CAACD,CAAC,CAAC,CAAC,EAAE;UACjDP,UAAU,CAACS,OAAO,CAChBP,WAAC,CAACQ,eAAe,CAACR,WAAC,CAACS,UAAU,CAAC,SAAS,CAAC,EAAEP,QAAQ,CACrD,CAAC;UACD;QACF;QAEAN,IAAI,CAACc,YAAY,CACfV,WAAC,CAACW,sBAAsB,CACtB,IAAI,EACJ,CAACX,WAAC,CAACQ,eAAe,CAACR,WAAC,CAACS,UAAU,CAAC,SAAS,CAAC,EAAEP,QAAQ,CAAC,CAAC,EACtDF,WAAC,CAACY,SAAS,CAACb,MAAM,CACpB,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}