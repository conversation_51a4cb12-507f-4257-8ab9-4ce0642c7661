{"version": 3, "names": ["useEffect", "useRef", "isWeb", "isJest", "areDependenciesEqual", "buildDependencies", "makeShareable", "useHandler", "handlers", "dependencies", "initRef", "current", "context", "savedDependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb"], "sourceRoot": "../../../src", "sources": ["hook/useHandler.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,KAAK,EAAEC,MAAM,QAAQ,uBAAoB;AAElD,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,YAAS;AACjE,SAASC,aAAa,QAAQ,kBAAe;;AAmC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASC,UAAUA,CAIxBC,QAAgD,EAChDC,YAA6B,EACD;EAC5B,MAAMC,OAAO,GAAGT,MAAM,CAA0C,IAAI,CAAC;EACrE,IAAIS,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,OAAO,GAAGN,aAAa,CAAC,CAAC,CAAY,CAAC;IAC5CI,OAAO,CAACC,OAAO,GAAG;MAChBC,OAAO;MACPC,iBAAiB,EAAE;IACrB,CAAC;EACH;EAEAb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,OAAO,CAACC,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC,OAAO;IAAEC;EAAkB,CAAC,GAAGH,OAAO,CAACC,OAAO;EAEtDF,YAAY,GAAGJ,iBAAiB,CAC9BI,YAAY,EACZD,QACF,CAAC;EAED,MAAMM,oBAAoB,GAAG,CAACV,oBAAoB,CAChDK,YAAY,EACZI,iBACF,CAAC;EACDH,OAAO,CAACC,OAAO,CAACE,iBAAiB,GAAGJ,YAAY;EAChD,MAAMM,MAAM,GAAGb,KAAK,CAAC,CAAC,IAAIC,MAAM,CAAC,CAAC;EAElC,OAAO;IAAES,OAAO;IAAEE,oBAAoB;IAAEC;EAAO,CAAC;AAClD", "ignoreList": []}