﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Resources">
      <UniqueIdentifier>accd3aa8-1ba0-4223-9bbe-0c431709210b</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tga;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{926ab91d-31b5-48c3-b9a4-e681349f27f0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="ReactPackageProvider.cpp" />
    <ClCompile Include="RNScreens.cpp" />
    <ClCompile Include="RNScreens.cpp" />
    <ClCompile Include="Screen.cpp" />
    <ClCompile Include="ScreenStackHeaderConfigViewManager.cpp" />
    <ClCompile Include="ScreenViewManager.cpp" />
    <ClCompile Include="ScreenStackHeaderConfig.cpp" />
    <ClCompile Include="ScreenStackViewManager.cpp" />
    <ClCompile Include="ScreenStack.cpp" />
    <ClCompile Include="ScreenContainerViewManager.cpp" />
    <ClCompile Include="ScreenContainer.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="ReactPackageProvider.h" />
    <ClInclude Include="RNScreens.h" />
    <ClInclude Include="RNScreens.h" />
    <ClInclude Include="Screen.h" />
    <ClInclude Include="ScreenStackHeaderConfigViewManager.h" />
    <ClInclude Include="ScreenViewManager.h" />
    <ClInclude Include="ScreenStackHeaderConfig.h" />
    <ClInclude Include="ScreenStackViewManager.h" />
    <ClInclude Include="ScreenStack.h" />
    <ClInclude Include="ScreenContainer.h" />
    <ClInclude Include="ScreenContainerViewManager.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RNScreens.def" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="PropertySheet.props" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="ReactPackageProvider.idl" />
    <Midl Include="RNScreens.idl" />
  </ItemGroup>
</Project>