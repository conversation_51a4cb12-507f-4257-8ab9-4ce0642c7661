{"version": 3, "names": ["_reactNative", "require", "formSheetModalHeight", "getDefaultHeaderHeight", "layout", "statusBarHeight", "stackPresentation", "is<PERSON>arge<PERSON><PERSON>er", "arguments", "length", "undefined", "headerHeight", "Platform", "OS", "isLandscape", "width", "height", "isFormSheetModal", "isPad", "isTV"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getDefaultHeaderHeight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAIA,MAAMC,oBAAoB,GAAG,EAAE;AAEhB,SAASC,sBAAsBA,CAC5CC,MAAc,EACdC,eAAuB,EACvBC,iBAAyC,EAEjC;EAAA,IADRC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAErB;EACA,IAAIG,YAAY,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE;EAEtD,IAAID,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMC,WAAW,GAAGV,MAAM,CAACW,KAAK,GAAGX,MAAM,CAACY,MAAM;IAChD,MAAMC,gBAAgB,GACpBX,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,WAAW;IACpE,IAAIW,gBAAgB,IAAI,CAACH,WAAW,EAAE;MACpC;MACAT,eAAe,GAAG,CAAC;IACrB;IAEA,IAAIO,qBAAQ,CAACM,KAAK,IAAIN,qBAAQ,CAACO,IAAI,EAAE;MACnCR,YAAY,GAAGM,gBAAgB,GAAGf,oBAAoB,GAAG,EAAE;IAC7D,CAAC,MAAM;MACL,IAAIY,WAAW,EAAE;QACfH,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIM,gBAAgB,EAAE;UACpBN,YAAY,GAAGT,oBAAoB;QACrC,CAAC,MAAM;UACLS,YAAY,GAAGJ,aAAa,GAAG,EAAE,GAAG,EAAE;QACxC;MACF;IACF;EACF;EAEA,OAAOI,YAAY,GAAGN,eAAe;AACvC"}