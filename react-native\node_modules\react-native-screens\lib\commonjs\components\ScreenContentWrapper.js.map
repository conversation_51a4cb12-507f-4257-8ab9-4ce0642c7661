{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenContentWrapperNativeComponent", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "ScreenContentWrapper", "props", "createElement", "collapsable", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ScreenContentWrapper.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,oCAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAgG,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAEhG,SAASQ,oBAAoBA,CAACC,KAAgB,EAAE;EAC9C,oBAAOtB,MAAA,CAAAM,OAAA,CAAAiB,aAAA,CAACpB,oCAAA,CAAAG,OAAmC,EAAAC,QAAA;IAACiB,WAAW,EAAE;EAAM,GAAKF,KAAK,CAAG,CAAC;AAC/E;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAApB,OAAA,GAEce,oBAAoB"}