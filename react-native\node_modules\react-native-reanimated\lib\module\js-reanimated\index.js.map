{"version": 3, "names": ["JSReanimated", "createReactDOMStyle", "createTransformValue", "createTextShadowValue", "PropsAllowlists", "logger", "ReanimatedError", "reanimatedJS", "global", "_makeShareableClone", "_scheduleHostFunctionOnJS", "_scheduleRemoteFunctionOnJS", "_scheduleOnRuntime", "_updatePropsJS", "updates", "viewRef", "isAnimatedProps", "component", "getAnimatableRef", "rawStyles", "Object", "keys", "reduce", "acc", "key", "value", "index", "setNativeProps", "undefined", "style", "updatePropsDOM", "props", "length", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "replace", "m", "toLowerCase", "_touchableNode", "setAttribute", "componentName", "className", "warn", "newProps", "uiProps", "isNativeProp", "previousStyle", "currentStyle", "domStyle", "Array", "isArray", "transform", "textShadowColor", "textShadowRadius", "textShadowOffset", "textShadow", "propName", "NATIVE_THREAD_PROPS_WHITELIST"], "sourceRoot": "../../../src", "sources": ["js-reanimated/index.ts"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,YAAY,MAAM,mBAAgB;AAEzC,SACEC,mBAAmB,EACnBC,oBAAoB,EACpBC,qBAAqB,QAChB,YAAY;AACnB,SAASC,eAAe,QAAQ,uBAAoB;AACpD,SAASC,MAAM,QAAQ,oBAAW;AAClC,SAASC,eAAe,QAAQ,cAAW;AAE3C,MAAMC,YAAY,GAAG,IAAIP,YAAY,CAAC,CAAC;AAEvCQ,MAAM,CAACC,mBAAmB,GAAG,MAAM;EACjC,MAAM,IAAIH,eAAe,CACvB,6DACF,CAAC;AACH,CAAC;AAEDE,MAAM,CAACE,yBAAyB,GAAG,MAAM;EACvC,MAAM,IAAIJ,eAAe,CACvB,mEACF,CAAC;AACH,CAAC;AAEDE,MAAM,CAACG,2BAA2B,GAAG,MAAM;EACzC,MAAM,IAAIL,eAAe,CACvB,mEACF,CAAC;AACH,CAAC;AAEDE,MAAM,CAACI,kBAAkB,GAAG,MAAM;EAChC,MAAM,IAAIN,eAAe,CACvB,4DACF,CAAC;AACH,CAAC;AAuBD,OAAO,MAAMO,cAAc,GAAGA,CAE5BC,OAAwC,EACxCC,OAEC,EACDC,eAAyB,KAChB;EACT,IAAID,OAAO,EAAE;IACX,MAAME,SAAS,GAAGF,OAAO,CAACG,gBAAgB,GACtCH,OAAO,CAACG,gBAAgB,CAAC,CAAC,GAC1BH,OAAO;IACX,MAAM,CAACI,SAAS,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,CAC7C,CAACC,GAAqC,EAAEC,GAAG,KAAK;MAC9C,MAAMC,KAAK,GAAGX,OAAO,CAACU,GAAG,CAAC;MAC1B,MAAME,KAAK,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;MACjDF,GAAG,CAACG,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGC,KAAK;MACvB,OAAOF,GAAG;IACZ,CAAC,EACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACT,CAAC;IAED,IAAI,OAAON,SAAS,CAACU,cAAc,KAAK,UAAU,EAAE;MAClD;MACA;MACA;MACAA,cAAc,CAACV,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IACLf,mBAAmB,KAAK2B,SAAS,IACjCX,SAAS,CAACY,KAAK,KAAKD,SAAS,EAC7B;MACA;MACA;MACAE,cAAc,CAACb,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IAAII,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClDZ,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACE,OAAO,CAAET,GAAG,IAAK;QAC5C,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,EAAE;UACnB;QACF;QACA,MAAMU,SAAS,GAAGV,GAAG,CAACW,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACrEpB,SAAS,CAACqB,cAAc,CAACC,YAAY,CAACL,SAAS,EAAEf,SAAS,CAACK,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMgB,aAAa,GACjB,WAAW,IAAIvB,SAAS,GAAGA,SAAS,EAAEwB,SAAS,GAAG,EAAE;MACtDpC,MAAM,CAACqC,IAAI,CACT,iDAAiDF,aAAa,EAChE,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMb,cAAc,GAAGA,CACrBV,SAAwD,EACxD0B,QAAoB,EACpB3B,eAAyB,KAChB;EACT,IAAIA,eAAe,EAAE;IACnB,MAAM4B,OAAgC,GAAG,CAAC,CAAC;IAC3C,KAAK,MAAMpB,GAAG,IAAImB,QAAQ,EAAE;MAC1B,IAAIE,YAAY,CAACrB,GAAG,CAAC,EAAE;QACrBoB,OAAO,CAACpB,GAAG,CAAC,GAAGmB,QAAQ,CAACnB,GAAG,CAAC;MAC9B;IACF;IACA;IACA;IACAP,SAAS,CAACU,cAAc,GAAGiB,OAAO,CAAC;EACrC;EAEA,MAAME,aAAa,GAAG7B,SAAS,CAAC6B,aAAa,GAAG7B,SAAS,CAAC6B,aAAa,GAAG,CAAC,CAAC;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGH;EAAS,CAAC;EACtD1B,SAAS,CAAC6B,aAAa,GAAGC,YAAY;EAEtC9B,SAAS,CAACU,cAAc,GAAG;IAAEE,KAAK,EAAEkB;EAAa,CAAC,CAAC;AACrD,CAAC;AAED,MAAMjB,cAAc,GAAGA,CACrBb,SAA8C,EAC9CY,KAAiB,EACjBb,eAAyB,KAChB;EACT,MAAM8B,aAAa,GAAI7B,SAAS,CAA2B6B,aAAa,GACnE7B,SAAS,CAA2B6B,aAAa,GAClD,CAAC,CAAC;EACN,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGjB;EAAM,CAAC;EAClDZ,SAAS,CAA2B6B,aAAa,GAAGC,YAAY;EAEjE,MAAMC,QAAQ,GAAG/C,mBAAmB,CAAC8C,YAAY,CAAC;EAClD,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACG,SAAS,CAAC,IAAIjD,oBAAoB,KAAK0B,SAAS,EAAE;IAC3EoB,QAAQ,CAACG,SAAS,GAAGjD,oBAAoB,CAAC8C,QAAQ,CAACG,SAAS,CAAC;EAC/D;EAEA,IACEhD,qBAAqB,KAAKyB,SAAS,KAClCoB,QAAQ,CAACI,eAAe,IACvBJ,QAAQ,CAACK,gBAAgB,IACzBL,QAAQ,CAACM,gBAAgB,CAAC,EAC5B;IACAN,QAAQ,CAACO,UAAU,GAAGpD,qBAAqB,CAAC;MAC1CiD,eAAe,EAAEJ,QAAQ,CAACI,eAAe;MACzCE,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;MAC3CD,gBAAgB,EAAEL,QAAQ,CAACK;IAC7B,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM7B,GAAG,IAAIwB,QAAQ,EAAE;IAC1B,IAAIhC,eAAe,EAAE;MAClBC,SAAS,CAAiBsB,YAAY,CAACf,GAAG,EAAEwB,QAAQ,CAACxB,GAAG,CAAC,CAAC;IAC7D,CAAC,MAAM;MACJP,SAAS,CAACY,KAAK,CAAgBL,GAAG,CAAC,GAAGwB,QAAQ,CAACxB,GAAG,CAAC;IACtD;EACF;AACF,CAAC;AAED,SAASqB,YAAYA,CAACW,QAAgB,EAAW;EAC/C,OAAO,CAAC,CAACpD,eAAe,CAACqD,6BAA6B,CAACD,QAAQ,CAAC;AAClE;AAEA,eAAejD,YAAY", "ignoreList": []}