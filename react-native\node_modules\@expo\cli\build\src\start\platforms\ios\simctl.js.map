{"version": 3, "sources": ["../../../../../src/start/platforms/ios/simctl.ts"], "sourcesContent": ["import spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport bplistCreator from 'bplist-creator';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nimport { isSpawnResultError, xcrunAsync } from './xcrun';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { memoize } from '../../../utils/fn';\nimport { parsePlistAsync } from '../../../utils/plist';\nimport { profile } from '../../../utils/profile';\n\nconst debug = require('debug')('expo:simctl') as typeof console.log;\n\ntype DeviceState = 'Shutdown' | 'Booted';\n\nexport type OSType = 'iOS' | 'tvOS' | 'watchOS' | 'macOS' | 'xrOS';\n\nexport type Device = {\n  availabilityError?: 'runtime profile not found';\n  /** '/Users/<USER>/Library/Developer/CoreSimulator/Devices/00E55DC0-0364-49DF-9EC6-77BE587137D4/data' */\n  dataPath: string;\n  /** @example `2811236352` */\n  dataPathSize?: number;\n  /** '/Users/<USER>/Library/Logs/CoreSimulator/00E55DC0-0364-49DF-9EC6-77BE587137D4' */\n  logPath: string;\n  /** @example `479232` */\n  logPathSize?: number;\n  /** '00E55DC0-0364-49DF-9EC6-77BE587137D4' */\n  udid: string;\n  /** 'com.apple.CoreSimulator.SimRuntime.iOS-15-1' */\n  runtime: string;\n  /** If the device is \"available\" which generally means that the OS files haven't been deleted (this can happen when Xcode updates).  */\n  isAvailable: boolean;\n  /** 'com.apple.CoreSimulator.SimDeviceType.iPhone-13-Pro' */\n  deviceTypeIdentifier: string;\n  state: DeviceState;\n  /** 'iPhone 13 Pro' */\n  name: string;\n  /** Type of OS the device uses. */\n  osType: OSType;\n  /** '15.1' */\n  osVersion: string;\n  /** 'iPhone 13 Pro (15.1)' */\n  windowName: string;\n};\n\ntype SimulatorDeviceList = {\n  devices: {\n    [runtime: string]: Device[];\n  };\n};\n\ntype DeviceContext = Pick<Device, 'udid'>;\n\n/** Returns true if the given value is an `OSType`, if we don't recognize the value we continue anyways but warn. */\nexport function isOSType(value: any): value is OSType {\n  if (!value || typeof value !== 'string') return false;\n\n  const knownTypes = ['iOS', 'tvOS', 'watchOS', 'macOS'];\n  if (!knownTypes.includes(value)) {\n    Log.warn(`Unknown OS type: ${value}. Expected one of: ${knownTypes.join(', ')}`);\n  }\n  return true;\n}\n\n/**\n * Returns the local path for the installed tar.app. Returns null when the app isn't installed.\n *\n * @param device context for selecting a device.\n * @param props.appId bundle identifier for app.\n * @returns local file path to installed app binary, e.g. '/Users/<USER>/Library/Developer/CoreSimulator/Devices/EFEEA6EF-E3F5-4EDE-9B72-29EAFA7514AE/data/Containers/Bundle/Application/FA43A0C6-C2AD-442D-B8B1-EAF3E88CF3BF/Exponent-2.21.3.tar.app'\n */\nexport async function getContainerPathAsync(\n  device: Partial<DeviceContext>,\n  {\n    appId,\n  }: {\n    appId: string;\n  }\n): Promise<string | null> {\n  try {\n    const { stdout } = await simctlAsync(['get_app_container', resolveId(device), appId]);\n    return stdout.trim();\n  } catch (error: any) {\n    if (error.stderr?.match(/No such file or directory/)) {\n      return null;\n    }\n    throw error;\n  }\n}\n\n/** Return a value from an installed app's Info.plist. */\nexport async function getInfoPlistValueAsync(\n  device: Partial<DeviceContext>,\n  {\n    appId,\n    key,\n    containerPath,\n  }: {\n    appId: string;\n    key: string;\n    containerPath?: string;\n  }\n): Promise<string | null> {\n  const ensuredContainerPath = containerPath ?? (await getContainerPathAsync(device, { appId }));\n  if (ensuredContainerPath) {\n    try {\n      const { output } = await spawnAsync(\n        'defaults',\n        ['read', `${ensuredContainerPath}/Info`, key],\n        {\n          stdio: 'pipe',\n        }\n      );\n      return output.join('\\n').trim();\n    } catch {\n      return null;\n    }\n  }\n  return null;\n}\n\n/** Rewrite the simulator permissions to allow opening deep links without needing to prompt the user first. */\nasync function updateSimulatorLinkingPermissionsAsync(\n  device: Partial<DeviceContext>,\n  { url, appId }: { url: string; appId?: string }\n) {\n  if (!device.udid || !appId) {\n    debug('Skipping deep link permissions as missing properties could not be found:', {\n      url,\n      appId,\n      udid: device.udid,\n    });\n    return;\n  }\n  debug('Rewriting simulator permissions to support deep linking:', {\n    url,\n    appId,\n    udid: device.udid,\n  });\n  let scheme: string;\n  try {\n    // Attempt to extract the scheme from the URL.\n    scheme = new URL(url).protocol.slice(0, -1);\n  } catch (error: any) {\n    debug(`Could not parse the URL scheme: ${error.message}`);\n    return;\n  }\n\n  // Get the hard-coded path to the simulator's scheme approval plist file.\n  const plistPath = path.join(\n    os.homedir(),\n    `Library/Developer/CoreSimulator/Devices`,\n    device.udid,\n    `data/Library/Preferences/com.apple.launchservices.schemeapproval.plist`\n  );\n\n  const plistData = fs.existsSync(plistPath)\n    ? // If the file exists, then read it in the bplist format.\n      await parsePlistAsync(plistPath)\n    : // The file doesn't exist when we first launch the simulator, but an empty object can be used to create it (June 2024 x Xcode 15.3).\n      // Can be tested by launching a new simulator or by deleting the file and relaunching the simulator.\n      {};\n\n  debug('Allowed links:', plistData);\n  const key = `com.apple.CoreSimulator.CoreSimulatorBridge-->${scheme}`;\n  // Replace any existing value for the scheme with the new appId.\n  plistData[key] = appId;\n  debug('Allowing deep link:', { key, appId });\n\n  try {\n    const data = bplistCreator(plistData);\n    // Write the updated plist back to disk\n    await fs.promises.writeFile(plistPath, data);\n  } catch (error: any) {\n    Log.warn(`Could not update simulator linking permissions: ${error.message}`);\n  }\n}\n\nconst updateSimulatorLinkingPermissionsAsyncMemo = memoize(updateSimulatorLinkingPermissionsAsync);\n\n/** Open a URL on a device. The url can have any protocol. */\nexport async function openUrlAsync(\n  device: Partial<DeviceContext>,\n  options: { url: string; appId?: string }\n): Promise<void> {\n  if (options.appId) {\n    await profile(\n      updateSimulatorLinkingPermissionsAsyncMemo,\n      'updateSimulatorLinkingPermissionsAsync'\n    )({ udid: device.udid }, options);\n  }\n\n  try {\n    // Skip logging since this is likely to fail.\n    await simctlAsync(['openurl', resolveId(device), options.url]);\n  } catch (error: any) {\n    if (!error.stderr?.match(/Unable to lookup in current state: Shut/)) {\n      throw error;\n    }\n\n    // If the device was in a weird in-between state (\"Shutting Down\" or \"Shutdown\"), then attempt to reboot it and try again.\n    // This can happen when quitting the Simulator app, and immediately pressing `i` to reopen the project.\n\n    // First boot the simulator\n    await bootDeviceAsync({ udid: resolveId(device) });\n\n    // Finally, try again...\n    return await openUrlAsync(device, options);\n  }\n}\n\n/** Open a simulator using a bundle identifier. If no app with a matching bundle identifier is installed then an error will be thrown. */\nexport async function openAppIdAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    appId: string;\n  }\n): Promise<SpawnResult> {\n  const results = await openAppIdInternalAsync(device, options);\n  // Similar to 194, this is a conformance issue which indicates that the given device has no app that can handle our launch request.\n  if (results.status === 4) {\n    throw new CommandError('APP_NOT_INSTALLED', results.stderr);\n  }\n  return results;\n}\nasync function openAppIdInternalAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    appId: string;\n  }\n): Promise<SpawnResult> {\n  try {\n    return await simctlAsync(['launch', resolveId(device), options.appId]);\n  } catch (error: any) {\n    if ('status' in error) {\n      return error;\n    }\n    throw error;\n  }\n}\n\n// This will only boot in headless mode if the Simulator app is not running.\nexport async function bootAsync(device: DeviceContext): Promise<Device | null> {\n  await bootDeviceAsync(device);\n  return isDeviceBootedAsync(device);\n}\n\n/** Returns a list of devices whose current state is 'Booted' as an array. */\nexport async function getBootedSimulatorsAsync(): Promise<Device[]> {\n  const simulatorDeviceInfo = await getRuntimesAsync('devices');\n  return Object.values(simulatorDeviceInfo.devices).flatMap((runtime) =>\n    runtime.filter((device) => device.state === 'Booted')\n  );\n}\n\n/** Returns the current device if its state is 'Booted'. */\nexport async function isDeviceBootedAsync(device: Partial<DeviceContext>): Promise<Device | null> {\n  // Simulators can be booted even if the app isn't running :(\n  const devices = await getBootedSimulatorsAsync();\n  if (device.udid) {\n    return devices.find((bootedDevice) => bootedDevice.udid === device.udid) ?? null;\n  }\n\n  return devices[0] ?? null;\n}\n\n/** Boot a device. */\nexport async function bootDeviceAsync(device: DeviceContext): Promise<void> {\n  try {\n    // Skip logging since this is likely to fail.\n    await simctlAsync(['boot', device.udid]);\n  } catch (error: any) {\n    if (!error.stderr?.match(/Unable to boot device in current state: Booted/)) {\n      throw error;\n    }\n  }\n}\n\n/** Install a binary file on the device. */\nexport async function installAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    /** Local absolute file path to an app binary that is built and provisioned for iOS simulators. */\n    filePath: string;\n  }\n): Promise<any> {\n  return simctlAsync(['install', resolveId(device), options.filePath]);\n}\n\n/** Uninstall an app from the provided device. */\nexport async function uninstallAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    /** Bundle identifier */\n    appId: string;\n  }\n): Promise<any> {\n  return simctlAsync(['uninstall', resolveId(device), options.appId]);\n}\n\nfunction parseSimControlJSONResults(input: string): any {\n  try {\n    return JSON.parse(input);\n  } catch (error: any) {\n    // Nov 15, 2020: Observed this can happen when opening the simulator and the simulator prompts the user to update the xcode command line tools.\n    // Unexpected token I in JSON at position 0\n    if (error.message.includes('Unexpected token')) {\n      Log.error(`Apple's simctl returned malformed JSON:\\n${input}`);\n    }\n    throw error;\n  }\n}\n\n/** Get all runtime devices given a certain type. */\nasync function getRuntimesAsync(\n  type: 'devices' | 'devicetypes' | 'runtimes' | 'pairs',\n  query?: string | 'available'\n): Promise<SimulatorDeviceList> {\n  const result = await simctlAsync(['list', type, '--json', query]);\n  const info = parseSimControlJSONResults(result.stdout) as SimulatorDeviceList;\n\n  for (const runtime of Object.keys(info.devices)) {\n    // Given a string like 'com.apple.CoreSimulator.SimRuntime.tvOS-13-4'\n    const runtimeSuffix = runtime.split('com.apple.CoreSimulator.SimRuntime.').pop()!;\n    // Create an array [tvOS, 13, 4]\n    const [osType, ...osVersionComponents] = runtimeSuffix.split('-');\n    // Join the end components [13, 4] -> '13.4'\n    const osVersion = osVersionComponents.join('.');\n    const sims = info.devices[runtime];\n    for (const device of sims) {\n      device.runtime = runtime;\n      device.osVersion = osVersion;\n      device.windowName = `${device.name} (${osVersion})`;\n      device.osType = osType as OSType;\n    }\n  }\n  return info;\n}\n\n/** Return a list of iOS simulators. */\nexport async function getDevicesAsync(): Promise<Device[]> {\n  const simulatorDeviceInfo = await getRuntimesAsync('devices');\n  return Object.values(simulatorDeviceInfo.devices).flat();\n}\n\n/** Run a `simctl` command. */\nexport async function simctlAsync(\n  args: (string | undefined)[],\n  options?: SpawnOptions\n): Promise<SpawnResult> {\n  try {\n    return await xcrunAsync(['simctl', ...args], options);\n  } catch (error) {\n    if (isSpawnResultError(error)) {\n      // TODO: Add more tips.\n      // if (error.status === 115) {\n      // }\n    }\n    throw error;\n  }\n}\n\nfunction resolveId(device: Partial<DeviceContext>): string {\n  return device.udid ?? 'booted';\n}\n"], "names": ["bootAsync", "bootDeviceAsync", "getBootedSimulatorsAsync", "getContainerPathAsync", "getDevicesAsync", "getInfoPlistValueAsync", "installAsync", "isDeviceBootedAsync", "isOSType", "openAppIdAsync", "openUrlAsync", "simctlAsync", "uninstallAsync", "debug", "require", "value", "knownTypes", "includes", "Log", "warn", "join", "device", "appId", "stdout", "resolveId", "trim", "error", "stderr", "match", "key", "containerPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "output", "spawnAsync", "stdio", "updateSimulatorLinkingPermissionsAsync", "url", "udid", "scheme", "URL", "protocol", "slice", "message", "plist<PERSON><PERSON>", "path", "os", "homedir", "plistData", "fs", "existsSync", "parsePlistAsync", "data", "bplistCreator", "promises", "writeFile", "updateSimulatorLinkingPermissionsAsyncMemo", "memoize", "options", "profile", "results", "openAppIdInternalAsync", "status", "CommandError", "simulatorDeviceInfo", "getRuntimesAsync", "Object", "values", "devices", "flatMap", "runtime", "filter", "state", "find", "bootedDevice", "filePath", "parseSimControlJSONResults", "input", "JSON", "parse", "type", "query", "result", "info", "keys", "runtimeSuffix", "split", "pop", "osType", "osVersionComponents", "osVersion", "sims", "windowName", "name", "flat", "args", "xcrunAsync", "isSpawnResultError"], "mappings": ";;;;;;;;;;;IAqPsBA,SAAS;eAATA;;IAyBAC,eAAe;eAAfA;;IAnBAC,wBAAwB;eAAxBA;;IAjLAC,qBAAqB;eAArBA;;IA6QAC,eAAe;eAAfA;;IAzPAC,sBAAsB;eAAtBA;;IA4LAC,YAAY;eAAZA;;IAvBAC,mBAAmB;eAAnBA;;IA1MNC,QAAQ;eAARA;;IA8JMC,cAAc;eAAdA;;IA/BAC,YAAY;eAAZA;;IAqKAC,WAAW;eAAXA;;IAxDAC,cAAc;eAAdA;;;;gEArSgC;;;;;;;gEAC5B;;;;;;;gEACX;;;;;;;gEACA;;;;;;;gEACE;;;;;;uBAE8B;6DAC1B;wBACQ;oBACL;uBACQ;yBACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,MAAMC,QAAQC,QAAQ,SAAS;AA4CxB,SAASN,SAASO,KAAU;IACjC,IAAI,CAACA,SAAS,OAAOA,UAAU,UAAU,OAAO;IAEhD,MAAMC,aAAa;QAAC;QAAO;QAAQ;QAAW;KAAQ;IACtD,IAAI,CAACA,WAAWC,QAAQ,CAACF,QAAQ;QAC/BG,KAAIC,IAAI,CAAC,CAAC,iBAAiB,EAAEJ,MAAM,mBAAmB,EAAEC,WAAWI,IAAI,CAAC,OAAO;IACjF;IACA,OAAO;AACT;AASO,eAAejB,sBACpBkB,MAA8B,EAC9B,EACEC,KAAK,EAGN;IAED,IAAI;QACF,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAMZ,YAAY;YAAC;YAAqBa,UAAUH;YAASC;SAAM;QACpF,OAAOC,OAAOE,IAAI;IACpB,EAAE,OAAOC,OAAY;YACfA;QAAJ,KAAIA,gBAAAA,MAAMC,MAAM,qBAAZD,cAAcE,KAAK,CAAC,8BAA8B;YACpD,OAAO;QACT;QACA,MAAMF;IACR;AACF;AAGO,eAAerB,uBACpBgB,MAA8B,EAC9B,EACEC,KAAK,EACLO,GAAG,EACHC,aAAa,EAKd;IAED,MAAMC,uBAAuBD,iBAAkB,MAAM3B,sBAAsBkB,QAAQ;QAAEC;IAAM;IAC3F,IAAIS,sBAAsB;QACxB,IAAI;YACF,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAMC,IAAAA,qBAAU,EACjC,YACA;gBAAC;gBAAQ,GAAGF,qBAAqB,KAAK,CAAC;gBAAEF;aAAI,EAC7C;gBACEK,OAAO;YACT;YAEF,OAAOF,OAAOZ,IAAI,CAAC,MAAMK,IAAI;QAC/B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,4GAA4G,GAC5G,eAAeU,uCACbd,MAA8B,EAC9B,EAAEe,GAAG,EAAEd,KAAK,EAAmC;IAE/C,IAAI,CAACD,OAAOgB,IAAI,IAAI,CAACf,OAAO;QAC1BT,MAAM,4EAA4E;YAChFuB;YACAd;YACAe,MAAMhB,OAAOgB,IAAI;QACnB;QACA;IACF;IACAxB,MAAM,4DAA4D;QAChEuB;QACAd;QACAe,MAAMhB,OAAOgB,IAAI;IACnB;IACA,IAAIC;IACJ,IAAI;QACF,8CAA8C;QAC9CA,SAAS,IAAIC,IAAIH,KAAKI,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3C,EAAE,OAAOf,OAAY;QACnBb,MAAM,CAAC,gCAAgC,EAAEa,MAAMgB,OAAO,EAAE;QACxD;IACF;IAEA,yEAAyE;IACzE,MAAMC,YAAYC,eAAI,CAACxB,IAAI,CACzByB,aAAE,CAACC,OAAO,IACV,CAAC,uCAAuC,CAAC,EACzCzB,OAAOgB,IAAI,EACX,CAAC,sEAAsE,CAAC;IAG1E,MAAMU,YAAYC,aAAE,CAACC,UAAU,CAACN,aAE5B,MAAMO,IAAAA,sBAAe,EAACP,aAEtB,oGAAoG;IACpG,CAAC;IAEL9B,MAAM,kBAAkBkC;IACxB,MAAMlB,MAAM,CAAC,8CAA8C,EAAES,QAAQ;IACrE,gEAAgE;IAChES,SAAS,CAAClB,IAAI,GAAGP;IACjBT,MAAM,uBAAuB;QAAEgB;QAAKP;IAAM;IAE1C,IAAI;QACF,MAAM6B,OAAOC,IAAAA,wBAAa,EAACL;QAC3B,uCAAuC;QACvC,MAAMC,aAAE,CAACK,QAAQ,CAACC,SAAS,CAACX,WAAWQ;IACzC,EAAE,OAAOzB,OAAY;QACnBR,KAAIC,IAAI,CAAC,CAAC,gDAAgD,EAAEO,MAAMgB,OAAO,EAAE;IAC7E;AACF;AAEA,MAAMa,6CAA6CC,IAAAA,WAAO,EAACrB;AAGpD,eAAezB,aACpBW,MAA8B,EAC9BoC,OAAwC;IAExC,IAAIA,QAAQnC,KAAK,EAAE;QACjB,MAAMoC,IAAAA,gBAAO,EACXH,4CACA,0CACA;YAAElB,MAAMhB,OAAOgB,IAAI;QAAC,GAAGoB;IAC3B;IAEA,IAAI;QACF,6CAA6C;QAC7C,MAAM9C,YAAY;YAAC;YAAWa,UAAUH;YAASoC,QAAQrB,GAAG;SAAC;IAC/D,EAAE,OAAOV,OAAY;YACdA;QAAL,IAAI,GAACA,gBAAAA,MAAMC,MAAM,qBAAZD,cAAcE,KAAK,CAAC,6CAA4C;YACnE,MAAMF;QACR;QAEA,0HAA0H;QAC1H,uGAAuG;QAEvG,2BAA2B;QAC3B,MAAMzB,gBAAgB;YAAEoC,MAAMb,UAAUH;QAAQ;QAEhD,wBAAwB;QACxB,OAAO,MAAMX,aAAaW,QAAQoC;IACpC;AACF;AAGO,eAAehD,eACpBY,MAA8B,EAC9BoC,OAEC;IAED,MAAME,UAAU,MAAMC,uBAAuBvC,QAAQoC;IACrD,mIAAmI;IACnI,IAAIE,QAAQE,MAAM,KAAK,GAAG;QACxB,MAAM,IAAIC,oBAAY,CAAC,qBAAqBH,QAAQhC,MAAM;IAC5D;IACA,OAAOgC;AACT;AACA,eAAeC,uBACbvC,MAA8B,EAC9BoC,OAEC;IAED,IAAI;QACF,OAAO,MAAM9C,YAAY;YAAC;YAAUa,UAAUH;YAASoC,QAAQnC,KAAK;SAAC;IACvE,EAAE,OAAOI,OAAY;QACnB,IAAI,YAAYA,OAAO;YACrB,OAAOA;QACT;QACA,MAAMA;IACR;AACF;AAGO,eAAe1B,UAAUqB,MAAqB;IACnD,MAAMpB,gBAAgBoB;IACtB,OAAOd,oBAAoBc;AAC7B;AAGO,eAAenB;IACpB,MAAM6D,sBAAsB,MAAMC,iBAAiB;IACnD,OAAOC,OAAOC,MAAM,CAACH,oBAAoBI,OAAO,EAAEC,OAAO,CAAC,CAACC,UACzDA,QAAQC,MAAM,CAAC,CAACjD,SAAWA,OAAOkD,KAAK,KAAK;AAEhD;AAGO,eAAehE,oBAAoBc,MAA8B;IACtE,4DAA4D;IAC5D,MAAM8C,UAAU,MAAMjE;IACtB,IAAImB,OAAOgB,IAAI,EAAE;QACf,OAAO8B,QAAQK,IAAI,CAAC,CAACC,eAAiBA,aAAapC,IAAI,KAAKhB,OAAOgB,IAAI,KAAK;IAC9E;IAEA,OAAO8B,OAAO,CAAC,EAAE,IAAI;AACvB;AAGO,eAAelE,gBAAgBoB,MAAqB;IACzD,IAAI;QACF,6CAA6C;QAC7C,MAAMV,YAAY;YAAC;YAAQU,OAAOgB,IAAI;SAAC;IACzC,EAAE,OAAOX,OAAY;YACdA;QAAL,IAAI,GAACA,gBAAAA,MAAMC,MAAM,qBAAZD,cAAcE,KAAK,CAAC,oDAAmD;YAC1E,MAAMF;QACR;IACF;AACF;AAGO,eAAepB,aACpBe,MAA8B,EAC9BoC,OAGC;IAED,OAAO9C,YAAY;QAAC;QAAWa,UAAUH;QAASoC,QAAQiB,QAAQ;KAAC;AACrE;AAGO,eAAe9D,eACpBS,MAA8B,EAC9BoC,OAGC;IAED,OAAO9C,YAAY;QAAC;QAAaa,UAAUH;QAASoC,QAAQnC,KAAK;KAAC;AACpE;AAEA,SAASqD,2BAA2BC,KAAa;IAC/C,IAAI;QACF,OAAOC,KAAKC,KAAK,CAACF;IACpB,EAAE,OAAOlD,OAAY;QACnB,+IAA+I;QAC/I,2CAA2C;QAC3C,IAAIA,MAAMgB,OAAO,CAACzB,QAAQ,CAAC,qBAAqB;YAC9CC,KAAIQ,KAAK,CAAC,CAAC,yCAAyC,EAAEkD,OAAO;QAC/D;QACA,MAAMlD;IACR;AACF;AAEA,kDAAkD,GAClD,eAAesC,iBACbe,IAAsD,EACtDC,KAA4B;IAE5B,MAAMC,SAAS,MAAMtE,YAAY;QAAC;QAAQoE;QAAM;QAAUC;KAAM;IAChE,MAAME,OAAOP,2BAA2BM,OAAO1D,MAAM;IAErD,KAAK,MAAM8C,WAAWJ,OAAOkB,IAAI,CAACD,KAAKf,OAAO,EAAG;QAC/C,qEAAqE;QACrE,MAAMiB,gBAAgBf,QAAQgB,KAAK,CAAC,uCAAuCC,GAAG;QAC9E,gCAAgC;QAChC,MAAM,CAACC,QAAQ,GAAGC,oBAAoB,GAAGJ,cAAcC,KAAK,CAAC;QAC7D,4CAA4C;QAC5C,MAAMI,YAAYD,oBAAoBpE,IAAI,CAAC;QAC3C,MAAMsE,OAAOR,KAAKf,OAAO,CAACE,QAAQ;QAClC,KAAK,MAAMhD,UAAUqE,KAAM;YACzBrE,OAAOgD,OAAO,GAAGA;YACjBhD,OAAOoE,SAAS,GAAGA;YACnBpE,OAAOsE,UAAU,GAAG,GAAGtE,OAAOuE,IAAI,CAAC,EAAE,EAAEH,UAAU,CAAC,CAAC;YACnDpE,OAAOkE,MAAM,GAAGA;QAClB;IACF;IACA,OAAOL;AACT;AAGO,eAAe9E;IACpB,MAAM2D,sBAAsB,MAAMC,iBAAiB;IACnD,OAAOC,OAAOC,MAAM,CAACH,oBAAoBI,OAAO,EAAE0B,IAAI;AACxD;AAGO,eAAelF,YACpBmF,IAA4B,EAC5BrC,OAAsB;IAEtB,IAAI;QACF,OAAO,MAAMsC,IAAAA,iBAAU,EAAC;YAAC;eAAaD;SAAK,EAAErC;IAC/C,EAAE,OAAO/B,OAAO;QACd,IAAIsE,IAAAA,yBAAkB,EAACtE,QAAQ;QAC7B,uBAAuB;QACvB,8BAA8B;QAC9B,IAAI;QACN;QACA,MAAMA;IACR;AACF;AAEA,SAASF,UAAUH,MAA8B;IAC/C,OAAOA,OAAOgB,IAAI,IAAI;AACxB"}