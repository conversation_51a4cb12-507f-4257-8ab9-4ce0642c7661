{"version": 3, "names": ["Animations", "LayoutAnimationType", "createAnimationWithInitialValues", "createCustomKeyFrameAnimation", "getProcessedConfig", "handleExitingAnimation", "handleLayoutTransition", "maybeModifyStyleForKeyframe", "setElementAnimation", "areDOMRectsEqual", "Keyframe", "makeElementVisible", "EasingNameSymbol", "logger", "chooseConfig", "animationType", "props", "config", "ENTERING", "entering", "EXITING", "exiting", "LAYOUT", "layout", "checkUndefinedAnimationFail", "initialAnimationName", "needsCustomization", "warn", "maybeReportOverwrittenProperties", "keyframe", "styles", "propertyRegex", "animationProperties", "Set", "match", "matchAll", "add", "commonProperties", "Array", "from", "filter", "style", "has", "length", "join", "chooseAction", "animationConfig", "element", "transitionData", "reversed", "tryGetAnimationConfig", "isLayoutTransition", "isCustomKeyframe", "hasInitialValues", "initialValues", "undefined", "animationName", "definitions", "presetName", "constructor", "shouldFail", "keyframeTimestamps", "Object", "keys", "includes", "startWebLayoutAnimation", "tryActivateLayoutTransition", "snapshot", "rect", "getBoundingClientRect", "enteringAnimation", "enteringV", "exitingAnimation", "exitingV", "translateX", "x", "translateY", "y", "scaleX", "width", "scaleY", "height", "easingX", "easingXV", "easingY", "easingYV"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/animationsManager.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,UAAU,QAAQ,aAAU;AAKrC,SAASC,mBAAmB,QAAQ,oCAAiC;AACrE,SACEC,gCAAgC,EAChCC,6BAA6B,QACxB,sBAAmB;AAC1B,SACEC,kBAAkB,EAClBC,sBAAsB,EACtBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,mBAAmB,QACd,qBAAkB;AACzB,SAASC,gBAAgB,QAAQ,eAAY;AAE7C,SAASC,QAAQ,QAAQ,8BAAqB;AAC9C,SAASC,kBAAkB,QAAQ,qBAAkB;AACrD,SAASC,gBAAgB,QAAQ,iBAAc;AAE/C,SAASC,MAAM,QAAQ,uBAAc;AAErC,SAASC,YAAYA,CACnBC,aAAkC,EAClCC,KAAuD,EACvD;EACA,MAAMC,MAAM,GACVF,aAAa,KAAKd,mBAAmB,CAACiB,QAAQ,GAC1CF,KAAK,CAACG,QAAQ,GACdJ,aAAa,KAAKd,mBAAmB,CAACmB,OAAO,GAC3CJ,KAAK,CAACK,OAAO,GACbN,aAAa,KAAKd,mBAAmB,CAACqB,MAAM,GAC1CN,KAAK,CAACO,MAAM,GACZ,IAAI;EAEd,OAAON,MAAM;AACf;AAEA,SAASO,2BAA2BA,CAClCC,oBAA4B,EAC5BC,kBAA2B,EAC3B;EACA;EACA;EACA,IAAID,oBAAoB,IAAIzB,UAAU,IAAI0B,kBAAkB,EAAE;IAC5D,OAAO,KAAK;EACd;EAEAb,MAAM,CAACc,IAAI,CACT,qLACF,CAAC;EAED,OAAO,IAAI;AACb;AAEA,SAASC,gCAAgCA,CACvCC,QAAgB,EAChBC,MAA2B,EAC3B;EACA,MAAMC,aAAa,GAAG,oBAAoB;EAC1C,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAErC,KAAK,MAAMC,KAAK,IAAIL,QAAQ,CAACM,QAAQ,CAACJ,aAAa,CAAC,EAAE;IACpDC,mBAAmB,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,MAAMG,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,CAAEC,KAAK,IACvDT,mBAAmB,CAACU,GAAG,CAACD,KAAK,CAC/B,CAAC;EAED,IAAIJ,gBAAgB,CAACM,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EAEA9B,MAAM,CAACc,IAAI,CACT,GACEU,gBAAgB,CAACM,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,YAAY,KACtDN,gBAAgB,CAACO,IAAI,CACxB,IACF,CAAC,6IACH,CAAC;AACH;AAEA,SAASC,YAAYA,CACnB9B,aAAkC,EAClC+B,eAAgC,EAChCC,OAA8B,EAC9BC,cAA8B,EAC9B;EACA,QAAQjC,aAAa;IACnB,KAAKd,mBAAmB,CAACiB,QAAQ;MAC/BV,mBAAmB,CAACuC,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;MACnD;IACF,KAAK7C,mBAAmB,CAACqB,MAAM;MAC7B0B,cAAc,CAACC,QAAQ,GAAGH,eAAe,CAACG,QAAQ;MAClD3C,sBAAsB,CAACyC,OAAO,EAAED,eAAe,EAAEE,cAAc,CAAC;MAChE;IACF,KAAK/C,mBAAmB,CAACmB,OAAO;MAC9Bf,sBAAsB,CAAC0C,OAAO,EAAED,eAAe,CAAC;MAChD;EACJ;AACF;AAEA,SAASI,qBAAqBA,CAC5BlC,KAAuD,EACvDD,aAAkC,EAClC;EACA,MAAME,MAAM,GAAGH,YAAY,CAACC,aAAa,EAAEC,KAAK,CAAC;EACjD,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAKA,MAAMkC,kBAAkB,GAAGpC,aAAa,KAAKd,mBAAmB,CAACqB,MAAM;EACvE,MAAM8B,gBAAgB,GAAGnC,MAAM,YAAYP,QAAQ;EACnD,MAAM2C,gBAAgB,GAAIpC,MAAM,CAAkBqC,aAAa,KAAKC,SAAS;EAE7E,IAAIC,aAAa;EAEjB,IAAIJ,gBAAgB,EAAE;IACpBI,aAAa,GAAGrD,6BAA6B,CAC1Cc,MAAM,CAAkBwC,WAC3B,CAAC;EACH,CAAC,MAAM,IAAI,OAAOxC,MAAM,KAAK,UAAU,EAAE;IACvCuC,aAAa,GAAGvC,MAAM,CAACyC,UAAU;EACnC,CAAC,MAAM;IACLF,aAAa,GAAIvC,MAAM,CAAC0C,WAAW,CAChCD,UAAU;EACf;EAEA,IAAIL,gBAAgB,EAAE;IACpBG,aAAa,GAAGtD,gCAAgC,CAC9CsD,aAAa,EACZvC,MAAM,CAAkBqC,aAC3B,CAAC;EACH;EAEA,MAAMM,UAAU,GAAGpC,2BAA2B,CAC5CgC,aAAa,EACbL,kBAAkB,IAAIC,gBAAgB,IAAIC,gBAC5C,CAAC;EAED,IAAIO,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EAEA,IAAIR,gBAAgB,EAAE;IACpB,MAAMS,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CACnC9C,MAAM,CAAkBwC,WAC3B,CAAC;IAED,IACE,EAAEI,kBAAkB,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIH,kBAAkB,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC1E;MACAnD,MAAM,CAACc,IAAI,CACT,+MACF,CAAC;IACH;EACF;EAEA,MAAMmB,eAAe,GAAG1C,kBAAkB,CACxCoD,aAAa,EACbzC,aAAa,EACbE,MACF,CAAC;EAED,OAAO6B,eAAe;AACxB;AAEA,OAAO,SAASmB,uBAAuBA,CAGrCjD,KAAuD,EACvD+B,OAA8B,EAC9BhC,aAAkC,EAClCiC,cAA+B,EAC/B;EACA,MAAMF,eAAe,GAAGI,qBAAqB,CAAClC,KAAK,EAAED,aAAa,CAAC;EAEnER,2BAA2B,CAACwC,OAAO,EAAE/B,KAAK,CAACG,QAAwB,CAAC;EAEpE,IAAK2B,eAAe,EAAEU,aAAa,IAAuBxD,UAAU,EAAE;IACpE4B,gCAAgC,CAC9B5B,UAAU,CAAC8C,eAAe,EAAEU,aAAa,CAAmB,CAACf,KAAK,EAClEM,OAAO,CAACN,KACV,CAAC;EACH;EAEA,IAAIK,eAAe,EAAE;IACnBD,YAAY,CACV9B,aAAa,EACb+B,eAAe,EACfC,OAAO,EACPC,cACF,CAAC;EACH,CAAC,MAAM;IACLrC,kBAAkB,CAACoC,OAAO,EAAE,CAAC,CAAC;EAChC;AACF;AAEA,OAAO,SAASmB,2BAA2BA,CAGzClD,KAAuD,EACvD+B,OAA8B,EAC9BoB,QAAiB,EACjB;EACA,IAAI,CAACnD,KAAK,CAACO,MAAM,EAAE;IACjB;EACF;EAEA,MAAM6C,IAAI,GAAGrB,OAAO,CAACsB,qBAAqB,CAAC,CAAC;EAE5C,IAAI5D,gBAAgB,CAAC2D,IAAI,EAAED,QAAQ,CAAC,EAAE;IACpC;EACF;EAEA,MAAMG,iBAAiB,GAAItD,KAAK,CAACO,MAAM,CAAkBgD,SAAS,EAC9Db,UAAU;EACd,MAAMc,gBAAgB,GAAIxD,KAAK,CAACO,MAAM,CAAkBkD,QAAQ,EAAEf,UAAU;EAE5E,MAAMV,cAA8B,GAAG;IACrC0B,UAAU,EAAEP,QAAQ,CAACQ,CAAC,GAAGP,IAAI,CAACO,CAAC;IAC/BC,UAAU,EAAET,QAAQ,CAACU,CAAC,GAAGT,IAAI,CAACS,CAAC;IAC/BC,MAAM,EAAEX,QAAQ,CAACY,KAAK,GAAGX,IAAI,CAACW,KAAK;IACnCC,MAAM,EAAEb,QAAQ,CAACc,MAAM,GAAGb,IAAI,CAACa,MAAM;IACrChC,QAAQ,EAAE,KAAK;IAAE;IACjBiC,OAAO,EACJlE,KAAK,CAACO,MAAM,CAAkB4D,QAAQ,GAAGvE,gBAAgB,CAAC,IAAI,MAAM;IACvEwE,OAAO,EACJpE,KAAK,CAACO,MAAM,CAAkB8D,QAAQ,GAAGzE,gBAAgB,CAAC,IAAI,MAAM;IACvEO,QAAQ,EAAEmD,iBAAiB;IAC3BjD,OAAO,EAAEmD;EACX,CAAC;EAEDP,uBAAuB,CACrBjD,KAAK,EACL+B,OAAO,EACP9C,mBAAmB,CAACqB,MAAM,EAC1B0B,cACF,CAAC;AACH", "ignoreList": []}