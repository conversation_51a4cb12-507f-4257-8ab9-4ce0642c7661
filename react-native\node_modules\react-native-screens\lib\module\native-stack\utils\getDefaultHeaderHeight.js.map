{"version": 3, "names": ["Platform", "formSheetModalHeight", "getDefaultHeaderHeight", "layout", "statusBarHeight", "stackPresentation", "is<PERSON>arge<PERSON><PERSON>er", "arguments", "length", "undefined", "headerHeight", "OS", "isLandscape", "width", "height", "isFormSheetModal", "isPad", "isTV"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getDefaultHeaderHeight.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,eAAe,SAASC,sBAAsBA,CAC5CC,MAAc,EACdC,eAAuB,EACvBC,iBAAyC,EAEjC;EAAA,IADRC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAErB;EACA,IAAIG,YAAY,GAAGV,QAAQ,CAACW,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE;EAEtD,IAAIX,QAAQ,CAACW,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMC,WAAW,GAAGT,MAAM,CAACU,KAAK,GAAGV,MAAM,CAACW,MAAM;IAChD,MAAMC,gBAAgB,GACpBV,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,WAAW;IACpE,IAAIU,gBAAgB,IAAI,CAACH,WAAW,EAAE;MACpC;MACAR,eAAe,GAAG,CAAC;IACrB;IAEA,IAAIJ,QAAQ,CAACgB,KAAK,IAAIhB,QAAQ,CAACiB,IAAI,EAAE;MACnCP,YAAY,GAAGK,gBAAgB,GAAGd,oBAAoB,GAAG,EAAE;IAC7D,CAAC,MAAM;MACL,IAAIW,WAAW,EAAE;QACfF,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIK,gBAAgB,EAAE;UACpBL,YAAY,GAAGT,oBAAoB;QACrC,CAAC,MAAM;UACLS,YAAY,GAAGJ,aAAa,GAAG,EAAE,GAAG,EAAE;QACxC;MACF;IACF;EACF;EAEA,OAAOI,YAAY,GAAGN,eAAe;AACvC"}