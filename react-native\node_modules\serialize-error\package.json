{"name": "serialize-error", "version": "2.1.0", "description": "Serialize an error into a plain object", "license": "MIT", "repository": "sindresorhus/serialize-error", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["error", "err", "serialize", "stringify", "object", "obj", "convert", "process", "send"], "devDependencies": {"ava": "*", "xo": "^0.16.0"}}