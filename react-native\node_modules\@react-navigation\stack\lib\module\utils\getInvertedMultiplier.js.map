{"version": 3, "names": ["I18nManager", "getInvertedMultiplier", "gestureDirection", "getConstants", "isRTL"], "sourceRoot": "../../../src", "sources": ["utils/getInvertedMultiplier.tsx"], "mappings": "AAAA,SAASA,WAAW,QAAQ,cAAc;AAI1C,eAAe,SAASC,qBAAqB,CAC3CC,gBAAkC,EAC1B;EACR,QAAQA,gBAAgB;IACtB,KAAK,UAAU;MACb,OAAO,CAAC;IACV,KAAK,mBAAmB;MACtB,OAAO,CAAC,CAAC;IACX,KAAK,YAAY;MACf,OAAOF,WAAW,CAACG,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IAClD,KAAK,qBAAqB;MACxB,OAAOJ,WAAW,CAACG,YAAY,EAAE,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAAC;AAEvD"}