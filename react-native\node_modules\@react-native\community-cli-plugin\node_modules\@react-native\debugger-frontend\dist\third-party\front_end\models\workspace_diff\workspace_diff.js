import*as e from"../../core/common/common.js";import*as i from"../../core/host/host.js";import*as t from"../../third_party/diff/diff.js";import*as o from"../formatter/formatter.js";import*as r from"../persistence/persistence.js";import*as s from"../workspace/workspace.js";class n extends e.ObjectWrapper.ObjectWrapper{uiSourceCodeDiffs;loadingUISourceCodes;modifiedUISourceCodesInternal;constructor(e){super(),this.uiSourceCodeDiffs=new WeakMap,this.loadingUISourceCodes=new Map,this.modifiedUISourceCodesInternal=new Set,e.addEventListener(s.Workspace.Events.WorkingCopyChanged,this.uiSourceCodeChanged,this),e.addEventListener(s.Workspace.Events.WorkingCopyCommitted,this.uiSourceCodeChanged,this),e.addEventListener(s.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAdded,this),e.addEventListener(s.Workspace.Events.UISourceCodeRemoved,this.uiSourceCodeRemoved,this),e.addEventListener(s.Workspace.Events.ProjectRemoved,this.projectRemoved,this),e.uiSourceCodes().forEach(this.updateModifiedState.bind(this))}requestDiff(e,i){return this.uiSourceCodeDiff(e).requestDiff(i)}subscribeToDiffChange(e,i,t){this.uiSourceCodeDiff(e).addEventListener("DiffChanged",i,t)}unsubscribeFromDiffChange(e,i,t){this.uiSourceCodeDiff(e).removeEventListener("DiffChanged",i,t)}modifiedUISourceCodes(){return Array.from(this.modifiedUISourceCodesInternal)}isUISourceCodeModified(e){return this.modifiedUISourceCodesInternal.has(e)||this.loadingUISourceCodes.has(e)}uiSourceCodeDiff(e){let i=this.uiSourceCodeDiffs.get(e);return i||(i=new d(e),this.uiSourceCodeDiffs.set(e,i)),i}uiSourceCodeChanged(e){const i=e.data.uiSourceCode;this.updateModifiedState(i)}uiSourceCodeAdded(e){const i=e.data;this.updateModifiedState(i)}uiSourceCodeRemoved(e){const i=e.data;this.removeUISourceCode(i)}projectRemoved(e){const i=e.data;for(const e of i.uiSourceCodes())this.removeUISourceCode(e)}removeUISourceCode(e){this.loadingUISourceCodes.delete(e);const i=this.uiSourceCodeDiffs.get(e);i&&(i.dispose=!0),this.markAsUnmodified(e)}markAsUnmodified(e){this.uiSourceCodeProcessedForTest(),this.modifiedUISourceCodesInternal.delete(e)&&this.dispatchEventToListeners("ModifiedStatusChanged",{uiSourceCode:e,isModified:!1})}markAsModified(e){this.uiSourceCodeProcessedForTest(),this.modifiedUISourceCodesInternal.has(e)||(this.modifiedUISourceCodesInternal.add(e),this.dispatchEventToListeners("ModifiedStatusChanged",{uiSourceCode:e,isModified:!0}))}uiSourceCodeProcessedForTest(){}async updateModifiedState(e){if(this.loadingUISourceCodes.delete(e),e.project().type()!==s.Workspace.projectTypes.Network)return void this.markAsUnmodified(e);if(e.isDirty())return void this.markAsModified(e);if(!e.hasCommits())return void this.markAsUnmodified(e);const i=Promise.all([this.requestOriginalContentForUISourceCode(e),e.requestContent().then((e=>e.content))]);this.loadingUISourceCodes.set(e,i);const t=await i;this.loadingUISourceCodes.get(e)===i&&(this.loadingUISourceCodes.delete(e),null!==t[0]&&null!==t[1]&&t[0]!==t[1]?this.markAsModified(e):this.markAsUnmodified(e))}requestOriginalContentForUISourceCode(e){return this.uiSourceCodeDiff(e).originalContent()}revertToOriginal(e){return i.userMetrics.actionTaken(i.UserMetrics.Action.RevisionApplied),this.requestOriginalContentForUISourceCode(e).then((function(i){"string"==typeof i&&e.addRevision(i)}))}}class d extends e.ObjectWrapper.ObjectWrapper{uiSourceCode;requestDiffPromise;pendingChanges;dispose;constructor(e){super(),this.uiSourceCode=e,e.addEventListener(s.UISourceCode.Events.WorkingCopyChanged,this.uiSourceCodeChanged,this),e.addEventListener(s.UISourceCode.Events.WorkingCopyCommitted,this.uiSourceCodeChanged,this),this.requestDiffPromise=null,this.pendingChanges=null,this.dispose=!1}uiSourceCodeChanged(){this.pendingChanges&&(clearTimeout(this.pendingChanges),this.pendingChanges=null),this.requestDiffPromise=null;const e=this.uiSourceCode.content(),i=!e||e.length<65536?0:a;this.pendingChanges=window.setTimeout(function(){if(this.dispose)return;this.dispatchEventToListeners("DiffChanged"),this.pendingChanges=null}.bind(this),i)}requestDiff(e){return this.requestDiffPromise||(this.requestDiffPromise=this.innerRequestDiff(e)),this.requestDiffPromise}async originalContent(){const e=r.NetworkPersistenceManager.NetworkPersistenceManager.instance().originalContentForUISourceCode(this.uiSourceCode);if(e)return e;const i=await this.uiSourceCode.project().requestFileContent(this.uiSourceCode);return i.content||"error"in i&&i.error||""}async innerRequestDiff({shouldFormatDiff:e}){if(this.dispose)return null;let i=await this.originalContent();if(null===i)return null;if(i.length>1048576)return null;if(this.dispose)return null;let r,s=this.uiSourceCode.workingCopy();if(s||this.uiSourceCode.contentLoaded()||(s=(await this.uiSourceCode.requestContent()).content),s.length>1048576)return null;if(this.dispose)return null;if(null===s||null===i)return null;if(e){i=(await o.ScriptFormatter.format(this.uiSourceCode.contentType(),this.uiSourceCode.mimeType(),i)).formattedContent;const e=await o.ScriptFormatter.format(this.uiSourceCode.contentType(),this.uiSourceCode.mimeType(),s);s=e.formattedContent,r=e.formattedMapping}const n=/\r\n?|\n/;return{diff:t.Diff.DiffWrapper.lineDiff(i.split(n),s.split(n)),formattedCurrentMapping:r}}}let u=null;const a=200;var c=Object.freeze({__proto__:null,WorkspaceDiffImpl:n,UISourceCodeDiff:d,workspaceDiff:function(){return u||(u=new n(s.Workspace.WorkspaceImpl.instance())),u},UpdateTimeout:a});export{c as WorkspaceDiff};
