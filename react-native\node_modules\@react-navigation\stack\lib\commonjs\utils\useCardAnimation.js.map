{"version": 3, "names": ["useCardAnimation", "animation", "React", "useContext", "CardAnimationContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useCardAnimation.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAA0D;AAAA;AAAA;AAE3C,SAASA,gBAAgB,GAAG;EACzC,MAAMC,SAAS,GAAGC,KAAK,CAACC,UAAU,CAACC,6BAAoB,CAAC;EAExD,IAAIH,SAAS,KAAKI,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOL,SAAS;AAClB"}