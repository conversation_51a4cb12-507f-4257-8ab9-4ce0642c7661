export declare function getOption(options: any, key: any, defaultValue: any): any;
export declare function getUnionOfKeys(...args: any[]): any;
export declare function comparePos(pos1: any, pos2: any): number;
export declare function copyPos(pos: any): {
    line: any;
    column: any;
};
export declare function composeSourceMaps(formerMap: any, latterMap: any): any;
export declare function getTrueLoc(node: any, lines: any): {
    start: any;
    end: any;
} | null;
export declare function fixFaultyLocations(node: any, lines: any): void;
export declare function isExportDeclaration(node: any): boolean;
export declare function getParentExportDeclaration(path: any): any;
export declare function isTrailingCommaEnabled(options: any, context: any): boolean;
