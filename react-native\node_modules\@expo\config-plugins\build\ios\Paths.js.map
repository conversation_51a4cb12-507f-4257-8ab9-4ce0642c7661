{"version": 3, "file": "Paths.js", "names": ["_fs", "data", "require", "_glob", "path", "_interopRequireWildcard", "Entitlements", "_errors", "_glob2", "_warnings", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "ignoredPaths", "getAppDelegateHeaderFilePath", "projectRoot", "using", "extra", "withSortedGlobResult", "globSync", "absolute", "cwd", "ignore", "UnexpectedError", "length", "warnMultipleFiles", "tag", "fileName", "getAppDelegateFilePath", "getAppDelegateObjcHeaderFilePath", "getPodfilePath", "getLanguage", "filePath", "extension", "extname", "basename", "getFileInfo", "normalize", "contents", "readFileSync", "language", "getAppDelegate", "getSourceRoot", "appDelegate", "dirname", "findSchemePaths", "findSchemeNames", "schemePaths", "map", "schemePath", "parse", "name", "getAllXcodeProjectPaths", "iosFolder", "pbxproj<PERSON><PERSON><PERSON>", "replace", "filter", "project", "test", "sort", "b", "isAInIos", "isBInIos", "value", "join", "getXcodeProjectPath", "getAllPBXProjectPaths", "projectPaths", "paths", "existsSync", "getPBXProjectPath", "getAllInfoPlistPaths", "getInfoPlistPath", "getAllEntitlementsPaths", "getEntitlementsPath", "getSupportingPath", "resolve", "getExpoPlistPath", "supporting<PERSON>ath", "usingPath", "relative", "extraPaths", "v", "addWarningIOS", "JSON", "stringify"], "sources": ["../../src/ios/Paths.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'fs';\nimport { globSync } from 'glob';\nimport * as path from 'path';\n\nimport * as Entitlements from './Entitlements';\nimport { UnexpectedError } from '../utils/errors';\nimport { withSortedGlobResult } from '../utils/glob';\nimport { addWarningIOS } from '../utils/warnings';\n\nconst ignoredPaths = ['**/@(Carthage|Pods|vendor|node_modules)/**'];\n\ninterface ProjectFile<L extends string = string> {\n  path: string;\n  language: L;\n  contents: string;\n}\n\ntype AppleLanguage = 'objc' | 'objcpp' | 'swift' | 'rb';\n\nexport type PodfileProjectFile = ProjectFile<'rb'>;\nexport type AppDelegateProjectFile = ProjectFile<AppleLanguage>;\n\nexport function getAppDelegateHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = withSortedGlobResult(\n    globSync('ios/*/AppDelegate.h', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  );\n\n  if (!using) {\n    throw new UnexpectedError(\n      `Could not locate a valid AppDelegate header at root: \"${projectRoot}\"`\n    );\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-header',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateFilePath(projectRoot: string): string {\n  const [using, ...extra] = withSortedGlobResult(\n    globSync('ios/*/AppDelegate.@(m|mm|swift)', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  );\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateObjcHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = withSortedGlobResult(\n    globSync('ios/*/AppDelegate.h', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  );\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate.h at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-objc-header',\n      fileName: 'AppDelegate.h',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getPodfilePath(projectRoot: string): string {\n  const [using, ...extra] = withSortedGlobResult(\n    globSync('ios/Podfile', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  );\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid Podfile at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'podfile',\n      fileName: 'Podfile',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nfunction getLanguage(filePath: string): AppleLanguage {\n  const extension = path.extname(filePath);\n  if (!extension && path.basename(filePath) === 'Podfile') {\n    return 'rb';\n  }\n  switch (extension) {\n    case '.mm':\n      return 'objcpp';\n    case '.m':\n    case '.h':\n      return 'objc';\n    case '.swift':\n      return 'swift';\n    default:\n      throw new UnexpectedError(`Unexpected iOS file extension: ${extension}`);\n  }\n}\n\nexport function getFileInfo(filePath: string) {\n  return {\n    path: path.normalize(filePath),\n    contents: readFileSync(filePath, 'utf8'),\n    language: getLanguage(filePath),\n  };\n}\n\nexport function getAppDelegate(projectRoot: string): AppDelegateProjectFile {\n  const filePath = getAppDelegateFilePath(projectRoot);\n  return getFileInfo(filePath);\n}\n\nexport function getSourceRoot(projectRoot: string): string {\n  const appDelegate = getAppDelegate(projectRoot);\n  return path.dirname(appDelegate.path);\n}\n\nexport function findSchemePaths(projectRoot: string): string[] {\n  return withSortedGlobResult(\n    globSync('ios/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  );\n}\n\nexport function findSchemeNames(projectRoot: string): string[] {\n  const schemePaths = findSchemePaths(projectRoot);\n  return schemePaths.map((schemePath) => path.parse(schemePath).name);\n}\n\nexport function getAllXcodeProjectPaths(projectRoot: string): string[] {\n  const iosFolder = 'ios';\n  const pbxprojPaths = withSortedGlobResult(\n    globSync('ios/**/*.xcodeproj', { cwd: projectRoot, ignore: ignoredPaths })\n      // Drop leading `/` from glob results to mimick glob@<9 behavior\n      .map((filePath) => filePath.replace(/^\\//, ''))\n      .filter(\n        (project) => !/test|example|sample/i.test(project) || path.dirname(project) === iosFolder\n      )\n  ).sort((a, b) => {\n    const isAInIos = path.dirname(a) === iosFolder;\n    const isBInIos = path.dirname(b) === iosFolder;\n    // preserve previous sort order\n    if ((isAInIos && isBInIos) || (!isAInIos && !isBInIos)) {\n      return 0;\n    }\n    return isAInIos ? -1 : 1;\n  });\n\n  if (!pbxprojPaths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return pbxprojPaths.map((value) => path.join(projectRoot, value));\n}\n\n/**\n * Get the pbxproj for the given path\n */\nexport function getXcodeProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllXcodeProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'xcodeproj',\n      fileName: '*.xcodeproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllPBXProjectPaths(projectRoot: string): string[] {\n  const projectPaths = getAllXcodeProjectPaths(projectRoot);\n  const paths = projectPaths\n    .map((value) => path.join(value, 'project.pbxproj'))\n    .filter((value) => existsSync(value));\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj/project.pbxproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getPBXProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllPBXProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'project-pbxproj',\n      fileName: 'project.pbxproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllInfoPlistPaths(projectRoot: string): string[] {\n  const paths = withSortedGlobResult(\n    globSync('ios/*/Info.plist', {\n      absolute: true,\n      cwd: projectRoot,\n      ignore: ignoredPaths,\n    })\n  ).sort(\n    // longer name means more suffixes, we want the shortest possible one to be first.\n    (a, b) => a.length - b.length\n  );\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate Info.plist files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getInfoPlistPath(projectRoot: string): string {\n  const [using, ...extra] = getAllInfoPlistPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'info-plist',\n      fileName: 'Info.plist',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllEntitlementsPaths(projectRoot: string): string[] {\n  const paths = globSync('ios/*/*.entitlements', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n  return paths;\n}\n\n/**\n * @deprecated: use Entitlements.getEntitlementsPath instead\n */\nexport function getEntitlementsPath(projectRoot: string): string | null {\n  return Entitlements.getEntitlementsPath(projectRoot);\n}\n\nexport function getSupportingPath(projectRoot: string): string {\n  return path.resolve(projectRoot, 'ios', path.basename(getSourceRoot(projectRoot)), 'Supporting');\n}\n\nexport function getExpoPlistPath(projectRoot: string): string {\n  const supportingPath = getSupportingPath(projectRoot);\n  return path.join(supportingPath, 'Expo.plist');\n}\n\nfunction warnMultipleFiles({\n  tag,\n  fileName,\n  projectRoot,\n  using,\n  extra,\n}: {\n  tag: string;\n  fileName: string;\n  projectRoot?: string;\n  using: string;\n  extra: string[];\n}) {\n  const usingPath = projectRoot ? path.relative(projectRoot, using) : using;\n  const extraPaths = projectRoot ? extra.map((v) => path.relative(projectRoot, v)) : extra;\n  addWarningIOS(\n    `paths-${tag}`,\n    `Found multiple ${fileName} file paths, using \"${usingPath}\". Ignored paths: ${JSON.stringify(\n      extraPaths\n    )}`\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,KAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,OAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,MAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,UAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAElD,MAAMW,YAAY,GAAG,CAAC,4CAA4C,CAAC;AAa5D,SAASC,4BAA4BA,CAACC,WAAmB,EAAU;EACxE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,6BAAoB,EAC5C,IAAAC,gBAAQ,EAAC,qBAAqB,EAAE;IAC9BC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC;EAED,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIO,yBAAe,EACvB,yDAAyDR,WAAW,GACtE,CAAC;EACH;EAEA,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,qBAAqB;MAC1BC,QAAQ,EAAE,aAAa;MACvBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASY,sBAAsBA,CAACb,WAAmB,EAAU;EAClE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,6BAAoB,EAC5C,IAAAC,gBAAQ,EAAC,iCAAiC,EAAE;IAC1CC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC;EAED,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIO,yBAAe,EAAC,kDAAkDR,WAAW,GAAG,CAAC;EAC7F;EAEA,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,cAAc;MACnBC,QAAQ,EAAE,aAAa;MACvBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASa,gCAAgCA,CAACd,WAAmB,EAAU;EAC5E,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,6BAAoB,EAC5C,IAAAC,gBAAQ,EAAC,qBAAqB,EAAE;IAC9BC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC;EAED,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIO,yBAAe,EAAC,oDAAoDR,WAAW,GAAG,CAAC;EAC/F;EAEA,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,0BAA0B;MAC/BC,QAAQ,EAAE,eAAe;MACzBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASc,cAAcA,CAACf,WAAmB,EAAU;EAC1D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,6BAAoB,EAC5C,IAAAC,gBAAQ,EAAC,aAAa,EAAE;IACtBC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC;EAED,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIO,yBAAe,EAAC,8CAA8CR,WAAW,GAAG,CAAC;EACzF;EAEA,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,SAAS;MACdC,QAAQ,EAAE,SAAS;MACnBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEA,SAASe,WAAWA,CAACC,QAAgB,EAAiB;EACpD,MAAMC,SAAS,GAAG9C,IAAI,CAAD,CAAC,CAAC+C,OAAO,CAACF,QAAQ,CAAC;EACxC,IAAI,CAACC,SAAS,IAAI9C,IAAI,CAAD,CAAC,CAACgD,QAAQ,CAACH,QAAQ,CAAC,KAAK,SAAS,EAAE;IACvD,OAAO,IAAI;EACb;EACA,QAAQC,SAAS;IACf,KAAK,KAAK;MACR,OAAO,QAAQ;IACjB,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,MAAM;IACf,KAAK,QAAQ;MACX,OAAO,OAAO;IAChB;MACE,MAAM,KAAIV,yBAAe,EAAC,kCAAkCU,SAAS,EAAE,CAAC;EAC5E;AACF;AAEO,SAASG,WAAWA,CAACJ,QAAgB,EAAE;EAC5C,OAAO;IACL7C,IAAI,EAAEA,IAAI,CAAD,CAAC,CAACkD,SAAS,CAACL,QAAQ,CAAC;IAC9BM,QAAQ,EAAE,IAAAC,kBAAY,EAACP,QAAQ,EAAE,MAAM,CAAC;IACxCQ,QAAQ,EAAET,WAAW,CAACC,QAAQ;EAChC,CAAC;AACH;AAEO,SAASS,cAAcA,CAAC1B,WAAmB,EAA0B;EAC1E,MAAMiB,QAAQ,GAAGJ,sBAAsB,CAACb,WAAW,CAAC;EACpD,OAAOqB,WAAW,CAACJ,QAAQ,CAAC;AAC9B;AAEO,SAASU,aAAaA,CAAC3B,WAAmB,EAAU;EACzD,MAAM4B,WAAW,GAAGF,cAAc,CAAC1B,WAAW,CAAC;EAC/C,OAAO5B,IAAI,CAAD,CAAC,CAACyD,OAAO,CAACD,WAAW,CAACxD,IAAI,CAAC;AACvC;AAEO,SAAS0D,eAAeA,CAAC9B,WAAmB,EAAY;EAC7D,OAAO,IAAAG,6BAAoB,EACzB,IAAAC,gBAAQ,EAAC,mDAAmD,EAAE;IAC5DC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC;AACH;AAEO,SAASiC,eAAeA,CAAC/B,WAAmB,EAAY;EAC7D,MAAMgC,WAAW,GAAGF,eAAe,CAAC9B,WAAW,CAAC;EAChD,OAAOgC,WAAW,CAACC,GAAG,CAAEC,UAAU,IAAK9D,IAAI,CAAD,CAAC,CAAC+D,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC;AACrE;AAEO,SAASC,uBAAuBA,CAACrC,WAAmB,EAAY;EACrE,MAAMsC,SAAS,GAAG,KAAK;EACvB,MAAMC,YAAY,GAAG,IAAApC,6BAAoB,EACvC,IAAAC,gBAAQ,EAAC,oBAAoB,EAAE;IAAEE,GAAG,EAAEN,WAAW;IAAEO,MAAM,EAAET;EAAa,CAAC;EACvE;EAAA,CACCmC,GAAG,CAAEhB,QAAQ,IAAKA,QAAQ,CAACuB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAC9CC,MAAM,CACJC,OAAO,IAAK,CAAC,sBAAsB,CAACC,IAAI,CAACD,OAAO,CAAC,IAAItE,IAAI,CAAD,CAAC,CAACyD,OAAO,CAACa,OAAO,CAAC,KAAKJ,SAClF,CACJ,CAAC,CAACM,IAAI,CAAC,CAACvD,CAAC,EAAEwD,CAAC,KAAK;IACf,MAAMC,QAAQ,GAAG1E,IAAI,CAAD,CAAC,CAACyD,OAAO,CAACxC,CAAC,CAAC,KAAKiD,SAAS;IAC9C,MAAMS,QAAQ,GAAG3E,IAAI,CAAD,CAAC,CAACyD,OAAO,CAACgB,CAAC,CAAC,KAAKP,SAAS;IAC9C;IACA,IAAKQ,QAAQ,IAAIC,QAAQ,IAAM,CAACD,QAAQ,IAAI,CAACC,QAAS,EAAE;MACtD,OAAO,CAAC;IACV;IACA,OAAOD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1B,CAAC,CAAC;EAEF,IAAI,CAACP,YAAY,CAAC9B,MAAM,EAAE;IACxB,MAAM,KAAID,yBAAe,EACvB,gEAAgER,WAAW,IAC7E,CAAC;EACH;EACA,OAAOuC,YAAY,CAACN,GAAG,CAAEe,KAAK,IAAK5E,IAAI,CAAD,CAAC,CAAC6E,IAAI,CAACjD,WAAW,EAAEgD,KAAK,CAAC,CAAC;AACnE;;AAEA;AACA;AACA;AACO,SAASE,mBAAmBA,CAAClD,WAAmB,EAAU;EAC/D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGmC,uBAAuB,CAACrC,WAAW,CAAC;EAE9D,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,aAAa;MACvBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASkD,qBAAqBA,CAACnD,WAAmB,EAAY;EACnE,MAAMoD,YAAY,GAAGf,uBAAuB,CAACrC,WAAW,CAAC;EACzD,MAAMqD,KAAK,GAAGD,YAAY,CACvBnB,GAAG,CAAEe,KAAK,IAAK5E,IAAI,CAAD,CAAC,CAAC6E,IAAI,CAACD,KAAK,EAAE,iBAAiB,CAAC,CAAC,CACnDP,MAAM,CAAEO,KAAK,IAAK,IAAAM,gBAAU,EAACN,KAAK,CAAC,CAAC;EAEvC,IAAI,CAACK,KAAK,CAAC5C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACvB,gFAAgFR,WAAW,IAC7F,CAAC;EACH;EACA,OAAOqD,KAAK;AACd;AAEO,SAASE,iBAAiBA,CAACvD,WAAmB,EAAU;EAC7D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGiD,qBAAqB,CAACnD,WAAW,CAAC;EAE5D,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,iBAAiB;MACtBC,QAAQ,EAAE,iBAAiB;MAC3BZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASuD,oBAAoBA,CAACxD,WAAmB,EAAY;EAClE,MAAMqD,KAAK,GAAG,IAAAlD,6BAAoB,EAChC,IAAAC,gBAAQ,EAAC,kBAAkB,EAAE;IAC3BC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CACH,CAAC,CAAC8C,IAAI;EACJ;EACA,CAACvD,CAAC,EAAEwD,CAAC,KAAKxD,CAAC,CAACoB,MAAM,GAAGoC,CAAC,CAACpC,MACzB,CAAC;EAED,IAAI,CAAC4C,KAAK,CAAC5C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACvB,uDAAuDR,WAAW,IACpE,CAAC;EACH;EACA,OAAOqD,KAAK;AACd;AAEO,SAASI,gBAAgBA,CAACzD,WAAmB,EAAU;EAC5D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGsD,oBAAoB,CAACxD,WAAW,CAAC;EAE3D,IAAIE,KAAK,CAACO,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,YAAY;MACjBC,QAAQ,EAAE,YAAY;MACtBZ,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASyD,uBAAuBA,CAAC1D,WAAmB,EAAY;EACrE,MAAMqD,KAAK,GAAG,IAAAjD,gBAAQ,EAAC,sBAAsB,EAAE;IAC7CC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEN,WAAW;IAChBO,MAAM,EAAET;EACV,CAAC,CAAC;EACF,OAAOuD,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAASM,mBAAmBA,CAAC3D,WAAmB,EAAiB;EACtE,OAAO1B,YAAY,CAAD,CAAC,CAACqF,mBAAmB,CAAC3D,WAAW,CAAC;AACtD;AAEO,SAAS4D,iBAAiBA,CAAC5D,WAAmB,EAAU;EAC7D,OAAO5B,IAAI,CAAD,CAAC,CAACyF,OAAO,CAAC7D,WAAW,EAAE,KAAK,EAAE5B,IAAI,CAAD,CAAC,CAACgD,QAAQ,CAACO,aAAa,CAAC3B,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;AAClG;AAEO,SAAS8D,gBAAgBA,CAAC9D,WAAmB,EAAU;EAC5D,MAAM+D,cAAc,GAAGH,iBAAiB,CAAC5D,WAAW,CAAC;EACrD,OAAO5B,IAAI,CAAD,CAAC,CAAC6E,IAAI,CAACc,cAAc,EAAE,YAAY,CAAC;AAChD;AAEA,SAASrD,iBAAiBA,CAAC;EACzBC,GAAG;EACHC,QAAQ;EACRZ,WAAW;EACXC,KAAK;EACLC;AAOF,CAAC,EAAE;EACD,MAAM8D,SAAS,GAAGhE,WAAW,GAAG5B,IAAI,CAAD,CAAC,CAAC6F,QAAQ,CAACjE,WAAW,EAAEC,KAAK,CAAC,GAAGA,KAAK;EACzE,MAAMiE,UAAU,GAAGlE,WAAW,GAAGE,KAAK,CAAC+B,GAAG,CAAEkC,CAAC,IAAK/F,IAAI,CAAD,CAAC,CAAC6F,QAAQ,CAACjE,WAAW,EAAEmE,CAAC,CAAC,CAAC,GAAGjE,KAAK;EACxF,IAAAkE,yBAAa,EACX,SAASzD,GAAG,EAAE,EACd,kBAAkBC,QAAQ,uBAAuBoD,SAAS,qBAAqBK,IAAI,CAACC,SAAS,CAC3FJ,UACF,CAAC,EACH,CAAC;AACH", "ignoreList": []}