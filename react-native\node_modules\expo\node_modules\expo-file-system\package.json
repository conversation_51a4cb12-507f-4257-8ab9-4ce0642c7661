{"name": "expo-file-system", "version": "18.1.11", "description": "Provides access to the local file system on the device.", "main": "src/index.ts", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "file-system", "file"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-file-system"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/filesystem/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.1.8", "jest-expo": "~53.0.8"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}