declare const _default: import("./createIconSet").Icon<"style" | "accessible" | "link" | "email" | "search" | "image" | "menu" | "radio" | "tab" | "timer" | "forward" | "info" | "check" | "close" | "book" | "pause" | "mail" | "home" | "laptop" | "star" | "filter" | "save" | "phone" | "inbox" | "lock" | "cloud" | "camera" | "delete" | "upload" | "download" | "tag" | "key" | "flag" | "man" | "wallet" | "woman" | "android" | "copyright" | "wifi" | "sync" | "login" | "logout" | "contacts" | "edit" | "warning" | "dashboard" | "wechat" | "adjust" | "air" | "archive" | "arrow-left" | "arrow-right" | "attachment" | "block" | "bookmark" | "bookmarks" | "brush" | "cake" | "chat" | "chevron-left" | "chevron-right" | "circle" | "code" | "credit-card" | "crop" | "facebook" | "fingerprint" | "folder" | "help" | "keyboard" | "language" | "layers" | "list" | "location-pin" | "lock-open" | "loop" | "map" | "merge" | "message" | "mic" | "mouse" | "note" | "notifications-off" | "palette" | "paypal" | "pie-chart" | "pin" | "print" | "publish" | "reply" | "reply-all" | "rocket" | "share" | "shield" | "shop" | "shopping-bag" | "shopping-basket" | "shopping-cart" | "shuffle" | "tablet" | "tv" | "voicemail" | "water" | "comment" | "redo" | "refresh" | "undo" | "airplay" | "anchor" | "bar-chart" | "bluetooth" | "cast" | "check-circle" | "cloud-off" | "coffee" | "fast-forward" | "headphones" | "hexagon" | "maximize" | "mic-off" | "minimize" | "monitor" | "navigation" | "pause-circle" | "percent" | "phone-forwarded" | "phone-missed" | "play-circle" | "power" | "repeat" | "send" | "settings" | "smartphone" | "speaker" | "square" | "stop-circle" | "terminal" | "trending-down" | "trending-up" | "umbrella" | "watch" | "wifi-off" | "zoom-in" | "zoom-out" | "remove" | "power-off" | "rotate-right" | "list-alt" | "volume-off" | "volume-down" | "volume-up" | "photo" | "stop" | "eject" | "expand" | "compress" | "folder-open" | "star-half" | "feed" | "arrow-circle-left" | "arrow-circle-right" | "arrow-circle-up" | "arrow-circle-down" | "group" | "reorder" | "money" | "sort" | "rotate-left" | "gavel" | "bolt" | "cloud-download" | "cloud-upload" | "gamepad" | "superscript" | "subscript" | "fire-extinguisher" | "euro" | "apple" | "female" | "male" | "try" | "wordpress" | "reddit" | "fax" | "support" | "history" | "tty" | "area-chart" | "toggle-off" | "toggle-on" | "diamond" | "motorcycle" | "transgender" | "hotel" | "bed" | "train" | "subway" | "battery-full" | "usb" | "blind" | "sign-language" | "snapchat" | "quora" | "telegram" | "shower" | "bathtub" | "input" | "article" | "details" | "html" | "label" | "output" | "source" | "title" | "pattern" | "polyline" | "light" | "web" | "discord" | "hive" | "php" | "shopify" | "tiktok" | "handshake" | "lightbulb" | "newspaper" | "backspace" | "blender" | "border-all" | "border-style" | "car-crash" | "chair" | "charging-station" | "church" | "cookie" | "directions" | "egg" | "file-download" | "file-upload" | "headset" | "heart-broken" | "hiking" | "hot-tub" | "memory" | "mosque" | "pallet" | "poll" | "portrait" | "receipt" | "route" | "satellite" | "school" | "sd-card" | "sim-card" | "sms" | "snowboarding" | "soap" | "spa" | "store" | "stream" | "synagogue" | "sync-alt" | "tram" | "volume-mute" | "warehouse" | "pix" | "computer" | "elevator" | "house" | "panorama" | "person" | "stairs" | "timeline" | "tornado" | "volcano" | "messenger" | "adobe" | "favorite" | "equalizer" | "music-note" | "preview" | "do-not-disturb" | "holiday-village" | "room" | "rectangle" | "closed-caption" | "contrast" | "foundation" | "accessibility" | "add" | "add-circle" | "add-circle-outline" | "alarm" | "analytics" | "apps" | "arrow-back" | "arrow-forward" | "bookmark-outline" | "build" | "business" | "call" | "cloud-circle" | "cloud-done" | "create" | "flash-off" | "help-outline" | "mail-outline" | "notifications" | "pause-circle-outline" | "people" | "people-outline" | "person-add" | "person-outline" | "person-remove" | "pie-chart-outline" | "play-circle-outline" | "qr-code" | "radio-button-off" | "radio-button-on" | "remove-circle" | "remove-circle-outline" | "restaurant" | "scale" | "star-outline" | "storefront" | "sunny" | "thunderstorm" | "today" | "videocam" | "videocam-off" | "account-box" | "account-circle" | "alarm-off" | "album" | "align-horizontal-center" | "align-horizontal-left" | "align-horizontal-right" | "align-vertical-bottom" | "align-vertical-center" | "align-vertical-top" | "all-inclusive" | "animation" | "api" | "aspect-ratio" | "assistant" | "atm" | "autorenew" | "av-timer" | "balcony" | "ballot" | "battery-alert" | "battery-unknown" | "blinds" | "bluetooth-audio" | "blur-linear" | "blur-off" | "bookmark-remove" | "border-bottom" | "border-color" | "border-horizontal" | "border-left" | "border-right" | "border-top" | "border-vertical" | "brightness-1" | "brightness-2" | "brightness-3" | "brightness-4" | "brightness-5" | "brightness-6" | "brightness-7" | "brightness-auto" | "bus-alert" | "cached" | "calendar-month" | "calendar-today" | "call-made" | "call-merge" | "call-missed" | "call-received" | "call-split" | "camera-enhance" | "camera-front" | "camera-rear" | "cancel" | "cast-connected" | "castle" | "check-circle-outline" | "cloud-sync" | "coffee-maker" | "compare" | "content-copy" | "content-cut" | "content-paste" | "credit-card-off" | "crop-free" | "crop-landscape" | "crop-portrait" | "crop-rotate" | "crop-square" | "currency-rupee" | "curtains" | "curtains-closed" | "delete-forever" | "delete-outline" | "delete-sweep" | "desk" | "desktop-mac" | "developer-board" | "devices" | "dialpad" | "dns" | "domain" | "door-sliding" | "doorbell" | "draw" | "earbuds" | "escalator" | "ev-station" | "exit-to-app" | "factory" | "fence" | "find-replace" | "fire-hydrant" | "fire-truck" | "fireplace" | "flare" | "flash-auto" | "flashlight-off" | "flip-to-back" | "flip-to-front" | "folder-zip" | "forest" | "forklift" | "format-align-center" | "format-align-justify" | "format-align-left" | "format-align-right" | "format-bold" | "format-clear" | "format-color-fill" | "format-color-text" | "format-indent-decrease" | "format-indent-increase" | "format-italic" | "format-line-spacing" | "format-list-bulleted" | "format-list-numbered" | "format-list-numbered-rtl" | "format-overline" | "format-paint" | "format-size" | "format-strikethrough" | "format-textdirection-l-to-r" | "format-textdirection-r-to-l" | "format-underline" | "forum" | "fullscreen" | "fullscreen-exit" | "garage" | "gesture" | "grain" | "grass" | "grid-off" | "hail" | "hdr-off" | "headset-off" | "hvac" | "image-search" | "invert-colors" | "invert-colors-off" | "iron" | "kayaking" | "keyboard-backspace" | "keyboard-return" | "keyboard-tab" | "kitesurfing" | "label-off" | "label-outline" | "lan" | "launch" | "lightbulb-outline" | "link-off" | "liquor" | "lock-clock" | "lock-outline" | "lock-reset" | "looks" | "loupe" | "margin" | "menu-open" | "microwave" | "moped" | "more" | "movie" | "movie-edit" | "movie-filter" | "music-off" | "nature" | "nature-people" | "near-me" | "nfc" | "opacity" | "open-in-new" | "panorama-fisheye" | "panorama-horizontal" | "panorama-vertical" | "panorama-wide-angle" | "paragliding" | "pentagon" | "phone-in-talk" | "phone-paused" | "piano" | "piano-off" | "playlist-play" | "playlist-remove" | "point-of-sale" | "polymer" | "pool" | "priority-high" | "propane-tank" | "radar" | "replay" | "restore" | "rocket-launch" | "room-service" | "rounded-corner" | "router" | "rowing" | "scanner" | "scatter-plot" | "scoreboard" | "screen-rotation" | "sd" | "security" | "segment" | "select-all" | "shield-moon" | "skateboarding" | "skip-next" | "skip-previous" | "sledding" | "snowmobile" | "solar-power" | "spellcheck" | "stadium" | "subdirectory-arrow-left" | "subdirectory-arrow-right" | "subtitles" | "surfing" | "surround-sound" | "swap-horizontal-circle" | "swap-vertical-circle" | "tab-unselected" | "tablet-android" | "tag-faces" | "terrain" | "texture" | "thermostat" | "thumb-down" | "thumb-up" | "thumbs-up-down" | "timelapse" | "timer-10" | "timer-3" | "timer-off" | "transcribe" | "translate" | "trending-neutral" | "tune" | "update" | "view-agenda" | "view-array" | "view-carousel" | "view-column" | "view-comfy" | "view-compact" | "view-day" | "view-headline" | "view-list" | "view-module" | "view-quilt" | "view-stream" | "view-week" | "wallet-giftcard" | "wallet-membership" | "wallet-travel" | "wallpaper" | "waves" | "webhook" | "widgets" | "wifi-lock" | "123" | "360" | "10k" | "10mp" | "11mp" | "12mp" | "13mp" | "14mp" | "15mp" | "16mp" | "17mp" | "18-up-rating" | "18mp" | "19mp" | "1k" | "1k-plus" | "1x-mobiledata" | "20mp" | "21mp" | "22mp" | "23mp" | "24mp" | "2k" | "2k-plus" | "2mp" | "30fps" | "30fps-select" | "3d-rotation" | "3g-mobiledata" | "3k" | "3k-plus" | "3mp" | "3p" | "4g-mobiledata" | "4g-plus-mobiledata" | "4k" | "4k-plus" | "4mp" | "5g" | "5k" | "5k-plus" | "5mp" | "60fps" | "60fps-select" | "6-ft-apart" | "6k" | "6k-plus" | "6mp" | "7k" | "7k-plus" | "7mp" | "8k" | "8k-plus" | "8mp" | "9k" | "9k-plus" | "9mp" | "abc" | "ac-unit" | "access-alarm" | "access-alarms" | "access-time" | "access-time-filled" | "accessibility-new" | "accessible-forward" | "account-balance" | "account-balance-wallet" | "account-tree" | "ad-units" | "adb" | "add-a-photo" | "add-alarm" | "add-alert" | "add-box" | "add-business" | "add-call" | "add-card" | "add-chart" | "add-comment" | "add-home" | "add-home-work" | "add-ic-call" | "add-link" | "add-location" | "add-location-alt" | "add-moderator" | "add-photo-alternate" | "add-reaction" | "add-road" | "add-shopping-cart" | "add-task" | "add-to-drive" | "add-to-home-screen" | "add-to-photos" | "add-to-queue" | "addchart" | "adf-scanner" | "admin-panel-settings" | "ads-click" | "agriculture" | "airline-seat-flat" | "airline-seat-flat-angled" | "airline-seat-individual-suite" | "airline-seat-legroom-extra" | "airline-seat-legroom-normal" | "airline-seat-legroom-reduced" | "airline-seat-recline-extra" | "airline-seat-recline-normal" | "airline-stops" | "airlines" | "airplane-ticket" | "airplanemode-active" | "airplanemode-inactive" | "airplanemode-off" | "airplanemode-on" | "airport-shuttle" | "alarm-add" | "alarm-on" | "all-inbox" | "all-out" | "alt-route" | "alternate-email" | "amp-stories" | "announcement" | "aod" | "apartment" | "app-blocking" | "app-registration" | "app-settings-alt" | "app-shortcut" | "approval" | "apps-outage" | "architecture" | "arrow-back-ios" | "arrow-back-ios-new" | "arrow-downward" | "arrow-drop-down" | "arrow-drop-down-circle" | "arrow-drop-up" | "arrow-forward-ios" | "arrow-outward" | "arrow-right-alt" | "arrow-upward" | "art-track" | "assessment" | "assignment" | "assignment-add" | "assignment-ind" | "assignment-late" | "assignment-return" | "assignment-returned" | "assignment-turned-in" | "assist-walker" | "assistant-direction" | "assistant-navigation" | "assistant-photo" | "assured-workload" | "attach-email" | "attach-file" | "attach-money" | "attractions" | "attribution" | "audio-file" | "audiotrack" | "auto-awesome" | "auto-awesome-mosaic" | "auto-awesome-motion" | "auto-delete" | "auto-fix-high" | "auto-fix-normal" | "auto-fix-off" | "auto-graph" | "auto-mode" | "auto-stories" | "autofps-select" | "baby-changing-station" | "back-hand" | "backpack" | "backup" | "backup-table" | "badge" | "bakery-dining" | "balance" | "barcode-reader" | "batch-prediction" | "bathroom" | "battery-0-bar" | "battery-1-bar" | "battery-2-bar" | "battery-3-bar" | "battery-4-bar" | "battery-5-bar" | "battery-6-bar" | "battery-charging-full" | "battery-saver" | "battery-std" | "beach-access" | "bedroom-baby" | "bedroom-child" | "bedroom-parent" | "bedtime" | "bedtime-off" | "beenhere" | "bento" | "bike-scooter" | "biotech" | "blinds-closed" | "block-flipped" | "bloodtype" | "bluetooth-connected" | "bluetooth-disabled" | "bluetooth-drive" | "bluetooth-searching" | "blur-circular" | "blur-on" | "book-online" | "bookmark-add" | "bookmark-added" | "bookmark-border" | "border-clear" | "border-inner" | "border-outer" | "boy" | "branding-watermark" | "breakfast-dining" | "brightness-high" | "brightness-low" | "brightness-medium" | "broadcast-on-home" | "broadcast-on-personal" | "broken-image" | "browse-gallery" | "browser-not-supported" | "browser-updated" | "brunch-dining" | "bubble-chart" | "bug-report" | "build-circle" | "bungalow" | "burst-mode" | "business-center" | "cabin" | "cable" | "calculate" | "calendar-view-day" | "calendar-view-month" | "calendar-view-week" | "call-end" | "call-missed-outgoing" | "call-to-action" | "camera-alt" | "camera-indoor" | "camera-outdoor" | "camera-roll" | "cameraswitch" | "campaign" | "cancel-presentation" | "cancel-schedule-send" | "candlestick-chart" | "car-rental" | "car-repair" | "card-giftcard" | "card-membership" | "card-travel" | "carpenter" | "cases" | "casino" | "cast-for-education" | "catching-pokemon" | "category" | "celebration" | "cell-tower" | "cell-wifi" | "center-focus-strong" | "center-focus-weak" | "chair-alt" | "chalet" | "change-circle" | "change-history" | "chat-bubble" | "chat-bubble-outline" | "check-box" | "check-box-outline-blank" | "checklist" | "checklist-rtl" | "checkroom" | "child-care" | "child-friendly" | "chrome-reader-mode" | "circle-notifications" | "class" | "clean-hands" | "cleaning-services" | "clear" | "clear-all" | "close-fullscreen" | "closed-caption-disabled" | "closed-caption-off" | "cloud-queue" | "cloudy-snowing" | "co2" | "co-present" | "code-off" | "collections" | "collections-bookmark" | "color-lens" | "colorize" | "comment-bank" | "comments-disabled" | "commit" | "commute" | "compare-arrows" | "compass-calibration" | "compost" | "confirmation-num" | "confirmation-number" | "connect-without-contact" | "connected-tv" | "connecting-airports" | "construction" | "contact-emergency" | "contact-mail" | "contact-page" | "contact-phone" | "contact-support" | "contactless" | "content-paste-go" | "content-paste-off" | "content-paste-search" | "control-camera" | "control-point" | "control-point-duplicate" | "conveyor-belt" | "copy-all" | "coronavirus" | "corporate-fare" | "cottage" | "countertops" | "create-new-folder" | "credit-score" | "crib" | "crisis-alert" | "crop-16-9" | "crop-3-2" | "crop-5-4" | "crop-7-5" | "crop-din" | "crop-original" | "cruelty-free" | "css" | "currency-bitcoin" | "currency-exchange" | "currency-franc" | "currency-lira" | "currency-pound" | "currency-ruble" | "currency-yen" | "currency-yuan" | "cyclone" | "dangerous" | "dark-mode" | "dashboard-customize" | "data-array" | "data-exploration" | "data-object" | "data-saver-off" | "data-saver-on" | "data-thresholding" | "data-usage" | "dataset" | "dataset-linked" | "date-range" | "deblur" | "deck" | "dehaze" | "delivery-dining" | "density-large" | "density-medium" | "density-small" | "departure-board" | "description" | "deselect" | "design-services" | "desktop-access-disabled" | "desktop-windows" | "developer-board-off" | "developer-mode" | "device-hub" | "device-thermostat" | "device-unknown" | "devices-fold" | "devices-other" | "dew-point" | "dialer-sip" | "difference" | "dining" | "dinner-dining" | "directions-bike" | "directions-boat" | "directions-boat-filled" | "directions-bus" | "directions-bus-filled" | "directions-car" | "directions-car-filled" | "directions-ferry" | "directions-off" | "directions-railway" | "directions-railway-filled" | "directions-run" | "directions-subway" | "directions-subway-filled" | "directions-train" | "directions-transit" | "directions-transit-filled" | "directions-walk" | "dirty-lens" | "disabled-by-default" | "disabled-visible" | "disc-full" | "discount" | "display-settings" | "diversity-1" | "diversity-2" | "diversity-3" | "dnd-forwardslash" | "do-disturb" | "do-disturb-alt" | "do-disturb-off" | "do-disturb-on" | "do-not-disturb-alt" | "do-not-disturb-off" | "do-not-disturb-on" | "do-not-disturb-on-total-silence" | "do-not-step" | "do-not-touch" | "dock" | "document-scanner" | "domain-add" | "domain-disabled" | "domain-verification" | "done" | "done-all" | "done-outline" | "donut-large" | "donut-small" | "door-back" | "door-front" | "double-arrow" | "downhill-skiing" | "download-done" | "download-for-offline" | "downloading" | "drafts" | "drag-handle" | "drag-indicator" | "drive-eta" | "drive-file-move" | "drive-file-move-outline" | "drive-file-move-rtl" | "drive-file-rename-outline" | "drive-folder-upload" | "dry" | "dry-cleaning" | "duo" | "dvr" | "dynamic-feed" | "dynamic-form" | "e-mobiledata" | "earbuds-battery" | "east" | "eco" | "edgesensor-high" | "edgesensor-low" | "edit-attributes" | "edit-calendar" | "edit-document" | "edit-location" | "edit-location-alt" | "edit-note" | "edit-notifications" | "edit-off" | "edit-road" | "edit-square" | "egg-alt" | "elderly" | "elderly-woman" | "electric-bike" | "electric-bolt" | "electric-car" | "electric-meter" | "electric-moped" | "electric-rickshaw" | "electric-scooter" | "electrical-services" | "emergency" | "emergency-recording" | "emergency-share" | "emoji-emotions" | "emoji-events" | "emoji-flags" | "emoji-food-beverage" | "emoji-nature" | "emoji-objects" | "emoji-people" | "emoji-symbols" | "emoji-transportation" | "energy-savings-leaf" | "engineering" | "enhance-photo-translate" | "enhanced-encryption" | "error" | "error-outline" | "escalator-warning" | "euro-symbol" | "event" | "event-available" | "event-busy" | "event-note" | "event-repeat" | "event-seat" | "expand-circle-down" | "expand-less" | "expand-more" | "explicit" | "explore" | "explore-off" | "exposure" | "exposure-minus-1" | "exposure-minus-2" | "exposure-neg-1" | "exposure-neg-2" | "exposure-plus-1" | "exposure-plus-2" | "exposure-zero" | "extension" | "extension-off" | "face" | "face-2" | "face-3" | "face-4" | "face-5" | "face-6" | "face-retouching-natural" | "face-retouching-off" | "fact-check" | "family-restroom" | "fast-rewind" | "fastfood" | "favorite-border" | "favorite-outline" | "featured-play-list" | "featured-video" | "feedback" | "festival" | "fiber-dvr" | "fiber-manual-record" | "fiber-new" | "fiber-pin" | "fiber-smart-record" | "file-copy" | "file-download-done" | "file-download-off" | "file-open" | "file-present" | "file-upload-off" | "filter-1" | "filter-2" | "filter-3" | "filter-4" | "filter-5" | "filter-6" | "filter-7" | "filter-8" | "filter-9" | "filter-9-plus" | "filter-alt" | "filter-alt-off" | "filter-b-and-w" | "filter-center-focus" | "filter-drama" | "filter-frames" | "filter-hdr" | "filter-list" | "filter-list-alt" | "filter-list-off" | "filter-none" | "filter-tilt-shift" | "filter-vintage" | "find-in-page" | "fire-hydrant-alt" | "first-page" | "fit-screen" | "fitbit" | "fitness-center" | "flag-circle" | "flaky" | "flash-on" | "flashlight-on" | "flatware" | "flight" | "flight-class" | "flight-land" | "flight-takeoff" | "flip" | "flip-camera-android" | "flip-camera-ios" | "flood" | "flourescent" | "fluorescent" | "flutter-dash" | "fmd-bad" | "fmd-good" | "foggy" | "folder-copy" | "folder-delete" | "folder-off" | "folder-shared" | "folder-special" | "follow-the-signs" | "font-download" | "font-download-off" | "food-bank" | "fork-left" | "fork-right" | "format-color-reset" | "format-list-bulleted-add" | "format-quote" | "format-shapes" | "format-underlined" | "fort" | "forward-10" | "forward-30" | "forward-5" | "forward-to-inbox" | "free-breakfast" | "free-cancellation" | "front-hand" | "front-loader" | "functions" | "g-mobiledata" | "g-translate" | "games" | "gas-meter" | "generating-tokens" | "get-app" | "gif" | "gif-box" | "girl" | "gite" | "goat" | "golf-course" | "gpp-bad" | "gpp-good" | "gpp-maybe" | "gps-fixed" | "gps-not-fixed" | "gps-off" | "grade" | "gradient" | "grading" | "graphic-eq" | "grid-3x3" | "grid-4x4" | "grid-goldenratio" | "grid-on" | "grid-view" | "group-add" | "group-off" | "group-remove" | "group-work" | "groups" | "groups-2" | "groups-3" | "h-mobiledata" | "h-plus-mobiledata" | "handyman" | "hardware" | "hd" | "hdr-auto" | "hdr-auto-select" | "hdr-enhanced-select" | "hdr-off-select" | "hdr-on" | "hdr-on-select" | "hdr-plus" | "hdr-strong" | "hdr-weak" | "headphones-battery" | "headset-mic" | "healing" | "health-and-safety" | "hearing" | "hearing-disabled" | "heat-pump" | "height" | "help-center" | "hevc" | "hide-image" | "hide-source" | "high-quality" | "highlight" | "highlight-alt" | "highlight-off" | "highlight-remove" | "history-edu" | "history-toggle-off" | "hls" | "hls-off" | "home-filled" | "home-max" | "home-mini" | "home-repair-service" | "home-work" | "horizontal-distribute" | "horizontal-rule" | "horizontal-split" | "hotel-class" | "hourglass-bottom" | "hourglass-disabled" | "hourglass-empty" | "hourglass-full" | "hourglass-top" | "house-siding" | "houseboat" | "how-to-reg" | "how-to-vote" | "http" | "https" | "hub" | "ice-skating" | "icecream" | "image-aspect-ratio" | "image-not-supported" | "imagesearch-roller" | "import-contacts" | "import-export" | "important-devices" | "incomplete-circle" | "indeterminate-check-box" | "info-outline" | "insert-chart" | "insert-chart-outlined" | "insert-comment" | "insert-drive-file" | "insert-emoticon" | "insert-invitation" | "insert-link" | "insert-page-break" | "insert-photo" | "insights" | "install-desktop" | "install-mobile" | "integration-instructions" | "interests" | "interpreter-mode" | "inventory" | "inventory-2" | "invert-colors-on" | "ios-share" | "iso" | "javascript" | "join-full" | "join-inner" | "join-left" | "join-right" | "kebab-dining" | "key-off" | "keyboard-alt" | "keyboard-arrow-down" | "keyboard-arrow-left" | "keyboard-arrow-right" | "keyboard-arrow-up" | "keyboard-capslock" | "keyboard-command" | "keyboard-command-key" | "keyboard-control" | "keyboard-control-key" | "keyboard-double-arrow-down" | "keyboard-double-arrow-left" | "keyboard-double-arrow-right" | "keyboard-double-arrow-up" | "keyboard-hide" | "keyboard-option" | "keyboard-option-key" | "keyboard-voice" | "king-bed" | "kitchen" | "label-important" | "label-important-outline" | "landscape" | "landslide" | "laptop-chromebook" | "laptop-mac" | "laptop-windows" | "last-page" | "layers-clear" | "leaderboard" | "leak-add" | "leak-remove" | "leave-bags-at-home" | "legend-toggle" | "lens" | "lens-blur" | "library-add" | "library-add-check" | "library-books" | "library-music" | "light-mode" | "lightbulb-circle" | "line-axis" | "line-style" | "line-weight" | "linear-scale" | "linked-camera" | "live-help" | "live-tv" | "living" | "local-activity" | "local-airport" | "local-atm" | "local-attraction" | "local-bar" | "local-cafe" | "local-car-wash" | "local-convenience-store" | "local-dining" | "local-drink" | "local-fire-department" | "local-florist" | "local-gas-station" | "local-grocery-store" | "local-hospital" | "local-hotel" | "local-laundry-service" | "local-library" | "local-mall" | "local-movies" | "local-offer" | "local-parking" | "local-pharmacy" | "local-phone" | "local-pizza" | "local-play" | "local-police" | "local-post-office" | "local-print-shop" | "local-printshop" | "local-restaurant" | "local-see" | "local-shipping" | "local-taxi" | "location-city" | "location-disabled" | "location-history" | "location-off" | "location-on" | "location-searching" | "lock-person" | "logo-dev" | "looks-3" | "looks-4" | "looks-5" | "looks-6" | "looks-one" | "looks-two" | "low-priority" | "loyalty" | "lte-mobiledata" | "lte-plus-mobiledata" | "luggage" | "lunch-dining" | "lyrics" | "macro-off" | "mail-lock" | "man-2" | "man-3" | "man-4" | "manage-accounts" | "manage-history" | "manage-search" | "maps-home-work" | "maps-ugc" | "mark-as-unread" | "mark-chat-read" | "mark-chat-unread" | "mark-email-read" | "mark-email-unread" | "mark-unread-chat-alt" | "markunread" | "markunread-mailbox" | "masks" | "media-bluetooth-off" | "media-bluetooth-on" | "mediation" | "medical-information" | "medical-services" | "medication" | "medication-liquid" | "meeting-room" | "menu-book" | "merge-type" | "messenger-outline" | "mic-external-off" | "mic-external-on" | "mic-none" | "military-tech" | "minor-crash" | "miscellaneous-services" | "missed-video-call" | "mms" | "mobile-friendly" | "mobile-off" | "mobile-screen-share" | "mobiledata-off" | "mode" | "mode-comment" | "mode-edit" | "mode-edit-outline" | "mode-fan-off" | "mode-night" | "mode-of-travel" | "mode-standby" | "model-training" | "monetization-on" | "money-off" | "money-off-csred" | "monitor-heart" | "monitor-weight" | "monochrome-photos" | "mood" | "mood-bad" | "more-horiz" | "more-time" | "more-vert" | "motion-photos-auto" | "motion-photos-off" | "motion-photos-on" | "motion-photos-pause" | "motion-photos-paused" | "move-down" | "move-to-inbox" | "move-up" | "movie-creation" | "moving" | "mp" | "multiline-chart" | "multiple-stop" | "multitrack-audio" | "museum" | "music-video" | "my-library-add" | "my-library-books" | "my-library-music" | "my-location" | "nat" | "navigate-before" | "navigate-next" | "near-me-disabled" | "nearby-error" | "nearby-off" | "nest-cam-wired-stand" | "network-cell" | "network-check" | "network-locked" | "network-ping" | "network-wifi" | "network-wifi-1-bar" | "network-wifi-2-bar" | "network-wifi-3-bar" | "new-label" | "new-releases" | "next-plan" | "next-week" | "night-shelter" | "nightlife" | "nightlight" | "nightlight-round" | "nights-stay" | "no-accounts" | "no-adult-content" | "no-backpack" | "no-cell" | "no-crash" | "no-drinks" | "no-encryption" | "no-encryption-gmailerrorred" | "no-flash" | "no-food" | "no-luggage" | "no-meals" | "no-meals-ouline" | "no-meeting-room" | "no-photography" | "no-sim" | "no-stroller" | "no-transfer" | "noise-aware" | "noise-control-off" | "nordic-walking" | "north" | "north-east" | "north-west" | "not-accessible" | "not-interested" | "not-listed-location" | "not-started" | "note-add" | "note-alt" | "notes" | "notification-add" | "notification-important" | "notifications-active" | "notifications-none" | "notifications-on" | "notifications-paused" | "now-wallpaper" | "now-widgets" | "numbers" | "offline-bolt" | "offline-pin" | "offline-share" | "oil-barrel" | "on-device-training" | "ondemand-video" | "online-prediction" | "open-in-browser" | "open-in-full" | "open-in-new-off" | "open-with" | "other-houses" | "outbond" | "outbound" | "outbox" | "outdoor-grill" | "outgoing-mail" | "outlet" | "outlined-flag" | "padding" | "pages" | "pageview" | "paid" | "pan-tool" | "pan-tool-alt" | "panorama-fish-eye" | "panorama-horizontal-select" | "panorama-photosphere" | "panorama-photosphere-select" | "panorama-vertical-select" | "panorama-wide-angle-select" | "park" | "party-mode" | "password" | "pause-circle-filled" | "pause-presentation" | "payment" | "payments" | "pedal-bike" | "pending" | "pending-actions" | "people-alt" | "perm-camera-mic" | "perm-contact-cal" | "perm-contact-calendar" | "perm-data-setting" | "perm-device-info" | "perm-device-information" | "perm-identity" | "perm-media" | "perm-phone-msg" | "perm-scan-wifi" | "person-2" | "person-3" | "person-4" | "person-add-alt" | "person-add-alt-1" | "person-add-disabled" | "person-off" | "person-pin" | "person-pin-circle" | "person-remove-alt-1" | "person-search" | "personal-injury" | "personal-video" | "pest-control" | "pest-control-rodent" | "pets" | "phishing" | "phone-android" | "phone-bluetooth-speaker" | "phone-callback" | "phone-disabled" | "phone-enabled" | "phone-iphone" | "phone-locked" | "phonelink" | "phonelink-erase" | "phonelink-lock" | "phonelink-off" | "phonelink-ring" | "phonelink-setup" | "photo-album" | "photo-camera" | "photo-camera-back" | "photo-camera-front" | "photo-filter" | "photo-library" | "photo-size-select-actual" | "photo-size-select-large" | "photo-size-select-small" | "picture-as-pdf" | "picture-in-picture" | "picture-in-picture-alt" | "pie-chart-outlined" | "pin-drop" | "pin-end" | "pin-invoke" | "pinch" | "pivot-table-chart" | "place" | "plagiarism" | "play-arrow" | "play-circle-fill" | "play-circle-filled" | "play-disabled" | "play-for-work" | "play-lesson" | "playlist-add" | "playlist-add-check" | "playlist-add-check-circle" | "playlist-add-circle" | "plumbing" | "plus-one" | "podcasts" | "policy" | "portable-wifi-off" | "post-add" | "power-input" | "power-settings-new" | "precision-manufacturing" | "pregnant-woman" | "present-to-all" | "price-change" | "price-check" | "print-disabled" | "privacy-tip" | "private-connectivity" | "production-quantity-limits" | "propane" | "psychology" | "psychology-alt" | "public" | "public-off" | "published-with-changes" | "punch-clock" | "push-pin" | "qr-code-2" | "qr-code-scanner" | "query-builder" | "query-stats" | "question-answer" | "question-mark" | "queue" | "queue-music" | "queue-play-next" | "quick-contacts-dialer" | "quick-contacts-mail" | "quickreply" | "quiz" | "r-mobiledata" | "radio-button-checked" | "radio-button-unchecked" | "railway-alert" | "ramen-dining" | "ramp-left" | "ramp-right" | "rate-review" | "raw-off" | "raw-on" | "read-more" | "real-estate-agent" | "rebase-edit" | "receipt-long" | "recent-actors" | "recommend" | "record-voice-over" | "recycling" | "redeem" | "reduce-capacity" | "remember-me" | "remove-done" | "remove-from-queue" | "remove-moderator" | "remove-red-eye" | "remove-road" | "remove-shopping-cart" | "repartition" | "repeat-on" | "repeat-one" | "repeat-one-on" | "replay-10" | "replay-30" | "replay-5" | "replay-circle-filled" | "report" | "report-gmailerrorred" | "report-off" | "report-problem" | "request-page" | "request-quote" | "reset-tv" | "restart-alt" | "restaurant-menu" | "restore-from-trash" | "restore-page" | "reviews" | "rice-bowl" | "ring-volume" | "roller-shades" | "roller-shades-closed" | "roller-skating" | "roofing" | "room-preferences" | "rotate-90-degrees-ccw" | "rotate-90-degrees-cw" | "roundabout-left" | "roundabout-right" | "rss-feed" | "rsvp" | "rtt" | "rule" | "rule-folder" | "run-circle" | "running-with-errors" | "rv-hookup" | "safety-check" | "safety-divider" | "sailing" | "sanitizer" | "satellite-alt" | "save-alt" | "save-as" | "saved-search" | "savings" | "schedule" | "schedule-send" | "schema" | "science" | "score" | "screen-lock-landscape" | "screen-lock-portrait" | "screen-lock-rotation" | "screen-rotation-alt" | "screen-search-desktop" | "screen-share" | "screenshot" | "screenshot-monitor" | "scuba-diving" | "sd-card-alert" | "sd-storage" | "search-off" | "security-update" | "security-update-good" | "security-update-warning" | "self-improvement" | "sell" | "send-and-archive" | "send-time-extension" | "send-to-mobile" | "sensor-door" | "sensor-occupied" | "sensor-window" | "sensors" | "sensors-off" | "sentiment-dissatisfied" | "sentiment-neutral" | "sentiment-satisfied" | "sentiment-satisfied-alt" | "sentiment-very-dissatisfied" | "sentiment-very-satisfied" | "set-meal" | "settings-accessibility" | "settings-applications" | "settings-backup-restore" | "settings-bluetooth" | "settings-brightness" | "settings-cell" | "settings-display" | "settings-ethernet" | "settings-input-antenna" | "settings-input-component" | "settings-input-composite" | "settings-input-hdmi" | "settings-input-svideo" | "settings-overscan" | "settings-phone" | "settings-power" | "settings-remote" | "settings-suggest" | "settings-system-daydream" | "settings-voice" | "severe-cold" | "shape-line" | "share-arrival-time" | "share-location" | "shelves" | "shop-2" | "shop-two" | "shopping-cart-checkout" | "short-text" | "shortcut" | "show-chart" | "shuffle-on" | "shutter-speed" | "sick" | "signal-cellular-0-bar" | "signal-cellular-4-bar" | "signal-cellular-alt" | "signal-cellular-alt-1-bar" | "signal-cellular-alt-2-bar" | "signal-cellular-connected-no-internet-0-bar" | "signal-cellular-connected-no-internet-4-bar" | "signal-cellular-no-sim" | "signal-cellular-nodata" | "signal-cellular-null" | "signal-cellular-off" | "signal-wifi-0-bar" | "signal-wifi-4-bar" | "signal-wifi-4-bar-lock" | "signal-wifi-bad" | "signal-wifi-connected-no-internet-4" | "signal-wifi-off" | "signal-wifi-statusbar-4-bar" | "signal-wifi-statusbar-connected-no-internet-4" | "signal-wifi-statusbar-null" | "signpost" | "sim-card-alert" | "sim-card-download" | "single-bed" | "sip" | "slideshow" | "slow-motion-video" | "smart-button" | "smart-display" | "smart-screen" | "smart-toy" | "smoke-free" | "smoking-rooms" | "sms-failed" | "snippet-folder" | "snooze" | "snowing" | "snowshoeing" | "social-distance" | "sort-by-alpha" | "sos" | "soup-kitchen" | "south" | "south-america" | "south-east" | "south-west" | "space-bar" | "space-dashboard" | "spatial-audio" | "spatial-audio-off" | "spatial-tracking" | "speaker-group" | "speaker-notes" | "speaker-notes-off" | "speaker-phone" | "speed" | "splitscreen" | "spoke" | "sports" | "sports-bar" | "sports-baseball" | "sports-basketball" | "sports-cricket" | "sports-esports" | "sports-football" | "sports-golf" | "sports-gymnastics" | "sports-handball" | "sports-hockey" | "sports-kabaddi" | "sports-martial-arts" | "sports-mma" | "sports-motorsports" | "sports-rugby" | "sports-score" | "sports-soccer" | "sports-tennis" | "sports-volleyball" | "square-foot" | "ssid-chart" | "stacked-bar-chart" | "stacked-line-chart" | "star-border" | "star-border-purple500" | "star-purple500" | "star-rate" | "stars" | "start" | "stay-current-landscape" | "stay-current-portrait" | "stay-primary-landscape" | "stay-primary-portrait" | "sticky-note-2" | "stop-screen-share" | "storage" | "store-mall-directory" | "storm" | "straight" | "straighten" | "streetview" | "strikethrough-s" | "stroller" | "subject" | "subscriptions" | "subtitles-off" | "summarize" | "sunny-snowing" | "supervised-user-circle" | "supervisor-account" | "support-agent" | "swap-calls" | "swap-horiz" | "swap-vert" | "swap-vert-circle" | "swipe" | "swipe-down" | "swipe-down-alt" | "swipe-left" | "swipe-left-alt" | "swipe-right" | "swipe-right-alt" | "swipe-up" | "swipe-up-alt" | "swipe-vertical" | "switch-access-shortcut" | "switch-access-shortcut-add" | "switch-account" | "switch-camera" | "switch-left" | "switch-right" | "switch-video" | "sync-disabled" | "sync-lock" | "sync-problem" | "system-security-update" | "system-security-update-good" | "system-security-update-warning" | "system-update" | "system-update-alt" | "system-update-tv" | "table-bar" | "table-chart" | "table-restaurant" | "table-rows" | "table-view" | "tablet-mac" | "takeout-dining" | "tap-and-play" | "tapas" | "task" | "task-alt" | "taxi-alert" | "temple-buddhist" | "temple-hindu" | "text-decrease" | "text-fields" | "text-format" | "text-increase" | "text-rotate-up" | "text-rotate-vertical" | "text-rotation-angledown" | "text-rotation-angleup" | "text-rotation-down" | "text-rotation-none" | "text-snippet" | "textsms" | "theater-comedy" | "theaters" | "thermostat-auto" | "thumb-down-alt" | "thumb-down-off-alt" | "thumb-up-alt" | "thumb-up-off-alt" | "time-to-leave" | "timer-10-select" | "timer-3-select" | "tips-and-updates" | "tire-repair" | "toc" | "token" | "toll" | "tonality" | "topic" | "touch-app" | "tour" | "toys" | "track-changes" | "traffic" | "transfer-within-a-station" | "transform" | "transit-enterexit" | "travel-explore" | "trending-flat" | "trip-origin" | "trolley" | "troubleshoot" | "tsunami" | "tungsten" | "turn-left" | "turn-right" | "turn-sharp-left" | "turn-sharp-right" | "turn-slight-left" | "turn-slight-right" | "turned-in" | "turned-in-not" | "tv-off" | "two-wheeler" | "type-specimen" | "u-turn-left" | "u-turn-right" | "unarchive" | "unfold-less" | "unfold-less-double" | "unfold-more" | "unfold-more-double" | "unpublished" | "unsubscribe" | "upcoming" | "update-disabled" | "upgrade" | "upload-file" | "usb-off" | "vaccines" | "vape-free" | "vaping-rooms" | "verified" | "verified-user" | "vertical-align-bottom" | "vertical-align-center" | "vertical-align-top" | "vertical-distribute" | "vertical-shades" | "vertical-shades-closed" | "vertical-split" | "vibration" | "video-call" | "video-camera-back" | "video-camera-front" | "video-chat" | "video-collection" | "video-file" | "video-label" | "video-library" | "video-settings" | "video-stable" | "videogame-asset" | "videogame-asset-off" | "view-comfortable" | "view-comfy-alt" | "view-compact-alt" | "view-cozy" | "view-in-ar" | "view-kanban" | "view-sidebar" | "view-timeline" | "vignette" | "villa" | "visibility" | "visibility-off" | "voice-chat" | "voice-over-off" | "volume-down-alt" | "volunteer-activism" | "vpn-key" | "vpn-key-off" | "vpn-lock" | "vrpano" | "warning-amber" | "wash" | "watch-later" | "watch-off" | "water-damage" | "water-drop" | "waterfall-chart" | "waving-hand" | "wb-auto" | "wb-cloudy" | "wb-incandescent" | "wb-iridescent" | "wb-shade" | "wb-sunny" | "wb-twighlight" | "wb-twilight" | "wc" | "web-asset" | "web-asset-off" | "web-stories" | "weekend" | "west" | "whatshot" | "wheelchair-pickup" | "where-to-vote" | "width-full" | "width-normal" | "width-wide" | "wifi-1-bar" | "wifi-2-bar" | "wifi-calling" | "wifi-calling-3" | "wifi-channel" | "wifi-find" | "wifi-password" | "wifi-protected-setup" | "wifi-tethering" | "wifi-tethering-error" | "wifi-tethering-error-rounded" | "wifi-tethering-off" | "wind-power" | "window" | "wine-bar" | "woman-2" | "woo-commerce" | "work" | "work-history" | "work-off" | "work-outline" | "workspace-premium" | "workspaces" | "workspaces-filled" | "workspaces-outline" | "wrap-text" | "wrong-location" | "wysiwyg" | "yard" | "youtube-searched-for" | "zoom-in-map" | "zoom-out-map", "material">;
export default _default;
//# sourceMappingURL=MaterialIcons.d.ts.map