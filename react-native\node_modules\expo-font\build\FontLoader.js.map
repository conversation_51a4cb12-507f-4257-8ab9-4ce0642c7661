{"version": 3, "file": "FontLoader.js", "sourceRoot": "", "sources": ["../src/FontLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAG9C,MAAM,UAAU,iBAAiB,CAAC,MAAkB;IAClD,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,WAAW,EAAE,CAAC;QAC3E,OAAO,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAED,oEAAoE;IACpE,uEAAuE;IACvE,yCAAyC;IACzC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,IAAY,EACZ,KAA2B;IAE3B,MAAM,KAAK,GAAG,KAAc,CAAC;IAC7B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QACzB,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,mFAAmF,CACpF,CAAC;IACJ,CAAC;IAED,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QACtB,MAAM,IAAI,UAAU,CAAC,cAAc,EAAE,sCAAsC,IAAI,GAAG,CAAC,CAAC;IACtF,CAAC;IACD,MAAM,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACvD,CAAC", "sourcesContent": ["import { Asset } from 'expo-asset';\nimport { CodedError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontResource, FontSource } from './Font.types';\n\nexport function getAssetForSource(source: FontSource): Asset | FontResource {\n  if (source instanceof Asset) {\n    return source;\n  }\n\n  if (typeof source === 'string') {\n    return Asset.fromURI(source);\n  } else if (typeof source === 'number') {\n    return Asset.fromModule(source);\n  } else if (typeof source === 'object' && typeof source.uri !== 'undefined') {\n    return getAssetForSource(source.uri);\n  }\n\n  // @ts-ignore Error: Type 'string' is not assignable to type 'Asset'\n  // We can't have a string here, we would have thrown an error if !isWeb\n  // or returned Asset.fromModule if isWeb.\n  return source;\n}\n\nexport async function loadSingleFontAsync(\n  name: string,\n  input: Asset | FontResource\n): Promise<void> {\n  const asset = input as Asset;\n  if (!asset.downloadAsync) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      '`loadSingleFontAsync` expected resource of type `Asset` from expo-asset on native'\n    );\n  }\n\n  await asset.downloadAsync();\n  if (!asset.downloaded) {\n    throw new CodedError(`ERR_DOWNLOAD`, `Failed to download asset for font \"${name}\"`);\n  }\n  await ExpoFontLoader.loadAsync(name, asset.localUri);\n}\n"]}