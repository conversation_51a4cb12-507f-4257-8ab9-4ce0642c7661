const express = require('express');
const { pool } = require('./database');
const PDFDocument = require('pdfkit');

const router = express.Router();

// 🧾 FONCTION DE GÉNÉRATION AUTOMATIQUE DE FACTURES
async function generateFactureAutomatique(consommation) {
  try {
    console.log('🧾 Génération automatique de facture pour consommation:', consommation.idcons);

    // 1. Récupérer les informations de la tranche tarifaire
    const tranchQuery = `
      SELECT prix, valeurmin, valeurmax
      FROM tranch
      WHERE idtranch = $1
    `;
    const tranchResult = await pool.query(tranchQuery, [consommation.idtranch]);

    if (tranchResult.rows.length === 0) {
      console.log('❌ Tranche tarifaire non trouvée pour idTranch:', consommation.idtranch);
      return { success: false, message: 'Tranche tarifaire non trouvée' };
    }

    const tranche = tranchResult.rows[0];
    console.log('📊 Tranche tarifaire:', tranche);

    // 2. Calculer la consommation facturée
    const consommationFacturee = consommation.consommationactuelle - consommation.consommationpre;
    console.log(`💧 Consommation facturée: ${consommationFacturee} m³`);

    // 3. Calculer le montant selon la tranche
    let montantFacture = 0;

    if (consommationFacturee <= tranche.valeurmin) {
      // Consommation dans la tranche minimale
      montantFacture = tranche.prix;
      console.log(`💰 Facturation forfait minimal: ${montantFacture} DH`);
    } else if (consommationFacturee <= tranche.valeurmax) {
      // Consommation dans la tranche normale
      montantFacture = consommationFacturee * tranche.prix;
      console.log(`💰 Facturation normale: ${consommationFacturee} × ${tranche.prix} = ${montantFacture} DH`);
    } else {
      // Consommation dépassant la tranche maximale (tarif majoré)
      const consommationNormale = tranche.valeurmax;
      const consommationExcedentaire = consommationFacturee - tranche.valeurmax;
      const tarifMajore = tranche.prix * 1.5; // Majoration de 50%

      montantFacture = (consommationNormale * tranche.prix) + (consommationExcedentaire * tarifMajore);
      console.log(`💰 Facturation avec dépassement: ${consommationNormale} × ${tranche.prix} + ${consommationExcedentaire} × ${tarifMajore} = ${montantFacture} DH`);
    }

    // 4. Générer une référence unique pour la facture
    const reference = Math.floor(Math.random() * 999999) + 100000; // Référence entre 100000 et 999999

    // 5. Insérer la facture dans la base de données
    const factureQuery = `
      INSERT INTO facture (date, idconst, montant, periode, reference, status)
      VALUES (NOW(), $1, $2, $3, $4, 'nonpayée')
      RETURNING *
    `;

    const factureValues = [
      consommation.idcons,
      parseFloat(montantFacture.toFixed(2)),
      consommation.periode,
      reference
    ];

    const factureResult = await pool.query(factureQuery, factureValues);
    const nouvelleFacture = factureResult.rows[0];

    console.log('✅ Facture générée automatiquement:', nouvelleFacture);

    return {
      success: true,
      data: nouvelleFacture,
      details: {
        consommationFacturee: consommationFacturee,
        tranche: tranche,
        calculMontant: montantFacture
      },
      message: `Facture générée: ${montantFacture.toFixed(2)} DH pour ${consommationFacturee} m³`
    };

  } catch (error) {
    console.error('❌ Erreur lors de la génération de facture:', error.message);
    return {
      success: false,
      message: 'Erreur lors de la génération de facture',
      error: error.message
    };
  }
}

// 🧾 Route pour récupérer toutes les factures avec détails
router.get('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures');

    const query = `
      SELECT
        f.idfact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        cons.consommationpre,
        cons.consommationactuelle,
        (cons.consommationactuelle - cons.consommationpre) as consommation_facturee,
        cont.codeqr,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        u.nom as technicien_nom,
        u.prenom as technicien_prenom
      FROM facture f
      LEFT JOIN consommation cons ON f.idconst = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN utilisateur u ON cons.idtech = u.idtech
      ORDER BY f.date DESC
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} facture(s) récupérée(s)`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} facture(s) trouvée(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures',
      error: error.message
    });
  }
});

// 🧾 Route pour récupérer une facture spécifique avec tous les détails
router.get('/api/factures/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/factures/${id}`);

    const query = `
      SELECT
        f.idfact,
        f.date,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        cons.consommationpre,
        cons.consommationactuelle,
        (cons.consommationactuelle - cons.consommationpre) as consommation_facturee,
        cons.jours,
        cont.codeqr,
        cont.marquecompteur,
        cont.numseriecompteur,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        u.nom as technicien_nom,
        u.prenom as technicien_prenom,
        t.prix as tarif_unitaire,
        t.valeurmin,
        t.valeurmax
      FROM facture f
      LEFT JOIN consommation cons ON f.idconst = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN utilisateur u ON cons.idtech = u.idtech
      LEFT JOIN tranch t ON cons.idtranch = t.idtranch
      WHERE f.idfact = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Facture ID ${id} non trouvée`
      });
    }

    console.log(`✅ Facture ${id} récupérée avec détails complets`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Facture trouvée avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la facture:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la facture',
      error: error.message
    });
  }
});

// 🧾 Route pour mettre à jour le statut d'une facture (payée/nonpayée)
router.put('/api/factures/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    console.log(`📥 Requête PUT /api/factures/${id}/status - Nouveau statut: ${status}`);

    // Validation du statut
    if (!['payée', 'nonpayée'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Statut invalide. Utilisez "payée" ou "nonpayée"'
      });
    }

    const query = `
      UPDATE facture
      SET status = $1
      WHERE idfact = $2
      RETURNING *
    `;

    const result = await pool.query(query, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Facture ID ${id} non trouvée`
      });
    }

    console.log(`✅ Statut de la facture ${id} mis à jour: ${status}`);
    res.json({
      success: true,
      data: result.rows[0],
      message: `Statut de la facture mis à jour: ${status}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut',
      error: error.message
    });
  }
});

// 🧾 Route pour créer une nouvelle facture manuellement
router.post('/api/factures', async (req, res) => {
  try {
    const { idconst, reference } = req.body;
    console.log(`📥 Requête POST /api/factures - Création manuelle pour consommation: ${idconst}`);

    // 1. Récupérer les détails de la consommation
    const consommationQuery = `
      SELECT * FROM consommation WHERE idcons = $1
    `;
    const consommationResult = await pool.query(consommationQuery, [idconst]);

    if (consommationResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Consommation ID ${idconst} non trouvée`
      });
    }

    const consommation = consommationResult.rows[0];

    // 2. Vérifier si une facture existe déjà pour cette consommation
    const factureExistanteQuery = `
      SELECT * FROM facture WHERE idconst = $1
    `;
    const factureExistanteResult = await pool.query(factureExistanteQuery, [idconst]);

    if (factureExistanteResult.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Une facture existe déjà pour cette consommation (ID: ${factureExistanteResult.rows[0].idfact})`
      });
    }

    // 3. Générer la facture automatiquement
    const factureResult = await generateFactureAutomatique(consommation);

    if (factureResult.success) {
      console.log(`✅ Facture créée manuellement avec ID: ${factureResult.data.idfact}`);
      res.status(201).json(factureResult);
    } else {
      res.status(500).json(factureResult);
    }

  } catch (error) {
    console.error('❌ Erreur lors de la création manuelle de facture:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la facture',
      error: error.message
    });
  }
});

// 🧾 Route pour supprimer une facture
router.delete('/api/factures/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête DELETE /api/factures/${id}`);

    const query = `
      DELETE FROM facture
      WHERE idfact = $1
      RETURNING *
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Facture ID ${id} non trouvée`
      });
    }

    console.log(`✅ Facture ${id} supprimée`);
    res.json({
      success: true,
      data: result.rows[0],
      message: `Facture ${id} supprimée avec succès`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la suppression de la facture:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la facture',
      error: error.message
    });
  }
});

// Route pour générer le PDF d'une facture
router.get('/api/factures/:id/pdf', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📄 Génération PDF pour la facture ${id}`);

    // Récupérer toutes les informations de la facture avec les données client et consommation
    const query = `
      SELECT
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cons.periode as periode_consommation,
        cont.codeqr as contrat_reference,
        cont.marquecompteur,
        cont.numseriecompteur,
        s.nom as secteur_nom,
        t.prix as tarif_prix,
        t.valeurmin as tarif_min,
        t.valeurmax as tarif_max
      FROM facture f
      LEFT JOIN consommation cons ON f.idconst = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      LEFT JOIN tranch t ON cons.idtranch = t.idtranch
      WHERE f.idfact = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    const facture = result.rows[0];
    console.log('📊 Données facture récupérées:', facture);

    // Créer le document PDF
    const doc = new PDFDocument({ margin: 50 });

    // Configuration des headers pour le téléchargement
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=facture-${id}.pdf`);

    // Pipe le PDF vers la réponse
    doc.pipe(res);

    // En-tête de la facture
    doc.fontSize(20);
    doc.text('AquaTrack', 50, 50);
    doc.fontSize(12);
    doc.text('Système de Facturation d\'Eau', 50, 75);
    doc.text('Province de Taounate', 50, 90);

    // Informations de la facture (côté droit)
    const factureNum = `FAC-${new Date(facture.date).getFullYear()}-${String(facture.idfact).padStart(3, '0')}`;
    doc.fontSize(14);
    doc.text(factureNum, 400, 50);
    doc.fontSize(10);
    doc.text(`Date: ${new Date(facture.date).toLocaleDateString('fr-FR')}`, 400, 70);
    doc.text(`Référence: ${facture.reference}`, 400, 85);
    doc.text(`Statut: ${facture.status === 'payée' ? 'Payée' : 'En attente'}`, 400, 100);

    // Ligne de séparation
    doc.moveTo(50, 130).lineTo(550, 130).stroke();

    // Informations client
    doc.fontSize(12);
    doc.text('INFORMATIONS CLIENT', 50, 150);
    doc.fontSize(10);
    doc.text(`Nom: ${facture.client_nom || 'N/A'} ${facture.client_prenom || ''}`, 50, 170);
    doc.text(`Adresse: ${facture.client_adresse || 'N/A'}`, 50, 185);
    doc.text(`Ville: ${facture.client_ville || 'N/A'}`, 50, 200);
    doc.text(`Téléphone: ${facture.client_tel || 'N/A'}`, 50, 215);
    doc.text(`Secteur: ${facture.secteur_nom || 'N/A'}`, 50, 230);

    // Informations compteur
    doc.fontSize(12);
    doc.text('INFORMATIONS COMPTEUR', 300, 150);
    doc.fontSize(10);
    doc.text(`Code QR: ${facture.contrat_reference || 'N/A'}`, 300, 170);
    doc.text(`Marque: ${facture.marquecompteur || 'N/A'}`, 300, 185);
    doc.text(`N° Série: ${facture.numseriecompteur || 'N/A'}`, 300, 200);

    // Ligne de séparation
    doc.moveTo(50, 260).lineTo(550, 260).stroke();

    // Détails de consommation
    doc.fontSize(12);
    doc.text('DÉTAILS DE CONSOMMATION', 50, 280);

    const consommationFacturee = (facture.consommationactuelle || 0) - (facture.consommationpre || 0);

    doc.fontSize(10);
    doc.text(`Période: ${facture.periode || 'N/A'}`, 50, 300);
    doc.text(`Consommation précédente: ${facture.consommationpre || 0} m³`, 50, 315);
    doc.text(`Consommation actuelle: ${facture.consommationactuelle || 0} m³`, 50, 330);
    doc.text(`Consommation facturée: ${consommationFacturee} m³`, 50, 345);
    doc.text(`Nombre de jours: ${facture.jours || 0}`, 50, 360);

    // Calcul tarifaire
    doc.fontSize(12);
    doc.text('CALCUL TARIFAIRE', 300, 280);
    doc.fontSize(10);
    doc.text(`Tarif: ${facture.tarif_prix || 0} DH/m³`, 300, 300);
    doc.text(`Tranche: ${facture.tarif_min || 0} - ${facture.tarif_max || 0} m³`, 300, 315);

    // Ligne de séparation
    doc.moveTo(50, 390).lineTo(550, 390).stroke();

    // Montant total
    doc.fontSize(16);
    doc.text('MONTANT À PAYER', 50, 410);
    doc.fontSize(20);
    doc.text(`${parseFloat(facture.montant || 0).toFixed(2)} DH`, 300, 410);

    // Pied de page
    doc.fontSize(8);
    doc.text('Cette facture est générée automatiquement par le système AquaTrack', 50, 500);
    doc.text(`Générée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`, 50, 515);

    // Finaliser le PDF
    doc.end();

    console.log(`✅ PDF généré pour la facture ${id}`);

  } catch (error) {
    console.error('❌ Erreur lors de la génération du PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du PDF',
      error: error.message
    });
  }
});

// Export de la fonction de génération automatique et du router
module.exports = { router, generateFactureAutomatique };