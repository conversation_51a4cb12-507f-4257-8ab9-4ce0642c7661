declare const _default: import("./createIconSet").Icon<"link" | "search" | "image" | "retweet" | "minus" | "plus" | "exclamation" | "check" | "close" | "question" | "star" | "user" | "paperclip" | "lock" | "eye" | "camera" | "heart" | "unlock" | "play" | "tag" | "calendar" | "archive" | "arrow-down" | "arrow-left" | "arrow-right" | "arrow-up" | "bell" | "chevron-down" | "chevron-left" | "chevron-right" | "chevron-up" | "clock" | "credit-card" | "location" | "pencil" | "trash" | "trophy" | "cart" | "chart" | "close-o" | "comment" | "envelope" | "external-link" | "gear" | "like" | "navicon" | "pointer" | "redo" | "refresh" | "sc-facebook" | "sc-github" | "sc-google-plus" | "sc-instagram" | "sc-linkedin" | "sc-odnoklassniki" | "sc-pinterest" | "sc-skype" | "sc-soundcloud" | "sc-telegram" | "sc-tumblr" | "sc-twitter" | "sc-vimeo" | "sc-vk" | "sc-youtube" | "share-apple" | "share-google" | "spinner" | "spinner-2" | "spinner-3" | "undo", "evilicons">;
export default _default;
//# sourceMappingURL=EvilIcons.d.ts.map