{"version": 3, "sources": ["../../../src/export/html.ts"], "sourcesContent": ["// <link rel=\"preload\" href=\"/_expo/static/css/xxxxxx.css\" as=\"style\">\nexport function appendLinkToHtml(\n  html: string,\n  links: { rel: string; href: string; as?: string }[]\n) {\n  return html.replace(\n    '</head>',\n    links\n      .map((link) => {\n        let linkTag = `<link rel=\"${link.rel}\"`;\n\n        if (link.href) linkTag += ` href=\"${link.href}\"`;\n        if (link.as) linkTag += ` as=\"${link.as}\"`;\n\n        linkTag += '>';\n\n        return linkTag;\n      })\n      .join('') + '</head>'\n  );\n}\n\nexport function appendScriptsToHtml(html: string, scripts: string[]) {\n  return html.replace(\n    '</body>',\n    scripts.map((script) => `<script src=\"${script}\" defer></script>`).join('') + '</body>'\n  );\n}\n"], "names": ["appendLinkToHtml", "appendScriptsToHtml", "html", "links", "replace", "map", "link", "linkTag", "rel", "href", "as", "join", "scripts", "script"], "mappings": "AAAA,sEAAsE;;;;;;;;;;;;IACtDA,gBAAgB;eAAhBA;;IAqBAC,mBAAmB;eAAnBA;;;AArBT,SAASD,iBACdE,IAAY,EACZC,KAAmD;IAEnD,OAAOD,KAAKE,OAAO,CACjB,WACAD,MACGE,GAAG,CAAC,CAACC;QACJ,IAAIC,UAAU,CAAC,WAAW,EAAED,KAAKE,GAAG,CAAC,CAAC,CAAC;QAEvC,IAAIF,KAAKG,IAAI,EAAEF,WAAW,CAAC,OAAO,EAAED,KAAKG,IAAI,CAAC,CAAC,CAAC;QAChD,IAAIH,KAAKI,EAAE,EAAEH,WAAW,CAAC,KAAK,EAAED,KAAKI,EAAE,CAAC,CAAC,CAAC;QAE1CH,WAAW;QAEX,OAAOA;IACT,GACCI,IAAI,CAAC,MAAM;AAElB;AAEO,SAASV,oBAAoBC,IAAY,EAAEU,OAAiB;IACjE,OAAOV,KAAKE,OAAO,CACjB,WACAQ,QAAQP,GAAG,CAAC,CAACQ,SAAW,CAAC,aAAa,EAAEA,OAAO,iBAAiB,CAAC,EAAEF,IAAI,CAAC,MAAM;AAElF"}