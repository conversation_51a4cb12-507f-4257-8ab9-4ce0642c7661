// Configuration de l'API
export const API_CONFIG = {
  BASE_URL: 'http://***********:3007', // IP de votre ordinateur (port 3007 - port du serveur)
  TIMEOUT: 15000, // 15 secondes pour plus de temps
};

// Endpoints de l'API
export const API_ENDPOINTS = {
  // Authentification
  LOGIN: '/login',
  
  // Clients
  CLIENTS: '/api/clients',
  CLIENT_CONTRACTS: (clientId) => `/api/clients/${clientId}/contracts`,
  CLIENT_LAST_CONSOMMATION: (clientId) => `/api/clients/${clientId}/last-consommation`,
  
  // Contrats
  CONTRACTS: '/api/contracts',
  CONTRACT_LAST_CONSOMMATION: (contractId) => `/api/contracts/${contractId}/last-consommation`,
  
  // Consommations
  CONSOMMATIONS: '/api/consommations',
  
  // Factures
  FACTURES: '/api/factures',
  FACTURE_DETAILS: (factureId) => `/api/factures/${factureId}`,
  FACTURE_PDF: (factureId) => `/api/factures/${factureId}/pdf`,
  FACTURE_STATUS: (factureId) => `/api/factures/${factureId}/status`,
  
  // Secteurs
  SECTEURS: '/api/secteurs',
};

// Couleurs de l'application
export const COLORS = {
  PRIMARY: '#2196F3',
  SECONDARY: '#FFC107',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  ERROR: '#F44336',
  INFO: '#2196F3',
  
  // Couleurs de fond
  BACKGROUND: '#F5F5F5',
  SURFACE: '#FFFFFF',
  
  // Couleurs de texte
  TEXT_PRIMARY: '#333333',
  TEXT_SECONDARY: '#666666',
  TEXT_DISABLED: '#999999',
  TEXT_ON_PRIMARY: '#FFFFFF',
  
  // Couleurs de bordure
  BORDER: '#DDDDDD',
  BORDER_LIGHT: '#EEEEEE',
  
  // Couleurs d'état
  PAID: '#4CAF50',
  UNPAID: '#FF9800',
  ACTIVE: '#2196F3',
  INACTIVE: '#9E9E9E',
};

// Tailles de police
export const FONT_SIZES = {
  SMALL: 12,
  MEDIUM: 14,
  LARGE: 16,
  XLARGE: 18,
  XXLARGE: 20,
  TITLE: 24,
  HEADER: 28,
  DISPLAY: 32,
};

// Espacements
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 12,
  LG: 16,
  XL: 20,
  XXL: 24,
  XXXL: 32,
};

// Rayons de bordure
export const BORDER_RADIUS = {
  SMALL: 4,
  MEDIUM: 8,
  LARGE: 12,
  XLARGE: 16,
  ROUND: 50,
};

// Ombres
export const SHADOWS = {
  SMALL: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  MEDIUM: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  LARGE: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Messages d'erreur
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Erreur de connexion. Vérifiez votre connexion internet.',
  SERVER_ERROR: 'Erreur serveur. Veuillez réessayer plus tard.',
  VALIDATION_ERROR: 'Veuillez vérifier les données saisies.',
  PERMISSION_DENIED: 'Permission refusée.',
  NOT_FOUND: 'Ressource non trouvée.',
  UNAUTHORIZED: 'Accès non autorisé.',
  
  // Erreurs spécifiques
  LOGIN_FAILED: 'Email ou mot de passe incorrect.',
  CAMERA_PERMISSION: 'Permission caméra requise pour scanner les QR codes.',
  LOCATION_PERMISSION: 'Permission localisation requise pour afficher la carte.',
  INVALID_QR_CODE: 'QR code non reconnu.',
  CONSUMPTION_VALIDATION: 'La consommation actuelle doit être supérieure à la précédente.',
  NO_CONTRACT: 'Aucun contrat trouvé pour ce client.',
  PDF_DOWNLOAD_ERROR: 'Erreur lors du téléchargement du PDF.',
};

// Messages de succès
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Connexion réussie.',
  CONSUMPTION_SAVED: 'Consommation enregistrée avec succès.',
  PROFILE_UPDATED: 'Profil mis à jour avec succès.',
  STATUS_UPDATED: 'Statut mis à jour avec succès.',
  PDF_DOWNLOADED: 'PDF téléchargé avec succès.',
};

// Configuration de la carte
export const MAP_CONFIG = {
  DEFAULT_REGION: {
    latitude: 33.8307,
    longitude: -4.8372,
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  },
  MARKER_SIZE: 40,
  ZOOM_LEVEL: {
    CITY: 0.1,
    DISTRICT: 0.01,
    STREET: 0.001,
  },
};

// Configuration du scanner
export const SCANNER_CONFIG = {
  SCAN_AREA_SIZE: 250,
  CORNER_SIZE: 30,
  CORNER_BORDER_WIDTH: 3,
};

// Formats de date
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  API: 'YYYY-MM-DD',
  PERIOD: 'YYYY-MM',
  FULL: 'DD/MM/YYYY HH:mm',
};

// Validation
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^[0-9+\-\s()]+$/,
  PERIOD_REGEX: /^\d{4}-\d{2}$/,
  
  MIN_PASSWORD_LENGTH: 6,
  MAX_NAME_LENGTH: 50,
  MAX_ADDRESS_LENGTH: 200,
  MAX_EMAIL_LENGTH: 100,
};

// Configuration des permissions
export const PERMISSIONS = {
  CAMERA: 'camera',
  LOCATION: 'location',
  STORAGE: 'storage',
};

// Types de fichiers
export const FILE_TYPES = {
  PDF: 'application/pdf',
  IMAGE: 'image/*',
};

// Configuration de l'application
export const APP_CONFIG = {
  NAME: 'AquaTrack',
  VERSION: '1.0.0',
  COMPANY: 'AquaTrack Solutions',
  SUPPORT_EMAIL: '<EMAIL>',
  
  // Limites
  MAX_RETRY_ATTEMPTS: 3,
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  REQUEST_TIMEOUT: 10000, // 10 secondes
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
};

// États des entités
export const ENTITY_STATUS = {
  FACTURE: {
    PAID: 'payée',
    UNPAID: 'nonpayée',
  },
  CONSOMMATION: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
  },
  USER: {
    ADMIN: 'Admin',
    TECH: 'Tech',
  },
};

// Configuration des animations
export const ANIMATION_CONFIG = {
  DURATION: {
    SHORT: 200,
    MEDIUM: 300,
    LONG: 500,
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
  },
};
