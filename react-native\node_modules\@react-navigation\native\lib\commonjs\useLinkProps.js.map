{"version": 3, "names": ["getStateFromParams", "params", "state", "screen", "routes", "name", "undefined", "useLinkProps", "to", "action", "root", "React", "useContext", "NavigationContainerRefContext", "navigation", "NavigationHelpersContext", "options", "LinkingContext", "linkTo", "useLinkTo", "onPress", "e", "<PERSON><PERSON><PERSON><PERSON>", "Platform", "OS", "defaultPrevented", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "button", "includes", "currentTarget", "target", "preventDefault", "dispatch", "Error", "getPathFromStateHelper", "getPathFromState", "href", "config", "accessibilityRole"], "sourceRoot": "../../src", "sources": ["useLinkProps.tsx"], "mappings": ";;;;;;AAAA;AASA;AACA;AAEA;AACA;AAA4C;AAAA;AAAA;AAO5C,MAAMA,kBAAkB,GACtBC,MAAyE,IACT;EAChE,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEC,KAAK,EAAE;IACjB,OAAOD,MAAM,CAACC,KAAK;EACrB;EAEA,IAAID,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEE,MAAM,EAAE;IAClB,OAAO;MACLC,MAAM,EAAE,CACN;QACEC,IAAI,EAAEJ,MAAM,CAACE,MAAM;QACnBF,MAAM,EAAEA,MAAM,CAACA,MAAM;QACrB;QACAC,KAAK,EAAED,MAAM,CAACE,MAAM,GAChBH,kBAAkB,CAChBC,MAAM,CAACA,MAAM,CAGd,GACDK;MACN,CAAC;IAEL,CAAC;EACH;EAEA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,YAAY,OAEA;EAAA,IAAlC;IAAEC,EAAE;IAAEC;EAAyB,CAAC;EAChC,MAAMC,IAAI,GAAGC,KAAK,CAACC,UAAU,CAACC,mCAA6B,CAAC;EAC5D,MAAMC,UAAU,GAAGH,KAAK,CAACC,UAAU,CAACG,8BAAwB,CAAC;EAC7D,MAAM;IAAEC;EAAQ,CAAC,GAAGL,KAAK,CAACC,UAAU,CAACK,uBAAc,CAAC;EACpD,MAAMC,MAAM,GAAG,IAAAC,kBAAS,GAAa;EAErC,MAAMC,OAAO,GACXC,CAA2E,IACxE;IAAA;IACH,IAAIC,YAAY,GAAG,KAAK;IAExB,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI,CAACH,CAAC,EAAE;MAC/BC,YAAY,GAAGD,CAAC,GAAG,CAACA,CAAC,CAACI,gBAAgB,GAAG,IAAI;IAC/C,CAAC,MAAM,IACL,CAACJ,CAAC,CAACI,gBAAgB;IAAI;IACvB;IACA,EAAEJ,CAAC,CAACK,OAAO,IAAIL,CAAC,CAACM,MAAM,IAAIN,CAAC,CAACO,OAAO,IAAIP,CAAC,CAACQ,QAAQ,CAAC;IAAI;IACvD;IACCR,CAAC,CAACS,MAAM,IAAI,IAAI,IAAIT,CAAC,CAACS,MAAM,KAAK,CAAC,CAAC;IAAI;IACxC;IACA,CAACxB,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAACyB,QAAQ,qBAACV,CAAC,CAACW,aAAa,qDAAf,iBAAiBC,MAAM,CAAC,CAAC;IAAA,EAChE;MACAZ,CAAC,CAACa,cAAc,EAAE;MAClBZ,YAAY,GAAG,IAAI;IACrB;IAEA,IAAIA,YAAY,EAAE;MAChB,IAAIb,MAAM,EAAE;QACV,IAAIK,UAAU,EAAE;UACdA,UAAU,CAACqB,QAAQ,CAAC1B,MAAM,CAAC;QAC7B,CAAC,MAAM,IAAIC,IAAI,EAAE;UACfA,IAAI,CAACyB,QAAQ,CAAC1B,MAAM,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAI2B,KAAK,CACb,kFAAkF,CACnF;QACH;MACF,CAAC,MAAM;QACLlB,MAAM,CAACV,EAAE,CAAC;MACZ;IACF;EACF,CAAC;EAED,MAAM6B,sBAAsB,GAAG,CAAArB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,gBAAgB,KAAIA,sBAAgB;EAE5E,MAAMC,IAAI,GACR,OAAO/B,EAAE,KAAK,QAAQ,GAClBA,EAAE,GACF6B,sBAAsB,CACpB;IACEjC,MAAM,EAAE,CACN;MACEC,IAAI,EAAEG,EAAE,CAACL,MAAM;MACf;MACAF,MAAM,EAAEO,EAAE,CAACP,MAAM;MACjB;MACAC,KAAK,EAAEF,kBAAkB,CAACQ,EAAE,CAACP,MAAM;IACrC,CAAC;EAEL,CAAC,EACDe,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,MAAM,CAChB;EAEP,OAAO;IACLD,IAAI;IACJE,iBAAiB,EAAE,MAAe;IAClCrB;EACF,CAAC;AACH"}