{"version": 3, "sources": ["../../../../src/start/server/webTemplate.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, getNameFromConfig } from '@expo/config';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { TEMPLATES } from '../../customize/templates';\nimport { appendLinkToHtml, appendScriptsToHtml } from '../../export/html';\nimport { env } from '../../utils/env';\n\n/**\n * Create a static HTML for SPA styled websites.\n * This method attempts to reuse the same patterns as `@expo/webpack-config`.\n */\nexport async function createTemplateHtmlFromExpoConfigAsync(\n  projectRoot: string,\n  {\n    scripts,\n    cssLinks,\n    exp = getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp,\n  }: {\n    scripts: string[];\n    cssLinks?: string[];\n    exp?: ExpoConfig;\n  }\n) {\n  return createTemplateHtmlAsync(projectRoot, {\n    langIsoCode: exp.web?.lang ?? 'en',\n    scripts,\n    cssLinks,\n    title: getNameFromConfig(exp).webName ?? 'Expo App',\n    description: exp.web?.description,\n    themeColor: exp.web?.themeColor,\n  });\n}\n\nfunction getFileFromLocalPublicFolder(\n  projectRoot: string,\n  { publicFolder, filePath }: { publicFolder: string; filePath: string }\n): string | null {\n  const localFilePath = path.resolve(projectRoot, publicFolder, filePath);\n  if (!fs.existsSync(localFilePath)) {\n    return null;\n  }\n  return localFilePath;\n}\n\n/** Attempt to read the `index.html` from the local project before falling back on the template `index.html`. */\nasync function getTemplateIndexHtmlAsync(projectRoot: string): Promise<string> {\n  let filePath = getFileFromLocalPublicFolder(projectRoot, {\n    // TODO: Maybe use the app.json override.\n    publicFolder: env.EXPO_PUBLIC_FOLDER,\n    filePath: 'index.html',\n  });\n  if (!filePath) {\n    filePath = TEMPLATES.find((value) => value.id === 'index.html')!.file(projectRoot);\n  }\n  return fs.promises.readFile(filePath, 'utf8');\n}\n\n/** Return an `index.html` string with template values added. */\nexport async function createTemplateHtmlAsync(\n  projectRoot: string,\n  {\n    scripts,\n    cssLinks,\n    description,\n    langIsoCode,\n    title,\n    themeColor,\n  }: {\n    scripts: string[];\n    cssLinks?: string[];\n    description?: string;\n    langIsoCode: string;\n    title: string;\n    themeColor?: string;\n  }\n): Promise<string> {\n  // Resolve the best possible index.html template file.\n  let contents = await getTemplateIndexHtmlAsync(projectRoot);\n\n  contents = contents.replace('%LANG_ISO_CODE%', langIsoCode);\n  contents = contents.replace('%WEB_TITLE%', title);\n\n  contents = appendScriptsToHtml(contents, scripts);\n\n  if (cssLinks) {\n    contents = appendLinkToHtml(\n      contents,\n      cssLinks\n        .map((href) => [\n          // NOTE: We probably don't have to preload the CSS files for SPA-styled websites.\n          {\n            as: 'style',\n            rel: 'preload',\n            href,\n          },\n          {\n            rel: 'stylesheet',\n            href,\n          },\n        ])\n        .flat()\n    );\n  }\n\n  if (themeColor) {\n    contents = addMeta(contents, `name=\"theme-color\" content=\"${themeColor}\"`);\n  }\n\n  if (description) {\n    contents = addMeta(contents, `name=\"description\" content=\"${description}\"`);\n  }\n\n  return contents;\n}\n\n/** Add a `<meta />` tag to the `<head />` element. */\nfunction addMeta(contents: string, meta: string): string {\n  return contents.replace('</head>', `<meta ${meta}>\\n</head>`);\n}\n"], "names": ["createTemplateHtmlAsync", "createTemplateHtmlFromExpoConfigAsync", "projectRoot", "scripts", "cssLinks", "exp", "getConfig", "skipSDKVersionRequirement", "langIsoCode", "web", "lang", "title", "getNameFromConfig", "webName", "description", "themeColor", "getFileFromLocalPublicFolder", "publicFolder", "filePath", "localFilePath", "path", "resolve", "fs", "existsSync", "getTemplateIndexHtmlAsync", "env", "EXPO_PUBLIC_FOLDER", "TEMPLATES", "find", "value", "id", "file", "promises", "readFile", "contents", "replace", "appendScriptsToHtml", "appendLinkToHtml", "map", "href", "as", "rel", "flat", "addMeta", "meta"], "mappings": ";;;;;;;;;;;IA2DsBA,uBAAuB;eAAvBA;;IA/CAC,qCAAqC;eAArCA;;;;yBAZmC;;;;;;;gEAC1C;;;;;;;gEACE;;;;;;2BAES;sBAC4B;qBAClC;;;;;;AAMb,eAAeA,sCACpBC,WAAmB,EACnB,EACEC,OAAO,EACPC,QAAQ,EACRC,MAAMC,IAAAA,mBAAS,EAACJ,aAAa;IAAEK,2BAA2B;AAAK,GAAGF,GAAG,EAKtE;QAGcA,UAIAA,WACDA;IANd,OAAOL,wBAAwBE,aAAa;QAC1CM,aAAaH,EAAAA,WAAAA,IAAII,GAAG,qBAAPJ,SAASK,IAAI,KAAI;QAC9BP;QACAC;QACAO,OAAOC,IAAAA,2BAAiB,EAACP,KAAKQ,OAAO,IAAI;QACzCC,WAAW,GAAET,YAAAA,IAAII,GAAG,qBAAPJ,UAASS,WAAW;QACjCC,UAAU,GAAEV,YAAAA,IAAII,GAAG,qBAAPJ,UAASU,UAAU;IACjC;AACF;AAEA,SAASC,6BACPd,WAAmB,EACnB,EAAEe,YAAY,EAAEC,QAAQ,EAA8C;IAEtE,MAAMC,gBAAgBC,eAAI,CAACC,OAAO,CAACnB,aAAae,cAAcC;IAC9D,IAAI,CAACI,aAAE,CAACC,UAAU,CAACJ,gBAAgB;QACjC,OAAO;IACT;IACA,OAAOA;AACT;AAEA,8GAA8G,GAC9G,eAAeK,0BAA0BtB,WAAmB;IAC1D,IAAIgB,WAAWF,6BAA6Bd,aAAa;QACvD,yCAAyC;QACzCe,cAAcQ,QAAG,CAACC,kBAAkB;QACpCR,UAAU;IACZ;IACA,IAAI,CAACA,UAAU;QACbA,WAAWS,oBAAS,CAACC,IAAI,CAAC,CAACC,QAAUA,MAAMC,EAAE,KAAK,cAAeC,IAAI,CAAC7B;IACxE;IACA,OAAOoB,aAAE,CAACU,QAAQ,CAACC,QAAQ,CAACf,UAAU;AACxC;AAGO,eAAelB,wBACpBE,WAAmB,EACnB,EACEC,OAAO,EACPC,QAAQ,EACRU,WAAW,EACXN,WAAW,EACXG,KAAK,EACLI,UAAU,EAQX;IAED,sDAAsD;IACtD,IAAImB,WAAW,MAAMV,0BAA0BtB;IAE/CgC,WAAWA,SAASC,OAAO,CAAC,mBAAmB3B;IAC/C0B,WAAWA,SAASC,OAAO,CAAC,eAAexB;IAE3CuB,WAAWE,IAAAA,yBAAmB,EAACF,UAAU/B;IAEzC,IAAIC,UAAU;QACZ8B,WAAWG,IAAAA,sBAAgB,EACzBH,UACA9B,SACGkC,GAAG,CAAC,CAACC,OAAS;gBACb,iFAAiF;gBACjF;oBACEC,IAAI;oBACJC,KAAK;oBACLF;gBACF;gBACA;oBACEE,KAAK;oBACLF;gBACF;aACD,EACAG,IAAI;IAEX;IAEA,IAAI3B,YAAY;QACdmB,WAAWS,QAAQT,UAAU,CAAC,4BAA4B,EAAEnB,WAAW,CAAC,CAAC;IAC3E;IAEA,IAAID,aAAa;QACfoB,WAAWS,QAAQT,UAAU,CAAC,4BAA4B,EAAEpB,YAAY,CAAC,CAAC;IAC5E;IAEA,OAAOoB;AACT;AAEA,oDAAoD,GACpD,SAASS,QAAQT,QAAgB,EAAEU,IAAY;IAC7C,OAAOV,SAASC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAES,KAAK,UAAU,CAAC;AAC9D"}