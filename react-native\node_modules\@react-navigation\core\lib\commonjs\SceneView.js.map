{"version": 3, "names": ["SceneView", "screen", "route", "navigation", "routeState", "getState", "setState", "options", "clearOptions", "navigator<PERSON><PERSON><PERSON><PERSON>", "React", "useRef", "<PERSON><PERSON><PERSON>", "useCallback", "current", "addOptionsGetter", "useOptionsGetters", "key", "<PERSON><PERSON><PERSON>", "getCurrentState", "state", "currentRoute", "routes", "find", "r", "undefined", "setCurrentState", "child", "map", "isInitialRef", "useEffect", "getIsInitial", "context", "useMemo", "ScreenComponent", "getComponent", "component", "name", "children"], "sourceRoot": "../../src", "sources": ["SceneView.tsx"], "mappings": ";;;;;;AAMA;AAEA;AACA;AACA;AAEA;AAAoD;AAAA;AAAA;AAmBpD;AACA;AACA;AACA;AACe,SAASA,SAAS,OAYD;EAAA,IAT9B;IACAC,MAAM;IACNC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC;EAC2B,CAAC;EAC5B,MAAMC,eAAe,GAAGC,KAAK,CAACC,MAAM,EAAsB;EAC1D,MAAMC,MAAM,GAAGF,KAAK,CAACG,WAAW,CAAC,MAAMJ,eAAe,CAACK,OAAO,EAAE,EAAE,CAAC;EAEnE,MAAM;IAAEC;EAAiB,CAAC,GAAG,IAAAC,0BAAiB,EAAC;IAC7CC,GAAG,EAAEf,KAAK,CAACe,GAAG;IACdV,OAAO;IACPJ;EACF,CAAC,CAAC;EAEF,MAAMe,MAAM,GAAGR,KAAK,CAACG,WAAW,CAAEI,GAAW,IAAK;IAChDR,eAAe,CAACK,OAAO,GAAGG,GAAG;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAGT,KAAK,CAACG,WAAW,CAAC,MAAM;IAC9C,MAAMO,KAAK,GAAGf,QAAQ,EAAE;IACxB,MAAMgB,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAKf,KAAK,CAACe,GAAG,CAAC;IAElE,OAAOI,YAAY,GAAGA,YAAY,CAACD,KAAK,GAAGK,SAAS;EACtD,CAAC,EAAE,CAACpB,QAAQ,EAAEH,KAAK,CAACe,GAAG,CAAC,CAAC;EAEzB,MAAMS,eAAe,GAAGhB,KAAK,CAACG,WAAW,CACtCc,KAAkE,IAAK;IACtE,MAAMP,KAAK,GAAGf,QAAQ,EAAE;IAExBC,QAAQ,CAAC;MACP,GAAGc,KAAK;MACRE,MAAM,EAAEF,KAAK,CAACE,MAAM,CAACM,GAAG,CAAEJ,CAAC,IACzBA,CAAC,CAACP,GAAG,KAAKf,KAAK,CAACe,GAAG,GAAG;QAAE,GAAGO,CAAC;QAAEJ,KAAK,EAAEO;MAAM,CAAC,GAAGH,CAAC;IAEpD,CAAC,CAAC;EACJ,CAAC,EACD,CAACnB,QAAQ,EAAEH,KAAK,CAACe,GAAG,EAAEX,QAAQ,CAAC,CAChC;EAED,MAAMuB,YAAY,GAAGnB,KAAK,CAACC,MAAM,CAAC,IAAI,CAAC;EAEvCD,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpBD,YAAY,CAACf,OAAO,GAAG,KAAK;EAC9B,CAAC,CAAC;;EAEF;EACAJ,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,OAAOtB,YAAY;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuB,YAAY,GAAGrB,KAAK,CAACG,WAAW,CAAC,MAAMgB,YAAY,CAACf,OAAO,EAAE,EAAE,CAAC;EAEtE,MAAMkB,OAAO,GAAGtB,KAAK,CAACuB,OAAO,CAC3B,OAAO;IACLb,KAAK,EAAEhB,UAAU;IACjBC,QAAQ,EAAEc,eAAe;IACzBb,QAAQ,EAAEoB,eAAe;IACzBd,MAAM;IACNM,MAAM;IACNa,YAAY;IACZhB;EACF,CAAC,CAAC,EACF,CACEX,UAAU,EACVe,eAAe,EACfO,eAAe,EACfd,MAAM,EACNM,MAAM,EACNa,YAAY,EACZhB,gBAAgB,CACjB,CACF;EAED,MAAMmB,eAAe,GAAGjC,MAAM,CAACkC,YAAY,GACvClC,MAAM,CAACkC,YAAY,EAAE,GACrBlC,MAAM,CAACmC,SAAS;EAEpB,oBACE,oBAAC,+BAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEJ;EAAQ,gBAC9C,oBAAC,8BAAqB,qBACpB,oBAAC,wBAAe;IACd,IAAI,EAAE/B,MAAM,CAACoC,IAAK;IAClB,MAAM,EAAEH,eAAe,IAAIjC,MAAM,CAACqC,QAAS;IAC3C,UAAU,EAAEnC,UAAW;IACvB,KAAK,EAAED;EAAM,GAEZgC,eAAe,KAAKT,SAAS,gBAC5B,oBAAC,eAAe;IAAC,UAAU,EAAEtB,UAAW;IAAC,KAAK,EAAED;EAAM,EAAG,GACvDD,MAAM,CAACqC,QAAQ,KAAKb,SAAS,GAC/BxB,MAAM,CAACqC,QAAQ,CAAC;IAAEnC,UAAU;IAAED;EAAM,CAAC,CAAC,GACpC,IAAI,CACQ,CACI,CACQ;AAEtC"}