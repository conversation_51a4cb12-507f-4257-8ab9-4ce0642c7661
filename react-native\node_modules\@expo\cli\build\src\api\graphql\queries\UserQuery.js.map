{"version": 3, "sources": ["../../../../../src/api/graphql/queries/UserQuery.ts"], "sourcesContent": ["import { gql } from '@urql/core';\n\nimport { CurrentUserQuery } from '../../../graphql/generated';\nimport { graphqlClient, withErrorHandlingAsync } from '../client';\n\nexport const UserQuery = {\n  async currentUserAsync(): Promise<CurrentUserQuery['meActor']> {\n    const data = await withErrorHandlingAsync(\n      graphqlClient\n        .query<CurrentUserQuery>(\n          gql`\n            query CurrentUser {\n              meActor {\n                __typename\n                id\n                ... on UserActor {\n                  primaryAccount {\n                    id\n                  }\n                  username\n                }\n                ... on Robot {\n                  firstName\n                }\n                accounts {\n                  id\n                  users {\n                    actor {\n                      id\n                    }\n                    permissions\n                  }\n                }\n              }\n            }\n          `,\n          /* variables */ undefined,\n          {\n            additionalTypenames: ['User', 'SSOUser'],\n          }\n        )\n        .toPromise()\n    );\n\n    return data.meActor;\n  },\n};\n"], "names": ["UserQuery", "currentUserAsync", "data", "withErrorHandlingAsync", "graphqlClient", "query", "gql", "undefined", "additionalTypenames", "to<PERSON>romise", "meActor"], "mappings": ";;;;+BAKaA;;;eAAAA;;;;yBALO;;;;;;wBAGkC;AAE/C,MAAMA,YAAY;IACvB,MAAMC;QACJ,MAAMC,OAAO,MAAMC,IAAAA,8BAAsB,EACvCC,qBAAa,CACVC,KAAK,CACJC,IAAAA,WAAG,CAAA,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;UAyBJ,CAAC,EACD,aAAa,GAAGC,WAChB;YACEC,qBAAqB;gBAAC;gBAAQ;aAAU;QAC1C,GAEDC,SAAS;QAGd,OAAOP,KAAKQ,OAAO;IACrB;AACF"}