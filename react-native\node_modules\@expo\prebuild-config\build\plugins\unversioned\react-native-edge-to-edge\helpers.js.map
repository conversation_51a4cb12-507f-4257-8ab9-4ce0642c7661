{"version": 3, "file": "helpers.js", "names": ["_resolveFrom", "data", "_interopRequireDefault", "require", "e", "__esModule", "default", "edgeToEdgePluginIndex", "config", "isEdgeToEdgePluginArray", "plugin", "Array", "isArray", "includes", "isEdgeToEdgePluginString", "pluginIndex", "plugins", "findIndex", "hasEnabledEdgeToEdge", "android", "edgeToEdgeEnabled", "loadEdgeToEdgeConfigPlugin", "projectRoot", "edgeToEdgePath", "resolveFrom", "silent", "expoPackageRoot"], "sources": ["../../../../src/plugins/unversioned/react-native-edge-to-edge/helpers.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\nimport resolveFrom from 'resolve-from';\n\nimport { EdgeToEdgePlugin } from './withEdgeToEdge';\n\nexport function edgeToEdgePluginIndex(config: ExpoConfig): number | null {\n  const isEdgeToEdgePluginArray = (plugin: string | [] | [string] | [string, any]) =>\n    Array.isArray(plugin) &&\n    typeof plugin[0] === 'string' &&\n    plugin[0].includes('react-native-edge-to-edge');\n  const isEdgeToEdgePluginString = (plugin: string | [] | [string] | [string, any]) =>\n    typeof plugin === 'string' && plugin.includes('react-native-edge-to-edge');\n\n  const pluginIndex =\n    config.plugins?.findIndex(\n      (plugin) => isEdgeToEdgePluginString(plugin) || isEdgeToEdgePluginArray(plugin)\n    ) ?? -1;\n\n  if (pluginIndex === -1) {\n    return null;\n  }\n  return pluginIndex;\n}\n\nexport function hasEnabledEdgeToEdge(config: ExpoConfig) {\n  return config.android?.edgeToEdgeEnabled === true || edgeToEdgePluginIndex(config) != null;\n}\n\nexport function loadEdgeToEdgeConfigPlugin(projectRoot: string): EdgeToEdgePlugin | null {\n  try {\n    let edgeToEdgePath = resolveFrom.silent(projectRoot, 'react-native-edge-to-edge/app.plugin');\n    if (edgeToEdgePath == null) {\n      const expoPackageRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n      edgeToEdgePath = resolveFrom.silent(\n        expoPackageRoot ?? projectRoot,\n        'react-native-edge-to-edge/app.plugin'\n      );\n    }\n\n    if (edgeToEdgePath) {\n      const { default: plugin } = require(edgeToEdgePath);\n      return plugin as EdgeToEdgePlugin;\n    }\n  } catch {\n    return null;\n  }\n  return null;\n}\n"], "mappings": ";;;;;;;;AACA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAC,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAIhC,SAASG,qBAAqBA,CAACC,MAAkB,EAAiB;EACvE,MAAMC,uBAAuB,GAAIC,MAA8C,IAC7EC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IACrB,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC7BA,MAAM,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,2BAA2B,CAAC;EACjD,MAAMC,wBAAwB,GAAIJ,MAA8C,IAC9E,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACG,QAAQ,CAAC,2BAA2B,CAAC;EAE5E,MAAME,WAAW,GACfP,MAAM,CAACQ,OAAO,EAAEC,SAAS,CACtBP,MAAM,IAAKI,wBAAwB,CAACJ,MAAM,CAAC,IAAID,uBAAuB,CAACC,MAAM,CAChF,CAAC,IAAI,CAAC,CAAC;EAET,IAAIK,WAAW,KAAK,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,OAAOA,WAAW;AACpB;AAEO,SAASG,oBAAoBA,CAACV,MAAkB,EAAE;EACvD,OAAOA,MAAM,CAACW,OAAO,EAAEC,iBAAiB,KAAK,IAAI,IAAIb,qBAAqB,CAACC,MAAM,CAAC,IAAI,IAAI;AAC5F;AAEO,SAASa,0BAA0BA,CAACC,WAAmB,EAA2B;EACvF,IAAI;IACF,IAAIC,cAAc,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,sCAAsC,CAAC;IAC5F,IAAIC,cAAc,IAAI,IAAI,EAAE;MAC1B,MAAMG,eAAe,GAAGF,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,mBAAmB,CAAC;MAC5EC,cAAc,GAAGC,sBAAW,CAACC,MAAM,CACjCC,eAAe,IAAIJ,WAAW,EAC9B,sCACF,CAAC;IACH;IAEA,IAAIC,cAAc,EAAE;MAClB,MAAM;QAAEjB,OAAO,EAAEI;MAAO,CAAC,GAAGP,OAAO,CAACoB,cAAc,CAAC;MACnD,OAAOb,MAAM;IACf;EACF,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;EACA,OAAO,IAAI;AACb", "ignoreList": []}