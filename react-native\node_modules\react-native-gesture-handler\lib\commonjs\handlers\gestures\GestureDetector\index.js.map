{"version": 3, "sources": ["index.tsx"], "names": ["propagateDetectorConfig", "props", "gesture", "keysToPropagate", "key", "value", "undefined", "g", "toGestureArray", "config", "GestureDetector", "rootViewContext", "GestureHandlerRootViewContext", "__DEV__", "Platform", "OS", "Error", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "shouldUseReanimated", "some", "webEventHandlersRef", "state", "firstRender", "viewRef", "previousViewTag", "forceRebuildReanimatedEvent", "current", "preparedGesture", "React", "useRef", "attachedGestures", "animatedEventHandler", "animatedHandlers", "isMounted", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "needsToRebuildReanimatedEvent", "viewTag", "children"], "mappings": ";;;;;;;AACA;;AAOA;;AAIA;;AAEA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;AAvBA;AAyBA,SAASA,uBAAT,CACEC,KADF,EAEEC,OAFF,EAGE;AACA,QAAMC,eAA+C,GAAG,CACtD,YADsD,EAEtD,mBAFsD,EAGtD,aAHsD,CAAxD;;AAMA,OAAK,MAAMC,GAAX,IAAkBD,eAAlB,EAAmC;AACjC,UAAME,KAAK,GAAGJ,KAAK,CAACG,GAAD,CAAnB;;AACA,QAAIC,KAAK,KAAKC,SAAd,EAAyB;AACvB;AACD;;AAED,SAAK,MAAMC,CAAX,IAAgBL,OAAO,CAACM,cAAR,EAAhB,EAA0C;AACxC,YAAMC,MAAM,GAAGF,CAAC,CAACE,MAAjB;AACAA,MAAAA,MAAM,CAACL,GAAD,CAAN,GAAcC,KAAd;AACD;AACF;AACF;;AA+BD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMK,eAAe,GAAIT,KAAD,IAAiC;AAC9D,QAAMU,eAAe,GAAG,uBAAWC,sCAAX,CAAxB;;AACA,MAAIC,OAAO,IAAI,CAACF,eAAZ,IAA+B,CAAC,uBAAhC,IAA+CG,sBAASC,EAAT,KAAgB,KAAnE,EAA0E;AACxE,UAAM,IAAIC,KAAJ,CACJ,wNADI,CAAN;AAGD,GAN6D,CAQ9D;;;AACA,QAAMC,aAAa,GAAGhB,KAAK,CAACC,OAA5B;AACAF,EAAAA,uBAAuB,CAACC,KAAD,EAAQgB,aAAR,CAAvB;AAEA,QAAMC,gBAAgB,GAAG,oBACvB,MAAMD,aAAa,CAACT,cAAd,EADiB,EAEvB,CAACS,aAAD,CAFuB,CAAzB;AAIA,QAAME,mBAAmB,GAAGD,gBAAgB,CAACE,IAAjB,CACzBb,CAAD,IAAOA,CAAC,CAACY,mBADiB,CAA5B;AAIA,QAAME,mBAAmB,GAAG,kCAA5B,CApB8D,CAqB9D;;AACA,QAAMC,KAAK,GAAG,mBAA6B;AACzCC,IAAAA,WAAW,EAAE,IAD4B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCC,IAAAA,eAAe,EAAE,CAAC,CAHuB;AAIzCC,IAAAA,2BAA2B,EAAE;AAJY,GAA7B,EAKXC,OALH;;AAOA,QAAMC,eAAe,GAAGC,eAAMC,MAAN,CAAmC;AACzDC,IAAAA,gBAAgB,EAAE,EADuC;AAEzDC,IAAAA,oBAAoB,EAAE,IAFmC;AAGzDC,IAAAA,gBAAgB,EAAE,IAHuC;AAIzDd,IAAAA,mBAAmB,EAAEA,mBAJoC;AAKzDe,IAAAA,SAAS,EAAE;AAL8C,GAAnC,EAMrBP,OANH;;AAQA,QAAMQ,sBAAsB,GAAG,4CAC7Bb,KAD6B,EAE7BM,eAF6B,EAG7BV,gBAH6B,EAI7BD,aAJ6B,EAK7BI,mBAL6B,CAA/B;AAQA,QAAMe,UAAU,GAAG,0CAAkBd,KAAlB,EAAyBa,sBAAzB,CAAnB,CA7C8D,CA+C9D;AACA;;AACA,QAAME,6BAA6B,GACjCf,KAAK,CAACC,WAAN,IACAD,KAAK,CAACI,2BADN,IAEA,sCAAgBE,eAAhB,EAAiCV,gBAAjC,CAHF;AAIAI,EAAAA,KAAK,CAACI,2BAAN,GAAoC,KAApC;AAEA,8CAAmBE,eAAnB,EAAoCS,6BAApC;AAEA,8BAAgB,MAAM;AACpB,UAAMC,OAAO,GAAG,iCAAehB,KAAK,CAACE,OAArB,CAAhB;AACAI,IAAAA,eAAe,CAACM,SAAhB,GAA4B,IAA5B;AAEA,wCAAe;AACbN,MAAAA,eADa;AAEbX,MAAAA,aAFa;AAGbC,MAAAA,gBAHa;AAIbG,MAAAA,mBAJa;AAKbiB,MAAAA;AALa,KAAf;AAQA,WAAO,MAAM;AACXV,MAAAA,eAAe,CAACM,SAAhB,GAA4B,KAA5B;AACA,sCAAaN,eAAb;AACD,KAHD;AAID,GAhBD,EAgBG,EAhBH;AAkBA,wBAAU,MAAM;AACd,QAAIN,KAAK,CAACC,WAAV,EAAuB;AACrBD,MAAAA,KAAK,CAACC,WAAN,GAAoB,KAApB;AACD,KAFD,MAEO;AACLY,MAAAA,sBAAsB;AACvB;AACF,GAND,EAMG,CAAClC,KAAD,CANH;;AAQA,MAAIkB,mBAAJ,EAAyB;AACvB,wBACE,6BAAC,kBAAD;AACE,MAAA,GAAG,EAAEiB,UADP;AAEE,MAAA,qBAAqB,EAAER,eAAe,CAACI;AAFzC,OAGG/B,KAAK,CAACsC,QAHT,CADF;AAOD,GARD,MAQO;AACL,wBAAO,6BAAC,UAAD;AAAM,MAAA,GAAG,EAAEH;AAAX,OAAwBnC,KAAK,CAACsC,QAA9B,CAAP;AACD;AACF,CA9FM", "sourcesContent": ["/* eslint-disable react/no-unused-prop-types */\nimport React, {\n  useContext,\n  useEffect,\n  useLayoutEffect,\n  useMemo,\n  useRef,\n} from 'react';\nimport { Platform, findNodeHandle } from 'react-native';\nimport { GestureType } from '../gesture';\nimport { UserSelect, TouchAction } from '../../gestureHandlerCommon';\nimport { ComposedGesture } from '../gestureComposition';\nimport { isJestEnv } from '../../../utils';\n\nimport GestureHandlerRootViewContext from '../../../GestureHandlerRootViewContext';\nimport { AttachedGestureState, GestureDetectorState } from './types';\nimport { useAnimatedGesture } from './useAnimatedGesture';\nimport { attachHandlers } from './attachHandlers';\nimport { needsToReattach } from './needsToReattach';\nimport { dropHandlers } from './dropHandlers';\nimport { useWebEventHandlers } from './utils';\nimport { Wrap, AnimatedWrap } from './Wrap';\nimport { useDetectorUpdater } from './useDetectorUpdater';\nimport { useViewRefHandler } from './useViewRefHandler';\n\nfunction propagateDetectorConfig(\n  props: GestureDetectorProps,\n  gesture: ComposedGesture | GestureType\n) {\n  const keysToPropagate: (keyof GestureDetectorProps)[] = [\n    'userSelect',\n    'enableContextMenu',\n    'touchAction',\n  ];\n\n  for (const key of keysToPropagate) {\n    const value = props[key];\n    if (value === undefined) {\n      continue;\n    }\n\n    for (const g of gesture.toGestureArray()) {\n      const config = g.config as { [key: string]: unknown };\n      config[key] = value;\n    }\n  }\n}\n\ninterface GestureDetectorProps {\n  children?: React.ReactNode;\n  /**\n   * A gesture object containing the configuration and callbacks.\n   * Can be any of:\n   * - base gestures (`Tap`, `Pan`, ...)\n   * - `ComposedGesture` (`Race`, `Simultaneous`, `Exclusive`)\n   */\n  gesture: ComposedGesture | GestureType;\n  /**\n   * #### Web only\n   * This parameter allows to specify which `userSelect` property should be applied to underlying view.\n   * Possible values are `\"none\" | \"auto\" | \"text\"`. Default value is set to `\"none\"`.\n   */\n  userSelect?: UserSelect;\n  /**\n   * #### Web only\n   * Specifies whether context menu should be enabled after clicking on underlying view with right mouse button.\n   * Default value is set to `false`.\n   */\n  enableContextMenu?: boolean;\n  /**\n   * #### Web only\n   * This parameter allows to specify which `touchAction` property should be applied to underlying view.\n   * Supports all CSS touch-action values (e.g. `\"none\"`, `\"pan-y\"`). Default value is set to `\"none\"`.\n   */\n  touchAction?: TouchAction;\n}\n\n/**\n * `GestureDetector` is responsible for creating and updating native gesture handlers based on the config of provided gesture.\n *\n * ### Props\n * - `gesture`\n * - `userSelect` (**Web only**)\n * - `enableContextMenu` (**Web only**)\n * - `touchAction` (**Web only**)\n *\n * ### Remarks\n * - Gesture Detector will use first native view in its subtree to recognize gestures, however if this view is used only to group its children it may get automatically collapsed.\n * - Using the same instance of a gesture across multiple Gesture Detectors is not possible.\n *\n * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture-detector\n */\nexport const GestureDetector = (props: GestureDetectorProps) => {\n  const rootViewContext = useContext(GestureHandlerRootViewContext);\n  if (__DEV__ && !rootViewContext && !isJestEnv() && Platform.OS !== 'web') {\n    throw new Error(\n      'GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n    );\n  }\n\n  // Gesture config should be wrapped with useMemo to prevent unnecessary re-renders\n  const gestureConfig = props.gesture;\n  propagateDetectorConfig(props, gestureConfig);\n\n  const gesturesToAttach = useMemo(\n    () => gestureConfig.toGestureArray(),\n    [gestureConfig]\n  );\n  const shouldUseReanimated = gesturesToAttach.some(\n    (g) => g.shouldUseReanimated\n  );\n\n  const webEventHandlersRef = useWebEventHandlers();\n  // Store state in ref to prevent unnecessary renders\n  const state = useRef<GestureDetectorState>({\n    firstRender: true,\n    viewRef: null,\n    previousViewTag: -1,\n    forceRebuildReanimatedEvent: false,\n  }).current;\n\n  const preparedGesture = React.useRef<AttachedGestureState>({\n    attachedGestures: [],\n    animatedEventHandler: null,\n    animatedHandlers: null,\n    shouldUseReanimated: shouldUseReanimated,\n    isMounted: false,\n  }).current;\n\n  const updateAttachedGestures = useDetectorUpdater(\n    state,\n    preparedGesture,\n    gesturesToAttach,\n    gestureConfig,\n    webEventHandlersRef\n  );\n\n  const refHandler = useViewRefHandler(state, updateAttachedGestures);\n\n  // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n  // config update will be enough as all necessary items are stored in shared values anyway\n  const needsToRebuildReanimatedEvent =\n    state.firstRender ||\n    state.forceRebuildReanimatedEvent ||\n    needsToReattach(preparedGesture, gesturesToAttach);\n  state.forceRebuildReanimatedEvent = false;\n\n  useAnimatedGesture(preparedGesture, needsToRebuildReanimatedEvent);\n\n  useLayoutEffect(() => {\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    preparedGesture.isMounted = true;\n\n    attachHandlers({\n      preparedGesture,\n      gestureConfig,\n      gesturesToAttach,\n      webEventHandlersRef,\n      viewTag,\n    });\n\n    return () => {\n      preparedGesture.isMounted = false;\n      dropHandlers(preparedGesture);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (state.firstRender) {\n      state.firstRender = false;\n    } else {\n      updateAttachedGestures();\n    }\n  }, [props]);\n\n  if (shouldUseReanimated) {\n    return (\n      <AnimatedWrap\n        ref={refHandler}\n        onGestureHandlerEvent={preparedGesture.animatedEventHandler}>\n        {props.children}\n      </AnimatedWrap>\n    );\n  } else {\n    return <Wrap ref={refHandler}>{props.children}</Wrap>;\n  }\n};\n"]}