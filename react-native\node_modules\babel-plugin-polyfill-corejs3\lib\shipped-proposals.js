"use strict";

exports.__esModule = true;
exports.default = void 0;
// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs
var _default = exports.default = new Set(["esnext.array.group", "esnext.array.group-to-map", "esnext.json.is-raw-json", "esnext.json.parse", "esnext.json.raw-json", "esnext.math.sum-precise", "esnext.symbol.metadata", "esnext.uint8-array.from-base64", "esnext.uint8-array.from-hex", "esnext.uint8-array.set-from-base64", "esnext.uint8-array.set-from-hex", "esnext.uint8-array.to-base64", "esnext.uint8-array.to-hex"]);