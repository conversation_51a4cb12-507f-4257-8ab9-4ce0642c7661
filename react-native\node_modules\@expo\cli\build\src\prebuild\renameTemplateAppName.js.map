{"version": 3, "sources": ["../../../src/prebuild/renameTemplateAppName.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport fs from 'fs';\nimport { glob } from 'glob';\nimport path from 'path';\n\nimport { ExtractProps } from '../utils/npm';\n\nconst debug = require('debug')('expo:prebuild:copyTemplateFiles') as typeof console.log;\n\nfunction escapeXMLCharacters(original: string): string {\n  const noAmps = original.replace('&', '&amp;');\n  const noLt = noAmps.replace('<', '&lt;');\n  const noGt = noLt.replace('>', '&gt;');\n  const noApos = noGt.replace('\"', '\\\\\"');\n  return noApos.replace(\"'\", \"\\\\'\");\n}\n\n/**\n * # Background\n *\n * `@expo/cli` and `create-expo` extract a template from a tarball (whether from\n * a local npm project or a GitHub repository), but these templates have a\n * static name that needs to be updated to match whatever app name the user\n * specified.\n *\n * By convention, the app name of all templates is \"HelloWorld\". During\n * extraction, filepaths are transformed via `createEntryResolver()` in\n * `createFileTransform.ts`, but the contents of files are left untouched.\n * Technically, the contents used to be transformed during extraction as well,\n * but due to poor configurability, we've moved to a post-extraction approach.\n *\n * # The new approach: Renaming the app post-extraction\n *\n * In this new approach, we take a list of file patterns, otherwise known as the\n * \"rename config\" to determine explicitly which files – relative to the root of\n * the template – to perform find-and-replace on, to update the app name.\n *\n * ## The rename config\n *\n * The rename config can be passed directly as a string array to\n * `getTemplateFilesToRenameAsync()`.\n *\n * The file patterns are formatted as glob expressions to be interpreted by\n * [glob](https://github.com/isaacs/node-glob). Comments are supported with\n * the `#` symbol, both in the plain-text file and string array formats.\n * Whitespace is trimmed and whitespace-only lines are ignored.\n *\n * If no rename config has been passed directly to\n * `getTemplateFilesToRenameAsync()` then this default rename config will be\n * used instead.\n */\nexport const defaultRenameConfig = [\n  // Common\n  '!**/node_modules',\n  'app.json',\n\n  // Android\n  'android/**/*.gradle',\n  'android/app/BUCK',\n  'android/app/src/**/*.java',\n  'android/app/src/**/*.kt',\n  'android/app/src/**/*.xml',\n\n  // iOS\n  'ios/Podfile',\n  'ios/**/*.xcodeproj/project.pbxproj',\n  'ios/**/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme',\n  'ios/**/*.xcworkspace/contents.xcworkspacedata',\n\n  // macOS\n  'macos/Podfile',\n  'macos/**/*.xcodeproj/project.pbxproj',\n  'macos/**/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme',\n  'macos/**/*.xcworkspace/contents.xcworkspacedata',\n] as const;\n\n/**\n * Returns a list of files within a template matched by the resolved rename\n * config.\n *\n * The rename config is resolved in the order of preference:\n * Config provided as function param > defaultRenameConfig\n */\nexport async function getTemplateFilesToRenameAsync({\n  cwd,\n  /**\n   * An array of patterns following the rename config format. If omitted, then\n   * we fall back to defaultRenameConfig.\n   * @see defaultRenameConfig\n   */\n  renameConfig: userConfig,\n}: Pick<ExtractProps, 'cwd'> & { renameConfig?: string[] }) {\n  let config = userConfig ?? defaultRenameConfig;\n\n  // Strip comments, trim whitespace, and remove empty lines.\n  config = config.map((line) => line.split(/(?<!\\\\)#/, 2)[0].trim()).filter((line) => line !== '');\n\n  return await glob(config, {\n    cwd,\n    // `true` is consistent with .gitignore. Allows `*.xml` to match .xml files\n    // in all subdirs.\n    matchBase: true,\n    dot: true,\n    // Prevent climbing out of the template directory in case a template\n    // includes a symlink to an external directory.\n    follow: false,\n    // Do not match on directories, only files\n    // Without this patterns like `android/**/*.gradle` actually match the folder `android/.gradle`\n    nodir: true,\n  });\n}\n\nexport async function renameTemplateAppNameAsync({\n  cwd,\n  name,\n  files,\n}: Pick<ExtractProps, 'cwd' | 'name'> & {\n  /**\n   * An array of files to transform. Usually provided by calling\n   * getTemplateFilesToRenameAsync().\n   * @see getTemplateFilesToRenameAsync\n   */\n  files: string[];\n}) {\n  debug(`Got files to transform: ${JSON.stringify(files)}`);\n\n  await Promise.all(\n    files.map(async (file) => {\n      const absoluteFilePath = path.resolve(cwd, file);\n\n      let contents: string;\n      try {\n        contents = await fs.promises.readFile(absoluteFilePath, { encoding: 'utf-8' });\n      } catch (error) {\n        throw new Error(\n          `Failed to read template file: \"${absoluteFilePath}\". Was it removed mid-operation?`,\n          { cause: error }\n        );\n      }\n\n      debug(`Renaming app name in file: ${absoluteFilePath}`);\n\n      const safeName = ['.xml', '.plist'].includes(path.extname(file))\n        ? escapeXMLCharacters(name)\n        : name;\n\n      try {\n        const replacement = contents\n          .replace(/Hello App Display Name/g, safeName)\n          .replace(/HelloWorld/g, IOSConfig.XcodeUtils.sanitizedName(safeName))\n          .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(safeName.toLowerCase()));\n\n        if (replacement === contents) {\n          return;\n        }\n\n        await fs.promises.writeFile(absoluteFilePath, replacement);\n      } catch (error) {\n        throw new Error(\n          `Failed to overwrite template file: \"${absoluteFilePath}\". Was it removed mid-operation?`,\n          { cause: error }\n        );\n      }\n    })\n  );\n}\n"], "names": ["defaultRenameConfig", "getTemplateFilesToRenameAsync", "renameTemplateAppNameAsync", "debug", "require", "escapeXMLCharacters", "original", "noAmps", "replace", "noLt", "noGt", "noApos", "cwd", "renameConfig", "userConfig", "config", "map", "line", "split", "trim", "filter", "glob", "matchBase", "dot", "follow", "nodir", "name", "files", "JSON", "stringify", "Promise", "all", "file", "absoluteFilePath", "path", "resolve", "contents", "fs", "promises", "readFile", "encoding", "error", "Error", "cause", "safeName", "includes", "extname", "replacement", "IOSConfig", "XcodeUtils", "sanitizedName", "toLowerCase", "writeFile"], "mappings": ";;;;;;;;;;;IAmDaA,mBAAmB;eAAnBA;;IAgCSC,6BAA6B;eAA7BA;;IA6BAC,0BAA0B;eAA1BA;;;;yBAhHI;;;;;;;gEACX;;;;;;;yBACM;;;;;;;gEACJ;;;;;;;;;;;AAIjB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,SAASC,oBAAoBC,QAAgB;IAC3C,MAAMC,SAASD,SAASE,OAAO,CAAC,KAAK;IACrC,MAAMC,OAAOF,OAAOC,OAAO,CAAC,KAAK;IACjC,MAAME,OAAOD,KAAKD,OAAO,CAAC,KAAK;IAC/B,MAAMG,SAASD,KAAKF,OAAO,CAAC,KAAK;IACjC,OAAOG,OAAOH,OAAO,CAAC,KAAK;AAC7B;AAoCO,MAAMR,sBAAsB;IACjC,SAAS;IACT;IACA;IAEA,UAAU;IACV;IACA;IACA;IACA;IACA;IAEA,MAAM;IACN;IACA;IACA;IACA;IAEA,QAAQ;IACR;IACA;IACA;IACA;CACD;AASM,eAAeC,8BAA8B,EAClDW,GAAG,EACH;;;;GAIC,GACDC,cAAcC,UAAU,EACgC;IACxD,IAAIC,SAASD,cAAcd;IAE3B,2DAA2D;IAC3De,SAASA,OAAOC,GAAG,CAAC,CAACC,OAASA,KAAKC,KAAK,CAAC,YAAY,EAAE,CAAC,EAAE,CAACC,IAAI,IAAIC,MAAM,CAAC,CAACH,OAASA,SAAS;IAE7F,OAAO,MAAMI,IAAAA,YAAI,EAACN,QAAQ;QACxBH;QACA,2EAA2E;QAC3E,kBAAkB;QAClBU,WAAW;QACXC,KAAK;QACL,oEAAoE;QACpE,+CAA+C;QAC/CC,QAAQ;QACR,0CAA0C;QAC1C,+FAA+F;QAC/FC,OAAO;IACT;AACF;AAEO,eAAevB,2BAA2B,EAC/CU,GAAG,EACHc,IAAI,EACJC,KAAK,EAQN;IACCxB,MAAM,CAAC,wBAAwB,EAAEyB,KAAKC,SAAS,CAACF,QAAQ;IAExD,MAAMG,QAAQC,GAAG,CACfJ,MAAMX,GAAG,CAAC,OAAOgB;QACf,MAAMC,mBAAmBC,eAAI,CAACC,OAAO,CAACvB,KAAKoB;QAE3C,IAAII;QACJ,IAAI;YACFA,WAAW,MAAMC,aAAE,CAACC,QAAQ,CAACC,QAAQ,CAACN,kBAAkB;gBAAEO,UAAU;YAAQ;QAC9E,EAAE,OAAOC,OAAO;YACd,MAAM,IAAIC,MACR,CAAC,+BAA+B,EAAET,iBAAiB,gCAAgC,CAAC,EACpF;gBAAEU,OAAOF;YAAM;QAEnB;QAEAtC,MAAM,CAAC,2BAA2B,EAAE8B,kBAAkB;QAEtD,MAAMW,WAAW;YAAC;YAAQ;SAAS,CAACC,QAAQ,CAACX,eAAI,CAACY,OAAO,CAACd,SACtD3B,oBAAoBqB,QACpBA;QAEJ,IAAI;YACF,MAAMqB,cAAcX,SACjB5B,OAAO,CAAC,2BAA2BoC,UACnCpC,OAAO,CAAC,eAAewC,0BAAS,CAACC,UAAU,CAACC,aAAa,CAACN,WAC1DpC,OAAO,CAAC,eAAewC,0BAAS,CAACC,UAAU,CAACC,aAAa,CAACN,SAASO,WAAW;YAEjF,IAAIJ,gBAAgBX,UAAU;gBAC5B;YACF;YAEA,MAAMC,aAAE,CAACC,QAAQ,CAACc,SAAS,CAACnB,kBAAkBc;QAChD,EAAE,OAAON,OAAO;YACd,MAAM,IAAIC,MACR,CAAC,oCAAoC,EAAET,iBAAiB,gCAAgC,CAAC,EACzF;gBAAEU,OAAOF;YAAM;QAEnB;IACF;AAEJ"}