{"version": 3, "sources": ["../../../../src/start/project/devices.ts"], "sourcesContent": ["import { createTemporaryProjectFile } from './dotExpo';\n\nconst debug = require('debug')('expo:start:project:devices') as typeof console.log;\n\nexport type DeviceInfo = {\n  installationId: string;\n  lastUsed: number;\n};\n\nexport type DevicesInfo = {\n  devices: DeviceInfo[];\n};\n\nconst DEVICES_FILE_NAME = 'devices.json';\n\nconst MILLISECONDS_IN_30_DAYS = 30 * 24 * 60 * 60 * 1000;\n\nexport const DevicesFile = createTemporaryProjectFile<DevicesInfo>(DEVICES_FILE_NAME, {\n  devices: [],\n});\n\nlet devicesInfo: DevicesInfo | null = null;\n\nexport async function getDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  if (devicesInfo) {\n    return devicesInfo;\n  }\n  return readDevicesInfoAsync(projectRoot);\n}\n\nexport async function readDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  try {\n    devicesInfo = await DevicesFile.readAsync(projectRoot);\n\n    // if the file on disk has old devices, filter them out here before we use them\n    const filteredDevices = filterOldDevices(devicesInfo.devices);\n    if (filteredDevices.length < devicesInfo.devices.length) {\n      devicesInfo = {\n        ...devicesInfo,\n        devices: filteredDevices,\n      };\n      // save the newly filtered list for consistency\n      try {\n        await setDevicesInfoAsync(projectRoot, devicesInfo);\n      } catch {\n        // do nothing here, we'll just keep using the filtered list in memory for now\n      }\n    }\n\n    return devicesInfo;\n  } catch {\n    return await DevicesFile.setAsync(projectRoot, { devices: [] });\n  }\n}\n\nexport async function setDevicesInfoAsync(\n  projectRoot: string,\n  json: DevicesInfo\n): Promise<DevicesInfo> {\n  devicesInfo = json;\n  return await DevicesFile.setAsync(projectRoot, json);\n}\n\nexport async function saveDevicesAsync(\n  projectRoot: string,\n  deviceIds: string | string[]\n): Promise<void> {\n  const currentTime = Date.now();\n  const newDeviceIds = typeof deviceIds === 'string' ? [deviceIds] : deviceIds;\n\n  debug(`Saving devices: ${newDeviceIds}`);\n  const { devices } = await getDevicesInfoAsync(projectRoot);\n  const newDevicesJson = devices\n    .filter((device) => !newDeviceIds.includes(device.installationId))\n    .concat(newDeviceIds.map((deviceId) => ({ installationId: deviceId, lastUsed: currentTime })));\n  await setDevicesInfoAsync(projectRoot, { devices: filterOldDevices(newDevicesJson) });\n}\n\nfunction filterOldDevices(devices: DeviceInfo[]) {\n  const currentTime = Date.now();\n  return (\n    devices\n      // filter out any devices that haven't been used to open this project in 30 days\n      .filter((device) => currentTime - device.lastUsed <= MILLISECONDS_IN_30_DAYS)\n      // keep only the 10 most recently used devices\n      .sort((a, b) => b.lastUsed - a.lastUsed)\n      .slice(0, 10)\n  );\n}\n"], "names": ["DevicesFile", "getDevicesInfoAsync", "readDevicesInfoAsync", "saveDevicesAsync", "setDevicesInfoAsync", "debug", "require", "DEVICES_FILE_NAME", "MILLISECONDS_IN_30_DAYS", "createTemporaryProjectFile", "devices", "devicesInfo", "projectRoot", "readAsync", "filteredDevices", "filterOldDevices", "length", "setAsync", "json", "deviceIds", "currentTime", "Date", "now", "newDeviceIds", "newDevicesJson", "filter", "device", "includes", "installationId", "concat", "map", "deviceId", "lastUsed", "sort", "a", "b", "slice"], "mappings": ";;;;;;;;;;;IAiBaA,WAAW;eAAXA;;IAMSC,mBAAmB;eAAnBA;;IAOAC,oBAAoB;eAApBA;;IAiCAC,gBAAgB;eAAhBA;;IARAC,mBAAmB;eAAnBA;;;yBAvDqB;AAE3C,MAAMC,QAAQC,QAAQ,SAAS;AAW/B,MAAMC,oBAAoB;AAE1B,MAAMC,0BAA0B,KAAK,KAAK,KAAK,KAAK;AAE7C,MAAMR,cAAcS,IAAAA,mCAA0B,EAAcF,mBAAmB;IACpFG,SAAS,EAAE;AACb;AAEA,IAAIC,cAAkC;AAE/B,eAAeV,oBAAoBW,WAAmB;IAC3D,IAAID,aAAa;QACf,OAAOA;IACT;IACA,OAAOT,qBAAqBU;AAC9B;AAEO,eAAeV,qBAAqBU,WAAmB;IAC5D,IAAI;QACFD,cAAc,MAAMX,YAAYa,SAAS,CAACD;QAE1C,+EAA+E;QAC/E,MAAME,kBAAkBC,iBAAiBJ,YAAYD,OAAO;QAC5D,IAAII,gBAAgBE,MAAM,GAAGL,YAAYD,OAAO,CAACM,MAAM,EAAE;YACvDL,cAAc;gBACZ,GAAGA,WAAW;gBACdD,SAASI;YACX;YACA,+CAA+C;YAC/C,IAAI;gBACF,MAAMV,oBAAoBQ,aAAaD;YACzC,EAAE,OAAM;YACN,6EAA6E;YAC/E;QACF;QAEA,OAAOA;IACT,EAAE,OAAM;QACN,OAAO,MAAMX,YAAYiB,QAAQ,CAACL,aAAa;YAAEF,SAAS,EAAE;QAAC;IAC/D;AACF;AAEO,eAAeN,oBACpBQ,WAAmB,EACnBM,IAAiB;IAEjBP,cAAcO;IACd,OAAO,MAAMlB,YAAYiB,QAAQ,CAACL,aAAaM;AACjD;AAEO,eAAef,iBACpBS,WAAmB,EACnBO,SAA4B;IAE5B,MAAMC,cAAcC,KAAKC,GAAG;IAC5B,MAAMC,eAAe,OAAOJ,cAAc,WAAW;QAACA;KAAU,GAAGA;IAEnEd,MAAM,CAAC,gBAAgB,EAAEkB,cAAc;IACvC,MAAM,EAAEb,OAAO,EAAE,GAAG,MAAMT,oBAAoBW;IAC9C,MAAMY,iBAAiBd,QACpBe,MAAM,CAAC,CAACC,SAAW,CAACH,aAAaI,QAAQ,CAACD,OAAOE,cAAc,GAC/DC,MAAM,CAACN,aAAaO,GAAG,CAAC,CAACC,WAAc,CAAA;YAAEH,gBAAgBG;YAAUC,UAAUZ;QAAY,CAAA;IAC5F,MAAMhB,oBAAoBQ,aAAa;QAAEF,SAASK,iBAAiBS;IAAgB;AACrF;AAEA,SAAST,iBAAiBL,OAAqB;IAC7C,MAAMU,cAAcC,KAAKC,GAAG;IAC5B,OACEZ,OACE,gFAAgF;KAC/Ee,MAAM,CAAC,CAACC,SAAWN,cAAcM,OAAOM,QAAQ,IAAIxB,wBACrD,8CAA8C;KAC7CyB,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEH,QAAQ,GAAGE,EAAEF,QAAQ,EACtCI,KAAK,CAAC,GAAG;AAEhB"}