{"version": 3, "sources": ["../../../src/utils/createFileTransform.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport path from 'path';\nimport picomatch from 'picomatch';\nimport { ReadEntry } from 'tar';\n\nconst debug = require('debug')('expo:file-transform') as typeof console.log;\n\nexport function createEntryResolver(name: string) {\n  return (entry: ReadEntry) => {\n    if (name) {\n      // Rewrite paths for bare workflow\n      entry.path = entry.path\n        .replace(\n          /HelloWorld/g,\n          entry.path.includes('android')\n            ? IOSConfig.XcodeUtils.sanitizedName(name.toLowerCase())\n            : IOSConfig.XcodeUtils.sanitizedName(name)\n        )\n        .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(name).toLowerCase());\n    }\n    if (entry.type && /^file$/i.test(entry.type) && path.basename(entry.path) === 'gitignore') {\n      // Rename `gitignore` because npm ignores files named `.gitignore` when publishing.\n      // See: https://github.com/npm/npm/issues/1862\n      entry.path = entry.path.replace(/gitignore$/, '.gitignore');\n    }\n  };\n}\n\nexport function createGlobFilter(\n  globPattern: picomatch.Glob,\n  options?: picomatch.PicomatchOptions\n) {\n  const matcher = picomatch(globPattern, options);\n\n  debug(\n    'filter: created for pattern(s) \"%s\" (%s)',\n    Array.isArray(globPattern) ? globPattern.join('\", \"') : globPattern,\n    options\n  );\n\n  return (path: string) => {\n    const included = matcher(path);\n    debug('filter: %s - %s', included ? 'include' : 'exclude', path);\n    return included;\n  };\n}\n"], "names": ["createEntryResolver", "createGlobFilter", "debug", "require", "name", "entry", "path", "replace", "includes", "IOSConfig", "XcodeUtils", "sanitizedName", "toLowerCase", "type", "test", "basename", "globPattern", "options", "matcher", "picomatch", "Array", "isArray", "join", "included"], "mappings": ";;;;;;;;;;;IAOgBA,mBAAmB;eAAnBA;;IAqBAC,gBAAgB;eAAhBA;;;;yBA5BU;;;;;;;gEACT;;;;;;;gEACK;;;;;;;;;;;AAGtB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASH,oBAAoBI,IAAY;IAC9C,OAAO,CAACC;QACN,IAAID,MAAM;YACR,kCAAkC;YAClCC,MAAMC,IAAI,GAAGD,MAAMC,IAAI,CACpBC,OAAO,CACN,eACAF,MAAMC,IAAI,CAACE,QAAQ,CAAC,aAChBC,0BAAS,CAACC,UAAU,CAACC,aAAa,CAACP,KAAKQ,WAAW,MACnDH,0BAAS,CAACC,UAAU,CAACC,aAAa,CAACP,OAExCG,OAAO,CAAC,eAAeE,0BAAS,CAACC,UAAU,CAACC,aAAa,CAACP,MAAMQ,WAAW;QAChF;QACA,IAAIP,MAAMQ,IAAI,IAAI,UAAUC,IAAI,CAACT,MAAMQ,IAAI,KAAKP,eAAI,CAACS,QAAQ,CAACV,MAAMC,IAAI,MAAM,aAAa;YACzF,mFAAmF;YACnF,8CAA8C;YAC9CD,MAAMC,IAAI,GAAGD,MAAMC,IAAI,CAACC,OAAO,CAAC,cAAc;QAChD;IACF;AACF;AAEO,SAASN,iBACde,WAA2B,EAC3BC,OAAoC;IAEpC,MAAMC,UAAUC,IAAAA,oBAAS,EAACH,aAAaC;IAEvCf,MACE,4CACAkB,MAAMC,OAAO,CAACL,eAAeA,YAAYM,IAAI,CAAC,UAAUN,aACxDC;IAGF,OAAO,CAACX;QACN,MAAMiB,WAAWL,QAAQZ;QACzBJ,MAAM,mBAAmBqB,WAAW,YAAY,WAAWjB;QAC3D,OAAOiB;IACT;AACF"}