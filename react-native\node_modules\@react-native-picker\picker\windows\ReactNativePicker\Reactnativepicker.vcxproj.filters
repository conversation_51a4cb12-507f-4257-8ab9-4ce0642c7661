﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Midl Include="ReactPackageProvider.idl" />
    <Midl Include="ReactPickerView.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="ReactPackageProvider.cpp" />
    <ClCompile Include="ReactPickerViewManager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="ReactPackageProvider.h" />
    <ClInclude Include="ReactPickerViewManager.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="PropertySheet.props" />
    <None Include="packages.config" />
    <None Include="ReactNativePicker.def" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ReactNativePicker.rc" />
  </ItemGroup>
</Project>