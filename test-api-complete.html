<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Contrats - Benali Fatima</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #f0fdf4; color: #059669; border: 1px solid #10b981; }
        .error { background-color: #fef2f2; color: #dc2626; border: 1px solid #dc2626; }
        .info { background-color: #f0f8ff; color: #3b82f6; border: 1px solid #3b82f6; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .contract-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9fafb;
        }
        .contract-card h4 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        .contract-detail {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test API Contrats - Benali Fatima</h1>
    
    <div class="test-container">
        <h2>🎯 Test Spécifique pour Benali Fatima (ID: 15)</h2>
        <button onclick="testBenaliFatimaContracts()">🔍 Tester Contrats Benali Fatima</button>
        <button onclick="testAllClients()">👥 Tester Tous les Clients</button>
        <button onclick="testServerConnection()">🔗 Test Connexion Serveur</button>
        <div id="testStatus"></div>
    </div>
    
    <div class="test-container">
        <h2>📋 Résultats des Contrats</h2>
        <div id="contractsResults"></div>
    </div>
    
    <div class="test-container">
        <h2>📝 Journal de Debug</h2>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status info"><span class="loading"></span> ${message}</div>`;
        }
        
        function displayContracts(contracts, clientName) {
            const resultsDiv = document.getElementById('contractsResults');
            
            if (contracts.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="status error">
                        ❌ Aucun contrat trouvé pour ${clientName}
                        <br><small>Ce client n'a pas de contrat dans la table Contract</small>
                    </div>
                `;
                return;
            }
            
            let html = `
                <div class="status success">
                    ✅ ${contracts.length} contrat(s) trouvé(s) pour ${clientName}
                </div>
            `;
            
            contracts.forEach((contract, index) => {
                html += `
                    <div class="contract-card">
                        <h4>📄 Contrat #${index + 1}</h4>
                        <div class="contract-detail"><strong>ID Contrat:</strong> ${contract.idcontract}</div>
                        <div class="contract-detail"><strong>Code QR:</strong> ${contract.codeqr || 'Non défini'}</div>
                        <div class="contract-detail"><strong>Marque Compteur:</strong> ${contract.marquecompteur || 'Non définie'}</div>
                        <div class="contract-detail"><strong>Numéro Série:</strong> ${contract.numseriecompteur || 'Non défini'}</div>
                        <div class="contract-detail"><strong>Date Contrat:</strong> ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}</div>
                        <div class="contract-detail"><strong>Client:</strong> ${contract.nom} ${contract.prenom}</div>
                        <div class="contract-detail"><strong>Adresse:</strong> ${contract.adresse || 'Non définie'}</div>
                        <div class="contract-detail"><strong>Position:</strong> ${contract.posx && contract.posy ? `X: ${contract.posx}, Y: ${contract.posy}` : 'Non définie'}</div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        async function testServerConnection() {
            log('🔄 Test de connexion au serveur...');
            showLoading('testStatus', 'Test de connexion...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Serveur accessible');
                    showStatus('testStatus', 'success', `✅ Serveur accessible - ${data.message}`);
                } else {
                    log(`❌ Erreur serveur: ${response.status}`);
                    showStatus('testStatus', 'error', `❌ Erreur serveur: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Erreur de connexion: ${error.message}`);
                showStatus('testStatus', 'error', `❌ Erreur de connexion: ${error.message}`);
            }
        }
        
        async function testBenaliFatimaContracts() {
            const clientId = 15;
            const clientName = 'Benali Fatima';
            
            log(`🔍 Test des contrats pour ${clientName} (ID: ${clientId})...`);
            showLoading('testStatus', `Test des contrats pour ${clientName}...`);
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`);
                const data = await response.json();
                
                log(`📥 Réponse reçue: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok && data.success) {
                    log(`✅ API contrats OK - ${data.count} contrat(s) trouvé(s)`);
                    showStatus('testStatus', 'success', 
                        `✅ ${data.count} contrat(s) trouvé(s) pour ${clientName}`
                    );
                    
                    displayContracts(data.data, clientName);
                    
                    // Test du comportement attendu dans React
                    if (data.count === 0) {
                        log('🎯 Comportement attendu: Afficher "Ce client n\'a pas de contrat"');
                    } else if (data.count === 1) {
                        log('🎯 Comportement attendu: Sélection automatique du contrat unique');
                    } else {
                        log('🎯 Comportement attendu: Dropdown avec plusieurs contrats à choisir');
                    }
                    
                } else {
                    log(`❌ Erreur API contrats: ${data.message || 'Erreur inconnue'}`);
                    showStatus('testStatus', 'error', `❌ Erreur API: ${data.message || 'Erreur inconnue'}`);
                    displayContracts([], clientName);
                }
            } catch (error) {
                log(`❌ Erreur lors du test: ${error.message}`);
                showStatus('testStatus', 'error', `❌ Erreur: ${error.message}`);
                displayContracts([], clientName);
            }
        }
        
        async function testAllClients() {
            log('🔄 Test de récupération de tous les clients...');
            showLoading('testStatus', 'Test de tous les clients...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ ${data.count} clients récupérés`);
                    showStatus('testStatus', 'success', `✅ ${data.count} clients récupérés`);
                    
                    // Afficher les premiers clients
                    const clientsList = data.data.slice(0, 5).map(c => 
                        `${c.nom} ${c.prenom} (ID: ${c.idclient}) - ${c.ville}`
                    ).join('<br>');
                    
                    document.getElementById('contractsResults').innerHTML = `
                        <div class="status info">
                            📋 Premiers clients trouvés:<br>
                            ${clientsList}
                            ${data.count > 5 ? `<br><em>... et ${data.count - 5} autres</em>` : ''}
                        </div>
                    `;
                } else {
                    log(`❌ Erreur API clients: ${data.message || 'Erreur inconnue'}`);
                    showStatus('testStatus', 'error', `❌ Erreur API clients: ${data.message || 'Erreur inconnue'}`);
                }
            } catch (error) {
                log(`❌ Erreur lors du test clients: ${error.message}`);
                showStatus('testStatus', 'error', `❌ Erreur clients: ${error.message}`);
            }
        }
        
        // Test automatique au chargement
        window.onload = function() {
            log('🚀 Page de test initialisée');
            log('💡 Cliquez sur "Tester Contrats Benali Fatima" pour voir les contrats');
            
            // Test automatique de connexion
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        };
    </script>
</body>
</html>
