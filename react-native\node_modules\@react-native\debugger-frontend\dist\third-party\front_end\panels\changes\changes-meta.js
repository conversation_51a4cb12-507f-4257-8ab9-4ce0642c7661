import*as e from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";let a;const t={changes:"Changes",showChanges:"Show Changes",revertAllChangesToCurrentFile:"Revert all changes to current file",copyAllChangesFromCurrentFile:"Copy all changes from current file"},i=e.i18n.registerUIStrings("panels/changes/changes-meta.ts",t),s=e.i18n.getLazilyComputedLocalizedString.bind(void 0,i);async function o(){return a||(a=await import("./changes.js")),a}function r(e){return void 0===a?[]:e(a)}n.ViewManager.registerViewExtension({location:"drawer-view",id:"changes.changes",title:s(t.changes),commandPrompt:s(t.showChanges),persistence:"closeable",loadView:async()=>new((await o()).ChangesView.ChangesView)}),n.ActionRegistration.registerActionExtension({actionId:"changes.revert",category:"CHANGES",title:s(t.revertAllChangesToCurrentFile),iconClass:"undo",loadActionDelegate:async()=>new((await o()).ChangesView.ActionDelegate),contextTypes:()=>r((e=>[e.ChangesView.ChangesView]))}),n.ActionRegistration.registerActionExtension({actionId:"changes.copy",category:"CHANGES",title:s(t.copyAllChangesFromCurrentFile),iconClass:"copy",loadActionDelegate:async()=>new((await o()).ChangesView.ActionDelegate),contextTypes:()=>r((e=>[e.ChangesView.ChangesView]))});
