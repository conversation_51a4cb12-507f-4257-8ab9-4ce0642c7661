{"version": 3, "names": ["createAnimatedComponent", "AnimatedText", "Text", "AnimatedView", "View", "AnimatedScrollView", "ScrollView", "AnimatedImage", "Image", "ReanimatedFlatList", "FlatList", "addWhitelistedNativeProps", "addWhitelistedUIProps"], "sourceRoot": "../../src", "sources": ["Animated.ts"], "mappings": "AAAA,YAAY;;AAoBZ,SAASA,uBAAuB,QAAQ,oCAA2B;AACnE,SAASC,YAAY,IAAIC,IAAI,QAAQ,qBAAkB;AACvD,SAASC,YAAY,IAAIC,IAAI,QAAQ,qBAAkB;AACvD,SAASC,kBAAkB,IAAIC,UAAU,QAAQ,2BAAwB;AACzE,SAASC,aAAa,IAAIC,KAAK,QAAQ,sBAAmB;AAC1D,SAASC,kBAAkB,IAAIC,QAAQ,QAAQ,yBAAsB;AACrE,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,mBAAgB;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}