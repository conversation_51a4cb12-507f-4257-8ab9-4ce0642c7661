{"version": 3, "sources": ["../../../../../src/run/ios/appleDevice/ClientManager.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { Socket } from 'net';\nimport { Duplex } from 'stream';\nimport * as tls from 'tls';\n\nimport { AFCClient } from './client/AFCClient';\nimport { DebugserverClient } from './client/DebugserverClient';\nimport { InstallationProxyClient } from './client/InstallationProxyClient';\nimport { LockdowndClient } from './client/LockdowndClient';\nimport { MobileImageMounterClient } from './client/MobileImageMounterClient';\nimport { ServiceClient } from './client/ServiceClient';\nimport { UsbmuxdClient, UsbmuxdDevice, UsbmuxdPairRecord } from './client/UsbmuxdClient';\nimport { CommandError } from '../../../utils/errors';\n\nexport class ClientManager {\n  private connections: Socket[];\n  constructor(\n    public pairRecord: UsbmuxdPairRecord,\n    public device: UsbmuxdDevice,\n    private lockdowndClient: LockdowndClient\n  ) {\n    this.connections = [lockdowndClient.socket];\n  }\n\n  static async create(udid?: string) {\n    const usbmuxClient = new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket());\n    const device = await usbmuxClient.getDevice(udid);\n    const pairRecord = await usbmuxClient.readPairRecord(device.Properties.SerialNumber);\n    const lockdownSocket = await usbmuxClient.connect(device, 62078);\n    const lockdownClient = new LockdowndClient(lockdownSocket);\n    await lockdownClient.doHandshake(pairRecord);\n    return new ClientManager(pairRecord, device, lockdownClient);\n  }\n\n  async getUsbmuxdClient() {\n    const usbmuxClient = new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket());\n    this.connections.push(usbmuxClient.socket);\n    return usbmuxClient;\n  }\n\n  async getLockdowndClient() {\n    const usbmuxClient = new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket());\n    const lockdownSocket = await usbmuxClient.connect(this.device, 62078);\n    const lockdownClient = new LockdowndClient(lockdownSocket);\n    this.connections.push(lockdownClient.socket);\n    return lockdownClient;\n  }\n\n  async getLockdowndClientWithHandshake() {\n    const lockdownClient = await this.getLockdowndClient();\n    await lockdownClient.doHandshake(this.pairRecord);\n    return lockdownClient;\n  }\n\n  async getAFCClient() {\n    return this.getServiceClient('com.apple.afc', AFCClient);\n  }\n\n  async getInstallationProxyClient() {\n    return this.getServiceClient('com.apple.mobile.installation_proxy', InstallationProxyClient);\n  }\n\n  async getMobileImageMounterClient() {\n    return this.getServiceClient('com.apple.mobile.mobile_image_mounter', MobileImageMounterClient);\n  }\n\n  async getDebugserverClient() {\n    try {\n      // iOS 14 added support for a secure debug service so try to connect to that first\n      return await this.getServiceClient(\n        'com.apple.debugserver.DVTSecureSocketProxy',\n        DebugserverClient\n      );\n    } catch {\n      // otherwise, fall back to the previous implementation\n      return this.getServiceClient('com.apple.debugserver', DebugserverClient, true);\n    }\n  }\n\n  private async getServiceClient<T extends ServiceClient<any>>(\n    name: string,\n    ServiceType: new (...args: any[]) => T,\n    disableSSL = false\n  ) {\n    const { port: servicePort, enableServiceSSL } = await this.lockdowndClient.startService(name);\n    const usbmuxClient = new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket());\n    let usbmuxdSocket = await usbmuxClient.connect(this.device, servicePort);\n\n    if (enableServiceSSL) {\n      const tlsOptions: tls.ConnectionOptions = {\n        rejectUnauthorized: false,\n        secureContext: tls.createSecureContext({\n          // Avoid using `secureProtocol` fixing the socket to a single TLS version.\n          // Newer Node versions might not support older TLS versions.\n          // By using the default `minVersion` and `maxVersion` options,\n          // The socket will automatically use the appropriate TLS version.\n          // See: https://nodejs.org/api/tls.html#tlscreatesecurecontextoptions\n          cert: this.pairRecord.RootCertificate,\n          key: this.pairRecord.RootPrivateKey,\n        }),\n      };\n\n      // Some services seem to not support TLS/SSL after the initial handshake\n      // More info: https://github.com/libimobiledevice/libimobiledevice/issues/793\n      if (disableSSL) {\n        // According to https://nodejs.org/api/tls.html#tls_tls_connect_options_callback we can\n        // pass any Duplex in to tls.connect instead of a Socket. So we'll use our proxy to keep\n        // the TLS wrapper and underlying usbmuxd socket separate.\n        const proxy: any = new UsbmuxdProxy(usbmuxdSocket);\n        tlsOptions.socket = proxy;\n\n        await new Promise<void>((resolve, reject) => {\n          const timeoutId = setTimeout(() => {\n            reject(\n              new CommandError('APPLE_DEVICE', 'The TLS handshake failed to complete after 5s.')\n            );\n          }, 5000);\n          tls.connect(tlsOptions, function (this: tls.TLSSocket) {\n            clearTimeout(timeoutId);\n            // After the handshake, we don't need TLS or the proxy anymore,\n            // since we'll just pass in the naked usbmuxd socket to the service client\n            this.destroy();\n            resolve();\n          });\n        });\n      } else {\n        tlsOptions.socket = usbmuxdSocket;\n        usbmuxdSocket = tls.connect(tlsOptions);\n      }\n    }\n    const client = new ServiceType(usbmuxdSocket);\n    this.connections.push(client.socket);\n    return client;\n  }\n\n  end() {\n    for (const socket of this.connections) {\n      // may already be closed\n      try {\n        socket.end();\n      } catch {}\n    }\n  }\n}\n\nclass UsbmuxdProxy extends Duplex {\n  constructor(private usbmuxdSock: Socket) {\n    super();\n\n    this.usbmuxdSock.on('data', (data) => {\n      this.push(data);\n    });\n  }\n\n  _write(chunk: any, encoding: string, callback: (err?: Error) => void) {\n    this.usbmuxdSock.write(chunk);\n    callback();\n  }\n\n  _read(size: number) {\n    // Stub so we don't error, since we push everything we get from usbmuxd as it comes in.\n    // TODO: better way to do this?\n  }\n\n  _destroy() {\n    this.usbmuxdSock.removeAllListeners();\n  }\n}\n"], "names": ["ClientManager", "constructor", "pairRecord", "device", "lockdowndClient", "connections", "socket", "create", "udid", "usbmuxClient", "UsbmuxdClient", "connectUsbmuxdSocket", "getDevice", "readPairRecord", "Properties", "SerialNumber", "lockdownSocket", "connect", "lockdownClient", "LockdowndClient", "doHandshake", "getUsbmuxdClient", "push", "getLockdowndClient", "getLockdowndClientWithHandshake", "getAFCClient", "getServiceClient", "AFCClient", "getInstallationProxyClient", "InstallationProxyClient", "getMobileImageMounterClient", "MobileImageMounterClient", "getDebugserverClient", "DebugserverClient", "name", "ServiceType", "disableSSL", "port", "servicePort", "enableServiceSSL", "startService", "usbmuxdSocket", "tlsOptions", "rejectUnauthorized", "secureContext", "tls", "createSecureContext", "cert", "RootCertificate", "key", "RootPrivateKey", "proxy", "UsbmuxdProxy", "Promise", "resolve", "reject", "timeoutId", "setTimeout", "CommandError", "clearTimeout", "destroy", "client", "end", "Duplex", "usbmuxdSock", "on", "data", "_write", "chunk", "encoding", "callback", "write", "_read", "size", "_destroy", "removeAllListeners"], "mappings": "AAAA;;;;;;CAMC;;;;+BAcYA;;;eAAAA;;;;yBAZU;;;;;;;iEACF;;;;;;2BAEK;mCACQ;yCACM;iCACR;0CACS;+BAEuB;wBACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMA;IAEXC,YACE,AAAOC,UAA6B,EACpC,AAAOC,MAAqB,EAC5B,AAAQC,eAAgC,CACxC;aAHOF,aAAAA;aACAC,SAAAA;aACCC,kBAAAA;QAER,IAAI,CAACC,WAAW,GAAG;YAACD,gBAAgBE,MAAM;SAAC;IAC7C;IAEA,aAAaC,OAAOC,IAAa,EAAE;QACjC,MAAMC,eAAe,IAAIC,4BAAa,CAACA,4BAAa,CAACC,oBAAoB;QACzE,MAAMR,SAAS,MAAMM,aAAaG,SAAS,CAACJ;QAC5C,MAAMN,aAAa,MAAMO,aAAaI,cAAc,CAACV,OAAOW,UAAU,CAACC,YAAY;QACnF,MAAMC,iBAAiB,MAAMP,aAAaQ,OAAO,CAACd,QAAQ;QAC1D,MAAMe,iBAAiB,IAAIC,gCAAe,CAACH;QAC3C,MAAME,eAAeE,WAAW,CAAClB;QACjC,OAAO,IAAIF,cAAcE,YAAYC,QAAQe;IAC/C;IAEA,MAAMG,mBAAmB;QACvB,MAAMZ,eAAe,IAAIC,4BAAa,CAACA,4BAAa,CAACC,oBAAoB;QACzE,IAAI,CAACN,WAAW,CAACiB,IAAI,CAACb,aAAaH,MAAM;QACzC,OAAOG;IACT;IAEA,MAAMc,qBAAqB;QACzB,MAAMd,eAAe,IAAIC,4BAAa,CAACA,4BAAa,CAACC,oBAAoB;QACzE,MAAMK,iBAAiB,MAAMP,aAAaQ,OAAO,CAAC,IAAI,CAACd,MAAM,EAAE;QAC/D,MAAMe,iBAAiB,IAAIC,gCAAe,CAACH;QAC3C,IAAI,CAACX,WAAW,CAACiB,IAAI,CAACJ,eAAeZ,MAAM;QAC3C,OAAOY;IACT;IAEA,MAAMM,kCAAkC;QACtC,MAAMN,iBAAiB,MAAM,IAAI,CAACK,kBAAkB;QACpD,MAAML,eAAeE,WAAW,CAAC,IAAI,CAAClB,UAAU;QAChD,OAAOgB;IACT;IAEA,MAAMO,eAAe;QACnB,OAAO,IAAI,CAACC,gBAAgB,CAAC,iBAAiBC,oBAAS;IACzD;IAEA,MAAMC,6BAA6B;QACjC,OAAO,IAAI,CAACF,gBAAgB,CAAC,uCAAuCG,gDAAuB;IAC7F;IAEA,MAAMC,8BAA8B;QAClC,OAAO,IAAI,CAACJ,gBAAgB,CAAC,yCAAyCK,kDAAwB;IAChG;IAEA,MAAMC,uBAAuB;QAC3B,IAAI;YACF,kFAAkF;YAClF,OAAO,MAAM,IAAI,CAACN,gBAAgB,CAChC,8CACAO,oCAAiB;QAErB,EAAE,OAAM;YACN,sDAAsD;YACtD,OAAO,IAAI,CAACP,gBAAgB,CAAC,yBAAyBO,oCAAiB,EAAE;QAC3E;IACF;IAEA,MAAcP,iBACZQ,IAAY,EACZC,WAAsC,EACtCC,aAAa,KAAK,EAClB;QACA,MAAM,EAAEC,MAAMC,WAAW,EAAEC,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAACnC,eAAe,CAACoC,YAAY,CAACN;QACxF,MAAMzB,eAAe,IAAIC,4BAAa,CAACA,4BAAa,CAACC,oBAAoB;QACzE,IAAI8B,gBAAgB,MAAMhC,aAAaQ,OAAO,CAAC,IAAI,CAACd,MAAM,EAAEmC;QAE5D,IAAIC,kBAAkB;YACpB,MAAMG,aAAoC;gBACxCC,oBAAoB;gBACpBC,eAAeC,OAAIC,mBAAmB,CAAC;oBACrC,0EAA0E;oBAC1E,4DAA4D;oBAC5D,8DAA8D;oBAC9D,iEAAiE;oBACjE,qEAAqE;oBACrEC,MAAM,IAAI,CAAC7C,UAAU,CAAC8C,eAAe;oBACrCC,KAAK,IAAI,CAAC/C,UAAU,CAACgD,cAAc;gBACrC;YACF;YAEA,wEAAwE;YACxE,6EAA6E;YAC7E,IAAId,YAAY;gBACd,uFAAuF;gBACvF,wFAAwF;gBACxF,0DAA0D;gBAC1D,MAAMe,QAAa,IAAIC,aAAaX;gBACpCC,WAAWpC,MAAM,GAAG6C;gBAEpB,MAAM,IAAIE,QAAc,CAACC,SAASC;oBAChC,MAAMC,YAAYC,WAAW;wBAC3BF,OACE,IAAIG,oBAAY,CAAC,gBAAgB;oBAErC,GAAG;oBACHb,OAAI5B,OAAO,CAACyB,YAAY;wBACtBiB,aAAaH;wBACb,+DAA+D;wBAC/D,0EAA0E;wBAC1E,IAAI,CAACI,OAAO;wBACZN;oBACF;gBACF;YACF,OAAO;gBACLZ,WAAWpC,MAAM,GAAGmC;gBACpBA,gBAAgBI,OAAI5B,OAAO,CAACyB;YAC9B;QACF;QACA,MAAMmB,SAAS,IAAI1B,YAAYM;QAC/B,IAAI,CAACpC,WAAW,CAACiB,IAAI,CAACuC,OAAOvD,MAAM;QACnC,OAAOuD;IACT;IAEAC,MAAM;QACJ,KAAK,MAAMxD,UAAU,IAAI,CAACD,WAAW,CAAE;YACrC,wBAAwB;YACxB,IAAI;gBACFC,OAAOwD,GAAG;YACZ,EAAE,OAAM,CAAC;QACX;IACF;AACF;AAEA,MAAMV,qBAAqBW,gBAAM;IAC/B9D,YAAY,AAAQ+D,WAAmB,CAAE;QACvC,KAAK,SADaA,cAAAA;QAGlB,IAAI,CAACA,WAAW,CAACC,EAAE,CAAC,QAAQ,CAACC;YAC3B,IAAI,CAAC5C,IAAI,CAAC4C;QACZ;IACF;IAEAC,OAAOC,KAAU,EAAEC,QAAgB,EAAEC,QAA+B,EAAE;QACpE,IAAI,CAACN,WAAW,CAACO,KAAK,CAACH;QACvBE;IACF;IAEAE,MAAMC,IAAY,EAAE;IAClB,uFAAuF;IACvF,+BAA+B;IACjC;IAEAC,WAAW;QACT,IAAI,CAACV,WAAW,CAACW,kBAAkB;IACrC;AACF"}