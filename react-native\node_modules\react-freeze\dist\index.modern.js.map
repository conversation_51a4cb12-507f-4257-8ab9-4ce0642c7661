{"version": 3, "file": "index.modern.js", "sources": ["../src/index.tsx"], "sourcesContent": ["import React, { Suspense, Fragment } from \"react\";\n\nconst infiniteThenable = { then() {} };\n\nfunction Suspender({\n  freeze,\n  children,\n}: {\n  freeze: boolean;\n  children: React.ReactNode;\n}) {\n  if (freeze) {\n    throw infiniteThenable;\n  }\n  return <Fragment>{children}</Fragment>;\n}\n\ninterface Props {\n  freeze: boolean;\n  children: React.ReactNode;\n  placeholder?: React.ReactNode;\n}\n\nexport function Freeze({ freeze, children, placeholder = null }: Props) {\n  return (\n    <Suspense fallback={placeholder}>\n      <Suspender freeze={freeze}>{children}</Suspender>\n    </Suspense>\n  );\n}\n"], "names": ["infiniteThenable", "then", "Suspender", "_ref", "freeze", "children", "React", "Fragment", "Freeze", "_ref2", "_ref2$placeholder", "placeholder", "Suspense", "fallback"], "mappings": ";;AAEA,IAAMA,gBAAgB,GAAG;EAAEC,IAAI,WAAAA;CAAO;AAEtC,SAASC,SAASA,CAAAC,IAAA;MAChBC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACNC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;EAKR,IAAID,MAAM,EAAE;IACV,MAAMJ,gBAAgB;;EAExB,OAAOM,oBAACC,QAAQ,QAAEF,QAAQ,CAAY;AACxC;SAQgBG,MAAMA,CAAAC,KAAA;MAAGL,MAAM,GAAAK,KAAA,CAANL,MAAM;IAAEC,QAAQ,GAAAI,KAAA,CAARJ,QAAQ;IAAAK,iBAAA,GAAAD,KAAA,CAAEE,WAAW;IAAXA,WAAW,GAAAD,iBAAA,cAAG,IAAI,GAAAA,iBAAA;EAC3D,OACEJ,oBAACM,QAAQ;IAACC,QAAQ,EAAEF;KAClBL,oBAACJ,SAAS;IAACE,MAAM,EAAEA;KAASC,QAAQ,CAAa,CACxC;AAEf;;;;"}