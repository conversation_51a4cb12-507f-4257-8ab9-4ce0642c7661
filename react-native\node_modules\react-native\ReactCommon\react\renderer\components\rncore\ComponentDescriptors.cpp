
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateComponentDescriptorCpp.js
 */

#include <react/renderer/components/rncore/ComponentDescriptors.h>
#include <react/renderer/core/ConcreteComponentDescriptor.h>
#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>

namespace facebook::react {

void rncore_registerComponentDescriptorsFromCodegen(
  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry) {
registry->add(concreteComponentDescriptorProvider<ActivityIndicatorViewComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<AndroidDrawerLayoutComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<AndroidHorizontalScrollContentViewComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<AndroidSwipeRefreshLayoutComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<DebuggingOverlayComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<PullToRefreshViewComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<SwitchComponentDescriptor>());
registry->add(concreteComponentDescriptorProvider<UnimplementedNativeViewComponentDescriptor>());
}

} // namespace facebook::react
