{"version": 3, "sources": ["../../../../src/export/web/exportWebAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { Options } from './resolveOptions';\nimport { Log } from '../../log';\nimport { WebSupportProjectPrerequisite } from '../../start/doctor/web/WebSupportProjectPrerequisite';\nimport { getPlatformBundlers } from '../../start/server/platformBundlers';\nimport { WebpackBundlerDevServer } from '../../start/server/webpack/WebpackBundlerDevServer';\nimport { CommandError } from '../../utils/errors';\nimport { setNodeEnv } from '../../utils/nodeEnv';\n\nexport async function exportWebAsync(projectRoot: string, options: Options) {\n  // Ensure webpack is available\n  await new WebSupportProjectPrerequisite(projectRoot).assertAsync();\n\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n\n  const { exp } = getConfig(projectRoot);\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n  // Create a bundler interface\n  const bundler = new WebpackBundlerDevServer(projectRoot, platformBundlers);\n\n  // If the user set `web.bundler: 'metro'` then they should use `expo export` instead.\n  if (!bundler.isTargetingWeb()) {\n    throw new CommandError(\n      chalk`{bold expo export:web} can only be used with Webpack. Use {bold expo export} for other bundlers.`\n    );\n  }\n\n  Log.log(`Exporting with Webpack...`);\n\n  // Bundle the app\n  await bundler.bundleAsync({\n    mode: options.dev ? 'development' : 'production',\n    clear: options.clear,\n  });\n}\n"], "names": ["exportWebAsync", "projectRoot", "options", "WebSupportProjectPrerequisite", "assertAsync", "setNodeEnv", "dev", "require", "load", "exp", "getConfig", "platformBundlers", "getPlatformBundlers", "bundler", "WebpackBundlerDevServer", "isTargetingWeb", "CommandError", "chalk", "Log", "log", "bundleAsync", "mode", "clear"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;;yBAXI;;;;;;;gEACR;;;;;;qBAGE;+CAC0B;kCACV;yCACI;wBACX;yBACF;;;;;;AAEpB,eAAeA,eAAeC,WAAmB,EAAEC,OAAgB;IACxE,8BAA8B;IAC9B,MAAM,IAAIC,4DAA6B,CAACF,aAAaG,WAAW;IAEhEC,IAAAA,mBAAU,EAACH,QAAQI,GAAG,GAAG,gBAAgB;IACzCC,QAAQ,aAAaC,IAAI,CAACP;IAE1B,MAAM,EAAEQ,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACT;IAC1B,MAAMU,mBAAmBC,IAAAA,qCAAmB,EAACX,aAAaQ;IAC1D,6BAA6B;IAC7B,MAAMI,UAAU,IAAIC,gDAAuB,CAACb,aAAaU;IAEzD,qFAAqF;IACrF,IAAI,CAACE,QAAQE,cAAc,IAAI;QAC7B,MAAM,IAAIC,oBAAY,CACpBC,IAAAA,gBAAK,CAAA,CAAC,gGAAgG,CAAC;IAE3G;IAEAC,QAAG,CAACC,GAAG,CAAC,CAAC,yBAAyB,CAAC;IAEnC,iBAAiB;IACjB,MAAMN,QAAQO,WAAW,CAAC;QACxBC,MAAMnB,QAAQI,GAAG,GAAG,gBAAgB;QACpCgB,OAAOpB,QAAQoB,KAAK;IACtB;AACF"}