{"name": "@expo/config-plugins", "version": "10.1.2", "description": "A library for Expo config plugins", "main": "build/index.js", "types": "build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./build/android": {"types": "./build/android/index.d.ts", "default": "./build/android/index.js"}, "./build/ios": {"types": "./build/ios/index.d.ts", "default": "./build/ios/index.js"}, "./build": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./build/*.js": "./build/*.js", "./build/*": "./build/*.js", "./package.json": "./package.json"}, "scripts": {"build": "tsc --emitDeclarationOnly && babel src --out-dir build --extensions \".ts\" --source-maps --ignore \"src/**/__mocks__/*\",\"src/**/__tests__/*\",\"src/**/__integration_tests__/*\"", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && yarn run build", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/config-plugins"}, "keywords": ["json", "expo", "react-native", "react"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://docs.expo.dev/guides/config-plugins/", "files": ["build", "paths"], "dependencies": {"@expo/config-types": "^53.0.5", "@expo/json-file": "~9.1.5", "@expo/plist": "^0.3.5", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^2.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}, "devDependencies": {"@types/debug": "^4.1.5", "@types/find-up": "^4.0.0", "@types/xml2js": "~0.4.11", "expo-module-scripts": "^4.1.9"}, "publishConfig": {"access": "public"}, "gitHead": "a670b4be3e2124fb62e0f393a0503d9ccb25fbf8"}