{"version": 3, "sources": ["../../../src/run/hints.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { Log } from '../log';\nimport { isInteractive } from '../utils/interactive';\n\n/** Log the device argument to use for the next run: `Using --device foobar` */\nexport function logDeviceArgument(id: string) {\n  Log.log(chalk.dim`› Using --device ${id}`);\n}\n\nexport function logPlatformRunCommand(platform: string, argv: string[] = []) {\n  Log.log(chalk.dim(`› Using expo run:${platform} ${argv.join(' ')}`));\n}\n\nexport function logProjectLogsLocation() {\n  Log.log(\n    chalk`\\n› Logs for your project will appear below.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n"], "names": ["logDeviceArgument", "logPlatformRunCommand", "logProjectLogsLocation", "id", "Log", "log", "chalk", "dim", "platform", "argv", "join", "isInteractive"], "mappings": ";;;;;;;;;;;IAMgBA,iBAAiB;eAAjBA;;IAIAC,qBAAqB;eAArBA;;IAIAC,sBAAsB;eAAtBA;;;;gEAdE;;;;;;qBAEE;6BACU;;;;;;AAGvB,SAASF,kBAAkBG,EAAU;IAC1CC,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,GAAG,CAAC;AAC3C;AAEO,SAASF,sBAAsBO,QAAgB,EAAEC,OAAiB,EAAE;IACzEL,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,GAAG,CAAC,CAAC,iBAAiB,EAAEC,SAAS,CAAC,EAAEC,KAAKC,IAAI,CAAC,MAAM;AACpE;AAEO,SAASR;IACdE,QAAG,CAACC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,4CAA4C,EAChDK,IAAAA,0BAAa,MAAKL,gBAAK,CAACC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,GACzD,CAAC;AAEN"}