{"version": 3, "names": ["defineAnimation", "getReduceMotionForAnimation", "rubberBandDecay", "isValidRubberBandConfig", "rigidDecay", "ReanimatedError", "validateConfig", "config", "clamp", "Array", "isArray", "length", "velocityFactor", "rubberBandEffect", "<PERSON><PERSON><PERSON><PERSON>", "userConfig", "callback", "deceleration", "velocity", "rubberBandFactor", "Object", "keys", "for<PERSON>ach", "key", "decay", "animation", "now", "onStart", "value", "current", "lastTimestamp", "startTimestamp", "initialVelocity", "reduceMotion", "onFrame"], "sourceRoot": "../../../../src", "sources": ["animation/decay/decay.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,EAAEC,2BAA2B,QAAQ,YAAS;AAMtE,SAASC,eAAe,QAAQ,sBAAmB;AACnD,SAASC,uBAAuB,QAAQ,YAAS;AAOjD,SAASC,UAAU,QAAQ,iBAAc;AACzC,SAASC,eAAe,QAAQ,iBAAc;;AAI9C;;AAMA,SAASC,cAAcA,CAACC,MAA0B,EAAQ;EACxD,SAAS;;EACT,IAAIA,MAAM,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIH,eAAe,CACvB,4CAA4C,OAAOE,MAAM,CAACC,KAAK,GACjE,CAAC;IACH;IACA,IAAID,MAAM,CAACC,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIN,eAAe,CACvB,qDACEE,MAAM,CAACC,KAAK,CAACG,MAAM,GAEvB,CAAC;IACH;EACF;EACA,IAAIJ,MAAM,CAACK,cAAc,IAAI,CAAC,EAAE;IAC9B,MAAM,IAAIP,eAAe,CACvB,2DAA2DE,MAAM,CAACK,cAAc,GAClF,CAAC;EACH;EACA,IAAIL,MAAM,CAACM,gBAAgB,IAAI,CAACN,MAAM,CAACC,KAAK,EAAE;IAC5C,MAAM,IAAIH,eAAe,CACvB,iEACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,SAAS,GAAG,SAAAA,CACvBC,UAAuB,EACvBC,QAA4B,EACD;EAC3B,SAAS;;EAET,OAAOhB,eAAe,CAAiB,CAAC,EAAE,MAAM;IAC9C,SAAS;;IACT,MAAMO,MAA0B,GAAG;MACjCU,YAAY,EAAE,KAAK;MACnBL,cAAc,EAAE,CAAC;MACjBM,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAIJ,UAAU,EAAE;MACdK,MAAM,CAACC,IAAI,CAACN,UAAU,CAAC,CAACO,OAAO,CAC5BC,GAAG,IACAhB,MAAM,CAASgB,GAAG,CAAC,GAAGR,UAAU,CAACQ,GAAG,CAC1C,CAAC;IACH;IAEA,MAAMC,KAA+D,GACnErB,uBAAuB,CAACI,MAAM,CAAC,GAC3B,CAACkB,SAAS,EAAEC,GAAG,KAAKxB,eAAe,CAACuB,SAAS,EAAEC,GAAG,EAAEnB,MAAM,CAAC,GAC3D,CAACkB,SAAS,EAAEC,GAAG,KAAKtB,UAAU,CAACqB,SAAS,EAAEC,GAAG,EAAEnB,MAAM,CAAC;IAE5D,SAASoB,OAAOA,CACdF,SAAyB,EACzBG,KAAa,EACbF,GAAc,EACR;MACND,SAAS,CAACI,OAAO,GAAGD,KAAK;MACzBH,SAAS,CAACK,aAAa,GAAGJ,GAAG;MAC7BD,SAAS,CAACM,cAAc,GAAGL,GAAG;MAC9BD,SAAS,CAACO,eAAe,GAAGzB,MAAM,CAACW,QAAQ;MAC3CZ,cAAc,CAACC,MAAM,CAAC;MAEtB,IAAIkB,SAAS,CAACQ,YAAY,IAAI1B,MAAM,CAACC,KAAK,EAAE;QAC1C,IAAIoB,KAAK,GAAGrB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAC3BiB,SAAS,CAACI,OAAO,GAAGtB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIoB,KAAK,GAAGrB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UAClCiB,SAAS,CAACI,OAAO,GAAGtB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;QACrC;MACF;IACF;IAEA,OAAO;MACL0B,OAAO,EAAEV,KAAK;MACdG,OAAO;MACPX,QAAQ;MACRE,QAAQ,EAAEX,MAAM,CAACW,QAAQ,IAAI,CAAC;MAC9Bc,eAAe,EAAE,CAAC;MAClBH,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBE,YAAY,EAAEhC,2BAA2B,CAACM,MAAM,CAAC0B,YAAY;IAC/D,CAAC;EACH,CAAC,CAAC;AACJ,CAA6B", "ignoreList": []}