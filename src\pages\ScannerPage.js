import React, { useState, useEffect, useRef } from 'react';
import jsQR from 'jsqr';

const ScannerPage = ({ onBack }) => {
  const [scannerActive, setScannerActive] = useState(false);
  const [stream, setStream] = useState(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [clientInfo, setClientInfo] = useState(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionIntervalRef = useRef(null);

  // Démarrer automatiquement le scanner dès l'ouverture
  useEffect(() => {
    setTimeout(() => {
      startCamera();
    }, 1000);
    
    return () => {
      // Nettoyer les ressources
      if (detectionIntervalRef.current) {
        clearInterval(detectionIntervalRef.current);
      }
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const startCamera = async () => {
    try {
      console.log('🎥 Démarrage de la caméra...');

      // Essayer d'abord avec la caméra arrière (environment)
      let constraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 }
        }
      };

      let mediaStream;
      try {
        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Caméra arrière activée');
      } catch (error) {
        console.log('⚠️ Caméra arrière non disponible, essai caméra frontale...');
        // Si la caméra arrière échoue, essayer la frontale
        constraints.video.facingMode = 'user';
        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Caméra frontale activée');
      }

      setStream(mediaStream);
      setScannerActive(true);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.autoplay = true;
        videoRef.current.playsInline = true;
        videoRef.current.muted = true;

        // Attendre que la vidéo soit prête
        const onVideoReady = () => {
          console.log('📹 Vidéo prête:', {
            width: videoRef.current.videoWidth,
            height: videoRef.current.videoHeight,
            readyState: videoRef.current.readyState
          });

          // Démarrer la détection après un court délai
          setTimeout(() => {
            console.log('🚀 Démarrage de la détection QR...');
            startQRDetection();
          }, 2000);
        };

        videoRef.current.onloadedmetadata = onVideoReady;
        videoRef.current.oncanplay = onVideoReady;

        // Forcer la lecture
        videoRef.current.play().then(() => {
          console.log('▶️ Lecture vidéo démarrée');
        }).catch(err => {
          console.error('❌ Erreur lecture vidéo:', err);
        });
      }
    } catch (error) {
      console.error('❌ Erreur caméra:', error);
      let message = '❌ Impossible d\'accéder à la caméra\n\n';

      if (error.name === 'NotAllowedError') {
        message += '🚫 Permission refusée. Veuillez:\n';
        message += '1. Autoriser l\'accès à la caméra\n';
        message += '2. Recharger la page';
      } else if (error.name === 'NotFoundError') {
        message += '📷 Aucune caméra trouvée sur cet appareil';
      } else {
        message += `Erreur: ${error.message}`;
      }

      alert(message);
    }
  };

  const startQRDetection = () => {
    console.log('🔍 Démarrage de la détection QR automatique...');
    setIsDetecting(true);

    if (detectionIntervalRef.current) {
      clearInterval(detectionIntervalRef.current);
    }

    let scanCount = 0;

    const detectQR = () => {
      if (!videoRef.current || !canvasRef.current || !scannerActive || !stream) {
        console.log('⏹️ Conditions non remplies pour la détection');
        return;
      }

      const video = videoRef.current;
      const canvas = canvasRef.current;

      if (video.readyState < 2 || video.videoWidth === 0 || video.videoHeight === 0) {
        if (scanCount % 20 === 0) { // Log toutes les 20 tentatives
          console.log('⏳ Vidéo pas encore prête...', {
            readyState: video.readyState,
            dimensions: `${video.videoWidth}x${video.videoHeight}`
          });
        }
        scanCount++;
        return;
      }

      try {
        const width = video.videoWidth;
        const height = video.videoHeight;

        // Redimensionner le canvas si nécessaire
        if (canvas.width !== width || canvas.height !== height) {
          canvas.width = width;
          canvas.height = height;
          console.log('📐 Canvas redimensionné:', width + 'x' + height);
        }

        const context = canvas.getContext('2d');
        context.drawImage(video, 0, 0, width, height);
        const imageData = context.getImageData(0, 0, width, height);

        // Essayer plusieurs configurations de jsQR pour améliorer la détection
        const qrCode = jsQR(imageData.data, imageData.width, imageData.height, {
          inversionAttempts: "attemptBoth",
        });

        scanCount++;

        // Log périodique pour debug
        if (scanCount % 50 === 0) { // Toutes les 50 tentatives (environ toutes les 5 secondes)
          console.log('🔍 Scan en cours...', {
            tentatives: scanCount,
            résolution: `${width}x${height}`,
            readyState: video.readyState
          });
        }

        if (qrCode && qrCode.data && qrCode.data.trim()) {
          console.log('🎯 QR Code détecté par la caméra!', {
            data: qrCode.data,
            location: qrCode.location,
            tentatives: scanCount
          });

          setIsDetecting(false);
          if (detectionIntervalRef.current) {
            clearInterval(detectionIntervalRef.current);
          }

          // Vibrer pour indiquer la détection
          if (navigator.vibrate) {
            navigator.vibrate([200, 100, 200]);
          }

          handleQRDetected(qrCode.data.trim());
        }
      } catch (error) {
        console.error('❌ Erreur détection QR:', error);
      }
    };

    // Démarrer la détection avec un intervalle optimisé
    detectionIntervalRef.current = setInterval(detectQR, 100); // Toutes les 100ms pour plus de réactivité
    console.log('✅ Détection QR automatique démarrée (intervalle: 100ms)');
  };

  const handleQRDetected = async (qrCode) => {
    try {
      console.log('🔍 Recherche client pour QR:', qrCode);
      const response = await fetch(`http://localhost:3007/api/scan/${qrCode}`);
      const data = await response.json();

      if (data.success && data.data && data.data.client) {
        setClientInfo(data.data);
        // Vibrer si disponible
        if (navigator.vibrate) {
          navigator.vibrate([200, 100, 200]);
        }
      } else {
        // Afficher un message personnalisé quand le client n'existe pas
        setClientInfo({
          notFound: true,
          qrCode: qrCode,
          message: 'Cette personne n\'est pas un client'
        });
        // Vibrer différemment pour indiquer une erreur
        if (navigator.vibrate) {
          navigator.vibrate([100, 100, 100, 100, 100]);
        }
      }
    } catch (error) {
      console.error('❌ Erreur API:', error);
      setClientInfo({
        error: true,
        qrCode: qrCode,
        message: 'Erreur de connexion au serveur'
      });
    }
  };

  const resetScanner = () => {
    setClientInfo(null);
    setTimeout(() => startQRDetection(), 500);
  };

  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      padding: '0',
      position: 'relative',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header avec bouton retour */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 1000
      }}>
        <button 
          onClick={onBack} 
          style={{
            background: 'rgba(255,255,255,0.9)',
            border: 'none',
            borderRadius: '25px',
            padding: '10px 20px',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#333',
            cursor: 'pointer',
            boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
          }}
        >
          ← Retour
        </button>
      </div>

      {/* Contenu principal */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        textAlign: 'center'
      }}>
        {!scannerActive ? (
          // Page de chargement
          <div style={{ color: 'white', textAlign: 'center' }}>
            <div style={{
              fontSize: '48px',
              marginBottom: '20px',
              animation: 'pulse 2s infinite'
            }}>📱</div>
            <h2 style={{ fontSize: '24px', marginBottom: '10px', fontWeight: 'bold' }}>
              Initialisation du Scanner
            </h2>
            <p style={{ fontSize: '16px', opacity: 0.8 }}>
              Préparation de la caméra...
            </p>
          </div>
        ) : clientInfo ? (
          // Affichage des résultats (client trouvé ou non trouvé)
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            maxWidth: '400px',
            width: '100%',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)'
          }}>
            {clientInfo.notFound ? (
              // Client non trouvé
              <div style={{ textAlign: 'center' }}>
                <h2 style={{ color: '#f44336', marginBottom: '20px', fontSize: '24px' }}>
                  ❌ CLIENT NON TROUVÉ
                </h2>
                <div style={{
                  background: '#ffebee',
                  borderRadius: '15px',
                  padding: '20px',
                  marginBottom: '20px',
                  border: '2px solid #f44336'
                }}>
                  <p style={{
                    fontSize: '18px',
                    fontWeight: 'bold',
                    color: '#d32f2f',
                    marginBottom: '10px'
                  }}>
                    {clientInfo.message}
                  </p>
                  <p style={{ color: '#666', fontSize: '14px' }}>
                    QR Code scanné: <strong>{clientInfo.qrCode}</strong>
                  </p>
                  <p style={{ color: '#666', fontSize: '14px', marginTop: '10px' }}>
                    Ce QR code ne correspond à aucun client enregistré dans notre système.
                  </p>
                </div>
              </div>
            ) : clientInfo.error ? (
              // Erreur de connexion
              <div style={{ textAlign: 'center' }}>
                <h2 style={{ color: '#ff9800', marginBottom: '20px', fontSize: '24px' }}>
                  ⚠️ ERREUR DE CONNEXION
                </h2>
                <div style={{
                  background: '#fff3e0',
                  borderRadius: '15px',
                  padding: '20px',
                  marginBottom: '20px',
                  border: '2px solid #ff9800'
                }}>
                  <p style={{
                    fontSize: '16px',
                    color: '#f57c00',
                    marginBottom: '10px'
                  }}>
                    {clientInfo.message}
                  </p>
                  <p style={{ color: '#666', fontSize: '14px' }}>
                    QR Code: <strong>{clientInfo.qrCode}</strong>
                  </p>
                </div>
              </div>
            ) : (
              // Client trouvé - Affichage des informations
              <div>
                <h2 style={{ color: '#4caf50', marginBottom: '20px', fontSize: '24px', textAlign: 'center' }}>
                  🎯 CLIENT IDENTIFIÉ
                </h2>
                <div style={{ textAlign: 'left', marginBottom: '20px' }}>
                  <div style={{
                    background: '#e8f5e8',
                    borderRadius: '10px',
                    padding: '15px',
                    marginBottom: '15px',
                    border: '1px solid #4caf50'
                  }}>
                    <h3 style={{ color: '#2e7d32', marginTop: '0', marginBottom: '10px' }}>👤 Informations Client</h3>
                    <p style={{ margin: '5px 0' }}><strong>Nom:</strong> {clientInfo.client.nom} {clientInfo.client.prenom}</p>
                    <p style={{ margin: '5px 0' }}><strong>🏠 Adresse:</strong> {clientInfo.client.adresse}</p>
                    <p style={{ margin: '5px 0' }}><strong>🏙️ Ville:</strong> {clientInfo.client.ville}</p>
                    <p style={{ margin: '5px 0' }}><strong>📞 Téléphone:</strong> {clientInfo.client.tel}</p>
                    <p style={{ margin: '5px 0' }}><strong>📧 Email:</strong> {clientInfo.client.email}</p>
                    {clientInfo.client.secteur_nom && (
                      <p style={{ margin: '5px 0' }}><strong>�️ Secteur:</strong> {clientInfo.client.secteur_nom}</p>
                    )}
                  </div>

                  {clientInfo.contract && (
                    <div style={{
                      background: '#e3f2fd',
                      borderRadius: '10px',
                      padding: '15px',
                      marginBottom: '15px',
                      border: '1px solid #2196f3'
                    }}>
                      <h3 style={{ color: '#1976d2', marginTop: '0', marginBottom: '10px' }}>⚙️ Compteur</h3>
                      <p style={{ margin: '5px 0' }}><strong>�🔢 QR Code:</strong> {clientInfo.contract.codeQr}</p>
                      <p style={{ margin: '5px 0' }}><strong>🏭 Marque:</strong> {clientInfo.contract.marqueCompteur}</p>
                      <p style={{ margin: '5px 0' }}><strong>🔢 N° Série:</strong> {clientInfo.contract.numSerieCompteur}</p>
                      <p style={{ margin: '5px 0' }}><strong>📅 Date contrat:</strong> {new Date(clientInfo.contract.dateContract).toLocaleDateString('fr-FR')}</p>
                      {clientInfo.contract.posX && clientInfo.contract.posY && (
                        <p style={{ margin: '5px 0' }}><strong>📍 Position:</strong> {clientInfo.contract.posX}, {clientInfo.contract.posY}</p>
                      )}
                    </div>
                  )}

                  {clientInfo.lastConsommation && (
                    <div style={{
                      background: '#f3e5f5',
                      borderRadius: '10px',
                      padding: '15px',
                      marginBottom: '15px',
                      border: '1px solid #9c27b0'
                    }}>
                      <h3 style={{ color: '#7b1fa2', marginTop: '0', marginBottom: '10px' }}>💧 Dernière Consommation</h3>
                      <p style={{ margin: '5px 0' }}><strong>📊 Précédente:</strong> {clientInfo.lastConsommation.consommationPre} m³</p>
                      <p style={{ margin: '5px 0' }}><strong>📈 Actuelle:</strong> {clientInfo.lastConsommation.consommationActuelle} m³</p>
                      <p style={{ margin: '5px 0' }}><strong>📅 Période:</strong> {clientInfo.lastConsommation.periode}</p>
                      <p style={{ margin: '5px 0' }}><strong>⏱️ Jours:</strong> {clientInfo.lastConsommation.jours}</p>
                      <p style={{ margin: '5px 0' }}><strong>📋 Statut:</strong> {clientInfo.lastConsommation.status}</p>
                    </div>
                  )}

                  {clientInfo.factures && clientInfo.factures.length > 0 && (
                    <div style={{
                      background: '#fff3e0',
                      borderRadius: '10px',
                      padding: '15px',
                      marginBottom: '15px',
                      border: '1px solid #ff9800'
                    }}>
                      <h3 style={{ color: '#f57c00', marginTop: '0', marginBottom: '10px' }}>🧾 Factures ({clientInfo.factures.length})</h3>
                      {clientInfo.factures.slice(0, 3).map((facture, index) => (
                        <p key={index} style={{ margin: '5px 0', fontSize: '14px' }}>
                          {index + 1}. {facture.reference} - {facture.montant}€ ({facture.status})
                        </p>
                      ))}
                      {clientInfo.factures.length > 3 && (
                        <p style={{ margin: '5px 0', fontSize: '12px', color: '#666' }}>
                          ... et {clientInfo.factures.length - 3} autres factures
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            <button
              onClick={resetScanner}
              style={{
                background: clientInfo.notFound || clientInfo.error ? '#f44336' : '#2196f3',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '15px 30px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                width: '100%'
              }}
            >
              🔄 Scanner un autre QR Code
            </button>
          </div>
        ) : (
          // Interface de scan moderne
          <div style={{ color: 'white', textAlign: 'center', width: '100%' }}>
            {/* Titre SCAN ME */}
            <h1 style={{
              fontSize: '48px',
              fontWeight: 'bold',
              marginBottom: '30px',
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
              background: 'linear-gradient(45deg, #ff6b6b, #feca57)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              SCAN ME
            </h1>

            {/* Cadre de scan */}
            <div style={{
              position: 'relative',
              maxWidth: '350px',
              margin: '0 auto 30px',
              background: 'linear-gradient(45deg, #ff6b6b, #feca57)',
              padding: '8px',
              borderRadius: '30px'
            }}>
              <div style={{
                background: 'white',
                borderRadius: '22px',
                padding: '20px',
                position: 'relative'
              }}>
                <video
                  ref={videoRef}
                  style={{
                    width: '100%',
                    height: '300px',
                    objectFit: 'cover',
                    borderRadius: '15px',
                    background: '#f0f0f0'
                  }}
                />
                <canvas ref={canvasRef} style={{ display: 'none' }} />
                
                {/* Indicateur de détection */}
                <div style={{
                  position: 'absolute',
                  top: '30px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  background: isDetecting ? 'rgba(76, 175, 80, 0.9)' : 'rgba(255, 152, 0, 0.9)',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  animation: isDetecting ? 'pulse 2s infinite' : 'none'
                }}>
                  {isDetecting ? '🎯 SCANNER ACTIF - Pointez vers un QR code' : '⏸️ Scanner en pause'}
                </div>

                {/* Instructions de scan */}
                <div style={{
                  position: 'absolute',
                  bottom: '30px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  background: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  padding: '10px 20px',
                  borderRadius: '15px',
                  fontSize: '12px',
                  textAlign: 'center',
                  maxWidth: '280px'
                }}>
                  📱 Placez un QR code devant la caméra<br/>
                  Le scanner détectera automatiquement le code
                </div>
              </div>
            </div>

            {/* Texte d'instruction */}
            <h2 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              marginBottom: '20px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>
              SCANNER QR CODE CLIENT
            </h2>

            <p style={{
              fontSize: '16px',
              marginBottom: '20px',
              opacity: 0.9,
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}>
              📱 Pointez votre caméra vers le QR code du compteur<br/>
              🎯 La détection se fait automatiquement
            </p>

            {/* Boutons de test et diagnostic */}
            <div style={{ marginTop: '20px' }}>
              <button
                onClick={() => handleQRDetected('QR-2025-0001')}
                style={{
                  background: 'rgba(76, 175, 80, 0.2)',
                  color: 'white',
                  border: '2px solid #4caf50',
                  borderRadius: '25px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  margin: '5px',
                  backdropFilter: 'blur(10px)'
                }}
              >
                ✅ Test Benali Fatima
              </button>
              <br />
              <button
                onClick={() => handleQRDetected('QR123')}
                style={{
                  background: 'rgba(33, 150, 243, 0.2)',
                  color: 'white',
                  border: '2px solid #2196f3',
                  borderRadius: '25px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  margin: '5px',
                  backdropFilter: 'blur(10px)'
                }}
              >
                ✅ Test Client1
              </button>
              <br />
              <button
                onClick={() => handleQRDetected('QR-INEXISTANT-999')}
                style={{
                  background: 'rgba(244, 67, 54, 0.2)',
                  color: 'white',
                  border: '2px solid #f44336',
                  borderRadius: '25px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  margin: '5px',
                  backdropFilter: 'blur(10px)'
                }}
              >
                ❌ Test Client Inexistant
              </button>
              <br />
              <button
                onClick={() => {
                  // Test de capture d'image pour diagnostic
                  if (videoRef.current && canvasRef.current) {
                    const video = videoRef.current;
                    const canvas = canvasRef.current;

                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;

                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);

                    // Créer une image de test
                    const dataURL = canvas.toDataURL('image/png');
                    const img = new Image();
                    img.src = dataURL;
                    img.style.position = 'fixed';
                    img.style.top = '10px';
                    img.style.right = '10px';
                    img.style.width = '200px';
                    img.style.height = '150px';
                    img.style.zIndex = '9999';
                    img.style.border = '2px solid yellow';
                    img.style.borderRadius = '10px';

                    document.body.appendChild(img);

                    alert('📸 Capture d\'écran prise!\nUne image de test apparaît en haut à droite.\nClic OK pour la supprimer.');
                    document.body.removeChild(img);

                    console.log('📸 Capture test:', {
                      dimensions: `${canvas.width}x${canvas.height}`,
                      readyState: video.readyState
                    });
                  } else {
                    alert('❌ Caméra non disponible pour le test');
                  }
                }}
                style={{
                  background: 'rgba(255, 152, 0, 0.2)',
                  color: 'white',
                  border: '2px solid #ff9800',
                  borderRadius: '25px',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  margin: '5px',
                  backdropFilter: 'blur(10px)'
                }}
              >
                📸 Test Caméra
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Styles CSS pour l'animation */}
      <style jsx>{`
        @keyframes pulse {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.1); opacity: 0.7; }
          100% { transform: scale(1); opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default ScannerPage;
