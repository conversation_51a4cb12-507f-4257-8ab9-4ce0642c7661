{"version": 3, "names": ["cancelAnimation", "defineAnimation", "initialUpdaterRun", "withTiming", "with<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "withClamp", "<PERSON><PERSON><PERSON><PERSON>", "withRepeat", "withSequence", "withStyleAnimation"], "sourceRoot": "../../../src", "sources": ["animation/index.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,WAAQ;AAC5E,SAASC,UAAU,QAAQ,aAAU;AAErC,SAASC,UAAU,QAAQ,aAAU;AAErC,SAASC,SAAS,QAAQ,kBAAS;AAEnC,SAASC,SAAS,QAAQ,YAAS;AACnC,SAASC,SAAS,QAAQ,YAAS;AACnC,SAASC,UAAU,QAAQ,aAAU;AACrC,SAASC,YAAY,QAAQ,eAAY;AACzC,SAASC,kBAAkB,QAAQ,qBAAkB", "ignoreList": []}