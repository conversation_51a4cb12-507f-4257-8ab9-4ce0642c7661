const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');

const app = express();
app.use(express.json());
app.use(cors());

console.log('🚀 Démarrage du serveur de test...');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
async function testDatabaseConnection() {
  try {
    console.log('🔄 Test de connexion à la base de données...');
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    
    // Test simple
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${result.rows[0].count} clients dans la table Client`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    console.error('❌ Détails:', error);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée - Serveur fonctionnel');
  res.json({
    message: 'Serveur test fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 GET /api/clients - Récupération de tous les clients');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    console.log(`✅ ${result.rows.length} clients récupérés`);

    // Log des premiers clients
    if (result.rows.length > 0) {
      console.log('📋 Premiers clients:');
      result.rows.slice(0, 3).forEach((client, index) => {
        console.log(`   ${index + 1}. ID:${client.idclient} - ${client.nom} ${client.prenom} (${client.ville})`);
      });
    }

    const response = {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    };

    console.log('📤 Envoi de la réponse clients');
    res.json(response);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route CRITIQUE pour récupérer les contrats d'un client spécifique
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: GET /api/clients/${id}/contracts`);
    console.log(`🔍 Recherche des contrats pour le client ID: ${id}`);

    // Vérifier d'abord si le client existe
    const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [id]);
    if (clientCheck.rows.length === 0) {
      console.log(`❌ Client ID ${id} n'existe pas dans la base`);
      return res.status(404).json({
        success: false,
        message: `Client ID ${id} non trouvé`,
        client_id: parseInt(id)
      });
    }

    const clientInfo = clientCheck.rows[0];
    console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

    // Requête pour récupérer les contrats
    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL...');
    const result = await pool.query(query, [id]);

    console.log(`📊 RÉSULTAT: ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);
    
    if (result.rows.length > 0) {
      console.log('📋 CONTRATS TROUVÉS:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
      });
    } else {
      console.log('⚠️ AUCUN CONTRAT TROUVÉ pour ce client');
    }

    const response = {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
      client_id: parseInt(id),
      client_name: `${clientInfo.nom} ${clientInfo.prenom}`
    };

    console.log('📤 ENVOI DE LA RÉPONSE CONTRATS');
    res.json(response);

  } catch (error) {
    console.error('❌ ERREUR CRITIQUE lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message,
      client_id: parseInt(req.params.id)
    });
  }
});

// Route pour récupérer la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur non gérée:', err.message);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  console.log('🚀 Démarrage du serveur test...');
  
  // Tester la connexion à la base de données
  const dbConnected = await testDatabaseConnection();
  
  if (!dbConnected) {
    console.error('❌ Impossible de se connecter à la base de données');
    console.error('❌ Vérifiez que PostgreSQL est démarré et que la base "Facutration" existe');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 SERVEUR TEST DÉMARRÉ sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('\n✅ PRÊT À RECEVOIR LES REQUÊTES !');
    console.log('═'.repeat(60));
  });
}

startServer();

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err.message);
  console.error('❌ Stack:', err.stack);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
  console.error('❌ Stack:', err.stack);
});
