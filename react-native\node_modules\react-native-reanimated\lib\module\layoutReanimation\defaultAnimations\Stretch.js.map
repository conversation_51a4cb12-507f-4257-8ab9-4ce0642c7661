{"version": 3, "names": ["ComplexAnimationBuilder", "StretchInX", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "animations", "transform", "scaleX", "StretchInY", "scaleY", "StretchOutX", "StretchOutY"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultAnimations/Stretch.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,uBAAuB,QAAQ,8BAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SACbD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,YAAY;EAEhC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,UAAU,CAAC,CAAC;EACzB;EAEAG,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAO,MAAM;MACX,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QACpE,CAAC;QACDM,aAAa,EAAE;UACbE,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAC,CAAC;UAC1B,GAAGH;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,UAAU,SACblB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,YAAY;EAEhC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIe,UAAU,CAAC,CAAC;EACzB;EAEAd,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAO,MAAM;MACX,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CAAC;YAAEG,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QACpE,CAAC;QACDM,aAAa,EAAE;UACbE,SAAS,EAAE,CAAC;YAAEG,MAAM,EAAE;UAAE,CAAC,CAAC;UAC1B,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,WAAW,SACdpB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIiB,WAAW,CAAC,CAAC;EAC1B;EAEAhB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAO,MAAM;MACX,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAEZ,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QACpE,CAAC;QACDM,aAAa,EAAE;UACbE,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAC,CAAC;UAC1B,GAAGH;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,WAAW,SACdrB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIkB,WAAW,CAAC,CAAC;EAC1B;EAEAjB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAO,MAAM;MACX,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CAAC;YAAEG,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC;QACpE,CAAC;QACDM,aAAa,EAAE;UACbE,SAAS,EAAE,CAAC;YAAEG,MAAM,EAAE;UAAE,CAAC,CAAC;UAC1B,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}