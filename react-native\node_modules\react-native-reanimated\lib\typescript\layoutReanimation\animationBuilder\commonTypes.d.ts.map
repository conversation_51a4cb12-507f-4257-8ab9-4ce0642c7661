{"version": 3, "file": "commonTypes.d.ts", "sourceRoot": "", "sources": ["../../../../src/layoutReanimation/animationBuilder/commonTypes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,YAAY,EACZ,UAAU,EACV,kBAAkB,EAClB,cAAc,EACf,MAAM,mBAAmB,CAAC;AAE3B,MAAM,MAAM,uBAAuB,GAC/B,SAAS,GACT,SAAS,GACT,OAAO,GACP,QAAQ,GACR,cAAc,GACd,eAAe,GACf,eAAe,CAAC;AAEpB,KAAK,6BAA6B,GAAG;KAClC,CAAC,IAAI,uBAAuB,IAAI,UAAU,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM;CAC7E,CAAC;AAEF,KAAK,4BAA4B,GAAG;KACjC,CAAC,IAAI,uBAAuB,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM;CAC5E,CAAC;AAEF,UAAU,gBAAgB;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,aAAc,SAAQ,UAAU;IAC/C,MAAM,CAAC,EAAE,cAAc,CAAC;CACzB;AAED,KAAK,UAAU,GACX;IACE,CAAC,EAAE,aAAa,GAAG;QAAE,MAAM,CAAC,EAAE,KAAK,CAAA;KAAE,CAAC;IACtC,IAAI,CAAC,EAAE,KAAK,CAAC;CACd,GACD;IACE,CAAC,CAAC,EAAE,KAAK,CAAC;IACV,IAAI,EAAE,aAAa,GAAG;QAAE,MAAM,CAAC,EAAE,KAAK,CAAA;KAAE,CAAC;CAC1C,CAAC;AAEN,KAAK,SAAS,GACV;IAAE,GAAG,CAAC,EAAE,aAAa,CAAC;IAAC,EAAE,CAAC,EAAE,KAAK,CAAA;CAAE,GACnC;IAAE,GAAG,CAAC,EAAE,KAAK,CAAC;IAAC,EAAE,EAAE,aAAa,CAAA;CAAE,CAAC;AAEvC,MAAM,MAAM,kBAAkB,GAAG,UAAU,GACzC,SAAS,GACT,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AAEhC,MAAM,MAAM,yBAAyB,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG;IACtE,EAAE,CAAC,EAAE,aAAa,CAAC;IACnB,IAAI,CAAC,EAAE,aAAa,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC5B,aAAa,EAAE,UAAU,CAAC;IAC1B,UAAU,EAAE,UAAU,CAAC;IACvB,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC;AAEnE,MAAM,MAAM,qBAAqB,GAAG,4BAA4B,GAC9D,gBAAgB,CAAC;AAEnB,MAAM,MAAM,oBAAoB,GAAG,6BAA6B,GAC9D,gBAAgB,CAAC;AAEnB,MAAM,MAAM,0BAA0B,GAClC,CAAC,CAAC,YAAY,EAAE,qBAAqB,KAAK,eAAe,CAAC,GAC1D,CAAC,CAAC,YAAY,EAAE,oBAAoB,KAAK,eAAe,CAAC,GACzD,CAAC,MAAM,eAAe,CAAC,CAAC;AAE5B,MAAM,MAAM,uBAAuB,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,eAAe,CAAC;AAE9E,MAAM,MAAM,sBAAsB,GAAG,6BAA6B,GAChE,4BAA4B,GAC5B,gBAAgB,CAAC;AAEnB,MAAM,WAAW,gCACf,SAAQ,sBAAsB;IAC9B,sBAAsB,EAAE,MAAM,EAAE,CAAC;IACjC,qBAAqB,EAAE,MAAM,EAAE,CAAC;CACjC;AAED,MAAM,MAAM,kCAAkC,GAAG,CAC/C,MAAM,EAAE,gCAAgC,KACrC,eAAe,CAAC;AAErB,oBAAY,mBAAmB;IAC7B,QAAQ,IAAI;IACZ,OAAO,IAAI;IACX,MAAM,IAAI;IACV,yBAAyB,IAAI;IAC7B,kCAAkC,IAAI;CACvC;AAED,MAAM,MAAM,uBAAuB,GAAG,CACpC,YAAY,EAAE,sBAAsB,KACjC,eAAe,CAAC;AAErB,MAAM,MAAM,4BAA4B,GAAG,CACzC,GAAG,EAAE,MAAM,EACX,IAAI,EAAE,mBAAmB,EACzB,UAAU,EAAE,OAAO,CAAC,gCAAgC,CAAC,EACrD,MAAM,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,gCAAgC,CAAC,KAAK,eAAe,KACxE,IAAI,CAAC;AAEV,MAAM,WAAW,uBAAuB;IACtC,KAAK,EAAE,MAAM,uBAAuB,CAAC;CACtC;AAED,MAAM,WAAW,yBAAyB;IACxC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,IAAI,CAAC,EAAE,iBAAiB,CAAC;IACzB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,yBAAyB,CAAC,EAAE,MAAM,CAAC;IACnC,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC7B;AAED,MAAM,WAAW,0BAA2B,SAAQ,yBAAyB;IAC3E,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;CAC1B;AAED,MAAM,MAAM,wBAAwB,GAAG;IACrC,iBAAiB;IACjB,0BAA0B;CAC3B,CAAC;AAEF,MAAM,WAAW,0BAA0B;IACzC,KAAK,EAAE,MAAM,0BAA0B,CAAC;CACzC;AAED,MAAM,WAAW,sBAAsB;IACrC,KAAK,EAAE,MAAM,uBAAuB,CAAC,qBAAqB,CAAC,CAAC;CAC7D;AAED,MAAM,WAAW,qBAAqB;IACpC,KAAK,EAAE,MAAM,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;CAC5D;AAED,MAAM,MAAM,yBAAyB,GAAG,CACtC,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,MAAM,KACb,IAAI,CAAC;AAEV,MAAM,MAAM,iBAAiB,GAAG,CAC9B,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,gCAAgC,EACxC,QAAQ,EAAE,MAAM,KACb,IAAI,CAAC;AAEV,MAAM,MAAM,uBAAuB,GAAG,CACpC,MAAM,EAAE,gCAAgC,EACxC,QAAQ,EAAE,MAAM,KACb,UAAU,CAAC;AAEhB;;;;GAIG;AACH,oBAAY,oBAAoB;IAC9B,SAAS,cAAc;IACvB,kBAAkB,sBAAsB;CACzC;AAED,MAAM,MAAM,yBAAyB,GACjC,qBAAqB,GACrB,oBAAoB,CAAC;AAEzB,MAAM,MAAM,4BAA4B,GAAG,UAAU,GAAG;IACtD,SAAS,CAAC,EAAE,kBAAkB,EAAE,CAAC;CAClC,CAAC;AAEF,MAAM,WAAW,wBAAwB;IACvC,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,mBAAmB,CAAC;IAC1B,MAAM,EACF,YAAY,CACR,QAAQ,GACR,uBAAuB,GACvB,kCAAkC,GAClC,yBAAyB,CAC5B,GACD,SAAS,CAAC;IACd,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC9B"}