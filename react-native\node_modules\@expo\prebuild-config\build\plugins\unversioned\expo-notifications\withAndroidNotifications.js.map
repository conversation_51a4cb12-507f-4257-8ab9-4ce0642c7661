{"version": 3, "file": "withAndroidNotifications.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fs", "_interopRequireDefault", "_path", "_withAndroidIcons", "e", "__esModule", "default", "Colors", "AndroidConfig", "addMetaDataItemToMainApplication", "getMainApplicationOrThrow", "removeMetaDataItemFromMainApplication", "Manifest", "BASELINE_PIXEL_SIZE", "META_DATA_NOTIFICATION_ICON", "exports", "META_DATA_NOTIFICATION_ICON_COLOR", "NOTIFICATION_ICON", "NOTIFICATION_ICON_RESOURCE", "NOTIFICATION_ICON_COLOR", "NOTIFICATION_ICON_COLOR_RESOURCE", "withNotificationIcons", "config", "withDangerousMod", "setNotificationIconAsync", "modRequest", "projectRoot", "withNotificationIconColor", "withAndroidColors", "modResults", "setNotificationIconColor", "withNotificationManifest", "withAndroidManifest", "setNotificationConfig", "getNotificationIcon", "notification", "icon", "getNotificationColor", "color", "writeNotificationIconImageFilesAsync", "removeNotificationIconImageFilesAsync", "manifest", "mainApplication", "colors", "assignColorValue", "name", "value", "Promise", "all", "Object", "values", "dpi<PERSON><PERSON><PERSON>", "map", "folderName", "scale", "drawableFolderName", "replace", "dpiFolderPath", "path", "resolve", "ANDROID_RES_PATH", "fs", "promises", "mkdir", "recursive", "iconSizePx", "resizedIcon", "generateImageAsync", "cacheType", "src", "width", "height", "resizeMode", "backgroundColor", "source", "writeFile", "Error", "rm", "force"], "sources": ["../../../../src/plugins/unversioned/expo-notifications/withAndroidNotifications.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  withAndroidColors,\n  withAndroidManifest,\n  withDangerousMod,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport { generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ANDROID_RES_PATH, dpiValues } from '../../icons/withAndroidIcons';\n\nconst { Colors } = AndroidConfig;\nconst {\n  addMetaDataItemToMainApplication,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} = AndroidConfig.Manifest;\n\ntype AndroidManifest = AndroidConfig.Manifest.AndroidManifest;\nconst BASELINE_PIXEL_SIZE = 24;\nexport const META_DATA_NOTIFICATION_ICON = 'expo.modules.notifications.default_notification_icon';\nexport const META_DATA_NOTIFICATION_ICON_COLOR =\n  'expo.modules.notifications.default_notification_color';\nexport const NOTIFICATION_ICON = 'notification_icon';\nexport const NOTIFICATION_ICON_RESOURCE = `@drawable/${NOTIFICATION_ICON}`;\nexport const NOTIFICATION_ICON_COLOR = 'notification_icon_color';\nexport const NOTIFICATION_ICON_COLOR_RESOURCE = `@color/${NOTIFICATION_ICON_COLOR}`;\n\nexport const withNotificationIcons: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await setNotificationIconAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport const withNotificationIconColor: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setNotificationIconColor(config, config.modResults);\n    return config;\n  });\n};\n\nexport const withNotificationManifest: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, (config) => {\n    config.modResults = setNotificationConfig(config, config.modResults);\n    return config;\n  });\n};\n\nexport function getNotificationIcon(config: ExpoConfig) {\n  return config.notification?.icon || null;\n}\n\nexport function getNotificationColor(config: ExpoConfig) {\n  return config.notification?.color || null;\n}\n\n/**\n * Applies configuration for expo-notifications, including\n * the notification icon and notification color.\n */\nexport async function setNotificationIconAsync(config: ExpoConfig, projectRoot: string) {\n  const icon = getNotificationIcon(config);\n  if (icon) {\n    await writeNotificationIconImageFilesAsync(icon, projectRoot);\n  } else {\n    await removeNotificationIconImageFilesAsync(projectRoot);\n  }\n}\n\nexport function setNotificationConfig(config: ExpoConfig, manifest: AndroidManifest) {\n  const icon = getNotificationIcon(config);\n  const color = getNotificationColor(config);\n  const mainApplication = getMainApplicationOrThrow(manifest);\n  if (icon) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DATA_NOTIFICATION_ICON,\n      NOTIFICATION_ICON_RESOURCE,\n      'resource'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_DATA_NOTIFICATION_ICON);\n  }\n  if (color) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DATA_NOTIFICATION_ICON_COLOR,\n      NOTIFICATION_ICON_COLOR_RESOURCE,\n      'resource'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_DATA_NOTIFICATION_ICON_COLOR);\n  }\n  return manifest;\n}\n\nexport function setNotificationIconColor(\n  config: ExpoConfig,\n  colors: AndroidConfig.Resources.ResourceXML\n) {\n  return Colors.assignColorValue(colors, {\n    name: NOTIFICATION_ICON_COLOR,\n    value: getNotificationColor(config),\n  });\n}\n\nasync function writeNotificationIconImageFilesAsync(icon: string, projectRoot: string) {\n  await Promise.all(\n    Object.values(dpiValues).map(async ({ folderName, scale }) => {\n      const drawableFolderName = folderName.replace('mipmap', 'drawable');\n      const dpiFolderPath = path.resolve(projectRoot, ANDROID_RES_PATH, drawableFolderName);\n      await fs.promises.mkdir(dpiFolderPath, { recursive: true });\n      const iconSizePx = BASELINE_PIXEL_SIZE * scale;\n\n      try {\n        const resizedIcon = (\n          await generateImageAsync(\n            { projectRoot, cacheType: 'android-notification' },\n            {\n              src: icon,\n              width: iconSizePx,\n              height: iconSizePx,\n              resizeMode: 'cover',\n              backgroundColor: 'transparent',\n            }\n          )\n        ).source;\n        await fs.promises.writeFile(\n          path.resolve(dpiFolderPath, NOTIFICATION_ICON + '.png'),\n          resizedIcon\n        );\n      } catch (e) {\n        throw new Error('Encountered an issue resizing Android notification icon: ' + e);\n      }\n    })\n  );\n}\n\nasync function removeNotificationIconImageFilesAsync(projectRoot: string) {\n  await Promise.all(\n    Object.values(dpiValues).map(async ({ folderName }) => {\n      const drawableFolderName = folderName.replace('mipmap', 'drawable');\n      const dpiFolderPath = path.resolve(projectRoot, ANDROID_RES_PATH, drawableFolderName);\n      await fs.promises.rm(path.resolve(dpiFolderPath, NOTIFICATION_ICON + '.png'), {\n        force: true,\n      });\n    })\n  );\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2E,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE3E,MAAM;EAAEG;AAAO,CAAC,GAAGC,8BAAa;AAChC,MAAM;EACJC,gCAAgC;EAChCC,yBAAyB;EACzBC;AACF,CAAC,GAAGH,8BAAa,CAACI,QAAQ;AAG1B,MAAMC,mBAAmB,GAAG,EAAE;AACvB,MAAMC,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,GAAG,sDAAsD;AAC1F,MAAME,iCAAiC,GAAAD,OAAA,CAAAC,iCAAA,GAC5C,uDAAuD;AAClD,MAAMC,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,GAAG,mBAAmB;AAC7C,MAAMC,0BAA0B,GAAAH,OAAA,CAAAG,0BAAA,GAAG,aAAaD,iBAAiB,EAAE;AACnE,MAAME,uBAAuB,GAAAJ,OAAA,CAAAI,uBAAA,GAAG,yBAAyB;AACzD,MAAMC,gCAAgC,GAAAL,OAAA,CAAAK,gCAAA,GAAG,UAAUD,uBAAuB,EAAE;AAE5E,MAAME,qBAAmC,GAAIC,MAAM,IAAK;EAC7D,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAME,wBAAwB,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IACrE,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAM,qBAAA,GAAAA,qBAAA;AAEK,MAAMM,yBAAuC,GAAIL,MAAM,IAAK;EACjE,OAAO,IAAAM,kCAAiB,EAACN,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACO,UAAU,GAAGC,wBAAwB,CAACR,MAAM,EAAEA,MAAM,CAACO,UAAU,CAAC;IACvE,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAY,yBAAA,GAAAA,yBAAA;AAEK,MAAMI,wBAAsC,GAAIT,MAAM,IAAK;EAChE,OAAO,IAAAU,oCAAmB,EAACV,MAAM,EAAGA,MAAM,IAAK;IAC7CA,MAAM,CAACO,UAAU,GAAGI,qBAAqB,CAACX,MAAM,EAAEA,MAAM,CAACO,UAAU,CAAC;IACpE,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAgB,wBAAA,GAAAA,wBAAA;AAEK,SAASG,mBAAmBA,CAACZ,MAAkB,EAAE;EACtD,OAAOA,MAAM,CAACa,YAAY,EAAEC,IAAI,IAAI,IAAI;AAC1C;AAEO,SAASC,oBAAoBA,CAACf,MAAkB,EAAE;EACvD,OAAOA,MAAM,CAACa,YAAY,EAAEG,KAAK,IAAI,IAAI;AAC3C;;AAEA;AACA;AACA;AACA;AACO,eAAed,wBAAwBA,CAACF,MAAkB,EAAEI,WAAmB,EAAE;EACtF,MAAMU,IAAI,GAAGF,mBAAmB,CAACZ,MAAM,CAAC;EACxC,IAAIc,IAAI,EAAE;IACR,MAAMG,oCAAoC,CAACH,IAAI,EAAEV,WAAW,CAAC;EAC/D,CAAC,MAAM;IACL,MAAMc,qCAAqC,CAACd,WAAW,CAAC;EAC1D;AACF;AAEO,SAASO,qBAAqBA,CAACX,MAAkB,EAAEmB,QAAyB,EAAE;EACnF,MAAML,IAAI,GAAGF,mBAAmB,CAACZ,MAAM,CAAC;EACxC,MAAMgB,KAAK,GAAGD,oBAAoB,CAACf,MAAM,CAAC;EAC1C,MAAMoB,eAAe,GAAGhC,yBAAyB,CAAC+B,QAAQ,CAAC;EAC3D,IAAIL,IAAI,EAAE;IACR3B,gCAAgC,CAC9BiC,eAAe,EACf5B,2BAA2B,EAC3BI,0BAA0B,EAC1B,UACF,CAAC;EACH,CAAC,MAAM;IACLP,qCAAqC,CAAC+B,eAAe,EAAE5B,2BAA2B,CAAC;EACrF;EACA,IAAIwB,KAAK,EAAE;IACT7B,gCAAgC,CAC9BiC,eAAe,EACf1B,iCAAiC,EACjCI,gCAAgC,EAChC,UACF,CAAC;EACH,CAAC,MAAM;IACLT,qCAAqC,CAAC+B,eAAe,EAAE1B,iCAAiC,CAAC;EAC3F;EACA,OAAOyB,QAAQ;AACjB;AAEO,SAASX,wBAAwBA,CACtCR,MAAkB,EAClBqB,MAA2C,EAC3C;EACA,OAAOpC,MAAM,CAACqC,gBAAgB,CAACD,MAAM,EAAE;IACrCE,IAAI,EAAE1B,uBAAuB;IAC7B2B,KAAK,EAAET,oBAAoB,CAACf,MAAM;EACpC,CAAC,CAAC;AACJ;AAEA,eAAeiB,oCAAoCA,CAACH,IAAY,EAAEV,WAAmB,EAAE;EACrF,MAAMqB,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,MAAM,CAACC,6BAAS,CAAC,CAACC,GAAG,CAAC,OAAO;IAAEC,UAAU;IAAEC;EAAM,CAAC,KAAK;IAC5D,MAAMC,kBAAkB,GAAGF,UAAU,CAACG,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;IACnE,MAAMC,aAAa,GAAGC,eAAI,CAACC,OAAO,CAACjC,WAAW,EAAEkC,oCAAgB,EAAEL,kBAAkB,CAAC;IACrF,MAAMM,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACN,aAAa,EAAE;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;IAC3D,MAAMC,UAAU,GAAGpD,mBAAmB,GAAGyC,KAAK;IAE9C,IAAI;MACF,MAAMY,WAAW,GAAG,CAClB,MAAM,IAAAC,gCAAkB,EACtB;QAAEzC,WAAW;QAAE0C,SAAS,EAAE;MAAuB,CAAC,EAClD;QACEC,GAAG,EAAEjC,IAAI;QACTkC,KAAK,EAAEL,UAAU;QACjBM,MAAM,EAAEN,UAAU;QAClBO,UAAU,EAAE,OAAO;QACnBC,eAAe,EAAE;MACnB,CACF,CAAC,EACDC,MAAM;MACR,MAAMb,aAAE,CAACC,QAAQ,CAACa,SAAS,CACzBjB,eAAI,CAACC,OAAO,CAACF,aAAa,EAAExC,iBAAiB,GAAG,MAAM,CAAC,EACvDiD,WACF,CAAC;IACH,CAAC,CAAC,OAAO9D,CAAC,EAAE;MACV,MAAM,IAAIwE,KAAK,CAAC,2DAA2D,GAAGxE,CAAC,CAAC;IAClF;EACF,CAAC,CACH,CAAC;AACH;AAEA,eAAeoC,qCAAqCA,CAACd,WAAmB,EAAE;EACxE,MAAMqB,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,MAAM,CAACC,6BAAS,CAAC,CAACC,GAAG,CAAC,OAAO;IAAEC;EAAW,CAAC,KAAK;IACrD,MAAME,kBAAkB,GAAGF,UAAU,CAACG,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;IACnE,MAAMC,aAAa,GAAGC,eAAI,CAACC,OAAO,CAACjC,WAAW,EAAEkC,oCAAgB,EAAEL,kBAAkB,CAAC;IACrF,MAAMM,aAAE,CAACC,QAAQ,CAACe,EAAE,CAACnB,eAAI,CAACC,OAAO,CAACF,aAAa,EAAExC,iBAAiB,GAAG,MAAM,CAAC,EAAE;MAC5E6D,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH", "ignoreList": []}