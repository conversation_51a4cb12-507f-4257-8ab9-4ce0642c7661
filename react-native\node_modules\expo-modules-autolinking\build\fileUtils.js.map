{"version": 3, "file": "fileUtils.js", "sourceRoot": "", "sources": ["../src/fileUtils.ts"], "names": [], "mappings": ";;;;;AAYA,0CAEC;AAKD,4DAoBC;AAKD,gEAmBC;AA/DD,2DAA6B;AAC7B,+BAAyC;AACzC,gDAAwB;AAOxB;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,IAAY;IAChD,OAAO,CAAC,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,KAAK,CAAC;AACpE,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAC5C,WAAmB,EACnB,YAA0B,EAC1B,OAAqB;IAErB,MAAM,UAAU,GAAG,WAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;IAClF,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1E,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjD,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAC9C,WAAmB,EACnB,YAA0B,EAC1B,OAAqB;IAErB,MAAM,UAAU,GAAG,WAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;IAClF,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1E,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjD,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import fs from 'fs/promises';\nimport { glob, GlobOptions } from 'glob';\nimport path from 'path';\n\n/**\n * A matching function that takes a file path and its contents and returns a string if it matches, or null otherwise.\n */\ntype MatchFunctor = (filePath: string, contents: Buffer) => string | null;\n\n/**\n * Check if the file exists.\n */\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await fs.stat(file).catch(() => null))?.isFile() ?? false;\n}\n\n/**\n * Search files that match the glob pattern and return all matches from the matchFunctor.\n */\nexport async function globMatchFunctorAllAsync(\n  globPattern: string,\n  matchFunctor: MatchFunctor,\n  options?: GlobOptions\n): Promise<string[]> {\n  const globStream = glob.stream(globPattern, { ...options, withFileTypes: false });\n  const cwd = options?.cwd !== undefined ? `${options.cwd}` : process.cwd();\n  const results: string[] = [];\n  for await (const file of globStream) {\n    let filePath = file.toString();\n    if (!path.isAbsolute(filePath)) {\n      filePath = path.resolve(cwd, filePath);\n    }\n    const contents = await fs.readFile(filePath);\n    const matched = matchFunctor(filePath, contents);\n    if (matched != null) {\n      results.push(matched);\n    }\n  }\n  return results;\n}\n\n/**\n * Search files that match the glob pattern and return the first match from the matchFunctor.\n */\nexport async function globMatchFunctorFirstAsync(\n  globPattern: string,\n  matchFunctor: MatchFunctor,\n  options?: GlobOptions\n): Promise<string | null> {\n  const globStream = glob.stream(globPattern, { ...options, withFileTypes: false });\n  const cwd = options?.cwd !== undefined ? `${options.cwd}` : process.cwd();\n  for await (const file of globStream) {\n    let filePath = file.toString();\n    if (!path.isAbsolute(filePath)) {\n      filePath = path.resolve(cwd, filePath);\n    }\n    const contents = await fs.readFile(filePath);\n    const matched = matchFunctor(filePath, contents);\n    if (matched != null) {\n      return matched;\n    }\n  }\n  return null;\n}\n"]}