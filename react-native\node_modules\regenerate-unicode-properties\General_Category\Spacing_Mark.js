const set = require('regenerate')(0x903, 0x93B, 0x9D7, 0xA03, 0xA83, 0xAC9, 0xB3E, 0xB40, 0xB57, 0xBD7, 0xCBE, 0xCF3, 0xD57, 0xF7F, 0x1031, 0x1038, 0x108F, 0x1715, 0x1734, 0x17B6, 0x1A55, 0x1A57, 0x1A61, 0x1B04, 0x1B35, 0x1B3B, 0x1B82, 0x1BA1, 0x1BAA, 0x1BE7, 0x1BEE, 0x1CE1, 0x1CF7, 0xA827, 0xA983, 0xAA4D, 0xAA7B, 0xAA7D, 0xAAEB, 0xAAF5, 0xABEC, 0x11000, 0x11002, 0x11082, 0x1112C, 0x11182, 0x111CE, 0x11235, 0x11357, 0x113C2, 0x113C5, 0x113CF, 0x11445, 0x114B9, 0x114C1, 0x115BE, 0x1163E, 0x116AC, 0x116B6, 0x1171E, 0x11726, 0x11838, 0x1193D, 0x11940, 0x11942, 0x119E4, 0x11A39, 0x11A97, 0x11C2F, 0x11C3E, 0x11CA9, 0x11CB1, 0x11CB4, 0x11D96, 0x11F03, 0x11F41);
set.addRange(0x93E, 0x940).addRange(0x949, 0x94C).addRange(0x94E, 0x94F).addRange(0x982, 0x983).addRange(0x9BE, 0x9C0).addRange(0x9C7, 0x9C8).addRange(0x9CB, 0x9CC).addRange(0xA3E, 0xA40).addRange(0xABE, 0xAC0).addRange(0xACB, 0xACC).addRange(0xB02, 0xB03).addRange(0xB47, 0xB48).addRange(0xB4B, 0xB4C).addRange(0xBBE, 0xBBF).addRange(0xBC1, 0xBC2).addRange(0xBC6, 0xBC8).addRange(0xBCA, 0xBCC).addRange(0xC01, 0xC03).addRange(0xC41, 0xC44).addRange(0xC82, 0xC83).addRange(0xCC0, 0xCC4).addRange(0xCC7, 0xCC8).addRange(0xCCA, 0xCCB).addRange(0xCD5, 0xCD6).addRange(0xD02, 0xD03).addRange(0xD3E, 0xD40).addRange(0xD46, 0xD48).addRange(0xD4A, 0xD4C).addRange(0xD82, 0xD83).addRange(0xDCF, 0xDD1).addRange(0xDD8, 0xDDF).addRange(0xDF2, 0xDF3).addRange(0xF3E, 0xF3F).addRange(0x102B, 0x102C).addRange(0x103B, 0x103C).addRange(0x1056, 0x1057).addRange(0x1062, 0x1064).addRange(0x1067, 0x106D).addRange(0x1083, 0x1084).addRange(0x1087, 0x108C).addRange(0x109A, 0x109C).addRange(0x17BE, 0x17C5).addRange(0x17C7, 0x17C8).addRange(0x1923, 0x1926).addRange(0x1929, 0x192B).addRange(0x1930, 0x1931).addRange(0x1933, 0x1938).addRange(0x1A19, 0x1A1A).addRange(0x1A63, 0x1A64).addRange(0x1A6D, 0x1A72).addRange(0x1B3D, 0x1B41);
set.addRange(0x1B43, 0x1B44).addRange(0x1BA6, 0x1BA7).addRange(0x1BEA, 0x1BEC).addRange(0x1BF2, 0x1BF3).addRange(0x1C24, 0x1C2B).addRange(0x1C34, 0x1C35).addRange(0x302E, 0x302F).addRange(0xA823, 0xA824).addRange(0xA880, 0xA881).addRange(0xA8B4, 0xA8C3).addRange(0xA952, 0xA953).addRange(0xA9B4, 0xA9B5).addRange(0xA9BA, 0xA9BB).addRange(0xA9BE, 0xA9C0).addRange(0xAA2F, 0xAA30).addRange(0xAA33, 0xAA34).addRange(0xAAEE, 0xAAEF).addRange(0xABE3, 0xABE4).addRange(0xABE6, 0xABE7).addRange(0xABE9, 0xABEA).addRange(0x110B0, 0x110B2).addRange(0x110B7, 0x110B8).addRange(0x11145, 0x11146).addRange(0x111B3, 0x111B5).addRange(0x111BF, 0x111C0).addRange(0x1122C, 0x1122E).addRange(0x11232, 0x11233).addRange(0x112E0, 0x112E2).addRange(0x11302, 0x11303).addRange(0x1133E, 0x1133F).addRange(0x11341, 0x11344).addRange(0x11347, 0x11348).addRange(0x1134B, 0x1134D).addRange(0x11362, 0x11363).addRange(0x113B8, 0x113BA).addRange(0x113C7, 0x113CA).addRange(0x113CC, 0x113CD).addRange(0x11435, 0x11437).addRange(0x11440, 0x11441).addRange(0x114B0, 0x114B2).addRange(0x114BB, 0x114BE).addRange(0x115AF, 0x115B1).addRange(0x115B8, 0x115BB).addRange(0x11630, 0x11632).addRange(0x1163B, 0x1163C).addRange(0x116AE, 0x116AF).addRange(0x11720, 0x11721).addRange(0x1182C, 0x1182E).addRange(0x11930, 0x11935).addRange(0x11937, 0x11938).addRange(0x119D1, 0x119D3);
set.addRange(0x119DC, 0x119DF).addRange(0x11A57, 0x11A58).addRange(0x11D8A, 0x11D8E).addRange(0x11D93, 0x11D94).addRange(0x11EF5, 0x11EF6).addRange(0x11F34, 0x11F35).addRange(0x11F3E, 0x11F3F).addRange(0x1612A, 0x1612C).addRange(0x16F51, 0x16F87).addRange(0x16FF0, 0x16FF1).addRange(0x1D165, 0x1D166).addRange(0x1D16D, 0x1D172);
exports.characters = set;
