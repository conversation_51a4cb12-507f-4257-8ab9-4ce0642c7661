{"version": 3, "file": "ReactNative78CompatPlugin.js", "names": ["_configPlugins", "data", "require", "_jsonFile", "_interopRequireDefault", "_resolveFrom", "_semver", "e", "__esModule", "default", "cachedIsTargetSdkVersion", "undefined", "withSdk52ReactNative78CompatAndroid", "config", "withSdk52ReactNative78CompatAndroidAppGradle", "withSdk52ReactNative78CompatAndroidProjectGradle", "exports", "withAppBuildGradle", "isTargetSdkVersionAsync", "modRequest", "projectRoot", "sdkVersion", "modResults", "contents", "replace", "withProjectBuildGradle", "reactNativeVersion", "queryReactNativeVersionAsync", "semver", "gte", "packageJsonPath", "resolveFrom", "silent", "packageJson", "JsonFile", "readAsync", "version", "parse"], "sources": ["../../../src/plugins/sdk52/ReactNative78CompatPlugin.ts"], "sourcesContent": ["import {\n  withAppBuildGradle,\n  withProjectBuildGradle,\n  type ConfigPlugin,\n} from '@expo/config-plugins';\nimport JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nlet cachedIsTargetSdkVersion: boolean | undefined = undefined;\n\n// TODO(kudo,20241210): Remove this plugin when we drop support for SDK 52.\nexport const withSdk52ReactNative78CompatAndroid: ConfigPlugin = (config) => {\n  config = withSdk52ReactNative78CompatAndroidAppGradle(config);\n  config = withSdk52ReactNative78CompatAndroidProjectGradle(config);\n  return config;\n};\n\nconst withSdk52ReactNative78CompatAndroidAppGradle: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, async (config) => {\n    if (!(await isTargetSdkVersionAsync(config.modRequest.projectRoot, config.sdkVersion))) {\n      return config;\n    }\n    config.modResults.contents = config.modResults.contents.replace(\n      /jscFlavor = ['\"]org\\.webkit:android-jsc(-intl)?:\\+['\"]/gm,\n      `jscFlavor = 'io.github.react-native-community:jsc-android$1:2026004.+'`\n    );\n    return config;\n  });\n};\n\nconst withSdk52ReactNative78CompatAndroidProjectGradle: ConfigPlugin = (config) => {\n  return withProjectBuildGradle(config, async (config) => {\n    if (!(await isTargetSdkVersionAsync(config.modRequest.projectRoot, config.sdkVersion))) {\n      return config;\n    }\n    config.modResults.contents = config.modResults.contents.replace(\n      /\\ndef jscAndroidDir = new File\\([\\s\\S]+?^\\)\\n/gm,\n      ''\n    );\n    config.modResults.contents = config.modResults.contents.replace(\n      /^\\s+maven \\{\\n\\s+\\/\\/ Android JSC.*\\n\\s+url\\(jscAndroidDir\\)[\\s\\S]+?\\}\\n/gm,\n      ''\n    );\n    return config;\n  });\n};\n\nasync function isTargetSdkVersionAsync(\n  projectRoot: string,\n  sdkVersion: string | undefined\n): Promise<boolean> {\n  if (cachedIsTargetSdkVersion !== undefined) {\n    return cachedIsTargetSdkVersion;\n  }\n\n  cachedIsTargetSdkVersion = false;\n  if (sdkVersion === '52.0.0') {\n    const reactNativeVersion = await queryReactNativeVersionAsync(projectRoot);\n    if (reactNativeVersion && semver.gte(reactNativeVersion, '0.78.0')) {\n      cachedIsTargetSdkVersion = true;\n    }\n  }\n  return cachedIsTargetSdkVersion;\n}\n\nasync function queryReactNativeVersionAsync(projectRoot: string): Promise<semver.SemVer | null> {\n  const packageJsonPath = resolveFrom.silent(projectRoot, 'react-native/package.json');\n  if (packageJsonPath) {\n    const packageJson = await JsonFile.readAsync(packageJsonPath);\n    const version = packageJson.version;\n    if (typeof version === 'string') {\n      return semver.parse(version);\n    }\n  }\n  return null;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAG,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5B,IAAIG,wBAA6C,GAAGC,SAAS;;AAE7D;AACO,MAAMC,mCAAiD,GAAIC,MAAM,IAAK;EAC3EA,MAAM,GAAGC,4CAA4C,CAACD,MAAM,CAAC;EAC7DA,MAAM,GAAGE,gDAAgD,CAACF,MAAM,CAAC;EACjE,OAAOA,MAAM;AACf,CAAC;AAACG,OAAA,CAAAJ,mCAAA,GAAAA,mCAAA;AAEF,MAAME,4CAA0D,GAAID,MAAM,IAAK;EAC7E,OAAO,IAAAI,mCAAkB,EAACJ,MAAM,EAAE,MAAOA,MAAM,IAAK;IAClD,IAAI,EAAE,MAAMK,uBAAuB,CAACL,MAAM,CAACM,UAAU,CAACC,WAAW,EAAEP,MAAM,CAACQ,UAAU,CAAC,CAAC,EAAE;MACtF,OAAOR,MAAM;IACf;IACAA,MAAM,CAACS,UAAU,CAACC,QAAQ,GAAGV,MAAM,CAACS,UAAU,CAACC,QAAQ,CAACC,OAAO,CAC7D,0DAA0D,EAC1D,wEACF,CAAC;IACD,OAAOX,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAME,gDAA8D,GAAIF,MAAM,IAAK;EACjF,OAAO,IAAAY,uCAAsB,EAACZ,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtD,IAAI,EAAE,MAAMK,uBAAuB,CAACL,MAAM,CAACM,UAAU,CAACC,WAAW,EAAEP,MAAM,CAACQ,UAAU,CAAC,CAAC,EAAE;MACtF,OAAOR,MAAM;IACf;IACAA,MAAM,CAACS,UAAU,CAACC,QAAQ,GAAGV,MAAM,CAACS,UAAU,CAACC,QAAQ,CAACC,OAAO,CAC7D,iDAAiD,EACjD,EACF,CAAC;IACDX,MAAM,CAACS,UAAU,CAACC,QAAQ,GAAGV,MAAM,CAACS,UAAU,CAACC,QAAQ,CAACC,OAAO,CAC7D,4EAA4E,EAC5E,EACF,CAAC;IACD,OAAOX,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,eAAeK,uBAAuBA,CACpCE,WAAmB,EACnBC,UAA8B,EACZ;EAClB,IAAIX,wBAAwB,KAAKC,SAAS,EAAE;IAC1C,OAAOD,wBAAwB;EACjC;EAEAA,wBAAwB,GAAG,KAAK;EAChC,IAAIW,UAAU,KAAK,QAAQ,EAAE;IAC3B,MAAMK,kBAAkB,GAAG,MAAMC,4BAA4B,CAACP,WAAW,CAAC;IAC1E,IAAIM,kBAAkB,IAAIE,iBAAM,CAACC,GAAG,CAACH,kBAAkB,EAAE,QAAQ,CAAC,EAAE;MAClEhB,wBAAwB,GAAG,IAAI;IACjC;EACF;EACA,OAAOA,wBAAwB;AACjC;AAEA,eAAeiB,4BAA4BA,CAACP,WAAmB,EAAiC;EAC9F,MAAMU,eAAe,GAAGC,sBAAW,CAACC,MAAM,CAACZ,WAAW,EAAE,2BAA2B,CAAC;EACpF,IAAIU,eAAe,EAAE;IACnB,MAAMG,WAAW,GAAG,MAAMC,mBAAQ,CAACC,SAAS,CAACL,eAAe,CAAC;IAC7D,MAAMM,OAAO,GAAGH,WAAW,CAACG,OAAO;IACnC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,OAAOR,iBAAM,CAACS,KAAK,CAACD,OAAO,CAAC;IAC9B;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}