import*as e from"../../../core/sdk/sdk.js";import*as t from"../types/types.js";import*as n from"../../../core/platform/platform.js";import*as r from"../helpers/helpers.js";const a=new Map,o=new Map;async function i(t,n){const r=a.get(t)?.get(n);if(void 0!==r)return r;const o=e.TargetManager.TargetManager.instance().primaryPageTarget(),i=o?.model(e.DOMModel.DOMModel);if(!i)return null;const s=await i.pushNodesByBackendIdsToFrontend(new Set([n])),c=s?.get(n)||null,l=a.get(t)||new Map;return l.set(n,c),a.set(t,l),c}async function s(t,n){const r=o.get(t)?.get(n);if(r)return r;const a=e.TargetManager.TargetManager.instance().primaryPageTarget(),i=a?.model(e.DOMModel.DOMModel);if(!i)return new Map;const s=await i.pushNodesByBackendIdsToFrontend(new Set(n))||new Map,c=o.get(t)||new Map;return c.set(n,s),o.set(t,c),s}const c=new Map,l=new Map;var d=Object.freeze({__proto__:null,_TEST_clearCache:function(){a.clear(),o.clear(),c.clear(),l.clear()},domNodeForBackendNodeID:i,extractRelatedDOMNodesFromEvent:async function(e,n){return t.TraceEvents.isSyntheticLayoutShift(n)&&n.args.data?.impacted_nodes?s(e,n.args.data.impacted_nodes.map((e=>e.node_id))):null},domNodesForMultipleBackendNodeIds:s,sourcesForLayoutShift:async function(e,t){const n=c.get(e)?.get(t);if(n)return n;const r=t.args.data?.impacted_nodes;if(!r)return[];const a=[];await Promise.all(r.map((async t=>{const n=await i(e,t.node_id);n&&a.push({previousRect:new DOMRect(t.old_rect[0],t.old_rect[1],t.old_rect[2],t.old_rect[3]),currentRect:new DOMRect(t.new_rect[0],t.new_rect[1],t.new_rect[2],t.new_rect[3]),node:n})})));const o=c.get(e)||new Map;return o.set(t,a),c.set(e,o),a},normalizedImpactedNodesForLayoutShift:async function(t,n){const r=l.get(t)?.get(n);if(r)return r;const a=n.args?.data?.impacted_nodes;if(!a)return[];let o=null;const i=e.TargetManager.TargetManager.instance().primaryPageTarget(),s=await(i?.runtimeAgent().invoke_evaluate({expression:"window.devicePixelRatio"}));if("number"===s?.result.type&&(o=s?.result.value??null),!o)return a;const c=[];for(const e of a){const t={...e};for(let n=0;n<e.old_rect.length;n++)t.old_rect[n]/=o;for(let n=0;n<e.new_rect.length;n++)t.new_rect[n]/=o;c.push(t)}const d=l.get(t)||new Map;return d.set(n,c),l.set(t,d),c}});const g=new Map;var u=Object.freeze({__proto__:null,fromTraceData:function(e,t){const r=[],a=void 0!==t?t:e.Meta.traceBounds.min,o=e.Meta.traceBounds.range,i=g.get(e)?.get(a);if(i)return i;for(const t of e.Screenshots){if(t.ts<a)continue;const e={index:r.length,screenshotEvent:t};r.push(e)}const s={zeroTime:a,spanTime:o,frames:Array.from(r)};return n.MapUtilities.getWithDefault(g,e,(()=>new Map)).set(a,s),s},frameClosestToTimestamp:function(e,t){const r=n.ArrayUtilities.nearestIndexFromEnd(e.frames,(e=>e.screenshotEvent.ts<t));return null===r?null:e.frames[r]}});const m=new Set(["(program)","(idle)","(root)"]);var f=Object.freeze({__proto__:null,calculateWindow:function(e,n){if(!n.length)return e;const a=n.filter((e=>!(t.TraceEvents.isProfileCall(e)&&(m.has(e.callFrame.functionName)||!e.callFrame.functionName))));if(0===a.length)return e;function o(e,t){let n=e;const o=a[n],i=r.Timing.eventTimingsMicroSeconds(o);let s=(i.startTime+i.endTime)/2,c=0;const l=Math.sign(t-e);for(let o=e;o!==t;o+=l){const e=a[o],t=r.Timing.eventTimingsMicroSeconds(e),i=(t.startTime+t.endTime)/2;c<.1*Math.abs(s-i)&&(n=o,s=i,c=0),c+=t.duration}return n}const i=o(a.length-1,0),s=o(0,i),c=r.Timing.eventTimingsMicroSeconds(a[s]),l=r.Timing.eventTimingsMicroSeconds(a[i]);let d=c.startTime,g=l.endTime;const u=g-d;return u<.1*e.range?e:(d=t.Timing.MicroSeconds(Math.max(d-.05*u,e.min)),g=t.Timing.MicroSeconds(Math.min(g+.05*u,e.max)),{min:d,max:g,range:t.Timing.MicroSeconds(g-d)})}});var M=Object.freeze({__proto__:null,forNewRecording:async function(t){try{const n=e.CPUThrottlingManager.CPUThrottlingManager.instance().hasPrimaryPageTargetSet()?await Promise.race([e.CPUThrottlingManager.CPUThrottlingManager.instance().getHardwareConcurrency(),new Promise((e=>{setTimeout((()=>e(void 0)),1e3)}))]):void 0,r=e.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate(),a=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),o="function"==typeof a.title?a.title():a.title;return{source:"DevTools",startTime:t?new Date(t).toJSON():void 0,cpuThrottling:r,networkThrottling:o,hardwareConcurrency:n}}catch{return{}}}});export{d as FetchNodes,u as FilmStrip,f as MainThreadActivity,M as Metadata};
