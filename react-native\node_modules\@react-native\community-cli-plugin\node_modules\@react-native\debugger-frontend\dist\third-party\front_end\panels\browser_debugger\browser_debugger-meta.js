import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";const r={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},n=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",r),a=e.i18n.getLazilyComputedLocalizedString.bind(void 0,n);let o,s;async function c(){return o||(o=await import("./browser_debugger.js")),o}async function d(){return s||(s=await import("../sources/sources.js")),s}i.ViewManager.registerViewExtension({loadView:async()=>(await c()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(r.showEventListenerBreakpoints),title:a(r.eventListenerBreakpoints),order:9,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await c()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(r.showCspViolationBreakpoints),title:a(r.cspViolationBreakpoints),order:10,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>(await c()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(r.showXhrfetchBreakpoints),title:a(r.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await c()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(r.showDomBreakpoints),title:a(r.domBreakpoints),order:7,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await c()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:a(r.showGlobalListeners),title:a(r.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await c()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:a(r.showDomBreakpoints),title:a(r.domBreakpoints),order:6,persistence:"permanent"}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:a(r.page),commandPrompt:a(r.showPage),order:2,persistence:"permanent",loadView:async()=>(await d()).SourcesNavigator.NetworkNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:a(r.overrides),commandPrompt:a(r.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await d()).SourcesNavigator.OverridesNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:a(r.contentScripts),commandPrompt:a(r.showContentScripts),order:5,persistence:"permanent",loadView:async()=>new((await d()).SourcesNavigator.ContentScriptsNavigatorView)}),i.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await c()).ObjectEventListenersSidebarPane.ActionDelegate),title:a(r.refreshGlobalListeners),iconClass:"refresh",contextTypes(){return e=e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane],void 0===o?[]:e(o);var e}}),i.ContextMenu.registerProvider({contextTypes:()=>[t.DOMModel.DOMNode],loadProvider:async()=>new((await c()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),i.Context.registerListener({contextTypes:()=>[t.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await c()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),i.Context.registerListener({contextTypes:()=>[t.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await c()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});
