{"version": 3, "names": ["BaseRouter", "getStateForAction", "state", "action", "type", "index", "source", "routes", "findIndex", "r", "key", "map", "i", "params", "payload", "nextState", "length", "some", "route", "routeNames", "includes", "name", "stale", "nanoid", "shouldActionChangeFocus"], "sourceRoot": "../../src", "sources": ["BaseRouter.tsx"], "mappings": ";;;;;;AAAA;AAQA;AACA;AACA;AACA;AACA,MAAMA,UAAU,GAAG;EACjBC,iBAAiB,CACfC,KAAY,EACZC,MAA8B,EACM;IACpC,QAAQA,MAAM,CAACC,IAAI;MACjB,KAAK,YAAY;QAAE;UACjB,MAAMC,KAAK,GAAGF,MAAM,CAACG,MAAM,GACvBJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKP,MAAM,CAACG,MAAM,CAAC,GACtDJ,KAAK,CAACG,KAAK;UAEf,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,IAAI;UACb;UAEA,OAAO;YACL,GAAGH,KAAK;YACRK,MAAM,EAAEL,KAAK,CAACK,MAAM,CAACI,GAAG,CAAC,CAACF,CAAC,EAAEG,CAAC,KAC5BA,CAAC,KAAKP,KAAK,GACP;cAAE,GAAGI,CAAC;cAAEI,MAAM,EAAE;gBAAE,GAAGJ,CAAC,CAACI,MAAM;gBAAE,GAAGV,MAAM,CAACW,OAAO,CAACD;cAAO;YAAE,CAAC,GAC3DJ,CAAC;UAET,CAAC;QACH;MAEA,KAAK,OAAO;QAAE;UACZ,MAAMM,SAAS,GAAGZ,MAAM,CAACW,OAAsC;UAE/D,IACEC,SAAS,CAACR,MAAM,CAACS,MAAM,KAAK,CAAC,IAC7BD,SAAS,CAACR,MAAM,CAACU,IAAI,CAClBC,KAAuB,IAAK,CAAChB,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACF,KAAK,CAACG,IAAI,CAAC,CACpE,EACD;YACA,OAAO,IAAI;UACb;UAEA,IAAIN,SAAS,CAACO,KAAK,KAAK,KAAK,EAAE;YAC7B,IACEpB,KAAK,CAACiB,UAAU,CAACH,MAAM,KAAKD,SAAS,CAACI,UAAU,CAACH,MAAM,IACvDD,SAAS,CAACI,UAAU,CAACF,IAAI,CACtBI,IAAI,IAAK,CAACnB,KAAK,CAACiB,UAAU,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC3C,EACD;cACA,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGN,SAAS;cACZR,MAAM,EAAEQ,SAAS,CAACR,MAAM,CAACI,GAAG,CAAEO,KAAK,IACjCA,KAAK,CAACR,GAAG,GAAGQ,KAAK,GAAG;gBAAE,GAAGA,KAAK;gBAAER,GAAG,EAAG,GAAEQ,KAAK,CAACG,IAAK,IAAG,IAAAE,iBAAM,GAAG;cAAE,CAAC;YAEtE,CAAC;UACH;UAEA,OAAOR,SAAS;QAClB;MAEA;QACE,OAAO,IAAI;IAAC;EAElB,CAAC;EAEDS,uBAAuB,CAACrB,MAA8B,EAAE;IACtD,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU;EACnC;AACF,CAAC;AAAC,eAEaJ,UAAU;AAAA"}