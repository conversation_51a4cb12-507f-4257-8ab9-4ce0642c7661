import React, { useState, useEffect } from 'react';
import './SaisieClientQR.css';

const SaisieClientQR = ({ onBack }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    adresse: '',
    ville: '',
    tel: '',
    email: '',
    ids: '',
    marqueCompteur: '',
    numSerieCompteur: '',
    posX: '',
    posY: ''
  });

  const [secteurs, setSecteurs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [generatedQR, setGeneratedQR] = useState(null);

  // Charger les secteurs
  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    try {
      const response = await fetch('http://localhost:3002/api/table/secteur');
      const data = await response.json();
      if (data.success) {
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des secteurs:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('http://localhost:3002/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(`Client ajouté avec succès ! QR Code généré: ${data.data.qrCode}`);
        setGeneratedQR(data.data);
        
        // Réinitialiser le formulaire
        setFormData({
          nom: '',
          prenom: '',
          adresse: '',
          ville: '',
          tel: '',
          email: '',
          ids: '',
          marqueCompteur: '',
          numSerieCompteur: '',
          posX: '',
          posY: ''
        });
      } else {
        setError(data.message || 'Erreur lors de l\'ajout du client');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="saisie-client-qr-container">
      <div className="saisie-client-qr-header">
        <button className="back-button" onClick={onBack}>
          ← Retour
        </button>
        <h1>Nouveau Client avec QR Code</h1>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      {success && (
        <div className="success-message">
          ✅ {success}
        </div>
      )}

      {generatedQR && (
        <div className="qr-result">
          <h3>🎉 Client créé avec succès !</h3>
          <div className="qr-info">
            <p><strong>Client:</strong> {generatedQR.client.nom} {generatedQR.client.prenom}</p>
            <p><strong>QR Code:</strong> <code>{generatedQR.qrCode}</code></p>
            <p><strong>ID Contrat:</strong> {generatedQR.contract.idcontract}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="saisie-client-form">
        <div className="form-section">
          <h3>📋 Informations Client</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Nom *</label>
              <input
                type="text"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                required
                placeholder="Nom du client"
              />
            </div>
            
            <div className="form-group">
              <label>Prénom *</label>
              <input
                type="text"
                name="prenom"
                value={formData.prenom}
                onChange={handleInputChange}
                required
                placeholder="Prénom du client"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Adresse *</label>
            <input
              type="text"
              name="adresse"
              value={formData.adresse}
              onChange={handleInputChange}
              required
              placeholder="Adresse complète"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Ville</label>
              <input
                type="text"
                name="ville"
                value={formData.ville}
                onChange={handleInputChange}
                placeholder="Ville"
              />
            </div>
            
            <div className="form-group">
              <label>Téléphone</label>
              <input
                type="tel"
                name="tel"
                value={formData.tel}
                onChange={handleInputChange}
                placeholder="Numéro de téléphone"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Email</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Adresse email"
              />
            </div>
            
            <div className="form-group">
              <label>Secteur</label>
              <select
                name="ids"
                value={formData.ids}
                onChange={handleInputChange}
              >
                <option value="">Sélectionner un secteur</option>
                {secteurs.map(secteur => (
                  <option key={secteur.ids} value={secteur.ids}>
                    {secteur.nom}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>🔧 Informations Compteur (Optionnel)</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Marque Compteur</label>
              <input
                type="text"
                name="marqueCompteur"
                value={formData.marqueCompteur}
                onChange={handleInputChange}
                placeholder="Marque du compteur"
              />
            </div>
            
            <div className="form-group">
              <label>Numéro de Série</label>
              <input
                type="text"
                name="numSerieCompteur"
                value={formData.numSerieCompteur}
                onChange={handleInputChange}
                placeholder="Numéro de série"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Position X</label>
              <input
                type="text"
                name="posX"
                value={formData.posX}
                onChange={handleInputChange}
                placeholder="Coordonnée X"
              />
            </div>
            
            <div className="form-group">
              <label>Position Y</label>
              <input
                type="text"
                name="posY"
                value={formData.posY}
                onChange={handleInputChange}
                placeholder="Coordonnée Y"
              />
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            disabled={loading}
            className="submit-button"
          >
            {loading ? '⏳ Enregistrement...' : '💾 Enregistrer Client + QR Code'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SaisieClientQR;
