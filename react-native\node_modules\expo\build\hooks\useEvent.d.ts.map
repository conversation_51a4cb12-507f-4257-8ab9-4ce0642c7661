{"version": 3, "file": "useEvent.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useEvent.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAG5D,KAAK,gBAAgB,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC;AAEhD;;GAEG;AACH,KAAK,cAAc,CAAC,UAAU,IAC5B,UAAU,SAAS,MAAM,CAAC,MAAM,UAAU,SAAS,MAAM,UAAU,EAAE,gBAAgB,CAAC,GAClF,UAAU,GACV,KAAK,CAAC;AAEZ;;GAEG;AACH,KAAK,kBAAkB,CAAC,UAAU,EAAE,UAAU,SAAS,MAAM,UAAU,IACrE,UAAU,SAAS,MAAM,CAAC,UAAU,EAAE,MAAM,cAAc,SAAS,gBAAgB,CAAC,GAChF,cAAc,GACd,KAAK,CAAC;AAEZ;;GAEG;AACH,KAAK,mBAAmB,CACtB,cAAc,SAAS,gBAAgB,EACvC,aAAa,IACX,aAAa,SAAS,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GACnD,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAC7B,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,IAAI,CAAC;AAEzD;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,QAAQ,CACtB,UAAU,SAAS,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,EACnD,UAAU,SAAS,cAAc,CAAC,UAAU,CAAC,EAC7C,cAAc,SAAS,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,EACjE,aAAa,SAAS,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAE1D,YAAY,EAAE,YAAY,CAAC,UAAU,CAAC,EACtC,SAAS,EAAE,UAAU,EACrB,YAAY,GAAE,aAAa,GAAG,IAAW,GACxC,mBAAmB,CAAC,cAAc,EAAE,aAAa,CAAC,CASpD;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAgB,gBAAgB,CAC9B,UAAU,SAAS,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,EACnD,UAAU,SAAS,cAAc,CAAC,UAAU,CAAC,EAC7C,cAAc,SAAS,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,EACjE,YAAY,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,GAAG,IAAI,CAe/F"}