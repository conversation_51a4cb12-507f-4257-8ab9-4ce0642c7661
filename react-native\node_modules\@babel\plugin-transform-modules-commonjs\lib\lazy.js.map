{"version": 3, "names": ["_core", "require", "_helperModuleTransforms", "lazyImportsHook", "lazy", "name", "version", "getWrapperPayload", "source", "metadata", "isSideEffectImport", "reexportAll", "includes", "Array", "isArray", "buildRequireWrapper", "init", "payload", "referenced", "template", "statement", "ast", "wrapReference", "ref", "t", "callExpression", "exports"], "sources": ["../src/lazy.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport { isSideEffectImport } from \"@babel/helper-module-transforms\";\nimport type { CommonJSHook } from \"./hooks.ts\";\n\ntype Lazy = boolean | string[] | ((source: string) => boolean);\n\nexport const lazyImportsHook = (lazy: Lazy): CommonJSHook => ({\n  name: `${PACKAGE_JSON.name}/lazy`,\n  version: PACKAGE_JSON.version,\n  getWrapperPayload(source, metadata) {\n    if (isSideEffectImport(metadata) || metadata.reexportAll) {\n      return null;\n    }\n    if (lazy === true) {\n      // 'true' means that local relative files are eagerly loaded and\n      // dependency modules are loaded lazily.\n      return source.includes(\".\") ? null : \"lazy/function\";\n    }\n    if (Array.isArray(lazy)) {\n      return !lazy.includes(source) ? null : \"lazy/function\";\n    }\n    if (typeof lazy === \"function\") {\n      return lazy(source) ? \"lazy/function\" : null;\n    }\n  },\n  buildRequireWrapper(name, init, payload, referenced) {\n    if (payload === \"lazy/function\") {\n      if (!referenced) return false;\n      return template.statement.ast`\n        function ${name}() {\n          const data = ${init};\n          ${name} = function(){ return data; };\n          return data;\n        }\n      `;\n    }\n  },\n  wrapReference(ref, payload) {\n    if (payload === \"lazy/function\") return t.callExpression(ref, []);\n  },\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAKO,MAAME,eAAe,GAAIC,IAAU,KAAoB;EAC5DC,IAAI,EAAE,oDAA2B;EACjCC,OAAO,UAAsB;EAC7BC,iBAAiBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAClC,IAAI,IAAAC,0CAAkB,EAACD,QAAQ,CAAC,IAAIA,QAAQ,CAACE,WAAW,EAAE;MACxD,OAAO,IAAI;IACb;IACA,IAAIP,IAAI,KAAK,IAAI,EAAE;MAGjB,OAAOI,MAAM,CAACI,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,eAAe;IACtD;IACA,IAAIC,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,EAAE;MACvB,OAAO,CAACA,IAAI,CAACQ,QAAQ,CAACJ,MAAM,CAAC,GAAG,IAAI,GAAG,eAAe;IACxD;IACA,IAAI,OAAOJ,IAAI,KAAK,UAAU,EAAE;MAC9B,OAAOA,IAAI,CAACI,MAAM,CAAC,GAAG,eAAe,GAAG,IAAI;IAC9C;EACF,CAAC;EACDO,mBAAmBA,CAACV,IAAI,EAAEW,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAE;IACnD,IAAID,OAAO,KAAK,eAAe,EAAE;MAC/B,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAOC,cAAQ,CAACC,SAAS,CAACC,GAAG;AACnC,mBAAmBhB,IAAI;AACvB,yBAAyBW,IAAI;AAC7B,YAAYX,IAAI;AAChB;AACA;AACA,OAAO;IACH;EACF,CAAC;EACDiB,aAAaA,CAACC,GAAG,EAAEN,OAAO,EAAE;IAC1B,IAAIA,OAAO,KAAK,eAAe,EAAE,OAAOO,WAAC,CAACC,cAAc,CAACF,GAAG,EAAE,EAAE,CAAC;EACnE;AACF,CAAC,CAAC;AAACG,OAAA,CAAAvB,eAAA,GAAAA,eAAA", "ignoreList": []}