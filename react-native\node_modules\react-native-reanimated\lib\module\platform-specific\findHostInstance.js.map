{"version": 3, "names": ["ReanimatedError", "isF<PERSON><PERSON>", "findHostInstanceFastPath", "maybeNativeRef", "undefined", "__internalInstanceHandle", "__nativeTag", "_viewConfig", "_nativeTag", "viewConfig", "resolveFindHostInstance_DEPRECATED", "findHostInstance_DEPRECATED", "ReactFabric", "require", "default", "e", "ReactNative", "findHostInstance", "component", "hostInstance", "_componentRef"], "sourceRoot": "../../../src", "sources": ["platform-specific/findHostInstance.ts"], "mappings": "AAAA;AACA,YAAY;;AAGZ,SAASA,eAAe,QAAQ,cAAW;AAC3C,SAASC,QAAQ,QAAQ,uBAAoB;AAe7C,SAASC,wBAAwBA,CAACC,cAAwC,EAAE;EAC1E,IAAI,CAACA,cAAc,EAAE;IACnB,OAAOC,SAAS;EAClB;EACA,IACED,cAAc,CAACE,wBAAwB,IACvCF,cAAc,CAACG,WAAW,IAC1BH,cAAc,CAACI,WAAW,EAC1B;IACA;IACA,OAAOJ,cAAc;EACvB;EACA,IAAIA,cAAc,CAACK,UAAU,IAAIL,cAAc,CAACM,UAAU,EAAE;IAC1D;IACA,OAAON,cAAc;EACvB;EACA;EACA;EACA,OAAOC,SAAS;AAClB;AAEA,SAASM,kCAAkCA,CAAA,EAAG;EAC5C,IAAIC,2BAA2B,KAAKP,SAAS,EAAE;IAC7C;EACF;EACA,IAAIH,QAAQ,CAAC,CAAC,EAAE;IACd,IAAI;MACF,MAAMW,WAAW,GAAGC,OAAO,CAAC,mDAAmD,CAAC;MAChF;MACA;MACAF,2BAA2B,GACzBC,WAAW,EAAEE,OAAO,EAAEH,2BAA2B,IACjDC,WAAW,EAAED,2BAA2B;IAC5C,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,MAAM,IAAIf,eAAe,CACvB,+CACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,MAAMgB,WAAW,GAAGH,OAAO,CAAC,mDAAmD,CAAC;IAChF;IACA;IACAF,2BAA2B,GACzBK,WAAW,EAAEF,OAAO,EAAEH,2BAA2B,IACjDK,WAAW,EAAEL,2BAA2B;EAC5C;AACF;AAEA,IAAIA,2BAA2D;AAC/D,OAAO,SAASM,gBAAgBA,CAC9BC,SAAuD,EACzC;EACd;EACA,MAAMC,YAAY,GAAGjB,wBAAwB,CAC1CgB,SAAS,CAAgCE,aAC5C,CAAC;EACD,IAAID,YAAY,KAAKf,SAAS,EAAE;IAC9B,OAAOe,YAAY;EACrB;EAEAT,kCAAkC,CAAC,CAAC;EACpC;EACA,OAAOC,2BAA2B,CAChCV,QAAQ,CAAC,CAAC,GACNiB,SAAS,GACRA,SAAS,CAAgCE,aAChD,CAAC;AACH", "ignoreList": []}