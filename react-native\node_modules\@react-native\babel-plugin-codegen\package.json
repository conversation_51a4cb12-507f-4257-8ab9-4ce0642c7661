{"name": "@react-native/babel-plugin-codegen", "version": "0.79.5", "description": "Babel plugin to generate native module and view manager code for React Native.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/babel-plugin-codegen"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/babel-plugin-codegen#readme", "keywords": ["babel", "plugin", "codegen", "react-native", "native-modules", "view-manager"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "files": ["index.js"], "dependencies": {"@babel/traverse": "^7.25.3", "@react-native/codegen": "0.79.5"}, "devDependencies": {"@babel/core": "^7.25.2"}}