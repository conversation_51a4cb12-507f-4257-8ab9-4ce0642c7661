# AquaTrack Mobile - Application React Native

Application mobile React Native pour la gestion des relevés de consommation d'eau et la facturation.

## 🚀 Fonctionnalités

### 🔐 Authentification
- Connexion sécurisée pour les techniciens
- Validation des rôles utilisateur
- Gestion de session

### 📱 Tableau de Bord
- Vue d'ensemble des statistiques
- Navigation intuitive vers toutes les fonctionnalités
- Affichage des informations utilisateur

### 👥 Gestion des Clients
- Liste complète des clients avec recherche
- Affichage des informations détaillées
- Navigation rapide vers les actions

### 💧 Saisie de Consommation
- Sélection de client avec modal
- Chargement automatique de la consommation précédente
- Validation des données saisies
- Calcul automatique du nombre de jours

### 📄 Gestion des Factures
- Liste des factures avec filtres
- Téléchargement et partage de PDF
- Changement de statut (payée/non payée)
- Statistiques en temps réel

### 📱 Scanner QR Code
- Scan en temps réel avec la caméra
- Reconnaissance automatique des compteurs
- Interface utilisateur intuitive
- Gestion des permissions caméra

### 🗺️ Localisation
- Carte interactive avec Google Maps
- Affichage des compteurs par secteur
- Géolocalisation de l'utilisateur
- Marqueurs personnalisés pour les compteurs

### 👤 Profil Utilisateur
- Gestion des informations personnelles
- Édition du profil
- Historique et statistiques
- Déconnexion sécurisée

## 🛠️ Installation

### Prérequis
- Node.js (version 16 ou supérieure)
- Expo CLI
- Android Studio (pour Android) ou Xcode (pour iOS)

### Étapes d'installation

1. **Installer Expo CLI globalement**
```bash
npm install -g expo-cli
```

2. **Naviguer vers le dossier React Native**
```bash
cd react-native
```

3. **Installer les dépendances**
```bash
npm install
```

4. **Démarrer le serveur de développement**
```bash
npm start
```

## 📱 Lancement de l'application

### Sur un appareil physique
1. Installer l'application Expo Go sur votre téléphone
2. Scanner le QR code affiché dans le terminal
3. L'application se lancera automatiquement

### Sur un émulateur
1. **Android** : Appuyer sur 'a' dans le terminal
2. **iOS** : Appuyer sur 'i' dans le terminal

## 🔧 Configuration

### API Backend
L'application se connecte au serveur Node.js sur le port 3007. Assurez-vous que :
- Le serveur backend est démarré
- L'URL API est correcte dans chaque écran : `http://localhost:3007`

### Permissions requises
- **Caméra** : Pour scanner les QR codes
- **Localisation** : Pour afficher la position sur la carte
- **Stockage** : Pour sauvegarder les PDF

## 📁 Structure du projet

```
react-native/
├── App.js                          # Point d'entrée principal
├── screens/                        # Écrans de l'application
│   ├── LoginScreen.js              # Écran de connexion
│   ├── TechnicianDashboard.js      # Tableau de bord
│   ├── ClientsListScreen.js        # Liste des clients
│   ├── ConsommationScreen.js       # Saisie de consommation
│   ├── FacturesScreen.js           # Gestion des factures
│   ├── ScannerScreen.js            # Scanner QR Code
│   ├── MapScreen.js                # Carte et localisation
│   └── ProfileScreen.js            # Profil utilisateur
├── package.json                    # Dépendances et scripts
├── app.json                        # Configuration Expo
├── babel.config.js                 # Configuration Babel
└── README.md                       # Documentation
```

## 🎨 Design et UX

### Thème de couleurs
- **Primaire** : #2196F3 (Bleu)
- **Succès** : #4CAF50 (Vert)
- **Attention** : #FF9800 (Orange)
- **Erreur** : #F44336 (Rouge)
- **Fond** : #F5F5F5 (Gris clair)

### Composants UI
- Navigation par stack avec React Navigation
- Icônes Ionicons pour une interface cohérente
- Cartes avec ombres pour la profondeur
- Boutons avec états de chargement
- Modals pour les sélections

## 🔐 Authentification

### Compte de test
- **Email** : <EMAIL>
- **Mot de passe** : Tech123

### Flux d'authentification
1. Saisie des identifiants
2. Validation côté serveur
3. Vérification du rôle (Tech uniquement)
4. Redirection vers le tableau de bord

## 📊 API Endpoints utilisés

- `POST /login` - Authentification
- `GET /api/clients` - Liste des clients
- `GET /api/clients/:id/contracts` - Contrats d'un client
- `GET /api/contracts/:id/last-consommation` - Dernière consommation
- `POST /api/consommations` - Créer une consommation
- `GET /api/factures` - Liste des factures
- `GET /api/factures/:id/pdf` - Télécharger PDF
- `PUT /api/factures/:id/status` - Changer statut facture
- `GET /api/secteurs` - Liste des secteurs
- `GET /api/contracts` - Liste des contrats

## 🚀 Déploiement

### Build de production
```bash
expo build:android
expo build:ios
```

### Publication sur les stores
```bash
expo publish
```

## 🐛 Dépannage

### Problèmes courants

1. **Erreur de connexion API**
   - Vérifier que le serveur backend est démarré
   - Vérifier l'URL de l'API dans les écrans

2. **Permissions caméra refusées**
   - Aller dans les paramètres de l'appareil
   - Autoriser l'accès à la caméra pour Expo Go

3. **Problème de géolocalisation**
   - Activer la localisation sur l'appareil
   - Autoriser l'accès à la localisation

## 📝 Notes de développement

### Améliorations futures
- Synchronisation hors ligne
- Notifications push
- Mode sombre
- Authentification biométrique
- Cache des données
- Optimisation des performances

### Technologies utilisées
- React Native avec Expo
- React Navigation pour la navigation
- Expo Camera pour le scanner QR
- React Native Maps pour la géolocalisation
- Expo FileSystem pour la gestion des fichiers
- Expo Sharing pour le partage de fichiers

## 📞 Support

Pour toute assistance technique, contactez l'équipe de développement ou consultez la documentation Expo : https://docs.expo.dev/
