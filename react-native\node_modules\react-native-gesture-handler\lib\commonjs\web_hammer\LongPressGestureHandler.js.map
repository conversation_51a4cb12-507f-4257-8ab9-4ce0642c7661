{"version": 3, "sources": ["LongPressGestureHandler.ts"], "names": ["LongPressGestureHandler", "PressGestureHandler", "minDurationMs", "config", "maxDist", "updateHasCustomActivationCriteria", "maxDistSq", "getConfig", "hasCustomActivationCriteria", "shouldCancelWhenOutside", "getHammerConfig", "time", "getState", "type", "Hammer", "INPUT_START", "State", "ACTIVE", "INPUT_MOVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED"], "mappings": ";;;;;;;AAEA;;AAEA;;AACA;;AACA;;;;AANA;;AACA;AASA,MAAMA,uBAAN,SAAsCC,4BAAtC,CAA0D;AACvC,MAAbC,aAAa,GAAW;AAC1B;AACA,WAAO,kBAAM,KAAKC,MAAL,CAAYD,aAAlB,IAAmC,GAAnC,GAAyC,KAAKC,MAAL,CAAYD,aAA5D;AACD;;AAEU,MAAPE,OAAO,GAAG;AACZ;AACA,WAAO,kBAAM,KAAKD,MAAL,CAAYC,OAAlB,IAA6B,CAA7B,GAAiC,KAAKD,MAAL,CAAYC,OAApD;AACD;;AAEDC,EAAAA,iCAAiC,CAAC;AAAEC,IAAAA;AAAF,GAAD,EAAwB;AACvD,WAAO,CAAC,0BAAcA,SAAd,CAAR;AACD;;AAEDC,EAAAA,SAAS,GAAG;AACV,QAAI,CAAC,KAAKC,2BAAV,EAAuC;AACrC;AACA;AACA,aAAO;AACLC,QAAAA,uBAAuB,EAAE,IADpB;AAELH,QAAAA,SAAS,EAAE;AAFN,OAAP;AAID;;AACD,WAAO,KAAKH,MAAZ;AACD;;AAEDO,EAAAA,eAAe,GAAG;AAChB,WAAO,EACL,GAAG,MAAMA,eAAN,EADE;AAEL;AACAC,MAAAA,IAAI,EAAE,KAAKT;AAHN,KAAP;AAKD;;AAEDU,EAAAA,QAAQ,CAACC,IAAD,EAAsC;AAC5C,WAAO;AACL,OAACC,kBAAOC,WAAR,GAAsBC,aAAMC,MADvB;AAEL,OAACH,kBAAOI,UAAR,GAAqBF,aAAMC,MAFtB;AAGL,OAACH,kBAAOK,SAAR,GAAoBH,aAAMI,GAHrB;AAIL,OAACN,kBAAOO,YAAR,GAAuBL,aAAMM;AAJxB,MAKLT,IALK,CAAP;AAMD;;AA1CuD;;eA6C3Cb,uB", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport Hammer from '@egjs/hammerjs';\n\nimport { State } from '../State';\nimport PressGestureHandler from './PressGestureHandler';\nimport { isnan, isValidNumber } from './utils';\nimport { Config } from './GestureHandler';\nimport { HammerInputNames } from './constants';\n\nclass LongPressGestureHandler extends PressGestureHandler {\n  get minDurationMs(): number {\n    // @ts-ignore FIXNE(TS)\n    return isnan(this.config.minDurationMs) ? 251 : this.config.minDurationMs;\n  }\n\n  get maxDist() {\n    // @ts-ignore FIXNE(TS)\n    return isnan(this.config.maxDist) ? 9 : this.config.maxDist;\n  }\n\n  updateHasCustomActivationCriteria({ maxDistSq }: Config) {\n    return !isValidNumber(maxDistSq);\n  }\n\n  getConfig() {\n    if (!this.hasCustomActivationCriteria) {\n      // Default config\n      // If no params have been defined then this config should emulate the native gesture as closely as possible.\n      return {\n        shouldCancelWhenOutside: true,\n        maxDistSq: 10,\n      };\n    }\n    return this.config;\n  }\n\n  getHammerConfig() {\n    return {\n      ...super.getHammerConfig(),\n      // threshold: this.maxDist,\n      time: this.minDurationMs,\n    };\n  }\n\n  getState(type: keyof typeof HammerInputNames) {\n    return {\n      [Hammer.INPUT_START]: State.ACTIVE,\n      [Hammer.INPUT_MOVE]: State.ACTIVE,\n      [Hammer.INPUT_END]: State.END,\n      [Hammer.INPUT_CANCEL]: State.FAILED,\n    }[type];\n  }\n}\n\nexport default LongPressGestureHandler;\n"]}