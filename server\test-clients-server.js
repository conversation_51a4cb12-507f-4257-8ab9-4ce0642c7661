require('dotenv').config();
const express = require('express');
const cors = require('cors');

// Import du module clients
const clientsRoutes = require('./clients');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Utilisation des routes clients
app.use('/', clientsRoutes);

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur test clients fonctionnel',r
    timestamp: new Date().toISOString(),
    routes: [
      'GET /api/clients',
      'GET /api/clients/:id',
      'GET /api/clients/:id/contracts',
      'POST /api/clients'
    ]
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

app.listen(PORT, () => {
  console.log(`\n🚀 Serveur test clients démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/clients (liste clients)');
  console.log('  - GET  /api/clients/:id (client spécifique)');
  console.log('  - GET  /api/clients/:id/contracts (contrats du client)');
  console.log('  - POST /api/clients (ajouter client)');
  console.log('\n✅ Prêt à recevoir les requêtes !');
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err);
  process.exit(1);
});
