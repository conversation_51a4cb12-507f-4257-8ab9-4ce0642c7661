{"version": 3, "file": "hammer.min.js", "sources": ["../src/utils/assign.js", "../src/browser.js", "../src/utils/utils-consts.js", "../src/utils/prefixed.js", "../src/touchactionjs/get-touchaction-props.js", "../src/touchactionjs/touchaction-Consts.js", "../src/inputjs/input-consts.js", "../src/utils/each.js", "../src/utils/bool-or-fn.js", "../src/utils/in-str.js", "../src/touchactionjs/touchaction-constructor.js", "../src/touchactionjs/clean-touch-actions.js", "../src/utils/has-parent.js", "../src/inputjs/get-center.js", "../src/inputjs/simple-clone-input-data.js", "../src/inputjs/get-distance.js", "../src/inputjs/get-angle.js", "../src/inputjs/get-direction.js", "../src/inputjs/get-velocity.js", "../src/inputjs/compute-input-data.js", "../src/inputjs/compute-delta-xy.js", "../src/inputjs/get-scale.js", "../src/inputjs/get-rotation.js", "../src/inputjs/compute-interval-input-data.js", "../src/inputjs/input-handler.js", "../src/utils/split-str.js", "../src/utils/add-event-listeners.js", "../src/utils/remove-event-listeners.js", "../src/utils/get-window-for-element.js", "../src/inputjs/input-constructor.js", "../src/utils/in-array.js", "../src/input/pointerevent.js", "../src/utils/to-array.js", "../src/utils/unique-array.js", "../src/input/touch.js", "../src/input/mouse.js", "../src/input/touchmouse.js", "../src/utils/invoke-array-arg.js", "../src/recognizerjs/recognizer-consts.js", "../src/utils/unique-id.js", "../src/recognizerjs/get-recognizer-by-name-if-manager.js", "../src/recognizerjs/state-str.js", "../src/recognizerjs/recognizer-constructor.js", "../src/recognizers/tap.js", "../src/recognizers/attribute.js", "../src/recognizerjs/direction-str.js", "../src/recognizers/pan.js", "../src/recognizers/swipe.js", "../src/recognizers/pinch.js", "../src/recognizers/rotate.js", "../src/recognizers/press.js", "../src/defaults.js", "../src/manager.js", "../src/inputjs/create-input-instance.js", "../src/input/singletouch.js", "../src/utils/deprecate.js", "../src/utils/extend.js", "../src/utils/merge.js", "../src/utils/inherit.js", "../src/utils/bind-fn.js", "../src/hammer.js"], "sourcesContent": ["/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nlet assign;\nif (typeof Object.assign !== 'function') {\n  assign = function assign(target) {\n    if (target === undefined || target === null) {\n      throw new TypeError('Cannot convert undefined or null to object');\n    }\n\n    let output = Object(target);\n    for (let index = 1; index < arguments.length; index++) {\n      const source = arguments[index];\n      if (source !== undefined && source !== null) {\n        for (const nextKey in source) {\n          if (source.hasOwnProperty(nextKey)) {\n            output[nextKey] = source[nextKey];\n          }\n        }\n      }\n    }\n    return output;\n  };\n} else {\n  assign = Object.assign;\n}\n\nexport default assign;", "/* eslint-disable no-new-func, no-nested-ternary */\n\nlet win;\n\nif (typeof window === \"undefined\") {\n\t// window is undefined in node.js\n\twin = {};\n} else {\n\twin = window;\n}\n/* eslint-enable no-new-func, no-nested-ternary */\n\nexport {win as window};\n", "\nconst VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nconst TEST_ELEMENT = typeof document === \"undefined\" ? {style: {}} : document.createElement('div');\n\nconst TYPE_FUNCTION = 'function';\n\nconst { round, abs } = Math;\nconst { now } = Date;\n\nexport {\n    VENDOR_PREFIXES,\n    TEST_ELEMENT,\n    TYPE_FUNCTION,\n    round,\n    abs,\n    now\n};\n", "import { VENDOR_PREFIXES } from './utils-consts';\n/**\n * @private\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nexport default function prefixed(obj, property) {\n  let prefix;\n  let prop;\n  let camelProp = property[0].toUpperCase() + property.slice(1);\n\n  let i = 0;\n  while (i < VENDOR_PREFIXES.length) {\n    prefix = VENDOR_PREFIXES[i];\n    prop = (prefix) ? prefix + camelProp : property;\n\n    if (prop in obj) {\n      return prop;\n    }\n    i++;\n  }\n  return undefined;\n}\n", "import prefixed from '../utils/prefixed';\nimport { TEST_ELEMENT } from '../utils/utils-consts';\nimport {window} from '../browser';\n\nexport const PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nexport const NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\nexport default function getTouchActionProps() {\n  if (!NATIVE_TOUCH_ACTION) {\n    return false;\n  }\n  let touchMap = {};\n  let cssSupports = window.CSS && window.CSS.supports;\n  ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach((val) => {\n\n    // If css.supports is not supported but there is native touch-action assume it supports\n    // all values. This is the case for IE 10 and 11.\n    return touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n  });\n  return touchMap;\n}\n", "import getTouchActionProps from './get-touchaction-props';\n\n\n\n// magical touchAction value\nconst TOUCH_ACTION_COMPUTE = 'compute';\nconst TOUCH_ACTION_AUTO = 'auto';\nconst TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nconst TOUCH_ACTION_NONE = 'none';\nconst TOUCH_ACTION_PAN_X = 'pan-x';\nconst TOUCH_ACTION_PAN_Y = 'pan-y';\nconst TOUCH_ACTION_MAP = getTouchActionProps();\n\nexport {\n  TOUCH_ACTION_AUTO,\n  TOUCH_ACTION_COMPUTE,\n  TOUCH_ACTION_MANIPULATION,\n  TOUCH_ACTION_NONE,\n  TOUCH_ACTION_PAN_X,\n  TOUCH_ACTION_PAN_Y,\n  TOUCH_ACTION_MAP\n};\n", "import prefixed from '../utils/prefixed';\nimport {window} from \"../browser\";\n\nconst MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nconst SUPPORT_TOUCH = ('ontouchstart' in window);\nconst SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nconst SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nconst INPUT_TYPE_TOUCH = 'touch';\nconst INPUT_TYPE_PEN = 'pen';\nconst INPUT_TYPE_MOUSE = 'mouse';\nconst INPUT_TYPE_KINECT = 'kinect';\n\nconst COMPUTE_INTERVAL = 25;\n\nconst INPUT_START = 1;\nconst INPUT_MOVE = 2;\nconst INPUT_END = 4;\nconst INPUT_CANCEL = 8;\n\nconst DIRECTION_NONE = 1;\nconst DIRECTION_LEFT = 2;\nconst DIRECTION_RIGHT = 4;\nconst DIRECTION_UP = 8;\nconst DIRECTION_DOWN = 16;\n\nconst DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nconst DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nconst DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nconst PROPS_XY = ['x', 'y'];\nconst PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\nexport {\n    MOBILE_REGEX,\n    SUPPORT_ONLY_TOUCH,\n    SUPPORT_POINTER_EVENTS,\n    SUPPORT_TOUCH,\n    INPUT_TYPE_KINECT,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_TOUCH,\n    COMPUTE_INTERVAL,\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    DIRECTION_NONE,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_ALL,\n    PROPS_XY,\n    PROPS_CLIENT_XY\n};\n", "/**\n * @private\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nexport default function each(obj, iterator, context) {\n  let i;\n\n  if (!obj) {\n    return;\n  }\n\n  if (obj.forEach) {\n    obj.forEach(iterator, context);\n  } else if (obj.length !== undefined) {\n    i = 0;\n    while (i < obj.length) {\n      iterator.call(context, obj[i], i, obj);\n      i++;\n    }\n  } else {\n    for (i in obj) {\n      obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n    }\n  }\n}\n", "import { TYPE_FUNCTION } from './utils-consts';\n/**\n * @private\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nexport default function boolOrFn(val, args) {\n  if (typeof val === TYPE_FUNCTION) {\n    return val.apply(args ? args[0] || undefined : undefined, args);\n  }\n  return val;\n}\n", "/**\n * @private\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nexport default function inStr(str, find) {\n  return str.indexOf(find) > -1;\n}\n", "import {\n    TOUCH_ACTION_COMPUTE,\n    TOUCH_ACTION_MAP,\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y\n} from './touchaction-Consts';\nimport {\n  NATIVE_TOUCH_ACTION,\n  PREFIXED_TOUCH_ACTION,\n} from \"./get-touchaction-props\";\nimport {\n    DIRECTION_VERTICAL,\n    DIRECTION_HORIZONTAL\n} from '../inputjs/input-consts';\nimport each from '../utils/each';\nimport boolOrFn from '../utils/bool-or-fn';\nimport inStr from '../utils/in-str';\nimport cleanTouchActions from './clean-touch-actions';\n\n/**\n * @private\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nexport default class TouchAction {\n  constructor(manager, value) {\n    this.manager = manager;\n    this.set(value);\n  }\n\n  /**\n   * @private\n   * set the touchAction value on the element or enable the polyfill\n   * @param {String} value\n   */\n  set(value) {\n    // find out the touch-action by the event handlers\n    if (value === TOUCH_ACTION_COMPUTE) {\n      value = this.compute();\n    }\n\n    if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n      this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n    }\n    this.actions = value.toLowerCase().trim();\n  }\n\n  /**\n   * @private\n   * just re-set the touchAction value\n   */\n  update() {\n    this.set(this.manager.options.touchAction);\n  }\n\n  /**\n   * @private\n   * compute the value for the touchAction property based on the recognizer's settings\n   * @returns {String} value\n   */\n  compute() {\n    let actions = [];\n    each(this.manager.recognizers, (recognizer) => {\n      if (boolOrFn(recognizer.options.enable, [recognizer])) {\n        actions = actions.concat(recognizer.getTouchAction());\n      }\n    });\n    return cleanTouchActions(actions.join(' '));\n  }\n\n  /**\n   * @private\n   * this method is called on each input cycle and provides the preventing of the browser behavior\n   * @param {Object} input\n   */\n  preventDefaults(input) {\n    let { srcEvent } = input;\n    let direction = input.offsetDirection;\n\n    // if the touch action did prevented once this session\n    if (this.manager.session.prevented) {\n      srcEvent.preventDefault();\n      return;\n    }\n\n    let { actions } = this;\n    let hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n    let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n    let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n    if (hasNone) {\n      // do not prevent defaults if this is a tap gesture\n      let isTapPointer = input.pointers.length === 1;\n      let isTapMovement = input.distance < 2;\n      let isTapTouchTime = input.deltaTime < 250;\n\n      if (isTapPointer && isTapMovement && isTapTouchTime) {\n        return;\n      }\n    }\n\n    if (hasPanX && hasPanY) {\n      // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n      return;\n    }\n\n    if (hasNone ||\n        (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n        (hasPanX && direction & DIRECTION_VERTICAL)) {\n      return this.preventSrc(srcEvent);\n    }\n  }\n\n  /**\n   * @private\n   * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n   * @param {Object} srcEvent\n   */\n  preventSrc(srcEvent) {\n    this.manager.session.prevented = true;\n    srcEvent.preventDefault();\n  }\n}\n", "import inStr from '../utils/in-str';\nimport {\n    TOUCH_ACTION_NONE,\n    TOUCH_ACTION_PAN_X,\n    TOUCH_ACTION_PAN_Y,\n    TOUCH_ACTION_MANIPULATION,\n    TOUCH_ACTION_AUTO\n} from './touchaction-Consts';\n\n/**\n * @private\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nexport default function cleanTouchActions(actions) {\n  // none\n  if (inStr(actions, TOUCH_ACTION_NONE)) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  let hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n  let hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n  // if both pan-x and pan-y are set (different recognizers\n  // for different directions, e.g. horizontal pan but vertical swipe?)\n  // we need none (as otherwise with pan-x pan-y combined none of these\n  // recognizers will work, since the browser would handle all panning\n  if (hasPanX && hasPanY) {\n    return TOUCH_ACTION_NONE;\n  }\n\n  // pan-x OR pan-y\n  if (hasPanX || hasPanY) {\n    return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n  }\n\n  // manipulation\n  if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n    return TOUCH_ACTION_MANIPULATION;\n  }\n\n  return TOUCH_ACTION_AUTO;\n}\n", "/**\n * @private\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {<PERSON>olean} found\n */\nexport default function hasParent(node, parent) {\n  while (node) {\n    if (node === parent) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}\n", "import { round } from '../utils/utils-consts';\n\n/**\n * @private\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nexport default function getCenter(pointers) {\n  let pointersLength = pointers.length;\n\n  // no need to loop when only one touch\n  if (pointersLength === 1) {\n    return {\n      x: round(pointers[0].clientX),\n      y: round(pointers[0].clientY)\n    };\n  }\n\n  let x = 0;\n  let y = 0;\n  let i = 0;\n  while (i < pointersLength) {\n    x += pointers[i].clientX;\n    y += pointers[i].clientY;\n    i++;\n  }\n\n  return {\n    x: round(x / pointersLength),\n    y: round(y / pointersLength)\n  };\n}\n", "import { now,round } from '../utils/utils-consts';\nimport getCenter from './get-center';\n\n/**\n * @private\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nexport default function simpleCloneInputData(input) {\n  // make a simple copy of the pointers because we will get a reference if we don't\n  // we only need clientXY for the calculations\n  let pointers = [];\n  let i = 0;\n  while (i < input.pointers.length) {\n    pointers[i] = {\n      clientX: round(input.pointers[i].clientX),\n      clientY: round(input.pointers[i].clientY)\n    };\n    i++;\n  }\n\n  return {\n    timeStamp: now(),\n    pointers,\n    center: getCenter(pointers),\n    deltaX: input.deltaX,\n    deltaY: input.deltaY\n  };\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nexport default function getDistance(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n\n  return Math.sqrt((x * x) + (y * y));\n}\n", "import { PROPS_XY } from './input-consts';\n\n/**\n * @private\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nexport default function getAngle(p1, p2, props) {\n  if (!props) {\n    props = PROPS_XY;\n  }\n  let x = p2[props[0]] - p1[props[0]];\n  let y = p2[props[1]] - p1[props[1]];\n  return Math.atan2(y, x) * 180 / Math.PI;\n}\n", "import { abs } from '../utils/utils-consts';\nimport { DIRECTION_NONE,DIRECTION_LEFT,DIRECTION_RIGHT,DIRECTION_UP,DIRECTION_DOWN } from './input-consts';\n\n/**\n * @private\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nexport default function getDirection(x, y) {\n  if (x === y) {\n    return DIRECTION_NONE;\n  }\n\n  if (abs(x) >= abs(y)) {\n    return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n  }\n  return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n", "/**\n * @private\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nexport default function getVelocity(deltaTime, x, y) {\n  return {\n    x: x / deltaTime || 0,\n    y: y / deltaTime || 0\n  };\n}\n", "import { now } from '../utils/utils-consts';\nimport { abs } from '../utils/utils-consts';\nimport hasParent from '../utils/has-parent';\nimport simpleCloneInputData from './simple-clone-input-data';\nimport getCenter from './get-center';\nimport getDistance from './get-distance';\nimport getAngle from './get-angle';\nimport getDirection from './get-direction';\nimport computeDeltaXY from './compute-delta-xy';\nimport getVelocity from './get-velocity';\nimport getScale from './get-scale';\nimport getRotation from './get-rotation';\nimport computeIntervalInputData from './compute-interval-input-data';\n\n/**\n* @private\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nexport default function computeInputData(manager, input) {\n  let { session } = manager;\n  let { pointers } = input;\n  let { length:pointersLength } = pointers;\n\n  // store the first input to calculate the distance and direction\n  if (!session.firstInput) {\n    session.firstInput = simpleCloneInputData(input);\n  }\n\n  // to compute scale and rotation we need to store the multiple touches\n  if (pointersLength > 1 && !session.firstMultiple) {\n    session.firstMultiple = simpleCloneInputData(input);\n  } else if (pointersLength === 1) {\n    session.firstMultiple = false;\n  }\n\n  let { firstInput, firstMultiple } = session;\n  let offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n  let center = input.center = getCenter(pointers);\n  input.timeStamp = now();\n  input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n  input.angle = getAngle(offsetCenter, center);\n  input.distance = getDistance(offsetCenter, center);\n\n  computeDeltaXY(session, input);\n  input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n  let overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n  input.overallVelocityX = overallVelocity.x;\n  input.overallVelocityY = overallVelocity.y;\n  input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n  input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n  input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n  input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n  session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n  computeIntervalInputData(session, input);\n\n  // find the correct target\n  let target = manager.element;\n  const srcEvent = input.srcEvent;\n  let srcEventTarget;\n\n  if (srcEvent.composedPath) {\n    srcEventTarget = srcEvent.composedPath()[0];\n  } else if (srcEvent.path) {\n    srcEventTarget = srcEvent.path[0];\n  } else {\n    srcEventTarget = srcEvent.target;\n  }\n\n  if (hasParent(srcEventTarget, target)) {\n    target = srcEventTarget;\n  }\n  input.target = target;\n}\n", "import { INPUT_START, INPUT_END } from './input-consts';\n\nexport default function computeDeltaXY(session, input) {\n  let { center } = input;\n  // let { offsetDelta:offset = {}, prevDelta = {}, prevInput = {} } = session;\n  // jscs throwing error on defalut destructured values and without defaults tests fail\n  let offset = session.offsetDelta || {};\n  let prevDelta = session.prevDelta || {};\n  let prevInput = session.prevInput || {};\n\n  if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n    prevDelta = session.prevDelta = {\n      x: prevInput.deltaX || 0,\n      y: prevInput.deltaY || 0\n    };\n\n    offset = session.offsetDelta = {\n      x: center.x,\n      y: center.y\n    };\n  }\n\n  input.deltaX = prevDelta.x + (center.x - offset.x);\n  input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n", "import { PROPS_CLIENT_XY } from './input-consts';\nimport getDistance from './get-distance';\n/**\n * @private\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nexport default function getScale(start, end) {\n  return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n", "import getAngle from './get-angle';\nimport { PROPS_CLIENT_XY } from './input-consts';\n\n/**\n * @private\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nexport default function getRotation(start, end) {\n  return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n", "import { INPUT_CANCEL,COMPUTE_INTERVAL } from './input-consts';\nimport { abs } from '../utils/utils-consts';\nimport getVelocity from './get-velocity';\nimport getDirection from './get-direction';\n\n/**\n * @private\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nexport default function computeIntervalInputData(session, input) {\n  let last = session.lastInterval || input;\n  let deltaTime = input.timeStamp - last.timeStamp;\n  let velocity;\n  let velocityX;\n  let velocityY;\n  let direction;\n\n  if (input.eventType !== INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n    let deltaX = input.deltaX - last.deltaX;\n    let deltaY = input.deltaY - last.deltaY;\n\n    let v = getVelocity(deltaTime, deltaX, deltaY);\n    velocityX = v.x;\n    velocityY = v.y;\n    velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n    direction = getDirection(deltaX, deltaY);\n\n    session.lastInterval = input;\n  } else {\n    // use latest velocity info if it doesn't overtake a minimum period\n    velocity = last.velocity;\n    velocityX = last.velocityX;\n    velocityY = last.velocityY;\n    direction = last.direction;\n  }\n\n  input.velocity = velocity;\n  input.velocityX = velocityX;\n  input.velocityY = velocityY;\n  input.direction = direction;\n}\n", "import { INPUT_START,INPUT_END,INPUT_CANCEL } from './input-consts';\nimport computeInputData from './compute-input-data';\n\n/**\n * @private\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nexport default function inputHandler(manager, eventType, input) {\n  let pointersLen = input.pointers.length;\n  let changedPointersLen = input.changedPointers.length;\n  let isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n  let isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n  input.isFirst = !!isFirst;\n  input.isFinal = !!isFinal;\n\n  if (isFirst) {\n    manager.session = {};\n  }\n\n  // source event is the normalized value of the domEvents\n  // like 'touchstart, mouseup, pointerdown'\n  input.eventType = eventType;\n\n  // compute scale, rotation etc\n  computeInputData(manager, input);\n\n  // emit secret event\n  manager.emit('hammer.input', input);\n\n  manager.recognize(input);\n  manager.session.prevInput = input;\n}\n", "/**\n * @private\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\n\nexport default function splitStr(str) {\n  return str.trim().split(/\\s+/g);\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function addEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.addEventListener(type, handler, false);\n  });\n}\n", "import each from './each';\nimport splitStr from './split-str';\n/**\n * @private\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nexport default function removeEventListeners(target, types, handler) {\n  each(splitStr(types), (type) => {\n    target.removeEventListener(type, handler, false);\n  });\n}\n", "/**\n * @private\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nexport default function getWindowForElement(element) {\n  let doc = element.ownerDocument || element;\n  return (doc.defaultView || doc.parentWindow || window);\n}\n", "import boolOrFn from '../utils/bool-or-fn';\nimport addEventListeners from '../utils/add-event-listeners';\nimport removeEventListeners from '../utils/remove-event-listeners';\nimport getWindowForElement from '../utils/get-window-for-element';\n\n/**\n * @private\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nexport default class Input {\n  constructor(manager, callback) {\n    let self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n      if (boolOrFn(manager.options.enable, [manager])) {\n        self.handler(ev);\n      }\n    };\n\n    this.init();\n\n  }\n  /**\n   * @private\n   * should handle the inputEvent data and trigger the callback\n   * @virtual\n   */\n  handler() { }\n\n  /**\n   * @private\n   * bind the events\n   */\n  init() {\n    this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n\n  /**\n   * @private\n   * unbind the events\n   */\n  destroy() {\n    this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n    this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n    this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n  }\n}\n", "/**\n * @private\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findBy<PERSON><PERSON>]\n * @return {Boolean|Number} false when not found, or the index\n */\nexport default function inArray(src, find, findByKey) {\n  if (src.indexOf && !findByKey) {\n    return src.indexOf(find);\n  } else {\n    let i = 0;\n    while (i < src.length) {\n      if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {// do not use === here, test fails\n        return i;\n      }\n      i++;\n    }\n    return -1;\n  }\n}\n", "import {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_MOVE,\n    INPUT_TYPE_TOUCH,\n    INPUT_TYPE_MOUSE,\n    INPUT_TYPE_PEN,\n    INPUT_TYPE_KINECT\n} from '../inputjs/input-consts';\nimport {window} from \"../browser\";\nimport Input from '../inputjs/input-constructor';\nimport inArray from '../utils/in-array';\n\nconst POINTER_INPUT_MAP = {\n  pointerdown: INPUT_START,\n  pointermove: INPUT_MOVE,\n  pointerup: INPUT_END,\n  pointercancel: INPUT_CANCEL,\n  pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nconst IE10_POINTER_TYPE_ENUM = {\n  2: INPUT_TYPE_TOUCH,\n  3: INPUT_TYPE_PEN,\n  4: INPUT_TYPE_MOUSE,\n  5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nlet POINTER_ELEMENT_EVENTS = 'pointerdown';\nlet POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n  POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n  POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * @private\n * Pointer events input\n * @constructor\n * @extends Input\n */\nexport default class PointerEventInput extends Input {\n  constructor() {\n    var proto = PointerEventInput.prototype;\n\n    proto.evEl = POINTER_ELEMENT_EVENTS;\n    proto.evWin = POINTER_WINDOW_EVENTS;\n    super(...arguments);\n    this.store = (this.manager.session.pointerEvents = []);\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let { store } = this;\n    let removePointer = false;\n\n    let eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n    let eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n    let pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n    let isTouch = (pointerType === INPUT_TYPE_TOUCH);\n\n    // get index of the event in the store\n    let storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n    // start and mouse must be down\n    if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n      if (storeIndex < 0) {\n        store.push(ev);\n        storeIndex = store.length - 1;\n      }\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n      removePointer = true;\n    }\n\n    // it not found, so the pointer hasn't been down (so it's probably a hover)\n    if (storeIndex < 0) {\n      return;\n    }\n\n    // update the event in the store\n    store[storeIndex] = ev;\n\n    this.callback(this.manager, eventType, {\n      pointers: store,\n      changedPointers: [ev],\n      pointerType,\n      srcEvent: ev\n    });\n\n    if (removePointer) {\n      // remove from the store\n      store.splice(storeIndex, 1);\n    }\n  }\n}\n", "/**\n * @private\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nexport default function toArray(obj) {\n  return Array.prototype.slice.call(obj, 0);\n}\n", "import inArray from './in-array';\n\n/**\n * @private\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nexport default function uniqueArray(src, key, sort) {\n  let results = [];\n  let values = [];\n  let i = 0;\n\n  while (i < src.length) {\n    let val = key ? src[i][key] : src[i];\n    if (inArray(values, val) < 0) {\n      results.push(src[i]);\n    }\n    values[i] = val;\n    i++;\n  }\n\n  if (sort) {\n    if (!key) {\n      results = results.sort();\n    } else {\n      results = results.sort((a, b) => {\n        return a[key] > b[key];\n      });\n    }\n  }\n\n  return results;\n}\n", "import {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport hasParent from '../utils/has-parent';\nimport uniqueArray from '../utils/unique-array';\n\nconst TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nexport default class TouchInput extends Input {\n  constructor() {\n    TouchInput.prototype.evTarget = TOUCH_TARGET_EVENTS;\n    super(...arguments);\n    this.targetIds = {};\n    // this.evTarget = TOUCH_TARGET_EVENTS;\n  }\n  handler(ev) {\n    let type = TOUCH_INPUT_MAP[ev.type];\n    let touches = getTouches.call(this, ev, type);\n    if (!touches) {\n      return;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n  let allTouches = toArray(ev.touches);\n  let { targetIds } = this;\n\n  // when there is only one touch, the process can be simplified\n  if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n    targetIds[allTouches[0].identifier] = true;\n    return [allTouches, allTouches];\n  }\n\n  let i;\n  let targetTouches;\n  let changedTouches = toArray(ev.changedTouches);\n  let changedTargetTouches = [];\n  let { target } = this;\n\n  // get target touches from touches\n  targetTouches = allTouches.filter((touch) => {\n    return hasParent(touch.target, target);\n  });\n\n  // collect touches\n  if (type === INPUT_START) {\n    i = 0;\n    while (i < targetTouches.length) {\n      targetIds[targetTouches[i].identifier] = true;\n      i++;\n    }\n  }\n\n  // filter changed touches to only contain touches that exist in the collected target ids\n  i = 0;\n  while (i < changedTouches.length) {\n    if (targetIds[changedTouches[i].identifier]) {\n      changedTargetTouches.push(changedTouches[i]);\n    }\n\n    // cleanup removed touches\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n      delete targetIds[changedTouches[i].identifier];\n    }\n    i++;\n  }\n\n  if (!changedTargetTouches.length) {\n    return;\n  }\n\n  return [\n    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n    uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n    changedTargetTouches\n  ];\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_TYPE_MOUSE\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\n\nconst MOUSE_INPUT_MAP = {\n  mousedown: INPUT_START,\n  mousemove: INPUT_MOVE,\n  mouseup: INPUT_END\n};\n\nconst MOUSE_ELEMENT_EVENTS = 'mousedown';\nconst MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * @private\n * Mouse events input\n * @constructor\n * @extends Input\n */\nexport default class MouseInput extends Input {\n  constructor() {\n    var proto = MouseInput.prototype;\n    proto.evEl = MOUSE_ELEMENT_EVENTS;\n    proto.evWin = MOUSE_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.pressed = false; // mousedown state\n  }\n\n  /**\n   * @private\n   * handle mouse events\n   * @param {Object} ev\n   */\n  handler(ev) {\n    let eventType = MOUSE_INPUT_MAP[ev.type];\n\n    // on start we want to have the left mouse button down\n    if (eventType & INPUT_START && ev.button === 0) {\n      this.pressed = true;\n    }\n\n    if (eventType & INPUT_MOVE && ev.which !== 1) {\n      eventType = INPUT_END;\n    }\n\n    // mouse must be down\n    if (!this.pressed) {\n      return;\n    }\n\n    if (eventType & INPUT_END) {\n      this.pressed = false;\n    }\n\n    this.callback(this.manager, eventType, {\n      pointers: [ev],\n      changedPointers: [ev],\n      pointerType: INPUT_TYPE_MOUSE,\n      srcEvent: ev\n    });\n  }\n}\n", "import Input from \"../inputjs/input-constructor\";\nimport TouchInput from \"./touch\";\nimport MouseInput from \"./mouse\";\nimport {\n\tINPUT_START,\n\tINPUT_END,\n\tINPUT_CANCEL,\n\tINPUT_TYPE_TOUCH,\n\tINPUT_TYPE_MOUSE,\n} from \"../inputjs/input-consts\";\n\n/**\n * @private\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nconst DEDUP_TIMEOUT = 2500;\nconst DEDUP_DISTANCE = 25;\n\nfunction setLastTouch(eventData) {\n\tconst { changedPointers: [touch] } = eventData;\n\n\tif (touch.identifier === this.primaryTouch) {\n\t\tconst lastTouch = { x: touch.clientX, y: touch.clientY };\n\t\tconst lts = this.lastTouches;\n\n\t\tthis.lastTouches.push(lastTouch);\n\n\n\t\tconst removeLastTouch = function() {\n\t\t\tconst i = lts.indexOf(lastTouch);\n\n\t\t\tif (i > -1) {\n\t\t\t\tlts.splice(i, 1);\n\t\t\t}\n\t\t};\n\n\t\tsetTimeout(removeLastTouch, DEDUP_TIMEOUT);\n\t}\n}\n\n\nfunction recordTouches(eventType, eventData) {\n\tif (eventType & INPUT_START) {\n\t\tthis.primaryTouch = eventData.changedPointers[0].identifier;\n\t\tsetLastTouch.call(this, eventData);\n\t} else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n\t\tsetLastTouch.call(this, eventData);\n\t}\n}\nfunction isSyntheticEvent(eventData) {\n\tconst x = eventData.srcEvent.clientX;\n\tconst y = eventData.srcEvent.clientY;\n\n\tfor (let i = 0; i < this.lastTouches.length; i++) {\n\t\tconst t = this.lastTouches[i];\n\t\tconst dx = Math.abs(x - t.x);\n\t\tconst dy = Math.abs(y - t.y);\n\n\t\tif (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn false;\n}\n\n\nexport default class TouchMouseInput extends Input {\n\tconstructor(manager, callback) {\n\t\tsuper(manager, callback);\n\n\t\tthis.touch = new TouchInput(this.manager, this.handler);\n\t\tthis.mouse = new MouseInput(this.manager, this.handler);\n\t\tthis.primaryTouch = null;\n\t\tthis.lastTouches = [];\n\t}\n\n\t/**\n\t * @private\n\t * handle mouse and touch events\n\t * @param {Hammer} manager\n\t * @param {String} inputEvent\n\t * @param {Object} inputData\n\t */\n\thandler = (manager, inputEvent, inputData) => {\n\t\tconst isTouch = (inputData.pointerType === INPUT_TYPE_TOUCH);\n\t\tconst isMouse = (inputData.pointerType === INPUT_TYPE_MOUSE);\n\n\t\tif (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n\t\t\treturn;\n\t\t}\n\n\t\t// when we're in a touch event, record touches to  de-dupe synthetic mouse event\n\t\tif (isTouch) {\n\t\t\trecordTouches.call(this, inputEvent, inputData);\n\t\t} else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.callback(manager, inputEvent, inputData);\n\t}\n\n\t/**\n\t * @private\n\t * remove the event listeners\n\t */\n\tdestroy() {\n\t\tthis.touch.destroy();\n\t\tthis.mouse.destroy();\n\t}\n}\n", "import each from './each';\n/**\n * @private\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nexport default function invokeArrayArg(arg, fn, context) {\n  if (Array.isArray(arg)) {\n    each(arg, context[fn], context);\n    return true;\n  }\n  return false;\n}\n", "const STATE_POSSIBLE = 1;\nconst STATE_BEGAN = 2;\nconst STATE_CHANGED = 4;\nconst STATE_ENDED = 8;\nconst STATE_RECOGNIZED = STATE_ENDED;\nconst STATE_CANCELLED = 16;\nconst STATE_FAILED = 32;\n\nexport {\n    STATE_POSSIBLE,\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_ENDED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_FAILED\n};\n", "/**\n * @private\n * get a unique id\n * @returns {number} uniqueId\n */\nlet _uniqueId = 1;\nexport default function uniqueId() {\n  return _uniqueId++;\n}\n", "/**\n * @private\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nexport default function getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n  let { manager } = recognizer;\n  if (manager) {\n    return manager.get(otherRecognizer);\n  }\n  return otherRecognizer;\n}\n", "import {\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_CHANGED,\n    STATE_BEGAN\n} from './recognizer-consts';\n\n/**\n * @private\n * get a usable string, used as event postfix\n * @param {constant} state\n * @returns {String} state\n */\nexport default function stateStr(state) {\n  if (state & STATE_CANCELLED) {\n    return 'cancel';\n  } else if (state & STATE_ENDED) {\n    return 'end';\n  } else if (state & STATE_CHANGED) {\n    return 'move';\n  } else if (state & STATE_BEGAN) {\n    return 'start';\n  }\n  return '';\n}\n", "import {\n    STATE_POSSIBLE,\n    STATE_ENDED,\n    STATE_FAILED,\n    STATE_RECOGNIZED,\n    STATE_CANCELLED,\n    STATE_BEGAN,\n    STATE_CHANGED\n} from './recognizer-consts';\nimport assign from '../utils/assign';\nimport uniqueId from '../utils/unique-id';\nimport invokeArrayArg from '../utils/invoke-array-arg';\nimport inArray from '../utils/in-array';\nimport boolOrFn from '../utils/bool-or-fn';\nimport getRecognizerByNameIfManager from './get-recognizer-by-name-if-manager';\nimport stateStr from './state-str';\n\n/**\n * @private\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, <PERSON>NC<PERSON>LED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\n\n/**\n * @private\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nexport default class Recognizer {\n  constructor(options = {}) {\n    this.options = {\n      enable: true,\n      ...options,\n    };\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.state = STATE_POSSIBLE;\n    this.simultaneous = {};\n    this.requireFail = [];\n  }\n\n  /**\n   * @private\n   * set options\n   * @param {Object} options\n   * @return {Recognizer}\n   */\n  set(options) {\n    assign(this.options, options);\n\n    // also update the touchAction, in case something changed about the directions/enabled state\n    this.manager && this.manager.touchAction.update();\n    return this;\n  }\n\n  /**\n   * @private\n   * recognize simultaneous with an other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  recognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n      return this;\n    }\n\n    let { simultaneous } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (!simultaneous[otherRecognizer.id]) {\n      simultaneous[otherRecognizer.id] = otherRecognizer;\n      otherRecognizer.recognizeWith(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRecognizeWith(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    delete this.simultaneous[otherRecognizer.id];\n    return this;\n  }\n\n  /**\n   * @private\n   * recognizer can only run when an other is failing\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  requireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n      return this;\n    }\n\n    let { requireFail } = this;\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    if (inArray(requireFail, otherRecognizer) === -1) {\n      requireFail.push(otherRecognizer);\n      otherRecognizer.requireFailure(this);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * drop the requireFailure link. it does not remove the link on the other recognizer.\n   * @param {Recognizer} otherRecognizer\n   * @returns {Recognizer} this\n   */\n  dropRequireFailure(otherRecognizer) {\n    if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n      return this;\n    }\n\n    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n    let index = inArray(this.requireFail, otherRecognizer);\n    if (index > -1) {\n      this.requireFail.splice(index, 1);\n    }\n    return this;\n  }\n\n  /**\n   * @private\n   * has require failures boolean\n   * @returns {boolean}\n   */\n  hasRequireFailures() {\n    return this.requireFail.length > 0;\n  }\n\n  /**\n   * @private\n   * if the recognizer can recognize simultaneous with an other recognizer\n   * @param {Recognizer} otherRecognizer\n   * @returns {Boolean}\n   */\n  canRecognizeWith(otherRecognizer) {\n    return !!this.simultaneous[otherRecognizer.id];\n  }\n\n  /**\n   * @private\n   * You should use `tryEmit` instead of `emit` directly to check\n   * that all the needed recognizers has failed before emitting.\n   * @param {Object} input\n   */\n  emit(input) {\n    let self = this;\n    let { state } = this;\n\n    function emit(event) {\n      self.manager.emit(event, input);\n    }\n\n    // 'panstart' and 'panmove'\n    if (state < STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n\n    emit(self.options.event); // simple 'eventName' events\n\n    if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n      emit(input.additionalEvent);\n    }\n\n    // panend and pancancel\n    if (state >= STATE_ENDED) {\n      emit(self.options.event + stateStr(state));\n    }\n  }\n\n  /**\n   * @private\n   * Check that all the require failure recognizers has failed,\n   * if true, it emits a gesture event,\n   * otherwise, setup the state to FAILED.\n   * @param {Object} input\n   */\n  tryEmit(input) {\n    if (this.canEmit()) {\n      return this.emit(input);\n    }\n    // it's failing anyway\n    this.state = STATE_FAILED;\n  }\n\n  /**\n   * @private\n   * can we emit?\n   * @returns {boolean}\n   */\n  canEmit() {\n    let i = 0;\n    while (i < this.requireFail.length) {\n      if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n        return false;\n      }\n      i++;\n    }\n    return true;\n  }\n\n  /**\n   * @private\n   * update the recognizer\n   * @param {Object} inputData\n   */\n  recognize(inputData) {\n    // make a new copy of the inputData\n    // so we can change the inputData without messing up the other recognizers\n    let inputDataClone = assign({}, inputData);\n\n    // is is enabled and allow recognizing?\n    if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n      this.reset();\n      this.state = STATE_FAILED;\n      return;\n    }\n\n    // reset when we've reached the end\n    if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n      this.state = STATE_POSSIBLE;\n    }\n\n    this.state = this.process(inputDataClone);\n\n    // the recognizer has recognized a gesture\n    // so trigger an event\n    if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n      this.tryEmit(inputDataClone);\n    }\n  }\n\n  /**\n   * @private\n   * return the state of the recognizer\n   * the actual recognizing happens in this method\n   * @virtual\n   * @param {Object} inputData\n   * @returns {constant} STATE\n   */\n\n  /* jshint ignore:start */\n  process(inputData) { }\n  /* jshint ignore:end */\n\n  /**\n   * @private\n   * return the preferred touch-action\n   * @virtual\n   * @returns {Array}\n   */\n  getTouchAction() { }\n\n  /**\n   * @private\n   * called when the gesture isn't allowed to recognize\n   * like when another is being recognized or it is disabled\n   * @virtual\n   */\n  reset() { }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport { TOUCH_ACTION_MANIPULATION } from '../touchactionjs/touchaction-Consts';\nimport {INPUT_START,INPUT_END } from '../inputjs/input-consts';\nimport {\n    STATE_RECOGNIZED,\n    STATE_BEGAN,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport getDistance from '../inputjs/get-distance';\n\n/**\n * @private\n * A tap is recognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nexport default class TapRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'tap',\n      pointers: 1,\n      taps: 1,\n      interval: 300, // max time between the multi-tap taps\n      time: 250, // max time of the pointer to be down (like finger on the screen)\n      threshold: 9, // a minimal movement is ok, but keep it low\n      posThreshold: 10, // a multi-tap can be a bit off the initial position\n      ...options,\n    });\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_MANIPULATION];\n  }\n\n  process(input) {\n    let { options } = this;\n\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTouchTime = input.deltaTime < options.time;\n\n    this.reset();\n\n    if ((input.eventType & INPUT_START) && (this.count === 0)) {\n      return this.failTimeout();\n    }\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (validMovement && validTouchTime && validPointers) {\n      if (input.eventType !== INPUT_END) {\n        return this.failTimeout();\n      }\n\n      let validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n      let validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n      this.pTime = input.timeStamp;\n      this.pCenter = input.center;\n\n      if (!validMultiTap || !validInterval) {\n        this.count = 1;\n      } else {\n        this.count += 1;\n      }\n\n      this._input = input;\n\n      // if tap count matches we have recognized it,\n      // else it has began recognizing...\n      let tapCount = this.count % options.taps;\n      if (tapCount === 0) {\n        // no failing requirements, immediately trigger the tap event\n        // or wait as long as the multitap interval to trigger\n        if (!this.hasRequireFailures()) {\n          return STATE_RECOGNIZED;\n        } else {\n          this._timer = setTimeout(() => {\n            this.state = STATE_RECOGNIZED;\n            this.tryEmit();\n          }, options.interval);\n          return STATE_BEGAN;\n        }\n      }\n    }\n    return STATE_FAILED;\n  }\n\n  failTimeout() {\n    this._timer = setTimeout(() => {\n      this.state = STATE_FAILED;\n    }, this.options.interval);\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit() {\n    if (this.state === STATE_RECOGNIZED) {\n      this._input.tapCount = this.count;\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_BEGAN,\n    STATE_CHANGED,\n    STATE_CANCELLED,\n    STATE_ENDED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport {\n    INPUT_CANCEL,\n    INPUT_END\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nexport default class AttrRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  /**\n   * @private\n   * Used to check if it the recognizer receives valid input, like input.distance > 10.\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {Boolean} recognized\n   */\n  attrTest(input) {\n    let optionPointers = this.options.pointers;\n    return optionPointers === 0 || input.pointers.length === optionPointers;\n  }\n\n  /**\n   * @private\n   * Process the input and return the state for the recognizer\n   * @memberof AttrRecognizer\n   * @param {Object} input\n   * @returns {*} State\n   */\n  process(input) {\n    let { state } = this;\n    let { eventType } = input;\n\n    let isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n    let isValid = this.attrTest(input);\n\n    // on cancel input and we've recognized before, return STATE_CANCELLED\n    if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n      return state | STATE_CANCELLED;\n    } else if (isRecognized || isValid) {\n      if (eventType & INPUT_END) {\n        return state | STATE_ENDED;\n      } else if (!(state & STATE_BEGAN)) {\n        return STATE_BEGAN;\n      }\n      return state | STATE_CHANGED;\n    }\n    return STATE_FAILED;\n  }\n}\n", "import {\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT,\n    DIRECTION_UP,\n    DIRECTION_DOWN\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * direction cons to string\n * @param {constant} direction\n * @returns {String}\n */\nexport default function directionStr(direction) {\n  if (direction === DIRECTION_DOWN) {\n    return 'down';\n  } else if (direction === DIRECTION_UP) {\n    return 'up';\n  } else if (direction === DIRECTION_LEFT) {\n    return 'left';\n  } else if (direction === DIRECTION_RIGHT) {\n    return 'right';\n  }\n  return '';\n}\n", "import  AttrR<PERSON>ognizer from './attribute';\nimport {\n    DIRECTION_ALL,\n    DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL,\n    DIRECTION_NONE,\n    DIRECTION_UP,\n    DIRECTION_DOWN,\n    DIRECTION_LEFT,\n    DIRECTION_RIGHT\n} from '../inputjs/input-consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\nimport { TOUCH_ACTION_PAN_X,TOUCH_ACTION_PAN_Y } from '../touchactionjs/touchaction-Consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PanRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pan',\n      threshold: 10,\n      pointers: 1,\n      direction: DIRECTION_ALL,\n      ...options,\n    });\n    this.pX = null;\n    this.pY = null;\n  }\n\n  getTouchAction() {\n    let { options:{ direction } } = this;\n    let actions = [];\n    if (direction & DIRECTION_HORIZONTAL) {\n      actions.push(TOUCH_ACTION_PAN_Y);\n    }\n    if (direction & DIRECTION_VERTICAL) {\n      actions.push(TOUCH_ACTION_PAN_X);\n    }\n    return actions;\n  }\n\n  directionTest(input) {\n    let { options } = this;\n    let hasMoved = true;\n    let { distance } = input;\n    let { direction } = input;\n    let x = input.deltaX;\n    let y = input.deltaY;\n\n    // lock to axis?\n    if (!(direction & options.direction)) {\n      if (options.direction & DIRECTION_HORIZONTAL) {\n        direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n        hasMoved = x !== this.pX;\n        distance = Math.abs(input.deltaX);\n      } else {\n        direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n        hasMoved = y !== this.pY;\n        distance = Math.abs(input.deltaY);\n      }\n    }\n    input.direction = direction;\n    return hasMoved && distance > options.threshold && direction & options.direction;\n  }\n\n  attrTest(input) {\n    return AttrRecognizer.prototype.attrTest.call(this, input) && // replace with a super call\n        (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n  }\n\n  emit(input) {\n\n    this.pX = input.deltaX;\n    this.pY = input.deltaY;\n\n    let direction = directionStr(input.direction);\n\n    if (direction) {\n      input.additionalEvent = this.options.event + direction;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from '../recognizers/attribute';\nimport { abs } from '../utils/utils-consts';\nimport { DIRECTION_HORIZONTAL,DIRECTION_VERTICAL } from '../inputjs/input-consts';\nimport PanRecognizer from './pan';\nimport { INPUT_END } from '../inputjs/input-consts';\nimport directionStr from '../recognizerjs/direction-str';\n\n/**\n * @private\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class SwipeRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'swipe',\n      threshold: 10,\n      velocity: 0.3,\n      direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n      pointers: 1,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return PanRecognizer.prototype.getTouchAction.call(this);\n  }\n\n  attrTest(input) {\n    let { direction } = this.options;\n    let velocity;\n\n    if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n      velocity = input.overallVelocity;\n    } else if (direction & DIRECTION_HORIZONTAL) {\n      velocity = input.overallVelocityX;\n    } else if (direction & DIRECTION_VERTICAL) {\n      velocity = input.overallVelocityY;\n    }\n\n    return super.attrTest(input) &&\n        direction & input.offsetDirection &&\n        input.distance > this.options.threshold &&\n        input.maxPointers === this.options.pointers &&\n        abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n  }\n\n  emit(input) {\n    let direction = directionStr(input.offsetDirection);\n    if (direction) {\n      this.manager.emit(this.options.event + direction, input);\n    }\n\n    this.manager.emit(this.options.event, input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class PinchRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super({\n      event: 'pinch',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n\n  emit(input) {\n    if (input.scale !== 1) {\n      let inOut = input.scale < 1 ? 'in' : 'out';\n      input.additionalEvent = this.options.event + inOut;\n    }\n    super.emit(input);\n  }\n}\n", "import AttrRecognizer from './attribute';\nimport { TOUCH_ACTION_NONE } from '../touchactionjs/touchaction-Consts';\nimport { STATE_BEGAN } from '../recognizerjs/recognizer-consts';\n\n/**\n * @private\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nexport default class RotateRecognizer extends AttrRecognizer {\n  constructor(options = {}) {\n    super( {\n      event: 'rotate',\n      threshold: 0,\n      pointers: 2,\n      ...options,\n    });\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_NONE];\n  }\n\n  attrTest(input) {\n    return super.attrTest(input) &&\n        (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n  }\n}", "import Recognizer from '../recognizerjs/recognizer-constructor';\nimport {\n    STATE_RECOGNIZED,\n    STATE_FAILED\n} from '../recognizerjs/recognizer-consts';\nimport { now } from '../utils/utils-consts';\nimport { TOUCH_ACTION_AUTO } from '../touchactionjs/touchaction-Consts';\nimport {\n    INPUT_START,\n    INPUT_END,\n    INPUT_CANCEL\n} from '../inputjs/input-consts';\n\n/**\n * @private\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nexport default class PressRecognizer extends Recognizer {\n  constructor(options = {}) {\n    super({\n      event: 'press',\n      pointers: 1,\n      time: 251, // minimal time of the pointer to be pressed\n      threshold: 9, // a minimal movement is ok, but keep it low\n      ...options,\n    });\n    this._timer = null;\n    this._input = null;\n  }\n\n  getTouchAction() {\n    return [TOUCH_ACTION_AUTO];\n  }\n\n  process(input) {\n    let { options } = this;\n    let validPointers = input.pointers.length === options.pointers;\n    let validMovement = input.distance < options.threshold;\n    let validTime = input.deltaTime > options.time;\n\n    this._input = input;\n\n    // we only allow little movement\n    // and we've reached an end event, so a tap is possible\n    if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n      this.reset();\n    } else if (input.eventType & INPUT_START) {\n      this.reset();\n      this._timer = setTimeout(() => {\n        this.state = STATE_RECOGNIZED;\n        this.tryEmit();\n      }, options.time);\n    } else if (input.eventType & INPUT_END) {\n      return STATE_RECOGNIZED;\n    }\n    return STATE_FAILED;\n  }\n\n  reset() {\n    clearTimeout(this._timer);\n  }\n\n  emit(input) {\n    if (this.state !== STATE_RECOGNIZED) {\n      return;\n    }\n\n    if (input && (input.eventType & INPUT_END)) {\n      this.manager.emit(`${this.options.event}up`, input);\n    } else {\n      this._input.timeStamp = now();\n      this.manager.emit(this.options.event, this._input);\n    }\n  }\n}\n\n", "import { TOUCH_ACTION_COMPUTE } from \"./touchactionjs/touchaction-Consts\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport <PERSON>wipe<PERSON><PERSON>ognizer from \"./recognizers/swipe\";\nimport <PERSON>nch<PERSON><PERSON>ognizer from \"./recognizers/pinch\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON>ognizer from \"./recognizers/rotate\";\nimport <PERSON><PERSON><PERSON>ognizer from \"./recognizers/press\";\nimport {DIRECTION_HORIZONTAL} from \"./inputjs/input-consts\";\n\nexport default {\n\t/**\n\t * @private\n\t * set if DOM events are being triggered.\n\t * But this is slower and unused by simple implementations, so disabled by default.\n\t * @type {Boolean}\n\t * @default false\n\t */\n\tdomEvents: false,\n\n\t/**\n\t * @private\n\t * The value for the touchAction property/fallback.\n\t * When set to `compute` it will magically set the correct value based on the added recognizers.\n\t * @type {String}\n\t * @default compute\n\t */\n\ttouchAction: TOUCH_ACTION_COMPUTE,\n\n\t/**\n\t * @private\n\t * @type {Boolean}\n\t * @default true\n\t */\n\tenable: true,\n\n\t/**\n\t * @private\n\t * EXPERIMENTAL FEATURE -- can be removed/changed\n\t * Change the parent input target element.\n\t * If Null, then it is being set the to main element.\n\t * @type {Null|EventTarget}\n\t * @default null\n\t */\n\tinputTarget: null,\n\n\t/**\n\t * @private\n\t * force an input class\n\t * @type {Null|Function}\n\t * @default null\n\t */\n\tinputClass: null,\n\n\t/**\n\t * @private\n\t * Some CSS properties can be used to improve the working of Hammer.\n\t * Add them to this method and they will be set when creating a new Manager.\n\t * @namespace\n\t */\n\tcssProps: {\n\t\t/**\n\t\t * @private\n\t\t * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disable the Windows Phone grippers when pressing an element.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchSelect: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Disables the default callout shown when you touch and hold a touch target.\n\t\t * On iOS, when you touch and hold a touch target such as a link, Safari displays\n\t\t * a callout containing information about the link. This property allows you to disable that callout.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\ttouchCallout: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies whether zooming is enabled. Used by IE10>\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tcontentZooming: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n\t\t * @type {String}\n\t\t * @default 'none'\n\t\t */\n\t\tuserDrag: \"none\",\n\n\t\t/**\n\t\t * @private\n\t\t * Overrides the highlight color shown when the user taps a link or a JavaScript\n\t\t * clickable element in iOS. This property obeys the alpha value, if specified.\n\t\t * @type {String}\n\t\t * @default 'rgba(0,0,0,0)'\n\t\t */\n\t\ttapHighlightColor: \"rgba(0,0,0,0)\",\n\t},\n};\n\n/**\n * @private\n * Default recognizer setup when calling `Hammer()`\n * When creating a new Manager these will be skipped.\n * This is separated with other defaults because of tree-shaking.\n * @type {Array}\n */\nexport const preset = [\n  [RotateRecognizer, { enable: false }],\n  [PinchRecognizer, { enable: false }, ['rotate']],\n  [SwipeRecognizer, { direction: DIRECTION_HORIZONTAL }],\n  [PanRecognizer, { direction: DIRECTION_HORIZONTAL }, ['swipe']],\n  [TapRecognizer],\n  [TapRecognizer, { event: 'doubletap', taps: 2 }, ['tap']],\n  [PressRecognizer]\n];\n", "import assign from \"./utils/assign\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport createInputInstance from \"./inputjs/create-input-instance\";\nimport each from \"./utils/each\";\nimport inArray from \"./utils/in-array\";\nimport invokeArrayArg from \"./utils/invoke-array-arg\";\nimport splitStr from \"./utils/split-str\";\nimport prefixed from \"./utils/prefixed\";\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport {\n  STATE_BEGAN,\n  STATE_ENDED,\n  STATE_CHANGED,\n  STATE_RECOGNIZED,\n} from \"./recognizerjs/recognizer-consts\";\nimport defaults from \"./defaults\";\n\nconst STOP = 1;\nconst FORCED_STOP = 2;\n\n\n/**\n * @private\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n  const { element } = manager;\n\n  if (!element.style) {\n    return;\n  }\n  let prop;\n\n  each(manager.options.cssProps, (value, name) => {\n    prop = prefixed(element.style, name);\n    if (add) {\n      manager.oldCssProps[prop] = element.style[prop];\n      element.style[prop] = value;\n    } else {\n      element.style[prop] = manager.oldCssProps[prop] || \"\";\n    }\n  });\n  if (!add) {\n    manager.oldCssProps = {};\n  }\n}\n\n/**\n * @private\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n  const gestureEvent = document.createEvent(\"Event\");\n\n  gestureEvent.initEvent(event, true, true);\n  gestureEvent.gesture = data;\n  data.target.dispatchEvent(gestureEvent);\n}\n\n\n/**\n* @private\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Manager {\n  constructor(element, options) {\n    this.options = assign({}, defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, item => {\n      const recognizer = this.add(new (item[0])(item[1]));\n\n      item[2] && recognizer.recognizeWith(item[2]);\n      item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n  }\n\n\t/**\n\t * @private\n\t * set options\n\t * @param {Object} options\n\t * @returns {Manager}\n\t */\n  set(options) {\n    assign(this.options, options);\n\n    // Options that need a little more setup\n    if (options.touchAction) {\n      this.touchAction.update();\n    }\n    if (options.inputTarget) {\n      // Clean up existing event listeners and reinitialize\n      this.input.destroy();\n      this.input.target = options.inputTarget;\n      this.input.init();\n    }\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * stop recognizing for this session.\n\t * This session will be discarded, when a new [input]start event is fired.\n\t * When forced, the recognizer cycle is stopped immediately.\n\t * @param {Boolean} [force]\n\t */\n  stop(force) {\n    this.session.stopped = force ? FORCED_STOP : STOP;\n  }\n\n\t/**\n\t * @private\n\t * run the recognizers!\n\t * called by the inputHandler function on every movement of the pointers (touches)\n\t * it walks through all the recognizers and tries to detect the gesture that is being made\n\t * @param {Object} inputData\n\t */\n  recognize(inputData) {\n    const { session } = this;\n\n    if (session.stopped) {\n      return;\n    }\n\n    // run the touch-action polyfill\n    this.touchAction.preventDefaults(inputData);\n\n    let recognizer;\n    const { recognizers } = this;\n\n    // this holds the recognizer that is being recognized.\n    // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n    // if no recognizer is detecting a thing, it is set to `null`\n    let { curRecognizer } = session;\n\n    // reset when the last recognizer is recognized\n    // or when we're in a new session\n    if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n      session.curRecognizer = null;\n      curRecognizer = null;\n    }\n\n    let i = 0;\n\n    while (i < recognizers.length) {\n      recognizer = recognizers[i];\n\n      // find out if we are allowed try to recognize the input for this one.\n      // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n      // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n      //      that is being recognized.\n      // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n      //      this can be setup with the `recognizeWith()` method on the recognizer.\n      if (session.stopped !== FORCED_STOP && (// 1\n        !curRecognizer || recognizer === curRecognizer || // 2\n        recognizer.canRecognizeWith(curRecognizer))) { // 3\n        recognizer.recognize(inputData);\n      } else {\n        recognizer.reset();\n      }\n\n      // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n      // current active recognizer. but only if we don't already have an active recognizer\n      if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n        session.curRecognizer = recognizer;\n        curRecognizer = recognizer;\n      }\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * get a recognizer by its event name.\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Recognizer|Null}\n\t */\n  get(recognizer) {\n    if (recognizer instanceof Recognizer) {\n      return recognizer;\n    }\n\n    const { recognizers } = this;\n\n    for (let i = 0; i < recognizers.length; i++) {\n      if (recognizers[i].options.event === recognizer) {\n        return recognizers[i];\n      }\n    }\n    return null;\n  }\n\n\t/**\n\t * @private add a recognizer to the manager\n\t * existing recognizers with the same event name will be removed\n\t * @param {Recognizer} recognizer\n\t * @returns {Recognizer|Manager}\n\t */\n  add(recognizer) {\n    if (invokeArrayArg(recognizer, \"add\", this)) {\n      return this;\n    }\n\n    // remove existing\n    const existing = this.get(recognizer.options.event);\n\n    if (existing) {\n      this.remove(existing);\n    }\n\n    this.recognizers.push(recognizer);\n    recognizer.manager = this;\n\n    this.touchAction.update();\n    return recognizer;\n  }\n\n\t/**\n\t * @private\n\t * remove a recognizer by name or instance\n\t * @param {Recognizer|String} recognizer\n\t * @returns {Manager}\n\t */\n  remove(recognizer) {\n    if (invokeArrayArg(recognizer, \"remove\", this)) {\n      return this;\n    }\n\n    const targetRecognizer = this.get(recognizer);\n\n    // let's make sure this recognizer exists\n    if (recognizer) {\n      const { recognizers } = this;\n      const index = inArray(recognizers, targetRecognizer);\n\n      if (index !== -1) {\n        recognizers.splice(index, 1);\n        this.touchAction.update();\n      }\n    }\n\n    return this;\n  }\n\n\t/**\n\t * @private\n\t * bind event\n\t * @param {String} events\n\t * @param {Function} handler\n\t * @returns {EventEmitter} this\n\t */\n  on(events, handler) {\n    if (events === undefined || handler === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      handlers[event] = handlers[event] || [];\n      handlers[event].push(handler);\n    });\n    return this;\n  }\n\n\t/**\n\t * @private unbind event, leave emit blank to remove all handlers\n\t * @param {String} events\n\t * @param {Function} [handler]\n\t * @returns {EventEmitter} this\n\t */\n  off(events, handler) {\n    if (events === undefined) {\n      return this;\n    }\n\n    const { handlers } = this;\n\n    each(splitStr(events), event => {\n      if (!handler) {\n        delete handlers[event];\n      } else {\n        handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n      }\n    });\n    return this;\n  }\n\n\t/**\n\t * @private emit event to the listeners\n\t * @param {String} event\n\t * @param {Object} data\n\t */\n  emit(event, data) {\n    // we also want to trigger dom events\n    if (this.options.domEvents) {\n      triggerDomEvent(event, data);\n    }\n\n    // no handlers, so skip it all\n    const handlers = this.handlers[event] && this.handlers[event].slice();\n\n    if (!handlers || !handlers.length) {\n      return;\n    }\n\n    data.type = event;\n    data.preventDefault = function () {\n      data.srcEvent.preventDefault();\n    };\n\n    let i = 0;\n\n    while (i < handlers.length) {\n      handlers[i](data);\n      i++;\n    }\n  }\n\n\t/**\n\t * @private\n\t * destroy the manager and unbinds all events\n\t * it doesn't unbind dom events, that is the user own responsibility\n\t */\n  destroy() {\n    this.element && toggleCssProps(this, false);\n\n    this.handlers = {};\n    this.session = {};\n    this.input.destroy();\n    this.element = null;\n  }\n}\n", "import { SUPPORT_POINTER_EVENTS,SUPPORT_ONLY_TOUCH,SUPPORT_TOUCH } from './input-consts';\nimport inputHandler from './input-handler';\nimport PointerEventInput from '../input/pointerevent';\nimport TouchInput from '../input/touch';\nimport MouseInput from '../input/mouse';\nimport TouchMouseInput from '../input/touchmouse';\n\n/**\n * @private\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nexport default function createInputInstance(manager) {\n  let Type;\n  // let inputClass = manager.options.inputClass;\n  let { options:{ inputClass } } = manager;\n  if (inputClass) {\n    Type = inputClass;\n  } else if (SUPPORT_POINTER_EVENTS) {\n    Type = PointerEventInput;\n  } else if (SUPPORT_ONLY_TOUCH) {\n    Type = TouchInput;\n  } else if (!SUPPORT_TOUCH) {\n    Type = MouseInput;\n  } else {\n    Type = TouchMouseInput;\n  }\n  return new (Type)(manager, inputHandler);\n}\n", "import {\n    INPUT_START,\n    INPUT_MOVE,\n    INPUT_END,\n    INPUT_CANCEL,\n    INPUT_TYPE_TOUCH\n} from '../inputjs/input-consts';\nimport Input from '../inputjs/input-constructor';\nimport toArray from '../utils/to-array';\nimport uniqueArray from '../utils/unique-array';\n\nconst SINGLE_TOUCH_INPUT_MAP = {\n  touchstart: INPUT_START,\n  touchmove: INPUT_MOVE,\n  touchend: INPUT_END,\n  touchcancel: INPUT_CANCEL\n};\n\nconst SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nconst SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * @private\n * Touch events input\n * @constructor\n * @extends Input\n */\nexport default class SingleTouchInput extends Input {\n  constructor() {\n    var proto = SingleTouchInput.prototype;\n    proto.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    proto.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n\n    super(...arguments);\n    this.started = false;\n  }\n\n  handler(ev) {\n    let type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n    // should we handle the touch events?\n    if (type === INPUT_START) {\n      this.started = true;\n    }\n\n    if (!this.started) {\n      return;\n    }\n\n    let touches = normalizeSingleTouches.call(this, ev, type);\n\n    // when done, reset the started state\n    if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n      this.started = false;\n    }\n\n    this.callback(this.manager, type, {\n      pointers: touches[0],\n      changedPointers: touches[1],\n      pointerType: INPUT_TYPE_TOUCH,\n      srcEvent: ev\n    });\n  }\n}\n\n/**\n * @private\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n  let all = toArray(ev.touches);\n  let changed = toArray(ev.changedTouches);\n\n  if (type & (INPUT_END | INPUT_CANCEL)) {\n    all = uniqueArray(all.concat(changed), 'identifier', true);\n  }\n\n  return [all, changed];\n}\n", "/**\n * @private\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nexport default function deprecate(method, name, message) {\n  let deprecationMessage = `DEPRECATED METHOD: ${name}\\n${message} AT \\n`;\n  return function() {\n    let e = new Error('get-stack-trace');\n    let stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n        .replace(/^\\s+at\\s+/gm, '')\n        .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n    let log = window.console && (window.console.warn || window.console.log);\n    if (log) {\n      log.call(window.console, deprecationMessage, stack);\n    }\n    return method.apply(this, arguments);\n  };\n}\n", "import deprecate from './deprecate';\n/**\n * @private\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nconst extend = deprecate((dest, src, merge) => {\n  let keys = Object.keys(src);\n  let i = 0;\n  while (i < keys.length) {\n    if (!merge || (merge && dest[keys[i]] === undefined)) {\n      dest[keys[i]] = src[keys[i]];\n    }\n    i++;\n  }\n  return dest;\n}, 'extend', 'Use `assign`.');\n\nexport default extend;\n", "import deprecate from './deprecate';\nimport extend from './extend';\n/**\n * @private\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nconst merge = deprecate((dest, src) => {\n  return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\nexport default merge;\n", "import assign from './assign';\n/**\n * @private\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nexport default function inherit(child, base, properties) {\n  let baseP = base.prototype;\n  let childP;\n\n  childP = child.prototype = Object.create(baseP);\n  childP.constructor = child;\n  childP._super = baseP;\n\n  if (properties) {\n    assign(childP, properties);\n  }\n}\n", "/**\n * @private\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nexport default function bindFn(fn, context) {\n  return function boundFn() {\n    return fn.apply(context, arguments);\n  };\n}\n", "import Manager from \"./manager\";\nimport defaults, { preset } from \"./defaults\";\nimport assign from './utils/assign';\nimport {\n  INPUT_START,\n  INPUT_MOVE,\n  INPUT_END,\n  INPUT_CANCEL,\n  DIRECTION_NONE,\n  DIRECTION_LEFT,\n  DIRECTION_RIGHT,\n  DIRECTION_UP,\n  DIRECTION_DOWN,\n  DIRECTION_HORIZONTAL,\n  DIRECTION_VERTICAL,\n  DIRECTION_ALL,\n} from \"./inputjs/input-consts\";\nimport {\n  STATE_POSSIBLE,\n  STATE_BEGAN,\n  STATE_CHANGED,\n  STATE_ENDED,\n  STATE_RECOGNIZED,\n  STATE_CANCELLED,\n  STATE_FAILED,\n} from \"./recognizerjs/recognizer-consts\";\n\nimport Input from \"./inputjs/input-constructor\";\nimport TouchAction from \"./touchactionjs/touchaction-constructor\";\nimport TouchInput from \"./input/touch\";\nimport MouseInput from \"./input/mouse\";\nimport PointerEventInput from \"./input/pointerevent\";\nimport SingleTouchInput from \"./input/singletouch\";\nimport TouchMouseInput from \"./input/touchmouse\";\n\nimport Recognizer from \"./recognizerjs/recognizer-constructor\";\nimport AttrRecognizer from \"./recognizers/attribute\";\nimport TapRecognizer from \"./recognizers/tap\";\nimport PanRecognizer from \"./recognizers/pan\";\nimport SwipeRecognizer from \"./recognizers/swipe\";\nimport PinchRecognizer from \"./recognizers/pinch\";\nimport RotateRecognizer from \"./recognizers/rotate\";\nimport PressRecognizer from \"./recognizers/press\";\n\nimport addEventListeners from \"./utils/add-event-listeners\";\nimport removeEventListeners from \"./utils/remove-event-listeners\";\nimport each from \"./utils/each\";\nimport merge from \"./utils/merge\";\nimport extend from \"./utils/extend\";\nimport inherit from \"./utils/inherit\";\nimport bindFn from \"./utils/bind-fn\";\nimport prefixed from \"./utils/prefixed\";\nimport toArray from \"./utils/to-array\";\nimport uniqueArray from \"./utils/unique-array\";\nimport splitStr from \"./utils/split-str\";\nimport inArray from \"./utils/in-array\";\nimport boolOrFn from \"./utils/bool-or-fn\";\nimport hasParent from \"./utils/has-parent\";\n/**\n * @private\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nexport default class Hammer {\n\t/**\n   * @private\n   * @const {string}\n   */\n\tstatic VERSION = \"#__VERSION__#\";\n\tstatic DIRECTION_ALL = DIRECTION_ALL;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic DIRECTION_LEFT = DIRECTION_LEFT;\n\tstatic DIRECTION_RIGHT = DIRECTION_RIGHT;\n\tstatic DIRECTION_UP = DIRECTION_UP;\n\tstatic DIRECTION_HORIZONTAL = DIRECTION_HORIZONTAL;\n\tstatic DIRECTION_VERTICAL = DIRECTION_VERTICAL;\n\tstatic DIRECTION_NONE = DIRECTION_NONE;\n\tstatic DIRECTION_DOWN = DIRECTION_DOWN;\n\tstatic INPUT_START = INPUT_START;\n\tstatic INPUT_MOVE = INPUT_MOVE;\n  static INPUT_END = INPUT_END;\n\tstatic INPUT_CANCEL = INPUT_CANCEL;\n\tstatic STATE_POSSIBLE = STATE_POSSIBLE;\n\tstatic STATE_BEGAN = STATE_BEGAN;\n\tstatic STATE_CHANGED = STATE_CHANGED;\n\tstatic STATE_ENDED = STATE_ENDED;\n\tstatic STATE_RECOGNIZED = STATE_RECOGNIZED;\n\tstatic STATE_CANCELLED = STATE_CANCELLED;\n\tstatic STATE_FAILED = STATE_FAILED;\n\tstatic Manager = Manager;\n\tstatic Input = Input;\n\tstatic TouchAction = TouchAction;\n\tstatic TouchInput = TouchInput;\n\tstatic MouseInput = MouseInput;\n\tstatic PointerEventInput = PointerEventInput;\n\tstatic TouchMouseInput = TouchMouseInput;\n\tstatic SingleTouchInput = SingleTouchInput;\n\tstatic Recognizer = Recognizer;\n\tstatic AttrRecognizer = AttrRecognizer;\n\tstatic Tap = TapRecognizer;\n\tstatic Pan = PanRecognizer;\n\tstatic Swipe = SwipeRecognizer;\n\tstatic Pinch = PinchRecognizer;\n\tstatic Rotate = RotateRecognizer;\n\tstatic Press = PressRecognizer;\n\tstatic on = addEventListeners;\n\tstatic off = removeEventListeners;\n\tstatic each = each;\n\tstatic merge = merge;\n\tstatic extend = extend;\n\tstatic bindFn = bindFn;\n\tstatic assign = assign;\n\tstatic inherit = inherit;\n\tstatic bindFn = bindFn;\n\tstatic prefixed = prefixed;\n\tstatic toArray = toArray;\n\tstatic inArray = inArray;\n\tstatic uniqueArray = uniqueArray;\n\tstatic splitStr = splitStr;\n\tstatic boolOrFn = boolOrFn;\n\tstatic hasParent = hasParent;\n\tstatic addEventListeners = addEventListeners;\n\tstatic removeEventListeners = removeEventListeners;\n\tstatic defaults = assign({}, defaults, { preset });\n\tconstructor(element, options = {}) {\n\t\treturn new Manager(element, {\n\t\t\trecognizers: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        ...preset\n\t\t\t],\n\t\t\t...options,\n\t\t});\n\t}\n}\n"], "names": ["win", "Object", "assign", "target", "TypeError", "output", "index", "arguments", "length", "source", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "VENDOR_PREFIXES", "TEST_ELEMENT", "document", "style", "createElement", "TYPE_FUNCTION", "round", "Math", "abs", "now", "Date", "prefixed", "obj", "property", "prefix", "prop", "camelProp", "toUpperCase", "slice", "i", "window", "PREFIXED_TOUCH_ACTION", "NATIVE_TOUCH_ACTION", "undefined", "TOUCH_ACTION_COMPUTE", "TOUCH_ACTION_AUTO", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_NONE", "TOUCH_ACTION_PAN_X", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MAP", "touchMap", "cssSupports", "CSS", "supports", "for<PERSON>ach", "val", "getTouchActionProps", "SUPPORT_TOUCH", "SUPPORT_POINTER_EVENTS", "SUPPORT_ONLY_TOUCH", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_MOUSE", "COMPUTE_INTERVAL", "INPUT_START", "INPUT_END", "INPUT_CANCEL", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "PROPS_XY", "PROPS_CLIENT_XY", "each", "iterator", "context", "call", "boolOrFn", "args", "apply", "inStr", "str", "find", "indexOf", "TouchAction", "manager", "value", "set", "this", "compute", "element", "actions", "toLowerCase", "trim", "update", "options", "touchAction", "recognizers", "recognizer", "enable", "concat", "getTouchAction", "hasPanX", "hasPanY", "cleanTouchActions", "join", "preventDefaults", "input", "srcEvent", "direction", "offsetDirection", "session", "prevented", "preventDefault", "hasNone", "isTapPointer", "pointers", "isTapMovement", "distance", "isTapTouchTime", "deltaTime", "preventSrc", "hasParent", "node", "parent", "parentNode", "getCenter", "pointers<PERSON><PERSON><PERSON>", "x", "clientX", "y", "clientY", "simpleCloneInputData", "timeStamp", "center", "deltaX", "deltaY", "getDistance", "p1", "p2", "props", "sqrt", "getAngle", "atan2", "PI", "getDirection", "getVelocity", "computeInputData", "firstInput", "firstMultiple", "offset", "prevDel<PERSON>", "prevInput", "offsetCenter", "angle", "offsetDelta", "eventType", "start", "end", "overallVelocity", "overallVelocityX", "overallVelocityY", "scale", "rotation", "maxPointers", "velocity", "velocityX", "velocityY", "last", "lastInterval", "v", "computeIntervalInputData", "srcEventTarget", "<PERSON><PERSON><PERSON>", "path", "inputHandler", "pointersLen", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "isFinal", "emit", "recognize", "splitStr", "split", "addEventListeners", "types", "handler", "type", "addEventListener", "removeEventListeners", "removeEventListener", "getWindowForElement", "doc", "ownerDocument", "defaultView", "parentWindow", "Input", "callback", "self", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "init", "evEl", "ev<PERSON><PERSON><PERSON>", "evWin", "destroy", "inArray", "src", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "MSPointerEvent", "PointerEvent", "PointerEventInput", "proto", "prototype", "store", "_this", "pointerEvents", "removePointer", "eventTypeNormalized", "replace", "pointerType", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "button", "push", "splice", "toArray", "Array", "uniqueArray", "key", "sort", "results", "values", "a", "b", "TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "TouchInput", "targetIds", "touches", "targetTouches", "allTouches", "identifier", "changedTouches", "changedTargetTouches", "filter", "touch", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "MouseInput", "pressed", "which", "DEDUP_TIMEOUT", "DEDUP_DISTANCE", "setLastTouch", "eventData", "primaryTouch", "lastTouch", "lts", "lastTouches", "setTimeout", "TouchMouseInput", "inputEvent", "inputData", "isMouse", "sourceCapabilities", "firesTouchEvents", "t", "dx", "dy", "mouse", "invokeArrayArg", "arg", "fn", "isArray", "_uniqueId", "getRecognizerByNameIfManager", "otherRecognizer", "get", "stateStr", "state", "Recognizer", "id", "simultaneous", "requireFail", "recognizeWith", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "event", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "reset", "process", "TapRecognizer", "taps", "interval", "time", "threshold", "pos<PERSON><PERSON><PERSON><PERSON>", "pTime", "pCenter", "_timer", "_input", "count", "validPointers", "validMovement", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "_this2", "_this3", "clearTimeout", "tapCount", "AttrRecognizer", "_Recognizer", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "directionStr", "PanRecognizer", "pX", "pY", "directionTest", "hasMoved", "SwipeRecognizer", "_AttrRecognizer", "PinchRecognizer", "inOut", "RotateRecognizer", "PressRecognizer", "validTime", "domEvents", "inputClass", "cssProps", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "preset", "toggleCssProps", "add", "name", "oldCssProps", "Manager", "defaults", "handlers", "item", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "targetRecognizer", "on", "events", "off", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "SINGLE_TOUCH_INPUT_MAP", "SingleTouchInput", "started", "all", "changed", "deprecate", "method", "message", "deprecationMessage", "e", "Error", "stack", "log", "console", "warn", "extend", "dest", "merge", "keys", "inherit", "child", "base", "properties", "childP", "baseP", "create", "constructor", "_super", "bindFn", "Hammer", "VERSION", "INPUT_MOVE", "STATE_POSSIBLE", "STATE_BEGAN", "STATE_CHANGED", "STATE_ENDED", "STATE_RECOGNIZED", "STATE_CANCELLED", "STATE_FAILED", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press"], "mappings": ";;;;;;2lBAQA,ICNIA,IDOyB,mBAAlBC,OAAOC,OACP,SAAgBC,MACnBA,MAAAA,QACI,IAAIC,UAAU,sDAGlBC,EAASJ,OAAOE,GACXG,EAAQ,EAAGA,EAAQC,UAAUC,OAAQF,IAAS,KAC/CG,EAASF,UAAUD,MACrBG,MAAAA,MACG,IAAMC,KAAWD,EAChBA,EAAOE,eAAeD,KACxBL,EAAOK,GAAWD,EAAOC,WAK1BL,GAGAJ,OAAOC,OE5BZU,EAAkB,CAAC,GAAI,SAAU,MAAO,KAAM,KAAM,KACpDC,EAAmC,oBAAbC,SAA2B,CAACC,MAAO,IAAMD,SAASE,cAAc,OAEtFC,EAAgB,WAEdC,EAAeC,KAAfD,MAAOE,EAAQD,KAARC,IACPC,EAAQC,KAARD,ICCO,SAASE,EAASC,EAAKC,WAChCC,EACAC,EACAC,EAAYH,EAAS,GAAGI,cAAgBJ,EAASK,MAAM,GAEvDC,EAAI,EACDA,EAAInB,EAAgBJ,QAAQ,KAEjCmB,GADAD,EAASd,EAAgBmB,IACPL,EAASE,EAAYH,KAE3BD,SACHG,EAETI,KFfH/B,EAFqB,oBAAXgC,OAEJ,GAEAA,OGJA,IAAMC,EAAwBV,EAASV,EAAaE,MAAO,eACrDmB,OAAgDC,IAA1BF,ECAnC,IAAMG,EAAuB,UACvBC,EAAoB,OACpBC,EAA4B,eAC5BC,EAAoB,OACpBC,EAAqB,QACrBC,EAAqB,QACrBC,EDJS,eACRR,SACI,MAELS,EAAW,GACXC,EAAcZ,EAAOa,KAAOb,EAAOa,IAAIC,gBAC1C,OAAQ,eAAgB,QAAS,QAAS,cAAe,QAAQC,QAAQ,SAACC,UAIlEL,EAASK,IAAOJ,GAAcZ,EAAOa,IAAIC,SAAS,eAAgBE,KAEpEL,ECRgBM,GCNnBC,EAAiB,iBAAkBlB,EACnCmB,OAA8DhB,IAArCZ,EAASS,EAAQ,gBAC1CoB,EAAqBF,GAJN,wCAIoCG,KAAKC,UAAUC,WAElEC,EAAmB,QAEnBC,EAAmB,QAGnBC,EAAmB,GAEnBC,EAAc,EAEdC,EAAY,EACZC,EAAe,EAEfC,EAAiB,EACjBC,EAAiB,EACjBC,EAAkB,EAClBC,EAAe,EACfC,EAAiB,GAEjBC,EAAuBJ,EAAiBC,EACxCI,EAAqBH,EAAeC,EACpCG,EAAgBF,EAAuBC,EAEvCE,EAAW,CAAC,IAAK,KACjBC,EAAkB,CAAC,UAAW,WCzBrB,SAASC,EAAKhD,EAAKiD,EAAUC,OACtC3C,KAECP,KAIDA,EAAIuB,QACNvB,EAAIuB,QAAQ0B,EAAUC,QACjB,QAAmBvC,IAAfX,EAAIhB,WACbuB,EAAI,EACGA,EAAIP,EAAIhB,QACbiE,EAASE,KAAKD,EAASlD,EAAIO,GAAIA,EAAGP,GAClCO,aAGGA,KAAKP,EACRA,EAAIb,eAAeoB,IAAM0C,EAASE,KAAKD,EAASlD,EAAIO,GAAIA,EAAGP,GCflD,SAASoD,EAAS5B,EAAK6B,iBACzB7B,IAAQ/B,EACV+B,EAAI8B,MAAMD,GAAOA,EAAK,SAAkB1C,EAAW0C,GAErD7B,ECNM,SAAS+B,EAAMC,EAAKC,UACL,EAArBD,EAAIE,QAAQD,OCoBAE,wBACPC,EAASC,QACdD,QAAUA,OACVE,IAAID,8BAQXC,aAAID,GAEEA,IAAUjD,IACZiD,EAAQE,KAAKC,WAGXtD,GAAuBqD,KAAKH,QAAQK,QAAQ1E,OAAS2B,EAAiB2C,UACnED,QAAQK,QAAQ1E,MAAMkB,GAAyBoD,QAEjDK,QAAUL,EAAMM,cAAcC,UAOrCC,uBACOP,IAAIC,KAAKH,QAAQU,QAAQC,gBAQhCP,uBACME,EAAU,UACdlB,EAAKe,KAAKH,QAAQY,YAAa,SAACC,GAC1BrB,EAASqB,EAAWH,QAAQI,OAAQ,CAACD,MACvCP,EAAUA,EAAQS,OAAOF,EAAWG,qBCrD7B,SAA2BV,MAEpCX,EAAMW,EAASnD,UACVA,MAGL8D,EAAUtB,EAAMW,EAASlD,GACzB8D,EAAUvB,EAAMW,EAASjD,UAMzB4D,GAAWC,EACN/D,EAIL8D,GAAWC,EACND,EAAU7D,EAAqBC,EAIpCsC,EAAMW,EAASpD,GACVA,EAGFD,ED6BEkE,CAAkBb,EAAQc,KAAK,SAQxCC,yBAAgBC,OACRC,EAAaD,EAAbC,SACFC,EAAYF,EAAMG,mBAGlBtB,KAAKH,QAAQ0B,QAAQC,UACvBJ,EAASK,0BAILtB,EAAYH,KAAZG,QACFuB,EAAUlC,EAAMW,EAASnD,KAAuBG,EAAiBH,GACjE+D,EAAUvB,EAAMW,EAASjD,KAAwBC,EAAiBD,GAClE4D,EAAUtB,EAAMW,EAASlD,KAAwBE,EAAiBF,MAElEyE,EAAS,KAEPC,EAAyC,IAA1BR,EAAMS,SAAS3G,OAC9B4G,EAAgBV,EAAMW,SAAW,EACjCC,EAAiBZ,EAAMa,UAAY,OAEnCL,GAAgBE,GAAiBE,aAKnCjB,IAAWC,SAKXW,GACCX,GAAWM,EAAYzC,GACvBkC,GAAWO,EAAYxC,EACnBmB,KAAKiC,WAAWb,cAS3Ba,oBAAWb,QACJvB,QAAQ0B,QAAQC,WAAY,EACjCJ,EAASK,uBEpHE,SAASS,EAAUC,EAAMC,QAC/BD,GAAM,IACPA,IAASC,SACJ,EAETD,EAAOA,EAAKE,kBAEP,ECPM,SAASC,EAAUV,OAC5BW,EAAiBX,EAAS3G,UAGP,IAAnBsH,QACK,CACLC,EAAG7G,EAAMiG,EAAS,GAAGa,SACrBC,EAAG/G,EAAMiG,EAAS,GAAGe,kBAIrBH,EAAI,EACJE,EAAI,EACJlG,EAAI,EACDA,EAAI+F,GACTC,GAAKZ,EAASpF,GAAGiG,QACjBC,GAAKd,EAASpF,GAAGmG,QACjBnG,UAGK,CACLgG,EAAG7G,EAAM6G,EAAID,GACbG,EAAG/G,EAAM+G,EAAIH,ICrBF,SAASK,EAAqBzB,WAGvCS,EAAW,GACXpF,EAAI,EACDA,EAAI2E,EAAMS,SAAS3G,QACxB2G,EAASpF,GAAK,CACZiG,QAAS9G,EAAMwF,EAAMS,SAASpF,GAAGiG,SACjCE,QAAShH,EAAMwF,EAAMS,SAASpF,GAAGmG,UAEnCnG,UAGK,CACLqG,UAAW/G,IACX8F,SAAAA,EACAkB,OAAQR,EAAUV,GAClBmB,OAAQ5B,EAAM4B,OACdC,OAAQ7B,EAAM6B,QCjBH,SAASC,EAAYC,EAAIC,EAAIC,GACrCA,IACHA,EAAQrE,OAENyD,EAAIW,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5BV,EAAIS,EAAGC,EAAM,IAAMF,EAAGE,EAAM,WAEzBxH,KAAKyH,KAAMb,EAAIA,EAAME,EAAIA,GCPnB,SAASY,EAASJ,EAAIC,EAAIC,GAClCA,IACHA,EAAQrE,OAENyD,EAAIW,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5BV,EAAIS,EAAGC,EAAM,IAAMF,EAAGE,EAAM,WACN,IAAnBxH,KAAK2H,MAAMb,EAAGF,GAAW5G,KAAK4H,GCNxB,SAASC,EAAajB,EAAGE,UAClCF,IAAME,EACDnE,EAGL1C,EAAI2G,IAAM3G,EAAI6G,GACTF,EAAI,EAAIhE,EAAiBC,EAE3BiE,EAAI,EAAIhE,EAAeC,ECVjB,SAAS+E,EAAY1B,EAAWQ,EAAGE,SACzC,CACLF,EAAGA,EAAIR,GAAa,EACpBU,EAAGA,EAAIV,GAAa,GCST,SAAS2B,EAAiB9D,EAASsB,OAC1CI,EAAY1B,EAAZ0B,QACAK,EAAaT,EAAbS,SACOW,EAAmBX,EAA1B3G,OAGDsG,EAAQqC,aACXrC,EAAQqC,WAAahB,EAAqBzB,IAIvB,EAAjBoB,IAAuBhB,EAAQsC,cACjCtC,EAAQsC,cAAgBjB,EAAqBzB,GACjB,IAAnBoB,IACThB,EAAQsC,eAAgB,OChCWtC,EAASJ,EACxC2B,EAGFgB,EACAC,EACAC,ED6BEJ,EAA8BrC,EAA9BqC,WAAYC,EAAkBtC,EAAlBsC,cACdI,EAAeJ,EAAgBA,EAAcf,OAASc,EAAWd,OAEjEA,EAAS3B,EAAM2B,OAASR,EAAUV,GACtCT,EAAM0B,UAAY/G,IAClBqF,EAAMa,UAAYb,EAAM0B,UAAYe,EAAWf,UAE/C1B,EAAM+C,MAAQZ,EAASW,EAAcnB,GACrC3B,EAAMW,SAAWmB,EAAYgB,EAAcnB,GC3CNvB,ED6CtBA,EC5CTuB,GADwC3B,ED6CtBA,GC5ClB2B,OAGFgB,EAASvC,EAAQ4C,aAAe,GAChCJ,EAAYxC,EAAQwC,WAAa,GACjCC,EAAYzC,EAAQyC,WAAa,GAEjC7C,EAAMiD,YAAchG,GAAe4F,EAAUI,YAAc/F,IAC7D0F,EAAYxC,EAAQwC,UAAY,CAC9BvB,EAAGwB,EAAUjB,QAAU,EACvBL,EAAGsB,EAAUhB,QAAU,GAGzBc,EAASvC,EAAQ4C,YAAc,CAC7B3B,EAAGM,EAAON,EACVE,EAAGI,EAAOJ,IAIdvB,EAAM4B,OAASgB,EAAUvB,GAAKM,EAAON,EAAIsB,EAAOtB,GAChDrB,EAAM6B,OAASe,EAAUrB,GAAKI,EAAOJ,EAAIoB,EAAOpB,GDyBhDvB,EAAMG,gBAAkBmC,EAAatC,EAAM4B,OAAQ5B,EAAM6B,YEtC1BqB,EAAOC,ECAJD,EAAOC,EHwCrCC,EAAkBb,EAAYvC,EAAMa,UAAWb,EAAM4B,OAAQ5B,EAAM6B,QACvE7B,EAAMqD,iBAAmBD,EAAgB/B,EACzCrB,EAAMsD,iBAAmBF,EAAgB7B,EACzCvB,EAAMoD,gBAAmB1I,EAAI0I,EAAgB/B,GAAK3G,EAAI0I,EAAgB7B,GAAM6B,EAAgB/B,EAAI+B,EAAgB7B,EAEhHvB,EAAMuD,MAAQb,GE7CiBQ,EF6CQR,EAAcjC,SE5C9CqB,GAD+BqB,EF6CyB1C,GE5CxC,GAAI0C,EAAI,GAAItF,GAAmBiE,EAAYoB,EAAM,GAAIA,EAAM,GAAIrF,IF4CX,EAC3EmC,EAAMwD,SAAWd,GG9CiBQ,EH8CWR,EAAcjC,SG7CpD0B,GADkCgB,EH8C4B1C,GG7CjD,GAAI0C,EAAI,GAAItF,GAAmBsE,EAASe,EAAM,GAAIA,EAAM,GAAIrF,IH6CC,EAEjFmC,EAAMyD,YAAerD,EAAQyC,UAAsC7C,EAAMS,SAAS3G,OAClFsG,EAAQyC,UAAUY,YAAezD,EAAMS,SAAS3G,OAASsG,EAAQyC,UAAUY,YADlCzD,EAAMS,SAAS3G,OI/C3C,SAAkCsG,EAASJ,OAGpD0D,EACAC,EACAC,EACA1D,EALA2D,EAAOzD,EAAQ0D,cAAgB9D,EAC/Ba,EAAYb,EAAM0B,UAAYmC,EAAKnC,aAMnC1B,EAAMiD,YAAc9F,IAA6BH,EAAZ6D,QAAkDpF,IAAlBoI,EAAKH,UAAyB,KACjG9B,EAAS5B,EAAM4B,OAASiC,EAAKjC,OAC7BC,EAAS7B,EAAM6B,OAASgC,EAAKhC,OAE7BkC,EAAIxB,EAAY1B,EAAWe,EAAQC,GACvC8B,EAAYI,EAAE1C,EACduC,EAAYG,EAAExC,EACdmC,EAAYhJ,EAAIqJ,EAAE1C,GAAK3G,EAAIqJ,EAAExC,GAAMwC,EAAE1C,EAAI0C,EAAExC,EAC3CrB,EAAYoC,EAAaV,EAAQC,GAEjCzB,EAAQ0D,aAAe9D,OAGvB0D,EAAWG,EAAKH,SAChBC,EAAYE,EAAKF,UACjBC,EAAYC,EAAKD,UACjB1D,EAAY2D,EAAK3D,UAGnBF,EAAM0D,SAAWA,EACjB1D,EAAM2D,UAAYA,EAClB3D,EAAM4D,UAAYA,EAClB5D,EAAME,UAAYA,EJoBlB8D,CAAyB5D,EAASJ,OAK9BiE,EAFAxK,EAASiF,EAAQK,QACfkB,EAAWD,EAAMC,SAWnBc,EAPFkD,EADEhE,EAASiE,aACMjE,EAASiE,eAAe,GAChCjE,EAASkE,KACDlE,EAASkE,KAAK,GAEdlE,EAASxG,OAGEA,KAC5BA,EAASwK,GAEXjE,EAAMvG,OAASA,EKrEF,SAAS2K,EAAa1F,EAASuE,EAAWjD,OACnDqE,EAAcrE,EAAMS,SAAS3G,OAC7BwK,EAAqBtE,EAAMuE,gBAAgBzK,OAC3C0K,EAAWvB,EAAYhG,GAAgBoH,EAAcC,GAAuB,EAC5EG,EAAWxB,GAAa/F,EAAYC,IAAkBkH,EAAcC,GAAuB,EAE/FtE,EAAMwE,UAAYA,EAClBxE,EAAMyE,UAAYA,EAEdD,IACF9F,EAAQ0B,QAAU,IAKpBJ,EAAMiD,UAAYA,EAGlBT,EAAiB9D,EAASsB,GAG1BtB,EAAQgG,KAAK,eAAgB1E,GAE7BtB,EAAQiG,UAAU3E,GAClBtB,EAAQ0B,QAAQyC,UAAY7C,EC3Bf,SAAS4E,GAAStG,UACxBA,EAAIY,OAAO2F,MAAM,QCCX,SAASC,GAAkBrL,EAAQsL,EAAOC,GACvDlH,EAAK8G,GAASG,GAAQ,SAACE,GACrBxL,EAAOyL,iBAAiBD,EAAMD,GAAS,KCF5B,SAASG,GAAqB1L,EAAQsL,EAAOC,GAC1DlH,EAAK8G,GAASG,GAAQ,SAACE,GACrBxL,EAAO2L,oBAAoBH,EAAMD,GAAS,KCL/B,SAASK,GAAoBtG,OACtCuG,EAAMvG,EAAQwG,eAAiBxG,SAC3BuG,EAAIE,aAAeF,EAAIG,cAAgBnK,WCK5BoK,wBACPhH,EAASiH,OACfC,EAAO/G,UACNH,QAAUA,OACViH,SAAWA,OACX5G,QAAUL,EAAQK,aAClBtF,OAASiF,EAAQU,QAAQyG,iBAIzBC,WAAa,SAASC,GACrB7H,EAASQ,EAAQU,QAAQI,OAAQ,CAACd,KACpCkH,EAAKZ,QAAQe,SAIZC,kCAQPhB,uBAMAgB,qBACOC,MAAQnB,GAAkBjG,KAAKE,QAASF,KAAKoH,KAAMpH,KAAKiH,iBACxDI,UAAYpB,GAAkBjG,KAAKpF,OAAQoF,KAAKqH,SAAUrH,KAAKiH,iBAC/DK,OAASrB,GAAkBO,GAAoBxG,KAAKE,SAAUF,KAAKsH,MAAOtH,KAAKiH,eAOtFM,wBACOH,MAAQd,GAAqBtG,KAAKE,QAASF,KAAKoH,KAAMpH,KAAKiH,iBAC3DI,UAAYf,GAAqBtG,KAAKpF,OAAQoF,KAAKqH,SAAUrH,KAAKiH,iBAClEK,OAAShB,GAAqBE,GAAoBxG,KAAKE,SAAUF,KAAKsH,MAAOtH,KAAKiH,kBChD5E,SAASO,GAAQC,EAAK/H,EAAMgI,MACrCD,EAAI9H,UAAY+H,SACXD,EAAI9H,QAAQD,WAEflD,EAAI,EACDA,EAAIiL,EAAIxM,QAAQ,IAChByM,GAAaD,EAAIjL,GAAGkL,IAAchI,IAAWgI,GAAaD,EAAIjL,KAAOkD,SACjElD,EAETA,WAEM,MCLNmL,GAAoB,CACxBC,YAAaxJ,EACbyJ,YzBCiB,EyBAjBC,UAAWzJ,EACX0J,cAAezJ,EACf0J,WAAY1J,GAIR2J,GAAyB,GAC1BhK,IzBdkB,QyBgBlBC,IzBdqB,UyBkBtBgK,GAAyB,cACzBC,GAAwB,sCAGxB1L,EAAO2L,iBAAmB3L,EAAO4L,eACnCH,GAAyB,gBACzBC,GAAwB,iDASLG,kCAEbC,EAAQD,EAAkBE,iBAE9BD,EAAMnB,KAAOc,GACbK,EAAMjB,MAAQa,mBACLnN,kBACJyN,MAASC,EAAK7I,QAAQ0B,QAAQoH,cAAgB,+BAQrDxC,iBAAQe,OACAuB,EAAUzI,KAAVyI,MACFG,GAAgB,EAEhBC,EAAsB3B,EAAGd,KAAKhG,cAAc0I,QAAQ,KAAM,IAC1D1E,EAAYuD,GAAkBkB,GAC9BE,EAAcd,GAAuBf,EAAG6B,cAAgB7B,EAAG6B,YAE3DC,EAAWD,IAAgB9K,EAG3BgL,EAAazB,GAAQiB,EAAOvB,EAAGgC,UAAW,aAG1C9E,EAAYhG,IAA8B,IAAd8I,EAAGiC,QAAgBH,GAC7CC,EAAa,IACfR,EAAMW,KAAKlC,GACX+B,EAAaR,EAAMxN,OAAS,GAErBmJ,GAAa/F,EAAYC,KAClCsK,GAAgB,GAIdK,EAAa,IAKjBR,EAAMQ,GAAc/B,OAEfJ,SAAS9G,KAAKH,QAASuE,EAAW,CACrCxC,SAAU6G,EACV/C,gBAAiB,CAACwB,GAClB6B,YAAAA,EACA3H,SAAU8F,IAGR0B,GAEFH,EAAMY,OAAOJ,EAAY,QAvDgBpC,GCvChC,SAASyC,GAAQrN,UACvBsN,MAAMf,UAAUjM,MAAM6C,KAAKnD,EAAK,GCG1B,SAASuN,GAAY/B,EAAKgC,EAAKC,WACxCC,EAAU,GACVC,EAAS,GACTpN,EAAI,EAEDA,EAAIiL,EAAIxM,QAAQ,KACjBwC,EAAMgM,EAAMhC,EAAIjL,GAAGiN,GAAOhC,EAAIjL,GAC9BgL,GAAQoC,EAAQnM,GAAO,GACzBkM,EAAQP,KAAK3B,EAAIjL,IAEnBoN,EAAOpN,GAAKiB,EACZjB,WAGEkN,IAIAC,EAHGF,EAGOE,EAAQD,KAAK,SAACG,EAAGC,UAClBD,EAAEJ,GAAOK,EAAEL,KAHVE,EAAQD,QAQfC,MCtBHI,GAAkB,CACtBC,WAAY5L,EACZ6L,U5BGiB,E4BFjBC,SAAU7L,EACV8L,YAAa7L,GAWM8L,yCAEjBA,EAAW5B,UAAUnB,SAVG,4DAWfrM,kBACJqP,UAAY,+BAGnBlE,iBAAQe,OACFd,EAAO2D,GAAgB7C,EAAGd,MAC1BkE,EAqBR,SAAoBpD,EAAId,OAUlB5J,EACA+N,EAVAC,EAAalB,GAAQpC,EAAGoD,SACtBD,EAAcrK,KAAdqK,aAGFjE,G5B7Ca,E4B6CLhI,IAAmD,IAAtBoM,EAAWvP,cAClDoP,EAAUG,EAAW,GAAGC,aAAc,EAC/B,CAACD,EAAYA,OAKlBE,EAAiBpB,GAAQpC,EAAGwD,gBAC5BC,EAAuB,GACrB/P,EAAWoF,KAAXpF,UAGN2P,EAAgBC,EAAWI,OAAO,SAACC,UAC1B3I,EAAU2I,EAAMjQ,OAAQA,KAI7BwL,IAAShI,MACX5B,EAAI,EACGA,EAAI+N,EAActP,QACvBoP,EAAUE,EAAc/N,GAAGiO,aAAc,EACzCjO,IAKJA,EAAI,OACGA,EAAIkO,EAAezP,QACpBoP,EAAUK,EAAelO,GAAGiO,aAC9BE,EAAqBvB,KAAKsB,EAAelO,IAIvC4J,GAAQ/H,EAAYC,WACf+L,EAAUK,EAAelO,GAAGiO,YAErCjO,WAGGmO,EAAqB1P,OAInB,CAELuO,GAAYe,EAAc3J,OAAO+J,GAAuB,cAAc,GACtEA,WAxEyBvL,KAAKY,KAAMkH,EAAId,GACnCkE,QAIAxD,SAAS9G,KAAKH,QAASuG,EAAM,CAChCxE,SAAU0I,EAAQ,GAClB5E,gBAAiB4E,EAAQ,GACzBvB,YAAa9K,EACbmD,SAAU8F,QAlBwBL,OCnBlCiE,GAAkB,CACtBC,UAAW3M,EACX4M,U7BOiB,E6BNjBC,QAAS5M,GAYU6M,kCAEb3C,EAAQ2C,EAAW1C,iBACvBD,EAAMnB,KAZmB,YAazBmB,EAAMjB,MAZkB,oCAcftM,kBACJmQ,SAAU,8BAQjBhF,iBAAQe,OACF9C,EAAY0G,GAAgB5D,EAAGd,MAG/BhC,EAAYhG,GAA6B,IAAd8I,EAAGiC,cAC3BgC,SAAU,G7B1BF,E6B6BX/G,GAAuC,IAAb8C,EAAGkE,QAC/BhH,EAAY/F,GAIT2B,KAAKmL,UAIN/G,EAAY/F,SACT8M,SAAU,QAGZrE,SAAS9G,KAAKH,QAASuE,EAAW,CACrCxC,SAAU,CAACsF,GACXxB,gBAAiB,CAACwB,GAClB6B,YAAa7K,EACbkD,SAAU8F,SAxCwBL,GCDlCwE,GAAgB,KAChBC,GAAiB,GAEvB,SAASC,GAAaC,OACKX,EAAWW,EAA7B9F,sBAEJmF,EAAMJ,aAAezK,KAAKyL,aAAc,KACrCC,EAAY,CAAElJ,EAAGqI,EAAMpI,QAASC,EAAGmI,EAAMlI,SACzCgJ,EAAM3L,KAAK4L,iBAEZA,YAAYxC,KAAKsC,GAWtBG,WARwB,eACjBrP,EAAImP,EAAIhM,QAAQ+L,IAEb,EAALlP,GACHmP,EAAItC,OAAO7M,EAAG,IAIY6O,SA8BTS,4CACRjM,EAASiH,8BACdjH,EAASiH,UAehBX,QAAU,SAACtG,EAASkM,EAAYC,OACzBhD,EAAWgD,EAAUjD,cAAgB9K,EACrCgO,EAAWD,EAAUjD,cAAgB7K,OAEvC+N,GAAWD,EAAUE,oBAAsBF,EAAUE,mBAAmBC,sBAKxEnD,GAnDN,SAAuB5E,EAAWoH,GAC7BpH,EAAYhG,QACVqN,aAAeD,EAAU9F,gBAAgB,GAAG+E,WACjDc,GAAanM,KAAKY,KAAMwL,IACdpH,GAAa/F,EAAYC,IACnCiN,GAAanM,KAAKY,KAAMwL,KA+CTpM,aAAW2M,EAAYC,QAC/B,GAAIC,GA7Cb,SAA0BT,WACnBhJ,EAAIgJ,EAAUpK,SAASqB,QACvBC,EAAI8I,EAAUpK,SAASuB,QAEpBnG,EAAI,EAAGA,EAAIwD,KAAK4L,YAAY3Q,OAAQuB,IAAK,KAC3C4P,EAAIpM,KAAK4L,YAAYpP,GACrB6P,EAAKzQ,KAAKC,IAAI2G,EAAI4J,EAAE5J,GACpB8J,EAAK1Q,KAAKC,IAAI6G,EAAI0J,EAAE1J,MAEtB2J,GAAMf,IAAkBgB,GAAMhB,UAC1B,SAGF,GAgCiClM,aAAW4M,YAI7ClF,SAASjH,EAASkM,EAAYC,OA5B9BnB,MAAQ,IAAIT,GAAW1B,EAAK7I,QAAS6I,EAAKvC,WAC1CoG,MAAQ,IAAIrB,GAAWxC,EAAK7I,QAAS6I,EAAKvC,WAC1CsF,aAAe,OACfG,YAAc,+BAgCpBrE,wBACMsD,MAAMtD,eACNgF,MAAMhF,cAzCgCV,MC9D9B,SAAS2F,GAAeC,EAAKC,EAAIvN,WAC1CoK,MAAMoD,QAAQF,KAChBxN,EAAKwN,EAAKtN,EAAQuN,GAAKvN,IAChB,GCdX,ICKIyN,GAAY,ECED,SAASC,GAA6BC,EAAiBpM,OAC9Db,EAAYa,EAAZb,eACFA,EACKA,EAAQkN,IAAID,GAEdA,ECCM,SAASE,GAASC,UHRT,GGSlBA,EACK,SHZS,EGaPA,EACF,MHfW,EGgBTA,EACF,OHlBS,EGmBPA,EACF,QAEF,OC8BYC,yBACP3M,YAAAA,IAAAA,EAAU,SACfA,WACHI,QAAQ,GACLJ,QAGA4M,GHrDAP,UGuDA/M,QAAU,UAGVoN,MJjEc,OIkEdG,aAAe,QACfC,YAAc,8BASrBtN,aAAIQ,UACF5F,EAAOqF,KAAKO,QAASA,QAGhBV,SAAWG,KAAKH,QAAQW,YAAYF,SAClCN,QASTsN,uBAAcR,MACRN,GAAeM,EAAiB,gBAAiB9M,aAC5CA,SAGHoN,EAAiBpN,KAAjBoN,oBAEDA,GADLN,EAAkBD,GAA6BC,EAAiB9M,OAC9BmN,MAChCC,EAAaN,EAAgBK,IAAML,GACnBQ,cAActN,MAEzBA,QASTuN,2BAAkBT,UACZN,GAAeM,EAAiB,oBAAqB9M,QAIzD8M,EAAkBD,GAA6BC,EAAiB9M,aACzDA,KAAKoN,aAAaN,EAAgBK,KAJhCnN,QAcXwN,wBAAeV,MACTN,GAAeM,EAAiB,iBAAkB9M,aAC7CA,SAGHqN,EAAgBrN,KAAhBqN,mBAEyC,IAA3C7F,GAAQ6F,EADZP,EAAkBD,GAA6BC,EAAiB9M,SAE9DqN,EAAYjE,KAAK0D,GACjBA,EAAgBU,eAAexN,OAE1BA,QASTyN,4BAAmBX,MACbN,GAAeM,EAAiB,qBAAsB9M,aACjDA,KAGT8M,EAAkBD,GAA6BC,EAAiB9M,UAC5DjF,EAAQyM,GAAQxH,KAAKqN,YAAaP,UACzB,EAAT/R,QACGsS,YAAYhE,OAAOtO,EAAO,GAE1BiF,QAQT0N,qCACmC,EAA1B1N,KAAKqN,YAAYpS,UAS1B0S,0BAAiBb,WACN9M,KAAKoN,aAAaN,EAAgBK,OAS7CtH,cAAK1E,OACC4F,EAAO/G,KACLiN,EAAUjN,KAAViN,eAEGpH,EAAK+H,GACZ7G,EAAKlH,QAAQgG,KAAK+H,EAAOzM,GAIvB8L,EJ9LY,GI+LdpH,EAAKkB,EAAKxG,QAAQqN,MAAQZ,GAASC,IAGrCpH,EAAKkB,EAAKxG,QAAQqN,OAEdzM,EAAM0M,iBACRhI,EAAK1E,EAAM0M,iBJrMG,GIyMZZ,GACFpH,EAAKkB,EAAKxG,QAAQqN,MAAQZ,GAASC,OAWvCa,iBAAQ3M,MACFnB,KAAK+N,iBACA/N,KAAK6F,KAAK1E,QAGd8L,MJvNY,MI+NnBc,2BACMvR,EAAI,EACDA,EAAIwD,KAAKqN,YAAYpS,QAAQ,SAC5B+E,KAAKqN,YAAY7Q,GAAGyQ,cACjB,EAETzQ,WAEK,KAQTsJ,mBAAUkG,OAGJgC,EAAiBrT,EAAO,GAAIqR,OAG3B3M,EAASW,KAAKO,QAAQI,OAAQ,CAACX,KAAMgO,gBACnCC,kBACAhB,MJvPU,OI4PbjN,KAAKiN,aACFA,MJnQY,QIsQdA,MAAQjN,KAAKkO,QAAQF,MAItBhO,KAAKiN,YACFa,QAAQE,MAcjBE,iBAAQlC,OASRnL,8BAQAoN,wBCrRmBE,0BACP5N,yBAAAA,IAAAA,EAAU,sBAElBqN,MAAO,MACPhM,SAAU,EACVwM,KAAM,EACNC,SAAU,IACVC,KAAM,IACNC,UAAW,EACXC,aAAc,IACXjO,WAKAkO,OAAQ,IACRC,SAAU,IAEVC,OAAS,OACTC,OAAS,OACTC,MAAQ,sCAGfhO,gCACS,CAAC9D,MAGVmR,iBAAQ/M,cACAZ,EAAYP,KAAZO,QAEFuO,EAAgB3N,EAAMS,SAAS3G,SAAWsF,EAAQqB,SAClDmN,EAAgB5N,EAAMW,SAAWvB,EAAQgO,UACzCS,EAAiB7N,EAAMa,UAAYzB,EAAQ+N,aAE1CL,QAEA9M,EAAMiD,UAAYhG,GAAgC,IAAf4B,KAAK6O,aACpC7O,KAAKiP,iBAKVF,GAAiBC,GAAkBF,EAAe,IAChD3N,EAAMiD,YAAc/F,SACf2B,KAAKiP,kBAGVC,GAAgBlP,KAAKyO,OAAStN,EAAM0B,UAAY7C,KAAKyO,MAAQlO,EAAQ8N,SACrEc,GAAiBnP,KAAK0O,SAAWzL,EAAYjD,KAAK0O,QAASvN,EAAM2B,QAAUvC,EAAQiO,qBAElFC,MAAQtN,EAAM0B,eACd6L,QAAUvN,EAAM2B,OAEhBqM,GAAkBD,OAGhBL,OAAS,OAFTA,MAAQ,OAKVD,OAASzN,EAKG,IADFnB,KAAK6O,MAAQtO,EAAQ6N,YAI7BpO,KAAK0N,2BAGHiB,OAAS9C,WAAW,WACvBuD,EAAKnC,MLzFG,EK0FRmC,EAAKtB,WACJvN,EAAQ8N,UL7FD,GAEA,SAGC,MKgGnBY,8CACON,OAAS9C,WAAW,WACvBwD,EAAKpC,MLlGU,IKmGdjN,KAAKO,QAAQ8N,ULnGC,MKuGnBJ,iBACEqB,aAAatP,KAAK2O,WAGpB9I,gBL9GkB,IK+GZ7F,KAAKiN,aACF2B,OAAOW,SAAWvP,KAAK6O,WACvBhP,QAAQgG,KAAK7F,KAAKO,QAAQqN,MAAO5N,KAAK4O,aA/FN1B,ICFtBsC,0BACPjP,mBAAAA,IAAAA,EAAU,IACpBkP,eACE7N,SAAU,GACPrB,4CAWPmP,kBAASvO,OACHwO,EAAiB3P,KAAKO,QAAQqB,gBACR,IAAnB+N,GAAwBxO,EAAMS,SAAS3G,SAAW0U,KAU3DzB,iBAAQ/M,OACA8L,EAAUjN,KAAViN,MACA7I,EAAcjD,EAAdiD,UAEFwL,IAAe3C,EACf4C,EAAU7P,KAAK0P,SAASvO,UAGxByO,IAAiBxL,EAAY9F,IAAiBuR,GNjD9B,GMkDX5C,EACE2C,GAAgBC,EACrBzL,EAAY/F,ENtDF,EMuDL4O,ENzDK,EM0DDA,ENzDG,EM4DTA,EN7DO,EAKC,OMauBC,ICN7B,SAAS4C,GAAazO,UAC/BA,IAAc1C,EACT,OACE0C,IAAc3C,EAChB,KACE2C,IAAc7C,EAChB,OACE6C,IAAc5C,EAChB,QAEF,OCDYsR,0BACPxP,yBAAAA,IAAAA,EAAU,sBAElBqN,MAAO,MACPW,UAAW,GACX3M,SAAU,EACVP,UAAWvC,GACRyB,WAEAyP,GAAK,OACLC,GAAK,yCAGZpP,8BACkBQ,EAAgBrB,KAA1BO,QAAUc,UACZlB,EAAU,UACVkB,EAAYzC,GACduB,EAAQiJ,KAAKlM,GAEXmE,EAAYxC,GACdsB,EAAQiJ,KAAKnM,GAERkD,KAGT+P,uBAAc/O,OACNZ,EAAYP,KAAZO,QACF4P,GAAW,EACTrO,EAAaX,EAAbW,SACAT,EAAcF,EAAdE,UACFmB,EAAIrB,EAAM4B,OACVL,EAAIvB,EAAM6B,cAGR3B,EAAYd,EAAQc,YAItBS,EAHEvB,EAAQc,UAAYzC,GACtByC,EAAmB,IAANmB,EAAWjE,EAAkBiE,EAAI,EAAKhE,EAAiBC,EACpE0R,EAAW3N,IAAMxC,KAAKgQ,GACXpU,KAAKC,IAAIsF,EAAM4B,UAE1B1B,EAAmB,IAANqB,EAAWnE,EAAkBmE,EAAI,EAAKhE,EAAeC,EAClEwR,EAAWzN,IAAM1C,KAAKiQ,GACXrU,KAAKC,IAAIsF,EAAM6B,UAG9B7B,EAAME,UAAYA,EACX8O,GAAYrO,EAAWvB,EAAQgO,WAAalN,EAAYd,EAAQc,aAGzEqO,kBAASvO,UACAqO,GAAehH,UAAUkH,SAAStQ,KAAKY,KAAMmB,KRvEpC,OQwEN8L,SRxEM,EQwEoBjN,KAAKiN,QAAwBjN,KAAKkQ,cAAc/O,OAGtF0E,cAAK1E,QAEE6O,GAAK7O,EAAM4B,YACXkN,GAAK9O,EAAM6B,WAEZ3B,EAAYyO,GAAa3O,EAAME,WAE/BA,IACFF,EAAM0M,gBAAkB7N,KAAKO,QAAQqN,MAAQvM,eAEzCwE,eAAK1E,OAhE4BqO,ICRtBY,0BACP7P,mBAAAA,IAAAA,EAAU,IACpB8P,eACEzC,MAAO,QACPW,UAAW,GACX1J,SAAU,GACVxD,UAAWzC,EAAuBC,EAClC+C,SAAU,GACPrB,4CAIPM,iCACSkP,GAAcvH,UAAU3H,eAAezB,KAAKY,SAGrD0P,kBAASvO,OAEH0D,EADExD,EAAcrB,KAAKO,QAAnBc,iBAGFA,GAAazC,EAAuBC,GACtCgG,EAAW1D,EAAMoD,gBACRlD,EAAYzC,EACrBiG,EAAW1D,EAAMqD,iBACRnD,EAAYxC,IACrBgG,EAAW1D,EAAMsD,kBAGZ4L,YAAMX,mBAASvO,IAClBE,EAAYF,EAAMG,iBAClBH,EAAMW,SAAW9B,KAAKO,QAAQgO,WAC9BpN,EAAMyD,cAAgB5E,KAAKO,QAAQqB,UACnC/F,EAAIgJ,GAAY7E,KAAKO,QAAQsE,UAAY1D,EAAMiD,UAAY/F,KAGjEwH,cAAK1E,OACCE,EAAYyO,GAAa3O,EAAMG,iBAC/BD,QACGxB,QAAQgG,KAAK7F,KAAKO,QAAQqN,MAAQvM,EAAWF,QAG/CtB,QAAQgG,KAAK7F,KAAKO,QAAQqN,MAAOzM,OAzCGqO,ICHxBc,0BACP/P,mBAAAA,IAAAA,EAAU,IACpB8P,eACEzC,MAAO,QACPW,UAAW,EACX3M,SAAU,GACPrB,4CAIPM,gCACS,CAAC7D,MAGV0S,kBAASvO,UACAkP,YAAMX,mBAASvO,KACjBvF,KAAKC,IAAIsF,EAAMuD,MAAQ,GAAK1E,KAAKO,QAAQgO,WV1B9B,EU0B2CvO,KAAKiN,UAGlEpH,cAAK1E,MACiB,IAAhBA,EAAMuD,MAAa,KACjB6L,EAAQpP,EAAMuD,MAAQ,EAAI,KAAO,MACrCvD,EAAM0M,gBAAkB7N,KAAKO,QAAQqN,MAAQ2C,cAEzC1K,eAAK1E,OAxB8BqO,ICAxBgB,0BACPjQ,mBAAAA,IAAAA,EAAU,IACpB8P,eACEzC,MAAO,SACPW,UAAW,EACX3M,SAAU,GACPrB,4CAIPM,gCACS,CAAC7D,MAGV0S,kBAASvO,UACAkP,YAAMX,mBAASvO,KACjBvF,KAAKC,IAAIsF,EAAMwD,UAAY3E,KAAKO,QAAQgO,WX1B7B,EW0B0CvO,KAAKiN,WAhBrBuC,ICSzBiB,0BACPlQ,yBAAAA,IAAAA,EAAU,sBAElBqN,MAAO,QACPhM,SAAU,EACV0M,KAAM,IACNC,UAAW,GACRhO,WAEAoO,OAAS,OACTC,OAAS,yCAGhB/N,gCACS,CAAC/D,MAGVoR,iBAAQ/M,cACAZ,EAAYP,KAAZO,QACFuO,EAAgB3N,EAAMS,SAAS3G,SAAWsF,EAAQqB,SAClDmN,EAAgB5N,EAAMW,SAAWvB,EAAQgO,UACzCmC,EAAYvP,EAAMa,UAAYzB,EAAQ+N,aAErCM,OAASzN,GAIT4N,IAAkBD,GAAkB3N,EAAMiD,WAAa/F,EAAYC,KAAkBoS,OACnFzC,aACA,GAAI9M,EAAMiD,UAAYhG,OACtB6P,aACAU,OAAS9C,WAAW,WACvBuD,EAAKnC,MZjDO,EYkDZmC,EAAKtB,WACJvN,EAAQ+N,WACN,GAAInN,EAAMiD,UAAY/F,SZpDb,SAGC,MYuDnB4P,iBACEqB,aAAatP,KAAK2O,WAGpB9I,cAAK1E,GZ9Da,IY+DZnB,KAAKiN,QAIL9L,GAAUA,EAAMiD,UAAY/F,OACzBwB,QAAQgG,KAAQ7F,KAAKO,QAAQqN,WAAWzM,SAExCyN,OAAO/L,UAAY/G,SACnB+D,QAAQgG,KAAK7F,KAAKO,QAAQqN,MAAO5N,KAAK4O,cAtDJ1B,OCX9B,CAQdyD,WAAW,EASXnQ,YAAa3D,EAOb8D,QAAQ,EAURqG,YAAa,KAQb4J,WAAY,KAQZC,SAAU,CAOTC,WAAY,OAQZC,YAAa,OAUbC,aAAc,OAQdC,eAAgB,OAQhBC,SAAU,OASVC,kBAAmB,kBAWRC,GAAS,CACpB,CAACZ,GAAkB,CAAE7P,QAAQ,IAC7B,CAAC2P,GAAiB,CAAE3P,QAAQ,GAAS,CAAC,WACtC,CAACyP,GAAiB,CAAE/O,UAAWzC,IAC/B,CAACmR,GAAe,CAAE1O,UAAWzC,GAAwB,CAAC,UACtD,CAACuP,IACD,CAACA,GAAe,CAAEP,MAAO,YAAaQ,KAAM,GAAK,CAAC,QAClD,CAACqC,KCpGH,SAASY,GAAexR,EAASyR,OAM3BlV,EALI8D,EAAYL,EAAZK,QAEHA,EAAQ1E,QAKbyD,EAAKY,EAAQU,QAAQsQ,SAAU,SAAC/Q,EAAOyR,GACrCnV,EAAOJ,EAASkE,EAAQ1E,MAAO+V,GAG7BrR,EAAQ1E,MAAMY,GAFZkV,GACFzR,EAAQ2R,YAAYpV,GAAQ8D,EAAQ1E,MAAMY,GACpB0D,GAEAD,EAAQ2R,YAAYpV,IAAS,KAGlDkV,IACHzR,EAAQ2R,YAAc,SA0BLC,yBACPvR,EAASK,OC1DqBV,cD2DnCU,QAAU5F,EAAO,GAAI+W,GAAUnR,GAAW,SAE1CA,QAAQyG,YAAchH,KAAKO,QAAQyG,aAAe9G,OAElDyR,SAAW,QACXpQ,QAAU,QACVd,YAAc,QACd+Q,YAAc,QAEdtR,QAAUA,OACViB,MCtDA,KAfmCtB,EDqEPG,MClE7BO,QAAUqQ,aAGLhT,EACF0K,GACEzK,EACFuM,GACGzM,EAGHmO,GAFAZ,KAISrL,EAAS0F,QDuDpB/E,YAAc,IAAIZ,EAAYI,KAAMA,KAAKO,QAAQC,aAEtD6Q,GAAerR,MAAM,GAErBf,EAAKe,KAAKO,QAAQE,YAAa,SAAAmR,OACvBlR,EAAagI,EAAK4I,IAAI,IAAKM,EAAK,GAAIA,EAAK,KAE/CA,EAAK,IAAMlR,EAAW4M,cAAcsE,EAAK,IACzCA,EAAK,IAAMlR,EAAW8M,eAAeoE,EAAK,KACzC5R,iCASLD,aAAIQ,UACF5F,EAAOqF,KAAKO,QAASA,GAGjBA,EAAQC,kBACLA,YAAYF,SAEfC,EAAQyG,mBAEL7F,MAAMoG,eACNpG,MAAMvG,OAAS2F,EAAQyG,iBACvB7F,MAAMgG,QAENnH,QAUT6R,cAAKC,QACEvQ,QAAQwQ,QAAUD,EA5GP,EADP,KAuHXhM,mBAAUkG,OACAzK,EAAYvB,KAAZuB,YAEJA,EAAQwQ,aAORrR,OAFCF,YAAYU,gBAAgB8K,OAGzBvL,EAAgBT,KAAhBS,YAKFuR,EAAkBzQ,EAAlByQ,gBAIDA,GAAkBA,GdzJP,EcyJwBA,EAAc/E,SAEpD+E,EADAzQ,EAAQyQ,cAAgB,cAItBxV,EAAI,EAEDA,EAAIiE,EAAYxF,QACrByF,EAAaD,EAAYjE,GAlJX,IA0JV+E,EAAQwQ,SACTC,GAAiBtR,IAAesR,IACjCtR,EAAWiN,iBAAiBqE,GAG5BtR,EAAWuN,QAFXvN,EAAWoF,UAAUkG,IAOlBgG,MAAiBtR,EAAWuM,QAE/B+E,EADAzQ,EAAQyQ,cAAgBtR,GAG1BlE,QAUJuQ,aAAIrM,MACEA,aAAsBwM,UACjBxM,UAGDD,EAAgBT,KAAhBS,YAECjE,EAAI,EAAGA,EAAIiE,EAAYxF,OAAQuB,OAClCiE,EAAYjE,GAAG+D,QAAQqN,QAAUlN,SAC5BD,EAAYjE,UAGhB,QAST8U,aAAI5Q,MACE8L,GAAe9L,EAAY,MAAOV,aAC7BA,SAIHiS,EAAWjS,KAAK+M,IAAIrM,EAAWH,QAAQqN,cAEzCqE,QACGC,OAAOD,QAGTxR,YAAY2I,KAAK1I,IACtBA,EAAWb,QAAUG,MAEhBQ,YAAYF,SACVI,KASTwR,gBAAOxR,MACD8L,GAAe9L,EAAY,SAAUV,aAChCA,SAGHmS,EAAmBnS,KAAK+M,IAAIrM,MAG9BA,EAAY,KACND,EAAgBT,KAAhBS,YACF1F,EAAQyM,GAAQ/G,EAAa0R,IAEpB,IAAXpX,IACF0F,EAAY4I,OAAOtO,EAAO,QACrByF,YAAYF,iBAIdN,QAUToS,YAAGC,EAAQlM,WACMvJ,IAAXyV,QAAoCzV,IAAZuJ,SACnBnG,SAGD2R,EAAa3R,KAAb2R,gBAER1S,EAAK8G,GAASsM,GAAS,SAAAzE,GACrB+D,EAAS/D,GAAS+D,EAAS/D,IAAU,GACrC+D,EAAS/D,GAAOxE,KAAKjD,KAEhBnG,QASTsS,aAAID,EAAQlM,WACKvJ,IAAXyV,SACKrS,SAGD2R,EAAa3R,KAAb2R,gBAER1S,EAAK8G,GAASsM,GAAS,SAAAzE,GAChBzH,EAGHwL,EAAS/D,IAAU+D,EAAS/D,GAAOvE,OAAO7B,GAAQmK,EAAS/D,GAAQzH,GAAU,UAFtEwL,EAAS/D,KAKb5N,QAQT6F,cAAK+H,EAAO2E,GAjQd,IAAyB3E,EAAO2E,EACxBC,EAkQAxS,KAAKO,QAAQoQ,YAnQI/C,EAoQHA,EApQU2E,EAoQHA,GAnQrBC,EAAejX,SAASkX,YAAY,UAE7BC,UAAU9E,GAAO,GAAM,IACpC4E,EAAaG,QAAUJ,GAClB3X,OAAOgY,cAAcJ,QAmQlBb,EAAW3R,KAAK2R,SAAS/D,IAAU5N,KAAK2R,SAAS/D,GAAOrR,WAEzDoV,GAAaA,EAAS1W,QAI3BsX,EAAKnM,KAAOwH,EACZ2E,EAAK9Q,eAAiB,WACpB8Q,EAAKnR,SAASK,0BAGZjF,EAAI,EAEDA,EAAImV,EAAS1W,QAClB0W,EAASnV,GAAG+V,GACZ/V,QASJ+K,wBACOrH,SAAWmR,GAAerR,MAAM,QAEhC2R,SAAW,QACXpQ,QAAU,QACVJ,MAAMoG,eACNrH,QAAU,WElVb2S,GAAyB,CAC7B7I,WAAY5L,EACZ6L,UhDIiB,EgDHjBC,SAAU7L,EACV8L,YAAa7L,GAYMwU,kCAEbvK,EAAQuK,EAAiBtK,iBAC7BD,EAAMlB,SAZyB,aAa/BkB,EAAMjB,MAZyB,4DActBtM,kBACJ+X,SAAU,8BAGjB5M,iBAAQe,OACFd,EAAOyM,GAAuB3L,EAAGd,SAGjCA,IAAShI,SACN2U,SAAU,GAGZ/S,KAAK+S,aAINzI,EAuBR,SAAgCpD,EAAId,OAC9B4M,EAAM1J,GAAQpC,EAAGoD,SACjB2I,EAAU3J,GAAQpC,EAAGwD,gBAErBtE,GAAQ/H,EAAYC,KACtB0U,EAAMxJ,GAAYwJ,EAAIpS,OAAOqS,GAAU,cAAc,UAGhD,CAACD,EAAKC,IA/B0B7T,KAAKY,KAAMkH,EAAId,GAGhDA,GAAQ/H,EAAYC,IAAiBgM,EAAQ,GAAGrP,OAASqP,EAAQ,GAAGrP,QAAW,SAC5E8X,SAAU,QAGZjM,SAAS9G,KAAKH,QAASuG,EAAM,CAChCxE,SAAU0I,EAAQ,GAClB5E,gBAAiB4E,EAAQ,GACzBvB,YAAa9K,EACbmD,SAAU8F,SAjC8BL,GCnB/B,SAASqM,GAAUC,EAAQ5B,EAAM6B,OAC1CC,wBAA2C9B,OAAS6B,kBACjD,eACDE,EAAI,IAAIC,MAAM,mBACdC,EAAQF,GAAKA,EAAEE,MAAQF,EAAEE,MAAM1K,QAAQ,kBAAmB,IACzDA,QAAQ,cAAe,IACvBA,QAAQ,6BAA8B,kBAAoB,sBAE3D2K,EAAMhX,OAAOiX,UAAYjX,OAAOiX,QAAQC,MAAQlX,OAAOiX,QAAQD,YAC/DA,GACFA,EAAIrU,KAAK3C,OAAOiX,QAASL,EAAoBG,GAExCL,EAAO5T,MAAMS,KAAMhF,YCV9B,IAAM4Y,GAASV,GAAU,SAACW,EAAMpM,EAAKqM,WAC/BC,EAAOrZ,OAAOqZ,KAAKtM,GACnBjL,EAAI,EACDA,EAAIuX,EAAK9Y,UACT6Y,GAAUA,QAA2BlX,IAAlBiX,EAAKE,EAAKvX,OAChCqX,EAAKE,EAAKvX,IAAMiL,EAAIsM,EAAKvX,KAE3BA,WAEKqX,GACN,SAAU,iBCVPC,GAAQZ,GAAU,SAACW,EAAMpM,UACtBmM,GAAOC,EAAMpM,GAAK,IACxB,QAAS,iBCJG,SAASuM,GAAQC,EAAOC,EAAMC,OAEvCC,EADAC,EAAQH,EAAK1L,WAGjB4L,EAASH,EAAMzL,UAAY9N,OAAO4Z,OAAOD,IAClCE,YAAcN,EACrBG,EAAOI,OAASH,EAEZF,GACFxZ,EAAOyZ,EAAQD,GCVJ,SAASM,GAAO/H,EAAIvN,UAC1B,kBACEuN,EAAGnN,MAAMJ,EAASnE,kCCwDR0Z,WA6DRxU,EAASK,mBAAAA,IAAAA,EAAU,IACvB,IAAIkR,GAAQvR,KAClBO,YAEQ2Q,aAEL7Q,YAnEemU,EAKbC,QAAU,YALGD,EAMb5V,cAAgBA,EANH4V,EAOb/V,eAAiBA,EAPJ+V,EAQblW,eAAiBA,EARJkW,EASbjW,gBAAkBA,EATLiW,EAUbhW,aAAeA,EAVFgW,EAWb9V,qBAAuBA,EAXV8V,EAYb7V,mBAAqBA,EAZR6V,EAabnW,eAAiBA,EAbJmW,EAcb/V,eAAiBA,EAdJ+V,EAebtW,YAAcA,EAfDsW,EAgBbE,WtDhEW,EsDgDEF,EAiBZrW,UAAYA,EAjBAqW,EAkBbpW,aAAeA,EAlBFoW,EAmBbG,etBpFe,EsBiEFH,EAoBbI,YtBpFY,EsBgECJ,EAqBbK,ctBpFc,EsB+DDL,EAsBbM,YtBpFY,EsB8DCN,EAuBbO,iBtBrFY,EsB8DCP,EAwBbQ,gBtBpFgB,GsB4DHR,EAyBbS,atBpFa,GsB2DAT,EA0BbjD,QAAUA,GA1BGiD,EA2Bb7N,MAAQA,EA3BK6N,EA4Bb9U,YAAcA,EA5BD8U,EA6BbtK,WAAaA,GA7BAsK,EA8BbxJ,WAAaA,GA9BAwJ,EA+BbpM,kBAAoBA,GA/BPoM,EAgCb5I,gBAAkBA,GAhCL4I,EAiCb5B,iBAAmBA,GAjCN4B,EAkCbxH,WAAaA,GAlCAwH,EAmCblF,eAAiBA,GAnCJkF,EAoCbU,IAAMjH,GApCOuG,EAqCbW,IAAMtF,GArCO2E,EAsCbY,MAAQlF,GAtCKsE,EAuCba,MAAQjF,GAvCKoE,EAwCbc,OAAShF,GAxCIkE,EAyCbe,MAAQhF,GAzCKiE,EA0CbtC,GAAKnM,GA1CQyO,EA2CbpC,IAAMhM,GA3COoO,EA4CbzV,KAAOA,EA5CMyV,EA6CbZ,MAAQA,GA7CKY,EA8Cbd,OAASA,GA9CIc,EA+CbD,OAASA,GA/CIC,EAgDb/Z,OAASA,EAhDI+Z,EAiDbV,QAAUA,GAjDGU,EAkDbD,OAASA,GAlDIC,EAmDb1Y,SAAWA,EAnDE0Y,EAoDbpL,QAAUA,GApDGoL,EAqDblN,QAAUA,GArDGkN,EAsDblL,YAAcA,GAtDDkL,EAuDb3O,SAAWA,GAvDE2O,EAwDbrV,SAAWA,EAxDEqV,EAyDbxS,UAAYA,EAzDCwS,EA0DbzO,kBAAoBA,GA1DPyO,EA2DbpO,qBAAuBA,GA3DVoO,EA4DbhD,SAAW/W,EAAO,GAAI+W,GAAU,CAAEN,OAAAA,KA5DrBsD"}