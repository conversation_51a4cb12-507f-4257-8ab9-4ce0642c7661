{"name": "babel-plugin-jest-hoist", "version": "29.6.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-plugin-jest-hoist"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-react": "^7.12.1", "@babel/preset-typescript": "^7.0.0", "@types/babel__template": "^7.0.2", "@types/node": "*", "@types/prettier": "^2.1.5", "babel-plugin-tester": "^11.0.2", "prettier": "^2.1.1"}, "publishConfig": {"access": "public"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b"}