{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/createSocketMap.ts"], "sourcesContent": ["import type { WebSocket } from 'ws';\n\nconst debug = require('debug')('expo:metro:dev-server:socketmap') as typeof console.log;\n\nexport type SocketId = string;\nexport type SocketMap = Map<string, WebSocket>;\n\nexport function createSocketMap() {\n  const map: SocketMap = new Map();\n  const createId = createSocketIdFactory();\n\n  const registerSocket = (socket: WebSocket) => {\n    const id = createId();\n    map.set(id, socket);\n    return {\n      id,\n      terminate: () => {\n        map.delete(id);\n        socket.removeAllListeners();\n        socket.terminate();\n      },\n    };\n  };\n\n  const findSocket = (id: SocketId): WebSocket | null => {\n    const socket = map.get(id);\n    if (!socket) debug(`No connected socket found with ID: ${id}`);\n    return socket ?? null;\n  };\n\n  return { map, registerSocket, findSocket };\n}\n\nfunction createSocketIdFactory() {\n  let nextId = 0;\n  return () => `socket#${nextId++}`;\n}\n"], "names": ["createSocketMap", "debug", "require", "map", "Map", "createId", "createSocketIdFactory", "registerSocket", "socket", "id", "set", "terminate", "delete", "removeAllListeners", "findSocket", "get", "nextId"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;AALhB,MAAMC,QAAQC,QAAQ,SAAS;AAKxB,SAASF;IACd,MAAMG,MAAiB,IAAIC;IAC3B,MAAMC,WAAWC;IAEjB,MAAMC,iBAAiB,CAACC;QACtB,MAAMC,KAAKJ;QACXF,IAAIO,GAAG,CAACD,IAAID;QACZ,OAAO;YACLC;YACAE,WAAW;gBACTR,IAAIS,MAAM,CAACH;gBACXD,OAAOK,kBAAkB;gBACzBL,OAAOG,SAAS;YAClB;QACF;IACF;IAEA,MAAMG,aAAa,CAACL;QAClB,MAAMD,SAASL,IAAIY,GAAG,CAACN;QACvB,IAAI,CAACD,QAAQP,MAAM,CAAC,mCAAmC,EAAEQ,IAAI;QAC7D,OAAOD,UAAU;IACnB;IAEA,OAAO;QAAEL;QAAKI;QAAgBO;IAAW;AAC3C;AAEA,SAASR;IACP,IAAIU,SAAS;IACb,OAAO,IAAM,CAAC,OAAO,EAAEA,UAAU;AACnC"}