import*as i from"../../core/i18n/i18n.js";import"../../core/root/root.js";import*as e from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as o from"../../ui/legacy/legacy.js";const t={autofill:"Autofill",showAutofill:"Show Autofill"},l=i.i18n.registerUIStrings("panels/autofill/autofill-meta.ts",t),r=i.i18n.getLazilyComputedLocalizedString.bind(void 0,l);let a;o.ViewManager.registerViewExtension({experiment:"autofill-view",location:"drawer-view",id:"autofill-view",title:r(t.autofill),commandPrompt:r(t.showAutofill),order:100,persistence:"closeable",async loadView(){const i=await async function(){return a||(a=await import("./autofill.js")),a}();return e.LegacyWrapper.legacyWrapper(o.Widget.Widget,new i.AutofillView.AutofillView)}});
