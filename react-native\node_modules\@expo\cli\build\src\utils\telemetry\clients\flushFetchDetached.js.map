{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/flushFetchDetached.ts"], "sourcesContent": ["import fs from 'node:fs';\n\nimport { FetchClient } from './FetchClient';\nimport type { TelemetryRecordInternal } from '../types';\n\nconst telemetryFile = process.argv[2];\n\nflush()\n  .catch(() => fs.promises.unlink(telemetryFile))\n  .finally(() => process.exit(0));\n\nasync function flush() {\n  if (!telemetryFile) return;\n\n  let json: string;\n  let data: { records: TelemetryRecordInternal[] };\n\n  try {\n    json = await fs.promises.readFile(telemetryFile, 'utf8');\n    data = JSON.parse(json) as any;\n  } catch (error: any) {\n    if (error.code === 'ENOENT') return;\n    throw error;\n  }\n\n  if (data.records.length) {\n    const client = new FetchClient();\n    await client.record(data.records);\n    await client.flush();\n  }\n\n  await fs.promises.unlink(telemetryFile);\n}\n"], "names": ["telemetryFile", "process", "argv", "flush", "catch", "fs", "promises", "unlink", "finally", "exit", "json", "data", "readFile", "JSON", "parse", "error", "code", "records", "length", "client", "FetchClient", "record"], "mappings": ";;;;;gEAAe;;;;;;6BAEa;;;;;;AAG5B,MAAMA,gBAAgBC,QAAQC,IAAI,CAAC,EAAE;AAErCC,QACGC,KAAK,CAAC,IAAMC,iBAAE,CAACC,QAAQ,CAACC,MAAM,CAACP,gBAC/BQ,OAAO,CAAC,IAAMP,QAAQQ,IAAI,CAAC;AAE9B,eAAeN;IACb,IAAI,CAACH,eAAe;IAEpB,IAAIU;IACJ,IAAIC;IAEJ,IAAI;QACFD,OAAO,MAAML,iBAAE,CAACC,QAAQ,CAACM,QAAQ,CAACZ,eAAe;QACjDW,OAAOE,KAAKC,KAAK,CAACJ;IACpB,EAAE,OAAOK,OAAY;QACnB,IAAIA,MAAMC,IAAI,KAAK,UAAU;QAC7B,MAAMD;IACR;IAEA,IAAIJ,KAAKM,OAAO,CAACC,MAAM,EAAE;QACvB,MAAMC,SAAS,IAAIC,wBAAW;QAC9B,MAAMD,OAAOE,MAAM,CAACV,KAAKM,OAAO;QAChC,MAAME,OAAOhB,KAAK;IACpB;IAEA,MAAME,iBAAE,CAACC,QAAQ,CAACC,MAAM,CAACP;AAC3B"}