{"version": 3, "file": "withIosIcons.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fs", "_interopRequireDefault", "_path", "_AssetContents", "e", "__esModule", "default", "getProjectName", "IOSConfig", "XcodeUtils", "IMAGE_CACHE_NAME", "IMAGESET_PATH", "withIosIcons", "config", "withDangerousMod", "setIconsAsync", "modRequest", "projectRoot", "exports", "getIcons", "iosSpecificIcons", "ios", "icon", "light", "dark", "tinted", "WarningAggregator", "addWarningIOS", "iosNamedProjectRoot", "getIosNamedProjectPath", "fs", "promises", "mkdir", "join", "recursive", "imagesJson", "baseIconPath", "baseIcon", "generateUniversalIconAsync", "cache<PERSON>ey", "platform", "push", "darkIcon", "appearance", "tintedIcon", "writeContentsJsonAsync", "images", "projectName", "getAppleIconName", "size", "scale", "name", "filename", "source", "generateImageAsync", "cacheType", "src", "width", "height", "removeTransparency", "resizeMode", "backgroundColor", "undefined", "createSquareAsync", "assetPath", "writeFile", "idiom", "appearances", "value"], "sources": ["../../../src/plugins/icons/withIosIcons.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, WarningAggregator, withDangerousMod } from '@expo/config-plugins';\nimport { ExpoConfig, IOSIcons } from '@expo/config-types';\nimport { createSquareAsync, generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs';\nimport { join } from 'path';\n\nimport { ContentsJson, ContentsJsonImage, writeContentsJsonAsync } from './AssetContents';\n\nconst { getProjectName } = IOSConfig.XcodeUtils;\n\nconst IMAGE_CACHE_NAME = 'icons';\nconst IMAGESET_PATH = 'Images.xcassets/AppIcon.appiconset';\n\nexport const withIosIcons: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      await setIconsAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport function getIcons(config: Pick<ExpoConfig, 'icon' | 'ios'>): IOSIcons | string | null {\n  const iosSpecificIcons = config.ios?.icon;\n\n  if (iosSpecificIcons) {\n    // For backwards compatibility, the icon can be a string\n    if (typeof iosSpecificIcons === 'string') {\n      return iosSpecificIcons || config.icon || null;\n    }\n\n    // in iOS 18 introduced the ability to specify dark and tinted icons, which users can specify as an object\n    if (!iosSpecificIcons.light && !iosSpecificIcons.dark && !iosSpecificIcons.tinted) {\n      return config.icon || null;\n    }\n\n    return iosSpecificIcons;\n  }\n\n  if (config.icon) {\n    return config.icon;\n  }\n\n  return null;\n}\n\nexport async function setIconsAsync(config: ExpoConfig, projectRoot: string) {\n  const icon = getIcons(config);\n\n  if (\n    !icon ||\n    (typeof icon === 'string' && !icon) ||\n    (typeof icon === 'object' && !icon?.light && !icon?.dark && !icon?.tinted)\n  ) {\n    WarningAggregator.addWarningIOS('icon', 'No icon is defined in the Expo config.');\n  }\n\n  // Something like projectRoot/ios/MyApp/\n  const iosNamedProjectRoot = getIosNamedProjectPath(projectRoot);\n\n  // Ensure the Images.xcassets/AppIcon.appiconset path exists\n  await fs.promises.mkdir(join(iosNamedProjectRoot, IMAGESET_PATH), { recursive: true });\n\n  const imagesJson: ContentsJson['images'] = [];\n\n  const baseIconPath = typeof icon === 'object' ? icon?.light || icon?.dark || icon?.tinted : icon;\n\n  // Store the image JSON data for assigning via the Contents.json\n  const baseIcon = await generateUniversalIconAsync(projectRoot, {\n    icon: baseIconPath,\n    cacheKey: 'universal-icon',\n    iosNamedProjectRoot,\n    platform: 'ios',\n  });\n\n  imagesJson.push(baseIcon);\n\n  if (typeof icon === 'object') {\n    if (icon?.dark) {\n      const darkIcon = await generateUniversalIconAsync(projectRoot, {\n        icon: icon.dark,\n        cacheKey: 'universal-icon-dark',\n        iosNamedProjectRoot,\n        platform: 'ios',\n        appearance: 'dark',\n      });\n\n      imagesJson.push(darkIcon);\n    }\n\n    if (icon?.tinted) {\n      const tintedIcon = await generateUniversalIconAsync(projectRoot, {\n        icon: icon.tinted,\n        cacheKey: 'universal-icon-tinted',\n        iosNamedProjectRoot,\n        platform: 'ios',\n        appearance: 'tinted',\n      });\n\n      imagesJson.push(tintedIcon);\n    }\n  }\n\n  // Finally, write the Contents.json\n  await writeContentsJsonAsync(join(iosNamedProjectRoot, IMAGESET_PATH), { images: imagesJson });\n}\n\n/**\n * Return the project's named iOS path: ios/MyProject/\n *\n * @param projectRoot Expo project root path.\n */\nfunction getIosNamedProjectPath(projectRoot: string): string {\n  const projectName = getProjectName(projectRoot);\n  return join(projectRoot, 'ios', projectName);\n}\n\nfunction getAppleIconName(size: number, scale: number, appearance?: 'dark' | 'tinted'): string {\n  let name = 'App-Icon';\n\n  if (appearance) {\n    name = `${name}-${appearance}`;\n  }\n\n  name = `${name}-${size}x${size}@${scale}x.png`;\n\n  return name;\n}\n\nexport async function generateUniversalIconAsync(\n  projectRoot: string,\n  {\n    icon,\n    cacheKey,\n    iosNamedProjectRoot,\n    platform,\n    appearance,\n  }: {\n    platform: 'watchos' | 'ios';\n    icon?: string | null;\n    appearance?: 'dark' | 'tinted';\n    iosNamedProjectRoot: string;\n    cacheKey: string;\n  }\n): Promise<ContentsJsonImage> {\n  const size = 1024;\n  const filename = getAppleIconName(size, 1, appearance);\n\n  let source: Buffer;\n\n  if (icon) {\n    // Using this method will cache the images in `.expo` based on the properties used to generate them.\n    // this method also supports remote URLs and using the global sharp instance.\n    source = (\n      await generateImageAsync(\n        { projectRoot, cacheType: IMAGE_CACHE_NAME + cacheKey },\n        {\n          src: icon,\n          name: filename,\n          width: size,\n          height: size,\n          // Transparency needs to be preserved in dark variant, but can safely be removed in \"light\" and \"tinted\" variants.\n          removeTransparency: appearance !== 'dark',\n          // The icon should be square, but if it's not then it will be cropped.\n          resizeMode: 'cover',\n          // Force the background color to solid white to prevent any transparency. (for \"any\" and \"tinted\" variants)\n          // TODO: Maybe use a more adaptive option based on the icon color?\n          backgroundColor: appearance !== 'dark' ? '#ffffff' : undefined,\n        }\n      )\n    ).source;\n  } else {\n    // Create a white square image if no icon exists to mitigate the chance of a submission failure to the app store.\n    source = await createSquareAsync({ size });\n  }\n  // Write image buffer to the file system.\n  const assetPath = join(iosNamedProjectRoot, IMAGESET_PATH, filename);\n  await fs.promises.writeFile(assetPath, source);\n\n  return {\n    filename,\n    idiom: 'universal',\n    platform,\n    size: `${size}x${size}`,\n    ...(appearance ? { appearances: [{ appearance: 'luminosity', value: appearance }] } : {}),\n  };\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,eAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,cAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0F,SAAAI,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1F,MAAM;EAAEG;AAAe,CAAC,GAAGC,0BAAS,CAACC,UAAU;AAE/C,MAAMC,gBAAgB,GAAG,OAAO;AAChC,MAAMC,aAAa,GAAG,oCAAoC;AAEnD,MAAMC,YAA0B,GAAIC,MAAM,IAAK;EACpD,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAME,aAAa,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IAC1D,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,YAAA,GAAAA,YAAA;AAEK,SAASO,QAAQA,CAACN,MAAwC,EAA4B;EAC3F,MAAMO,gBAAgB,GAAGP,MAAM,CAACQ,GAAG,EAAEC,IAAI;EAEzC,IAAIF,gBAAgB,EAAE;IACpB;IACA,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;MACxC,OAAOA,gBAAgB,IAAIP,MAAM,CAACS,IAAI,IAAI,IAAI;IAChD;;IAEA;IACA,IAAI,CAACF,gBAAgB,CAACG,KAAK,IAAI,CAACH,gBAAgB,CAACI,IAAI,IAAI,CAACJ,gBAAgB,CAACK,MAAM,EAAE;MACjF,OAAOZ,MAAM,CAACS,IAAI,IAAI,IAAI;IAC5B;IAEA,OAAOF,gBAAgB;EACzB;EAEA,IAAIP,MAAM,CAACS,IAAI,EAAE;IACf,OAAOT,MAAM,CAACS,IAAI;EACpB;EAEA,OAAO,IAAI;AACb;AAEO,eAAeP,aAAaA,CAACF,MAAkB,EAAEI,WAAmB,EAAE;EAC3E,MAAMK,IAAI,GAAGH,QAAQ,CAACN,MAAM,CAAC;EAE7B,IACE,CAACS,IAAI,IACJ,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAK,IAClC,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAEC,KAAK,IAAI,CAACD,IAAI,EAAEE,IAAI,IAAI,CAACF,IAAI,EAAEG,MAAO,EAC1E;IACAC,kCAAiB,CAACC,aAAa,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACnF;;EAEA;EACA,MAAMC,mBAAmB,GAAGC,sBAAsB,CAACZ,WAAW,CAAC;;EAE/D;EACA,MAAMa,aAAE,CAACC,QAAQ,CAACC,KAAK,CAAC,IAAAC,YAAI,EAACL,mBAAmB,EAAEjB,aAAa,CAAC,EAAE;IAAEuB,SAAS,EAAE;EAAK,CAAC,CAAC;EAEtF,MAAMC,UAAkC,GAAG,EAAE;EAE7C,MAAMC,YAAY,GAAG,OAAOd,IAAI,KAAK,QAAQ,GAAGA,IAAI,EAAEC,KAAK,IAAID,IAAI,EAAEE,IAAI,IAAIF,IAAI,EAAEG,MAAM,GAAGH,IAAI;;EAEhG;EACA,MAAMe,QAAQ,GAAG,MAAMC,0BAA0B,CAACrB,WAAW,EAAE;IAC7DK,IAAI,EAAEc,YAAY;IAClBG,QAAQ,EAAE,gBAAgB;IAC1BX,mBAAmB;IACnBY,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFL,UAAU,CAACM,IAAI,CAACJ,QAAQ,CAAC;EAEzB,IAAI,OAAOf,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAIA,IAAI,EAAEE,IAAI,EAAE;MACd,MAAMkB,QAAQ,GAAG,MAAMJ,0BAA0B,CAACrB,WAAW,EAAE;QAC7DK,IAAI,EAAEA,IAAI,CAACE,IAAI;QACfe,QAAQ,EAAE,qBAAqB;QAC/BX,mBAAmB;QACnBY,QAAQ,EAAE,KAAK;QACfG,UAAU,EAAE;MACd,CAAC,CAAC;MAEFR,UAAU,CAACM,IAAI,CAACC,QAAQ,CAAC;IAC3B;IAEA,IAAIpB,IAAI,EAAEG,MAAM,EAAE;MAChB,MAAMmB,UAAU,GAAG,MAAMN,0BAA0B,CAACrB,WAAW,EAAE;QAC/DK,IAAI,EAAEA,IAAI,CAACG,MAAM;QACjBc,QAAQ,EAAE,uBAAuB;QACjCX,mBAAmB;QACnBY,QAAQ,EAAE,KAAK;QACfG,UAAU,EAAE;MACd,CAAC,CAAC;MAEFR,UAAU,CAACM,IAAI,CAACG,UAAU,CAAC;IAC7B;EACF;;EAEA;EACA,MAAM,IAAAC,uCAAsB,EAAC,IAAAZ,YAAI,EAACL,mBAAmB,EAAEjB,aAAa,CAAC,EAAE;IAAEmC,MAAM,EAAEX;EAAW,CAAC,CAAC;AAChG;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASN,sBAAsBA,CAACZ,WAAmB,EAAU;EAC3D,MAAM8B,WAAW,GAAGxC,cAAc,CAACU,WAAW,CAAC;EAC/C,OAAO,IAAAgB,YAAI,EAAChB,WAAW,EAAE,KAAK,EAAE8B,WAAW,CAAC;AAC9C;AAEA,SAASC,gBAAgBA,CAACC,IAAY,EAAEC,KAAa,EAAEP,UAA8B,EAAU;EAC7F,IAAIQ,IAAI,GAAG,UAAU;EAErB,IAAIR,UAAU,EAAE;IACdQ,IAAI,GAAG,GAAGA,IAAI,IAAIR,UAAU,EAAE;EAChC;EAEAQ,IAAI,GAAG,GAAGA,IAAI,IAAIF,IAAI,IAAIA,IAAI,IAAIC,KAAK,OAAO;EAE9C,OAAOC,IAAI;AACb;AAEO,eAAeb,0BAA0BA,CAC9CrB,WAAmB,EACnB;EACEK,IAAI;EACJiB,QAAQ;EACRX,mBAAmB;EACnBY,QAAQ;EACRG;AAOF,CAAC,EAC2B;EAC5B,MAAMM,IAAI,GAAG,IAAI;EACjB,MAAMG,QAAQ,GAAGJ,gBAAgB,CAACC,IAAI,EAAE,CAAC,EAAEN,UAAU,CAAC;EAEtD,IAAIU,MAAc;EAElB,IAAI/B,IAAI,EAAE;IACR;IACA;IACA+B,MAAM,GAAG,CACP,MAAM,IAAAC,gCAAkB,EACtB;MAAErC,WAAW;MAAEsC,SAAS,EAAE7C,gBAAgB,GAAG6B;IAAS,CAAC,EACvD;MACEiB,GAAG,EAAElC,IAAI;MACT6B,IAAI,EAAEC,QAAQ;MACdK,KAAK,EAAER,IAAI;MACXS,MAAM,EAAET,IAAI;MACZ;MACAU,kBAAkB,EAAEhB,UAAU,KAAK,MAAM;MACzC;MACAiB,UAAU,EAAE,OAAO;MACnB;MACA;MACAC,eAAe,EAAElB,UAAU,KAAK,MAAM,GAAG,SAAS,GAAGmB;IACvD,CACF,CAAC,EACDT,MAAM;EACV,CAAC,MAAM;IACL;IACAA,MAAM,GAAG,MAAM,IAAAU,+BAAiB,EAAC;MAAEd;IAAK,CAAC,CAAC;EAC5C;EACA;EACA,MAAMe,SAAS,GAAG,IAAA/B,YAAI,EAACL,mBAAmB,EAAEjB,aAAa,EAAEyC,QAAQ,CAAC;EACpE,MAAMtB,aAAE,CAACC,QAAQ,CAACkC,SAAS,CAACD,SAAS,EAAEX,MAAM,CAAC;EAE9C,OAAO;IACLD,QAAQ;IACRc,KAAK,EAAE,WAAW;IAClB1B,QAAQ;IACRS,IAAI,EAAE,GAAGA,IAAI,IAAIA,IAAI,EAAE;IACvB,IAAIN,UAAU,GAAG;MAAEwB,WAAW,EAAE,CAAC;QAAExB,UAAU,EAAE,YAAY;QAAEyB,KAAK,EAAEzB;MAAW,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;EAC1F,CAAC;AACH", "ignoreList": []}