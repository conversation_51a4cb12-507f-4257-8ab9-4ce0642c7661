{"version": 3, "sources": ["ReanimatedSwipeable.tsx"], "names": ["DRAG_TOSS", "Swipeable", "props", "ref", "leftThreshold", "rightT<PERSON><PERSON><PERSON>", "onSwipeableOpenStartDrag", "onSwipeableCloseStartDrag", "enableTrackpadTwoFingerGesture", "enabled", "containerStyle", "childrenContainerStyle", "animationOptions", "overshootLeft", "overshootRight", "onSwipeableWillOpen", "onSwipeableWillClose", "onSwipeableOpen", "onSwipeableClose", "testID", "remainingProps", "rowState", "userDrag", "appliedTranslation", "row<PERSON>id<PERSON>", "leftWidth", "rightWidth", "rightOffset", "leftActionTranslate", "rightActionTranslate", "showLeftProgress", "showRightProgress", "swipeableMethods", "close", "openLeft", "openRight", "reset", "defaultProps", "friction", "overshootFriction", "overshootLeftProp", "overshootRightProp", "calculateCurrentOffset", "value", "updateAnimatedEvent", "Math", "max", "startOffset", "offsetDrag", "Number", "MIN_VALUE", "Extrapolation", "CLAMP", "dispatchImmediateEvents", "fromValue", "toValue", "closingDirection", "dispatchEndEvents", "current", "animationOptionsProp", "animateRow", "velocityX", "sign", "translationSpringConfig", "duration", "dampingRatio", "stiffness", "velocity", "overshootClamping", "progressSpringConfig", "isFinished", "progressTarget", "onRowLayout", "nativeEvent", "layout", "width", "children", "renderLeftActions", "renderRightActions", "dragOffsetFromLeftEdge", "dragOffsetFromRightEdge", "leftAnimatedStyle", "transform", "translateX", "leftElement", "styles", "leftActions", "x", "rightAnimatedStyle", "rightElement", "rightActions", "leftThresholdProp", "rightThresholdProp", "handleRelease", "event", "translationX", "startOffsetX", "tapGesture", "Gesture", "Tap", "onStart", "panGesture", "Pan", "onUpdate", "direction", "onEnd", "activeOffsetX", "shouldCancelWhenOutside", "animatedStyle", "pointerEvents", "swipeableComponent", "container", "StyleSheet", "create", "overflow", "absoluteFillObject", "flexDirection", "I18nManager", "isRTL"], "mappings": ";;;;;;;AAIA;;AAOA;;AACA;;AAOA;;AASA;;;;;;;;AASA,MAAMA,SAAS,GAAG,IAAlB;AA+JA,MAAMC,SAAS,gBAAG,uBAChB,SAASA,SAAT,CACEC,KADF,EAEEC,GAFF,EAGE;AACA,QAAM;AACJC,IAAAA,aADI;AAEJC,IAAAA,cAFI;AAGJC,IAAAA,wBAHI;AAIJC,IAAAA,yBAJI;AAKJC,IAAAA,8BALI;AAMJC,IAAAA,OANI;AAOJC,IAAAA,cAPI;AAQJC,IAAAA,sBARI;AASJC,IAAAA,gBATI;AAUJC,IAAAA,aAVI;AAWJC,IAAAA,cAXI;AAYJC,IAAAA,mBAZI;AAaJC,IAAAA,oBAbI;AAcJC,IAAAA,eAdI;AAeJC,IAAAA,gBAfI;AAgBJC,IAAAA,MAhBI;AAiBJ,OAAGC;AAjBC,MAkBFlB,KAlBJ;AAoBA,QAAMmB,QAAQ,GAAG,2CAAuB,CAAvB,CAAjB;AAEA,QAAMC,QAAQ,GAAG,2CAAuB,CAAvB,CAAjB;AACA,QAAMC,kBAAkB,GAAG,2CAAuB,CAAvB,CAA3B;AAEA,QAAMC,QAAQ,GAAG,2CAAuB,CAAvB,CAAjB;AACA,QAAMC,SAAS,GAAG,2CAAuB,CAAvB,CAAlB;AACA,QAAMC,UAAU,GAAG,2CAAuB,CAAvB,CAAnB;AACA,QAAMC,WAAW,GAAG,2CAAuB,CAAvB,CAApB;AAEA,QAAMC,mBAAmB,GAAG,2CAAuB,CAAvB,CAA5B;AACA,QAAMC,oBAAoB,GAAG,2CAAuB,CAAvB,CAA7B;AAEA,QAAMC,gBAAgB,GAAG,2CAAuB,CAAvB,CAAzB;AACA,QAAMC,iBAAiB,GAAG,2CAAuB,CAAvB,CAA1B;AAEA,QAAMC,gBAAgB,GAAG,mBAAyB;AAChDC,IAAAA,KAAK,EAAE,MAAM;AACX;AACD,KAH+C;AAIhDC,IAAAA,QAAQ,EAAE,MAAM;AACd;AACD,KAN+C;AAOhDC,IAAAA,SAAS,EAAE,MAAM;AACf;AACD,KAT+C;AAUhDC,IAAAA,KAAK,EAAE,MAAM;AACX;AACD;AAZ+C,GAAzB,CAAzB;AAeA,QAAMC,YAAY,GAAG;AACnBC,IAAAA,QAAQ,EAAE,CADS;AAEnBC,IAAAA,iBAAiB,EAAE;AAFA,GAArB;AAKA,QAAM;AACJD,IAAAA,QAAQ,GAAGD,YAAY,CAACC,QADpB;AAEJC,IAAAA,iBAAiB,GAAGF,YAAY,CAACE;AAF7B,MAGFrC,KAHJ;AAKA,QAAMsC,iBAAiB,GAAG3B,aAA1B;AACA,QAAM4B,kBAAkB,GAAG3B,cAA3B;AAEA,QAAM4B,sBAAsB,GAAG,wBAAY,MAAM;AAC/C;;AACA,QAAIrB,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxB,aAAOlB,SAAS,CAACkB,KAAjB;AACD,KAFD,MAEO,IAAItB,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAAxB,EAA2B;AAChC,aAAO,CAACnB,QAAQ,CAACmB,KAAV,GAAkBhB,WAAW,CAACgB,KAArC;AACD;;AACD,WAAO,CAAP;AACD,GAR8B,EAQ5B,CAAClB,SAAD,EAAYE,WAAZ,EAAyBN,QAAzB,EAAmCG,QAAnC,CAR4B,CAA/B;;AAUA,QAAMoB,mBAAmB,GAAG,MAAM;AAChC;;AACAlB,IAAAA,UAAU,CAACiB,KAAX,GAAmBE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYtB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAzC,CAAnB;AAEA,UAAM9B,aAAa,GAAG2B,iBAAH,aAAGA,iBAAH,cAAGA,iBAAH,GAAwBf,SAAS,CAACkB,KAAV,GAAkB,CAA7D;AACA,UAAM7B,cAAc,GAAG2B,kBAAH,aAAGA,kBAAH,cAAGA,kBAAH,GAAyBf,UAAU,CAACiB,KAAX,GAAmB,CAAhE;AAEA,UAAMI,WAAW,GACf1B,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GACIlB,SAAS,CAACkB,KADd,GAEItB,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAApB,GACA,CAACjB,UAAU,CAACiB,KADZ,GAEA,CALN;AAOA,UAAMK,UAAU,GAAG1B,QAAQ,CAACqB,KAAT,GAAiBL,QAAjB,GAA4BS,WAA/C;AAEAxB,IAAAA,kBAAkB,CAACoB,KAAnB,GAA2B,wCACzBK,UADyB,EAEzB,CACE,CAACtB,UAAU,CAACiB,KAAZ,GAAoB,CADtB,EAEE,CAACjB,UAAU,CAACiB,KAFd,EAGElB,SAAS,CAACkB,KAHZ,EAIElB,SAAS,CAACkB,KAAV,GAAkB,CAJpB,CAFyB,EAQzB,CACE,CAACjB,UAAU,CAACiB,KAAZ,IAAqB7B,cAAc,GAAG,IAAIyB,iBAAP,GAA2B,CAA9D,CADF,EAEE,CAACb,UAAU,CAACiB,KAFd,EAGElB,SAAS,CAACkB,KAHZ,EAIElB,SAAS,CAACkB,KAAV,IAAmB9B,aAAa,GAAG,IAAI0B,iBAAP,GAA2B,CAA3D,CAJF,CARyB,CAA3B;AAgBAT,IAAAA,gBAAgB,CAACa,KAAjB,GACElB,SAAS,CAACkB,KAAV,GAAkB,CAAlB,GACI,wCACEpB,kBAAkB,CAACoB,KADrB,EAEE,CAAC,CAAC,CAAF,EAAK,CAAL,EAAQlB,SAAS,CAACkB,KAAlB,CAFF,EAGE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHF,CADJ,GAMI,CAPN;AAQAf,IAAAA,mBAAmB,CAACe,KAApB,GAA4B,wCAC1Bb,gBAAgB,CAACa,KADS,EAE1B,CAAC,CAAD,EAAIM,MAAM,CAACC,SAAX,CAF0B,EAG1B,CAAC,CAAC,KAAF,EAAS,CAAT,CAH0B,EAI1BC,qCAAcC,KAJY,CAA5B;AAMArB,IAAAA,iBAAiB,CAACY,KAAlB,GACEjB,UAAU,CAACiB,KAAX,GAAmB,CAAnB,GACI,wCACEpB,kBAAkB,CAACoB,KADrB,EAEE,CAAC,CAACjB,UAAU,CAACiB,KAAb,EAAoB,CAApB,EAAuB,CAAvB,CAFF,EAGE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAHF,CADJ,GAMI,CAPN;AAQAd,IAAAA,oBAAoB,CAACc,KAArB,GAA6B,wCAC3BZ,iBAAiB,CAACY,KADS,EAE3B,CAAC,CAAD,EAAIM,MAAM,CAACC,SAAX,CAF2B,EAG3B,CAAC,CAAC,KAAF,EAAS,CAAT,CAH2B,EAI3BC,qCAAcC,KAJa,CAA7B;AAMD,GA5DD;;AA8DA,QAAMC,uBAAuB,GAAG,wBAC9B,CAACC,SAAD,EAAoBC,OAApB,KAAwC;AACtC,QAAIA,OAAO,GAAG,CAAV,IAAexC,mBAAnB,EAAwC;AACtCA,MAAAA,mBAAmB,CAAC,MAAD,CAAnB;AACD,KAFD,MAEO,IAAIwC,OAAO,GAAG,CAAV,IAAexC,mBAAnB,EAAwC;AAC7CA,MAAAA,mBAAmB,CAAC,OAAD,CAAnB;AACD,KAFM,MAEA,IAAIC,oBAAJ,EAA0B;AAC/B,YAAMwC,gBAAgB,GAAGF,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACAtC,MAAAA,oBAAoB,CAACwC,gBAAD,CAApB;AACD;AACF,GAV6B,EAW9B,CAACxC,oBAAD,EAAuBD,mBAAvB,CAX8B,CAAhC;AAcA,QAAM0C,iBAAiB,GAAG,wBACxB,CAACH,SAAD,EAAoBC,OAApB,KAAwC;AACtC,QAAIA,OAAO,GAAG,CAAV,IAAetC,eAAnB,EAAoC;AAClCA,MAAAA,eAAe,CAAC,MAAD,EAASe,gBAAgB,CAAC0B,OAA1B,CAAf;AACD,KAFD,MAEO,IAAIH,OAAO,GAAG,CAAV,IAAetC,eAAnB,EAAoC;AACzCA,MAAAA,eAAe,CAAC,OAAD,EAAUe,gBAAgB,CAAC0B,OAA3B,CAAf;AACD,KAFM,MAEA,IAAIxC,gBAAJ,EAAsB;AAC3B,YAAMsC,gBAAgB,GAAGF,SAAS,GAAG,CAAZ,GAAgB,MAAhB,GAAyB,OAAlD;AACApC,MAAAA,gBAAgB,CAACsC,gBAAD,EAAmBxB,gBAAgB,CAAC0B,OAApC,CAAhB;AACD;AACF,GAVuB,EAWxB,CAACxC,gBAAD,EAAmBD,eAAnB,CAXwB,CAA1B;AAcA,QAAM0C,oBAAoB,GAAG/C,gBAA7B;AAEA,QAAMgD,UAAU,GAAG,wBACjB,CAACN,SAAD,EAAoBC,OAApB,EAAqCM,SAArC,KAA4D;AAC1D;;AACAxC,IAAAA,QAAQ,CAACsB,KAAT,GAAiBE,IAAI,CAACiB,IAAL,CAAUP,OAAV,CAAjB;AAEA,UAAMQ,uBAAuB,GAAG;AAC9BC,MAAAA,QAAQ,EAAE,IADoB;AAE9BC,MAAAA,YAAY,EAAE,GAFgB;AAG9BC,MAAAA,SAAS,EAAE,GAHmB;AAI9BC,MAAAA,QAAQ,EAAEN,SAJoB;AAK9BO,MAAAA,iBAAiB,EAAE,IALW;AAM9B,SAAGT;AAN2B,KAAhC;AASA,UAAMU,oBAAoB,GAAG,EAC3B,GAAGN,uBADwB;AAE3BI,MAAAA,QAAQ,EAAE;AAFiB,KAA7B;AAKA5C,IAAAA,kBAAkB,CAACoB,KAAnB,GAA2B,uCACzBY,OADyB,EAEzBQ,uBAFyB,EAGxBO,UAAD,IAAgB;AACd,UAAIA,UAAJ,EAAgB;AACd,4CAAQb,iBAAR,EAA2BH,SAA3B,EAAsCC,OAAtC;AACD;AACF,KAPwB,CAA3B;AAUA,UAAMgB,cAAc,GAAGhB,OAAO,KAAK,CAAZ,GAAgB,CAAhB,GAAoB,CAA3C;AAEAzB,IAAAA,gBAAgB,CAACa,KAAjB,GACElB,SAAS,CAACkB,KAAV,GAAkB,CAAlB,GACI,uCAAW4B,cAAX,EAA2BF,oBAA3B,CADJ,GAEI,CAHN;AAIAtC,IAAAA,iBAAiB,CAACY,KAAlB,GACEjB,UAAU,CAACiB,KAAX,GAAmB,CAAnB,GACI,uCAAW4B,cAAX,EAA2BF,oBAA3B,CADJ,GAEI,CAHN;AAKA,wCAAQhB,uBAAR,EAAiCC,SAAjC,EAA4CC,OAA5C;AACD,GAzCgB,EA0CjB,CACElC,QADF,EAEEsC,oBAFF,EAGEpC,kBAHF,EAIEO,gBAJF,EAKEL,SAAS,CAACkB,KALZ,EAMEZ,iBANF,EAOEL,UAAU,CAACiB,KAPb,EAQEU,uBARF,EASEI,iBATF,CA1CiB,CAAnB;;AAuDA,QAAMe,WAAW,GAAG,CAAC;AAAEC,IAAAA;AAAF,GAAD,KAAwC;AAC1DjD,IAAAA,QAAQ,CAACmB,KAAT,GAAiB8B,WAAW,CAACC,MAAZ,CAAmBC,KAApC;AACD,GAFD;;AAIA,QAAM;AACJC,IAAAA,QADI;AAEJC,IAAAA,iBAFI;AAGJC,IAAAA,kBAHI;AAIJC,IAAAA,sBAAsB,GAAG,EAJrB;AAKJC,IAAAA,uBAAuB,GAAG;AALtB,MAMF9E,KANJ;AAQA8B,EAAAA,gBAAgB,CAAC0B,OAAjB,GAA2B;AACzBzB,IAAAA,KAAK,GAAG;AACN;;AACA2B,MAAAA,UAAU,CAAClB,sBAAsB,EAAvB,EAA2B,CAA3B,CAAV;AACD,KAJwB;;AAKzBR,IAAAA,QAAQ,GAAG;AACT;;AACA0B,MAAAA,UAAU,CAAClB,sBAAsB,EAAvB,EAA2BjB,SAAS,CAACkB,KAArC,CAAV;AACD,KARwB;;AASzBR,IAAAA,SAAS,GAAG;AACV;;AACAT,MAAAA,UAAU,CAACiB,KAAX,GAAmBnB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAhD;AACAiB,MAAAA,UAAU,CAAClB,sBAAsB,EAAvB,EAA2B,CAAChB,UAAU,CAACiB,KAAvC,CAAV;AACD,KAbwB;;AAczBP,IAAAA,KAAK,GAAG;AACN;;AACAd,MAAAA,QAAQ,CAACqB,KAAT,GAAiB,CAAjB;AACAb,MAAAA,gBAAgB,CAACa,KAAjB,GAAyB,CAAzB;AACApB,MAAAA,kBAAkB,CAACoB,KAAnB,GAA2B,CAA3B;AACAtB,MAAAA,QAAQ,CAACsB,KAAT,GAAiB,CAAjB;AACD;;AApBwB,GAA3B;AAuBA,QAAMsC,iBAAiB,GAAG,6CACxB,OAAO;AACLC,IAAAA,SAAS,EAAE,CACT;AACEC,MAAAA,UAAU,EAAEvD,mBAAmB,CAACe;AADlC,KADS;AADN,GAAP,CADwB,EAQxB,CAACf,mBAAD,CARwB,CAA1B;;AAWA,QAAMwD,WAAW,GAAGP,iBAAiB,iBACnC,6BAAC,8BAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACQ,MAAM,CAACC,WAAR,EAAqBL,iBAArB;AAAtB,KACGJ,iBAAiB,CAChB/C,gBADgB,EAEhBP,kBAFgB,EAGhBS,gBAAgB,CAAC0B,OAHD,CADpB,eAME,6BAAC,iBAAD;AACE,IAAA,QAAQ,EAAE,CAAC;AAAEe,MAAAA;AAAF,KAAD,KACPhD,SAAS,CAACkB,KAAV,GAAkB8B,WAAW,CAACC,MAAZ,CAAmBa;AAF1C,IANF,CADF;;AAeA,QAAMC,kBAAkB,GAAG,6CACzB,OAAO;AACLN,IAAAA,SAAS,EAAE,CACT;AACEC,MAAAA,UAAU,EAAEtD,oBAAoB,CAACc;AADnC,KADS;AADN,GAAP,CADyB,EAQzB,CAACd,oBAAD,CARyB,CAA3B;;AAWA,QAAM4D,YAAY,GAAGX,kBAAkB,iBACrC,6BAAC,8BAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACO,MAAM,CAACK,YAAR,EAAsBF,kBAAtB;AAAtB,KACGV,kBAAkB,CACjB/C,iBADiB,EAEjBR,kBAFiB,EAGjBS,gBAAgB,CAAC0B,OAHA,CADrB,eAME,6BAAC,iBAAD;AACE,IAAA,QAAQ,EAAE,CAAC;AAAEe,MAAAA;AAAF,KAAD,KACP9C,WAAW,CAACgB,KAAZ,GAAoB8B,WAAW,CAACC,MAAZ,CAAmBa;AAF5C,IANF,CADF;;AAeA,QAAMI,iBAAiB,GAAGvF,aAA1B;AACA,QAAMwF,kBAAkB,GAAGvF,cAA3B;;AAEA,QAAMwF,aAAa,GACjBC,KADoB,IAEjB;AACH;;AACA,UAAM;AAAEjC,MAAAA;AAAF,QAAgBiC,KAAtB;AACAxE,IAAAA,QAAQ,CAACqB,KAAT,GAAiBmD,KAAK,CAACC,YAAvB;AAEArE,IAAAA,UAAU,CAACiB,KAAX,GAAmBnB,QAAQ,CAACmB,KAAT,GAAiBhB,WAAW,CAACgB,KAAhD;AAEA,UAAMvC,aAAa,GAAGuF,iBAAH,aAAGA,iBAAH,cAAGA,iBAAH,GAAwBlE,SAAS,CAACkB,KAAV,GAAkB,CAA7D;AACA,UAAMtC,cAAc,GAAGuF,kBAAH,aAAGA,kBAAH,cAAGA,kBAAH,GAAyBlE,UAAU,CAACiB,KAAX,GAAmB,CAAhE;AAEA,UAAMqD,YAAY,GAAGtD,sBAAsB,KAAKpB,QAAQ,CAACqB,KAAT,GAAiBL,QAAjE;AACA,UAAMyD,YAAY,GAAG,CAACzE,QAAQ,CAACqB,KAAT,GAAiB3C,SAAS,GAAG6D,SAA9B,IAA2CvB,QAAhE;AAEA,QAAIiB,OAAO,GAAG,CAAd;;AAEA,QAAIlC,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxB,UAAIoD,YAAY,GAAG3F,aAAnB,EAAkC;AAChCmD,QAAAA,OAAO,GAAG9B,SAAS,CAACkB,KAApB;AACD,OAFD,MAEO,IAAIoD,YAAY,GAAG,CAAC1F,cAApB,EAAoC;AACzCkD,QAAAA,OAAO,GAAG,CAAC7B,UAAU,CAACiB,KAAtB;AACD;AACF,KAND,MAMO,IAAItB,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AAC/B;AACA,UAAIoD,YAAY,GAAG,CAAC3F,aAApB,EAAmC;AACjCmD,QAAAA,OAAO,GAAG9B,SAAS,CAACkB,KAApB;AACD;AACF,KALM,MAKA;AACL;AACA,UAAIoD,YAAY,GAAG1F,cAAnB,EAAmC;AACjCkD,QAAAA,OAAO,GAAG,CAAC7B,UAAU,CAACiB,KAAtB;AACD;AACF;;AAEDiB,IAAAA,UAAU,CAACoC,YAAD,EAAezC,OAAf,EAAwBM,SAAS,GAAGvB,QAApC,CAAV;AACD,GApCD;;AAsCA,QAAML,KAAK,GAAG,MAAM;AAClB;;AACA2B,IAAAA,UAAU,CAAClB,sBAAsB,EAAvB,EAA2B,CAA3B,CAAV;AACD,GAHD;;AAKA,QAAMuD,UAAU,GAAGC,+BAAQC,GAAR,GAAcC,OAAd,CAAsB,MAAM;AAC7C,QAAI/E,QAAQ,CAACsB,KAAT,KAAmB,CAAvB,EAA0B;AACxBV,MAAAA,KAAK;AACN;AACF,GAJkB,CAAnB;;AAMA,QAAMoE,UAAU,GAAGH,+BAAQI,GAAR,GAChBC,QADgB,CACNT,KAAD,IAA8D;AACtExE,IAAAA,QAAQ,CAACqB,KAAT,GAAiBmD,KAAK,CAACC,YAAvB;AAEA,UAAMS,SAAS,GACbnF,QAAQ,CAACsB,KAAT,KAAmB,CAAC,CAApB,GACI,OADJ,GAEItB,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GACA,MADA,GAEAmD,KAAK,CAACC,YAAN,GAAqB,CAArB,GACA,MADA,GAEA,OAPN;;AASA,QAAI1E,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,IAAwBrC,wBAA5B,EAAsD;AACpD,0CAAQA,wBAAR,EAAkCkG,SAAlC;AACD,KAFD,MAEO,IAAInF,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,IAAwBpC,yBAA5B,EAAuD;AAC5D,0CAAQA,yBAAR,EAAmCiG,SAAnC;AACD;;AACD5D,IAAAA,mBAAmB;AACpB,GAnBgB,EAoBhB6D,KApBgB,CAqBdX,KAAD,IAAmE;AACjED,IAAAA,aAAa,CAACC,KAAD,CAAb;AACD,GAvBc,CAAnB;;AA0BA,MAAItF,8BAAJ,EAAoC;AAClC6F,IAAAA,UAAU,CAAC7F,8BAAX,CAA0CA,8BAA1C;AACD;;AAED6F,EAAAA,UAAU,CAACK,aAAX,CAAyB,CACvB,CAAC1B,uBADsB,EAEvBD,sBAFuB,CAAzB;AAIAkB,EAAAA,UAAU,CAACU,uBAAX,CAAmC,IAAnC;AAEA,kCAAoBxG,GAApB,EAAyB,MAAM6B,gBAAgB,CAAC0B,OAAhD,EAAyD,CACvD1B,gBADuD,CAAzD;AAIAqE,EAAAA,UAAU,CAAC5F,OAAX,CAAmBA,OAAO,KAAK,KAA/B;AAEA,QAAMmG,aAAa,GAAG,6CACpB,OAAO;AACL1B,IAAAA,SAAS,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE5D,kBAAkB,CAACoB;AAAjC,KAAD,CADN;AAELkE,IAAAA,aAAa,EAAExF,QAAQ,CAACsB,KAAT,KAAmB,CAAnB,GAAuB,MAAvB,GAAgC;AAF1C,GAAP,CADoB,EAKpB,CAACpB,kBAAD,EAAqBF,QAArB,CALoB,CAAtB;;AAQA,QAAMyF,kBAAkB,gBACtB,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAET,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,6BAAC,8BAAD,CAAU,IAAV,eACMjF,cADN;AAEE,IAAA,QAAQ,EAAEoD,WAFZ;AAGE,IAAA,KAAK,EAAE,CAACa,MAAM,CAAC0B,SAAR,EAAmBrG,cAAnB;AAHT,MAIG0E,WAJH,EAKGK,YALH,eAME,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAEQ,UAA1B;AAAsC,IAAA,WAAW,EAAC;AAAlD,kBACE,6BAAC,8BAAD,CAAU,IAAV;AAAe,IAAA,KAAK,EAAE,CAACW,aAAD,EAAgBjG,sBAAhB;AAAtB,KACGiE,QADH,CADF,CANF,CADF,CADF;;AAiBA,SAAOzD,MAAM,gBACX,6BAAC,iBAAD;AAAM,IAAA,MAAM,EAAEA;AAAd,KAAuB2F,kBAAvB,CADW,GAGXA,kBAHF;AAKD,CArbe,CAAlB;eAwbe7G,S;;;AAGf,MAAMoF,MAAM,GAAG2B,wBAAWC,MAAX,CAAkB;AAC/BF,EAAAA,SAAS,EAAE;AACTG,IAAAA,QAAQ,EAAE;AADD,GADoB;AAI/B5B,EAAAA,WAAW,EAAE,EACX,GAAG0B,wBAAWG,kBADH;AAEXC,IAAAA,aAAa,EAAEC,yBAAYC,KAAZ,GAAoB,aAApB,GAAoC;AAFxC,GAJkB;AAQ/B5B,EAAAA,YAAY,EAAE,EACZ,GAAGsB,wBAAWG,kBADF;AAEZC,IAAAA,aAAa,EAAEC,yBAAYC,KAAZ,GAAoB,KAApB,GAA4B;AAF/B;AARiB,CAAlB,CAAf", "sourcesContent": ["// Similarily to the DrawerLayout component this deserves to be put in a\n// separate repo. Although, keeping it here for the time being will allow us to\n// move faster and fix possible issues quicker\n\nimport React, {\n  ForwardedRef,\n  forwardRef,\n  useCallback,\n  useImperativeHandle,\n  useRef,\n} from 'react';\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\nimport {\n  GestureStateChangeEvent,\n  GestureUpdateEvent,\n} from '../handlers/gestureHandlerCommon';\nimport type { PanGestureHandlerProps } from '../handlers/PanGestureHandler';\nimport type { PanGestureHandlerEventPayload } from '../handlers/GestureHandlerEventPayload';\nimport Animated, {\n  Extrapolation,\n  SharedValue,\n  interpolate,\n  runOnJS,\n  useAnimatedStyle,\n  useSharedValue,\n  withSpring,\n} from 'react-native-reanimated';\nimport {\n  I18nManager,\n  LayoutChangeEvent,\n  StyleProp,\n  StyleSheet,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nconst DRAG_TOSS = 0.05;\n\ntype SwipeableExcludes = Exclude<\n  keyof PanGestureHandlerProps,\n  'onGestureEvent' | 'onHandlerStateChange'\n>;\n\nexport interface SwipeableProps\n  extends Pick<PanGestureHandlerProps, SwipeableExcludes> {\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * `enableTrackpadTwoFingerGesture` swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance. e.g. value of 1 will indicate that the swipeable panel\n   * should exactly follow the gesture, 2 means it is going to be two times\n   * \"slower\".\n   */\n  friction?: number;\n\n  /**\n   * Distance from the left edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  leftThreshold?: number;\n\n  /**\n   * Distance from the right edge at which released panel will animate to the\n   * open state (or the open panel will animate into the closed state). By\n   * default it's a half of the panel's width.\n   */\n  rightThreshold?: number;\n\n  /**\n   * Distance that the panel must be dragged from the left edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromLeftEdge?: number;\n\n  /**\n   * Distance that the panel must be dragged from the right edge to be considered\n   * a swipe. The default value is 10.\n   */\n  dragOffsetFromRightEdge?: number;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the left\n   * actions panel's width. It is set to true by default as long as the left\n   * panel render method is present.\n   */\n  overshootLeft?: boolean;\n\n  /**\n   * Value indicating if the swipeable panel can be pulled further than the\n   * right actions panel's width. It is set to true by default as long as the\n   * right panel render method is present.\n   */\n  overshootRight?: boolean;\n\n  /**\n   * Specifies how much the visual interaction will be delayed compared to the\n   * gesture distance at overshoot. Default value is 1, it mean no friction, for\n   * a native feel, try 8 or above.\n   */\n  overshootFriction?: number;\n\n  /**\n   * Called when action panel gets open (either right or left).\n   */\n  onSwipeableOpen?: (\n    direction: 'left' | 'right',\n    swipeable: SwipeableMethods\n  ) => void;\n\n  /**\n   * Called when action panel is closed.\n   */\n  onSwipeableClose?: (\n    direction: 'left' | 'right',\n    swipeable: SwipeableMethods\n  ) => void;\n\n  /**\n   * Called when action panel starts animating on open (either right or left).\n   */\n  onSwipeableWillOpen?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts animating on close.\n   */\n  onSwipeableWillClose?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to open.\n   */\n  onSwipeableOpenStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   * Called when action panel starts being shown on dragging to close.\n   */\n  onSwipeableCloseStartDrag?: (direction: 'left' | 'right') => void;\n\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, +]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderLeftActions?: (\n    progressAnimatedValue: SharedValue<number>,\n    dragAnimatedValue: SharedValue<number>,\n    swipeable: SwipeableMethods\n  ) => React.ReactNode;\n  /**\n   *\n   * This map describes the values to use as inputRange for extra interpolation:\n   * AnimatedValue: [startValue, endValue]\n   *\n   * progressAnimatedValue: [0, 1] dragAnimatedValue: [0, -]\n   *\n   * To support `rtl` flexbox layouts use `flexDirection` styling.\n   * */\n  renderRightActions?: (\n    progressAnimatedValue: SharedValue<number>,\n    dragAnimatedValue: SharedValue<number>,\n    swipeable: SwipeableMethods\n  ) => React.ReactNode;\n\n  animationOptions?: Record<string, unknown>;\n\n  /**\n   * Style object for the container (`Animated.View`), for example to override\n   * `overflow: 'hidden'`.\n   */\n  containerStyle?: StyleProp<ViewStyle>;\n\n  /**\n   * Style object for the children container (`Animated.View`), for example to\n   * apply `flex: 1`\n   */\n  childrenContainerStyle?: StyleProp<ViewStyle>;\n}\n\nexport interface SwipeableMethods {\n  close: () => void;\n  openLeft: () => void;\n  openRight: () => void;\n  reset: () => void;\n}\n\nconst Swipeable = forwardRef<SwipeableMethods, SwipeableProps>(\n  function Swipeable(\n    props: SwipeableProps,\n    ref: ForwardedRef<SwipeableMethods>\n  ) {\n    const {\n      leftThreshold,\n      rightThreshold,\n      onSwipeableOpenStartDrag,\n      onSwipeableCloseStartDrag,\n      enableTrackpadTwoFingerGesture,\n      enabled,\n      containerStyle,\n      childrenContainerStyle,\n      animationOptions,\n      overshootLeft,\n      overshootRight,\n      onSwipeableWillOpen,\n      onSwipeableWillClose,\n      onSwipeableOpen,\n      onSwipeableClose,\n      testID,\n      ...remainingProps\n    } = props;\n\n    const rowState = useSharedValue<number>(0);\n\n    const userDrag = useSharedValue<number>(0);\n    const appliedTranslation = useSharedValue<number>(0);\n\n    const rowWidth = useSharedValue<number>(0);\n    const leftWidth = useSharedValue<number>(0);\n    const rightWidth = useSharedValue<number>(0);\n    const rightOffset = useSharedValue<number>(0);\n\n    const leftActionTranslate = useSharedValue<number>(0);\n    const rightActionTranslate = useSharedValue<number>(0);\n\n    const showLeftProgress = useSharedValue<number>(0);\n    const showRightProgress = useSharedValue<number>(0);\n\n    const swipeableMethods = useRef<SwipeableMethods>({\n      close: () => {\n        'worklet';\n      },\n      openLeft: () => {\n        'worklet';\n      },\n      openRight: () => {\n        'worklet';\n      },\n      reset: () => {\n        'worklet';\n      },\n    });\n\n    const defaultProps = {\n      friction: 1,\n      overshootFriction: 1,\n    };\n\n    const {\n      friction = defaultProps.friction,\n      overshootFriction = defaultProps.overshootFriction,\n    } = props;\n\n    const overshootLeftProp = overshootLeft;\n    const overshootRightProp = overshootRight;\n\n    const calculateCurrentOffset = useCallback(() => {\n      'worklet';\n      if (rowState.value === 1) {\n        return leftWidth.value;\n      } else if (rowState.value === -1) {\n        return -rowWidth.value - rightOffset.value;\n      }\n      return 0;\n    }, [leftWidth, rightOffset, rowState, rowWidth]);\n\n    const updateAnimatedEvent = () => {\n      'worklet';\n      rightWidth.value = Math.max(0, rowWidth.value - rightOffset.value);\n\n      const overshootLeft = overshootLeftProp ?? leftWidth.value > 0;\n      const overshootRight = overshootRightProp ?? rightWidth.value > 0;\n\n      const startOffset =\n        rowState.value === 1\n          ? leftWidth.value\n          : rowState.value === -1\n          ? -rightWidth.value\n          : 0;\n\n      const offsetDrag = userDrag.value / friction + startOffset;\n\n      appliedTranslation.value = interpolate(\n        offsetDrag,\n        [\n          -rightWidth.value - 1,\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + 1,\n        ],\n        [\n          -rightWidth.value - (overshootRight ? 1 / overshootFriction : 0),\n          -rightWidth.value,\n          leftWidth.value,\n          leftWidth.value + (overshootLeft ? 1 / overshootFriction : 0),\n        ]\n      );\n\n      showLeftProgress.value =\n        leftWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-1, 0, leftWidth.value],\n              [0, 0, 1]\n            )\n          : 0;\n      leftActionTranslate.value = interpolate(\n        showLeftProgress.value,\n        [0, Number.MIN_VALUE],\n        [-10000, 0],\n        Extrapolation.CLAMP\n      );\n      showRightProgress.value =\n        rightWidth.value > 0\n          ? interpolate(\n              appliedTranslation.value,\n              [-rightWidth.value, 0, 1],\n              [1, 0, 0]\n            )\n          : 0;\n      rightActionTranslate.value = interpolate(\n        showRightProgress.value,\n        [0, Number.MIN_VALUE],\n        [-10000, 0],\n        Extrapolation.CLAMP\n      );\n    };\n\n    const dispatchImmediateEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        if (toValue > 0 && onSwipeableWillOpen) {\n          onSwipeableWillOpen('left');\n        } else if (toValue < 0 && onSwipeableWillOpen) {\n          onSwipeableWillOpen('right');\n        } else if (onSwipeableWillClose) {\n          const closingDirection = fromValue > 0 ? 'left' : 'right';\n          onSwipeableWillClose(closingDirection);\n        }\n      },\n      [onSwipeableWillClose, onSwipeableWillOpen]\n    );\n\n    const dispatchEndEvents = useCallback(\n      (fromValue: number, toValue: number) => {\n        if (toValue > 0 && onSwipeableOpen) {\n          onSwipeableOpen('left', swipeableMethods.current);\n        } else if (toValue < 0 && onSwipeableOpen) {\n          onSwipeableOpen('right', swipeableMethods.current);\n        } else if (onSwipeableClose) {\n          const closingDirection = fromValue > 0 ? 'left' : 'right';\n          onSwipeableClose(closingDirection, swipeableMethods.current);\n        }\n      },\n      [onSwipeableClose, onSwipeableOpen]\n    );\n\n    const animationOptionsProp = animationOptions;\n\n    const animateRow = useCallback(\n      (fromValue: number, toValue: number, velocityX?: number) => {\n        'worklet';\n        rowState.value = Math.sign(toValue);\n\n        const translationSpringConfig = {\n          duration: 1000,\n          dampingRatio: 0.9,\n          stiffness: 500,\n          velocity: velocityX,\n          overshootClamping: true,\n          ...animationOptionsProp,\n        };\n\n        const progressSpringConfig = {\n          ...translationSpringConfig,\n          velocity: 0,\n        };\n\n        appliedTranslation.value = withSpring(\n          toValue,\n          translationSpringConfig,\n          (isFinished) => {\n            if (isFinished) {\n              runOnJS(dispatchEndEvents)(fromValue, toValue);\n            }\n          }\n        );\n\n        const progressTarget = toValue === 0 ? 0 : 1;\n\n        showLeftProgress.value =\n          leftWidth.value > 0\n            ? withSpring(progressTarget, progressSpringConfig)\n            : 0;\n        showRightProgress.value =\n          rightWidth.value > 0\n            ? withSpring(progressTarget, progressSpringConfig)\n            : 0;\n\n        runOnJS(dispatchImmediateEvents)(fromValue, toValue);\n      },\n      [\n        rowState,\n        animationOptionsProp,\n        appliedTranslation,\n        showLeftProgress,\n        leftWidth.value,\n        showRightProgress,\n        rightWidth.value,\n        dispatchImmediateEvents,\n        dispatchEndEvents,\n      ]\n    );\n\n    const onRowLayout = ({ nativeEvent }: LayoutChangeEvent) => {\n      rowWidth.value = nativeEvent.layout.width;\n    };\n\n    const {\n      children,\n      renderLeftActions,\n      renderRightActions,\n      dragOffsetFromLeftEdge = 10,\n      dragOffsetFromRightEdge = 10,\n    } = props;\n\n    swipeableMethods.current = {\n      close() {\n        'worklet';\n        animateRow(calculateCurrentOffset(), 0);\n      },\n      openLeft() {\n        'worklet';\n        animateRow(calculateCurrentOffset(), leftWidth.value);\n      },\n      openRight() {\n        'worklet';\n        rightWidth.value = rowWidth.value - rightOffset.value;\n        animateRow(calculateCurrentOffset(), -rightWidth.value);\n      },\n      reset() {\n        'worklet';\n        userDrag.value = 0;\n        showLeftProgress.value = 0;\n        appliedTranslation.value = 0;\n        rowState.value = 0;\n      },\n    };\n\n    const leftAnimatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [\n          {\n            translateX: leftActionTranslate.value,\n          },\n        ],\n      }),\n      [leftActionTranslate]\n    );\n\n    const leftElement = renderLeftActions && (\n      <Animated.View style={[styles.leftActions, leftAnimatedStyle]}>\n        {renderLeftActions(\n          showLeftProgress,\n          appliedTranslation,\n          swipeableMethods.current\n        )}\n        <View\n          onLayout={({ nativeEvent }) =>\n            (leftWidth.value = nativeEvent.layout.x)\n          }\n        />\n      </Animated.View>\n    );\n\n    const rightAnimatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [\n          {\n            translateX: rightActionTranslate.value,\n          },\n        ],\n      }),\n      [rightActionTranslate]\n    );\n\n    const rightElement = renderRightActions && (\n      <Animated.View style={[styles.rightActions, rightAnimatedStyle]}>\n        {renderRightActions(\n          showRightProgress,\n          appliedTranslation,\n          swipeableMethods.current\n        )}\n        <View\n          onLayout={({ nativeEvent }) =>\n            (rightOffset.value = nativeEvent.layout.x)\n          }\n        />\n      </Animated.View>\n    );\n\n    const leftThresholdProp = leftThreshold;\n    const rightThresholdProp = rightThreshold;\n\n    const handleRelease = (\n      event: GestureStateChangeEvent<PanGestureHandlerEventPayload>\n    ) => {\n      'worklet';\n      const { velocityX } = event;\n      userDrag.value = event.translationX;\n\n      rightWidth.value = rowWidth.value - rightOffset.value;\n\n      const leftThreshold = leftThresholdProp ?? leftWidth.value / 2;\n      const rightThreshold = rightThresholdProp ?? rightWidth.value / 2;\n\n      const startOffsetX = calculateCurrentOffset() + userDrag.value / friction;\n      const translationX = (userDrag.value + DRAG_TOSS * velocityX) / friction;\n\n      let toValue = 0;\n\n      if (rowState.value === 0) {\n        if (translationX > leftThreshold) {\n          toValue = leftWidth.value;\n        } else if (translationX < -rightThreshold) {\n          toValue = -rightWidth.value;\n        }\n      } else if (rowState.value === 1) {\n        // Swiped to left\n        if (translationX > -leftThreshold) {\n          toValue = leftWidth.value;\n        }\n      } else {\n        // Swiped to right\n        if (translationX < rightThreshold) {\n          toValue = -rightWidth.value;\n        }\n      }\n\n      animateRow(startOffsetX, toValue, velocityX / friction);\n    };\n\n    const close = () => {\n      'worklet';\n      animateRow(calculateCurrentOffset(), 0);\n    };\n\n    const tapGesture = Gesture.Tap().onStart(() => {\n      if (rowState.value !== 0) {\n        close();\n      }\n    });\n\n    const panGesture = Gesture.Pan()\n      .onUpdate((event: GestureUpdateEvent<PanGestureHandlerEventPayload>) => {\n        userDrag.value = event.translationX;\n\n        const direction =\n          rowState.value === -1\n            ? 'right'\n            : rowState.value === 1\n            ? 'left'\n            : event.translationX > 0\n            ? 'left'\n            : 'right';\n\n        if (rowState.value === 0 && onSwipeableOpenStartDrag) {\n          runOnJS(onSwipeableOpenStartDrag)(direction);\n        } else if (rowState.value !== 0 && onSwipeableCloseStartDrag) {\n          runOnJS(onSwipeableCloseStartDrag)(direction);\n        }\n        updateAnimatedEvent();\n      })\n      .onEnd(\n        (event: GestureStateChangeEvent<PanGestureHandlerEventPayload>) => {\n          handleRelease(event);\n        }\n      );\n\n    if (enableTrackpadTwoFingerGesture) {\n      panGesture.enableTrackpadTwoFingerGesture(enableTrackpadTwoFingerGesture);\n    }\n\n    panGesture.activeOffsetX([\n      -dragOffsetFromRightEdge,\n      dragOffsetFromLeftEdge,\n    ]);\n    tapGesture.shouldCancelWhenOutside(true);\n\n    useImperativeHandle(ref, () => swipeableMethods.current, [\n      swipeableMethods,\n    ]);\n\n    panGesture.enabled(enabled !== false);\n\n    const animatedStyle = useAnimatedStyle(\n      () => ({\n        transform: [{ translateX: appliedTranslation.value }],\n        pointerEvents: rowState.value === 0 ? 'auto' : 'box-only',\n      }),\n      [appliedTranslation, rowState]\n    );\n\n    const swipeableComponent = (\n      <GestureDetector gesture={panGesture} touchAction=\"pan-y\">\n        <Animated.View\n          {...remainingProps}\n          onLayout={onRowLayout}\n          style={[styles.container, containerStyle]}>\n          {leftElement}\n          {rightElement}\n          <GestureDetector gesture={tapGesture} touchAction=\"pan-y\">\n            <Animated.View style={[animatedStyle, childrenContainerStyle]}>\n              {children}\n            </Animated.View>\n          </GestureDetector>\n        </Animated.View>\n      </GestureDetector>\n    );\n\n    return testID ? (\n      <View testID={testID}>{swipeableComponent}</View>\n    ) : (\n      swipeableComponent\n    );\n  }\n);\n\nexport default Swipeable;\nexport type SwipeableRef = ForwardedRef<SwipeableMethods>;\n\nconst styles = StyleSheet.create({\n  container: {\n    overflow: 'hidden',\n  },\n  leftActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',\n  },\n  rightActions: {\n    ...StyleSheet.absoluteFillObject,\n    flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse',\n  },\n});\n"]}