{"version": 3, "sources": ["NativeViewGestureHandler.ts"], "names": ["DiscreteGestureHandler", "NodeManager", "PressGestureHandler", "TEST_MIN_IF_NOT_NAN", "VEC_LEN_SQ", "NativeViewGestureHandler", "isNative", "onRawEvent", "ev", "isFinal", "x", "deltaX", "y", "deltaY", "config", "disallowInterruption", "gestures", "Object", "values", "getNodes", "filter", "gesture", "handlerTag", "view", "isGestureRunning", "contains", "forceInvalidate"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,0BAAnC;AAEA,OAAO,KAAKC,WAAZ,MAA6B,eAA7B;AACA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,SAASC,mBAAT,EAA8BC,UAA9B,QAAgD,SAAhD;;AAEA,MAAMC,wBAAN,SAAuCH,mBAAvC,CAA2D;AAC7C,MAARI,QAAQ,GAAG;AACb,WAAO,IAAP;AACD;;AAEDC,EAAAA,UAAU,CAACC,EAAD,EAAqB;AAC7B,UAAMD,UAAN,CAAiBC,EAAjB;;AACA,QAAI,CAACA,EAAE,CAACC,OAAR,EAAiB;AACf;AACA,UAAIN,mBAAmB,CAACC,UAAU,CAAC;AAAEM,QAAAA,CAAC,EAAEF,EAAE,CAACG,MAAR;AAAgBC,QAAAA,CAAC,EAAEJ,EAAE,CAACK;AAAtB,OAAD,CAAX,EAA6C,EAA7C,CAAvB,EAAyE;AACvE;AACA,YAAI,KAAKC,MAAL,CAAYC,oBAAhB,EAAsC;AACpC,gBAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAP,CAAcjB,WAAW,CAACkB,QAAZ,EAAd,EAAsCC,MAAtC,CACdC,OAAD,IAAa;AACX,kBAAM;AAAEC,cAAAA,UAAF;AAAcC,cAAAA,IAAd;AAAoBC,cAAAA;AAApB,gBAAyCH,OAA/C;AACA,mBACE;AACAC,cAAAA,UAAU,KAAK,KAAKA,UAApB,IACA;AACAE,cAAAA,gBAFA,IAGA;AACAH,cAAAA,OAAO,YAAYrB,sBAJnB,IAKA;AACAuB,cAAAA,IANA,IAOA;AACA,mBAAKA,IAAL,CAAUE,QAAV,CAAmBF,IAAnB;AAVF;AAYD,WAfc,CAAjB,CADoC,CAkBpC;;AACA,eAAK,MAAMF,OAAX,IAAsBL,QAAtB,EAAgC;AAC9B;AACAK,YAAAA,OAAO,CAACK,eAAR,CAAwBlB,EAAxB;AACD;AACF;AACF;AACF;AACF;;AArCwD;;AAwC3D,eAAeH,wBAAf", "sourcesContent": ["import DiscreteGestureHandler from './DiscreteGestureHandler';\nimport { HammerInputExt } from './GestureHandler';\nimport * as NodeManager from './NodeManager';\nimport PressGestureHandler from './PressGestureHandler';\nimport { TEST_MIN_IF_NOT_NAN, VEC_LEN_SQ } from './utils';\n\nclass NativeViewGestureHandler extends PressGestureHandler {\n  get isNative() {\n    return true;\n  }\n\n  onRawEvent(ev: HammerInputExt) {\n    super.onRawEvent(ev);\n    if (!ev.isFinal) {\n      // if (this.ref instanceof ScrollView) {\n      if (TEST_MIN_IF_NOT_NAN(VEC_LEN_SQ({ x: ev.deltaX, y: ev.deltaY }), 10)) {\n        // @ts-ignore FIXME(TS) config type\n        if (this.config.disallowInterruption) {\n          const gestures = Object.values(NodeManager.getNodes()).filter(\n            (gesture) => {\n              const { handlerTag, view, isGestureRunning } = gesture;\n              return (\n                // Check if this gesture isn't self\n                handlerTag !== this.handlerTag &&\n                // Ensure the gesture needs to be cancelled\n                isGestureRunning &&\n                // ScrollView can cancel discrete gestures like taps and presses\n                gesture instanceof DiscreteGestureHandler &&\n                // Ensure a view exists and is a child of the current view\n                view &&\n                // @ts-ignore FIXME(TS) view type\n                this.view.contains(view)\n              );\n            }\n          );\n          // Cancel all of the gestures that passed the filter\n          for (const gesture of gestures) {\n            // TODO: Bacon: Send some cached event.\n            gesture.forceInvalidate(ev);\n          }\n        }\n      }\n    }\n  }\n}\n\nexport default NativeViewGestureHandler;\n"]}