{"version": 3, "file": "Scheme.js", "names": ["_iosPlugins", "data", "require", "withScheme", "exports", "createInfoPlistPluginWithPropertyGuard", "setScheme", "infoPlistProperty", "expoConfigProperty", "getScheme", "config", "Array", "isArray", "scheme", "validate", "value", "filter", "infoPlist", "ios", "bundleIdentifier", "push", "length", "CFBundleURLTypes", "CFBundleURLSchemes", "appendScheme", "existingSchemes", "some", "includes", "removeScheme", "map", "bundleUrlType", "index", "indexOf", "splice", "undefined", "Boolean", "hasScheme", "schemes", "getSchemesFromPlist", "reduce"], "sources": ["../../src/ios/Scheme.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist, URLScheme } from './IosConfig.types';\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\n\nexport const withScheme = createInfoPlistPluginWithPropertyGuard(\n  setScheme,\n  {\n    infoPlistProperty: 'CFBundleURLTypes',\n    expoConfigProperty: 'scheme',\n  },\n  'withScheme'\n);\n\nexport function getScheme(config: { scheme?: string | string[] }): string[] {\n  if (Array.isArray(config.scheme)) {\n    const validate = (value: any): value is string => {\n      return typeof value === 'string';\n    };\n    return config.scheme.filter<string>(validate);\n  } else if (typeof config.scheme === 'string') {\n    return [config.scheme];\n  }\n  return [];\n}\n\nexport function setScheme(\n  config: Partial<Pick<ExpoConfig, 'scheme' | 'ios'>>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const scheme = [...getScheme(config), ...getScheme(config.ios ?? {})];\n  // Add the bundle identifier to the list of schemes for easier Google auth and parity with Turtle v1.\n  if (config.ios?.bundleIdentifier) {\n    scheme.push(config.ios.bundleIdentifier);\n  }\n  if (scheme.length === 0) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleURLTypes: [{ CFBundleURLSchemes: scheme }],\n  };\n}\n\nexport function appendScheme(scheme: string | null, infoPlist: InfoPlist): InfoPlist {\n  if (!scheme) {\n    return infoPlist;\n  }\n\n  const existingSchemes = infoPlist.CFBundleURLTypes ?? [];\n  if (existingSchemes?.some(({ CFBundleURLSchemes }) => CFBundleURLSchemes.includes(scheme))) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleURLTypes: [\n      ...existingSchemes,\n      {\n        CFBundleURLSchemes: [scheme],\n      },\n    ],\n  };\n}\n\nexport function removeScheme(scheme: string | null, infoPlist: InfoPlist): InfoPlist {\n  if (!scheme) {\n    return infoPlist;\n  }\n\n  // No need to remove if we don't have any\n  if (!infoPlist.CFBundleURLTypes) {\n    return infoPlist;\n  }\n\n  infoPlist.CFBundleURLTypes = infoPlist.CFBundleURLTypes.map((bundleUrlType) => {\n    const index = bundleUrlType.CFBundleURLSchemes.indexOf(scheme);\n    if (index > -1) {\n      bundleUrlType.CFBundleURLSchemes.splice(index, 1);\n      if (bundleUrlType.CFBundleURLSchemes.length === 0) {\n        return undefined;\n      }\n    }\n    return bundleUrlType;\n  }).filter(Boolean) as URLScheme[];\n\n  return infoPlist;\n}\n\nexport function hasScheme(scheme: string, infoPlist: InfoPlist): boolean {\n  const existingSchemes = infoPlist.CFBundleURLTypes;\n\n  if (!Array.isArray(existingSchemes)) return false;\n\n  return existingSchemes?.some(({ CFBundleURLSchemes: schemes }: any) =>\n    Array.isArray(schemes) ? schemes.includes(scheme) : false\n  );\n}\n\nexport function getSchemesFromPlist(infoPlist: InfoPlist): string[] {\n  if (Array.isArray(infoPlist.CFBundleURLTypes)) {\n    return infoPlist.CFBundleURLTypes.reduce<string[]>((schemes, { CFBundleURLSchemes }) => {\n      if (Array.isArray(CFBundleURLSchemes)) {\n        return [...schemes, ...CFBundleURLSchemes];\n      }\n      return schemes;\n    }, []);\n  }\n  return [];\n}\n"], "mappings": ";;;;;;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,IAAAE,oDAAsC,EAC9DC,SAAS,EACT;EACEC,iBAAiB,EAAE,kBAAkB;EACrCC,kBAAkB,EAAE;AACtB,CAAC,EACD,YACF,CAAC;AAEM,SAASC,SAASA,CAACC,MAAsC,EAAY;EAC1E,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACG,MAAM,CAAC,EAAE;IAChC,MAAMC,QAAQ,GAAIC,KAAU,IAAsB;MAChD,OAAO,OAAOA,KAAK,KAAK,QAAQ;IAClC,CAAC;IACD,OAAOL,MAAM,CAACG,MAAM,CAACG,MAAM,CAASF,QAAQ,CAAC;EAC/C,CAAC,MAAM,IAAI,OAAOJ,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;IAC5C,OAAO,CAACH,MAAM,CAACG,MAAM,CAAC;EACxB;EACA,OAAO,EAAE;AACX;AAEO,SAASP,SAASA,CACvBI,MAAmD,EACnDO,SAAoB,EACT;EACX,MAAMJ,MAAM,GAAG,CAAC,GAAGJ,SAAS,CAACC,MAAM,CAAC,EAAE,GAAGD,SAAS,CAACC,MAAM,CAACQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;EACrE;EACA,IAAIR,MAAM,CAACQ,GAAG,EAAEC,gBAAgB,EAAE;IAChCN,MAAM,CAACO,IAAI,CAACV,MAAM,CAACQ,GAAG,CAACC,gBAAgB,CAAC;EAC1C;EACA,IAAIN,MAAM,CAACQ,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOJ,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZK,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAEV;IAAO,CAAC;EACnD,CAAC;AACH;AAEO,SAASW,YAAYA,CAACX,MAAqB,EAAEI,SAAoB,EAAa;EACnF,IAAI,CAACJ,MAAM,EAAE;IACX,OAAOI,SAAS;EAClB;EAEA,MAAMQ,eAAe,GAAGR,SAAS,CAACK,gBAAgB,IAAI,EAAE;EACxD,IAAIG,eAAe,EAAEC,IAAI,CAAC,CAAC;IAAEH;EAAmB,CAAC,KAAKA,kBAAkB,CAACI,QAAQ,CAACd,MAAM,CAAC,CAAC,EAAE;IAC1F,OAAOI,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZK,gBAAgB,EAAE,CAChB,GAAGG,eAAe,EAClB;MACEF,kBAAkB,EAAE,CAACV,MAAM;IAC7B,CAAC;EAEL,CAAC;AACH;AAEO,SAASe,YAAYA,CAACf,MAAqB,EAAEI,SAAoB,EAAa;EACnF,IAAI,CAACJ,MAAM,EAAE;IACX,OAAOI,SAAS;EAClB;;EAEA;EACA,IAAI,CAACA,SAAS,CAACK,gBAAgB,EAAE;IAC/B,OAAOL,SAAS;EAClB;EAEAA,SAAS,CAACK,gBAAgB,GAAGL,SAAS,CAACK,gBAAgB,CAACO,GAAG,CAAEC,aAAa,IAAK;IAC7E,MAAMC,KAAK,GAAGD,aAAa,CAACP,kBAAkB,CAACS,OAAO,CAACnB,MAAM,CAAC;IAC9D,IAAIkB,KAAK,GAAG,CAAC,CAAC,EAAE;MACdD,aAAa,CAACP,kBAAkB,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjD,IAAID,aAAa,CAACP,kBAAkB,CAACF,MAAM,KAAK,CAAC,EAAE;QACjD,OAAOa,SAAS;MAClB;IACF;IACA,OAAOJ,aAAa;EACtB,CAAC,CAAC,CAACd,MAAM,CAACmB,OAAO,CAAgB;EAEjC,OAAOlB,SAAS;AAClB;AAEO,SAASmB,SAASA,CAACvB,MAAc,EAAEI,SAAoB,EAAW;EACvE,MAAMQ,eAAe,GAAGR,SAAS,CAACK,gBAAgB;EAElD,IAAI,CAACX,KAAK,CAACC,OAAO,CAACa,eAAe,CAAC,EAAE,OAAO,KAAK;EAEjD,OAAOA,eAAe,EAAEC,IAAI,CAAC,CAAC;IAAEH,kBAAkB,EAAEc;EAAa,CAAC,KAChE1B,KAAK,CAACC,OAAO,CAACyB,OAAO,CAAC,GAAGA,OAAO,CAACV,QAAQ,CAACd,MAAM,CAAC,GAAG,KACtD,CAAC;AACH;AAEO,SAASyB,mBAAmBA,CAACrB,SAAoB,EAAY;EAClE,IAAIN,KAAK,CAACC,OAAO,CAACK,SAAS,CAACK,gBAAgB,CAAC,EAAE;IAC7C,OAAOL,SAAS,CAACK,gBAAgB,CAACiB,MAAM,CAAW,CAACF,OAAO,EAAE;MAAEd;IAAmB,CAAC,KAAK;MACtF,IAAIZ,KAAK,CAACC,OAAO,CAACW,kBAAkB,CAAC,EAAE;QACrC,OAAO,CAAC,GAAGc,OAAO,EAAE,GAAGd,kBAAkB,CAAC;MAC5C;MACA,OAAOc,OAAO;IAChB,CAAC,EAAE,EAAE,CAAC;EACR;EACA,OAAO,EAAE;AACX", "ignoreList": []}