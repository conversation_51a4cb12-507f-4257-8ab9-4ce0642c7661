{"name": "@isaacs/ttlcache", "version": "1.4.1", "files": ["index.js", "index.d.ts"], "main": "index.js", "exports": {".": "./index.js"}, "description": "The time-based use-recency-unaware cousin of [`lru-cache`](http://npm.im/lru-cache)", "repository": {"type": "git", "url": "git+https://github.com/isaacs/ttlcache"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "devDependencies": {"@types/node": "^17.0.42", "@types/tap": "^15.0.7", "clock-mock": "^1.0.6", "prettier": "^2.7.0", "tap": "^16.0.1", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "engines": {"node": ">=12"}, "tap": {"nyc-arg": ["--include=index.js"], "node-arg": ["--require", "ts-node/register"], "ts": false}, "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}}