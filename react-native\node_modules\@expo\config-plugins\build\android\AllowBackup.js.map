{"version": 3, "file": "AllowBackup.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "withA<PERSON>Backup", "exports", "createAndroidManifestPlugin", "setAllowBackup", "getAllowBackup", "config", "android", "allowBackup", "androidManifest", "mainApplication", "getMainApplication", "$", "String", "getAllowBackupFromManifest"], "sources": ["../../src/android/AllowBackup.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, getMainApplication, StringBoolean } from './Manifest';\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\n\nexport const withAllowBackup = createAndroidManifestPlugin(setAllowBackup, 'withAllowBackup');\n\nexport function getAllowBackup(config: Pick<ExpoConfig, 'android'>) {\n  // Defaults to true.\n  // https://docs.expo.dev/versions/latest/config/app/#allowbackup\n  return config.android?.allowBackup ?? true;\n}\n\nexport function setAllowBackup(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const allowBackup = getAllowBackup(config);\n\n  const mainApplication = getMainApplication(androidManifest);\n  if (mainApplication?.$) {\n    mainApplication.$['android:allowBackup'] = String(allowBackup) as StringBoolean;\n  }\n\n  return androidManifest;\n}\n\nexport function getAllowBackupFromManifest(androidManifest: AndroidManifest): boolean | null {\n  const mainApplication = getMainApplication(androidManifest);\n\n  if (mainApplication?.$) {\n    return String(mainApplication.$['android:allowBackup']) === 'true';\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,IAAAE,6CAA2B,EAACC,cAAc,EAAE,iBAAiB,CAAC;AAEtF,SAASC,cAAcA,CAACC,MAAmC,EAAE;EAClE;EACA;EACA,OAAOA,MAAM,CAACC,OAAO,EAAEC,WAAW,IAAI,IAAI;AAC5C;AAEO,SAASJ,cAAcA,CAC5BE,MAAmC,EACnCG,eAAgC,EAChC;EACA,MAAMD,WAAW,GAAGH,cAAc,CAACC,MAAM,CAAC;EAE1C,MAAMI,eAAe,GAAG,IAAAC,8BAAkB,EAACF,eAAe,CAAC;EAC3D,IAAIC,eAAe,EAAEE,CAAC,EAAE;IACtBF,eAAe,CAACE,CAAC,CAAC,qBAAqB,CAAC,GAAGC,MAAM,CAACL,WAAW,CAAkB;EACjF;EAEA,OAAOC,eAAe;AACxB;AAEO,SAASK,0BAA0BA,CAACL,eAAgC,EAAkB;EAC3F,MAAMC,eAAe,GAAG,IAAAC,8BAAkB,EAACF,eAAe,CAAC;EAE3D,IAAIC,eAAe,EAAEE,CAAC,EAAE;IACtB,OAAOC,MAAM,CAACH,eAAe,CAACE,CAAC,CAAC,qBAAqB,CAAC,CAAC,KAAK,MAAM;EACpE;EAEA,OAAO,IAAI;AACb", "ignoreList": []}