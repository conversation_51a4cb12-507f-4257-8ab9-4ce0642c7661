{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_ScreenFooterNativeComponent", "obj", "__esModule", "default", "ScreenFooter", "props", "createElement", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ScreenFooter.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,4BAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAgF,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEhF;AACA;AACA;AACA,SAASG,YAAYA,CAACC,KAAgB,EAAE;EACtC,oBAAOR,MAAA,CAAAM,OAAA,CAAAG,aAAA,CAACN,4BAAA,CAAAG,OAA2B,EAAKE,KAAQ,CAAC;AACnD;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAL,OAAA,GAEcC,YAAY"}