{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcodeDeveloperDiskImagePrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport fs from 'fs/promises';\n\nimport * as Log from '../../../log';\nimport { Prerequisite, PrerequisiteCommandError } from '../Prerequisite';\n\nconst ERROR_CODE = 'XCODE_DEVELOPER_DISK_IMAGE';\nasync function getXcodePathAsync(): Promise<string> {\n  try {\n    const { stdout } = await spawnAsync('xcode-select', ['-p']);\n    if (stdout) {\n      return stdout.trim();\n    }\n  } catch (error: any) {\n    Log.debug(`Could not find Xcode path: %O`, error);\n  }\n  throw new PrerequisiteCommandError(ERROR_CODE, 'Unable to locate Xcode.');\n}\n\nexport class XcodeDeveloperDiskImagePrerequisite extends Prerequisite<string, { version: string }> {\n  static instance = new XcodeDeveloperDiskImagePrerequisite();\n\n  async assertImplementation({ version }: { version: string }): Promise<string> {\n    const xcodePath = await getXcodePathAsync();\n    // Like \"11.2 (15C107)\"\n    const versions = await fs.readdir(`${xcodePath}/Platforms/iPhoneOS.platform/DeviceSupport/`);\n    const prefix = version.match(/\\d+\\.\\d+/);\n    if (prefix === null) {\n      throw new PrerequisiteCommandError(ERROR_CODE, `Invalid iOS version: ${version}`);\n    }\n    for (const directory of versions) {\n      if (directory.includes(prefix[0])) {\n        return `${xcodePath}/Platforms/iPhoneOS.platform/DeviceSupport/${directory}/DeveloperDiskImage.dmg`;\n      }\n    }\n    throw new PrerequisiteCommandError(\n      ERROR_CODE,\n      `Unable to find Developer Disk Image path for SDK ${version}.`\n    );\n  }\n}\n"], "names": ["XcodeDeveloperDiskImagePrerequisite", "ERROR_CODE", "getXcodePathAsync", "stdout", "spawnAsync", "trim", "error", "Log", "debug", "PrerequisiteCommandError", "Prerequisite", "instance", "assertImplementation", "version", "xcodePath", "versions", "fs", "readdir", "prefix", "match", "directory", "includes"], "mappings": ";;;;+BAmBaA;;;eAAAA;;;;gEAnBU;;;;;;;gEACR;;;;;;6DAEM;8BACkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvD,MAAMC,aAAa;AACnB,eAAeC;IACb,IAAI;QACF,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAMC,IAAAA,qBAAU,EAAC,gBAAgB;YAAC;SAAK;QAC1D,IAAID,QAAQ;YACV,OAAOA,OAAOE,IAAI;QACpB;IACF,EAAE,OAAOC,OAAY;QACnBC,KAAIC,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAEF;IAC7C;IACA,MAAM,IAAIG,sCAAwB,CAACR,YAAY;AACjD;AAEO,MAAMD,4CAA4CU,0BAAY;qBAC5DC,WAAW,IAAIX;IAEtB,MAAMY,qBAAqB,EAAEC,OAAO,EAAuB,EAAmB;QAC5E,MAAMC,YAAY,MAAMZ;QACxB,uBAAuB;QACvB,MAAMa,WAAW,MAAMC,mBAAE,CAACC,OAAO,CAAC,GAAGH,UAAU,2CAA2C,CAAC;QAC3F,MAAMI,SAASL,QAAQM,KAAK,CAAC;QAC7B,IAAID,WAAW,MAAM;YACnB,MAAM,IAAIT,sCAAwB,CAACR,YAAY,CAAC,qBAAqB,EAAEY,SAAS;QAClF;QACA,KAAK,MAAMO,aAAaL,SAAU;YAChC,IAAIK,UAAUC,QAAQ,CAACH,MAAM,CAAC,EAAE,GAAG;gBACjC,OAAO,GAAGJ,UAAU,2CAA2C,EAAEM,UAAU,uBAAuB,CAAC;YACrG;QACF;QACA,MAAM,IAAIX,sCAAwB,CAChCR,YACA,CAAC,iDAAiD,EAAEY,QAAQ,CAAC,CAAC;IAElE;AACF"}