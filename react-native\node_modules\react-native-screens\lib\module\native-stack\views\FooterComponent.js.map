{"version": 3, "names": ["React", "ScreenFooter", "FooterComponent", "_ref", "children", "createElement", "collapsable"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/FooterComponent.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,+BAA+B;AAMxD,eAAe,SAASC,eAAeA,CAAAC,IAAA,EAA4B;EAAA,IAA3B;IAAEC;EAAsB,CAAC,GAAAD,IAAA;EAC/D,oBAAOH,KAAA,CAAAK,aAAA,CAACJ,YAAY;IAACK,WAAW,EAAE;EAAM,GAAEF,QAAuB,CAAC;AACpE"}