{"version": 3, "names": ["runOnUIImmediately", "prepareUIRegistry", "frameCallbackRegistry", "Map", "activeFrameCallbacks", "Set", "previousFrameTimestamp", "nextCallId", "runCallbacks", "callId", "loop", "timestamp", "delta", "for<PERSON>ach", "callbackId", "callbackDetails", "get", "startTime", "callback", "timeSincePreviousFrame", "timeSinceFirstFrame", "size", "requestAnimationFrame", "registerFrameCallback", "set", "unregisterFrameCallback", "manageStateFrameCallback", "delete", "state", "add", "global", "_frameCallbackRegistry"], "sourceRoot": "../../../src", "sources": ["frameCallback/FrameCallbackRegistryUI.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,kBAAkB,QAAQ,eAAY;AA2B/C,OAAO,MAAMC,iBAAiB,GAAGD,kBAAkB,CAAC,MAAM;EACxD,SAAS;;EAET,MAAME,qBAA8C,GAAG;IACrDA,qBAAqB,EAAE,IAAIC,GAAG,CAA0B,CAAC;IACzDC,oBAAoB,EAAE,IAAIC,GAAG,CAAS,CAAC;IACvCC,sBAAsB,EAAE,IAAI;IAC5BC,UAAU,EAAE,CAAC;IAEbC,YAAYA,CAACC,MAAM,EAAE;MACnB,MAAMC,IAAI,GAAIC,SAAiB,IAAK;QAClC,IAAIF,MAAM,KAAK,IAAI,CAACF,UAAU,EAAE;UAC9B;QACF;QACA,IAAI,IAAI,CAACD,sBAAsB,KAAK,IAAI,EAAE;UACxC,IAAI,CAACA,sBAAsB,GAAGK,SAAS;QACzC;QAEA,MAAMC,KAAK,GAAGD,SAAS,GAAG,IAAI,CAACL,sBAAsB;QAErD,IAAI,CAACF,oBAAoB,CAACS,OAAO,CAAEC,UAAkB,IAAK;UACxD,MAAMC,eAAe,GAAG,IAAI,CAACb,qBAAqB,CAACc,GAAG,CAACF,UAAU,CAAE;UAEnE,MAAM;YAAEG;UAAU,CAAC,GAAGF,eAAe;UAErC,IAAIE,SAAS,KAAK,IAAI,EAAE;YACtB;YACAF,eAAe,CAACE,SAAS,GAAGN,SAAS;YAErCI,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS;cACTQ,sBAAsB,EAAE,IAAI;cAC5BC,mBAAmB,EAAE;YACvB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAL,eAAe,CAACG,QAAQ,CAAC;cACvBP,SAAS;cACTQ,sBAAsB,EAAEP,KAAK;cAC7BQ,mBAAmB,EAAET,SAAS,GAAGM;YACnC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,IAAI,IAAI,CAACb,oBAAoB,CAACiB,IAAI,GAAG,CAAC,EAAE;UACtC,IAAI,CAACf,sBAAsB,GAAGK,SAAS;UACvCW,qBAAqB,CAACZ,IAAI,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACJ,sBAAsB,GAAG,IAAI;QACpC;MACF,CAAC;;MAED;MACA;MACA;MACA,IAAI,IAAI,CAACF,oBAAoB,CAACiB,IAAI,KAAK,CAAC,IAAIZ,MAAM,KAAK,IAAI,CAACF,UAAU,EAAE;QACtEe,qBAAqB,CAACZ,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDa,qBAAqBA,CACnBL,QAAwC,EACxCJ,UAAkB,EAClB;MACA,IAAI,CAACZ,qBAAqB,CAACsB,GAAG,CAACV,UAAU,EAAE;QACzCI,QAAQ;QACRD,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAEDQ,uBAAuBA,CAACX,UAAkB,EAAE;MAC1C,IAAI,CAACY,wBAAwB,CAACZ,UAAU,EAAE,KAAK,CAAC;MAChD,IAAI,CAACZ,qBAAqB,CAACyB,MAAM,CAACb,UAAU,CAAC;IAC/C,CAAC;IAEDY,wBAAwBA,CAACZ,UAAkB,EAAEc,KAAc,EAAE;MAC3D,IAAId,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB;MACF;MACA,IAAIc,KAAK,EAAE;QACT,IAAI,CAACxB,oBAAoB,CAACyB,GAAG,CAACf,UAAU,CAAC;QACzC,IAAI,CAACN,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;MACpC,CAAC,MAAM;QACL,MAAMW,QAAQ,GAAG,IAAI,CAAChB,qBAAqB,CAACc,GAAG,CAACF,UAAU,CAAE;QAC5DI,QAAQ,CAACD,SAAS,GAAG,IAAI;QAEzB,IAAI,CAACb,oBAAoB,CAACuB,MAAM,CAACb,UAAU,CAAC;QAC5C,IAAI,IAAI,CAACV,oBAAoB,CAACiB,IAAI,KAAK,CAAC,EAAE;UACxC,IAAI,CAACd,UAAU,IAAI,CAAC;QACtB;MACF;IACF;EACF,CAAC;EAEDuB,MAAM,CAACC,sBAAsB,GAAG7B,qBAAqB;AACvD,CAAC,CAAC", "ignoreList": []}