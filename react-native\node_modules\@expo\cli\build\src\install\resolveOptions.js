"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "resolveArgsAsync", {
    enumerable: true,
    get: function() {
        return resolveArgsAsync;
    }
});
const _errors = require("../utils/errors");
const _variadic = require("../utils/variadic");
function resolveOptions(options) {
    if (options.fix && options.check) {
        throw new _errors.CommandError('BAD_ARGS', 'Specify at most one of: --check, --fix');
    }
    if ([
        options.npm,
        options.pnpm,
        options.yarn,
        options.bun
    ].filter(Boolean).length > 1) {
        throw new _errors.CommandError('BAD_ARGS', 'Specify at most one of: --npm, --pnpm, --yarn, --bun');
    }
    return {
        ...options
    };
}
async function resolveArgsAsync(argv) {
    const { variadic, extras, flags } = (0, _variadic.parseVariadicArguments)(argv);
    (0, _variadic.assertUnexpectedVariadicFlags)([
        '--check',
        '--dev',
        '--fix',
        '--npm',
        '--pnpm',
        '--yarn',
        '--bun'
    ], {
        variadic,
        extras,
        flags
    }, 'npx expo install');
    return {
        // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']
        variadic,
        options: resolveOptions({
            fix: !!flags['--fix'],
            dev: !!flags['--dev'],
            check: !!flags['--check'],
            yarn: !!flags['--yarn'],
            npm: !!flags['--npm'],
            pnpm: !!flags['--pnpm'],
            bun: !!flags['--bun']
        }),
        extras
    };
}

//# sourceMappingURL=resolveOptions.js.map