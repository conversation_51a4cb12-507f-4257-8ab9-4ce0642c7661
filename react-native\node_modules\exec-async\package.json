{"name": "exec-async", "version": "2.2.0", "description": "Returns a promise with the results of a shell command", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/ccheever/exec-async.git"}, "keywords": ["shell", "exec", "promise"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ccheever/exec-async/issues"}, "homepage": "https://github.com/ccheever/exec-async#readme"}