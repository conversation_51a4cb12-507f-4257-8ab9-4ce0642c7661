{"version": 3, "names": ["_updatePropsJS", "snapshots", "WeakMap", "makeElementVisible", "element", "delay", "visibility", "setTimeout", "fixElementPosition", "parent", "snapshot", "parentRect", "getBoundingClientRect", "parentBorderTopValue", "parseInt", "getComputedStyle", "borderTopWidth", "parentBorderLeftValue", "borderLeftWidth", "dummyRect", "top", "style", "left", "setElementPosition", "transform", "position", "width", "height", "margin", "parentElement"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/componentStyle.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,QAAQ,8BAAqB;AAgBpD,OAAO,MAAMC,SAAS,GAAG,IAAIC,OAAO,CAAkC,CAAC;AAEvE,OAAO,SAASC,kBAAkBA,CAACC,OAAoB,EAAEC,KAAa,EAAE;EACtE,IAAIA,KAAK,KAAK,CAAC,EAAE;IACfL,cAAc,CAAC;MAAEM,UAAU,EAAE;IAAU,CAAC,EAAEF,OAAgC,CAAC;EAC7E,CAAC,MAAM;IACLG,UAAU,CAAC,MAAM;MACfP,cAAc,CACZ;QAAEM,UAAU,EAAE;MAAU,CAAC,EACzBF,OACF,CAAC;IACH,CAAC,EAAEC,KAAK,GAAG,IAAI,CAAC;EAClB;AACF;AAEA,SAASG,kBAAkBA,CACzBJ,OAAoB,EACpBK,MAAmB,EACnBC,QAA4B,EAC5B;EACA,MAAMC,UAAU,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;EAEjD,MAAMC,oBAAoB,GAAGC,QAAQ,CACnCC,gBAAgB,CAACN,MAAM,CAAC,CAACO,cAC3B,CAAC;EAED,MAAMC,qBAAqB,GAAGH,QAAQ,CACpCC,gBAAgB,CAACN,MAAM,CAAC,CAACS,eAC3B,CAAC;EAED,MAAMC,SAAS,GAAGf,OAAO,CAACQ,qBAAqB,CAAC,CAAC;EACjD;EACA;EACA;EACA,IAAIO,SAAS,CAACC,GAAG,KAAKV,QAAQ,CAACU,GAAG,EAAE;IAClChB,OAAO,CAACiB,KAAK,CAACD,GAAG,GAAG,GAClBV,QAAQ,CAACU,GAAG,GAAGT,UAAU,CAACS,GAAG,GAAGP,oBAAoB,IAClD;EACN;EAEA,IAAIM,SAAS,CAACG,IAAI,KAAKZ,QAAQ,CAACY,IAAI,EAAE;IACpClB,OAAO,CAACiB,KAAK,CAACC,IAAI,GAAG,GACnBZ,QAAQ,CAACY,IAAI,GAAGX,UAAU,CAACW,IAAI,GAAGL,qBAAqB,IACrD;EACN;AACF;AAEA,OAAO,SAASM,kBAAkBA,CAChCnB,OAAoB,EACpBM,QAA4B,EAC5B;EACAN,OAAO,CAACiB,KAAK,CAACG,SAAS,GAAG,EAAE;EAC5BpB,OAAO,CAACiB,KAAK,CAACI,QAAQ,GAAG,UAAU;EACnCrB,OAAO,CAACiB,KAAK,CAACD,GAAG,GAAG,GAAGV,QAAQ,CAACU,GAAG,IAAI;EACvChB,OAAO,CAACiB,KAAK,CAACC,IAAI,GAAG,GAAGZ,QAAQ,CAACY,IAAI,IAAI;EACzClB,OAAO,CAACiB,KAAK,CAACK,KAAK,GAAG,GAAGhB,QAAQ,CAACgB,KAAK,IAAI;EAC3CtB,OAAO,CAACiB,KAAK,CAACM,MAAM,GAAG,GAAGjB,QAAQ,CAACiB,MAAM,IAAI;EAC7CvB,OAAO,CAACiB,KAAK,CAACO,MAAM,GAAG,KAAK,CAAC,CAAC;;EAE9B,IAAIxB,OAAO,CAACyB,aAAa,EAAE;IACzBrB,kBAAkB,CAACJ,OAAO,EAAEA,OAAO,CAACyB,aAAa,EAAEnB,QAAQ,CAAC;EAC9D;AACF", "ignoreList": []}