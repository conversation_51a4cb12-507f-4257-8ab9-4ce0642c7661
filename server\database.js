require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Variables globales pour stocker les informations de la base
let databaseTables = [];
let databaseSchema = {};

// Fonction pour détecter toutes les tables de la base de données
async function detectDatabaseTables() {
  try {
    console.log('🔍 Détection des tables de la base de données...');

    const result = await pool.query(`
      SELECT
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_schema = 'public'
      ORDER BY table_name, ordinal_position
    `);

    // Organiser les colonnes par table
    const tableSchema = {};
    result.rows.forEach(row => {
      if (!tableSchema[row.table_name]) {
        tableSchema[row.table_name] = [];
      }
      tableSchema[row.table_name].push({
        column: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      });
    });

    // Mettre à jour les variables globales
    databaseTables = Object.keys(tableSchema);
    databaseSchema = tableSchema;

    console.log(`✅ ${databaseTables.length} tables détectées:`, databaseTables);
    return tableSchema;

  } catch (error) {
    console.error('❌ Erreur lors de la détection des tables:', error);
    throw error;
  }
}

// Fonction pour obtenir le nombre d'enregistrements dans chaque table
async function getTableCounts() {
  try {
    const counts = {};
    
    for (const tableName of databaseTables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) as count FROM "${tableName}"`);
        counts[tableName] = parseInt(result.rows[0].count);
      } catch (error) {
        console.warn(`⚠️ Impossible de compter les enregistrements de la table ${tableName}:`, error.message);
        counts[tableName] = 0;
      }
    }
    
    return counts;
  } catch (error) {
    console.error('❌ Erreur lors du comptage des tables:', error);
    throw error;
  }
}

// Fonction d'initialisation de la base de données
async function initializeDatabase() {
  try {
    console.log('🚀 Initialisation de la connexion à la base de données...');
    
    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie');
    client.release();
    
    // Détection automatique des tables
    await detectDatabaseTables();
    
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation de la base de données:', error);
    throw error;
  }
}

// Fonction pour récupérer tous les clients
async function getAllClients() {
  try {
    console.log('📥 Récupération de tous les clients depuis la table Client');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    console.log(`✅ ${result.rows.length} clients récupérés depuis la table Client`);

    return {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    };

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    throw error;
  }
}

// Fonction pour récupérer les contrats d'un client spécifique
async function getClientContracts(clientId) {
  try {
    console.log(`🎯 Récupération des contrats pour le client ID: ${clientId}`);

    // Vérifier d'abord si le client existe
    const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [clientId]);
    if (clientCheck.rows.length === 0) {
      console.log(`❌ Client ID ${clientId} n'existe pas dans la base`);
      return {
        success: false,
        message: `Client ID ${clientId} non trouvé`,
        client_id: parseInt(clientId)
      };
    }

    const clientInfo = clientCheck.rows[0];
    console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

    // Requête pour récupérer les contrats depuis la table Contract
    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL pour les contrats...');
    const result = await pool.query(query, [clientId]);

    console.log(`📊 RÉSULTAT: ${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`);

    if (result.rows.length > 0) {
      console.log('📋 CONTRATS TROUVÉS:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
        console.log(`      Client: ${contract.nom} ${contract.prenom}`);
        console.log(`      Date contrat: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}`);
      });
    } else {
      console.log('⚠️ AUCUN CONTRAT TROUVÉ pour ce client');
    }

    return {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`,
      client_id: parseInt(clientId),
      client_name: `${clientInfo.nom} ${clientInfo.prenom}`
    };

  } catch (error) {
    console.error('❌ ERREUR lors de la récupération des contrats:', error.message);
    throw error;
  }
}

// Fonction pour récupérer la dernière consommation d'un contrat
async function getLastConsommation(contractId) {
  try {
    console.log(`📥 Récupération de la dernière consommation pour le contrat ${contractId}`);

    const query = `
      SELECT
        consommationactuelle,
        periode,
        jours
      FROM consommation
      WHERE idcont = $1
      ORDER BY periode DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [contractId]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      return {
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      };
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${contractId}`);
      return {
        success: false,
        message: 'Aucune consommation précédente trouvée'
      };
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    throw error;
  }
}

module.exports = {
  pool,
  detectDatabaseTables,
  getTableCounts,
  initializeDatabase,
  getDatabaseTables: () => databaseTables,
  getDatabaseSchema: () => databaseSchema,
  getAllClients,
  getClientContracts,
  getLastConsommation
};
