{"version": 3, "sources": ["../../../../../src/start/server/webpack/tls.ts"], "sourcesContent": ["import { certificateFor } from '@expo/devcert';\nimport chalk from 'chalk';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport * as Log from '../../../log';\nimport { ensureDirectoryAsync } from '../../../utils/dir';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\n\n// TODO: Move to doctor as a prereq.\n\n/** Ensure TLS is setup and environment variables are set. */\nexport async function ensureEnvironmentSupportsTLSAsync(projectRoot: string) {\n  if (!process.env.SSL_CRT_FILE || !process.env.SSL_KEY_FILE) {\n    const tls = await getTLSCertAsync(projectRoot);\n    if (tls) {\n      process.env.SSL_CRT_FILE = tls.certPath;\n      process.env.SSL_KEY_FILE = tls.keyPath;\n    }\n  }\n}\n\n/** Create TLS and write to files in the temporary directory. Exposed for testing. */\nexport async function getTLSCertAsync(\n  projectRoot: string\n): Promise<{ keyPath: string; certPath: string } | false> {\n  Log.log(\n    chalk`Creating TLS certificate for localhost. {dim This functionality may not work on all computers.}`\n  );\n\n  const name = 'localhost';\n  const result = await certificateFor(name);\n  if (result) {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n\n    const { key, cert } = result;\n    const folder = path.join(dotExpoDir, 'tls');\n    const keyPath = path.join(folder, `key-${name}.pem`);\n    const certPath = path.join(folder, `cert-${name}.pem`);\n\n    await ensureDirectoryAsync(folder);\n    await Promise.allSettled([fs.writeFile(keyPath, key), fs.writeFile(certPath, cert)]);\n\n    return {\n      keyPath,\n      certPath,\n    };\n  }\n  return result;\n}\n"], "names": ["ensureEnvironmentSupportsTLSAsync", "getTLSCertAsync", "projectRoot", "process", "env", "SSL_CRT_FILE", "SSL_KEY_FILE", "tls", "certPath", "keyP<PERSON>", "Log", "log", "chalk", "name", "result", "certificateFor", "dotExpoDir", "ensureDotExpoProjectDirectoryInitialized", "key", "cert", "folder", "path", "join", "ensureDirectoryAsync", "Promise", "allSettled", "fs", "writeFile"], "mappings": ";;;;;;;;;;;IAYsBA,iCAAiC;eAAjCA;;IAWAC,eAAe;eAAfA;;;;yBAvBS;;;;;;;gEACb;;;;;;;gEACH;;;;;;;gEACE;;;;;;6DAEI;qBACgB;yBACoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlD,eAAeD,kCAAkCE,WAAmB;IACzE,IAAI,CAACC,QAAQC,GAAG,CAACC,YAAY,IAAI,CAACF,QAAQC,GAAG,CAACE,YAAY,EAAE;QAC1D,MAAMC,MAAM,MAAMN,gBAAgBC;QAClC,IAAIK,KAAK;YACPJ,QAAQC,GAAG,CAACC,YAAY,GAAGE,IAAIC,QAAQ;YACvCL,QAAQC,GAAG,CAACE,YAAY,GAAGC,IAAIE,OAAO;QACxC;IACF;AACF;AAGO,eAAeR,gBACpBC,WAAmB;IAEnBQ,KAAIC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,+FAA+F,CAAC;IAGxG,MAAMC,OAAO;IACb,MAAMC,SAAS,MAAMC,IAAAA,yBAAc,EAACF;IACpC,IAAIC,QAAQ;QACV,MAAME,aAAaC,IAAAA,iDAAwC,EAACf;QAE5D,MAAM,EAAEgB,GAAG,EAAEC,IAAI,EAAE,GAAGL;QACtB,MAAMM,SAASC,eAAI,CAACC,IAAI,CAACN,YAAY;QACrC,MAAMP,UAAUY,eAAI,CAACC,IAAI,CAACF,QAAQ,CAAC,IAAI,EAAEP,KAAK,IAAI,CAAC;QACnD,MAAML,WAAWa,eAAI,CAACC,IAAI,CAACF,QAAQ,CAAC,KAAK,EAAEP,KAAK,IAAI,CAAC;QAErD,MAAMU,IAAAA,yBAAoB,EAACH;QAC3B,MAAMI,QAAQC,UAAU,CAAC;YAACC,mBAAE,CAACC,SAAS,CAAClB,SAASS;YAAMQ,mBAAE,CAACC,SAAS,CAACnB,UAAUW;SAAM;QAEnF,OAAO;YACLV;YACAD;QACF;IACF;IACA,OAAOM;AACT"}