{"version": 3, "sources": ["../../../../src/utils/tsconfig/resolveWithTsConfigPaths.ts"], "sourcesContent": ["import { Resolution } from 'metro-resolver';\nimport path from 'path';\n\nimport { matchTsConfigPathAlias } from './matchTsConfigPathAlias';\n\ntype Paths = { [match: string]: string[] };\n\nconst debug = require('debug')('expo:metro:tsconfig-paths') as typeof console.log;\n\nconst isAbsolute = process.platform === 'win32' ? path.win32.isAbsolute : path.posix.isAbsolute;\n\nexport function resolveWithTsConfigPaths(\n  config: { paths: Paths; baseUrl: string; hasBaseUrl: boolean },\n  request: {\n    /** Import request */\n    moduleName: string;\n    /** Originating file path */\n    originModulePath: string;\n  },\n  resolve: (moduleName: string) => Resolution | null\n): Resolution | null {\n  const aliases = Object.keys(config.paths);\n\n  if (\n    // If no aliases are added bail out\n    (!aliases.length && !config.hasBaseUrl) ||\n    // Library authors cannot utilize this feature in userspace.\n    /node_modules/.test(request.originModulePath) ||\n    // Absolute paths are not supported\n    isAbsolute(request.moduleName) ||\n    // Relative paths are not supported\n    /^\\.\\.?($|[\\\\/])/.test(request.moduleName)\n  ) {\n    return null;\n  }\n\n  const matched = matchTsConfigPathAlias(aliases, request.moduleName);\n  if (matched) {\n    for (const alias of config.paths[matched.text]) {\n      const nextModuleName = matched.star ? alias.replace('*', matched.star) : alias;\n\n      if (/\\.d\\.ts$/.test(nextModuleName)) continue;\n\n      const possibleResult = path.join(config.baseUrl, nextModuleName);\n\n      const result = resolve(possibleResult);\n      if (result) {\n        debug(`${request.moduleName} -> ${possibleResult}`);\n        return result;\n      }\n    }\n  } else {\n    // Only resolve against baseUrl if no `paths` groups were matched.\n    // Base URL is resolved after paths, and before node_modules.\n    if (config.hasBaseUrl) {\n      const possibleResult = path.join(config.baseUrl, request.moduleName);\n      const result = resolve(possibleResult);\n      if (result) {\n        debug(`baseUrl: ${request.moduleName} -> ${possibleResult}`);\n        return result;\n      }\n    }\n  }\n\n  return null;\n}\n"], "names": ["resolveWithTsConfigPaths", "debug", "require", "isAbsolute", "process", "platform", "path", "win32", "posix", "config", "request", "resolve", "aliases", "Object", "keys", "paths", "length", "hasBaseUrl", "test", "originModulePath", "moduleName", "matched", "matchTsConfigPathAlias", "alias", "text", "nextModuleName", "star", "replace", "possibleResult", "join", "baseUrl", "result"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;;gEAVC;;;;;;wCAEsB;;;;;;AAIvC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,aAAaC,QAAQC,QAAQ,KAAK,UAAUC,eAAI,CAACC,KAAK,CAACJ,UAAU,GAAGG,eAAI,CAACE,KAAK,CAACL,UAAU;AAExF,SAASH,yBACdS,MAA8D,EAC9DC,OAKC,EACDC,OAAkD;IAElD,MAAMC,UAAUC,OAAOC,IAAI,CAACL,OAAOM,KAAK;IAExC,IAEE,AADA,mCAAmC;IAClC,CAACH,QAAQI,MAAM,IAAI,CAACP,OAAOQ,UAAU,IACtC,4DAA4D;IAC5D,eAAeC,IAAI,CAACR,QAAQS,gBAAgB,KAC5C,mCAAmC;IACnChB,WAAWO,QAAQU,UAAU,KAC7B,mCAAmC;IACnC,kBAAkBF,IAAI,CAACR,QAAQU,UAAU,GACzC;QACA,OAAO;IACT;IAEA,MAAMC,UAAUC,IAAAA,8CAAsB,EAACV,SAASF,QAAQU,UAAU;IAClE,IAAIC,SAAS;QACX,KAAK,MAAME,SAASd,OAAOM,KAAK,CAACM,QAAQG,IAAI,CAAC,CAAE;YAC9C,MAAMC,iBAAiBJ,QAAQK,IAAI,GAAGH,MAAMI,OAAO,CAAC,KAAKN,QAAQK,IAAI,IAAIH;YAEzE,IAAI,WAAWL,IAAI,CAACO,iBAAiB;YAErC,MAAMG,iBAAiBtB,eAAI,CAACuB,IAAI,CAACpB,OAAOqB,OAAO,EAAEL;YAEjD,MAAMM,SAASpB,QAAQiB;YACvB,IAAIG,QAAQ;gBACV9B,MAAM,GAAGS,QAAQU,UAAU,CAAC,IAAI,EAAEQ,gBAAgB;gBAClD,OAAOG;YACT;QACF;IACF,OAAO;QACL,kEAAkE;QAClE,6DAA6D;QAC7D,IAAItB,OAAOQ,UAAU,EAAE;YACrB,MAAMW,iBAAiBtB,eAAI,CAACuB,IAAI,CAACpB,OAAOqB,OAAO,EAAEpB,QAAQU,UAAU;YACnE,MAAMW,SAASpB,QAAQiB;YACvB,IAAIG,QAAQ;gBACV9B,MAAM,CAAC,SAAS,EAAES,QAAQU,UAAU,CAAC,IAAI,EAAEQ,gBAAgB;gBAC3D,OAAOG;YACT;QACF;IACF;IAEA,OAAO;AACT"}