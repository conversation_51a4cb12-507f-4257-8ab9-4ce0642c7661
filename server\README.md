# Architecture Modulaire du Serveur TechnicianDashboard

## Vue d'ensemble

Le serveur TechnicianDashboard a été restructuré en une architecture modulaire pour améliorer la maintenabilité, la lisibilité et la réutilisabilité du code. Chaque fonctionnalité est maintenant organisée dans des fichiers spécialisés.

## Structure des fichiers

```
server/
├── TechnicianDashboard.js    # Fichier principal (routes génériques + initialisation)
├── database.js               # Configuration et gestion de la base de données
├── clients.js                # Gestion des clients
├── consommation.js           # Gestion des consommations d'eau
├── factures.js               # Gestion des factures
├── codeQR.js                 # Scanner QR et identification des clients
├── auth.js                   # Authentification et gestion des utilisateurs
└── README.md                 # Cette documentation
```

## Description des modules

### 1. `TechnicianDashboard.js` (Fichier principal)
- **Rôle** : Point d'entrée principal du serveur
- **Contenu** :
  - Configuration Express et middleware
  - Import et utilisation des modules spécialisés
  - Routes génériques (schéma DB, consultation tables, requêtes SQL)
  - Routes spécialisées pour le dashboard technicien
  - Initialisation et démarrage du serveur
- **Port** : 3002

### 2. `database.js`
- **Rôle** : Configuration et gestion de la base de données PostgreSQL
- **Fonctionnalités** :
  - Configuration de la connexion PostgreSQL (base 'Facutration')
  - Détection automatique des tables et schémas
  - Comptage des enregistrements par table
  - Fonction d'initialisation de la base de données
- **Exports** : `pool`, `detectDatabaseTables`, `getTableCounts`, `initializeDatabase`

### 3. `clients.js`
- **Rôle** : Gestion complète des clients
- **Routes** :
  - `GET /api/clients` - Liste tous les clients
  - `GET /api/clients/:id` - Récupère un client spécifique
  - `POST /api/clients` - Ajoute un nouveau client
  - `PUT /api/clients/:id` - Met à jour un client
  - `DELETE /api/clients/:id` - Supprime un client
  - `GET /api/clients/search/:term` - Recherche de clients
- **Fonctionnalités** :
  - Jointures avec la table secteur
  - Validation des données
  - Gestion des erreurs

### 4. `consommation.js`
- **Rôle** : Gestion des relevés de consommation d'eau
- **Routes** :
  - `POST /api/consommation` - Ajoute une nouvelle consommation
  - `GET /api/consommation` - Liste toutes les consommations
  - `GET /api/contracts` - Liste des contrats avec infos client
  - `GET /api/contracts/:id/last-consommation` - Dernière consommation d'un contrat
  - `GET /api/last-consommation-global` - Dernière consommation globale
  - `GET /api/consommation/technicien/:id` - Consommations d'un technicien
- **Fonctionnalités** :
  - Validation des consommations (actuelle > précédente)
  - Jointures avec tables client, contract, utilisateur
  - Auto-calcul des périodes et jours

### 5. `factures.js`
- **Rôle** : Gestion des factures et facturation
- **Routes** :
  - `GET /api/factures` - Liste toutes les factures
  - `GET /api/factures/:id` - Récupère une facture spécifique
  - `POST /api/factures` - Crée une nouvelle facture
  - `PUT /api/factures/:id/status` - Met à jour le statut d'une facture
  - `GET /api/factures/status/:status` - Factures par statut (payée/nonpayée)
  - `GET /api/factures/stats/summary` - Statistiques des factures
- **Fonctionnalités** :
  - Gestion des statuts (payée/nonpayée)
  - Calculs de montants et statistiques
  - Jointures avec consommation et client

### 6. `codeQR.js`
- **Rôle** : Scanner QR et identification des clients via QR codes
- **Routes** :
  - `GET /api/scan/:qrCode` - Scan d'un QR code
  - `GET /api/generate-qr/:clientId` - Génération QR pour un client
  - `POST /api/validate-qr` - Validation d'un QR code
  - `GET /api/qr-history/:techId` - Historique des scans d'un technicien
- **Fonctionnalités** :
  - Recherche multi-critères (ID client, référence contrat, téléphone, email)
  - Génération de QR codes uniques
  - Historique des scans

### 7. `auth.js`
- **Rôle** : Authentification et gestion des utilisateurs
- **Routes** :
  - `POST /login` - Connexion utilisateur
  - `GET /api/user/:id` - Informations d'un utilisateur
  - `PUT /api/user/:id` - Mise à jour du profil
  - `PUT /api/user/:id/password` - Changement de mot de passe
  - `GET /api/users` - Liste des utilisateurs (admin)
  - `POST /api/users` - Création d'utilisateur (admin)
- **Fonctionnalités** :
  - Authentification par email/mot de passe
  - Gestion des rôles (Admin/Tech)
  - Redirection selon le rôle
  - Gestion des profils utilisateur

## Configuration de la base de données

La configuration se fait via les variables d'environnement :
```env
DB_USER=postgres
DB_HOST=localhost
DB_NAME=Facutration
DB_PASSWORD=123456
DB_PORT=5432
```

## Utilisation

1. **Démarrage du serveur** :
   ```bash
   node server/TechnicianDashboard.js
   ```

2. **Test de fonctionnement** :
   ```bash
   curl http://localhost:3002/
   ```

3. **Accès aux routes spécialisées** :
   - Clients : `http://localhost:3002/api/clients`
   - Consommations : `http://localhost:3002/api/consommation`
   - Factures : `http://localhost:3002/api/factures`
   - Scanner QR : `http://localhost:3002/api/scan/[qrcode]`
   - Authentification : `http://localhost:3002/login`

## Avantages de cette architecture

1. **Séparation des responsabilités** : Chaque module a une fonction spécifique
2. **Maintenabilité** : Plus facile de modifier ou déboguer une fonctionnalité
3. **Réutilisabilité** : Les modules peuvent être réutilisés dans d'autres projets
4. **Lisibilité** : Code plus organisé et plus facile à comprendre
5. **Évolutivité** : Facile d'ajouter de nouveaux modules
6. **Tests** : Chaque module peut être testé indépendamment

## Prochaines étapes

- Ajouter des tests unitaires pour chaque module
- Implémenter la validation des données avec des schémas
- Ajouter la documentation API avec Swagger
- Implémenter la gestion des logs
- Ajouter la gestion des erreurs centralisée
