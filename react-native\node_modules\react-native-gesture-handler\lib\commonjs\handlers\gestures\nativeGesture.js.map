{"version": 3, "sources": ["nativeGesture.ts"], "names": ["NativeGesture", "BaseGesture", "constructor", "handler<PERSON>ame", "shouldActivateOnStart", "value", "config", "disallowInterruption"], "mappings": ";;;;;;;AAAA;;;;AAIO,MAAMA,aAAN,SAA4BC,oBAA5B,CAAyE;AAG9EC,EAAAA,WAAW,GAAG;AACZ;;AADY,oCAF+C,EAE/C;;AAGZ,SAAKC,WAAL,GAAmB,0BAAnB;AACD;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,qBAAqB,CAACC,KAAD,EAAiB;AACpC,SAAKC,MAAL,CAAYF,qBAAZ,GAAoCC,KAApC;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;;;AACEE,EAAAA,oBAAoB,CAACF,KAAD,EAAiB;AACnC,SAAKC,MAAL,CAAYC,oBAAZ,GAAmCF,KAAnC;AACA,WAAO,IAAP;AACD;;AAzB6E", "sourcesContent": ["import { BaseGestureConfig, BaseGesture } from './gesture';\nimport { NativeViewGestureConfig } from '../NativeViewGestureHandler';\nimport type { NativeViewGestureHandlerPayload } from '../GestureHandlerEventPayload';\n\nexport class NativeGesture extends BaseGesture<NativeViewGestureHandlerPayload> {\n  public config: BaseGestureConfig & NativeViewGestureConfig = {};\n\n  constructor() {\n    super();\n\n    this.handlerName = 'NativeViewGestureHandler';\n  }\n\n  /**\n   * When true, underlying handler will activate unconditionally when in `BEGAN` or `UNDETERMINED` state.\n   * @param value\n   */\n  shouldActivateOnStart(value: boolean) {\n    this.config.shouldActivateOnStart = value;\n    return this;\n  }\n\n  /**\n   * When true, cancels all other gesture handlers when this `NativeViewGestureHandler` receives an `ACTIVE` state event.\n   * @param value\n   */\n  disallowInterruption(value: boolean) {\n    this.config.disallowInterruption = value;\n    return this;\n  }\n}\n\nexport type NativeGestureType = InstanceType<typeof NativeGesture>;\n"]}