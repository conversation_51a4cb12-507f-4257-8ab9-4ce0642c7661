{"version": 3, "sources": ["EnableNewWebImplementation.ts"], "names": ["useNewWebImplementation", "getWasCalled", "enableExperimentalWebImplementation", "_shouldEnable", "enableLegacyWebImplementation", "shouldUseLegacyImplementation", "Platform", "OS", "console", "error", "isNewWebImplementationEnabled"], "mappings": ";;;;;;;;;AAAA;;AAEA,IAAIA,uBAAuB,GAAG,IAA9B;AACA,IAAIC,YAAY,GAAG,KAAnB;;AAEO,SAASC,mCAAT,CACLC,aAAa,GAAG,IADX,EAEC,CACN;AACD;;AAEM,SAASC,6BAAT,CACLC,6BAA6B,GAAG,IAD3B,EAEC;AACN,MACEC,sBAASC,EAAT,KAAgB,KAAhB,IACAP,uBAAuB,KAAK,CAACK,6BAF/B,EAGE;AACA;AACD;;AAED,MAAIJ,YAAJ,EAAkB;AAChBO,IAAAA,OAAO,CAACC,KAAR,CACE,mLADF;AAGA;AACD;;AAEDT,EAAAA,uBAAuB,GAAG,CAACK,6BAA3B;AACD;;AAEM,SAASK,6BAAT,GAAkD;AACvDT,EAAAA,YAAY,GAAG,IAAf;AACA,SAAOD,uBAAP;AACD", "sourcesContent": ["import { Platform } from 'react-native';\n\nlet useNewWebImplementation = true;\nlet getWasCalled = false;\n\nexport function enableExperimentalWebImplementation(\n  _shouldEnable = true\n): void {\n  // NO-OP since the new implementation is now the default\n}\n\nexport function enableLegacyWebImplementation(\n  shouldUseLegacyImplementation = true\n): void {\n  if (\n    Platform.OS !== 'web' ||\n    useNewWebImplementation === !shouldUseLegacyImplementation\n  ) {\n    return;\n  }\n\n  if (getWasCalled) {\n    console.error(\n      'Some parts of this application have already started using the new gesture handler implementation. No changes will be applied. You can try enabling legacy implementation earlier.'\n    );\n    return;\n  }\n\n  useNewWebImplementation = !shouldUseLegacyImplementation;\n}\n\nexport function isNewWebImplementationEnabled(): boolean {\n  getWasCalled = true;\n  return useNewWebImplementation;\n}\n"]}