// Test simple de l'API sur le port 4000
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 4000,
  path: '/api/clients',
  method: 'GET'
};

console.log('🧪 Test de l\'API sur http://localhost:4000/api/clients...\n');

const req = http.request(options, (res) => {
  console.log(`📡 Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const jsonData = JSON.parse(data);
      console.log('\n📊 Réponse API:');
      console.log('✅ Success:', jsonData.success);
      console.log('📊 Count:', jsonData.count);
      console.log('📋 Data type:', typeof jsonData.data);
      console.log('📋 Data is array:', Array.isArray(jsonData.data));
      
      if (jsonData.data && jsonData.data.length > 0) {
        console.log('\n👤 Premier client:');
        console.log('ID:', jsonData.data[0].idclient);
        console.log('Nom:', jsonData.data[0].nom);
        console.log('Prénom:', jsonData.data[0].prenom);
        console.log('Secteur:', jsonData.data[0].secteur_nom);
        
        console.log('\n📋 Liste des 3 premiers clients:');
        jsonData.data.slice(0, 3).forEach((client, index) => {
          console.log(`${index + 1}. ${client.nom} ${client.prenom} (ID: ${client.idclient})`);
        });
        
        console.log('\n🎉 L\'API fonctionne parfaitement !');
      } else {
        console.log('\n❌ Aucune donnée client');
        console.log('Structure complète:', JSON.stringify(jsonData, null, 2));
      }
    } catch (error) {
      console.error('❌ Erreur parsing JSON:', error.message);
      console.log('Raw data:', data);
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Erreur requête: ${e.message}`);
  console.log('💡 Vérifiez que le serveur est démarré sur le port 4000');
});

req.end();
