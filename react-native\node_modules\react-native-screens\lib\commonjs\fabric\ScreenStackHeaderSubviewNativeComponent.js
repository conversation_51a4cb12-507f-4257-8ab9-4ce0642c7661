"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var _default = exports.default = (0, _codegenNativeComponent.default)('RNSScreenStackHeaderSubview', {
  interfaceOnly: true
});
//# sourceMappingURL=ScreenStackHeaderSubviewNativeComponent.js.map