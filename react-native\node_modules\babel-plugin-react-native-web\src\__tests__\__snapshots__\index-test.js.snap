// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Rewrite react-native to react-native-web export from "react-native": export from "react-native" 1`] = `

export { View } from 'react-native';
export { StyleSheet, Text, unstable_createElement } from 'react-native';

      ↓ ↓ ↓ ↓ ↓ ↓

export { default as View } from 'react-native-web/dist/exports/View';
export { default as StyleSheet } from 'react-native-web/dist/exports/StyleSheet';
export { default as Text } from 'react-native-web/dist/exports/Text';
export { default as unstable_createElement } from 'react-native-web/dist/exports/createElement';


`;

exports[`Rewrite react-native to react-native-web export from "react-native-web": export from "react-native-web" 1`] = `

export { View } from 'react-native-web';
export { StyleSheet, Text, unstable_createElement } from 'react-native-web';

      ↓ ↓ ↓ ↓ ↓ ↓

export { default as View } from 'react-native-web/dist/exports/View';
export { default as StyleSheet } from 'react-native-web/dist/exports/StyleSheet';
export { default as Text } from 'react-native-web/dist/exports/Text';
export { default as unstable_createElement } from 'react-native-web/dist/exports/createElement';


`;

exports[`Rewrite react-native to react-native-web import from "react-native": import from "react-native" 1`] = `

import ReactNative from 'react-native';
import { View } from 'react-native';
import { Invalid, View as MyView } from 'react-native';
import { useLocaleContext } from 'react-native';
import * as ReactNativeModules from 'react-native';

      ↓ ↓ ↓ ↓ ↓ ↓

import ReactNative from 'react-native-web/dist/index';
import View from 'react-native-web/dist/exports/View';
import { Invalid } from 'react-native-web/dist/index';
import MyView from 'react-native-web/dist/exports/View';
import useLocaleContext from 'react-native-web/dist/exports/useLocaleContext';
import * as ReactNativeModules from 'react-native-web/dist/index';


`;

exports[`Rewrite react-native to react-native-web import from "react-native": import from "react-native" 2`] = `

import ReactNative from 'react-native';
import { View } from 'react-native';
import { Invalid, View as MyView } from 'react-native';
import * as ReactNativeModules from 'react-native';

      ↓ ↓ ↓ ↓ ↓ ↓

import ReactNative from 'react-native-web/dist/cjs/index';
import View from 'react-native-web/dist/cjs/exports/View';
import { Invalid } from 'react-native-web/dist/cjs/index';
import MyView from 'react-native-web/dist/cjs/exports/View';
import * as ReactNativeModules from 'react-native-web/dist/cjs/index';


`;

exports[`Rewrite react-native to react-native-web import from "react-native-web": import from "react-native-web" 1`] = `

import { unstable_createElement } from 'react-native-web';
import { StyleSheet, View, Pressable, processColor } from 'react-native-web';
import * as ReactNativeModules from 'react-native-web';

      ↓ ↓ ↓ ↓ ↓ ↓

import unstable_createElement from 'react-native-web/dist/exports/createElement';
import StyleSheet from 'react-native-web/dist/exports/StyleSheet';
import View from 'react-native-web/dist/exports/View';
import Pressable from 'react-native-web/dist/exports/Pressable';
import processColor from 'react-native-web/dist/exports/processColor';
import * as ReactNativeModules from 'react-native-web/dist/index';


`;

exports[`Rewrite react-native to react-native-web require "react-native": require "react-native" 1`] = `

const ReactNative = require('react-native');
const { View } = require('react-native');
const { StyleSheet, Pressable } = require('react-native');

      ↓ ↓ ↓ ↓ ↓ ↓

const ReactNative = require('react-native-web/dist/index');
const View = require('react-native-web/dist/exports/View').default;
const StyleSheet = require('react-native-web/dist/exports/StyleSheet').default;
const Pressable = require('react-native-web/dist/exports/Pressable').default;


`;

exports[`Rewrite react-native to react-native-web require "react-native": require "react-native" 2`] = `

const ReactNative = require('react-native');
const { View } = require('react-native');
const { StyleSheet, Pressable } = require('react-native');

      ↓ ↓ ↓ ↓ ↓ ↓

const ReactNative = require('react-native-web/dist/cjs/index');
const View = require('react-native-web/dist/cjs/exports/View').default;
const StyleSheet =
  require('react-native-web/dist/cjs/exports/StyleSheet').default;
const Pressable =
  require('react-native-web/dist/cjs/exports/Pressable').default;


`;

exports[`Rewrite react-native to react-native-web require "react-native-web": require "react-native-web" 1`] = `

const ReactNative = require('react-native-web');
const { unstable_createElement } = require('react-native-web');
const { StyleSheet, View, Pressable, processColor } = require('react-native-web');

      ↓ ↓ ↓ ↓ ↓ ↓

const ReactNative = require('react-native-web/dist/index');
const unstable_createElement =
  require('react-native-web/dist/exports/createElement').default;
const StyleSheet = require('react-native-web/dist/exports/StyleSheet').default;
const View = require('react-native-web/dist/exports/View').default;
const Pressable = require('react-native-web/dist/exports/Pressable').default;
const processColor =
  require('react-native-web/dist/exports/processColor').default;


`;
