/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */

'use strict';

const _require = require('./CppHelpers'),
  IncludeTemplate = _require.IncludeTemplate;

// File path -> contents

const FileTemplate = ({libraryName, componentRegistrations, headerPrefix}) => `
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * ${'@'}generated by codegen project: GenerateComponentDescriptorCpp.js
 */

${IncludeTemplate({
  headerPrefix,
  file: 'ComponentDescriptors.h',
})}
#include <react/renderer/core/ConcreteComponentDescriptor.h>
#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>

namespace facebook::react {

void ${libraryName}_registerComponentDescriptorsFromCodegen(
  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry) {
${componentRegistrations}
}

} // namespace facebook::react
`;
const ComponentRegistrationTemplate = ({className}) =>
  `
registry->add(concreteComponentDescriptorProvider<${className}ComponentDescriptor>());
`.trim();
module.exports = {
  generate(
    libraryName,
    schema,
    packageName,
    assumeNonnull = false,
    headerPrefix,
  ) {
    const fileName = 'ComponentDescriptors.cpp';
    const componentRegistrations = Object.keys(schema.modules)
      .map(moduleName => {
        const module = schema.modules[moduleName];
        if (module.type !== 'Component') {
          return;
        }
        const components = module.components;
        // No components in this module
        if (components == null) {
          return null;
        }
        return Object.keys(components)
          .map(componentName => {
            if (components[componentName].interfaceOnly === true) {
              return;
            }
            return ComponentRegistrationTemplate({
              className: componentName,
            });
          })
          .join('\n');
      })
      .filter(Boolean)
      .join('\n');
    const replacedTemplate = FileTemplate({
      libraryName,
      componentRegistrations,
      headerPrefix:
        headerPrefix !== null && headerPrefix !== void 0 ? headerPrefix : '',
    });
    return new Map([[fileName, replacedTemplate]]);
  },
};
