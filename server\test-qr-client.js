// Script de test pour la création de client avec QR Code
const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

async function testClientWithQR() {
  console.log('🧪 Test de création de client avec QR Code automatique\n');

  // Données de test pour un nouveau client
  const clientData = {
    nom: 'Dupont',
    prenom: '<PERSON>',
    adresse: '123 Rue de la Paix',
    ville: 'Tunis',
    tel: '71234567',
    email: '<EMAIL>',
    ids: 1, // ID du secteur (ajustez selon votre base)
    marqueCompteur: 'Sensus',
    numSerieCompteur: 'SN123456789',
    posX: '36.8065',
    posY: '10.1815'
  };

  try {
    console.log('📤 Envoi des données client:', clientData);
    
    const response = await axios.post(`${BASE_URL}/api/clients`, clientData);
    
    if (response.data.success) {
      console.log('\n✅ Client créé avec succès !');
      console.log('👤 Client:', response.data.data.client);
      console.log('📄 Contrat:', response.data.data.contract);
      console.log('🔗 QR Code généré:', response.data.data.qrCode);
      
      // Test du scan du QR Code généré
      console.log('\n🔍 Test du scan du QR Code...');
      const qrCode = response.data.data.qrCode;
      
      const scanResponse = await axios.get(`${BASE_URL}/api/scan/${qrCode}`);
      
      if (scanResponse.data.success) {
        console.log('✅ QR Code scanné avec succès !');
        console.log('📊 Données récupérées:', scanResponse.data.data);
      } else {
        console.log('❌ Erreur lors du scan:', scanResponse.data.message);
      }
      
    } else {
      console.log('❌ Erreur lors de la création:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.response?.data || error.message);
  }
}

// Test de récupération du QR Code d'un client existant
async function testGetClientQR(clientId) {
  try {
    console.log(`\n🔍 Test de récupération QR Code pour client ${clientId}...`);
    
    const response = await axios.get(`${BASE_URL}/api/clients/${clientId}/qrcode`);
    
    if (response.data.success) {
      console.log('✅ QR Code récupéré:', response.data.data);
    } else {
      console.log('❌ Erreur:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.response?.data || error.message);
  }
}

// Test de génération de QR Code pour un client existant
async function testGenerateQRForExistingClient(clientId) {
  try {
    console.log(`\n🔧 Test de génération QR Code pour client existant ${clientId}...`);
    
    const qrData = {
      marqueCompteur: 'Itron',
      numSerieCompteur: 'ITR987654321',
      posX: '36.8000',
      posY: '10.1800'
    };
    
    const response = await axios.post(`${BASE_URL}/api/clients/${clientId}/generate-qrcode`, qrData);
    
    if (response.data.success) {
      console.log('✅ QR Code généré:', response.data.data);
    } else {
      console.log('❌ Erreur:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.response?.data || error.message);
  }
}

// Exécuter les tests
async function runAllTests() {
  console.log('🚀 Démarrage des tests QR Code\n');
  console.log('=' .repeat(60));
  
  // Test 1: Créer un nouveau client avec QR Code
  await testClientWithQR();
  
  // Test 2: Récupérer QR Code d'un client existant (ID 1)
  await testGetClientQR(1);
  
  // Test 3: Générer QR Code pour un client existant sans contrat
  // await testGenerateQRForExistingClient(2);
  
  console.log('\n' + '='.repeat(60));
  console.log('🎉 Tests terminés !');
}

if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testClientWithQR, testGetClientQR, testGenerateQRForExistingClient };
