{"version": 3, "file": "environmentVariableSerializerPlugin.js", "sourceRoot": "", "sources": ["../../src/serializer/environmentVariableSerializerPlugin.ts"], "names": [], "mappings": ";;;;;AAcA,0DAGC;AA8BD,sEAmBC;AAED,kFA6CC;AAED,gDAoBC;AAhID,4EAAoD;AACpD,0EAAkD;AAIlD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAuB,CAAC;AAE7F,SAAgB,uBAAuB,CAAC,GAAW;IACjD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC9D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACjC,CAAC;AAED,SAAS,uBAAuB,CAAC,WAA8B,OAAO,CAAC,GAAG;IACxE,qFAAqF;IACrF,MAAM,GAAG,GAAG,EAAE,CAAC;IACf,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC3B,IAAI,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YACnC,0EAA0E;YAC1E,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAoB,EAAE,OAA0B;IAC3E,iDAAiD;IACjD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;QACnD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,cAAc,CAAC;QAClD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,8DAA8D;IAC9D,MAAM,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,WAAW,CAAC;IACtE,OAAO,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,cAAc,CAAC;AAClD,CAAC;AAED,0HAA0H;AAC1H,SAAgB,6BAA6B,CAC3C,UAAkB,EAClB,UAA0C,EAC1C,KAAoB,EACpB,OAA0B;IAE1B,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;QAC3E,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACxE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;iBACtD,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;iBAC3C,OAAO,CACN,yFAAyF,EACzF,EAAE,CACH,CAAC;QACN,CAAC;IACH,CAAC;IACD,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,SAAgB,mCAAmC,CACjD,UAAkB,EAClB,UAA0C,EAC1C,KAAoB,EACpB,OAA0B;IAE1B,4CAA4C;IAC5C,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;QACxC,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACxE,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,qEAAqE;IACrE,yCAAyC;IACzC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACjB,KAAK,CACH,2HAA2H,CAC5H,CAAC;QACF,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,IAAI,GAAG,kBAAkB,EAAE,CAAC;IAElC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,kCAAkC,CAAC,CAAC;IAChG,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAE5D,8FAA8F;QAC9F,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,0GAA0G;IAC1G,6EAA6E;IAE7E,mCAAmC;IACnC,gJAAgJ;IAChJ,UAAU,CAAC,MAAM;IACf,kFAAkF;IAClF,CAAC,EACD,CAAC,EACD,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC;IAEF,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,SAAgB,kBAAkB,CAAC,MAAyB,OAAO,CAAC,GAAG;IACrE,yEAAyE;IACzE,0DAA0D;IAC1D,MAAM,GAAG,GACP,oDAAoD;QACpD,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;aACtC,GAAG,CACF,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAC5F;aACA,IAAI,CAAC,GAAG,CAAC;QACZ,KAAK,CAAC;IACR,MAAM,IAAI,GAAG,8CAA8C,GAAG,GAAG,CAAC;IAElE,MAAM,SAAS,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,CAAC;IACnC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CACb,4DAA4D,SAAS,UAAU,CAChF,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CAAC,IAAY;IACjC,MAAM,IAAI,GAAG,kCAAkC,CAAC;IAEhD,OAAO;QACL,YAAY,EAAE,IAAI,GAAG,EAAE;QACvB,SAAS,EAAE,GAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,mBAAmB,EAAE,IAAI,qBAAW,EAAE;QACtC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE;oBACJ,IAAI;oBACJ,SAAS,EAAE,CAAC;oBACZ,GAAG,EAAE,EAAE;iBACR;aACF;SACF;KACF,CAAC;AACJ,CAAC"}