{"version": 3, "file": "lan-network-cli.js", "sources": ["../src/cli.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport type { GatewayAssignment } from './types';\nimport {\n  DEFAULT_ASSIGNMENT,\n  interfaceAssignments,\n  matchAssignment,\n} from './network';\nimport { probeDefaultRoute } from './route';\nimport { dhcpDiscover } from './dhcp';\nimport { lanNetwork } from './index';\n\ntype Mode = 'help' | 'dhcp' | 'probe' | 'fallback' | 'default';\n\nfunction help() {\n  const output = [\n    \"Discover the machine's default gateway and local network IP (test utility)\",\n    '',\n    'Usage',\n    '  $ lan-network',\n    '  $ lan-network --default',\n    '',\n    'Modes',\n    '  --probe     Discover gateway via UDP4 socket to publicly routed address',\n    '  --dhcp      Discover gateway via DHCPv4 discover broadcast',\n    '  --fallback  Return highest-priority IPv4 network interface assignment',\n    '  --default   Try the three above modes in order',\n    '  --help      Print help output',\n  ].join('\\n');\n  console.log(output);\n}\n\nasync function dhcp() {\n  const assignments = interfaceAssignments();\n  if (!assignments.length) {\n    console.error('No available network interface assignments');\n    process.exit(1);\n  }\n  const discoveries = await Promise.allSettled(\n    assignments.map(assignment => {\n      // For each assignment, we send a DHCPDISCOVER packet to its network mask\n      return dhcpDiscover(assignment);\n    })\n  );\n  let assignment: GatewayAssignment | null = null;\n  for (const discovery of discoveries) {\n    // The first discovered gateway is returned, if it matches an assignment\n    if (discovery.status === 'fulfilled' && discovery.value) {\n      const dhcpRoute = discovery.value;\n      if ((assignment = matchAssignment(assignments, dhcpRoute))) {\n        break;\n      }\n    }\n  }\n  if (assignment && assignment !== DEFAULT_ASSIGNMENT) {\n    console.log(JSON.stringify(assignment, null, 2));\n    process.exit(0);\n  } else {\n    console.error('No DHCP router was discoverable');\n    process.exit(1);\n  }\n}\n\nasync function probe() {\n  const assignments = interfaceAssignments();\n  if (!assignments.length) {\n    console.error('No available network interface assignments');\n    process.exit(1);\n  }\n  try {\n    const defaultRoute = await probeDefaultRoute();\n    const assignment = matchAssignment(assignments, defaultRoute);\n    if (assignment && assignment !== DEFAULT_ASSIGNMENT) {\n      console.log(JSON.stringify(assignment, null, 2));\n      process.exit(0);\n    } else {\n      console.error('No default gateway or route');\n      process.exit(1);\n    }\n  } catch (error) {\n    console.error('No default gateway or route');\n    console.error(error);\n    process.exit(1);\n  }\n}\n\nasync function fallback() {\n  const assignments = interfaceAssignments();\n  if (!assignments.length) {\n    console.error('No available network interface assignments');\n    process.exit(1);\n  }\n  const assignment = { ...assignments[0], gateway: null };\n  console.log(JSON.stringify(assignment, null, 2));\n  process.exit(0);\n}\n\nasync function main() {\n  const assignment = await lanNetwork();\n  if (assignment !== DEFAULT_ASSIGNMENT) {\n    console.log(JSON.stringify(assignment, null, 2));\n    process.exit(0);\n  } else {\n    console.error('No default gateway, route, or DHCP router');\n    process.exit(1);\n  }\n}\n\nfunction cli() {\n  let mode: Mode = 'default';\n  parseArgs: for (let i = 1; i < process.argv.length; i++) {\n    const arg = process.argv[i].trim().toLowerCase();\n    switch (arg) {\n      case '-h':\n      case '--help':\n        mode = 'help';\n        break parseArgs;\n      case '-d':\n      case '--dhcp':\n        mode = 'dhcp';\n        break;\n      case '-p':\n      case '--probe':\n        mode = 'probe';\n        break;\n      case '-f':\n      case '--fallback':\n        mode = 'fallback';\n        break;\n      default:\n        if (arg.startsWith('-')) throw new TypeError(`Invalid flag: ${arg}`);\n    }\n  }\n  switch (mode) {\n    case 'help':\n      return help();\n    case 'dhcp':\n      return dhcp();\n    case 'probe':\n      return probe();\n    case 'fallback':\n      return fallback();\n    case 'default':\n      return main();\n  }\n}\n\ncli();\n"], "names": ["cli", "mode", "parseArgs", "i", "process", "argv", "length", "arg", "trim", "toLowerCase", "startsWith", "TypeError", "help", "output", "join", "console", "log", "async", "dhcp", "assignments", "interfaceAssignments", "error", "exit", "discoveries", "Promise", "allSettled", "map", "assignment", "dhcpDiscover", "discovery", "status", "value", "matchAssignment", "DEFAULT_ASSIGNMENT", "JSON", "stringify", "probe", "defaultRoute", "probeDefaultRoute", "fallback", "gateway", "main", "lanNetwork"], "mappings": ";;;CA4GA,SAASA;EACP,IAAIC,IAAa;EACjBC,GAAW,KAAK,IAAIC,IAAI,GAAGA,IAAIC,QAAQC,KAAKC,QAAQH,KAAK;IACvD,IAAMI,IAAMH,QAAQC,KAAKF,GAAGK,OAAOC;IACnC,QAAQF;KACN,KAAK;KACL,KAAK;MACHN,IAAO;MACP,MAAMC;;KACR,KAAK;KACL,KAAK;MACHD,IAAO;MACP;;KACF,KAAK;KACL,KAAK;MACHA,IAAO;MACP;;KACF,KAAK;KACL,KAAK;MACHA,IAAO;MACP;;KACF;MACE,IAAIM,EAAIG,WAAW;QAAM,MAAM,IAAIC,UAAU,iBAAiBJ;;;AAEpE;EACA,QAAQN;GACN,KAAK;IACH,OAzHN,SAASW;MACP,IAAMC,IAAS,EACb,8EACA,IACA,SACA,mBACA,6BACA,IACA,SACA,6EACA,gEACA,2EACA,oDACA,oCACAC,KAAK;MACPC,QAAQC,IAAIH;AACd,KAyGaD;;GACT,KAAK;IACH,OAzGNK,eAAeC;MACb,IAAMC,IAAcC,EAAAA;MACpB,KAAKD,EAAYb,QAAQ;QACvBS,QAAQM,MAAM;QACdjB,QAAQkB,KAAK;AACf;MACA,IAAMC,UAAoBC,QAAQC,WAChCN,EAAYO,KAAIC,KAEPC,EAAAA,aAAaD;MAGxB,IAAIA,IAAuC;MAC3C,KAAK,IAAME,KAAaN;QAEtB,IAAyB,gBAArBM,EAAUC,UAA0BD,EAAUE;UAEhD,IAAKJ,IAAaK,EAAAA,gBAAgBb,GADhBU,EAAUE;YAE1B;;;;MAIN,IAAIJ,KAAcA,MAAeM,sBAAoB;QACnDlB,QAAQC,IAAIkB,KAAKC,UAAUR,GAAY,MAAM;QAC7CvB,QAAQkB,KAAK;AACf,aAAO;QACLP,QAAQM,MAAM;QACdjB,QAAQkB,KAAK;AACf;AACF,KA4EaJ;;GACT,KAAK;IACH,OA5END,eAAemB;MACb,IAAMjB,IAAcC,EAAAA;MACpB,KAAKD,EAAYb,QAAQ;QACvBS,QAAQM,MAAM;QACdjB,QAAQkB,KAAK;AACf;MACA;QACE,IAAMe,UAAqBC;QAC3B,IAAMX,IAAaK,EAAAA,gBAAgBb,GAAakB;QAChD,IAAIV,KAAcA,MAAeM,sBAAoB;UACnDlB,QAAQC,IAAIkB,KAAKC,UAAUR,GAAY,MAAM;UAC7CvB,QAAQkB,KAAK;AACf,eAAO;UACLP,QAAQM,MAAM;UACdjB,QAAQkB,KAAK;AACf;AACD,QAAC,OAAOD;QACPN,QAAQM,MAAM;QACdN,QAAQM,MAAMA;QACdjB,QAAQkB,KAAK;AACf;AACF,KAuDac;;GACT,KAAK;IACH,OAvDNnB,eAAesB;MACb,IAAMpB,IAAcC,EAAAA;MACpB,KAAKD,EAAYb,QAAQ;QACvBS,QAAQM,MAAM;QACdjB,QAAQkB,KAAK;AACf;MACA,IAAMK,IAAa;WAAKR,EAAY;QAAIqB,SAAS;;MACjDzB,QAAQC,IAAIkB,KAAKC,UAAUR,GAAY,MAAM;MAC7CvB,QAAQkB,KAAK;AACf,KA8CaiB;;GACT,KAAK;IACH,OA9CNtB,eAAewB;MACb,IAAMd,UAAmBe;MACzB,IAAIf,MAAeM,EAAAA,oBAAoB;QACrClB,QAAQC,IAAIkB,KAAKC,UAAUR,GAAY,MAAM;QAC7CvB,QAAQkB,KAAK;AACf,aAAO;QACLP,QAAQM,MAAM;QACdjB,QAAQkB,KAAK;AACf;AACF,KAqCamB;;AAEb,CAEAzC"}