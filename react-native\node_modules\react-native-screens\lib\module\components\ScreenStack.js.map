{"version": 3, "names": ["_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "React", "GHContext", "RNSScreensRefContext", "freezeEnabled", "DelayedFreeze", "warnOnce", "ScreenStackNativeComponent", "isF<PERSON><PERSON>", "global", "assertGHProvider", "ScreenGestureDetector", "goBackGesture", "isGestureDetectorProviderNotDetected", "name", "undefined", "assertCustomScreenTransitionsProps", "screensRefs", "currentScreenId", "isGestureDetectorNotConfiguredProperly", "ScreenStack", "props", "passedScreenRefs", "transitionAnimation", "screenEdgeGesture", "onFinishTransitioning", "children", "rest", "useRef", "current", "ref", "size", "Children", "count", "useContext", "gestureDetectorBridge", "stackUseEffectCallback", "_stackRef", "childrenWithFreeze", "map", "child", "index", "descriptor", "descriptors", "isFreezeEnabled", "options", "freezeOnBlur", "freezePreviousScreen", "createElement", "freeze", "useEffect", "Provider", "value"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAEb,OAAOQ,KAAK,MAA6B,OAAO;AAQhD,SAASC,SAAS,EAAEC,oBAAoB,QAAQ,aAAa;AAC7D,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,QAAQ,MAAM,WAAW;;AAEhC;AACA,OAAOC,0BAA0B,MAE1B,sCAAsC;AAE7C,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAO,uBAAuB,IAAIC,MAAM;AAC1C;AAEA,MAAMC,gBAAgB,GAAGA,CACvBC,qBAEsB,EACtBC,aAAwC,KACrC;EACH,MAAMC,oCAAoC,GACxCF,qBAAqB,CAACG,IAAI,KAAK,WAAW,IAAIF,aAAa,KAAKG,SAAS;EAE3ET,QAAQ,CACNO,oCAAoC,EACpC,8IACF,CAAC;AACH,CAAC;AAED,MAAMG,kCAAkC,GAAGA,CACzCC,WAA4C,EAC5CC,eAAoD,EACpDN,aAAgD,KAC7C;EACH,MAAMO,sCAAsC,GAC1CP,aAAa,KAAKG,SAAS,IAC3BE,WAAW,KAAK,IAAI,IACpBC,eAAe,KAAKH,SAAS;EAE/BT,QAAQ,CACNa,sCAAsC,EACtC,kFACF,CAAC;AACH,CAAC;AAED,SAASC,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IACJT,aAAa;IACbK,WAAW,EAAEK,gBAAgB;IAAE;IAC/BJ,eAAe;IACfK,mBAAmB;IACnBC,iBAAiB;IACjBC,qBAAqB;IACrBC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGN,KAAK;EAET,MAAMJ,WAAW,GAAGhB,KAAK,CAAC2B,MAAM,CAC9BN,gBAAgB,EAAEO,OAAO,IAAI,CAAC,CAChC,CAAC;EACD,MAAMC,GAAG,GAAG7B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMG,IAAI,GAAG9B,KAAK,CAAC+B,QAAQ,CAACC,KAAK,CAACP,QAAQ,CAAC;EAC3C,MAAMf,qBAAqB,GAAGV,KAAK,CAACiC,UAAU,CAAChC,SAAS,CAAC;EACzD,MAAMiC,qBAAqB,GAAGlC,KAAK,CAAC2B,MAAM,CAAwB;IAChEQ,sBAAsB,EAAEC,SAAS,IAAI;MACnC;IAAA;EAEJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGrC,KAAK,CAAC+B,QAAQ,CAACO,GAAG,CAACb,QAAQ,EAAE,CAACc,KAAK,EAAEC,KAAK,KAAK;IACxE;IACA,MAAM;MAAEpB,KAAK;MAAEzB;IAAI,CAAC,GAAG4C,KAAK;IAC5B,MAAME,UAAU,GAAGrB,KAAK,EAAEqB,UAAU,IAAIrB,KAAK,EAAEsB,WAAW,GAAG/C,GAAG,CAAC;IACjE,MAAMgD,eAAe,GACnBF,UAAU,EAAEG,OAAO,EAAEC,YAAY,IAAI1C,aAAa,CAAC,CAAC;;IAEtD;IACA;IACA,MAAM2C,oBAAoB,GAAGvC,QAAQ,CAAC,CAAC,GACnCuB,IAAI,GAAGU,KAAK,GAAG,CAAC,GAChBV,IAAI,GAAGU,KAAK,GAAG,CAAC;IAEpB,oBACExC,KAAA,CAAA+C,aAAA,CAAC3C,aAAa;MAAC4C,MAAM,EAAEL,eAAe,IAAIG;IAAqB,GAC5DP,KACY,CAAC;EAEpB,CAAC,CAAC;EAEFvC,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpBf,qBAAqB,CAACN,OAAO,CAACO,sBAAsB,CAACN,GAAG,CAAC;EAC3D,CAAC,CAAC;EAEFpB,gBAAgB,CAACC,qBAAqB,EAAEC,aAAa,CAAC;EAEtDI,kCAAkC,CAChCC,WAAW,EACXC,eAAe,EACfN,aACF,CAAC;EAED,oBACEX,KAAA,CAAA+C,aAAA,CAAC7C,oBAAoB,CAACgD,QAAQ;IAACC,KAAK,EAAEnC;EAAY,gBAChDhB,KAAA,CAAA+C,aAAA,CAACrC,qBAAqB;IACpBwB,qBAAqB,EAAEA,qBAAsB;IAC7CvB,aAAa,EAAEA,aAAc;IAC7BW,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAiB,IAAI,KAAM;IAC9CP,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA;EAAgB,gBACjCjB,KAAA,CAAA+C,aAAA,CAACzC,0BAA0B,EAAApB,QAAA,KACrBwC,IAAI;IACR;AACV;AACA;AACA;AACA;IACUF,qBAAqB,EACnBA,qBACD;IACDK,GAAG,EAAEA;EAAI,IACRQ,kBACyB,CACP,CACM,CAAC;AAEpC;AAEA,eAAelB,WAAW"}