{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithProgress.ts"], "sourcesContent": ["import { Response } from 'undici';\n\nimport { FetchLike } from './client.types';\nimport * as Log from '../../log';\n\nconst debug = require('debug')('expo:api:fetch:progress') as typeof console.log;\n\nexport function wrapFetchWithProgress(fetch: Fetch<PERSON>ike): FetchLike {\n  return function fetchWithProgress(url, init) {\n    return fetch(url, init).then((response) => {\n      const onProgress = init?.onProgress;\n\n      // Abort if no `onProgress` is provided, the request failed, or there is no response body\n      if (!onProgress || !response.ok || !response.body) {\n        return response;\n      }\n\n      // Calculate total progress size\n      const contentLength = response.headers.get('Content-Length');\n      const progressTotal = Number(contentLength);\n\n      debug(`Download size: %d`, progressTotal);\n\n      // Abort if the `Content-Length` header is missing or invalid\n      if (!progressTotal || isNaN(progressTotal) || progressTotal < 0) {\n        Log.warn(\n          'Progress callback not supported for network request because \"Content-Length\" header missing or invalid in response from URL:',\n          url.toString()\n        );\n        return response;\n      }\n\n      debug(`Starting progress animation for: %s`, url);\n\n      // Initialize the progression variables\n      let progressCurrent = 0;\n      const progressUpdate = () => {\n        const progress = progressCurrent / progressTotal || 0;\n        onProgress({\n          progress,\n          total: progressTotal,\n          loaded: progressCurrent,\n        });\n      };\n\n      // Create a new body-wrapping stream that handles the progression methods\n      const bodyReader = response.body.getReader();\n      const bodyWithProgress = new ReadableStream({\n        start(controller) {\n          function next() {\n            bodyReader.read().then(({ done, value }) => {\n              // Close the controller once stream is done\n              if (done) return controller.close();\n\n              // Update the progression\n              progressCurrent += Buffer.byteLength(value);\n              progressUpdate();\n\n              // Continue the stream, and read the next chunk\n              controller.enqueue(value);\n              next();\n            });\n          }\n\n          progressUpdate();\n          next();\n        },\n      });\n\n      // Return the new response with the wrapped body stream\n      return new Response(bodyWithProgress as any, response);\n    });\n  };\n}\n"], "names": ["wrapFetchWithProgress", "debug", "require", "fetch", "fetchWithProgress", "url", "init", "then", "response", "onProgress", "ok", "body", "contentLength", "headers", "get", "progressTotal", "Number", "isNaN", "Log", "warn", "toString", "progressCurrent", "progressUpdate", "progress", "total", "loaded", "bodyReader", "<PERSON><PERSON><PERSON><PERSON>", "bodyWithProgress", "ReadableStream", "start", "controller", "next", "read", "done", "value", "close", "<PERSON><PERSON><PERSON>", "byteLength", "enqueue", "Response"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;;yBAPS;;;;;;6DAGJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF,sBAAsBG,KAAgB;IACpD,OAAO,SAASC,kBAAkBC,GAAG,EAAEC,IAAI;QACzC,OAAOH,MAAME,KAAKC,MAAMC,IAAI,CAAC,CAACC;YAC5B,MAAMC,aAAaH,wBAAAA,KAAMG,UAAU;YAEnC,yFAAyF;YACzF,IAAI,CAACA,cAAc,CAACD,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;gBACjD,OAAOH;YACT;YAEA,gCAAgC;YAChC,MAAMI,gBAAgBJ,SAASK,OAAO,CAACC,GAAG,CAAC;YAC3C,MAAMC,gBAAgBC,OAAOJ;YAE7BX,MAAM,CAAC,iBAAiB,CAAC,EAAEc;YAE3B,6DAA6D;YAC7D,IAAI,CAACA,iBAAiBE,MAAMF,kBAAkBA,gBAAgB,GAAG;gBAC/DG,KAAIC,IAAI,CACN,gIACAd,IAAIe,QAAQ;gBAEd,OAAOZ;YACT;YAEAP,MAAM,CAAC,mCAAmC,CAAC,EAAEI;YAE7C,uCAAuC;YACvC,IAAIgB,kBAAkB;YACtB,MAAMC,iBAAiB;gBACrB,MAAMC,WAAWF,kBAAkBN,iBAAiB;gBACpDN,WAAW;oBACTc;oBACAC,OAAOT;oBACPU,QAAQJ;gBACV;YACF;YAEA,yEAAyE;YACzE,MAAMK,aAAalB,SAASG,IAAI,CAACgB,SAAS;YAC1C,MAAMC,mBAAmB,IAAIC,eAAe;gBAC1CC,OAAMC,UAAU;oBACd,SAASC;wBACPN,WAAWO,IAAI,GAAG1B,IAAI,CAAC,CAAC,EAAE2B,IAAI,EAAEC,KAAK,EAAE;4BACrC,2CAA2C;4BAC3C,IAAID,MAAM,OAAOH,WAAWK,KAAK;4BAEjC,yBAAyB;4BACzBf,mBAAmBgB,OAAOC,UAAU,CAACH;4BACrCb;4BAEA,+CAA+C;4BAC/CS,WAAWQ,OAAO,CAACJ;4BACnBH;wBACF;oBACF;oBAEAV;oBACAU;gBACF;YACF;YAEA,uDAAuD;YACvD,OAAO,IAAIQ,CAAAA,SAAO,UAAC,CAACZ,kBAAyBpB;QAC/C;IACF;AACF"}