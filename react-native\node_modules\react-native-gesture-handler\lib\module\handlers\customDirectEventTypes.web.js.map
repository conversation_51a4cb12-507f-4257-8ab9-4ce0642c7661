{"version": 3, "sources": ["customDirectEventTypes.web.ts"], "names": ["customDirectEventTypes"], "mappings": "AAAA;AACA;AACA,MAAMA,sBAAsB,GAAG,EAA/B;AAEA,SAASA,sBAAT", "sourcesContent": ["// customDirectEventTypes doesn't exist in react-native-web, therefore importing it\n// directly in createHandler.tsx would end in crash.\nconst customDirectEventTypes = {};\n\nexport { customDirectEventTypes };\n"]}