import { Alert } from 'react-native';
import { VALIDATION, ERROR_MESSAGES, DATE_FORMATS } from '../config/constants';

/**
 * Utilitaires pour la validation des données
 */
export const ValidationUtils = {
  /**
   * Valide une adresse email
   */
  isValidEmail: (email) => {
    return VALIDATION.EMAIL_REGEX.test(email);
  },

  /**
   * Valide un numéro de téléphone
   */
  isValidPhone: (phone) => {
    return VALIDATION.PHONE_REGEX.test(phone);
  },

  /**
   * Valide une période (YYYY-MM)
   */
  isValidPeriod: (period) => {
    return VALIDATION.PERIOD_REGEX.test(period);
  },

  /**
   * Valide un mot de passe
   */
  isValidPassword: (password) => {
    return password && password.length >= VALIDATION.MIN_PASSWORD_LENGTH;
  },

  /**
   * Valide que la consommation actuelle est supérieure à la précédente
   */
  isValidConsommation: (current, previous) => {
    const currentNum = parseFloat(current);
    const previousNum = parseFloat(previous);
    return !isNaN(currentNum) && !isNaN(previousNum) && currentNum > previousNum;
  },

  /**
   * Valide les champs obligatoires
   */
  areRequiredFieldsFilled: (fields) => {
    return fields.every(field => field && field.toString().trim() !== '');
  },
};

/**
 * Utilitaires pour le formatage des données
 */
export const FormatUtils = {
  /**
   * Formate une date
   */
  formatDate: (dateString, format = DATE_FORMATS.DISPLAY) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    switch (format) {
      case DATE_FORMATS.DISPLAY:
        return date.toLocaleDateString('fr-FR');
      case DATE_FORMATS.FULL:
        return date.toLocaleString('fr-FR');
      case DATE_FORMATS.API:
        return date.toISOString().split('T')[0];
      case DATE_FORMATS.PERIOD:
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        return `${year}-${month}`;
      default:
        return date.toLocaleDateString('fr-FR');
    }
  },

  /**
   * Formate un montant en devise
   */
  formatCurrency: (amount, currency = 'DH') => {
    if (isNaN(amount)) return '0.00 ' + currency;
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
  },

  /**
   * Formate un nom complet
   */
  formatFullName: (prenom, nom) => {
    return `${prenom || ''} ${nom || ''}`.trim();
  },

  /**
   * Formate les initiales
   */
  formatInitials: (prenom, nom) => {
    const prenomInitial = prenom ? prenom.charAt(0).toUpperCase() : '';
    const nomInitial = nom ? nom.charAt(0).toUpperCase() : '';
    return prenomInitial + nomInitial;
  },

  /**
   * Formate un numéro de téléphone
   */
  formatPhone: (phone) => {
    if (!phone) return '';
    // Supprime tous les caractères non numériques sauf le +
    const cleaned = phone.replace(/[^\d+]/g, '');
    return cleaned;
  },

  /**
   * Formate une adresse complète
   */
  formatAddress: (adresse, ville) => {
    const parts = [adresse, ville].filter(part => part && part.trim());
    return parts.join(', ');
  },

  /**
   * Tronque un texte avec des points de suspension
   */
  truncateText: (text, maxLength = 50) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  },
};

/**
 * Utilitaires pour les calculs
 */
export const CalculationUtils = {
  /**
   * Calcule la différence de consommation
   */
  calculateConsommationDifference: (current, previous) => {
    const currentNum = parseFloat(current) || 0;
    const previousNum = parseFloat(previous) || 0;
    return Math.max(0, currentNum - previousNum);
  },

  /**
   * Calcule le nombre de jours entre deux dates
   */
  calculateDaysBetween: (date1, date2) => {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    const diffTime = Math.abs(d2 - d1);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  /**
   * Calcule la consommation moyenne par jour
   */
  calculateAverageConsommation: (consommation, days) => {
    if (!days || days === 0) return 0;
    return (parseFloat(consommation) || 0) / days;
  },

  /**
   * Calcule le pourcentage
   */
  calculatePercentage: (value, total) => {
    if (!total || total === 0) return 0;
    return Math.round((value / total) * 100);
  },
};

/**
 * Utilitaires pour la gestion des erreurs
 */
export const ErrorUtils = {
  /**
   * Affiche une alerte d'erreur
   */
  showError: (message = ERROR_MESSAGES.NETWORK_ERROR, title = 'Erreur') => {
    Alert.alert(title, message);
  },

  /**
   * Affiche une alerte de succès
   */
  showSuccess: (message, title = 'Succès') => {
    Alert.alert(title, message);
  },

  /**
   * Affiche une alerte de confirmation
   */
  showConfirmation: (message, onConfirm, onCancel = null, title = 'Confirmation') => {
    Alert.alert(
      title,
      message,
      [
        { text: 'Annuler', style: 'cancel', onPress: onCancel },
        { text: 'Confirmer', onPress: onConfirm },
      ]
    );
  },

  /**
   * Gère les erreurs d'API
   */
  handleApiError: (error) => {
    console.error('API Error:', error);
    
    if (error.message?.includes('Failed to fetch')) {
      return ERROR_MESSAGES.NETWORK_ERROR;
    }
    
    if (error.status === 401) {
      return ERROR_MESSAGES.UNAUTHORIZED;
    }
    
    if (error.status === 404) {
      return ERROR_MESSAGES.NOT_FOUND;
    }
    
    if (error.status >= 500) {
      return ERROR_MESSAGES.SERVER_ERROR;
    }
    
    return error.message || ERROR_MESSAGES.NETWORK_ERROR;
  },
};

/**
 * Utilitaires pour les API calls
 */
export const ApiUtils = {
  /**
   * Crée les headers par défaut pour les requêtes
   */
  getDefaultHeaders: () => ({
    'Content-Type': 'application/json',
  }),

  /**
   * Gère la réponse d'une requête fetch
   */
  handleResponse: async (response) => {
    if (!response.ok) {
      const error = new Error(`HTTP ${response.status}`);
      error.status = response.status;
      throw error;
    }
    
    const data = await response.json();
    return data;
  },

  /**
   * Effectue une requête GET
   */
  get: async (url) => {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: ApiUtils.getDefaultHeaders(),
      });
      return await ApiUtils.handleResponse(response);
    } catch (error) {
      throw new Error(ErrorUtils.handleApiError(error));
    }
  },

  /**
   * Effectue une requête POST
   */
  post: async (url, data) => {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: ApiUtils.getDefaultHeaders(),
        body: JSON.stringify(data),
      });
      return await ApiUtils.handleResponse(response);
    } catch (error) {
      throw new Error(ErrorUtils.handleApiError(error));
    }
  },

  /**
   * Effectue une requête PUT
   */
  put: async (url, data) => {
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: ApiUtils.getDefaultHeaders(),
        body: JSON.stringify(data),
      });
      return await ApiUtils.handleResponse(response);
    } catch (error) {
      throw new Error(ErrorUtils.handleApiError(error));
    }
  },
};

/**
 * Utilitaires pour le stockage local
 */
export const StorageUtils = {
  /**
   * Sauvegarde des données dans le stockage local
   */
  saveData: async (key, data) => {
    try {
      const jsonData = JSON.stringify(data);
      // Ici vous pourriez utiliser AsyncStorage
      // await AsyncStorage.setItem(key, jsonData);
      console.log(`Données sauvegardées pour la clé: ${key}`);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  },

  /**
   * Récupère des données du stockage local
   */
  getData: async (key) => {
    try {
      // Ici vous pourriez utiliser AsyncStorage
      // const jsonData = await AsyncStorage.getItem(key);
      // return jsonData ? JSON.parse(jsonData) : null;
      console.log(`Récupération des données pour la clé: ${key}`);
      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération:', error);
      return null;
    }
  },

  /**
   * Supprime des données du stockage local
   */
  removeData: async (key) => {
    try {
      // Ici vous pourriez utiliser AsyncStorage
      // await AsyncStorage.removeItem(key);
      console.log(`Données supprimées pour la clé: ${key}`);
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  },
};

/**
 * Utilitaires pour les permissions
 */
export const PermissionUtils = {
  /**
   * Demande la permission caméra
   */
  requestCameraPermission: async () => {
    try {
      // Cette fonction serait implémentée avec expo-camera
      console.log('Demande de permission caméra');
      return true;
    } catch (error) {
      console.error('Erreur permission caméra:', error);
      return false;
    }
  },

  /**
   * Demande la permission localisation
   */
  requestLocationPermission: async () => {
    try {
      // Cette fonction serait implémentée avec expo-location
      console.log('Demande de permission localisation');
      return true;
    } catch (error) {
      console.error('Erreur permission localisation:', error);
      return false;
    }
  },
};

/**
 * Utilitaires divers
 */
export const MiscUtils = {
  /**
   * Génère un ID unique simple
   */
  generateId: () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  /**
   * Attend un délai spécifié
   */
  delay: (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Débounce une fonction
   */
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Vérifie si une chaîne est vide ou ne contient que des espaces
   */
  isEmpty: (str) => {
    return !str || str.trim().length === 0;
  },

  /**
   * Capitalise la première lettre d'une chaîne
   */
  capitalize: (str) => {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },
};
