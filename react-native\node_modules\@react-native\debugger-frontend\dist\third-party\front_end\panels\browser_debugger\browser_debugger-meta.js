import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/root/root.js";import*as r from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";const n={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},o=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",n),a=e.i18n.getLazilyComputedLocalizedString.bind(void 0,o);let s,c;async function d(){return s||(s=await import("./browser_debugger.js")),s}async function p(){return c||(c=await import("../sources/sources.js")),c}i.ViewManager.registerViewExtension({loadView:async()=>(await d()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(n.showEventListenerBreakpoints),title:a(n.eventListenerBreakpoints),order:9,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await d()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(n.showCspViolationBreakpoints),title:a(n.cspViolationBreakpoints),order:10,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>(await d()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(n.showXhrfetchBreakpoints),title:a(n.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await d()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:a(n.showDomBreakpoints),title:a(n.domBreakpoints),order:7,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await d()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:a(n.showGlobalListeners),title:a(n.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await d()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:a(n.showDomBreakpoints),title:a(n.domBreakpoints),order:6,persistence:"permanent"}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:a(n.page),commandPrompt:a(n.showPage),order:2,persistence:"permanent",loadView:async()=>(await p()).SourcesNavigator.NetworkNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:a(n.overrides),commandPrompt:a(n.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await p()).SourcesNavigator.OverridesNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:a(n.contentScripts),commandPrompt:a(n.showContentScripts),order:5,persistence:"permanent",condition:()=>"/bundled/worker_app.html"!==t.Runtime.getPathName(),loadView:async()=>new((await p()).SourcesNavigator.ContentScriptsNavigatorView)}),i.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await d()).ObjectEventListenersSidebarPane.ActionDelegate),title:a(n.refreshGlobalListeners),iconClass:"refresh",contextTypes(){return e=e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane],void 0===s?[]:e(s);var e}}),i.ContextMenu.registerProvider({contextTypes:()=>[r.DOMModel.DOMNode],loadProvider:async()=>new((await d()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),i.Context.registerListener({contextTypes:()=>[r.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await d()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),i.Context.registerListener({contextTypes:()=>[r.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await d()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});
