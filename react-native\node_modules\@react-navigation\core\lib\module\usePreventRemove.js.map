{"version": 3, "names": ["nanoid", "React", "useLatestCallback", "useNavigation", "usePreventRemoveContext", "useRoute", "usePreventRemove", "preventRemove", "callback", "id", "useState", "navigation", "key", "routeKey", "setPreventRemove", "useEffect", "beforeRemoveListener", "e", "preventDefault", "data", "addListener"], "sourceRoot": "../../src", "sources": ["usePreventRemove.tsx"], "mappings": "AACA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgB,CACtCC,aAAsB,EACtBC,QAAmE,EACnE;EACA,MAAM,CAACC,EAAE,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,MAAMV,MAAM,EAAE,CAAC;EAE3C,MAAMW,UAAU,GAAGR,aAAa,EAAE;EAClC,MAAM;IAAES,GAAG,EAAEC;EAAS,CAAC,GAAGR,QAAQ,EAAE;EAEpC,MAAM;IAAES;EAAiB,CAAC,GAAGV,uBAAuB,EAAE;EAEtDH,KAAK,CAACc,SAAS,CAAC,MAAM;IACpBD,gBAAgB,CAACL,EAAE,EAAEI,QAAQ,EAAEN,aAAa,CAAC;IAC7C,OAAO,MAAM;MACXO,gBAAgB,CAACL,EAAE,EAAEI,QAAQ,EAAE,KAAK,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACC,gBAAgB,EAAEL,EAAE,EAAEI,QAAQ,EAAEN,aAAa,CAAC,CAAC;EAEnD,MAAMS,oBAAoB,GAAGd,iBAAiB,CAE3Ce,CAAC,IAAK;IACP,IAAI,CAACV,aAAa,EAAE;MAClB;IACF;IAEAU,CAAC,CAACC,cAAc,EAAE;IAElBV,QAAQ,CAAC;MAAEW,IAAI,EAAEF,CAAC,CAACE;IAAK,CAAC,CAAC;EAC5B,CAAC,CAAC;EAEFlB,KAAK,CAACc,SAAS,CACb,MAAMJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAAC,cAAc,EAAEJ,oBAAoB,CAAC,EACnE,CAACL,UAAU,EAAEK,oBAAoB,CAAC,CACnC;AACH"}