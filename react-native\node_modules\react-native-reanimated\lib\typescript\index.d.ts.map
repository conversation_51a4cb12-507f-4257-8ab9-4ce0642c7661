{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAEA,OAAO,iBAAiB,CAAC;AACzB,OAAO,KAAK,QAAQ,MAAM,YAAY,CAAC;AAEvC,eAAe,QAAQ,CAAC;AAExB,OAAO,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,EAAE,QAAQ,IAAI,kBAAkB,EAAE,MAAM,UAAU,CAAC;AAC1D,YAAY,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC;AAC7C,OAAO,EACL,OAAO,EACP,OAAO,EACP,oBAAoB,EACpB,YAAY,EACZ,WAAW,EACX,2BAA2B,EAC3B,aAAa,EACb,YAAY,EACZ,sBAAsB,EACtB,WAAW,EACX,sBAAsB,GACvB,MAAM,QAAQ,CAAC;AAChB,YAAY,EACV,eAAe,EACf,WAAW,EACX,YAAY,EACZ,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,aAAa,EACb,WAAW,EACX,YAAY,EACZ,qBAAqB,EACrB,iBAAiB,EACjB,eAAe,GAChB,MAAM,QAAQ,CAAC;AAChB,OAAO,EACL,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,yBAAyB,EACzB,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACxB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,uBAAuB,GACxB,MAAM,QAAQ,CAAC;AAChB,YAAY,EACV,cAAc,EACd,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,cAAc,GACf,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,eAAe,EACf,eAAe,EACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACZ,UAAU,EACV,UAAU,GACX,MAAM,aAAa,CAAC;AACrB,YAAY,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAC9E,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AACpE,YAAY,EACV,oBAAoB,EACpB,iBAAiB,EACjB,cAAc,EACd,cAAc,GACf,MAAM,oBAAoB,CAAC;AAC5B,OAAO;AACL,4DAA4D;AAC5D,WAAW,EACX,UAAU,EACV,gBAAgB,EAChB,oBAAoB,GACrB,MAAM,oBAAoB,CAAC;AAC5B,YAAY,EACV,QAAQ,EACR,qBAAqB,EACrB,eAAe,GAChB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,YAAY,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EACL,OAAO,EACP,eAAe,EACf,QAAQ,EACR,eAAe,EACf,cAAc,EACd,iBAAiB,GAClB,MAAM,qBAAqB,CAAC;AAC7B,YAAY,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAChE,OAAO,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,YAAY,EACV,eAAe,EACf,qBAAqB,EACrB,oBAAoB,EACpB,0BAA0B,EAC1B,sBAAsB,EACtB,uBAAuB,EACvB,4BAA4B,EAC5B,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,EACvB,0BAA0B,EAC1B,yBAAyB,GAC1B,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,oBAAoB,EACpB,uBAAuB,EACvB,QAAQ,EAER,SAAS,EACT,WAAW,EACX,WAAW,EACX,YAAY,EACZ,WAAW,EACX,WAAW,EACX,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,YAAY,EAEZ,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EAEX,MAAM,EACN,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,OAAO,EACP,YAAY,EACZ,WAAW,EACX,SAAS,EACT,WAAW,EAEX,YAAY,EACZ,WAAW,EACX,aAAa,EACb,YAAY,EACZ,SAAS,EACT,WAAW,EACX,UAAU,EACV,YAAY,EAEZ,MAAM,EACN,YAAY,EACZ,UAAU,EACV,WAAW,EACX,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,cAAc,EACd,OAAO,EACP,aAAa,EACb,WAAW,EACX,YAAY,EACZ,SAAS,EACT,WAAW,EACX,aAAa,EACb,eAAe,EAEf,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,aAAa,EACb,SAAS,EACT,aAAa,EACb,WAAW,EACX,aAAa,EACb,cAAc,EAEd,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EAEjB,UAAU,EACV,WAAW,EAEX,gBAAgB,EAChB,iBAAiB,EACjB,cAAc,EACd,eAAe,EACf,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAEhB,UAAU,EACV,WAAW,EACX,WAAW,EACX,YAAY,EAEZ,MAAM,EACN,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EAEjB,gBAAgB,EAChB,oBAAoB,GACrB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,YAAY,EACV,UAAU,EACV,WAAW,EACX,qBAAqB,EACrB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,uBAAuB,EACvB,kBAAkB,EAClB,cAAc,EACd,iBAAiB,EACjB,kBAAkB,EAClB,YAAY,EACZ,aAAa,EACb,eAAe,GAChB,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,UAAU,EACV,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,EACb,YAAY,EACZ,iBAAiB,GAClB,MAAM,eAAe,CAAC;AACvB,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,EAAE,2BAA2B,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,EACL,mBAAmB,EACnB,sBAAsB,EACtB,uBAAuB,EACvB,UAAU,EACV,gBAAgB,GACjB,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,qBAAqB,EAAE,MAAM,mCAAmC,CAAC;AAC1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AACpE,YAAY,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AAC9E,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,YAAY,EACV,SAAS,EACT,eAAe,EACf,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,iBAAiB,GAClB,MAAM,eAAe,CAAC;AACvB,YAAY,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AACtE,YAAY,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EACL,qBAAqB,EACrB,sBAAsB,EACtB,gBAAgB,GACjB,MAAM,oBAAoB,CAAC;AAC5B,YAAY,EACV,wBAAwB,EACxB,aAAa,EACb,sBAAsB,GACvB,MAAM,oBAAoB,CAAC"}