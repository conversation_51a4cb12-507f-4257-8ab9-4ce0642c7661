"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Entitlements = exports.DeviceFamily = exports.DevelopmentTeam = exports.BundleIdentifier = exports.BuildScheme = exports.BuildProperties = exports.Bitcode = void 0;
Object.defineProperty(exports, "ExpoPlist", {
  enumerable: true,
  get: function () {
    return _IosConfig().ExpoPlist;
  }
});
exports.Google = void 0;
Object.defineProperty(exports, "InfoPlist", {
  enumerable: true,
  get: function () {
    return _IosConfig().InfoPlist;
  }
});
exports.XcodeUtils = exports.XcodeProjectFile = exports.Version = exports.UsesNonExemptEncryption = exports.Updates = exports.Target = exports.Scheme = exports.RequiresFullScreen = exports.ProvisioningProfile = exports.PrivacyInfo = exports.Permissions = exports.Paths = exports.Orientation = exports.Name = exports.Maps = exports.Locales = void 0;
function Bitcode() {
  const data = _interopRequireWildcard(require("./Bitcode"));
  Bitcode = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Bitcode", {
  enumerable: true,
  get: function () {
    return Bitcode();
  }
});
function BuildProperties() {
  const data = _interopRequireWildcard(require("./BuildProperties"));
  BuildProperties = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "BuildProperties", {
  enumerable: true,
  get: function () {
    return BuildProperties();
  }
});
function BuildScheme() {
  const data = _interopRequireWildcard(require("./BuildScheme"));
  BuildScheme = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "BuildScheme", {
  enumerable: true,
  get: function () {
    return BuildScheme();
  }
});
function BundleIdentifier() {
  const data = _interopRequireWildcard(require("./BundleIdentifier"));
  BundleIdentifier = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "BundleIdentifier", {
  enumerable: true,
  get: function () {
    return BundleIdentifier();
  }
});
function DevelopmentTeam() {
  const data = _interopRequireWildcard(require("./DevelopmentTeam"));
  DevelopmentTeam = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "DevelopmentTeam", {
  enumerable: true,
  get: function () {
    return DevelopmentTeam();
  }
});
function DeviceFamily() {
  const data = _interopRequireWildcard(require("./DeviceFamily"));
  DeviceFamily = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "DeviceFamily", {
  enumerable: true,
  get: function () {
    return DeviceFamily();
  }
});
function Entitlements() {
  const data = _interopRequireWildcard(require("./Entitlements"));
  Entitlements = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Entitlements", {
  enumerable: true,
  get: function () {
    return Entitlements();
  }
});
function Google() {
  const data = _interopRequireWildcard(require("./Google"));
  Google = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Google", {
  enumerable: true,
  get: function () {
    return Google();
  }
});
function _IosConfig() {
  const data = require("./IosConfig.types");
  _IosConfig = function () {
    return data;
  };
  return data;
}
function Locales() {
  const data = _interopRequireWildcard(require("./Locales"));
  Locales = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Locales", {
  enumerable: true,
  get: function () {
    return Locales();
  }
});
function Maps() {
  const data = _interopRequireWildcard(require("./Maps"));
  Maps = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Maps", {
  enumerable: true,
  get: function () {
    return Maps();
  }
});
function Name() {
  const data = _interopRequireWildcard(require("./Name"));
  Name = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Name", {
  enumerable: true,
  get: function () {
    return Name();
  }
});
function Orientation() {
  const data = _interopRequireWildcard(require("./Orientation"));
  Orientation = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Orientation", {
  enumerable: true,
  get: function () {
    return Orientation();
  }
});
function Paths() {
  const data = _interopRequireWildcard(require("./Paths"));
  Paths = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Paths", {
  enumerable: true,
  get: function () {
    return Paths();
  }
});
function Permissions() {
  const data = _interopRequireWildcard(require("./Permissions"));
  Permissions = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Permissions", {
  enumerable: true,
  get: function () {
    return Permissions();
  }
});
function PrivacyInfo() {
  const data = _interopRequireWildcard(require("./PrivacyInfo"));
  PrivacyInfo = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "PrivacyInfo", {
  enumerable: true,
  get: function () {
    return PrivacyInfo();
  }
});
function ProvisioningProfile() {
  const data = _interopRequireWildcard(require("./ProvisioningProfile"));
  ProvisioningProfile = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "ProvisioningProfile", {
  enumerable: true,
  get: function () {
    return ProvisioningProfile();
  }
});
function RequiresFullScreen() {
  const data = _interopRequireWildcard(require("./RequiresFullScreen"));
  RequiresFullScreen = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "RequiresFullScreen", {
  enumerable: true,
  get: function () {
    return RequiresFullScreen();
  }
});
function Scheme() {
  const data = _interopRequireWildcard(require("./Scheme"));
  Scheme = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Scheme", {
  enumerable: true,
  get: function () {
    return Scheme();
  }
});
function Target() {
  const data = _interopRequireWildcard(require("./Target"));
  Target = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Target", {
  enumerable: true,
  get: function () {
    return Target();
  }
});
function Updates() {
  const data = _interopRequireWildcard(require("./Updates"));
  Updates = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Updates", {
  enumerable: true,
  get: function () {
    return Updates();
  }
});
function UsesNonExemptEncryption() {
  const data = _interopRequireWildcard(require("./UsesNonExemptEncryption"));
  UsesNonExemptEncryption = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "UsesNonExemptEncryption", {
  enumerable: true,
  get: function () {
    return UsesNonExemptEncryption();
  }
});
function Version() {
  const data = _interopRequireWildcard(require("./Version"));
  Version = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Version", {
  enumerable: true,
  get: function () {
    return Version();
  }
});
function XcodeProjectFile() {
  const data = _interopRequireWildcard(require("./XcodeProjectFile"));
  XcodeProjectFile = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "XcodeProjectFile", {
  enumerable: true,
  get: function () {
    return XcodeProjectFile();
  }
});
function XcodeUtils() {
  const data = _interopRequireWildcard(require("./utils/Xcodeproj"));
  XcodeUtils = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "XcodeUtils", {
  enumerable: true,
  get: function () {
    return XcodeUtils();
  }
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
//# sourceMappingURL=index.js.map