{"version": 3, "names": ["Easing", "<PERSON><PERSON><PERSON><PERSON>", "withSequence", "withTiming", "ReduceMotion", "assertEasingIsWorklet", "getReduceMotionFromConfig", "ReanimatedError", "InnerKeyframe", "reduceMotionV", "System", "constructor", "definitions", "parseDefinitions", "parsedKeyframes", "from", "to", "initialValues", "Object", "keys", "for<PERSON>ach", "styleProp", "Array", "isArray", "transform", "transformStyle", "index", "transformProp", "makeKey<PERSON>ey", "duration", "durationV", "animationKeyPoints", "map", "Number", "getAnimationDuration", "key", "currentKeyPoint", "maxDuration", "currentDuration", "reduce", "acc", "value", "addKeyPoint", "easing", "__DEV__", "push", "filter", "sort", "a", "b", "keyPoint", "keyframe", "addKeyPointWith", "keyframes", "durationMs", "delay", "delayMs", "delayV", "<PERSON><PERSON><PERSON><PERSON>", "callback", "callbackV", "reduceMotion", "getDelayFunction", "animation", "_", "build", "delayFunction", "animations", "addAnimation", "keyframePoints", "length", "linear", "keyframePoint", "includes", "split", "transformPropKey", "Keyframe"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/animationBuilder/Keyframe.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,iBAAc;AACrC,SAASC,SAAS,EAAEC,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAerE,SAASC,YAAY,QAAQ,sBAAmB;AAChD,SACEC,qBAAqB,EACrBC,yBAAyB,QACpB,yBAAsB;AAC7B,SAASC,eAAe,QAAQ,iBAAc;AAY9C,MAAMC,aAAa,CAAuC;EAGxDC,aAAa,GAAiBL,YAAY,CAACM,MAAM;EAIjD;AACF;AACA;AACA;EACEC,WAAWA,CAACC,WAA+B,EAAE;IAC3C,IAAI,CAACA,WAAW,GAAGA,WAAwC;EAC7D;EAEQC,gBAAgBA,CAAA,EAA8B;IACpD;AACJ;AACA;AACA;IACI,MAAMC,eAAgD,GAAG,CAAC,CAAC;IAC3D;AACJ;AACA;IACI,IAAI,IAAI,CAACF,WAAW,CAACG,IAAI,EAAE;MACzB,IAAI,IAAI,CAACH,WAAW,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIL,eAAe,CACvB,sFACF,CAAC;MACH;MACA,IAAI,CAACK,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,CAACG,IAAI;MAC7C,OAAO,IAAI,CAACH,WAAW,CAACG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACH,WAAW,CAACI,EAAE,EAAE;MACvB,IAAI,IAAI,CAACJ,WAAW,CAAC,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAIL,eAAe,CACvB,0GACF,CAAC;MACH;MACA,IAAI,CAACK,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAACA,WAAW,CAACI,EAAE;MAC7C,OAAO,IAAI,CAACJ,WAAW,CAACI,EAAE;IAC5B;IACA;AACJ;AACA;AACA;IACI,IAAI,CAAC,IAAI,CAACJ,WAAW,CAAC,GAAG,CAAC,EAAE;MAC1B,MAAM,IAAIL,eAAe,CACvB,wEACF,CAAC;IACH;IACA,MAAMU,aAAyB,GAAG,IAAI,CAACL,WAAW,CAAC,GAAG,CAAe;IACrE;AACJ;AACA;IACIM,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACG,OAAO,CAAEC,SAAiB,IAAK;MACxD,IAAIA,SAAS,KAAK,WAAW,EAAE;QAC7B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACN,aAAa,CAACO,SAAS,CAAC,EAAE;UAC3C;QACF;QACAP,aAAa,CAACO,SAAS,CAACJ,OAAO,CAAC,CAACK,cAAc,EAAEC,KAAK,KAAK;UACzDR,MAAM,CAACC,IAAI,CAACM,cAAc,CAAC,CAACL,OAAO,CAAEO,aAAqB,IAAK;YAC7Db,eAAe,CAACc,eAAe,CAACF,KAAK,EAAEC,aAAa,CAAC,CAAC,GAAG,EAAE;UAC7D,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLb,eAAe,CAACO,SAAS,CAAC,GAAG,EAAE;MACjC;IACF,CAAC,CAAC;IAEF,MAAMQ,QAAgB,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG;IAC9D,MAAMC,kBAAiC,GAAGT,KAAK,CAACP,IAAI,CAClDG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,WAAW,CAC9B,CAAC,CAACoB,GAAG,CAACC,MAAM,CAAC;IAEb,MAAMC,oBAAoB,GAAGA,CAC3BC,GAAW,EACXC,eAAuB,KACZ;MACX,MAAMC,WAAW,GAAID,eAAe,GAAG,GAAG,GAAIP,QAAQ;MACtD,MAAMS,eAAe,GAAGxB,eAAe,CAACqB,GAAG,CAAC,CAACI,MAAM,CACjD,CAACC,GAAW,EAAEC,KAAoB,KAAKD,GAAG,GAAGC,KAAK,CAACZ,QAAQ,EAC3D,CACF,CAAC;MACD,OAAOQ,WAAW,GAAGC,eAAe;IACtC,CAAC;;IAED;AACJ;AACA;IACI,MAAMI,WAAW,GAAGA,CAAC;MACnBP,GAAG;MACHM,KAAK;MACLL,eAAe;MACfO;IAMF,CAAC,KAAW;MACV,IAAI,EAAER,GAAG,IAAIrB,eAAe,CAAC,EAAE;QAC7B,MAAM,IAAIP,eAAe,CACvB,+GACF,CAAC;MACH;MAEA,IAAIqC,OAAO,IAAID,MAAM,EAAE;QACrBtC,qBAAqB,CAACsC,MAAM,CAAC;MAC/B;MAEA7B,eAAe,CAACqB,GAAG,CAAC,CAACU,IAAI,CAAC;QACxBhB,QAAQ,EAAEK,oBAAoB,CAACC,GAAG,EAAEC,eAAe,CAAC;QACpDK,KAAK;QACLE;MACF,CAAC,CAAC;IACJ,CAAC;IACDZ,kBAAkB,CACfe,MAAM,CAAEL,KAAa,IAAKA,KAAK,KAAK,CAAC,CAAC,CACtCM,IAAI,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACrC7B,OAAO,CAAE8B,QAAgB,IAAK;MAC7B,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,GAAG,EAAE;QAClC,MAAM,IAAI3C,eAAe,CACvB,8CACF,CAAC;MACH;MACA,MAAM4C,QAAuB,GAAG,IAAI,CAACvC,WAAW,CAACsC,QAAQ,CAAC;MAC1D,MAAMP,MAAM,GAAGQ,QAAQ,CAACR,MAAM;MAC9B,OAAOQ,QAAQ,CAACR,MAAM;MACtB,MAAMS,eAAe,GAAGA,CAACjB,GAAW,EAAEM,KAAsB,KAC1DC,WAAW,CAAC;QACVP,GAAG;QACHM,KAAK;QACLL,eAAe,EAAEc,QAAQ;QACzBP;MACF,CAAC,CAAC;MACJzB,MAAM,CAACC,IAAI,CAACgC,QAAQ,CAAC,CAAC/B,OAAO,CAAEe,GAAW,IAAK;QAC7C,IAAIA,GAAG,KAAK,WAAW,EAAE;UACvB,IAAI,CAACb,KAAK,CAACC,OAAO,CAAC4B,QAAQ,CAAC3B,SAAS,CAAC,EAAE;YACtC;UACF;UACA2B,QAAQ,CAAC3B,SAAS,CAACJ,OAAO,CAAC,CAACK,cAAc,EAAEC,KAAK,KAAK;YACpDR,MAAM,CAACC,IAAI,CAACM,cAAc,CAAC,CAACL,OAAO,CAAEO,aAAqB,IAAK;cAC7DyB,eAAe,CACbxB,eAAe,CAACF,KAAK,EAAEC,aAAa,CAAC,EACrCF,cAAc,CACZE,aAAa,CACd,CAAoB;cACrB;cACF,CAAC;YACH,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLyB,eAAe,CAACjB,GAAG,EAAEgB,QAAQ,CAAChB,GAAG,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ,OAAO;MAAElB,aAAa;MAAEoC,SAAS,EAAEvC;IAAgB,CAAC;EACtD;EAEAe,QAAQA,CAACyB,UAAkB,EAAiB;IAC1C,IAAI,CAACxB,SAAS,GAAGwB,UAAU;IAC3B,OAAO,IAAI;EACb;EAEAC,KAAKA,CAACC,OAAe,EAAiB;IACpC,IAAI,CAACC,MAAM,GAAGD,OAAO;IACrB,OAAO,IAAI;EACb;EAEAE,YAAYA,CAACC,QAAqC,EAAiB;IACjE,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAO,IAAI;EACb;EAEAE,YAAYA,CAACpD,aAA2B,EAAQ;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;EAEQqD,gBAAgBA,CAAA,EAAsB;IAC5C,MAAMP,KAAK,GAAG,IAAI,CAACE,MAAM;IACzB,MAAMI,YAAY,GAAG,IAAI,CAACpD,aAAa;IACvC,OAAO8C,KAAK;IACR;IACA,CAACA,KAAK,EAAEQ,SAAS,KAAK;MACpB,SAAS;;MACT,OAAO9D,SAAS,CAACsD,KAAK,EAAEQ,SAAS,EAAEF,YAAY,CAAC;IAClD,CAAC,GACD,CAACG,CAAC,EAAED,SAAS,KAAK;MAChB,SAAS;;MACTA,SAAS,CAACF,YAAY,GAAGvD,yBAAyB,CAACuD,YAAY,CAAC;MAChE,OAAOE,SAAS;IAClB,CAAC;EACP;EAEAE,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMV,KAAK,GAAG,IAAI,CAACE,MAAM;IACzB,MAAMS,aAAa,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IAC7C,MAAM;MAAET,SAAS;MAAEpC;IAAc,CAAC,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IAC5D,MAAM8C,QAAQ,GAAG,IAAI,CAACC,SAAS;IAE/B,OAAO,MAAM;MACX,SAAS;;MACT,MAAMO,UAAwC,GAAG,CAAC,CAAC;;MAEnD;AACN;AACA;AACA;MACM,MAAMC,YAAY,GAAIjC,GAAW,IAAK;QACpC,MAAMkC,cAAc,GAAGhB,SAAS,CAAClB,GAAG,CAAC;QACrC;QACA,IAAIkC,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;UAC/B;QACF;QACA,MAAMP,SAAS,GAAGG,aAAa,CAC7BX,KAAK,EACLc,cAAc,CAACC,MAAM,KAAK,CAAC,GACvBnE,UAAU,CAACkE,cAAc,CAAC,CAAC,CAAC,CAAC5B,KAAK,EAAE;UAClCZ,QAAQ,EAAEwC,cAAc,CAAC,CAAC,CAAC,CAACxC,QAAQ;UACpCc,MAAM,EAAE0B,cAAc,CAAC,CAAC,CAAC,CAAC1B,MAAM,GAC5B0B,cAAc,CAAC,CAAC,CAAC,CAAC1B,MAAM,GACxB3C,MAAM,CAACuE;QACb,CAAC,CAAC,GACFrE,YAAY,CACV,GAAGmE,cAAc,CAACrC,GAAG,CAAEwC,aAA4B,IACjDrE,UAAU,CAACqE,aAAa,CAAC/B,KAAK,EAAE;UAC9BZ,QAAQ,EAAE2C,aAAa,CAAC3C,QAAQ;UAChCc,MAAM,EAAE6B,aAAa,CAAC7B,MAAM,GACxB6B,aAAa,CAAC7B,MAAM,GACpB3C,MAAM,CAACuE;QACb,CAAC,CACH,CACF,CACN,CAAC;QACD,IAAIpC,GAAG,CAACsC,QAAQ,CAAC,WAAW,CAAC,EAAE;UAC7B,IAAI,EAAE,WAAW,IAAIN,UAAU,CAAC,EAAE;YAChCA,UAAU,CAAC3C,SAAS,GAAG,EAAE;UAC3B;UACA2C,UAAU,CAAC3C,SAAS,CAAEqB,IAAI,CAAqB;YAC7C,CAACV,GAAG,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGX;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLI,UAAU,CAAChC,GAAG,CAAC,GAAG4B,SAAS;QAC7B;MACF,CAAC;MACD7C,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACG,OAAO,CAAEe,GAAW,IAAK;QAClD,IAAIA,GAAG,CAACsC,QAAQ,CAAC,WAAW,CAAC,EAAE;UAC7BxD,aAAa,CAACkB,GAAG,CAAC,CAACf,OAAO,CACxB,CAACO,aAA8C,EAAED,KAAa,KAAK;YACjER,MAAM,CAACC,IAAI,CAACQ,aAAa,CAAC,CAACP,OAAO,CAAEuD,gBAAwB,IAAK;cAC/DP,YAAY,CAACxC,eAAe,CAACF,KAAK,EAAEiD,gBAAgB,CAAC,CAAC;YACxD,CAAC,CAAC;UACJ,CACF,CAAC;QACH,CAAC,MAAM;UACLP,YAAY,CAACjC,GAAG,CAAC;QACnB;MACF,CAAC,CAAC;MACF,OAAO;QACLgC,UAAU;QACVlD,aAAa;QACb0C;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;AAEA,SAAS/B,eAAeA,CAACF,KAAa,EAAEC,aAAqB,EAAE;EAC7D,SAAS;;EACT,OAAO,GAAGD,KAAK,cAAcC,aAAa,EAAE;AAC9C;AAUA,OAAO,MAAMiD,QAAQ,GAAGpE,aAA0C", "ignoreList": []}