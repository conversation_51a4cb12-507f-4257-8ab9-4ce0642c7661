{"version": 3, "sources": ["../../../../src/utils/telemetry/events.ts"], "sourcesContent": ["import type { TelemetryRecord } from './types';\n\n/** A single command invocation, with the invoked command name */\nexport function commandEvent(commandName: string): TelemetryRecord {\n  return {\n    event: 'action',\n    properties: {\n      action: `expo ${commandName}`,\n    },\n  };\n}\n"], "names": ["commandEvent", "commandName", "event", "properties", "action"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;AAAT,SAASA,aAAaC,WAAmB;IAC9C,OAAO;QACLC,OAAO;QACPC,YAAY;YACVC,QAAQ,CAAC,KAAK,EAAEH,aAAa;QAC/B;IACF;AACF"}