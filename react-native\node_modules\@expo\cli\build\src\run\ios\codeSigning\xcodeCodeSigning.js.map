{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/xcodeCodeSigning.ts"], "sourcesContent": ["import { IOSConfig, XcodeProject } from '@expo/config-plugins';\nimport fs from 'fs';\n\nexport type CodeSigningInfo = Record<\n  string,\n  {\n    developmentTeams: string[];\n    provisioningProfiles: string[];\n  }\n>;\n\n/** Find the development team and provisioning profile that's currently in use by the Xcode project. */\nexport function getCodeSigningInfoForPbxproj(projectRoot: string): CodeSigningInfo {\n  const project = IOSConfig.XcodeUtils.getPbxproj(projectRoot);\n  const targets = IOSConfig.Target.findSignableTargets(project);\n\n  const signingInfo: CodeSigningInfo = {};\n  for (const [nativeTargetId, nativeTarget] of targets) {\n    const developmentTeams: string[] = [];\n    const provisioningProfiles: string[] = [];\n\n    IOSConfig.XcodeUtils.getBuildConfigurationsForListId(\n      project,\n      nativeTarget.buildConfigurationList\n    )\n      .filter(\n        ([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) =>\n          item.buildSettings.PRODUCT_NAME\n      )\n      .forEach(([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) => {\n        const { DEVELOPMENT_TEAM, PROVISIONING_PROFILE } = item.buildSettings;\n        if (\n          typeof DEVELOPMENT_TEAM === 'string' &&\n          // If the user selects \"Team: none\" in Xcode, it'll be an empty string.\n          !!DEVELOPMENT_TEAM &&\n          // xcode package sometimes reads an empty string as a quoted empty string.\n          DEVELOPMENT_TEAM !== '\"\"'\n        ) {\n          developmentTeams.push(DEVELOPMENT_TEAM);\n        }\n        if (typeof PROVISIONING_PROFILE === 'string' && !!PROVISIONING_PROFILE) {\n          provisioningProfiles.push(PROVISIONING_PROFILE);\n        }\n      });\n    signingInfo[nativeTargetId] = {\n      developmentTeams,\n      provisioningProfiles,\n    };\n  }\n\n  return signingInfo;\n}\n\n/**\n * Set the development team and configure the Xcode project for automatic code signing,\n * this helps us resolve the code signing on subsequent runs and emulates Xcode behavior.\n *\n * @param props.project xcode project object from `xcode` package.\n * @param props.appleTeamId Apple Team ID to use for code signing.\n */\nexport function mutateXcodeProjectWithAutoCodeSigningInfo({\n  project,\n  appleTeamId,\n}: {\n  project: XcodeProject;\n  appleTeamId: string;\n}): XcodeProject {\n  const targets = IOSConfig.Target.findSignableTargets(project);\n\n  const quotedAppleTeamId = ensureQuotes(appleTeamId);\n\n  for (const [nativeTargetId, nativeTarget] of targets) {\n    IOSConfig.XcodeUtils.getBuildConfigurationsForListId(\n      project,\n      nativeTarget.buildConfigurationList\n    )\n      .filter(\n        ([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) =>\n          item.buildSettings.PRODUCT_NAME\n      )\n      .forEach(([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) => {\n        item.buildSettings.DEVELOPMENT_TEAM = quotedAppleTeamId;\n        item.buildSettings.CODE_SIGN_IDENTITY = '\"Apple Development\"';\n        item.buildSettings.CODE_SIGN_STYLE = 'Automatic';\n      });\n\n    Object.entries(IOSConfig.XcodeUtils.getProjectSection(project))\n      .filter(IOSConfig.XcodeUtils.isNotComment)\n      .forEach(([, item]: IOSConfig.XcodeUtils.ProjectSectionEntry) => {\n        if (!item.attributes.TargetAttributes) {\n          item.attributes.TargetAttributes = {};\n        }\n\n        if (!item.attributes.TargetAttributes[nativeTargetId]) {\n          item.attributes.TargetAttributes[nativeTargetId] = {};\n        }\n\n        item.attributes.TargetAttributes[nativeTargetId].DevelopmentTeam = quotedAppleTeamId;\n        item.attributes.TargetAttributes[nativeTargetId].ProvisioningStyle = 'Automatic';\n      });\n  }\n\n  return project;\n}\n\n/**\n * Configures the Xcode project for automatic code signing and persists the results.\n */\nexport function setAutoCodeSigningInfoForPbxproj(\n  projectRoot: string,\n  { appleTeamId }: { appleTeamId: string }\n): void {\n  const project = IOSConfig.XcodeUtils.getPbxproj(projectRoot);\n  mutateXcodeProjectWithAutoCodeSigningInfo({ project, appleTeamId });\n\n  fs.writeFileSync(project.filepath, project.writeSync());\n}\n\nconst ensureQuotes = (value: string) => {\n  if (!value.match(/^['\"]/)) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n"], "names": ["getCodeSigningInfoForPbxproj", "mutateXcodeProjectWithAutoCodeSigningInfo", "setAutoCodeSigningInfoForPbxproj", "projectRoot", "project", "IOSConfig", "XcodeUtils", "getPbxproj", "targets", "Target", "findSignableTargets", "signingInfo", "nativeTargetId", "nativeTarget", "developmentTeams", "provisioningProfiles", "getBuildConfigurationsForListId", "buildConfigurationList", "filter", "item", "buildSettings", "PRODUCT_NAME", "for<PERSON>ach", "DEVELOPMENT_TEAM", "PROVISIONING_PROFILE", "push", "appleTeamId", "quotedAppleTeamId", "ensureQuotes", "CODE_SIGN_IDENTITY", "CODE_SIGN_STYLE", "Object", "entries", "getProjectSection", "isNotComment", "attributes", "TargetAttributes", "DevelopmentTeam", "ProvisioningStyle", "fs", "writeFileSync", "filepath", "writeSync", "value", "match"], "mappings": ";;;;;;;;;;;IAYgBA,4BAA4B;eAA5BA;;IAgDAC,yCAAyC;eAAzCA;;IAgDAC,gCAAgC;eAAhCA;;;;yBA5GwB;;;;;;;gEACzB;;;;;;;;;;;AAWR,SAASF,6BAA6BG,WAAmB;IAC9D,MAAMC,UAAUC,0BAAS,CAACC,UAAU,CAACC,UAAU,CAACJ;IAChD,MAAMK,UAAUH,0BAAS,CAACI,MAAM,CAACC,mBAAmB,CAACN;IAErD,MAAMO,cAA+B,CAAC;IACtC,KAAK,MAAM,CAACC,gBAAgBC,aAAa,IAAIL,QAAS;QACpD,MAAMM,mBAA6B,EAAE;QACrC,MAAMC,uBAAiC,EAAE;QAEzCV,0BAAS,CAACC,UAAU,CAACU,+BAA+B,CAClDZ,SACAS,aAAaI,sBAAsB,EAElCC,MAAM,CACL,CAAC,GAAGC,KAAqD,GACvDA,KAAKC,aAAa,CAACC,YAAY,EAElCC,OAAO,CAAC,CAAC,GAAGH,KAAqD;YAChE,MAAM,EAAEI,gBAAgB,EAAEC,oBAAoB,EAAE,GAAGL,KAAKC,aAAa;YACrE,IACE,OAAOG,qBAAqB,YAC5B,uEAAuE;YACvE,CAAC,CAACA,oBACF,0EAA0E;YAC1EA,qBAAqB,MACrB;gBACAT,iBAAiBW,IAAI,CAACF;YACxB;YACA,IAAI,OAAOC,yBAAyB,YAAY,CAAC,CAACA,sBAAsB;gBACtET,qBAAqBU,IAAI,CAACD;YAC5B;QACF;QACFb,WAAW,CAACC,eAAe,GAAG;YAC5BE;YACAC;QACF;IACF;IAEA,OAAOJ;AACT;AASO,SAASV,0CAA0C,EACxDG,OAAO,EACPsB,WAAW,EAIZ;IACC,MAAMlB,UAAUH,0BAAS,CAACI,MAAM,CAACC,mBAAmB,CAACN;IAErD,MAAMuB,oBAAoBC,aAAaF;IAEvC,KAAK,MAAM,CAACd,gBAAgBC,aAAa,IAAIL,QAAS;QACpDH,0BAAS,CAACC,UAAU,CAACU,+BAA+B,CAClDZ,SACAS,aAAaI,sBAAsB,EAElCC,MAAM,CACL,CAAC,GAAGC,KAAqD,GACvDA,KAAKC,aAAa,CAACC,YAAY,EAElCC,OAAO,CAAC,CAAC,GAAGH,KAAqD;YAChEA,KAAKC,aAAa,CAACG,gBAAgB,GAAGI;YACtCR,KAAKC,aAAa,CAACS,kBAAkB,GAAG;YACxCV,KAAKC,aAAa,CAACU,eAAe,GAAG;QACvC;QAEFC,OAAOC,OAAO,CAAC3B,0BAAS,CAACC,UAAU,CAAC2B,iBAAiB,CAAC7B,UACnDc,MAAM,CAACb,0BAAS,CAACC,UAAU,CAAC4B,YAAY,EACxCZ,OAAO,CAAC,CAAC,GAAGH,KAA+C;YAC1D,IAAI,CAACA,KAAKgB,UAAU,CAACC,gBAAgB,EAAE;gBACrCjB,KAAKgB,UAAU,CAACC,gBAAgB,GAAG,CAAC;YACtC;YAEA,IAAI,CAACjB,KAAKgB,UAAU,CAACC,gBAAgB,CAACxB,eAAe,EAAE;gBACrDO,KAAKgB,UAAU,CAACC,gBAAgB,CAACxB,eAAe,GAAG,CAAC;YACtD;YAEAO,KAAKgB,UAAU,CAACC,gBAAgB,CAACxB,eAAe,CAACyB,eAAe,GAAGV;YACnER,KAAKgB,UAAU,CAACC,gBAAgB,CAACxB,eAAe,CAAC0B,iBAAiB,GAAG;QACvE;IACJ;IAEA,OAAOlC;AACT;AAKO,SAASF,iCACdC,WAAmB,EACnB,EAAEuB,WAAW,EAA2B;IAExC,MAAMtB,UAAUC,0BAAS,CAACC,UAAU,CAACC,UAAU,CAACJ;IAChDF,0CAA0C;QAAEG;QAASsB;IAAY;IAEjEa,aAAE,CAACC,aAAa,CAACpC,QAAQqC,QAAQ,EAAErC,QAAQsC,SAAS;AACtD;AAEA,MAAMd,eAAe,CAACe;IACpB,IAAI,CAACA,MAAMC,KAAK,CAAC,UAAU;QACzB,OAAO,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC;IACrB;IACA,OAAOA;AACT"}