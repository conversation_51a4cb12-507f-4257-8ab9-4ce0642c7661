{"version": 3, "names": ["_classApplyDescriptorSet", "receiver", "descriptor", "value", "set", "call", "writable", "TypeError"], "sources": ["../../src/helpers/classApplyDescriptorSet.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nexport default function _classApplyDescriptorSet(receiver, descriptor, value) {\n  if (descriptor.set) {\n    descriptor.set.call(receiver, value);\n  } else {\n    if (!descriptor.writable) {\n      // This should only throw in strict mode, but class bodies are\n      // always strict and private fields can only be used inside\n      // class bodies.\n      throw new TypeError(\"attempted to set read only private field\");\n    }\n    descriptor.value = value;\n  }\n}\n"], "mappings": ";;;;;;AAGe,SAASA,wBAAwBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC5E,IAAID,UAAU,CAACE,GAAG,EAAE;IAClBF,UAAU,CAACE,GAAG,CAACC,IAAI,CAACJ,QAAQ,EAAEE,KAAK,CAAC;EACtC,CAAC,MAAM;IACL,IAAI,CAACD,UAAU,CAACI,QAAQ,EAAE;MAIxB,MAAM,IAAIC,SAAS,CAAC,0CAA0C,CAAC;IACjE;IACAL,UAAU,CAACC,KAAK,GAAGA,KAAK;EAC1B;AACF", "ignoreList": []}