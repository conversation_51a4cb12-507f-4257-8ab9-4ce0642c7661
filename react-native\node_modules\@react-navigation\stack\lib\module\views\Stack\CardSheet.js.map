{"version": 3, "names": ["React", "StyleSheet", "View", "forwardRef", "CardSheet", "ref", "enabled", "layout", "style", "rest", "fill", "setFill", "useState", "pointerEvents", "setPointerEvents", "useImperativeHandle", "useEffect", "document", "body", "width", "clientWidth", "height", "clientHeight", "styles", "page", "card", "create", "minHeight", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/Stack/CardSheet.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAY1D;AACA;AACA;AACA;AACA,4BAAeF,KAAK,CAACG,UAAU,CAAsB,SAASC,SAAS,OAErEC,GAAG,EACH;EAAA,IAFA;IAAEC,OAAO;IAAEC,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAK,CAAC;EAGnC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAAC,KAAK,CAAC;EAC7C;EACA;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GACrCd,KAAK,CAACY,QAAQ,CAA6B,MAAM,CAAC;EAEpDZ,KAAK,CAACe,mBAAmB,CAACV,GAAG,EAAE,MAAM;IACnC,OAAO;MAAES;IAAiB,CAAC;EAC7B,CAAC,CAAC;EAEFd,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MACrD;MACA;IACF;IAEA,MAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACE,WAAW;IACvC,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,IAAI,CAACI,YAAY;IAEzCX,OAAO,CAACQ,KAAK,KAAKZ,MAAM,CAACY,KAAK,IAAIE,MAAM,KAAKd,MAAM,CAACc,MAAM,CAAC;EAC7D,CAAC,EAAE,CAACd,MAAM,CAACc,MAAM,EAAEd,MAAM,CAACY,KAAK,CAAC,CAAC;EAEjC,oBACE,oBAAC,IAAI,eACCV,IAAI;IACR,aAAa,EAAEI,aAAc;IAC7B,KAAK,EAAE,CAACP,OAAO,IAAII,IAAI,GAAGa,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAEjB,KAAK;EAAE,GAC5D;AAEN,CAAC,CAAC;AAEF,MAAMe,MAAM,GAAGtB,UAAU,CAACyB,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb,CAAC;EACDF,IAAI,EAAE;IACJG,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}