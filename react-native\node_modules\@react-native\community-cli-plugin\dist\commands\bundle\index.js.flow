/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { Command } from "@react-native-community/cli-types";

export type { BundleCommandArgs } from "./buildBundle";

declare const bundleCommand: Command;

declare export default typeof bundleCommand;
