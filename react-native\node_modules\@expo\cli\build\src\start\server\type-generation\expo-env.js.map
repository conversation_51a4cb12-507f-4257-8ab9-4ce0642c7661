{"version": 3, "sources": ["../../../../../src/start/server/type-generation/expo-env.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport path from 'path';\n\nconst template = `/// <reference types=\"expo/types\" />\n\n// NOTE: This file should not be edited and should be in your git ignore`;\n\nexport async function writeExpoEnvDTS(projectRoot: string) {\n  return fs.writeFile(path.join(projectRoot, 'expo-env.d.ts'), template);\n}\n\nexport async function removeExpoEnvDTS(projectRoot: string) {\n  // Force removal of expo-env.d.ts - Ignore any errors if the file does not exist\n  return fs.rm(path.join(projectRoot, 'expo-env.d.ts'), { force: true });\n}\n"], "names": ["removeExpoEnvDTS", "writeExpoEnvDTS", "template", "projectRoot", "fs", "writeFile", "path", "join", "rm", "force"], "mappings": ";;;;;;;;;;;IAWsBA,gBAAgB;eAAhBA;;IAJAC,eAAe;eAAfA;;;;gEAPP;;;;;;;gEACE;;;;;;;;;;;AAEjB,MAAMC,WAAW,CAAC;;wEAEsD,CAAC;AAElE,eAAeD,gBAAgBE,WAAmB;IACvD,OAAOC,mBAAE,CAACC,SAAS,CAACC,eAAI,CAACC,IAAI,CAACJ,aAAa,kBAAkBD;AAC/D;AAEO,eAAeF,iBAAiBG,WAAmB;IACxD,gFAAgF;IAChF,OAAOC,mBAAE,CAACI,EAAE,CAACF,eAAI,CAACC,IAAI,CAACJ,aAAa,kBAAkB;QAAEM,OAAO;IAAK;AACtE"}