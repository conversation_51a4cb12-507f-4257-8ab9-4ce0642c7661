{"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "useBackPressSubscription", "_ref", "onBackPress", "isDisabled", "isActive", "setIsActive", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "arguments", "length", "undefined", "current", "remove", "createSubscription", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useBackPressSubscription.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAiC,cAAc;AAcnE;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAAAC,IAAA,EAGL;EAAA,IAHM;IACvCC,WAAW;IACXC;EACI,CAAC,GAAAF,IAAA;EACL,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMC,YAAY,GAAGT,KAAK,CAACU,MAAM,CAAsC,CAAC;EAExE,MAAMC,iBAAiB,GAAGX,KAAK,CAACY,WAAW,CAAC,YAA4B;IAAA,IAA3BC,eAAe,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACjEL,YAAY,CAACQ,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC9BT,YAAY,CAACQ,OAAO,GAAGD,SAAS;IAChC,IAAIH,eAAe,EAAEN,WAAW,CAAC,KAAK,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,kBAAkB,GAAGnB,KAAK,CAACY,WAAW,CAAC,MAAM;IACjD,IAAI,CAACP,UAAU,EAAE;MACfI,YAAY,CAACQ,OAAO,EAAEC,MAAM,CAAC,CAAC;MAC9BT,YAAY,CAACQ,OAAO,GAAGhB,WAAW,CAACmB,gBAAgB,CACjD,mBAAmB,EACnBhB,WACF,CAAC;MACDG,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACF,UAAU,EAAED,WAAW,CAAC,CAAC;EAE7B,MAAMiB,cAAc,GAAGrB,KAAK,CAACY,WAAW,CAAC,MAAM;IAC7C,IAAIN,QAAQ,EAAE;MACZa,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEb,QAAQ,CAAC,CAAC;EAElC,MAAMgB,cAAc,GAAGtB,KAAK,CAACY,WAAW,CAAC,MAAM;IAC7CD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvBX,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,IAAIlB,UAAU,EAAE;MACdM,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACN,UAAU,EAAEM,iBAAiB,CAAC,CAAC;EAEnC,OAAO;IACLU,cAAc;IACdC,cAAc;IACdH,kBAAkB;IAClBR;EACF,CAAC;AACH"}