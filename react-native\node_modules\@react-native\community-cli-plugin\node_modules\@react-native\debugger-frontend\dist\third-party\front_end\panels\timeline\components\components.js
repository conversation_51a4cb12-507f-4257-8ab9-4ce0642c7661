import*as e from"../../../models/trace/trace.js";import*as t from"../../../ui/components/helpers/helpers.js";import*as a from"../../../ui/components/icon_button/icon_button.js";import*as r from"../../../ui/lit-html/lit-html.js";import*as n from"../../../core/i18n/i18n.js";import*as s from"../../../core/platform/platform.js";import*as o from"../../../ui/legacy/legacy.js";function i(e){const t=[e];let a=e;for(;null!==a.child;){const e=a.child;null!==e&&(t.push(e),a=e)}return t}var c=Object.freeze({__proto__:null,flattenBreadcrumbs:i,Breadcrumbs:class{initialBreadcrumb;lastBreadcrumb;constructor(e){this.initialBreadcrumb={window:e,child:null},this.lastBreadcrumb=this.initialBreadcrumb}add(e){if(!this.isTraceWindowWithinTraceWindow(e,this.lastBreadcrumb.window))throw new Error("Can not add a breadcrumb that is equal to or is outside of the parent breadcrumb TimeWindow");{const t={window:e,child:null};this.lastBreadcrumb.child=t,this.lastBreadcrumb=this.lastBreadcrumb.child}}isTraceWindowWithinTraceWindow(e,t){return e.min>=t.min&&e.max<=t.max&&!(e.min===t.min&&e.max===t.max)}makeBreadcrumbActive(e){this.lastBreadcrumb=e,this.lastBreadcrumb.child=null}}});const d=new CSSStyleSheet;d.replaceSync(".breadcrumbs{display:none;align-items:center;height:29px;padding:3px;overflow-y:hidden;overflow-x:scroll}.breadcrumbs::-webkit-scrollbar{display:none}.breadcrumb{padding:2px 6px;border-radius:4px}.breadcrumb:hover{background-color:var(--sys-color-state-hover-on-subtle)}.range{font-size:12px;white-space:nowrap}.last-breadcrumb{font-weight:bold;color:var(--app-color-active-breadcrumb)}\n/*# sourceURL=breadcrumbsUI.css */\n");const{render:l,html:u}=r;class m extends Event{breadcrumb;static eventName="breadcrumbremoved";constructor(e){super(m.eventName),this.breadcrumb=e}}class b extends HTMLElement{static litTagName=r.literal`devtools-breadcrumbs-ui`;#e=this.attachShadow({mode:"open"});#t=this.#a.bind(this);#r=null;connectedCallback(){this.#e.adoptedStyleSheets=[d]}set data(e){this.#r=e.breadcrumb,t.ScheduledRender.scheduleRender(this,this.#t)}#n(e){this.dispatchEvent(new m(e))}#s(){const e=this.#e.querySelector(".breadcrumbs");e&&(e.style.display="flex",requestAnimationFrame((()=>{e.scrollWidth-e.clientWidth>0&&requestAnimationFrame((()=>{e.scrollLeft=e.scrollWidth-e.clientWidth}))})))}#o(t,r){const n=e.Helpers.Timing.microSecondsToMilliseconds(t.window.range);return u`
          <div class="breadcrumb" @click=${()=>this.#n(t)}>
           <span class="${0!==r&&null===t.child?"last-breadcrumb":""} range">
            ${0===r?`Full range (${n.toFixed(2)}ms)`:`${n.toFixed(2)}ms`}
            </span>
          </div>
          ${null!==t.child?u`
            <${a.Icon.Icon.litTagName} .data=${{iconName:"chevron-right",color:"var(--icon-default)",width:"16px",height:"16px"}}>`:""}
      `}#a(){const e=u`
      ${null===this.#r?u``:u`<div class="breadcrumbs">
        ${i(this.#r).map(((e,t)=>this.#o(e,t)))}
      </div>`}
    `;l(e,this.#e,{host:this}),this.#r?.child&&this.#s()}}customElements.define("devtools-breadcrumbs-ui",b);var h=Object.freeze({__proto__:null,BreadcrumbRemovedEvent:m,BreadcrumbsUI:b});const p={forcedReflow:"Forced reflow",sIsALikelyPerformanceBottleneck:"{PH1} is a likely performance bottleneck.",idleCallbackExecutionExtended:"Idle callback execution extended beyond deadline by {PH1}",sTookS:"{PH1} took {PH2}.",longTask:"Long task",longInteractionINP:"Long interaction",sIsLikelyPoorPageResponsiveness:"{PH1} is indicating poor page responsiveness.",websocketProtocol:"WebSocket Protocol"},g=n.i18n.registerUIStrings("panels/timeline/components/DetailsView.ts",p),v=n.i18n.getLocalizedString.bind(void 0,g);var w=Object.freeze({__proto__:null,buildWarningElementsForEvent:function(t,a){const r=a.Warnings.perEvent.get(t),i=[];if(!r)return i;for(const a of r){const r=e.Helpers.Timing.microSecondsToMilliseconds(e.Types.Timing.MicroSeconds(t.dur||0)),c=document.createElement("span");switch(a){case"FORCED_REFLOW":{const e=o.XLink.XLink.create("https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts",v(p.forcedReflow),void 0,void 0,"forced-reflow");c.appendChild(n.i18n.getFormatLocalizedString(g,p.sIsALikelyPerformanceBottleneck,{PH1:e}));break}case"IDLE_CALLBACK_OVER_TIME":{if(!e.Types.TraceEvents.isTraceEventFireIdleCallback(t))break;const a=n.TimeUtilities.millisToString((r||0)-t.args.data.allottedMilliseconds,!0);c.textContent=v(p.idleCallbackExecutionExtended,{PH1:a});break}case"LONG_TASK":{const e=o.XLink.XLink.create("https://web.dev/optimize-long-tasks/",v(p.longTask),void 0,void 0,"long-tasks");c.appendChild(n.i18n.getFormatLocalizedString(g,p.sTookS,{PH1:e,PH2:n.TimeUtilities.millisToString(r||0,!0)}));break}case"LONG_INTERACTION":{const e=o.XLink.XLink.create("https://web.dev/inp",v(p.longInteractionINP),void 0,void 0,"long-interaction");c.appendChild(n.i18n.getFormatLocalizedString(g,p.sIsLikelyPoorPageResponsiveness,{PH1:e}));break}default:s.assertNever(a,`Unhandled warning type ${a}`)}i.push(c)}return i},buildRowsForWebSocketEvent:function(t,a){const r=[],s=a.Initiators.eventToInitiator.get(t);return s&&e.Types.TraceEvents.isTraceEventWebSocketCreate(s)?(r.push({key:n.i18n.lockedString("URL"),value:s.args.data.url}),s.args.data.websocketProtocol&&r.push({key:v(p.websocketProtocol),value:s.args.data.websocketProtocol})):e.Types.TraceEvents.isTraceEventWebSocketCreate(t)&&(r.push({key:n.i18n.lockedString("URL"),value:t.args.data.url}),t.args.data.websocketProtocol&&r.push({key:v(p.websocketProtocol),value:t.args.data.websocketProtocol})),r},generateInvalidationsList:function(t){const a={},r=new Set;for(const n of t){r.add(n.nodeId);let t=n.reason||"unknown";if("unknown"===t&&e.Types.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(n.rawEvent)&&n.rawEvent.args.data.invalidatedSelectorId)switch(n.rawEvent.args.data.invalidatedSelectorId){case"attribute":t="Attribute",n.rawEvent.args.data.changedAttribute&&(t+=` (${n.rawEvent.args.data.changedAttribute})`);break;case"class":t="Class",n.rawEvent.args.data.changedClass&&(t+=` (${n.rawEvent.args.data.changedClass})`);break;case"id":t="Id",n.rawEvent.args.data.changedId&&(t+=` (${n.rawEvent.args.data.changedId})`)}if("PseudoClass"===t&&e.Types.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(n.rawEvent)&&n.rawEvent.args.data.extraData&&(t+=n.rawEvent.args.data.extraData),"Attribute"===t&&e.Types.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(n.rawEvent)&&n.rawEvent.args.data.extraData&&(t+=` (${n.rawEvent.args.data.extraData})`),"StyleInvalidator"===t)continue;const s=a[t]||[];s.push(n),a[t]=s}return{groupedByReason:a,backendNodeIds:r}}});const k=new CSSStyleSheet;k.replaceSync(":host{display:block}.breakdown{margin:0;padding:0;list-style:none;color:var(--sys-color-token-subtle)}.value{display:inline-block;padding:0 5px;color:var(--sys-color-on-surface)}\n/*# sourceURL=interactionBreakdown.css */\n");const y={inputDelay:"Input delay",processingTime:"Processing time",presentationDelay:"Presentation delay"},T=n.i18n.registerUIStrings("panels/timeline/components/InteractionBreakdown.ts",y),f=n.i18n.getLocalizedString.bind(void 0,T);class E extends HTMLElement{static litTagName=r.literal`devtools-interaction-breakdown`;#e=this.attachShadow({mode:"open"});#t=this.#a.bind(this);#i=null;connectedCallback(){this.#e.adoptedStyleSheets=[k]}set entry(e){e!==this.#i&&(this.#i=e,t.ScheduledRender.scheduleRender(this,this.#t))}#a(){if(!this.#i)return;const t=e.Helpers.Timing.formatMicrosecondsTime(this.#i.inputDelay),a=e.Helpers.Timing.formatMicrosecondsTime(this.#i.mainThreadHandling),n=e.Helpers.Timing.formatMicrosecondsTime(this.#i.presentationDelay);r.render(r.html`<ul class="breakdown">
                     <li data-entry="input-delay">${f(y.inputDelay)}<span class="value">${t}</span></li>
                     <li data-entry="processing-time">${f(y.processingTime)}<span class="value">${a}</span></li>
                     <li data-entry="presentation-delay">${f(y.presentationDelay)}<span class="value">${n}</span></li>
                   </ul>
                   `,this.#e,{host:this})}}customElements.define("devtools-interaction-breakdown",E);var S=Object.freeze({__proto__:null,InteractionBreakdown:E});export{c as Breadcrumbs,h as BreadcrumbsUI,w as DetailsView,S as InteractionBreakdown};
