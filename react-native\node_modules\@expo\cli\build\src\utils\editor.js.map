{"version": 3, "sources": ["../../../src/utils/editor.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport editors from 'env-editor';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:editor') as typeof console.log;\n\n/** Guess what the default editor is and default to VSCode. */\nexport function guessEditor(): editors.Editor {\n  try {\n    const editor = env.EXPO_EDITOR;\n    if (editor) {\n      debug('Using $EXPO_EDITOR:', editor);\n      return editors.getEditor(editor);\n    }\n    debug('Falling back on $EDITOR:', editor);\n    return editors.defaultEditor();\n  } catch {\n    debug('Falling back on vscode');\n    return editors.getEditor('vscode');\n  }\n}\n\n/** Open a file path in a given editor. */\nexport async function openInEditorAsync(path: string, lineNumber?: number): Promise<boolean> {\n  const editor = guessEditor();\n  const fileReference = lineNumber ? `${path}:${lineNumber}` : path;\n\n  debug(`Opening ${fileReference} in ${editor?.name} (bin: ${editor?.binary}, id: ${editor?.id})`);\n\n  if (editor) {\n    try {\n      await spawnAsync(editor.binary, getEditorArguments(editor, path, lineNumber));\n      return true;\n    } catch (error: any) {\n      debug(\n        `Failed to open ${fileReference} in editor (path: ${path}, binary: ${editor.binary}):`,\n        error\n      );\n    }\n  }\n\n  Log.error(\n    'Could not open editor, you can set it by defining the $EDITOR environment variable with the binary of your editor. (e.g. \"vscode\" or \"atom\")'\n  );\n  return false;\n}\n\nfunction getEditorArguments(editor: editors.Editor, path: string, lineNumber?: number): string[] {\n  if (!lineNumber) {\n    return [path];\n  }\n\n  switch (editor.id) {\n    case 'atom':\n    case 'sublime':\n      return [`${path}:${lineNumber}`];\n\n    case 'emacs':\n    case 'emacsforosx':\n    case 'nano':\n    case 'neovim':\n    case 'vim':\n      return [`+${lineNumber}`, path];\n\n    case 'android-studio':\n    case 'intellij':\n    case 'textmate':\n    case 'webstorm':\n    case 'xcode':\n      return [`--line=${lineNumber}`, path];\n\n    case 'vscode':\n    case 'vscodium':\n      return ['-g', `${path}:${lineNumber}`];\n\n    default:\n      return [path];\n  }\n}\n"], "names": ["guessEditor", "openInEditorAsync", "debug", "require", "editor", "env", "EXPO_EDITOR", "editors", "getEditor", "defaultEditor", "path", "lineNumber", "fileReference", "name", "binary", "id", "spawnAsync", "getEditorArguments", "error", "Log"], "mappings": ";;;;;;;;;;;IASgBA,WAAW;eAAXA;;IAgBMC,iBAAiB;eAAjBA;;;;gEAzBC;;;;;;;gEACH;;;;;;qBAEA;6DACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASH;IACd,IAAI;QACF,MAAMI,SAASC,QAAG,CAACC,WAAW;QAC9B,IAAIF,QAAQ;YACVF,MAAM,uBAAuBE;YAC7B,OAAOG,oBAAO,CAACC,SAAS,CAACJ;QAC3B;QACAF,MAAM,4BAA4BE;QAClC,OAAOG,oBAAO,CAACE,aAAa;IAC9B,EAAE,OAAM;QACNP,MAAM;QACN,OAAOK,oBAAO,CAACC,SAAS,CAAC;IAC3B;AACF;AAGO,eAAeP,kBAAkBS,IAAY,EAAEC,UAAmB;IACvE,MAAMP,SAASJ;IACf,MAAMY,gBAAgBD,aAAa,GAAGD,KAAK,CAAC,EAAEC,YAAY,GAAGD;IAE7DR,MAAM,CAAC,QAAQ,EAAEU,cAAc,IAAI,EAAER,0BAAAA,OAAQS,IAAI,CAAC,OAAO,EAAET,0BAAAA,OAAQU,MAAM,CAAC,MAAM,EAAEV,0BAAAA,OAAQW,EAAE,CAAC,CAAC,CAAC;IAE/F,IAAIX,QAAQ;QACV,IAAI;YACF,MAAMY,IAAAA,qBAAU,EAACZ,OAAOU,MAAM,EAAEG,mBAAmBb,QAAQM,MAAMC;YACjE,OAAO;QACT,EAAE,OAAOO,OAAY;YACnBhB,MACE,CAAC,eAAe,EAAEU,cAAc,kBAAkB,EAAEF,KAAK,UAAU,EAAEN,OAAOU,MAAM,CAAC,EAAE,CAAC,EACtFI;QAEJ;IACF;IAEAC,KAAID,KAAK,CACP;IAEF,OAAO;AACT;AAEA,SAASD,mBAAmBb,MAAsB,EAAEM,IAAY,EAAEC,UAAmB;IACnF,IAAI,CAACA,YAAY;QACf,OAAO;YAACD;SAAK;IACf;IAEA,OAAQN,OAAOW,EAAE;QACf,KAAK;QACL,KAAK;YACH,OAAO;gBAAC,GAAGL,KAAK,CAAC,EAAEC,YAAY;aAAC;QAElC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAC,CAAC,CAAC,EAAEA,YAAY;gBAAED;aAAK;QAEjC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;gBAAC,CAAC,OAAO,EAAEC,YAAY;gBAAED;aAAK;QAEvC,KAAK;QACL,KAAK;YACH,OAAO;gBAAC;gBAAM,GAAGA,KAAK,CAAC,EAAEC,YAAY;aAAC;QAExC;YACE,OAAO;gBAACD;aAAK;IACjB;AACF"}