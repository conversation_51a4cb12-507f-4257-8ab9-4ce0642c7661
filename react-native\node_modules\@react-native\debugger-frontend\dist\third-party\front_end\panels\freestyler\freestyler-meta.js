import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";const o="Show Freestyler",i="Freestyler",r="Enable Freestyler",a="Ask Freestyler",s=t.i18n.lockedLazyString,l="freestyler-enabled";let c;async function y(){return c||(c=await import("./freestyler.js")),c}function g(e){return!0===e?.devToolsFreestylerDogfood?.enabled}n.ViewManager.registerViewExtension({location:"drawer-view",id:"freestyler",commandPrompt:s(o),title:s(i),order:10,persistence:"closeable",hasToolbar:!1,condition:g,loadView:async()=>(await y()).FreestylerPanel.instance()}),e.Settings.registerSettingExtension({category:"GLOBAL",settingName:l,settingType:"boolean",title:s(r),defaultValue:g,reloadRequired:!0,condition:g}),n.ActionRegistration.registerActionExtension({actionId:"freestyler.element-panel-context",contextTypes:()=>[],setting:l,category:"GLOBAL",title:s(a),loadActionDelegate:async()=>new((await y()).ActionDelegate),condition:g}),n.ActionRegistration.registerActionExtension({actionId:"freestyler.style-tab-context",contextTypes:()=>[],setting:l,category:"GLOBAL",title:s(a),iconClass:"spark",loadActionDelegate:async()=>new((await y()).ActionDelegate),condition:g});
