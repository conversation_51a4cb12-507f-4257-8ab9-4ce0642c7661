<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Sélection Client et Contrat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #2563eb;
        }
        .status {
            margin-top: 8px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background-color: #f0fdf4; color: #059669; border: 1px solid #10b981; }
        .info { background-color: #f0f8ff; color: #3b82f6; border: 1px solid #3b82f6; }
        .error { background-color: #fef2f2; color: #dc2626; border: 1px solid #dc2626; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test - Sélection Client et Contrat</h1>
    
    <div class="test-container">
        <h2>📋 Formulaire de Test</h2>
        
        <div class="form-group">
            <label>Client *</label>
            <select id="clientSelect">
                <option value="">Sélectionner un client</option>
            </select>
            <button type="button" onclick="showClientList()">📋 Sélectionner depuis la liste</button>
        </div>
        
        <div class="form-group">
            <label>Contrat *</label>
            <select id="contractSelect" disabled>
                <option value="">Sélectionnez d'abord un client</option>
            </select>
            <div id="contractStatus"></div>
        </div>
        
        <div class="form-group">
            <label>Consommation Précédente (m³)</label>
            <input type="number" id="prevConsumption" readonly placeholder="Auto-rempli depuis la base">
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 Scénarios de Test</h2>
        <button onclick="testClientWithOneContract()">Test: Client avec 1 contrat</button>
        <button onclick="testClientWithMultipleContracts()">Test: Client avec plusieurs contrats</button>
        <button onclick="testClientWithNoContract()">Test: Client sans contrat</button>
    </div>
    
    <div class="test-container">
        <h2>📝 Journal des Tests</h2>
        <div id="testLog" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        let clients = [];
        let filteredContracts = [];
        
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        async function loadClients() {
            try {
                log('🔄 Chargement des clients...');
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (data.success) {
                    clients = data.data;
                    const clientSelect = document.getElementById('clientSelect');
                    clientSelect.innerHTML = '<option value="">Sélectionner un client</option>';
                    
                    clients.forEach(client => {
                        const option = document.createElement('option');
                        option.value = client.idclient;
                        option.textContent = `${client.nom} ${client.prenom} - ${client.ville}`;
                        clientSelect.appendChild(option);
                    });
                    
                    log(`✅ ${clients.length} clients chargés`);
                } else {
                    log('❌ Erreur lors du chargement des clients');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function handleClientChange(clientId) {
            log(`🔄 Client sélectionné: ${clientId}`);
            
            const contractSelect = document.getElementById('contractSelect');
            const contractStatus = document.getElementById('contractStatus');
            const prevConsumption = document.getElementById('prevConsumption');
            
            if (!clientId) {
                contractSelect.innerHTML = '<option value="">Sélectionnez d\'abord un client</option>';
                contractSelect.disabled = true;
                contractStatus.innerHTML = '';
                prevConsumption.value = '';
                return;
            }
            
            try {
                log(`🔍 Récupération des contrats pour le client ${clientId}...`);
                const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    filteredContracts = data.data;
                    contractSelect.innerHTML = '';
                    contractSelect.disabled = false;
                    
                    log(`📋 ${filteredContracts.length} contrat(s) trouvé(s)`);
                    
                    if (filteredContracts.length === 0) {
                        // Aucun contrat
                        contractSelect.innerHTML = '<option value="">Ce client n\'a pas de contrat</option>';
                        contractSelect.disabled = true;
                        contractSelect.style.border = '2px solid #dc2626';
                        contractSelect.style.backgroundColor = '#fef2f2';
                        contractStatus.innerHTML = '<div class="status error">❌ Ce client n\'a pas de contrat</div>';
                        log('❌ Aucun contrat trouvé');
                        
                    } else if (filteredContracts.length === 1) {
                        // Un seul contrat - sélection automatique
                        const contract = filteredContracts[0];
                        contractSelect.innerHTML = `<option value="${contract.idcontract}" selected>
                            ${contract.codeqr} - Contrat #${contract.idcontract}
                        </option>`;
                        contractSelect.disabled = true;
                        contractSelect.style.border = '2px solid #10b981';
                        contractSelect.style.backgroundColor = '#f0fdf4';
                        contractStatus.innerHTML = '<div class="status success">✅ Contrat unique affiché automatiquement</div>';
                        
                        // Récupérer la dernière consommation
                        await fetchLastConsommation(contract.idcontract);
                        log(`✅ Contrat unique auto-sélectionné: ${contract.codeqr}`);
                        
                    } else {
                        // Plusieurs contrats - sélection manuelle
                        contractSelect.innerHTML = '<option value="">Sélectionner un contrat</option>';
                        filteredContracts.forEach(contract => {
                            const option = document.createElement('option');
                            option.value = contract.idcontract;
                            option.textContent = `${contract.codeqr} - Contrat #${contract.idcontract}`;
                            contractSelect.appendChild(option);
                        });
                        contractSelect.style.border = '2px solid #3b82f6';
                        contractSelect.style.backgroundColor = '#f0f8ff';
                        contractStatus.innerHTML = `<div class="status info">📋 ${filteredContracts.length} contrats disponibles - Sélectionnez-en un</div>`;
                        log(`📋 ${filteredContracts.length} contrats disponibles pour sélection`);
                    }
                } else {
                    log('❌ Erreur dans la réponse API');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function fetchLastConsommation(contractId) {
            try {
                log(`🔍 Récupération de la dernière consommation pour le contrat ${contractId}...`);
                const response = await fetch(`${API_BASE_URL}/api/contracts/${contractId}/last-consommation`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        const prevConsumption = document.getElementById('prevConsumption');
                        prevConsumption.value = data.data.consommationactuelle;
                        log(`✅ Dernière consommation: ${data.data.consommationactuelle} m³`);
                    } else {
                        log('ℹ️ Aucune consommation précédente trouvée');
                    }
                } else {
                    log('⚠️ Erreur lors de la récupération de la dernière consommation');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        function showClientList() {
            log('📋 Ouverture de la liste des clients...');
            alert('Dans l\'application React, ceci ouvrirait la page ListesClients.js');
        }
        
        // Tests automatiques
        async function testClientWithOneContract() {
            log('\n🧪 TEST: Client avec un seul contrat');
            // Simuler la sélection d'un client avec un contrat
            const clientWithOneContract = clients.find(c => c.nom === 'Benali');
            if (clientWithOneContract) {
                document.getElementById('clientSelect').value = clientWithOneContract.idclient;
                await handleClientChange(clientWithOneContract.idclient);
            } else {
                log('⚠️ Client de test non trouvé');
            }
        }
        
        async function testClientWithMultipleContracts() {
            log('\n🧪 TEST: Client avec plusieurs contrats');
            // Trouver un client avec plusieurs contrats
            for (let client of clients) {
                const response = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/contracts`);
                const data = await response.json();
                if (data.success && data.data && data.data.length > 1) {
                    document.getElementById('clientSelect').value = client.idclient;
                    await handleClientChange(client.idclient);
                    return;
                }
            }
            log('⚠️ Aucun client avec plusieurs contrats trouvé');
        }
        
        async function testClientWithNoContract() {
            log('\n🧪 TEST: Client sans contrat');
            // Trouver un client sans contrat
            for (let client of clients) {
                const response = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/contracts`);
                const data = await response.json();
                if (data.success && (!data.data || data.data.length === 0)) {
                    document.getElementById('clientSelect').value = client.idclient;
                    await handleClientChange(client.idclient);
                    return;
                }
            }
            log('⚠️ Aucun client sans contrat trouvé');
        }
        
        // Event listeners
        document.getElementById('clientSelect').addEventListener('change', (e) => {
            handleClientChange(e.target.value);
        });
        
        document.getElementById('contractSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                fetchLastConsommation(e.target.value);
                const contractStatus = document.getElementById('contractStatus');
                contractStatus.innerHTML = '<div class="status success">✅ Contrat sélectionné</div>';
            }
        });
        
        // Initialisation
        loadClients();
        log('🚀 Test de sélection client/contrat initialisé');
    </script>
</body>
</html>
