const http = require('http');
const url = require('url');
const { Pool } = require('pg');

console.log('🚀 Démarrage du serveur de contrats...');

// Configuration PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Test de connexion
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL réussie');
    const result = await client.query('SELECT COUNT(*) FROM contract');
    console.log(`📊 ${result.rows[0].count} contrats dans la base`);
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur connexion:', error.message);
    return false;
  }
}

function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data, null, 2));
}

const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📥 ${new Date().toISOString()} - ${method} ${path}`);

  // Gérer CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Route de test
  if (path === '/' && method === 'GET') {
    console.log('✅ Route / appelée');
    sendJSON(res, 200, {
      message: 'Serveur contrats fonctionnel',
      timestamp: new Date().toISOString(),
      database: 'Facutration'
    });
    return;
  }

  // Route clients
  if (path === '/api/clients' && method === 'GET') {
    try {
      console.log('📥 GET /api/clients');
      
      const query = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          COALESCE(s.nom, 'Non défini') as secteur_nom
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        ORDER BY c.nom, c.prenom
      `;

      const result = await pool.query(query);
      console.log(`✅ ${result.rows.length} clients récupérés`);

      sendJSON(res, 200, {
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} client(s) trouvé(s)`
      });
    } catch (error) {
      console.error('❌ Erreur clients:', error.message);
      sendJSON(res, 500, {
        success: false,
        message: 'Erreur lors de la récupération des clients',
        error: error.message
      });
    }
    return;
  }

  // Route contrats d'un client - NOUVELLE IMPLEMENTATION
  const contractsMatch = path.match(/^\/api\/clients\/(\d+)\/contracts$/);
  if (contractsMatch && method === 'GET') {
    try {
      const clientId = contractsMatch[1];
      console.log(`📥 GET /api/clients/${clientId}/contracts`);
      console.log(`🔍 Recherche des contrats pour le client ID: ${clientId}`);

      // Requête pour récupérer les contrats du client depuis la table Contract
      const query = `
        SELECT
          c.idcontract,
          c.codeqr,
          c.datecontract,
          c.idclient,
          c.marquecompteur,
          c.numseriecompteur,
          c.posx,
          c.posy,
          cl.nom,
          cl.prenom,
          cl.adresse,
          cl.ville
        FROM contract c
        INNER JOIN client cl ON c.idclient = cl.idclient
        WHERE c.idclient = $1
        ORDER BY c.datecontract DESC
      `;

      console.log('📡 Exécution requête SQL...');
      const result = await pool.query(query, [clientId]);

      console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`);
      
      if (result.rows.length > 0) {
        console.log('📋 Contrats trouvés:');
        result.rows.forEach((contract, index) => {
          console.log(`   ${index + 1}. ID: ${contract.idcontract} - QR: ${contract.codeqr} - Marque: ${contract.marquecompteur || 'Non définie'}`);
        });
      } else {
        console.log('⚠️ Aucun contrat trouvé pour ce client');
      }

      const response = {
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`,
        client_id: parseInt(clientId)
      };

      console.log('📤 Envoi réponse...');
      sendJSON(res, 200, response);

    } catch (error) {
      console.error('❌ Erreur contrats:', error.message);
      sendJSON(res, 500, {
        success: false,
        message: 'Erreur lors de la récupération des contrats',
        error: error.message
      });
    }
    return;
  }

  // Route dernière consommation
  const consommationMatch = path.match(/^\/api\/contracts\/(\d+)\/last-consommation$/);
  if (consommationMatch && method === 'GET') {
    try {
      const contractId = consommationMatch[1];
      console.log(`📥 GET /api/contracts/${contractId}/last-consommation`);

      const query = `
        SELECT 
          consommationactuelle,
          periode,
          jours
        FROM consommation 
        WHERE idcont = $1 
        ORDER BY periode DESC 
        LIMIT 1
      `;

      const result = await pool.query(query, [contractId]);

      if (result.rows.length > 0) {
        console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
        sendJSON(res, 200, {
          success: true,
          data: result.rows[0],
          message: 'Dernière consommation trouvée'
        });
      } else {
        console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${contractId}`);
        sendJSON(res, 200, {
          success: false,
          message: 'Aucune consommation précédente trouvée'
        });
      }
    } catch (error) {
      console.error('❌ Erreur consommation:', error.message);
      sendJSON(res, 500, {
        success: false,
        message: 'Erreur lors de la récupération de la consommation',
        error: error.message
      });
    }
    return;
  }

  // Route non trouvée
  console.log(`❌ Route non trouvée: ${method} ${path}`);
  sendJSON(res, 404, {
    success: false,
    message: 'Route non trouvée',
    url: path
  });
});

const PORT = 3002;

async function startServer() {
  console.log('🔄 Test de connexion à la base...');
  const connected = await testConnection();
  
  if (!connected) {
    console.error('❌ Impossible de se connecter à la base');
    process.exit(1);
  }

  server.listen(PORT, () => {
    console.log(`\n🚀 Serveur démarré sur http://localhost:${PORT}`);
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client)');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('\n✅ Prêt à recevoir les requêtes !');
  });
}

startServer();

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err.message);
});
