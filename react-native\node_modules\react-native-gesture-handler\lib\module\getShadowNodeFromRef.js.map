{"version": 3, "sources": ["getShadowNodeFromRef.ts"], "names": ["findHostInstance_DEPRECATED", "getInternalInstanceHandleFromPublicInstance", "getShadowNodeFromRef", "ref", "undefined", "require", "e", "_ref", "_internalInstanceHandle", "stateNode", "node"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,2BAAJ;AACA,IAAIC,2CAAJ;AAIA,OAAO,SAASC,oBAAT,CAA8BC,GAA9B,EAA4C;AACjD;AACA,MAAIH,2BAA2B,KAAKI,SAApC,EAA+C;AAC7C,QAAI;AACF;AACAJ,MAAAA,2BAA2B,GACzB;AACAK,MAAAA,OAAO,CAAC,mDAAD,CAAP,CAA6DL,2BAF/D;AAGD,KALD,CAKE,OAAOM,CAAP,EAAU;AACVN,MAAAA,2BAA2B,GAAIO,IAAD,IAAmB,IAAjD;AACD;AACF,GAXgD,CAajD;;;AACA,MAAIN,2CAA2C,KAAKG,SAApD,EAA+D;AAC7D,QAAI;AAAA;;AACF;AACAH,MAAAA,2CAA2C,GACzC;AADyC,+BAEzCI,OAAO,CAAC,wFAAD,CAAP,CACGJ,2CAHsC,yEAIzC;AACEE,MAAAA,GAAD,IAAcA,GAAG,CAACK,uBALrB;AAMD,KARD,CAQE,OAAOF,CAAP,EAAU;AACVL,MAAAA,2CAA2C,GAAIE,GAAD,IAC5C;AACAA,MAAAA,GAAG,CAACK,uBAFN;AAGD;AACF,GA5BgD,CA8BjD;;;AACA,SAAOP,2CAA2C,CAChDD,2BAA2B,CAACG,GAAD,CADqB,CAA3C,CAELM,SAFK,CAEKC,IAFZ;AAGD", "sourcesContent": ["// Used by G<PERSON>ure<PERSON>ete<PERSON> (unsupported on web at the moment) to check whether the\n// attached view may get flattened on <PERSON>abric. This implementation causes errors\n// on web due to the static resolution of `require` statements by webpack breaking\n// the conditional importing. Solved by making .web file.\nlet findHostInstance_DEPRECATED: (ref: unknown) => void;\nlet getInternalInstanceHandleFromPublicInstance: (ref: unknown) => {\n  stateNode: { node: unknown };\n};\n\nexport function getShadowNodeFromRef(ref: unknown) {\n  // Load findHostInstance_DEPRECATED lazily because it may not be available before render\n  if (findHostInstance_DEPRECATED === undefined) {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      findHostInstance_DEPRECATED =\n        // eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-unsafe-member-access\n        require('react-native/Libraries/Renderer/shims/ReactFabric').findHostInstance_DEPRECATED;\n    } catch (e) {\n      findHostInstance_DEPRECATED = (_ref: unknown) => null;\n    }\n  }\n\n  // Load findHostInstance_DEPRECATED lazily because it may not be available before render\n  if (getInternalInstanceHandleFromPublicInstance === undefined) {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      getInternalInstanceHandleFromPublicInstance =\n        // eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-unsafe-member-access\n        require('react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactFabricPublicInstance')\n          .getInternalInstanceHandleFromPublicInstance ??\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return\n        ((ref: any) => ref._internalInstanceHandle);\n    } catch (e) {\n      getInternalInstanceHandleFromPublicInstance = (ref: any) =>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return\n        ref._internalInstanceHandle;\n    }\n  }\n\n  // @ts-ignore Fabric\n  return getInternalInstanceHandleFromPublicInstance(\n    findHostInstance_DEPRECATED(ref)\n  ).stateNode.node;\n}\n"]}