{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "ExportNamedDeclaration", "path", "_exported$name", "node", "scope", "specifiers", "index", "t", "isExportDefaultSpecifier", "isExportNamespaceSpecifier", "nodes", "exportNamedDeclaration", "shift", "source", "specifier", "exported", "uid", "generateUidIdentifier", "value", "importDeclaration", "importNamespaceSpecifier", "cloneNode", "exportSpecifier", "length", "replaceWithMultiple", "registerDeclaration"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-export-namespace-from\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"exportNamespaceFrom\"),\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const { node, scope } = path;\n        const { specifiers } = node;\n\n        const index = t.isExportDefaultSpecifier(specifiers[0]) ? 1 : 0;\n        if (!t.isExportNamespaceSpecifier(specifiers[index])) return;\n\n        const nodes = [];\n\n        if (index === 1) {\n          nodes.push(\n            t.exportNamedDeclaration(null, [specifiers.shift()], node.source),\n          );\n        }\n\n        const specifier = specifiers.shift();\n        const { exported } = specifier;\n        const uid = scope.generateUidIdentifier(\n          // @ts-expect-error Identifier ?? StringLiteral\n          exported.name ?? exported.value,\n        );\n\n        nodes.push(\n          t.importDeclaration(\n            [t.importNamespaceSpecifier(uid)],\n            t.cloneNode(node.source),\n          ),\n          t.exportNamedDeclaration(null, [\n            t.exportSpecifier(t.cloneNode(uid), exported),\n          ]),\n        );\n\n        if (node.specifiers.length >= 1) {\n          nodes.push(node);\n        }\n\n        const [importDeclaration] = path.replaceWithMultiple(nodes);\n        path.scope.registerDeclaration(importDeclaration);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,iCAAiC;IACvCC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,qBAAqB,CAAC;IAE7DC,OAAO,EAAE;MACPC,sBAAsBA,CAACC,IAAI,EAAE;QAAA,IAAAC,cAAA;QAC3B,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGH,IAAI;QAC5B,MAAM;UAAEI;QAAW,CAAC,GAAGF,IAAI;QAE3B,MAAMG,KAAK,GAAGC,WAAC,CAACC,wBAAwB,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/D,IAAI,CAACE,WAAC,CAACE,0BAA0B,CAACJ,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE;QAEtD,MAAMI,KAAK,GAAG,EAAE;QAEhB,IAAIJ,KAAK,KAAK,CAAC,EAAE;UACfI,KAAK,CAACZ,IAAI,CACRS,WAAC,CAACI,sBAAsB,CAAC,IAAI,EAAE,CAACN,UAAU,CAACO,KAAK,CAAC,CAAC,CAAC,EAAET,IAAI,CAACU,MAAM,CAClE,CAAC;QACH;QAEA,MAAMC,SAAS,GAAGT,UAAU,CAACO,KAAK,CAAC,CAAC;QACpC,MAAM;UAAEG;QAAS,CAAC,GAAGD,SAAS;QAC9B,MAAME,GAAG,GAAGZ,KAAK,CAACa,qBAAqB,EAAAf,cAAA,GAErCa,QAAQ,CAACtB,IAAI,YAAAS,cAAA,GAAIa,QAAQ,CAACG,KAC5B,CAAC;QAEDR,KAAK,CAACZ,IAAI,CACRS,WAAC,CAACY,iBAAiB,CACjB,CAACZ,WAAC,CAACa,wBAAwB,CAACJ,GAAG,CAAC,CAAC,EACjCT,WAAC,CAACc,SAAS,CAAClB,IAAI,CAACU,MAAM,CACzB,CAAC,EACDN,WAAC,CAACI,sBAAsB,CAAC,IAAI,EAAE,CAC7BJ,WAAC,CAACe,eAAe,CAACf,WAAC,CAACc,SAAS,CAACL,GAAG,CAAC,EAAED,QAAQ,CAAC,CAC9C,CACH,CAAC;QAED,IAAIZ,IAAI,CAACE,UAAU,CAACkB,MAAM,IAAI,CAAC,EAAE;UAC/Bb,KAAK,CAACZ,IAAI,CAACK,IAAI,CAAC;QAClB;QAEA,MAAM,CAACgB,iBAAiB,CAAC,GAAGlB,IAAI,CAACuB,mBAAmB,CAACd,KAAK,CAAC;QAC3DT,IAAI,CAACG,KAAK,CAACqB,mBAAmB,CAACN,iBAAiB,CAAC;MACnD;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}