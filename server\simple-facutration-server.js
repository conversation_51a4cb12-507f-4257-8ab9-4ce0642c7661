const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

console.log('🚀 Démarrage du serveur Facutration...');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'Connected'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients - Récupération depuis la table Client');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom,
        c.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} clients récupérés depuis la table Client`);
    
    // Log des premiers clients
    if (result.rows.length > 0) {
      console.log('📋 Premiers clients:');
      result.rows.slice(0, 3).forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
      });
    }

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s) dans la base Facutration`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients depuis la base Facutration',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}/contracts`);
    console.log(`🔍 Recherche des contrats pour le client ID: ${id}`);

    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        COALESCE(c.marquecompteur, 'Non définie') as marquecompteur,
        cl.nom,
        cl.prenom
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL...');
    console.log('SQL:', query);
    console.log('Paramètre:', [id]);

    const result = await pool.query(query, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    if (result.rows.length > 0) {
      console.log('📋 Contrats trouvés:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur}`);
        console.log(`      Client: ${contract.nom} ${contract.prenom}`);
        console.log(`      Date contrat: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}`);
      });
    } else {
      console.log('⚠️ Aucun contrat trouvé pour ce client');
    }

    const response = {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
      client_id: parseInt(id)
    };

    console.log('📤 Envoi de la réponse:', JSON.stringify(response, null, 2));
    res.json(response);

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error.message);
    console.error('❌ Stack trace:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée pour le contrat ${id}: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.message);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Test de connexion à la base de données au démarrage
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données "Facutration" réussie');
    
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`📊 ${result.rows[0].count} clients disponibles dans la table Client`);
    
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    return false;
  }
}

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  // Tester la connexion DB
  const dbConnected = await testDatabaseConnection();
  
  if (!dbConnected) {
    console.error('❌ Impossible de se connecter à la base de données');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur Facutration démarré sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (tous les clients)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client)');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('\n✅ Prêt à recevoir les requêtes depuis React !');
    console.log('🔍 Tous les appels API seront loggés dans ce terminal');
  });
}

startServer();

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err.message);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err.message);
});
