{"version": 3, "sources": ["../../../../src/start/interface/startInterface.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './KeyPressHandler';\nimport { BLT, printHelp, printUsage, StartOptions } from './commandsTable';\nimport { DevServerManagerActions } from './interactiveActions';\nimport * as Log from '../../log';\nimport { openInEditorAsync } from '../../utils/editor';\nimport { AbortCommandError } from '../../utils/errors';\nimport { getAllSpinners, ora } from '../../utils/ora';\nimport { getProgressBar, setProgressBar } from '../../utils/progress';\nimport { addInteractionListener, pauseInteractions } from '../../utils/prompts';\nimport { WebSupportProjectPrerequisite } from '../doctor/web/WebSupportProjectPrerequisite';\nimport { DevServerManager } from '../server/DevServerManager';\n\nconst debug = require('debug')('expo:start:interface:startInterface') as typeof console.log;\n\nconst CTRL_C = '\\u0003';\nconst CTRL_D = '\\u0004';\nconst CTRL_L = '\\u000C';\n\nconst PLATFORM_SETTINGS: Record<\n  string,\n  { name: string; key: 'android' | 'ios'; launchTarget: 'emulator' | 'simulator' }\n> = {\n  android: {\n    name: 'Android',\n    key: 'android',\n    launchTarget: 'emulator',\n  },\n  ios: {\n    name: 'iOS',\n    key: 'ios',\n    launchTarget: 'simulator',\n  },\n};\n\nexport async function startInterfaceAsync(\n  devServerManager: DevServerManager,\n  options: Pick<StartOptions, 'devClient' | 'platforms'>\n) {\n  const actions = new DevServerManagerActions(devServerManager, options);\n\n  const isWebSocketsEnabled = devServerManager.getDefaultDevServer()?.isTargetingNative();\n\n  const usageOptions = {\n    isWebSocketsEnabled,\n    devClient: devServerManager.options.devClient,\n    ...options,\n  };\n\n  actions.printDevServerInfo(usageOptions);\n\n  const onPressAsync = async (key: string) => {\n    // Auxillary commands all escape.\n    switch (key) {\n      case CTRL_C:\n      case CTRL_D: {\n        // Prevent terminal UI from accepting commands while the process is closing.\n        // Without this, fast typers will close the server then start typing their\n        // next command and have a bunch of unrelated things pop up.\n        pauseInteractions();\n\n        const spinners = getAllSpinners();\n        spinners.forEach((spinner) => {\n          spinner.fail();\n        });\n\n        const currentProgress = getProgressBar();\n        if (currentProgress) {\n          currentProgress.terminate();\n          setProgressBar(null);\n        }\n        const spinner = ora({ text: 'Stopping server', color: 'white' }).start();\n        try {\n          await devServerManager.stopAsync();\n          spinner.stopAndPersist({ text: 'Stopped server', symbol: `\\u203A` });\n          // @ts-ignore: Argument of type '\"SIGINT\"' is not assignable to parameter of type '\"disconnect\"'.\n          process.emit('SIGINT');\n\n          // TODO: Is this the right place to do this?\n          process.exit();\n        } catch (error) {\n          spinner.fail('Failed to stop server');\n          throw error;\n        }\n        break;\n      }\n      case CTRL_L:\n        return Log.clear();\n      case '?':\n        return printUsage(usageOptions, { verbose: true });\n    }\n\n    // Optionally enabled\n\n    if (isWebSocketsEnabled) {\n      switch (key) {\n        case 'm':\n          return actions.toggleDevMenu();\n        case 'M':\n          return actions.openMoreToolsAsync();\n      }\n    }\n\n    const { platforms = ['ios', 'android', 'web'] } = options;\n\n    if (['i', 'a'].includes(key.toLowerCase())) {\n      const platform = key.toLowerCase() === 'i' ? 'ios' : 'android';\n\n      const shouldPrompt = ['I', 'A'].includes(key);\n      if (shouldPrompt) {\n        Log.clear();\n      }\n\n      const server = devServerManager.getDefaultDevServer();\n      const settings = PLATFORM_SETTINGS[platform];\n\n      Log.log(`${BLT} Opening on ${settings.name}...`);\n\n      if (server.isTargetingNative() && !platforms.includes(settings.key)) {\n        Log.warn(\n          chalk`${settings.name} is disabled, enable it by adding {bold ${settings.key}} to the platforms array in your app.json or app.config.js`\n        );\n      } else {\n        try {\n          await server.openPlatformAsync(settings.launchTarget, { shouldPrompt });\n          printHelp();\n        } catch (error: any) {\n          if (!(error instanceof AbortCommandError)) {\n            Log.exception(error);\n          }\n        }\n      }\n      // Break out early.\n      return;\n    }\n\n    switch (key) {\n      case 's': {\n        Log.clear();\n        if (await devServerManager.toggleRuntimeMode()) {\n          usageOptions.devClient = devServerManager.options.devClient;\n          return actions.printDevServerInfo(usageOptions);\n        }\n        break;\n      }\n      case 'w': {\n        try {\n          await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n          if (!platforms.includes('web')) {\n            platforms.push('web');\n            options.platforms?.push('web');\n          }\n        } catch (e: any) {\n          Log.warn(e.message);\n          break;\n        }\n\n        const isDisabled = !platforms.includes('web');\n        if (isDisabled) {\n          debug('Web is disabled');\n          // Use warnings from the web support setup.\n          break;\n        }\n\n        // Ensure the Webpack dev server is running first\n        if (!devServerManager.getWebDevServer()) {\n          debug('Starting up webpack dev server');\n          await devServerManager.ensureWebDevServerRunningAsync();\n          // When this is the first time webpack is started, reprint the connection info.\n          actions.printDevServerInfo(usageOptions);\n        }\n\n        Log.log(`${BLT} Open in the web browser...`);\n        try {\n          await devServerManager.getWebDevServer()?.openPlatformAsync('desktop');\n          printHelp();\n        } catch (error: any) {\n          if (!(error instanceof AbortCommandError)) {\n            Log.exception(error);\n          }\n        }\n        break;\n      }\n      case 'c':\n        Log.clear();\n        return actions.printDevServerInfo(usageOptions);\n      case 'j':\n        return actions.openJsInspectorAsync();\n      case 'r':\n        return actions.reloadApp();\n      case 'o':\n        Log.log(`${BLT} Opening the editor...`);\n        return openInEditorAsync(devServerManager.projectRoot);\n    }\n  };\n\n  const keyPressHandler = new KeyPressHandler(onPressAsync);\n\n  const listener = keyPressHandler.createInteractionListener();\n\n  addInteractionListener(listener);\n\n  // Start observing...\n  keyPressHandler.startInterceptingKeyStrokes();\n}\n"], "names": ["startInterfaceAsync", "debug", "require", "CTRL_C", "CTRL_D", "CTRL_L", "PLATFORM_SETTINGS", "android", "name", "key", "launchTarget", "ios", "devServerManager", "options", "actions", "DevServerManagerActions", "isWebSocketsEnabled", "getDefaultDevServer", "isTargetingNative", "usageOptions", "devClient", "printDevServerInfo", "onPressAsync", "pauseInteractions", "spinners", "getAllSpinners", "for<PERSON>ach", "spinner", "fail", "currentProgress", "getProgressBar", "terminate", "setProgressBar", "ora", "text", "color", "start", "stopAsync", "stopAndPersist", "symbol", "process", "emit", "exit", "error", "Log", "clear", "printUsage", "verbose", "toggleDevMenu", "openMoreToolsAsync", "platforms", "includes", "toLowerCase", "platform", "should<PERSON>rompt", "server", "settings", "log", "BLT", "warn", "chalk", "openPlatformAsync", "printHelp", "AbortCommandError", "exception", "toggleRuntimeMode", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "push", "e", "message", "isDisabled", "getWebDevServer", "ensureWebDevServerRunningAsync", "openJsInspectorAsync", "reloadApp", "openInEditorAsync", "projectRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listener", "createInteractionListener", "addInteractionListener", "startInterceptingKeyStrokes"], "mappings": ";;;;+BAoCsBA;;;eAAAA;;;;gEApCJ;;;;;;iCAEc;+BACyB;oCACjB;6DACnB;wBACa;wBACA;qBACE;0BACW;yBACW;+CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG9C,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,SAAS;AACf,MAAMC,SAAS;AACf,MAAMC,SAAS;AAEf,MAAMC,oBAGF;IACFC,SAAS;QACPC,MAAM;QACNC,KAAK;QACLC,cAAc;IAChB;IACAC,KAAK;QACHH,MAAM;QACNC,KAAK;QACLC,cAAc;IAChB;AACF;AAEO,eAAeV,oBACpBY,gBAAkC,EAClCC,OAAsD;QAI1BD;IAF5B,MAAME,UAAU,IAAIC,2CAAuB,CAACH,kBAAkBC;IAE9D,MAAMG,uBAAsBJ,wCAAAA,iBAAiBK,mBAAmB,uBAApCL,sCAAwCM,iBAAiB;IAErF,MAAMC,eAAe;QACnBH;QACAI,WAAWR,iBAAiBC,OAAO,CAACO,SAAS;QAC7C,GAAGP,OAAO;IACZ;IAEAC,QAAQO,kBAAkB,CAACF;IAE3B,MAAMG,eAAe,OAAOb;QAC1B,iCAAiC;QACjC,OAAQA;YACN,KAAKN;YACL,KAAKC;gBAAQ;oBACX,4EAA4E;oBAC5E,0EAA0E;oBAC1E,4DAA4D;oBAC5DmB,IAAAA,0BAAiB;oBAEjB,MAAMC,WAAWC,IAAAA,mBAAc;oBAC/BD,SAASE,OAAO,CAAC,CAACC;wBAChBA,QAAQC,IAAI;oBACd;oBAEA,MAAMC,kBAAkBC,IAAAA,wBAAc;oBACtC,IAAID,iBAAiB;wBACnBA,gBAAgBE,SAAS;wBACzBC,IAAAA,wBAAc,EAAC;oBACjB;oBACA,MAAML,UAAUM,IAAAA,QAAG,EAAC;wBAAEC,MAAM;wBAAmBC,OAAO;oBAAQ,GAAGC,KAAK;oBACtE,IAAI;wBACF,MAAMxB,iBAAiByB,SAAS;wBAChCV,QAAQW,cAAc,CAAC;4BAAEJ,MAAM;4BAAkBK,QAAQ,CAAC,MAAM,CAAC;wBAAC;wBAClE,iGAAiG;wBACjGC,QAAQC,IAAI,CAAC;wBAEb,4CAA4C;wBAC5CD,QAAQE,IAAI;oBACd,EAAE,OAAOC,OAAO;wBACdhB,QAAQC,IAAI,CAAC;wBACb,MAAMe;oBACR;oBACA;gBACF;YACA,KAAKtC;gBACH,OAAOuC,KAAIC,KAAK;YAClB,KAAK;gBACH,OAAOC,IAAAA,yBAAU,EAAC3B,cAAc;oBAAE4B,SAAS;gBAAK;QACpD;QAEA,qBAAqB;QAErB,IAAI/B,qBAAqB;YACvB,OAAQP;gBACN,KAAK;oBACH,OAAOK,QAAQkC,aAAa;gBAC9B,KAAK;oBACH,OAAOlC,QAAQmC,kBAAkB;YACrC;QACF;QAEA,MAAM,EAAEC,YAAY;YAAC;YAAO;YAAW;SAAM,EAAE,GAAGrC;QAElD,IAAI;YAAC;YAAK;SAAI,CAACsC,QAAQ,CAAC1C,IAAI2C,WAAW,KAAK;YAC1C,MAAMC,WAAW5C,IAAI2C,WAAW,OAAO,MAAM,QAAQ;YAErD,MAAME,eAAe;gBAAC;gBAAK;aAAI,CAACH,QAAQ,CAAC1C;YACzC,IAAI6C,cAAc;gBAChBV,KAAIC,KAAK;YACX;YAEA,MAAMU,SAAS3C,iBAAiBK,mBAAmB;YACnD,MAAMuC,WAAWlD,iBAAiB,CAAC+C,SAAS;YAE5CT,KAAIa,GAAG,CAAC,GAAGC,kBAAG,CAAC,YAAY,EAAEF,SAAShD,IAAI,CAAC,GAAG,CAAC;YAE/C,IAAI+C,OAAOrC,iBAAiB,MAAM,CAACgC,UAAUC,QAAQ,CAACK,SAAS/C,GAAG,GAAG;gBACnEmC,KAAIe,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,EAAEJ,SAAShD,IAAI,CAAC,wCAAwC,EAAEgD,SAAS/C,GAAG,CAAC,0DAA0D,CAAC;YAE5I,OAAO;gBACL,IAAI;oBACF,MAAM8C,OAAOM,iBAAiB,CAACL,SAAS9C,YAAY,EAAE;wBAAE4C;oBAAa;oBACrEQ,IAAAA,wBAAS;gBACX,EAAE,OAAOnB,OAAY;oBACnB,IAAI,CAAEA,CAAAA,iBAAiBoB,yBAAiB,AAAD,GAAI;wBACzCnB,KAAIoB,SAAS,CAACrB;oBAChB;gBACF;YACF;YACA,mBAAmB;YACnB;QACF;QAEA,OAAQlC;YACN,KAAK;gBAAK;oBACRmC,KAAIC,KAAK;oBACT,IAAI,MAAMjC,iBAAiBqD,iBAAiB,IAAI;wBAC9C9C,aAAaC,SAAS,GAAGR,iBAAiBC,OAAO,CAACO,SAAS;wBAC3D,OAAON,QAAQO,kBAAkB,CAACF;oBACpC;oBACA;gBACF;YACA,KAAK;gBAAK;oBACR,IAAI;wBACF,MAAMP,iBAAiBsD,8BAA8B,CAACC,4DAA6B;wBACnF,IAAI,CAACjB,UAAUC,QAAQ,CAAC,QAAQ;gCAE9BtC;4BADAqC,UAAUkB,IAAI,CAAC;6BACfvD,qBAAAA,QAAQqC,SAAS,qBAAjBrC,mBAAmBuD,IAAI,CAAC;wBAC1B;oBACF,EAAE,OAAOC,GAAQ;wBACfzB,KAAIe,IAAI,CAACU,EAAEC,OAAO;wBAClB;oBACF;oBAEA,MAAMC,aAAa,CAACrB,UAAUC,QAAQ,CAAC;oBACvC,IAAIoB,YAAY;wBACdtE,MAAM;wBAEN;oBACF;oBAEA,iDAAiD;oBACjD,IAAI,CAACW,iBAAiB4D,eAAe,IAAI;wBACvCvE,MAAM;wBACN,MAAMW,iBAAiB6D,8BAA8B;wBACrD,+EAA+E;wBAC/E3D,QAAQO,kBAAkB,CAACF;oBAC7B;oBAEAyB,KAAIa,GAAG,CAAC,GAAGC,kBAAG,CAAC,2BAA2B,CAAC;oBAC3C,IAAI;4BACI9C;wBAAN,QAAMA,oCAAAA,iBAAiB4D,eAAe,uBAAhC5D,kCAAoCiD,iBAAiB,CAAC;wBAC5DC,IAAAA,wBAAS;oBACX,EAAE,OAAOnB,OAAY;wBACnB,IAAI,CAAEA,CAAAA,iBAAiBoB,yBAAiB,AAAD,GAAI;4BACzCnB,KAAIoB,SAAS,CAACrB;wBAChB;oBACF;oBACA;gBACF;YACA,KAAK;gBACHC,KAAIC,KAAK;gBACT,OAAO/B,QAAQO,kBAAkB,CAACF;YACpC,KAAK;gBACH,OAAOL,QAAQ4D,oBAAoB;YACrC,KAAK;gBACH,OAAO5D,QAAQ6D,SAAS;YAC1B,KAAK;gBACH/B,KAAIa,GAAG,CAAC,GAAGC,kBAAG,CAAC,sBAAsB,CAAC;gBACtC,OAAOkB,IAAAA,yBAAiB,EAAChE,iBAAiBiE,WAAW;QACzD;IACF;IAEA,MAAMC,kBAAkB,IAAIC,gCAAe,CAACzD;IAE5C,MAAM0D,WAAWF,gBAAgBG,yBAAyB;IAE1DC,IAAAA,+BAAsB,EAACF;IAEvB,qBAAqB;IACrBF,gBAAgBK,2BAA2B;AAC7C"}