const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4002;

// Configuration CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Données de test
const testClients = [
  {
    idclient: 1,
    nom: '<PERSON><PERSON>',
    prenom: 'Fatima',
    adresse: '45 Avenue Hassan II, près de l\'école <PERSON>, Safrou',
    ville: 'Safrou',
    tel: '0647895655',
    email: '<EMAIL>',
    ids: 1,
    secteur_nom: 'Secteur <PERSON>és<PERSON>'
  },
  {
    idclient: 2,
    nom: 'Loukil',
    prenom: '<PERSON>',
    adresse: 'Rue de la République',
    ville: 'Tunis',
    tel: '0678126945',
    email: '<EMAIL>',
    ids: 2,
    secteur_nom: 'Centre-Ville'
  }
];

const testSecteurs = [
  { ids: 1, nom: 'Secteur Résidentiel' },
  { ids: 2, nom: 'Centre-Ville' },
  { ids: 3, nom: 'Zone Industrielle' }
];

const testContracts = [
  {
    idcontract: 1,
    codeqr: 'QR001',
    datecontract: '2024-01-15',
    idclient: 1,
    marquecompteur: 'Sensus',
    numseriecompteur: 'SN123456',
    posx: '10.1658', // Longitude (Tunis)
    posy: '36.8065'  // Latitude (Tunis)
  },
  {
    idcontract: 2,
    codeqr: 'QR002',
    datecontract: '2024-02-20',
    idclient: 2,
    marquecompteur: 'Itron',
    numseriecompteur: 'IT789012',
    posx: '10.1950', // Longitude (Ariana)
    posy: '36.8625'  // Latitude (Ariana)
  }
];

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'API AquaTrack - Serveur Debug',
    version: '1.0.0',
    port: PORT,
    status: 'Fonctionnel'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', (req, res) => {
  console.log('📥 Requête GET /api/clients');
  res.json({
    success: true,
    count: testClients.length,
    data: testClients
  });
});

// Route pour récupérer un secteur spécifique par ID
app.get('/api/secteurs/:id', (req, res) => {
  const { id } = req.params;
  console.log(`📥 Requête GET /api/secteurs/${id}`);
  
  const secteur = testSecteurs.find(s => s.ids === parseInt(id));
  
  if (secteur) {
    console.log(`✅ Secteur trouvé: ${secteur.nom}`);
    res.json({
      success: true,
      data: secteur
    });
  } else {
    console.log(`❌ Secteur non trouvé pour l'ID: ${id}`);
    res.status(404).json({
      success: false,
      message: 'Secteur non trouvé'
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', (req, res) => {
  const { id } = req.params;
  console.log(`📥 Requête GET /api/clients/${id}/contracts`);
  
  const clientContracts = testContracts.filter(contract => contract.idclient === parseInt(id));
  
  console.log(`✅ ${clientContracts.length} contrat(s) trouvé(s) pour le client ${id}`);
  res.json({
    success: true,
    count: clientContracts.length,
    data: clientContracts,
    client_id: parseInt(id)
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur Debug AquaTrack démarré sur le port ${PORT}`);
  console.log(`📡 Endpoints disponibles:`);
  console.log(`   - GET  /api/clients          - Liste des clients`);
  console.log(`   - GET  /api/secteurs/:id     - Secteur spécifique`);
  console.log(`   - GET  /api/clients/:id/contracts - Contrats d'un client`);
  console.log(`🌐 CORS autorisé pour: localhost:3000, localhost:3001, localhost:3002`);
  console.log(`🎯 Interface web: http://localhost:3002/technician-dashboard`);
  console.log(`✅ PRÊT À RECEVOIR LES REQUÊTES !`);
});
