{"version": 3, "names": ["HeaderSegment", "props", "leftLabelLayout", "setLeftLabelLayout", "React", "useState", "undefined", "titleLayout", "setTitleLayout", "handleTitleLayout", "e", "height", "width", "nativeEvent", "layout", "handleLeftLabelLayout", "getInterpolatedStyle", "memoize", "styleInterpolator", "current", "next", "headerHeight", "progress", "layouts", "header", "screen", "title", "leftLabel", "modal", "onGoBack", "headerTitle", "headerLeft", "left", "headerRight", "right", "headerBackImage", "headerBackTitle", "headerBackTitleVisible", "Platform", "OS", "headerTruncatedBackTitle", "headerBackAccessibilityLabel", "headerBackTestID", "headerBackAllowFontScaling", "headerBackTitleStyle", "headerTitleContainerStyle", "headerLeftContainerStyle", "headerRightContainerStyle", "headerBackgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerStatusBarHeight", "rest", "defaultHeight", "getDefaultHeaderHeight", "StyleSheet", "flatten", "titleStyle", "leftButtonStyle", "leftLabelStyle", "rightButtonStyle", "backgroundStyle", "backImage", "accessibilityLabel", "testID", "allowFontScaling", "onPress", "label", "truncatedLabel", "labelStyle", "onLabelLayout", "screenLayout", "canGoBack", "Boolean", "onLayout"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderSegment.tsx"], "mappings": ";;;;;;AAAA;AAOA;AACA;AAcA;AAA0C;AAAA;AAAA;AAAA;AAY3B,SAASA,aAAa,CAACC,KAAY,EAAE;EAClD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAE1DC,SAAS,CAAC;EAEZ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGJ,KAAK,CAACC,QAAQ,CAClDC,SAAS,CACV;EAED,MAAMG,iBAAiB,GAAIC,CAAoB,IAAK;IAClD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9CN,cAAc,CAAED,WAAW,IAAK;MAC9B,IACEA,WAAW,IACXI,MAAM,KAAKJ,WAAW,CAACI,MAAM,IAC7BC,KAAK,KAAKL,WAAW,CAACK,KAAK,EAC3B;QACA,OAAOL,WAAW;MACpB;MAEA,OAAO;QAAEI,MAAM;QAAEC;MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,qBAAqB,GAAIL,CAAoB,IAAK;IACtD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9C,IACEZ,eAAe,IACfS,MAAM,KAAKT,eAAe,CAACS,MAAM,IACjCC,KAAK,KAAKV,eAAe,CAACU,KAAK,EAC/B;MACA;IACF;IAEAT,kBAAkB,CAAC;MAAEQ,MAAM;MAAEC;IAAM,CAAC,CAAC;EACvC,CAAC;EAED,MAAMI,oBAAoB,GAAG,IAAAC,gBAAO,EAClC,CACEC,iBAA+C,EAC/CJ,MAAc,EACdK,OAA+C,EAC/CC,IAAwD,EACxDb,WAA+B,EAC/BL,eAAmC,EACnCmB,YAAoB,KAEpBH,iBAAiB,CAAC;IAChBC,OAAO,EAAE;MAAEG,QAAQ,EAAEH;IAAQ,CAAC;IAC9BC,IAAI,EAAEA,IAAI,IAAI;MAAEE,QAAQ,EAAEF;IAAK,CAAC;IAChCG,OAAO,EAAE;MACPC,MAAM,EAAE;QACNb,MAAM,EAAEU,YAAY;QACpBT,KAAK,EAAEE,MAAM,CAACF;MAChB,CAAC;MACDa,MAAM,EAAEX,MAAM;MACdY,KAAK,EAAEnB,WAAW;MAClBoB,SAAS,EAAEzB;IACb;EACF,CAAC,CAAC,CACL;EAED,MAAM;IACJoB,QAAQ;IACRR,MAAM;IACNc,KAAK;IACLC,QAAQ;IACRC,WAAW,EAAEJ,KAAK;IAClBK,UAAU,EAAEC,IAAI,GAAGH,QAAQ,GACtB5B,KAA4B,iBAAK,oBAAC,0BAAgB,EAAKA,KAAK,CAAI,GACjEK,SAAS;IACb2B,WAAW,EAAEC,KAAK;IAClBC,eAAe;IACfC,eAAe;IACfC,sBAAsB,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;IAC9CC,wBAAwB;IACxBC,4BAA4B;IAC5BC,gBAAgB;IAChBC,0BAA0B;IAC1BC,oBAAoB;IACpBC,yBAAyB;IACzBC,wBAAwB;IACxBC,yBAAyB;IACzBC,8BAA8B;IAC9BC,WAAW,EAAEC,iBAAiB;IAC9BC,qBAAqB;IACrBjC,iBAAiB;IACjB,GAAGkC;EACL,CAAC,GAAGnD,KAAK;EAET,MAAMoD,aAAa,GAAG,IAAAC,gCAAsB,EAC1CxC,MAAM,EACNc,KAAK,EACLuB,qBAAqB,CACtB;EAED,MAAM;IAAExC,MAAM,GAAG0C;EAAc,CAAC,GAAGE,uBAAU,CAACC,OAAO,CACnDN,iBAAiB,IAAI,CAAC,CAAC,CACX;EAEd,MAAM;IACJO,UAAU;IACVC,eAAe;IACfC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC,GAAG7C,oBAAoB,CACtBE,iBAAiB,EACjBJ,MAAM,EACNQ,QAAQ,CAACH,OAAO,EAChBG,QAAQ,CAACF,IAAI,EACbb,WAAW,EACX6B,eAAe,GAAGlC,eAAe,GAAGI,SAAS,EAC7C,OAAOK,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG0C,aAAa,CACpD;EAED,MAAMtB,UAA4C,GAAGC,IAAI,GACpD/B,KAAK,IACJ+B,IAAI,CAAC;IACH,GAAG/B,KAAK;IACR6D,SAAS,EAAE3B,eAAe;IAC1B4B,kBAAkB,EAAEtB,4BAA4B;IAChDuB,MAAM,EAAEtB,gBAAgB;IACxBuB,gBAAgB,EAAEtB,0BAA0B;IAC5CuB,OAAO,EAAErC,QAAQ;IACjBsC,KAAK,EAAE/B,eAAe;IACtBgC,cAAc,EAAE5B,wBAAwB;IACxC6B,UAAU,EAAE,CAACV,cAAc,EAAEf,oBAAoB,CAAC;IAClD0B,aAAa,EAAEvD,qBAAqB;IACpCwD,YAAY,EAAEzD,MAAM;IACpBP,WAAW;IACXiE,SAAS,EAAEC,OAAO,CAAC5C,QAAQ;EAC7B,CAAC,CAAC,GACJvB,SAAS;EAEb,MAAM2B,WAA8C,GAAGC,KAAK,GACvDjC,KAAK,IACJiC,KAAK,CAAC;IACJ,GAAGjC,KAAK;IACRuE,SAAS,EAAEC,OAAO,CAAC5C,QAAQ;EAC7B,CAAC,CAAC,GACJvB,SAAS;EAEb,MAAMwB,WAA8C,GAClD,OAAOJ,KAAK,KAAK,UAAU,GACtBzB,KAAK,iBAAK,oBAAC,qBAAW,eAAKA,KAAK;IAAE,QAAQ,EAAEQ;EAAkB,GAAG,GACjER,KAAK,IAAKyB,KAAK,CAAC;IAAE,GAAGzB,KAAK;IAAEyE,QAAQ,EAAEjE;EAAkB,CAAC,CAAC;EAEjE,oBACE,oBAAC,gBAAM;IACL,KAAK,EAAEmB,KAAM;IACb,MAAM,EAAEd,MAAO;IACf,WAAW,EAAEgB,WAAY;IACzB,UAAU,EAAEC,UAAW;IACvB,sBAAsB,EAAEM,sBAAuB;IAC/C,WAAW,EAAEJ,WAAY;IACzB,yBAAyB,EAAE,CAACwB,UAAU,EAAEZ,yBAAyB,CAAE;IACnE,wBAAwB,EAAE,CAACa,eAAe,EAAEZ,wBAAwB,CAAE;IACtE,yBAAyB,EAAE,CAACc,gBAAgB,EAAEb,yBAAyB,CAAE;IACzE,8BAA8B,EAAE,CAC9Bc,eAAe,EACfb,8BAA8B,CAC9B;IACF,WAAW,EAAEE,iBAAkB;IAC/B,qBAAqB,EAAEC;EAAsB,GACzCC,IAAI,EACR;AAEN"}