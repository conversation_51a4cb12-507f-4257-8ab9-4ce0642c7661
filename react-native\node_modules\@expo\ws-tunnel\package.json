{"name": "@expo/ws-tunnel", "version": "1.0.6", "license": "MIT", "main": "./build", "files": ["build"], "scripts": {"build": "ncc build src/index.ts --out build --minify", "test": "jest", "lint": "eslint . --ext ts,tsx"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/debug": "^4.1.12", "@types/jest": "^29.5.14", "@types/node": "^22.10.5", "@types/ws": "^8.5.13", "@vercel/ncc": "^0.38.3", "debug": "^4.4.0", "eslint": "^8.57.1", "eslint-config-universe": "^14.0.0", "fetch-nodeshim": "^0.2.1", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "typescript": "^5.5.2", "ws": "^8.18.0"}, "eslintConfig": {"extends": "universe", "ignorePatterns": ["build"]}, "jest": {"testEnvironment": "node", "transform": {"^.+.tsx?$": ["ts-jest"]}}, "prettier": {"printWidth": 100, "tabWidth": 2, "singleQuote": true, "bracketSameLine": true}}