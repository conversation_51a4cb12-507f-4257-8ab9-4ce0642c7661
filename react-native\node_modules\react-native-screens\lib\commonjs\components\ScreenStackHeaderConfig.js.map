{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ScreenStackHeaderSubview", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderConfig", "ScreenStackHeaderCenterView", "ScreenStackHeaderBackButtonImage", "_react", "_interopRequireDefault", "require", "_reactNative", "_ScreenStackHeaderConfigNativeComponent", "_ScreenStackHeaderSubviewNativeComponent", "obj", "__esModule", "default", "_extends", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "ScreenStackHeaderSubviewNativeComponent", "React", "forwardRef", "props", "ref", "createElement", "style", "styles", "headerConfig", "pointerEvents", "displayName", "type", "headerSubview", "Image", "resizeMode", "fadeDuration", "rest", "headerSubviewCenter", "StyleSheet", "create", "flexDirection", "alignItems", "justifyContent", "flexShrink", "position", "top", "width", "Platform", "OS", "undefined"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,wBAAA,GAAAF,OAAA,CAAAG,8BAAA,GAAAH,OAAA,CAAAI,0BAAA,GAAAJ,OAAA,CAAAK,yBAAA,GAAAL,OAAA,CAAAM,uBAAA,GAAAN,OAAA,CAAAO,2BAAA,GAAAP,OAAA,CAAAQ,gCAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,uCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,wCAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAwG,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAApB,MAAA,CAAAqB,MAAA,GAAArB,MAAA,CAAAqB,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAA3B,MAAA,CAAA6B,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAH,QAAA,CAAAY,KAAA,OAAAP,SAAA,KAFxG;AAIO,MAAMrB,wBAEZ,GAAAF,OAAA,CAAAE,wBAAA,GAAG6B,gDAA8C;AAE3C,MAAMzB,uBAAuB,GAAAN,OAAA,CAAAM,uBAAA,gBAAG0B,cAAK,CAACC,UAAU,CAGrD,CAACC,KAAK,EAAEC,GAAG,kBACX1B,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAACvB,uCAAA,CAAAI,OAAsC,EAAAC,QAAA,KACjCgB,KAAK;EACTC,GAAG,EAAEA,GAAI;EACTE,KAAK,EAAEC,MAAM,CAACC,YAAa;EAC3BC,aAAa,EAAC;AAAU,EACzB,CACF,CAAC;AAEFlC,uBAAuB,CAACmC,WAAW,GAAG,yBAAyB;AAExD,MAAMjC,gCAAgC,GAC3C0B,KAAiB,iBAEjBzB,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAAClC,wBAAwB;EAACwC,IAAI,EAAC,MAAM;EAACL,KAAK,EAAEC,MAAM,CAACK;AAAc,gBAChElC,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAACxB,YAAA,CAAAgC,KAAK,EAAA1B,QAAA;EAAC2B,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKZ,KAAK,CAAG,CAChC,CAC3B;AAAClC,OAAA,CAAAQ,gCAAA,GAAAA,gCAAA;AAEK,MAAMJ,0BAA0B,GACrC8B,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEzB,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAAClC,wBAAwB,EAAAgB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,OAAO;IACZL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACrC,OAAA,CAAAI,0BAAA,GAAAA,0BAAA;AAEK,MAAMC,yBAAyB,GACpC6B,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEzB,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAAClC,wBAAwB,EAAAgB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,MAAM;IACXL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACrC,OAAA,CAAAK,yBAAA,GAAAA,yBAAA;AAEK,MAAME,2BAA2B,GACtC2B,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEzB,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAAClC,wBAAwB,EAAAgB,QAAA,KACnB6B,IAAI;IACRL,IAAI,EAAC,QAAQ;IACbL,KAAK,EAAE,CAACC,MAAM,CAACU,mBAAmB,EAAEX,KAAK;EAAE,EAC5C,CAAC;AAEN,CAAC;AAACrC,OAAA,CAAAO,2BAAA,GAAAA,2BAAA;AAEK,MAAMJ,8BAA8B,GACzC+B,KAA8C,iBAE9CzB,MAAA,CAAAQ,OAAA,CAAAmB,aAAA,CAAClC,wBAAwB,EAAAgB,QAAA,KACnBgB,KAAK;EACTQ,IAAI,EAAC,WAAW;EAChBL,KAAK,EAAEC,MAAM,CAACK;AAAc,EAC7B,CACF;AAAC3C,OAAA,CAAAG,8BAAA,GAAAA,8BAAA;AAEF,MAAMmC,MAAM,GAAGW,uBAAU,CAACC,MAAM,CAAC;EAC/BP,aAAa,EAAE;IACbQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDL,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDf,YAAY,EAAE;IACZgB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,MAAM;IACbN,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE,eAAe;IAC/B;IACA;IACAD,UAAU,EAAEM,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAGC;EACjD;AACF,CAAC,CAAC"}