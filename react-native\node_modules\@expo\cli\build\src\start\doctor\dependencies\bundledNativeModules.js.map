{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/bundledNativeModules.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport chalk from 'chalk';\nimport resolveFrom from 'resolve-from';\n\nimport { getNativeModuleVersionsAsync } from '../../../api/getNativeModuleVersions';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:doctor:dependencies:bundledNativeModules'\n) as typeof console.log;\n\nexport type BundledNativeModules = Record<string, string>;\n\n/**\n * Gets the bundledNativeModules.json for a given SDK version:\n * - Tries to fetch the data from the /sdks/:sdkVersion/native-modules API endpoint.\n * - If the data is missing on the server (it can happen for SDKs that are yet fully released)\n *    or there's a downtime, reads the local .json file from the \"expo\" package.\n * - For UNVERSIONED, returns the local .json file contents.\n */\nexport async function getVersionedNativeModulesAsync(\n  projectRoot: string,\n  sdkVersion: string,\n  options: {\n    skipRemoteVersions?: boolean;\n  } = {}\n): Promise<BundledNativeModules> {\n  if (sdkVersion !== 'UNVERSIONED' && !env.EXPO_OFFLINE && !options.skipRemoteVersions) {\n    try {\n      debug('Fetching bundled native modules from the server...');\n      return await getNativeModuleVersionsAsync(sdkVersion);\n    } catch (error: any) {\n      if (error instanceof CommandError && (error.code === 'OFFLINE' || error.code === 'API')) {\n        Log.warn(\n          chalk`Unable to reach well-known versions endpoint. Using local dependency map {bold expo/bundledNativeModules.json} for version validation`\n        );\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  debug('Fetching bundled native modules from the local JSON file...');\n  return await getBundledNativeModulesAsync(projectRoot);\n}\n\n/**\n * Get the legacy static `bundledNativeModules.json` file\n * that's shipped with the version of `expo` that the project has installed.\n */\nasync function getBundledNativeModulesAsync(projectRoot: string): Promise<BundledNativeModules> {\n  // TODO: Revisit now that this code is in the `expo` package.\n  const bundledNativeModulesPath = resolveFrom.silent(\n    projectRoot,\n    'expo/bundledNativeModules.json'\n  );\n  if (!bundledNativeModulesPath) {\n    Log.log();\n    throw new CommandError(\n      chalk`The dependency map {bold expo/bundledNativeModules.json} cannot be found, please ensure you have the package \"{bold expo}\" installed in your project.`\n    );\n  }\n  return await JsonFile.readAsync<BundledNativeModules>(bundledNativeModulesPath);\n}\n"], "names": ["getVersionedNativeModulesAsync", "debug", "require", "projectRoot", "sdkVersion", "options", "env", "EXPO_OFFLINE", "skipRemoteVersions", "getNativeModuleVersionsAsync", "error", "CommandError", "code", "Log", "warn", "chalk", "getBundledNativeModulesAsync", "bundledNativeModulesPath", "resolveFrom", "silent", "log", "JsonFile", "readAsync"], "mappings": ";;;;+BAsBsBA;;;eAAAA;;;;gEAtBD;;;;;;;gEACH;;;;;;;gEACM;;;;;;yCAEqB;6DACxB;qBACD;wBACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SACpB;AAYK,eAAeF,+BACpBG,WAAmB,EACnBC,UAAkB,EAClBC,UAEI,CAAC,CAAC;IAEN,IAAID,eAAe,iBAAiB,CAACE,QAAG,CAACC,YAAY,IAAI,CAACF,QAAQG,kBAAkB,EAAE;QACpF,IAAI;YACFP,MAAM;YACN,OAAO,MAAMQ,IAAAA,qDAA4B,EAACL;QAC5C,EAAE,OAAOM,OAAY;YACnB,IAAIA,iBAAiBC,oBAAY,IAAKD,CAAAA,MAAME,IAAI,KAAK,aAAaF,MAAME,IAAI,KAAK,KAAI,GAAI;gBACvFC,KAAIC,IAAI,CACNC,IAAAA,gBAAK,CAAA,CAAC,qIAAqI,CAAC;YAEhJ,OAAO;gBACL,MAAML;YACR;QACF;IACF;IAEAT,MAAM;IACN,OAAO,MAAMe,6BAA6Bb;AAC5C;AAEA;;;CAGC,GACD,eAAea,6BAA6Bb,WAAmB;IAC7D,6DAA6D;IAC7D,MAAMc,2BAA2BC,sBAAW,CAACC,MAAM,CACjDhB,aACA;IAEF,IAAI,CAACc,0BAA0B;QAC7BJ,KAAIO,GAAG;QACP,MAAM,IAAIT,oBAAY,CACpBI,IAAAA,gBAAK,CAAA,CAAC,qJAAqJ,CAAC;IAEhK;IACA,OAAO,MAAMM,mBAAQ,CAACC,SAAS,CAAuBL;AACxD"}