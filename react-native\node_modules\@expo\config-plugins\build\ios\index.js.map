{"version": 3, "file": "index.js", "names": ["Bitcode", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "BuildProperties", "BuildScheme", "BundleIdentifier", "DevelopmentTeam", "DeviceFamily", "Entitlements", "Google", "_IosConfig", "Locales", "Maps", "Name", "Orientation", "Paths", "Permissions", "PrivacyInfo", "ProvisioningProfile", "RequiresFullScreen", "Scheme", "Target", "Updates", "UsesNonExemptEncryption", "Version", "XcodeProjectFile", "XcodeUtils", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set"], "sources": ["../../src/ios/index.ts"], "sourcesContent": ["import * as Bitcode from './Bitcode';\nimport * as BuildProperties from './BuildProperties';\nimport * as BuildScheme from './BuildScheme';\nimport * as BundleIdentifier from './BundleIdentifier';\nimport * as DevelopmentTeam from './DevelopmentTeam';\nimport * as DeviceFamily from './DeviceFamily';\nimport * as Entitlements from './Entitlements';\nimport * as Google from './Google';\nimport { ExpoPlist, InfoPlist } from './IosConfig.types';\nimport * as Locales from './Locales';\nimport * as Maps from './Maps';\nimport * as Name from './Name';\nimport * as Orientation from './Orientation';\nimport * as Paths from './Paths';\nimport * as Permissions from './Permissions';\nimport * as PrivacyInfo from './PrivacyInfo';\nimport * as ProvisioningProfile from './ProvisioningProfile';\nimport * as RequiresFullScreen from './RequiresFullScreen';\nimport * as Scheme from './Scheme';\nimport * as Target from './Target';\nimport * as Updates from './Updates';\nimport * as UsesNonExemptEncryption from './UsesNonExemptEncryption';\nimport * as Version from './Version';\nimport * as XcodeProjectFile from './XcodeProjectFile';\nimport * as XcodeUtils from './utils/Xcodeproj';\n\n// We can change this to export * as X with TypeScript 3.8+\n// https://devblogs.microsoft.com/typescript/announcing-typescript-3-8-beta/#export-star-as-namespace-syntax\n// .. but we should wait for this to be the default VSCode version.\nexport { InfoPlist, ExpoPlist, Entitlements, Paths, Permissions, XcodeUtils };\n\nexport {\n  Bitcode,\n  BundleIdentifier,\n  BuildProperties,\n  BuildScheme,\n  DevelopmentTeam,\n  DeviceFamily,\n  Google,\n  Maps,\n  Locales,\n  Name,\n  Orientation,\n  ProvisioningProfile,\n  RequiresFullScreen,\n  Scheme,\n  Target,\n  Updates,\n  UsesNonExemptEncryption,\n  Version,\n  XcodeProjectFile,\n  PrivacyInfo,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,OAAA;EAAA;AAAA;AACrC,SAAAS,gBAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,eAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,eAAA;EAAA;AAAA;AACrD,SAAAC,YAAA;EAAA,MAAAT,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAO,WAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAE,WAAA;EAAA;AAAA;AAC7C,SAAAC,iBAAA;EAAA,MAAAV,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAQ,gBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAG,gBAAA;EAAA;AAAA;AACvD,SAAAC,gBAAA;EAAA,MAAAX,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAS,eAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAI,eAAA;EAAA;AAAA;AACrD,SAAAC,aAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,YAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,YAAA;EAAA;AAAA;AAC/C,SAAAC,aAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,YAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,YAAA;EAAA;AAAA;AAC/C,SAAAC,OAAA;EAAA,MAAAd,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAY,MAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAO,MAAA;EAAA;AAAA;AACnC,SAAAC,WAAA;EAAA,MAAAf,IAAA,GAAAE,OAAA;EAAAa,UAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAgB,QAAA;EAAA,MAAAhB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAc,OAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAS,OAAA;EAAA;AAAA;AACrC,SAAAC,KAAA;EAAA,MAAAjB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAe,IAAA,YAAAA,CAAA;IAAA,OAAAjB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAU,IAAA;EAAA;AAAA;AAC/B,SAAAC,KAAA;EAAA,MAAAlB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAgB,IAAA,YAAAA,CAAA;IAAA,OAAAlB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAW,IAAA;EAAA;AAAA;AAC/B,SAAAC,YAAA;EAAA,MAAAnB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAiB,WAAA,YAAAA,CAAA;IAAA,OAAAnB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAY,WAAA;EAAA;AAAA;AAC7C,SAAAC,MAAA;EAAA,MAAApB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAkB,KAAA,YAAAA,CAAA;IAAA,OAAApB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAa,KAAA;EAAA;AAAA;AACjC,SAAAC,YAAA;EAAA,MAAArB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAmB,WAAA,YAAAA,CAAA;IAAA,OAAArB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAc,WAAA;EAAA;AAAA;AAC7C,SAAAC,YAAA;EAAA,MAAAtB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAoB,WAAA,YAAAA,CAAA;IAAA,OAAAtB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAe,WAAA;EAAA;AAAA;AAC7C,SAAAC,oBAAA;EAAA,MAAAvB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAqB,mBAAA,YAAAA,CAAA;IAAA,OAAAvB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAgB,mBAAA;EAAA;AAAA;AAC7D,SAAAC,mBAAA;EAAA,MAAAxB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAsB,kBAAA,YAAAA,CAAA;IAAA,OAAAxB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAiB,kBAAA;EAAA;AAAA;AAC3D,SAAAC,OAAA;EAAA,MAAAzB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAuB,MAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAkB,MAAA;EAAA;AAAA;AACnC,SAAAC,OAAA;EAAA,MAAA1B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAwB,MAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAmB,MAAA;EAAA;AAAA;AACnC,SAAAC,QAAA;EAAA,MAAA3B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAyB,OAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAoB,OAAA;EAAA;AAAA;AACrC,SAAAC,wBAAA;EAAA,MAAA5B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA0B,uBAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqEG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAqB,uBAAA;EAAA;AAAA;AACrE,SAAAC,QAAA;EAAA,MAAA7B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA2B,OAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAsB,OAAA;EAAA;AAAA;AACrC,SAAAC,iBAAA;EAAA,MAAA9B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA4B,gBAAA,YAAAA,CAAA;IAAA,OAAA9B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAuB,gBAAA;EAAA;AAAA;AACvD,SAAAC,WAAA;EAAA,MAAA/B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA6B,UAAA,YAAAA,CAAA;IAAA,OAAA/B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAwB,UAAA;EAAA;AAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAhC,wBAAAgC,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAA7B,GAAA,CAAA0B,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAvC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAwC,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAvC,MAAA,CAAAwC,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAxC,GAAA,IAAAwC,CAAA,CAAAC,GAAA,IAAA7C,MAAA,CAAAC,cAAA,CAAAoC,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAF,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA", "ignoreList": []}