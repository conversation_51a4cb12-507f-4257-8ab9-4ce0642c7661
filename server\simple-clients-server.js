require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Test de connexion à la base de données
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie');
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error.message);
    return false;
  }
}

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} clients récupérés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client spécifique
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}/contracts`);

    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email,
        s.nom as secteur_nom
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      LEFT JOIN secteur s ON cl.ids = s.ids
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    const result = await pool.query(query, [id]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
      client_id: parseInt(id)
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours
      FROM consommation 
      WHERE idcont = $1 
      ORDER BY periode DESC 
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée pour le contrat ${id}`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur clients simple fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    routes: [
      'GET /api/clients',
      'GET /api/clients/:id/contracts',
      'GET /api/contracts/:id/last-consommation'
    ]
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
const PORT = 3002;

async function startServer() {
  // Tester la connexion à la base de données
  const dbConnected = await testConnection();
  
  if (!dbConnected) {
    console.error('❌ Impossible de se connecter à la base de données. Arrêt du serveur.');
    process.exit(1);
  }

  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur clients simple démarré sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/clients (liste clients)');
    console.log('  - GET  /api/clients/:id/contracts (contrats du client)');
    console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
    console.log('\n✅ Prêt à recevoir les requêtes !');
  });
}

// Démarrer le serveur
startServer();

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non capturée:', err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Promesse rejetée:', err);
  process.exit(1);
});
