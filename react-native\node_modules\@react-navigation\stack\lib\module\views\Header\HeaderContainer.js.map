{"version": 3, "names": ["getHeaderTitle", "HeaderBackContext", "NavigationContext", "NavigationRouteContext", "React", "Animated", "StyleSheet", "View", "forNoAnimation", "forSlideLeft", "forSlideRight", "forSlideUp", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode", "scenes", "layout", "getPreviousScene", "getFocusedRoute", "onContentHeightChange", "style", "focusedRoute", "parentHeaderBack", "useContext", "slice", "map", "scene", "i", "self", "length", "header", "headerMode", "headerShown", "headerTransparent", "headerStyleInterpolator", "descriptor", "options", "isFocused", "key", "route", "previousScene", "headerBack", "title", "name", "previousDescriptor", "nextDescriptor", "previousHeaderShown", "previousHeaderMode", "nextHeaderlessScene", "find", "currentHeaderShown", "currentHeaderMode", "gestureDirection", "nextHeaderlessGestureDirection", "isHeaderStatic", "props", "back", "progress", "navigation", "styleInterpolator", "e", "height", "nativeEvent", "undefined", "styles", "create", "position", "top", "left", "right"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderContainer.tsx"], "mappings": "AAAA,SAASA,cAAc,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC9E,SACEC,iBAAiB,EACjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAaC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAE/E,SACEC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,UAAU,QACL,kDAAkD;AAQzD,OAAOC,MAAM,MAAM,UAAU;AAe7B,eAAe,SAASC,eAAe,OAQ7B;EAAA,IAR8B;IACtCC,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC,gBAAgB;IAChBC,eAAe;IACfC,qBAAqB;IACrBC;EACK,CAAC;EACN,MAAMC,YAAY,GAAGH,eAAe,EAAE;EACtC,MAAMI,gBAAgB,GAAGlB,KAAK,CAACmB,UAAU,CAACtB,iBAAiB,CAAC;EAE5D,oBACE,oBAAC,QAAQ,CAAC,IAAI;IAAC,aAAa,EAAC,UAAU;IAAC,KAAK,EAAEmB;EAAM,GAClDL,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,KAAK;IAAA;IACxC,IAAKd,IAAI,KAAK,QAAQ,IAAIa,CAAC,KAAKC,IAAI,CAACC,MAAM,GAAG,CAAC,IAAK,CAACH,KAAK,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,MAAM;MACJI,MAAM;MACNC,UAAU;MACVC,WAAW,GAAG,IAAI;MAClBC,iBAAiB;MACjBC;IACF,CAAC,GAAGR,KAAK,CAACS,UAAU,CAACC,OAAO;IAE5B,IAAIL,UAAU,KAAKjB,IAAI,IAAI,CAACkB,WAAW,EAAE;MACvC,OAAO,IAAI;IACb;IAEA,MAAMK,SAAS,GAAGhB,YAAY,CAACiB,GAAG,KAAKZ,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GAAG;IACjE,MAAME,aAAa,GAAGvB,gBAAgB,CAAC;MACrCsB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI;IAC1B,CAAC,CAAC;IAEF,IAAIE,UAAU,GAAGnB,gBAAgB;IAEjC,IAAIkB,aAAa,EAAE;MACjB,MAAM;QAAEJ,OAAO;QAAEG;MAAM,CAAC,GAAGC,aAAa,CAACL,UAAU;MAEnDM,UAAU,GAAGD,aAAa,GACtB;QAAEE,KAAK,EAAE1C,cAAc,CAACoC,OAAO,EAAEG,KAAK,CAACI,IAAI;MAAE,CAAC,GAC9CrB,gBAAgB;IACtB;;IAEA;IACA;IACA,MAAMsB,kBAAkB,YAAGhB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,0CAAX,MAAaQ,UAAU;IAClD,MAAMU,cAAc,aAAGjB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,2CAAX,OAAaQ,UAAU;IAE9C,MAAM;MACJH,WAAW,EAAEc,mBAAmB,GAAG,IAAI;MACvCf,UAAU,EAAEgB;IACd,CAAC,GAAG,CAAAH,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAER,OAAO,KAAI,CAAC,CAAC;;IAErC;IACA;IACA,MAAMY,mBAAmB,GAAGpB,IAAI,CAACJ,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAACsB,IAAI,CAAEvB,KAAK,IAAK;MAC5D,MAAM;QACJM,WAAW,EAAEkB,kBAAkB,GAAG,IAAI;QACtCnB,UAAU,EAAEoB;MACd,CAAC,GAAG,CAAAzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;MAEnC,OAAOc,kBAAkB,KAAK,KAAK,IAAIC,iBAAiB,KAAK,QAAQ;IACvE,CAAC,CAAC;IAEF,MAAM;MAAEC,gBAAgB,EAAEC;IAA+B,CAAC,GACxD,CAAAL,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEb,UAAU,CAACC,OAAO,KAAI,CAAC,CAAC;IAE/C,MAAMkB,cAAc,GACjB,CAACR,mBAAmB,KAAK,KAAK,IAAIC,kBAAkB,KAAK,QAAQ;IAChE;IACA;IACA,CAACF,cAAc,IACjBG,mBAAmB;IAErB,MAAMO,KAAuB,GAAG;MAC9BvC,MAAM;MACNwC,IAAI,EAAEf,UAAU;MAChBgB,QAAQ,EAAE/B,KAAK,CAAC+B,QAAQ;MACxBrB,OAAO,EAAEV,KAAK,CAACS,UAAU,CAACC,OAAO;MACjCG,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;MAC7BmB,UAAU,EAAEhC,KAAK,CAACS,UAAU,CACzBuB,UAAgD;MACnDC,iBAAiB,EACf7C,IAAI,KAAK,OAAO,GACZwC,cAAc,GACZD,8BAA8B,KAAK,UAAU,IAC7CA,8BAA8B,KAAK,mBAAmB,GACpD1C,UAAU,GACV0C,8BAA8B,KAAK,qBAAqB,GACxD3C,aAAa,GACbD,YAAY,GACdyB,uBAAuB,GACzB1B;IACR,CAAC;IAED,oBACE,oBAAC,iBAAiB,CAAC,QAAQ;MACzB,GAAG,EAAEkB,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GAAI;MAChC,KAAK,EAAEZ,KAAK,CAACS,UAAU,CAACuB;IAAW,gBAEnC,oBAAC,sBAAsB,CAAC,QAAQ;MAAC,KAAK,EAAEhC,KAAK,CAACS,UAAU,CAACI;IAAM,gBAC7D,oBAAC,IAAI;MACH,QAAQ,EACNpB,qBAAqB,GAChByC,CAAC,IAAK;QACL,MAAM;UAAEC;QAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAAC9C,MAAM;QAEvCG,qBAAqB,CAAC;UACpBoB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;UAC7BsB;QACF,CAAC,CAAC;MACJ,CAAC,GACDE,SACL;MACD,aAAa,EAAE1B,SAAS,GAAG,UAAU,GAAG,MAAO;MAC/C,2BAA2B,EAAE,CAACA,SAAU;MACxC,yBAAyB,EACvBA,SAAS,GAAG,MAAM,GAAG,qBACtB;MACD,KAAK;MACH;MACA;MACCvB,IAAI,KAAK,OAAO,IAAI,CAACuB,SAAS,IAAKJ,iBAAiB,GACjD+B,MAAM,CAAClC,MAAM,GACb;IACL,GAEAA,MAAM,KAAKiC,SAAS,GAAGjC,MAAM,CAACyB,KAAK,CAAC,gBAAG,oBAAC,MAAM,EAAKA,KAAK,CAAI,CACxD,CACyB,CACP;EAEjC,CAAC,CAAC,CACY;AAEpB;AAEA,MAAMS,MAAM,GAAG1D,UAAU,CAAC2D,MAAM,CAAC;EAC/BnC,MAAM,EAAE;IACNoC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC"}