{"version": 3, "sources": ["../../../src/lint/index.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport type { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\n\nexport const expoLint: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Other options are parsed manually.\n      '--help': <PERSON><PERSON><PERSON>,\n      '--no-cache': <PERSON><PERSON><PERSON>,\n      '--fix': <PERSON><PERSON><PERSON>,\n      '--quiet': <PERSON><PERSON><PERSON>,\n      '--no-ignore': <PERSON><PERSON><PERSON>,\n\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow other options, we'll throw an error if unexpected values are passed.\n      permissive: true,\n    }\n  );\n\n  if (args['--help']) {\n    printHelp(\n      chalk`Lint all files in {bold /src}, {bold /app}, {bold /components} directories with ESLint`,\n      chalk`npx expo lint {dim [path...] -- [eslint options]}`,\n\n      [\n        chalk`[path...]                  List of files and directories to lint`,\n        chalk`--ext {dim <string>}             Additional file extensions to lint. {dim Default: .js, .jsx, .ts, .tsx, .mjs, .cjs}`,\n        chalk`--config {dim <path>}            Custom ESLint config file`,\n        `--no-cache                 Check all files, instead of changes between runs`,\n        `--fix                      Automatically fix problems`,\n        chalk`--fix-type {dim <string>}        Specify the types of fixes to apply. {dim Example: problem, suggestion, layout}`,\n        `--no-ignore                Disable use of ignore files and patterns`,\n        chalk`--ignore-pattern {dim <string>}  Patterns of files to ignore`,\n        `--quiet                    Only report errors`,\n        chalk`--max-warnings {dim <number>}    Number of warnings to trigger nonzero exit code`,\n        `-h, --help                 Usage info`,\n      ].join('\\n'),\n      [\n        '',\n        chalk`  Additional options can be passed to {bold npx eslint} by using {bold --}`,\n        chalk`    {dim $} npx expo lint -- --no-error-on-unmatched-pattern`,\n        chalk`    {dim >} npx eslint --no-error-on-unmatched-pattern {dim ...}`,\n        '',\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo lint -h` shows as fast as possible.\n  const { lintAsync } = require('./lintAsync') as typeof import('./lintAsync');\n  const { logCmdError } = require('../utils/errors') as typeof import('../utils/errors');\n  const { resolveArgsAsync } = require('./resolveOptions') as typeof import('./resolveOptions');\n\n  const { variadic, options, extras } = await resolveArgsAsync(process.argv.slice(3)).catch(\n    logCmdError\n  );\n  return lintAsync(variadic, options, extras).catch(logCmdError);\n};\n"], "names": ["expoLint", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "printHelp", "chalk", "join", "lintAsync", "require", "logCmdError", "resolveArgsAsync", "variadic", "options", "extras", "process", "slice", "catch"], "mappings": ";;;;+BAKaA;;;eAAAA;;;;gEALK;;;;;;sBAG+B;;;;;;AAE1C,MAAMA,WAAoB,OAAOC;IACtC,MAAMC,OAAOC,IAAAA,2BAAqB,EAChC;QACE,qCAAqC;QACrC,UAAUC;QACV,cAAcA;QACd,SAASA;QACT,WAAWA;QACX,eAAeA;QAEf,UAAU;QACV,MAAM;IACR,GACA;QACEH;QACA,6EAA6E;QAC7EI,YAAY;IACd;IAGF,IAAIH,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACPC,IAAAA,gBAAK,CAAA,CAAC,sFAAsF,CAAC,EAC7FA,IAAAA,gBAAK,CAAA,CAAC,iDAAiD,CAAC,EAExD;YACEA,IAAAA,gBAAK,CAAA,CAAC,gEAAgE,CAAC;YACvEA,IAAAA,gBAAK,CAAA,CAAC,oHAAoH,CAAC;YAC3HA,IAAAA,gBAAK,CAAA,CAAC,0DAA0D,CAAC;YACjE,CAAC,2EAA2E,CAAC;YAC7E,CAAC,qDAAqD,CAAC;YACvDA,IAAAA,gBAAK,CAAA,CAAC,gHAAgH,CAAC;YACvH,CAAC,mEAAmE,CAAC;YACrEA,IAAAA,gBAAK,CAAA,CAAC,4DAA4D,CAAC;YACnE,CAAC,6CAA6C,CAAC;YAC/CA,IAAAA,gBAAK,CAAA,CAAC,gFAAgF,CAAC;YACvF,CAAC,qCAAqC,CAAC;SACxC,CAACC,IAAI,CAAC,OACP;YACE;YACAD,IAAAA,gBAAK,CAAA,CAAC,0EAA0E,CAAC;YACjFA,IAAAA,gBAAK,CAAA,CAAC,4DAA4D,CAAC;YACnEA,IAAAA,gBAAK,CAAA,CAAC,gEAAgE,CAAC;YACvE;SACD,CAACC,IAAI,CAAC;IAEX;IAEA,sFAAsF;IACtF,MAAM,EAAEC,SAAS,EAAE,GAAGC,QAAQ;IAC9B,MAAM,EAAEC,WAAW,EAAE,GAAGD,QAAQ;IAChC,MAAM,EAAEE,gBAAgB,EAAE,GAAGF,QAAQ;IAErC,MAAM,EAAEG,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAG,MAAMH,iBAAiBI,QAAQf,IAAI,CAACgB,KAAK,CAAC,IAAIC,KAAK,CACvFP;IAEF,OAAOF,UAAUI,UAAUC,SAASC,QAAQG,KAAK,CAACP;AACpD"}