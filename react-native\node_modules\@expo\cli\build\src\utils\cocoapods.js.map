{"version": 3, "sources": ["../../../src/utils/cocoapods.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport JsonFile from '@expo/json-file';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ensureDirectoryAsync } from './dir';\nimport { env } from './env';\nimport { AbortCommandError } from './errors';\nimport { logNewSection } from './ora';\nimport * as Log from '../log';\nimport { hashForDependencyMap } from '../prebuild/updatePackageJson';\n\ntype PackageChecksums = {\n  /** checksum for the `package.json` dependency object. */\n  dependencies: string;\n  /** checksum for the `package.json` devDependency object. */\n  devDependencies: string;\n};\n\nconst PROJECT_PREBUILD_SETTINGS = '.expo/prebuild';\nconst CACHED_PACKAGE_JSON = 'cached-packages.json';\n\nfunction getTempPrebuildFolder(projectRoot: string): string {\n  return path.join(projectRoot, PROJECT_PREBUILD_SETTINGS);\n}\n\nfunction hasNewDependenciesSinceLastBuild(\n  projectRoot: string,\n  packageChecksums: PackageChecksums\n): boolean {\n  // TODO: Maybe comparing lock files would be better...\n  const templateDirectory = getTempPrebuildFolder(projectRoot);\n  const tempPkgJsonPath = path.join(templateDirectory, CACHED_PACKAGE_JSON);\n  if (!fs.existsSync(tempPkgJsonPath)) {\n    return true;\n  }\n  const { dependencies, devDependencies } = JsonFile.read(tempPkgJsonPath);\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies = packageChecksums.dependencies !== dependencies;\n  const hasNewDevDependencies = packageChecksums.devDependencies !== devDependencies;\n\n  return hasNewDependencies || hasNewDevDependencies;\n}\n\nfunction createPackageChecksums(pkg: PackageJSONConfig): PackageChecksums {\n  return {\n    dependencies: hashForDependencyMap(pkg.dependencies || {}),\n    devDependencies: hashForDependencyMap(pkg.devDependencies || {}),\n  };\n}\n\n/** @returns `true` if the package.json dependency hash does not match the cached hash from the last run. */\nexport async function hasPackageJsonDependencyListChangedAsync(\n  projectRoot: string\n): Promise<boolean> {\n  const pkg = getPackageJson(projectRoot);\n\n  const packages = createPackageChecksums(pkg);\n  const hasNewDependencies = hasNewDependenciesSinceLastBuild(projectRoot, packages);\n\n  // Cache package.json\n  await ensureDirectoryAsync(getTempPrebuildFolder(projectRoot));\n  const templateDirectory = path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON);\n  await JsonFile.writeAsync(templateDirectory, packages);\n\n  return hasNewDependencies;\n}\n\nexport async function installCocoaPodsAsync(projectRoot: string): Promise<boolean> {\n  let step = logNewSection('Installing CocoaPods...');\n  if (process.platform !== 'darwin') {\n    step.succeed('Skipped installing CocoaPods because operating system is not on macOS.');\n    return false;\n  }\n\n  const packageManager = new PackageManager.CocoaPodsPackageManager({\n    cwd: path.join(projectRoot, 'ios'),\n    silent: !(env.EXPO_DEBUG || env.CI),\n  });\n\n  if (!(await packageManager.isCLIInstalledAsync())) {\n    try {\n      // prompt user -- do you want to install cocoapods right now?\n      step.text = 'CocoaPods CLI not found in your PATH, installing it now.';\n      step.stopAndPersist();\n      await PackageManager.CocoaPodsPackageManager.installCLIAsync({\n        nonInteractive: true,\n        spawnOptions: {\n          ...packageManager.options,\n          // Don't silence this part\n          stdio: ['inherit', 'inherit', 'pipe'],\n        },\n      });\n      step.succeed('Installed CocoaPods CLI.');\n      step = logNewSection('Running `pod install` in the `ios` directory.');\n    } catch (error: any) {\n      step.stopAndPersist({\n        symbol: '⚠️ ',\n        text: chalk.red('Unable to install the CocoaPods CLI.'),\n      });\n      if (error instanceof PackageManager.CocoaPodsError) {\n        Log.log(error.message);\n      } else {\n        Log.log(`Unknown error: ${error.message}`);\n      }\n      return false;\n    }\n  }\n\n  try {\n    await packageManager.installAsync({ spinner: step });\n    // Create cached list for later\n    await hasPackageJsonDependencyListChangedAsync(projectRoot).catch(() => null);\n    step.succeed('Installed CocoaPods');\n    return true;\n  } catch (error: any) {\n    step.stopAndPersist({\n      symbol: '⚠️ ',\n      text: chalk.red('Something went wrong running `pod install` in the `ios` directory.'),\n    });\n    if (error instanceof PackageManager.CocoaPodsError) {\n      Log.log(error.message);\n    } else {\n      Log.log(`Unknown error: ${error.message}`);\n    }\n    return false;\n  }\n}\n\nfunction doesProjectUseCocoaPods(projectRoot: string): boolean {\n  return fs.existsSync(path.join(projectRoot, 'ios', 'Podfile'));\n}\n\nfunction isLockfileCreated(projectRoot: string): boolean {\n  const podfileLockPath = path.join(projectRoot, 'ios', 'Podfile.lock');\n  return fs.existsSync(podfileLockPath);\n}\n\nfunction isPodFolderCreated(projectRoot: string): boolean {\n  const podFolderPath = path.join(projectRoot, 'ios', 'Pods');\n  return fs.existsSync(podFolderPath);\n}\n\n// TODO: Same process but with app.config changes + default plugins.\n// This will ensure the user is prompted for extra setup.\nexport async function maybePromptToSyncPodsAsync(projectRoot: string) {\n  if (!doesProjectUseCocoaPods(projectRoot)) {\n    // Project does not use CocoaPods\n    return;\n  }\n  if (!isLockfileCreated(projectRoot) || !isPodFolderCreated(projectRoot)) {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n    return;\n  }\n\n  // Getting autolinked packages can be heavy, optimize around checking every time.\n  if (!(await hasPackageJsonDependencyListChangedAsync(projectRoot))) {\n    return;\n  }\n\n  await promptToInstallPodsAsync(projectRoot, []);\n}\n\nasync function promptToInstallPodsAsync(projectRoot: string, missingPods?: string[]) {\n  if (missingPods?.length) {\n    Log.log(\n      `Could not find the following native modules: ${missingPods\n        .map((pod) => chalk.bold(pod))\n        .join(', ')}. Did you forget to run \"${chalk.bold('pod install')}\" ?`\n    );\n  }\n\n  try {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n  } catch (error) {\n    await fs.promises.rm(path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON), {\n      recursive: true,\n      force: true,\n    });\n    throw error;\n  }\n}\n"], "names": ["hasPackageJsonDependencyListChangedAsync", "installCocoaPodsAsync", "maybePromptToSyncPodsAsync", "PROJECT_PREBUILD_SETTINGS", "CACHED_PACKAGE_JSON", "getTempPrebuildFolder", "projectRoot", "path", "join", "hasNewDependenciesSinceLastBuild", "packageChecksums", "templateDirectory", "tempPkgJsonPath", "fs", "existsSync", "dependencies", "devDependencies", "JsonFile", "read", "hasNewDependencies", "hasNewDevDependencies", "createPackageChecksums", "pkg", "hashForDependencyMap", "getPackageJson", "packages", "ensureDirectoryAsync", "writeAsync", "step", "logNewSection", "process", "platform", "succeed", "packageManager", "PackageManager", "CocoaPodsPackageManager", "cwd", "silent", "env", "EXPO_DEBUG", "CI", "isCLIInstalledAsync", "text", "stopAndPersist", "installCLIAsync", "nonInteractive", "spawnOptions", "options", "stdio", "error", "symbol", "chalk", "red", "CocoaPodsError", "Log", "log", "message", "installAsync", "spinner", "catch", "doesProjectUseCocoaPods", "isLockfileCreated", "podfileLockPath", "isPodFolderCreated", "podFolderPath", "AbortCommandError", "promptToInstallPodsAsync", "missingPods", "length", "map", "pod", "bold", "promises", "rm", "recursive", "force"], "mappings": ";;;;;;;;;;;IAsDsBA,wCAAwC;eAAxCA;;IAgBAC,qBAAqB;eAArBA;;IA6EAC,0BAA0B;eAA1BA;;;;yBAnJ4B;;;;;;;gEAC7B;;;;;;;iEACW;;;;;;;gEACd;;;;;;;gEACH;;;;;;;gEACE;;;;;;qBAEoB;qBACjB;wBACc;qBACJ;6DACT;mCACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC,MAAMC,4BAA4B;AAClC,MAAMC,sBAAsB;AAE5B,SAASC,sBAAsBC,WAAmB;IAChD,OAAOC,eAAI,CAACC,IAAI,CAACF,aAAaH;AAChC;AAEA,SAASM,iCACPH,WAAmB,EACnBI,gBAAkC;IAElC,sDAAsD;IACtD,MAAMC,oBAAoBN,sBAAsBC;IAChD,MAAMM,kBAAkBL,eAAI,CAACC,IAAI,CAACG,mBAAmBP;IACrD,IAAI,CAACS,aAAE,CAACC,UAAU,CAACF,kBAAkB;QACnC,OAAO;IACT;IACA,MAAM,EAAEG,YAAY,EAAEC,eAAe,EAAE,GAAGC,mBAAQ,CAACC,IAAI,CAACN;IACxD,yGAAyG;IACzG,MAAMO,qBAAqBT,iBAAiBK,YAAY,KAAKA;IAC7D,MAAMK,wBAAwBV,iBAAiBM,eAAe,KAAKA;IAEnE,OAAOG,sBAAsBC;AAC/B;AAEA,SAASC,uBAAuBC,GAAsB;IACpD,OAAO;QACLP,cAAcQ,IAAAA,uCAAoB,EAACD,IAAIP,YAAY,IAAI,CAAC;QACxDC,iBAAiBO,IAAAA,uCAAoB,EAACD,IAAIN,eAAe,IAAI,CAAC;IAChE;AACF;AAGO,eAAehB,yCACpBM,WAAmB;IAEnB,MAAMgB,MAAME,IAAAA,wBAAc,EAAClB;IAE3B,MAAMmB,WAAWJ,uBAAuBC;IACxC,MAAMH,qBAAqBV,iCAAiCH,aAAamB;IAEzE,qBAAqB;IACrB,MAAMC,IAAAA,yBAAoB,EAACrB,sBAAsBC;IACjD,MAAMK,oBAAoBJ,eAAI,CAACC,IAAI,CAACH,sBAAsBC,cAAcF;IACxE,MAAMa,mBAAQ,CAACU,UAAU,CAAChB,mBAAmBc;IAE7C,OAAON;AACT;AAEO,eAAelB,sBAAsBK,WAAmB;IAC7D,IAAIsB,OAAOC,IAAAA,kBAAa,EAAC;IACzB,IAAIC,QAAQC,QAAQ,KAAK,UAAU;QACjCH,KAAKI,OAAO,CAAC;QACb,OAAO;IACT;IAEA,MAAMC,iBAAiB,IAAIC,CAAAA,iBAAa,EAAEC,uBAAuB,CAAC;QAChEC,KAAK7B,eAAI,CAACC,IAAI,CAACF,aAAa;QAC5B+B,QAAQ,CAAEC,CAAAA,QAAG,CAACC,UAAU,IAAID,QAAG,CAACE,EAAE,AAAD;IACnC;IAEA,IAAI,CAAE,MAAMP,eAAeQ,mBAAmB,IAAK;QACjD,IAAI;YACF,6DAA6D;YAC7Db,KAAKc,IAAI,GAAG;YACZd,KAAKe,cAAc;YACnB,MAAMT,kBAAeC,uBAAuB,CAACS,eAAe,CAAC;gBAC3DC,gBAAgB;gBAChBC,cAAc;oBACZ,GAAGb,eAAec,OAAO;oBACzB,0BAA0B;oBAC1BC,OAAO;wBAAC;wBAAW;wBAAW;qBAAO;gBACvC;YACF;YACApB,KAAKI,OAAO,CAAC;YACbJ,OAAOC,IAAAA,kBAAa,EAAC;QACvB,EAAE,OAAOoB,OAAY;YACnBrB,KAAKe,cAAc,CAAC;gBAClBO,QAAQ;gBACRR,MAAMS,gBAAK,CAACC,GAAG,CAAC;YAClB;YACA,IAAIH,iBAAiBf,kBAAemB,cAAc,EAAE;gBAClDC,KAAIC,GAAG,CAACN,MAAMO,OAAO;YACvB,OAAO;gBACLF,KAAIC,GAAG,CAAC,CAAC,eAAe,EAAEN,MAAMO,OAAO,EAAE;YAC3C;YACA,OAAO;QACT;IACF;IAEA,IAAI;QACF,MAAMvB,eAAewB,YAAY,CAAC;YAAEC,SAAS9B;QAAK;QAClD,+BAA+B;QAC/B,MAAM5B,yCAAyCM,aAAaqD,KAAK,CAAC,IAAM;QACxE/B,KAAKI,OAAO,CAAC;QACb,OAAO;IACT,EAAE,OAAOiB,OAAY;QACnBrB,KAAKe,cAAc,CAAC;YAClBO,QAAQ;YACRR,MAAMS,gBAAK,CAACC,GAAG,CAAC;QAClB;QACA,IAAIH,iBAAiBf,kBAAemB,cAAc,EAAE;YAClDC,KAAIC,GAAG,CAACN,MAAMO,OAAO;QACvB,OAAO;YACLF,KAAIC,GAAG,CAAC,CAAC,eAAe,EAAEN,MAAMO,OAAO,EAAE;QAC3C;QACA,OAAO;IACT;AACF;AAEA,SAASI,wBAAwBtD,WAAmB;IAClD,OAAOO,aAAE,CAACC,UAAU,CAACP,eAAI,CAACC,IAAI,CAACF,aAAa,OAAO;AACrD;AAEA,SAASuD,kBAAkBvD,WAAmB;IAC5C,MAAMwD,kBAAkBvD,eAAI,CAACC,IAAI,CAACF,aAAa,OAAO;IACtD,OAAOO,aAAE,CAACC,UAAU,CAACgD;AACvB;AAEA,SAASC,mBAAmBzD,WAAmB;IAC7C,MAAM0D,gBAAgBzD,eAAI,CAACC,IAAI,CAACF,aAAa,OAAO;IACpD,OAAOO,aAAE,CAACC,UAAU,CAACkD;AACvB;AAIO,eAAe9D,2BAA2BI,WAAmB;IAClE,IAAI,CAACsD,wBAAwBtD,cAAc;QACzC,iCAAiC;QACjC;IACF;IACA,IAAI,CAACuD,kBAAkBvD,gBAAgB,CAACyD,mBAAmBzD,cAAc;QACvE,IAAI,CAAE,MAAML,sBAAsBK,cAAe;YAC/C,MAAM,IAAI2D,yBAAiB;QAC7B;QACA;IACF;IAEA,iFAAiF;IACjF,IAAI,CAAE,MAAMjE,yCAAyCM,cAAe;QAClE;IACF;IAEA,MAAM4D,yBAAyB5D,aAAa,EAAE;AAChD;AAEA,eAAe4D,yBAAyB5D,WAAmB,EAAE6D,WAAsB;IACjF,IAAIA,+BAAAA,YAAaC,MAAM,EAAE;QACvBd,KAAIC,GAAG,CACL,CAAC,6CAA6C,EAAEY,YAC7CE,GAAG,CAAC,CAACC,MAAQnB,gBAAK,CAACoB,IAAI,CAACD,MACxB9D,IAAI,CAAC,MAAM,yBAAyB,EAAE2C,gBAAK,CAACoB,IAAI,CAAC,eAAe,GAAG,CAAC;IAE3E;IAEA,IAAI;QACF,IAAI,CAAE,MAAMtE,sBAAsBK,cAAe;YAC/C,MAAM,IAAI2D,yBAAiB;QAC7B;IACF,EAAE,OAAOhB,OAAO;QACd,MAAMpC,aAAE,CAAC2D,QAAQ,CAACC,EAAE,CAAClE,eAAI,CAACC,IAAI,CAACH,sBAAsBC,cAAcF,sBAAsB;YACvFsE,WAAW;YACXC,OAAO;QACT;QACA,MAAM1B;IACR;AACF"}