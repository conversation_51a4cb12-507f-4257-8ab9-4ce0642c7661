{"version": 3, "names": ["isChromeDebugger", "isJest", "isWeb", "isWindowAvailable", "SensorType", "mockedRequestAnimationFrame", "logger", "ReanimatedError", "requestAnimationFrameImpl", "globalThis", "requestAnimationFrame", "JSReanimated", "nextSensorId", "sensors", "Map", "platform", "undefined", "makeShareableClone", "scheduleOnUI", "worklet", "createWorkletRuntime", "_name", "_initializer", "scheduleOnRuntime", "registerEventHandler", "_event<PERSON><PERSON><PERSON>", "_eventName", "_emitterReactTag", "unregisterEventHandler", "_", "enableLayoutAnimations", "warn", "configureLayoutAnimationBatch", "setShouldAnimateExitingForTag", "registerSensor", "sensorType", "interval", "_iosReferenceFrame", "<PERSON><PERSON><PERSON><PERSON>", "detectPlatform", "getSensorName", "window", "location", "protocol", "Platform", "WEB_IOS", "sensor", "initializeSensor", "addEventListener", "getSensorCallback", "start", "set", "ACCELEROMETER", "GRAVITY", "x", "y", "z", "WEB_ANDROID", "interfaceOrientation", "GYROSCOPE", "MAGNETIC_FIELD", "ROTATION", "qw", "qx", "qy", "qz", "quaternion", "yaw", "Math", "atan2", "pitch", "sin", "roll", "unregisterSensor", "id", "get", "stop", "delete", "subscribeForKeyboardEvents", "unsubscribeFromKeyboardEvents", "config", "referenceFrame", "frequency", "Accelerometer", "Gyroscope", "GravitySensor", "Magnetometer", "AbsoluteOrientationSensor", "userAgent", "navigator", "vendor", "opera", "UNKNOWN", "test", "WEB", "getViewProp", "_viewTag", "_propName", "_component", "_callback", "configureProps", "executeOnUIRuntimeSync", "_shareable"], "sourceRoot": "../../../src", "sources": ["js-reanimated/JSReanimated.ts"], "mappings": "AAAA,YAAY;;AACZ,SACEA,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,iBAAiB,QACZ,uBAAoB;AAE3B,SAASC,UAAU,QAAQ,mBAAgB;AAE3C,SAASC,2BAA2B,QAAQ,mCAAgC;AAE5E,SAASC,MAAM,QAAQ,oBAAW;AAClC,SAASC,eAAe,QAAQ,cAAW;;AAE3C;AACA;AACA;AACA,MAAMC,yBAAyB,GAC7BP,MAAM,CAAC,CAAC,IAAI,CAACQ,UAAU,CAACC,qBAAqB,GACzCL,2BAA2B,GAC3BI,UAAU,CAACC,qBAAqB;AAEtC,eAAe,MAAMC,YAAY,CAAC;EAChCC,YAAY,GAAG,CAAC;EAChBC,OAAO,GAAG,IAAIC,GAAG,CAAoB,CAAC;EACtCC,QAAQ,GAAcC,SAAS;EAE/BC,kBAAkBA,CAAA,EAAuB;IACvC,MAAM,IAAIV,eAAe,CACvB,4DACF,CAAC;EACH;EAEAW,YAAYA,CAAIC,OAAwB,EAAE;IACxC;IACAX,yBAAyB,CAACW,OAAO,CAAC;EACpC;EAEAC,oBAAoBA,CAClBC,KAAa,EACbC,YAAsC,EACtB;IAChB,MAAM,IAAIf,eAAe,CACvB,wDACF,CAAC;EACH;EAEAgB,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAIhB,eAAe,CACvB,qDACF,CAAC;EACH;EAEAiB,oBAAoBA,CAClBC,aAA8B,EAC9BC,UAAkB,EAClBC,gBAAwB,EAChB;IACR,MAAM,IAAIpB,eAAe,CACvB,wDACF,CAAC;EACH;EAEAqB,sBAAsBA,CAACC,CAAS,EAAQ;IACtC,MAAM,IAAItB,eAAe,CACvB,0DACF,CAAC;EACH;EAEAuB,sBAAsBA,CAAA,EAAG;IACvB,IAAI5B,KAAK,CAAC,CAAC,EAAE;MACXI,MAAM,CAACyB,IAAI,CAAC,iDAAiD,CAAC;IAChE,CAAC,MAAM,IAAI9B,MAAM,CAAC,CAAC,EAAE;MACnBK,MAAM,CAACyB,IAAI,CAAC,+CAA+C,CAAC;IAC9D,CAAC,MAAM,IAAI/B,gBAAgB,CAAC,CAAC,EAAE;MAC7BM,MAAM,CAACyB,IAAI,CAAC,0DAA0D,CAAC;IACzE,CAAC,MAAM;MACLzB,MAAM,CAACyB,IAAI,CAAC,4DAA4D,CAAC;IAC3E;EACF;EAEAC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,6BAA6BA,CAAA,EAAG;IAC9B;EAAA;EAGFC,cAAcA,CACZC,UAAsB,EACtBC,QAAgB,EAChBC,kBAA0B,EAC1BC,YAAmE,EAC3D;IACR,IAAI,CAACnC,iBAAiB,CAAC,CAAC,EAAE;MACxB;MACA;MACA,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAACY,QAAQ,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACuB,cAAc,CAAC,CAAC;IACvB;IAEA,IAAI,EAAE,IAAI,CAACC,aAAa,CAACL,UAAU,CAAC,IAAIM,MAAM,CAAC,EAAE;MAC/C;MACAnC,MAAM,CAACyB,IAAI,CACT,0BAA0B,IACvB7B,KAAK,CAAC,CAAC,IAAIwC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GACtC,uEAAuE,GACvE,EAAE,CAAC,IACN,IAAI,CAAC5B,QAAQ,KAAK6B,QAAQ,CAACC,OAAO,GAC/B,oLAAoL,GACpL,EAAE,CACV,CAAC;MACD,OAAO,CAAC,CAAC;IACX;IAEA,IAAI,IAAI,CAAC9B,QAAQ,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACuB,cAAc,CAAC,CAAC;IACvB;IAEA,MAAMO,MAAiB,GAAG,IAAI,CAACC,gBAAgB,CAACZ,UAAU,EAAEC,QAAQ,CAAC;IACrEU,MAAM,CAACE,gBAAgB,CACrB,SAAS,EACT,IAAI,CAACC,iBAAiB,CAACH,MAAM,EAAEX,UAAU,EAAEG,YAAY,CACzD,CAAC;IACDQ,MAAM,CAACI,KAAK,CAAC,CAAC;IAEd,IAAI,CAACrC,OAAO,CAACsC,GAAG,CAAC,IAAI,CAACvC,YAAY,EAAEkC,MAAM,CAAC;IAC3C,OAAO,IAAI,CAAClC,YAAY,EAAE;EAC5B;EAEAqC,iBAAiB,GAAGA,CAClBH,MAAiB,EACjBX,UAAsB,EACtBG,YAAmE,KAChE;IACH,QAAQH,UAAU;MAChB,KAAK/B,UAAU,CAACgD,aAAa;MAC7B,KAAKhD,UAAU,CAACiD,OAAO;QACrB,OAAO,MAAM;UACX,IAAI;YAAEC,CAAC;YAAEC,CAAC;YAAEC;UAAE,CAAC,GAAGV,MAAM;;UAExB;UACA,IAAI,IAAI,CAAC/B,QAAQ,KAAK6B,QAAQ,CAACa,WAAW,EAAE;YAC1C,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAACF,CAAC,EAAE,CAACC,CAAC,EAAE,CAACC,CAAC,CAAC;UAC1B;UACA;UACClB,YAAY,CAAS;YAAEgB,CAAC;YAAEC,CAAC;YAAEC,CAAC;YAAEE,oBAAoB,EAAE;UAAE,CAAC,CAAC;QAC7D,CAAC;MACH,KAAKtD,UAAU,CAACuD,SAAS;MACzB,KAAKvD,UAAU,CAACwD,cAAc;QAC5B,OAAO,MAAM;UACX,MAAM;YAAEN,CAAC;YAAEC,CAAC;YAAEC;UAAE,CAAC,GAAGV,MAAM;UAC1B;UACCR,YAAY,CAAS;YAAEgB,CAAC;YAAEC,CAAC;YAAEC,CAAC;YAAEE,oBAAoB,EAAE;UAAE,CAAC,CAAC;QAC7D,CAAC;MACH,KAAKtD,UAAU,CAACyD,QAAQ;QACtB,OAAO,MAAM;UACX,IAAI,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGnB,MAAM,CAACoB,UAAU;;UAExC;UACA,IAAI,IAAI,CAACnD,QAAQ,KAAK6B,QAAQ,CAACa,WAAW,EAAE;YAC1C,CAACO,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACA,EAAE,EAAE,CAACD,EAAE,CAAC;UACtB;;UAEA;UACA,MAAMG,GAAG,GAAG,CAACC,IAAI,CAACC,KAAK,CACrB,GAAG,IAAIL,EAAE,GAAGC,EAAE,GAAGH,EAAE,GAAGC,EAAE,CAAC,EACzBD,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;UACD,MAAMK,KAAK,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,IAAIR,EAAE,GAAGE,EAAE,GAAGH,EAAE,GAAGE,EAAE,CAAC,CAAC;UAClD,MAAMQ,IAAI,GAAG,CAACJ,IAAI,CAACC,KAAK,CACtB,GAAG,IAAIN,EAAE,GAAGC,EAAE,GAAGF,EAAE,GAAGG,EAAE,CAAC,EACzBH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EACrC,CAAC;UACD;UACC3B,YAAY,CAAS;YACpBwB,EAAE;YACFC,EAAE;YACFC,EAAE;YACFC,EAAE;YACFE,GAAG;YACHG,KAAK;YACLE,IAAI;YACJd,oBAAoB,EAAE;UACxB,CAAC,CAAC;QACJ,CAAC;IACL;EACF,CAAC;EAEDe,gBAAgBA,CAACC,EAAU,EAAQ;IACjC,MAAM5B,MAA6B,GAAG,IAAI,CAACjC,OAAO,CAAC8D,GAAG,CAACD,EAAE,CAAC;IAC1D,IAAI5B,MAAM,KAAK9B,SAAS,EAAE;MACxB8B,MAAM,CAAC8B,IAAI,CAAC,CAAC;MACb,IAAI,CAAC/D,OAAO,CAACgE,MAAM,CAACH,EAAE,CAAC;IACzB;EACF;EAEAI,0BAA0BA,CAACjD,CAAuB,EAAU;IAC1D,IAAI3B,KAAK,CAAC,CAAC,EAAE;MACXI,MAAM,CAACyB,IAAI,CAAC,kDAAkD,CAAC;IACjE,CAAC,MAAM,IAAI9B,MAAM,CAAC,CAAC,EAAE;MACnBK,MAAM,CAACyB,IAAI,CAAC,uDAAuD,CAAC;IACtE,CAAC,MAAM,IAAI/B,gBAAgB,CAAC,CAAC,EAAE;MAC7BM,MAAM,CAACyB,IAAI,CACT,kEACF,CAAC;IACH,CAAC,MAAM;MACLzB,MAAM,CAACyB,IAAI,CACT,6DACF,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX;EAEAgD,6BAA6BA,CAAClD,CAAS,EAAQ;IAC7C;EAAA;EAGFkB,gBAAgBA,CAACZ,UAAsB,EAAEC,QAAgB,EAAa;IACpE,MAAM4C,MAAM,GACV5C,QAAQ,IAAI,CAAC,GACT;MAAE6C,cAAc,EAAE;IAAS,CAAC,GAC5B;MAAEC,SAAS,EAAE,IAAI,GAAG9C;IAAS,CAAC;IACpC,QAAQD,UAAU;MAChB,KAAK/B,UAAU,CAACgD,aAAa;QAC3B,OAAO,IAAIX,MAAM,CAAC0C,aAAa,CAACH,MAAM,CAAC;MACzC,KAAK5E,UAAU,CAACuD,SAAS;QACvB,OAAO,IAAIlB,MAAM,CAAC2C,SAAS,CAACJ,MAAM,CAAC;MACrC,KAAK5E,UAAU,CAACiD,OAAO;QACrB,OAAO,IAAIZ,MAAM,CAAC4C,aAAa,CAACL,MAAM,CAAC;MACzC,KAAK5E,UAAU,CAACwD,cAAc;QAC5B,OAAO,IAAInB,MAAM,CAAC6C,YAAY,CAACN,MAAM,CAAC;MACxC,KAAK5E,UAAU,CAACyD,QAAQ;QACtB,OAAO,IAAIpB,MAAM,CAAC8C,yBAAyB,CAACP,MAAM,CAAC;IACvD;EACF;EAEAxC,aAAaA,CAACL,UAAsB,EAAU;IAC5C,QAAQA,UAAU;MAChB,KAAK/B,UAAU,CAACgD,aAAa;QAC3B,OAAO,eAAe;MACxB,KAAKhD,UAAU,CAACiD,OAAO;QACrB,OAAO,eAAe;MACxB,KAAKjD,UAAU,CAACuD,SAAS;QACvB,OAAO,WAAW;MACpB,KAAKvD,UAAU,CAACwD,cAAc;QAC5B,OAAO,cAAc;MACvB,KAAKxD,UAAU,CAACyD,QAAQ;QACtB,OAAO,2BAA2B;IACtC;EACF;EAEAtB,cAAcA,CAAA,EAAG;IACf,MAAMiD,SAAS,GAAGC,SAAS,CAACD,SAAS,IAAIC,SAAS,CAACC,MAAM,IAAIjD,MAAM,CAACkD,KAAK;IACzE,IAAIH,SAAS,KAAKxE,SAAS,EAAE;MAC3B,IAAI,CAACD,QAAQ,GAAG6B,QAAQ,CAACgD,OAAO;IAClC,CAAC,MAAM,IAAI,kBAAkB,CAACC,IAAI,CAACL,SAAS,CAAC,EAAE;MAC7C,IAAI,CAACzE,QAAQ,GAAG6B,QAAQ,CAACC,OAAO;IAClC,CAAC,MAAM,IAAI,UAAU,CAACgD,IAAI,CAACL,SAAS,CAAC,EAAE;MACrC,IAAI,CAACzE,QAAQ,GAAG6B,QAAQ,CAACa,WAAW;IACtC,CAAC,MAAM;MACL,IAAI,CAAC1C,QAAQ,GAAG6B,QAAQ,CAACkD,GAAG;IAC9B;EACF;EAEAC,WAAWA,CACTC,QAAgB,EAChBC,SAAiB,EACjBC,UAA4B,EAC5BC,SAA+B,EACnB;IACZ,MAAM,IAAI5F,eAAe,CAAC,+CAA+C,CAAC;EAC5E;EAEA6F,cAAcA,CAAA,EAAG;IACf,MAAM,IAAI7F,eAAe,CACvB,kDACF,CAAC;EACH;EAEA8F,sBAAsBA,CAAOC,UAA2B,EAAK;IAC3D,MAAM,IAAI/F,eAAe,CACvB,4DACF,CAAC;EACH;AACF;AAAC,IAEIqC,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA,EAARA,QAAQ", "ignoreList": []}