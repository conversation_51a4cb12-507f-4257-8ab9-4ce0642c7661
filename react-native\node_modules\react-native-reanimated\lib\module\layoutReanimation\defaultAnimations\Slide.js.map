{"version": 3, "names": ["ComplexAnimationBuilder", "SlideInRight", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "values", "animations", "originX", "targetOriginX", "windowWidth", "SlideInLeft", "SlideOutRight", "Math", "max", "currentOriginX", "SlideOutLeft", "min", "SlideInUp", "originY", "targetOriginY", "windowHeight", "SlideInDown", "SlideOutUp", "currentOriginY", "SlideOutDown"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultAnimations/Slide.ts"], "mappings": "AAAA,YAAY;;AASZ,SAASA,uBAAuB,QAAQ,8BAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,SACfD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,cAAc;EAElC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,YAAY,CAAC,CAAC;EAC3B;EAEAG,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CACpBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACG,aAAa,EAAEV,MAAM,CACxC;QACF,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAEF,MAAM,CAACG,aAAa,GAAGH,MAAM,CAACI,WAAW;UAClD,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,WAAW,SACdpB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIiB,WAAW,CAAC,CAAC;EAC1B;EAEAhB,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CACpBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACG,aAAa,EAAEV,MAAM,CACxC;QACF,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAEF,MAAM,CAACG,aAAa,GAAGH,MAAM,CAACI,WAAW;UAClD,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,aAAa,SAChBrB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,eAAe;EAEnC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIkB,aAAa,CAAC,CAAC;EAC5B;EAEAjB,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CACpBK,KAAK,EACLH,SAAS,CACPe,IAAI,CAACC,GAAG,CACNR,MAAM,CAACS,cAAc,GAAGT,MAAM,CAACI,WAAW,EAC1CJ,MAAM,CAACI,WACT,CAAC,EACDX,MACF,CACF;QACF,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAEF,MAAM,CAACS,cAAc;UAC9B,GAAGV;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,YAAY,SACfzB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,cAAc;EAElC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIsB,YAAY,CAAC,CAAC;EAC3B;EAEArB,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,OAAO,EAAEZ,aAAa,CACpBK,KAAK,EACLH,SAAS,CACPe,IAAI,CAACI,GAAG,CACNX,MAAM,CAACS,cAAc,GAAGT,MAAM,CAACI,WAAW,EAC1C,CAACJ,MAAM,CAACI,WACV,CAAC,EACDX,MACF,CACF;QACF,CAAC;QACDM,aAAa,EAAE;UACbG,OAAO,EAAEF,MAAM,CAACS,cAAc;UAC9B,GAAGV;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,SAAS,SACZ3B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,WAAW;EAE/B,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIwB,SAAS,CAAC,CAAC;EACxB;EAEAvB,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVY,OAAO,EAAEvB,aAAa,CACpBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACc,aAAa,EAAErB,MAAM,CACxC;QACF,CAAC;QACDM,aAAa,EAAE;UACbc,OAAO,EAAE,CAACb,MAAM,CAACe,YAAY;UAC7B,GAAGhB;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,WAAW,SACd/B,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI4B,WAAW,CAAC,CAAC;EAC1B;EAEA3B,KAAK,GAAGA,CAAA,KAAsD;IAC5D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVY,OAAO,EAAEvB,aAAa,CACpBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACc,aAAa,EAAErB,MAAM,CACxC;QACF,CAAC;QACDM,aAAa,EAAE;UACbc,OAAO,EAAEb,MAAM,CAACc,aAAa,GAAGd,MAAM,CAACe,YAAY;UACnD,GAAGhB;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,UAAU,SACbhC,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,YAAY;EAEhC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI6B,UAAU,CAAC,CAAC;EACzB;EAEA5B,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVY,OAAO,EAAEvB,aAAa,CACpBK,KAAK,EACLH,SAAS,CACPe,IAAI,CAACI,GAAG,CACNX,MAAM,CAACkB,cAAc,GAAGlB,MAAM,CAACe,YAAY,EAC3C,CAACf,MAAM,CAACe,YACV,CAAC,EACDtB,MACF,CACF;QACF,CAAC;QACDM,aAAa,EAAE;UAAEc,OAAO,EAAEb,MAAM,CAACkB,cAAc;UAAE,GAAGnB;QAAc,CAAC;QACnEF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,YAAY,SACflC,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,cAAc;EAElC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAI+B,YAAY,CAAC,CAAC;EAC3B;EAEA9B,KAAK,GAAGA,CAAA,KAAqD;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVY,OAAO,EAAEvB,aAAa,CACpBK,KAAK,EACLH,SAAS,CACPe,IAAI,CAACC,GAAG,CACNR,MAAM,CAACkB,cAAc,GAAGlB,MAAM,CAACe,YAAY,EAC3Cf,MAAM,CAACe,YACT,CAAC,EACDtB,MACF,CACF;QACF,CAAC;QACDM,aAAa,EAAE;UAAEc,OAAO,EAAEb,MAAM,CAACkB,cAAc;UAAE,GAAGnB;QAAc,CAAC;QACnEF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}