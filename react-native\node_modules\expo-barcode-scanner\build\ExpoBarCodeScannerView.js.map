{"version": 3, "file": "ExpoBarCodeScannerView.js", "sourceRoot": "", "sources": ["../src/ExpoBarCodeScannerView.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAG7D,MAAM,sBAAsB,GAC1B,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;AAEjD,eAAe,sBAAsB,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport React from 'react';\n\nconst ExpoBarCodeScannerView: React.ComponentType<any> =\n  requireNativeViewManager('ExpoBarCodeScanner');\n\nexport default ExpoBarCodeScannerView;\n"]}