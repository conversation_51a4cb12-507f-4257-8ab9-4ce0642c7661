const { Pool } = require('pg');

console.log('🔍 Test de l\'API contrats pour Benali Fatima (ID: 15)...');

// Configuration PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

async function testContractsQuery() {
  try {
    const clientId = 15; // ID de Benali Fatima
    
    console.log(`📥 Test de la requête pour le client ${clientId}`);

    // Requête exacte utilisée dans le serveur
    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        COALESCE(c.marquecompteur, 'Non définie') as marquecompteur,
        cl.nom,
        cl.prenom
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL...');
    const result = await pool.query(query, [clientId]);

    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`);
    
    if (result.rows.length > 0) {
      console.log('📋 Contrats trouvés:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur}`);
        console.log(`      Client: ${contract.nom} ${contract.prenom}`);
        console.log(`      Date contrat: ${contract.datecontract ? new Date(contract.datecontract).toLocaleDateString() : 'Non définie'}`);
        console.log('');
      });
      
      // Simuler la réponse JSON
      const apiResponse = {
        success: true,
        data: result.rows,
        count: result.rows.length,
        message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${clientId}`,
        client_id: parseInt(clientId)
      };
      
      console.log('📤 Réponse API simulée:');
      console.log(JSON.stringify(apiResponse, null, 2));
      
    } else {
      console.log('⚠️ Aucun contrat trouvé pour ce client');
    }

    await pool.end();
    return true;
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Exécuter le test
testContractsQuery().then((success) => {
  if (success) {
    console.log('\n🎉 Test terminé avec succès !');
  } else {
    console.log('\n❌ Test échoué');
  }
  process.exit(success ? 0 : 1);
});
