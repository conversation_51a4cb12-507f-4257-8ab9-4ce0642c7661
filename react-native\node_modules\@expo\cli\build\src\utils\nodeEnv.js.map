{"version": 3, "sources": ["../../../src/utils/nodeEnv.ts"], "sourcesContent": ["import * as env from '@expo/env';\n\n/**\n * Set the environment to production or development\n * lots of tools use this to determine if they should run in a dev mode.\n */\nexport function setNodeEnv(mode: 'development' | 'production') {\n  process.env.NODE_ENV = process.env.NODE_ENV || mode;\n  process.env.BABEL_ENV = process.env.BABEL_ENV || process.env.NODE_ENV;\n\n  // @ts-expect-error: Add support for external React libraries being loaded in the same process.\n  globalThis.__DEV__ = process.env.NODE_ENV !== 'production';\n}\n\n/**\n * Load the dotenv files into the current `process.env` scope.\n * Note, this requires `NODE_ENV` being set through `setNodeEnv`.\n */\nexport function loadEnvFiles(projectRoot: string, options?: Parameters<typeof env.load>[1]) {\n  return env.load(projectRoot, options);\n}\n"], "names": ["loadEnvFiles", "setNodeEnv", "mode", "process", "env", "NODE_ENV", "BABEL_ENV", "globalThis", "__DEV__", "projectRoot", "options", "load"], "mappings": ";;;;;;;;;;;IAkBgBA,YAAY;eAAZA;;IAZAC,UAAU;eAAVA;;;;iEANK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMd,SAASA,WAAWC,IAAkC;IAC3DC,QAAQC,GAAG,CAACC,QAAQ,GAAGF,QAAQC,GAAG,CAACC,QAAQ,IAAIH;IAC/CC,QAAQC,GAAG,CAACE,SAAS,GAAGH,QAAQC,GAAG,CAACE,SAAS,IAAIH,QAAQC,GAAG,CAACC,QAAQ;IAErE,+FAA+F;IAC/FE,WAAWC,OAAO,GAAGL,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAChD;AAMO,SAASL,aAAaS,WAAmB,EAAEC,OAAwC;IACxF,OAAON,OAAIO,IAAI,CAACF,aAAaC;AAC/B"}