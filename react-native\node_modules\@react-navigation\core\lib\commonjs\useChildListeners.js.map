{"version": 3, "names": ["useChildListeners", "current", "listeners", "React", "useRef", "action", "focus", "addListener", "useCallback", "type", "listener", "push", "removed", "index", "indexOf", "splice"], "sourceRoot": "../../src", "sources": ["useChildListeners.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAI/B;AACA;AACA;AACe,SAASA,iBAAiB,GAAG;EAC1C,MAAM;IAAEC,OAAO,EAAEC;EAAU,CAAC,GAAGC,KAAK,CAACC,MAAM,CAExC;IACDC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGJ,KAAK,CAACK,WAAW,CACnC,CAA8BC,IAAO,EAAEC,QAAwB,KAAK;IAClER,SAAS,CAACO,IAAI,CAAC,CAACE,IAAI,CAACD,QAAQ,CAAC;IAE9B,IAAIE,OAAO,GAAG,KAAK;IACnB,OAAO,MAAM;MACX,MAAMC,KAAK,GAAGX,SAAS,CAACO,IAAI,CAAC,CAACK,OAAO,CAACJ,QAAQ,CAAC;MAE/C,IAAI,CAACE,OAAO,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC1BD,OAAO,GAAG,IAAI;QACdV,SAAS,CAACO,IAAI,CAAC,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EACD,CAACX,SAAS,CAAC,CACZ;EAED,OAAO;IACLA,SAAS;IACTK;EACF,CAAC;AACH"}