{"version": 3, "file": "FileSystem.d.ts", "sourceRoot": "", "sources": ["../src/FileSystem.ts"], "names": [], "mappings": "AAIA,OAAO,EACL,eAAe,EACf,kBAAkB,EAClB,qCAAqC,EACrC,oBAAoB,EACpB,kBAAkB,EAClB,QAAQ,EAER,wBAAwB,EACxB,2CAA2C,EAE3C,uBAAuB,EACvB,sBAAsB,EAGtB,cAAc,EACd,cAAc,EACd,eAAe,EACf,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACrB,MAAM,oBAAoB,CAAC;AAe5B;;;;GAIG;AACH,eAAO,MAAM,iBAAiB,eAA6D,CAAC;AAE5F;;;;GAIG;AACH,eAAO,MAAM,cAAc,eAA0D,CAAC;AAEtF;;GAEG;AACH,eAAO,MAAM,eAAe,eAA2D,CAAC;AAExF;;;;;;GAMG;AACH,wBAAsB,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,WAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,CAKhG;AAED;;;;;GAKG;AACH,wBAAsB,iBAAiB,CACrC,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,MAAM,CAAC,CAKjB;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAsB,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CASzE;AAED;;;;;;GAMG;AACH,wBAAsB,kBAAkB,CACtC,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,IAAI,CAAC,CAKf;AAED;;;;GAIG;AACH,wBAAsB,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,eAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,CAK/F;AAED,wBAAsB,oCAAoC,IAAI,OAAO,CAAC,IAAI,CAAC,CAM1E;AAED;;;GAGG;AACH,wBAAsB,SAAS,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAKzE;AAED;;;;GAIG;AACH,wBAAsB,SAAS,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAKzE;AAED;;;;GAIG;AACH,wBAAsB,kBAAkB,CACtC,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,oBAAyB,GACjC,OAAO,CAAC,IAAI,CAAC,CAKf;AAED;;;;GAIG;AACH,wBAAsB,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAK3E;AAED;;;GAGG;AACH,wBAAsB,uBAAuB,IAAI,OAAO,CAAC,MAAM,CAAC,CAK/D;AAED;;;GAGG;AACH,wBAAsB,yBAAyB,IAAI,OAAO,CAAC,MAAM,CAAC,CAKjE;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAsB,aAAa,CACjC,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,eAAoB,GAC5B,OAAO,CAAC,wBAAwB,CAAC,CASnC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAsB,WAAW,CAC/B,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,uBAA4B,GACpC,OAAO,CAAC,sBAAsB,CAAC,CAWjC;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,uBAAuB,CACrC,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,eAAe,EACzB,QAAQ,CAAC,EAAE,qCAAqC,CAAC,oBAAoB,CAAC,EACtE,UAAU,CAAC,EAAE,MAAM,GAClB,iBAAiB,CAEnB;AAED,wBAAgB,gBAAgB,CAC9B,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,uBAAuB,EACjC,QAAQ,CAAC,EAAE,qCAAqC,CAAC,kBAAkB,CAAC,GACnE,UAAU,CAEZ;AAED,8BAAsB,gCAAgC,CACpD,CAAC,SAAS,oBAAoB,GAAG,kBAAkB;IAEnD,OAAO,CAAC,KAAK,CAAa;IAC1B,SAAS,CAAC,eAAe,UAAS;IAClC,OAAO,CAAC,YAAY,CAAC,CAA2B;IAGnC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAUzC,SAAS,CAAC,eAAe,IAAI,OAAO;IASpC,SAAS,KAAK,IAAI,IAAI,MAAM,CAE3B;IAED,SAAS,CAAC,QAAQ,CAAC,YAAY,IAAI,MAAM;IAEzC,SAAS,CAAC,QAAQ,CAAC,WAAW,IAAI,qCAAqC,CAAC,CAAC,CAAC,GAAG,SAAS;IAEtF,SAAS,CAAC,eAAe;IAkBzB,SAAS,CAAC,kBAAkB;CAO7B;AAED,qBAAa,UAAW,SAAQ,gCAAgC,CAAC,kBAAkB,CAAC;IAIhF,OAAO,CAAC,GAAG;IACX,OAAO,CAAC,OAAO;IAEf,OAAO,CAAC,QAAQ,CAAC;IANnB,OAAO,CAAC,OAAO,CAA0B;gBAG/B,GAAG,EAAE,MAAM,EACX,OAAO,EAAE,MAAM,EACvB,OAAO,CAAC,EAAE,uBAAuB,EACzB,QAAQ,CAAC,uEAA2D;IAe9E,SAAS,CAAC,YAAY,IAAI,MAAM;IAGhC,SAAS,CAAC,WAAW,IAAI,qCAAqC,CAAC,kBAAkB,CAAC,GAAG,SAAS;IAKjF,WAAW,IAAI,OAAO,CAAC,sBAAsB,GAAG,SAAS,CAAC;CAoBxE;AAED,qBAAa,iBAAkB,SAAQ,gCAAgC,CAAC,oBAAoB,CAAC;IAEzF,OAAO,CAAC,GAAG;IACX,OAAO,CAAC,QAAQ;IAChB,OAAO,CAAC,OAAO;IACf,OAAO,CAAC,QAAQ,CAAC;IACjB,OAAO,CAAC,UAAU,CAAC;gBAJX,GAAG,EAAE,MAAM,EACX,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,eAAoB,EAC7B,QAAQ,CAAC,yEAA6D,EACtE,UAAU,CAAC,oBAAQ;IAK7B,IAAW,OAAO,IAAI,MAAM,CAE3B;IAED,SAAS,CAAC,YAAY,IAAI,MAAM;IAIhC,SAAS,CAAC,WAAW,IAAI,qCAAqC,CAAC,oBAAoB,CAAC,GAAG,SAAS;IAIhG;;;OAGG;IACG,aAAa,IAAI,OAAO,CAAC,wBAAwB,GAAG,SAAS,CAAC;IAmBpE;;;;OAIG;IACG,UAAU,IAAI,OAAO,CAAC,kBAAkB,CAAC;IAuB/C;;;OAGG;IACG,WAAW,IAAI,OAAO,CAAC,wBAAwB,GAAG,SAAS,CAAC;IAmBlE;;;OAGG;IACH,OAAO,IAAI,kBAAkB;CAQ9B;AAQD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgFG;AACH,yBAAiB,sBAAsB,CAAC;IACtC;;;;;OAKG;IACH,SAAgB,wBAAwB,CAAC,UAAU,EAAE,MAAM,UAE1D;IAED;;;;;;OAMG;IACH,SAAsB,gCAAgC,CACpD,cAAc,GAAE,MAAM,GAAG,IAAW,GACnC,OAAO,CAAC,2CAA2C,CAAC,CAStD;IAED;;;;OAIG;IACH,SAAsB,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAQ1E;IAED;;;;;OAKG;IACH,SAAsB,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAQ5F;IAED;;;;;;OAMG;IACH,SAAsB,eAAe,CACnC,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,MAAM,CAAC,CAKjB;IAED;;OAEG;IACI,MAAM,kBAAkB,kDAAyB,CAAC;IACzD;;OAEG;IACI,MAAM,iBAAiB,iDAAwB,CAAC;IACvD;;OAEG;IACI,MAAM,WAAW,2CAAkB,CAAC;IAC3C;;OAEG;IACI,MAAM,SAAS,yCAAgB,CAAC;IACvC;;OAEG;IACI,MAAM,SAAS,yCAAgB,CAAC;CACxC"}