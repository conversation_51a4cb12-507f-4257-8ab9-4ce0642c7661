{"version": 3, "sources": ["eventReceiver.ts"], "names": ["gestureHandlerEventSubscription", "gestureHandlerStateChangeEventSubscription", "gestureStateManagers", "Map", "lastUpdateEvent", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "onGestureHandlerEvent", "handler", "handlerTag", "State", "UNDETERMINED", "state", "BEGAN", "handlers", "onBegin", "ACTIVE", "onStart", "END", "onEnd", "onFinalize", "undefined", "FAILED", "CANCELLED", "delete", "has", "set", "GestureStateManager", "create", "manager", "get", "TouchEventType", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "onUpdate", "onChange", "changeEventCalculator", "<PERSON><PERSON><PERSON><PERSON>", "nativeEvent", "onGestureStateChange", "onGestureEvent", "startListening", "stopListening", "DeviceEventEmitter", "addListener", "remove"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;AAMA;;AAEA;;AAKA,IAAIA,+BAA2D,GAAG,IAAlE;AACA,IAAIC,0CAAsE,GACxE,IADF;AAGA,MAAMC,oBAA0D,GAAG,IAAIC,GAAJ,EAAnE;AAKA,MAAMC,eAAmD,GAAG,EAA5D;;AAEA,SAASC,kBAAT,CACEC,KADF,EAEoC;AAClC;AACA,SAAOA,KAAK,CAACC,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACEF,KADF,EAE8B;AAC5B,SAAOA,KAAK,CAACG,SAAN,IAAmB,IAA1B;AACD;;AAEM,SAASC,qBAAT,CACLJ,KADK,EAEL;AAAA;;AACA,QAAMK,OAAO,GAAG,mCAAYL,KAAK,CAACM,UAAlB,CAAhB;;AAIA,MAAID,OAAJ,EAAa;AACX,QAAIN,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7B,UACEA,KAAK,CAACC,QAAN,KAAmBM,aAAMC,YAAzB,IACAR,KAAK,CAACS,KAAN,KAAgBF,aAAMG,KAFxB,EAGE;AAAA;;AACA,sDAAAL,OAAO,CAACM,QAAR,EAAiBC,OAAjB,wGAA2BZ,KAA3B;AACD,OALD,MAKO,IACL,CAACA,KAAK,CAACC,QAAN,KAAmBM,aAAMG,KAAzB,IACCV,KAAK,CAACC,QAAN,KAAmBM,aAAMC,YAD3B,KAEAR,KAAK,CAACS,KAAN,KAAgBF,aAAMM,MAHjB,EAIL;AAAA;;AACA,uDAAAR,OAAO,CAACM,QAAR,EAAiBG,OAAjB,yGAA2Bd,KAA3B;AACAF,QAAAA,eAAe,CAACO,OAAO,CAACM,QAAR,CAAiBL,UAAlB,CAAf,GAA+CN,KAA/C;AACD,OAPM,MAOA,IAAIA,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAACS,KAAzB,IAAkCT,KAAK,CAACS,KAAN,KAAgBF,aAAMQ,GAA5D,EAAiE;AAAA;;AACtE,YAAIf,KAAK,CAACC,QAAN,KAAmBM,aAAMM,MAA7B,EAAqC;AAAA;;AACnC,yDAAAR,OAAO,CAACM,QAAR,EAAiBK,KAAjB,yGAAyBhB,KAAzB,EAAgC,IAAhC;AACD;;AACD,uDAAAK,OAAO,CAACM,QAAR,EAAiBM,UAAjB,yGAA8BjB,KAA9B,EAAqC,IAArC;AACAF,QAAAA,eAAe,CAACO,OAAO,CAACM,QAAR,CAAiBL,UAAlB,CAAf,GAA+CY,SAA/C;AACD,OANM,MAMA,IACL,CAAClB,KAAK,CAACS,KAAN,KAAgBF,aAAMY,MAAtB,IAAgCnB,KAAK,CAACS,KAAN,KAAgBF,aAAMa,SAAvD,KACApB,KAAK,CAACC,QAAN,KAAmBD,KAAK,CAACS,KAFpB,EAGL;AAAA;;AACA,YAAIT,KAAK,CAACC,QAAN,KAAmBM,aAAMM,MAA7B,EAAqC;AAAA;;AACnC,0DAAAR,OAAO,CAACM,QAAR,EAAiBK,KAAjB,2GAAyBhB,KAAzB,EAAgC,KAAhC;AACD;;AACD,wDAAAK,OAAO,CAACM,QAAR,EAAiBM,UAAjB,2GAA8BjB,KAA9B,EAAqC,KAArC;AACAJ,QAAAA,oBAAoB,CAACyB,MAArB,CAA4BrB,KAAK,CAACM,UAAlC;AACAR,QAAAA,eAAe,CAACO,OAAO,CAACM,QAAR,CAAiBL,UAAlB,CAAf,GAA+CY,SAA/C;AACD;AACF,KA9BD,MA8BO,IAAIhB,YAAY,CAACF,KAAD,CAAhB,EAAyB;AAC9B,UAAI,CAACJ,oBAAoB,CAAC0B,GAArB,CAAyBtB,KAAK,CAACM,UAA/B,CAAL,EAAiD;AAC/CV,QAAAA,oBAAoB,CAAC2B,GAArB,CACEvB,KAAK,CAACM,UADR,EAEEkB,yCAAoBC,MAApB,CAA2BzB,KAAK,CAACM,UAAjC,CAFF;AAID,OAN6B,CAQ9B;;;AACA,YAAMoB,OAAO,GAAG9B,oBAAoB,CAAC+B,GAArB,CAAyB3B,KAAK,CAACM,UAA/B,CAAhB;;AAEA,cAAQN,KAAK,CAACG,SAAd;AACE,aAAKyB,+BAAeC,YAApB;AACE,gCAAAxB,OAAO,CAACM,QAAR,mGAAkBmB,aAAlB,yGAAkC9B,KAAlC,EAAyC0B,OAAzC;AACA;;AACF,aAAKE,+BAAeG,YAApB;AACE,gCAAA1B,OAAO,CAACM,QAAR,mGAAkBqB,aAAlB,yGAAkChC,KAAlC,EAAyC0B,OAAzC;AACA;;AACF,aAAKE,+BAAeK,UAApB;AACE,gCAAA5B,OAAO,CAACM,QAAR,mGAAkBuB,WAAlB,yGAAgClC,KAAhC,EAAuC0B,OAAvC;AACA;;AACF,aAAKE,+BAAeO,iBAApB;AACE,iCAAA9B,OAAO,CAACM,QAAR,qGAAkByB,kBAAlB,0GAAuCpC,KAAvC,EAA8C0B,OAA9C;AACA;AAZJ;AAcD,KAzBM,MAyBA;AAAA;;AACL,sDAAArB,OAAO,CAACM,QAAR,EAAiB0B,QAAjB,0GAA4BrC,KAA5B;;AAEA,UAAIK,OAAO,CAACM,QAAR,CAAiB2B,QAAjB,IAA6BjC,OAAO,CAACM,QAAR,CAAiB4B,qBAAlD,EAAyE;AAAA;;AACvE,wDAAAlC,OAAO,CAACM,QAAR,EAAiB2B,QAAjB,mIACE,uBAAAjC,OAAO,CAACM,QAAR,EAAiB4B,qBADnB,0DACE,gDACEvC,KADF,EAEEF,eAAe,CAACO,OAAO,CAACM,QAAR,CAAiBL,UAAlB,CAFjB,CADF;AAOAR,QAAAA,eAAe,CAACO,OAAO,CAACM,QAAR,CAAiBL,UAAlB,CAAf,GAA+CN,KAA/C;AACD;AACF;AACF,GAtED,MAsEO;AACL,UAAMwC,UAAU,GAAG,6CAAsBxC,KAAK,CAACM,UAA5B,CAAnB;;AACA,QAAIkC,UAAJ,EAAgB;AACd,YAAMC,WAAW,GAAG;AAAEA,QAAAA,WAAW,EAAEzC;AAAf,OAApB;;AACA,UAAID,kBAAkB,CAACC,KAAD,CAAtB,EAA+B;AAC7BwC,QAAAA,UAAU,CAACE,oBAAX,CAAgCD,WAAhC;AACD,OAFD,MAEO;AACLD,QAAAA,UAAU,CAACG,cAAX,CAA0BF,WAA1B;AACD;;AACD;AACD;AACF;AACF;;AAEM,SAASG,cAAT,GAA0B;AAC/BC,EAAAA,aAAa;AAEbnD,EAAAA,+BAA+B,GAAGoD,gCAAmBC,WAAnB,CAChC,uBADgC,EAEhC3C,qBAFgC,CAAlC;AAKAT,EAAAA,0CAA0C,GAAGmD,gCAAmBC,WAAnB,CAC3C,6BAD2C,EAE3C3C,qBAF2C,CAA7C;AAID;;AAEM,SAASyC,aAAT,GAAyB;AAC9B,MAAInD,+BAAJ,EAAqC;AACnCA,IAAAA,+BAA+B,CAACsD,MAAhC;AACAtD,IAAAA,+BAA+B,GAAG,IAAlC;AACD;;AAED,MAAIC,0CAAJ,EAAgD;AAC9CA,IAAAA,0CAA0C,CAACqD,MAA3C;AACArD,IAAAA,0CAA0C,GAAG,IAA7C;AACD;AACF", "sourcesContent": ["import { DeviceEventEmitter, EmitterSubscription } from 'react-native';\nimport { State } from '../../State';\nimport { TouchEventType } from '../../TouchEventType';\nimport {\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from '../gestureHandlerCommon';\nimport { findHandler, findOldGestureHandler } from '../handlersRegistry';\nimport { BaseGesture } from './gesture';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from './gestureStateManager';\n\nlet gestureHandlerEventSubscription: EmitterSubscription | null = null;\nlet gestureHandlerStateChangeEventSubscription: EmitterSubscription | null =\n  null;\n\nconst gestureStateManagers: Map<number, GestureStateManagerType> = new Map<\n  number,\n  GestureStateManagerType\n>();\n\nconst lastUpdateEvent: (GestureUpdateEvent | undefined)[] = [];\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  // @ts-ignore oldState doesn't exist on GestureTouchEvent and that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  return event.eventType != null;\n}\n\nexport function onGestureHandlerEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n) {\n  const handler = findHandler(event.handlerTag) as BaseGesture<\n    Record<string, unknown>\n  >;\n\n  if (handler) {\n    if (isStateChangeEvent(event)) {\n      if (\n        event.oldState === State.UNDETERMINED &&\n        event.state === State.BEGAN\n      ) {\n        handler.handlers.onBegin?.(event);\n      } else if (\n        (event.oldState === State.BEGAN ||\n          event.oldState === State.UNDETERMINED) &&\n        event.state === State.ACTIVE\n      ) {\n        handler.handlers.onStart?.(event);\n        lastUpdateEvent[handler.handlers.handlerTag] = event;\n      } else if (event.oldState !== event.state && event.state === State.END) {\n        if (event.oldState === State.ACTIVE) {\n          handler.handlers.onEnd?.(event, true);\n        }\n        handler.handlers.onFinalize?.(event, true);\n        lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n      } else if (\n        (event.state === State.FAILED || event.state === State.CANCELLED) &&\n        event.oldState !== event.state\n      ) {\n        if (event.oldState === State.ACTIVE) {\n          handler.handlers.onEnd?.(event, false);\n        }\n        handler.handlers.onFinalize?.(event, false);\n        gestureStateManagers.delete(event.handlerTag);\n        lastUpdateEvent[handler.handlers.handlerTag] = undefined;\n      }\n    } else if (isTouchEvent(event)) {\n      if (!gestureStateManagers.has(event.handlerTag)) {\n        gestureStateManagers.set(\n          event.handlerTag,\n          GestureStateManager.create(event.handlerTag)\n        );\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const manager = gestureStateManagers.get(event.handlerTag)!;\n\n      switch (event.eventType) {\n        case TouchEventType.TOUCHES_DOWN:\n          handler.handlers?.onTouchesDown?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_MOVE:\n          handler.handlers?.onTouchesMove?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_UP:\n          handler.handlers?.onTouchesUp?.(event, manager);\n          break;\n        case TouchEventType.TOUCHES_CANCELLED:\n          handler.handlers?.onTouchesCancelled?.(event, manager);\n          break;\n      }\n    } else {\n      handler.handlers.onUpdate?.(event);\n\n      if (handler.handlers.onChange && handler.handlers.changeEventCalculator) {\n        handler.handlers.onChange?.(\n          handler.handlers.changeEventCalculator?.(\n            event,\n            lastUpdateEvent[handler.handlers.handlerTag]\n          )\n        );\n\n        lastUpdateEvent[handler.handlers.handlerTag] = event;\n      }\n    }\n  } else {\n    const oldHandler = findOldGestureHandler(event.handlerTag);\n    if (oldHandler) {\n      const nativeEvent = { nativeEvent: event };\n      if (isStateChangeEvent(event)) {\n        oldHandler.onGestureStateChange(nativeEvent);\n      } else {\n        oldHandler.onGestureEvent(nativeEvent);\n      }\n      return;\n    }\n  }\n}\n\nexport function startListening() {\n  stopListening();\n\n  gestureHandlerEventSubscription = DeviceEventEmitter.addListener(\n    'onGestureHandlerEvent',\n    onGestureHandlerEvent\n  );\n\n  gestureHandlerStateChangeEventSubscription = DeviceEventEmitter.addListener(\n    'onGestureHandlerStateChange',\n    onGestureHandlerEvent\n  );\n}\n\nexport function stopListening() {\n  if (gestureHandlerEventSubscription) {\n    gestureHandlerEventSubscription.remove();\n    gestureHandlerEventSubscription = null;\n  }\n\n  if (gestureHandlerStateChangeEventSubscription) {\n    gestureHandlerStateChangeEventSubscription.remove();\n    gestureHandlerStateChangeEventSubscription = null;\n  }\n}\n"]}