{"version": 3, "sources": ["GestureComponents.tsx"], "names": ["React", "ScrollView", "RNScrollView", "Switch", "RNSwitch", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "RNDrawerLayoutAndroid", "FlatList", "RNFlatList", "RefreshControl", "RNRefreshControl", "createNativeWrapper", "nativeViewProps", "toArray", "disallowInterruption", "shouldCancelWhenOutside", "GHScrollView", "forwardRef", "props", "ref", "refreshControlGestureRef", "useRef", "refreshControl", "waitFor", "rest", "cloneElement", "undefined", "shouldActivateOnStart", "flatListProps", "scrollViewProps", "propName", "value", "Object", "entries", "includes", "scrollProps"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AAOA,SACEC,UAAU,IAAIC,YADhB,EAGEC,MAAM,IAAIC,QAHZ,EAKEC,SAAS,IAAIC,WALf,EAOEC,mBAAmB,IAAIC,qBAPzB,EASEC,QAAQ,IAAIC,UATd,EAWEC,cAAc,IAAIC,gBAXpB,QAYO,cAZP;AAcA,OAAOC,mBAAP,MAAgC,iCAAhC;AAEA,SAEEC,eAFF,QAGO,sCAHP;AAKA,SAASC,OAAT,QAAwB,UAAxB;AAEA,OAAO,MAAMJ,cAAc,GAAGE,mBAAmB,CAACD,gBAAD,EAAmB;AAClEI,EAAAA,oBAAoB,EAAE,IAD4C;AAElEC,EAAAA,uBAAuB,EAAE;AAFyC,CAAnB,CAA1C,C,CAIP;;AAGA,MAAMC,YAAY,GAAGL,mBAAmB,CACtCX,YADsC,EAEtC;AACEc,EAAAA,oBAAoB,EAAE,IADxB;AAEEC,EAAAA,uBAAuB,EAAE;AAF3B,CAFsC,CAAxC;AAOA,OAAO,MAAMhB,UAAU,gBAAGD,KAAK,CAACmB,UAAN,CAGxB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB,QAAMC,wBAAwB,GAAGtB,KAAK,CAACuB,MAAN,CAA6B,IAA7B,CAAjC;AACA,QAAM;AAAEC,IAAAA,cAAF;AAAkBC,IAAAA,OAAlB;AAA2B,OAAGC;AAA9B,MAAuCN,KAA7C;AAEA,sBACE,oBAAC,YAAD,eACMM,IADN;AAEE;AACA,IAAA,GAAG,EAAEL,GAHP;AAIE,IAAA,OAAO,EAAE,CAAC,GAAGN,OAAO,CAACU,OAAD,aAACA,OAAD,cAACA,OAAD,GAAY,EAAZ,CAAX,EAA4BH,wBAA5B,CAJX,CAKE;AALF;AAME,IAAA,cAAc,EACZE,cAAc,gBACVxB,KAAK,CAAC2B,YAAN,CAAmBH,cAAnB,EAAmC;AACjC;AACAH,MAAAA,GAAG,EAAEC;AAF4B,KAAnC,CADU,GAKVM;AAZR,KADF;AAiBD,CAxByB,CAAnB,C,CAyBP;AACA;AACA;;AAGA,OAAO,MAAMzB,MAAM,GAAGU,mBAAmB,CAAgBT,QAAhB,EAA0B;AACjEa,EAAAA,uBAAuB,EAAE,KADwC;AAEjEY,EAAAA,qBAAqB,EAAE,IAF0C;AAGjEb,EAAAA,oBAAoB,EAAE;AAH2C,CAA1B,CAAlC,C,CAKP;;AAGA,OAAO,MAAMX,SAAS,GAAGQ,mBAAmB,CAAmBP,WAAnB,CAArC,C,CACP;;AAGA,OAAO,MAAMC,mBAAmB,GAAGM,mBAAmB,CAEpDL,qBAFoD,EAE7B;AAAEQ,EAAAA,oBAAoB,EAAE;AAAxB,CAF6B,CAA/C,C,CAGP;;AAIA,OAAO,MAAMP,QAAQ,gBAAGT,KAAK,CAACmB,UAAN,CAAiB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACvD,QAAMC,wBAAwB,GAAGtB,KAAK,CAACuB,MAAN,CAA6B,IAA7B,CAAjC;AAEA,QAAM;AAAEE,IAAAA,OAAF;AAAWD,IAAAA,cAAX;AAA2B,OAAGE;AAA9B,MAAuCN,KAA7C;AAEA,QAAMU,aAAa,GAAG,EAAtB;AACA,QAAMC,eAAe,GAAG,EAAxB;;AACA,OAAK,MAAM,CAACC,QAAD,EAAWC,KAAX,CAAX,IAAgCC,MAAM,CAACC,OAAP,CAAeT,IAAf,CAAhC,EAAsD;AACpD;AACA,QAAKZ,eAAD,CAAuCsB,QAAvC,CAAgDJ,QAAhD,CAAJ,EAA+D;AAC7D;AACA;AACAD,MAAAA,eAAe,CAACC,QAAD,CAAf,GAA4BC,KAA5B;AACD,KAJD,MAIO;AACL;AACA;AACAH,MAAAA,aAAa,CAACE,QAAD,CAAb,GAA0BC,KAA1B;AACD;AACF;;AAED;AAAA;AACE;AACA,wBAAC,UAAD;AACE,MAAA,GAAG,EAAEZ;AADP,OAEMS,aAFN;AAGE,MAAA,qBAAqB,EAAGO,WAAD,iBACrB,oBAAC,UAAD,eAEOA,WAFP,EAGON,eAHP;AAIIN,QAAAA,OAAO,EAAE,CAAC,GAAGV,OAAO,CAACU,OAAD,aAACA,OAAD,cAACA,OAAD,GAAY,EAAZ,CAAX,EAA4BH,wBAA5B;AAJb,SAJJ,CAYE;AAZF;AAaE,MAAA,cAAc,EACZE,cAAc,gBACVxB,KAAK,CAAC2B,YAAN,CAAmBH,cAAnB,EAAmC;AACjC;AACAH,QAAAA,GAAG,EAAEC;AAF4B,OAAnC,CADU,GAKVM;AAnBR;AAFF;AAyBD,CA7CuB,CAAjB,C,CAqDP", "sourcesContent": ["import * as React from 'react';\nimport {\n  PropsWithChildren,\n  ForwardedRef,\n  RefAttributes,\n  ReactElement,\n} from 'react';\nimport {\n  ScrollView as RNScrollView,\n  ScrollViewProps as RNScrollViewProps,\n  Switch as RNSwitch,\n  SwitchProps as RNSwitchProps,\n  TextInput as RNTextInput,\n  TextInputProps as RNTextInputProps,\n  DrawerLayoutAndroid as RNDrawerLayoutAndroid,\n  DrawerLayoutAndroidProps as RNDrawerLayoutAndroidProps,\n  FlatList as RNFlatList,\n  FlatListProps as RNFlatListProps,\n  RefreshControl as RNRefreshControl,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\n\nimport {\n  NativeViewGestureHandlerProps,\n  nativeViewProps,\n} from '../handlers/NativeViewGestureHandler';\n\nimport { toArray } from '../utils';\n\nexport const RefreshControl = createNativeWrapper(RNRefreshControl, {\n  disallowInterruption: true,\n  shouldCancelWhenOutside: false,\n});\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type RefreshControl = typeof RefreshControl & RNRefreshControl;\n\nconst GHScrollView = createNativeWrapper<PropsWithChildren<RNScrollViewProps>>(\n  RNScrollView,\n  {\n    disallowInterruption: true,\n    shouldCancelWhenOutside: false,\n  }\n);\nexport const ScrollView = React.forwardRef<\n  RNScrollView,\n  RNScrollViewProps & NativeViewGestureHandlerProps\n>((props, ref) => {\n  const refreshControlGestureRef = React.useRef<RefreshControl>(null);\n  const { refreshControl, waitFor, ...rest } = props;\n\n  return (\n    <GHScrollView\n      {...rest}\n      // @ts-ignore `ref` exists on `GHScrollView`\n      ref={ref}\n      waitFor={[...toArray(waitFor ?? []), refreshControlGestureRef]}\n      // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n      refreshControl={\n        refreshControl\n          ? React.cloneElement(refreshControl, {\n              // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n              ref: refreshControlGestureRef,\n            })\n          : undefined\n      }\n    />\n  );\n});\n// Backward type compatibility with https://github.com/software-mansion/react-native-gesture-handler/blob/db78d3ca7d48e8ba57482d3fe9b0a15aa79d9932/react-native-gesture-handler.d.ts#L440-L457\n// include methods of wrapped components by creating an intersection type with the RN component instead of duplicating them.\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type ScrollView = typeof GHScrollView & RNScrollView;\n\nexport const Switch = createNativeWrapper<RNSwitchProps>(RNSwitch, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: true,\n  disallowInterruption: true,\n});\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type Switch = typeof Switch & RNSwitch;\n\nexport const TextInput = createNativeWrapper<RNTextInputProps>(RNTextInput);\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type TextInput = typeof TextInput & RNTextInput;\n\nexport const DrawerLayoutAndroid = createNativeWrapper<\n  PropsWithChildren<RNDrawerLayoutAndroidProps>\n>(RNDrawerLayoutAndroid, { disallowInterruption: true });\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type DrawerLayoutAndroid = typeof DrawerLayoutAndroid &\n  RNDrawerLayoutAndroid;\n\nexport const FlatList = React.forwardRef((props, ref) => {\n  const refreshControlGestureRef = React.useRef<RefreshControl>(null);\n\n  const { waitFor, refreshControl, ...rest } = props;\n\n  const flatListProps = {};\n  const scrollViewProps = {};\n  for (const [propName, value] of Object.entries(rest)) {\n    // https://github.com/microsoft/TypeScript/issues/26255\n    if ((nativeViewProps as readonly string[]).includes(propName)) {\n      // @ts-ignore - this function cannot have generic type so we have to ignore this error\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      scrollViewProps[propName] = value;\n    } else {\n      // @ts-ignore - this function cannot have generic type so we have to ignore this error\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      flatListProps[propName] = value;\n    }\n  }\n\n  return (\n    // @ts-ignore - this function cannot have generic type so we have to ignore this error\n    <RNFlatList\n      ref={ref}\n      {...flatListProps}\n      renderScrollComponent={(scrollProps) => (\n        <ScrollView\n          {...{\n            ...scrollProps,\n            ...scrollViewProps,\n            waitFor: [...toArray(waitFor ?? []), refreshControlGestureRef],\n          }}\n        />\n      )}\n      // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n      refreshControl={\n        refreshControl\n          ? React.cloneElement(refreshControl, {\n              // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n              ref: refreshControlGestureRef,\n            })\n          : undefined\n      }\n    />\n  );\n}) as <ItemT = any>(\n  props: PropsWithChildren<\n    Omit<RNFlatListProps<ItemT>, 'renderScrollComponent'> &\n      RefAttributes<FlatList<ItemT>> &\n      NativeViewGestureHandlerProps\n  >,\n  ref: ForwardedRef<FlatList<ItemT>>\n) => ReactElement | null;\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type FlatList<ItemT = any> = typeof FlatList & RNFlatList<ItemT>;\n"]}