import"../shell/shell.js";import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as o from"../../core/common/common.js";import*as i from"../../core/root/root.js";import*as n from"../../core/sdk/sdk.js";import*as a from"../../models/extensions/extensions.js";import*as r from"../../models/workspace/workspace.js";import*as s from"../../panels/timeline/utils/utils.js";import*as l from"../../panels/network/forward/forward.js";import*as c from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as d from"../../panels/application/preloading/helper/helper.js";import*as g from"../../models/issues_manager/issues_manager.js";import*as w from"../main/main.js";const m={cssOverview:"CSS overview",showCssOverview:"Show CSS overview"},p=e.i18n.registerUIStrings("panels/css_overview/css_overview-meta.ts",m),u=e.i18n.getLazilyComputedLocalizedString.bind(void 0,p);let y;t.ViewManager.registerViewExtension({location:"panel",id:"cssoverview",commandPrompt:u(m.showCssOverview),title:u(m.cssOverview),order:95,persistence:"closeable",async loadView(){const e=await async function(){return y||(y=await import("../../panels/css_overview/css_overview.js")),y}();return new e.CSSOverviewPanel.CSSOverviewPanel(new e.CSSOverviewController.OverviewController)},isPreviewFeature:!0});const h={showElements:"Show Elements",elements:"Elements",showEventListeners:"Show Event Listeners",eventListeners:"Event Listeners",showProperties:"Show Properties",properties:"Properties",showStackTrace:"Show Stack Trace",stackTrace:"Stack Trace",showLayout:"Show Layout",layout:"Layout",hideElement:"Hide element",editAsHtml:"Edit as HTML",duplicateElement:"Duplicate element",undo:"Undo",redo:"Redo",captureAreaScreenshot:"Capture area screenshot",selectAnElementInThePageTo:"Select an element in the page to inspect it",newStyleRule:"New Style Rule",refreshEventListeners:"Refresh event listeners",wordWrap:"Word wrap",enableDomWordWrap:"Enable `DOM` word wrap",disableDomWordWrap:"Disable `DOM` word wrap",showHtmlComments:"Show `HTML` comments",hideHtmlComments:"Hide `HTML` comments",revealDomNodeOnHover:"Reveal `DOM` node on hover",showDetailedInspectTooltip:"Show detailed inspect tooltip",showCSSDocumentationTooltip:"Show CSS documentation tooltip",copyStyles:"Copy styles",showUserAgentShadowDOM:"Show user agent shadow `DOM`",showComputedStyles:"Show Computed Styles",showStyles:"Show Styles",toggleEyeDropper:"Toggle eye dropper"},S=e.i18n.registerUIStrings("panels/elements/elements-meta.ts",h),v=e.i18n.getLazilyComputedLocalizedString.bind(void 0,S);let R,E;async function A(){return R||(R=await import("../../panels/elements/elements.js")),R}function b(e){return void 0===R?[]:e(R)}t.ViewManager.registerViewExtension({location:"panel",id:"elements",commandPrompt:v(h.showElements),title:v(h.elements),order:10,persistence:"permanent",hasToolbar:!1,loadView:async()=>(await A()).ElementsPanel.ElementsPanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"elements.show-styles",category:"ELEMENTS",title:v(h.showStyles),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"elements.show-computed",category:"ELEMENTS",title:v(h.showComputedStyles),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate)}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.event-listeners",commandPrompt:v(h.showEventListeners),title:v(h.eventListeners),order:5,hasToolbar:!0,persistence:"permanent",loadView:async()=>(await A()).EventListenersWidget.EventListenersWidget.instance()}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.dom-properties",commandPrompt:v(h.showProperties),title:v(h.properties),order:7,persistence:"permanent",loadView:async()=>new((await A()).PropertiesWidget.PropertiesWidget)}),t.ViewManager.registerViewExtension({experiment:"capture-node-creation-stacks",location:"elements-sidebar",id:"elements.dom-creation",commandPrompt:v(h.showStackTrace),title:v(h.stackTrace),order:10,persistence:"permanent",loadView:async()=>new((await A()).NodeStackTraceWidget.NodeStackTraceWidget)}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.layout",commandPrompt:v(h.showLayout),title:v(h.layout),order:4,persistence:"permanent",loadView:async()=>(await async function(){return E||(E=await import("../../panels/elements/components/components.js")),E}()).LayoutPane.LayoutPane.instance().wrapper}),t.ActionRegistration.registerActionExtension({actionId:"elements.hide-element",category:"ELEMENTS",title:v(h.hideElement),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"H"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.toggle-eye-dropper",category:"ELEMENTS",title:v(h.toggleEyeDropper),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ColorSwatchPopoverIcon.ColorSwatchPopoverIcon])),bindings:[{shortcut:"c"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.edit-as-html",category:"ELEMENTS",title:v(h.editAsHtml),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"F2"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.duplicate-element",category:"ELEMENTS",title:v(h.duplicateElement),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Shift+Alt+Down"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.copy-styles",category:"ELEMENTS",title:v(h.copyStyles),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Alt+C",platform:"windows,linux"},{shortcut:"Meta+Alt+C",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.undo",category:"ELEMENTS",title:v(h.undo),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Z",platform:"windows,linux"},{shortcut:"Meta+Z",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.redo",category:"ELEMENTS",title:v(h.redo),loadActionDelegate:async()=>new((await A()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>b((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Y",platform:"windows,linux"},{shortcut:"Meta+Shift+Z",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.capture-area-screenshot",loadActionDelegate:async()=>new((await A()).InspectElementModeController.ToggleSearchActionDelegate),condition:i.Runtime.conditions.canDock,title:v(h.captureAreaScreenshot),category:"SCREENSHOT"}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.toggle-element-search",toggleable:!0,loadActionDelegate:async()=>new((await A()).InspectElementModeController.ToggleSearchActionDelegate),title:v(h.selectAnElementInThePageTo),iconClass:"select-element",bindings:[{shortcut:"Ctrl+Shift+C",platform:"windows,linux"},{shortcut:"Meta+Shift+C",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.new-style-rule",title:v(h.newStyleRule),iconClass:"plus",loadActionDelegate:async()=>new((await A()).StylesSidebarPane.ActionDelegate),contextTypes:()=>b((e=>[e.StylesSidebarPane.StylesSidebarPane]))}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.refresh-event-listeners",title:v(h.refreshEventListeners),iconClass:"refresh",loadActionDelegate:async()=>new((await A()).EventListenersWidget.ActionDelegate),contextTypes:()=>b((e=>[e.EventListenersWidget.EventListenersWidget]))}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:1,title:v(h.showUserAgentShadowDOM),settingName:"show-ua-shadow-dom",settingType:"boolean",defaultValue:!1}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:2,title:v(h.wordWrap),settingName:"dom-word-wrap",settingType:"boolean",options:[{value:!0,title:v(h.enableDomWordWrap)},{value:!1,title:v(h.disableDomWordWrap)}],defaultValue:!0}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:3,title:v(h.showHtmlComments),settingName:"show-html-comments",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:v(h.showHtmlComments)},{value:!1,title:v(h.hideHtmlComments)}]}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:4,title:v(h.revealDomNodeOnHover),settingName:"highlight-node-on-hover-in-overlay",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:5,title:v(h.showDetailedInspectTooltip),settingName:"show-detailed-inspect-tooltip",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({settingName:"show-event-listeners-for-ancestors",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({category:"ADORNER",storageType:"Synced",settingName:"adorner-settings",settingType:"array",defaultValue:[]}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",title:v(h.showCSSDocumentationTooltip),settingName:"show-css-property-documentation-on-hover",settingType:"boolean",defaultValue:!0}),t.ContextMenu.registerProvider({contextTypes:()=>[n.RemoteObject.RemoteObject,n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadProvider:async()=>new((await A()).ElementsPanel.ContextMenuProvider),experiment:void 0}),t.ViewManager.registerLocationResolver({name:"elements-sidebar",category:"ELEMENTS",loadResolver:async()=>(await A()).ElementsPanel.ElementsPanel.instance()}),o.Revealer.registerRevealer({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode,n.RemoteObject.RemoteObject],destination:o.Revealer.RevealerDestination.ELEMENTS_PANEL,loadRevealer:async()=>new((await A()).ElementsPanel.DOMNodeRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[n.CSSProperty.CSSProperty],destination:o.Revealer.RevealerDestination.STYLES_SIDEBAR,loadRevealer:async()=>new((await A()).ElementsPanel.CSSPropertyRevealer)}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await A()).LayersWidget.ButtonProvider.instance(),order:1,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await A()).ElementStatePaneWidget.ButtonProvider.instance(),order:2,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await A()).ClassesPaneWidget.ButtonProvider.instance(),order:3,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await A()).StylesSidebarPane.ButtonProvider.instance(),order:100,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({actionId:"elements.toggle-element-search",location:"main-toolbar-left",order:0}),t.UIUtils.registerRenderer({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadRenderer:async()=>(await A()).ElementsTreeOutline.Renderer.instance()}),o.Linkifier.registerLinkifier({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadLinkifier:async()=>(await A()).DOMLinkifier.Linkifier.instance()});const P={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},f=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",P),T=e.i18n.getLazilyComputedLocalizedString.bind(void 0,f);let k,D;async function x(){return k||(k=await import("../../panels/browser_debugger/browser_debugger.js")),k}async function L(){return D||(D=await import("../../panels/sources/sources.js")),D}t.ViewManager.registerViewExtension({loadView:async()=>(await x()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:T(P.showEventListenerBreakpoints),title:T(P.eventListenerBreakpoints),order:9,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>new((await x()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:T(P.showCspViolationBreakpoints),title:T(P.cspViolationBreakpoints),order:10,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>(await x()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:T(P.showXhrfetchBreakpoints),title:T(P.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await x()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:T(P.showDomBreakpoints),title:T(P.domBreakpoints),order:7,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>new((await x()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:T(P.showGlobalListeners),title:T(P.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await x()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:T(P.showDomBreakpoints),title:T(P.domBreakpoints),order:6,persistence:"permanent"}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:T(P.page),commandPrompt:T(P.showPage),order:2,persistence:"permanent",loadView:async()=>(await L()).SourcesNavigator.NetworkNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:T(P.overrides),commandPrompt:T(P.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await L()).SourcesNavigator.OverridesNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:T(P.contentScripts),commandPrompt:T(P.showContentScripts),order:5,persistence:"permanent",condition:()=>"/bundled/worker_app.html"!==i.Runtime.getPathName(),loadView:async()=>new((await L()).SourcesNavigator.ContentScriptsNavigatorView)}),t.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await x()).ObjectEventListenersSidebarPane.ActionDelegate),title:T(P.refreshGlobalListeners),iconClass:"refresh",contextTypes:()=>void 0===k?[]:(e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane])(k)}),t.ContextMenu.registerProvider({contextTypes:()=>[n.DOMModel.DOMNode],loadProvider:async()=>new((await x()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),t.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await x()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),t.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await x()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});const M={showNetwork:"Show Network",network:"Network",networkExpoUnstable:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},N=e.i18n.registerUIStrings("panels/network/network-meta.ts",M),I=e.i18n.getLazilyComputedLocalizedString.bind(void 0,N),C=e.i18n.getLocalizedString.bind(void 0,N);let V;async function O(){return V||(V=await import("../../panels/network/network.js")),V}function B(e){return void 0===V?[]:e(V)}t.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:I(M.showNetwork),title:()=>i.Runtime.experiments.isEnabled(i.Runtime.RNExperimentName.ENABLE_NETWORK_PANEL)?C(M.network):C(M.networkExpoUnstable),order:40,isPreviewFeature:!0,condition:i.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await O()).NetworkPanel.NetworkPanel.instance()}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:I(M.showNetworkRequestBlocking),title:I(M.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await O()).BlockedURLsPane.BlockedURLsPane)}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:I(M.showNetworkConditions),title:I(M.networkConditions),persistence:"closeable",order:40,tags:[I(M.diskCache),I(M.networkThrottling),e.i18n.lockedLazyString("useragent"),e.i18n.lockedLazyString("user agent"),e.i18n.lockedLazyString("user-agent")],loadView:async()=>(await O()).NetworkConfigView.NetworkConfigView.instance()}),t.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:I(M.showSearch),title:I(M.search),persistence:"permanent",loadView:async()=>(await O()).NetworkPanel.SearchNetworkView.instance()}),t.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>B((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await O()).NetworkPanel.ActionDelegate),options:[{value:!0,title:I(M.recordNetworkLog)},{value:!1,title:I(M.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:I(M.clear),iconClass:"clear",loadActionDelegate:async()=>new((await O()).NetworkPanel.ActionDelegate),contextTypes:()=>B((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:I(M.hideRequestDetails),contextTypes:()=>B((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await O()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:I(M.search),contextTypes:()=>B((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await O()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),t.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:I(M.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>B((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await O()).BlockedURLsPane.ActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:I(M.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>B((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await O()).BlockedURLsPane.ActionDelegate)}),o.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:I(M.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[I(M.colorCode),I(M.resourceType)],options:[{value:!0,title:I(M.colorCodeByResourceType)},{value:!1,title:I(M.useDefaultColors)}]}),o.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:I(M.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[I(M.netWork),I(M.frame),I(M.group)],options:[{value:!0,title:I(M.groupNetworkLogItemsByFrame)},{value:!1,title:I(M.dontGroupNetworkLogItemsByFrame)}]}),t.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await O()).NetworkPanel.NetworkPanel.instance()}),t.ContextMenu.registerProvider({contextTypes:()=>[n.NetworkRequest.NetworkRequest,n.Resource.Resource,r.UISourceCode.UISourceCode,s.NetworkRequest.TimelineNetworkRequest],loadProvider:async()=>(await O()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),o.Revealer.registerRevealer({contextTypes:()=>[n.NetworkRequest.NetworkRequest],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await O()).NetworkPanel.RequestRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[l.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await O()).NetworkPanel.RequestLocationRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[l.NetworkRequestId.NetworkRequestId],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await O()).NetworkPanel.RequestIdRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[l.UIFilter.UIRequestFilter,a.ExtensionServer.RevealableNetworkRequestFilter],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await O()).NetworkPanel.NetworkLogWithFilterRevealer)});const z={security:"Security",showSecurity:"Show Security"},U=e.i18n.registerUIStrings("panels/security/security-meta.ts",z),W=e.i18n.getLazilyComputedLocalizedString.bind(void 0,U);let F;t.ViewManager.registerViewExtension({location:"panel",id:"security",title:W(z.security),commandPrompt:W(z.showSecurity),order:80,persistence:"closeable",loadView:async()=>(await async function(){return F||(F=await import("../../panels/security/security.js")),F}()).SecurityPanel.SecurityPanel.instance()});const _={toggleDeviceToolbar:"Toggle device toolbar",captureScreenshot:"Capture screenshot",captureFullSizeScreenshot:"Capture full size screenshot",captureNodeScreenshot:"Capture node screenshot",showMediaQueries:"Show media queries",device:"device",hideMediaQueries:"Hide media queries",showRulers:"Show rulers in the Device Mode toolbar",hideRulers:"Hide rulers in the Device Mode toolbar",showDeviceFrame:"Show device frame",hideDeviceFrame:"Hide device frame"},j=e.i18n.registerUIStrings("panels/emulation/emulation-meta.ts",_),q=e.i18n.getLazilyComputedLocalizedString.bind(void 0,j);let H;async function G(){return H||(H=await import("../../panels/emulation/emulation.js")),H}t.ActionRegistration.registerActionExtension({category:"MOBILE",actionId:"emulation.toggle-device-mode",toggleable:!0,loadActionDelegate:async()=>new((await G()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:q(_.toggleDeviceToolbar),iconClass:"devices",bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+M"},{platform:"mac",shortcut:"Shift+Meta+M"}]}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await G()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:q(_.captureScreenshot)}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-full-height-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await G()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:q(_.captureFullSizeScreenshot)}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-node-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await G()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:q(_.captureNodeScreenshot)}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"show-media-query-inspector",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:q(_.showMediaQueries)},{value:!1,title:q(_.hideMediaQueries)}],tags:[q(_.device)]}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-rulers",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:q(_.showRulers)},{value:!1,title:q(_.hideRulers)}],tags:[q(_.device)]}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-device-outline",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:q(_.showDeviceFrame)},{value:!1,title:q(_.hideDeviceFrame)}],tags:[q(_.device)]}),t.Toolbar.registerToolbarItem({actionId:"emulation.toggle-device-mode",condition:i.Runtime.conditions.canDock,location:"main-toolbar-left",order:1,showLabel:void 0,loadItem:void 0,separator:void 0}),o.AppProvider.registerAppProvider({loadAppProvider:async()=>(await G()).AdvancedApp.AdvancedAppProvider.instance(),condition:i.Runtime.conditions.canDock,order:0}),t.ContextMenu.registerItem({location:"deviceModeMenu/save",order:12,actionId:"emulation.capture-screenshot"}),t.ContextMenu.registerItem({location:"deviceModeMenu/save",order:13,actionId:"emulation.capture-full-height-screenshot"});const K={sensors:"Sensors",geolocation:"geolocation",timezones:"timezones",locale:"locale",locales:"locales",accelerometer:"accelerometer",deviceOrientation:"device orientation",locations:"Locations",touch:"Touch",devicebased:"Device-based",forceEnabled:"Force enabled",emulateIdleDetectorState:"Emulate Idle Detector state",noIdleEmulation:"No idle emulation",userActiveScreenUnlocked:"User active, screen unlocked",userActiveScreenLocked:"User active, screen locked",userIdleScreenUnlocked:"User idle, screen unlocked",userIdleScreenLocked:"User idle, screen locked",showSensors:"Show Sensors",showLocations:"Show Locations"},Y=e.i18n.registerUIStrings("panels/sensors/sensors-meta.ts",K),X=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Y);let Z,Q;async function J(){return Z||(Z=await import("../../panels/sensors/sensors.js")),Z}t.ViewManager.registerViewExtension({location:"drawer-view",commandPrompt:X(K.showSensors),title:X(K.sensors),id:"sensors",persistence:"closeable",order:100,loadView:async()=>new((await J()).SensorsView.SensorsView),tags:[X(K.geolocation),X(K.timezones),X(K.locale),X(K.locales),X(K.accelerometer),X(K.deviceOrientation)]}),t.ViewManager.registerViewExtension({location:"settings-view",id:"emulation-locations",commandPrompt:X(K.showLocations),title:X(K.locations),order:40,loadView:async()=>new((await J()).LocationsSettingsTab.LocationsSettingsTab),settings:["emulation.locations"],iconName:"location-on"}),o.Settings.registerSettingExtension({storageType:"Synced",settingName:"emulation.locations",settingType:"array",defaultValue:[{title:"Berlin",lat:52.520007,long:13.404954,timezoneId:"Europe/Berlin",locale:"de-DE"},{title:"London",lat:51.507351,long:-.127758,timezoneId:"Europe/London",locale:"en-GB"},{title:"Moscow",lat:55.755826,long:37.6173,timezoneId:"Europe/Moscow",locale:"ru-RU"},{title:"Mountain View",lat:37.386052,long:-122.083851,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Mumbai",lat:19.075984,long:72.877656,timezoneId:"Asia/Kolkata",locale:"mr-IN"},{title:"San Francisco",lat:37.774929,long:-122.419416,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Shanghai",lat:31.230416,long:121.473701,timezoneId:"Asia/Shanghai",locale:"zh-Hans-CN"},{title:"São Paulo",lat:-23.55052,long:-46.633309,timezoneId:"America/Sao_Paulo",locale:"pt-BR"},{title:"Tokyo",lat:35.689487,long:139.691706,timezoneId:"Asia/Tokyo",locale:"ja-JP"}]}),o.Settings.registerSettingExtension({title:X(K.touch),reloadRequired:!0,settingName:"emulation.touch",settingType:"enum",defaultValue:"none",options:[{value:"none",title:X(K.devicebased),text:X(K.devicebased)},{value:"force",title:X(K.forceEnabled),text:X(K.forceEnabled)}]}),o.Settings.registerSettingExtension({title:X(K.emulateIdleDetectorState),settingName:"emulation.idle-detection",settingType:"enum",defaultValue:"none",options:[{value:"none",title:X(K.noIdleEmulation),text:X(K.noIdleEmulation)},{value:'{"isUserActive":true,"isScreenUnlocked":true}',title:X(K.userActiveScreenUnlocked),text:X(K.userActiveScreenUnlocked)},{value:'{"isUserActive":true,"isScreenUnlocked":false}',title:X(K.userActiveScreenLocked),text:X(K.userActiveScreenLocked)},{value:'{"isUserActive":false,"isScreenUnlocked":true}',title:X(K.userIdleScreenUnlocked),text:X(K.userIdleScreenUnlocked)},{value:'{"isUserActive":false,"isScreenUnlocked":false}',title:X(K.userIdleScreenLocked),text:X(K.userIdleScreenLocked)}]});const $={accessibility:"Accessibility",shoAccessibility:"Show Accessibility"},ee=e.i18n.registerUIStrings("panels/accessibility/accessibility-meta.ts",$),te=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ee);let oe;t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"accessibility.view",title:te($.accessibility),commandPrompt:te($.shoAccessibility),order:10,persistence:"permanent",loadView:async()=>(await async function(){return Q||(Q=await import("../../panels/accessibility/accessibility.js")),Q}()).AccessibilitySidebarView.AccessibilitySidebarView.instance()});const ie={animations:"Animations",showAnimations:"Show Animations"},ne=e.i18n.registerUIStrings("panels/animation/animation-meta.ts",ie),ae=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ne);t.ViewManager.registerViewExtension({location:"drawer-view",id:"animations",title:ae(ie.animations),commandPrompt:ae(ie.showAnimations),persistence:"closeable",order:0,loadView:async()=>(await async function(){return oe||(oe=await import("../../panels/animation/animation.js")),oe}()).AnimationTimeline.AnimationTimeline.instance()});const re={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},se=e.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",re),le=e.i18n.getLazilyComputedLocalizedString.bind(void 0,se);let ce;async function de(){return ce||(ce=await import("../../panels/developer_resources/developer_resources.js")),ce}t.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:le(re.developerResources),commandPrompt:le(re.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await de()).DeveloperResourcesView.DeveloperResourcesView)}),o.Revealer.registerRevealer({contextTypes:()=>[n.PageResourceLoader.ResourceKey],destination:o.Revealer.RevealerDestination.DEVELOPER_RESOURCES_PANEL,loadRevealer:async()=>new((await de()).DeveloperResourcesView.DeveloperResourcesRevealer)});const ge={autofill:"Autofill",showAutofill:"Show Autofill"},we=e.i18n.registerUIStrings("panels/autofill/autofill-meta.ts",ge),me=e.i18n.getLazilyComputedLocalizedString.bind(void 0,we);let pe;t.ViewManager.registerViewExtension({experiment:"autofill-view",location:"drawer-view",id:"autofill-view",title:me(ge.autofill),commandPrompt:me(ge.showAutofill),order:100,persistence:"closeable",async loadView(){const e=await async function(){return pe||(pe=await import("../../panels/autofill/autofill.js")),pe}();return c.LegacyWrapper.legacyWrapper(t.Widget.Widget,new e.AutofillView.AutofillView)}});const ue={rendering:"Rendering",showRendering:"Show Rendering",paint:"paint",layout:"layout",fps:"fps",cssMediaType:"CSS media type",cssMediaFeature:"CSS media feature",visionDeficiency:"vision deficiency",colorVisionDeficiency:"color vision deficiency",reloadPage:"Reload page",hardReloadPage:"Hard reload page",forceAdBlocking:"Force ad blocking on this site",blockAds:"Block ads on this site",showAds:"Show ads on this site, if allowed",autoOpenDevTools:"Auto-open DevTools for popups",doNotAutoOpen:"Do not auto-open DevTools for popups",disablePaused:"Disable paused state overlay",toggleCssPrefersColorSchemeMedia:"Toggle CSS media feature prefers-color-scheme"},ye=e.i18n.registerUIStrings("entrypoints/inspector_main/inspector_main-meta.ts",ue),he=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ye);let Se;async function ve(){return Se||(Se=await import("../inspector_main/inspector_main.js")),Se}t.ViewManager.registerViewExtension({location:"drawer-view",id:"rendering",title:he(ue.rendering),commandPrompt:he(ue.showRendering),persistence:"closeable",order:50,loadView:async()=>new((await ve()).RenderingOptions.RenderingOptionsView),tags:[he(ue.paint),he(ue.layout),he(ue.fps),he(ue.cssMediaType),he(ue.cssMediaFeature),he(ue.visionDeficiency),he(ue.colorVisionDeficiency)]}),t.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.reload",loadActionDelegate:async()=>new((await ve()).InspectorMain.ReloadActionDelegate),iconClass:"refresh",title:he(ue.reloadPage),bindings:[{platform:"windows,linux",shortcut:"Ctrl+R"},{platform:"windows,linux",shortcut:"F5"},{platform:"mac",shortcut:"Meta+R"}]}),t.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.hard-reload",loadActionDelegate:async()=>new((await ve()).InspectorMain.ReloadActionDelegate),title:he(ue.hardReloadPage),bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+R"},{platform:"windows,linux",shortcut:"Shift+F5"},{platform:"windows,linux",shortcut:"Ctrl+F5"},{platform:"windows,linux",shortcut:"Ctrl+Shift+F5"},{platform:"mac",shortcut:"Shift+Meta+R"}]}),t.ActionRegistration.registerActionExtension({actionId:"rendering.toggle-prefers-color-scheme",category:"RENDERING",title:he(ue.toggleCssPrefersColorSchemeMedia),loadActionDelegate:async()=>new((await ve()).RenderingOptions.ReloadActionDelegate)}),o.Settings.registerSettingExtension({category:"NETWORK",title:he(ue.forceAdBlocking),settingName:"network.ad-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:he(ue.blockAds)},{value:!1,title:he(ue.showAds)}]}),o.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Synced",title:he(ue.autoOpenDevTools),settingName:"auto-attach-to-created-pages",settingType:"boolean",order:2,defaultValue:!1,options:[{value:!0,title:he(ue.autoOpenDevTools)},{value:!1,title:he(ue.doNotAutoOpen)}]}),o.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:he(ue.disablePaused),settingName:"disable-paused-state-overlay",settingType:"boolean",defaultValue:!1}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ve()).InspectorMain.NodeIndicator.instance(),order:2,location:"main-toolbar-left"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ve()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",experiment:"outermost-target-selector"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ve()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0,experiment:"outermost-target-selector"});const Re={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},Ee=e.i18n.registerUIStrings("panels/application/application-meta.ts",Re),Ae=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ee);let be;async function Pe(){return be||(be=await import("../../panels/application/application.js")),be}t.ViewManager.registerViewExtension({location:"panel",id:"resources",title:Ae(Re.application),commandPrompt:Ae(Re.showApplication),order:70,loadView:async()=>(await Pe()).ResourcesPanel.ResourcesPanel.instance(),tags:[Ae(Re.pwa)]}),t.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear",title:Ae(Re.clearSiteData),loadActionDelegate:async()=>new((await Pe()).StorageView.ActionDelegate)}),t.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear-incl-third-party-cookies",title:Ae(Re.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>new((await Pe()).StorageView.ActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===be?[]:(e=>[e.BackgroundServiceView.BackgroundServiceView])(be),loadActionDelegate:async()=>new((await Pe()).BackgroundServiceView.ActionDelegate),category:"BACKGROUND_SERVICES",options:[{value:!0,title:Ae(Re.startRecordingEvents)},{value:!1,title:Ae(Re.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),o.Revealer.registerRevealer({contextTypes:()=>[n.Resource.Resource],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Pe()).ResourcesPanel.ResourceRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[n.ResourceTreeModel.ResourceTreeFrame],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Pe()).ResourcesPanel.FrameDetailsRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[d.PreloadingForward.RuleSetView],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Pe()).ResourcesPanel.RuleSetViewRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[d.PreloadingForward.AttemptViewWithFilter],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Pe()).ResourcesPanel.AttemptViewWithFilterRevealer)});const fe={issues:"Issues",showIssues:"Show Issues"},Te=e.i18n.registerUIStrings("panels/issues/issues-meta.ts",fe),ke=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Te);let De;async function xe(){return De||(De=await import("../../panels/issues/issues.js")),De}t.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:ke(fe.issues),commandPrompt:ke(fe.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await xe()).IssuesPane.IssuesPane)}),o.Revealer.registerRevealer({contextTypes:()=>[g.Issue.Issue],destination:o.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await xe()).IssueRevealer.IssueRevealer)});const Le={layers:"Layers",showLayers:"Show Layers"},Me=e.i18n.registerUIStrings("panels/layers/layers-meta.ts",Le),Ne=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Me);let Ie;t.ViewManager.registerViewExtension({location:"panel",id:"layers",title:Ne(Le.layers),commandPrompt:Ne(Le.showLayers),order:100,persistence:"closeable",loadView:async()=>(await async function(){return Ie||(Ie=await import("../../panels/layers/layers.js")),Ie}()).LayersPanel.LayersPanel.instance()});const Ce={showLighthouse:"Show `Lighthouse`"},Ve=e.i18n.registerUIStrings("panels/lighthouse/lighthouse-meta.ts",Ce),Oe=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ve);let Be;t.ViewManager.registerViewExtension({location:"panel",id:"lighthouse",title:e.i18n.lockedLazyString("Lighthouse"),commandPrompt:Oe(Ce.showLighthouse),order:90,loadView:async()=>(await async function(){return Be||(Be=await import("../../panels/lighthouse/lighthouse.js")),Be}()).LighthousePanel.LighthousePanel.instance(),tags:[e.i18n.lockedLazyString("lighthouse"),e.i18n.lockedLazyString("pwa")]});const ze={media:"Media",video:"video",showMedia:"Show Media"},Ue=e.i18n.registerUIStrings("panels/media/media-meta.ts",ze),We=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ue);let Fe;t.ViewManager.registerViewExtension({location:"panel",id:"medias",title:We(ze.media),commandPrompt:We(ze.showMedia),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return Fe||(Fe=await import("../../panels/media/media.js")),Fe}()).MainView.MainView),tags:[We(ze.media),We(ze.video)]});const _e={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},je=e.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",_e),qe=e.i18n.getLazilyComputedLocalizedString.bind(void 0,je);let He;async function Ge(){return He||(He=await import("../../panels/mobile_throttling/mobile_throttling.js")),He}t.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:qe(_e.throttling),commandPrompt:qe(_e.showThrottling),order:35,loadView:async()=>new((await Ge()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:qe(_e.goOffline),loadActionDelegate:async()=>new((await Ge()).ThrottlingManager.ActionDelegate),tags:[qe(_e.device),qe(_e.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:qe(_e.enableSlowGThrottling),loadActionDelegate:async()=>new((await Ge()).ThrottlingManager.ActionDelegate),tags:[qe(_e.device),qe(_e.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:qe(_e.enableFastGThrottling),loadActionDelegate:async()=>new((await Ge()).ThrottlingManager.ActionDelegate),tags:[qe(_e.device),qe(_e.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:qe(_e.goOnline),loadActionDelegate:async()=>new((await Ge()).ThrottlingManager.ActionDelegate),tags:[qe(_e.device),qe(_e.throttlingTag)]}),o.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const Ke={performanceMonitor:"Performance monitor",performance:"performance",systemMonitor:"system monitor",monitor:"monitor",activity:"activity",metrics:"metrics",showPerformanceMonitor:"Show Performance monitor"},Ye=e.i18n.registerUIStrings("panels/performance_monitor/performance_monitor-meta.ts",Ke),Xe=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ye);let Ze;t.ViewManager.registerViewExtension({location:"drawer-view",id:"performance.monitor",title:Xe(Ke.performanceMonitor),commandPrompt:Xe(Ke.showPerformanceMonitor),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return Ze||(Ze=await import("../../panels/performance_monitor/performance_monitor.js")),Ze}()).PerformanceMonitor.PerformanceMonitorImpl),tags:[Xe(Ke.performance),Xe(Ke.systemMonitor),Xe(Ke.monitor),Xe(Ke.activity),Xe(Ke.metrics)]});const Qe={performance:"Performance",showPerformance:"Show Performance",record:"Record",stop:"Stop",recordAndReload:"Record and reload",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view"},Je=e.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",Qe),$e=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Je);let et;async function tt(){return et||(et=await import("../../panels/timeline/timeline.js")),et}function ot(e){return void 0===et?[]:e(et)}t.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:$e(Qe.performance),commandPrompt:$e(Qe.showPerformance),order:50,experiment:"enable-performance-panel",loadView:async()=>(await tt()).TimelinePanel.TimelinePanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),options:[{value:!0,title:$e(Qe.record)},{value:!1,title:$e(Qe.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:$e(Qe.recordAndReload),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}],experiment:"!react-native-specific-ui"}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),title:$e(Qe.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),title:$e(Qe.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:$e(Qe.previousFrame),contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:$e(Qe.nextFrame),contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:$e(Qe.showRecentTimelineSessions),contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),title:$e(Qe.previousRecording),contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await tt()).TimelinePanel.ActionDelegate),title:$e(Qe.nextRecording),contextTypes:()=>ot((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),o.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:$e(Qe.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),o.Linkifier.registerLinkifier({contextTypes:()=>ot((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await tt()).CLSLinkifier.Linkifier.instance()}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});const it={webaudio:"WebAudio",audio:"audio",showWebaudio:"Show WebAudio"},nt=e.i18n.registerUIStrings("panels/web_audio/web_audio-meta.ts",it),at=e.i18n.getLazilyComputedLocalizedString.bind(void 0,nt);let rt;t.ViewManager.registerViewExtension({location:"drawer-view",id:"web-audio",title:at(it.webaudio),commandPrompt:at(it.showWebaudio),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return rt||(rt=await import("../../panels/web_audio/web_audio.js")),rt}()).WebAudioView.WebAudioView),tags:[at(it.audio)]});const st={webauthn:"WebAuthn",showWebauthn:"Show WebAuthn"},lt=e.i18n.registerUIStrings("panels/webauthn/webauthn-meta.ts",st),ct=e.i18n.getLazilyComputedLocalizedString.bind(void 0,lt);let dt;t.ViewManager.registerViewExtension({location:"drawer-view",id:"webauthn-pane",title:ct(st.webauthn),commandPrompt:ct(st.showWebauthn),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return dt||(dt=await import("../../panels/webauthn/webauthn.js")),dt}()).WebauthnPane.WebauthnPaneImpl)});const gt={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},wt=e.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",gt),mt=e.i18n.getLazilyComputedLocalizedString.bind(void 0,wt);t.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:"LAYERS",title:mt(gt.resetView),bindings:[{shortcut:"0"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:"LAYERS",title:mt(gt.switchToPanMode),bindings:[{shortcut:"x"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:"LAYERS",title:mt(gt.switchToRotateMode),bindings:[{shortcut:"v"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:"LAYERS",title:mt(gt.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:"LAYERS",title:mt(gt.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.up",category:"LAYERS",title:mt(gt.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.down",category:"LAYERS",title:mt(gt.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.left",category:"LAYERS",title:mt(gt.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.right",category:"LAYERS",title:mt(gt.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});const pt={recorder:"Recorder",showRecorder:"Show Recorder",startStopRecording:"Start/Stop recording",createRecording:"Create a new recording",replayRecording:"Replay recording",toggleCode:"Toggle code view"},ut=e.i18n.registerUIStrings("panels/recorder/recorder-meta.ts",pt),yt=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ut);let ht;async function St(){return ht||(ht=await import("../../panels/recorder/recorder.js")),ht}function vt(e,t){return void 0===ht?[]:t&&ht.RecorderPanel.RecorderPanel.instance().isActionPossible(t)?e(ht):[]}const Rt="chrome-recorder";t.ViewManager.defaultOptionsForTabs[Rt]=!0,t.ViewManager.registerViewExtension({location:"panel",id:Rt,commandPrompt:yt(pt.showRecorder),title:yt(pt.recorder),order:90,persistence:"closeable",loadView:async()=>(await St()).RecorderPanel.RecorderPanel.instance()}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.create-recording",title:yt(pt.createRecording),loadActionDelegate:async()=>new((await St()).RecorderPanel.ActionDelegate)}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.start-recording",title:yt(pt.startStopRecording),contextTypes:()=>vt((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.start-recording"),loadActionDelegate:async()=>new((await St()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.replay-recording",title:yt(pt.replayRecording),contextTypes:()=>vt((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.replay-recording"),loadActionDelegate:async()=>new((await St()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+Enter",platform:"windows,linux"},{shortcut:"Meta+Enter",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.toggle-code-view",title:yt(pt.toggleCode),contextTypes:()=>vt((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.toggle-code-view"),loadActionDelegate:async()=>new((await St()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+B",platform:"windows,linux"},{shortcut:"Meta+B",platform:"mac"}]}),self.runtime=i.Runtime.Runtime.instance({forceNew:!0}),new w.MainImpl.MainImpl;
