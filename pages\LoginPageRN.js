import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';

const API_BASE_URL = 'http://localhost:3002'; // Changez selon votre IP

const LoginPageRN = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [motDepass, setMotDepass] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !motDepass.trim()) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion pour:', email);
      
      const response = await fetch(`${API_BASE_URL}/api/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          motDepass: motDepass.trim(),
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        console.log('✅ Connexion réussie');
        
        // Vérifier le rôle de l'utilisateur
        if (data.user && data.user.role === 'Tech') {
          console.log('👨‍🔧 Redirection vers le tableau de bord technicien');
          
          // Réinitialiser les champs
          setEmail('');
          setMotDepass('');
          
          // Naviguer vers le tableau de bord principal
          navigation.replace('Main');
        } else {
          Alert.alert(
            'Accès refusé', 
            'Seuls les techniciens peuvent accéder à cette application mobile.'
          );
        }
      } else {
        console.log('❌ Échec de la connexion:', data.message);
        Alert.alert('Erreur de connexion', data.message || 'Email ou mot de passe incorrect');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la connexion:', error);
      Alert.alert(
        'Erreur de connexion', 
        'Impossible de se connecter au serveur. Vérifiez votre connexion internet.'
      );
    } finally {
      setLoading(false);
    }
  };

  const fillTestCredentials = () => {
    setEmail('<EMAIL>');
    setMotDepass('Tech123');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardContainer}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.loginContainer}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.logo}>💧</Text>
              <Text style={styles.title}>AquaTrack</Text>
              <Text style={styles.subtitle}>Gestion de Facturation Mobile</Text>
            </View>

            {/* Formulaire de connexion */}
            <View style={styles.formContainer}>
              <Text style={styles.formTitle}>🔐 Connexion Technicien</Text>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>📧 Email</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>🔒 Mot de passe</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre mot de passe"
                  value={motDepass}
                  onChangeText={setMotDepass}
                  secureTextEntry
                  editable={!loading}
                />
              </View>

              {/* Bouton de connexion */}
              <TouchableOpacity
                style={[styles.loginButton, loading && styles.loginButtonDisabled]}
                onPress={handleLogin}
                disabled={loading}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="white" />
                    <Text style={styles.loginButtonText}>Connexion...</Text>
                  </View>
                ) : (
                  <Text style={styles.loginButtonText}>🚀 Se connecter</Text>
                )}
              </TouchableOpacity>

              {/* Bouton de test (développement) */}
              {__DEV__ && (
                <TouchableOpacity
                  style={styles.testButton}
                  onPress={fillTestCredentials}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>🧪 Remplir données de test</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                Application mobile pour techniciens
              </Text>
              <Text style={styles.versionText}>Version 1.0.0</Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f9ff',
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  loginContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    fontSize: 64,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 24,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f9fafb',
  },
  loginButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  loginButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#10b981',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 12,
  },
  testButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  versionText: {
    fontSize: 12,
    color: '#9ca3af',
  },
});

export default LoginPageRN;
