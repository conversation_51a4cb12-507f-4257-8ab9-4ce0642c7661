#!/usr/bin/env node
const Module = require("module");
const {resolve} = require("path");

/*
 * Simple wrapper around node that first registers Sucrase with default settings.
 *
 * This is meant for simple use cases, and doesn't support custom Node/V8 args,
 * executing a code snippet, a REPL, or other things that you might find in
 * node, babel-node, or ts-node. For more advanced use cases, you can use
 * `node -r sucrase/register` or register a require hook programmatically from
 * your own code.
 */
require("../register");

process.argv.splice(1, 1);
process.argv[1] = resolve(process.argv[1]);
Module.runMain();
