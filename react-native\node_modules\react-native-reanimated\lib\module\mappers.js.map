{"version": 3, "names": ["isJest", "runOnUI", "isSharedValue", "IS_JEST", "createMapperRegistry", "mappers", "Map", "sortedMappers", "runRequested", "processingMappers", "updateMappersOrder", "pre", "for<PERSON>ach", "mapper", "outputs", "output", "preMappers", "get", "undefined", "set", "push", "visited", "Set", "newOrder", "dfs", "add", "input", "inputs", "preMapper", "has", "mapperRun", "size", "length", "dirty", "worklet", "maybeRequestUpdates", "requestAnimationFrame", "queueMicrotask", "extractInputs", "resultArray", "Array", "isArray", "Object", "getPrototypeOf", "prototype", "element", "values", "start", "mapperID", "id", "sv", "addListener", "stop", "delete", "removeListener", "MAPPER_ID", "startMapper", "mapperRegistry", "global", "__mapperRegistry", "stopMapper"], "sourceRoot": "../../src", "sources": ["mappers.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,MAAM,QAAQ,sBAAmB;AAC1C,SAASC,OAAO,QAAQ,cAAW;AACnC,SAASC,aAAa,QAAQ,oBAAiB;AAE/C,MAAMC,OAAO,GAAGH,MAAM,CAAC,CAAC;AAYxB,SAASI,oBAAoBA,CAAA,EAAG;EAC9B,SAAS;;EACT,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAiB,CAAC;EACzC,IAAIC,aAAuB,GAAG,EAAE;EAEhC,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,SAASC,kBAAkBA,CAAA,EAAG;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,GAAG,GAAG,IAAIL,GAAG,CAAC,CAAC,CAAC,CAAC;IACvBD,OAAO,CAACO,OAAO,CAAEC,MAAM,IAAK;MAC1B,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClB,KAAK,MAAMC,MAAM,IAAIF,MAAM,CAACC,OAAO,EAAE;UACnC,MAAME,UAAU,GAAGL,GAAG,CAACM,GAAG,CAACF,MAAM,CAAC;UAClC,IAAIC,UAAU,KAAKE,SAAS,EAAE;YAC5BP,GAAG,CAACQ,GAAG,CAACJ,MAAM,EAAE,CAACF,MAAM,CAAC,CAAC;UAC3B,CAAC,MAAM;YACLG,UAAU,CAACI,IAAI,CAACP,MAAM,CAAC;UACzB;QACF;MACF;IACF,CAAC,CAAC;IACF,MAAMQ,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,MAAMC,QAAkB,GAAG,EAAE;IAC7B,SAASC,GAAGA,CAACX,MAAc,EAAE;MAC3BQ,OAAO,CAACI,GAAG,CAACZ,MAAM,CAAC;MACnB,KAAK,MAAMa,KAAK,IAAIb,MAAM,CAACc,MAAM,EAAE;QACjC,MAAMX,UAAU,GAAGL,GAAG,CAACM,GAAG,CAACS,KAAK,CAAC;QACjC,IAAIV,UAAU,EAAE;UACd,KAAK,MAAMY,SAAS,IAAIZ,UAAU,EAAE;YAClC,IAAI,CAACK,OAAO,CAACQ,GAAG,CAACD,SAAS,CAAC,EAAE;cAC3BJ,GAAG,CAACI,SAAS,CAAC;YAChB;UACF;QACF;MACF;MACAL,QAAQ,CAACH,IAAI,CAACP,MAAM,CAAC;IACvB;IACAR,OAAO,CAACO,OAAO,CAAEC,MAAM,IAAK;MAC1B,IAAI,CAACQ,OAAO,CAACQ,GAAG,CAAChB,MAAM,CAAC,EAAE;QACxBW,GAAG,CAACX,MAAM,CAAC;MACb;IACF,CAAC,CAAC;IACFN,aAAa,GAAGgB,QAAQ;EAC1B;EAEA,SAASO,SAASA,CAAA,EAAG;IACnBtB,YAAY,GAAG,KAAK;IACpB,IAAIC,iBAAiB,EAAE;MACrB;IACF;IACA,IAAI;MACFA,iBAAiB,GAAG,IAAI;MACxB,IAAIJ,OAAO,CAAC0B,IAAI,KAAKxB,aAAa,CAACyB,MAAM,EAAE;QACzCtB,kBAAkB,CAAC,CAAC;MACtB;MACA,KAAK,MAAMG,MAAM,IAAIN,aAAa,EAAE;QAClC,IAAIM,MAAM,CAACoB,KAAK,EAAE;UAChBpB,MAAM,CAACoB,KAAK,GAAG,KAAK;UACpBpB,MAAM,CAACqB,OAAO,CAAC,CAAC;QAClB;MACF;IACF,CAAC,SAAS;MACRzB,iBAAiB,GAAG,KAAK;IAC3B;EACF;EAEA,SAAS0B,mBAAmBA,CAAA,EAAG;IAC7B,IAAIhC,OAAO,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA2B,SAAS,CAAC,CAAC;IACb,CAAC,MAAM,IAAI,CAACtB,YAAY,EAAE;MACxB,IAAIC,iBAAiB,EAAE;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA2B,qBAAqB,CAACN,SAAS,CAAC;MAClC,CAAC,MAAM;QACLO,cAAc,CAACP,SAAS,CAAC;MAC3B;MACAtB,YAAY,GAAG,IAAI;IACrB;EACF;EAEA,SAAS8B,aAAaA,CACpBX,MAAe,EACfY,WAAkC,EACX;IACvB,IAAIC,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,EAAE;MACzB,KAAK,MAAMD,KAAK,IAAIC,MAAM,EAAE;QAC1BD,KAAK,IAAIY,aAAa,CAACZ,KAAK,EAAEa,WAAW,CAAC;MAC5C;IACF,CAAC,MAAM,IAAIrC,aAAa,CAACyB,MAAM,CAAC,EAAE;MAChCY,WAAW,CAACnB,IAAI,CAACO,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIe,MAAM,CAACC,cAAc,CAAChB,MAAM,CAAC,KAAKe,MAAM,CAACE,SAAS,EAAE;MAC7D;MACA;MACA;MACA,KAAK,MAAMC,OAAO,IAAIH,MAAM,CAACI,MAAM,CAACnB,MAAiC,CAAC,EAAE;QACtEkB,OAAO,IAAIP,aAAa,CAACO,OAAO,EAAEN,WAAW,CAAC;MAChD;IACF;IACA,OAAOA,WAAW;EACpB;EAEA,OAAO;IACLQ,KAAK,EAAEA,CACLC,QAAgB,EAChBd,OAAmB,EACnBP,MAAuB,EACvBb,OAAuB,KACpB;MACH,MAAMD,MAAc,GAAG;QACrBoC,EAAE,EAAED,QAAQ;QACZf,KAAK,EAAE,IAAI;QACXC,OAAO;QACPP,MAAM,EAAEW,aAAa,CAACX,MAAM,EAAE,EAAE,CAAC;QACjCb;MACF,CAAC;MACDT,OAAO,CAACc,GAAG,CAACN,MAAM,CAACoC,EAAE,EAAEpC,MAAM,CAAC;MAC9BN,aAAa,GAAG,EAAE;MAClB,KAAK,MAAM2C,EAAE,IAAIrC,MAAM,CAACc,MAAM,EAAE;QAC9BuB,EAAE,CAACC,WAAW,CAACtC,MAAM,CAACoC,EAAE,EAAE,MAAM;UAC9BpC,MAAM,CAACoB,KAAK,GAAG,IAAI;UACnBE,mBAAmB,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ;MACAA,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACDiB,IAAI,EAAGJ,QAAgB,IAAK;MAC1B,MAAMnC,MAAM,GAAGR,OAAO,CAACY,GAAG,CAAC+B,QAAQ,CAAC;MACpC,IAAInC,MAAM,EAAE;QACVR,OAAO,CAACgD,MAAM,CAACxC,MAAM,CAACoC,EAAE,CAAC;QACzB1C,aAAa,GAAG,EAAE;QAClB,KAAK,MAAM2C,EAAE,IAAIrC,MAAM,CAACc,MAAM,EAAE;UAC9BuB,EAAE,CAACI,cAAc,CAACzC,MAAM,CAACoC,EAAE,CAAC;QAC9B;MACF;IACF;EACF,CAAC;AACH;AAEA,IAAIM,SAAS,GAAG,IAAI;AAEpB,OAAO,SAASC,WAAWA,CACzBtB,OAAmB,EACnBP,MAAuB,GAAG,EAAE,EAC5Bb,OAAsB,GAAG,EAAE,EACnB;EACR,MAAMkC,QAAQ,GAAIO,SAAS,IAAI,CAAE;EAEjCtD,OAAO,CAAC,MAAM;IACZ,IAAIwD,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC5C,IAAIF,cAAc,KAAKvC,SAAS,EAAE;MAChCuC,cAAc,GAAGC,MAAM,CAACC,gBAAgB,GAAGvD,oBAAoB,CAAC,CAAC;IACnE;IACAqD,cAAc,CAACV,KAAK,CAACC,QAAQ,EAAEd,OAAO,EAAEP,MAAM,EAAEb,OAAO,CAAC;EAC1D,CAAC,CAAC,CAAC,CAAC;EAEJ,OAAOkC,QAAQ;AACjB;AAEA,OAAO,SAASY,UAAUA,CAACZ,QAAgB,EAAQ;EACjD/C,OAAO,CAAC,MAAM;IACZ,MAAMwD,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC9CF,cAAc,EAAEL,IAAI,CAACJ,QAAQ,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}