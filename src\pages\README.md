# Pages Frontend pour l'Architecture Modulaire

## Vue d'ensemble

Ce document décrit les pages frontend qui correspondent aux modules backend de l'architecture modulaire du serveur TechnicianDashboard.

## Pages à créer/modifier

### 1. `clients.js` - Page de gestion des clients
**Correspondance backend** : `server/clients.js`

**Fonctionnalités** :
- Affichage de la liste des clients depuis `/api/clients`
- Recherche de clients avec `/api/clients/search/:term`
- Formulaire d'ajout de nouveau client (POST `/api/clients`)
- Modification des informations client (PUT `/api/clients/:id`)
- Suppression de clients (DELETE `/api/clients/:id`)

**Interface** :
```jsx
// Exemple de structure
const ClientsPage = () => {
  const [clients, setClients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState(null);
  
  // Fonctions pour CRUD operations
  const fetchClients = () => { /* GET /api/clients */ };
  const searchClients = (term) => { /* GET /api/clients/search/:term */ };
  const addClient = (clientData) => { /* POST /api/clients */ };
  const updateClient = (id, clientData) => { /* PUT /api/clients/:id */ };
  const deleteClient = (id) => { /* DELETE /api/clients/:id */ };
  
  return (
    <div>
      {/* Interface de recherche */}
      {/* Liste des clients */}
      {/* Formulaire d'ajout/modification */}
    </div>
  );
};
```

### 2. `consommation.js` - Page de saisie des consommations
**Correspondance backend** : `server/consommation.js`

**Fonctionnalités** :
- Formulaire de saisie de nouvelle consommation (POST `/api/consommation`)
- Sélection de contrat depuis `/api/contracts`
- Auto-remplissage de la consommation précédente via `/api/contracts/:id/last-consommation`
- Affichage des consommations enregistrées (GET `/api/consommation`)
- Validation des données (consommation actuelle > précédente)

**Interface** :
```jsx
const ConsommationPage = () => {
  const [contracts, setContracts] = useState([]);
  const [selectedContract, setSelectedContract] = useState(null);
  const [lastConsommation, setLastConsommation] = useState(null);
  const [formData, setFormData] = useState({
    periode: '',
    consommationpre: '',
    consommationactuelle: '',
    jours: '',
    idcont: '',
    idtech: ''
  });
  
  // Fonctions
  const fetchContracts = () => { /* GET /api/contracts */ };
  const fetchLastConsommation = (contractId) => { /* GET /api/contracts/:id/last-consommation */ };
  const submitConsommation = () => { /* POST /api/consommation */ };
  
  return (
    <div>
      {/* Formulaire de saisie */}
      {/* Sélection de contrat */}
      {/* Affichage consommation précédente */}
    </div>
  );
};
```

### 3. `factures.js` - Page de gestion des factures
**Correspondance backend** : `server/factures.js`

**Fonctionnalités** :
- Affichage de toutes les factures (GET `/api/factures`)
- Filtrage par statut (GET `/api/factures/status/:status`)
- Mise à jour du statut (PUT `/api/factures/:id/status`)
- Affichage des statistiques (GET `/api/factures/stats/summary`)
- Création de nouvelles factures (POST `/api/factures`)

**Interface** :
```jsx
const FacturesPage = () => {
  const [factures, setFactures] = useState([]);
  const [statusFilter, setStatusFilter] = useState('toutes');
  const [stats, setStats] = useState({});
  
  // Fonctions
  const fetchFactures = () => { /* GET /api/factures */ };
  const fetchFacturesByStatus = (status) => { /* GET /api/factures/status/:status */ };
  const updateFactureStatus = (id, status) => { /* PUT /api/factures/:id/status */ };
  const fetchStats = () => { /* GET /api/factures/stats/summary */ };
  
  return (
    <div>
      {/* Statistiques */}
      {/* Filtres par statut */}
      {/* Liste des factures */}
      {/* Actions sur les factures */}
    </div>
  );
};
```

### 4. `codeQR.js` - Page de scanner QR
**Correspondance backend** : `server/codeQR.js`

**Fonctionnalités** :
- Interface de scan QR (GET `/api/scan/:qrCode`)
- Validation de QR codes (POST `/api/validate-qr`)
- Génération de QR codes (GET `/api/generate-qr/:clientId`)
- Historique des scans (GET `/api/qr-history/:techId`)

**Interface** :
```jsx
const CodeQRPage = () => {
  const [qrCode, setQrCode] = useState('');
  const [scanResult, setScanResult] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  
  // Fonctions
  const scanQRCode = (code) => { /* GET /api/scan/:qrCode */ };
  const validateQR = (code) => { /* POST /api/validate-qr */ };
  const generateQR = (clientId) => { /* GET /api/generate-qr/:clientId */ };
  
  return (
    <div>
      {/* Interface de scan */}
      {/* Résultats du scan */}
      {/* Génération de QR */}
    </div>
  );
};
```

### 5. `profile.js` - Page de profil utilisateur
**Correspondance backend** : `server/auth.js`

**Fonctionnalités** :
- Affichage des informations utilisateur (GET `/api/user/:id`)
- Modification du profil (PUT `/api/user/:id`)
- Changement de mot de passe (PUT `/api/user/:id/password`)

**Interface** :
```jsx
const ProfilePage = () => {
  const [user, setUser] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  
  // Fonctions
  const fetchUserInfo = () => { /* GET /api/user/:id */ };
  const updateProfile = (userData) => { /* PUT /api/user/:id */ };
  const changePassword = (passwords) => { /* PUT /api/user/:id/password */ };
  
  return (
    <div>
      {/* Informations utilisateur */}
      {/* Formulaire de modification */}
      {/* Changement de mot de passe */}
    </div>
  );
};
```

## Navigation et intégration

### Mise à jour du `TechnicianDashboard.js` frontend

Le dashboard principal doit être mis à jour pour utiliser ces nouvelles pages :

```jsx
const TechnicianDashboard = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  
  const renderPage = () => {
    switch(currentPage) {
      case 'clients': return <ClientsPage />;
      case 'consommation': return <ConsommationPage />;
      case 'factures': return <FacturesPage />;
      case 'scanner': return <CodeQRPage />;
      case 'profile': return <ProfilePage />;
      default: return <DashboardHome />;
    }
  };
  
  return (
    <div className="dashboard-container">
      <Sidebar onPageChange={setCurrentPage} />
      <main className="dashboard-content">
        {renderPage()}
      </main>
    </div>
  );
};
```

## Configuration des appels API

Créer un fichier `src/services/api.js` pour centraliser les appels :

```jsx
const API_BASE_URL = 'http://localhost:3002';

export const api = {
  // Clients
  clients: {
    getAll: () => fetch(`${API_BASE_URL}/api/clients`),
    getById: (id) => fetch(`${API_BASE_URL}/api/clients/${id}`),
    create: (data) => fetch(`${API_BASE_URL}/api/clients`, { method: 'POST', body: JSON.stringify(data) }),
    update: (id, data) => fetch(`${API_BASE_URL}/api/clients/${id}`, { method: 'PUT', body: JSON.stringify(data) }),
    delete: (id) => fetch(`${API_BASE_URL}/api/clients/${id}`, { method: 'DELETE' }),
    search: (term) => fetch(`${API_BASE_URL}/api/clients/search/${term}`)
  },
  
  // Consommation
  consommation: {
    getAll: () => fetch(`${API_BASE_URL}/api/consommation`),
    create: (data) => fetch(`${API_BASE_URL}/api/consommation`, { method: 'POST', body: JSON.stringify(data) }),
    getContracts: () => fetch(`${API_BASE_URL}/api/contracts`),
    getLastConsommation: (contractId) => fetch(`${API_BASE_URL}/api/contracts/${contractId}/last-consommation`)
  },
  
  // Factures
  factures: {
    getAll: () => fetch(`${API_BASE_URL}/api/factures`),
    getById: (id) => fetch(`${API_BASE_URL}/api/factures/${id}`),
    create: (data) => fetch(`${API_BASE_URL}/api/factures`, { method: 'POST', body: JSON.stringify(data) }),
    updateStatus: (id, status) => fetch(`${API_BASE_URL}/api/factures/${id}/status`, { method: 'PUT', body: JSON.stringify({status}) }),
    getByStatus: (status) => fetch(`${API_BASE_URL}/api/factures/status/${status}`),
    getStats: () => fetch(`${API_BASE_URL}/api/factures/stats/summary`)
  },
  
  // QR Code
  qr: {
    scan: (code) => fetch(`${API_BASE_URL}/api/scan/${code}`),
    validate: (code) => fetch(`${API_BASE_URL}/api/validate-qr`, { method: 'POST', body: JSON.stringify({qrCode: code}) }),
    generate: (clientId) => fetch(`${API_BASE_URL}/api/generate-qr/${clientId}`),
    getHistory: (techId) => fetch(`${API_BASE_URL}/api/qr-history/${techId}`)
  },
  
  // Auth
  auth: {
    login: (credentials) => fetch(`${API_BASE_URL}/login`, { method: 'POST', body: JSON.stringify(credentials) }),
    getUser: (id) => fetch(`${API_BASE_URL}/api/user/${id}`),
    updateUser: (id, data) => fetch(`${API_BASE_URL}/api/user/${id}`, { method: 'PUT', body: JSON.stringify(data) }),
    changePassword: (id, passwords) => fetch(`${API_BASE_URL}/api/user/${id}/password`, { method: 'PUT', body: JSON.stringify(passwords) }),
    getUsers: () => fetch(`${API_BASE_URL}/api/users`),
    createUser: (data) => fetch(`${API_BASE_URL}/api/users`, { method: 'POST', body: JSON.stringify(data) })
  }
};
```

## Prochaines étapes

1. Créer les pages frontend correspondantes
2. Implémenter les appels API
3. Tester l'intégration frontend-backend
4. Ajouter la gestion d'erreurs
5. Implémenter la validation côté client
6. Ajouter les notifications utilisateur
