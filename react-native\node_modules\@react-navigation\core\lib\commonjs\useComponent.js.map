{"version": 3, "names": ["NavigationContent", "render", "children", "useComponent", "renderRef", "React", "useRef", "current", "useEffect", "Error"], "sourceRoot": "../../src", "sources": ["useComponent.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAS/B,MAAMA,iBAAiB,GAAG,QAAiC;EAAA,IAAhC;IAAEC,MAAM;IAAEC;EAAgB,CAAC;EACpD,OAAOD,MAAM,CAACC,QAAQ,CAAC;AACzB,CAAC;AAEc,SAASC,YAAY,CAACF,MAAc,EAAE;EACnD,MAAMG,SAAS,GAAGC,KAAK,CAACC,MAAM,CAAgBL,MAAM,CAAC;;EAErD;EACA;EACA;EACAG,SAAS,CAACG,OAAO,GAAGN,MAAM;EAE1BI,KAAK,CAACG,SAAS,CAAC,MAAM;IACpBJ,SAAS,CAACG,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,OAAOF,KAAK,CAACC,MAAM,CAAC,SAAiD;IAAA,IAAhD;MAAEJ;IAAwC,CAAC;IAC9D,MAAMD,MAAM,GAAGG,SAAS,CAACG,OAAO;IAEhC,IAAIN,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM,IAAIQ,KAAK,CACb,+EAA+E,CAChF;IACH;IAEA,oBAAO,oBAAC,iBAAiB;MAAC,MAAM,EAAER;IAAO,GAAEC,QAAQ,CAAqB;EAC1E,CAAC,CAAC,CAACK,OAAO;AACZ"}