{"version": 3, "sources": ["../../../../../src/start/server/middleware/DevToolsPluginMiddleware.ts"], "sourcesContent": ["import assert from 'assert';\nimport send from 'send';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport DevToolsPluginManager, { DevToolsPluginEndpoint } from '../DevToolsPluginManager';\n\nexport { DevToolsPluginEndpoint };\n\nexport class DevToolsPluginMiddleware extends ExpoMiddleware {\n  constructor(\n    projectRoot: string,\n    private readonly pluginManager: DevToolsPluginManager\n  ) {\n    super(projectRoot, [DevToolsPluginEndpoint]);\n  }\n\n  override shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url?.startsWith(DevToolsPluginEndpoint)) {\n      return false;\n    }\n    return true;\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    assert(req.headers.host, 'Request headers must include host');\n    const { pathname } = new URL(req.url ?? '/', `http://${req.headers.host}`);\n    const pluginName = this.queryPossiblePluginName(\n      pathname.substring(DevToolsPluginEndpoint.length + 1)\n    );\n    const webpageRoot = await this.pluginManager.queryPluginWebpageRootAsync(pluginName);\n    if (!webpageRoot) {\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n\n    const pathInPluginRoot =\n      pathname.substring(DevToolsPluginEndpoint.length + pluginName.length + 1) || '/';\n    send(req, pathInPluginRoot, { root: webpageRoot }).pipe(res);\n  }\n\n  private queryPossiblePluginName(pathname: string): string {\n    const parts = pathname.split('/');\n    if (parts[0][0] === '@' && parts.length > 1) {\n      // Scoped package name\n      return `${parts[0]}/${parts[1]}`;\n    }\n    return parts[0];\n  }\n}\n"], "names": ["DevToolsPluginEndpoint", "DevToolsPluginMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "pluginManager", "shouldHandleRequest", "req", "url", "startsWith", "handleRequestAsync", "res", "assert", "headers", "host", "pathname", "URL", "pluginName", "queryPossiblePluginName", "substring", "length", "webpageRoot", "queryPluginWebpageRootAsync", "statusCode", "end", "pathInPluginRoot", "send", "root", "pipe", "parts", "split"], "mappings": ";;;;;;;;;;;IAOSA,sBAAsB;eAAtBA,6CAAsB;;IAElBC,wBAAwB;eAAxBA;;;;gEATM;;;;;;;gEACF;;;;;;gCAEc;uCAE+B;;;;;;AAIvD,MAAMA,iCAAiCC,8BAAc;IAC1DC,YACEC,WAAmB,EACnB,AAAiBC,aAAoC,CACrD;QACA,KAAK,CAACD,aAAa;YAACJ,6CAAsB;SAAC,QAF1BK,gBAAAA;IAGnB;IAESC,oBAAoBC,GAAkB,EAAW;YACnDA;QAAL,IAAI,GAACA,WAAAA,IAAIC,GAAG,qBAAPD,SAASE,UAAU,CAACT,6CAAsB,IAAG;YAChD,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAMU,mBAAmBH,GAAkB,EAAEI,GAAmB,EAAiB;QAC/EC,IAAAA,iBAAM,EAACL,IAAIM,OAAO,CAACC,IAAI,EAAE;QACzB,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAIT,IAAIC,GAAG,IAAI,KAAK,CAAC,OAAO,EAAED,IAAIM,OAAO,CAACC,IAAI,EAAE;QACzE,MAAMG,aAAa,IAAI,CAACC,uBAAuB,CAC7CH,SAASI,SAAS,CAACnB,6CAAsB,CAACoB,MAAM,GAAG;QAErD,MAAMC,cAAc,MAAM,IAAI,CAAChB,aAAa,CAACiB,2BAA2B,CAACL;QACzE,IAAI,CAACI,aAAa;YAChBV,IAAIY,UAAU,GAAG;YACjBZ,IAAIa,GAAG;YACP;QACF;QAEA,MAAMC,mBACJV,SAASI,SAAS,CAACnB,6CAAsB,CAACoB,MAAM,GAAGH,WAAWG,MAAM,GAAG,MAAM;QAC/EM,IAAAA,eAAI,EAACnB,KAAKkB,kBAAkB;YAAEE,MAAMN;QAAY,GAAGO,IAAI,CAACjB;IAC1D;IAEQO,wBAAwBH,QAAgB,EAAU;QACxD,MAAMc,QAAQd,SAASe,KAAK,CAAC;QAC7B,IAAID,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,OAAOA,MAAMT,MAAM,GAAG,GAAG;YAC3C,sBAAsB;YACtB,OAAO,GAAGS,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEA,KAAK,CAAC,EAAE,EAAE;QAClC;QACA,OAAOA,KAAK,CAAC,EAAE;IACjB;AACF"}