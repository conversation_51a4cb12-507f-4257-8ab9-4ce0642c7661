{"version": 3, "file": "FontUtils.types.js", "sourceRoot": "", "sources": ["../src/FontUtils.types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface RenderToImageOptions {\n  /**\n   * Font family name.\n   * @default system default\n   */\n  fontFamily?: string;\n  /**\n   * Size of the font.\n   * @default 24\n   */\n  size?: number;\n  /**\n   * Font color\n   * @default 'black'\n   */\n  color?: string;\n}\n"]}