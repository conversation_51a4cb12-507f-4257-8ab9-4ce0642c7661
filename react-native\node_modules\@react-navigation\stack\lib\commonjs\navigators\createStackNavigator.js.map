{"version": 3, "names": ["StackNavigator", "id", "initialRouteName", "children", "screenListeners", "screenOptions", "rest", "mode", "warnOnce", "headerMode", "keyboardHandlingEnabled", "undefined", "defaultScreenOptions", "presentation", "headerShown", "state", "descriptors", "navigation", "NavigationContent", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useEffect", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "target", "key", "createNavigatorFactory"], "sourceRoot": "../../../src", "sources": ["navigators/createStackNavigator.tsx"], "mappings": ";;;;;;AAAA;AAYA;AACA;AAQA;AAAiD;AAAA;AAAA;AAAA;AAWjD,SAASA,cAAc,OAOb;EAAA,IAPc;IACtBC,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACb,GAAGC;EACE,CAAC;EACN;EACA,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAoC;EAEtD,IAAAC,iBAAQ,EACND,IAAI,IAAI,IAAI,EACX,2BAA0BA,IAAK,yCAAwCA,IAAK,uHAAsH,CACpM;;EAED;EACA,MAAME,UAAU,GAAGH,IAAI,CAACG,UAAkD;EAE1E,IAAAD,iBAAQ,EACNC,UAAU,KAAK,MAAM,EACpB,iMAAgM,CAClM;EAED,IAAAD,iBAAQ,EACNC,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAC1C,kMAAiM,CACnM;;EAED;EACA,MAAMC,uBAAuB,GAAGJ,IAAI,CAACI,uBAAuB;EAE5D,IAAAF,iBAAQ,EACNE,uBAAuB,KAAKC,SAAS,EACpC,4NAA2N,CAC7N;EAED,MAAMC,oBAA4C,GAAG;IACnDC,YAAY,EAAEN,IAAI;IAClBO,WAAW,EAAEL,UAAU,GAAGA,UAAU,KAAK,MAAM,GAAG,IAAI;IACtDA,UAAU,EAAEA,UAAU,IAAIA,UAAU,KAAK,MAAM,GAAGA,UAAU,GAAGE,SAAS;IACxED;EACF,CAAC;EAED,MAAM;IAAEK,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzD,IAAAC,4BAAoB,EAMlBC,mBAAW,EAAE;IACbnB,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACbO;EACF,CAAC,CAAC;EAEJS,KAAK,CAACC,SAAS,CACb;IAAA;IAAA,OACE;MAAA,yBACAL,UAAU,CAACM,WAAW,0DAAtB,2BAAAN,UAAU,EAAe,UAAU,EAAGO,CAAC,IAAK;QAC1C,MAAMC,SAAS,GAAGR,UAAU,CAACQ,SAAS,EAAE;;QAExC;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,IACEX,KAAK,CAACY,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAA2CI,gBAAgB,EAC9D;YACA;YACA;YACAX,UAAU,CAACY,QAAQ,CAAC;cAClB,GAAGC,oBAAY,CAACC,QAAQ,EAAE;cAC1BC,MAAM,EAAEjB,KAAK,CAACkB;YAChB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;IAAC;EAAA,GACJ,CAAChB,UAAU,EAAEF,KAAK,CAACY,KAAK,EAAEZ,KAAK,CAACkB,GAAG,CAAC,CACrC;EAED,oBACE,oBAAC,iBAAiB,qBAChB,oBAAC,kBAAS,eACJ3B,IAAI;IACR,KAAK,EAAES,KAAM;IACb,WAAW,EAAEC,WAAY;IACzB,UAAU,EAAEC;EAAW,GACvB,CACgB;AAExB;AAAC,eAEc,IAAAiB,8BAAsB,EAKnClC,cAAc,CAAC;AAAA"}