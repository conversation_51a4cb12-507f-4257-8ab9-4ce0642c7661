{"version": 3, "file": "parsePodfileLock.js", "sourceRoot": "", "sources": ["../../src/utils/parsePodfileLock.ts"], "names": [], "mappings": ";;;AAkCA,0CAMC;AAkCD,4CAqBC;AAeD,oEAGC;AAjHD,qCAA+B;AAE/B,MAAM,oBAAoB,GAAG,kBAAkB,CAAC;AA0BhD;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,GAAW;IACzC,MAAM,QAAQ,GAAG,IAAA,cAAI,EAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAEM,MAAM,kBAAkB,GAAG,CAChC,GAA0D,EACzC,EAAE;IACnB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,mHAAmH;QACnH,+FAA+F;QAC/F,MAAM,kBAAkB,GAAG,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACtD,IAAI,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAG,CAAC,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAA,0BAAkB,EAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACxC,MAAM,OAAO,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YACrB,OAAO;gBACL,GAAG,OAAO;gBACV,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,0BAAkB,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;aACvD,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO;gBACL,GAAG,OAAO;gBACV,YAAY,EAAE,IAAA,0BAAkB,EAAC,CAAC,CAAC;aACpC,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AA9BW,QAAA,kBAAkB,sBA8B7B;AAEF,SAAgB,gBAAgB,CAAC,WAAmB;;IAClD,MAAM,QAAQ,GACZ,MAAA,eAAe,CAAC,WAAW,CAAC,mCAC5B,eAAe,CAAC,oBAAoB,GAAG,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACnE,OAAO;YACL,GAAG,GAAG;YACN,CAAC,oBAAoB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK;SACpD,CAAC;IACJ,CAAC,EAAE,EAAE,CAAQ,CAAC;IAEd,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,0BAAkB,CAAC,CAAC;QACvD,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW;;IACtC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAA,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,0CAAG,CAAC,CAAC,EAAE,CAAC;AACvD,CAAC;AAED,SAAS,eAAe,CAAC,GAAW;IAClC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW;IACvC,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,SAAgB,4BAA4B,CAAC,OAAoB,EAAE,GAAW;;IAC5E,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,eAAe,0CAAG,GAAG,CAAC,CAAC;IAC9C,OAAO,MAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,UAAU,CAAC,mCAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,OAAO,CAAC,mCAAI,IAAI,CAAC;AAC3D,CAAC", "sourcesContent": ["import { load } from 'js-yaml';\n\nconst EXTERNAL_SOURCES_KEY = 'EXTERNAL SOURCES';\n\nexport interface PodDependency {\n  name: string;\n  version?: string;\n  dependencies?: PodDependency[];\n}\n\nexport interface ExternalSource {\n  /** \"../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec\" */\n  ':podspec'?: string;\n  /** \"../node_modules/expo-application/ios\" */\n  ':path'?: string;\n}\n\nexport interface PodfileLock {\n  pods?: PodDependency[];\n  /** \"1.11.2\" */\n  cocoapods?: string;\n  externalSources?: Record<string, ExternalSource>;\n  /** 73e35020f8f5d49ffd32debe3c1bdd501f8029a6 */\n  podfileChecksum?: string;\n  /** { \"DoubleConversion\": \"cf9b38bf0b2d048436d9a82ad2abe1404f11e7de\" } */\n  specChecksums?: Record<string, string>;\n}\n\n/**\n * Parses a podfile.lock file from from YAML into a JSON object.\n *\n * @param str Podfile.lock file contents in YAML format.\n * @returns\n */\nexport function loadPodfileLock(str: string): null | Record<string, any> {\n  const contents = load(str);\n  if (!contents || typeof contents !== 'object') {\n    return null;\n  }\n  return contents;\n}\n\nexport const parsePodDependency = (\n  pod: string | Record<string, string | Record<string, any>>\n): PodDependency[] => {\n  if (typeof pod === 'string') {\n    // js-yaml fails to parse an array with a single item and instead formats it as a string divided by a `-` (hyphen).\n    // Here we match if a hyphen comes after a space. We use fake-nested-Podfile to test this hack.\n    const singleItemArrayBug = pod.match(/(.*)\\s-\\s(.*)/);\n    if (singleItemArrayBug?.[2]) {\n      return parsePodDependency({ [singleItemArrayBug[1]]: singleItemArrayBug[2] });\n    }\n\n    return [splitPodNameVersion(pod)];\n  }\n  return Object.entries(pod).map(([k, v]) => {\n    const results = splitPodNameVersion(k);\n\n    if (Array.isArray(v)) {\n      return {\n        ...results,\n        dependencies: v.map(x => parsePodDependency(x)).flat(),\n      };\n    } else if (typeof v === 'string') {\n      return {\n        ...results,\n        dependencies: parsePodDependency(v),\n      };\n    }\n\n    return results;\n  });\n};\n\nexport function parsePodfileLock(fileContent: string): PodfileLock | null {\n  const contents =\n    loadPodfileLock(fileContent) ??\n    loadPodfileLock(EXTERNAL_SOURCES_KEY + fileContent.split(EXTERNAL_SOURCES_KEY).slice(1));\n  if (!contents) {\n    return null;\n  }\n\n  const parsed = Object.entries(contents).reduce((acc, [key, value]) => {\n    return {\n      ...acc,\n      [kebabCaseToCamelCase(rubyCaseToKebab(key))]: value,\n    };\n  }, {}) as any;\n\n  if (Array.isArray(parsed.pods)) {\n    const parsedPods = parsed.pods.map(parsePodDependency);\n    parsed.pods = parsedPods.flat();\n  }\n\n  return parsed;\n}\n\nfunction splitPodNameVersion(pod: string): { name: string; version?: string } {\n  const [name] = pod.split(' ');\n  return { name, version: pod.match(/\\((.*)\\)/)?.[1] };\n}\n\nfunction rubyCaseToKebab(str: string) {\n  return str.toLowerCase().split(' ').join('-');\n}\n\nfunction kebabCaseToCamelCase(str: string) {\n  return str.replace(/-([a-z])/g, g => g[1].toUpperCase());\n}\n\nexport function getFilePathForExternalSource(podLock: PodfileLock, pod: string): string | null {\n  const source = podLock.externalSources?.[pod];\n  return source?.[':podspec'] ?? source?.[':path'] ?? null;\n}\n"]}