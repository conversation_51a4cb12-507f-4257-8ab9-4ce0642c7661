[ req ]
# Which algorithm to use
default_md = sha256
# Don't prompt the TTY for input, just use the config file values
prompt = no
# Interpret strings as utf8, not ASCII
utf8 = yes
# This specifies the section containing the distinguished name fields to
# prompt for when generating a certificate request.
distinguished_name = req_distinguished_name
# This specifies the configuration file section containing a list of extensions
# to add to the certificate request.
x509_extensions = v3_ca
# How long is the CA valid for
default_days = 7000

[ req_distinguished_name ]
CN = devcert

[ v3_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
# Mark our CA as a CA, and only allow it to issue server certificates - no intermediate certificates allowed
basicConstraints = critical, CA:true, pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign
