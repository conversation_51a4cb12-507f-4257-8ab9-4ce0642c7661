const express = require('express');
const { pool } = require('./database');
const PDFDocument = require('pdfkit');

const router = express.Router();

// Route pour récupérer toutes les factures
router.get('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures');

    const query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cont.reference as contrat_reference,
        s.nom as secteur_nom
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY f.date DESC, f.idfact DESC
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} factures récupérées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} facture(s) trouvée(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion au serveur de factures',
      error: error.message
    });
  }
});

// Route pour récupérer une facture spécifique par ID
router.get('/api/factures/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/factures/${id}`);

    const query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cont.reference as contrat_reference,
        s.nom as secteur_nom
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE f.idfact = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    console.log(`✅ Facture ${id} récupérée`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Facture trouvée'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la facture',
      error: error.message
    });
  }
});

// Route pour créer une nouvelle facture
router.post('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/factures:', req.body);
    const { date, idcons, montant, periode, reference, status = 'nonpayée' } = req.body;

    // Validation des champs requis
    if (!date || !idcons || !montant || !periode || !reference) {
      return res.status(400).json({
        success: false,
        message: 'Les champs date, idcons, montant, periode et reference sont requis'
      });
    }

    const query = `
      INSERT INTO facture (date, idcons, montant, periode, reference, status)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const values = [date, idcons, montant, periode, reference, status];
    const result = await pool.query(query, values);

    console.log('✅ Nouvelle facture créée:', result.rows[0]);
    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Facture créée avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la facture',
      error: error.message
    });
  }
});

// Route pour mettre à jour le statut d'une facture
router.put('/api/factures/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    console.log(`📥 Requête PUT /api/factures/${id}/status:`, { status });

    // Validation du statut
    if (!['payée', 'nonpayée'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Le statut doit être "payée" ou "nonpayée"'
      });
    }

    const query = `
      UPDATE facture 
      SET status = $1
      WHERE idfact = $2
      RETURNING *
    `;

    const result = await pool.query(query, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    console.log('✅ Statut de la facture mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Statut de la facture mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut de la facture',
      error: error.message
    });
  }
});

// Route pour récupérer les factures par statut
router.get('/api/factures/status/:status', async (req, res) => {
  try {
    const { status } = req.params;
    console.log(`📥 Requête GET /api/factures/status/${status}`);

    // Validation du statut
    if (!['payée', 'nonpayée', 'toutes'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Le statut doit être "payée", "nonpayée" ou "toutes"'
      });
    }

    let query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        cons.consommationpre,
        cons.consommationactuelle,
        cont.reference as contrat_reference
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
    `;

    let values = [];
    if (status !== 'toutes') {
      query += ' WHERE f.status = $1';
      values = [status];
    }

    query += ' ORDER BY f.date DESC, f.idfact DESC';

    const result = await pool.query(query, values);

    console.log(`✅ ${result.rows.length} factures trouvées avec le statut "${status}"`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} facture(s) trouvée(s) avec le statut "${status}"`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures par statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures par statut',
      error: error.message
    });
  }
});

// Route pour calculer les statistiques des factures
router.get('/api/factures/stats/summary', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures/stats/summary');

    const query = `
      SELECT 
        COUNT(*) as total_factures,
        COUNT(CASE WHEN status = 'payée' THEN 1 END) as factures_payees,
        COUNT(CASE WHEN status = 'nonpayée' THEN 1 END) as factures_non_payees,
        COALESCE(SUM(montant), 0) as montant_total,
        COALESCE(SUM(CASE WHEN status = 'payée' THEN montant ELSE 0 END), 0) as montant_paye,
        COALESCE(SUM(CASE WHEN status = 'nonpayée' THEN montant ELSE 0 END), 0) as montant_impaye
      FROM facture
    `;

    const result = await pool.query(query);

    console.log('✅ Statistiques des factures calculées');
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Statistiques des factures récupérées'
    });

  } catch (error) {
    console.error('❌ Erreur lors du calcul des statistiques des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques des factures',
      error: error.message
    });
  }
});

// Route pour générer le PDF d'une facture
router.get('/api/factures/:id/pdf', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📄 Génération PDF pour la facture ${id}`);

    // Récupérer toutes les informations de la facture avec les données client et consommation
    const query = `
      SELECT
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cons.periode as periode_consommation,
        cont.codeqr as contrat_reference,
        cont.marquecompteur,
        cont.numseriecompteur,
        s.nom as secteur_nom,
        t.prix as tarif_prix,
        t.valeurmin as tarif_min,
        t.valeurmax as tarif_max
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      LEFT JOIN tranch t ON cons.idtranch = t.idtranch
      WHERE f.idfact = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    const facture = result.rows[0];
    console.log('📊 Données facture récupérées:', {
      client: `${facture.client_nom} ${facture.client_prenom}`,
      montant: facture.montant,
      consommation: `${facture.consommationpre} → ${facture.consommationactuelle}`
    });

    // Créer le document PDF
    const doc = new PDFDocument({ margin: 50 });

    // Configuration des headers pour le téléchargement
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=facture-${id}.pdf`);

    // Pipe le PDF vers la réponse
    doc.pipe(res);

    // En-tête de la facture
    doc.fontSize(20).font('Helvetica-Bold');
    doc.text('AquaTrack', 50, 50);
    doc.fontSize(12).font('Helvetica');
    doc.text('Système de Facturation d\'Eau', 50, 75);
    doc.text('Province de Taounate', 50, 90);

    // Informations de la facture (côté droit)
    const factureNum = `FAC-${new Date(facture.date).getFullYear()}-${String(facture.idfact).padStart(3, '0')}`;
    doc.fontSize(14).font('Helvetica-Bold');
    doc.text(factureNum, 400, 50);
    doc.fontSize(10).font('Helvetica');
    doc.text(`Date: ${new Date(facture.date).toLocaleDateString('fr-FR')}`, 400, 70);
    doc.text(`Référence: ${facture.reference}`, 400, 85);
    doc.text(`Statut: ${facture.status === 'payée' ? 'Payée' : 'En attente'}`, 400, 100);

    // Ligne de séparation
    doc.moveTo(50, 130).lineTo(550, 130).stroke();

    // Informations client
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('INFORMATIONS CLIENT', 50, 150);
    doc.fontSize(10).font('Helvetica');
    doc.text(`Nom: ${facture.client_nom || 'N/A'} ${facture.client_prenom || ''}`, 50, 170);
    doc.text(`Adresse: ${facture.client_adresse || 'N/A'}`, 50, 185);
    doc.text(`Ville: ${facture.client_ville || 'N/A'}`, 50, 200);
    doc.text(`Téléphone: ${facture.client_tel || 'N/A'}`, 50, 215);
    doc.text(`Secteur: ${facture.secteur_nom || 'N/A'}`, 50, 230);

    // Informations compteur
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('INFORMATIONS COMPTEUR', 300, 150);
    doc.fontSize(10).font('Helvetica');
    doc.text(`Code QR: ${facture.contrat_reference || 'N/A'}`, 300, 170);
    doc.text(`Marque: ${facture.marquecompteur || 'N/A'}`, 300, 185);
    doc.text(`N° Série: ${facture.numseriecompteur || 'N/A'}`, 300, 200);

    // Ligne de séparation
    doc.moveTo(50, 260).lineTo(550, 260).stroke();

    // Détails de consommation
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('DÉTAILS DE CONSOMMATION', 50, 280);

    const consommationFacturee = (facture.consommationactuelle || 0) - (facture.consommationpre || 0);

    doc.fontSize(10).font('Helvetica');
    doc.text(`Période: ${facture.periode || 'N/A'}`, 50, 300);
    doc.text(`Consommation précédente: ${facture.consommationpre || 0} m³`, 50, 315);
    doc.text(`Consommation actuelle: ${facture.consommationactuelle || 0} m³`, 50, 330);
    doc.text(`Consommation facturée: ${consommationFacturee} m³`, 50, 345);
    doc.text(`Nombre de jours: ${facture.jours || 0}`, 50, 360);

    // Calcul tarifaire
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('CALCUL TARIFAIRE', 300, 280);
    doc.fontSize(10).font('Helvetica');
    doc.text(`Tarif: ${facture.tarif_prix || 0} DH/m³`, 300, 300);
    doc.text(`Tranche: ${facture.tarif_min || 0} - ${facture.tarif_max || 0} m³`, 300, 315);

    // Ligne de séparation
    doc.moveTo(50, 390).lineTo(550, 390).stroke();

    // Montant total
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('MONTANT À PAYER', 50, 410);
    doc.fontSize(20).font('Helvetica-Bold');
    doc.text(`${parseFloat(facture.montant || 0).toFixed(2)} DH`, 300, 410);

    // Pied de page
    doc.fontSize(8).font('Helvetica');
    doc.text('Cette facture est générée automatiquement par le système AquaTrack', 50, 500);
    doc.text(`Générée le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`, 50, 515);

    // Finaliser le PDF
    doc.end();

    console.log(`✅ PDF généré pour la facture ${id}`);

  } catch (error) {
    console.error('❌ Erreur lors de la génération du PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du PDF',
      error: error.message
    });
  }
});

module.exports = router;
