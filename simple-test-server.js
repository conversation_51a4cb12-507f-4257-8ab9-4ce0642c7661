const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🔄 Démarrage du serveur de test...');

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route racine appelée');
  res.json({
    message: 'Serveur de test fonctionnel',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Route pour les clients (données de test)
app.get('/api/clients', (req, res) => {
  console.log('📥 Récupération des clients');

  const testClients = [
    {
      idclient: 1,
      nom: 'Dupont',
      prenom: 'Jean',
      adresse: '123 Rue de la Paix',
      ville: 'Paris',
      tel: '0123456789',
      email: '<EMAIL>',
      ids: 1
    },
    {
      idclient: 2,
      nom: '<PERSON>',
      prenom: '<PERSON>',
      adresse: '456 Avenue des Champs',
      ville: 'Lyon',
      tel: '0987654321',
      email: '<EMAIL>',
      ids: 2
    }
  ];

  res.json({
    success: true,
    data: testClients,
    count: testClients.length,
    message: `${testClients.length} client(s) trouvé(s) (données de test)`
  });
});

// Route pour scanner un QR Code
app.get('/api/scan/:qrCode', (req, res) => {
  const { qrCode } = req.params;
  console.log(`🔍 Scan du QR Code: ${qrCode}`);

  // Données de test pour les QR Codes
  const qrData = {
    'QR-2025-0001': {
      client: {
        idclient: 1,
        nom: 'Dupont',
        prenom: 'Jean',
        adresse: '123 Rue de la Paix',
        ville: 'Paris',
        tel: '0123456789',
        email: '<EMAIL>',
        ids: 1
      },
      contract: {
        idcontract: 1,
        codeqr: 'QR-2025-0001',
        datecontract: '2024-01-15',
        marquecompteur: 'AquaMeter Pro',
        numseriecompteur: 'AM1001',
        posx: 36.8065,
        posy: 10.1815
      }
    },
    'QR-2025-0002': {
      client: {
        idclient: 2,
        nom: 'Martin',
        prenom: 'Marie',
        adresse: '456 Avenue des Champs',
        ville: 'Lyon',
        tel: '0987654321',
        email: '<EMAIL>',
        ids: 2
      },
      contract: {
        idcontract: 2,
        codeqr: 'QR-2025-0002',
        datecontract: '2024-02-10',
        marquecompteur: 'WaterFlow',
        numseriecompteur: 'WF2001',
        posx: 36.8075,
        posy: 10.1825
      }
    }
  };

  const data = qrData[qrCode];

  if (data) {
    console.log(`✅ QR Code ${qrCode} trouvé - Client: ${data.client.nom} ${data.client.prenom}`);
    res.json({
      success: true,
      data: data,
      message: 'QR Code trouvé avec succès (données de test)'
    });
  } else {
    console.log(`❌ QR Code ${qrCode} non trouvé`);
    res.status(404).json({
      success: false,
      message: 'QR Code non trouvé dans les données de test',
      qrCode: qrCode
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`\n🚀 Serveur de test démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/clients (clients de test)');
  console.log('\n✅ Serveur prêt !');
});

console.log('📝 Script de test chargé');
