{"version": 3, "sources": ["../../../src/customize/generate.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { DestinationResolutionProps, selectTemplatesAsync, TEMPLATES } from './templates';\nimport { installAsync } from '../install/installAsync';\nimport { Log } from '../log';\nimport { copyAsync } from '../utils/dir';\nimport { CommandError } from '../utils/errors';\n\nexport async function queryAndGenerateAsync(\n  projectRoot: string,\n  {\n    files,\n    props,\n    extras,\n  }: {\n    files: string[];\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  const valid = files.filter(\n    (file) => !!TEMPLATES.find((template) => template.destination(props) === file)\n  );\n\n  if (valid.length !== files.length) {\n    const diff = files.filter(\n      (file) => !TEMPLATES.find((template) => template.destination(props) === file)\n    );\n    throw new CommandError(\n      `Invalid files: ${diff.join(', ')}. Allowed: ${TEMPLATES.map((template) =>\n        template.destination(props)\n      ).join(', ')}`\n    );\n  }\n\n  if (!valid.length) {\n    return;\n  }\n  Log.log(`Generating: ${valid.join(', ')}`);\n  return generateAsync(projectRoot, {\n    answer: files.map((file) =>\n      TEMPLATES.findIndex((template) => template.destination(props) === file)\n    ),\n    props,\n    extras,\n  });\n}\n\n/** Select templates to generate then generate and install. */\nexport async function selectAndGenerateAsync(\n  projectRoot: string,\n  {\n    props,\n    extras,\n  }: {\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  const answer = await selectTemplatesAsync(projectRoot, props);\n\n  if (!answer?.length) {\n    Log.exit('\\n\\u203A Exiting with no change...', 0);\n  }\n\n  await generateAsync(projectRoot, {\n    answer,\n    props,\n    extras,\n  });\n}\n\nasync function generateAsync(\n  projectRoot: string,\n  {\n    answer,\n    props,\n    extras,\n  }: {\n    answer: number[];\n    props: DestinationResolutionProps;\n    /** Any extra props to pass to the install command. */\n    extras: any[];\n  }\n) {\n  // Copy files\n  await Promise.all(\n    answer.map(async (file) => {\n      const template = TEMPLATES[file];\n\n      if (template.configureAsync) {\n        if (await template.configureAsync(projectRoot)) {\n          return;\n        }\n      }\n\n      const projectFilePath = path.resolve(projectRoot, template.destination(props));\n      // copy the file from template\n      return copyAsync(template.file(projectRoot), projectFilePath);\n    })\n  );\n\n  // Install dependencies\n  const packages = answer\n    .map((file) => TEMPLATES[file].dependencies)\n    .flat()\n    .filter((pkg) => !resolveFrom.silent(projectRoot, pkg));\n  if (packages.length) {\n    Log.debug('Installing ' + packages.join(', '));\n    await installAsync(packages, {}, ['--dev', ...extras]);\n  }\n}\n"], "names": ["queryAndGenerateAsync", "selectAndGenerateAsync", "projectRoot", "files", "props", "extras", "valid", "filter", "file", "TEMPLATES", "find", "template", "destination", "length", "diff", "CommandError", "join", "map", "Log", "log", "generateAsync", "answer", "findIndex", "selectTemplatesAsync", "exit", "Promise", "all", "configure<PERSON><PERSON>", "projectFilePath", "path", "resolve", "copyAsync", "packages", "dependencies", "flat", "pkg", "resolveFrom", "silent", "debug", "installAsync"], "mappings": ";;;;;;;;;;;IASsBA,qBAAqB;eAArBA;;IA0CAC,sBAAsB;eAAtBA;;;;gEAnDL;;;;;;;gEACO;;;;;;2BAEoD;8BAC/C;qBACT;qBACM;wBACG;;;;;;AAEtB,eAAeD,sBACpBE,WAAmB,EACnB,EACEC,KAAK,EACLC,KAAK,EACLC,MAAM,EAMP;IAED,MAAMC,QAAQH,MAAMI,MAAM,CACxB,CAACC,OAAS,CAAC,CAACC,oBAAS,CAACC,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW,CAACR,WAAWI;IAG3E,IAAIF,MAAMO,MAAM,KAAKV,MAAMU,MAAM,EAAE;QACjC,MAAMC,OAAOX,MAAMI,MAAM,CACvB,CAACC,OAAS,CAACC,oBAAS,CAACC,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW,CAACR,WAAWI;QAE1E,MAAM,IAAIO,oBAAY,CACpB,CAAC,eAAe,EAAED,KAAKE,IAAI,CAAC,MAAM,WAAW,EAAEP,oBAAS,CAACQ,GAAG,CAAC,CAACN,WAC5DA,SAASC,WAAW,CAACR,QACrBY,IAAI,CAAC,OAAO;IAElB;IAEA,IAAI,CAACV,MAAMO,MAAM,EAAE;QACjB;IACF;IACAK,QAAG,CAACC,GAAG,CAAC,CAAC,YAAY,EAAEb,MAAMU,IAAI,CAAC,OAAO;IACzC,OAAOI,cAAclB,aAAa;QAChCmB,QAAQlB,MAAMc,GAAG,CAAC,CAACT,OACjBC,oBAAS,CAACa,SAAS,CAAC,CAACX,WAAaA,SAASC,WAAW,CAACR,WAAWI;QAEpEJ;QACAC;IACF;AACF;AAGO,eAAeJ,uBACpBC,WAAmB,EACnB,EACEE,KAAK,EACLC,MAAM,EAKP;IAED,MAAMgB,SAAS,MAAME,IAAAA,+BAAoB,EAACrB,aAAaE;IAEvD,IAAI,EAACiB,0BAAAA,OAAQR,MAAM,GAAE;QACnBK,QAAG,CAACM,IAAI,CAAC,sCAAsC;IACjD;IAEA,MAAMJ,cAAclB,aAAa;QAC/BmB;QACAjB;QACAC;IACF;AACF;AAEA,eAAee,cACblB,WAAmB,EACnB,EACEmB,MAAM,EACNjB,KAAK,EACLC,MAAM,EAMP;IAED,aAAa;IACb,MAAMoB,QAAQC,GAAG,CACfL,OAAOJ,GAAG,CAAC,OAAOT;QAChB,MAAMG,WAAWF,oBAAS,CAACD,KAAK;QAEhC,IAAIG,SAASgB,cAAc,EAAE;YAC3B,IAAI,MAAMhB,SAASgB,cAAc,CAACzB,cAAc;gBAC9C;YACF;QACF;QAEA,MAAM0B,kBAAkBC,eAAI,CAACC,OAAO,CAAC5B,aAAaS,SAASC,WAAW,CAACR;QACvE,8BAA8B;QAC9B,OAAO2B,IAAAA,cAAS,EAACpB,SAASH,IAAI,CAACN,cAAc0B;IAC/C;IAGF,uBAAuB;IACvB,MAAMI,WAAWX,OACdJ,GAAG,CAAC,CAACT,OAASC,oBAAS,CAACD,KAAK,CAACyB,YAAY,EAC1CC,IAAI,GACJ3B,MAAM,CAAC,CAAC4B,MAAQ,CAACC,sBAAW,CAACC,MAAM,CAACnC,aAAaiC;IACpD,IAAIH,SAASnB,MAAM,EAAE;QACnBK,QAAG,CAACoB,KAAK,CAAC,gBAAgBN,SAAShB,IAAI,CAAC;QACxC,MAAMuB,IAAAA,0BAAY,EAACP,UAAU,CAAC,GAAG;YAAC;eAAY3B;SAAO;IACvD;AACF"}