{"version": 3, "file": "WebCameraUtils.js", "sourceRoot": "", "sources": ["../../src/web/WebCameraUtils.ts"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,OAAO,KAAK,eAAe,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,sBAAsB,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAC7F,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAiB9D,MAAM,UAAU,YAAY,CAAC,UAAkB,EAAE,WAAmB,EAAE,KAAa;IACjF,MAAM,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;IACjC,MAAM,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;IACjC,MAAM,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC;IAEnC,OAAO;QACL,KAAK;QACL,MAAM;KACP,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,SAAS,CACvB,MAAyB,EACzB,SAAoB,EACpB,OAAe;IAEf,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7B,SAAS,CACP,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EACzB,gBAAgB,SAAS,sDAAsD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAClG,CAAC;IAEF,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;IAC1C,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;QACxB,SAAS,CACP,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,EAC5B,gBAAgB,OAAO,6DAA6D,CACrF,CAAC;QACF,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,mBAAgC,EAChC,KAAmC,EACnC,MAAoC;IAEpC,OAAO,mBAAmB,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,CAAC;AAC1F,CAAC;AAED,SAAS,0BAA0B,CAAC,MAA4B;IAC9D,MAAM,cAAc,GAAyB;QAC3C,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,KAAkB;QAC7B,aAAa,EAAE,KAAK;KACrB,CAAC;IACF,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,IAAI,GAAG,GAAiC,CAAC;QAC/C,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,IAAI,IAAI,cAAc,EAAE,CAAC;YAC3E,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAQ,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAM,eAAe,GAAG,IAAI,CAAC;AAE7B,MAAM,UAAU,gBAAgB,CAC9B,KAA8B,EAC9B,iBAAwE,EAAE;IAE1E,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAE1D,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAC1D,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1E,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,KAAuB,EACvB,EAAE,KAAK,GAAG,CAAC,EAAE,aAAa,GAAG,KAAK,EAAyD;IAE3F,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;IAC1C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,UAAU,EAAE,WAAW,EAAE,KAAM,CAAC,CAAC;IAExE,4EAA4E;IAC5E,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAE1D,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,yBAAyB;QACzB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC5C,CAAC;IACD,sBAAsB;IACtB,yCAAyC;IAEzC,wDAAwD;IACxD,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAE9C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,KAAuB,EACvB,cAAoC;IAEpC,MAAM,MAAM,GAAG,0BAA0B,CAAC,cAAc,CAAC,CAAC;IAC1D,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAClD,MAAM,EAAE,SAAS,EAAE,OAAO,GAAG,eAAe,EAAE,GAAG,MAAM,CAAC;IACxD,OAAO,SAAS,CAAC,MAAM,EAAE,SAAU,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,uBAAuB;IAC9B,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC;QAC7E,OAAO,SAAS,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC;IAC1D,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,mBAA+B,EAC/B,KAAmC,EACnC,MAAoC;IAEpC,MAAM,oBAAoB,GAA2B;QACnD,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,EAAE;KACV,CAAC;IAEF,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;QAC5D,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,MAAM,QAAQ,GAAG,uBAAuB,EAAE,CAAC;IAC3C,yBAAyB;IACzB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC7E,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACD,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChC,IAAI,mBAAmB,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;QAC/D,MAAM,UAAU,GAAG,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YACrD,oBAAoB,CAAC,KAA+B,CAAC,UAAU,GAAG;gBACjE,CAAC,GAAG,CAAC,EAAE,UAAU;aAClB,CAAC;QACJ,CAAC;aAAM,CAAC;YACL,oBAAoB,CAAC,KAA+B,CAAC,UAAU,GAAG;gBACjE,KAAK,EAAE,sBAAsB,CAAC,mBAAmB,CAAC;aACnD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,IAAI,uBAAuB,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC;QACxD,oBAAoB,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACzC,oBAAoB,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IAC7C,CAAC;IAED,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED,SAAS,uBAAuB,CAAC,KAAU;IACzC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC;AACnD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,mBAA+B,EAC/B,cAA4C,EAC5C,eAA6C;IAE7C,IAAI,CAAC;QACH,OAAO,MAAM,eAAe,CAAC,mBAAmB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;IACrF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,2DAA2D;QAC3D,oCAAoC;QACpC,IAAI,KAAK,YAAY,oBAAoB,IAAI,KAAK,CAAC,UAAU,KAAK,YAAY,EAAE,CAAC;YAC/E,MAAM,cAAc,GAAG,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACzE,OAAO,MAAM,eAAe,CAAC,cAAc,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QAChF,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,eAAe,CACnC,mBAA+B,EAC/B,cAA4C,EAC5C,eAA6C;IAE7C,MAAM,WAAW,GAA2B,mBAAmB,CAC7D,mBAAmB,EACnB,cAAc,EACd,eAAe,CAChB,CAAC;IACF,MAAM,MAAM,GAAgB,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAC;IACrE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,QAAQ;IACtB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAChF,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,CAAqB,EAAE,CAAqB;IACzE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACjD,MAAM,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACjD,OAAO,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;AACnD,CAAC;AAED,MAAM,UAAU,OAAO,CACrB,KAAuB,EACvB,QAA4B,EAC5B,MAA4B;IAE5B,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAE3C,MAAM,eAAe,GAA0B;QAC7C,GAAG,EAAE,MAAM;QACX,MAAM;QACN,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,MAAM,CAAC,SAAS,IAAI,KAAK;KAClC,CAAC;IAEF,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC;QAC3C,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;QAChC,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,UAAsB,EACtB,MAA0B,EAC1B,WAA8B,EAAE;IAEhC,IAAI,MAAM,EAAE,cAAc,EAAE,CAAC;QAC3B,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CACzF,CAAC;IACJ,CAAC;AACH,CAAC;AAED,yEAAyE;AACzE,KAAK,UAAU,mBAAmB,CAChC,UAAsB,EACtB,KAAuB,EACvB,WAA8B,EAAE;IAEhC,IAAI,OAAO,KAAK,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;QAChD,OAAO;IACT,CAAC;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;IAE7C,uGAAuG;IACvG,MAAM,WAAW,GAA4B,EAAE,CAAC;IAEhD,8CAA8C;IAC9C,MAAM,aAAa,GAAG;QACpB,sBAAsB;QACtB,kBAAkB;QAClB,KAAK;QACL,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,WAAW;QACX,eAAe;QACf,MAAM;KACE,CAAC;IAEX,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;QACrC,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,WAAW,CAAC,QAAQ,CAAC,GAAG,wBAAwB,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED,SAAS,iCAAiC,CACxC,aAA2C,EAC3C,WAAoC,EACpC,SAAgD;QAEhD,MAAM,gBAAgB,GAAG,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1D,OAAO,yBAAyB,CAAC;YAC/B,aAAa;YACb,WAAW;YACX,gBAAgB;YAChB,YAAY;YACZ,QAAQ;YACR,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED,IAAI,YAAY,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAC/D,WAAW,CAAC,SAAS,GAAG,iCAAiC,CACvD,WAAW,EACX,WAAW,EACX,eAAe,CAAC,4BAA4B,CAC7C,CAAC;IACJ,CAAC;IAED,IAAI,YAAY,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAC3D,WAAW,CAAC,KAAK,GAAG,iCAAiC,CACnD,OAAO,EACP,WAAW,EACX,eAAe,CAAC,4BAA4B,CAC7C,CAAC;IACJ,CAAC;IAED,IAAI,YAAY,CAAC,gBAAgB,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QACzE,WAAW,CAAC,gBAAgB,GAAG,iCAAiC,CAE9D,kBAAkB,EAAE,cAAc,EAAE,eAAe,CAAC,+BAA+B,CAAC,CAAC;IACzF,CAAC;IAED,IAAI,CAAC;QACH,MAAM,KAAK,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,OAAO;YAAE,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,MAA0B;IACxD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IACD,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,MAAM,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,MAAM,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,KAAuB,EACvB,MAA+C;IAE/C,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC;IAEvF,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,WAAW,EAAE,CAAC;QAC3C,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;IAC3B,CAAC;SAAM,IAAI,OAAQ,KAAa,CAAC,YAAY,KAAK,WAAW,EAAE,CAAC;QAC7D,KAAa,CAAC,YAAY,GAAG,MAAM,CAAC;IACvC,CAAC;SAAM,IAAI,MAAM,IAAI,eAAe,EAAE,CAAC;QACrC,KAAK,CAAC,GAAG,GAAG,eAAe,CAAC,MAA4B,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC;QACvF,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,SAAS,IAAK,KAAa,CAAC,YAAY,CAAC;QAC3E,IAAI,eAAe,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAClD,eAAe,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,KAAuB,EACvB,OAAqC;IAErC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC;IAE/B,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAU;IACpC,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AAC1C,CAAC;AAED,SAAS,wBAAwB,CAAC,KAAyB,EAAE,KAAc;IACzE,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;IACT,CAAC;IACD,0EAA0E;IAC1E,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,uCAAuC;IACvC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;AAC7D,CAAC;AAED,SAAS,YAAY,CAAC,KAAa,EAAE,EAAY,EAAE,KAAe,CAAC,CAAC,EAAE,CAAC,CAAC;IACtE,OAAO,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,yBAAyB,CAAI,KAOrC;IACC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,GACxF,KAAK,CAAC;IACR,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IACtC,IACE,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAC1C,gBAAgB;QAChB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EACvD,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,yBAAyB;YACzB,OAAO,CAAC,IAAI,CACV,MAAM,WAAW,MAAM,OAAO,sBAAsB,gBAAgB,uDAAuD,UAAU,qDAAqD,CAC3L,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["/* eslint-env browser */\nimport invariant from 'invariant';\n\nimport * as CapabilityUtils from './WebCapabilityUtils';\nimport { CameraTypeToFacingMode, ImageTypeFormat, MinimumConstraints } from './WebConstants';\nimport { requestUserMediaAsync } from './WebUserMediaManager';\nimport {\n  CameraType,\n  CameraCapturedPicture,\n  ImageSize,\n  ImageType,\n  WebCameraSettings,\n  CameraPictureOptions,\n} from '../Camera.types';\n\ninterface ConstrainLongRange {\n  max?: number;\n  min?: number;\n  exact?: number;\n  ideal?: number;\n}\n\nexport function getImageSize(videoWidth: number, videoHeight: number, scale: number): ImageSize {\n  const width = videoWidth * scale;\n  const ratio = videoWidth / width;\n  const height = videoHeight / ratio;\n\n  return {\n    width,\n    height,\n  };\n}\n\nexport function toDataURL(\n  canvas: HTMLCanvasElement,\n  imageType: ImageType,\n  quality: number\n): string {\n  const types = ['png', 'jpg'];\n  invariant(\n    types.includes(imageType),\n    `expo-camera: ${imageType} is not a valid ImageType. Expected a string from: ${types.join(', ')}`\n  );\n\n  const format = ImageTypeFormat[imageType];\n  if (imageType === 'jpg') {\n    invariant(\n      quality <= 1 && quality >= 0,\n      `expo-camera: ${quality} is not a valid image quality. Expected a number from 0...1`\n    );\n    return canvas.toDataURL(format, quality);\n  } else {\n    return canvas.toDataURL(format);\n  }\n}\n\nexport function hasValidConstraints(\n  preferredCameraType?: CameraType,\n  width?: number | ConstrainLongRange,\n  height?: number | ConstrainLongRange\n): boolean {\n  return preferredCameraType !== undefined && width !== undefined && height !== undefined;\n}\n\nfunction ensureCameraPictureOptions(config: CameraPictureOptions): CameraPictureOptions {\n  const captureOptions: CameraPictureOptions = {\n    scale: 1,\n    imageType: 'png' as ImageType,\n    isImageMirror: false,\n  };\n  for (const key in config) {\n    const prop = key as keyof CameraPictureOptions;\n    if (prop in config && config[prop] !== undefined && prop in captureOptions) {\n      captureOptions[prop] = config[prop] as any;\n    }\n  }\n  return captureOptions;\n}\n\nconst DEFAULT_QUALITY = 0.92;\n\nexport function captureImageData(\n  video: HTMLVideoElement | null,\n  pictureOptions: Pick<CameraPictureOptions, 'scale' | 'isImageMirror'> = {}\n): ImageData | null {\n  if (!video || video.readyState !== video.HAVE_ENOUGH_DATA) {\n    return null;\n  }\n  const canvas = captureImageContext(video, pictureOptions);\n\n  const context = canvas.getContext('2d', { alpha: false });\n  if (!context || !canvas.width || !canvas.height) {\n    return null;\n  }\n\n  const imageData = context.getImageData(0, 0, canvas.width, canvas.height);\n  return imageData;\n}\n\nexport function captureImageContext(\n  video: HTMLVideoElement,\n  { scale = 1, isImageMirror = false }: Pick<CameraPictureOptions, 'scale' | 'isImageMirror'>\n): HTMLCanvasElement {\n  const { videoWidth, videoHeight } = video;\n  const { width, height } = getImageSize(videoWidth, videoHeight, scale!);\n\n  // Build the canvas size and draw the camera image to the context from video\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  const context = canvas.getContext('2d', { alpha: false });\n\n  if (!context) {\n    // Should never be called\n    throw new Error('Context is not defined');\n  }\n  // sharp image details\n  // context.imageSmoothingEnabled = false;\n\n  // Flip horizontally (as css transform: rotateY(180deg))\n  if (isImageMirror) {\n    context.setTransform(-1, 0, 0, 1, canvas.width, 0);\n  }\n\n  context.drawImage(video, 0, 0, width, height);\n\n  return canvas;\n}\n\nexport function captureImage(\n  video: HTMLVideoElement,\n  pictureOptions: CameraPictureOptions\n): string {\n  const config = ensureCameraPictureOptions(pictureOptions);\n  const canvas = captureImageContext(video, config);\n  const { imageType, quality = DEFAULT_QUALITY } = config;\n  return toDataURL(canvas, imageType!, quality);\n}\n\nfunction getSupportedConstraints(): MediaTrackSupportedConstraints | null {\n  if (navigator.mediaDevices && navigator.mediaDevices.getSupportedConstraints) {\n    return navigator.mediaDevices.getSupportedConstraints();\n  }\n  return null;\n}\n\nexport function getIdealConstraints(\n  preferredCameraType: CameraType,\n  width?: number | ConstrainLongRange,\n  height?: number | ConstrainLongRange\n): MediaStreamConstraints {\n  const preferredConstraints: MediaStreamConstraints = {\n    audio: false,\n    video: {},\n  };\n\n  if (hasValidConstraints(preferredCameraType, width, height)) {\n    return MinimumConstraints;\n  }\n\n  const supports = getSupportedConstraints();\n  // TODO(Bacon): Test this\n  if (!supports || !supports.facingMode || !supports.width || !supports.height) {\n    return MinimumConstraints;\n  }\n  const types = ['front', 'back'];\n  if (preferredCameraType && types.includes(preferredCameraType)) {\n    const facingMode = CameraTypeToFacingMode[preferredCameraType];\n    if (isWebKit()) {\n      const key = facingMode === 'user' ? 'exact' : 'ideal';\n      (preferredConstraints.video as MediaTrackConstraints).facingMode = {\n        [key]: facingMode,\n      };\n    } else {\n      (preferredConstraints.video as MediaTrackConstraints).facingMode = {\n        ideal: CameraTypeToFacingMode[preferredCameraType],\n      };\n    }\n  }\n\n  if (isMediaTrackConstraints(preferredConstraints.video)) {\n    preferredConstraints.video.width = width;\n    preferredConstraints.video.height = height;\n  }\n\n  return preferredConstraints;\n}\n\nfunction isMediaTrackConstraints(input: any): input is MediaTrackConstraints {\n  return input && typeof input.video !== 'boolean';\n}\n\n/**\n * Invoke getStreamDevice a second time with the opposing camera type if the preferred type cannot be retrieved.\n *\n * @param preferredCameraType\n * @param preferredWidth\n * @param preferredHeight\n */\nexport async function getPreferredStreamDevice(\n  preferredCameraType: CameraType,\n  preferredWidth?: number | ConstrainLongRange,\n  preferredHeight?: number | ConstrainLongRange\n): Promise<MediaStream> {\n  try {\n    return await getStreamDevice(preferredCameraType, preferredWidth, preferredHeight);\n  } catch (error) {\n    // A hack on desktop browsers to ensure any camera is used.\n    // eslint-disable-next-line no-undef\n    if (error instanceof OverconstrainedError && error.constraint === 'facingMode') {\n      const nextCameraType = preferredCameraType === 'back' ? 'front' : 'back';\n      return await getStreamDevice(nextCameraType, preferredWidth, preferredHeight);\n    }\n    throw error;\n  }\n}\n\nexport async function getStreamDevice(\n  preferredCameraType: CameraType,\n  preferredWidth?: number | ConstrainLongRange,\n  preferredHeight?: number | ConstrainLongRange\n): Promise<MediaStream> {\n  const constraints: MediaStreamConstraints = getIdealConstraints(\n    preferredCameraType,\n    preferredWidth,\n    preferredHeight\n  );\n  const stream: MediaStream = await requestUserMediaAsync(constraints);\n  return stream;\n}\n\nexport function isWebKit(): boolean {\n  return /WebKit/.test(navigator.userAgent) && !/Edg/.test(navigator.userAgent);\n}\n\nexport function compareStreams(a: MediaStream | null, b: MediaStream | null): boolean {\n  if (!a || !b) {\n    return false;\n  }\n  const settingsA = a.getTracks()[0].getSettings();\n  const settingsB = b.getTracks()[0].getSettings();\n  return settingsA.deviceId === settingsB.deviceId;\n}\n\nexport function capture(\n  video: HTMLVideoElement,\n  settings: MediaTrackSettings,\n  config: CameraPictureOptions\n): CameraCapturedPicture {\n  const base64 = captureImage(video, config);\n\n  const capturedPicture: CameraCapturedPicture = {\n    uri: base64,\n    base64,\n    width: 0,\n    height: 0,\n    format: config.imageType ?? 'jpg',\n  };\n\n  if (settings) {\n    const { width = 0, height = 0 } = settings;\n    capturedPicture.width = width;\n    capturedPicture.height = height;\n    capturedPicture.exif = settings;\n  }\n\n  if (config.onPictureSaved) {\n    config.onPictureSaved(capturedPicture);\n  }\n  return capturedPicture;\n}\n\nexport async function syncTrackCapabilities(\n  cameraType: CameraType,\n  stream: MediaStream | null,\n  settings: WebCameraSettings = {}\n): Promise<void> {\n  if (stream?.getVideoTracks) {\n    await Promise.all(\n      stream.getVideoTracks().map((track) => onCapabilitiesReady(cameraType, track, settings))\n    );\n  }\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints\nasync function onCapabilitiesReady(\n  cameraType: CameraType,\n  track: MediaStreamTrack,\n  settings: WebCameraSettings = {}\n): Promise<void> {\n  if (typeof track.getCapabilities !== 'function') {\n    return;\n  }\n\n  const capabilities = track.getCapabilities();\n\n  // Create an empty object because if you set a constraint that isn't available an error will be thrown.\n  const constraints: MediaTrackConstraintSet = {};\n\n  // TODO(Bacon): Add `pointsOfInterest` support\n  const clampedValues = [\n    'exposureCompensation',\n    'colorTemperature',\n    'iso',\n    'brightness',\n    'contrast',\n    'saturation',\n    'sharpness',\n    'focusDistance',\n    'zoom',\n  ] as const;\n\n  for (const property of clampedValues) {\n    if (capabilities[property]) {\n      constraints[property] = convertNormalizedSetting(capabilities[property], settings[property]);\n    }\n  }\n\n  function validatedInternalConstrainedValue<IConvertedType>(\n    constraintKey: keyof MediaTrackCapabilities,\n    settingsKey: keyof WebCameraSettings,\n    converter: (settingValue: any) => IConvertedType\n  ) {\n    const convertedSetting = converter(settings[settingsKey]);\n    return validatedConstrainedValue({\n      constraintKey,\n      settingsKey,\n      convertedSetting,\n      capabilities,\n      settings,\n      cameraType,\n    });\n  }\n\n  if (capabilities.focusMode && settings.autoFocus !== undefined) {\n    constraints.focusMode = validatedInternalConstrainedValue<MediaTrackConstraintSet['focusMode']>(\n      'focusMode',\n      'autoFocus',\n      CapabilityUtils.convertAutoFocusJSONToNative\n    );\n  }\n\n  if (capabilities.torch && settings.flashMode !== undefined) {\n    constraints.torch = validatedInternalConstrainedValue<MediaTrackConstraintSet['torch']>(\n      'torch',\n      'flashMode',\n      CapabilityUtils.convertFlashModeJSONToNative\n    );\n  }\n\n  if (capabilities.whiteBalanceMode && settings.whiteBalance !== undefined) {\n    constraints.whiteBalanceMode = validatedInternalConstrainedValue<\n      MediaTrackConstraintSet['whiteBalanceMode']\n    >('whiteBalanceMode', 'whiteBalance', CapabilityUtils.convertWhiteBalanceJSONToNative);\n  }\n\n  try {\n    await track.applyConstraints({ advanced: [constraints] });\n  } catch (error) {\n    if (__DEV__) console.warn('Failed to apply constraints', error);\n  }\n}\n\nexport function stopMediaStream(stream: MediaStream | null) {\n  if (!stream) {\n    return;\n  }\n  if (stream.getAudioTracks) {\n    stream.getAudioTracks().forEach((track) => track.stop());\n  }\n  if (stream.getVideoTracks) {\n    stream.getVideoTracks().forEach((track) => track.stop());\n  }\n  if (isMediaStreamTrack(stream)) {\n    stream.stop();\n  }\n}\n\nexport function setVideoSource(\n  video: HTMLVideoElement,\n  stream: MediaStream | MediaSource | Blob | null\n): void {\n  const createObjectURL = window.URL.createObjectURL ?? window.webkitURL.createObjectURL;\n\n  if (typeof video.srcObject !== 'undefined') {\n    video.srcObject = stream;\n  } else if (typeof (video as any).mozSrcObject !== 'undefined') {\n    (video as any).mozSrcObject = stream;\n  } else if (stream && createObjectURL) {\n    video.src = createObjectURL(stream as MediaSource | Blob);\n  }\n\n  if (!stream) {\n    const revokeObjectURL = window.URL.revokeObjectURL ?? window.webkitURL.revokeObjectURL;\n    const source = video.src ?? video.srcObject ?? (video as any).mozSrcObject;\n    if (revokeObjectURL && typeof source === 'string') {\n      revokeObjectURL(source);\n    }\n  }\n}\n\nexport function isCapabilityAvailable(\n  video: HTMLVideoElement,\n  keyName: keyof MediaTrackCapabilities\n): boolean {\n  const stream = video.srcObject;\n\n  if (stream instanceof MediaStream) {\n    const videoTrack = stream.getVideoTracks()[0];\n    return !!videoTrack.getCapabilities?.()?.[keyName];\n  }\n\n  return false;\n}\n\nfunction isMediaStreamTrack(input: any): input is MediaStreamTrack {\n  return typeof input.stop === 'function';\n}\n\nfunction convertNormalizedSetting(range: MediaSettingsRange, value?: number): number | undefined {\n  if (!value) {\n    return;\n  }\n  // convert the normalized incoming setting to the native camera zoom range\n  const converted = convertRange(value, [range.min, range.max]);\n  // clamp value so we don't get an error\n  return Math.min(range.max, Math.max(range.min, converted));\n}\n\nfunction convertRange(value: number, r2: number[], r1: number[] = [0, 1]): number {\n  return ((value - r1[0]) * (r2[1] - r2[0])) / (r1[1] - r1[0]) + r2[0];\n}\n\nfunction validatedConstrainedValue<T>(props: {\n  constraintKey: keyof MediaTrackCapabilities;\n  settingsKey: keyof WebCameraSettings;\n  convertedSetting: T;\n  capabilities: MediaTrackCapabilities;\n  settings: WebCameraSettings;\n  cameraType: string;\n}): T | undefined {\n  const { constraintKey, settingsKey, convertedSetting, capabilities, settings, cameraType } =\n    props;\n  const setting = settings[settingsKey];\n  if (\n    Array.isArray(capabilities[constraintKey]) &&\n    convertedSetting &&\n    !capabilities[constraintKey].includes(convertedSetting)\n  ) {\n    if (__DEV__) {\n      // Only warn in dev mode.\n      console.warn(\n        ` { ${settingsKey}: \"${setting}\" } (converted to \"${convertedSetting}\" in the browser) is not supported for camera type \"${cameraType}\" in your browser. Using the default value instead.`\n      );\n    }\n    return undefined;\n  }\n  return convertedSetting;\n}\n"]}