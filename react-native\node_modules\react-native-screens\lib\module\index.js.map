{"version": 3, "names": ["enableScreens", "enableFreeze", "screensEnabled", "freezeEnabled", "default", "Screen", "InnerScreen", "ScreenContext", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderSearchBarView", "SearchBar", "ScreenContainer", "ScreenStack", "ScreenStackItem", "FullWindowOverlay", "ScreenFooter", "ScreenContentWrapper", "isSearchBarAvailableForCurrentPlatform", "compatibilityFlags", "executeNativeBackPress", "useTransitionProgress"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA;AACA;AACA,OAAO,8BAA8B;AAErC,cAAc,SAAS;;AAEvB;AACA;AACA;AACA,SACEA,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,aAAa,QACR,QAAQ;;AAEf;AACA;AACA;AACA,SACEC,OAAO,IAAIC,MAAM,EACjBC,WAAW,EACXC,aAAa,QACR,qBAAqB;AAE5B,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB,EACzBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,8BAA8B,QACzB,sCAAsC;AAE7C,SAASV,OAAO,IAAIW,SAAS,QAAQ,wBAAwB;AAC7D,SAASX,OAAO,IAAIY,eAAe,QAAQ,8BAA8B;AACzE,SAASZ,OAAO,IAAIa,WAAW,QAAQ,0BAA0B;AACjE,SAASb,OAAO,IAAIc,eAAe,QAAQ,8BAA8B;AACzE,SAASd,OAAO,IAAIe,iBAAiB,QAAQ,gCAAgC;AAC7E,SAASf,OAAO,IAAIgB,YAAY,QAAQ,2BAA2B;AACnE,SAAShB,OAAO,IAAIiB,oBAAoB,QAAQ,mCAAmC;;AAEnF;AACA;AACA;AACA,SACEC,sCAAsC,EACtCC,kBAAkB,EAClBC,sBAAsB,QACjB,SAAS;;AAEhB;AACA;AACA;AACA,SAASpB,OAAO,IAAIqB,qBAAqB,QAAQ,yBAAyB"}