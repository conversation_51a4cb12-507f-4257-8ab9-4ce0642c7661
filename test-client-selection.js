// Test de sélection de client avec contrat
const axios = require('axios');

async function testClientSelection() {
  console.log('🧪 Test de sélection de client avec contrat\n');

  try {
    // 1. Récupérer le client Benali Fatima (ID 15)
    console.log('📤 1. Récupération du client Benali Fatima...');
    const clientResponse = await axios.get('http://localhost:3002/api/clients');
    
    if (clientResponse.data.success) {
      const benaliClient = clientResponse.data.data.find(c => 
        c.nom === 'Benali' && c.prenom === 'Fatima'
      );
      
      if (benaliClient) {
        console.log('✅ Client trouvé:', benaliClient.nom, benaliClient.prenom);
        console.log('📋 ID Client:', benaliClient.idclient);
        
        // 2. Récupérer ses contrats
        console.log('\n📤 2. Récupération des contrats du client...');
        const contractsResponse = await axios.get(`http://localhost:3002/api/clients/${benaliClient.idclient}/contracts`);
        
        if (contractsResponse.data.success) {
          const contracts = contractsResponse.data.data;
          console.log(`✅ ${contracts.length} contrat(s) trouvé(s)`);
          
          if (contracts.length > 0) {
            const contract = contracts[0];
            console.log('📋 Détails du contrat:');
            console.log(`   - ID: ${contract.idcontract}`);
            console.log(`   - Code QR: ${contract.codeqr}`);
            console.log(`   - Marque compteur: ${contract.marquecompteur}`);
            console.log(`   - Date: ${new Date(contract.datecontract).toLocaleDateString()}`);
            
            console.log('\n🎯 Simulation de sélection automatique:');
            console.log(`   ✅ Le client a ${contracts.length} contrat(s)`);
            
            if (contracts.length === 1) {
              console.log(`   🎯 SÉLECTION AUTOMATIQUE: ${contract.codeqr}`);
              console.log(`   📝 Le champ contrat devrait afficher: "${contract.codeqr} - Contrat #${contract.idcontract}"`);
            } else {
              console.log(`   📋 SÉLECTION MANUELLE: ${contracts.length} options disponibles`);
            }
            
            // 3. Test de la dernière consommation
            console.log('\n📤 3. Test de la dernière consommation...');
            try {
              const lastConsResponse = await axios.get(`http://localhost:3002/api/contracts/${contract.idcontract}/last-consommation`);
              
              if (lastConsResponse.data.success && lastConsResponse.data.data) {
                console.log('✅ Dernière consommation trouvée:', lastConsResponse.data.data.consommationactuelle, 'm³');
                console.log('📅 Période:', lastConsResponse.data.data.periode);
              } else {
                console.log('ℹ️ Aucune consommation précédente pour ce contrat');
              }
            } catch (error) {
              console.log('⚠️ Erreur lors de la récupération de la dernière consommation');
            }
            
          } else {
            console.log('❌ Aucun contrat trouvé pour ce client');
          }
        } else {
          console.log('❌ Erreur lors de la récupération des contrats:', contractsResponse.data.message);
        }
        
      } else {
        console.log('❌ Client Benali Fatima non trouvé dans la liste');
      }
    } else {
      console.log('❌ Erreur lors de la récupération des clients:', clientResponse.data.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.response?.data || error.message);
  }
}

// Test de simulation de sélection depuis la liste
async function simulateListSelection() {
  console.log('\n\n🧪 Simulation de sélection depuis la liste de clients\n');
  
  try {
    // Simuler les données que la page ConsommationPage reçoit
    const selectedClientFromList = {
      idclient: 15,
      nom: 'Benali',
      prenom: 'Fatima',
      adresse: '45 Avenue Hassan II, près de l\'école Omar Ibn Al Khattab',
      ville: 'Sefrou',
      tel: '0647895655',
      email: '<EMAIL>'
    };
    
    console.log('📋 Client sélectionné depuis la liste:', selectedClientFromList);
    console.log('🔄 Simulation de handleClientChange(' + selectedClientFromList.idclient + ')...');
    
    // Simuler l'appel API que fait handleClientChange
    const response = await axios.get(`http://localhost:3002/api/clients/${selectedClientFromList.idclient}/contracts`);
    
    if (response.data.success) {
      const clientContracts = response.data.data;
      console.log(`✅ ${clientContracts.length} contrat(s) récupéré(s)`);
      
      if (clientContracts.length === 1) {
        const autoSelectedContract = clientContracts[0];
        console.log('🎯 Sélection automatique du contrat unique:', autoSelectedContract.idcontract);
        console.log('📝 Valeur pour le champ contrat:', autoSelectedContract.codeqr);
        console.log('✅ Le formulaire devrait être pré-rempli automatiquement');
      } else if (clientContracts.length > 1) {
        console.log('📋 Plusieurs contrats - sélection manuelle requise');
      } else {
        console.log('❌ Aucun contrat - message d\'erreur affiché');
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur simulation:', error.message);
  }
}

// Exécuter les tests
testClientSelection()
  .then(() => simulateListSelection())
  .then(() => {
    console.log('\n🎉 Tests terminés !');
    console.log('💡 Si le problème persiste, vérifiez la console du navigateur');
  })
  .catch(console.error);
