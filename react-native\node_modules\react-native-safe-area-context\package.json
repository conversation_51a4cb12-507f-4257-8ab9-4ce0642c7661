{"name": "react-native-safe-area-context", "version": "4.12.0", "description": "A flexible way to handle safe area, also works on Android and web.", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "react-native": "src/index.tsx", "types": "lib/typescript/src/index.d.ts", "source": "src/index.tsx", "sideEffects": false, "files": ["src", "lib", "android", "ios", "common", "jest", "*.podsp<PERSON>", "react-native.config.js", "!**/__tests__", "!example"], "author": "<PERSON><PERSON> <janic<PERSON>ples<PERSON>@gmail.com>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/evanbacon)"], "homepage": "https://github.com/th3rdwave/react-native-safe-area-context#readme", "license": "MIT", "scripts": {"start": "react-native start", "test": "yarn format:check && yarn validate:eslint && yarn validate:typescript && yarn validate:jest", "validate:eslint": "eslint \"src/**/*.{js,ts,tsx}\" \"example/**/*.{js,ts,tsx}\"", "validate:typescript": "tsc --project ./ --noEmit", "validate:jest": "jest", "format:prettier:check": "prettier \"src/**/*.{js,ts,tsx}\" \"example/**/*.{js,ts,tsx}\" --check", "format:prettier:write": "yarn format:prettier:check --write", "format:clang:check": "clang-format --dry-run --Werror --glob='{ios,android/src,common}/**/*.{h,cpp,m,mm}'", "format:clang:write": "clang-format -i --glob='{ios,android/src,common}/**/*.{h,hpp,cpp,m,mm}'", "format:spotless:check": "cd android && ./gradlew spotlessCheck", "format:spotless:write": "cd android && ./gradlew spotlessApply", "format:check": "yarn format:prettier:check && yarn format:clang:check && yarn format:spotless:check", "format:write": "yarn format:prettier:write && yarn format:clang:write && yarn format:spotless:write", "release": "release-it", "prepare": "bob build"}, "keywords": ["react-native", "react native", "react-native-web", "expo-web", "safe area", "view"], "publishConfig": {"registry": "https://registry.npmjs.org/"}, "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@commitlint/config-conventional": "^19.2.2", "@jest/globals": "^29.7.0", "@react-native/babel-preset": "^0.74.84", "@react-native/eslint-config": "^0.74.84", "@react-native/eslint-plugin-specs": "^0.74.84", "@release-it/conventional-changelog": "^8.0.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-test-renderer": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "babel-plugin-module-resolver": "^5.0.2", "clang-format": "^1.8.0", "commitlint": "^19.3.0", "eslint": "^8.57.0", "eslint-plugin-jest": "^28.6.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "jest": "^29.7.0", "prettier": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "^0.74.2", "react-native-builder-bob": "^0.23.2", "react-test-renderer": "^18.3.1", "release-it": "^17.4.0", "typescript": "^5.5.2"}, "repository": {"type": "git", "url": "https://github.com/th3rdwave/react-native-safe-area-context.git"}, "jest": {"preset": "react-native", "testEnvironment": "node", "clearMocks": true, "modulePathIgnorePatterns": ["<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}, "codegenConfig": {"android": {"javaPackageName": "com.th3rdwave.safeareacontext"}, "name": "safeareacontext", "type": "all", "jsSrcsDir": "./src/specs"}}