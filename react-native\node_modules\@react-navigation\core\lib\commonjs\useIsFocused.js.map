{"version": 3, "names": ["useIsFocused", "navigation", "useNavigation", "isFocused", "setIsFocused", "useState", "valueToReturn", "React", "useEffect", "unsubscribeFocus", "addListener", "unsubscribeBlur", "useDebugValue"], "sourceRoot": "../../src", "sources": ["useIsFocused.tsx"], "mappings": ";;;;;;AAAA;AAGA;AAA4C;AAAA;AAAA;AAE5C;AACA;AACA;AACA;AACe,SAASA,YAAY,GAAY;EAC9C,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAAE;EAClC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG,IAAAC,cAAQ,EAACJ,UAAU,CAACE,SAAS,CAAC;EAEhE,MAAMG,aAAa,GAAGL,UAAU,CAACE,SAAS,EAAE;EAE5C,IAAIA,SAAS,KAAKG,aAAa,EAAE;IAC/B;IACA;IACA;IACA;IACA;IACAF,YAAY,CAACE,aAAa,CAAC;EAC7B;EAEAC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,MAAMC,gBAAgB,GAAGR,UAAU,CAACS,WAAW,CAAC,OAAO,EAAE,MACvDN,YAAY,CAAC,IAAI,CAAC,CACnB;IAED,MAAMO,eAAe,GAAGV,UAAU,CAACS,WAAW,CAAC,MAAM,EAAE,MACrDN,YAAY,CAAC,KAAK,CAAC,CACpB;IAED,OAAO,MAAM;MACXK,gBAAgB,EAAE;MAClBE,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;EAEhBM,KAAK,CAACK,aAAa,CAACN,aAAa,CAAC;EAElC,OAAOA,aAAa;AACtB"}