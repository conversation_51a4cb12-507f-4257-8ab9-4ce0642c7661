import { ConfigPlugin } from 'expo/config-plugins';
declare const _default: ConfigPlugin<void | {
    locationAlwaysAndWhenInUsePermission?: string | false | undefined;
    locationAlwaysPermission?: string | false | undefined;
    locationWhenInUsePermission?: string | false | undefined;
    isIosBackgroundLocationEnabled?: boolean | undefined;
    isAndroidBackgroundLocationEnabled?: boolean | undefined;
    isAndroidForegroundServiceEnabled?: boolean | undefined;
}>;
export default _default;
