{"version": 3, "names": ["useRoute", "route", "React", "useContext", "NavigationRouteContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useRoute.tsx"], "mappings": ";;;;;;AACA;AAEA;AAA8D;AAAA;AAAA;AAG9D;AACA;AACA;AACA;AACA;AACe,SAASA,QAAQ,GAA0C;EACxE,MAAMC,KAAK,GAAGC,KAAK,CAACC,UAAU,CAACC,+BAAsB,CAAC;EAEtD,IAAIH,KAAK,KAAKI,SAAS,EAAE;IACvB,MAAM,IAAIC,KAAK,CACb,iFAAiF,CAClF;EACH;EAEA,OAAOL,KAAK;AACd"}