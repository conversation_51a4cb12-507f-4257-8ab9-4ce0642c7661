import*as e from"../../core/platform/platform.js";import*as t from"../../core/sdk/sdk.js";import*as n from"../bindings/bindings.js";import*as r from"../formatter/formatter.js";import*as o from"../text_utils/text_utils.js";const s=new WeakMap;function i(e){let t=s.get(e);return void 0===t&&(t=e.requestContent().then((({content:t})=>{if(null===t)return null;const n=e.isModule?"module":"script";return r.FormatterWorkerPool.formatterWorkerPool().javaScriptScopeTree(t,n)})),s.set(e,t)),t}var a=Object.freeze({__proto__:null,scopeTreeForScript:i});const c=new WeakMap,u=new WeakMap,l=new WeakMap;async function p(e){const t=await e.requestContent();let n=l.get(t);if(void 0===n){const{content:e}=t;n=e?new o.Text.Text(e):null,l.set(t,n)}return n}class f{name;positions;constructor(e,t=[]){this.name=e,this.positions=t}addPosition(e,t){this.positions.push({lineNumber:e,columnNumber:t})}}const g=async function(e){if(!e.sourceMapURL)return null;const t=await p(e);if(!t)return null;const n=await i(e);return n?{scopeTree:n,text:t}:null},m=function(e,t){if(!i(e,t))return[];let n=e;const r=[e];for(;;){let e=!1;for(const a of n.children){if(i(a,t)){r.push(a),n=a,e=!0;break}if(!(o=t,s=a,o.end<=s.start||s.end<=o.start||i(t,a)))return console.error("Wrong nesting of scopes"),[]}if(!e)break}var o,s;return r;function i(e,t){return e.start<=t.start&&e.end>=t.end}};async function b(e){const t=e.range()?.start,n=e.range()?.end;if(!t||!n)return[];const r=t.script();if(!r)return[];const o=await g(r);if(!o)return[];const{scopeTree:s,text:i}=o,a={start:i.offsetFromPosition(t.lineNumber,t.columnNumber),end:i.offsetFromPosition(n.lineNumber,n.columnNumber)};return m(s,a)}const d=async function(e,t,n){const r=await p(e);if(!r)return null;const s=[],i=new o.TextCursor.TextCursor(r.lineEndings());for(const e of t.variables){if(3===e.kind&&e.offsets.length<=1)continue;const t=new f(e.name);for(const n of e.offsets)i.resetTo(n),t.addPosition(i.lineNumber(),i.columnNumber());s.push(t)}const a=[];for(const e of n)for(const n of e.variables){let e=null;for(const r of n.offsets)r>=t.start&&r<t.end&&(e||(e=new f(n.name)),i.resetTo(r),e.addPosition(i.lineNumber(),i.columnNumber()));e&&a.push(e)}return{boundVariables:s,freeVariables:a}},h=/^\s*([A-Za-z_$][A-Za-z_$0-9]*)\s*([.;,=]?)\s*$/,w=async e=>{const t=e.callFrame().script,n=await b(e);return M(t,n)},M=async(e,t)=>{const r=t[t.length-1];if(!r)return{variableMapping:new Map,thisMapping:null};let o=c.get(r);const s=e.debuggerModel.sourceMapManager().sourceMapForClient(e);if(!o||o.sourceMap!==s){const n=(async()=>{const n=new Map;let o=null;if(!s)return{variableMapping:n,thisMapping:o};const a=[],c=(t,n)=>{for(const e of t.positions){const t=s.findEntry(e.lineNumber,e.columnNumber);if(t&&t.name)return void n(t.name)}a.push(async function(){if(s)for(const r of t.positions){const o=await i(e,s,t.name,r);if(o)return void n(o)}}())},u=await d(e,r,t.slice(0,-1));if(!u)return{variableMapping:n,thisMapping:o};for(const e of u.boundVariables)c(e,(t=>{"this"!==t&&n.set(e.name,t)}));for(const e of u.freeVariables)c(e,(t=>{"this"===t&&(o=e.name)}));return await Promise.all(a).then(P()),{variableMapping:n,thisMapping:o}})();o={sourceMap:s,mappingPromise:n},c.set(r,{sourceMap:s,mappingPromise:n})}return await o.mappingPromise;async function i(e,t,r,o){const s=t.findEntryRanges(o.lineNumber,o.columnNumber);if(!s)return null;const i=n.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiSourceCodeForSourceMapSourceURL(e.debuggerModel,s.sourceURL,e.isContentScript());if(!i)return null;const a=await p(e);if(!a)return null;const c=d(a.extract(s.range));if(!c)return null;const{name:u,punctuation:l}=c;if(u!==r)return null;const f=await p(i);if(!f)return null;const g=d(f.extract(s.sourceRange));if(!g)return null;const{name:m,punctuation:b}=g;return l===b||"comma"===l&&"semicolon"===b?m:null;function d(e){const t=e.match(h);if(!t)return null;const n=t[1];let r=null;switch(t[2]){case".":r="dot";break;case",":r="comma";break;case";":r="semicolon";break;case"=":r="equals";break;case"":r="none";break;default:return console.error(`Name token parsing error: unexpected token "${t[2]}"`),null}return{name:n,punctuation:r}}}},j=async e=>{const t=u.get(e);if(t)return t;const n=e.scopeChain(),r=await Promise.all(n.map(w)),o=new Map,s=new Set;for(const{variableMapping:e}of r)for(const[t,n]of e)if(n){if(!o.has(n)){const e=s.has(t)?null:t;o.set(n,e)}s.add(t)}return u.set(e,o),o};class v extends t.RemoteObject.RemoteObject{scope;object;constructor(e){super(),this.scope=e,this.object=e.object()}customPreview(){return this.object.customPreview()}get objectId(){return this.object.objectId}get type(){return this.object.type}get subtype(){return this.object.subtype}get value(){return this.object.value}get description(){return this.object.description}get hasChildren(){return this.object.hasChildren}get preview(){return this.object.preview}arrayLength(){return this.object.arrayLength()}getOwnProperties(e){return this.object.getOwnProperties(e)}async getAllProperties(e,t){const n=await this.object.getAllProperties(e,t),{variableMapping:r}=await w(this.scope),o=n.properties,s=n.internalProperties,i=o?.map((e=>{const t=r.get(e.name);return void 0!==t?e.cloneWithNewName(t):e}));return{properties:i??[],internalProperties:s}}async setPropertyValue(e,t){const{variableMapping:n}=await w(this.scope);let r;r="string"==typeof e?e:e.value;let o=r;for(const e of n.keys())if(n.get(e)===r){o=e;break}return this.object.setPropertyValue(o,t)}async deleteProperty(e){return this.object.deleteProperty(e)}callFunction(e,t){return this.object.callFunction(e,t)}callFunctionJSON(e,t){return this.object.callFunctionJSON(e,t)}release(){this.object.release()}debuggerModel(){return this.object.debuggerModel()}runtimeModel(){return this.object.runtimeModel()}isNode(){return this.object.isNode()}}async function y(e,t,n){const r=e.debuggerModel.sourceMapManager().sourceMapForClient(e);if(!r)return null;const s=r.findEntry(t,n);if(!s||!s.sourceURL)return null;const i=r.findScopeEntry(s.sourceURL,s.sourceLineNumber,s.sourceColumnNumber)?.scopeName();if(i)return i;const a=s.name;if(!a)return null;const c=await p(e);if(!c)return null;const u=new o.TextRange.TextRange(t,n,t,n+1);return"("!==c.extract(u)?null:a}let N=function(){};const P=()=>N;var k=Object.freeze({__proto__:null,IdentifierPositions:f,findScopeChainForDebuggerScope:b,scopeIdentifiers:d,resolveScopeChain:async function(e){if(!e)return null;const{pluginManager:t}=n.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),r=await t.resolveScopeChain(e);return r||e.scopeChain()},allVariablesInCallFrame:j,allVariablesAtPosition:async e=>{const t=new Map,n=e.script();if(!n)return t;const r=await g(n);if(!r)return t;const{scopeTree:o,text:s}=r,i=s.offsetFromPosition(e.lineNumber,e.columnNumber),a=m(o,{start:i,end:i}),c=new Set;for(;a.length>0;){const{variableMapping:e}=await M(n,a);for(const[n,r]of e)if(r){if(!t.has(r)){const e=c.has(n)?null:n;t.set(r,e)}c.add(n)}a.pop()}return t},resolveExpression:async(t,s,i,a,c,u)=>{if("application/wasm"===i.mimeType())return`memories["${s}"] ?? locals["${s}"] ?? tables["${s}"] ?? functions["${s}"] ?? globals["${s}"]`;if(!i.contentType().isFromSourceMap())return"";const l=await j(t);if(l.has(s))return l.get(s);const f=(await n.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiLocationToRawLocations(i,a,c)).find((e=>e.debuggerModel===t.debuggerModel));if(!f)return"";const g=f.script();if(!g)return"";const m=g.debuggerModel.sourceMapManager().sourceMapForClient(g);if(!m)return"";const b=await p(g);if(!b)return"";const d=m.reverseMapTextRanges(i.url(),new o.TextRange.TextRange(a,c,a,u));if(1!==d.length)return"";const[h]=d,w=b.extract(h);if(!w)return"";const M=await p(i);if(!M)return"";const v=m.findEntryRanges(h.startLine,h.startColumn),y=0===h.endColumn?h.endLine-1:h.endLine,N=0===h.endColumn?b.lineEndings()[y]:h.endColumn-1,P=m.findEntryRanges(y,N);if(!v||!P)return"";const k=M.extract(new o.TextRange.TextRange(v.sourceRange.startLine,v.sourceRange.startColumn,P.sourceRange.endLine,P.sourceRange.endColumn));return new RegExp(`^[\\s,;]*${e.StringUtilities.escapeForRegExp(s)}`,"g").test(k)?await r.FormatterWorkerPool.formatterWorkerPool().evaluatableJavaScriptSubstring(w):""},resolveThisObject:async e=>{if(!e)return null;const t=e.scopeChain();if(0===t.length)return e.thisObject();const{thisMapping:n}=await w(t[0]);if(!n)return e.thisObject();const r=await e.evaluate({expression:n,objectGroup:"backtrace",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!0});return"exceptionDetails"in r?!r.exceptionDetails&&r.object?r.object:e.thisObject():null},resolveScopeInObject:function(e){const t=e.range()?.end,n=e.range()?.start.script()??null;return"global"!==e.type()&&n&&t&&n.sourceMapURL?new v(e):e.object()},RemoteObject:v,resolveDebuggerFrameFunctionName:async function(e){const t=e.localScope()?.range()?.start;return t?await y(e.script,t.lineNumber,t.columnNumber):null},resolveProfileFrameFunctionName:async function({scriptId:e,lineNumber:r,columnNumber:o},s){if(!s||void 0===r||void 0===o||void 0===e)return null;const i=s.model(t.DebuggerModel.DebuggerModel),a=i?.scriptForId(String(e));if(!i||!a)return null;const c=n.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),u=new t.DebuggerModel.Location(i,e,r,o),l=await c.pluginManager.getFunctionInfo(a,u);if(l&&"frames"in l){const e=l.frames.at(-1);if(e?.name)return e.name}return await y(a,r,o)},getScopeResolvedForTest:P,setScopeResolvedForTest:e=>{N=e}});export{k as NamesResolver,a as ScopeTreeCache};
