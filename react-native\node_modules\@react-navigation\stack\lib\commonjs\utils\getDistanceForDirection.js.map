{"version": 3, "names": ["getDistanceForDirection", "layout", "gestureDirection", "multiplier", "getInvertedMultiplier", "height", "width"], "sourceRoot": "../../../src", "sources": ["utils/getDistanceForDirection.tsx"], "mappings": ";;;;;;AACA;AAA4D;AAE7C,SAASA,uBAAuB,CAC7CC,MAAc,EACdC,gBAAkC,EAC1B;EACR,MAAMC,UAAU,GAAG,IAAAC,8BAAqB,EAACF,gBAAgB,CAAC;EAE1D,QAAQA,gBAAgB;IACtB,KAAK,UAAU;IACf,KAAK,mBAAmB;MACtB,OAAOD,MAAM,CAACI,MAAM,GAAGF,UAAU;IACnC,KAAK,YAAY;IACjB,KAAK,qBAAqB;MACxB,OAAOF,MAAM,CAACK,KAAK,GAAGH,UAAU;EAAC;AAEvC"}