{"version": 3, "sources": ["../../../src/export/createMetadataJson.ts"], "sourcesContent": ["import path from 'path';\n\nimport type { BundleOutput } from './saveAssets';\n\nexport type BundlePlatform = 'android' | 'ios';\n\ntype PlatformMetadataAsset = { path: string; ext: string };\n\nexport type PlatformMetadata = { bundle: string; assets: PlatformMetadataAsset[] };\n\ntype FileMetadata = {\n  [key in BundlePlatform]: PlatformMetadata;\n};\n\nexport function createMetadataJson({\n  bundles,\n  fileNames,\n  embeddedHashSet,\n  domComponentAssetsMetadata,\n}: {\n  bundles: Partial<Record<BundlePlatform, Pick<BundleOutput, 'assets'>>>;\n  fileNames: Record<string, string[]>;\n  embeddedHashSet?: Set<string>;\n  domComponentAssetsMetadata?: Record<string, PlatformMetadataAsset[]>;\n}): {\n  version: 0;\n  bundler: 'metro';\n  fileMetadata: FileMetadata;\n} {\n  // Build metadata.json\n  return {\n    version: 0,\n    bundler: 'metro',\n    fileMetadata: Object.entries(bundles).reduce<Record<string, Partial<PlatformMetadata>>>(\n      (metadata, [platform, bundle]) => {\n        if (platform === 'web') return metadata;\n\n        // Collect all of the assets and convert them to the serial format.\n        const assets = bundle.assets\n          .filter((asset) => !embeddedHashSet || !embeddedHashSet.has(asset.hash))\n          .map((asset) =>\n            // Each asset has multiple hashes which we convert and then flatten.\n            asset.fileHashes?.map((hash) => ({\n              path: path.join('assets', hash),\n              ext: asset.type,\n            }))\n          )\n          .filter(Boolean)\n          .flat();\n\n        if (domComponentAssetsMetadata?.[platform] != null) {\n          assets.push(...domComponentAssetsMetadata?.[platform]);\n        }\n\n        return {\n          ...metadata,\n          [platform]: {\n            // Get the filename for each platform's bundle.\n            // TODO: Add multi-bundle support to EAS Update!!\n            bundle: fileNames[platform][0],\n            assets,\n          },\n        };\n      },\n      {}\n    ) as FileMetadata,\n  };\n}\n"], "names": ["createMetadataJson", "bundles", "fileNames", "embeddedHashSet", "domComponentAssetsMetadata", "version", "bundler", "fileMetadata", "Object", "entries", "reduce", "metadata", "platform", "bundle", "assets", "filter", "asset", "has", "hash", "map", "fileHashes", "path", "join", "ext", "type", "Boolean", "flat", "push"], "mappings": ";;;;+BAcgBA;;;eAAAA;;;;gEAdC;;;;;;;;;;;AAcV,SAASA,mBAAmB,EACjCC,OAAO,EACPC,SAAS,EACTC,eAAe,EACfC,0BAA0B,EAM3B;IAKC,sBAAsB;IACtB,OAAO;QACLC,SAAS;QACTC,SAAS;QACTC,cAAcC,OAAOC,OAAO,CAACR,SAASS,MAAM,CAC1C,CAACC,UAAU,CAACC,UAAUC,OAAO;YAC3B,IAAID,aAAa,OAAO,OAAOD;YAE/B,mEAAmE;YACnE,MAAMG,SAASD,OAAOC,MAAM,CACzBC,MAAM,CAAC,CAACC,QAAU,CAACb,mBAAmB,CAACA,gBAAgBc,GAAG,CAACD,MAAME,IAAI,GACrEC,GAAG,CAAC,CAACH;oBACJ,oEAAoE;gBACpEA;wBAAAA,oBAAAA,MAAMI,UAAU,qBAAhBJ,kBAAkBG,GAAG,CAAC,CAACD,OAAU,CAAA;wBAC/BG,MAAMA,eAAI,CAACC,IAAI,CAAC,UAAUJ;wBAC1BK,KAAKP,MAAMQ,IAAI;oBACjB,CAAA;eAEDT,MAAM,CAACU,SACPC,IAAI;YAEP,IAAItB,CAAAA,8CAAAA,0BAA4B,CAACQ,SAAS,KAAI,MAAM;gBAClDE,OAAOa,IAAI,IAAIvB,8CAAAA,0BAA4B,CAACQ,SAAS;YACvD;YAEA,OAAO;gBACL,GAAGD,QAAQ;gBACX,CAACC,SAAS,EAAE;oBACV,+CAA+C;oBAC/C,iDAAiD;oBACjDC,QAAQX,SAAS,CAACU,SAAS,CAAC,EAAE;oBAC9BE;gBACF;YACF;QACF,GACA,CAAC;IAEL;AACF"}