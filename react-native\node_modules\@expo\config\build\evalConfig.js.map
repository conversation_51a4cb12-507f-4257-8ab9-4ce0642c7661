{"version": 3, "file": "evalConfig.js", "names": ["_fs", "data", "require", "_requireFromString", "_interopRequireDefault", "_sucrase", "_Errors", "_Serialize", "_environment", "e", "__esModule", "default", "evalConfig", "configFile", "request", "contents", "readFileSync", "result", "code", "transform", "filePath", "transforms", "requireString", "error", "location", "extractLocationFromSyntaxError", "codeFrameColumns", "codeFrame", "start", "loc", "highlightCode", "message", "importantStack", "extractImportantStackFromNodeError", "resolveConfigExport", "line", "lineNumber", "column", "columnNumber", "isSyntaxError", "traces", "stack", "split", "filter", "startsWith", "length", "pop", "join", "SyntaxError", "constructor", "name", "hasBaseStaticConfig", "NON_STANDARD_SYMBOL", "config", "exportedObjectType", "Promise", "ConfigError", "mayHaveUnusedStaticConfig", "_hasBaseStaticConfig", "expo", "serializeSkippingMods"], "sources": ["../src/evalConfig.ts"], "sourcesContent": ["import { readFileSync } from 'fs';\nimport requireString from 'require-from-string';\nimport { transform } from 'sucrase';\n\nimport { AppJSONConfig, ConfigContext, ExpoConfig } from './Config.types';\nimport { ConfigError } from './Errors';\nimport { serializeSkippingMods } from './Serialize';\nimport { NON_STANDARD_SYMBOL } from './environment';\n\ntype RawDynamicConfig = AppJSONConfig | Partial<ExpoConfig> | null;\n\nexport type DynamicConfigResults = {\n  config: RawDynamicConfig;\n  exportedObjectType: string;\n  mayHaveUnusedStaticConfig: boolean;\n};\n\n/**\n * Transpile and evaluate the dynamic config object.\n * This method is shared between the standard reading method in getConfig, and the headless script.\n *\n * @param options configFile path to the dynamic app.config.*, request to send to the dynamic config if it exports a function.\n * @returns the serialized and evaluated config along with the exported object type (object or function).\n */\nexport function evalConfig(\n  configFile: string,\n  request: ConfigContext | null\n): DynamicConfigResults {\n  const contents = readFileSync(configFile, 'utf8');\n  let result: any;\n  try {\n    const { code } = transform(contents, {\n      filePath: configFile,\n      transforms: ['typescript', 'imports'],\n    });\n\n    result = requireString(code, configFile);\n  } catch (error: any) {\n    const location = extractLocationFromSyntaxError(error);\n\n    // Apply a code frame preview to the error if possible, sucrase doesn't do this by default.\n    if (location) {\n      const { codeFrameColumns } = require('@babel/code-frame');\n      const codeFrame = codeFrameColumns(contents, { start: error.loc }, { highlightCode: true });\n      error.codeFrame = codeFrame;\n      error.message += `\\n${codeFrame}`;\n    } else {\n      const importantStack = extractImportantStackFromNodeError(error);\n\n      if (importantStack) {\n        error.message += `\\n${importantStack}`;\n      }\n    }\n    throw error;\n  }\n  return resolveConfigExport(result, configFile, request);\n}\n\nfunction extractLocationFromSyntaxError(\n  error: Error | any\n): { line: number; column?: number } | null {\n  // sucrase provides the `loc` object\n  if (error.loc) {\n    return error.loc;\n  }\n\n  // `SyntaxError`s provide the `lineNumber` and `columnNumber` properties\n  if ('lineNumber' in error && 'columnNumber' in error) {\n    return { line: error.lineNumber, column: error.columnNumber };\n  }\n\n  return null;\n}\n\n// These kinda errors often come from syntax errors in files that were imported by the main file.\n// An example is a module that includes an import statement.\nfunction extractImportantStackFromNodeError(error: any): string | null {\n  if (isSyntaxError(error)) {\n    const traces = error.stack?.split('\\n').filter((line) => !line.startsWith('    at '));\n    if (!traces) return null;\n\n    // Remove redundant line\n    if (traces[traces.length - 1].startsWith('SyntaxError:')) {\n      traces.pop();\n    }\n    return traces.join('\\n');\n  }\n  return null;\n}\n\nfunction isSyntaxError(error: any): error is SyntaxError {\n  return error instanceof SyntaxError || error.constructor.name === 'SyntaxError';\n}\n\n/**\n * - Resolve the exported contents of an Expo config (be it default or module.exports)\n * - Assert no promise exports\n * - Return config type\n * - Serialize config\n *\n * @param result\n * @param configFile\n * @param request\n */\nexport function resolveConfigExport(\n  result: any,\n  configFile: string,\n  request: ConfigContext | null\n) {\n  // add key to static config that we'll check for after the dynamic is evaluated\n  // to see if the static config was used in determining the dynamic\n  const hasBaseStaticConfig = NON_STANDARD_SYMBOL;\n  if (request?.config) {\n    // @ts-ignore\n    request.config[hasBaseStaticConfig] = true;\n  }\n  if (result.default != null) {\n    result = result.default;\n  }\n  const exportedObjectType = typeof result;\n  if (typeof result === 'function') {\n    result = result(request);\n  }\n\n  if (result instanceof Promise) {\n    throw new ConfigError(`Config file ${configFile} cannot return a Promise.`, 'INVALID_CONFIG');\n  }\n\n  // If the key is not added, it suggests that the static config was not used as the base for the dynamic.\n  // note(Keith): This is the most common way to use static and dynamic config together, but not the only way.\n  // Hence, this is only output from getConfig() for informational purposes for use by tools like Expo Doctor\n  // to suggest that there *may* be a problem.\n  const mayHaveUnusedStaticConfig =\n    // @ts-ignore\n    request?.config?.[hasBaseStaticConfig] && !result?.[hasBaseStaticConfig];\n  if (result) {\n    delete result._hasBaseStaticConfig;\n  }\n\n  // If the expo object exists, ignore all other values.\n  if (result?.expo) {\n    result = serializeSkippingMods(result.expo);\n  } else {\n    result = serializeSkippingMods(result);\n  }\n\n  return { config: result, exportedObjectType, mayHaveUnusedStaticConfig };\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,mBAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,kBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,WAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,UAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,aAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,YAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoD,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAUpD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,UAAUA,CACxBC,UAAkB,EAClBC,OAA6B,EACP;EACtB,MAAMC,QAAQ,GAAG,IAAAC,kBAAY,EAACH,UAAU,EAAE,MAAM,CAAC;EACjD,IAAII,MAAW;EACf,IAAI;IACF,MAAM;MAAEC;IAAK,CAAC,GAAG,IAAAC,oBAAS,EAACJ,QAAQ,EAAE;MACnCK,QAAQ,EAAEP,UAAU;MACpBQ,UAAU,EAAE,CAAC,YAAY,EAAE,SAAS;IACtC,CAAC,CAAC;IAEFJ,MAAM,GAAG,IAAAK,4BAAa,EAACJ,IAAI,EAAEL,UAAU,CAAC;EAC1C,CAAC,CAAC,OAAOU,KAAU,EAAE;IACnB,MAAMC,QAAQ,GAAGC,8BAA8B,CAACF,KAAK,CAAC;;IAEtD;IACA,IAAIC,QAAQ,EAAE;MACZ,MAAM;QAAEE;MAAiB,CAAC,GAAGxB,OAAO,CAAC,mBAAmB,CAAC;MACzD,MAAMyB,SAAS,GAAGD,gBAAgB,CAACX,QAAQ,EAAE;QAAEa,KAAK,EAAEL,KAAK,CAACM;MAAI,CAAC,EAAE;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;MAC3FP,KAAK,CAACI,SAAS,GAAGA,SAAS;MAC3BJ,KAAK,CAACQ,OAAO,IAAI,KAAKJ,SAAS,EAAE;IACnC,CAAC,MAAM;MACL,MAAMK,cAAc,GAAGC,kCAAkC,CAACV,KAAK,CAAC;MAEhE,IAAIS,cAAc,EAAE;QAClBT,KAAK,CAACQ,OAAO,IAAI,KAAKC,cAAc,EAAE;MACxC;IACF;IACA,MAAMT,KAAK;EACb;EACA,OAAOW,mBAAmB,CAACjB,MAAM,EAAEJ,UAAU,EAAEC,OAAO,CAAC;AACzD;AAEA,SAASW,8BAA8BA,CACrCF,KAAkB,EACwB;EAC1C;EACA,IAAIA,KAAK,CAACM,GAAG,EAAE;IACb,OAAON,KAAK,CAACM,GAAG;EAClB;;EAEA;EACA,IAAI,YAAY,IAAIN,KAAK,IAAI,cAAc,IAAIA,KAAK,EAAE;IACpD,OAAO;MAAEY,IAAI,EAAEZ,KAAK,CAACa,UAAU;MAAEC,MAAM,EAAEd,KAAK,CAACe;IAAa,CAAC;EAC/D;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA,SAASL,kCAAkCA,CAACV,KAAU,EAAiB;EACrE,IAAIgB,aAAa,CAAChB,KAAK,CAAC,EAAE;IACxB,MAAMiB,MAAM,GAAGjB,KAAK,CAACkB,KAAK,EAAEC,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAER,IAAI,IAAK,CAACA,IAAI,CAACS,UAAU,CAAC,SAAS,CAAC,CAAC;IACrF,IAAI,CAACJ,MAAM,EAAE,OAAO,IAAI;;IAExB;IACA,IAAIA,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,CAACD,UAAU,CAAC,cAAc,CAAC,EAAE;MACxDJ,MAAM,CAACM,GAAG,CAAC,CAAC;IACd;IACA,OAAON,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EACA,OAAO,IAAI;AACb;AAEA,SAASR,aAAaA,CAAChB,KAAU,EAAwB;EACvD,OAAOA,KAAK,YAAYyB,WAAW,IAAIzB,KAAK,CAAC0B,WAAW,CAACC,IAAI,KAAK,aAAa;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAShB,mBAAmBA,CACjCjB,MAAW,EACXJ,UAAkB,EAClBC,OAA6B,EAC7B;EACA;EACA;EACA,MAAMqC,mBAAmB,GAAGC,kCAAmB;EAC/C,IAAItC,OAAO,EAAEuC,MAAM,EAAE;IACnB;IACAvC,OAAO,CAACuC,MAAM,CAACF,mBAAmB,CAAC,GAAG,IAAI;EAC5C;EACA,IAAIlC,MAAM,CAACN,OAAO,IAAI,IAAI,EAAE;IAC1BM,MAAM,GAAGA,MAAM,CAACN,OAAO;EACzB;EACA,MAAM2C,kBAAkB,GAAG,OAAOrC,MAAM;EACxC,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChCA,MAAM,GAAGA,MAAM,CAACH,OAAO,CAAC;EAC1B;EAEA,IAAIG,MAAM,YAAYsC,OAAO,EAAE;IAC7B,MAAM,KAAIC,qBAAW,EAAC,eAAe3C,UAAU,2BAA2B,EAAE,gBAAgB,CAAC;EAC/F;;EAEA;EACA;EACA;EACA;EACA,MAAM4C,yBAAyB;EAC7B;EACA3C,OAAO,EAAEuC,MAAM,GAAGF,mBAAmB,CAAC,IAAI,CAAClC,MAAM,GAAGkC,mBAAmB,CAAC;EAC1E,IAAIlC,MAAM,EAAE;IACV,OAAOA,MAAM,CAACyC,oBAAoB;EACpC;;EAEA;EACA,IAAIzC,MAAM,EAAE0C,IAAI,EAAE;IAChB1C,MAAM,GAAG,IAAA2C,kCAAqB,EAAC3C,MAAM,CAAC0C,IAAI,CAAC;EAC7C,CAAC,MAAM;IACL1C,MAAM,GAAG,IAAA2C,kCAAqB,EAAC3C,MAAM,CAAC;EACxC;EAEA,OAAO;IAAEoC,MAAM,EAAEpC,MAAM;IAAEqC,kBAAkB;IAAEG;EAA0B,CAAC;AAC1E", "ignoreList": []}