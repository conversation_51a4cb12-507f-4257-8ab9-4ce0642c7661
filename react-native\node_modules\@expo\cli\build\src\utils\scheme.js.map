{"version": 3, "sources": ["../../../src/utils/scheme.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport { AndroidConfig, IOSConfig } from '@expo/config-plugins';\nimport { getInfoPlistPathFromPbxproj } from '@expo/config-plugins/build/ios/utils/getInfoPlistPath';\nimport plist from '@expo/plist';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { intersecting } from './array';\nimport * as Log from '../log';\nimport {\n  hasRequiredAndroidFilesAsync,\n  hasRequiredIOSFilesAsync,\n} from '../prebuild/clearNativeFolder';\n\nconst debug = require('debug')('expo:utils:scheme') as typeof console.log;\n\n// sort longest to ensure uniqueness.\n// this might be undesirable as it causes the QR code to be longer.\nfunction sortLongest(obj: string[]): string[] {\n  return obj.sort((a, b) => b.length - a.length);\n}\n\n/**\n * Resolve the scheme for the dev client using two methods:\n *   - filter on known Expo schemes, starting with `exp+`, avoiding 3rd party schemes.\n *   - filter on longest to ensure uniqueness.\n */\nfunction resolveExpoOrLongestScheme(schemes: string[]): string[] {\n  const expoOnlySchemes = schemes.filter((scheme) => scheme.startsWith('exp+'));\n  return expoOnlySchemes.length > 0 ? sortLongest(expoOnlySchemes) : sortLongest(schemes);\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getSchemesForIosAsync(projectRoot: string): Promise<string[]> {\n  try {\n    const infoPlistBuildProperty = getInfoPlistPathFromPbxproj(projectRoot);\n    debug(`ios application Info.plist path:`, infoPlistBuildProperty);\n    if (infoPlistBuildProperty) {\n      const configPath = path.join(projectRoot, 'ios', infoPlistBuildProperty);\n      const rawPlist = fs.readFileSync(configPath, 'utf8');\n      const plistObject = plist.parse(rawPlist);\n      const schemes = IOSConfig.Scheme.getSchemesFromPlist(plistObject);\n      debug(`ios application schemes:`, schemes);\n      return resolveExpoOrLongestScheme(schemes);\n    }\n  } catch (error) {\n    debug(`expected error collecting ios application schemes for the main target:`, error);\n  }\n  // No ios folder or some other error\n  return [];\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getSchemesForAndroidAsync(projectRoot: string): Promise<string[]> {\n  try {\n    const configPath = await AndroidConfig.Paths.getAndroidManifestAsync(projectRoot);\n    const manifest = await AndroidConfig.Manifest.readAndroidManifestAsync(configPath);\n    const schemes = await AndroidConfig.Scheme.getSchemesFromManifest(manifest);\n    debug(`android application schemes:`, schemes);\n    return resolveExpoOrLongestScheme(schemes);\n  } catch (error) {\n    debug(`expected error collecting android application schemes for the main activity:`, error);\n    // No android folder or some other error\n    return [];\n  }\n}\n\n// TODO: Revisit and test after run code is merged.\nasync function getManagedDevClientSchemeAsync(projectRoot: string): Promise<string | null> {\n  const { exp } = getConfig(projectRoot);\n  try {\n    const getDefaultScheme = require(resolveFrom(projectRoot, 'expo-dev-client/getDefaultScheme'));\n    const scheme = getDefaultScheme(exp);\n    return scheme;\n  } catch {\n    Log.warn(\n      '\\nDevelopment build: Unable to determine the default URI scheme for deep linking into the app. Ensure that the expo-dev-client package is installed.'\n    );\n    return null;\n  }\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getOptionalDevClientSchemeAsync(\n  projectRoot: string\n): Promise<{ scheme: string | null; resolution: 'config' | 'shared' | 'android' | 'ios' }> {\n  const [hasIos, hasAndroid] = await Promise.all([\n    hasRequiredIOSFilesAsync(projectRoot),\n    hasRequiredAndroidFilesAsync(projectRoot),\n  ]);\n\n  const [ios, android] = await Promise.all([\n    getSchemesForIosAsync(projectRoot),\n    getSchemesForAndroidAsync(projectRoot),\n  ]);\n\n  // Allow managed projects\n  if (!hasIos && !hasAndroid) {\n    return { scheme: await getManagedDevClientSchemeAsync(projectRoot), resolution: 'config' };\n  }\n\n  // Allow for only one native project to exist.\n  if (!hasIos) {\n    return { scheme: android[0], resolution: 'android' };\n  } else if (!hasAndroid) {\n    return { scheme: ios[0], resolution: 'ios' };\n  } else {\n    return { scheme: intersecting(ios, android)[0], resolution: 'shared' };\n  }\n}\n"], "names": ["getOptionalDevClientSchemeAsync", "getSchemesForAndroidAsync", "getSchemesForIosAsync", "debug", "require", "sortLongest", "obj", "sort", "a", "b", "length", "resolveExpoOrLongestScheme", "schemes", "expoOnlySchemes", "filter", "scheme", "startsWith", "projectRoot", "infoPlistBuildProperty", "getInfoPlistPathFromPbxproj", "config<PERSON><PERSON>", "path", "join", "rawPlist", "fs", "readFileSync", "plistObject", "plist", "parse", "IOSConfig", "Scheme", "getSchemesFromPlist", "error", "AndroidConfig", "Paths", "getAndroidManifestAsync", "manifest", "Manifest", "readAndroidManifestAsync", "getSchemesFromManifest", "getManagedDevClientSchemeAsync", "exp", "getConfig", "getDefaultScheme", "resolveFrom", "Log", "warn", "hasIos", "hasAndroid", "Promise", "all", "hasRequiredIOSFilesAsync", "hasRequiredAndroidFilesAsync", "ios", "android", "resolution", "intersecting"], "mappings": ";;;;;;;;;;;IAoFsBA,+BAA+B;eAA/BA;;IA9BAC,yBAAyB;eAAzBA;;IApBAC,qBAAqB;eAArBA;;;;yBAlCI;;;;;;;yBACe;;;;;;;yBACG;;;;;;;gEAC1B;;;;;;;gEACH;;;;;;;gEACE;;;;;;;gEACO;;;;;;uBAEK;6DACR;mCAId;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,qCAAqC;AACrC,mEAAmE;AACnE,SAASC,YAAYC,GAAa;IAChC,OAAOA,IAAIC,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEC,MAAM,GAAGF,EAAEE,MAAM;AAC/C;AAEA;;;;CAIC,GACD,SAASC,2BAA2BC,OAAiB;IACnD,MAAMC,kBAAkBD,QAAQE,MAAM,CAAC,CAACC,SAAWA,OAAOC,UAAU,CAAC;IACrE,OAAOH,gBAAgBH,MAAM,GAAG,IAAIL,YAAYQ,mBAAmBR,YAAYO;AACjF;AAGO,eAAeV,sBAAsBe,WAAmB;IAC7D,IAAI;QACF,MAAMC,yBAAyBC,IAAAA,+CAA2B,EAACF;QAC3Dd,MAAM,CAAC,gCAAgC,CAAC,EAAEe;QAC1C,IAAIA,wBAAwB;YAC1B,MAAME,aAAaC,eAAI,CAACC,IAAI,CAACL,aAAa,OAAOC;YACjD,MAAMK,WAAWC,aAAE,CAACC,YAAY,CAACL,YAAY;YAC7C,MAAMM,cAAcC,gBAAK,CAACC,KAAK,CAACL;YAChC,MAAMX,UAAUiB,0BAAS,CAACC,MAAM,CAACC,mBAAmB,CAACL;YACrDvB,MAAM,CAAC,wBAAwB,CAAC,EAAES;YAClC,OAAOD,2BAA2BC;QACpC;IACF,EAAE,OAAOoB,OAAO;QACd7B,MAAM,CAAC,sEAAsE,CAAC,EAAE6B;IAClF;IACA,oCAAoC;IACpC,OAAO,EAAE;AACX;AAGO,eAAe/B,0BAA0BgB,WAAmB;IACjE,IAAI;QACF,MAAMG,aAAa,MAAMa,8BAAa,CAACC,KAAK,CAACC,uBAAuB,CAAClB;QACrE,MAAMmB,WAAW,MAAMH,8BAAa,CAACI,QAAQ,CAACC,wBAAwB,CAAClB;QACvE,MAAMR,UAAU,MAAMqB,8BAAa,CAACH,MAAM,CAACS,sBAAsB,CAACH;QAClEjC,MAAM,CAAC,4BAA4B,CAAC,EAAES;QACtC,OAAOD,2BAA2BC;IACpC,EAAE,OAAOoB,OAAO;QACd7B,MAAM,CAAC,4EAA4E,CAAC,EAAE6B;QACtF,wCAAwC;QACxC,OAAO,EAAE;IACX;AACF;AAEA,mDAAmD;AACnD,eAAeQ,+BAA+BvB,WAAmB;IAC/D,MAAM,EAAEwB,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACzB;IAC1B,IAAI;QACF,MAAM0B,mBAAmBvC,QAAQwC,IAAAA,sBAAW,EAAC3B,aAAa;QAC1D,MAAMF,SAAS4B,iBAAiBF;QAChC,OAAO1B;IACT,EAAE,OAAM;QACN8B,KAAIC,IAAI,CACN;QAEF,OAAO;IACT;AACF;AAGO,eAAe9C,gCACpBiB,WAAmB;IAEnB,MAAM,CAAC8B,QAAQC,WAAW,GAAG,MAAMC,QAAQC,GAAG,CAAC;QAC7CC,IAAAA,2CAAwB,EAAClC;QACzBmC,IAAAA,+CAA4B,EAACnC;KAC9B;IAED,MAAM,CAACoC,KAAKC,QAAQ,GAAG,MAAML,QAAQC,GAAG,CAAC;QACvChD,sBAAsBe;QACtBhB,0BAA0BgB;KAC3B;IAED,yBAAyB;IACzB,IAAI,CAAC8B,UAAU,CAACC,YAAY;QAC1B,OAAO;YAAEjC,QAAQ,MAAMyB,+BAA+BvB;YAAcsC,YAAY;QAAS;IAC3F;IAEA,8CAA8C;IAC9C,IAAI,CAACR,QAAQ;QACX,OAAO;YAAEhC,QAAQuC,OAAO,CAAC,EAAE;YAAEC,YAAY;QAAU;IACrD,OAAO,IAAI,CAACP,YAAY;QACtB,OAAO;YAAEjC,QAAQsC,GAAG,CAAC,EAAE;YAAEE,YAAY;QAAM;IAC7C,OAAO;QACL,OAAO;YAAExC,QAAQyC,IAAAA,mBAAY,EAACH,KAAKC,QAAQ,CAAC,EAAE;YAAEC,YAAY;QAAS;IACvE;AACF"}