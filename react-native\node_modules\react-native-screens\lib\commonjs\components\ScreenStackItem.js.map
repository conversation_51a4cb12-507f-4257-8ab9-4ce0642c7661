{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_warnOnce", "_interopRequireDefault", "_DebugContainer", "_ScreenStackHeaderConfig", "_Screen", "_ScreenStack", "_contexts", "obj", "__esModule", "default", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "target", "arguments", "length", "source", "key", "apply", "ScreenStackItem", "_ref", "ref", "children", "headerConfig", "activityState", "stackPresentation", "contentStyle", "style", "screenId", "rest", "currentScreenRef", "useRef", "screenRefs", "useContext", "RNSScreensRefContext", "useImperativeHandle", "current", "isHeaderInModal", "Platform", "OS", "hidden", "headerHiddenPreviousRef", "useEffect", "warnOnce", "content", "createElement", "Fragment", "styles", "absolute", "container", "ScreenStackHeaderConfig", "internalScreenStyle", "flattenContentStyles", "StyleSheet", "flatten", "backgroundColor", "node", "console", "warn", "currentRefs", "enabled", "isNativeStack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "largeTitle", "absoluteFill", "_default", "exports", "forwardRef", "create", "flex", "position", "top", "start", "end"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackItem.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,eAAA,GAAAD,sBAAA,CAAAH,OAAA;AAEA,IAAAK,wBAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,YAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;AAAmD,SAAAG,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAd,wBAAAc,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAH,UAAA,SAAAG,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAF,OAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAJ,CAAA,UAAAG,CAAA,CAAAE,GAAA,CAAAL,CAAA,OAAAM,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAY,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAN,CAAA,CAAAR,OAAA,GAAAE,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAc,GAAA,CAAAjB,CAAA,EAAAM,CAAA,GAAAA,CAAA;AAAA,SAAAY,SAAA,IAAAA,QAAA,GAAAT,MAAA,CAAAU,MAAA,GAAAV,MAAA,CAAAU,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,MAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,GAAA,IAAAD,MAAA,QAAAf,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAS,MAAA,EAAAC,GAAA,KAAAJ,MAAA,CAAAI,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAJ,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAJ,SAAA;AAWnD,SAASK,eAAeA,CAAAC,IAAA,EAWtBC,GAA6B,EAC7B;EAAA,IAXA;IACEC,QAAQ;IACRC,YAAY;IACZC,aAAa;IACbC,iBAAiB;IACjBC,YAAY;IACZC,KAAK;IACLC,QAAQ;IACR,GAAGC;EACE,CAAC,GAAAT,IAAA;EAGR,MAAMU,gBAAgB,GAAGrD,KAAK,CAACsD,MAAM,CAAc,IAAI,CAAC;EACxD,MAAMC,UAAU,GAAGvD,KAAK,CAACwD,UAAU,CAACC,8BAAoB,CAAC;EAEzDzD,KAAK,CAAC0D,mBAAmB,CAACd,GAAG,EAAE,MAAMS,gBAAgB,CAACM,OAAQ,CAAC;EAE/D,MAAMC,eAAe,GACnBC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,KAAK,GACLd,iBAAiB,KAAK,MAAM,IAAIF,YAAY,EAAEiB,MAAM,KAAK,KAAK;EAEpE,MAAMC,uBAAuB,GAAGhE,KAAK,CAACsD,MAAM,CAACR,YAAY,EAAEiB,MAAM,CAAC;EAElE/D,KAAK,CAACiE,SAAS,CAAC,MAAM;IACpB,IAAAC,iBAAQ,EACNL,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACvBd,iBAAiB,KAAK,MAAM,IAC5BgB,uBAAuB,CAACL,OAAO,KAAKb,YAAY,EAAEiB,MAAM,EACzD,qHACH,CAAC;IAEDC,uBAAuB,CAACL,OAAO,GAAGb,YAAY,EAAEiB,MAAM;EACxD,CAAC,EAAE,CAACjB,YAAY,EAAEiB,MAAM,EAAEf,iBAAiB,CAAC,CAAC;EAE7C,MAAMmB,OAAO,gBACXnE,KAAA,CAAAoE,aAAA,CAAApE,KAAA,CAAAqE,QAAA,qBACErE,KAAA,CAAAoE,aAAA,CAAC9D,eAAA,CAAAO,OAAc;IACbqC,KAAK,EAAE,CACLF,iBAAiB,KAAK,WAAW,GAC7Ba,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACnBQ,MAAM,CAACC,QAAQ,GACf,IAAI,GACND,MAAM,CAACE,SAAS,EACpBvB,YAAY,CACZ;IACFD,iBAAiB,EAAEA,iBAAiB,IAAI;EAAO,GAC9CH,QACa,CAAC,eAYjB7C,KAAA,CAAAoE,aAAA,CAAC7D,wBAAA,CAAAkE,uBAAuB,EAAK3B,YAAe,CAC5C,CACH;;EAED;EACA;EACA;EACA,IAAI4B,mBAAmB;EAEvB,IAAI1B,iBAAiB,KAAK,WAAW,IAAIC,YAAY,EAAE;IACrD,MAAM0B,oBAAoB,GAAGC,uBAAU,CAACC,OAAO,CAAC5B,YAAY,CAAC;IAC7DyB,mBAAmB,GAAG;MACpBI,eAAe,EAAEH,oBAAoB,EAAEG;IACzC,CAAC;EACH;EAEA,oBACE9E,KAAA,CAAAoE,aAAA,CAAC5D,OAAA,CAAAK,OAAM,EAAAoB,QAAA;IACLW,GAAG,EAAEmC,IAAI,IAAI;MACX1B,gBAAgB,CAACM,OAAO,GAAGoB,IAAI;MAE/B,IAAIxB,UAAU,KAAK,IAAI,EAAE;QACvByB,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;QACD;MACF;MAEA,MAAMC,WAAW,GAAG3B,UAAU,CAACI,OAAO;MAEtC,IAAIoB,IAAI,KAAK,IAAI,EAAE;QACjB;QACA,OAAOG,WAAW,CAAC/B,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACL+B,WAAW,CAAC/B,QAAQ,CAAC,GAAG;UAAEQ,OAAO,EAAEoB;QAAK,CAAC;MAC3C;IACF,CAAE;IACFI,OAAO;IACPC,aAAa;IACbrC,aAAa,EAAEA,aAAc;IAC7BC,iBAAiB,EAAEA,iBAAkB;IACrCqC,cAAc,EAAEvC,YAAY,EAAEwC,UAAU,IAAI,KAAM;IAClDpC,KAAK,EAAE,CAACA,KAAK,EAAEwB,mBAAmB;EAAE,GAChCtB,IAAI,GACPQ,eAAe,gBACd5D,KAAA,CAAAoE,aAAA,CAAC3D,YAAA,CAAAI,OAAW;IAACqC,KAAK,EAAEoB,MAAM,CAACE;EAAU,gBACnCxE,KAAA,CAAAoE,aAAA,CAAC5D,OAAA,CAAAK,OAAM;IACLsE,OAAO;IACPC,aAAa;IACbrC,aAAa,EAAEA,aAAc;IAC7BsC,cAAc,EAAEvC,YAAY,EAAEwC,UAAU,IAAI,KAAM;IAClDpC,KAAK,EAAE0B,uBAAU,CAACW;EAAa,GAC9BpB,OACK,CACG,CAAC,GAEdA,OAEI,CAAC;AAEb;AAAC,IAAAqB,QAAA,GAAAC,OAAA,CAAA5E,OAAA,gBAEcb,KAAK,CAAC0F,UAAU,CAAChD,eAAe,CAAC;AAEhD,MAAM4B,MAAM,GAAGM,uBAAU,CAACe,MAAM,CAAC;EAC/BnB,SAAS,EAAE;IACToB,IAAI,EAAE;EACR,CAAC;EACDrB,QAAQ,EAAE;IACRsB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC"}