{"name": "@expo/metro-config", "version": "0.20.17", "description": "A Metro config for running React Native projects with the Metro bundler", "main": "build/ExpoMetroConfig.js", "types": "build/ExpoMetroConfig.d.ts", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/metro-config"}, "keywords": ["expo", "metro"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-config#readme", "files": ["build", "file-store", "babel-transformer"], "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "@expo/config": "~11.0.12", "@expo/env": "~1.0.7", "@expo/json-file": "~9.1.5", "@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "debug": "^4.3.2", "getenv": "^2.0.0", "glob": "^10.4.2", "jsc-safe-url": "^0.2.4", "lightningcss": "~1.27.0", "minimatch": "^9.0.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0"}, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20", "dedent": "^1.5.3", "expo-module-scripts": "^4.1.9", "sass": "^1.60.0"}, "publishConfig": {"access": "public"}, "gitHead": "1c4a89b0c0adebb53ef84b4a6ac25864e4652917"}