import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { API_CONFIG } from '../config/constants';

const API_BASE_URL = API_CONFIG.BASE_URL;

const TechnicianDashboard = ({ navigation, route }) => {
  const [user, setUser] = useState(route.params?.user || {});
  const [stats, setStats] = useState({
    totalClients: 0,
    totalConsommations: 0,
    totalFactures: 0,
    facturesEnAttente: 0,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    setLoading(true);
    try {
      // Charger les statistiques depuis l'API
      const [clientsRes, consommationsRes, facturesRes] = await Promise.all([
        fetch(`${API_BASE_URL}/api/clients`),
        fetch(`${API_BASE_URL}/api/consommations`),
        fetch(`${API_BASE_URL}/api/factures`),
      ]);

      const [clientsData, consommationsData, facturesData] = await Promise.all([
        clientsRes.json(),
        consommationsRes.json(),
        facturesRes.json(),
      ]);

      setStats({
        totalClients: clientsData.success ? clientsData.data?.length || 0 : 0,
        totalConsommations: consommationsData.success ? consommationsData.count : 0,
        totalFactures: facturesData.success ? facturesData.count : 0,
        facturesEnAttente: facturesData.success 
          ? facturesData.data.filter(f => f.status === 'nonpayée').length 
          : 0,
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Déconnexion', 
          style: 'destructive',
          onPress: () => navigation.replace('Login')
        },
      ]
    );
  };

  const menuItems = [
    {
      title: 'Liste des Clients',
      subtitle: `${stats.totalClients} clients enregistrés`,
      icon: 'people-outline',
      color: '#4CAF50',
      onPress: () => navigation.navigate('ClientsList'),
    },
    {
      title: 'Saisie Consommation',
      subtitle: 'Enregistrer les relevés',
      icon: 'water-outline',
      color: '#2196F3',
      onPress: () => navigation.navigate('Consommation'),
    },
    {
      title: 'Factures',
      subtitle: `${stats.facturesEnAttente} en attente`,
      icon: 'receipt-outline',
      color: '#FF9800',
      onPress: () => navigation.navigate('Factures'),
    },
    {
      title: 'Scanner QR Code',
      subtitle: 'Identifier un compteur',
      icon: 'qr-code-outline',
      color: '#9C27B0',
      onPress: () => navigation.navigate('Scanner'),
    },
    {
      title: 'Localisation',
      subtitle: 'Voir sur la carte',
      icon: 'location-outline',
      color: '#F44336',
      onPress: () => navigation.navigate('Map'),
    },
    {
      title: 'Profil',
      subtitle: 'Gérer mon compte',
      icon: 'person-outline',
      color: '#607D8B',
      onPress: () => navigation.navigate('Profile'),
    },
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.welcomeText}>Bonjour,</Text>
            <Text style={styles.userName}>{user.prenom} {user.nom}</Text>
            <Text style={styles.userRole}>Technicien</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Statistiques */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalClients}</Text>
          <Text style={styles.statLabel}>Clients</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalConsommations}</Text>
          <Text style={styles.statLabel}>Relevés</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalFactures}</Text>
          <Text style={styles.statLabel}>Factures</Text>
        </View>
      </View>

      {/* Menu principal */}
      <ScrollView 
        style={styles.menuContainer}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={loadDashboardStats} />
        }
      >
        <Text style={styles.menuTitle}>Menu Principal</Text>
        
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={item.onPress}
          >
            <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
              <Ionicons name={item.icon} size={24} color="#fff" />
            </View>
            <View style={styles.menuContent}>
              <Text style={styles.menuItemTitle}>{item.title}</Text>
              <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
            </View>
            <Ionicons name="chevron-forward-outline" size={20} color="#ccc" />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    color: '#fff',
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  userRole: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
  },
  logoutButton: {
    padding: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: -10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  menuContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  menuTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  menuIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  menuContent: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});

export default TechnicianDashboard;
