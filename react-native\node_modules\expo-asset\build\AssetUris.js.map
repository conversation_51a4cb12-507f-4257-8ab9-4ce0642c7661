{"version": 3, "file": "AssetUris.js", "sourceRoot": "", "sources": ["../src/AssetUris.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAE7D,iGAAiG;IACjG,qDAAqD;IACrD,uHAAuH;IACvH,QAAQ;IACR,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;YACtC,MAAM,eAAe,GAAG,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAE,CAAC,CAAC;YAC/E,OAAO,WAAW,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,WAAW,CAAC,QAAgB;IACnC,OAAO,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,GAAW;IAC1C,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC3C,uCAAuC;IACvC,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1D,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,kBAAkB,CAAC,WAAmB;IACpD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;IAEvC,IAAI,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC;IACtC,+CAA+C;IAC/C,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;QAC5B,YAAY,GAAG,OAAO,CAAC;IACzB,CAAC;SAAM,IAAI,YAAY,KAAK,OAAO,EAAE,CAAC;QACpC,YAAY,GAAG,QAAQ,CAAC;IAC1B,CAAC;IACD,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC;IAElC,wDAAwD;IACxD,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3F,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC/B,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;IACtB,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC;IAEpB,4EAA4E;IAC5E,wEAAwE;IACxE,OAAO,SAAS,CAAC,QAAQ,KAAK,YAAY;QACxC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,YAAY,CAAC;QAC1D,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;AACrB,CAAC", "sourcesContent": ["export function getFilename(url: string): string {\n  const { pathname, searchParams } = new URL(url, 'https://e');\n\n  // When attached to a dev server, we use `unstable_path` to represent the file path. This ensures\n  // the file name is not canonicalized by the browser.\n  // NOTE(EvanBacon): This is technically not tied to `__DEV__` as it's possible to use this while bundling in production\n  // mode.\n  if (__DEV__) {\n    if (searchParams.has('unstable_path')) {\n      const encodedFilePath = decodeURIComponent(searchParams.get('unstable_path')!);\n      return getBasename(encodedFilePath);\n    }\n  }\n\n  return getBasename(pathname);\n}\n\nfunction getBasename(pathname: string): string {\n  return pathname.substring(pathname.lastIndexOf('/') + 1);\n}\n\nexport function getFileExtension(url: string): string {\n  const filename = getFilename(url);\n  const dotIndex = filename.lastIndexOf('.');\n  // Ignore leading dots for hidden files\n  return dotIndex > 0 ? filename.substring(dotIndex) : '';\n}\n\n/**\n * Returns the base URL from a manifest's URL. For example, given a manifest hosted at\n * https://example.com/app/manifest.json, the base URL would be https://example.com/app/. Query\n * parameters and fragments also are removed.\n *\n * For an Expo-hosted project with a manifest hosted at https://exp.host/@user/project/index.exp, the\n * base URL would be https://exp.host/@user/project.\n *\n * We also normalize the \"exp\" protocol to \"http\" to handle internal URLs with the Expo schemes used\n * to tell the OS to open the URLs in the the Expo client.\n */\nexport function getManifestBaseUrl(manifestUrl: string): string {\n  const urlObject = new URL(manifestUrl);\n\n  let nextProtocol = urlObject.protocol;\n  // Change the scheme to http(s) if it is exp(s)\n  if (nextProtocol === 'exp:') {\n    nextProtocol = 'http:';\n  } else if (nextProtocol === 'exps:') {\n    nextProtocol = 'https:';\n  }\n  urlObject.protocol = nextProtocol;\n\n  // Trim filename, query parameters, and fragment, if any\n  const directory = urlObject.pathname.substring(0, urlObject.pathname.lastIndexOf('/') + 1);\n  urlObject.pathname = directory;\n  urlObject.search = '';\n  urlObject.hash = '';\n\n  // The URL spec doesn't allow for changing the protocol to `http` or `https`\n  // without a port set so instead, we'll just swap the protocol manually.\n  return urlObject.protocol !== nextProtocol\n    ? urlObject.href.replace(urlObject.protocol, nextProtocol)\n    : urlObject.href;\n}\n"]}