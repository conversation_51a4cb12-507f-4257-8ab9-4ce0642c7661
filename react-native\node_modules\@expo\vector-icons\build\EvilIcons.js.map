{"version": 3, "file": "EvilIcons.js", "sourceRoot": "", "sources": ["../src/EvilIcons.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,wDAAwD,CAAC;AAC1E,OAAO,QAAQ,MAAM,6DAA6D,CAAC;AAEnF,eAAe,aAAa,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/EvilIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/EvilIcons.json';\n\nexport default createIconSet(glyphMap, 'evilicons', font);\n"]}