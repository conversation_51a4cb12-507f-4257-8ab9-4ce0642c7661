{"version": 3, "sources": ["ActionType.ts"], "names": ["ActionType", "REANIMATED_WORKLET", "NATIVE_ANIMATED_EVENT", "JS_FUNCTION_OLD_API", "JS_FUNCTION_NEW_API"], "mappings": ";;;;;;AAAO,MAAMA,UAAU,GAAG;AACxBC,EAAAA,kBAAkB,EAAE,CADI;AAExBC,EAAAA,qBAAqB,EAAE,CAFC;AAGxBC,EAAAA,mBAAmB,EAAE,CAHG;AAIxBC,EAAAA,mBAAmB,EAAE;AAJG,CAAnB,C,CAOP", "sourcesContent": ["export const ActionType = {\n  REANIMATED_WORKLET: 1,\n  NATIVE_ANIMATED_EVENT: 2,\n  JS_FUNCTION_OLD_API: 3,\n  JS_FUNCTION_NEW_API: 4,\n} as const;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\nexport type ActionType = typeof ActionType[keyof typeof ActionType];\n"]}