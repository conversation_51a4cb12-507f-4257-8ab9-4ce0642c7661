import*as i from"../../core/i18n/i18n.js";import*as e from"../../ui/legacy/legacy.js";let s;const t={accessibility:"Accessibility",shoAccessibility:"Show Accessibility"},c=i.i18n.registerUIStrings("panels/accessibility/accessibility-meta.ts",t),a=i.i18n.getLazilyComputedLocalizedString.bind(void 0,c);e.ViewManager.registerViewExtension({location:"elements-sidebar",id:"accessibility.view",title:a(t.accessibility),commandPrompt:a(t.shoAccessibility),order:10,persistence:"permanent",loadView:async()=>(await async function(){return s||(s=await import("./accessibility.js")),s}()).AccessibilitySidebarView.AccessibilitySidebarView.instance()});
