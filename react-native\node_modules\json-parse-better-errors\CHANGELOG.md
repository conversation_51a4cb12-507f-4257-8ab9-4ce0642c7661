# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="1.0.2"></a>
## [1.0.2](https://github.com/zkat/json-parse-better-errors/compare/v1.0.1...v1.0.2) (2018-03-30)


### Bug Fixes

* **messages:** More friendly messages for non-string ([#1](https://github.com/zkat/json-parse-better-errors/issues/1)) ([a476d42](https://github.com/zkat/json-parse-better-errors/commit/a476d42))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/zkat/json-parse-better-errors/compare/v1.0.0...v1.0.1) (2017-08-16)


### Bug Fixes

* **license:** oops. Forgot to update license.md ([efe2958](https://github.com/zkat/json-parse-better-errors/commit/efe2958))



<a name="1.0.0"></a>
# 1.0.0 (2017-08-15)


### Features

* **init:** Initial Commit ([562c977](https://github.com/zkat/json-parse-better-errors/commit/562c977))


### BREAKING CHANGES

* **init:** This is the first commit!



<a name="0.1.0"></a>
# 0.1.0 (2017-08-15)


### Features

* **init:** Initial Commit ([9dd1a19](https://github.com/zkat/json-parse-better-errors/commit/9dd1a19))
