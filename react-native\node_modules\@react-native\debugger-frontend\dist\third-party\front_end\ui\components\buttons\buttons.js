import*as e from"../../lit-html/lit-html.js";import*as t from"../../visual_logging/visual_logging.js";import*as o from"../helpers/helpers.js";import*as r from"../icon_button/icon_button.js";const s=new CSSStyleSheet;s.replaceSync('*{margin:0;padding:0;box-sizing:border-box}*:focus,\n*:focus-visible,\n:host(:focus),\n:host(:focus-visible){outline:none}:host{display:inline-flex;flex-direction:row;align-items:center}button{--hover-layer-color:var(--sys-color-state-hover-on-subtle);--active-layer-color:var(--sys-color-state-ripple-neutral-on-subtle);--button-border-size:1px;--button-height:var(--sys-size-11);--button-width:fit-content;align-items:center;background:transparent;border-radius:var(--sys-shape-corner-full);display:inline-flex;position:relative;font-size:var(--sys-typescale-body4-size);font-weight:var(--ref-typeface-weight-medium);line-height:var(--sys-typescale-body4-line-height);height:var(--button-height);justify-content:center;padding:0 var(--sys-size-6);white-space:nowrap;width:var(--button-width);&.primary-toggle{--toggle-color:var(--sys-color-primary-bright)}&.red-toggle{--toggle-color:var(--sys-color-error-bright)}devtools-icon{width:var(--icon-size);height:var(--icon-size)}&.toolbar,\n  &.icon,\n  &.only-icon{--button-height:26px;--button-width:26px;--icon-size:var(--sys-size-9);padding:0;border:none;overflow:hidden;&.small{--button-height:var(--sys-size-9);--button-width:var(--sys-size-9);--icon-size:var(--sys-size-8)}&.toggled devtools-icon{color:var(--toggle-color)}}&.primary{--hover-layer-color:var(--sys-color-state-hover-on-prominent);--active-layer-color:var(--sys-color-state-ripple-primary);border:var(--button-border-size) solid var(--sys-color-primary);background:var(--sys-color-primary);color:var(--sys-color-on-primary);devtools-icon{color:var(--sys-color-on-primary)}}&.tonal{border:none;background:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container);devtools-icon{color:var(--sys-color-on-tonal-container)}}&.primary-toolbar{devtools-icon{color:var(--icon-primary)}}&.text{border:none;color:var(--sys-color-primary);devtools-icon{color:var(--icon-primary)}}&.text-with-icon{padding-left:var(--sys-size-4);devtools-icon{width:var(--sys-size-9);height:var(--sys-size-9);margin-right:var(--sys-size-2)}}&.outlined{border:var(--button-border-size) solid var(--sys-color-tonal-outline);background:transparent;color:var(--sys-color-primary);devtools-icon{color:var(--icon-primary)}&.micro{--button-height:var(--sys-size-7);padding:0 var(--sys-size-5);font-size:var(--sys-typescale-body4-size);font-weight:var(--ref-typeface-weight-medium);line-height:var(--sys-typescale-body4-line-height);&:not(.icon):has(devtools-icon){padding-left:var(--sys-size-3)}&:not(.icon) devtools-icon{height:var(--sys-size-6);width:var(--sys-size-6)}}}&:disabled{pointer-events:none;color:var(--sys-color-state-disabled);&.primary{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container)}&.tonal{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container)}&.outlined{border:var(--button-border-size) solid var(--sys-color-state-disabled-container)}&.toolbar,\n    &.icon{background:transparent}devtools-icon{color:var(--icon-disabled)}}&:not(.icon):not(.toolbar).only-icon{width:100%;padding:0;&.small{width:var(--button-height)}}&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;z-index:999;&.toolbar,\n    &.icon{outline-offset:-1px;&.small{outline-offset:-2px}}}&:has(.spinner){padding-left:var(--sys-size-4)}&:hover::after{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0;background-color:var(--hover-layer-color);&.primary{border:var(--button-border-size) solid color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%)}&.tonal{background:color-mix(in sRGB,var(--sys-color-tonal-container),var(--sys-color-state-hover-on-subtle))}&.toobar{devtools-icon{color:var(--icon-default-hover)}}}&:active::before,\n  &.active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0;background-color:var(--active-layer-color);&.primary{border:var(--button-border-size) solid color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%)}&.tonal{background:color-mix(in sRGB,var(--sys-color-tonal-container),var(--sys-color-state-ripple-primary) 50%)}&.toolbar{devtools-icon{color:var(--icon-toggled)}}}}.spinner{display:block;width:12px;height:12px;border-radius:6px;border:2px solid var(--sys-color-cdt-base-container);animation:spinner-animation 1s linear infinite;border-right-color:transparent;margin-right:4px;&.outlined{border:2px solid var(--sys-color-primary);border-right-color:transparent}&.disabled{border:2px solid var(--sys-color-state-disabled);border-right-color:transparent}}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}\n/*# sourceURL=button.css */\n');class i extends HTMLElement{static formAssociated=!0;static litTagName=e.literal`devtools-button`;#e=this.attachShadow({mode:"open",delegatesFocus:!0});#t=this.#o.bind(this);#r=this.#s.bind(this);#i={size:"REGULAR",disabled:!1,active:!1,spinner:!1,type:"button"};#n=!0;#a=this.attachInternals();constructor(){super(),this.setAttribute("role","presentation"),this.addEventListener("click",this.#r,!0)}set data(e){this.#i.variant=e.variant,this.#i.iconUrl=e.iconUrl,this.#i.iconName=e.iconName,this.#i.toggledIconName=e.toggledIconName,this.#i.size="REGULAR","size"in e&&e.size&&(this.#i.size=e.size),this.#i.active=Boolean(e.active),this.#i.spinner=Boolean("spinner"in e&&e.spinner),this.#i.type="button","type"in e&&e.type&&(this.#i.type=e.type),this.#i.toggled=e.toggled,this.#i.toggleType=e.toggleType,this.#l(e.disabled||!1),this.#i.title=e.title,this.#i.jslogContext=e.jslogContext,o.ScheduledRender.scheduleRender(this,this.#t)}set iconUrl(e){this.#i.iconUrl=e,o.ScheduledRender.scheduleRender(this,this.#t)}set iconName(e){this.#i.iconName=e,o.ScheduledRender.scheduleRender(this,this.#t)}set toggledIconName(e){this.#i.toggledIconName=e}set toggleType(e){this.#i.toggleType=e}set variant(e){this.#i.variant=e,o.ScheduledRender.scheduleRender(this,this.#t)}set size(e){this.#i.size=e,o.ScheduledRender.scheduleRender(this,this.#t)}set type(e){this.#i.type=e,o.ScheduledRender.scheduleRender(this,this.#t)}set title(e){this.#i.title=e,o.ScheduledRender.scheduleRender(this,this.#t)}set disabled(e){this.#l(e),o.ScheduledRender.scheduleRender(this,this.#t)}set toggled(e){this.#i.toggled=e,o.ScheduledRender.scheduleRender(this,this.#t)}get toggled(){return Boolean(this.#i.toggled)}set active(e){this.#i.active=e,o.ScheduledRender.scheduleRender(this,this.#t)}get active(){return this.#i.active}set spinner(e){this.#i.spinner=e,o.ScheduledRender.scheduleRender(this,this.#t)}get jslogContext(){return this.#i.jslogContext}set jslogContext(e){this.#i.jslogContext=e,o.ScheduledRender.scheduleRender(this,this.#t)}#l(e){this.#i.disabled=e,this.toggleAttribute("disabled",e)}focus(){this.#e.querySelector("button")?.focus()}connectedCallback(){this.#e.adoptedStyleSheets=[s],o.ScheduledRender.scheduleRender(this,this.#t)}#s(e){if(this.#i.disabled)return e.stopPropagation(),void e.preventDefault();this.form&&"submit"===this.#i.type&&(e.preventDefault(),this.form.dispatchEvent(new SubmitEvent("submit",{submitter:this}))),this.form&&"reset"===this.#i.type&&(e.preventDefault(),this.form.reset()),"icon_toggle"===this.#i.variant&&this.#i.iconName&&(this.toggled=!this.#i.toggled)}#d(e){const t=e.target,r=t?.assignedNodes();this.#n=!r||!Boolean(r.length),o.ScheduledRender.scheduleRender(this,this.#t)}#p(){return"toolbar"===this.#i.variant||"primary_toolbar"===this.#i.variant}#o(){if(!this.#i.variant)throw new Error("Button requires a variant to be defined");if(this.#p()){if(!this.#i.iconUrl&&!this.#i.iconName)throw new Error("Toolbar button requires an icon");if(!this.#n)throw new Error("Toolbar button does not accept children")}if("icon"===this.#i.variant){if(!this.#i.iconUrl&&!this.#i.iconName)throw new Error("Icon button requires an icon");if(!this.#n)throw new Error("Icon button does not accept children")}if(this.#i.iconName&&this.#i.iconUrl)throw new Error("Both iconName and iconUrl are provided.");const o=Boolean(this.#i.iconUrl)||Boolean(this.#i.iconName),s={primary:"primary"===this.#i.variant,tonal:"tonal"===this.#i.variant,outlined:"outlined"===this.#i.variant,text:"text"===this.#i.variant,toolbar:this.#p(),"primary-toolbar":"primary_toolbar"===this.#i.variant,icon:"icon"===this.#i.variant||"icon_toggle"===this.#i.variant,"primary-toggle":"primary-toggle"===this.#i.toggleType,"red-toggle":"red-toggle"===this.#i.toggleType,toggled:Boolean(this.#i.toggled),"text-with-icon":o&&!this.#n,"only-icon":o&&this.#n,"only-text":!o&&!this.#n,micro:"MICRO"===this.#i.size,small:Boolean("SMALL"===this.#i.size),active:this.#i.active},i={primary:"primary"===this.#i.variant,outlined:"outlined"===this.#i.variant,disabled:Boolean(this.#i.disabled),spinner:!0},n=this.#i.jslogContext&&t.action().track({click:!0}).context(this.#i.jslogContext);e.render(e.html`
        <button title=${e.Directives.ifDefined(this.#i.title)} .disabled=${this.#i.disabled} class=${e.Directives.classMap(s)} jslog=${e.Directives.ifDefined(n)}>
          ${o?e.html`
                <${r.Icon.Icon.litTagName} name=${this.#i.toggled?this.#i.toggledIconName:this.#i.iconName||this.#i.iconUrl}>
                </${r.Icon.Icon.litTagName}>`:""}
          ${this.#i.spinner?e.html`<span class=${e.Directives.classMap(i)}></span>`:""}
          <slot @slotchange=${this.#d}></slot>
        </button>
      `,this.#e,{host:this})}get value(){return this.#i.value||""}set value(e){this.#i.value=e}get form(){return this.#a.form}get name(){return this.getAttribute("name")}get type(){return this.#i.type}get validity(){return this.#a.validity}get validationMessage(){return this.#a.validationMessage}get willValidate(){return this.#a.willValidate}checkValidity(){return this.#a.checkValidity()}reportValidity(){return this.#a.reportValidity()}}customElements.define("devtools-button",i);var n=Object.freeze({__proto__:null,Button:i});export{n as Button};
