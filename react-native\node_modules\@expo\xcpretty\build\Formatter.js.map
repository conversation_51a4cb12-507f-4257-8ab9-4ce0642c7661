{"version": 3, "file": "Formatter.js", "sourceRoot": "", "sources": ["../src/Formatter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAqD;AACrD,kDAA0B;AAC1B,uCAAyB;AACzB,uCAAyB;AACzB,2CAA6B;AAE7B,yCAAsC;AACtC,qCAA2C;AAC3C,+CAA4C;AAC5C,6CAUyB;AAEzB,IAAK,MAOJ;AAPD,WAAK,MAAM;IACT,uBAAa,CAAA;IACb,uBAAa,CAAA;IACb,6BAAmB,CAAA;IACnB,yBAAe,CAAA;IACf,mCAAyB,CAAA;IACzB,6BAAmB,CAAA;AACrB,CAAC,EAPI,MAAM,KAAN,MAAM,QAOV;AAED,SAAS,0BAA0B,CAAC,QAAgB;IAClD,OAAO,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC3E,CAAC;AAED,SAAS,MAAM,CAAC,OAAe,EAAE,YAAY,GAAG,EAAE,EAAE,OAAO,GAAG,IAAI;IAChE,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACvE,OAAO,CAAC,MAAM,EAAE,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACtE,CAAC;AAED,SAAS,UAAU,CAAC,QAAgB,EAAE,MAAc;IAClD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC3D,CAAC;AAED,SAAS,OAAO,CAAC,MAAc,EAAE,IAAY,EAAE,WAAmB;IAChE,OAAO,CAAC,MAAM,EAAE,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACnE,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,MAAM,CAAC,IAAI;YACd,OAAO,eAAK,CAAC,KAAK,CAAC,cAAI,CAAC,CAAC;QAC3B,KAAK,MAAM,CAAC,IAAI;YACd,OAAO,eAAK,CAAC,GAAG,CAAC,cAAI,CAAC,CAAC;QACzB,KAAK,MAAM,CAAC,OAAO;YACjB,OAAO,eAAK,CAAC,IAAI,CAAC,iBAAO,CAAC,CAAC;QAC7B,KAAK,MAAM,CAAC,KAAK;YACf,OAAO,eAAK,CAAC,GAAG,CAAC,eAAK,CAAC,CAAC;QAC1B,KAAK,MAAM,CAAC,UAAU;YACpB,OAAO,eAAK,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QACjC,KAAK,MAAM,CAAC,OAAO;YACjB,OAAO,eAAK,CAAC,OAAO,CAAC,iBAAO,CAAC,CAAC;QAChC;YACE,OAAO,EAAE,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,OAAO,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,UAAU,CAAC,MAAc;IAChC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,YAAY,CAAC,WAAmB,EAAE,QAAgB;IACzD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAgB,EAAE,MAAe,EAAE,OAAgB;IAC3E,iBAAiB;IACjB,OAAO,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;SAC1F,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,IAAI,oBAAU,GAAG,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,UAAU,CAAC,QAAgB;IAClC,IAAI,OAAO,GAAG,QAAQ,CAAC;IAEvB,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,sCAAsC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE1F,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrD,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,CAAC;AAwCD,MAAa,SAAS;IASpB,IAAI,MAAM;QACR,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,eAAM,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAMD,YACS,KAMN;QANM,UAAK,GAAL,KAAK,CAMX;QAXH,WAAM,GAAa,EAAE,CAAC;QAEtB,aAAQ,GAAa,EAAE,CAAC;IAUrB,CAAC;IAEJ,IAAI,CAAC,IAAY;QACf,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;gBAClD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAgB,CAAC,aAAqB;QAC5C,OAAO,eAAK,CAAC,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC;IACzC,CAAC;IAED,4BAA4B,CAAC,IAAoC;QAC/D,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,OAAO;gBACV,OAAO,UAAU,CAAC;YACpB,KAAK,WAAW;gBACd,OAAO,WAAW,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC;YACrB,KAAK,OAAO;gBACV,OAAO,UAAU,CAAC;YACpB;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY,CAAC,KAA6B;QACxC,OAAO,MAAM,CACX,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,EAC7C,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAC1F,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,EAAE,IAAI,EAAE,EAAE,EAAiB;QACpC,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,eAAe,GAAG,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,qBAAqB,CAAC,IAA2B;QAC/C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC;YACrB,KAAK,kBAAkB;gBACrB,OAAO,kBAAkB,CAAC;YAC5B,KAAK,IAAI;gBACP,OAAO,WAAW,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC;YACrB,KAAK,YAAY;gBACf,OAAO,cAAc,CAAC;YACxB,KAAK,sBAAsB;gBACzB,OAAO,WAAW,CAAC;YACrB,KAAK,UAAU;gBACb,OAAO,WAAW,CAAC;YACrB,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC;YACrB,KAAK,UAAU,CAAC;YAChB,KAAK,cAAc,CAAC;YACpB,KAAK,YAAY,CAAC;YAClB,KAAK,mBAAmB;gBACtB,OAAO,WAAW,CAAC;YACrB;gBACE,yBAAyB;gBACzB,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,KAAoB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAErD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,SAAS;gBACZ,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,kBAAkB;gBACrB,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,IAAI,KAAK,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7F,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,SAAS;gBACZ,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,YAAY;gBACf,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,sBAAsB;gBACzB,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,UAAU;gBACb,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF,KAAK,OAAO;gBACV,OAAO,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YACvC,KAAK,UAAU,CAAC;YAChB,KAAK,cAAc,CAAC;YACpB,KAAK,YAAY,CAAC;YAClB,KAAK,mBAAmB;gBACtB,OAAO,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACtF;gBACE,yBAAyB;gBACzB,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,SAAiB,EAAE,QAAiB;QACrD,OAAO,MAAM,CACX,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,EACnC,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAC9C,IAAI,CACL,CAAC;IACJ,CAAC;IAED,0BAA0B,CAAC,UAAkB,EAAE,MAAe,EAAE,OAAgB;QAC9E,wDAAwD;QACxD,OAAO,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,UAAU,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,gBAAgB,CAAC,IAAY;QAC3B,OAAO,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,kBAAkB,CAAC,OAAe,EAAE,IAAY;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,oBAAoB,CAAC,eAAuB,EAAE,QAAgB;QAC5D,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,uBAAuB,CAAC,QAAgB;QACtC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,eAAe,CAAC,IAAY;QAC1B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,WAAW,CAAC,IAAY;QACtB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,4BAA4B,CAC1B,IAAyB,EACzB,mBAA2B,EAC3B,QAAgB,EAChB,MAAc,EACd,MAAc,EACd,OAAe;QAEf,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAClF,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC;gBACvE,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QACD,iFAAiF;QACjF,MAAM,iBAAiB,GACrB,UAAU,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC;QAE9E,mCAAmC;QACnC,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,kBAAkB;QAClB,MAAM,KAAK,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC;QAC5D,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QAC/C,mBAAmB;QACnB,MAAM,MAAM,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,iBAAO,CAAC,CAAC,CAAC,eAAK,CAAC;QAEpD,0EAA0E;QAC1E,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAClD,MAAM,eAAe,GAAG,WAAW;YACjC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,WAAW,EAAE,iBAAiB,CAAC;YACrE,CAAC,CAAC,iBAAiB,CAAC;QAEtB,sBAAsB;QACtB,MAAM,OAAO,GAAG,IAAA,eAAK,EAAA,GAAG,MAAM,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,gBAAgB,eAAe,GAAG,CAAC;QAE9F,8BAA8B;QAC9B,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,8CAA8C;IAC9C,YAAY,CAAC,GAAW;QACtB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,qBAAqB,CACnB,IAAuB,EACvB,IAAuB,EACvB,MAAe,EACf,OAAgB;QAEhB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,yBAAyB,CACvB,IAAuB,EACvB,IAAuB,EACvB,GAAoD,EACpD,MAAe,EACf,OAAgB;QAEhB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,iBAAiB,CAAC,GAAW;QAC3B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,yBAAyB,CAAC,IAAY;QACpC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,OAAO,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,uBAAuB,CAAC,IAAY;QAClC,OAAO,MAAM,CAAC,oBAAoB,CAAC,CAAC;IACtC,CAAC;IAED,yBAAyB,CAAC,OAAe;QACvC,MAAM,OAAO,GAAG,GAAG,eAAK,CAAC,MAAM,CAAC,iBAAO,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,wCAAwC;IACxC;;;;;;;OAOG;IACH,kBAAkB,CAChB,QAAgB,EAChB,mBAA2B,EAC3B,MAAc,EACd,IAAY,EACZ,MAAc;QAEd,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAClF,MAAM,OAAO,GAAG,oBAAoB,CAAC;YACnC,OAAO,EAAE,IAAI;YACb,QAAQ;YACR,MAAM;YACN,MAAM;YACN,QAAQ,EAAE,IAAI;YACd,UAAU;YACV,YAAY;YACZ,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;SACtD,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,OAAO,GAAG,IAAA,yBAAW,EAAC,OAAO,EAAE;YACnC;gBACE,mBAAQ,CAAC,MAAM,CAAC,+BAA+B;gBAC/C,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACzB,OAAO,IAAI,CAAC,iCAAiC,CAC3C,EAAE,EACF,EAAE,EACF,EAAkC,EAClC,EAAE,EACF,EAAE,CACH,CAAC;gBACJ,CAAC;aACF;YACD,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,iCAAiC,CAC/B,WAAmB,EACnB,eAAuB,EACvB,eAA6C,EAC7C,MAAe,EACf,OAAgB;QAEhB,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QAEnD,IAAI,eAAe,KAAK,YAAY,EAAE,CAAC;YACrC,OAAO,IAAA,eAAK,EAAA,GAAG,eAAK,IAAI,QAAQ,yBAAyB,WAAW,8BAA8B,eAAe,eAAe,CAAC;QACnI,CAAC;QACD,OAAO,IAAA,eAAK,EAAA,GAAG,eAAK,IAAI,QAAQ,8CAA8C,eAAe,0CAA0C,WAAW,GAAG,CAAC;IACxJ,CAAC;IAED,sBAAsB,CAAC,MAAc,EAAE,QAAgB;QACrD,MAAM,OAAO,GAAG,KAAK,eAAK,CAAC,GAAG,CAAC,eAAK,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,QAAQ,MAAM,CAAC;QACvE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,eAAe,CAAC,MAAc;QAC5B,MAAM,OAAO,GAAG,IAAA,yBAAW,EAAC,MAAM,EAAE;YAClC;gBACE,mBAAQ,CAAC,QAAQ,CAAC,sBAAsB;gBACxC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACzB,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE;wBACrC,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;wBAC1B,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;qBAC3B,CAAC,CAAC;gBACL,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,6BAA6B;gBAC/C,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACjB,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;aACF;YACD,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,eAAK,CAAC,MAAM,CAAC,iBAAO,GAAG,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC;SACxD,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,sBAAsB,CAAC,OAAe,EAAE,MAAc,EAAE,SAAiB;QACvE,MAAM,OAAO,GAAG,eAAK,CAAC,IAAI,CAAC,cAAc,MAAM,yBAAyB,SAAS,EAAE,CAAC,CAAC;QACrF,MAAM,OAAO,GAAG,GAAG,eAAK,CAAC,GAAG,CAAC,eAAK,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,OAAO,IAAI,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,sBAAsB,CACpB,UAAkB,EAClB,UAAgD;QAEhD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC;YAC1D,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,gBAAgB,GAAG,eAAK,CAAC,MAAM,CACnC,iBAAO,GAAG,0BAA0B,eAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CACjE,CAAC;QACF,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACpC,MAAM,OAAO,GAAG,eAAK,CAAC,IAAI,CACxB,UAAU;aACP,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,eAAe,GAAG,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,MAAM,GAAG,CAAC,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1E,OAAO,GAAG,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,IAAI,eAAK,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;QAC9E,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;QACF,OAAO,GAAG,gBAAgB,KAAK,OAAO,IAAI,CAAC;IAC7C,CAAC;IAED,sBAAsB,CAAC,OAAe,EAAE,SAAmB,EAAE,SAAkB;QAC7E,MAAM,gBAAgB,GAAG,SAAS;YAChC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC,iBAAO,GAAG,GAAG,GAAG,OAAO,CAAC;YACvC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,eAAK,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,eAAK,CAAC,IAAI,CACxB,SAAS;aACN,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACZ,MAAM,MAAM,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACzE,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACzC,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;QACF,MAAM,OAAO,GAAG,GAAG,gBAAgB,KAAK,OAAO,IAAI,CAAC;QAEpD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;OAWG;IACH,4BAA4B,CAC1B,EAAU,EACV,gBAAwB,EACxB,OAAe,EACf,UAAkB,EAClB,UAAkB,EAClB,MAAc,EACd,OAAe;QAEf,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,eAAK,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QAC/C,MAAM,sBAAsB,GAAG,eAAK,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,eAAK,CAAC,IAAI,CAAA,GAAG,GAAG,eAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9F,MAAM,aAAa,GAAG,MAAM,UAAU,OAAO,UAAU,EAAE,CAAC;QAC1D,OAAO,GAAG,iBAAO,IAAI,QAAQ,IAAI,sBAAsB,0CAA0C,aAAa,EAAE,CAAC;IACnH,CAAC;IAED;;;;;;;;;;;OAWG;IACH,gCAAgC,CAC9B,UAAkB,EAClB,aAAuB,EACvB,oBAA8B;QAE9B,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,WAAW;YACjC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC;YAC9D,CAAC,CAAC,UAAU,CAAC;QAEf,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC;QAChG,MAAM,eAAe,GAAG,eAAK,CAAC,GAAG,CAAC,4BAA4B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3F,OAAO,CACL,eAAK,CAAC,MAAM,CACV,GAAG,iBAAO,qBAAqB,eAAe,oCAAoC,kBAAkB,KAAK,eAAK,CAAC,IAAI,CACjH,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,KAAK,CACP,GAAG,eAAe,CACpB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,kCAAkC,CAChC,QAAgB,EAChB,UAAkB,EAClB,MAAc,EACd,OAAe;QAEf,MAAM,OAAO,GAAG,GAAG,eAAK,CAAC,MAAM,CAAA,0BAA0B,IAAI,YAAY,CACvE,IAAI,CAAC,KAAK,CAAC,WAAW,EACtB,QAAQ,CACT,EAAE,CAAC;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,eAAK,CAAC,IAAI,CACvB,UAAU;YACR,CAAC,OAAO,EAAE,GAAG,OAAO,IAAI,MAAM,EAAE,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAC1E,IAAI,oBAAU,GAAG,CAClB,CACJ,CAAC;QAEF,OAAO,GAAG,iBAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC;IACjD,CAAC;IAED;;;;;;;;;;OAUG;IACH,sDAAsD,CACpD,QAAgB,EAChB,uBAA+B,EAC/B,MAAc,EACd,OAAe;QAEf,MAAM,OAAO,GAAG,GAAG,eAAK,CAAC,MAAM,CAAA,YAAY,eAAK,CAAC,IAAI,CACnD,uBAAuB,CACxB,8BAA8B,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;QACpF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,eAAK,CAAC,IAAI,CACvB,UAAU;YACR,CAAC,OAAO,EAAE,GAAG,OAAO,IAAI,MAAM,EAAE,EAAE,cAAc,EAAE,uBAAuB,EAAE,QAAQ,CAAC,CAAC,IAAI,CACvF,IAAI,oBAAU,GAAG,CAClB,CACJ,CAAC;QAEF,OAAO,GAAG,iBAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC;IACjD,CAAC;IAED;;;;;;;;;;OAUG;IACH,sCAAsC,CAAC,MAAc,EAAE,MAAc,EAAE,OAAe;QACpF,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAG,eAAK,CAAC,MAAM,CAAA,qEAAqE,CAAC;QAClG,MAAM,MAAM,GAAG,eAAK,CAAC,IAAI,CACvB,iBAAiB;YACf,CAAC,OAAO,EAAE,GAAG,OAAO,IAAI,MAAM,EAAE,EAAE,cAAc,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,oBAAU,GAAG,CAAC,CAC3F,CAAC;QAEF,MAAM,QAAQ,GAAG,eAAK,CAAC,IAAI,CACzB,8FAA8F,CAC/F,CAAC;QAEF,OAAO,GAAG,iBAAO,IAAI,OAAO,QAAQ,MAAM,QAAQ,QAAQ,IAAI,CAAC;IACjE,CAAC;IAED,gCAAgC,CAAC,QAAgB;QAC/C,OAAO,GAAG,iBAAO,IAAI,eAAK,CAAC,MAAM,CAAA,4BAA4B,IAAI,QAAQ,EAAE,CAAC;IAC9E,CAAC;IAED,kBAAkB,CAAC,OAAe;QAChC,OAAO,KAAK,eAAK,CAAC,GAAG,CAAC,eAAK,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;IACrD,CAAC;IAED,oBAAoB,CAAC,OAAe;QAClC,OAAO,gBAAM,GAAG,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,aAAa,CAAC,OAAe;QAC3B,MAAM,OAAO,GAAG,IAAA,yBAAW,EAAC,OAAO,EAAE;YACnC;gBACE,mBAAQ,CAAC,QAAQ,CAAC,oBAAoB;gBACtC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACjB,OAAO,IAAI,CAAC,gCAAgC,CAC1C,EAAE,EACF,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EACzC,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAC1C,CAAC;gBACJ,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,gBAAgB;gBAClC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACjC,OAAO,IAAI,CAAC,4BAA4B,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACvE,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,qCAAqC;gBACvD,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;oBACT,OAAO,IAAI,CAAC,gCAAgC,CAAC,EAAE,CAAC,CAAC;gBACnD,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,uBAAuB;gBACzC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACrB,OAAO,IAAI,CAAC,kCAAkC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACjE,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,qBAAqB;gBACvC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACrB,OAAO,IAAI,CAAC,sDAAsD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACrF,CAAC;aACF;YACD;gBACE,mBAAQ,CAAC,QAAQ,CAAC,oBAAoB;gBACtC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;oBACrB,OAAO,IAAI,CAAC,sCAAsC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACjE,CAAC;aACF;YACD,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACjD,CAAC,CAAC;QAEH,6CAA6C;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iEAAiE;IACjE,8BAA8B;IAC9B,oBAAoB,CAClB,QAAgB,EAChB,mBAA2B,EAC3B,MAAc,EACd,IAAa,EACb,MAAe;QAEf,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAClF,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC;YACtE,MAAM,OAAO,GAAG,oBAAoB,CAAC;gBACnC,OAAO,EAAE,KAAK;gBACd,QAAQ;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ,EAAE,IAAI;gBACd,UAAU;gBACV,YAAY;gBACZ,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;gBACnC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;aACtD,CAAC,CAAC;YACH,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,yBAAyB,CAAC,KAA0B;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wBAAwB,CAAC,QAAgB,EAAE,UAAmB,EAAE,YAAqB;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uBAAuB,CACrB,UAAkB,EAClB,UAAgD;QAEhD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,IAAY;QAC3C,OAAO,gBAAM,GAAG,UAAU,CAAC,GAAG,IAAI,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY;QACzD,OAAO,gBAAM,GAAG,UAAU,CAAC,GAAG,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACpF,CAAC;IAED,mBAAmB,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY;QAC3D,OAAO,gBAAM,GAAG,UAAU,CAAC,GAAG,IAAI,cAAc,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IAChG,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,IAAY,EAAE,MAAc,EAAE,QAAgB;QAC7E,OAAO,gBAAM,GAAG,UAAU,CAAC,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAED,oBAAoB,CAAC,IAAY;QAC/B,OAAO,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,sBAAsB,CAAC,IAAY;QACjC,OAAO,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,qBAAqB,CAAC,IAAY,EAAE,IAAY;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,yEAAyE;IACzE,iBAAiB,CAAC,eAAuB,EAAE,gBAA2C;QACpF,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACvD,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,YAAY,GAAG,eAAK,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,eAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,OAAO,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,cAAc,CAAC,gBAA2C;QACxD,OAAO,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;YACzB,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE5F,OAAO,KAAK,KAAK,KAAK,iBAAiB,EAAE,CAAC;QAC5C,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,aAAa,CAAC,CAAU;QACtB,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEzE,OAAO,oBAAoB,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,CAAC,CAAC,QAAQ;YACpB,QAAQ;YACR,MAAM,EAAE,CAAC,CAAC,MAAM;YAChB,UAAU;YACV,UAAU;YACV,YAAY;YACZ,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;SACtD,CAAC,CAAC;IACL,CAAC;IAED,MAAM,KAAI,CAAC;IAEX,mEAAmE;IACnE,YAAY,CAAC,IAAY;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,iEAAiE;IACjE,eAAe;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,eAAe;QACb,OAAO,YAAY,IAAI,CAAC,MAAM,CAAC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,MAAM,eAAe,CAAC;IAC7F,CAAC;IAED,sBAAsB,CAAC,KAAwB;QAC7C,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzD,yDAAyD;QACzD,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAE5C,wDAAwD;QACxD,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,gDAAgD;QAChD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,EAAE,cAAc,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;;AA3xBH,8BA4xBC;AA3xBQ,gBAAM,GAAG,MAAM,AAAT,CAAU;AAChB,0BAAgB,GAAG,gBAAgB,AAAnB,CAAoB;AACpC,oBAAU,GAAG,UAAU,AAAb,CAAc;AACxB,oCAA0B,GAAG,0BAA0B,AAA7B,CAA8B;AACxD,sBAAY,GAAG,YAAY,AAAf,CAAgB;AAyxBrC,SAAS,aAAa,CAAC,GAAa;IAClC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED,SAAS,WAAW,CAAC,MAAgE;IACnF,MAAM,QAAQ,GAAG,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnD,OAAO,CACL,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACd,QAAQ;QACR,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CACtE,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa,CACpB,mBAA2B;IAM3B,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE5D,OAAO;QACL,QAAQ,EAAE,IAAI,IAAI,mBAAmB;QACrC,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE,MAAM;KACrB,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAa;IACrC,IAAI,CAAC,IAAI;QAAE,OAAO,SAAS,CAAC;IAC5B,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;IAC5C,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,EAC5B,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,OAAO,EACP,oBAAoB,GAAG,GAAG,GAY3B;;IACC,MAAM,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,MAAM,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC;IACjD,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAK,CAAC,CAAC,CAAC,iBAAO,CAAC,CAAC;IAE9C,MAAM,eAAe,GAAG,CAAC,QAAQ;QAC/B,CAAC,CAAC,4BAA4B;YAC5B,IAAI;QACN,CAAC,CAAC,mFAAmF;YACrF,UAAU,CAAC,QAAQ,CAAC;gBACpB,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,+BAA+B;oBAC/B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEhD,MAAM,aAAa,GAAG,WAAW,CAAC;QAChC,QAAQ,EAAE,eAAe;QACzB,GAAG,EAAE,MAAM;QACX,IAAI;KACL,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,GAAG,IAAI,IAAI,aAAa,EAAE,CAAC;IAElD,MAAM,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;IAE9F,oEAAoE;IACpE,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAChD,MAAM,gBAAgB,GAAG,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC;IAC1F,iFAAiF;IACjF,uDAAuD;IACvD,IAAI,gBAAgB,EAAE,CAAC;QACrB,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,UAAU,GAAG,eAAe,CAAC;QAEjC,wCAAwC;QACxC,kHAAkH;QAClH,sEAAsE;QACtE,IAAI,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,mCAAI,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/E,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACzE,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAEnD,mEAAmE;YACnE,iEAAiE;YACjE,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAClB,wEAAwE;gBACxE,SAAS,IAAI,CAAC,CAAC;gBACf,WAAW,GAAG,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;YAC/C,CAAC;YACD,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAChC,WAAW,IAAI,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;YAED,kHAAkH;YAClH,UAAU;gBACR,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,eAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;oBACzF,GAAG;oBACH,eAAe,CAAC;QACpB,CAAC;QAED,OAAO,CAAC,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAC7F,IAAI,CACL,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,GAAG,EAAE,QAAQ,EAAE;YAC7C,4DAA4D;YAC5D,gDAAgD;YAChD,aAAa,EAAE,KAAK;YACpB,+DAA+D;YAC/D,OAAO,EAAE,eAAe;SACzB,CAAC,CAAC;QAEH,OAAO,KAAK,cAAc,OAAO,MAAM,IAAI,CAAC;IAC9C,CAAC;IAAC,MAAM,CAAC;QACP,kHAAkH;QAClH,MAAM,YAAY,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,eAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE5F,MAAM,MAAM,GAAG,GAAG,QAAQ,KAAK,YAAY,IAAI,eAAe,EAAE,CAAC;QAEjE,OAAO,KAAK,cAAc,OAAO,MAAM,IAAI,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,SAAS,IAAI,CAAC,KAAa;IACzB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,iDAAiD;AACjD,SAAS,YAAY,CAAC,IAAY,EAAE,GAAW;IAC7C,OAAO,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,YAAY,CAAC,IAAY,EAAE,GAAW,EAAE,QAAkC;IACjF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,KAAK,CAAC,IAAY;IACzB,MAAM,oBAAoB,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;IAE3F,IAAI,oBAAoB,IAAI,WAAW,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC", "sourcesContent": ["import { codeFrameColumns } from '@babel/code-frame';\nimport chalk from 'chalk';\nimport * as fs from 'fs';\nimport * as os from 'os';\nimport * as path from 'path';\n\nimport { Matchers } from './Matchers';\nimport { Failure, Parser } from './Parser';\nimport { switchRegex } from './switchRegex';\nimport {\n  BREADCRUMB,\n  COMPLETION,\n  ERROR,\n  FAIL,\n  INDENT,\n  MEASURE,\n  PASS,\n  PENDING,\n  WARNING,\n} from './utils/symbols';\n\nenum Status {\n  Pass = 'pass',\n  Fail = 'fail',\n  Pending = 'pending',\n  Error = 'error',\n  Completion = 'completion',\n  Measure = 'measure',\n}\n\nfunction highlightLastPathComponent(filePath: string): string {\n  return chalk.dim(path.dirname(filePath) + '/') + path.basename(filePath);\n}\n\nfunction format(command: string, argumentText = '', success = true) {\n  const symbol = statusSymbol(success ? Status.Completion : Status.Fail);\n  return [symbol, chalk.bold(command), argumentText].join(' ').trim();\n}\n\nfunction formatTest(testCase: string, status: Status) {\n  return [statusSymbol(status), testCase].join(' ').trim();\n}\n\nfunction heading(prefix: string, text: string, description: string): string {\n  return [prefix, chalk.white(text), description].join(' ').trim();\n}\n\nfunction statusSymbol(status: Status) {\n  switch (status) {\n    case Status.Pass:\n      return chalk.green(PASS);\n    case Status.Fail:\n      return chalk.red(FAIL);\n    case Status.Pending:\n      return chalk.cyan(PENDING);\n    case Status.Error:\n      return chalk.red(ERROR);\n    case Status.Completion:\n      return chalk.white(COMPLETION);\n    case Status.Measure:\n      return chalk.magenta(MEASURE);\n    default:\n      return '';\n  }\n}\n\nfunction coloredTime(time: string) {\n  const flt = parseFloat(time);\n  if (flt >= 0 && flt <= 0.025) {\n    return time;\n  } else if (flt >= 0.026 && flt <= 0.1) {\n    return chalk.yellow(time);\n  }\n  return chalk.red(time);\n}\n\nfunction capitalize(string: string): string {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\nfunction relativePath(projectRoot: string, filePath: string): string {\n  return slash(path.relative(projectRoot, filePath));\n}\n\nfunction formatBreadCrumb(fileName: string, target?: string, project?: string): string {\n  // TODO: Simplify\n  return [project === target ? project : [project, target].filter(Boolean).join(`/`), fileName]\n    .filter(Boolean)\n    .join(` ${BREADCRUMB} `);\n}\n\nfunction getAppRoot(filePath: string): string {\n  let appRoot = filePath;\n\n  const isBuildFolder = (path: string) => /((Debug|Release)-[^/\\s\\d]*$)|(.app$)/.test(path);\n\n  while (!isBuildFolder(appRoot) && appRoot.length > 1) {\n    appRoot = path.dirname(appRoot);\n  }\n  return isBuildFolder(appRoot) ? appRoot : '';\n}\n\nexport type FileOperation = {\n  type:\n    | 'Analyze'\n    | 'GenerateDSYMFile'\n    | 'Ld'\n    | 'Libtool'\n    | 'ProcessPCH'\n    | 'ProcessInfoPlistFile'\n    | 'CodeSign'\n    | 'Touch'\n    | 'CompileC'\n    | 'CompileSwift'\n    | 'CompileXIB'\n    | 'CompileStoryboard';\n  filePath: string;\n  fileName: string;\n  arch?: string;\n  // normal\n  linkType?: string;\n  target?: string;\n  project?: string;\n};\n\nexport type ConfigurationOperation = {\n  type: 'Analyze' | 'Aggregate' | 'Build' | 'Clean';\n  configuration: string;\n  target?: string;\n  project?: string;\n};\n\nexport type CopyFileProps = {\n  type: 'CpResource' | 'CopyStringsFile' | 'CopyPlistFile' | 'CpHeader';\n  from: string;\n  to: string;\n  target?: string;\n  project?: string;\n};\n\nexport class Formatter {\n  static format = format;\n  static formatBreadCrumb = formatBreadCrumb;\n  static getAppRoot = getAppRoot;\n  static highlightLastPathComponent = highlightLastPathComponent;\n  static relativePath = relativePath;\n\n  _parser: Parser | undefined;\n\n  get parser(): Parser {\n    if (this._parser) {\n      return this._parser;\n    }\n    this._parser = new Parser(this);\n    return this._parser;\n  }\n\n  errors: string[] = [];\n\n  warnings: string[] = [];\n\n  constructor(\n    public props: {\n      // isASCII: boolean;\n      // isColorDisabled: boolean;\n      projectRoot: string;\n      /** Sets the max character length for a warning before cropping the preview. Useful for minified files that can be upwards of a thousand characters long. */\n      maxWarningLineLength?: number;\n    }\n  ) {}\n\n  pipe(data: string): string[] {\n    const lines: string[] = [];\n    data.split(os.EOL).forEach(line => {\n      const results = this.parser.parse(line);\n      if (typeof results === 'string' && results.trim()) {\n        lines.push(results);\n      }\n    });\n    this.checkForBundlingErrors(lines);\n    return lines;\n  }\n\n  private dimConfiguration(configuration: string): string {\n    return chalk.dim(`(${configuration})`);\n  }\n\n  getTitleForConfigurationType(type: ConfigurationOperation['type']): string {\n    switch (type) {\n      case 'Clean':\n        return 'Cleaning';\n      case 'Aggregate':\n        return 'Aggregate';\n      case 'Analyze':\n        return 'Analyzing';\n      case 'Build':\n        return 'Building';\n      default:\n        return 'Unknown';\n    }\n  }\n\n  formatTarget(props: ConfigurationOperation): string {\n    return format(\n      this.getTitleForConfigurationType(props.type),\n      this.dimConfiguration(formatBreadCrumb(props.configuration, props.target, props.project))\n    );\n  }\n\n  formatCopy({ from, to }: CopyFileProps): string {\n    const relativeFile = relativePath(this.props.projectRoot, from);\n    const appFileRoot = getAppRoot(to);\n    const relativeAppFile = relativePath(appFileRoot, to);\n    return format('Copying  ', [relativeFile, relativeAppFile].join(' ➜ '));\n  }\n\n  getFileOperationTitle(type: FileOperation['type']): string {\n    switch (type) {\n      case 'Analyze':\n        return 'Analyzing';\n      case 'GenerateDSYMFile':\n        return `Generating debug`;\n      case 'Ld':\n        return 'Linking  ';\n      case 'Libtool':\n        return 'Packaging';\n      case 'ProcessPCH':\n        return 'Precompiling';\n      case 'ProcessInfoPlistFile':\n        return 'Preparing';\n      case 'CodeSign':\n        return 'Signing  ';\n      case 'Touch':\n        return 'Creating ';\n      case 'CompileC':\n      case 'CompileSwift':\n      case 'CompileXIB':\n      case 'CompileStoryboard':\n        return 'Compiling';\n      default:\n        // Unknown file operation\n        return '';\n    }\n  }\n\n  formatFileOperation(props: FileOperation): string {\n    const title = this.getFileOperationTitle(props.type);\n\n    switch (props.type) {\n      case 'Analyze':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'GenerateDSYMFile':\n        return format(title, formatBreadCrumb(`'${props.fileName}'`, props.target, props.project));\n      case 'Ld':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'Libtool':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'ProcessPCH':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'ProcessInfoPlistFile':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'CodeSign':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      case 'Touch':\n        return format(title, props.fileName);\n      case 'CompileC':\n      case 'CompileSwift':\n      case 'CompileXIB':\n      case 'CompileStoryboard':\n        return format(title, formatBreadCrumb(props.fileName, props.target, props.project));\n      default:\n        // Unknown file operation\n        return '';\n    }\n  }\n\n  formatPhaseSuccess(phaseName: string, duration?: string): string {\n    return format(\n      capitalize(phaseName.toLowerCase()),\n      `Succeeded${duration ? ` (${duration})` : ''}`,\n      true\n    );\n  }\n\n  formatPhaseScriptExecution(scriptName: string, target?: string, project?: string): string {\n    // TODO: Track (scriptName === '[CP] Copy XCFrameworks')\n    return format('Executing', formatBreadCrumb(`'${scriptName}'`, target, project));\n  }\n\n  formatPreprocess(file: string): string {\n    return format('Preprocessing', file);\n  }\n\n  formatShellCommand(command: string, args: string): string {\n    return '';\n  }\n\n  formatCompileCommand(compilerCommand: string, filePath: string): string {\n    return '';\n  }\n\n  formatProcessPchCommand(filePath: string): string {\n    return '';\n  }\n\n  formatWriteFile(file: string): string {\n    return '';\n  }\n\n  formatOther(text: string): string {\n    return '';\n  }\n\n  formatSingleLineCompileIssue(\n    type: 'warning' | 'error',\n    filePathAndLocation: string,\n    fileName: string,\n    reason: string,\n    target: string,\n    project: string\n  ): string {\n    // Allow disabling this warning.\n    const { filePath, lineNumber, columnNumber } = splitPathInfo(filePathAndLocation);\n    if (type === 'warning') {\n      if (!this.shouldShowCompileWarning(filePath, lineNumber, columnNumber)) {\n        return '';\n      }\n    }\n    // Prevent `/foo/bar:1:1` instead using `/foo/bar` since it's a bit more focused.\n    const sanitizedFilePath =\n      lineNumber === '1' && columnNumber === '1' ? filePath : filePathAndLocation;\n\n    // Get the `Project/Target` prefix.\n    const packageName = [project, target].join('/');\n    // Choose a color.\n    const color = type === 'warning' ? chalk.yellow : chalk.red;\n    const platform = color.bold(`${packageName}:`);\n    // Choose a symbol.\n    const symbol = type === 'warning' ? WARNING : ERROR;\n\n    // Get a more concise file path when the issue is inside the build folder.\n    const appFileRoot = getAppRoot(sanitizedFilePath);\n    const relativeAppFile = appFileRoot\n      ? chalk.gray('[app]/') + relativePath(appFileRoot, sanitizedFilePath)\n      : sanitizedFilePath;\n\n    // Create the message.\n    const results = chalk`${symbol} ${platform} ${reason.trim()}\\n   {gray └─${relativeAppFile}}`;\n\n    // Ensure we track the message\n    if (type === 'warning') {\n      this.warnings.push(results);\n    } else {\n      this.errors.push(results);\n    }\n    return results;\n  }\n\n  // These are like comments but for Xcode logs.\n  formatRemark(msg: string): string {\n    return '';\n  }\n\n  formatEmitSwiftModule(\n    type: 'normal' | string,\n    arch: 'x86_64' | string,\n    target?: string,\n    project?: string\n  ): string {\n    return '';\n  }\n  formatCompileSwiftSources(\n    type: 'normal' | string,\n    arch: 'x86_64' | string,\n    pkg: string | 'com.apple.xcode.tools.swift.compiler',\n    target?: string,\n    project?: string\n  ): string {\n    return '';\n  }\n\n  formatCleanRemove(msg: string): string {\n    return '';\n  }\n\n  formatWriteAuxiliaryFiles(text: string): string {\n    return '';\n  }\n\n  formatTiffutil(file: string): string {\n    return format('Validating', file);\n  }\n\n  formatCheckDependencies(text: string): string {\n    return format('Check Dependencies');\n  }\n\n  formatWillNotBeCodeSigned(message: string): string {\n    const results = `${chalk.yellow(WARNING + ' ' + message)}`;\n    this.warnings.push(results);\n    return results;\n  }\n\n  // COMPILER / LINKER ERRORS AND WARNINGS\n  /**\n   *\n   * @param fileName 'SampleTest.m',\n   * @param filePathAndLocation '/Users/<USER>/bar.m:12:59',\n   * @param reason 'expected identifier',\n   * @param line '                [[thread should] equal:thread.];',\n   * @param cursor '                                           ^'\n   */\n  formatCompileError(\n    fileName: string,\n    filePathAndLocation: string,\n    reason: string,\n    line: string,\n    cursor: string\n  ): string {\n    const { filePath, lineNumber, columnNumber } = splitPathInfo(filePathAndLocation);\n    const results = formatWarningOrError({\n      isError: true,\n      filePath,\n      reason,\n      cursor,\n      lineText: line,\n      lineNumber,\n      columnNumber,\n      projectRoot: this.props.projectRoot,\n      maxWarningLineLength: this.props.maxWarningLineLength,\n    });\n    this.errors.push(results);\n    return results;\n  }\n\n  formatError(message: string): string {\n    const results = switchRegex(message, [\n      [\n        Matchers.Errors.UNSUPPORTED_ENTITLEMENT_MATCHER,\n        ([, $1, $2, $3, $4, $5]) => {\n          return this.formatUnsupportedEntitlementError(\n            $1,\n            $2,\n            $3 as 'capability' | 'entitlement',\n            $4,\n            $5\n          );\n        },\n      ],\n      [null, () => this.formatGenericError(message)],\n    ]);\n\n    this.errors.push(results);\n\n    return results;\n  }\n\n  /**\n   * In: `error: Provisioning profile \"iOS Team Provisioning Profile: *\" doesn't support the Push Notifications capability. (in target 'yolo90' from project 'yolo90')`\n   * Out: `❌  yolo90/yolo90: Provisioning Profile \"iOS Team Provisioning Profile: *\" does not support the Push Notifications capability.`\n   *\n   * In: `error: Provisioning profile \"iOS Team Provisioning Profile: *\" doesn't include the aps-environment entitlement. (in target 'yolo90' from project 'yolo90')`\n   * Out: `❌  yolo90/yolo90: Entitlements file defines the value \"aps-environment\" which is not registered for profile \"iOS Team Provisioning Profile: *\".`\n   *\n   * @param profileName `\"iOS Team Provisioning Profile: *\"`\n   * @param entitlementName `Push Notifications` | `aps-environment`\n   * @param entitlementType `capability` | `entitlement`\n   * @param target boost-for-react-native\n   * @param project Pods\n   */\n  formatUnsupportedEntitlementError(\n    profileName: string,\n    entitlementName: string,\n    entitlementType: 'capability' | 'entitlement',\n    target?: string,\n    project?: string\n  ): string {\n    const packageName = [project, target].join('/');\n    const platform = chalk.red.bold(`${packageName}:`);\n\n    if (entitlementType === 'capability') {\n      return chalk`${ERROR} ${platform} Provisioning Profile ${profileName} does not support the {red ${entitlementName}} capability.`;\n    }\n    return chalk`${ERROR} ${platform} Entitlements file defines the value {red \"${entitlementName}\"} which is not registered for profile ${profileName}.`;\n  }\n\n  formatFileMissingError(reason: string, filePath: string): string {\n    const results = `\\n${chalk.red(ERROR + ' ' + reason)} ${filePath}\\n\\n`;\n    this.errors.push(results);\n    return results;\n  }\n\n  formatLdWarning(reason: string): string {\n    const results = switchRegex(reason, [\n      [\n        Matchers.Warnings.LINKER_METHOD_OVERRIDE,\n        ([, $1, $2, $3, $4, $5]) => {\n          return this.formatLdMethodOverride($1, [\n            { filePath: $2, name: $3 },\n            { filePath: $4, name: $5 },\n          ]);\n        },\n      ],\n      [\n        Matchers.Warnings.LINKER_METHOD_SINGLE_OVERRIDE,\n        ([, $1, $2, $3]) => {\n          return this.formatLdMethodOverride($1, [{ filePath: $2, name: $3 }]);\n        },\n      ],\n      [null, () => `${chalk.yellow(WARNING + ' ' + reason)}`],\n    ]);\n\n    if (results) {\n      this.warnings.push(results);\n    }\n\n    return results;\n  }\n\n  formatUndefinedSymbols(message: string, symbol: string, reference: string): string {\n    const symbols = chalk.gray(`┌─ Symbol: ${symbol}\\n└─ Referenced from: ${reference}`);\n    const results = `${chalk.red(ERROR + ' ' + message)}\\n${symbols}\\n`;\n    this.errors.push(results);\n    return results;\n  }\n\n  formatLdMethodOverride(\n    methodName: string,\n    collisions: { filePath: string; name: string }[]\n  ): string {\n    if (!this.shouldShowLinkerWarning(methodName, collisions)) {\n      return '';\n    }\n\n    const formattedMessage = chalk.yellow(\n      WARNING + ` ld: duplicate method '${chalk.bold(methodName)}' in`\n    );\n    const types = ['category', 'class'];\n    const symbols = chalk.gray(\n      collisions\n        .map(({ filePath, name }, i) => {\n          const appFileRoot = getAppRoot(filePath);\n          const relativeAppFile = relativePath(appFileRoot, filePath);\n          const branch = i === collisions.length - 1 ? '└─' : i === 0 ? '┌─' : '├─';\n          return `${branch}${`[${types[i]}]`}: ${name} ${chalk.dim(relativeAppFile)}`;\n        })\n        .join('\\n')\n    );\n    return `${formattedMessage}\\n${symbols}\\n`;\n  }\n\n  formatDuplicateSymbols(message: string, filePaths: string[], isWarning: boolean): string {\n    const formattedMessage = isWarning\n      ? chalk.yellow(WARNING + ' ' + message)\n      : chalk.red(ERROR + ' ' + message);\n    const symbols = chalk.gray(\n      filePaths\n        .map((p, i) => {\n          const branch = i === filePaths.length - 1 ? '└─' : i === 0 ? '┌─' : '├─';\n          return `${branch} ${path.basename(p)}`;\n        })\n        .join('\\n')\n    );\n    const results = `${formattedMessage}\\n${symbols}\\n`;\n\n    if (isWarning) {\n      this.warnings.push(results);\n    } else {\n      this.errors.push(results);\n    }\n    return results;\n  }\n\n  /**\n   * In: `The iOS Simulator deployment target 'IPHONEOS_DEPLOYMENT_TARGET' is set to 8.0, but the range of supported deployment target versions is 9.0 to 14.3.99. (in target 'boost-for-react-native' from project 'Pods')`\n   * Out: `⚠️ Pods/boost-for-react-native: iOS@8.0 version mismatch. Expected >= 9.0 < 14.3.99`\n   *\n   * @param os iOS\n   * @param deploymentTarget IPHONEOS_DEPLOYMENT_TARGET\n   * @param version 8.0\n   * @param minVersion 9.0\n   * @param maxVersion 14.3.99\n   * @param target boost-for-react-native\n   * @param project Pods\n   */\n  formatVersionMismatchWarning(\n    os: string,\n    deploymentTarget: string,\n    version: string,\n    minVersion: string,\n    maxVersion: string,\n    target: string,\n    project: string\n  ): string {\n    const packageName = [project, target].join('/');\n    const platform = chalk.bold(`${packageName}:`);\n    const packageNameWithVersion = chalk.greenBright(os) + chalk.cyan`@` + chalk.magenta(version);\n    const expectedRange = `>= ${minVersion} <= ${maxVersion}`;\n    return `${WARNING} ${platform} ${packageNameWithVersion} deployment version mismatch, expected ${expectedRange}`;\n  }\n\n  /**\n   * In: `warning: [CP] Vendored binary '/Users/<USER>/Library/Developer/Xcode/DerivedData/yolo67-hcjsxsdqyxnsgdednlbpylgeffja/Build/Intermediates.noindex/Pods.build/Debug-iphonesimulator/hermes-engine.build/DerivedSources/hermes.framework.dSYM/Contents/Resources/DWARF/hermes' contains architectures (armv7 armv7s arm64) none of which match the current build architectures (x86_64).`\n   * Out: `⚠️  Vendored binary '[app]/hermes-engine.build/DerivedSources/hermes.framework.dSYM/Contents/Resources/DWARF/hermes' does not support current build architecture (x86_64). Supported architectures: armv7, armv7s, arm64.`\n   *\n   * @param os iOS\n   * @param deploymentTarget IPHONEOS_DEPLOYMENT_TARGET\n   * @param version 8.0\n   * @param minVersion 9.0\n   * @param maxVersion 14.3.99\n   * @param target boost-for-react-native\n   * @param project Pods\n   */\n  formatMissingArchitectureWarning(\n    binaryPath: string,\n    architectures: string[],\n    currentArchitectures: string[]\n  ): string {\n    const appFileRoot = getAppRoot(binaryPath);\n    const relativeAppFile = appFileRoot\n      ? chalk.gray('[app]/') + relativePath(appFileRoot, binaryPath)\n      : binaryPath;\n\n    const architectureString = currentArchitectures.length === 1 ? 'architecture' : 'architectures';\n    const supportedString = chalk.dim(`Supported architectures: ${architectures.join(', ')}.`);\n    return (\n      chalk.yellow(\n        `${WARNING} Vendored binary '${relativeAppFile}' does not support current build ${architectureString} (${chalk.bold(\n          currentArchitectures.join(', ')\n        )}). `\n      ) + supportedString\n    );\n  }\n\n  /**\n   * In: `Skipping duplicate build file in Compile Sources build phase: /Users/<USER>/Documents/GitHub/expo/ios/Exponent/Kernel/ReactAppManager/EXReactAppManager.mm (in target 'Exponent' from project 'Exponent')`\n   * Out:\n   * `⚠️ Skipping duplicate file: Exponent/Kernel/ReactAppManager/EXReactAppManager.mm:\n   *    Remove: Exponent » Exponent » Build Phases » Compile Sources » EXReactAppManager.mm`\n   *\n   * @param filePath\n   * @param buildPhase 'Compile Sources'\n   * @param target Exponent-watch-app\n   * @param project Exponent\n   */\n  formatDuplicateFileCompilerWarning(\n    filePath: string,\n    buildPhase: string,\n    target: string,\n    project: string\n  ): string {\n    const message = `${chalk.yellow`Skipping duplicate file:`} ${relativePath(\n      this.props.projectRoot,\n      filePath\n    )}`;\n    const fileName = path.basename(filePath);\n    const crumbs = chalk.gray(\n      'Remove: ' +\n        ['Xcode', `${project}/${target}`, 'Build Phases', buildPhase, fileName].join(\n          ` ${BREADCRUMB} `\n        )\n    );\n\n    return `${WARNING} ${message}\\n   ${crumbs}\\n`;\n  }\n\n  /**\n   * In: `The Copy Bundle Resources build phase contains this target's Info.plist file '/Users/<USER>/Documents/GitHub/expo/ios/Exponent/Supporting/Info.plist'. (in target 'Exponent' from project 'Exponent')`\n   * Out:\n   * `⚠️ Target's Info.plist file is incorrectly linked: Exponent/Supporting/Info.plist:\n   *    Remove: Exponent » Exponent » Build Phases » Copy Bundle Resources » Info.plist`\n   *\n   * @param filePath\n   * @param reservedFileDescription 'entitlements'\n   * @param target Exponent-watch-app\n   * @param project Exponent\n   */\n  formatReservedFileInCopyBundleResourcesCompilerWarning(\n    filePath: string,\n    reservedFileDescription: string,\n    target: string,\n    project: string\n  ): string {\n    const message = `${chalk.yellow`Target's ${chalk.bold(\n      reservedFileDescription\n    )} file is incorrectly linked:`} ${relativePath(this.props.projectRoot, filePath)}`;\n    const fileName = path.basename(filePath);\n    const crumbs = chalk.gray(\n      'Remove: ' +\n        ['Xcode', `${project}/${target}`, 'Build Phases', 'Copy Bundle Resources', fileName].join(\n          ` ${BREADCRUMB} `\n        )\n    );\n\n    return `${WARNING} ${message}\\n   ${crumbs}\\n`;\n  }\n\n  /**\n   * In: `Run script build phase '[CP-User] [Hermes] Replace Hermes for the right configuration, if needed' will be run during every build because it does not specify any outputs. To address this warning, either add output dependencies to the script phase, or configure it to run in every build by unchecking \"Based on dependency analysis\" in the script phase. (in target 'hermes-engine' from project 'Pods')`\n   * Out:\n   * `⚠️ Script has ambiguous dependencies causing it to run on every build.\n   *    To fix, go to: Xcode » Pods/hermes-engine » Build Phases » [CP-User] [Hermes] Replace Hermes for the right configuration, if needed\n   *    Either: Uncheck \"Based on dependency analysis\", or select output files to trigger the script`\n   *\n   * @param script [CP-User] [Hermes] Replace Hermes for the right configuration, if needed\n   * @param target Exponent-watch-app\n   * @param project Exponent\n   */\n  formatAmbiguousRunScriptOutputsWarning(script: string, target: string, project: string): string {\n    if (!this.shouldShowWarningInTarget({ target })) {\n      return '';\n    }\n\n    const message = chalk.yellow`Script has ambiguous dependencies causing it to run on every build.`;\n    const crumbs = chalk.gray(\n      'To fix, go to: ' +\n        ['Xcode', `${project}/${target}`, 'Build Phases', `'${script}'`].join(` ${BREADCRUMB} `)\n    );\n\n    const solution = chalk.gray(\n      `Either: Uncheck \"Based on dependency analysis\", or select output files to trigger the script`\n    );\n\n    return `${WARNING} ${message}\\n   ${crumbs}\\n   ${solution}\\n`;\n  }\n\n  formatMissingFileCompilerWarning(filePath: string): string {\n    return `${WARNING} ${chalk.yellow`No such file or directory:`} ${filePath}`;\n  }\n\n  formatGenericError(message: string): string {\n    return `\\n${chalk.red(ERROR + ' ' + message)}\\n\\n`;\n  }\n\n  formatGenericWarning(message: string): string {\n    return INDENT + chalk.yellow(message);\n  }\n\n  formatWarning(message: string): string {\n    const results = switchRegex(message, [\n      [\n        Matchers.Warnings.MISSING_ARCHITECTURE,\n        ([, $1, $2, $3]) => {\n          return this.formatMissingArchitectureWarning(\n            $1,\n            $2?.split(' ').map(value => value.trim()),\n            $3?.split(' ').map(value => value.trim())\n          );\n        },\n      ],\n      [\n        Matchers.Warnings.VERSION_MISMATCH,\n        ([, $1, $2, $3, $4, $5, $6, $7]) => {\n          return this.formatVersionMismatchWarning($1, $2, $3, $4, $5, $6, $7);\n        },\n      ],\n      [\n        Matchers.Warnings.MISSING_FILE_COMPILER_WARNING_MATCHER,\n        ([, $1]) => {\n          return this.formatMissingFileCompilerWarning($1);\n        },\n      ],\n      [\n        Matchers.Warnings.SKIPPING_DUPLICATE_FILE,\n        ([, $1, $2, $3, $4]) => {\n          return this.formatDuplicateFileCompilerWarning($2, $1, $3, $4);\n        },\n      ],\n      [\n        Matchers.Warnings.TARGETS_FILE_INCLUDED,\n        ([, $1, $2, $3, $4]) => {\n          return this.formatReservedFileInCopyBundleResourcesCompilerWarning($2, $1, $3, $4);\n        },\n      ],\n      [\n        Matchers.Warnings.AMBIGUOUS_RUN_SCRIPT,\n        ([, $1, $2, $3, $4]) => {\n          return this.formatAmbiguousRunScriptOutputsWarning($1, $2, $3);\n        },\n      ],\n      [null, () => this.formatGenericWarning(message)],\n    ]);\n\n    // If the warning wasn't skipped then add it.\n    if (results) {\n      this.warnings.push(results);\n    }\n\n    return results;\n  }\n\n  // TODO: see how we can unify formatError and formatCompileError,\n  //       the same for warnings\n  formatCompileWarning(\n    fileName: string,\n    filePathAndLocation: string,\n    reason: string,\n    line?: string,\n    cursor?: string\n  ): string {\n    const { filePath, lineNumber, columnNumber } = splitPathInfo(filePathAndLocation);\n    if (this.shouldShowCompileWarning(filePath, lineNumber, columnNumber)) {\n      const results = formatWarningOrError({\n        isError: false,\n        filePath,\n        reason,\n        cursor,\n        lineText: line,\n        lineNumber,\n        columnNumber,\n        projectRoot: this.props.projectRoot,\n        maxWarningLineLength: this.props.maxWarningLineLength,\n      });\n      if (results) {\n        this.warnings.push(results);\n      }\n      return results;\n    }\n    return '';\n  }\n\n  shouldShowWarningInTarget(props: { target?: string }): boolean {\n    return true;\n  }\n\n  shouldShowCompileWarning(filePath: string, lineNumber?: string, columnNumber?: string): boolean {\n    return true;\n  }\n\n  shouldShowLinkerWarning(\n    methodName: string,\n    collisions: { filePath: string; name: string }[]\n  ): boolean {\n    return true;\n  }\n\n  formatPendingTest(suite: string, test: string): string {\n    return INDENT + formatTest(`${test} [PENDING]`, Status.Pending);\n  }\n\n  formatPassingTest(suite: string, test: string, time: string): string {\n    return INDENT + formatTest(`${test} (${coloredTime(time)} seconds)`, Status.Pass);\n  }\n\n  formatMeasuringTest(suite: string, test: string, time: string): string {\n    return INDENT + formatTest(`${test} measured (${coloredTime(time)} seconds)`, Status.Measure);\n  }\n\n  formatFailingTest(suite: string, test: string, reason: string, filePath: string): string {\n    return INDENT + formatTest(`${test}, ${reason}`, Status.Fail);\n  }\n\n  formatTestRunStarted(name: string): string {\n    return heading('Test Suite', name, 'started');\n  }\n\n  formatTestSuiteStarted(name: string): string {\n    return heading('', name, '');\n  }\n\n  formatTestRunFinished(name: string, time: string): string {\n    return '';\n  }\n\n  // Will be printed by default. Override with '' if you don't want summary\n  formatTestSummary(executedMessage: string, failuresPerSuite: Record<string, Failure[]>): string {\n    const failures = this.formatFailures(failuresPerSuite);\n    let finalMessage = '';\n    if (!failures) {\n      finalMessage = chalk.green(executedMessage);\n    } else {\n      finalMessage = chalk.red(executedMessage);\n    }\n\n    const text = [failures, finalMessage].join('\\n\\n\\n').trim();\n    return `\\n\\n${text}`;\n  }\n\n  formatFailures(failuresPerSuite: Record<string, Failure[]>): string {\n    return Object.entries(failuresPerSuite)\n      .map(([suite, failures]) => {\n        const formattedFailures = failures.map(failure => this.formatFailure(failure)).join('\\n\\n');\n\n        return `\\n${suite}\\n${formattedFailures}`;\n      })\n      .join('\\n');\n  }\n\n  formatFailure(f: Failure): string {\n    const { filePath, lineNumber, columnNumber } = splitPathInfo(f.filePath);\n\n    return formatWarningOrError({\n      isError: true,\n      testName: f.testCase,\n      filePath,\n      reason: f.reason,\n      // cursor,\n      lineNumber,\n      columnNumber,\n      projectRoot: this.props.projectRoot,\n      maxWarningLineLength: this.props.maxWarningLineLength,\n    });\n  }\n\n  finish() {}\n\n  // Override if you want to catch something specific with your regex\n  prettyFormat(text: string) {\n    return this.parser.parse(text);\n  }\n\n  // If you want to print inline, override #optionalNewline with ''\n  optionalNewline() {\n    return '\\n';\n  }\n\n  getBuildSummary(): string {\n    return `\\n\\u203A ${this.errors.length} error(s), and ${this.warnings.length} warning(s)\\n`;\n  }\n\n  checkForBundlingErrors(lines: string | string[]) {\n    lines = Array.isArray(lines) ? lines : lines.split('\\n');\n    // Find the last line beginning with `Error:` in the logs\n    const lastErrorIndex = findLastIndex(lines);\n\n    // Unless we find an error, we don't need to do anything\n    if (lastErrorIndex === -1) {\n      return;\n    }\n\n    // Take 12 lines from the `Error:` line forwards\n    const block = lines.slice(lastErrorIndex, lastErrorIndex + 11).join('\\n');\n    const result = this.formatError(block);\n\n    this.errors.push(result);\n  }\n}\n\nfunction findLastIndex(arr: string[]) {\n  for (let i = arr.length - 1; i >= 0; i--) {\n    if (arr[i].startsWith('Error:')) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nfunction formatPaths(config: { filePath: string | null; line?: number; col?: number }) {\n  const filePath = chalk.reset.cyan(config.filePath);\n  return (\n    chalk.dim('(') +\n    filePath +\n    chalk.dim(`:${[config.line, config.col].filter(Boolean).join(':')})`)\n  );\n}\n\n/**\n * Split a string like `/Users/<USER>/bar.m:420:68` into its components.\n *\n * @param filePath '/Users/<USER>/bar.m:420:68'\n */\nfunction splitPathInfo(\n  filePathAndLocation: string\n): {\n  filePath: string;\n  lineNumber?: string;\n  columnNumber?: string;\n} {\n  const [path, line, column] = filePathAndLocation.split(':');\n\n  return {\n    filePath: path || filePathAndLocation,\n    lineNumber: line,\n    columnNumber: column,\n  };\n}\n\nfunction parseOptionalInt(text?: string): number | undefined {\n  if (!text) return undefined;\n  try {\n    const result = parseInt(text, 10);\n    return isNaN(result) ? undefined : result;\n  } catch {\n    return undefined;\n  }\n}\n\nfunction formatWarningOrError({\n  projectRoot,\n  filePath,\n  reason,\n  cursor,\n  lineText,\n  lineNumber,\n  columnNumber,\n  isError,\n  maxWarningLineLength = 200,\n}: {\n  projectRoot: string;\n  filePath: string;\n  reason: string;\n  cursor?: string;\n  lineText?: string;\n  lineNumber?: string;\n  columnNumber?: string;\n  isError: boolean;\n  testName?: string;\n  maxWarningLineLength?: number;\n}) {\n  const line = parseOptionalInt(lineNumber) || 0;\n  const column = parseOptionalInt(columnNumber);\n  const color = isError ? chalk.red : chalk.yellow;\n  const icon = color(isError ? ERROR : WARNING);\n\n  const displayFilePath = !filePath\n    ? // If no file path, use null\n      null\n    : // If the file path is inside of the build folder (Hermes), then use absolute path.\n    getAppRoot(filePath)\n    ? filePath\n    : // Otherwise, use relative path\n      slash(path.relative(projectRoot, filePath));\n\n  const formattedPath = formatPaths({\n    filePath: displayFilePath,\n    col: column,\n    line,\n  });\n\n  const pathWithPrefix = `${icon} ${formattedPath}`;\n\n  const formattedReason = color(grayOutMatch(reason, /(\\[-.*?\\])/).replace(/(\\(.*?\\)\\s?)/, ''));\n\n  // Add special case for .jsbundle files that are parsed with Hermes.\n  const isHermes = filePath.endsWith('.jsbundle');\n  const isPreviewTooLong = isHermes || (lineText && lineText.length > maxWarningLineLength);\n  // When the preview is too long, we skip reading the file and attempting to apply\n  // code coloring, this is because it can get very slow.\n  if (isPreviewTooLong) {\n    let previewLine = '';\n    let cursorLine = formattedReason;\n\n    // Create a curtailed preview line like:\n    // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`\n    // If there is no text preview or column number, we can't do anything.\n    if (lineText && column != null) {\n      const rangeWindow = Math.round(Math.max(displayFilePath?.length ?? 0, 80) / 2);\n      let minBounds = Math.max(0, column - rangeWindow);\n      const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);\n      previewLine = lineText.slice(minBounds, maxBounds);\n\n      // If we splice content off the start, then we should append `...`.\n      // This is unlikely to happen since we limit the activation size.\n      if (minBounds > 0) {\n        // Adjust the min bounds so the cursor is aligned after we add the \"...\"\n        minBounds -= 3;\n        previewLine = chalk.dim('...') + previewLine;\n      }\n      if (maxBounds < lineText.length) {\n        previewLine += chalk.dim('...');\n      }\n\n      // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n      cursorLine =\n        (column == null ? chalk.reset(cursor) : fill(column) + chalk.reset('^')).slice(minBounds) +\n        ' ' +\n        formattedReason;\n    }\n\n    return ['', pathWithPrefix, '', previewLine, cursorLine, chalk.dim('(warning truncated)')].join(\n      '\\n'\n    );\n  }\n\n  try {\n    const raw = fs.readFileSync(filePath, 'utf8');\n    const location = { start: { line, column } };\n    const framed = codeFrameColumns(raw, location, {\n      // TODO: Support iOS languages: C++, Objc, swift, Ruby, Bash\n      // Maybe something like prism but for terminals?\n      highlightCode: false,\n      // Remove `(_Nonnull, _Nullable, or _Null_unspecified)` options\n      message: formattedReason,\n    });\n\n    return `\\n${pathWithPrefix}\\n\\n${framed}\\n`;\n  } catch {\n    // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n    const customCursor = column == null ? chalk.reset(cursor) : fill(column) + chalk.reset('^');\n\n    const framed = `${lineText}\\n${customCursor} ${formattedReason}`;\n\n    return `\\n${pathWithPrefix}\\n\\n${framed}\\n`;\n  }\n}\n\nfunction fill(width: number): string {\n  return Array(width).join(' ');\n}\n\n// Dim values like `[-Wnullability-completeness]`\nfunction grayOutMatch(text: string, reg: RegExp): string {\n  return replaceMatch(text, reg, chalk.gray.dim);\n}\n\nfunction replaceMatch(text: string, reg: RegExp, callback: (text: string) => string): string {\n  const match = text.match(reg);\n  if (match?.length) {\n    return text.replace(reg, callback(match[0]));\n  }\n  return text;\n}\n\nfunction slash(path: string) {\n  const isExtendedLengthPath = /^\\\\\\\\\\?\\\\/.test(path);\n  const hasNonAscii = /[^\\u0000-\\u0080]+/.test(path); // eslint-disable-line no-control-regex\n\n  if (isExtendedLengthPath || hasNonAscii) {\n    return path;\n  }\n\n  return path.replace(/\\\\/g, '/');\n}\n"]}