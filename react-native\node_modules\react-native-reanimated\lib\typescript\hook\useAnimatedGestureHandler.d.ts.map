{"version": 3, "file": "useAnimatedGestureHandler.d.ts", "sourceRoot": "", "sources": ["../../../src/hook/useAnimatedGestureHandler.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,cAAc,EACd,kBAAkB,EAClB,eAAe,EAChB,MAAM,eAAe,CAAC;AAIvB,QAAA,MAAM,UAAU;;;;;;;CAON,CAAC;AAEX,KAAK,SAAS,GAAG,CAAC,OAAO,UAAU,CAAC,CAAC,MAAM,OAAO,UAAU,CAAC,CAAC;AAI9D,KAAK,YAAY,GAAG;IAClB,WAAW,EAAE;QACX,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;QAC5B,QAAQ,CAAC,gBAAgB,EAAE,MAAM,CAAC;QAClC,QAAQ,CAAC,KAAK,EAAE,CAAC,OAAO,UAAU,CAAC,CAAC,MAAM,OAAO,UAAU,CAAC,CAAC;QAC7D,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;QAC3B,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;QAC3B,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC;QAC9B,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC;QAC9B,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;QAC3B,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;KAC5B,CAAC;CACH,CAAC;AAEF,UAAU,oCAAoC;IAC5C,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB;AAED,MAAM,MAAM,mBAAmB,CAAC,KAAK,SAAS,MAAM,IAChD,eAAe,CAAC,KAAK,CAAC,GACtB,KAAK,CAAC;AAEV,KAAK,cAAc,CACjB,KAAK,SAAS,kBAAkB,CAAC,oCAAoC,CAAC,EACtE,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IACrC,CACF,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC,EACpC,OAAO,EAAE,OAAO,EAChB,kBAAkB,CAAC,EAAE,OAAO,KACzB,IAAI,CAAC;AAEV,MAAM,WAAW,eAAe,CAC9B,KAAK,SAAS,kBAAkB,CAAC,oCAAoC,CAAC,EACtE,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAEvC,CAAC,GAAG,EAAE,MAAM,GAAG,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;IAC1D,OAAO,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACzC,QAAQ,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1C,KAAK,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,QAAQ,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1C,QAAQ,CAAC,EAAE,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C;AAED;;;;;;;GAOG;AACH,wBAAgB,yBAAyB,CACvC,KAAK,SACH,kBAAkB,CAAC,oCAAoC,CAAC,GAAG,YAAY,EACzE,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACjE,QAAQ,EAAE,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,cAAc,OAqEpD,KAAK,KAAK,IAAI,CACnC"}