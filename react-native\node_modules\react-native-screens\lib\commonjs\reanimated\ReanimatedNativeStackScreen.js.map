{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_Screen", "_reactNativeReanimated", "_interopRequireWildcard", "_ReanimatedTransitionProgressContext", "_reactNativeSafeAreaContext", "_getDefaultHeaderHeight", "_getStatusBarHeight", "_ReanimatedHeaderHeightContext", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "_extends", "assign", "bind", "target", "arguments", "length", "source", "key", "apply", "AnimatedScreen", "Animated", "createAnimatedComponent", "InnerScreen", "ENABLE_FABRIC", "global", "_IS_FABRIC", "ReanimatedNativeStackScreen", "React", "forwardRef", "props", "ref", "children", "rest", "stackPresentation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimensions", "useSafeAreaFrame", "topInset", "useSafeAreaInsets", "top", "isStatusBarTranslucent", "statusBarTranslucent", "statusBarHeight", "getStatusBarHeight", "defaultHeaderHeight", "getDefaultHeaderHeight", "cachedHeaderHeight", "useRef", "headerHeight", "useSharedValue", "progress", "closing", "goingForward", "createElement", "onTransitionProgressReanimated", "useEvent", "event", "value", "Platform", "OS", "onHeaderHeightChangeReanimated", "current", "Provider", "displayName", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedNativeStackScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAQA,IAAAG,sBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,oCAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,2BAAA,GAAAN,OAAA;AAIA,IAAAO,uBAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,mBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,8BAAA,GAAAV,sBAAA,CAAAC,OAAA;AAA4E,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAApB,uBAAAgC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAAA,SAAAC,SAAA,IAAAA,QAAA,GAAAV,MAAA,CAAAW,MAAA,GAAAX,MAAA,CAAAW,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAO,SAAA,CAAAC,MAAA,EAAAR,CAAA,UAAAS,MAAA,GAAAF,SAAA,CAAAP,CAAA,YAAAU,GAAA,IAAAD,MAAA,QAAAhB,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAU,MAAA,EAAAC,GAAA,KAAAJ,MAAA,CAAAI,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAJ,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAJ,SAAA,KAT5E;AAWA,MAAMK,cAAc,GAAGC,8BAAQ,CAACC,uBAAuB,CACrDC,mBACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,aAAa,GAAG,CAAC,CAACC,MAAM,EAAEC,UAAU;AAE1C,MAAMC,2BAA2B,gBAAGC,cAAK,CAACC,UAAU,CAGlD,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EACnC,MAAM;IAAEI,iBAAiB,GAAG,MAAM;IAAEC;EAAe,CAAC,GAAGF,IAAI;EAE3D,MAAMG,UAAU,GAAG,IAAAC,4CAAgB,EAAC,CAAC;EACrC,MAAMC,QAAQ,GAAG,IAAAC,6CAAiB,EAAC,CAAC,CAACC,GAAG;EACxC,MAAMC,sBAAsB,GAAGR,IAAI,CAACS,oBAAoB,IAAI,KAAK;EACjE,MAAMC,eAAe,GAAG,IAAAC,2BAAkB,EACxCN,QAAQ,EACRF,UAAU,EACVK,sBACF,CAAC;;EAED;EACA;EACA,MAAMI,mBAAmB,GAAG,IAAAC,+BAAsB,EAChDV,UAAU,EACVO,eAAe,EACfT,iBAAiB,EACjBC,cACF,CAAC;EAED,MAAMY,kBAAkB,GAAGnB,cAAK,CAACoB,MAAM,CAACH,mBAAmB,CAAC;EAC5D,MAAMI,YAAY,GAAG,IAAAC,qCAAc,EAACL,mBAAmB,CAAC;EAExD,MAAMM,QAAQ,GAAG,IAAAD,qCAAc,EAAC,CAAC,CAAC;EAClC,MAAME,OAAO,GAAG,IAAAF,qCAAc,EAAC,CAAC,CAAC;EACjC,MAAMG,YAAY,GAAG,IAAAH,qCAAc,EAAC,CAAC,CAAC;EAEtC,oBACEzE,MAAA,CAAAkB,OAAA,CAAA2D,aAAA,CAAClC;EACC;EAAA,EAAAT,QAAA;IACAoB,GAAG,EAAEA,GAAI;IACTwB,8BAA8B,EAAE,IAAAC,+BAAQ,EACrCC,KAAkC,IAAK;MACtC,SAAS;;MACTN,QAAQ,CAACO,KAAK,GAAGD,KAAK,CAACN,QAAQ;MAC/BC,OAAO,CAACM,KAAK,GAAGD,KAAK,CAACL,OAAO;MAC7BC,YAAY,CAACK,KAAK,GAAGD,KAAK,CAACJ,YAAY;IACzC,CAAC,EACD;IACE;IACA;IACAM,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,sBAAsB;IACtB;IACFpC,aAAa,GACX,sBAAsB,GACtB,uBAAuB,CAE/B,CAAE;IACFqC,8BAA8B,EAAE,IAAAL,+BAAQ,EACrCC,KAAkC,IAAK;MACtC,SAAS;;MACT,IAAIA,KAAK,CAACR,YAAY,KAAKF,kBAAkB,CAACe,OAAO,EAAE;QACrDb,YAAY,CAACS,KAAK,GAAGD,KAAK,CAACR,YAAY;QACvCF,kBAAkB,CAACe,OAAO,GAAGL,KAAK,CAACR,YAAY;MACjD;IACF,CAAC,EACD;IACE;IACAU,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrB,sBAAsB,GACtBpC,aAAa,GACb,sBAAsB,GACtB,uBAAuB,CAE/B;EAAE,GACES,IAAI,gBACRxD,MAAA,CAAAkB,OAAA,CAAA2D,aAAA,CAAClE,8BAAA,CAAAO,OAA6B,CAACoE,QAAQ;IAACL,KAAK,EAAET;EAAa,gBAC1DxE,MAAA,CAAAkB,OAAA,CAAA2D,aAAA,CAACtE,oCAAA,CAAAW,OAAmC,CAACoE,QAAQ;IAC3CL,KAAK,EAAE;MACLP,QAAQ;MACRC,OAAO;MACPC;IACF;EAAE,GACDrB,QAC2C,CACR,CAC1B,CAAC;AAErB,CAAC,CAAC;AAEFL,2BAA2B,CAACqC,WAAW,GAAG,6BAA6B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAvE,OAAA,GAEzDgC,2BAA2B"}