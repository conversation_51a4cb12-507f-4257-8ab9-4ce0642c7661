import"../shell/shell.js";import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as o from"../../core/common/common.js";import*as i from"../../core/root/root.js";import*as n from"../../core/sdk/sdk.js";import*as a from"../../models/workspace/workspace.js";import*as r from"../../panels/network/forward/forward.js";import*as s from"../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as l from"../../panels/application/preloading/helper/helper.js";import*as c from"../../models/issues_manager/issues_manager.js";import*as d from"../main/main.js";const g={cssOverview:"CSS overview",showCssOverview:"Show CSS overview"},w=e.i18n.registerUIStrings("panels/css_overview/css_overview-meta.ts",g),m=e.i18n.getLazilyComputedLocalizedString.bind(void 0,w);let p;t.ViewManager.registerViewExtension({location:"panel",id:"cssoverview",commandPrompt:m(g.showCssOverview),title:m(g.cssOverview),order:95,persistence:"closeable",async loadView(){const e=await async function(){return p||(p=await import("../../panels/css_overview/css_overview.js")),p}();return new e.CSSOverviewPanel.CSSOverviewPanel(new e.CSSOverviewController.OverviewController)},isPreviewFeature:!0});const u={showElements:"Show Elements",elements:"Elements",showEventListeners:"Show Event Listeners",eventListeners:"Event Listeners",showProperties:"Show Properties",properties:"Properties",showStackTrace:"Show Stack Trace",stackTrace:"Stack Trace",showLayout:"Show Layout",layout:"Layout",hideElement:"Hide element",editAsHtml:"Edit as HTML",duplicateElement:"Duplicate element",undo:"Undo",redo:"Redo",captureAreaScreenshot:"Capture area screenshot",selectAnElementInThePageTo:"Select an element in the page to inspect it",newStyleRule:"New Style Rule",refreshEventListeners:"Refresh event listeners",wordWrap:"Word wrap",enableDomWordWrap:"Enable `DOM` word wrap",disableDomWordWrap:"Disable `DOM` word wrap",showHtmlComments:"Show `HTML` comments",hideHtmlComments:"Hide `HTML` comments",revealDomNodeOnHover:"Reveal `DOM` node on hover",showDetailedInspectTooltip:"Show detailed inspect tooltip",showCSSDocumentationTooltip:"Show CSS documentation tooltip",copyStyles:"Copy styles",showUserAgentShadowDOM:"Show user agent shadow `DOM`",showComputedStyles:"Show Computed Styles",showStyles:"Show Styles",toggleEyeDropper:"Toggle eye dropper"},y=e.i18n.registerUIStrings("panels/elements/elements-meta.ts",u),h=e.i18n.getLazilyComputedLocalizedString.bind(void 0,y);let S,v;async function E(){return S||(S=await import("../../panels/elements/elements.js")),S}function R(e){return void 0===S?[]:e(S)}t.ViewManager.registerViewExtension({location:"panel",id:"elements",commandPrompt:h(u.showElements),title:h(u.elements),order:10,persistence:"permanent",hasToolbar:!1,loadView:async()=>(await E()).ElementsPanel.ElementsPanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"elements.show-styles",category:"ELEMENTS",title:h(u.showStyles),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"elements.show-computed",category:"ELEMENTS",title:h(u.showComputedStyles),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate)}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.event-listeners",commandPrompt:h(u.showEventListeners),title:h(u.eventListeners),order:5,hasToolbar:!0,persistence:"permanent",loadView:async()=>(await E()).EventListenersWidget.EventListenersWidget.instance()}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.dom-properties",commandPrompt:h(u.showProperties),title:h(u.properties),order:7,persistence:"permanent",loadView:async()=>new((await E()).PropertiesWidget.PropertiesWidget)}),t.ViewManager.registerViewExtension({experiment:"capture-node-creation-stacks",location:"elements-sidebar",id:"elements.dom-creation",commandPrompt:h(u.showStackTrace),title:h(u.stackTrace),order:10,persistence:"permanent",loadView:async()=>new((await E()).NodeStackTraceWidget.NodeStackTraceWidget)}),t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"elements.layout",commandPrompt:h(u.showLayout),title:h(u.layout),order:4,persistence:"permanent",loadView:async()=>(await async function(){return v||(v=await import("../../panels/elements/components/components.js")),v}()).LayoutPane.LayoutPane.instance().wrapper}),t.ActionRegistration.registerActionExtension({actionId:"elements.hide-element",category:"ELEMENTS",title:h(u.hideElement),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"H"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.toggle-eye-dropper",category:"ELEMENTS",title:h(u.toggleEyeDropper),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ColorSwatchPopoverIcon.ColorSwatchPopoverIcon])),bindings:[{shortcut:"c"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.edit-as-html",category:"ELEMENTS",title:h(u.editAsHtml),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"F2"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.duplicate-element",category:"ELEMENTS",title:h(u.duplicateElement),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Shift+Alt+Down"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.copy-styles",category:"ELEMENTS",title:h(u.copyStyles),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Alt+C",platform:"windows,linux"},{shortcut:"Meta+Alt+C",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.undo",category:"ELEMENTS",title:h(u.undo),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Z",platform:"windows,linux"},{shortcut:"Meta+Z",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.redo",category:"ELEMENTS",title:h(u.redo),loadActionDelegate:async()=>new((await E()).ElementsPanel.ElementsActionDelegate),contextTypes:()=>R((e=>[e.ElementsPanel.ElementsPanel])),bindings:[{shortcut:"Ctrl+Y",platform:"windows,linux"},{shortcut:"Meta+Shift+Z",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"elements.capture-area-screenshot",loadActionDelegate:async()=>new((await E()).InspectElementModeController.ToggleSearchActionDelegate),condition:i.Runtime.conditions.canDock,title:h(u.captureAreaScreenshot),category:"SCREENSHOT"}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.toggle-element-search",toggleable:!0,loadActionDelegate:async()=>new((await E()).InspectElementModeController.ToggleSearchActionDelegate),title:h(u.selectAnElementInThePageTo),iconClass:"select-element",bindings:[{shortcut:"Ctrl+Shift+C",platform:"windows,linux"},{shortcut:"Meta+Shift+C",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.new-style-rule",title:h(u.newStyleRule),iconClass:"plus",loadActionDelegate:async()=>new((await E()).StylesSidebarPane.ActionDelegate),contextTypes:()=>R((e=>[e.StylesSidebarPane.StylesSidebarPane]))}),t.ActionRegistration.registerActionExtension({category:"ELEMENTS",actionId:"elements.refresh-event-listeners",title:h(u.refreshEventListeners),iconClass:"refresh",loadActionDelegate:async()=>new((await E()).EventListenersWidget.ActionDelegate),contextTypes:()=>R((e=>[e.EventListenersWidget.EventListenersWidget]))}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:1,title:h(u.showUserAgentShadowDOM),settingName:"show-ua-shadow-dom",settingType:"boolean",defaultValue:!1}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:2,title:h(u.wordWrap),settingName:"dom-word-wrap",settingType:"boolean",options:[{value:!0,title:h(u.enableDomWordWrap)},{value:!1,title:h(u.disableDomWordWrap)}],defaultValue:!0}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:3,title:h(u.showHtmlComments),settingName:"show-html-comments",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:h(u.showHtmlComments)},{value:!1,title:h(u.hideHtmlComments)}]}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:4,title:h(u.revealDomNodeOnHover),settingName:"highlight-node-on-hover-in-overlay",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",order:5,title:h(u.showDetailedInspectTooltip),settingName:"show-detailed-inspect-tooltip",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({settingName:"show-event-listeners-for-ancestors",settingType:"boolean",defaultValue:!0}),o.Settings.registerSettingExtension({category:"ADORNER",storageType:"Synced",settingName:"adorner-settings",settingType:"array",defaultValue:[]}),o.Settings.registerSettingExtension({category:"ELEMENTS",storageType:"Synced",title:h(u.showCSSDocumentationTooltip),settingName:"show-css-property-documentation-on-hover",settingType:"boolean",defaultValue:!0}),t.ContextMenu.registerProvider({contextTypes:()=>[n.RemoteObject.RemoteObject,n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadProvider:async()=>new((await E()).ElementsPanel.ContextMenuProvider),experiment:void 0}),t.ViewManager.registerLocationResolver({name:"elements-sidebar",category:"ELEMENTS",loadResolver:async()=>(await E()).ElementsPanel.ElementsPanel.instance()}),o.Revealer.registerRevealer({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode,n.RemoteObject.RemoteObject],destination:o.Revealer.RevealerDestination.ELEMENTS_PANEL,loadRevealer:async()=>new((await E()).ElementsPanel.DOMNodeRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[n.CSSProperty.CSSProperty],destination:o.Revealer.RevealerDestination.STYLES_SIDEBAR,loadRevealer:async()=>new((await E()).ElementsPanel.CSSPropertyRevealer)}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await E()).LayersWidget.ButtonProvider.instance(),order:1,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await E()).ElementStatePaneWidget.ButtonProvider.instance(),order:2,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await E()).ClassesPaneWidget.ButtonProvider.instance(),order:3,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await E()).StylesSidebarPane.ButtonProvider.instance(),order:100,location:"styles-sidebarpane-toolbar"}),t.Toolbar.registerToolbarItem({actionId:"elements.toggle-element-search",location:"main-toolbar-left",order:0}),t.UIUtils.registerRenderer({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadRenderer:async()=>(await E()).ElementsTreeOutline.Renderer.instance()}),o.Linkifier.registerLinkifier({contextTypes:()=>[n.DOMModel.DOMNode,n.DOMModel.DeferredDOMNode],loadLinkifier:async()=>(await E()).DOMLinkifier.Linkifier.instance()});const A={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},b=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",A),P=e.i18n.getLazilyComputedLocalizedString.bind(void 0,b);let f,T;async function k(){return f||(f=await import("../../panels/browser_debugger/browser_debugger.js")),f}async function D(){return T||(T=await import("../../panels/sources/sources.js")),T}t.ViewManager.registerViewExtension({loadView:async()=>(await k()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:P(A.showEventListenerBreakpoints),title:P(A.eventListenerBreakpoints),order:9,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>new((await k()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:P(A.showCspViolationBreakpoints),title:P(A.cspViolationBreakpoints),order:10,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>(await k()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:P(A.showXhrfetchBreakpoints),title:P(A.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await k()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:P(A.showDomBreakpoints),title:P(A.domBreakpoints),order:7,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>new((await k()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:P(A.showGlobalListeners),title:P(A.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await k()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:P(A.showDomBreakpoints),title:P(A.domBreakpoints),order:6,persistence:"permanent"}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:P(A.page),commandPrompt:P(A.showPage),order:2,persistence:"permanent",loadView:async()=>(await D()).SourcesNavigator.NetworkNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:P(A.overrides),commandPrompt:P(A.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await D()).SourcesNavigator.OverridesNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:P(A.contentScripts),commandPrompt:P(A.showContentScripts),order:5,persistence:"permanent",loadView:async()=>new((await D()).SourcesNavigator.ContentScriptsNavigatorView)}),t.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await k()).ObjectEventListenersSidebarPane.ActionDelegate),title:P(A.refreshGlobalListeners),iconClass:"refresh",contextTypes:()=>void 0===f?[]:(e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane])(f)}),t.ContextMenu.registerProvider({contextTypes:()=>[n.DOMModel.DOMNode],loadProvider:async()=>new((await k()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),t.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await k()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),t.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await k()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});const x={showNetwork:"Show Network",network:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},L=e.i18n.registerUIStrings("panels/network/network-meta.ts",x),M=e.i18n.getLazilyComputedLocalizedString.bind(void 0,L);let I;async function C(){return I||(I=await import("../../panels/network/network.js")),I}function N(e){return void 0===I?[]:e(I)}t.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:M(x.showNetwork),title:M(x.network),order:1100,condition:i.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await C()).NetworkPanel.NetworkPanel.instance()}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:M(x.showNetworkRequestBlocking),title:M(x.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await C()).BlockedURLsPane.BlockedURLsPane)}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:M(x.showNetworkConditions),title:M(x.networkConditions),persistence:"closeable",order:40,tags:[M(x.diskCache),M(x.networkThrottling),e.i18n.lockedLazyString("useragent"),e.i18n.lockedLazyString("user agent"),e.i18n.lockedLazyString("user-agent")],loadView:async()=>(await C()).NetworkConfigView.NetworkConfigView.instance()}),t.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:M(x.showSearch),title:M(x.search),persistence:"permanent",loadView:async()=>(await C()).NetworkPanel.SearchNetworkView.instance()}),t.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>N((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await C()).NetworkPanel.ActionDelegate),options:[{value:!0,title:M(x.recordNetworkLog)},{value:!1,title:M(x.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:M(x.clear),iconClass:"clear",loadActionDelegate:async()=>new((await C()).NetworkPanel.ActionDelegate),contextTypes:()=>N((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:M(x.hideRequestDetails),contextTypes:()=>N((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await C()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:M(x.search),contextTypes:()=>N((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await C()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),t.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:M(x.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>N((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await C()).BlockedURLsPane.ActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:M(x.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>N((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await C()).BlockedURLsPane.ActionDelegate)}),o.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:M(x.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[M(x.colorCode),M(x.resourceType)],options:[{value:!0,title:M(x.colorCodeByResourceType)},{value:!1,title:M(x.useDefaultColors)}]}),o.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:M(x.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[M(x.netWork),M(x.frame),M(x.group)],options:[{value:!0,title:M(x.groupNetworkLogItemsByFrame)},{value:!1,title:M(x.dontGroupNetworkLogItemsByFrame)}]}),t.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await C()).NetworkPanel.NetworkPanel.instance()}),t.ContextMenu.registerProvider({contextTypes:()=>[n.NetworkRequest.NetworkRequest,n.Resource.Resource,a.UISourceCode.UISourceCode],loadProvider:async()=>(await C()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),o.Revealer.registerRevealer({contextTypes:()=>[n.NetworkRequest.NetworkRequest],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await C()).NetworkPanel.RequestRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[r.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await C()).NetworkPanel.RequestLocationRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[r.NetworkRequestId.NetworkRequestId],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await C()).NetworkPanel.RequestIdRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[r.UIFilter.UIRequestFilter],destination:o.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await C()).NetworkPanel.NetworkLogWithFilterRevealer)});const V={security:"Security",showSecurity:"Show Security"},O=e.i18n.registerUIStrings("panels/security/security-meta.ts",V),B=e.i18n.getLazilyComputedLocalizedString.bind(void 0,O);let z;t.ViewManager.registerViewExtension({location:"panel",id:"security",title:B(V.security),commandPrompt:B(V.showSecurity),order:80,persistence:"closeable",loadView:async()=>(await async function(){return z||(z=await import("../../panels/security/security.js")),z}()).SecurityPanel.SecurityPanel.instance()});const U={toggleDeviceToolbar:"Toggle device toolbar",captureScreenshot:"Capture screenshot",captureFullSizeScreenshot:"Capture full size screenshot",captureNodeScreenshot:"Capture node screenshot",showMediaQueries:"Show media queries",device:"device",hideMediaQueries:"Hide media queries",showRulers:"Show rulers in the Device Mode toolbar",hideRulers:"Hide rulers in the Device Mode toolbar",showDeviceFrame:"Show device frame",hideDeviceFrame:"Hide device frame"},W=e.i18n.registerUIStrings("panels/emulation/emulation-meta.ts",U),j=e.i18n.getLazilyComputedLocalizedString.bind(void 0,W);let F;async function _(){return F||(F=await import("../../panels/emulation/emulation.js")),F}t.ActionRegistration.registerActionExtension({category:"MOBILE",actionId:"emulation.toggle-device-mode",toggleable:!0,loadActionDelegate:async()=>new((await _()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:j(U.toggleDeviceToolbar),iconClass:"devices",bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+M"},{platform:"mac",shortcut:"Shift+Meta+M"}]}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await _()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:j(U.captureScreenshot)}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-full-height-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await _()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:j(U.captureFullSizeScreenshot)}),t.ActionRegistration.registerActionExtension({actionId:"emulation.capture-node-screenshot",category:"SCREENSHOT",loadActionDelegate:async()=>new((await _()).DeviceModeWrapper.ActionDelegate),condition:i.Runtime.conditions.canDock,title:j(U.captureNodeScreenshot)}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"show-media-query-inspector",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:j(U.showMediaQueries)},{value:!1,title:j(U.hideMediaQueries)}],tags:[j(U.device)]}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-rulers",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:j(U.showRulers)},{value:!1,title:j(U.hideRulers)}],tags:[j(U.device)]}),o.Settings.registerSettingExtension({category:"MOBILE",settingName:"emulation.show-device-outline",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:j(U.showDeviceFrame)},{value:!1,title:j(U.hideDeviceFrame)}],tags:[j(U.device)]}),t.Toolbar.registerToolbarItem({actionId:"emulation.toggle-device-mode",condition:i.Runtime.conditions.canDock,location:"main-toolbar-left",order:1,showLabel:void 0,loadItem:void 0,separator:void 0}),o.AppProvider.registerAppProvider({loadAppProvider:async()=>(await _()).AdvancedApp.AdvancedAppProvider.instance(),condition:i.Runtime.conditions.canDock,order:0}),t.ContextMenu.registerItem({location:"deviceModeMenu/save",order:12,actionId:"emulation.capture-screenshot"}),t.ContextMenu.registerItem({location:"deviceModeMenu/save",order:13,actionId:"emulation.capture-full-height-screenshot"});const q={sensors:"Sensors",geolocation:"geolocation",timezones:"timezones",locale:"locale",locales:"locales",accelerometer:"accelerometer",deviceOrientation:"device orientation",locations:"Locations",touch:"Touch",devicebased:"Device-based",forceEnabled:"Force enabled",emulateIdleDetectorState:"Emulate Idle Detector state",noIdleEmulation:"No idle emulation",userActiveScreenUnlocked:"User active, screen unlocked",userActiveScreenLocked:"User active, screen locked",userIdleScreenUnlocked:"User idle, screen unlocked",userIdleScreenLocked:"User idle, screen locked",showSensors:"Show Sensors",showLocations:"Show Locations"},H=e.i18n.registerUIStrings("panels/sensors/sensors-meta.ts",q),G=e.i18n.getLazilyComputedLocalizedString.bind(void 0,H);let K,Y;async function J(){return K||(K=await import("../../panels/sensors/sensors.js")),K}t.ViewManager.registerViewExtension({location:"drawer-view",commandPrompt:G(q.showSensors),title:G(q.sensors),id:"sensors",persistence:"closeable",order:100,loadView:async()=>new((await J()).SensorsView.SensorsView),tags:[G(q.geolocation),G(q.timezones),G(q.locale),G(q.locales),G(q.accelerometer),G(q.deviceOrientation)]}),t.ViewManager.registerViewExtension({location:"settings-view",id:"emulation-locations",commandPrompt:G(q.showLocations),title:G(q.locations),order:40,loadView:async()=>new((await J()).LocationsSettingsTab.LocationsSettingsTab),settings:["emulation.locations"]}),o.Settings.registerSettingExtension({storageType:"Synced",settingName:"emulation.locations",settingType:"array",defaultValue:[{title:"Berlin",lat:52.520007,long:13.404954,timezoneId:"Europe/Berlin",locale:"de-DE"},{title:"London",lat:51.507351,long:-.127758,timezoneId:"Europe/London",locale:"en-GB"},{title:"Moscow",lat:55.755826,long:37.6173,timezoneId:"Europe/Moscow",locale:"ru-RU"},{title:"Mountain View",lat:37.386052,long:-122.083851,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Mumbai",lat:19.075984,long:72.877656,timezoneId:"Asia/Kolkata",locale:"mr-IN"},{title:"San Francisco",lat:37.774929,long:-122.419416,timezoneId:"America/Los_Angeles",locale:"en-US"},{title:"Shanghai",lat:31.230416,long:121.473701,timezoneId:"Asia/Shanghai",locale:"zh-Hans-CN"},{title:"São Paulo",lat:-23.55052,long:-46.633309,timezoneId:"America/Sao_Paulo",locale:"pt-BR"},{title:"Tokyo",lat:35.689487,long:139.691706,timezoneId:"Asia/Tokyo",locale:"ja-JP"}]}),o.Settings.registerSettingExtension({title:G(q.touch),reloadRequired:!0,settingName:"emulation.touch",settingType:"enum",defaultValue:"none",options:[{value:"none",title:G(q.devicebased),text:G(q.devicebased)},{value:"force",title:G(q.forceEnabled),text:G(q.forceEnabled)}]}),o.Settings.registerSettingExtension({title:G(q.emulateIdleDetectorState),settingName:"emulation.idle-detection",settingType:"enum",defaultValue:"none",options:[{value:"none",title:G(q.noIdleEmulation),text:G(q.noIdleEmulation)},{value:'{"isUserActive":true,"isScreenUnlocked":true}',title:G(q.userActiveScreenUnlocked),text:G(q.userActiveScreenUnlocked)},{value:'{"isUserActive":true,"isScreenUnlocked":false}',title:G(q.userActiveScreenLocked),text:G(q.userActiveScreenLocked)},{value:'{"isUserActive":false,"isScreenUnlocked":true}',title:G(q.userIdleScreenUnlocked),text:G(q.userIdleScreenUnlocked)},{value:'{"isUserActive":false,"isScreenUnlocked":false}',title:G(q.userIdleScreenLocked),text:G(q.userIdleScreenLocked)}]});const X={accessibility:"Accessibility",shoAccessibility:"Show Accessibility"},Z=e.i18n.registerUIStrings("panels/accessibility/accessibility-meta.ts",X),Q=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Z);let $;t.ViewManager.registerViewExtension({location:"elements-sidebar",id:"accessibility.view",title:Q(X.accessibility),commandPrompt:Q(X.shoAccessibility),order:10,persistence:"permanent",loadView:async()=>(await async function(){return Y||(Y=await import("../../panels/accessibility/accessibility.js")),Y}()).AccessibilitySidebarView.AccessibilitySidebarView.instance()});const ee={animations:"Animations",showAnimations:"Show Animations"},te=e.i18n.registerUIStrings("panels/animation/animation-meta.ts",ee),oe=e.i18n.getLazilyComputedLocalizedString.bind(void 0,te);t.ViewManager.registerViewExtension({location:"drawer-view",id:"animations",title:oe(ee.animations),commandPrompt:oe(ee.showAnimations),persistence:"closeable",order:0,loadView:async()=>(await async function(){return $||($=await import("../../panels/animation/animation.js")),$}()).AnimationTimeline.AnimationTimeline.instance()});const ie={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},ne=e.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",ie),ae=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ne);let re;t.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:ae(ie.developerResources),commandPrompt:ae(ie.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return re||(re=await import("../../panels/developer_resources/developer_resources.js")),re}()).DeveloperResourcesView.DeveloperResourcesView)});const se={autofill:"Autofill",showAutofill:"Show Autofill"},le=e.i18n.registerUIStrings("panels/autofill/autofill-meta.ts",se),ce=e.i18n.getLazilyComputedLocalizedString.bind(void 0,le);let de;t.ViewManager.registerViewExtension({experiment:"autofill-view",location:"drawer-view",id:"autofill-view",title:ce(se.autofill),commandPrompt:ce(se.showAutofill),order:100,persistence:"closeable",async loadView(){const e=await async function(){return de||(de=await import("../../panels/autofill/autofill.js")),de}();return s.LegacyWrapper.legacyWrapper(t.Widget.Widget,new e.AutofillView.AutofillView)}});const ge={rendering:"Rendering",showRendering:"Show Rendering",paint:"paint",layout:"layout",fps:"fps",cssMediaType:"CSS media type",cssMediaFeature:"CSS media feature",visionDeficiency:"vision deficiency",colorVisionDeficiency:"color vision deficiency",reloadPage:"Reload page",hardReloadPage:"Hard reload page",forceAdBlocking:"Force ad blocking on this site",blockAds:"Block ads on this site",showAds:"Show ads on this site, if allowed",autoOpenDevTools:"Auto-open DevTools for popups",doNotAutoOpen:"Do not auto-open DevTools for popups",disablePaused:"Disable paused state overlay",toggleCssPrefersColorSchemeMedia:"Toggle CSS media feature prefers-color-scheme"},we=e.i18n.registerUIStrings("entrypoints/inspector_main/inspector_main-meta.ts",ge),me=e.i18n.getLazilyComputedLocalizedString.bind(void 0,we);let pe;async function ue(){return pe||(pe=await import("../inspector_main/inspector_main.js")),pe}t.ViewManager.registerViewExtension({location:"drawer-view",id:"rendering",title:me(ge.rendering),commandPrompt:me(ge.showRendering),persistence:"closeable",order:50,loadView:async()=>new((await ue()).RenderingOptions.RenderingOptionsView),tags:[me(ge.paint),me(ge.layout),me(ge.fps),me(ge.cssMediaType),me(ge.cssMediaFeature),me(ge.visionDeficiency),me(ge.colorVisionDeficiency)]}),t.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.reload",loadActionDelegate:async()=>new((await ue()).InspectorMain.ReloadActionDelegate),iconClass:"refresh",title:me(ge.reloadPage),bindings:[{platform:"windows,linux",shortcut:"Ctrl+R"},{platform:"windows,linux",shortcut:"F5"},{platform:"mac",shortcut:"Meta+R"}]}),t.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.hard-reload",loadActionDelegate:async()=>new((await ue()).InspectorMain.ReloadActionDelegate),title:me(ge.hardReloadPage),bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+R"},{platform:"windows,linux",shortcut:"Shift+F5"},{platform:"windows,linux",shortcut:"Ctrl+F5"},{platform:"windows,linux",shortcut:"Ctrl+Shift+F5"},{platform:"mac",shortcut:"Shift+Meta+R"}]}),t.ActionRegistration.registerActionExtension({actionId:"rendering.toggle-prefers-color-scheme",category:"RENDERING",title:me(ge.toggleCssPrefersColorSchemeMedia),loadActionDelegate:async()=>new((await ue()).RenderingOptions.ReloadActionDelegate)}),o.Settings.registerSettingExtension({category:"NETWORK",title:me(ge.forceAdBlocking),settingName:"network.ad-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:me(ge.blockAds)},{value:!1,title:me(ge.showAds)}]}),o.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Synced",title:me(ge.autoOpenDevTools),settingName:"auto-attach-to-created-pages",settingType:"boolean",order:2,defaultValue:!1,options:[{value:!0,title:me(ge.autoOpenDevTools)},{value:!1,title:me(ge.doNotAutoOpen)}]}),o.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:me(ge.disablePaused),settingName:"disable-paused-state-overlay",settingType:"boolean",defaultValue:!1}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ue()).InspectorMain.NodeIndicator.instance(),order:2,location:"main-toolbar-left"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ue()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",experiment:"outermost-target-selector"}),t.Toolbar.registerToolbarItem({loadItem:async()=>(await ue()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0,experiment:"outermost-target-selector"});const ye={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},he=e.i18n.registerUIStrings("panels/application/application-meta.ts",ye),Se=e.i18n.getLazilyComputedLocalizedString.bind(void 0,he);let ve;async function Ee(){return ve||(ve=await import("../../panels/application/application.js")),ve}t.ViewManager.registerViewExtension({location:"panel",id:"resources",title:Se(ye.application),commandPrompt:Se(ye.showApplication),order:70,loadView:async()=>(await Ee()).ResourcesPanel.ResourcesPanel.instance(),tags:[Se(ye.pwa)]}),t.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear",title:Se(ye.clearSiteData),loadActionDelegate:async()=>new((await Ee()).StorageView.ActionDelegate)}),t.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear-incl-third-party-cookies",title:Se(ye.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>new((await Ee()).StorageView.ActionDelegate)}),t.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===ve?[]:(e=>[e.BackgroundServiceView.BackgroundServiceView])(ve),loadActionDelegate:async()=>new((await Ee()).BackgroundServiceView.ActionDelegate),category:"BACKGROUND_SERVICES",options:[{value:!0,title:Se(ye.startRecordingEvents)},{value:!1,title:Se(ye.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),o.Revealer.registerRevealer({contextTypes:()=>[n.Resource.Resource],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Ee()).ResourcesPanel.ResourceRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[n.ResourceTreeModel.ResourceTreeFrame],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Ee()).ResourcesPanel.FrameDetailsRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[l.PreloadingForward.RuleSetView],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Ee()).ResourcesPanel.RuleSetViewRevealer)}),o.Revealer.registerRevealer({contextTypes:()=>[l.PreloadingForward.AttemptViewWithFilter],destination:o.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Ee()).ResourcesPanel.AttemptViewWithFilterRevealer)});const Re={issues:"Issues",showIssues:"Show Issues"},Ae=e.i18n.registerUIStrings("panels/issues/issues-meta.ts",Re),be=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ae);let Pe;async function fe(){return Pe||(Pe=await import("../../panels/issues/issues.js")),Pe}t.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:be(Re.issues),commandPrompt:be(Re.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await fe()).IssuesPane.IssuesPane)}),o.Revealer.registerRevealer({contextTypes:()=>[c.Issue.Issue],destination:o.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await fe()).IssueRevealer.IssueRevealer)});const Te={layers:"Layers",showLayers:"Show Layers"},ke=e.i18n.registerUIStrings("panels/layers/layers-meta.ts",Te),De=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ke);let xe;t.ViewManager.registerViewExtension({location:"panel",id:"layers",title:De(Te.layers),commandPrompt:De(Te.showLayers),order:100,persistence:"closeable",loadView:async()=>(await async function(){return xe||(xe=await import("../../panels/layers/layers.js")),xe}()).LayersPanel.LayersPanel.instance()});const Le={showLighthouse:"Show `Lighthouse`"},Me=e.i18n.registerUIStrings("panels/lighthouse/lighthouse-meta.ts",Le),Ie=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Me);let Ce;t.ViewManager.registerViewExtension({location:"panel",id:"lighthouse",title:e.i18n.lockedLazyString("Lighthouse"),commandPrompt:Ie(Le.showLighthouse),order:90,loadView:async()=>(await async function(){return Ce||(Ce=await import("../../panels/lighthouse/lighthouse.js")),Ce}()).LighthousePanel.LighthousePanel.instance(),tags:[e.i18n.lockedLazyString("lighthouse"),e.i18n.lockedLazyString("pwa")]});const Ne={media:"Media",video:"video",showMedia:"Show Media"},Ve=e.i18n.registerUIStrings("panels/media/media-meta.ts",Ne),Oe=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ve);let Be;t.ViewManager.registerViewExtension({location:"panel",id:"medias",title:Oe(Ne.media),commandPrompt:Oe(Ne.showMedia),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return Be||(Be=await import("../../panels/media/media.js")),Be}()).MainView.MainView),tags:[Oe(Ne.media),Oe(Ne.video)]});const ze={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},Ue=e.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",ze),We=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ue);let je;async function Fe(){return je||(je=await import("../../panels/mobile_throttling/mobile_throttling.js")),je}t.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:We(ze.throttling),commandPrompt:We(ze.showThrottling),order:35,loadView:async()=>new((await Fe()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:We(ze.goOffline),loadActionDelegate:async()=>new((await Fe()).ThrottlingManager.ActionDelegate),tags:[We(ze.device),We(ze.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:We(ze.enableSlowGThrottling),loadActionDelegate:async()=>new((await Fe()).ThrottlingManager.ActionDelegate),tags:[We(ze.device),We(ze.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:We(ze.enableFastGThrottling),loadActionDelegate:async()=>new((await Fe()).ThrottlingManager.ActionDelegate),tags:[We(ze.device),We(ze.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:We(ze.goOnline),loadActionDelegate:async()=>new((await Fe()).ThrottlingManager.ActionDelegate),tags:[We(ze.device),We(ze.throttlingTag)]}),o.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const _e={performanceMonitor:"Performance monitor",performance:"performance",systemMonitor:"system monitor",monitor:"monitor",activity:"activity",metrics:"metrics",showPerformanceMonitor:"Show Performance monitor"},qe=e.i18n.registerUIStrings("panels/performance_monitor/performance_monitor-meta.ts",_e),He=e.i18n.getLazilyComputedLocalizedString.bind(void 0,qe);let Ge;t.ViewManager.registerViewExtension({location:"drawer-view",id:"performance.monitor",title:He(_e.performanceMonitor),commandPrompt:He(_e.showPerformanceMonitor),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return Ge||(Ge=await import("../../panels/performance_monitor/performance_monitor.js")),Ge}()).PerformanceMonitor.PerformanceMonitorImpl),tags:[He(_e.performance),He(_e.systemMonitor),He(_e.monitor),He(_e.activity),He(_e.metrics)]});const Ke={performance:"Performance",showPerformance:"Show Performance",javascriptProfiler:"JavaScript Profiler",showJavascriptProfiler:"Show JavaScript Profiler",record:"Record",stop:"Stop",startProfilingAndReloadPage:"Start profiling and reload page",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view",startStopRecording:"Start/stop recording"},Ye=e.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",Ke),Je=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Ye);let Xe,Ze;async function Qe(){return Xe||(Xe=await import("../../panels/timeline/timeline.js")),Xe}async function $e(){return Ze||(Ze=await import("../../panels/profiler/profiler.js")),Ze}function et(e){return void 0===Xe?[]:e(Xe)}t.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:Je(Ke.performance),commandPrompt:Je(Ke.showPerformance),order:50,experiment:"enable-performance-panel",loadView:async()=>(await Qe()).TimelinePanel.TimelinePanel.instance()}),t.ViewManager.registerViewExtension({location:"panel",id:"js-profiler",title:Je(Ke.javascriptProfiler),commandPrompt:Je(Ke.showJavascriptProfiler),persistence:"permanent",order:65,experiment:"js-profiler-temporarily-enable",loadView:async()=>(await $e()).ProfilesPanel.JSProfilerPanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),options:[{value:!0,title:Je(Ke.record)},{value:!1,title:Je(Ke.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:Je(Ke.startProfilingAndReloadPage),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),title:Je(Ke.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),t.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),title:Je(Ke.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:Je(Ke.previousFrame),contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:Je(Ke.nextFrame),contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:Je(Ke.showRecentTimelineSessions),contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),title:Je(Ke.previousRecording),contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await Qe()).TimelinePanel.ActionDelegate),title:Je(Ke.nextRecording),contextTypes:()=>et((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),t.ActionRegistration.registerActionExtension({actionId:"profiler.js-toggle-recording",category:"JAVASCRIPT_PROFILER",title:Je(Ke.startStopRecording),iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===Ze?[]:(e=>[e.ProfilesPanel.JSProfilerPanel])(Ze),loadActionDelegate:async()=>(await $e()).ProfilesPanel.JSProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),o.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:Je(Ke.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),o.Linkifier.registerLinkifier({contextTypes:()=>et((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await Qe()).CLSLinkifier.Linkifier.instance()}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),t.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});const tt={webaudio:"WebAudio",audio:"audio",showWebaudio:"Show WebAudio"},ot=e.i18n.registerUIStrings("panels/web_audio/web_audio-meta.ts",tt),it=e.i18n.getLazilyComputedLocalizedString.bind(void 0,ot);let nt;t.ViewManager.registerViewExtension({location:"drawer-view",id:"web-audio",title:it(tt.webaudio),commandPrompt:it(tt.showWebaudio),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return nt||(nt=await import("../../panels/web_audio/web_audio.js")),nt}()).WebAudioView.WebAudioView),tags:[it(tt.audio)]});const at={webauthn:"WebAuthn",showWebauthn:"Show WebAuthn"},rt=e.i18n.registerUIStrings("panels/webauthn/webauthn-meta.ts",at),st=e.i18n.getLazilyComputedLocalizedString.bind(void 0,rt);let lt;t.ViewManager.registerViewExtension({location:"drawer-view",id:"webauthn-pane",title:st(at.webauthn),commandPrompt:st(at.showWebauthn),order:100,persistence:"closeable",loadView:async()=>new((await async function(){return lt||(lt=await import("../../panels/webauthn/webauthn.js")),lt}()).WebauthnPane.WebauthnPaneImpl)});const ct={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},dt=e.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",ct),gt=e.i18n.getLazilyComputedLocalizedString.bind(void 0,dt);t.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:"LAYERS",title:gt(ct.resetView),bindings:[{shortcut:"0"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:"LAYERS",title:gt(ct.switchToPanMode),bindings:[{shortcut:"x"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:"LAYERS",title:gt(ct.switchToRotateMode),bindings:[{shortcut:"v"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:"LAYERS",title:gt(ct.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:"LAYERS",title:gt(ct.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.up",category:"LAYERS",title:gt(ct.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.down",category:"LAYERS",title:gt(ct.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.left",category:"LAYERS",title:gt(ct.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.right",category:"LAYERS",title:gt(ct.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});const wt={recorder:"Recorder",showRecorder:"Show Recorder",startStopRecording:"Start/Stop recording",createRecording:"Create a new recording",replayRecording:"Replay recording",toggleCode:"Toggle code view"},mt=e.i18n.registerUIStrings("panels/recorder/recorder-meta.ts",wt),pt=e.i18n.getLazilyComputedLocalizedString.bind(void 0,mt);let ut;async function yt(){return ut||(ut=await import("../../panels/recorder/recorder.js")),ut}function ht(e,t){return void 0===ut?[]:t&&ut.RecorderPanel.RecorderPanel.instance().isActionPossible(t)?e(ut):[]}t.ViewManager.defaultOptionsForTabs.chrome_recorder=!0,t.ViewManager.registerViewExtension({location:"panel",id:"chrome-recorder",commandPrompt:pt(wt.showRecorder),title:pt(wt.recorder),order:90,persistence:"closeable",isPreviewFeature:!0,loadView:async()=>(await yt()).RecorderPanel.RecorderPanel.instance()}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.create-recording",title:pt(wt.createRecording),loadActionDelegate:async()=>new((await yt()).RecorderPanel.ActionDelegate)}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.start-recording",title:pt(wt.startStopRecording),contextTypes:()=>ht((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.start-recording"),loadActionDelegate:async()=>new((await yt()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.replay-recording",title:pt(wt.replayRecording),contextTypes:()=>ht((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.replay-recording"),loadActionDelegate:async()=>new((await yt()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+Enter",platform:"windows,linux"},{shortcut:"Meta+Enter",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.toggle-code-view",title:pt(wt.toggleCode),contextTypes:()=>ht((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.toggle-code-view"),loadActionDelegate:async()=>new((await yt()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+B",platform:"windows,linux"},{shortcut:"Meta+B",platform:"mac"}]}),self.runtime=i.Runtime.Runtime.instance({forceNew:!0}),new d.MainImpl.MainImpl;
