{"version": 3, "names": ["MISSING_CONTEXT_ERROR", "React", "createContext", "isDefault", "<PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON>", "getState", "setState", "getIsInitial"], "sourceRoot": "../../src", "sources": ["NavigationStateContext.tsx"], "mappings": ";;;;;;AACA;AAA+B;AAAA;AAE/B,MAAMA,qBAAqB,GACzB,wKAAwK;AAAC,4BAE5JC,KAAK,CAACC,aAAa,CAc/B;EACDC,SAAS,EAAE,IAAI;EAEf,IAAIC,MAAM,GAAQ;IAChB,MAAM,IAAIC,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIM,MAAM,GAAQ;IAChB,MAAM,IAAID,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIO,QAAQ,GAAQ;IAClB,MAAM,IAAIF,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIQ,QAAQ,GAAQ;IAClB,MAAM,IAAIH,KAAK,CAACL,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIS,YAAY,GAAQ;IACtB,MAAM,IAAIJ,KAAK,CAACL,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC;AAAA"}