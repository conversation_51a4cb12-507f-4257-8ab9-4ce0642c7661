{"name": "stream-buffers", "version": "2.2.0", "description": "Buffer-backed Streams for reading and writing.", "keywords": ["memory streams", "streams", "buffer streams"], "author": "Sam Day <<EMAIL>>", "main": "./lib/streambuffer.js", "engines": {"node": ">= 0.10.0"}, "dependencies": {}, "devDependencies": {"istanbul": "~0.3.2", "vows": ">= 0.5.6"}, "license": "Unlicense", "repository": {"type": "git", "url": "https://github.com/samcday/node-stream-buffer.git"}, "scripts": {"test": "[ -n \"$NO_COVERAGE\" ] && vows --spec || istanbul cover vows -- --spec"}}