import*as t from"../../core/i18n/i18n.js";import*as e from"../../core/sdk/sdk.js";import*as n from"../../core/common/common.js";import*as i from"../../core/host/host.js";import*as o from"../../core/platform/platform.js";import*as r from"../../ui/components/icon_button/icon_button.js";import*as s from"../../ui/legacy/legacy.js";import*as a from"../../ui/visual_logging/visual_logging.js";const l={noThrottling:"No throttling",noInternetConnectivity:"No internet connectivity",lowendMobile:"Low-end mobile",slowGXCpuSlowdown:"Slow 3G & 6x CPU slowdown",midtierMobile:"Mid-tier mobile",fastGXCpuSlowdown:"Fast 3G & 4x CPU slowdown",custom:"Custom",checkNetworkAndPerformancePanels:"Check Network and Performance panels"},d=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingPresets.ts",l),c=t.i18n.getLocalizedString.bind(void 0,d);class g{static getNoThrottlingConditions(){return{title:"function"==typeof e.NetworkManager.NoThrottlingConditions.title?e.NetworkManager.NoThrottlingConditions.title():e.NetworkManager.NoThrottlingConditions.title,description:c(l.noThrottling),network:e.NetworkManager.NoThrottlingConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,jslogContext:"no-throttling"}}static getOfflineConditions(){return{title:"function"==typeof e.NetworkManager.OfflineConditions.title?e.NetworkManager.OfflineConditions.title():e.NetworkManager.OfflineConditions.title,description:c(l.noInternetConnectivity),network:e.NetworkManager.OfflineConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,jslogContext:"offline"}}static getLowEndMobileConditions(){return{title:c(l.lowendMobile),description:c(l.slowGXCpuSlowdown),network:e.NetworkManager.Slow3GConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.LowEndMobile,jslogContext:"low-end-mobile"}}static getMidTierMobileConditions(){return{title:c(l.midtierMobile),description:c(l.fastGXCpuSlowdown),network:e.NetworkManager.Fast3GConditions,cpuThrottlingRate:e.CPUThrottlingManager.CPUThrottlingRates.MidTierMobile,jslogContext:"mid-tier-mobile"}}static getCustomConditions(){return{title:c(l.custom),description:c(l.checkNetworkAndPerformancePanels),jslogContext:"custom"}}static getMobilePresets(){return[g.getMidTierMobileConditions(),g.getLowEndMobileConditions(),g.getCustomConditions()]}static getAdvancedMobilePresets(){return[g.getOfflineConditions()]}static networkPresets=[e.NetworkManager.Fast3GConditions,e.NetworkManager.Slow3GConditions,e.NetworkManager.OfflineConditions];static cpuThrottlingPresets=[e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling,e.CPUThrottlingManager.CPUThrottlingRates.MidTierMobile,e.CPUThrottlingManager.CPUThrottlingRates.LowEndMobile]}globalThis.MobileThrottling=globalThis.MobileThrottling||{},globalThis.MobileThrottling.networkPresets=g.networkPresets;var h=Object.freeze({__proto__:null,ThrottlingPresets:g});const u={disabled:"Disabled",presets:"Presets",custom:"Custom"},p=t.i18n.registerUIStrings("panels/mobile_throttling/NetworkThrottlingSelector.ts",u),w=t.i18n.getLocalizedString.bind(void 0,p);class C{populateCallback;selectCallback;customNetworkConditionsSetting;options;constructor(t,n,i){this.populateCallback=t,this.selectCallback=n,this.customNetworkConditionsSetting=i,this.customNetworkConditionsSetting.addChangeListener(this.populateOptions,this),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(()=>{this.networkConditionsChanged()}),this),this.populateOptions()}revealAndUpdate(){n.Revealer.reveal(this.customNetworkConditionsSetting),this.networkConditionsChanged()}optionSelected(t){e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t)}populateOptions(){const t={title:w(u.disabled),items:[e.NetworkManager.NoThrottlingConditions]},n={title:w(u.presets),items:g.networkPresets},i={title:w(u.custom),items:this.customNetworkConditionsSetting.get()};if(this.options=this.populateCallback([t,n,i]),!this.networkConditionsChanged())for(let t=this.options.length-1;t>=0;t--)if(this.options[t]){this.optionSelected(this.options[t]);break}}networkConditionsChanged(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions();for(let n=0;n<this.options.length;++n){const i=this.options[n];if(i&&e.NetworkManager.networkConditionsEqual(t,i))return this.selectCallback(n),!0}return!1}}var k=Object.freeze({__proto__:null,NetworkThrottlingSelector:C});const m={sS:"{PH1}: {PH2}",add:"Add…",addS:"Add {PH1}",offline:"Offline",forceDisconnectedFromNetwork:"Force disconnected from network",throttling:"Throttling",cpuThrottlingIsEnabled:"CPU throttling is enabled",cpuThrottling:"CPU throttling",noThrottling:"No throttling",dSlowdown:"{PH1}× slowdown",excessConcurrency:"Exceeding the default value may degrade system performance.",resetConcurrency:"Reset to the default value",hardwareConcurrency:"Hardware concurrency",hardwareConcurrencySettingTooltip:"Override the value reported by navigator.hardwareConcurrency on the page",hardwareConcurrencyIsEnabled:"Hardware concurrency override is enabled"},b=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingManager.ts",m),T=t.i18n.getLocalizedString.bind(void 0,b);let f;class M{cpuThrottlingControls;cpuThrottlingRates;customNetworkConditionsSetting;currentNetworkThrottlingConditionsSetting;lastNetworkThrottlingConditions;cpuThrottlingManager;#t=!1;get hardwareConcurrencyOverrideEnabled(){return this.#t}constructor(){this.cpuThrottlingManager=e.CPUThrottlingManager.CPUThrottlingManager.instance(),this.cpuThrottlingControls=new Set,this.cpuThrottlingRates=g.cpuThrottlingPresets,this.customNetworkConditionsSetting=n.Settings.Settings.instance().moduleSetting("custom-network-conditions"),this.currentNetworkThrottlingConditionsSetting=n.Settings.Settings.instance().createSetting("preferred-network-condition",e.NetworkManager.NoThrottlingConditions),this.currentNetworkThrottlingConditionsSetting.setSerializer(new e.NetworkManager.ConditionsSerializer),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(()=>{this.lastNetworkThrottlingConditions=this.currentNetworkThrottlingConditionsSetting.get(),this.currentNetworkThrottlingConditionsSetting.set(e.NetworkManager.MultitargetNetworkManager.instance().networkConditions())})),this.isDirty()&&e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(this.currentNetworkThrottlingConditionsSetting.get())}static instance(t={forceNew:null}){const{forceNew:e}=t;return f&&!e||(f=new M),f}decorateSelectWithNetworkThrottling(t){let e=[];const n=new C((function(n){t.removeChildren(),e=[];for(let i=0;i<n.length;++i){const r=n[i],l=t.createChild("optgroup");l.label=r.title;for(const t of r.items){const n="function"==typeof t.title?t.title():t.title,i=new Option(n,n);s.ARIAUtils.setLabel(i,T(m.sS,{PH1:r.title,PH2:n})),i.setAttribute("jslog",`${a.item(o.StringUtilities.toKebabCase(t.i18nTitleKey||n)).track({click:!0})}`),l.appendChild(i),e.push(t)}if(i===n.length-1){const t=new Option(T(m.add),T(m.add));s.ARIAUtils.setLabel(t,T(m.addS,{PH1:r.title})),t.setAttribute("jslog",`${a.action("add").track({click:!0})}`),l.appendChild(t),e.push(null)}}return e}),(function(e){t.selectedIndex!==e&&(t.selectedIndex=e)}),this.customNetworkConditionsSetting);return t.setAttribute("jslog",`${a.dropDown().track({change:!0}).context(this.currentNetworkThrottlingConditionsSetting.name)}`),t.addEventListener("change",(function(){if(t.selectedIndex===t.options.length-1)n.revealAndUpdate();else{const i=e[t.selectedIndex];i&&n.optionSelected(i)}}),!1),n}createOfflineToolbarCheckbox(){const t=new s.Toolbar.ToolbarCheckbox(T(m.offline),T(m.forceDisconnectedFromNetwork),function(){if(t.checked())e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.OfflineConditions);else{const t=this.lastNetworkThrottlingConditions.download||this.lastNetworkThrottlingConditions.upload?this.lastNetworkThrottlingConditions:e.NetworkManager.NoThrottlingConditions;e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t)}}.bind(this));return t.element.setAttribute("jslog",`${a.toggle("disconnect-from-network").track({click:!0})}`),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",(function(){t.setChecked(e.NetworkManager.MultitargetNetworkManager.instance().isOffline())})),t.setChecked(e.NetworkManager.MultitargetNetworkManager.instance().isOffline()),t}createMobileThrottlingButton(){const t=new s.Toolbar.ToolbarMenuButton((function(t){for(let o=0;o<e.length;++o){const r=e[o];r&&(r.title===g.getCustomConditions().title&&r.description===g.getCustomConditions().description||t.defaultSection().appendCheckboxItem(r.title,i.optionSelected.bind(i,r),{checked:n===o,jslogContext:r.jslogContext}))}}),void 0,"mobile-throttling");t.setTitle(T(m.throttling)),t.setGlyph(""),t.turnIntoSelect(),t.setDarkText();let e=[],n=-1;const i=new y((function(t){e=[];for(const n of t){for(const t of n.items)e.push(t);e.push(null)}return e}),(function(i){n=i;const o=e[i];o&&(t.setText(o.title),t.setTitle(`${o.title} ${o.description}`))}));return t}updatePanelIcon(){const t=[];this.cpuThrottlingManager.cpuThrottlingRate()!==e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling&&t.push(T(m.cpuThrottlingIsEnabled)),this.hardwareConcurrencyOverrideEnabled&&t.push(T(m.hardwareConcurrencyIsEnabled)),s.InspectorView.InspectorView.instance().setPanelWarnings("timeline",t)}setCPUThrottlingRate(t){this.cpuThrottlingManager.setCPUThrottlingRate(t),t!==e.CPUThrottlingManager.CPUThrottlingRates.NoThrottling&&i.userMetrics.actionTaken(i.UserMetrics.Action.CpuThrottlingEnabled);const n=this.cpuThrottlingRates.indexOf(t);for(const t of this.cpuThrottlingControls)t.setSelectedIndex(n);this.updatePanelIcon()}createCPUThrottlingSelector(){const t=new s.Toolbar.ToolbarComboBox((t=>this.setCPUThrottlingRate(this.cpuThrottlingRates[t.target.selectedIndex])),T(m.cpuThrottling));this.cpuThrottlingControls.add(t);const e=this.cpuThrottlingManager.cpuThrottlingRate();for(let n=0;n<this.cpuThrottlingRates.length;++n){const i=this.cpuThrottlingRates[n],o=1===i?T(m.noThrottling):T(m.dSlowdown,{PH1:i}),r=t.createOption(o);t.addOption(r),e===i&&t.setSelectedIndex(n)}return t}createHardwareConcurrencySelector(){const t=new s.Toolbar.ToolbarItem(s.UIUtils.createInput("devtools-text-input","number"));t.setTitle(T(m.hardwareConcurrencySettingTooltip));const e=t.element;e.min="1",t.setEnabled(!1);const n=new s.Toolbar.ToolbarCheckbox(T(m.hardwareConcurrency),T(m.hardwareConcurrencySettingTooltip)),i=new s.Toolbar.ToolbarButton("Reset concurrency","undo");i.setTitle(T(m.resetConcurrency));const o=new r.Icon.Icon;o.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"};const a=new s.Toolbar.ToolbarItem(o);return a.setTitle(T(m.excessConcurrency)),n.inputElement.disabled=!0,i.element.classList.add("timeline-concurrency-hidden"),a.element.classList.add("timeline-concurrency-hidden"),this.cpuThrottlingManager.getHardwareConcurrency().then((o=>{if(void 0===o)return;const r=t=>{t>=1&&this.cpuThrottlingManager.setHardwareConcurrency(t),t>o?a.element.classList.remove("timeline-concurrency-hidden"):a.element.classList.add("timeline-concurrency-hidden"),t===o?i.element.classList.add("timeline-concurrency-hidden"):i.element.classList.remove("timeline-concurrency-hidden")};e.value=`${o}`,e.oninput=()=>r(Number(e.value)),n.inputElement.disabled=!1,n.inputElement.addEventListener("change",(()=>{this.#t=n.checked(),this.updatePanelIcon(),t.setEnabled(this.hardwareConcurrencyOverrideEnabled),r(this.hardwareConcurrencyOverrideEnabled?Number(e.value):o)})),i.addEventListener("Click",(()=>{e.value=`${o}`,r(o)}))})),{input:t,reset:i,warning:a,toggle:n}}setHardwareConcurrency(t){this.cpuThrottlingManager.setHardwareConcurrency(t)}isDirty(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),n=this.currentNetworkThrottlingConditionsSetting.get();return!e.NetworkManager.networkConditionsEqual(t,n)}}function v(){return M.instance()}var N=Object.freeze({__proto__:null,ThrottlingManager:M,ActionDelegate:class{handleAction(t,n){return"network-conditions.network-online"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.NoThrottlingConditions),!0):"network-conditions.network-low-end-mobile"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.Slow3GConditions),!0):"network-conditions.network-mid-tier-mobile"===n?(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.Fast3GConditions),!0):"network-conditions.network-offline"===n&&(e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(e.NetworkManager.OfflineConditions),!0)}},throttlingManager:v});const S={disabled:"Disabled",presets:"Presets",advanced:"Advanced"},x=t.i18n.registerUIStrings("panels/mobile_throttling/MobileThrottlingSelector.ts",S),P=t.i18n.getLocalizedString.bind(void 0,x);class y{populateCallback;selectCallback;options;constructor(t,n){this.populateCallback=t,this.selectCallback=n,e.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("RateChanged",this.conditionsChanged,this),e.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",this.conditionsChanged,this),this.options=this.populateOptions(),this.conditionsChanged()}optionSelected(t){e.NetworkManager.MultitargetNetworkManager.instance().setNetworkConditions(t.network),v().setCPUThrottlingRate(t.cpuThrottlingRate)}populateOptions(){const t={title:P(S.disabled),items:[g.getNoThrottlingConditions()]},e={title:P(S.presets),items:g.getMobilePresets()},n={title:P(S.advanced),items:g.getAdvancedMobilePresets()};return this.populateCallback([t,e,n])}conditionsChanged(){const t=e.NetworkManager.MultitargetNetworkManager.instance().networkConditions(),n=e.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate();for(let e=0;e<this.options.length;++e){const i=this.options[e];if(i&&"network"in i&&i.network===t&&i.cpuThrottlingRate===n)return void this.selectCallback(e)}const i=g.getCustomConditions();for(let t=0;t<this.options.length;++t){const e=this.options[t];if(e&&e.title===i.title&&e.description===i.description)return void this.selectCallback(t)}}}var I=Object.freeze({__proto__:null,MobileThrottlingSelector:y});const U={networkThrottlingIsEnabled:"Network throttling is enabled",requestsMayBeOverridden:"Requests may be overridden locally, see the Sources panel",requestsMayBeBlocked:"Requests may be blocked, see the Network request blocking panel",acceptedEncodingOverrideSet:"The set of accepted `Content-Encoding` headers has been modified by DevTools, see the Network conditions panel"},E=t.i18n.registerUIStrings("panels/mobile_throttling/NetworkPanelIndicator.ts",U),L=t.i18n.getLocalizedString.bind(void 0,E);var A=Object.freeze({__proto__:null,NetworkPanelIndicator:class{constructor(){if(!s.InspectorView.InspectorView.instance().hasPanel("network"))return;const t=e.NetworkManager.MultitargetNetworkManager.instance();function i(){const n=[];t.isThrottling()&&n.push(L(U.networkThrottlingIsEnabled)),e.NetworkManager.MultitargetNetworkManager.instance().isIntercepting()&&n.push(L(U.requestsMayBeOverridden)),t.isBlocking()&&n.push(L(U.requestsMayBeBlocked)),t.isAcceptedEncodingOverrideSet()&&n.push(L(U.acceptedEncodingOverrideSet)),s.InspectorView.InspectorView.instance().setPanelWarnings("network",n)}t.addEventListener("ConditionsChanged",i),t.addEventListener("BlockedPatternsChanged",i),t.addEventListener("InterceptorsChanged",i),t.addEventListener("AcceptedEncodingsChanged",i),n.Settings.Settings.instance().moduleSetting("cache-disabled").addChangeListener(i,this),i()}}});const R=new CSSStyleSheet;R.replaceSync(":host{overflow:hidden}.header{padding:0 0 6px;border-bottom:1px solid var(--sys-color-divider);font-size:18px;font-weight:normal;flex:none}.add-conditions-button{flex:none;margin:10px 2px;min-width:140px;align-self:flex-start}.conditions-list{max-width:500px;min-width:340px;flex:auto}.conditions-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.conditions-list-text{white-space:nowrap;text-overflow:ellipsis;flex:0 0 70px;user-select:none;color:var(--sys-color-on-surface);text-align:end;position:relative}.conditions-list-text:last-child{flex-basis:140px;text-align:left}.conditions-edit-row .conditions-list-text:last-child{text-align:right}.conditions-list-title{text-align:start;display:flex;flex:auto;align-items:flex-start}.conditions-list-title-text{overflow:hidden;flex:auto;white-space:nowrap;text-overflow:ellipsis}.conditions-list-separator{flex:0 0 1px;background-color:var(--sys-color-divider);height:30px;margin:0 4px}.conditions-list-separator-invisible{visibility:hidden;height:100%!important}.conditions-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px}.conditions-edit-row input{width:100%;text-align:inherit}.conditions-edit-optional{position:absolute;bottom:-20px;right:0;color:var(--sys-color-state-disabled)}.editor-buttons{margin-top:10px}\n/*# sourceURL=throttlingSettingsTab.css */\n");const O={networkThrottlingProfiles:"Network Throttling Profiles",addCustomProfile:"Add custom profile...",dms:"{PH1} `ms`",profileName:"Profile Name",download:"Download",upload:"Upload",latency:"Latency",optional:"optional",profileNameCharactersLengthMust:"Profile Name characters length must be between 1 to {PH1} inclusive",sMustBeANumberBetweenSkbsToSkbs:"{PH1} must be a number between {PH2} `kbit/s` to {PH3} `kbit/s` inclusive",latencyMustBeAnIntegerBetweenSms:"Latency must be an integer between {PH1} `ms` to {PH2} `ms` inclusive",dskbits:"{PH1} `kbit/s`",fsmbits:"{PH1} `Mbit/s`"},H=t.i18n.registerUIStrings("panels/mobile_throttling/ThrottlingSettingsTab.ts",O),_=t.i18n.getLocalizedString.bind(void 0,H);class j extends s.Widget.VBox{list;customSetting;editor;constructor(){super(!0),this.element.setAttribute("jslog",`${a.pane("throttling-conditions")}`);const t=this.contentElement.createChild("div","header");t.textContent=_(O.networkThrottlingProfiles),s.ARIAUtils.markAsHeading(t,1);const e=s.UIUtils.createTextButton(_(O.addCustomProfile),this.addButtonClicked.bind(this),{className:"add-conditions-button",jslogContext:"network.add-conditions"});this.contentElement.appendChild(e),this.list=new s.ListWidget.ListWidget(this),this.list.element.classList.add("conditions-list"),this.list.show(this.contentElement),this.customSetting=n.Settings.Settings.instance().moduleSetting("custom-network-conditions"),this.customSetting.addChangeListener(this.conditionsUpdated,this),this.setDefaultFocusedElement(e)}wasShown(){super.wasShown(),this.list.registerCSSFiles([R]),this.registerCSSFiles([R]),this.conditionsUpdated()}conditionsUpdated(){this.list.clear();const t=this.customSetting.get();for(let e=0;e<t.length;++e)this.list.appendItem(t[e],!0);this.list.appendSeparator()}addButtonClicked(){this.list.addNewItem(this.customSetting.get().length,{title:()=>"",download:-1,upload:-1,latency:0})}renderItem(t,e){const n=document.createElement("div");n.classList.add("conditions-list-item");const i=n.createChild("div","conditions-list-text conditions-list-title").createChild("div","conditions-list-title-text"),o=this.retrieveOptionsTitle(t);return i.textContent=o,s.Tooltip.Tooltip.install(i,o),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=B(t.download),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=B(t.upload),n.createChild("div","conditions-list-separator"),n.createChild("div","conditions-list-text").textContent=_(O.dms,{PH1:t.latency}),n}removeItemRequested(t,e){const n=this.customSetting.get();n.splice(e,1),this.customSetting.set(n)}retrieveOptionsTitle(t){return"function"==typeof t.title?t.title():t.title}commitEdit(t,e,n){t.title=e.control("title").value.trim();const i=e.control("download").value.trim();t.download=i?125*parseInt(i,10):-1;const o=e.control("upload").value.trim();t.upload=o?125*parseInt(o,10):-1;const r=e.control("latency").value.trim();t.latency=r?parseInt(r,10):0;const s=this.customSetting.get();n&&s.push(t),this.customSetting.set(s)}beginEdit(t){const e=this.createEditor();return e.control("title").value=this.retrieveOptionsTitle(t),e.control("download").value=t.download<=0?"":String(t.download/125),e.control("upload").value=t.upload<=0?"":String(t.upload/125),e.control("latency").value=t.latency?String(t.latency):"",e}createEditor(){if(this.editor)return this.editor;const e=new s.ListWidget.Editor;this.editor=e;const n=e.contentElement(),i=n.createChild("div","conditions-edit-row"),o=i.createChild("div","conditions-list-text conditions-list-title"),r=_(O.profileName);o.createChild("div","conditions-list-title-text").textContent=r,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const a=i.createChild("div","conditions-list-text"),l=_(O.download);a.createChild("div","conditions-list-title-text").textContent=l,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const d=i.createChild("div","conditions-list-text").createChild("div","conditions-list-title-text"),c=_(O.upload);d.textContent=c,i.createChild("div","conditions-list-separator conditions-list-separator-invisible");const g=i.createChild("div","conditions-list-text"),h=_(O.latency);g.createChild("div","conditions-list-title-text").textContent=h;const u=n.createChild("div","conditions-edit-row"),p=e.createInput("title","text","",(function(t,e,n){const i=n.value.trim(),o=i.length>0&&i.length<=49;if(!o){return{valid:o,errorMessage:_(O.profileNameCharactersLengthMust,{PH1:49})}}return{valid:o,errorMessage:void 0}}));s.ARIAUtils.setLabel(p,r),u.createChild("div","conditions-list-text conditions-list-title").appendChild(p),u.createChild("div","conditions-list-separator conditions-list-separator-invisible");let w=u.createChild("div","conditions-list-text");const C=e.createInput("download","text",t.i18n.lockedString("kbit/s"),f);w.appendChild(C),s.ARIAUtils.setLabel(C,l);const k=w.createChild("div","conditions-edit-optional"),m=_(O.optional);k.textContent=m,s.ARIAUtils.setDescription(C,m),u.createChild("div","conditions-list-separator conditions-list-separator-invisible"),w=u.createChild("div","conditions-list-text");const b=e.createInput("upload","text",t.i18n.lockedString("kbit/s"),f);s.ARIAUtils.setLabel(b,c),w.appendChild(b);w.createChild("div","conditions-edit-optional").textContent=m,s.ARIAUtils.setDescription(b,m),u.createChild("div","conditions-list-separator conditions-list-separator-invisible"),w=u.createChild("div","conditions-list-text");const T=e.createInput("latency","text",t.i18n.lockedString("ms"),(function(t,e,n){const i=1e6,o=n.value.trim(),r=Number(o),s=Number.isInteger(r)&&r>=0&&r<=i;if(!s){return{valid:s,errorMessage:_(O.latencyMustBeAnIntegerBetweenSms,{PH1:0,PH2:i})}}return{valid:s,errorMessage:void 0}}));s.ARIAUtils.setLabel(T,h),w.appendChild(T);return w.createChild("div","conditions-edit-optional").textContent=m,s.ARIAUtils.setDescription(T,m),e;function f(t,e,n){const i=1e7,o=n.value.trim(),r=Number(o),s=n.getAttribute("aria-label"),a=!Number.isNaN(r)&&r>=0&&r<=i;if(!a){return{valid:a,errorMessage:_(O.sMustBeANumberBetweenSkbsToSkbs,{PH1:String(s),PH2:0,PH3:i})}}return{valid:a,errorMessage:void 0}}}}function B(t){if(t<0)return"";const e=t/125;if(e<1e3)return _(O.dskbits,{PH1:e});if(e<1e4){const t=(e/1e3).toFixed(1);return _(O.fsmbits,{PH1:t})}return _(O.fsmbits,{PH1:e/1e3|0})}var z=Object.freeze({__proto__:null,ThrottlingSettingsTab:j});export{I as MobileThrottlingSelector,A as NetworkPanelIndicator,k as NetworkThrottlingSelector,N as ThrottlingManager,h as ThrottlingPresets,z as ThrottlingSettingsTab};
