<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Sélection Client et Contrat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .readonly-input {
            background-color: #f0fdf4 !important;
            border: 2px solid #10b981 !important;
            color: #059669 !important;
            font-weight: bold !important;
            cursor: not-allowed !important;
        }
        .contract-auto {
            background-color: #f0fdf4 !important;
            border: 2px solid #10b981 !important;
        }
        .contract-manual {
            background-color: #f0f8ff !important;
            border: 2px solid #3b82f6 !important;
        }
        .contract-error {
            background-color: #fef2f2 !important;
            border: 2px solid #dc2626 !important;
        }
        .status {
            margin-top: 8px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background-color: #f0fdf4; color: #059669; }
        .info { background-color: #f0f8ff; color: #3b82f6; }
        .error { background-color: #fef2f2; color: #dc2626; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>🧪 Test Final - Sélection Client et Contrat</h1>
    
    <div class="form-container">
        <h2>📋 Formulaire de Consommation</h2>
        
        <div class="form-group">
            <label>Période (YYYY-MM) *</label>
            <input type="month" value="2025-01" />
        </div>
        
        <div class="form-group">
            <label>Client *</label>
            <select id="clientSelect">
                <option value="">Sélectionner un client</option>
            </select>
            <div id="clientStatus"></div>
        </div>
        
        <div class="form-group">
            <label>Contrat *</label>
            <select id="contractSelect" disabled>
                <option value="">Sélectionnez d'abord un client</option>
            </select>
            <div id="contractStatus"></div>
        </div>
        
        <div class="form-group">
            <label>Consommation Précédente (m³)</label>
            <input type="number" id="prevConsumption" readonly placeholder="Auto-rempli depuis la base" />
        </div>
        
        <div class="form-group">
            <label>Consommation Actuelle (m³) *</label>
            <input type="number" step="0.1" placeholder="Saisir la nouvelle consommation" />
        </div>
    </div>
    
    <div class="log" id="testLog"></div>

    <script>
        const API_BASE_URL = 'http://localhost:3002';
        let clients = [];
        let filteredContracts = [];
        let selectedClientFromList = null; // Simule la sélection depuis la liste
        
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        async function loadClients() {
            try {
                log('🔄 Chargement des clients depuis la table Client...');
                const response = await fetch(`${API_BASE_URL}/api/clients`);
                const data = await response.json();
                
                if (data.success) {
                    clients = data.data;
                    const clientSelect = document.getElementById('clientSelect');
                    clientSelect.innerHTML = '<option value="">Sélectionner un client</option>';
                    
                    clients.forEach(client => {
                        const option = document.createElement('option');
                        option.value = client.idclient;
                        option.textContent = `${client.nom} ${client.prenom} - ${client.ville}`;
                        clientSelect.appendChild(option);
                    });
                    
                    log(`✅ ${clients.length} clients chargés depuis la table Client`);
                } else {
                    log('❌ Erreur lors du chargement des clients');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function handleClientChange(clientId) {
            log(`🔄 Client sélectionné: ${clientId}`);
            
            const contractSelect = document.getElementById('contractSelect');
            const contractStatus = document.getElementById('contractStatus');
            const prevConsumption = document.getElementById('prevConsumption');
            
            if (!clientId) {
                contractSelect.innerHTML = '<option value="">Sélectionnez d\'abord un client</option>';
                contractSelect.disabled = true;
                contractSelect.className = '';
                contractStatus.innerHTML = '';
                prevConsumption.value = '';
                return;
            }
            
            try {
                log(`🔍 Récupération des contrats pour le client ${clientId} depuis la table Contract...`);
                const response = await fetch(`${API_BASE_URL}/api/clients/${clientId}/contracts`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    filteredContracts = data.data;
                    contractSelect.innerHTML = '';
                    contractSelect.disabled = false;
                    
                    log(`📋 ${filteredContracts.length} contrat(s) trouvé(s) dans la table Contract`);
                    
                    if (filteredContracts.length === 0) {
                        // ❌ AUCUN CONTRAT
                        contractSelect.innerHTML = '<option value="">Ce client n\'a pas de contrat</option>';
                        contractSelect.disabled = true;
                        contractSelect.className = 'contract-error';
                        contractStatus.innerHTML = '<div class="status error">❌ Ce client n\'a pas de contrat</div>';
                        log('❌ CLIENT SANS CONTRAT: Impossible de créer une consommation');
                        
                    } else if (filteredContracts.length === 1) {
                        // ✅ UN SEUL CONTRAT - AFFICHAGE AUTOMATIQUE
                        const contract = filteredContracts[0];
                        contractSelect.innerHTML = `<option value="${contract.idcontract}" selected>
                            ${contract.codeqr || 'QR non défini'} - Contrat #${contract.idcontract}
                            ${contract.marquecompteur ? ` (${contract.marquecompteur})` : ''}
                        </option>`;
                        contractSelect.disabled = true;
                        contractSelect.className = 'contract-auto';
                        contractStatus.innerHTML = '<div class="status success">✅ Contrat unique affiché automatiquement</div>';
                        
                        // Récupérer la dernière consommation
                        await fetchLastConsommation(contract.idcontract);
                        log(`✅ CONTRAT UNIQUE: ${contract.codeqr} affiché par défaut`);
                        
                    } else {
                        // 📋 PLUSIEURS CONTRATS - SÉLECTION MANUELLE
                        contractSelect.innerHTML = '<option value="">Sélectionner un contrat</option>';
                        filteredContracts.forEach(contract => {
                            const option = document.createElement('option');
                            option.value = contract.idcontract;
                            option.textContent = `${contract.codeqr || 'QR non défini'} - Contrat #${contract.idcontract}${contract.marquecompteur ? ` (${contract.marquecompteur})` : ''}`;
                            contractSelect.appendChild(option);
                        });
                        contractSelect.className = 'contract-manual';
                        contractStatus.innerHTML = `<div class="status info">📋 ${filteredContracts.length} contrats disponibles - Sélectionnez-en un</div>`;
                        log(`📋 PLUSIEURS CONTRATS: ${filteredContracts.length} contrats disponibles pour sélection`);
                    }
                } else {
                    log('❌ Erreur dans la réponse API');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function fetchLastConsommation(contractId) {
            try {
                log(`🔍 Récupération de la dernière consommation pour le contrat ${contractId}...`);
                const response = await fetch(`${API_BASE_URL}/api/contracts/${contractId}/last-consommation`);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        const prevConsumption = document.getElementById('prevConsumption');
                        prevConsumption.value = data.data.consommationactuelle;
                        log(`✅ Dernière consommation trouvée: ${data.data.consommationactuelle} m³`);
                    } else {
                        log('ℹ️ Aucune consommation précédente trouvée');
                    }
                } else {
                    log('⚠️ Erreur lors de la récupération de la dernière consommation');
                }
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        // Simuler la sélection d'un client depuis la liste
        function simulateClientFromList(clientId) {
            const client = clients.find(c => c.idclient == clientId);
            if (client) {
                selectedClientFromList = client;
                
                // Transformer le dropdown en champ lecture seule
                const clientSelect = document.getElementById('clientSelect');
                const clientStatus = document.getElementById('clientStatus');
                
                // Créer un input readonly
                const readonlyInput = document.createElement('input');
                readonlyInput.type = 'text';
                readonlyInput.value = `${client.nom} ${client.prenom} - ${client.ville}`;
                readonlyInput.className = 'readonly-input';
                readonlyInput.readOnly = true;
                
                // Remplacer le select par l'input
                clientSelect.parentNode.replaceChild(readonlyInput, clientSelect);
                
                clientStatus.innerHTML = '<div class="status success">✅ Client sélectionné depuis la liste (non modifiable)</div>';
                
                log(`🎯 CLIENT SÉLECTIONNÉ DEPUIS LA LISTE: ${client.nom} ${client.prenom}`);
                
                // Charger automatiquement les contrats
                handleClientChange(clientId);
            }
        }
        
        // Event listeners
        document.getElementById('clientSelect').addEventListener('change', (e) => {
            handleClientChange(e.target.value);
        });
        
        document.getElementById('contractSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                fetchLastConsommation(e.target.value);
                const contractStatus = document.getElementById('contractStatus');
                contractStatus.innerHTML = '<div class="status success">✅ Contrat sélectionné manuellement</div>';
            }
        });
        
        // Initialisation
        loadClients();
        log('🚀 Test de sélection client/contrat initialisé');
        
        // Ajouter des boutons de test après le chargement
        setTimeout(() => {
            if (clients.length > 0) {
                log('\n🧪 TESTS DISPONIBLES:');
                log('- Tapez simulateClientFromList(15) pour tester un client avec contrat');
                log('- Ou sélectionnez manuellement un client dans le dropdown');
            }
        }, 2000);
    </script>
</body>
</html>
