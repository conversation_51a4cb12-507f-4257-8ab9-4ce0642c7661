// Resolve `expo` > `expo-modules-autolinking` dependency chain
def autolinkingPath = ["node", "--print", "require.resolve('expo-modules-autolinking/package.json', { paths: [require.resolve('expo/package.json')] })"]
apply from: new File(
  providers.exec {
    workingDir(rootDir)
    commandLine(autolinkingPath)
  }.standardOutput.asText.get().trim(), 
  "../scripts/android/autolinking_implementation.gradle"
)
