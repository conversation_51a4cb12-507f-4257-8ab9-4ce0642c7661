{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireDefault", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "ScreenStackHeaderBackButtonImage", "props", "createElement", "View", "Image", "resizeMode", "fadeDuration", "exports", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA0B,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAOnB,MAAMQ,gCAAgC,GAC3CC,KAAiB,iBAEjBpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,qBACHtB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAyB,KAAK,EAAAlB,QAAA;EAACmB,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKL,KAAK,CAAG,CACpD,CACP;AAACM,OAAA,CAAAP,gCAAA,GAAAA,gCAAA;AAEK,MAAMQ,0BAA0B,GACrCP,KAAyC,iBACzBpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAC,0BAAA,GAAAA,0BAAA;AAE/B,MAAMC,yBAAyB,GACpCR,KAAyC,iBACzBpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAE,yBAAA,GAAAA,yBAAA;AAE/B,MAAMC,2BAA2B,GACtCT,KAAyC,iBACzBpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAG,2BAAA,GAAAA,2BAAA;AAE/B,MAAMC,8BAA8B,GACzCV,KAA2D,iBAC3CpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAI,8BAAA,GAAAA,8BAAA;AAE/B,MAAMC,uBAAuB,GAClCX,KAA4D,iBAC5CpB,MAAA,CAAAI,OAAA,CAAAiB,aAAA,CAACvB,YAAA,CAAAwB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAK,uBAAA,GAAAA,uBAAA;AAE/B,MAAMC,wBAEZ,GAAAN,OAAA,CAAAM,wBAAA,GAAGV,iBAAI"}