# Script PowerShell pour démarrer AquaTrack
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AQUATRACK - APPLICATION MOBILE" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Prérequis :" -ForegroundColor Yellow
Write-Host "  ✓ Serveur backend sur le port 3007" -ForegroundColor Green
Write-Host "  ✓ Téléphone et PC sur le même WiFi" -ForegroundColor Green
Write-Host "  ✓ Application 'Expo Go' installée" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Démarrage de l'application..." -ForegroundColor Blue
Write-Host ""

# Démarrer Expo
& npx expo start --offline

Write-Host ""
Write-Host "📱 SCANNEZ LE QR CODE CI-DESSUS AVEC EXPO GO !" -ForegroundColor Magenta
Write-Host ""
Read-Host "Appuyez sur Entrée pour fermer"
