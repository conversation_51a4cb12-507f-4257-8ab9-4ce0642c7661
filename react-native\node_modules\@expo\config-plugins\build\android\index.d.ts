import * as AllowBackup from './AllowBackup';
import * as BuildProperties from './BuildProperties';
import * as Colors from './Colors';
import * as EasBuild from './EasBuild';
import * as GoogleMapsApiKey from './GoogleMapsApiKey';
import * as GoogleServices from './GoogleServices';
import * as IntentFilters from './IntentFilters';
import * as Locales from './Locales';
import * as Manifest from './Manifest';
import * as Name from './Name';
import * as Orientation from './Orientation';
import * as Package from './Package';
import * as Paths from './Paths';
import * as Permissions from './Permissions';
import * as PrimaryColor from './PrimaryColor';
import * as Properties from './Properties';
import * as Resources from './Resources';
import * as Scheme from './Scheme';
import * as StatusBar from './StatusBar';
import * as Strings from './Strings';
import * as Styles from './Styles';
import * as Updates from './Updates';
import * as Version from './Version';
import * as WindowSoftInputMode from './WindowSoftInputMode';
export { Manifest, Colors, Paths, Permissions, Properties, Resources, Scheme, Strings, Styles };
export { AllowBackup, BuildProperties, EasBuild, GoogleMapsApiKey, GoogleServices, IntentFilters, Name, Locales, Orientation, Package, PrimaryColor, StatusBar, Updates, Version, WindowSoftInputMode, };
