{"version": 3, "file": "withAndroidSplashScreen.js", "names": ["_configPlugins", "data", "require", "_getAndroidSplashConfig", "_withAndroidSplashDrawables", "_withAndroidSplashImages", "_withAndroidSplashMainActivity", "_withAndroidSplashStrings", "_withAndroidSplashStyles", "withAndroidSplashScreen", "config", "props", "isLegacyConfig", "undefined", "splashConfig", "getAndroidSplashConfig", "backgroundColor", "androidStatusBar", "toLowerCase", "WarningAggregator", "addWarningAndroid", "with<PERSON><PERSON><PERSON>", "withAndroidSplashMainActivity", "withAndroidSplashImages", "withAndroidSplashDrawables", "withAndroidSplashStyles", "withAndroidSplashStrings", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashScreen.ts"], "sourcesContent": ["import { ConfigPlugin, WarningAggregator, withPlugins } from '@expo/config-plugins';\n\nimport { AndroidSplashConfig, getAndroidSplashConfig } from './getAndroidSplashConfig';\nimport { withAndroidSplashDrawables } from './withAndroidSplashDrawables';\nimport { withAndroidSplashImages } from './withAndroidSplashImages';\nimport { withAndroidSplashMainActivity } from './withAndroidSplashMainActivity';\nimport { withAndroidSplashStrings } from './withAndroidSplashStrings';\nimport { withAndroidSplashStyles } from './withAndroidSplashStyles';\n\nexport const withAndroidSplashScreen: ConfigPlugin<\n  AndroidSplashConfig | undefined | null | void\n> = (config, props) => {\n  const isLegacyConfig = props === undefined;\n  const splashConfig = getAndroidSplashConfig(config, props ?? null);\n\n  // Update the android status bar to match the splash screen\n  // androidStatusBar applies info to the app activity style.\n  const backgroundColor = splashConfig?.backgroundColor || '#ffffff';\n  if (config.androidStatusBar?.backgroundColor) {\n    if (\n      backgroundColor.toLowerCase() !== config.androidStatusBar?.backgroundColor?.toLowerCase?.()\n    ) {\n      WarningAggregator.addWarningAndroid(\n        'androidStatusBar.backgroundColor',\n        'Color conflicts with the splash.backgroundColor'\n      );\n    }\n  } else {\n    if (!config.androidStatusBar) config.androidStatusBar = {};\n    config.androidStatusBar.backgroundColor = backgroundColor;\n  }\n\n  return withPlugins(config, [\n    [withAndroidSplashMainActivity, { isLegacyConfig }],\n    [withAndroidSplashImages, splashConfig],\n    [withAndroidSplashDrawables, splashConfig],\n    [withAndroidSplashStyles, { splashConfig, isLegacyConfig }],\n    [withAndroidSplashStrings, splashConfig],\n  ]);\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,wBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,uBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,4BAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,2BAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,yBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,wBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,+BAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,8BAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,0BAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,yBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,yBAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,wBAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMQ,uBAEZ,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACrB,MAAMC,cAAc,GAAGD,KAAK,KAAKE,SAAS;EAC1C,MAAMC,YAAY,GAAG,IAAAC,gDAAsB,EAACL,MAAM,EAAEC,KAAK,IAAI,IAAI,CAAC;;EAElE;EACA;EACA,MAAMK,eAAe,GAAGF,YAAY,EAAEE,eAAe,IAAI,SAAS;EAClE,IAAIN,MAAM,CAACO,gBAAgB,EAAED,eAAe,EAAE;IAC5C,IACEA,eAAe,CAACE,WAAW,CAAC,CAAC,KAAKR,MAAM,CAACO,gBAAgB,EAAED,eAAe,EAAEE,WAAW,GAAG,CAAC,EAC3F;MACAC,kCAAiB,CAACC,iBAAiB,CACjC,kCAAkC,EAClC,iDACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI,CAACV,MAAM,CAACO,gBAAgB,EAAEP,MAAM,CAACO,gBAAgB,GAAG,CAAC,CAAC;IAC1DP,MAAM,CAACO,gBAAgB,CAACD,eAAe,GAAGA,eAAe;EAC3D;EAEA,OAAO,IAAAK,4BAAW,EAACX,MAAM,EAAE,CACzB,CAACY,8DAA6B,EAAE;IAAEV;EAAe,CAAC,CAAC,EACnD,CAACW,kDAAuB,EAAET,YAAY,CAAC,EACvC,CAACU,wDAA0B,EAAEV,YAAY,CAAC,EAC1C,CAACW,kDAAuB,EAAE;IAAEX,YAAY;IAAEF;EAAe,CAAC,CAAC,EAC3D,CAACc,oDAAwB,EAAEZ,YAAY,CAAC,CACzC,CAAC;AACJ,CAAC;AAACa,OAAA,CAAAlB,uBAAA,GAAAA,uBAAA", "ignoreList": []}