{"version": 3, "file": "useNavigationState.d.ts", "sourceRoot": "", "sources": ["../../../src/useNavigationState.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAMhF,KAAK,QAAQ,CAAC,SAAS,SAAS,aAAa,EAAE,CAAC,IAAI,CAClD,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,KAC9B,CAAC,CAAC;AAEP;;;;GAIG;AACH,MAAM,CAAC,OAAO,UAAU,kBAAkB,CAAC,SAAS,SAAS,aAAa,EAAE,CAAC,EAC3E,QAAQ,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,GAC/B,CAAC,CAuBH"}