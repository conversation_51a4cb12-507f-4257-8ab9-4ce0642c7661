{"version": 3, "sources": ["../../../../src/export/embed/exportEager.ts"], "sourcesContent": ["import { exportEmbedInternalAsync } from './exportEmbedAsync';\nimport { getExportEmbedOptionsKey, resolveEagerOptionsAsync } from './resolveOptions';\nimport { env } from '../../utils/env';\n\nconst debug = require('debug')('expo:eager');\n\nexport async function exportEagerAsync(\n  projectRoot: string,\n  {\n    dev,\n    platform,\n    // We default to resetting the cache in non-CI environments since prebundling overwrites the cache reset later.\n    resetCache = !env.CI,\n    assetsDest,\n    bundleOutput,\n  }: {\n    dev: boolean;\n    platform: string;\n    resetCache?: boolean;\n    assetsDest?: string;\n    bundleOutput?: string;\n  }\n) {\n  const options = await resolveEagerOptionsAsync(projectRoot, {\n    dev,\n    platform,\n    resetCache,\n    assetsDest,\n    bundleOutput,\n  });\n  debug('Starting eager export: ' + options.bundleOutput);\n\n  await exportEmbedInternalAsync(projectRoot, options);\n\n  debug('Eager export complete');\n\n  return { options, key: getExportEmbedOptionsKey(options) };\n}\n"], "names": ["exportEagerAsync", "debug", "require", "projectRoot", "dev", "platform", "resetCache", "env", "CI", "assetsDest", "bundleOutput", "options", "resolveEagerOptionsAsync", "exportEmbedInternalAsync", "key", "getExportEmbedOptionsKey"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;kCANmB;gCAC0B;qBAC/C;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,iBACpBG,WAAmB,EACnB,EACEC,GAAG,EACHC,QAAQ,EACR,+GAA+G;AAC/GC,aAAa,CAACC,QAAG,CAACC,EAAE,EACpBC,UAAU,EACVC,YAAY,EAOb;IAED,MAAMC,UAAU,MAAMC,IAAAA,wCAAwB,EAACT,aAAa;QAC1DC;QACAC;QACAC;QACAG;QACAC;IACF;IACAT,MAAM,4BAA4BU,QAAQD,YAAY;IAEtD,MAAMG,IAAAA,0CAAwB,EAACV,aAAaQ;IAE5CV,MAAM;IAEN,OAAO;QAAEU;QAASG,KAAKC,IAAAA,wCAAwB,EAACJ;IAAS;AAC3D"}