{"version": 3, "names": ["isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "logger", "measure", "measureFabric", "animatedRef", "_WORKLET", "viewTag", "warn", "measured", "global", "_measureFabric", "x", "isNaN", "measurePaper", "_measurePaper", "measureJest", "measureChromeDebugger", "measureDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/measure.ts"], "mappings": "AAAA,YAAY;;AAEZ,SACEA,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,uBAAoB;AAO3B,SAASC,MAAM,QAAQ,oBAAW;AAMlC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAgB;AAE3B,SAASC,aAAaA,CAACC,WAA8C,EAAE;EACrE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBL,MAAM,CAACM,IAAI,CACT,qBAAqBD,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAME,QAAQ,GAAGC,MAAM,CAACC,cAAc,CAAEJ,OAA4B,CAAC;EACrE,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrBP,MAAM,CAACM,IAAI,CACT,kNACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIC,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCV,MAAM,CAACM,IAAI,CACT,qGACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BV,MAAM,CAACM,IAAI,CACT,qHACF,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOC,QAAQ;EACjB;AACF;AAEA,SAASK,YAAYA,CAACT,WAA8C,EAAE;EACpE,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMC,OAAO,GAAGF,WAAW,CAAC,CAAC;EAC7B,IAAIE,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBL,MAAM,CAACM,IAAI,CACT,qBAAqBD,OAAO,4JAC9B,CAAC;IACD,OAAO,IAAI;EACb;EAEA,MAAME,QAAQ,GAAGC,MAAM,CAACK,aAAa,CAAER,OAAiB,CAAC;EACzD,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrBP,MAAM,CAACM,IAAI,CACT,qBACED,OAAO,0MAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIE,QAAQ,CAACG,CAAC,KAAK,CAAC,OAAO,EAAE;IAClCV,MAAM,CAACM,IAAI,CACT,qBACED,OAAO,6FAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM,IAAIM,KAAK,CAACJ,QAAQ,CAACG,CAAC,CAAC,EAAE;IAC5BV,MAAM,CAACM,IAAI,CACT,qBACED,OAAO,6GAEX,CAAC;IACD,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOE,QAAQ;EACjB;AACF;AAEA,SAASO,WAAWA,CAAA,EAAG;EACrBd,MAAM,CAACM,IAAI,CAAC,qCAAqC,CAAC;EAClD,OAAO,IAAI;AACb;AAEA,SAASS,qBAAqBA,CAAA,EAAG;EAC/Bf,MAAM,CAACM,IAAI,CAAC,gDAAgD,CAAC;EAC7D,OAAO,IAAI;AACb;AAEA,SAASU,cAAcA,CAAA,EAAG;EACxBhB,MAAM,CAACM,IAAI,CAAC,mDAAmD,CAAC;EAChE,OAAO,IAAI;AACb;AAEA,IAAI,CAACP,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdI,OAAO,GAAGC,aAAmC;EAC/C,CAAC,MAAM;IACLD,OAAO,GAAGW,YAAkC;EAC9C;AACF,CAAC,MAAM,IAAId,MAAM,CAAC,CAAC,EAAE;EACnBG,OAAO,GAAGa,WAAW;AACvB,CAAC,MAAM,IAAIlB,gBAAgB,CAAC,CAAC,EAAE;EAC7BK,OAAO,GAAGc,qBAAqB;AACjC,CAAC,MAAM;EACLd,OAAO,GAAGe,cAAc;AAC1B", "ignoreList": []}