{"version": 3, "sources": ["../../../src/api/updateDevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport os from 'os';\nimport { URLSearchParams } from 'url';\n\nimport { fetchAsync } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\n/** Create the expected session info. */\nexport function createSessionInfo({\n  exp,\n  runtime,\n  url,\n}: {\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  return {\n    session: {\n      description: `${exp.name} on ${os.hostname()}`,\n      hostname: os.hostname(),\n      platform: runtime,\n      config: {\n        // TODO: if icons are specified, upload a url for them too so people can distinguish\n        description: exp.description,\n        name: exp.name,\n        slug: exp.slug,\n        primaryColor: exp.primaryColor,\n      },\n      url,\n      source: 'desktop',\n    },\n  };\n}\n\n/** Send a request to Expo API to keep the 'development session' alive for the provided devices. */\nexport async function updateDevelopmentSessionAsync({\n  deviceIds,\n  exp,\n  runtime,\n  url,\n}: {\n  deviceIds: string[];\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-alive', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      data: createSessionInfo({ exp, runtime, url }),\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when updating the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n\n/** Send a request to Expo API to close the 'development session' for the provided devices. */\nexport async function closeDevelopmentSessionAsync({\n  deviceIds,\n  url,\n}: {\n  deviceIds: string[];\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-close', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      session: { url },\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when closing the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n"], "names": ["closeDevelopmentSessionAsync", "createSessionInfo", "updateDevelopmentSessionAsync", "exp", "runtime", "url", "session", "description", "name", "os", "hostname", "platform", "config", "slug", "primaryColor", "source", "deviceIds", "searchParams", "URLSearchParams", "for<PERSON>ach", "id", "append", "results", "fetchAsync", "method", "body", "JSON", "stringify", "data", "ok", "CommandError", "statusText"], "mappings": ";;;;;;;;;;;IAqEsBA,4BAA4B;eAA5BA;;IA7DNC,iBAAiB;eAAjBA;;IA4BMC,6BAA6B;eAA7BA;;;;gEAnCP;;;;;;;yBACiB;;;;;;wBAEL;wBACE;;;;;;AAGtB,SAASD,kBAAkB,EAChCE,GAAG,EACHC,OAAO,EACPC,GAAG,EAKJ;IACC,OAAO;QACLC,SAAS;YACPC,aAAa,GAAGJ,IAAIK,IAAI,CAAC,IAAI,EAAEC,aAAE,CAACC,QAAQ,IAAI;YAC9CA,UAAUD,aAAE,CAACC,QAAQ;YACrBC,UAAUP;YACVQ,QAAQ;gBACN,oFAAoF;gBACpFL,aAAaJ,IAAII,WAAW;gBAC5BC,MAAML,IAAIK,IAAI;gBACdK,MAAMV,IAAIU,IAAI;gBACdC,cAAcX,IAAIW,YAAY;YAChC;YACAT;YACAU,QAAQ;QACV;IACF;AACF;AAGO,eAAeb,8BAA8B,EAClDc,SAAS,EACTb,GAAG,EACHC,OAAO,EACPC,GAAG,EAMJ;IACC,MAAMY,eAAe,IAAIC,CAAAA,MAAc,iBAAC;IACxCF,UAAUG,OAAO,CAAC,CAACC;QACjBH,aAAaI,MAAM,CAAC,YAAYD;IAClC;IAEA,MAAME,UAAU,MAAMC,IAAAA,kBAAU,EAAC,qCAAqC;QACpEN;QACAO,QAAQ;QACRC,MAAMC,KAAKC,SAAS,CAAC;YACnBC,MAAM3B,kBAAkB;gBAAEE;gBAAKC;gBAASC;YAAI;QAC9C;IACF;IAEA,IAAI,CAACiB,QAAQO,EAAE,EAAE;QACf,MAAM,IAAIC,oBAAY,CACpB,OACA,CAAC,2EAA2E,EAAER,QAAQS,UAAU,CAAC,CAAC,CAAC;IAEvG;AACF;AAGO,eAAe/B,6BAA6B,EACjDgB,SAAS,EACTX,GAAG,EAIJ;IACC,MAAMY,eAAe,IAAIC,CAAAA,MAAc,iBAAC;IACxCF,UAAUG,OAAO,CAAC,CAACC;QACjBH,aAAaI,MAAM,CAAC,YAAYD;IAClC;IAEA,MAAME,UAAU,MAAMC,IAAAA,kBAAU,EAAC,qCAAqC;QACpEN;QACAO,QAAQ;QACRC,MAAMC,KAAKC,SAAS,CAAC;YACnBrB,SAAS;gBAAED;YAAI;QACjB;IACF;IAEA,IAAI,CAACiB,QAAQO,EAAE,EAAE;QACf,MAAM,IAAIC,oBAAY,CACpB,OACA,CAAC,0EAA0E,EAAER,QAAQS,UAAU,CAAC,CAAC,CAAC;IAEtG;AACF"}