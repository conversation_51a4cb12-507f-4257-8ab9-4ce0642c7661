const http = require('http');
const url = require('url');

// Données de test
const testClients = [
  {
    idclient: 1,
    nom: '<PERSON><PERSON>',
    prenom: 'Fatima',
    adresse: '45 Avenue Hassan II',
    ville: '<PERSON><PERSON><PERSON>',
    tel: '0647895655',
    email: '<EMAIL>',
    secteur_nom: 'R<PERSON>identie<PERSON>'
  },
  {
    idclient: 2,
    nom: '<PERSON> Amrani',
    prenom: '<PERSON>',
    adresse: '123 Rue de la Paix',
    ville: 'Fès',
    tel: '0612345678',
    email: '<EMAIL>',
    secteur_nom: 'Commercial'
  },
  {
    idclient: 3,
    nom: '<PERSON><PERSON><PERSON>',
    prenom: 'Fatima',
    adresse: '789 Boulevard Mohammed V',
    ville: 'Rabat',
    tel: '0698765432',
    email: '<EMAIL>',
    secteur_nom: 'Résidentiel'
  }
];

const testContracts = {
  1: [
    {
      idcontract: 1,
      codeqr: 'QR-2025-0001',
      datecontract: '2025-07-01T23:00:00.000Z',
      idclient: 1,
      marquecompteur: 'SAGEMCOM',
      nom: '<PERSON><PERSON>',
      prenom: '<PERSON>ima'
    }
  ],
  2: [], // Client sans contrat
  3: [
    {
      idcontract: 2,
      codeqr: 'QR-2025-0002A',
      datecontract: '2025-07-01T23:00:00.000Z',
      idclient: 3,
      marquecompteur: 'SENSUS',
      nom: 'Bennani',
      prenom: 'Fatima'
    },
    {
      idcontract: 3,
      codeqr: 'QR-2025-0002B',
      datecontract: '2025-07-02T23:00:00.000Z',
      idclient: 3,
      marquecompteur: 'ITRON',
      nom: 'Bennani',
      prenom: 'Fatima'
    }
  ]
};

function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data, null, 2));
}

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📥 ${new Date().toISOString()} - ${method} ${path}`);

  // Gérer les requêtes OPTIONS (CORS preflight)
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Route de test
  if (path === '/' && method === 'GET') {
    console.log('✅ Route / appelée');
    sendJSON(res, 200, {
      message: 'Serveur minimal fonctionnel',
      timestamp: new Date().toISOString(),
      status: 'OK'
    });
    return;
  }

  // Route clients
  if (path === '/api/clients' && method === 'GET') {
    console.log('✅ Route /api/clients appelée');
    console.log(`✅ Retour de ${testClients.length} clients de test`);
    
    sendJSON(res, 200, {
      success: true,
      data: testClients,
      count: testClients.length,
      message: `${testClients.length} client(s) de test trouvé(s)`
    });
    return;
  }

  // Route contrats d'un client
  const contractsMatch = path.match(/^\/api\/clients\/(\d+)\/contracts$/);
  if (contractsMatch && method === 'GET') {
    const clientId = contractsMatch[1];
    console.log(`✅ Route /api/clients/${clientId}/contracts appelée`);
    
    const contracts = testContracts[clientId] || [];
    console.log(`✅ Retour de ${contracts.length} contrat(s) pour le client ${clientId}`);
    
    sendJSON(res, 200, {
      success: true,
      data: contracts,
      count: contracts.length,
      message: `${contracts.length} contrat(s) trouvé(s) pour le client ${clientId}`,
      client_id: parseInt(clientId)
    });
    return;
  }

  // Route dernière consommation
  const consommationMatch = path.match(/^\/api\/contracts\/(\d+)\/last-consommation$/);
  if (consommationMatch && method === 'GET') {
    const contractId = consommationMatch[1];
    console.log(`✅ Route /api/contracts/${contractId}/last-consommation appelée`);
    
    const testConsommation = {
      consommationactuelle: 1250.5,
      periode: '2025-01',
      jours: 30
    };

    console.log(`✅ Retour de la dernière consommation pour le contrat ${contractId}`);
    
    sendJSON(res, 200, {
      success: true,
      data: testConsommation,
      message: 'Dernière consommation de test trouvée'
    });
    return;
  }

  // Route non trouvée
  console.log(`❌ Route non trouvée: ${method} ${path}`);
  sendJSON(res, 404, {
    success: false,
    message: 'Route non trouvée',
    url: path
  });
});

const PORT = 3002;

server.listen(PORT, () => {
  console.log(`\n🚀 Serveur minimal démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes de test disponibles:');
  console.log('  - GET  / (test de base)');
  console.log('  - GET  /api/clients (3 clients de test)');
  console.log('  - GET  /api/clients/1/contracts (1 contrat - Benali)');
  console.log('  - GET  /api/clients/2/contracts (0 contrat - El Amrani)');
  console.log('  - GET  /api/clients/3/contracts (2 contrats - Bennani)');
  console.log('  - GET  /api/contracts/:id/last-consommation');
  console.log('\n✅ Prêt à recevoir les requêtes !');
  console.log('🔍 Tous les appels API seront loggés dans ce terminal');
});

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err);
});

console.log('🔄 Démarrage du serveur minimal...');
