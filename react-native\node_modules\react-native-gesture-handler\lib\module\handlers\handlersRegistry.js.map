{"version": 3, "sources": ["handlersRegistry.ts"], "names": ["isJestEnv", "handlerIDToTag", "gestures", "Map", "oldHandlers", "testIDs", "registerHandler", "handlerTag", "handler", "testID", "set", "registerOldGestureHandler", "unregister<PERSON><PERSON><PERSON>", "delete", "<PERSON><PERSON><PERSON><PERSON>", "get", "findOldGestureHandler", "findHandlerByTestID", "undefined"], "mappings": "AAAA,SAASA,SAAT,QAA0B,UAA1B;AAIA,OAAO,MAAMC,cAAsC,GAAG,EAA/C;AACP,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;AACA,MAAMC,WAAW,GAAG,IAAID,GAAJ,EAApB;AACA,MAAME,OAAO,GAAG,IAAIF,GAAJ,EAAhB;AAEA,OAAO,SAASG,eAAT,CACLC,UADK,EAELC,OAFK,EAGLC,MAHK,EAIL;AACAP,EAAAA,QAAQ,CAACQ,GAAT,CAAaH,UAAb,EAAyBC,OAAzB;;AACA,MAAIR,SAAS,MAAMS,MAAnB,EAA2B;AACzBJ,IAAAA,OAAO,CAACK,GAAR,CAAYD,MAAZ,EAAoBF,UAApB;AACD;AACF;AAED,OAAO,SAASI,yBAAT,CACLJ,UADK,EAELC,OAFK,EAGL;AACAJ,EAAAA,WAAW,CAACM,GAAZ,CAAgBH,UAAhB,EAA4BC,OAA5B;AACD;AAED,OAAO,SAASI,iBAAT,CAA2BL,UAA3B,EAA+CE,MAA/C,EAAgE;AACrEP,EAAAA,QAAQ,CAACW,MAAT,CAAgBN,UAAhB;;AACA,MAAIP,SAAS,MAAMS,MAAnB,EAA2B;AACzBJ,IAAAA,OAAO,CAACQ,MAAR,CAAeJ,MAAf;AACD;AACF;AAED,OAAO,SAASK,WAAT,CAAqBP,UAArB,EAAyC;AAC9C,SAAOL,QAAQ,CAACa,GAAT,CAAaR,UAAb,CAAP;AACD;AAED,OAAO,SAASS,qBAAT,CAA+BT,UAA/B,EAAmD;AACxD,SAAOH,WAAW,CAACW,GAAZ,CAAgBR,UAAhB,CAAP;AACD;AAED,OAAO,SAASU,mBAAT,CAA6BR,MAA7B,EAA6C;AAClD,QAAMF,UAAU,GAAGF,OAAO,CAACU,GAAR,CAAYN,MAAZ,CAAnB;;AACA,MAAIF,UAAU,KAAKW,SAAnB,EAA8B;AAAA;;AAC5B,2BAAOJ,WAAW,CAACP,UAAD,CAAlB,uDAAkC,IAAlC;AACD;;AACD,SAAO,IAAP;AACD", "sourcesContent": ["import { isJestEnv } from '../utils';\nimport { GestureType } from './gestures/gesture';\nimport { GestureEvent, HandlerStateChangeEvent } from './gestureHandlerCommon';\n\nexport const handlerIDToTag: Record<string, number> = {};\nconst gestures = new Map<number, GestureType>();\nconst oldHandlers = new Map<number, GestureHandlerCallbacks>();\nconst testIDs = new Map<string, number>();\n\nexport function registerHandler(\n  handlerTag: number,\n  handler: GestureType,\n  testID?: string\n) {\n  gestures.set(handlerTag, handler);\n  if (isJestEnv() && testID) {\n    testIDs.set(testID, handlerTag);\n  }\n}\n\nexport function registerOldGestureHandler(\n  handlerTag: number,\n  handler: GestureHandlerCallbacks\n) {\n  oldHandlers.set(handlerTag, handler);\n}\n\nexport function unregisterHandler(handlerTag: number, testID?: string) {\n  gestures.delete(handlerTag);\n  if (isJestEnv() && testID) {\n    testIDs.delete(testID);\n  }\n}\n\nexport function findHandler(handlerTag: number) {\n  return gestures.get(handlerTag);\n}\n\nexport function findOldGestureHandler(handlerTag: number) {\n  return oldHandlers.get(handlerTag);\n}\n\nexport function findHandlerByTestID(testID: string) {\n  const handlerTag = testIDs.get(testID);\n  if (handlerTag !== undefined) {\n    return findHandler(handlerTag) ?? null;\n  }\n  return null;\n}\n\nexport interface GestureHandlerCallbacks {\n  onGestureEvent: (event: GestureEvent<any>) => void;\n  onGestureStateChange: (event: HandlerStateChangeEvent<any>) => void;\n}\n"]}