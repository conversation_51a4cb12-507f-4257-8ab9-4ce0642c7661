{"version": 3, "names": ["Dummy", "children", "PanGestureHandler", "GestureHandlerRootView", "View", "GestureState", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": ";;;;;;AAAA;AACA;AAAoC;AAAA;AAGpC,MAAMA,KAAU,GAAG;EAAA,IAAC;IAAEC;EAAwC,CAAC;EAAA,oBAC7D,0CAAGA,QAAQ,CAAI;AAAA,CAChB;AAEM,MAAMC,iBAAiB,GAC5BF,KAAyD;AAAC;AAErD,MAAMG,sBAAsB,GAAGC,iBAAI;AAAC;AAEpC,MAAMC,YAAY,GAAG;EAC1BC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAC;AAAC"}