{"version": 3, "sources": ["../../../../../src/start/doctor/ngrok/ExternalModule.ts"], "sourcesContent": ["import * as PackageManager from '@expo/package-manager';\nimport requireGlobal from 'requireg';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport * as Log from '../../../log';\nimport { delayAsync } from '../../../utils/delay';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { confirmAsync } from '../../../utils/prompts';\n\nconst debug = require('debug')('expo:doctor:externalModule') as typeof console.log;\n\n/** An error that is thrown when a package is installed but doesn't meet the version criteria. */\nexport class ExternalModuleVersionError extends CommandError {\n  constructor(\n    message: string,\n    public readonly shouldGloballyInstall: boolean\n  ) {\n    super('EXTERNAL_MODULE_VERSION', message);\n  }\n}\n\ninterface PromptOptions {\n  /** Should prompt the user to install, when false the module will just assert on missing packages, default `true`. Ignored when `autoInstall` is true. */\n  shouldPrompt?: boolean;\n  /** Should automatically install the package without prompting, default `false` */\n  autoInstall?: boolean;\n}\n\nexport interface InstallPromptOptions extends PromptOptions {\n  /** Should install the package globally, default `false` */\n  shouldGloballyInstall?: boolean;\n}\n\nexport interface ResolvePromptOptions extends PromptOptions {\n  /**\n   * Prefer to install the package globally, this can be overridden if the function\n   * detects that a locally installed package simply needs an upgrade, default `false`\n   */\n  prefersGlobalInstall?: boolean;\n}\n\n/** Resolves a local or globally installed package, prompts to install if missing. */\nexport class ExternalModule<TModule> {\n  private instance: TModule | null = null;\n\n  constructor(\n    /** Project root for checking if the package is installed locally. */\n    private projectRoot: string,\n    /** Info on the external package. */\n    private pkg: {\n      /** NPM package name. */\n      name: string;\n      /** Required semver range, ex: `^1.0.0`. */\n      versionRange: string;\n    },\n    /** A function used to create the installation prompt message. */\n    private promptMessage: (pkgName: string) => string\n  ) {}\n\n  /** Resolve the globally or locally installed instance, or prompt to install. */\n  async resolveAsync({\n    prefersGlobalInstall,\n    ...options\n  }: ResolvePromptOptions = {}): Promise<TModule> {\n    try {\n      return (\n        this.getVersioned() ??\n        this.installAsync({\n          ...options,\n          shouldGloballyInstall: prefersGlobalInstall,\n        })\n      );\n    } catch (error: any) {\n      if (error instanceof ExternalModuleVersionError) {\n        // If the module version in not compliant with the version range,\n        // we should prompt the user to install the package where it already exists.\n        return this.installAsync({\n          ...options,\n          shouldGloballyInstall: error.shouldGloballyInstall ?? prefersGlobalInstall,\n        });\n      }\n      throw error;\n    }\n  }\n\n  /** Prompt the user to install the package and try again. */\n  async installAsync({\n    shouldPrompt = true,\n    autoInstall,\n    shouldGloballyInstall,\n  }: InstallPromptOptions = {}): Promise<TModule> {\n    const packageName = [this.pkg.name, this.pkg.versionRange].join('@');\n    if (!autoInstall) {\n      // Delay the prompt so it doesn't conflict with other dev tool logs\n      await delayAsync(100);\n    }\n    const answer =\n      autoInstall ||\n      (shouldPrompt &&\n        (await confirmAsync({\n          message: this.promptMessage(packageName),\n          initial: true,\n        })));\n    if (answer) {\n      Log.log(`Installing ${packageName}...`);\n\n      // Always use npm for global installs\n      const packageManager = shouldGloballyInstall\n        ? new PackageManager.NpmPackageManager({\n            cwd: this.projectRoot,\n            log: Log.log,\n            silent: !(env.EXPO_DEBUG || env.CI),\n          })\n        : PackageManager.createForProject(this.projectRoot, {\n            silent: !(env.EXPO_DEBUG || env.CI),\n          });\n\n      try {\n        if (shouldGloballyInstall) {\n          await packageManager.addGlobalAsync([packageName]);\n        } else {\n          await packageManager.addDevAsync([packageName]);\n        }\n        Log.log(`Installed ${packageName}`);\n      } catch (error: any) {\n        error.message = `Failed to install ${packageName} ${\n          shouldGloballyInstall ? 'globally' : 'locally'\n        }: ${error.message}`;\n        throw error;\n      }\n      return await this.resolveAsync({ shouldPrompt: false });\n    }\n\n    throw new CommandError('EXTERNAL_MODULE_AVAILABILITY', `Install ${packageName} and try again`);\n  }\n\n  /** Get the module. */\n  get(): TModule | null {\n    try {\n      return this.getVersioned();\n    } catch {\n      return null;\n    }\n  }\n\n  /** Get the module, throws if the module is not versioned correctly. */\n  getVersioned(): TModule | null {\n    this.instance ??= this._resolveModule(true) ?? this._resolveModule(false);\n    return this.instance;\n  }\n\n  /** Exposed for testing. */\n  _require(moduleId: string): any {\n    return require(moduleId);\n  }\n\n  /** Resolve a copy that's installed in the project. Exposed for testing. */\n  _resolveLocal(moduleId: string): string {\n    return resolveFrom(this.projectRoot, moduleId);\n  }\n\n  /** Resolve a copy that's installed globally. Exposed for testing. */\n  _resolveGlobal(moduleId: string): string {\n    return requireGlobal.resolve(moduleId);\n  }\n\n  /** Resolve the module and verify the version. Exposed for testing. */\n  _resolveModule(isLocal: boolean): TModule | null {\n    const resolver = isLocal ? this._resolveLocal.bind(this) : this._resolveGlobal.bind(this);\n    try {\n      const packageJsonPath = resolver(`${this.pkg.name}/package.json`);\n      const packageJson = this._require(packageJsonPath);\n      if (packageJson) {\n        if (semver.satisfies(packageJson.version, this.pkg.versionRange)) {\n          const modulePath = resolver(this.pkg.name);\n          const requiredModule = this._require(modulePath);\n          if (requiredModule == null) {\n            throw new CommandError(\n              'EXTERNAL_MODULE_EXPORT',\n              `${this.pkg.name} exports a nullish value, which is not allowed.`\n            );\n          }\n          return requiredModule;\n        }\n        throw new ExternalModuleVersionError(\n          `Required module '${this.pkg.name}@${packageJson.version}' does not satisfy ${this.pkg.versionRange}. Installed at: ${packageJsonPath}`,\n          !isLocal\n        );\n      }\n    } catch (error: any) {\n      if (error instanceof CommandError) {\n        throw error;\n      } else if (error.code !== 'MODULE_NOT_FOUND') {\n        debug('Failed to resolve module', error.message);\n      }\n    }\n    return null;\n  }\n}\n"], "names": ["ExternalModule", "ExternalModuleVersionError", "debug", "require", "CommandError", "constructor", "message", "shouldGloballyInstall", "projectRoot", "pkg", "promptMessage", "instance", "resolveAsync", "prefersGlobalInstall", "options", "getVersioned", "installAsync", "error", "should<PERSON>rompt", "autoInstall", "packageName", "name", "versionRange", "join", "delayAsync", "answer", "<PERSON><PERSON><PERSON>", "initial", "Log", "log", "packageManager", "PackageManager", "NpmPackageManager", "cwd", "silent", "env", "EXPO_DEBUG", "CI", "createForProject", "addGlobalAsync", "addDevAsync", "get", "_resolveModule", "_require", "moduleId", "_resolveLocal", "resolveFrom", "_resolveGlobal", "requireGlobal", "resolve", "isLocal", "resolver", "bind", "packageJsonPath", "packageJson", "semver", "satisfies", "version", "modulePath", "requiredModule", "code"], "mappings": ";;;;;;;;;;;IA4CaA,cAAc;eAAdA;;IA9BAC,0BAA0B;eAA1BA;;;;iEAdmB;;;;;;;gEACN;;;;;;;gEACF;;;;;;;gEACL;;;;;;6DAEE;uBACM;qBACP;wBACS;yBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMF,mCAAmCG,oBAAY;IAC1DC,YACEC,OAAe,EACf,AAAgBC,qBAA8B,CAC9C;QACA,KAAK,CAAC,2BAA2BD,eAFjBC,wBAAAA;IAGlB;AACF;AAuBO,MAAMP;IAGXK,YACE,mEAAmE,GACnE,AAAQG,WAAmB,EAC3B,kCAAkC,GAClC,AAAQC,GAKP,EACD,+DAA+D,GAC/D,AAAQC,aAA0C,CAClD;aAVQF,cAAAA;aAEAC,MAAAA;aAOAC,gBAAAA;aAbFC,WAA2B;IAchC;IAEH,8EAA8E,GAC9E,MAAMC,aAAa,EACjBC,oBAAoB,EACpB,GAAGC,SACkB,GAAG,CAAC,CAAC,EAAoB;QAC9C,IAAI;YACF,OACE,IAAI,CAACC,YAAY,MACjB,IAAI,CAACC,YAAY,CAAC;gBAChB,GAAGF,OAAO;gBACVP,uBAAuBM;YACzB;QAEJ,EAAE,OAAOI,OAAY;YACnB,IAAIA,iBAAiBhB,4BAA4B;gBAC/C,iEAAiE;gBACjE,4EAA4E;gBAC5E,OAAO,IAAI,CAACe,YAAY,CAAC;oBACvB,GAAGF,OAAO;oBACVP,uBAAuBU,MAAMV,qBAAqB,IAAIM;gBACxD;YACF;YACA,MAAMI;QACR;IACF;IAEA,0DAA0D,GAC1D,MAAMD,aAAa,EACjBE,eAAe,IAAI,EACnBC,WAAW,EACXZ,qBAAqB,EACA,GAAG,CAAC,CAAC,EAAoB;QAC9C,MAAMa,cAAc;YAAC,IAAI,CAACX,GAAG,CAACY,IAAI;YAAE,IAAI,CAACZ,GAAG,CAACa,YAAY;SAAC,CAACC,IAAI,CAAC;QAChE,IAAI,CAACJ,aAAa;YAChB,mEAAmE;YACnE,MAAMK,IAAAA,iBAAU,EAAC;QACnB;QACA,MAAMC,SACJN,eACCD,gBACE,MAAMQ,IAAAA,qBAAY,EAAC;YAClBpB,SAAS,IAAI,CAACI,aAAa,CAACU;YAC5BO,SAAS;QACX;QACJ,IAAIF,QAAQ;YACVG,KAAIC,GAAG,CAAC,CAAC,WAAW,EAAET,YAAY,GAAG,CAAC;YAEtC,qCAAqC;YACrC,MAAMU,iBAAiBvB,wBACnB,IAAIwB,CAAAA,iBAAa,EAAEC,iBAAiB,CAAC;gBACnCC,KAAK,IAAI,CAACzB,WAAW;gBACrBqB,KAAKD,KAAIC,GAAG;gBACZK,QAAQ,CAAEC,CAAAA,QAAG,CAACC,UAAU,IAAID,QAAG,CAACE,EAAE,AAAD;YACnC,KACAN,kBAAeO,gBAAgB,CAAC,IAAI,CAAC9B,WAAW,EAAE;gBAChD0B,QAAQ,CAAEC,CAAAA,QAAG,CAACC,UAAU,IAAID,QAAG,CAACE,EAAE,AAAD;YACnC;YAEJ,IAAI;gBACF,IAAI9B,uBAAuB;oBACzB,MAAMuB,eAAeS,cAAc,CAAC;wBAACnB;qBAAY;gBACnD,OAAO;oBACL,MAAMU,eAAeU,WAAW,CAAC;wBAACpB;qBAAY;gBAChD;gBACAQ,KAAIC,GAAG,CAAC,CAAC,UAAU,EAAET,aAAa;YACpC,EAAE,OAAOH,OAAY;gBACnBA,MAAMX,OAAO,GAAG,CAAC,kBAAkB,EAAEc,YAAY,CAAC,EAChDb,wBAAwB,aAAa,UACtC,EAAE,EAAEU,MAAMX,OAAO,EAAE;gBACpB,MAAMW;YACR;YACA,OAAO,MAAM,IAAI,CAACL,YAAY,CAAC;gBAAEM,cAAc;YAAM;QACvD;QAEA,MAAM,IAAId,oBAAY,CAAC,gCAAgC,CAAC,QAAQ,EAAEgB,YAAY,cAAc,CAAC;IAC/F;IAEA,oBAAoB,GACpBqB,MAAsB;QACpB,IAAI;YACF,OAAO,IAAI,CAAC1B,YAAY;QAC1B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,qEAAqE,GACrEA,eAA+B;QAC7B,IAAI,CAACJ,QAAQ,KAAK,IAAI,CAAC+B,cAAc,CAAC,SAAS,IAAI,CAACA,cAAc,CAAC;QACnE,OAAO,IAAI,CAAC/B,QAAQ;IACtB;IAEA,yBAAyB,GACzBgC,SAASC,QAAgB,EAAO;QAC9B,OAAOzC,QAAQyC;IACjB;IAEA,yEAAyE,GACzEC,cAAcD,QAAgB,EAAU;QACtC,OAAOE,IAAAA,sBAAW,EAAC,IAAI,CAACtC,WAAW,EAAEoC;IACvC;IAEA,mEAAmE,GACnEG,eAAeH,QAAgB,EAAU;QACvC,OAAOI,mBAAa,CAACC,OAAO,CAACL;IAC/B;IAEA,oEAAoE,GACpEF,eAAeQ,OAAgB,EAAkB;QAC/C,MAAMC,WAAWD,UAAU,IAAI,CAACL,aAAa,CAACO,IAAI,CAAC,IAAI,IAAI,IAAI,CAACL,cAAc,CAACK,IAAI,CAAC,IAAI;QACxF,IAAI;YACF,MAAMC,kBAAkBF,SAAS,GAAG,IAAI,CAAC1C,GAAG,CAACY,IAAI,CAAC,aAAa,CAAC;YAChE,MAAMiC,cAAc,IAAI,CAACX,QAAQ,CAACU;YAClC,IAAIC,aAAa;gBACf,IAAIC,iBAAM,CAACC,SAAS,CAACF,YAAYG,OAAO,EAAE,IAAI,CAAChD,GAAG,CAACa,YAAY,GAAG;oBAChE,MAAMoC,aAAaP,SAAS,IAAI,CAAC1C,GAAG,CAACY,IAAI;oBACzC,MAAMsC,iBAAiB,IAAI,CAAChB,QAAQ,CAACe;oBACrC,IAAIC,kBAAkB,MAAM;wBAC1B,MAAM,IAAIvD,oBAAY,CACpB,0BACA,GAAG,IAAI,CAACK,GAAG,CAACY,IAAI,CAAC,+CAA+C,CAAC;oBAErE;oBACA,OAAOsC;gBACT;gBACA,MAAM,IAAI1D,2BACR,CAAC,iBAAiB,EAAE,IAAI,CAACQ,GAAG,CAACY,IAAI,CAAC,CAAC,EAAEiC,YAAYG,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAChD,GAAG,CAACa,YAAY,CAAC,gBAAgB,EAAE+B,iBAAiB,EACvI,CAACH;YAEL;QACF,EAAE,OAAOjC,OAAY;YACnB,IAAIA,iBAAiBb,oBAAY,EAAE;gBACjC,MAAMa;YACR,OAAO,IAAIA,MAAM2C,IAAI,KAAK,oBAAoB;gBAC5C1D,MAAM,4BAA4Be,MAAMX,OAAO;YACjD;QACF;QACA,OAAO;IACT;AACF"}