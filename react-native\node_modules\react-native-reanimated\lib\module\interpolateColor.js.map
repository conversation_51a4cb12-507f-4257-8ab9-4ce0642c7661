{"version": 3, "names": ["hsvToColor", "RGBtoHSV", "rgbaColor", "processColor", "red", "green", "blue", "opacity", "makeMutable", "Extrapolation", "interpolate", "useSharedValue", "ReanimatedError", "Extrapolate", "interpolateColorsHSV", "value", "inputRange", "colors", "options", "h", "useCorrectedHSVInterpolation", "correctedInputRange", "originalH", "correctedH", "i", "length", "d", "push", "CLAMP", "s", "v", "a", "toLinearSpace", "x", "gamma", "map", "Math", "pow", "toGammaSpace", "round", "interpolateColorsRGB", "r", "outputR", "g", "outputG", "b", "outputB", "getInterpolateRGB", "color", "processedColor", "undefined", "getInterpolateHSV", "processedHSVColor", "interpolateColor", "outputRange", "colorSpace", "ColorSpace", "useInterpolateConfig", "RGB", "cache"], "sourceRoot": "../../src", "sources": ["interpolateColor.ts"], "mappings": "AAAA,YAAY;;AACZ,SACEA,UAAU,EACVC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,QACF,aAAU;AACjB,SAASC,WAAW,QAAQ,WAAQ;AACpC,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAiB;AAE5D,SAASC,cAAc,QAAQ,0BAAuB;AACtD,SAASC,eAAe,QAAQ,aAAU;;AAE1C;AACA,OAAO,MAAMC,WAAW,GAAGJ,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,MAAMK,oBAAoB,GAAGA,CAC3BC,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,MAAM;IAAEC,4BAA4B,GAAG;EAAK,CAAC,GAAGF,OAAO;EACvD,IAAIE,4BAA4B,EAAE;IAChC;IACA;IACA;IACA;IACA,MAAMC,mBAAmB,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMM,SAAS,GAAGL,MAAM,CAACE,CAAC;IAC1B,MAAMI,UAAU,GAAG,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC;IAEjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,MAAME,CAAC,GAAGJ,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC;MACzC,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,GAAG,EAAE;QAC9CL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAG,CAAC,GAAG,EAAE;QACtDL,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,GAAG,OAAO,CAAC;QACjDD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;QACjCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QACLH,mBAAmB,CAACM,IAAI,CAACX,UAAU,CAACQ,CAAC,CAAC,CAAC;QACvCD,UAAU,CAACI,IAAI,CAACL,SAAS,CAACE,CAAC,CAAC,CAAC;MAC/B;IACF;IACAL,CAAC,GACC,CAACT,WAAW,CACVK,KAAK,EACLM,mBAAmB,EACnBE,UAAU,EACVd,aAAa,CAACmB,KAChB,CAAC,GACC,CAAC,IACH,CAAC;EACL,CAAC,MAAM;IACLT,CAAC,GAAGT,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACE,CAAC,EAAEV,aAAa,CAACmB,KAAK,CAAC;EACnE;EACA,MAAMC,CAAC,GAAGnB,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACY,CAAC,EAAEpB,aAAa,CAACmB,KAAK,CAAC;EACvE,MAAME,CAAC,GAAGpB,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACa,CAAC,EAAErB,aAAa,CAACmB,KAAK,CAAC;EACvE,MAAMG,CAAC,GAAGrB,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEtB,aAAa,CAACmB,KAAK,CAAC;EACvE,OAAO5B,UAAU,CAACmB,CAAC,EAAEU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAC/B,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,CAAW,EAAEC,KAAa,KAAe;EAC9D,SAAS;;EACT,OAAOD,CAAC,CAACE,GAAG,CAAEL,CAAC,IAAKM,IAAI,CAACC,GAAG,CAACP,CAAC,GAAG,GAAG,EAAEI,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAMI,YAAY,GAAGA,CAACL,CAAS,EAAEC,KAAa,KAAa;EACzD,SAAS;;EACT,OAAOE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAE,CAAC,GAAGC,KAAK,CAAC,GAAG,GAAG,CAAC;AACjD,CAAC;AAED,MAAMM,oBAAoB,GAAGA,CAC3BzB,KAAa,EACbC,UAA6B,EAC7BC,MAAsB,EACtBC,OAA6B,KAC1B;EACH,SAAS;;EACT,MAAM;IAAEgB,KAAK,GAAG;EAAI,CAAC,GAAGhB,OAAO;EAC/B,IAAI;IAAEuB,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC,OAAO;IAAEC,CAAC,EAAEC;EAAQ,CAAC,GAAG7B,MAAM;EACnD,IAAIiB,KAAK,KAAK,CAAC,EAAE;IACfQ,OAAO,GAAGV,aAAa,CAACU,OAAO,EAAER,KAAK,CAAC;IACvCU,OAAO,GAAGZ,aAAa,CAACY,OAAO,EAAEV,KAAK,CAAC;IACvCY,OAAO,GAAGd,aAAa,CAACc,OAAO,EAAEZ,KAAK,CAAC;EACzC;EACA,MAAMO,CAAC,GAAG/B,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAE0B,OAAO,EAAEjC,aAAa,CAACmB,KAAK,CAAC;EACtE,MAAMe,CAAC,GAAGjC,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAE4B,OAAO,EAAEnC,aAAa,CAACmB,KAAK,CAAC;EACtE,MAAMiB,CAAC,GAAGnC,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAE8B,OAAO,EAAErC,aAAa,CAACmB,KAAK,CAAC;EACtE,MAAMG,CAAC,GAAGrB,WAAW,CAACK,KAAK,EAAEC,UAAU,EAAEC,MAAM,CAACc,CAAC,EAAEtB,aAAa,CAACmB,KAAK,CAAC;EACvE,IAAIM,KAAK,KAAK,CAAC,EAAE;IACf,OAAOhC,SAAS,CAACuC,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEd,CAAC,CAAC;EAC9B;EACA,OAAO7B,SAAS,CACdoC,YAAY,CAACG,CAAC,EAAEP,KAAK,CAAC,EACtBI,YAAY,CAACK,CAAC,EAAET,KAAK,CAAC,EACtBI,YAAY,CAACO,CAAC,EAAEX,KAAK,CAAC,EACtBH,CACF,CAAC;AACH,CAAC;AASD,MAAMgB,iBAAiB,GACrB9B,MAAoC,IACjB;EACnB,SAAS;;EAET,MAAMwB,CAAC,GAAG,EAAE;EACZ,MAAME,CAAC,GAAG,EAAE;EACZ,MAAME,CAAC,GAAG,EAAE;EACZ,MAAMd,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,MAAMwB,KAAK,GAAG/B,MAAM,CAACO,CAAC,CAAC;IACvB,MAAMyB,cAAc,GAAG9C,YAAY,CAAC6C,KAAK,CAAC;IAC1C;IACA,IAAIC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKC,SAAS,EAAE;MAC3DT,CAAC,CAACd,IAAI,CAACvB,GAAG,CAAC6C,cAAc,CAAC,CAAC;MAC3BN,CAAC,CAAChB,IAAI,CAACtB,KAAK,CAAC4C,cAAc,CAAC,CAAC;MAC7BJ,CAAC,CAAClB,IAAI,CAACrB,IAAI,CAAC2C,cAAc,CAAC,CAAC;MAC5BlB,CAAC,CAACJ,IAAI,CAACpB,OAAO,CAAC0C,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAER,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEd;EAAE,CAAC;AACvB,CAAC;AASD,MAAMoB,iBAAiB,GACrBlC,MAAoC,IACjB;EACnB,SAAS;;EACT,MAAME,CAAC,GAAG,EAAE;EACZ,MAAMU,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;IACtC,MAAMwB,KAAK,GAAG/B,MAAM,CAACO,CAAC,CAAC;IACvB,MAAMyB,cAAc,GAAG9C,YAAY,CAAC6C,KAAK,CAAQ;IACjD,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACtC,MAAMG,iBAAiB,GAAGnD,QAAQ,CAChCG,GAAG,CAAC6C,cAAc,CAAC,EACnB5C,KAAK,CAAC4C,cAAc,CAAC,EACrB3C,IAAI,CAAC2C,cAAc,CACrB,CAAC;MAED9B,CAAC,CAACQ,IAAI,CAACyB,iBAAiB,CAACjC,CAAC,CAAC;MAC3BU,CAAC,CAACF,IAAI,CAACyB,iBAAiB,CAACvB,CAAC,CAAC;MAC3BC,CAAC,CAACH,IAAI,CAACyB,iBAAiB,CAACtB,CAAC,CAAC;MAC3BC,CAAC,CAACJ,IAAI,CAACpB,OAAO,CAAC0C,cAAc,CAAC,CAAC;IACjC;EACF;EACA,OAAO;IAAE9B,CAAC;IAAEU,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAiBA,OAAO,SAASsB,gBAAgBA,CAC9BtC,KAAa,EACbC,UAA6B,EAC7BsC,WAAyC,EACzCC,UAAyB,GAAG,KAAK,EACjCrC,OAA6B,GAAG,CAAC,CAAC,EACjB;EACjB,SAAS;;EACT,IAAIqC,UAAU,KAAK,KAAK,EAAE;IACxB,OAAOzC,oBAAoB,CACzBC,KAAK,EACLC,UAAU,EACVmC,iBAAiB,CAACG,WAAW,CAAC,EAC9BpC,OACF,CAAC;EACH,CAAC,MAAM,IAAIqC,UAAU,KAAK,KAAK,EAAE;IAC/B,OAAOf,oBAAoB,CACzBzB,KAAK,EACLC,UAAU,EACV+B,iBAAiB,CAACO,WAAW,CAAC,EAC9BpC,OACF,CAAC;EACH;EACA,MAAM,IAAIN,eAAe,CACvB,iCACE2C,UAAU,yCAEd,CAAC;AACH;AAEA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAatB,OAAO,SAASC,oBAAoBA,CAClCzC,UAA6B,EAC7BsC,WAAyC,EACzCC,UAAU,GAAGC,UAAU,CAACE,GAAG,EAC3BxC,OAA6B,GAAG,CAAC,CAAC,EACF;EAChC,OAAOP,cAAc,CAAoB;IACvCK,UAAU;IACVsC,WAAW;IACXC,UAAU;IACVI,KAAK,EAAEnD,WAAW,CAAyC,IAAI,CAAC;IAChEU;EACF,CAAC,CAAC;AACJ", "ignoreList": []}