import*as e from"../../core/platform/platform.js";import*as t from"../../core/i18n/i18n.js";import*as i from"../../ui/components/icon_button/icon_button.js";import*as s from"../../ui/legacy/components/data_grid/data_grid.js";import*as r from"../../ui/legacy/legacy.js";import*as o from"../../core/host/host.js";import*as n from"../../core/common/common.js";import*as a from"../../core/sdk/sdk.js";import*as l from"../../ui/visual_logging/visual_logging.js";import*as d from"../../ui/components/buttons/buttons.js";import*as h from"../../core/root/root.js";import*as c from"../../models/cpu_profile/cpu_profile.js";import*as p from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as u from"../../ui/legacy/components/utils/utils.js";import*as m from"../../models/bindings/bindings.js";import*as f from"../../models/heap_snapshot_model/heap_snapshot_model.js";import*as g from"../../ui/legacy/components/object_ui/object_ui.js";import*as v from"../../models/workspace/workspace.js";const S={notOptimizedS:"Not optimized: {PH1}",genericTextTwoPlaceholders:"{PH1}, {PH2}"},w=t.i18n.registerUIStrings("panels/profiler/ProfileDataGrid.ts",S),b=t.i18n.getLocalizedString.bind(void 0,w);class C extends s.DataGrid.DataGridNode{searchMatchedSelfColumn;searchMatchedTotalColumn;searchMatchedFunctionColumn;profileNode;tree;childrenByCallUID;lastComparator;callUID;self;total;functionName;deoptReason;url;linkElement;populated;savedSelf;savedTotal;savedChildren;constructor(e,t,i){super(null,i),this.searchMatchedSelfColumn=!1,this.searchMatchedTotalColumn=!1,this.searchMatchedFunctionColumn=!1,this.profileNode=e,this.tree=t,this.childrenByCallUID=new Map,this.lastComparator=null,this.callUID=e.callUID,this.self=e.self,this.total=e.total,this.functionName=r.UIUtils.beautifyFunctionName(e.functionName),this.deoptReason=e.deoptReason||"",this.url=e.url,this.linkElement=null,this.populated=!1}static sort(e,t,i){for(let s=0;s<e.length;++s){const r=e[s],o=r.length;for(let s=0;s<o;++s){const o=r[s];if(!(i||o.expanded&&o.lastComparator!==t)){o.children.length&&(o.shouldRefreshChildren=!0);continue}o.lastComparator=t;const n=o.children,a=n.length;if(a){n.sort(t);for(let e=0;e<a;++e)n[e].recalculateSiblings(e);e.push(n)}}}}static merge(e,t,i){e.self+=t.self,i||(e.total+=t.total);let s=e.children.slice();e.removeChildren();let r=s.length;for(let o=0;o<r;++o)i&&s[o]===t||e.appendChild(s[o]);s=t.children.slice(),r=s.length;for(let t=0;t<r;++t){const i=s[t],r=e.childrenByCallUID.get(i.callUID);r?r.merge(i,!1):e.appendChild(i)}}static populate(e){if(e.populated)return;e.populated=!0,e.populateChildren();const t=e.tree.lastComparator;t&&e.sort(t,!0)}createCell(e){switch(e){case"self":{const t=this.createValueCell(this.self,this.selfPercent,e);return t.classList.toggle("highlight",this.searchMatchedSelfColumn),t}case"total":{const t=this.createValueCell(this.total,this.totalPercent,e);return t.classList.toggle("highlight",this.searchMatchedTotalColumn),t}case"function":{const t=this.createTD(e);if(t.classList.toggle("highlight",this.searchMatchedFunctionColumn),this.deoptReason){t.classList.add("not-optimized");const e=new i.Icon.Icon;e.data={iconName:"warning-filled",color:"var(--icon-warning)",width:"14px",height:"14px"},e.classList.add("profile-warn-marker"),r.Tooltip.Tooltip.install(e,b(S.notOptimizedS,{PH1:this.deoptReason})),t.appendChild(e)}if(r.UIUtils.createTextChild(t,this.functionName),"0"===this.profileNode.scriptId)return t;const s=this.tree.formatter.linkifyNode(this);return s?(s.style.maxWidth="75%",t.appendChild(s),this.linkElement=s,t):t}}return super.createCell(e)}createValueCell(e,t,i){const s=document.createElement("td");s.classList.add("numeric-column");const r=s.createChild("div","profile-multiple-values"),o=r.createChild("span"),n=this.tree.formatter.formatValue(e,this);o.textContent=n;const a=r.createChild("span","percent-column"),l=this.tree.formatter.formatPercent(t,this);a.textContent=l;const d=this.tree.formatter.formatValueAccessibleText(e,this);return this.setCellAccessibleName(b(S.genericTextTwoPlaceholders,{PH1:d,PH2:l}),s,i),s}sort(e,t){const i=e;return C.sort([[this]],i,t)}insertChild(e,t){const i=e;super.insertChild(i,t),this.childrenByCallUID.set(i.callUID,i)}removeChild(e){super.removeChild(e),this.childrenByCallUID.delete(e.callUID)}removeChildren(){super.removeChildren(),this.childrenByCallUID.clear()}findChild(e){return e&&this.childrenByCallUID.get(e.callUID)||null}get selfPercent(){return this.self/this.tree.total*100}get totalPercent(){return this.total/this.tree.total*100}populate(){C.populate(this)}populateChildren(){}save(){this.savedChildren||(this.savedSelf=this.self,this.savedTotal=this.total,this.savedChildren=this.children.slice())}restore(){if(!this.savedChildren)return;this.savedSelf&&this.savedTotal&&(this.self=this.savedSelf,this.total=this.savedTotal),this.removeChildren();const e=this.savedChildren,t=e.length;for(let i=0;i<t;++i)e[i].restore(),this.appendChild(e[i])}merge(e,t){C.merge(this,e,t)}}class y{tree;self;children;formatter;searchableView;total;lastComparator;childrenByCallUID;deepSearch;populated;searchResults;savedTotal;savedChildren;searchResultIndex=-1;constructor(e,t,i){this.tree=this,this.self=0,this.children=[],this.formatter=e,this.searchableView=t,this.total=i,this.lastComparator=null,this.childrenByCallUID=new Map,this.deepSearch=!0,this.populated=!1}static propertyComparator(e,t){let i=P[t?1:0][e];return i||(i=t?function(t,i){return t[e]<i[e]?-1:t[e]>i[e]?1:0}:function(t,i){return t[e]>i[e]?-1:t[e]<i[e]?1:0},P[t?1:0][e]=i),i}get expanded(){return!0}appendChild(e){this.insertChild(e,this.children.length)}focus(e){}exclude(e){}insertChild(e,t){const i=e;this.children.splice(t,0,i),this.childrenByCallUID.set(i.callUID,e)}removeChildren(){this.children=[],this.childrenByCallUID.clear()}populateChildren(){}findChild(e){return e&&this.childrenByCallUID.get(e.callUID)||null}sort(e,t){return C.sort([[this]],e,t)}save(){this.savedChildren||(this.savedTotal=this.total,this.savedChildren=this.children.slice())}restore(){if(!this.savedChildren)return;this.children=this.savedChildren,this.savedTotal&&(this.total=this.savedTotal);const e=this.children,t=e.length;for(let i=0;i<t;++i)e[i].restore();this.savedChildren=null}matchFunction(t){const i=t.query.trim();if(!i.length)return null;const s=i.startsWith(">"),r=i.startsWith("<");let o=i.startsWith("=")||(s||r)&&1===i.indexOf("=");const n=i.endsWith("%"),a=i.length>2&&i.endsWith("ms"),l=!a&&i.endsWith("s");let d=parseFloat(i);(s||r||o)&&(d=o&&(s||r)?parseFloat(i.substring(2)):parseFloat(i.substring(1)));const h=l?1e3*d:d;isNaN(d)||s||r||(o=!0);const c=e.StringUtilities.createPlainTextSearchRegex(i,"i");return function(e){return e.searchMatchedSelfColumn=!1,e.searchMatchedTotalColumn=!1,e.searchMatchedFunctionColumn=!1,n?(r?(e.selfPercent<d&&(e.searchMatchedSelfColumn=!0),e.totalPercent<d&&(e.searchMatchedTotalColumn=!0)):s&&(e.selfPercent>d&&(e.searchMatchedSelfColumn=!0),e.totalPercent>d&&(e.searchMatchedTotalColumn=!0)),o&&(e.selfPercent===d&&(e.searchMatchedSelfColumn=!0),e.totalPercent===d&&(e.searchMatchedTotalColumn=!0))):(a||l)&&(r?(e.self<h&&(e.searchMatchedSelfColumn=!0),e.total<h&&(e.searchMatchedTotalColumn=!0)):s&&(e.self>h&&(e.searchMatchedSelfColumn=!0),e.total>h&&(e.searchMatchedTotalColumn=!0)),o&&(e.self===h&&(e.searchMatchedSelfColumn=!0),e.total===h&&(e.searchMatchedTotalColumn=!0))),(e.functionName.match(c)||e.url&&e.url.match(c))&&(e.searchMatchedFunctionColumn=!0),!!(e.searchMatchedSelfColumn||e.searchMatchedTotalColumn||e.searchMatchedFunctionColumn)&&(e.refresh(),!0)}}performSearch(e,t,i){this.onSearchCanceled();const s=this.matchFunction(e);if(!s)return;this.searchResults=[];const r=this.deepSearch;let o;for(o=this.children[0];o;o=o.traverseNextNode(!r,null,!r)){const e=o;if(!e)break;s(e)&&this.searchResults.push({profileNode:e})}this.searchResultIndex=i?0:this.searchResults.length-1,this.searchableView.updateSearchMatchesCount(this.searchResults.length),this.searchableView.updateCurrentMatchIndex(this.searchResultIndex)}onSearchCanceled(){if(this.searchResults)for(let e=0;e<this.searchResults.length;++e){const t=this.searchResults[e].profileNode;t.searchMatchedSelfColumn=!1,t.searchMatchedTotalColumn=!1,t.searchMatchedFunctionColumn=!1,t.refresh()}this.searchResults=[],this.searchResultIndex=-1}jumpToNextSearchResult(){this.searchResults&&this.searchResults.length&&(this.searchResultIndex=(this.searchResultIndex+1)%this.searchResults.length,this.jumpToSearchResult(this.searchResultIndex))}jumpToPreviousSearchResult(){this.searchResults&&this.searchResults.length&&(this.searchResultIndex=(this.searchResultIndex-1+this.searchResults.length)%this.searchResults.length,this.jumpToSearchResult(this.searchResultIndex))}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!1}jumpToSearchResult(e){const t=this.searchResults[e];if(!t)return;t.profileNode.revealAndSelect(),this.searchableView.updateCurrentMatchIndex(e)}}const P=[{},{}];var T=Object.freeze({__proto__:null,ProfileDataGridNode:C,ProfileDataGridTree:y});class I extends C{remainingNodeInfos;constructor(e,t){super(e,t,null!==e.parent&&Boolean(e.parent.parent)),this.remainingNodeInfos=[]}static sharedPopulate(e){if(void 0===e.remainingNodeInfos)return;const t=e.remainingNodeInfos,i=t.length;for(let s=0;s<i;++s){const i=t[s],r=i.ancestor,o=i.focusNode;let n=e.findChild(r);if(n){const e=i.totalAccountedFor;n.self+=o.self,e||(n.total+=o.total)}else n=new I(r,e.tree),r!==o&&(n.self=o.self,n.total=o.total),e.appendChild(n);const a=r.parent;a&&a.parent&&(i.ancestor=a,n.remainingNodeInfos||(n.remainingNodeInfos=[]),n.remainingNodeInfos.push(i))}delete e.remainingNodeInfos}takePropertiesFromProfileDataGridNode(e){this.save(),this.self=e.self,this.total=e.total}keepOnlyChild(e){this.save(),this.removeChildren(),this.appendChild(e)}exclude(e){this.remainingNodeInfos&&this.populate(),this.save();const t=this.children;let i=this.children.length;for(;i--;)t[i].exclude(e);const s=this.childrenByCallUID.get(e);s&&this.merge(s,!0)}restore(){super.restore(),this.children.length||this.setHasChildren(this.willHaveChildren(this.profileNode))}merge(e,t){this.self-=e.self,super.merge(e,t)}populateChildren(){I.sharedPopulate(this)}willHaveChildren(e){return Boolean(e.parent&&e.parent.parent)}}class x extends y{deepSearch;remainingNodeInfos;constructor(e,t,i,s){super(e,t,s),this.deepSearch=!1;let r=0;const o=[[],[i]],n=new Map;this.remainingNodeInfos=[];for(let e=0;e<o.length;++e){const t=o[e],i=o[++e],s=i.length,a=new WeakMap;for(let e=0;e<s;++e){const s=i[e];if(a.get(s)||a.set(s,++r),s.parent){let e=n.get(s.callUID),i=!1;if(e){const s=t.length;for(let r=0;r<s;++r){const s=a.get(t[r]);if(s&&e.has(s)){i=!0;break}}}else e=new Set,n.set(s.callUID,e);const r=a.get(s);r&&e.add(r),this.remainingNodeInfos.push({ancestor:s,focusNode:s,totalAccountedFor:i})}const l=s.children;l.length&&(o.push(t.concat([s])),o.push(l))}}return C.populate(this),this}focus(e){if(!e)return;this.save();let t=e,i=e;for(;t.parent&&t instanceof I;)t.takePropertiesFromProfileDataGridNode(e),i=t,t=t.parent,t instanceof I&&t.keepOnlyChild(i);this.children=[i],this.total=e.total}exclude(t){if(!t)return;this.save();const i=t.callUID,s=this.childrenByCallUID.get(i);s&&e.ArrayUtilities.removeElement(this.children,s);const r=this.children,o=r.length;for(let e=0;e<o;++e)r[e].exclude(i);this.lastComparator&&this.sort(this.lastComparator,!0)}populateChildren(){I.sharedPopulate(this)}}var R=Object.freeze({__proto__:null,BottomUpProfileDataGridNode:I,BottomUpProfileDataGridTree:x}),N=Object.freeze({__proto__:null});const k=new CSSStyleSheet;k.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");const D=new CSSStyleSheet;D.replaceSync('.heap-snapshot-view{overflow:hidden}.heap-snapshot-view .data-grid{border:none;flex:auto}.heap-snapshot-view .data-grid tr:empty{height:16px;visibility:hidden}.heap-snapshot-view .data-grid span.percent-column{width:35px!important}.heap-snapshot-view .object-value-object,\n.object-value-node{display:inline;position:static}.heap-snapshot-view .object-value-string{white-space:nowrap}.heap-snapshot-view td.object-column .objects-count{margin-left:10px;font-size:11px;color:var(--sys-color-token-subtle)}.heap-snapshot-view tr:not(.selected) .object-value-id{color:var(--sys-color-token-subtle)}.profile-view .heap-tracking-overview{flex:0 0 80px;height:80px}.heap-snapshot-view .retaining-paths-view{overflow:hidden}.heap-snapshot-view .heap-snapshot-view-resizer{background-color:var(--sys-color-surface1);display:flex;flex:0 0 21px}.heap-snapshot-view td.object-column > div > span{margin-right:6px}.heap-snapshot-view .heap-snapshot-view-resizer .title{flex:0 1 auto;overflow:hidden;white-space:nowrap}.heap-snapshot-view .heap-snapshot-view-resizer .verticalResizerIcon{background-image:var(--image-file-toolbarResizerVertical);background-repeat:no-repeat;background-position:center;flex:0 0 28px;margin-left:auto}.heap-snapshot-view .heap-snapshot-view-resizer .title > span{display:inline-block;padding-top:3px;vertical-align:middle;margin-left:4px;margin-right:8px}.heap-snapshot-view .heap-snapshot-view-resizer *{pointer-events:none}.heap-snapshot-view tr:not(.selected) td.object-column span.highlight{background-color:inherit}.heap-snapshot-view td.object-column span.heap-object-source-link{float:right}.heap-snapshot-view td.object-column span.heap-object-source-link:empty{animation:fadeInOut 2s infinite}.heap-snapshot-view td.object-column span.heap-object-source-link:empty::before{content:"\\b7\\b7";font-weight:bold}@keyframes fadeInOut{0%{transform:rotate(0)}50%{transform:rotate(0.5turn)}100%{transform:rotate(1turn)}}.heap-object-tag{height:14px;width:14px}.heap-snapshot-view tr:not(.selected) td.object-column span.heap-object-tag,\n.heap-snapshot-view tr:not(.selected) td.object-column span.grayed{color:var(--sys-color-token-subtle)}.heap-snapshot-view tr:not(.selected) .cycled-ancestor-node,\n.heap-snapshot-view tr:not(.selected) .unreachable-ancestor-node{opacity:60%}#heap-recording-view .profile-view{top:80px}.heap-overview-container{overflow:hidden;position:absolute;top:0;width:100%;height:80px}#heap-recording-overview-grid .resources-dividers-label-bar{pointer-events:auto}.heap-recording-overview-canvas{position:absolute;top:20px;left:0;right:0;bottom:0}.heap-snapshot-statistics-view{overflow:auto}.heap-snapshot-stats-pie-chart{margin:12px 30px;flex-shrink:0}.heap-allocation-stack .stack-frame{display:flex;justify-content:space-between;border-bottom:1px solid var(--sys-color-divider);padding:2px}.heap-allocation-stack .stack-frame:focus{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.heap-allocation-stack .stack-frame:hover:not(:focus){background-color:var(--sys-color-state-hover-on-subtle)}.heap-allocation-stack .stack-frame .devtools-link{color:var(--sys-color-primary)}.no-heap-allocation-stack{padding:5px}@media (forced-colors: active){.cycled-ancestor-node{opacity:100%}.heap-snapshot-view td.object-column .objects-count,\n  .heap-snapshot-view tr:not(.selected) td.object-column span.heap-object-tag,\n  .heap-snapshot-view tr:not(.selected) .object-value-id{color:ButtonText}}\n/*# sourceURL=heapProfiler.css */\n');class E extends n.ObjectWrapper.ObjectWrapper{profileTypeInternal;title;uid;fromFileInternal;tempFile;constructor(e,t){super(),this.profileTypeInternal=e,this.title=t,this.uid=e.incrementProfileUid(),this.fromFileInternal=!1,this.tempFile=null}setTitle(e){this.title=e,this.dispatchEventToListeners("ProfileTitleChanged",this)}profileType(){return this.profileTypeInternal}updateStatus(e,t){this.dispatchEventToListeners("UpdateStatus",new M(e,t))}createSidebarTreeElement(e){throw new Error("Not implemented.")}createView(e){throw new Error("Not implemented.")}removeTempFile(){this.tempFile&&this.tempFile.remove()}dispose(){}canSaveToFile(){return!1}saveToFile(){throw new Error("Not implemented.")}loadFromFile(e){throw new Error("Not implemented.")}fromFile(){return this.fromFileInternal}setFromFile(){this.fromFileInternal=!0}setProfile(e){}}class M{subtitle;wait;constructor(e,t){this.subtitle=e,this.wait=t}}class F extends n.ObjectWrapper.ObjectWrapper{idInternal;nameInternal;profiles;profileBeingRecordedInternal;nextProfileUidInternal;constructor(e,t){super(),this.idInternal=e,this.nameInternal=t,this.profiles=[],this.profileBeingRecordedInternal=null,this.nextProfileUidInternal=1,window.opener||window.addEventListener("pagehide",this.clearTempStorage.bind(this),!1)}typeName(){return""}nextProfileUid(){return this.nextProfileUidInternal}incrementProfileUid(){return this.nextProfileUidInternal++}hasTemporaryView(){return!1}fileExtension(){return null}get buttonTooltip(){return""}get id(){return this.idInternal}get treeItemTitle(){return this.nameInternal}get name(){return this.nameInternal}buttonClicked(){return!1}get description(){return""}isInstantProfile(){return!1}isEnabled(){return!0}getProfiles(){return this.profiles.filter(function(e){return this.profileBeingRecordedInternal!==e}.bind(this))}customContent(){return null}setCustomContentEnabled(e){}getProfile(e){for(let t=0;t<this.profiles.length;++t)if(this.profiles[t].uid===e)return this.profiles[t];return null}loadFromFile(e){let t=e.name;const i=this.fileExtension();i&&t.endsWith(i)&&(t=t.substr(0,t.length-i.length));const s=this.createProfileLoadedFromFile(t);return s.setFromFile(),this.setProfileBeingRecorded(s),this.addProfile(s),s.loadFromFile(e)}createProfileLoadedFromFile(e){throw new Error("Not implemented")}addProfile(e){this.profiles.push(e),this.dispatchEventToListeners("add-profile-header",e)}removeProfile(e){const t=this.profiles.indexOf(e);-1!==t&&(this.profiles.splice(t,1),this.disposeProfile(e))}clearTempStorage(){for(let e=0;e<this.profiles.length;++e)this.profiles[e].removeTempFile()}profileBeingRecorded(){return this.profileBeingRecordedInternal}setProfileBeingRecorded(e){this.profileBeingRecordedInternal=e}profileBeingRecordedRemoved(){}reset(){for(const e of this.profiles.slice())this.disposeProfile(e);this.profiles=[],this.nextProfileUidInternal=1}disposeProfile(e){this.dispatchEventToListeners("remove-profile-header",e),e.dispose(),this.profileBeingRecordedInternal===e&&(this.profileBeingRecordedRemoved(),this.setProfileBeingRecorded(null))}}var j=Object.freeze({__proto__:null,ProfileHeader:E,StatusUpdate:M,ProfileType:F});const H={javascriptVmInstances:"JavaScript VM instances",totalJsHeapSize:"Total JS heap size",totalPageJsHeapSizeChangeTrend:"Total page JS heap size change trend over the last {PH1} minutes.",totalPageJsHeapSizeAcrossAllVm:"Total page JS heap size across all VM instances.",changeRate:"{PH1}/s",increasingBySPerSecond:"increasing by {PH1} per second",decreasingBySPerSecond:"decreasing by {PH1} per second",heapSizeInUseByLiveJsObjects:"Heap size in use by live JS objects.",heapSizeChangeTrendOverTheLastS:"Heap size change trend over the last {PH1} minutes.",empty:"(empty)"},L=t.i18n.registerUIStrings("panels/profiler/IsolateSelector.ts",H),O=t.i18n.getLocalizedString.bind(void 0,L);class z extends r.Widget.VBox{items;list;itemByIsolate;totalElement;totalValueDiv;totalTrendDiv;constructor(){super(!1),this.items=new r.ListModel.ListModel,this.list=new r.ListControl.ListControl(this.items,this,r.ListControl.ListMode.NonViewport),this.list.element.classList.add("javascript-vm-instances-list"),r.ARIAUtils.setLabel(this.list.element,O(H.javascriptVmInstances)),this.contentElement.appendChild(this.list.element),this.itemByIsolate=new Map,this.totalElement=document.createElement("div"),this.totalElement.classList.add("profile-memory-usage-item"),this.totalElement.classList.add("hbox"),this.totalValueDiv=this.totalElement.createChild("div","profile-memory-usage-item-size"),this.totalTrendDiv=this.totalElement.createChild("div","profile-memory-usage-item-trend"),this.totalElement.createChild("div").textContent=O(H.totalJsHeapSize);const e=Math.round(a.IsolateManager.MemoryTrendWindowMs/6e4);r.Tooltip.Tooltip.install(this.totalTrendDiv,O(H.totalPageJsHeapSizeChangeTrend,{PH1:e})),r.Tooltip.Tooltip.install(this.totalValueDiv,O(H.totalPageJsHeapSizeAcrossAllVm)),a.IsolateManager.IsolateManager.instance().observeIsolates(this),a.TargetManager.TargetManager.instance().addEventListener("NameChanged",this.targetChanged,this),a.TargetManager.TargetManager.instance().addEventListener("InspectedURLChanged",this.targetChanged,this)}wasShown(){super.wasShown(),a.IsolateManager.IsolateManager.instance().addEventListener("MemoryChanged",this.heapStatsChanged,this)}willHide(){a.IsolateManager.IsolateManager.instance().removeEventListener("MemoryChanged",this.heapStatsChanged,this)}isolateAdded(e){this.list.element.tabIndex=0;const t=new B(e),i=t.model().target()===a.TargetManager.TargetManager.instance().rootTarget()?0:this.items.length;this.items.insert(i,t),this.itemByIsolate.set(e,t),(1===this.items.length||e.isMainThread())&&this.list.selectItem(t),this.update()}isolateChanged(e){const t=this.itemByIsolate.get(e);t&&t.updateTitle(),this.update()}isolateRemoved(e){const t=this.itemByIsolate.get(e);t&&this.items.remove(this.items.indexOf(t)),this.itemByIsolate.delete(e),0===this.items.length&&(this.list.element.tabIndex=-1),this.update()}targetChanged(e){const t=e.data.model(a.RuntimeModel.RuntimeModel);if(!t)return;const i=a.IsolateManager.IsolateManager.instance().isolateByModel(t),s=i&&this.itemByIsolate.get(i);s&&s.updateTitle()}heapStatsChanged(e){const t=e.data,i=this.itemByIsolate.get(t);i&&i.updateStats(),this.updateTotal()}updateTotal(){let t=0,i=0;for(const e of a.IsolateManager.IsolateManager.instance().isolates())t+=e.usedHeapSize(),i+=e.usedHeapSizeGrowRate();this.totalValueDiv.textContent=e.NumberUtilities.bytesToString(t),z.formatTrendElement(i,this.totalTrendDiv)}static formatTrendElement(t,i){const s=1e3*t;if(Math.abs(s)<1e3)return;const o=e.NumberUtilities.bytesToString(Math.abs(s));let n,a;s>0?(n="⬆"+O(H.changeRate,{PH1:o}),i.classList.toggle("increasing",!0),a=O(H.increasingBySPerSecond,{PH1:o})):(n="⬇"+O(H.changeRate,{PH1:o}),i.classList.toggle("increasing",!1),a=O(H.decreasingBySPerSecond,{PH1:o})),i.textContent=n,r.ARIAUtils.setLabel(i,a)}totalMemoryElement(){return this.totalElement}createElementForItem(e){return e.element}heightForItem(e){return console.assert(!1,"should not be called"),0}updateSelectedItemARIA(e,t){return!1}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,s){i&&i.classList.remove("selected"),s&&s.classList.add("selected");const o=t&&t.model();r.Context.Context.instance().setFlavor(a.HeapProfilerModel.HeapProfilerModel,o&&o.heapProfilerModel()),r.Context.Context.instance().setFlavor(a.CPUProfilerModel.CPUProfilerModel,o&&o.target().model(a.CPUProfilerModel.CPUProfilerModel))}update(){this.updateTotal(),this.list.invalidateRange(0,this.items.length)}}class B{isolate;element;heapDiv;trendDiv;nameDiv;constructor(e){this.isolate=e;const t=Math.round(a.IsolateManager.MemoryTrendWindowMs/6e4);this.element=document.createElement("div"),this.element.classList.add("profile-memory-usage-item"),this.element.classList.add("hbox"),r.ARIAUtils.markAsOption(this.element),this.heapDiv=this.element.createChild("div","profile-memory-usage-item-size"),r.Tooltip.Tooltip.install(this.heapDiv,O(H.heapSizeInUseByLiveJsObjects)),this.trendDiv=this.element.createChild("div","profile-memory-usage-item-trend"),r.Tooltip.Tooltip.install(this.trendDiv,O(H.heapSizeChangeTrendOverTheLastS,{PH1:t})),this.nameDiv=this.element.createChild("div","profile-memory-usage-item-name"),this.updateTitle()}model(){return this.isolate.runtimeModel()}updateStats(){this.heapDiv.textContent=e.NumberUtilities.bytesToString(this.isolate.usedHeapSize()),z.formatTrendElement(this.isolate.usedHeapSizeGrowRate(),this.trendDiv)}updateTitle(){const e=new Map;for(const t of this.isolate.models()){const i=t.target(),s=a.TargetManager.TargetManager.instance().rootTarget()!==i?i.name():"",r=new n.ParsedURL.ParsedURL(i.inspectedURL()),o=r.isValid?r.domain():"",l=i.decorateLabel(o&&s?`${o}: ${s}`:s||o||O(H.empty));e.set(l,(e.get(l)||0)+1)}this.nameDiv.removeChildren();const t=[];for(const[i,s]of e){const e=s>1?`${i} (${s})`:i;t.push(e);const o=this.nameDiv.createChild("div");o.textContent=e,r.Tooltip.Tooltip.install(o,String(e))}}}var A=Object.freeze({__proto__:null,IsolateSelector:z,ListItem:B});const G=new CSSStyleSheet;G.replaceSync('.profile-launcher-view{overflow:auto}.profile-launcher-view-content{margin:10px 16px;flex:auto 1 0}.profile-launcher-view-content h1{font-size:15px;font-weight:normal;margin:6px 0 10px}.profile-launcher-view-content [is="dt-radio"]{font-size:13px}.profile-launcher-view-content p{color:var(--sys-color-token-subtle);margin-top:1px;margin-left:22px}.profile-launcher-view-content p [is="dt-checkbox"]{display:flex}.profile-launcher-view-content button.text-button.running,\n.profile-launcher-view-content button.text-button.running:focus{color:var(--sys-color-error)}.profile-launcher-view-content > div{flex:auto 0 0}.profile-launcher-view-content > .profile-isolate-selector-block{flex:auto 1 0;overflow:hidden}.profile-isolate-selector-block button{min-width:110px}.profile-launcher-target-list{margin-bottom:6px;border:1px solid var(--sys-color-divider);flex:150px 1 0}.profile-launcher-target-list-container{overflow:auto}.profile-memory-usage-item{min-width:100%;width:max-content;padding:4px;line-height:16px}.profile-isolate-selector-block > .profile-memory-usage-item{margin-left:1px;margin-bottom:4px;font-weight:bolder}.profile-memory-usage-item.selected{background-color:var(--sys-color-neutral-container)}.profile-memory-usage-item:focus{background-color:var(--sys-color-tonal-container)}.profile-launcher-target-list .profile-memory-usage-item:hover:not(.selected){background-color:var(--sys-color-state-hover-on-subtle)}.javascript-vm-instances-list{width:max-content;min-width:100%}.javascript-vm-instances-list:focus .profile-memory-usage-item.selected{background-color:var(--sys-color-tonal-container)}.profile-memory-usage-item > div{flex-shrink:0;margin-right:12px}.profile-memory-usage-item-size{width:60px;text-align:right}.profile-memory-usage-item-trend{min-width:5em;color:var(--sys-color-tertiary)}.profile-memory-usage-item-trend.increasing{color:var(--sys-color-error)}.profile-launcher-buttons{flex-wrap:wrap;column-gap:8px}@media (forced-colors: active){.profile-memory-usage-item{forced-color-adjust:none;border-left-color:transparent}.profile-memory-usage-item-trend,\n  .profile-memory-usage-item-trend.increasing,\n  .profile-launcher-view-content button.running,\n  body.inactive .profile-launcher-view-content button.running:not(.toolbar-item){color:ButtonText}.javascript-vm-instances-list .profile-memory-usage-item:hover:not(.selected){background-color:Highlight;color:HighlightText}.javascript-vm-instances-list .profile-memory-usage-item.selected .profile-memory-usage-item-trend,\n  .javascript-vm-instances-list .profile-memory-usage-item.selected .profile-memory-usage-item-trend.increasing{color:ButtonFace}.javascript-vm-instances-list .profile-memory-usage-item:hover:not(.selected) .profile-memory-usage-item-trend,\n  .javascript-vm-instances-list .profile-memory-usage-item:hover:not(.selected) .profile-memory-usage-item-trend.increasing{background-color:Highlight;color:HighlightText}.javascript-vm-instances-list .profile-memory-usage-item.selected{background-color:ButtonText;border-color:Highlight;color:ButtonFace}.javascript-vm-instances-list:focus .profile-memory-usage-item.selected,\n  .javascript-vm-instances-list:focus-visible .profile-memory-usage-item.selected{background-color:Highlight;border-color:ButtonText;color:HighlightText}}\n/*# sourceURL=profileLauncherView.css */\n');const V={selectJavascriptVmInstance:"Select JavaScript VM instance",load:"Load profile",takeSnapshot:"Take snapshot",stop:"Stop",start:"Start",selectProfilingType:"Select profiling type"},W=t.i18n.registerUIStrings("panels/profiler/ProfileLauncherView.ts",V),U=t.i18n.getLocalizedString.bind(void 0,W);class _ extends(n.ObjectWrapper.eventMixin(r.Widget.VBox)){panel;contentElementInternal;selectedProfileTypeSetting;profileTypeHeaderElement;profileTypeSelectorForm;controlButton;loadButton;recordButtonEnabled;typeIdToOptionElementAndProfileType;isProfiling;isInstantProfile;isEnabled;constructor(e){super(),this.panel=e,this.element.classList.add("profile-launcher-view"),this.contentElementInternal=this.element.createChild("div","profile-launcher-view-content vbox");const t=this.contentElementInternal.createChild("div","vbox");this.selectedProfileTypeSetting=n.Settings.Settings.instance().createSetting("selected-profile-type","CPU"),this.profileTypeHeaderElement=t.createChild("h1"),this.profileTypeSelectorForm=t.createChild("form"),r.ARIAUtils.markAsRadioGroup(this.profileTypeSelectorForm);const i=this.contentElementInternal.createChild("div","vbox profile-isolate-selector-block");i.createChild("h1").textContent=U(V.selectJavascriptVmInstance);const s=new z,o=i.createChild("div","vbox profile-launcher-target-list");o.classList.add("profile-launcher-target-list-container"),s.show(o),i.appendChild(s.totalMemoryElement());const a=this.contentElementInternal.createChild("div","hbox profile-launcher-buttons");this.controlButton=r.UIUtils.createTextButton("",this.controlButtonClicked.bind(this),{jslogContext:"profiler.heap-toggle-recording",variant:"primary"}),this.loadButton=new d.Button.Button,this.loadButton.data={iconName:"import",variant:"outlined",jslogContext:"profiler.load-from-file"},this.loadButton.textContent=U(V.load),this.loadButton.addEventListener("click",this.loadButtonClicked.bind(this)),a.appendChild(this.loadButton),a.appendChild(this.controlButton),this.recordButtonEnabled=!0,this.typeIdToOptionElementAndProfileType=new Map}loadButtonClicked(){r.ActionRegistry.ActionRegistry.instance().getAction("profiler.load-from-file").execute()}updateControls(){this.isEnabled&&this.recordButtonEnabled?this.controlButton.removeAttribute("disabled"):this.controlButton.setAttribute("disabled",""),r.Tooltip.Tooltip.install(this.controlButton,this.recordButtonEnabled?"":r.UIUtils.anotherProfilerActiveLabel()),this.isInstantProfile?(this.controlButton.classList.remove("running"),this.controlButton.textContent=U(V.takeSnapshot)):this.isProfiling?(this.controlButton.classList.add("running"),this.controlButton.textContent=U(V.stop)):(this.controlButton.classList.remove("running"),this.controlButton.textContent=U(V.start));for(const{optionElement:e}of this.typeIdToOptionElementAndProfileType.values())e.disabled=Boolean(this.isProfiling)}profileStarted(){this.isProfiling=!0,this.updateControls()}profileFinished(){this.isProfiling=!1,this.updateControls()}updateProfileType(e,t){this.isInstantProfile=e.isInstantProfile(),this.recordButtonEnabled=t,this.isEnabled=e.isEnabled(),this.updateControls()}addProfileType(e){const t=r.UIUtils.createRadioLabel("profile-type",e.name,void 0,"profiler.profile-type");this.profileTypeSelectorForm.appendChild(t);const i=t.radioElement;this.typeIdToOptionElementAndProfileType.set(e.id,{optionElement:i,profileType:e}),i.addEventListener("change",this.profileTypeChanged.bind(this,e),!1);this.profileTypeSelectorForm.createChild("p").textContent=e.description,r.ARIAUtils.setDescription(i,e.description);const s=e.customContent();s&&(s.setAttribute("role","group"),s.setAttribute("aria-labelledby",`${i.id}`),this.profileTypeSelectorForm.createChild("p").appendChild(s),e.setCustomContentEnabled(!1));const o=this.typeIdToOptionElementAndProfileType.size>1?U(V.selectProfilingType):e.name;this.profileTypeHeaderElement.textContent=o,r.ARIAUtils.setLabel(this.profileTypeSelectorForm,o)}restoreSelectedProfileType(){let e=this.selectedProfileTypeSetting.get();this.typeIdToOptionElementAndProfileType.has(e)||(e=this.typeIdToOptionElementAndProfileType.keys().next().value,this.selectedProfileTypeSetting.set(e));const t=this.typeIdToOptionElementAndProfileType.get(e);t.optionElement.checked=!0;const i=t.profileType;for(const[t,{profileType:i}]of this.typeIdToOptionElementAndProfileType){const s=t===e;i.setCustomContentEnabled(s)}this.dispatchEventToListeners("ProfileTypeSelected",i)}controlButtonClicked(){this.panel.toggleRecord()}profileTypeChanged(e){const t=this.selectedProfileTypeSetting.get();this.typeIdToOptionElementAndProfileType.get(t).profileType.setCustomContentEnabled(!1),e.setCustomContentEnabled(!0),this.dispatchEventToListeners("ProfileTypeSelected",e),this.isInstantProfile=e.isInstantProfile(),this.isEnabled=e.isEnabled(),this.updateControls(),this.selectedProfileTypeSetting.set(e.id)}wasShown(){super.wasShown(),this.registerCSSFiles([G])}}var $=Object.freeze({__proto__:null,ProfileLauncherView:_});const J={profileOptions:"Profile options"},q=t.i18n.registerUIStrings("panels/profiler/ProfileSidebarTreeElement.ts",J),Q=t.i18n.getLocalizedString.bind(void 0,q);class K extends r.TreeOutline.TreeElement{iconElement;titlesElement;menuElement;titleContainer;titleElement;subtitleElement;className;small;dataDisplayDelegate;profile;saveLinkElement;editing;constructor(e,t,i){super("",!1),this.iconElement=document.createElement("div"),this.iconElement.classList.add("icon"),this.titlesElement=document.createElement("div"),this.titlesElement.classList.add("titles"),this.titlesElement.classList.add("no-subtitle"),this.titlesElement.setAttribute("jslog",`${l.value("title").track({dblclick:!0,change:!0})}`),this.titleContainer=this.titlesElement.createChild("span","title-container"),this.titleElement=this.titleContainer.createChild("span","title"),this.subtitleElement=this.titlesElement.createChild("span","subtitle"),this.menuElement=new d.Button.Button,this.menuElement.data={variant:"icon",iconName:"dots-vertical",title:Q(J.profileOptions)},this.menuElement.tabIndex=-1,this.menuElement.addEventListener("click",this.handleContextMenuEvent.bind(this)),this.menuElement.setAttribute("jslog",`${l.dropDown("profile-options").track({click:!0})}`),this.titleElement.textContent=t.title,this.className=i,this.small=!1,this.dataDisplayDelegate=e,this.profile=t,t.addEventListener("UpdateStatus",this.updateStatus,this)}updateStatus(e){const t=e.data;null!==t.subtitle&&(this.subtitleElement.textContent=t.subtitle.length>0?`(${t.subtitle})`:"",this.titlesElement.classList.toggle("no-subtitle",!t.subtitle),r.ARIAUtils.setLabel(this.listItemElement,`${this.profile.title}, ${t.subtitle}`)),"boolean"==typeof t.wait&&this.listItemElement&&(this.iconElement.classList.toggle("spinner",t.wait),this.listItemElement.classList.toggle("wait",t.wait))}ondblclick(e){return this.editing||this.startEditing(e.target),!1}startEditing(e){const t=e.enclosingNodeOrSelfWithClass("title");if(!t)return;const i=new r.InplaceEditor.Config(this.editingCommitted.bind(this),this.editingCancelled.bind(this));this.editing=r.InplaceEditor.InplaceEditor.startEditing(t,i)}editingCommitted(e,t){delete this.editing,this.profile.setTitle(t)}editingCancelled(){delete this.editing}dispose(){this.profile.removeEventListener("UpdateStatus",this.updateStatus,this)}onselect(){return this.dataDisplayDelegate.showProfile(this.profile),!0}ondelete(){return this.profile.profileType().removeProfile(this.profile),!0}onattach(){this.className&&this.listItemElement.classList.add(this.className),this.small&&this.listItemElement.classList.add("small"),this.listItemElement.append(this.iconElement,this.titlesElement,this.menuElement),this.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!0),r.ARIAUtils.setDescription(this.listItemElement,this.profile.profileType().name)}handleContextMenuEvent(e){const t=new r.ContextMenu.ContextMenu(e);t.appendItemsAtLocation("profilerMenu"),t.show()}setSmall(e){this.small=e,this.listItemElement&&this.listItemElement.classList.toggle("small",this.small)}setMainTitle(e){this.titleElement.textContent=e}}var Y=Object.freeze({__proto__:null,ProfileSidebarTreeElement:K});const Z=new CSSStyleSheet;Z.replaceSync("#profile-views{flex:auto;position:relative}.profile-view .data-grid table.data{background:var(--sys-color-cdt-base-container)}.profile-view .data-grid tr:not(.selected) .highlight{background-color:var(--sys-color-tonal-container)}.profile-view .data-grid tr:hover td:not(.bottom-filler-td){background-color:var(--sys-color-state-hover-on-subtle)}.profile-view .data-grid td.numeric-column{text-align:right}.profile-view .data-grid div.profile-multiple-values{float:right}.profile-view .data-grid span.percent-column{color:var(--sys-color-token-subtle);width:9ex;display:inline-block}.profile-view .data-grid tr.selected span{color:inherit}.profiles-toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider);flex-shrink:0}.profiles-tree-sidebar{flex:auto;overflow:hidden}.profiles-sidebar-tree-box{overflow-y:auto}.profile-view{display:flex;overflow:hidden}.profile-view .data-grid{border:none;flex:auto}.profile-view .data-grid th.self-column,\n.profile-view .data-grid th.total-column{text-align:center}.profile-node-file{float:right;color:var(--sys-color-token-subtle)}.profile-warn-marker{vertical-align:-1px;margin-right:2px}.cpu-profile-flame-chart-overview-container{overflow:hidden;position:absolute;top:0;width:100%;height:80px}#cpu-profile-flame-chart-overview-container{border-bottom:1px solid var(--sys-color-divider);overflow:hidden}.cpu-profile-flame-chart-overview-canvas{position:absolute;top:20px;left:0;right:0;bottom:0}#cpu-profile-flame-chart-overview-grid .resources-dividers-label-bar{pointer-events:auto}.cpu-profile-flame-chart-overview-pane{flex:0 0 80px!important}.profile-text-view{padding:10px;overflow:auto;margin:0;user-select:text;cursor:text}.empty-landing-page{position:absolute;background-color:var(--sys-color-cdt-base-container);justify-content:center;align-items:center;overflow:auto;font-size:16px;color:var(--sys-color-token-subtle);padding:50px}button{cursor:pointer}@media (forced-colors: active){.profile-view .data-grid tr:hover td:not(.bottom-filler-td){background:Highlight}.profile-view .data-grid table.data{background:transparent}}\n/*# sourceURL=profilesPanel.css */\n");const X=new CSSStyleSheet;X.replaceSync(':host{padding:var(--sys-size-3) 0}.tree-outline-disclosure{width:100%}li .icon{width:20px;height:20px;margin-right:var(--sys-size-6);flex:none}.heap-snapshot-sidebar-tree-item .icon,\n.profile-sidebar-tree-item .icon{mask-image:var(--image-file-heap-snapshot);background:var(--icon-default)}.profile-group-sidebar-tree-item .icon{mask-image:var(--image-file-heap-snapshots);background:var(--icon-default)}li.small .icon{width:16px;height:16px}li.wait .icon{content:none}.heap-snapshot-sidebar-tree-item.wait .icon{mask-image:unset;background-color:inherit}.heap-snapshot-sidebar-tree-item.small .icon{mask-image:var(--image-file-heap-snapshots);background:var(--icon-default)}.profile-sidebar-tree-item.small .icon{mask-image:var(--image-file-heap-snapshots);background:var(--icon-default)}.tree-outline li:not(.parent)::before{content:none}ol.tree-outline{flex:auto;padding:0}.tree-outline li{height:var(--sys-size-12);padding-left:var(--sys-size-7);margin-right:var(--sys-size-5);color:var(--sys-color-on-surface);& .leading-icons{margin-right:var(--sys-size-6);flex:none}& .selection{border-radius:0 100px 100px 0}}.tree-outline .profile-launcher-view-tree-item,\n.tree-outline li.profiles-tree-section + .children > li{border-radius:0 100px 100px 0;position:relative;&.selected{background-color:var(--app-color-navigation-drawer-background-selected);color:var(--app-color-navigation-drawer-label-selected);& devtools-icon{color:var(--app-color-navigation-drawer-label-selected)}& > .icon:not(.spinner){background-color:var(--app-color-navigation-drawer-label-selected)}}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle);mask-image:none;content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:focus-visible{box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}}.tree-outline li.profiles-tree-section{margin-top:var(--sys-size-6);line-height:var(--sys-size-8);&:hover:not(:has(span[is="dt-checkbox"])) .selection{background-color:transparent}}.tree-outline li.profiles-tree-section::before{display:none}.tree-outline ol{overflow:hidden;padding:0}li.wait .spinner::before{--dimension:20px;margin:0}li.wait.small .spinner::before{--dimension:14px;--clip-size:9px;--override-spinner-size:2px;margin:1px}li.wait.selected .spinner::before{--override-spinner-color:var(--ref-palette-neutral100)}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}li.small{height:20px}li .titles{display:inline-flex;padding-right:var(--sys-size-5)}li .titles > .title-container{z-index:1;text-overflow:ellipsis;overflow:hidden}li.small .titles{top:2px;line-height:normal}li:not(.small) .title::after{content:"\\A";white-space:pre}li .subtitle{text-overflow:ellipsis;overflow:hidden;margin-left:var(--sys-size-3)}li.small .subtitle{display:none}li devtools-button{min-width:var(--sys-size-12);visibility:hidden}li.selected:hover devtools-button{visibility:visible;margin-left:auto}@media (forced-colors: active){.tree-outline li,\n  .tree-outline li.profiles-tree-section,\n  .tree-outline li:hover .tree-element-title{forced-color-adjust:none;color:ButtonText;text-shadow:unset}.tree-outline .profile-launcher-view-tree-item,\n  .tree-outline li.profiles-tree-section + .children > li{&.selected{background-color:Highlight;color:HighlightText;& devtools-icon{color:HighlightText}& > .icon:not(.spinner){background-color:HighlightText}}}}\n/*# sourceURL=profilesSidebarTree.css */\n');const ee={cantLoadFileSupportedFile:"Can’t load file. Supported file extensions: ''{PH1}''.",cantLoadProfileWhileAnother:"Can’t load profile while another profile is being recorded.",profileLoadingFailedS:"Profile loading failed: {PH1}.",runD:"Run {PH1}",profiles:"Profiles"},te=t.i18n.registerUIStrings("panels/profiler/ProfilesPanel.ts",ee),ie=t.i18n.getLocalizedString.bind(void 0,te);class se extends r.Panel.PanelWithSidebar{profileTypes;profilesItemTreeElement;sidebarTree;profileViews;toolbarElement;toggleRecordAction;toggleRecordButton;#e;profileViewToolbar;profileGroups;launcherView;visibleView;profileToView;typeIdToSidebarSection;fileSelectorElement;selectedProfileType;constructor(e,t,i){super(e),this.profileTypes=t;const s=new r.Widget.VBox;this.splitWidget().setMainWidget(s),this.profilesItemTreeElement=new ae(this),this.sidebarTree=new r.TreeOutline.TreeOutlineInShadow,this.sidebarTree.element.classList.add("profiles-sidebar-tree-box"),this.panelSidebarElement().appendChild(this.sidebarTree.element),this.sidebarTree.appendChild(this.profilesItemTreeElement),this.sidebarTree.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.profileViews=document.createElement("div"),this.profileViews.id="profile-views",this.profileViews.classList.add("vbox"),s.element.appendChild(this.profileViews),this.toolbarElement=document.createElement("div"),this.toolbarElement.classList.add("profiles-toolbar"),s.element.insertBefore(this.toolbarElement,s.element.firstChild),this.panelSidebarElement().classList.add("profiles-tree-sidebar");const o=document.createElement("div");o.classList.add("profiles-toolbar"),o.setAttribute("jslog",`${l.toolbar("profiles-sidebar")}`),this.panelSidebarElement().insertBefore(o,this.panelSidebarElement().firstChild);const n=new r.Toolbar.Toolbar("",o);n.makeWrappable(!0),this.toggleRecordAction=r.ActionRegistry.ActionRegistry.instance().getAction(i),this.toggleRecordButton=r.Toolbar.Toolbar.createActionButton(this.toggleRecordAction),n.appendToolbarItem(this.toggleRecordButton),n.appendToolbarItem(r.Toolbar.Toolbar.createActionButtonForId("profiler.clear-all")),n.appendSeparator(),n.appendToolbarItem(r.Toolbar.Toolbar.createActionButtonForId("profiler.load-from-file")),this.#e=r.ActionRegistry.ActionRegistry.instance().getAction("profiler.save-to-file"),this.#e.setEnabled(!1),n.appendToolbarItem(r.Toolbar.Toolbar.createActionButton(this.#e)),n.appendSeparator(),n.appendToolbarItem(r.Toolbar.Toolbar.createActionButtonForId("components.collect-garbage")),this.profileViewToolbar=new r.Toolbar.Toolbar("",this.toolbarElement),this.profileViewToolbar.makeWrappable(!0),this.profileViewToolbar.element.setAttribute("jslog",`${l.toolbar("profile-view")}`),this.profileGroups={},this.launcherView=new _(this),this.launcherView.addEventListener("ProfileTypeSelected",this.onProfileTypeSelected,this),this.profileToView=[],this.typeIdToSidebarSection={};const d=this.profileTypes;for(let e=0;e<d.length;e++)this.registerProfileType(d[e]);this.launcherView.restoreSelectedProfileType(),this.profilesItemTreeElement.select(),this.showLauncherView(),this.createFileSelectorElement(),a.TargetManager.TargetManager.instance().addEventListener("SuspendStateChanged",this.onSuspendStateChanged,this),r.Context.Context.instance().addFlavorChangeListener(a.CPUProfilerModel.CPUProfilerModel,this.updateProfileTypeSpecificUI,this),r.Context.Context.instance().addFlavorChangeListener(a.HeapProfilerModel.HeapProfilerModel,this.updateProfileTypeSpecificUI,this)}onKeyDown(e){const t=e;let i=!1;"ArrowDown"!==t.key||t.altKey?"ArrowUp"!==t.key||t.altKey||(i=this.sidebarTree.selectPrevious()):i=this.sidebarTree.selectNext(),i&&t.consume(!0)}searchableView(){const e=this.visibleView;return e&&e.searchableView?e.searchableView():null}createFileSelectorElement(){this.fileSelectorElement&&this.element.removeChild(this.fileSelectorElement),this.fileSelectorElement=r.UIUtils.createFileSelectorElement(this.loadFromFile.bind(this)),this.element.appendChild(this.fileSelectorElement)}findProfileTypeByExtension(e){return this.profileTypes.find((t=>Boolean(t.fileExtension())&&e.endsWith(t.fileExtension()||"")))||null}async loadFromFile(e){this.createFileSelectorElement();const t=this.findProfileTypeByExtension(e.name);if(!t){const e=new Set(this.profileTypes.map((e=>e.fileExtension())).filter((e=>e)));return void n.Console.Console.instance().error(ie(ee.cantLoadFileSupportedFile,{PH1:Array.from(e).join("', '")}))}if(Boolean(t.profileBeingRecorded()))return void n.Console.Console.instance().error(ie(ee.cantLoadProfileWhileAnother));const i=await t.loadFromFile(e);i&&"message"in i&&r.UIUtils.MessageDialog.show(ie(ee.profileLoadingFailedS,{PH1:i.message}),void 0,"profile-loading-failed")}toggleRecord(){if(!this.toggleRecordAction.enabled())return!0;const t=e.DOMUtilities.deepActiveElement(this.element.ownerDocument),i=this.selectedProfileType;if(!i)return!0;const s=i.buttonClicked();return this.updateToggleRecordAction(s),s?(this.launcherView.profileStarted(),i.hasTemporaryView()&&this.showProfile(i.profileBeingRecorded())):this.launcherView.profileFinished(),t&&t.focus(),!0}onSuspendStateChanged(){this.updateToggleRecordAction(this.toggleRecordAction.toggled())}updateToggleRecordAction(e){const t=Boolean(r.Context.Context.instance().flavor(a.CPUProfilerModel.CPUProfilerModel)||r.Context.Context.instance().flavor(a.HeapProfilerModel.HeapProfilerModel)),i=e||!a.TargetManager.TargetManager.instance().allTargetsSuspended()&&t;this.toggleRecordAction.setEnabled(i),this.toggleRecordAction.setToggled(e),i?this.toggleRecordButton.setTitle(this.selectedProfileType?this.selectedProfileType.buttonTooltip:""):this.toggleRecordButton.setTitle(r.UIUtils.anotherProfilerActiveLabel()),this.selectedProfileType&&this.launcherView.updateProfileType(this.selectedProfileType,i)}profileBeingRecordedRemoved(){this.updateToggleRecordAction(!1),this.launcherView.profileFinished()}onProfileTypeSelected(e){this.selectedProfileType=e.data,this.updateProfileTypeSpecificUI()}updateProfileTypeSpecificUI(){this.updateToggleRecordAction(this.toggleRecordAction.toggled())}reset(){this.profileTypes.forEach((e=>e.reset())),delete this.visibleView,this.profileGroups={},this.updateToggleRecordAction(!1),this.launcherView.profileFinished(),this.sidebarTree.element.classList.remove("some-expandable"),this.launcherView.detach(),this.profileViews.removeChildren(),this.profileViewToolbar.removeToolbarItems(),this.profilesItemTreeElement.select(),this.showLauncherView()}showLauncherView(){this.closeVisibleView(),this.profileViewToolbar.removeToolbarItems(),this.launcherView.show(this.profileViews),this.visibleView=this.launcherView,this.toolbarElement.classList.add("hidden"),this.#e.setEnabled(!1)}registerProfileType(e){this.launcherView.addProfileType(e);const t=new re(this,e);this.typeIdToSidebarSection[e.id]=t,this.sidebarTree.appendChild(t),e.addEventListener("view-updated",this.updateProfileTypeSpecificUI,this),e.addEventListener("add-profile-header",(function(e){this.addProfileHeader(e.data)}),this),e.addEventListener("remove-profile-header",(function(e){this.removeProfileHeader(e.data)}),this),e.addEventListener("profile-complete",(function(e){this.showProfile(e.data)}),this);const i=e.getProfiles();for(let e=0;e<i.length;e++)this.addProfileHeader(i[e])}showLoadFromFileDialog(){this.fileSelectorElement.click()}addProfileHeader(e){const t=e.profileType().id;this.typeIdToSidebarSection[t].addProfileHeader(e),this.visibleView&&this.visibleView!==this.launcherView||this.showProfile(e)}removeProfileHeader(e){e.profileType().profileBeingRecorded()===e&&this.profileBeingRecordedRemoved();const t=this.indexOfViewForProfile(e);-1!==t&&this.profileToView.splice(t,1);const i=e.profileType().id;this.typeIdToSidebarSection[i].removeProfileHeader(e)&&(this.profilesItemTreeElement.select(),this.showLauncherView())}showProfile(e){if(!e||e.profileType().profileBeingRecorded()===e&&!e.profileType().hasTemporaryView())return null;const t=this.viewForProfile(e);if(t===this.visibleView)return t;this.closeVisibleView(),r.Context.Context.instance().setFlavor(E,e),this.#e.setEnabled(e.canSaveToFile()),t.show(this.profileViews),this.toolbarElement.classList.remove("hidden"),this.visibleView=t;const i=this.typeIdToSidebarSection[e.profileType().id].sidebarElementForProfile(e);return i&&i.revealAndSelect(),this.profileViewToolbar.removeToolbarItems(),t.toolbarItems().then((e=>{e.map((e=>this.profileViewToolbar.appendToolbarItem(e)))})),t}showObject(e,t){}async linkifyObject(e){return null}viewForProfile(e){const t=this.indexOfViewForProfile(e);if(-1!==t)return this.profileToView[t].view;const i=e.createView(this);return i.element.classList.add("profile-view"),this.profileToView.push({profile:e,view:i}),i}indexOfViewForProfile(e){return this.profileToView.findIndex((t=>t.profile===e))}closeVisibleView(){r.Context.Context.instance().setFlavor(E,null),this.#e.setEnabled(!1),this.visibleView&&this.visibleView.detach(),delete this.visibleView}focus(){this.sidebarTree.focus()}wasShown(){super.wasShown(),r.Context.Context.instance().setFlavor(se,this),this.registerCSSFiles([k,Z,D]),this.sidebarTree.registerCSSFiles([X])}willHide(){r.Context.Context.instance().setFlavor(se,null),super.willHide()}}class re extends r.TreeOutline.TreeElement{dataDisplayDelegate;profileTreeElements;profileGroups;constructor(e,t){super(t.treeItemTitle,!0),this.selectable=!1,this.dataDisplayDelegate=e,this.profileTreeElements=[],this.profileGroups={},this.expand(),this.hidden=!0,this.setCollapsible(!1)}addProfileHeader(e){this.hidden=!1;const t=e.profileType();let i=this;const s=e.createSidebarTreeElement(this.dataDisplayDelegate);if(this.profileTreeElements.push(s),!e.fromFile()&&t.profileBeingRecorded()!==e){const t=e.title;let r=this.profileGroups[t];r||(r=new oe,this.profileGroups[t]=r),r.profileSidebarTreeElements.push(s);const o=r.profileSidebarTreeElements.length;if(2===o){r.sidebarTreeElement=new ne(this.dataDisplayDelegate,e.title);const t=r.profileSidebarTreeElements[0],i=this.children().indexOf(t);this.insertChild(r.sidebarTreeElement,i);const s=t.selected;this.removeChild(t),r.sidebarTreeElement.appendChild(t),s&&t.revealAndSelect(),t.setSmall(!0),t.setMainTitle(ie(ee.runD,{PH1:1})),this.treeOutline&&this.treeOutline.element.classList.add("some-expandable")}o>=2&&(i=r.sidebarTreeElement,s.setSmall(!0),s.setMainTitle(ie(ee.runD,{PH1:o})))}i&&i.appendChild(s)}removeProfileHeader(e){const t=this.sidebarElementIndex(e);if(-1===t)return!1;const i=this.profileTreeElements[t];this.profileTreeElements.splice(t,1);let s=this;const r=this.profileGroups[e.title];if(r){const t=r.profileSidebarTreeElements;if(t.splice(t.indexOf(i),1),1===t.length){const i=s.children().indexOf(r.sidebarTreeElement);r.sidebarTreeElement&&r.sidebarTreeElement.removeChild(t[0]),this.insertChild(t[0],i),t[0].setSmall(!1),t[0].setMainTitle(e.title),r.sidebarTreeElement&&this.removeChild(r.sidebarTreeElement)}0!==t.length&&(s=r.sidebarTreeElement)}return s&&s.removeChild(i),i.dispose(),!this.childCount()&&(this.hidden=!0,!0)}sidebarElementForProfile(e){const t=this.sidebarElementIndex(e);return-1===t?null:this.profileTreeElements[t]}sidebarElementIndex(e){const t=this.profileTreeElements;for(let i=0;i<t.length;i++)if(t[i].profile===e)return i;return-1}onattach(){this.listItemElement.classList.add("profiles-tree-section")}}class oe{profileSidebarTreeElements;sidebarTreeElement;constructor(){this.profileSidebarTreeElements=[],this.sidebarTreeElement=null}}class ne extends r.TreeOutline.TreeElement{dataDisplayDelegate;profileTitle;toggleOnClick;constructor(e,t){super("",!0),this.selectable=!1,this.dataDisplayDelegate=e,this.profileTitle=t,this.expand(),this.toggleOnClick=!0}onselect(){const e=this.childCount()>0;if(e){const e=this.lastChild();e instanceof K&&this.dataDisplayDelegate.showProfile(e.profile)}return e}onattach(){this.listItemElement.classList.add("profile-group-sidebar-tree-item"),this.listItemElement.createChild("div","icon"),this.listItemElement.createChild("div","titles no-subtitle").createChild("span","title-container").createChild("span","title").textContent=this.profileTitle}}class ae extends r.TreeOutline.TreeElement{panel;constructor(e){super("",!1),this.selectable=!0,this.panel=e}onselect(){return this.panel.showLauncherView(),!0}onattach(){this.listItemElement.classList.add("profile-launcher-view-tree-item"),this.listItemElement.createChild("div","titles no-subtitle").createChild("span","title-container").createChild("span","title").textContent=ie(ee.profiles),this.setLeadingIcons([i.Icon.create("document")])}}var le=Object.freeze({__proto__:null,ProfilesPanel:se,ProfileTypeSidebarSection:re,ProfileGroup:oe,ProfileGroupSidebarTreeElement:ne,ProfilesSidebarTreeElement:ae,ActionDelegate:class{handleAction(e,t){switch(t){case"profiler.clear-all":{const t=e.flavor(se);return null!==t&&(t.reset(),!0)}case"profiler.load-from-file":{const t=e.flavor(se);return null!==t&&(t.showLoadFromFileDialog(),!0)}case"profiler.save-to-file":{const t=e.flavor(E);return null!==t&&(t.saveToFile(),!0)}case"profiler.delete-profile":{const t=e.flavor(E);return null!==t&&(t.profileType().removeProfile(t),!0)}}return!1}}});class de extends(n.ObjectWrapper.eventMixin(r.Widget.VBox)){overviewCalculator;overviewContainer;overviewGrid;overviewCanvas;windowLeft;windowRight;yScale;xScale;profileSamples;running;updateOverviewCanvas;updateGridTimerId;updateTimerId;windowWidth;constructor(){super(),this.element.id="heap-recording-view",this.element.classList.add("heap-tracking-overview"),this.element.setAttribute("jslog",`${l.section("heap-tracking-overview")}`),this.overviewCalculator=new pe,this.overviewContainer=this.element.createChild("div","heap-overview-container"),this.overviewGrid=new p.OverviewGrid.OverviewGrid("heap-recording",this.overviewCalculator),this.overviewGrid.element.classList.add("fill"),this.overviewCanvas=this.overviewContainer.createChild("canvas","heap-recording-overview-canvas"),this.overviewContainer.appendChild(this.overviewGrid.element),this.overviewGrid.addEventListener("WindowChanged",this.onWindowChanged,this),this.windowLeft=0,this.windowRight=1,this.overviewGrid.setWindow(this.windowLeft,this.windowRight),this.yScale=new he,this.xScale=new he,this.profileSamples=new ce}start(){this.running=!0;const e=()=>{this.update(),this.running&&this.element.window().requestAnimationFrame(e)};e()}stop(){this.running=!1}setSamples(e){this.profileSamples=e,this.running||this.update()}drawOverviewCanvas(t,i){if(!this.profileSamples)return;const s=this.profileSamples,r=s.sizes,o=s.max,n=s.timestamps,a=n[0],l=this.xScale.nextScale(t/s.totalTime);let d=0;function h(e,t){let i=0,s=0;for(let r=1;r<n.length;++r){const o=Math.floor((n[r]-a)*l);o!==s&&(i&&t(s,i),i=0,s=o),i+=e[r]}t(s,i)}h(r,(function(e,t){d=Math.max(d,t)}));const c=this.yScale.nextScale(d?i/(1.1*d):0);this.overviewCanvas.width=t*window.devicePixelRatio,this.overviewCanvas.height=i*window.devicePixelRatio,this.overviewCanvas.style.width=t+"px",this.overviewCanvas.style.height=i+"px";const p=this.overviewCanvas.getContext("2d");if(!p)throw new Error("Failed to get canvas context");const u=p;if(u.scale(window.devicePixelRatio,window.devicePixelRatio),this.running){u.beginPath(),u.lineWidth=2,u.strokeStyle="rgba(192, 192, 192, 0.6)";const e=(Date.now()-a)*l;u.moveTo(e,i-1),u.lineTo(e,0),u.stroke(),u.closePath()}let m,f=0;if(c){const e=(i-14)/c;m=Math.pow(1024,Math.floor(Math.log(e)/Math.log(1024))),m*=Math.pow(10,Math.floor(Math.log(e/m)/Math.LN10)),5*m<=e&&(m*=5),f=Math.round(i-m*c-.5)+.5,u.beginPath(),u.lineWidth=1,u.strokeStyle="rgba(0, 0, 0, 0.2)",u.moveTo(0,f),u.lineTo(t,f),u.stroke(),u.closePath()}function g(e,t){u.moveTo(e,i-1),u.lineTo(e,Math.round(i-t*c-1))}if(u.beginPath(),u.lineWidth=2,u.strokeStyle="rgba(192, 192, 192, 0.6)",h(o,g),u.stroke(),u.closePath(),u.beginPath(),u.lineWidth=2,u.strokeStyle="rgba(0, 0, 192, 0.8)",h(r,g),u.stroke(),u.closePath(),m){const t=e.NumberUtilities.bytesToString(m),i=4,s=0,r=f-.5,o=2*i+u.measureText(t).width;u.beginPath(),u.textBaseline="bottom",u.font="10px "+window.getComputedStyle(this.element,null).getPropertyValue("font-family"),u.fillStyle="rgba(255, 255, 255, 0.75)",u.fillRect(s,r-14,o,14),u.fillStyle="rgb(64, 64, 64)",u.fillText(t,s+i,r),u.fill(),u.closePath()}}onResize(){this.updateOverviewCanvas=!0,this.scheduleUpdate()}onWindowChanged(){this.updateGridTimerId||(this.updateGridTimerId=window.setTimeout(this.updateGrid.bind(this),10))}scheduleUpdate(){this.updateTimerId||(this.updateTimerId=window.setTimeout(this.update.bind(this),10))}updateBoundaries(){this.windowLeft=this.overviewGrid.windowLeft(),this.windowRight=this.overviewGrid.windowRight(),this.windowWidth=this.windowRight-this.windowLeft}update(){this.updateTimerId=null,this.isShowing()&&(this.updateBoundaries(),this.overviewCalculator.updateBoundaries(this),this.overviewGrid.updateDividers(this.overviewCalculator),this.drawOverviewCanvas(this.overviewContainer.clientWidth,this.overviewContainer.clientHeight-20))}updateGrid(){this.updateGridTimerId=0,this.updateBoundaries();const t=this.profileSamples.ids;if(!t.length)return;const i=this.profileSamples.timestamps,s=this.profileSamples.sizes,r=i[0],o=this.profileSamples.totalTime,n=r+o*this.windowLeft,a=r+o*this.windowRight,l=e.ArrayUtilities.lowerBound(i,n,e.ArrayUtilities.DEFAULT_COMPARATOR),d=e.ArrayUtilities.upperBound(i,a,e.ArrayUtilities.DEFAULT_COMPARATOR);let h=0;for(let e=l;e<d;++e)h+=s[e];const c=l>0?t[l-1]:0,p=d<t.length?t[d]:1/0;this.dispatchEventToListeners("IdsRangeChanged",{minId:c,maxId:p,size:h})}}class he{lastUpdate;currentScale;constructor(){this.lastUpdate=0,this.currentScale=0}nextScale(t){if(t=t||this.currentScale,this.currentScale){const i=Date.now(),s=i-this.lastUpdate;this.lastUpdate=i;const r=20,o=Math.pow(r,s/1e3),n=t/this.currentScale;this.currentScale*=e.NumberUtilities.clamp(n,1/o,o)}else this.currentScale=t;return this.currentScale}}class ce{sizes;ids;timestamps;max;totalTime;constructor(){this.sizes=[],this.ids=[],this.timestamps=[],this.max=[],this.totalTime=3e4}}class pe{maximumBoundaries;minimumBoundaries;xScaleFactor;constructor(){this.maximumBoundaries=0,this.minimumBoundaries=0,this.xScaleFactor=0}updateBoundaries(e){this.minimumBoundaries=0,this.maximumBoundaries=e.profileSamples.totalTime,this.xScaleFactor=e.overviewContainer.clientWidth/this.maximumBoundaries}computePosition(e){return(e-this.minimumBoundaries)*this.xScaleFactor}formatValue(e,i){return t.TimeUtilities.secondsToString(e/1e3,Boolean(i))}maximumBoundary(){return this.maximumBoundaries}minimumBoundary(){return this.minimumBoundaries}zeroTime(){return this.minimumBoundaries}boundarySpan(){return this.maximumBoundaries-this.minimumBoundaries}}var ue=Object.freeze({__proto__:null,HeapTimelineOverview:de,SmoothScale:he,Samples:ce,OverviewCalculator:pe});let me=null;class fe{colorGeneratorInternal;maxStackDepthInternal;timelineDataInternal;entryNodes;#t;boldFont;constructor(){this.colorGeneratorInternal=fe.colorGenerator(),this.maxStackDepthInternal=0,this.timelineDataInternal=null,this.entryNodes=[],this.#t=`${p.Font.DEFAULT_FONT_SIZE} ${p.Font.getFontFamilyForCanvas()}`}static colorGenerator(){return me||(me=new n.Color.Generator({min:30,max:330,count:void 0},{min:50,max:80,count:5},{min:80,max:90,count:3}),me.setColorForID("(idle)","hsl(0, 0%, 94%)"),me.setColorForID("(program)","hsl(0, 0%, 80%)"),me.setColorForID("(garbage collector)","hsl(0, 0%, 80%)")),me}minimumBoundary(){throw"Not implemented."}totalTime(){throw"Not implemented."}formatValue(e,i){return t.TimeUtilities.preciseMillisToString(e,i)}maxStackDepth(){return this.maxStackDepthInternal}hasTrackConfigurationMode(){return!1}timelineData(){return this.timelineDataInternal||this.calculateTimelineData()}calculateTimelineData(){throw"Not implemented."}prepareHighlightedEntryInfo(e){throw"Not implemented."}canJumpToEntry(e){return"0"!==this.entryNodes[e].scriptId}entryTitle(e){const t=this.entryNodes[e];return r.UIUtils.beautifyFunctionName(t.functionName)}entryFont(e){const t="bold "+this.#t;return this.entryHasDeoptReason(e)?t:this.#t}entryHasDeoptReason(e){throw"Not implemented."}entryColor(e){const t=this.entryNodes[e];return this.colorGeneratorInternal.colorForID(t.url||("0"!==t.scriptId?t.scriptId:t.functionName))}decorateEntry(e,t,i,s,r,o,n){return!1}forceDecoration(e){return!1}textColor(e){return"#333"}entryNodesLength(){return this.entryNodes.length}}class ge extends(n.ObjectWrapper.eventMixin(r.Widget.VBox)){searchableView;overviewPane;mainPane;entrySelected;dataProvider;searchResults;searchResultIndex=-1;constructor(e,t){super(),this.element.id="cpu-flame-chart",this.searchableView=e,this.overviewPane=new Se(t),this.overviewPane.show(this.element),this.mainPane=new p.FlameChart.FlameChart(t,this.overviewPane),this.mainPane.setBarHeight(15),this.mainPane.setTextBaseline(4),this.mainPane.setTextPadding(2),this.mainPane.show(this.element),this.mainPane.addEventListener("EntrySelected",this.onEntrySelected,this),this.mainPane.addEventListener("EntryInvoked",this.onEntryInvoked,this),this.entrySelected=!1,this.mainPane.addEventListener("CanvasFocused",this.onEntrySelected,this),this.overviewPane.addEventListener("WindowChanged",this.onWindowChanged,this),this.dataProvider=t,this.searchResults=[]}focus(){this.mainPane.focus()}onWindowChanged(e){const{windowTimeLeft:t,windowTimeRight:i}=e.data;this.mainPane.setWindowTimes(t,i,!0)}selectRange(e,t){this.overviewPane.selectRange(e,t)}onEntrySelected(e){if(e.data){const t=e.data;this.mainPane.setSelectedEntry(t),this.entrySelected=-1!==t}else this.entrySelected||(this.mainPane.setSelectedEntry(0),this.entrySelected=!0)}onEntryInvoked(e){this.onEntrySelected(e),this.dispatchEventToListeners("EntryInvoked",e.data)}update(){this.overviewPane.update(),this.mainPane.update()}performSearch(t,i,s){const r=e.StringUtilities.createPlainTextSearchRegex(t.query,t.caseSensitive?"":"i"),o=-1!==this.searchResultIndex?this.searchResults[this.searchResultIndex]:-1;this.searchResults=[];const n=this.dataProvider.entryNodesLength();for(let e=0;e<n;++e)this.dataProvider.entryTitle(e).match(r)&&this.searchResults.push(e);this.searchResults.length?(this.searchResultIndex=this.searchResults.indexOf(o),-1===this.searchResultIndex&&(this.searchResultIndex=s?this.searchResults.length-1:0),this.mainPane.setSelectedEntry(this.searchResults[this.searchResultIndex])):this.onSearchCanceled(),this.searchableView.updateSearchMatchesCount(this.searchResults.length),this.searchableView.updateCurrentMatchIndex(this.searchResultIndex)}onSearchCanceled(){this.mainPane.setSelectedEntry(-1),this.searchResults=[],this.searchResultIndex=-1}jumpToNextSearchResult(){this.searchResultIndex=(this.searchResultIndex+1)%this.searchResults.length,this.mainPane.setSelectedEntry(this.searchResults[this.searchResultIndex]),this.searchableView.updateCurrentMatchIndex(this.searchResultIndex)}jumpToPreviousSearchResult(){this.searchResultIndex=(this.searchResultIndex-1+this.searchResults.length)%this.searchResults.length,this.mainPane.setSelectedEntry(this.searchResults[this.searchResultIndex]),this.searchableView.updateCurrentMatchIndex(this.searchResultIndex)}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!1}}class ve{formatter;minimumBoundaries;maximumBoundaries;xScaleFactor;constructor(e){this.formatter=e}updateBoundaries(e){this.minimumBoundaries=e.dataProvider.minimumBoundary();const t=e.dataProvider.totalTime();this.maximumBoundaries=this.minimumBoundaries+t,this.xScaleFactor=e.overviewContainer.clientWidth/t}computePosition(e){return(e-this.minimumBoundaries)*this.xScaleFactor}formatValue(e,t){return this.formatter(e-this.minimumBoundaries,t)}maximumBoundary(){return this.maximumBoundaries}minimumBoundary(){return this.minimumBoundaries}zeroTime(){return this.minimumBoundaries}boundarySpan(){return this.maximumBoundaries-this.minimumBoundaries}}class Se extends(n.ObjectWrapper.eventMixin(r.Widget.VBox)){overviewContainer;overviewCalculator;overviewGrid;overviewCanvas;dataProvider;windowTimeLeft;windowTimeRight;updateTimerId;constructor(e){super(),this.element.classList.add("cpu-profile-flame-chart-overview-pane"),this.overviewContainer=this.element.createChild("div","cpu-profile-flame-chart-overview-container"),this.overviewCalculator=new ve(e.formatValue),this.overviewGrid=new p.OverviewGrid.OverviewGrid("cpu-profile-flame-chart",this.overviewCalculator),this.overviewGrid.element.classList.add("fill"),this.overviewCanvas=this.overviewContainer.createChild("canvas","cpu-profile-flame-chart-overview-canvas"),this.overviewContainer.appendChild(this.overviewGrid.element),this.dataProvider=e,this.overviewGrid.addEventListener("WindowChangedWithPosition",this.onWindowChanged,this)}windowChanged(e,t){this.selectRange(e,t)}updateRangeSelection(e,t){}updateSelectedGroup(e,t){}selectRange(e,t){const i=this.dataProvider.minimumBoundary(),s=this.dataProvider.totalTime();this.overviewGrid.setWindow((e-i)/s,(t-i)/s)}onWindowChanged(e){const t={windowTimeLeft:e.data.rawStartValue,windowTimeRight:e.data.rawEndValue};this.windowTimeLeft=t.windowTimeLeft,this.windowTimeRight=t.windowTimeRight,this.dispatchEventToListeners("WindowChanged",t)}timelineData(){return this.dataProvider.timelineData()}onResize(){this.scheduleUpdate()}scheduleUpdate(){this.updateTimerId||(this.updateTimerId=this.element.window().requestAnimationFrame(this.update.bind(this)))}update(){this.updateTimerId=0;this.timelineData()&&(this.resetCanvas(this.overviewContainer.clientWidth,this.overviewContainer.clientHeight-p.FlameChart.RulerHeight),this.overviewCalculator.updateBoundaries(this),this.overviewGrid.updateDividers(this.overviewCalculator),this.drawOverviewCanvas())}drawOverviewCanvas(){const e=this.overviewCanvas.width,t=this.overviewCanvas.height,i=this.calculateDrawData(e),s=this.overviewCanvas.getContext("2d");if(!s)throw new Error("Failed to get canvas context");const r=window.devicePixelRatio,o=t/(1.1*this.dataProvider.maxStackDepth());s.lineWidth=1,s.translate(.5,.5),s.strokeStyle="rgba(20,0,0,0.4)",s.fillStyle="rgba(214,225,254,0.8)",s.moveTo(-1,t+1),s.lineTo(-1,Math.round(t-i[0]*o-r));let n=0;for(let a=0;a<e;++a)n=Math.round(t-i[a]*o-r),s.lineTo(a,n);s.lineTo(e+1,n),s.lineTo(e+1,t+1),s.fill(),s.stroke(),s.closePath()}calculateDrawData(e){const t=this.dataProvider,i=this.timelineData(),s=i.entryStartTimes,r=i.entryTotalTimes,o=i.entryLevels,n=s.length,a=this.dataProvider.minimumBoundary(),l=new Uint8Array(e),d=e/t.totalTime();for(let e=0;e<n;++e){const t=Math.floor((s[e]-a)*d),i=Math.floor((s[e]-a+r[e])*d);for(let s=t;s<=i;++s)l[s]=Math.max(l[s],o[e]+1)}return l}resetCanvas(e,t){const i=window.devicePixelRatio;this.overviewCanvas.width=e*i,this.overviewCanvas.height=t*i,this.overviewCanvas.style.width=e+"px",this.overviewCanvas.style.height=t+"px"}}var we=Object.freeze({__proto__:null,ProfileFlameChartDataProvider:fe,ProfileFlameChart:ge,OverviewCalculator:ve,OverviewPane:Se});class be extends C{remainingChildren;constructor(e,t){super(e,t,Boolean(e.children&&e.children.length)),this.remainingChildren=e.children}static sharedPopulate(e){const t=e.remainingChildren,i=t.length;for(let s=0;s<i;++s)e.appendChild(new be(t[s],e.tree));e.remainingChildren=[]}static excludeRecursively(e,t){e.remainingChildren.length>0&&e.populate(),e.save();const i=e.children;let s=e.children.length;for(;s--;)be.excludeRecursively(i[s],t);const r=e.childrenByCallUID.get(t);r&&C.merge(e,r,!0)}populateChildren(){be.sharedPopulate(this)}}class Ce extends y{remainingChildren;constructor(e,t,i,s){super(e,t,s),this.remainingChildren=i.children,C.populate(this)}focus(e){e&&(this.save(),e.savePosition(),this.children=[e],this.total=e.total)}exclude(e){e&&(this.save(),be.excludeRecursively(this,e.callUID),this.lastComparator&&this.sort(this.lastComparator,!0))}restore(){this.savedChildren&&(this.children[0].restorePosition(),super.restore())}populateChildren(){be.sharedPopulate(this)}}var ye=Object.freeze({__proto__:null,TopDownProfileDataGridNode:be,TopDownProfileDataGridTree:Ce});const Pe={profile:"Profile",findByCostMsNameOrFile:"Find by cost (>50ms), name or file",function:"Function",profiler:"Profiler",profileViewMode:"Profile view mode",focusSelectedFunction:"Focus selected function",excludeSelectedFunction:"Exclude selected function",restoreAllFunctions:"Restore all functions",chart:"Chart",heavyBottomUp:"Heavy (Bottom Up)",treeTopDown:"Tree (Top Down)",profileD:"Profile {PH1}",loadingD:"Loading… {PH1}%",fileSReadErrorS:"File ''{PH1}'' read error: {PH2}",loading:"Loading…",failedToReadFile:"Failed to read file",parsing:"Parsing…",loaded:"Loaded"},Te=t.i18n.registerUIStrings("panels/profiler/ProfileView.ts",Pe),Ie=t.i18n.getLocalizedString.bind(void 0,Te);class xe extends r.View.SimpleView{profileInternal;searchableViewInternal;dataGrid;viewSelectComboBox;focusButton;excludeButton;resetButton;linkifierInternal;nodeFormatter;viewType;adjustedTotal;profileHeader;bottomUpProfileDataGridTree;topDownProfileDataGridTree;currentSearchResultIndex;dataProvider;flameChart;visibleView;searchableElement;profileDataGridTree;constructor(){super(Ie(Pe.profile)),this.profileInternal=null,this.searchableViewInternal=new r.SearchableView.SearchableView(this,null),this.searchableViewInternal.setPlaceholder(Ie(Pe.findByCostMsNameOrFile)),this.searchableViewInternal.show(this.element);const e=[];e.push({id:"self",title:this.columnHeader("self"),width:"120px",fixedWidth:!0,sortable:!0,sort:s.DataGrid.Order.Descending,titleDOMFragment:void 0,align:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0}),e.push({id:"total",title:this.columnHeader("total"),width:"120px",fixedWidth:!0,sortable:!0,sort:void 0,titleDOMFragment:void 0,align:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0}),e.push({id:"function",title:Ie(Pe.function),disclosure:!0,sortable:!0,sort:void 0,titleDOMFragment:void 0,align:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0,width:void 0,fixedWidth:void 0}),this.dataGrid=new s.DataGrid.DataGridImpl({displayName:Ie(Pe.profiler),columns:e,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.addEventListener("SortingChanged",this.sortProfile,this),this.dataGrid.addEventListener("SelectedNode",this.nodeSelected.bind(this,!0)),this.dataGrid.addEventListener("DeselectedNode",this.nodeSelected.bind(this,!1)),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this)),this.viewSelectComboBox=new r.Toolbar.ToolbarComboBox(this.changeView.bind(this),Ie(Pe.profileViewMode),void 0,"profile-view.selected-view"),this.focusButton=new r.Toolbar.ToolbarButton(Ie(Pe.focusSelectedFunction),"eye",void 0,"profile-view.focus-selected-function"),this.focusButton.setEnabled(!1),this.focusButton.addEventListener("Click",this.focusClicked,this),this.excludeButton=new r.Toolbar.ToolbarButton(Ie(Pe.excludeSelectedFunction),"cross",void 0,"profile-view.exclude-selected-function"),this.excludeButton.setEnabled(!1),this.excludeButton.addEventListener("Click",this.excludeClicked,this),this.resetButton=new r.Toolbar.ToolbarButton(Ie(Pe.restoreAllFunctions),"refresh",void 0,"profile-view.restore-all-functions"),this.resetButton.setEnabled(!1),this.resetButton.addEventListener("Click",this.resetClicked,this),this.linkifierInternal=new u.Linkifier.Linkifier(Re)}static buildPopoverTable(e){const t=document.createElement("table");for(const i of e){const e=t.createChild("tr");e.createChild("td").textContent=i.title,e.createChild("td").textContent=i.value}return t}setProfile(e){this.profileInternal=e,this.bottomUpProfileDataGridTree=null,this.topDownProfileDataGridTree=null,this.changeView(),this.refresh()}profile(){return this.profileInternal}initialize(e){this.nodeFormatter=e,this.viewType=n.Settings.Settings.instance().createSetting("profile-view","Heavy");const t=["Flame","Heavy","Tree"],i=new Map([["Flame",Ie(Pe.chart)],["Heavy",Ie(Pe.heavyBottomUp)],["Tree",Ie(Pe.treeTopDown)]]),s=new Map(t.map((e=>[e,this.viewSelectComboBox.createOption(i.get(e),e)]))),r=this.viewType.get()||t[0],o=s.get(r)||s.get(t[0]);this.viewSelectComboBox.select(o),this.changeView(),this.flameChart&&this.flameChart.update()}focus(){this.flameChart?this.flameChart.focus():super.focus()}columnHeader(e){throw"Not implemented"}selectRange(e,t){this.flameChart&&this.flameChart.selectRange(e,t)}async toolbarItems(){return[this.viewSelectComboBox,this.focusButton,this.excludeButton,this.resetButton]}getBottomUpProfileDataGridTree(){return this.bottomUpProfileDataGridTree||(this.bottomUpProfileDataGridTree=new x(this.nodeFormatter,this.searchableViewInternal,this.profileInternal.root,this.adjustedTotal)),this.bottomUpProfileDataGridTree}getTopDownProfileDataGridTree(){return this.topDownProfileDataGridTree||(this.topDownProfileDataGridTree=new Ce(this.nodeFormatter,this.searchableViewInternal,this.profileInternal.root,this.adjustedTotal)),this.topDownProfileDataGridTree}populateContextMenu(e,t){const i=t;i.linkElement&&e.appendApplicableItems(i.linkElement)}willHide(){this.currentSearchResultIndex=-1}refresh(){if(!this.profileDataGridTree)return;const e=this.dataGrid.selectedNode?this.dataGrid.selectedNode.profileNode:null;this.dataGrid.rootNode().removeChildren();const t=this.profileDataGridTree.children,i=t.length;for(let e=0;e<i;++e)this.dataGrid.rootNode().appendChild(t[e]);e&&(e.selected=!0)}refreshVisibleData(){let e=this.dataGrid.rootNode().children[0];for(;e;)e.refresh(),e=e.traverseNextNode(!1,null,!0)}searchableView(){return this.searchableViewInternal}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!1}onSearchCanceled(){this.searchableElement&&this.searchableElement.onSearchCanceled()}performSearch(e,t,i){this.searchableElement&&this.searchableElement.performSearch(e,t,i)}jumpToNextSearchResult(){this.searchableElement&&this.searchableElement.jumpToNextSearchResult()}jumpToPreviousSearchResult(){this.searchableElement&&this.searchableElement.jumpToPreviousSearchResult()}linkifier(){return this.linkifierInternal}createFlameChartDataProvider(){throw"Not implemented"}ensureFlameChartCreated(){this.flameChart||(this.dataProvider=this.createFlameChartDataProvider(),this.flameChart=new ge(this.searchableViewInternal,this.dataProvider),this.flameChart.addEventListener("EntryInvoked",(e=>{this.onEntryInvoked(e)})))}async onEntryInvoked(e){if(!this.dataProvider)return;const t=e.data,i=this.dataProvider.entryNodes[t],s=this.profileHeader.debuggerModel;if(!i||!i.scriptId||!s)return;const r=s.scriptForId(i.scriptId);if(!r)return;const o=s.createRawLocation(r,i.lineNumber,i.columnNumber),a=await m.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().rawLocationToUILocation(o);n.Revealer.reveal(a)}changeView(){if(!this.profileInternal)return;switch(this.searchableViewInternal.closeSearch(),this.visibleView&&this.visibleView.detach(),this.viewType.set(this.viewSelectComboBox.selectedOption().value),this.viewType.get()){case"Flame":this.ensureFlameChartCreated(),this.visibleView=this.flameChart,this.searchableElement=this.flameChart;break;case"Tree":this.profileDataGridTree=this.getTopDownProfileDataGridTree(),this.sortProfile(),this.visibleView=this.dataGrid.asWidget(),this.searchableElement=this.profileDataGridTree;break;case"Heavy":this.profileDataGridTree=this.getBottomUpProfileDataGridTree(),this.sortProfile(),this.visibleView=this.dataGrid.asWidget(),this.searchableElement=this.profileDataGridTree}const e="Flame"===this.viewType.get();this.focusButton.setVisible(!e),this.excludeButton.setVisible(!e),this.resetButton.setVisible(!e),this.visibleView&&this.visibleView.show(this.searchableViewInternal.element)}nodeSelected(e){this.focusButton.setEnabled(e),this.excludeButton.setEnabled(e)}focusClicked(){this.dataGrid.selectedNode&&(this.resetButton.setEnabled(!0),this.resetButton.element.focus(),this.profileDataGridTree&&this.profileDataGridTree.focus(this.dataGrid.selectedNode),this.refresh(),this.refreshVisibleData(),o.userMetrics.actionTaken(o.UserMetrics.Action.CpuProfileNodeFocused))}excludeClicked(){const e=this.dataGrid.selectedNode;e&&(this.resetButton.setEnabled(!0),this.resetButton.element.focus(),e.deselect(),this.profileDataGridTree&&this.profileDataGridTree.exclude(e),this.refresh(),this.refreshVisibleData(),o.userMetrics.actionTaken(o.UserMetrics.Action.CpuProfileNodeExcluded))}resetClicked(){this.viewSelectComboBox.selectElement().focus(),this.resetButton.setEnabled(!1),this.profileDataGridTree&&this.profileDataGridTree.restore(),this.linkifierInternal.reset(),this.refresh(),this.refreshVisibleData()}sortProfile(){if(!this.profileDataGridTree)return;const e=this.dataGrid.isSortOrderAscending(),t=this.dataGrid.sortColumnId(),i="function"===t?"functionName":t||"";this.profileDataGridTree.sort(y.propertyComparator(i,e),!1),this.refresh()}}const Re=30;class Ne extends E{debuggerModel;fileName;jsonifiedProfile;profile;protocolProfileInternal;#i;#s=()=>{};constructor(e,t,i){super(t,i||Ie(Pe.profileD,{PH1:t.nextProfileUid()})),this.debuggerModel=e,this.#i=new Promise((e=>{this.#s=e}))}onChunkTransferred(t){this.jsonifiedProfile&&this.updateStatus(Ie(Pe.loadingD,{PH1:e.NumberUtilities.bytesToString(this.jsonifiedProfile.length)}))}onError(e){const t=e.error();t&&this.updateStatus(Ie(Pe.fileSReadErrorS,{PH1:e.fileName(),PH2:t.message}))}async write(e){this.jsonifiedProfile+=e}async close(){}dispose(){this.removeTempFile()}createSidebarTreeElement(e){return new K(e,this,"profile-sidebar-tree-item")}canSaveToFile(){return!this.fromFile()}async saveToFile(){await this.#i;const t=new m.FileUtils.FileOutputStream;if(!this.fileName){const t=e.DateUtilities.toISO8601Compact(new Date),i=this.profileType().fileExtension();this.fileName=`${this.profileType().typeName()}-${t}${i}`}if(!await t.open(this.fileName)||!this.tempFile)return;const i=await this.tempFile.read();i&&await t.write(i),t.close()}async loadFromFile(e){this.updateStatus(Ie(Pe.loading),!0);const t=new m.FileUtils.ChunkedFileReader(e,1e7,this.onChunkTransferred.bind(this));this.jsonifiedProfile="";if(!await t.read(this))return this.onError(t),new Error(Ie(Pe.failedToReadFile));this.updateStatus(Ie(Pe.parsing),!0);let i=null;try{this.profile=JSON.parse(this.jsonifiedProfile),this.setProfile(this.profile),this.updateStatus(Ie(Pe.loaded),!1)}catch(e){i=e,this.profileType().removeProfile(this)}return this.jsonifiedProfile=null,this.profileType().profileBeingRecorded()===this&&this.profileType().setProfileBeingRecorded(null),i}setProtocolProfile(e){this.setProfile(e),this.protocolProfileInternal=e,this.tempFile=new m.TempFile.TempFile,this.tempFile.write([JSON.stringify(e)]),this.#s()}}var ke=Object.freeze({__proto__:null,ProfileView:xe,maxLinkLength:Re,WritableProfileHeader:Ne});const De={selectedSizeS:"Selected size: {PH1}",selfSizeBytes:"Self Size (bytes)",totalSizeBytes:"Total Size (bytes)",stopHeapProfiling:"Stop heap profiling",startHeapProfiling:"Start heap profiling",recording:"Recording…",heapProfilerIsRecording:"Heap profiler is recording",stopping:"Stopping…",allocationSampling:"Allocation sampling",samplingProfiles:"Sampling profiles",recordMemoryAllocations:"Record memory allocations using sampling method.",thisProfileTypeHasMinimal:"This profile type has minimal performance overhead and can be used for long running operations.",itProvidesGoodApproximation:"It provides good approximation of allocations broken down by `JavaScript` execution stack.",profileD:"Profile {PH1}",sBytes:"{PH1} bytes",formatPercent:"{PH1} %",skb:"{PH1} kB",name:"Name",selfSize:"Self size",totalSize:"Total size",url:"URL"},Ee=t.i18n.registerUIStrings("panels/profiler/HeapProfileView.ts",De),Me=t.i18n.getLocalizedString.bind(void 0,Ee);function Fe(e){return e.profile||e.protocolProfile()}class je extends xe{profileHeader;profileType;adjustedTotal;selectedSizeText;timestamps;sizes;max;ordinals;totalTime;lastOrdinal;timelineOverview;constructor(e){super(),this.profileHeader=e,this.profileType=e.profileType(),this.initialize(new Ge(this));const t=new Ae(Fe(e));this.adjustedTotal=t.total,this.setProfile(t),this.selectedSizeText=new r.Toolbar.ToolbarText,this.timestamps=[],this.sizes=[],this.max=[],this.ordinals=[],this.totalTime=0,this.lastOrdinal=0,this.timelineOverview=new de,h.Runtime.experiments.isEnabled("sampling-heap-profiler-timeline")&&(this.timelineOverview.addEventListener("IdsRangeChanged",this.onIdsRangeChanged.bind(this)),this.timelineOverview.show(this.element,this.element.firstChild),this.timelineOverview.start(),this.profileType.addEventListener("StatsUpdate",this.onStatsUpdate,this),this.profileType.once("profile-complete").then((()=>{this.profileType.removeEventListener("StatsUpdate",this.onStatsUpdate,this),this.timelineOverview.stop(),this.timelineOverview.updateGrid()})))}async toolbarItems(){return[...await super.toolbarItems(),this.selectedSizeText]}onIdsRangeChanged(t){const{minId:i,maxId:s}=t.data;this.selectedSizeText.setText(Me(De.selectedSizeS,{PH1:e.NumberUtilities.bytesToString(t.data.size)})),this.setSelectionRange(i,s)}setSelectionRange(e,t){const i=Fe(this.profileHeader),s=new Ae(i,e,t);this.adjustedTotal=s.total,this.setProfile(s)}onStatsUpdate(t){const i=t.data;this.totalTime||(this.timestamps=[],this.sizes=[],this.max=[],this.ordinals=[],this.totalTime=3e4,this.lastOrdinal=0),this.sizes.fill(0),this.sizes.push(0),this.timestamps.push(Date.now()),this.ordinals.push(this.lastOrdinal+1);for(const t of i?.samples??[]){this.lastOrdinal=Math.max(this.lastOrdinal,t.ordinal);const i=e.ArrayUtilities.upperBound(this.ordinals,t.ordinal,e.ArrayUtilities.DEFAULT_COMPARATOR)-1;this.sizes[i]+=t.size}this.max.push(this.sizes[this.sizes.length-1]);this.timestamps[this.timestamps.length-1]-this.timestamps[0]>this.totalTime&&(this.totalTime*=2);const s={sizes:this.sizes,max:this.max,ids:this.ordinals,timestamps:this.timestamps,totalTime:this.totalTime};this.timelineOverview.setSamples(s)}columnHeader(e){switch(e){case"self":return Me(De.selfSizeBytes);case"total":return Me(De.totalSizeBytes)}return n.UIString.LocalizedEmptyString}createFlameChartDataProvider(){return new Ve(this.profile(),this.profileHeader.heapProfilerModel())}}class He extends(n.ObjectWrapper.eventMixin(F)){recording;clearedDuringRecording;constructor(e,t){super(e,t),this.recording=!1,this.clearedDuringRecording=!1}profileBeingRecorded(){return super.profileBeingRecorded()}typeName(){return"Heap"}fileExtension(){return".heapprofile"}get buttonTooltip(){return this.recording?Me(De.stopHeapProfiling):Me(De.startHeapProfiling)}buttonClicked(){return this.recording?this.stopRecordingProfile():this.startRecordingProfile(),this.recording}startRecordingProfile(){const e=r.Context.Context.instance().flavor(a.HeapProfilerModel.HeapProfilerModel);if(this.profileBeingRecorded()||!e)return;const t=new ze(e,this);this.setProfileBeingRecorded(t),this.addProfile(t),t.updateStatus(Me(De.recording));const i=[Me(De.heapProfilerIsRecording)];r.InspectorView.InspectorView.instance().setPanelWarnings("heap-profiler",i),this.recording=!0,this.startSampling()}async stopRecordingProfile(){this.recording=!1;const e=this.profileBeingRecorded();if(!e||!e.heapProfilerModel())return;e.updateStatus(Me(De.stopping));const t=await this.stopSampling();e&&(console.assert(void 0!==t),e.setProtocolProfile(t),e.updateStatus(""),this.setProfileBeingRecorded(null)),r.InspectorView.InspectorView.instance().setPanelWarnings("heap-profiler",[]);const i=this.clearedDuringRecording;this.clearedDuringRecording=!1,i||this.dispatchEventToListeners("profile-complete",e)}createProfileLoadedFromFile(e){return new ze(null,this,e)}profileBeingRecordedRemoved(){this.clearedDuringRecording=!0,this.stopRecordingProfile()}startSampling(){throw"Not implemented"}stopSampling(){throw"Not implemented"}}let Le;class Oe extends He{updateTimer;updateIntervalMs;constructor(){super(Oe.TypeId,Me(De.allocationSampling)),Le||(Le=this),this.updateTimer=0,this.updateIntervalMs=200}static get instance(){return Le}get treeItemTitle(){return Me(De.samplingProfiles)}get description(){return[Me(De.recordMemoryAllocations),Me(De.thisProfileTypeHasMinimal),Me(De.itProvidesGoodApproximation)].join("\n")}hasTemporaryView(){return h.Runtime.experiments.isEnabled("sampling-heap-profiler-timeline")}startSampling(){o.rnPerfMetrics.heapSamplingStarted();const e=this.obtainRecordingProfile();e?(e.startSampling().then((e=>{e&&o.rnPerfMetrics.heapSamplingFinished(!1)})),h.Runtime.experiments.isEnabled("sampling-heap-profiler-timeline")&&(this.updateTimer=window.setTimeout((()=>{this.updateStats()}),this.updateIntervalMs))):o.rnPerfMetrics.heapSamplingFinished(!1)}obtainRecordingProfile(){const e=this.profileBeingRecorded();if(e){return e.heapProfilerModel()}return null}async stopSampling(){window.clearTimeout(this.updateTimer),this.updateTimer=0,this.dispatchEventToListeners("RecordingStopped");const e=this.obtainRecordingProfile();if(!e)throw o.rnPerfMetrics.heapSamplingFinished(!1),new Error("No heap profiler model");const t=await e.stopSampling();if(!t)throw o.rnPerfMetrics.heapSamplingFinished(!1),new Error("No sampling profile found");return o.rnPerfMetrics.heapSamplingFinished(!0),t}async updateStats(){const e=this.obtainRecordingProfile();if(!e)return;const t=await e.getSamplingProfile();this.updateTimer&&(this.dispatchEventToListeners("StatsUpdate",t),this.updateTimer=window.setTimeout((()=>{this.updateStats()}),this.updateIntervalMs))}static TypeId="SamplingHeap"}class ze extends Ne{heapProfilerModelInternal;protocolProfileInternal;constructor(e,t,i){super(e&&e.debuggerModel(),t,i||Me(De.profileD,{PH1:t.nextProfileUid()})),this.heapProfilerModelInternal=e,this.protocolProfileInternal={head:{callFrame:{functionName:"",scriptId:"",url:"",lineNumber:0,columnNumber:0},children:[],selfSize:0,id:0},samples:[],startTime:0,endTime:0,nodes:[]}}createView(){return new je(this)}protocolProfile(){return this.protocolProfileInternal}heapProfilerModel(){return this.heapProfilerModelInternal}profileType(){return super.profileType()}}class Be extends c.ProfileTreeModel.ProfileNode{self;constructor(e){super(e.callFrame||{functionName:e.functionName,scriptId:e.scriptId,url:e.url,lineNumber:e.lineNumber-1,columnNumber:e.columnNumber-1}),this.self=e.selfSize}}class Ae extends c.ProfileTreeModel.ProfileTreeModel{modules;constructor(e,t,i){super(),this.modules=e.modules||[];let s=null;if(t||i){s=new Map,t=t||0,i=i||1/0;for(const r of e.samples){if(r.ordinal<t||r.ordinal>i)continue;const e=s.get(r.nodeId)||0;s.set(r.nodeId,e+r.size)}}function r(e){return e.children=e.children.filter(r),Boolean(e.children.length||e.self)}this.initialize(function(e){const t=new Be(e),i=[e],o=[t];for(;i.length;){const e=i.pop(),t=o.pop();t.children=e.children.map((e=>{const t=new Be(e);return s&&(t.self=s.get(e.id)||0),t})),i.push(...e.children),o.push(...t.children)}return r(t),t}(e.head))}}class Ge{profileView;constructor(e){this.profileView=e}formatValue(t){return e.NumberUtilities.withThousandsSeparator(t)}formatValueAccessibleText(e){return Me(De.sBytes,{PH1:e})}formatPercent(e,t){return Me(De.formatPercent,{PH1:e.toFixed(2)})}linkifyNode(e){const t=this.profileView.profileHeader.heapProfilerModel(),i=t?t.target():null;return this.profileView.linkifier().maybeLinkifyConsoleCallFrame(i,e.profileNode.callFrame,{className:"profile-node-file",inlineFrameIndex:0})}}class Ve extends fe{profile;heapProfilerModel;constructor(e,t){super(),this.profile=e,this.heapProfilerModel=t}minimumBoundary(){return 0}totalTime(){return this.profile.root.total}entryHasDeoptReason(e){return!1}formatValue(t,i){return Me(De.skb,{PH1:e.NumberUtilities.withThousandsSeparator(t/1e3)})}calculateTimelineData(){const e=function e(t){return t.children.reduce(((t,i)=>t+e(i)),1)}(this.profile.root),t=new Array(e),i=new Uint16Array(e),s=new Float32Array(e),r=new Float64Array(e);let o=0,n=0,a=0,l=0;return function e(d){const h=a;t[l]=d,i[l]=o,s[l]=d.total,r[l]=a,++l,++o,d.children.forEach(e),--o,n=Math.max(n,o),a=h+d.total}(this.profile.root),this.maxStackDepthInternal=n+1,this.entryNodes=t,this.timelineDataInternal=p.FlameChart.FlameChartTimelineData.create({entryLevels:i,entryTotalTimes:s,entryStartTimes:r,groups:null}),this.timelineDataInternal}prepareHighlightedEntryInfo(t){const i=this.entryNodes[t];if(!i)return null;const s=[];function o(e,t){s.push({title:e,value:t})}o(Me(De.name),r.UIUtils.beautifyFunctionName(i.functionName)),o(Me(De.selfSize),e.NumberUtilities.bytesToString(i.self)),o(Me(De.totalSize),e.NumberUtilities.bytesToString(i.total));const n=new u.Linkifier.Linkifier,a=n.maybeLinkifyConsoleCallFrame(this.heapProfilerModel?this.heapProfilerModel.target():null,i.callFrame);return a&&o(Me(De.url),a.textContent),n.dispose(),xe.buildPopoverTable(s)}}var We=Object.freeze({__proto__:null,HeapProfileView:je,SamplingHeapProfileTypeBase:He,SamplingHeapProfileType:Oe,SamplingHeapProfileHeader:ze,SamplingHeapProfileNode:Be,SamplingHeapProfileModel:Ae,NodeFormatter:Ge,HeapFlameChartDataProvider:Ve});const Ue={genericStringsTwoPlaceholders:"{PH1}, {PH2}",internalArray:"(internal array)[]",userObjectReachableFromWindow:"User object reachable from window",detachedFromDomTree:"Detached from DOM tree",previewIsNotAvailable:"Preview is not available",revealInSummaryView:"Reveal in Summary view",summary:"Summary",revealObjectSWithIdSInSummary:"Reveal object ''{PH1}'' with id @{PH2} in Summary view",storeAsGlobalVariable:"Store as global variable",ignoreThisRetainer:"Ignore this retainer",stopIgnoringThisRetainer:"Stop ignoring this retainer",ignored:"ignored",inElement:"in",compiledCodeSummary:"Internal data which V8 uses to run functions defined by JavaScript or WebAssembly.",concatenatedStringSummary:"A string which represents the contents of two other strings joined together.",contextSummary:"An internal object containing variables from a JavaScript scope which may be needed by a function created within that scope.",descriptorArraySummary:"A list of the property names used by a JavaScript Object.",internalArraySummary:"An internal array-like data structure (not a JavaScript Array).",internalNodeSummary:"An object allocated by a component other than V8, such as C++ objects defined by Blink.",mapSummary:"An internal object representing the shape of a JavaScript Object (not a JavaScript Map).",objectElementsSummary:"An internal object which stores the indexed properties in a JavaScript Object, such as the contents of an Array.",objectPropertiesSummary:"An internal object which stores the named properties in a JavaScript Object.",slicedStringSummary:"A string which represents some of the characters from another string."},_e=t.i18n.registerUIStrings("panels/profiler/HeapSnapshotGridNodes.ts",Ue),$e=t.i18n.getLocalizedString.bind(void 0,_e);class Je extends s.DataGrid.DataGridNode{}class qe extends(n.ObjectWrapper.eventMixin(Je)){dataGridInternal;instanceCount;savedChildren;retrievedChildrenRanges;providerObject;reachableFromWindow;populated;constructor(e,t){super(null,t),this.dataGridInternal=e,this.instanceCount=0,this.savedChildren=new Map,this.retrievedChildrenRanges=[],this.providerObject=null,this.reachableFromWindow=!1}get name(){}heapSnapshotDataGrid(){return this.dataGridInternal}createProvider(){throw new Error("Not implemented.")}comparator(){throw new Error("Not implemented.")}getHash(){throw new Error("Not implemented.")}createChildNode(e){throw new Error("Not implemented.")}retainersDataSource(){return null}provider(){return this.providerObject||(this.providerObject=this.createProvider()),this.providerObject}createCell(e){return super.createCell(e)}collapse(){super.collapse(),this.dataGridInternal.updateVisibleNodes(!0)}expand(){super.expand(),this.dataGridInternal.updateVisibleNodes(!0)}dispose(){this.providerObject&&this.providerObject.dispose();for(let e=this.children[0];e;e=e.traverseNextNode(!0,this,!0))e.dispose()}queryObjectContent(e,t){throw new Error("Not implemented.")}tryQueryObjectContent(e,t){throw new Error("Not implemented.")}populateContextMenu(e,t,i){}toPercentString(e){return e.toFixed(0)+" %"}toUIDistance(e){const t=f.HeapSnapshotModel.baseSystemDistance;return e>=0&&e<t?e.toString():"−"}allChildren(){return this.dataGridInternal.allChildren(this)}removeChildByIndex(e){this.dataGridInternal.removeChildByIndex(this,e)}childForPosition(e){let t=0;for(let i=0;i<this.retrievedChildrenRanges.length;i++){const s=this.retrievedChildrenRanges[i];if(s.from<=e&&e<s.to){const i=t+e-s.from;return this.allChildren()[i]}t+=s.to-s.from+1}return null}createValueCell(e){const t=l.tableCell("numeric-column").track({click:!0}),i=r.Fragment.html`<td class="numeric-column" jslog=${t} />`,s=this.dataGrid;if(s.snapshot&&0!==s.snapshot.totalSize){const t=document.createElement("div"),s=r.Fragment.html`<span>${this.data[e]}</span>`;t.appendChild(s);const o=e+"-percent";if(o in this.data){const n=r.Fragment.html`<span class="percent-column">${this.data[o]}</span>`;t.appendChild(n),t.classList.add("profile-multiple-values"),r.ARIAUtils.markAsHidden(s),r.ARIAUtils.markAsHidden(n),this.setCellAccessibleName($e(Ue.genericStringsTwoPlaceholders,{PH1:this.data[e],PH2:this.data[o]}),i,e)}i.appendChild(t)}return i}populate(){this.populated||(this.populated=!0,this.provider().sortAndRewind(this.comparator()).then((()=>this.populateChildren())))}expandWithoutPopulate(){return this.populated=!0,this.expand(),this.provider().sortAndRewind(this.comparator())}childHashForEntity(e){return"edgeIndex"in e?e.edgeIndex:e.id}populateChildren(e,t){return new Promise((i=>{e=e||0,t=t||e+this.dataGridInternal.defaultPopulateCount();let r=e;function o(e){if(r>=e)return;const t=Math.min(r+this.dataGridInternal.defaultPopulateCount(),e);this.provider().serializeItemsRange(r,t).then((t=>l.call(this,t,e))),r=t}function n(e,t){if(this.savedChildren){const i=this.childHashForEntity(e),s=this.savedChildren.get(i);if(s)return void this.dataGridInternal.insertChild(this,s,t)}this.dataGridInternal.insertChild(this,this.createChildNode(e),t)}function a(e,t,i){const r=new s.ShowMoreDataGridNode.ShowMoreDataGridNode(this.populateChildren.bind(this),e,t,this.dataGridInternal.defaultPopulateCount());this.dataGridInternal.insertChild(this,r,i)}function l(e,t){let s=0,l=e.startPosition;const d=e.items;let h=0;if(this.retrievedChildrenRanges.length){let t=0,i=!1,r={from:0,to:0};for(;t<this.retrievedChildrenRanges.length;){if(r=this.retrievedChildrenRanges[t],r.to>=l){i=!0;break}h+=r.to-r.from,r.to<e.totalLength&&(h+=1),++t}if(!i||e.startPosition<r.from){this.allChildren()[h-1].setEndPosition(e.startPosition),a.call(this,e.startPosition,i?r.from:e.totalLength,h),r={from:e.startPosition,to:e.startPosition},i||(t=this.retrievedChildrenRanges.length),this.retrievedChildrenRanges.splice(t,0,r)}else h+=l-r.from;for(;r.to<e.endPosition;){const i=r.to-l;h+=i,s+=i,l=r.to;const o=this.retrievedChildrenRanges[t+1];let a=o?o.from:e.totalLength;for(a>e.endPosition&&(a=e.endPosition);l<a;)n.call(this,d[s++],h++),++l;o&&a===o.from?(r.to=o.to,this.removeChildByIndex(h),this.retrievedChildrenRanges.splice(t+1,1)):(r.to=a,a===e.totalLength?this.removeChildByIndex(h):this.allChildren()[h].setStartPosition(e.endPosition))}}else{e.startPosition>0&&(this.retrievedChildrenRanges.push({from:0,to:0}),a.call(this,0,e.startPosition,h++)),this.retrievedChildrenRanges.push({from:e.startPosition,to:e.endPosition});for(let e=0,t=d.length;e<t;++e)n.call(this,d[e],h++);e.endPosition<e.totalLength&&a.call(this,e.endPosition,e.totalLength,h++)}this.instanceCount+=d.length,r<t&&r<e.totalLength?o.call(this,t):(this.expanded&&this.dataGridInternal.updateVisibleNodes(!0),i(),this.dispatchEventToListeners(qe.Events.PopulateComplete))}o.call(this,t)}))}saveChildren(){this.savedChildren.clear();const e=this.allChildren();for(let t=0,i=e.length;t<i;++t){const i=e[t];i.expanded&&this.savedChildren.set(i.getHash(),i)}}async sort(){this.dataGridInternal.recursiveSortingEnter(),await this.provider().sortAndRewind(this.comparator()),this.saveChildren(),this.dataGridInternal.removeAllChildren(this),this.retrievedChildrenRanges=[];const e=this.instanceCount;this.instanceCount=0,await this.populateChildren(0,e);for(const e of this.allChildren())e.expanded&&e.sort();this.dataGridInternal.recursiveSortingLeave()}}!function(e){let t;!function(e){e.PopulateComplete="PopulateComplete"}(t=e.Events||(e.Events={}))}(qe||(qe={}));class Qe extends qe{referenceName;nameInternal;type;distance;shallowSize;retainedSize;snapshotNodeId;snapshotNodeIndex;detachedDOMTreeNode;linkElement;constructor(t,i){if(super(t,!1),!i)return;this.referenceName=null,this.nameInternal=i.name,this.type=i.type,this.distance=i.distance,this.shallowSize=i.selfSize,this.retainedSize=i.retainedSize,this.snapshotNodeId=i.id,this.snapshotNodeIndex=i.nodeIndex,"string"===this.type?this.reachableFromWindow=!0:"object"===this.type&&this.nameInternal.startsWith("Window")?(this.nameInternal=this.shortenWindowURL(this.nameInternal,!1),this.reachableFromWindow=!0):i.canBeQueried&&(this.reachableFromWindow=!0),i.detachedDOMTreeNode&&(this.detachedDOMTreeNode=!0);const s=t.snapshot,r=this.shallowSize/s.totalSize*100,o=this.retainedSize/s.totalSize*100;this.data={distance:this.toUIDistance(this.distance),shallowSize:e.NumberUtilities.withThousandsSeparator(this.shallowSize),retainedSize:e.NumberUtilities.withThousandsSeparator(this.retainedSize),"shallowSize-percent":this.toPercentString(r),"retainedSize-percent":this.toPercentString(o)}}get name(){return this.nameInternal}retainersDataSource(){return void 0===this.snapshotNodeIndex?null:{snapshot:this.dataGridInternal.snapshot,snapshotNodeIndex:this.snapshotNodeIndex,snapshotNodeId:this.snapshotNodeId}}createCell(e){return"object"!==e?this.createValueCell(e):this.createObjectCell()}createObjectCell(){let e=this.nameInternal,t="object";switch(this.type){case"concatenated string":case"string":e=`"${e}"`,t="string";break;case"regexp":e=`/${e}/`,t="string";break;case"closure":e=`${e}()`,t="function";break;case"bigint":t="bigint";break;case"number":t="number";break;case"hidden":case"object shape":t="null";break;case"array":e=e?`${e}[]`:$e(Ue.internalArray)}return this.createObjectCellWithValue(t,e||"")}createObjectCellWithValue(e,t){const s=l.tableCell("object-column").track({click:!0}),o=r.Fragment.Fragment.build`
  <td class="object-column disclosure" jslog=${s}>
  <div class="source-code event-properties" style="overflow: visible;" $="container">
  <span class="value object-value-${e}">${t}</span>
  <span class="object-value-id">@${this.snapshotNodeId}</span>
  </div>
  </td>`,n=o.$("container");if(this.prefixObjectCell(n),this.reachableFromWindow){const e=i.Icon.create("frame","heap-object-tag");r.Tooltip.Tooltip.install(e,$e(Ue.userObjectReachableFromWindow)),n.appendChild(e)}if(this.detachedDOMTreeNode){const e=i.Icon.create("scissors","heap-object-tag");r.Tooltip.Tooltip.install(e,$e(Ue.detachedFromDomTree)),n.appendChild(e)}this.appendSourceLocation(n);const a=o.element();return this.depth&&a.style.setProperty("padding-left",this.depth*this.dataGrid.indentWidth+"px"),a}prefixObjectCell(e){}async appendSourceLocation(e){const t=r.Fragment.html`<span class="heap-object-source-link" />`;e.appendChild(t);const i=await this.dataGridInternal.dataDisplayDelegate().linkifyObject(this.snapshotNodeIndex);i?(i.setAttribute("tabindex","0"),t.appendChild(i),this.linkElement=i):t.remove()}async queryObjectContent(e,t){return await this.tryQueryObjectContent(e,t)||this.tryGetTooltipDescription()||e.runtimeModel().createRemoteObjectFromPrimitiveValue($e(Ue.previewIsNotAvailable))}async tryQueryObjectContent(e,t){return"string"===this.type?e.runtimeModel().createRemoteObjectFromPrimitiveValue(this.nameInternal):await e.objectForSnapshotObjectId(String(this.snapshotNodeId),t)}tryGetTooltipDescription(){const e="https://developer.chrome.com/docs/devtools/memory-problems/heap-snapshots#";switch(this.type){case"code":return{description:$e(Ue.compiledCodeSummary),link:e+"compiled-code"};case"concatenated string":return{description:$e(Ue.concatenatedStringSummary),link:e+"concatenated-string"};case"sliced string":return{description:$e(Ue.slicedStringSummary),link:e+"sliced-string"}}switch(this.type+":"+this.nameInternal){case"array:":return{description:$e(Ue.internalArraySummary),link:e+"array"};case"array:(object elements)":return{description:$e(Ue.objectElementsSummary),link:e+"array"};case"array:(object properties)":case"hidden:system / PropertyArray":return{description:$e(Ue.objectPropertiesSummary),link:e+"array"};case"object:system / Context":return{description:$e(Ue.contextSummary),link:e+"system-context"};case"object shape:system / DescriptorArray":return{description:$e(Ue.descriptorArraySummary),link:e+"object-shape"};case"object shape:system / Map":return{description:$e(Ue.mapSummary),link:e+"object-shape"};case"native:InternalNode":return{description:$e(Ue.internalNodeSummary),link:e+"internal-node"}}}async updateHasChildren(){const e=await this.provider().isEmpty();this.setHasChildren(!e)}shortenWindowURL(t,i){const s=t.indexOf("/"),r=i?t.indexOf("@"):t.length;if(-1===s||-1===r)return t;const o=t.substring(s+1,r).trimLeft();let n=e.StringUtilities.trimURL(o);return n.length>40&&(n=e.StringUtilities.trimMiddle(n,40)),t.substr(0,s+2)+n+t.substr(r)}populateContextMenu(e,t,i){if(0!==this.shallowSize&&e.revealSection().appendItem($e(Ue.revealInSummaryView),(()=>{t.showObject(String(this.snapshotNodeId),$e(Ue.summary))}),{jslogContext:"reveal-in-summary"}),this.referenceName)for(const i of this.referenceName.matchAll(/\((?<objectName>[^@)]*) @(?<snapshotNodeId>\d+)\)/g)){const{objectName:s,snapshotNodeId:r}=i.groups;e.revealSection().appendItem($e(Ue.revealObjectSWithIdSInSummary,{PH1:s,PH2:r}),(()=>{t.showObject(r,$e(Ue.summary))}),{jslogContext:"reveal-in-summary"})}i&&e.revealSection().appendItem($e(Ue.storeAsGlobalVariable),(async()=>{const e=await this.tryQueryObjectContent(i,"");if(e){const t=i.target().model(a.ConsoleModel.ConsoleModel);await(t?.saveToTempVariable(r.Context.Context.instance().flavor(a.RuntimeModel.ExecutionContext),e))}else n.Console.Console.instance().error($e(Ue.previewIsNotAvailable))}),{jslogContext:"store-as-global-variable"})}}class Ke extends Qe{referenceName;referenceType;edgeIndex;snapshot;parentObjectNode;cycledWithAncestorGridNode;constructor(e,t,i,s){super(e,i.node),this.referenceName=i.name,this.referenceType=i.type,this.edgeIndex=i.edgeIndex,this.snapshot=t,this.parentObjectNode=s,this.cycledWithAncestorGridNode=this.findAncestorWithSameSnapshotNodeId(),this.cycledWithAncestorGridNode||this.updateHasChildren();const r=this.data;r.count="",r.addedCount="",r.removedCount="",r.countDelta="",r.addedSize="",r.removedSize="",r.sizeDelta=""}retainersDataSource(){return void 0===this.snapshotNodeIndex?null:{snapshot:this.snapshot,snapshotNodeIndex:this.snapshotNodeIndex,snapshotNodeId:this.snapshotNodeId}}createProvider(){if(void 0===this.snapshotNodeIndex)throw new Error("Cannot create a provider on a root node");return this.snapshot.createEdgesProvider(this.snapshotNodeIndex)}findAncestorWithSameSnapshotNodeId(){let e=this.parentObjectNode;for(;e;){if(e.snapshotNodeId===this.snapshotNodeId)return e;e=e.parentObjectNode}return null}createChildNode(e){return new Ke(this.dataGridInternal,this.snapshot,e,this)}getHash(){return this.edgeIndex}comparator(){const e=this.dataGridInternal.isSortOrderAscending();switch(this.dataGridInternal.sortColumnId()){case"object":return new f.HeapSnapshotModel.ComparatorConfig("!edgeName",e,"retainedSize",!1);case"count":default:return new f.HeapSnapshotModel.ComparatorConfig("!edgeName",!0,"retainedSize",!1);case"shallowSize":return new f.HeapSnapshotModel.ComparatorConfig("selfSize",e,"!edgeName",!0);case"retainedSize":return new f.HeapSnapshotModel.ComparatorConfig("retainedSize",e,"!edgeName",!0);case"distance":return new f.HeapSnapshotModel.ComparatorConfig("distance",e,"name",!0)}}prefixObjectCell(e){let t=this.referenceName||"(empty)",i="name";switch(this.referenceType){case"context":i="object-value-number";break;case"internal":case"hidden":case"weak":i="object-value-null";break;case"element":t=`[${t}]`}this.cycledWithAncestorGridNode&&e.classList.add("cycled-ancestor-node"),e.prepend(r.Fragment.html`<span class="property-name ${i}">${t}</span>
  <span class="grayed">${this.edgeNodeSeparator()}</span>`)}edgeNodeSeparator(){return"::"}}class Ye extends Ke{#r;constructor(e,t,i,s){super(e,t,i,s),this.#r=i.node.ignored,this.#r&&(this.data.distance=$e(Ue.ignored))}createProvider(){if(void 0===this.snapshotNodeIndex)throw new Error("Cannot create providers on root nodes");return this.snapshot.createRetainingEdgesProvider(this.snapshotNodeIndex)}createChildNode(e){return new Ye(this.dataGridInternal,this.snapshot,e,this)}edgeNodeSeparator(){return $e(Ue.inElement)}expand(){this.expandRetainersChain(20)}populateContextMenu(e,t,i){super.populateContextMenu(e,t,i);const s=this.snapshotNodeIndex;void 0!==s&&(this.#r?e.revealSection().appendItem($e(Ue.stopIgnoringThisRetainer),(async()=>{await this.snapshot.unignoreNodeInRetainersView(s),await this.dataGridInternal.dataSourceChanged()}),{jslogContext:"stop-ignoring-this-retainer"}):e.revealSection().appendItem($e(Ue.ignoreThisRetainer),(async()=>{await this.snapshot.ignoreNodeInRetainersView(s),await this.dataGridInternal.dataSourceChanged()}),{jslogContext:"ignore-this-retainer"}))}isReachable(){return(this.distance??0)<f.HeapSnapshotModel.baseUnreachableDistance}prefixObjectCell(e){super.prefixObjectCell(e),this.isReachable()||e.classList.add("unreachable-ancestor-node")}expandRetainersChain(e){if(!this.populated)return this.once(qe.Events.PopulateComplete).then((()=>this.expandRetainersChain(e))),void this.populate();if(super.expand(),--e>0&&this.children.length>0){const t=this.children[0];if((t.distance||0)>1&&t.isReachable())return void t.expandRetainersChain(e)}this.dataGridInternal.dispatchEventToListeners(ht.ExpandRetainersComplete)}comparator(){const e=super.comparator();return"distance"===e.fieldName1&&(e.fieldName1="!edgeDistance"),"distance"===e.fieldName2&&(e.fieldName2="!edgeDistance"),e}}class Ze extends Qe{baseSnapshotOrSnapshot;isDeletedNode;constructor(t,i,s,r){super(t,s),this.baseSnapshotOrSnapshot=i,this.isDeletedNode=r,this.updateHasChildren();const o=this.data;o.count="",o.countDelta="",o.sizeDelta="",this.isDeletedNode?(o.addedCount="",o.addedSize="",o.removedCount="•",o.removedSize=e.NumberUtilities.withThousandsSeparator(this.shallowSize||0)):(o.addedCount="•",o.addedSize=e.NumberUtilities.withThousandsSeparator(this.shallowSize||0),o.removedCount="",o.removedSize="")}retainersDataSource(){return void 0===this.snapshotNodeIndex?null:{snapshot:this.baseSnapshotOrSnapshot,snapshotNodeIndex:this.snapshotNodeIndex,snapshotNodeId:this.snapshotNodeId}}createProvider(){if(void 0===this.snapshotNodeIndex)throw new Error("Cannot create providers on root nodes");return this.baseSnapshotOrSnapshot.createEdgesProvider(this.snapshotNodeIndex)}createChildNode(e){return new Ke(this.dataGridInternal,this.baseSnapshotOrSnapshot,e,null)}getHash(){if(void 0===this.snapshotNodeId)throw new Error("Cannot hash root nodes");return this.snapshotNodeId}comparator(){const e=this.dataGridInternal.isSortOrderAscending();switch(this.dataGridInternal.sortColumnId()){case"object":return new f.HeapSnapshotModel.ComparatorConfig("!edgeName",e,"retainedSize",!1);case"distance":return new f.HeapSnapshotModel.ComparatorConfig("distance",e,"retainedSize",!1);case"count":default:return new f.HeapSnapshotModel.ComparatorConfig("!edgeName",!0,"retainedSize",!1);case"addedSize":case"removedSize":case"shallowSize":return new f.HeapSnapshotModel.ComparatorConfig("selfSize",e,"!edgeName",!0);case"retainedSize":return new f.HeapSnapshotModel.ComparatorConfig("retainedSize",e,"!edgeName",!0)}}}class Xe extends qe{nameInternal;nodeFilter;distance;count;shallowSize;retainedSize;constructor(t,i,s,r){super(t,s.count>0),this.nameInternal=i,this.nodeFilter=r,this.distance=s.distance,this.count=s.count,this.shallowSize=s.self,this.retainedSize=s.maxRet;const o=t.snapshot,n=this.retainedSize/o.totalSize*100,a=this.shallowSize/o.totalSize*100;this.data={object:i,count:e.NumberUtilities.withThousandsSeparator(this.count),distance:this.toUIDistance(this.distance),shallowSize:e.NumberUtilities.withThousandsSeparator(this.shallowSize),retainedSize:e.NumberUtilities.withThousandsSeparator(this.retainedSize),"shallowSize-percent":this.toPercentString(a),"retainedSize-percent":this.toPercentString(n)}}get name(){return this.nameInternal}createProvider(){return this.dataGridInternal.snapshot.createNodesProviderForClass(this.nameInternal,this.nodeFilter)}async populateNodeBySnapshotObjectId(e){this.dataGridInternal.resetNameFilter(),await this.expandWithoutPopulate();const t=await this.provider().nodePosition(e);if(-1===t)return this.collapse(),[];await this.populateChildren(t,null);const i=this.childForPosition(t);return i?[this,i]:[]}filteredOut(e){return-1===this.nameInternal.toLowerCase().indexOf(e)}createCell(e){const t="object"===e?super.createCell(e):this.createValueCell(e);return"object"===e&&this.count>1&&t.appendChild(r.Fragment.html`<span class="objects-count">×${this.count}</span>`),t}createChildNode(e){return new Ze(this.dataGridInternal,this.dataGridInternal.snapshot,e,!1)}comparator(){const e=this.dataGridInternal.isSortOrderAscending(),t=this.dataGridInternal.sortColumnId();switch(t){case"object":return new f.HeapSnapshotModel.ComparatorConfig("name",e,"id",!0);case"distance":return new f.HeapSnapshotModel.ComparatorConfig("distance",e,"retainedSize",!1);case"shallowSize":return new f.HeapSnapshotModel.ComparatorConfig("selfSize",e,"id",!0);case"retainedSize":return new f.HeapSnapshotModel.ComparatorConfig("retainedSize",e,"id",!0);default:throw new Error(`Invalid sort column id ${t}`)}}}class et{addedNodesProvider;deletedNodesProvider;addedCount;removedCount;constructor(e,t,i,s){this.addedNodesProvider=e,this.deletedNodesProvider=t,this.addedCount=i,this.removedCount=s}dispose(){this.addedNodesProvider.dispose(),this.deletedNodesProvider.dispose()}nodePosition(e){throw new Error("Unreachable")}isEmpty(){return Promise.resolve(!1)}async serializeItemsRange(e,t){let i,s;if(e<this.addedCount){i=await this.addedNodesProvider.serializeItemsRange(e,t);for(const e of i.items)e.isAddedNotRemoved=!0;if(i.endPosition>=t)return i.totalLength=this.addedCount+this.removedCount,i;s=i,i=await this.deletedNodesProvider.serializeItemsRange(0,t-i.endPosition)}else s=new f.HeapSnapshotModel.ItemsRange(0,0,0,[]),i=await this.deletedNodesProvider.serializeItemsRange(e-this.addedCount,t-this.addedCount);s.items.length||(s.startPosition=this.addedCount+i.startPosition);for(const e of i.items)e.isAddedNotRemoved=!1;return s.items.push(...i.items),s.endPosition=this.addedCount+i.endPosition,s.totalLength=this.addedCount+this.removedCount,s}async sortAndRewind(e){await this.addedNodesProvider.sortAndRewind(e),await this.deletedNodesProvider.sortAndRewind(e)}}class tt extends qe{nameInternal;addedCount;removedCount;countDelta;addedSize;removedSize;sizeDelta;deletedIndexes;constructor(t,i,s){super(t,!0),this.nameInternal=i,this.addedCount=s.addedCount,this.removedCount=s.removedCount,this.countDelta=s.countDelta,this.addedSize=s.addedSize,this.removedSize=s.removedSize,this.sizeDelta=s.sizeDelta,this.deletedIndexes=s.deletedIndexes,this.data={object:i,addedCount:e.NumberUtilities.withThousandsSeparator(this.addedCount),removedCount:e.NumberUtilities.withThousandsSeparator(this.removedCount),countDelta:this.signForDelta(this.countDelta)+e.NumberUtilities.withThousandsSeparator(Math.abs(this.countDelta)),addedSize:e.NumberUtilities.withThousandsSeparator(this.addedSize),removedSize:e.NumberUtilities.withThousandsSeparator(this.removedSize),sizeDelta:this.signForDelta(this.sizeDelta)+e.NumberUtilities.withThousandsSeparator(Math.abs(this.sizeDelta))}}get name(){return this.nameInternal}createProvider(){const e=this.dataGridInternal;if(null===e.snapshot||void 0===e.baseSnapshot||void 0===e.baseSnapshot.uid)throw new Error("Data sources have not been set correctly");const t=e.snapshot.createAddedNodesProvider(e.baseSnapshot.uid,this.nameInternal),i=e.baseSnapshot.createDeletedNodesProvider(this.deletedIndexes);if(!t||!i)throw new Error("Failed to create node providers");return new et(t,i,this.addedCount,this.removedCount)}createCell(e){const t=super.createCell(e);return"object"!==e&&t.classList.add("numeric-column"),t}createChildNode(e){const t=this.dataGridInternal;if(e.isAddedNotRemoved){if(null===t.snapshot)throw new Error("Data sources have not been set correctly");return new Ze(this.dataGridInternal,t.snapshot,e,!1)}if(void 0===t.baseSnapshot)throw new Error("Data sources have not been set correctly");return new Ze(this.dataGridInternal,t.baseSnapshot,e,!0)}comparator(){const e=this.dataGridInternal.isSortOrderAscending(),t=this.dataGridInternal.sortColumnId();switch(t){case"object":return new f.HeapSnapshotModel.ComparatorConfig("name",e,"id",!0);case"addedCount":case"removedCount":case"countDelta":return new f.HeapSnapshotModel.ComparatorConfig("name",!0,"id",!0);case"addedSize":case"removedSize":case"sizeDelta":return new f.HeapSnapshotModel.ComparatorConfig("selfSize",e,"id",!0);default:throw new Error(`Invalid sort column ${t}`)}}filteredOut(e){return-1===this.nameInternal.toLowerCase().indexOf(e)}signForDelta(e){return 0===e?"":e>0?"+":"−"}}class it extends qe{populated;allocationNode;constructor(t,i){super(t,i.hasChildren),this.populated=!1,this.allocationNode=i,this.data={liveCount:e.NumberUtilities.withThousandsSeparator(i.liveCount),count:e.NumberUtilities.withThousandsSeparator(i.count),liveSize:e.NumberUtilities.withThousandsSeparator(i.liveSize),size:e.NumberUtilities.withThousandsSeparator(i.size),name:i.name}}populate(){this.populated||this.doPopulate()}async doPopulate(){this.populated=!0;const e=await this.dataGridInternal.snapshot.allocationNodeCallers(this.allocationNode.id),t=e.nodesWithSingleCaller;let i=this;const s=this.dataGridInternal;for(const e of t){const t=new it(s,e);s.appendNode(i,t),i=t,i.populated=!0,this.expanded&&i.expand()}const r=e.branchingCallers;r.sort(this.dataGridInternal.createComparator());for(const e of r)s.appendNode(i,new it(s,e));s.updateVisibleNodes(!0)}expand(){super.expand(),1===this.children.length&&this.children[0].expand()}createCell(e){if("name"!==e)return this.createValueCell(e);const t=super.createCell(e),i=this.allocationNode,s=this.dataGridInternal.heapProfilerModel();if(i.scriptId){const e=this.dataGridInternal.linkifier.linkifyScriptLocation(s?s.target():null,String(i.scriptId),i.scriptName,i.line-1,{columnNumber:i.column-1,inlineFrameIndex:0,className:"profile-node-file"});e.style.maxWidth="75%",t.insertBefore(e,t.firstChild)}return t}allocationNodeId(){return this.allocationNode.id}}var st=Object.freeze({__proto__:null,get HeapSnapshotGridNode(){return qe},HeapSnapshotGenericObjectNode:Qe,HeapSnapshotObjectNode:Ke,HeapSnapshotRetainingObjectNode:Ye,HeapSnapshotInstanceNode:Ze,HeapSnapshotConstructorNode:Xe,HeapSnapshotDiffNodesProvider:et,HeapSnapshotDiffNode:tt,AllocationGridNode:it});const rt={distanceFromWindowObject:"Distance from window object",sizeOfTheObjectItselfInBytes:"Size of the object itself in bytes",sizeOfTheObjectPlusTheGraphIt:"Size of the object plus the graph it retains in bytes",object:"Object",distance:"Distance",shallowSize:"Shallow Size",retainedSize:"Retained Size",heapSnapshotRetainment:"Heap Snapshot Retainment",constructorString:"Constructor",heapSnapshotConstructors:"Heap Snapshot Constructors",New:"# New",Deleted:"# Deleted",Delta:"# Delta",allocSize:"Alloc. Size",freedSize:"Freed Size",sizeDelta:"Size Delta",heapSnapshotDiff:"Heap Snapshot Diff",liveCount:"Live Count",count:"Count",liveSize:"Live Size",size:"Size",function:"Function",allocation:"Allocation"},ot=t.i18n.registerUIStrings("panels/profiler/HeapSnapshotDataGrids.ts",rt),nt=t.i18n.getLocalizedString.bind(void 0,ot),at=new WeakMap;class lt extends s.DataGrid.DataGridImpl{}class dt extends(n.ObjectWrapper.eventMixin(lt)){snapshot;selectedNode;heapProfilerModelInternal;dataDisplayDelegateInternal;recursiveSortingDepth;populatedAndSorted;nameFilter;nodeFilterInternal;lastSortColumnId;lastSortAscending;constructor(e,t,i){super(i),this.snapshot=null,this.selectedNode=null,this.heapProfilerModelInternal=e,this.dataDisplayDelegateInternal=t;const s=[["distance",nt(rt.distanceFromWindowObject)],["shallowSize",nt(rt.sizeOfTheObjectItselfInBytes)],["retainedSize",nt(rt.sizeOfTheObjectPlusTheGraphIt)]];for(const e of s){const t=this.headerTableHeader(e[0]);t&&t.setAttribute("title",e[1])}this.recursiveSortingDepth=0,this.populatedAndSorted=!1,this.nameFilter=null,this.nodeFilterInternal=new f.HeapSnapshotModel.NodeFilter,this.addEventListener(ht.SortingComplete,this.sortingComplete,this),this.addEventListener("SortingChanged",this.sortingChanged,this),this.setRowContextMenuCallback(this.populateContextMenu.bind(this))}async setDataSource(e,t){}isFilteredOut(e){const t=this.nameFilter?this.nameFilter.value().toLowerCase():"";return!!(t&&(e instanceof tt||e instanceof Xe)&&e.filteredOut(t))}heapProfilerModel(){return this.heapProfilerModelInternal}dataDisplayDelegate(){return this.dataDisplayDelegateInternal}nodeFilter(){return this.nodeFilterInternal}setNameFilter(e){this.nameFilter=e}defaultPopulateCount(){return 100}disposeAllNodes(){const e=this.topLevelNodes();for(let t=0,i=e.length;t<i;++t)e[t].dispose()}wasShown(){this.nameFilter&&(this.nameFilter.addEventListener("TextChanged",this.onNameFilterChanged,this),this.updateVisibleNodes(!0)),this.populatedAndSorted&&this.dispatchEventToListeners(ht.ContentShown,this)}sortingComplete(){this.removeEventListener(ht.SortingComplete,this.sortingComplete,this),this.populatedAndSorted=!0,this.dispatchEventToListeners(ht.ContentShown,this)}willHide(){this.nameFilter&&this.nameFilter.removeEventListener("TextChanged",this.onNameFilterChanged,this)}populateContextMenu(e,t){const i=t;i.populateContextMenu(e,this.dataDisplayDelegateInternal,this.heapProfilerModel()),i instanceof Qe&&i.linkElement&&e.appendApplicableItems(i.linkElement)}resetSortingCache(){delete this.lastSortColumnId,delete this.lastSortAscending}topLevelNodes(){return this.rootNode().children}revealObjectByHeapSnapshotId(e){return Promise.resolve(null)}resetNameFilter(){this.nameFilter&&this.nameFilter.setValue("")}onNameFilterChanged(){this.updateVisibleNodes(!0),this.deselectFilteredNodes()}deselectFilteredNodes(){let e=this.selectedNode;for(;e;){if(this.selectedNode&&this.isFilteredOut(e))return this.selectedNode.deselect(),void(this.selectedNode=null);e=e.parent}}sortFields(e,t){throw new Error("Not implemented")}sortingChanged(){const e=this.isSortOrderAscending(),t=this.sortColumnId();if(this.lastSortColumnId===t&&this.lastSortAscending===e)return;this.lastSortColumnId=t,this.lastSortAscending=e;const i=this.sortFields(t||"",e);this.performSorting((function(e,t){let s=e[i.fieldName1],r=t[i.fieldName1],o=s<r?-1:s>r?1:0;return i.ascending1||(o=-o),0!==o||(s=e[i.fieldName2],r=t[i.fieldName2],o=s<r?-1:s>r?1:0,i.ascending2||(o=-o)),o}))}performSorting(e){this.recursiveSortingEnter();const t=this.allChildren(this.rootNode());this.rootNode().removeChildren(),t.sort(e);for(let e=0,i=t.length;e<i;++e){const i=t[e];this.appendChildAfterSorting(i),i.expanded&&i.sort()}this.recursiveSortingLeave()}appendChildAfterSorting(e){const t=e.revealed;this.rootNode().appendChild(e),e.revealed=t}recursiveSortingEnter(){++this.recursiveSortingDepth}recursiveSortingLeave(){this.recursiveSortingDepth&&(--this.recursiveSortingDepth||(this.updateVisibleNodes(!0),this.dispatchEventToListeners(ht.SortingComplete)))}updateVisibleNodes(e){}allChildren(e){return e.children}insertChild(e,t,i){e.insertChild(t,i)}removeChildByIndex(e,t){e.removeChild(e.children[t])}removeAllChildren(e){e.removeChildren()}async dataSourceChanged(){throw new Error("Not implemented")}}var ht,ct;!function(e){e.ContentShown="ContentShown",e.SortingComplete="SortingComplete",e.ExpandRetainersComplete="ExpandRetainersComplete"}(ht||(ht={}));class pt extends dt{topPaddingHeight;bottomPaddingHeight;selectedNode;scrollToResolveCallback;constructor(e,t,i){super(e,t,i),this.scrollContainer.addEventListener("scroll",this.onScroll.bind(this),!0),this.topPaddingHeight=0,this.bottomPaddingHeight=0,this.selectedNode=null}topLevelNodes(){return this.allChildren(this.rootNode())}appendChildAfterSorting(e){}updateVisibleNodes(e){const t=this.scrollContainer.scrollHeight;let i=this.scrollContainer.scrollTop,s=t-i-this.scrollContainer.offsetHeight;i=Math.max(0,i-40),s=Math.max(0,s-40);let r=t-i-s;if(!e&&i>=this.topPaddingHeight&&s>=this.bottomPaddingHeight)return;i-=500,r+=1e3;const o=this.selectedNode;this.rootNode().removeChildren(),this.topPaddingHeight=0,this.bottomPaddingHeight=0,this.addVisibleNodes(this.rootNode(),i,i+r),this.setVerticalPadding(this.topPaddingHeight,this.bottomPaddingHeight),o&&(o.parent?o.select(!0):this.selectedNode=o)}addVisibleNodes(e,t,i){if(!e.expanded)return 0;const s=this.allChildren(e);let r=0,o=0;for(;o<s.length;++o){const e=s[o];if(this.isFilteredOut(e))continue;const i=r+this.nodeHeight(e);if(i>t)break;r=i}let n=r;for(;o<s.length&&n<i;++o){const r=s[o];if(this.isFilteredOut(r))continue;const a=r.hasChildren();r.removeChildren(),r.setHasChildren(a),e.appendChild(r),n+=r.nodeSelfHeight(),n+=this.addVisibleNodes(r,t-n,i-n)}let a=0;for(;o<s.length;++o){const e=s[o];this.isFilteredOut(e)||(a+=this.nodeHeight(e))}return this.topPaddingHeight+=r,this.bottomPaddingHeight+=a,n+a}nodeHeight(e){let t=e.nodeSelfHeight();if(!e.expanded)return t;const i=this.allChildren(e);for(let e=0;e<i.length;e++)t+=this.nodeHeight(i[e]);return t}revealTreeNode(e){const t=this.calculateOffset(e),i=e[e.length-1],s=this.scrollContainer.scrollTop,r=s+this.scrollContainer.offsetHeight;if(t>=s&&t<r)return Promise.resolve(i);return this.scrollContainer.scrollTop=Math.max(0,t-40),new Promise((e=>{console.assert(!this.scrollToResolveCallback),this.scrollToResolveCallback=e.bind(null,i),this.scrollContainer.window().requestAnimationFrame((()=>{this.scrollToResolveCallback&&(this.scrollToResolveCallback(),this.scrollToResolveCallback=null)}))}))}calculateOffset(e){let t=this.rootNode(),i=0;if(0===e.length)return 0;for(let s=0;s<e.length;++s){const r=e[s],o=this.allChildren(t);for(let e=0;e<o.length;++e){const t=o[e];if(r===t){i+=r.nodeSelfHeight();break}i+=this.nodeHeight(t)}t=r}return i-e[e.length-1].nodeSelfHeight()}allChildren(e){const t=at.get(e)||[];return at.has(e)||at.set(e,t),t}appendNode(e,t){this.allChildren(e).push(t)}insertChild(e,t,i){this.allChildren(e).splice(i,0,t)}removeChildByIndex(e,t){this.allChildren(e).splice(t,1)}removeAllChildren(e){at.delete(e)}removeTopLevelNodes(){this.disposeAllNodes(),this.rootNode().removeChildren(),this.removeAllChildren(this.rootNode())}isScrolledIntoView(e){const t=this.scrollContainer.scrollTop,i=t+this.scrollContainer.clientHeight,s=e.offsetTop;return s+e.offsetHeight<=i&&s>=t}onResize(){super.onResize(),this.updateVisibleNodes(!1)}onScroll(e){this.updateVisibleNodes(!1),this.scrollToResolveCallback&&(this.scrollToResolveCallback(),this.scrollToResolveCallback=null)}}class ut extends dt{constructor(e,t,i,r){super(e,t,{displayName:i,columns:r=r||[{id:"object",title:nt(rt.object),disclosure:!0,sortable:!0},{id:"distance",title:nt(rt.distance),width:"70px",sortable:!0,fixedWidth:!0},{id:"shallowSize",title:nt(rt.shallowSize),width:"110px",sortable:!0,fixedWidth:!0},{id:"retainedSize",title:nt(rt.retainedSize),width:"110px",sortable:!0,fixedWidth:!0,sort:s.DataGrid.Order.Descending}]})}async setDataSource(e,t,i){this.snapshot=e;const s=new f.HeapSnapshotModel.Node(i??-1,"root",0,t||e.rootNodeIndex,0,0,"");this.setRootNode(this.createRootNode(e,s)),this.rootNode().sort()}createRootNode(e,t){const i=new f.HeapSnapshotModel.Edge("",t,"",-1);return new Ke(this,e,i,null)}sortingChanged(){const e=this.rootNode();e.hasChildren()&&e.sort()}}class mt extends ut{resetRetainersButton;constructor(e,t){const i=[{id:"object",title:nt(rt.object),disclosure:!0,sortable:!0},{id:"distance",title:nt(rt.distance),width:"70px",sortable:!0,fixedWidth:!0,sort:s.DataGrid.Order.Ascending},{id:"shallowSize",title:nt(rt.shallowSize),width:"110px",sortable:!0,fixedWidth:!0},{id:"retainedSize",title:nt(rt.retainedSize),width:"110px",sortable:!0,fixedWidth:!0}];super(e,t,nt(rt.heapSnapshotRetainment),i)}createRootNode(e,t){const i=new f.HeapSnapshotModel.Edge("",t,"",-1);return new Ye(this,e,i,null)}sortFields(e,t){switch(e){case"object":return new f.HeapSnapshotModel.ComparatorConfig("name",t,"count",!1);case"count":return new f.HeapSnapshotModel.ComparatorConfig("count",t,"name",!0);case"shallowSize":return new f.HeapSnapshotModel.ComparatorConfig("shallowSize",t,"name",!0);case"retainedSize":return new f.HeapSnapshotModel.ComparatorConfig("retainedSize",t,"name",!0);case"distance":return new f.HeapSnapshotModel.ComparatorConfig("distance",t,"name",!0);default:throw new Error(`Unknown column ${e}`)}}reset(){this.rootNode().removeChildren(),this.resetSortingCache()}updateResetButtonVisibility(){this.snapshot?.areNodesIgnoredInRetainersView().then((e=>{this.resetRetainersButton?.setVisible(e)}))}async setDataSource(e,t,i){await super.setDataSource(e,t,i),this.rootNode().expand(),this.updateResetButtonVisibility()}async dataSourceChanged(){this.reset(),await this.rootNode().sort(),this.rootNode().expand(),this.updateResetButtonVisibility()}}!function(e){e.ExpandRetainersComplete="ExpandRetainersComplete"}(ct||(ct={}));class ft extends pt{profileIndex;objectIdToSelect;nextRequestedFilter;lastFilter;filterInProgress;constructor(e,t){const i=[{id:"object",title:nt(rt.constructorString),disclosure:!0,sortable:!0},{id:"distance",title:nt(rt.distance),width:"70px",sortable:!0,fixedWidth:!0},{id:"shallowSize",title:nt(rt.shallowSize),width:"110px",sortable:!0,fixedWidth:!0},{id:"retainedSize",title:nt(rt.retainedSize),width:"110px",sort:s.DataGrid.Order.Descending,sortable:!0,fixedWidth:!0}];super(e,t,{displayName:nt(rt.heapSnapshotConstructors).toString(),columns:i}),this.profileIndex=-1,this.objectIdToSelect=null,this.nextRequestedFilter=null}sortFields(e,t){switch(e){case"object":return new f.HeapSnapshotModel.ComparatorConfig("name",t,"retainedSize",!1);case"distance":return new f.HeapSnapshotModel.ComparatorConfig("distance",t,"retainedSize",!1);case"shallowSize":return new f.HeapSnapshotModel.ComparatorConfig("shallowSize",t,"name",!0);case"retainedSize":return new f.HeapSnapshotModel.ComparatorConfig("retainedSize",t,"name",!0);default:throw new Error(`Unknown column ${e}`)}}async revealObjectByHeapSnapshotId(e){if(!this.snapshot)return this.objectIdToSelect=e,null;const t=await this.snapshot.nodeClassName(parseInt(e,10));if(!t)return null;const i=this.topLevelNodes().find((e=>e.name===t));if(!i)return null;const s=await i.populateNodeBySnapshotObjectId(parseInt(e,10));return s.length?this.revealTreeNode(s):null}clear(){this.nextRequestedFilter=null,this.lastFilter=null,this.removeTopLevelNodes()}async setDataSource(e,t){this.snapshot=e,-1===this.profileIndex&&this.populateChildren(),this.objectIdToSelect&&(this.revealObjectByHeapSnapshotId(this.objectIdToSelect),this.objectIdToSelect=null)}setSelectionRange(e,t){this.nodeFilterInternal=new f.HeapSnapshotModel.NodeFilter(e,t),this.populateChildren(this.nodeFilterInternal)}setAllocationNodeId(e){this.nodeFilterInternal=new f.HeapSnapshotModel.NodeFilter,this.nodeFilterInternal.allocationNodeId=e,this.populateChildren(this.nodeFilterInternal)}aggregatesReceived(e,t){this.filterInProgress=null,this.nextRequestedFilter&&this.snapshot&&(this.snapshot.aggregatesWithFilter(this.nextRequestedFilter).then(this.aggregatesReceived.bind(this,this.nextRequestedFilter)),this.filterInProgress=this.nextRequestedFilter,this.nextRequestedFilter=null),this.removeTopLevelNodes(),this.resetSortingCache();for(const i in t)this.appendNode(this.rootNode(),new Xe(this,i,t[i],e));this.sortingChanged(),this.lastFilter=e}async populateChildren(e){const t=e||new f.HeapSnapshotModel.NodeFilter;if(this.filterInProgress)this.nextRequestedFilter=this.filterInProgress.equals(t)?null:t;else if((!this.lastFilter||!this.lastFilter.equals(t))&&(this.filterInProgress=t,this.snapshot)){const e=await this.snapshot.aggregatesWithFilter(t);this.aggregatesReceived(t,e)}}filterSelectIndexChanged(e,t,i){if(this.profileIndex=t,this.nodeFilterInternal=void 0,-1!==t){const i=t>0?e[t-1].maxJSObjectId:0,s=e[t].maxJSObjectId;this.nodeFilterInternal=new f.HeapSnapshotModel.NodeFilter(i,s)}else void 0!==i&&(this.nodeFilterInternal=new f.HeapSnapshotModel.NodeFilter,this.nodeFilterInternal.filterName=i);this.populateChildren(this.nodeFilterInternal)}}class gt extends pt{baseSnapshot;constructor(e,t){const i=[{id:"object",title:nt(rt.constructorString),disclosure:!0,sortable:!0},{id:"addedCount",title:nt(rt.New),width:"75px",sortable:!0,fixedWidth:!0},{id:"removedCount",title:nt(rt.Deleted),width:"75px",sortable:!0,fixedWidth:!0},{id:"countDelta",title:nt(rt.Delta),width:"65px",sortable:!0,fixedWidth:!0},{id:"addedSize",title:nt(rt.allocSize),width:"75px",sortable:!0,fixedWidth:!0,sort:s.DataGrid.Order.Descending},{id:"removedSize",title:nt(rt.freedSize),width:"75px",sortable:!0,fixedWidth:!0},{id:"sizeDelta",title:nt(rt.sizeDelta),width:"75px",sortable:!0,fixedWidth:!0}];super(e,t,{displayName:nt(rt.heapSnapshotDiff).toString(),columns:i})}defaultPopulateCount(){return 50}sortFields(e,t){switch(e){case"object":return new f.HeapSnapshotModel.ComparatorConfig("name",t,"count",!1);case"addedCount":return new f.HeapSnapshotModel.ComparatorConfig("addedCount",t,"name",!0);case"removedCount":return new f.HeapSnapshotModel.ComparatorConfig("removedCount",t,"name",!0);case"countDelta":return new f.HeapSnapshotModel.ComparatorConfig("countDelta",t,"name",!0);case"addedSize":return new f.HeapSnapshotModel.ComparatorConfig("addedSize",t,"name",!0);case"removedSize":return new f.HeapSnapshotModel.ComparatorConfig("removedSize",t,"name",!0);case"sizeDelta":return new f.HeapSnapshotModel.ComparatorConfig("sizeDelta",t,"name",!0);default:throw new Error(`Unknown column ${e}`)}}async setDataSource(e,t){this.snapshot=e}setBaseDataSource(e){this.baseSnapshot=e,this.removeTopLevelNodes(),this.resetSortingCache(),this.baseSnapshot!==this.snapshot?this.populateChildren():this.dispatchEventToListeners(ht.SortingComplete)}async populateChildren(){if(null===this.snapshot||void 0===this.baseSnapshot||void 0===this.baseSnapshot.uid)throw new Error("Data sources have not been set correctly");const e=await this.baseSnapshot.aggregatesForDiff(),t=await this.snapshot.calculateSnapshotDiff(this.baseSnapshot.uid,e);for(const e in t){const i=t[e];this.appendNode(this.rootNode(),new tt(this,e,i))}this.sortingChanged()}}class vt extends pt{linkifierInternal;topNodes;constructor(e,t){const i=[{id:"liveCount",title:nt(rt.liveCount),width:"75px",sortable:!0,fixedWidth:!0},{id:"count",title:nt(rt.count),width:"65px",sortable:!0,fixedWidth:!0},{id:"liveSize",title:nt(rt.liveSize),width:"75px",sortable:!0,fixedWidth:!0},{id:"size",title:nt(rt.size),width:"75px",sortable:!0,fixedWidth:!0,sort:s.DataGrid.Order.Descending},{id:"name",title:nt(rt.function),disclosure:!0,sortable:!0}];super(e,t,{displayName:nt(rt.allocation).toString(),columns:i}),this.linkifierInternal=new u.Linkifier.Linkifier}get linkifier(){return this.linkifierInternal}dispose(){this.linkifierInternal.reset()}async setDataSource(e,t){this.snapshot=e,this.topNodes=await this.snapshot.allocationTracesTops(),this.populateChildren()}populateChildren(){this.removeTopLevelNodes();const e=this.rootNode(),t=this.topNodes||[];for(const i of t)this.appendNode(e,new it(this,i));this.updateVisibleNodes(!0)}sortingChanged(){void 0!==this.topNodes&&(this.topNodes.sort(this.createComparator()),this.rootNode().removeChildren(),this.populateChildren())}createComparator(){const e=this.sortColumnId(),t=this.sortOrder()===s.DataGrid.Order.Ascending?1:-1;return function(i,s){return i[e]>s[e]?t:i[e]<s[e]?-t:0}}}var St=Object.freeze({__proto__:null,HeapSnapshotSortableDataGrid:dt,get HeapSnapshotSortableDataGridEvents(){return ht},HeapSnapshotViewportDataGrid:pt,HeapSnapshotContainmentDataGrid:ut,HeapSnapshotRetainmentDataGrid:mt,get HeapSnapshotRetainmentDataGridEvents(){return ct},HeapSnapshotConstructorsDataGrid:ft,HeapSnapshotDiffDataGrid:gt,AllocationDataGrid:vt});const wt={anErrorOccurredWhenACallToMethod:"An error occurred when a call to method ''{PH1}'' was requested"},bt=t.i18n.registerUIStrings("panels/profiler/HeapSnapshotProxy.ts",wt),Ct=t.i18n.getLocalizedString.bind(void 0,bt);class yt extends n.ObjectWrapper.ObjectWrapper{eventHandler;nextObjectId;nextCallId;callbacks;previousCallbacks;worker;interval;constructor(e){super(),this.eventHandler=e,this.nextObjectId=1,this.nextCallId=1,this.callbacks=new Map,this.previousCallbacks=new Set,this.worker=n.Worker.WorkerWrapper.fromURL(new URL("../../entrypoints/heap_snapshot_worker/heap_snapshot_worker-entrypoint.js",import.meta.url)),this.worker.onmessage=this.messageReceived.bind(this)}createLoader(e,t){const i=this.nextObjectId++,s=new Tt(this,i,e,t);return this.postMessage({callId:this.nextCallId++,disposition:"createLoader",objectId:i}),s}dispose(){this.worker.terminate(),this.interval&&clearInterval(this.interval)}disposeObject(e){this.postMessage({callId:this.nextCallId++,disposition:"dispose",objectId:e})}evaluateForTest(e,t){const i=this.nextCallId++;this.callbacks.set(i,t),this.postMessage({callId:i,disposition:"evaluateForTest",source:e})}callFactoryMethod(e,t,i,s,...r){const o=this.nextCallId++,n=this.nextObjectId++;return e?(this.callbacks.set(o,(t=>{e(t?new s(this,n):null)})),this.postMessage({callId:o,disposition:"factory",objectId:t,methodName:i,methodArguments:r,newObjectId:n}),null):(this.postMessage({callId:o,disposition:"factory",objectId:t,methodName:i,methodArguments:r,newObjectId:n}),new s(this,n))}callMethod(e,t,i,...s){const r=this.nextCallId++;e&&this.callbacks.set(r,e),this.postMessage({callId:r,disposition:"method",objectId:t,methodName:i,methodArguments:s})}startCheckingForLongRunningCalls(){this.interval||(this.checkLongRunningCalls(),this.interval=window.setInterval(this.checkLongRunningCalls.bind(this),300))}checkLongRunningCalls(){for(const e of this.previousCallbacks)this.callbacks.has(e)||this.previousCallbacks.delete(e);const e=Boolean(this.previousCallbacks.size);this.dispatchEventToListeners("Wait",e);for(const e of this.callbacks.keys())this.previousCallbacks.add(e)}messageReceived(e){const t=e.data;if(t.eventName)return void(this.eventHandler&&this.eventHandler(t.eventName,t.data));if(t.error)return t.errorMethodName&&n.Console.Console.instance().error(Ct(wt.anErrorOccurredWhenACallToMethod,{PH1:t.errorMethodName})),n.Console.Console.instance().error(t.errorCallStack),void this.callbacks.delete(t.callId);const i=this.callbacks.get(t.callId);i&&(this.callbacks.delete(t.callId),i(t.result))}postMessage(e){this.worker.postMessage(e)}}class Pt{worker;objectId;constructor(e,t){this.worker=e,this.objectId=t}dispose(){this.worker.disposeObject(this.objectId)}disposeWorker(){this.worker.dispose()}callFactoryMethod(e,t,...i){return this.worker.callFactoryMethod(null,String(this.objectId),e,t,...i)}callFactoryMethodPromise(e,t,...i){return new Promise((s=>this.worker.callFactoryMethod(s,String(this.objectId),e,t,...i)))}callMethodPromise(e,...t){return new Promise((i=>this.worker.callMethod(i,String(this.objectId),e,...t)))}}class Tt extends Pt{profileUid;snapshotReceivedCallback;constructor(e,t,i,s){super(e,t),this.profileUid=i,this.snapshotReceivedCallback=s}async write(e){await this.callMethodPromise("write",e)}async close(){await this.callMethodPromise("close");const e=await this.callFactoryMethodPromise("buildSnapshot",It);this.dispose(),e.setProfileUid(this.profileUid),await e.updateStaticData(),this.snapshotReceivedCallback(e)}}class It extends Pt{staticData;profileUid;constructor(e,t){super(e,t),this.staticData=null}search(e,t){return this.callMethodPromise("search",e,t)}aggregatesWithFilter(e){return this.callMethodPromise("aggregatesWithFilter",e)}aggregatesForDiff(){return this.callMethodPromise("aggregatesForDiff")}calculateSnapshotDiff(e,t){return this.callMethodPromise("calculateSnapshotDiff",e,t)}nodeClassName(e){return this.callMethodPromise("nodeClassName",e)}createEdgesProvider(e){return this.callFactoryMethod("createEdgesProvider",xt,e)}createRetainingEdgesProvider(e){return this.callFactoryMethod("createRetainingEdgesProvider",xt,e)}createAddedNodesProvider(e,t){return this.callFactoryMethod("createAddedNodesProvider",xt,e,t)}createDeletedNodesProvider(e){return this.callFactoryMethod("createDeletedNodesProvider",xt,e)}createNodesProvider(e){return this.callFactoryMethod("createNodesProvider",xt,e)}createNodesProviderForClass(e,t){return this.callFactoryMethod("createNodesProviderForClass",xt,e,t)}allocationTracesTops(){return this.callMethodPromise("allocationTracesTops")}allocationNodeCallers(e){return this.callMethodPromise("allocationNodeCallers",e)}allocationStack(e){return this.callMethodPromise("allocationStack",e)}dispose(){throw new Error("Should never be called")}get nodeCount(){return this.staticData?this.staticData.nodeCount:0}get rootNodeIndex(){return this.staticData?this.staticData.rootNodeIndex:0}async updateStaticData(){this.staticData=await this.callMethodPromise("updateStaticData")}getStatistics(){return this.callMethodPromise("getStatistics")}getLocation(e){return this.callMethodPromise("getLocation",e)}getSamples(){return this.callMethodPromise("getSamples")}ignoreNodeInRetainersView(e){return this.callMethodPromise("ignoreNodeInRetainersView",e)}unignoreNodeInRetainersView(e){return this.callMethodPromise("unignoreNodeInRetainersView",e)}unignoreAllNodesInRetainersView(){return this.callMethodPromise("unignoreAllNodesInRetainersView")}areNodesIgnoredInRetainersView(){return this.callMethodPromise("areNodesIgnoredInRetainersView")}get totalSize(){return this.staticData?this.staticData.totalSize:0}get uid(){return this.profileUid}setProfileUid(e){this.profileUid=e}maxJSObjectId(){return this.staticData?this.staticData.maxJSObjectId:0}}class xt extends Pt{constructor(e,t){super(e,t)}nodePosition(e){return this.callMethodPromise("nodePosition",e)}isEmpty(){return this.callMethodPromise("isEmpty")}serializeItemsRange(e,t){return this.callMethodPromise("serializeItemsRange",e,t)}async sortAndRewind(e){await this.callMethodPromise("sortAndRewind",e)}}var Rt=Object.freeze({__proto__:null,HeapSnapshotWorkerProxy:yt,HeapSnapshotProxyObject:Pt,HeapSnapshotLoaderProxy:Tt,HeapSnapshotProxy:It,HeapSnapshotProviderProxy:xt});const Nt={find:"Find",containment:"Containment",retainers:"Retainers",allocationStack:"Allocation stack",perspective:"Perspective",baseSnapshot:"Base snapshot",filter:"Filter",filterByClass:"Filter by class",code:"Code",strings:"Strings",jsArrays:"JS arrays",typedArrays:"Typed arrays",systemObjects:"System objects",selectedSizeS:"Selected size: {PH1}",allObjects:"All objects",objectsAllocatedBeforeS:"Objects allocated before {PH1}",objectsAllocatedBetweenSAndS:"Objects allocated between {PH1} and {PH2}",duplicatedStrings:"Duplicated strings",objectsRetainedByDetachedDomNodes:"Objects retained by detached DOM nodes",objectsRetainedByConsole:"Objects retained by the DevTools console",summary:"Summary",comparison:"Comparison",allocation:"Allocation",liveObjects:"Live objects",statistics:"Statistics",heapSnapshot:"Heap snapshot",takeHeapSnapshot:"Take heap snapshot",heapSnapshots:"Heap snapshots",heapSnapshotProfilesShowMemory:"Heap snapshot profiles show memory distribution among your page's JavaScript objects and related DOM nodes.",exposeInternals:"Expose internals (includes additional implementation-specific details)",snapshotting:"Snapshotting…",snapshotD:"Snapshot {PH1}",percentagePlaceholder:"{PH1}%",allocationInstrumentationOn:"Allocation instrumentation on timeline",stopRecordingHeapProfile:"Stop recording heap profile",startRecordingHeapProfile:"Start recording heap profile",recordAllocationStacksExtra:"Record stack traces of allocations (extra performance overhead)",recording:"Recording…",allocationTimelines:"Allocation timelines",AllocationTimelinesShowInstrumented:"Allocation timelines show instrumented JavaScript memory allocations over time. Once profile is recorded you can select a time interval to see objects that were allocated within it and still alive by the end of recording. Use this profile type to isolate memory leaks.",loading:"Loading…",savingD:"Saving… {PH1}%",heapMemoryUsage:"Heap memory usage",stackWasNotRecordedForThisObject:"Stack was not recorded for this object because it had been allocated before this profile recording started.",restoreIgnoredRetainers:"Restore ignored retainers"},kt=t.i18n.registerUIStrings("panels/profiler/HeapSnapshotView.ts",Nt),Dt=t.i18n.getLocalizedString.bind(void 0,kt),Et=t.i18n.registerUIStrings("panels/profiler/ModuleUIStrings.ts",{buildingEdgeIndexes:"Building edge indexes…",buildingRetainers:"Building retainers…",propagatingDomState:"Propagating DOM state…",calculatingNodeFlags:"Calculating node flags…",calculatingDistances:"Calculating distances…",buildingPostorderIndex:"Building postorder index…",buildingDominatorTree:"Building dominator tree…",calculatingShallowSizes:"Calculating shallow sizes…",calculatingRetainedSizes:"Calculating retained sizes…",buildingDominatedNodes:"Building dominated nodes…",calculatingStatistics:"Calculating statistics…",calculatingSamples:"Calculating samples…",buildingLocations:"Building locations…",finishedProcessing:"Finished processing.",buildingAllocationStatistics:"Building allocation statistics…",done:"Done",processingSnapshot:"Processing snapshot…",parsingStrings:"Parsing strings…",loadingSnapshotInfo:"Loading snapshot info…",loadingNodesD:"Loading nodes… {PH1}%",loadingEdgesD:"Loading edges… {PH1}%",loadingAllocationTracesD:"Loading allocation traces… {PH1}%",loadingSamples:"Loading samples…",loadingLocations:"Loading locations…",loadingStrings:"Loading strings…"}),Mt=t.i18n.getLocalizedString.bind(void 0,Et);class Ft extends r.View.SimpleView{searchResults;profile;linkifier;parentDataDisplayDelegate;searchableViewInternal;splitWidget;containmentDataGrid;containmentWidget;statisticsView;constructorsDataGrid;constructorsWidget;diffDataGrid;diffWidget;allocationDataGrid;allocationWidget;allocationStackView;tabbedPane;retainmentDataGrid;retainmentWidget;objectDetailsView;perspectives;comparisonPerspective;perspectiveSelect;baseSelect;filterSelect;classNameFilter;selectedSizeText;resetRetainersButton;popoverHelper;currentPerspectiveIndex;currentPerspective;dataGrid;searchThrottler;baseProfile;trackingOverviewGrid;currentSearchResultIndex=-1;currentQuery;constructor(e,t){super(Dt(Nt.heapSnapshot)),this.searchResults=[],this.element.classList.add("heap-snapshot-view"),this.profile=t,this.linkifier=new u.Linkifier.Linkifier;const i=t.profileType();i.addEventListener("SnapshotReceived",this.onReceiveSnapshot,this),i.addEventListener("remove-profile-header",this.onProfileHeaderRemoved,this);const s=i.id===Gt.TypeId;s&&this.createOverview();const o=qt.trackingHeapSnapshotProfileType.recordAllocationStacksSetting().get();this.parentDataDisplayDelegate=e,this.searchableViewInternal=new r.SearchableView.SearchableView(this,null),this.searchableViewInternal.setPlaceholder(Dt(Nt.find),Dt(Nt.find)),this.searchableViewInternal.show(this.element),this.splitWidget=new r.SplitWidget.SplitWidget(!1,!0,"heap-snapshot-split-view-state",200,200),this.splitWidget.show(this.searchableViewInternal.element);const a=t.heapProfilerModel();let d;if(this.containmentDataGrid=new ut(a,this,Dt(Nt.containment)),this.containmentDataGrid.addEventListener("SelectedNode",this.selectionChanged,this),this.containmentWidget=this.containmentDataGrid.asWidget(),this.containmentWidget.setMinimumSize(50,25),this.statisticsView=new Wt,this.constructorsDataGrid=new ft(a,this),this.constructorsDataGrid.addEventListener("SelectedNode",this.selectionChanged,this),this.constructorsWidget=this.constructorsDataGrid.asWidget(),this.constructorsWidget.setMinimumSize(50,25),this.constructorsWidget.element.setAttribute("jslog",`${l.pane("heap-snapshot.constructors-view").track({resize:!0})}`),this.diffDataGrid=new gt(a,this),this.diffDataGrid.addEventListener("SelectedNode",this.selectionChanged,this),this.diffWidget=this.diffDataGrid.asWidget(),this.diffWidget.setMinimumSize(50,25),this.allocationDataGrid=null,s&&o&&(this.allocationDataGrid=new vt(a,this),this.allocationDataGrid.addEventListener("SelectedNode",this.onSelectAllocationNode,this),this.allocationWidget=this.allocationDataGrid.asWidget(),this.allocationWidget.setMinimumSize(50,25),this.allocationStackView=new Ut(a),this.allocationStackView.setMinimumSize(50,25),this.tabbedPane=new r.TabbedPane.TabbedPane),this.retainmentDataGrid=new mt(a,this),this.retainmentWidget=this.retainmentDataGrid.asWidget(),this.retainmentWidget.setMinimumSize(50,21),this.retainmentWidget.element.classList.add("retaining-paths-view"),this.retainmentWidget.element.setAttribute("jslog",`${l.pane("heap-snapshot.retaining-paths-view").track({resize:!0})}`),this.allocationStackView)this.tabbedPane=new r.TabbedPane.TabbedPane,this.tabbedPane.appendTab("retainers",Dt(Nt.retainers),this.retainmentWidget),this.tabbedPane.appendTab("allocation-stack",Dt(Nt.allocationStack),this.allocationStackView),d=this.tabbedPane.headerElement(),this.objectDetailsView=this.tabbedPane;else{const e=document.createElement("div");e.classList.add("heap-snapshot-view-resizer");const t=e.createChild("div","title");e.createChild("div","verticalResizerIcon");t.createChild("span").textContent=Dt(Nt.retainers),d=e,this.objectDetailsView=new r.Widget.VBox,this.objectDetailsView.element.appendChild(e),this.retainmentWidget.show(this.objectDetailsView.element)}this.splitWidget.hideDefaultResizer(),this.splitWidget.installResizer(d),this.retainmentDataGrid.addEventListener("SelectedNode",this.inspectedObjectChanged,this),this.retainmentDataGrid.reset(),this.perspectives=[],this.comparisonPerspective=new Lt,this.perspectives.push(new Ht),t.profileType()!==qt.trackingHeapSnapshotProfileType&&this.perspectives.push(this.comparisonPerspective),this.perspectives.push(new Ot),this.allocationWidget&&this.perspectives.push(new zt),this.perspectives.push(new Bt),this.perspectiveSelect=new r.Toolbar.ToolbarComboBox(this.onSelectedPerspectiveChanged.bind(this),Dt(Nt.perspective),void 0,"profiler.heap-snapshot-perspective"),this.updatePerspectiveOptions(),this.baseSelect=new r.Toolbar.ToolbarComboBox(this.changeBase.bind(this),Dt(Nt.baseSnapshot),void 0,"profiler.heap-snapshot-base"),this.baseSelect.setVisible(!1),this.updateBaseOptions(),this.filterSelect=new r.Toolbar.ToolbarComboBox(this.changeFilter.bind(this),Dt(Nt.filter),void 0,"profiler.heap-snapshot-filter"),this.filterSelect.setVisible(!1),this.updateFilterOptions(),this.classNameFilter=new r.Toolbar.ToolbarFilter(Dt(Nt.filterByClass)),this.classNameFilter.setVisible(!1),this.constructorsDataGrid.setNameFilter(this.classNameFilter),this.diffDataGrid.setNameFilter(this.classNameFilter),this.selectedSizeText=new r.Toolbar.ToolbarText;const h=Dt(Nt.restoreIgnoredRetainers);this.resetRetainersButton=new r.Toolbar.ToolbarButton(h,"clear-list",h),this.resetRetainersButton.setVisible(!1),this.resetRetainersButton.addEventListener("Click",(async()=>{await(this.retainmentDataGrid.snapshot?.unignoreAllNodesInRetainersView()),await this.retainmentDataGrid.dataSourceChanged()})),this.retainmentDataGrid.resetRetainersButton=this.resetRetainersButton,this.popoverHelper=new r.PopoverHelper.PopoverHelper(this.element,this.getPopoverRequest.bind(this),"profiler.heap-snapshot-object"),this.popoverHelper.setDisableOnClick(!0),this.popoverHelper.setHasPadding(!0),this.element.addEventListener("scroll",this.popoverHelper.hidePopover.bind(this.popoverHelper),!0),this.currentPerspectiveIndex=0,this.currentPerspective=this.perspectives[0],this.currentPerspective.activate(this),this.dataGrid=this.currentPerspective.masterGrid(this),this.populate(),this.searchThrottler=new n.Throttler.Throttler(0);for(const e of this.profiles())e.addEventListener("ProfileTitleChanged",this.updateControls,this)}createOverview(){const e=this.profile.profileType();this.trackingOverviewGrid=new de,this.trackingOverviewGrid.addEventListener("IdsRangeChanged",this.onIdsRangeChanged.bind(this)),this.profile.fromFile()||e.profileBeingRecorded()!==this.profile||(e.addEventListener("HeapStatsUpdate",this.onHeapStatsUpdate,this),e.addEventListener("TrackingStopped",this.onStopTracking,this),this.trackingOverviewGrid.start())}onStopTracking(){const e=this.profile.profileType();e.removeEventListener("HeapStatsUpdate",this.onHeapStatsUpdate,this),e.removeEventListener("TrackingStopped",this.onStopTracking,this),this.trackingOverviewGrid&&this.trackingOverviewGrid.stop()}onHeapStatsUpdate({data:e}){this.trackingOverviewGrid&&this.trackingOverviewGrid.setSamples(e)}searchableView(){return this.searchableViewInternal}showProfile(e){return this.parentDataDisplayDelegate.showProfile(e)}showObject(e,t){Number(e)<=this.profile.maxJSObjectId?this.selectLiveObject(t,e):this.parentDataDisplayDelegate.showObject(e,t)}async linkifyObject(e){const t=this.profile.heapProfilerModel();if(!t)return null;const i=await this.profile.getLocation(e);if(!i)return null;const s=t.runtimeModel().debuggerModel().createRawLocationByScriptId(String(i.scriptId),i.lineNumber,i.columnNumber);if(!s)return null;const r=s.script(),o=r&&r.sourceURL;return o&&this.linkifier?this.linkifier.linkifyRawLocation(s,o):null}async populate(){const e=await this.profile.loadPromise;if(this.retrieveStatistics(e),this.dataGrid&&this.dataGrid.setDataSource(e,0),this.profile.profileType().id===Gt.TypeId&&this.profile.fromFile()){const t=await e.getSamples();if(t){console.assert(Boolean(t.timestamps.length));const e=new ce;e.sizes=t.sizes,e.ids=t.lastAssignedIds,e.timestamps=t.timestamps,e.max=t.sizes,e.totalTime=Math.max(t.timestamps[t.timestamps.length-1]||0,1e4),this.trackingOverviewGrid&&this.trackingOverviewGrid.setSamples(e)}}const t=this.profiles().indexOf(this.profile);this.baseSelect.setSelectedIndex(Math.max(0,t-1)),this.trackingOverviewGrid&&this.trackingOverviewGrid.updateGrid()}async retrieveStatistics(e){const t=await e.getStatistics(),i=[{value:t.code,color:"#f77",title:Dt(Nt.code)},{value:t.strings,color:"#5e5",title:Dt(Nt.strings)},{value:t.jsArrays,color:"#7af",title:Dt(Nt.jsArrays)},{value:t.native,color:"#fc5",title:Dt(Nt.typedArrays)},{value:t.system,color:"#98f",title:Dt(Nt.systemObjects)}];return this.statisticsView.setTotalAndRecords(t.total,i),t}onIdsRangeChanged(t){const{minId:i,maxId:s}=t.data;this.selectedSizeText.setText(Dt(Nt.selectedSizeS,{PH1:e.NumberUtilities.bytesToString(t.data.size)})),this.constructorsDataGrid.snapshot&&this.constructorsDataGrid.setSelectionRange(i,s)}async toolbarItems(){const e=[this.perspectiveSelect,this.classNameFilter];return this.profile.profileType()!==qt.trackingHeapSnapshotProfileType&&e.push(this.baseSelect,this.filterSelect),e.push(this.selectedSizeText),e.push(this.resetRetainersButton),e}willHide(){this.currentSearchResultIndex=-1,this.popoverHelper.hidePopover()}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!1}onSearchCanceled(){this.currentSearchResultIndex=-1,this.searchResults=[]}selectRevealedNode(e){e&&e.select()}performSearch(e,t,i){const s=new f.HeapSnapshotModel.SearchConfig(e.query.trim(),e.caseSensitive,e.isRegex,t,i||!1);this.searchThrottler.schedule(this.performSearchInternal.bind(this,s))}async performSearchInternal(e){if(this.onSearchCanceled(),!this.currentPerspective.supportsSearch())return;this.currentQuery=e;const t=e.query.trim();if(!t)return;if("@"===t.charAt(0)){const e=parseInt(t.substring(1),10);if(isNaN(e))return;if(!this.dataGrid)return;const i=await this.dataGrid.revealObjectByHeapSnapshotId(String(e));return void this.selectRevealedNode(i)}if(!this.profile.snapshotProxy||!this.dataGrid)return;const i=this.dataGrid.nodeFilter();this.searchResults=i?await this.profile.snapshotProxy.search(this.currentQuery,i):[],this.searchableViewInternal.updateSearchMatchesCount(this.searchResults.length),this.searchResults.length&&(this.currentSearchResultIndex=e.jumpBackward?this.searchResults.length-1:0),await this.jumpToSearchResult(this.currentSearchResultIndex)}jumpToNextSearchResult(){this.searchResults.length&&(this.currentSearchResultIndex=(this.currentSearchResultIndex+1)%this.searchResults.length,this.searchThrottler.schedule(this.jumpToSearchResult.bind(this,this.currentSearchResultIndex)))}jumpToPreviousSearchResult(){this.searchResults.length&&(this.currentSearchResultIndex=(this.currentSearchResultIndex+this.searchResults.length-1)%this.searchResults.length,this.searchThrottler.schedule(this.jumpToSearchResult.bind(this,this.currentSearchResultIndex)))}async jumpToSearchResult(e){if(this.searchableViewInternal.updateCurrentMatchIndex(e),-1===e)return;if(!this.dataGrid)return;const t=await this.dataGrid.revealObjectByHeapSnapshotId(String(this.searchResults[e]));this.selectRevealedNode(t)}refreshVisibleData(){if(!this.dataGrid)return;let e=this.dataGrid.rootNode().children[0];for(;e;)e.refresh(),e=e.traverseNextNode(!1,null,!0)}changeBase(){if(this.baseProfile===this.profiles()[this.baseSelect.selectedIndex()])return;this.baseProfile=this.profiles()[this.baseSelect.selectedIndex()];const e=this.dataGrid;e.snapshot&&this.baseProfile.loadPromise.then(e.setBaseDataSource.bind(e)),this.currentQuery&&this.searchResults&&this.performSearch(this.currentQuery,!1)}static ALWAYS_AVAILABLE_FILTERS=[{uiName:Dt(Nt.duplicatedStrings),filterName:"duplicatedStrings"},{uiName:Dt(Nt.objectsRetainedByDetachedDomNodes),filterName:"objectsRetainedByDetachedDomNodes"},{uiName:Dt(Nt.objectsRetainedByConsole),filterName:"objectsRetainedByConsole"}];changeFilter(){let e,t=this.filterSelect.selectedIndex();const i=this.filterSelect.size()-Ft.ALWAYS_AVAILABLE_FILTERS.length;t>=i&&(e=Ft.ALWAYS_AVAILABLE_FILTERS[t-i].filterName,t=0);const s=t-1;this.dataGrid&&(this.dataGrid.filterSelectIndexChanged(this.profiles(),s,e),this.currentQuery&&this.searchResults&&this.performSearch(this.currentQuery,!1))}profiles(){return this.profile.profileType().getProfiles()}selectionChanged(e){const t=e.data;this.setSelectedNodeForDetailsView(t),this.inspectedObjectChanged(e)}onSelectAllocationNode(e){const t=e.data;this.constructorsDataGrid.setAllocationNodeId(t.allocationNodeId()),this.setSelectedNodeForDetailsView(null)}inspectedObjectChanged(e){const t=e.data,i=this.profile.heapProfilerModel();i&&t instanceof Qe&&i.addInspectedHeapObject(String(t.snapshotNodeId))}setSelectedNodeForDetailsView(e){const t=e&&e.retainersDataSource();t?(this.retainmentDataGrid.setDataSource(t.snapshot,t.snapshotNodeIndex,t.snapshotNodeId),this.allocationStackView&&this.allocationStackView.setAllocatedObject(t.snapshot,t.snapshotNodeIndex)):(this.allocationStackView&&this.allocationStackView.clear(),this.retainmentDataGrid.reset())}async changePerspectiveAndWait(e){const t=this.perspectives.findIndex((t=>t.title()===e));if(-1===t||this.currentPerspectiveIndex===t)return;const i=this.perspectives[t].masterGrid(this);if(!i)return;const s=i.once(ht.ContentShown),r=this.perspectiveSelect.options().find((e=>e.value===String(t)));this.perspectiveSelect.select(r),this.changePerspective(t),await s}async updateDataSourceAndView(){const e=this.dataGrid;if(!e||e.snapshot)return;const t=await this.profile.loadPromise;if(this.dataGrid!==e)return;if(e.snapshot!==t&&e.setDataSource(t,0),e!==this.diffDataGrid)return;this.baseProfile||(this.baseProfile=this.profiles()[this.baseSelect.selectedIndex()]);const i=await this.baseProfile.loadPromise;this.diffDataGrid.baseSnapshot!==i&&this.diffDataGrid.setBaseDataSource(i)}onSelectedPerspectiveChanged(e){this.changePerspective(Number(e.target.selectedOptions[0].value))}changePerspective(e){if(e===this.currentPerspectiveIndex)return;this.currentPerspectiveIndex=e,this.currentPerspective.deactivate(this);const t=this.perspectives[e];this.currentPerspective=t,this.dataGrid=t.masterGrid(this),t.activate(this),this.refreshVisibleData(),this.dataGrid&&this.dataGrid.updateWidths(),this.updateDataSourceAndView(),this.currentQuery&&this.searchResults&&this.performSearch(this.currentQuery,!1)}async selectLiveObject(e,t){if(await this.changePerspectiveAndWait(e),!this.dataGrid)return;const i=await this.dataGrid.revealObjectByHeapSnapshotId(t);i?i.select():n.Console.Console.instance().error("Cannot find corresponding heap snapshot node")}getPopoverRequest(e){const t=r.UIUtils.enclosingNodeOrSelfWithNodeName(e.target,"span"),i=r.UIUtils.enclosingNodeOrSelfWithNodeName(e.target,"tr");if(!i)return null;if(!this.dataGrid)return null;const s=this.dataGrid.dataGridNodeFromNode(i)||this.containmentDataGrid.dataGridNodeFromNode(i)||this.constructorsDataGrid.dataGridNodeFromNode(i)||this.diffDataGrid.dataGridNodeFromNode(i)||this.allocationDataGrid&&this.allocationDataGrid.dataGridNodeFromNode(i)||this.retainmentDataGrid.dataGridNodeFromNode(i),o=this.profile.heapProfilerModel();if(!s||!t||!o)return null;let n;return{box:t.boxInWindow(),show:async e=>{if(!o)return!1;const t=await s.queryObjectContent(o,"popover");return n=t instanceof a.RemoteObject.RemoteObject?await g.ObjectPopoverHelper.ObjectPopoverHelper.buildObjectPopover(t,e):g.ObjectPopoverHelper.ObjectPopoverHelper.buildDescriptionPopover(t.description,t.link,e),!!n||(o.runtimeModel().releaseObjectGroup("popover"),!1)},hide:()=>{o.runtimeModel().releaseObjectGroup("popover"),n&&n.dispose()}}}updatePerspectiveOptions(){const e=this.profiles().length>1;this.perspectiveSelect.removeOptions(),this.perspectives.forEach(((t,i)=>{if(e||t!==this.comparisonPerspective){const e=this.perspectiveSelect.createOption(t.title(),String(i));t===this.currentPerspective&&this.perspectiveSelect.select(e)}}))}updateBaseOptions(){const e=this.profiles(),t=this.baseSelect.selectedIndex();this.baseSelect.removeOptions();for(const t of e)this.baseSelect.createOption(t.title);t>-1&&this.baseSelect.setSelectedIndex(t)}updateFilterOptions(){const e=this.profiles(),t=this.filterSelect.selectedIndex(),i=this.filterSelect.size();this.filterSelect.removeOptions(),this.filterSelect.createOption(Dt(Nt.allObjects));for(let t=0;t<e.length;++t){let i;i=t?Dt(Nt.objectsAllocatedBetweenSAndS,{PH1:e[t-1].title,PH2:e[t].title}):Dt(Nt.objectsAllocatedBeforeS,{PH1:e[t].title}),this.filterSelect.createOption(i)}const s=this.filterSelect.size();this.filterSelect.createOption("—".repeat(18)).disabled=!0;for(const e of Ft.ALWAYS_AVAILABLE_FILTERS)this.filterSelect.createOption(e.uiName);const r=this.filterSelect.size();if(t>-1){const e=i-t;e<=Ft.ALWAYS_AVAILABLE_FILTERS.length?this.filterSelect.setSelectedIndex(r-e):t>=s?this.filterSelect.setSelectedIndex(-1):this.filterSelect.setSelectedIndex(t)}}updateControls(){this.updatePerspectiveOptions(),this.updateBaseOptions(),this.updateFilterOptions()}onReceiveSnapshot(e){this.updateControls();e.data.addEventListener("ProfileTitleChanged",this.updateControls,this)}onProfileHeaderRemoved(e){const t=e.data;t.removeEventListener("ProfileTitleChanged",this.updateControls,this),this.profile===t?(this.detach(),this.profile.profileType().removeEventListener("SnapshotReceived",this.onReceiveSnapshot,this),this.profile.profileType().removeEventListener("remove-profile-header",this.onProfileHeaderRemoved,this),this.dispose()):this.updateControls()}dispose(){this.linkifier.dispose(),this.popoverHelper.dispose(),this.allocationStackView&&(this.allocationStackView.clear(),this.allocationDataGrid&&this.allocationDataGrid.dispose()),this.onStopTracking(),this.trackingOverviewGrid&&this.trackingOverviewGrid.removeEventListener("IdsRangeChanged",this.onIdsRangeChanged.bind(this))}}class jt{titleInternal;constructor(e){this.titleInternal=e}activate(e){}deactivate(e){e.baseSelect.setVisible(!1),e.filterSelect.setVisible(!1),e.classNameFilter.setVisible(!1),e.trackingOverviewGrid&&e.trackingOverviewGrid.detach(),e.allocationWidget&&e.allocationWidget.detach(),e.statisticsView&&e.statisticsView.detach(),e.splitWidget.detach(),e.splitWidget.detachChildWidgets()}masterGrid(e){return null}title(){return this.titleInternal}supportsSearch(){return!1}}class Ht extends jt{constructor(){super(Dt(Nt.summary))}activate(e){e.splitWidget.setMainWidget(e.constructorsWidget),e.splitWidget.setSidebarWidget(e.objectDetailsView),e.splitWidget.show(e.searchableViewInternal.element),e.filterSelect.setVisible(!0),e.classNameFilter.setVisible(!0),e.trackingOverviewGrid&&(e.trackingOverviewGrid.show(e.searchableViewInternal.element,e.splitWidget.element),e.trackingOverviewGrid.update(),e.trackingOverviewGrid.updateGrid())}masterGrid(e){return e.constructorsDataGrid}supportsSearch(){return!0}}class Lt extends jt{constructor(){super(Dt(Nt.comparison))}activate(e){e.splitWidget.setMainWidget(e.diffWidget),e.splitWidget.setSidebarWidget(e.objectDetailsView),e.splitWidget.show(e.searchableViewInternal.element),e.baseSelect.setVisible(!0),e.classNameFilter.setVisible(!0)}masterGrid(e){return e.diffDataGrid}supportsSearch(){return!0}}class Ot extends jt{constructor(){super(Dt(Nt.containment))}activate(e){e.splitWidget.setMainWidget(e.containmentWidget),e.splitWidget.setSidebarWidget(e.objectDetailsView),e.splitWidget.show(e.searchableViewInternal.element)}masterGrid(e){return e.containmentDataGrid}}class zt extends jt{allocationSplitWidget;constructor(){super(Dt(Nt.allocation)),this.allocationSplitWidget=new r.SplitWidget.SplitWidget(!1,!0,"heap-snapshot-allocation-split-view-state",200,200),this.allocationSplitWidget.setSidebarWidget(new r.Widget.VBox)}activate(e){e.allocationWidget&&this.allocationSplitWidget.setMainWidget(e.allocationWidget),e.splitWidget.setMainWidget(e.constructorsWidget),e.splitWidget.setSidebarWidget(e.objectDetailsView);const t=new r.Widget.VBox,i=document.createElement("div");i.classList.add("heap-snapshot-view-resizer");const s=i.createChild("div","title").createChild("span");if(i.createChild("div","verticalResizerIcon"),s.textContent=Dt(Nt.liveObjects),this.allocationSplitWidget.hideDefaultResizer(),this.allocationSplitWidget.installResizer(i),t.element.appendChild(i),e.splitWidget.show(t.element),this.allocationSplitWidget.setSidebarWidget(t),this.allocationSplitWidget.show(e.searchableViewInternal.element),e.constructorsDataGrid.clear(),e.allocationDataGrid){const t=e.allocationDataGrid.selectedNode;t&&e.constructorsDataGrid.setAllocationNodeId(t.allocationNodeId())}}deactivate(e){this.allocationSplitWidget.detach(),super.deactivate(e)}masterGrid(e){return e.allocationDataGrid}}class Bt extends jt{constructor(){super(Dt(Nt.statistics))}activate(e){e.statisticsView.show(e.searchableViewInternal.element)}masterGrid(e){return null}}class At extends(n.ObjectWrapper.eventMixin(F)){exposeInternals;customContentInternal;constructor(e,t){super(e||At.TypeId,t||Dt(Nt.heapSnapshot)),a.TargetManager.TargetManager.instance().observeModels(a.HeapProfilerModel.HeapProfilerModel,this),a.TargetManager.TargetManager.instance().addModelListener(a.HeapProfilerModel.HeapProfilerModel,"ResetProfiles",this.resetProfiles,this),a.TargetManager.TargetManager.instance().addModelListener(a.HeapProfilerModel.HeapProfilerModel,"AddHeapSnapshotChunk",this.addHeapSnapshotChunk,this),a.TargetManager.TargetManager.instance().addModelListener(a.HeapProfilerModel.HeapProfilerModel,"ReportHeapSnapshotProgress",this.reportHeapSnapshotProgress,this),this.exposeInternals=n.Settings.Settings.instance().createSetting("expose-internals",!1),this.customContentInternal=null}modelAdded(e){e.enable()}modelRemoved(e){}getProfiles(){return super.getProfiles()}fileExtension(){return".heapsnapshot"}get buttonTooltip(){return Dt(Nt.takeHeapSnapshot)}isInstantProfile(){return!0}buttonClicked(){return this.takeHeapSnapshot(),o.userMetrics.actionTaken(o.UserMetrics.Action.ProfilesHeapProfileTaken),!1}get treeItemTitle(){return Dt(Nt.heapSnapshots)}get description(){return Dt(Nt.heapSnapshotProfilesShowMemory)}customContent(){const e=h.Runtime.experiments.isEnabled("show-option-tp-expose-internals-in-heap-snapshot"),t=r.SettingsUI.createSettingCheckbox(Dt(Nt.exposeInternals),this.exposeInternals,!0);return this.customContentInternal=t,e?t:null}setCustomContentEnabled(e){this.customContentInternal&&(this.customContentInternal.checkboxElement.disabled=!e)}createProfileLoadedFromFile(e){return new Vt(null,this,e)}async takeHeapSnapshot(){if(o.rnPerfMetrics.heapSnapshotStarted(),this.profileBeingRecorded())return void o.rnPerfMetrics.heapSnapshotFinished(!1);const e=r.Context.Context.instance().flavor(a.HeapProfilerModel.HeapProfilerModel);if(!e)return void o.rnPerfMetrics.heapSnapshotFinished(!1);let t=new Vt(e,this);this.setProfileBeingRecorded(t),this.addProfile(t),t.updateStatus(Dt(Nt.snapshotting));const i=await e.takeHeapSnapshot({reportProgress:!0,captureNumericValue:!0,exposeInternals:this.exposeInternals.get()});o.rnPerfMetrics.heapSnapshotFinished(!i),t=this.profileBeingRecorded(),t&&(t.title=Dt(Nt.snapshotD,{PH1:t.uid}),t.finishLoad(),this.setProfileBeingRecorded(null),this.dispatchEventToListeners("profile-complete",t))}addHeapSnapshotChunk(e){const t=this.profileBeingRecorded();t&&t.transferChunk(e.data)}reportHeapSnapshotProgress(e){const t=this.profileBeingRecorded();if(!t)return;const{done:i,total:s,finished:r}=e.data;t.updateStatus(Dt(Nt.percentagePlaceholder,{PH1:(i/s*100).toFixed(0)}),!0),r&&t.prepareToLoad()}resetProfiles(e){const t=e.data;for(const e of this.getProfiles())e.heapProfilerModel()===t&&this.removeProfile(e)}snapshotReceived(e){this.profileBeingRecorded()===e&&this.setProfileBeingRecorded(null),this.dispatchEventToListeners("SnapshotReceived",e)}static TypeId="HEAP";static SnapshotReceived="SnapshotReceived"}class Gt extends(n.ObjectWrapper.eventMixin(At)){recordAllocationStacksSettingInternal;customContentInternal;recording;profileSamples;constructor(){super(Gt.TypeId,Dt(Nt.allocationInstrumentationOn)),this.recordAllocationStacksSettingInternal=n.Settings.Settings.instance().createSetting("record-allocation-stacks",!1),this.customContentInternal=null,this.recording=!1}modelAdded(e){super.modelAdded(e),e.addEventListener("HeapStatsUpdate",this.heapStatsUpdate,this),e.addEventListener("LastSeenObjectId",this.lastSeenObjectId,this)}modelRemoved(e){super.modelRemoved(e),e.removeEventListener("HeapStatsUpdate",this.heapStatsUpdate,this),e.removeEventListener("LastSeenObjectId",this.lastSeenObjectId,this)}heapStatsUpdate(e){if(!this.profileSamples)return;const t=e.data;let i;for(let e=0;e<t.length;e+=3){i=t[e];const s=t[e+2];this.profileSamples.sizes[i]=s,this.profileSamples.max[i]||(this.profileSamples.max[i]=s)}}lastSeenObjectId(e){const t=this.profileSamples;if(!t)return;const{lastSeenObjectId:i,timestamp:s}=e.data,r=Math.max(t.ids.length,t.max.length-1);t.ids[r]=i,t.max[r]||(t.max[r]=0,t.sizes[r]=0),t.timestamps[r]=s,t.totalTime<s-t.timestamps[0]&&(t.totalTime*=2),this.profileSamples&&this.dispatchEventToListeners("HeapStatsUpdate",this.profileSamples);const o=this.profileBeingRecorded();o&&o.updateStatus(null,!0)}hasTemporaryView(){return!0}get buttonTooltip(){return this.recording?Dt(Nt.stopRecordingHeapProfile):Dt(Nt.startRecordingHeapProfile)}isInstantProfile(){return!1}buttonClicked(){return this.toggleRecording()}async startRecordingProfile(){if(o.rnPerfMetrics.heapProfilingStarted(),this.profileBeingRecorded())return void o.rnPerfMetrics.heapProfilingFinished(!1);const e=this.addNewProfile();if(!e)return void o.rnPerfMetrics.heapProfilingFinished(!1);await e.startTrackingHeapObjects(this.recordAllocationStacksSettingInternal.get())&&o.rnPerfMetrics.heapProfilingFinished(!1)}customContent(){const e=r.SettingsUI.createSettingCheckbox(Dt(Nt.recordAllocationStacksExtra),this.recordAllocationStacksSettingInternal,!0);return this.customContentInternal=e,e}setCustomContentEnabled(e){this.customContentInternal&&(this.customContentInternal.checkboxElement.disabled=!e)}recordAllocationStacksSetting(){return this.recordAllocationStacksSettingInternal}addNewProfile(){const e=r.Context.Context.instance().flavor(a.HeapProfilerModel.HeapProfilerModel);return e?(this.setProfileBeingRecorded(new Vt(e,this,void 0)),this.profileSamples=new ce,this.profileBeingRecorded()._profileSamples=this.profileSamples,this.recording=!0,this.addProfile(this.profileBeingRecorded()),this.profileBeingRecorded().updateStatus(Dt(Nt.recording)),this.dispatchEventToListeners("TrackingStarted"),e):null}async stopRecordingProfile(){let e=this.profileBeingRecorded();e.updateStatus(Dt(Nt.snapshotting));const t=e.heapProfilerModel().stopTrackingHeapObjects(!0);this.recording=!1,this.dispatchEventToListeners("TrackingStopped");const i=await t;o.rnPerfMetrics.heapProfilingFinished(!i),e=this.profileBeingRecorded(),e&&(e.finishLoad(),this.profileSamples=null,this.setProfileBeingRecorded(null),this.dispatchEventToListeners("profile-complete",e))}toggleRecording(){return this.recording?this.stopRecordingProfile():this.startRecordingProfile(),this.recording}fileExtension(){return".heaptimeline"}get treeItemTitle(){return Dt(Nt.allocationTimelines)}get description(){return Dt(Nt.AllocationTimelinesShowInstrumented)}resetProfiles(e){const t=this.recording;this.setProfileBeingRecorded(null),super.resetProfiles(e),this.profileSamples=null,t&&this.addNewProfile()}profileBeingRecordedRemoved(){this.stopRecordingProfile(),this.profileSamples=null}static TypeId="HEAP-RECORD";static HeapStatsUpdate="HeapStatsUpdate";static TrackingStarted="TrackingStarted";static TrackingStopped="TrackingStopped"}class Vt extends E{heapProfilerModelInternal;maxJSObjectId;workerProxy;receiver;snapshotProxy;loadPromise;fulfillLoad;totalNumberOfChunks;bufferedWriter;onTempFileReady;failedToCreateTempFile;wasDisposed;fileName;constructor(e,t,i){super(t,i||Dt(Nt.snapshotD,{PH1:t.nextProfileUid()})),this.heapProfilerModelInternal=e,this.maxJSObjectId=-1,this.workerProxy=null,this.receiver=null,this.snapshotProxy=null,this.loadPromise=new Promise((e=>{this.fulfillLoad=e})),this.totalNumberOfChunks=0,this.bufferedWriter=null,this.onTempFileReady=null}heapProfilerModel(){return this.heapProfilerModelInternal}async getLocation(e){return this.snapshotProxy?this.snapshotProxy.getLocation(e):null}createSidebarTreeElement(e){return new K(e,this,"heap-snapshot-sidebar-tree-item")}createView(e){return new Ft(e,this)}prepareToLoad(){console.assert(!this.receiver,"Already loading"),this.setupWorker(),this.updateStatus(Dt(Nt.loading),!0)}finishLoad(){!this.wasDisposed&&this.receiver&&this.receiver.close(),this.bufferedWriter&&this.didWriteToTempFile(this.bufferedWriter)}didWriteToTempFile(e){this.wasDisposed?e&&e.remove():(this.tempFile=e,e||(this.failedToCreateTempFile=!0),this.onTempFileReady&&(this.onTempFileReady(),this.onTempFileReady=null))}setupWorker(){console.assert(!this.workerProxy,"HeapSnapshotWorkerProxy already exists"),this.workerProxy=new yt(this.handleWorkerEvent.bind(this)),this.workerProxy.addEventListener("Wait",(e=>{this.updateStatus(null,e.data)}),this),this.receiver=this.workerProxy.createLoader(this.uid,this.snapshotReceived.bind(this))}handleWorkerEvent(e,i){if(f.HeapSnapshotModel.HeapSnapshotProgressEvent.BrokenSnapshot===e){const e=i;return void n.Console.Console.instance().error(e)}if(f.HeapSnapshotModel.HeapSnapshotProgressEvent.Update!==e)return;const s=i,r=t.i18n.deserializeUIString(s);this.updateStatus(Mt(r.string,r.values))}dispose(){this.workerProxy&&this.workerProxy.dispose(),this.removeTempFile(),this.wasDisposed=!0}didCompleteSnapshotTransfer(){this.snapshotProxy&&this.updateStatus(e.NumberUtilities.bytesToString(this.snapshotProxy.totalSize),!1)}transferChunk(e){this.bufferedWriter||(this.bufferedWriter=new m.TempFile.TempFile),this.bufferedWriter.write([e]),++this.totalNumberOfChunks,this.receiver&&this.receiver.write(e)}snapshotReceived(e){this.wasDisposed||(this.receiver=null,this.snapshotProxy=e,this.maxJSObjectId=e.maxJSObjectId(),this.didCompleteSnapshotTransfer(),this.workerProxy&&this.workerProxy.startCheckingForLongRunningCalls(),this.notifySnapshotReceived())}notifySnapshotReceived(){this.snapshotProxy&&this.fulfillLoad&&this.fulfillLoad(this.snapshotProxy),this.profileType().snapshotReceived(this)}canSaveToFile(){return!this.fromFile()}async saveToFile(){await this.loadPromise;const t=new m.FileUtils.FileOutputStream;this.fileName=this.fileName||"Heap-"+e.DateUtilities.toISO8601Compact(new Date)+this.profileType().fileExtension();const i=async e=>{if(e){if(this.failedToCreateTempFile)return n.Console.Console.instance().error("Failed to open temp file with heap snapshot"),void t.close();if(this.tempFile){const e=await this.tempFile.copyToOutputStream(t,this.onChunkTransferred.bind(this));return e&&n.Console.Console.instance().error("Failed to read heap snapshot from temp file: "+e.message),void this.didCompleteSnapshotTransfer()}this.onTempFileReady=()=>{i(e)},this.updateSaveProgress(0,1)}};await t.open(this.fileName).then(i.bind(this))}onChunkTransferred(e){this.updateSaveProgress(e.loadedSize(),e.fileSize())}updateSaveProgress(e,t){const i=(100*(t&&e/t)).toFixed(0);this.updateStatus(Dt(Nt.savingD,{PH1:i}))}async loadFromFile(e){this.updateStatus(Dt(Nt.loading),!0),this.setupWorker();const t=new m.FileUtils.ChunkedFileReader(e,1e7),i=await t.read(this.receiver);if(!i){const e=t.error();e&&this.updateStatus(e.message)}return i?null:t.error()}profileType(){return super.profileType()}}class Wt extends r.Widget.VBox{pieChart;constructor(){super(),this.element.classList.add("heap-snapshot-statistics-view"),this.element.setAttribute("jslog",`${l.pane("profiler.heap-snapshot-statistics-view").track({resize:!0})}`),this.pieChart=new p.PieChart.PieChart,this.setTotalAndRecords(0,[]),this.pieChart.classList.add("heap-snapshot-stats-pie-chart"),this.element.appendChild(this.pieChart)}static valueFormatter(e){return new Intl.NumberFormat(t.DevToolsLocale.DevToolsLocale.instance().locale,{style:"unit",unit:"kilobyte"}).format(Math.round(e/1e3))}setTotalAndRecords(e,t){this.pieChart.data={chartName:Dt(Nt.heapMemoryUsage),size:150,formatter:Wt.valueFormatter,showLegend:!0,total:e,slices:t}}}class Ut extends r.Widget.Widget{heapProfilerModel;linkifier;frameElements;constructor(e){super(),this.heapProfilerModel=e,this.linkifier=new u.Linkifier.Linkifier,this.frameElements=[]}onContextMenu(e,t){const i=new r.ContextMenu.ContextMenu(t);i.appendApplicableItems(e),i.show(),t.consume(!0)}onStackViewKeydown(e){const t=e.target;if(!t)return;if("Enter"===e.key){const i=_t.get(t);if(!i)return;const s=u.Linkifier.Linkifier.linkInfo(i);if(!s)return;return void(u.Linkifier.Linkifier.invokeFirstAction(s)&&e.consume(!0))}let i;const s=e;if("ArrowUp"===s.key)i=!1;else{if("ArrowDown"!==s.key)return;i=!0}const r=this.frameElements.indexOf(t);if(-1===r)return;const o=i?r+1:r-1;if(o<0||o>=this.frameElements.length)return;const n=this.frameElements[o];n.tabIndex=0,t.tabIndex=-1,n.focus(),e.consume(!0)}async setAllocatedObject(e,t){this.clear();const i=await e.allocationStack(t);if(!i){const e=this.element.createChild("div","no-heap-allocation-stack");return void r.UIUtils.createTextChild(e,Dt(Nt.stackWasNotRecordedForThisObject))}const s=this.element.createChild("div","heap-allocation-stack");s.addEventListener("keydown",this.onStackViewKeydown.bind(this),!1);for(const e of i){const t=s.createChild("div","stack-frame");this.frameElements.push(t),t.tabIndex=-1;if(t.createChild("div").textContent=r.UIUtils.beautifyFunctionName(e.functionName),!e.scriptId)continue;const i=this.heapProfilerModel?this.heapProfilerModel.target():null,o={columnNumber:e.column-1,inlineFrameIndex:0},n=this.linkifier.linkifyScriptLocation(i,String(e.scriptId),e.scriptName,e.line-1,o);t.appendChild(n),_t.set(t,n),t.addEventListener("contextmenu",this.onContextMenu.bind(this,n))}this.frameElements[0].tabIndex=0}clear(){this.element.removeChildren(),this.frameElements=[],this.linkifier.reset()}}const _t=new WeakMap;var $t=Object.freeze({__proto__:null,HeapSnapshotView:Ft,Perspective:jt,SummaryPerspective:Ht,ComparisonPerspective:Lt,ContainmentPerspective:Ot,AllocationPerspective:zt,StatisticsPerspective:Bt,HeapSnapshotProfileType:At,TrackingHeapSnapshotProfileType:Gt,HeapProfileHeader:Vt,HeapSnapshotStatisticsView:Wt,HeapAllocationStackView:Ut});class Jt{heapSnapshotProfileType;samplingHeapProfileType;trackingHeapSnapshotProfileType;constructor(){this.heapSnapshotProfileType=new At,this.samplingHeapProfileType=new Oe,this.trackingHeapSnapshotProfileType=new Gt}}const qt=new Jt;var Qt=Object.freeze({__proto__:null,ProfileTypeRegistry:Jt,instance:qt});const Kt={revealInSummaryView:"Reveal in Summary view"},Yt=t.i18n.registerUIStrings("panels/profiler/HeapProfilerPanel.ts",Kt),Zt=t.i18n.getLocalizedString.bind(void 0,Yt);let Xt;class ei extends se{constructor(){const e=qt;super("heap-profiler",[e.heapSnapshotProfileType,e.trackingHeapSnapshotProfileType,e.samplingHeapProfileType],"profiler.heap-toggle-recording")}static instance(){return Xt||(Xt=new ei),Xt}appendApplicableItems(e,t,i){if(!this.isShowing())return;if(!i.objectId)return;const s=i.objectId;if(!qt.heapSnapshotProfileType.getProfiles().length)return;const r=i.runtimeModel().heapProfilerModel();r&&t.revealSection().appendItem(Zt(Kt.revealInSummaryView),function(e){r.snapshotObjectIdForObjectId(s).then((t=>{this.isShowing()&&t&&this.showObject(t,e)}))}.bind(this,"Summary"),{jslogContext:"reveal-in-summary"})}handleAction(e,t){const i=r.Context.Context.instance().flavor(ei);return console.assert(Boolean(i)&&i instanceof ei),i&&i.toggleRecord(),!0}wasShown(){super.wasShown(),r.Context.Context.instance().setFlavor(ei,this),o.userMetrics.panelLoaded("heap-profiler","DevTools.Launch.HeapProfiler")}willHide(){r.Context.Context.instance().setFlavor(ei,null),super.willHide()}showObject(e,t){const i=qt.heapSnapshotProfileType.getProfiles();for(let s=0;s<i.length;s++){const r=i[s];if(r.maxJSObjectId>=parseInt(e,10)){this.showProfile(r);this.viewForProfile(r).selectLiveObject(t,e);break}}}}var ti=Object.freeze({__proto__:null,HeapProfilerPanel:ei});const ii=new CSSStyleSheet;ii.replaceSync(".data-grid{border:none}.data-grid td .size-units{margin-left:4px;font-size:75%}.data-grid tr:not(.selected) td .size-units{color:var(--sys-color-token-subtle)}.toolbar{border-bottom:1px solid var(--sys-color-divider)}\n/*# sourceURL=liveHeapProfile.css */\n");const si={jsHeap:"JS Heap",allocatedJsHeapSizeCurrentlyIn:"Allocated JS heap size currently in use",vms:"VMs",numberOfVmsSharingTheSameScript:"Number of VMs sharing the same script source",scriptUrl:"Script URL",urlOfTheScriptSource:"URL of the script source",heapProfile:"Heap Profile",anonymousScriptS:"(Anonymous Script {PH1})",kb:"kB"},ri=t.i18n.registerUIStrings("panels/profiler/LiveHeapProfileView.ts",si),oi=t.i18n.getLocalizedString.bind(void 0,ri);let ni;class ai extends r.Widget.VBox{gridNodeByUrl;setting;toggleRecordAction;toggleRecordButton;startWithReloadButton;dataGrid;currentPollId;constructor(){super(!0),this.gridNodeByUrl=new Map,this.setting=n.Settings.Settings.instance().moduleSetting("memory-live-heap-profile");const e=new r.Toolbar.Toolbar("live-heap-profile-toolbar",this.contentElement);this.toggleRecordAction=r.ActionRegistry.ActionRegistry.instance().getAction("live-heap-profile.toggle-recording"),this.toggleRecordButton=r.Toolbar.Toolbar.createActionButton(this.toggleRecordAction),this.toggleRecordButton.setToggled(this.setting.get()),e.appendToolbarItem(this.toggleRecordButton);const t=a.TargetManager.TargetManager.instance().primaryPageTarget();if(t&&t.model(a.ResourceTreeModel.ResourceTreeModel)){const t=r.ActionRegistry.ActionRegistry.instance().getAction("live-heap-profile.start-with-reload");this.startWithReloadButton=r.Toolbar.Toolbar.createActionButton(t),e.appendToolbarItem(this.startWithReloadButton)}this.dataGrid=this.createDataGrid(),this.dataGrid.asWidget().show(this.contentElement),this.currentPollId=0}static instance(){return ni||(ni=new ai),ni}createDataGrid(){const e={id:"",title:n.UIString.LocalizedEmptyString,width:void 0,fixedWidth:!0,sortable:!0,align:"right",sort:s.DataGrid.Order.Descending,titleDOMFragment:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,weight:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0},t=[{...e,id:"size",title:oi(si.jsHeap),width:"72px",fixedWidth:!0,sortable:!0,align:"right",sort:s.DataGrid.Order.Descending,tooltip:oi(si.allocatedJsHeapSizeCurrentlyIn)},{...e,id:"isolates",title:oi(si.vms),width:"40px",fixedWidth:!0,align:"right",tooltip:oi(si.numberOfVmsSharingTheSameScript)},{...e,id:"url",title:oi(si.scriptUrl),fixedWidth:!1,sortable:!0,tooltip:oi(si.urlOfTheScriptSource)}],i=new s.SortableDataGrid.SortableDataGrid({displayName:oi(si.heapProfile),columns:t,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0});i.setResizeMethod("last"),i.element.classList.add("flex-auto"),i.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),i.addEventListener("OpenedNode",this.revealSourceForSelectedNode,this),i.addEventListener("SortingChanged",this.sortingChanged,this);for(const e of t){const t=i.headerTableHeader(e.id);t&&t.setAttribute("title",e.tooltip)}return i}wasShown(){super.wasShown(),this.poll(),this.registerCSSFiles([ii]),this.setting.addChangeListener(this.settingChanged,this)}willHide(){++this.currentPollId,this.setting.removeChangeListener(this.settingChanged,this)}settingChanged(e){this.toggleRecordButton.setToggled(e.data)}async poll(){const e=this.currentPollId;do{const t=Array.from(a.IsolateManager.IsolateManager.instance().isolates()),i=await Promise.all(t.map((e=>{const t=e.heapProfilerModel();return t?t.getSamplingProfile():null})));if(this.currentPollId!==e)return;this.update(t,i),await new Promise((e=>window.setTimeout(e,3e3)))}while(this.currentPollId===e)}update(e,t){const i=new Map;t.forEach(((t,i)=>{t&&o(e[i],"",t.head)}));const s=this.dataGrid.rootNode(),r=new Set;for(const e of i){const t=e[0],i=e[1].size,o=e[1].isolates.size;if(!t){console.info(`Node with empty URL: ${i} bytes`);continue}let n=this.gridNodeByUrl.get(t);n?n.updateNode(i,o):(n=new li(t,i,o),this.gridNodeByUrl.set(t,n),s.appendChild(n)),r.add(n)}for(const e of s.children.slice()){const t=e;r.has(t)||t.remove(),this.gridNodeByUrl.delete(t.url)}function o(e,t,s){const r=s.callFrame.url||t||function(e){const t=e.callFrame.functionName;return t.startsWith("(")&&"(root)"!==t?t:""}(s)||function(e){return Number(e.callFrame.scriptId)?oi(si.anonymousScriptS,{PH1:e.callFrame.scriptId}):""}(s);if(s.children.forEach(o.bind(null,e,r)),!s.selfSize)return;let n=i.get(r);n||(n={size:0,isolates:new Set},i.set(r,n)),n.size+=s.selfSize,n.isolates.add(e)}this.sortingChanged()}onKeyDown(e){"Enter"===e.key&&(e.consume(!0),this.revealSourceForSelectedNode())}revealSourceForSelectedNode(){const e=this.dataGrid.selectedNode;if(!e||!e.url)return;const t=v.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e.url);t&&n.Revealer.reveal(t)}sortingChanged(){const e=this.dataGrid.sortColumnId();if(!e)return;const t="url"===e?function(e,t){return t.url.localeCompare(e.url)}:function(e,t){return t.size-e.size};this.dataGrid.sortNodes(t,this.dataGrid.isSortOrderAscending())}toggleRecording(){!this.setting.get()?this.startRecording(!1):this.stopRecording()}startRecording(e){if(this.setting.set(!0),!e)return;const t=a.TargetManager.TargetManager.instance().primaryPageTarget();if(!t)return;const i=t.model(a.ResourceTreeModel.ResourceTreeModel);i&&i.reloadPage()}async stopRecording(){this.setting.set(!1)}}class li extends s.SortableDataGrid.SortableDataGridNode{url;size;isolateCount;constructor(e,t,i){super(),this.url=e,this.size=t,this.isolateCount=i}updateNode(e,t){this.size===e&&this.isolateCount===t||(this.size=e,this.isolateCount=t,this.refresh())}createCell(t){const i=this.createTD(t);switch(t){case"url":i.textContent=this.url;break;case"size":i.textContent=e.NumberUtilities.withThousandsSeparator(Math.round(this.size/1e3)),i.createChild("span","size-units").textContent=oi(si.kb);break;case"isolates":i.textContent=`${this.isolateCount}`}return i}}var di=Object.freeze({__proto__:null,LiveHeapProfileView:ai,GridNode:li,ActionDelegate:class{handleAction(e,t){return(async()=>{const e="live-heap-profile";await r.ViewManager.ViewManager.instance().showView(e);const i=r.ViewManager.ViewManager.instance().view(e);if(i){const e=await i.widget();this.innerHandleAction(e,t)}})(),!0}innerHandleAction(e,t){switch(t){case"live-heap-profile.toggle-recording":e.toggleRecording();break;case"live-heap-profile.start-with-reload":e.startRecording(!0);break;default:console.assert(!1,`Unknown action: ${t}`)}}}});export{R as BottomUpProfileDataGrid,N as ChildrenProvider,We as HeapProfileView,ti as HeapProfilerPanel,St as HeapSnapshotDataGrids,st as HeapSnapshotGridNodes,Rt as HeapSnapshotProxy,$t as HeapSnapshotView,ue as HeapTimelineOverview,A as IsolateSelector,di as LiveHeapProfileView,T as ProfileDataGrid,we as ProfileFlameChart,j as ProfileHeader,$ as ProfileLauncherView,Y as ProfileSidebarTreeElement,Qt as ProfileTypeRegistry,ke as ProfileView,le as ProfilesPanel,ye as TopDownProfileDataGrid};
