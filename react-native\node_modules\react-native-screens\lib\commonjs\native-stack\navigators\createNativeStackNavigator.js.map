{"version": 3, "names": ["_native", "require", "React", "_interopRequireWildcard", "_NativeStackView", "_interopRequireDefault", "obj", "__esModule", "default", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "target", "arguments", "length", "source", "key", "apply", "NativeStackNavigator", "_ref", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "createElement", "_default", "exports", "createNavigatorFactory"], "sourceRoot": "../../../../src", "sources": ["native-stack/navigators/createNativeStackNavigator.tsx"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAWA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAMA,IAAAG,gBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAuD,SAAAI,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAH,UAAA,SAAAG,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAF,OAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAJ,CAAA,UAAAG,CAAA,CAAAE,GAAA,CAAAL,CAAA,OAAAM,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAZ,CAAA,oBAAAY,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAY,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAX,CAAA,EAAAY,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAZ,CAAA,CAAAY,CAAA,YAAAN,CAAA,CAAAR,OAAA,GAAAE,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAc,GAAA,CAAAjB,CAAA,EAAAM,CAAA,GAAAA,CAAA;AAAA,SAAAY,SAAA,IAAAA,QAAA,GAAAT,MAAA,CAAAU,MAAA,GAAAV,MAAA,CAAAU,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,MAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,GAAA,IAAAD,MAAA,QAAAf,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAS,MAAA,EAAAC,GAAA,KAAAJ,MAAA,CAAAI,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAJ,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAJ,SAAA;AAEvD,SAASK,oBAAoBA,CAAAC,IAAA,EAKC;EAAA,IALA;IAC5BC,gBAAgB;IAChBC,QAAQ;IACRC,aAAa;IACb,GAAGC;EACsB,CAAC,GAAAJ,IAAA;EAC1B,MAAM;IAAEK,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAG,IAAAC,4BAAoB,EAM7DC,mBAAW,EAAE;IACbR,gBAAgB;IAChBC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;EACA;EACAvC,KAAK,CAAC8C,SAAS,CAAC,MAAM;IACpB;IACA,IAAIH,UAAU,EAAEI,oBAAoB,KAAKC,SAAS,EAAE;MAClDC,OAAO,CAACC,IAAI,CACV,2LACF,CAAC;IACH;EACF,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhB3C,KAAK,CAAC8C,SAAS,CACb;EACE;EACCH,UAAU,EAA+CQ,WAAW,GACnE,UAAU,EACT3C,CAAM,IAAK;IACV,MAAM4C,SAAS,GAAGT,UAAU,CAACS,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACEZ,KAAK,CAACa,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAE5C,CAAC,CAAgC+C,gBAAgB,EACnD;QACA;QACA;QACAZ,UAAU,CAACa,QAAQ,CAAC;UAClB,GAAGC,oBAAY,CAACC,QAAQ,CAAC,CAAC;UAC1B7B,MAAM,EAAEY,KAAK,CAACR;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CACF,CAAC,EACH,CAACU,UAAU,EAAEF,KAAK,CAACa,KAAK,EAAEb,KAAK,CAACR,GAAG,CACrC,CAAC;EAED,oBACEjC,KAAA,CAAA2D,aAAA,CAACzD,gBAAA,CAAAI,OAAe,EAAAoB,QAAA,KACVc,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN;;AAEA;AACA;AACA;AAFA,IAAAkB,QAAA,GAAAC,OAAA,CAAAvD,OAAA,GAGe,IAAAwD,8BAAsB,EAKnC3B,oBAAoB,CAAC"}