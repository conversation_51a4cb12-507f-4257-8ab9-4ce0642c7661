{"name": "expo-barcode-scanner", "version": "14.0.1", "description": "Allows scanning variety of supported barcodes both as standalone module and as extension for expo-camera. It also allows scanning barcodes from existing images.", "main": "build/BarCodeScanner.js", "types": "build/BarCodeScanner.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["barcode", "barcode-scanner", "react-native", "expo"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-barcode-scanner"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/bar-code-scanner/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"expo-image-loader": "~5.0.0"}, "devDependencies": {"@testing-library/react-native": "^12.5.1", "expo-module-scripts": "^4.0.0"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "f7adac0a6b82ab484a8254a68c3808ba6f2afde5"}