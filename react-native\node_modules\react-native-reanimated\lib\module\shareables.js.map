{"version": 3, "names": ["NativeReanimatedModule", "isWorkletFunction", "shouldBeUseWeb", "ReanimatedError", "registerWorkletStackDetails", "jsVersion", "shareableMappingCache", "shareableMappingFlag", "logger", "SHOULD_BE_USE_WEB", "MAGIC_KEY", "isHostObject", "value", "isPlainJSObject", "object", "Object", "getPrototypeOf", "prototype", "INACCESSIBLE_OBJECT", "__init", "Proxy", "get", "_", "prop", "String", "set", "VALID_ARRAY_VIEWS_NAMES", "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD", "processedObjectAtThresholdDepth", "makeShareableCloneRecursive", "shouldPersistRemote", "depth", "undefined", "type", "isTypeObject", "isTypeFunction", "cached", "toAdapt", "Array", "isArray", "map", "element", "freezeObjectIfDev", "__workletContextObjectFactory", "workletContextObjectFactory", "handle", "__DEV__", "babelVersion", "__initData", "version", "getWorkletCode", "__workletHash", "__stackDetails", "key", "entries", "RegExp", "pattern", "source", "flags", "Error", "name", "message", "stack", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "typeName", "constructor", "includes", "global", "inaccessibleObject", "adapted", "makeShareableClone", "WORKLET_CODE_THRESHOLD", "code", "length", "substring", "isRemoteFunction", "__remoteFunction", "for<PERSON>ach", "descriptor", "getOwnPropertyDescriptor", "configurable", "defineProperty", "warn", "preventExtensions", "makeShareableCloneOnUIRecursive", "cloneRecursive", "_makeShareableClone", "makeShareableJS", "makeShareableNative", "makeShareable"], "sourceRoot": "../../src", "sources": ["shareables.ts"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,iBAAiB,QAAQ,kBAAe;AAMjD,SAASC,cAAc,QAAQ,sBAAmB;AAClD,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,aAAU;AACvE,SAASC,SAAS,QAAQ,kCAA+B;AACzD,SACEC,qBAAqB,EACrBC,oBAAoB,QACf,4BAAyB;AAChC,SAASC,MAAM,QAAQ,mBAAU;;AAEjC;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGP,cAAc,CAAC,CAAC;AAE1C,MAAMQ,SAAS,GAAG,sBAAsB;AAExC,SAASC,YAAYA,CAACC,KAA0B,EAAE;EAChD,SAAS;;EACT;EACA;EACA;EACA;EACA,OAAOF,SAAS,IAAIE,KAAK;AAC3B;AAEA,SAASC,eAAeA,CAACC,MAAc,EAAE;EACvC,OAAOC,MAAM,CAACC,cAAc,CAACF,MAAM,CAAC,KAAKC,MAAM,CAACE,SAAS;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,MAAM,EAAEA,CAAA,KAAM;IACZ,SAAS;;IACT,OAAO,IAAIC,KAAK,CACd,CAAC,CAAC,EACF;MACEC,GAAG,EAAEA,CAACC,CAAU,EAAEC,IAAqB,KAAK;QAC1C,IACEA,IAAI,KAAK,0BAA0B,IACnCA,IAAI,KAAK,kBAAkB,EAC3B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,OAAO,KAAK;QACd;QACA,MAAM,IAAIpB,eAAe,CACvB,+BAA+BqB,MAAM,CACnCD,IACF,CAAC,yDACH,CAAC;MACH,CAAC;MACDE,GAAG,EAAEA,CAAA,KAAM;QACT,MAAM,IAAItB,eAAe,CACvB,sEACF,CAAC;MACH;IACF,CACF,CAAC;EACH;AACF,CAAC;AAED,MAAMuB,uBAAuB,GAAG,CAC9B,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,UAAU,CACX;AAED,MAAMC,oCAAoC,GAAG,EAAE;AAC/C;AACA;AACA,IAAIC,+BAAwC;AAE5C,OAAO,SAASC,2BAA2BA,CACzCjB,KAAU,EACVkB,mBAAmB,GAAG,KAAK,EAC3BC,KAAK,GAAG,CAAC,EACQ;EACjB,IAAItB,iBAAiB,EAAE;IACrB,OAAOG,KAAK;EACd;EACA,IAAImB,KAAK,IAAIJ,oCAAoC,EAAE;IACjD;IACA;IACA;IACA;IACA;IACA,IAAII,KAAK,KAAKJ,oCAAoC,EAAE;MAClDC,+BAA+B,GAAGhB,KAAK;IACzC,CAAC,MAAM,IAAIA,KAAK,KAAKgB,+BAA+B,EAAE;MACpD,MAAM,IAAIzB,eAAe,CACvB,0EACF,CAAC;IACH;EACF,CAAC,MAAM;IACLyB,+BAA+B,GAAGI,SAAS;EAC7C;EACA;EACA,MAAMC,IAAI,GAAG,OAAOrB,KAAK;EACzB,MAAMsB,YAAY,GAAGD,IAAI,KAAK,QAAQ;EACtC,MAAME,cAAc,GAAGF,IAAI,KAAK,UAAU;EAC1C,IAAI,CAACC,YAAY,IAAIC,cAAc,KAAKvB,KAAK,KAAK,IAAI,EAAE;IACtD,MAAMwB,MAAM,GAAG9B,qBAAqB,CAACe,GAAG,CAACT,KAAK,CAAC;IAC/C,IAAIwB,MAAM,KAAK7B,oBAAoB,EAAE;MACnC,OAAOK,KAAK;IACd,CAAC,MAAM,IAAIwB,MAAM,KAAKJ,SAAS,EAAE;MAC/B,OAAOI,MAAM;IACf,CAAC,MAAM;MACL,IAAIC,OAAY;MAChB,IAAIC,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;QACxByB,OAAO,GAAGzB,KAAK,CAAC4B,GAAG,CAAEC,OAAO,IAC1BZ,2BAA2B,CAACY,OAAO,EAAEX,mBAAmB,EAAEC,KAAK,GAAG,CAAC,CACrE,CAAC;QACDW,iBAAiB,CAAC9B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIuB,cAAc,IAAI,CAAClC,iBAAiB,CAACW,KAAK,CAAC,EAAE;QACtD;QACAyB,OAAO,GAAGzB,KAAK;QACf8B,iBAAiB,CAAC9B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE;QAC9B;QACA;QACA;QACAyB,OAAO,GAAGzB,KAAK;MACjB,CAAC,MAAM,IACLC,eAAe,CAACD,KAAK,CAAC,IACtBA,KAAK,CAAC+B,6BAA6B,EACnC;QACA,MAAMC,2BAA2B,GAAGhC,KAAK,CAAC+B,6BAA6B;QACvE,MAAME,MAAM,GAAGhB,2BAA2B,CAAC;UACzCV,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,OAAOyB,2BAA2B,CAAC,CAAC;UACtC;QACF,CAAC,CAAC;QACFtC,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAEiC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIhC,eAAe,CAACD,KAAK,CAAC,IAAIuB,cAAc,EAAE;QACnDE,OAAO,GAAG,CAAC,CAAC;QACZ,IAAIpC,iBAAiB,CAACW,KAAK,CAAC,EAAE;UAC5B,IAAIkC,OAAO,EAAE;YACX,MAAMC,YAAY,GAAGnC,KAAK,CAACoC,UAAU,CAACC,OAAO;YAC7C,IAAIF,YAAY,KAAKf,SAAS,IAAIe,YAAY,KAAK1C,SAAS,EAAE;cAC5D,MAAM,IAAIF,eAAe,CAAC,iFAAiFE,SAAS,QAAQ0C,YAAY;AACtJ;AACA,wBAAwBG,cAAc,CAACtC,KAAK,CAAC,IAAI,CAAC;YACtC;YACAR,2BAA2B,CACzBQ,KAAK,CAACuC,aAAa,EACnBvC,KAAK,CAACwC,cACR,CAAC;UACH;UACA,IAAIxC,KAAK,CAACwC,cAAc,EAAE;YACxB;YACA;YACA;YACA;YACA,OAAOxC,KAAK,CAACwC,cAAc;UAC7B;UACA;UACA;UACA;UACA;UACA;UACAf,OAAO,CAACW,UAAU,GAAGnB,2BAA2B,CAC9CjB,KAAK,CAACoC,UAAU,EAChB,IAAI,EACJjB,KAAK,GAAG,CACV,CAAC;QACH;QAEA,KAAK,MAAM,CAACsB,GAAG,EAAEZ,OAAO,CAAC,IAAI1B,MAAM,CAACuC,OAAO,CAAC1C,KAAK,CAAC,EAAE;UAClD,IAAIyC,GAAG,KAAK,YAAY,IAAIhB,OAAO,CAACW,UAAU,KAAKhB,SAAS,EAAE;YAC5D;UACF;UACAK,OAAO,CAACgB,GAAG,CAAC,GAAGxB,2BAA2B,CACxCY,OAAO,EACPX,mBAAmB,EACnBC,KAAK,GAAG,CACV,CAAC;QACH;QACAW,iBAAiB,CAAC9B,KAAK,CAAC;MAC1B,CAAC,MAAM,IAAIA,KAAK,YAAY2C,MAAM,EAAE;QAClC,MAAMC,OAAO,GAAG5C,KAAK,CAAC6C,MAAM;QAC5B,MAAMC,KAAK,GAAG9C,KAAK,CAAC8C,KAAK;QACzB,MAAMb,MAAM,GAAGhB,2BAA2B,CAAC;UACzCV,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,OAAO,IAAIoC,MAAM,CAACC,OAAO,EAAEE,KAAK,CAAC;UACnC;QACF,CAAC,CAAC;QACFpD,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAEiC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIjC,KAAK,YAAY+C,KAAK,EAAE;QACjC,MAAM;UAAEC,IAAI;UAAEC,OAAO;UAAEC;QAAM,CAAC,GAAGlD,KAAK;QACtC,MAAMiC,MAAM,GAAGhB,2BAA2B,CAAC;UACzCV,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT;YACA,MAAM4C,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;YACzBI,KAAK,CAACH,IAAI,GAAGA,IAAI;YACjBG,KAAK,CAACF,OAAO,GAAGA,OAAO;YACvBE,KAAK,CAACD,KAAK,GAAGA,KAAK;YACnB,OAAOC,KAAK;UACd;QACF,CAAC,CAAC;QACFzD,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAEiC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM,IAAIjC,KAAK,YAAYoD,WAAW,EAAE;QACvC3B,OAAO,GAAGzB,KAAK;MACjB,CAAC,MAAM,IAAIoD,WAAW,CAACC,MAAM,CAACrD,KAAK,CAAC,EAAE;QACpC;QACA,MAAMsD,MAAM,GAAGtD,KAAK,CAACsD,MAAM;QAC3B,MAAMC,QAAQ,GAAGvD,KAAK,CAACwD,WAAW,CAACR,IAAI;QACvC,MAAMf,MAAM,GAAGhB,2BAA2B,CAAC;UACzCV,MAAM,EAAEA,CAAA,KAAM;YACZ,SAAS;;YACT,IAAI,CAACO,uBAAuB,CAAC2C,QAAQ,CAACF,QAAQ,CAAC,EAAE;cAC/C,MAAM,IAAIhE,eAAe,CACvB,6BAA6BgE,QAAQ,KACvC,CAAC;YACH;YACA,MAAMC,WAAW,GAAGE,MAAM,CAACH,QAAQ,CAAwB;YAC3D,IAAIC,WAAW,KAAKpC,SAAS,EAAE;cAC7B,MAAM,IAAI7B,eAAe,CACvB,qBAAqBgE,QAAQ,eAC/B,CAAC;YACH;YACA,OAAO,IAAIC,WAAW,CAACF,MAAM,CAAC;UAChC;QACF,CAAC,CAAC;QACF5D,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAEiC,MAAM,CAAC;QACxC,OAAOA,MAAM;MACf,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM0B,kBAAkB,GACtB1C,2BAA2B,CAAIX,mBAAmB,CAAC;QACrDZ,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAE2D,kBAAkB,CAAC;QACpD,OAAOA,kBAAkB;MAC3B;MACA,MAAMC,OAAO,GAAGxE,sBAAsB,CAACyE,kBAAkB,CACvDpC,OAAO,EACPP,mBAAmB,EACnBlB,KACF,CAAC;MACDN,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAE4D,OAAO,CAAC;MACzClE,qBAAqB,CAACmB,GAAG,CAAC+C,OAAO,CAAC;MAClC,OAAOA,OAAO;IAChB;EACF;EACA,OAAOxE,sBAAsB,CAACyE,kBAAkB,CAC9C7D,KAAK,EACLkB,mBAAmB,EACnBE,SACF,CAAC;AACH;AAEA,MAAM0C,sBAAsB,GAAG,GAAG;AAElC,SAASxB,cAAcA,CAACtC,KAAsB,EAAE;EAC9C;EACA,MAAM+D,IAAI,GAAG/D,KAAK,EAAEoC,UAAU,EAAE2B,IAAI;EACpC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,SAAS;EAClB;EACA,IAAIA,IAAI,CAACC,MAAM,GAAGF,sBAAsB,EAAE;IACxC,OAAO,GAAGC,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEH,sBAAsB,CAAC,KAAK;EAC1D;EACA,OAAOC,IAAI;AACb;AAMA,SAASG,gBAAgBA,CAAIlE,KAE5B,EAA8B;EAC7B,SAAS;;EACT,OAAO,CAAC,CAACA,KAAK,CAACmE,gBAAgB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrC,iBAAiBA,CAAmB9B,KAAQ,EAAE;EACrD,IAAI,CAACkC,OAAO,EAAE;IACZ;EACF;EACA/B,MAAM,CAACuC,OAAO,CAAC1C,KAAK,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC3B,GAAG,EAAEZ,OAAO,CAAC,KAAK;IAChD,MAAMwC,UAAU,GAAGlE,MAAM,CAACmE,wBAAwB,CAACtE,KAAK,EAAEyC,GAAG,CAAE;IAC/D,IAAI,CAAC4B,UAAU,CAACE,YAAY,EAAE;MAC5B;IACF;IACApE,MAAM,CAACqE,cAAc,CAACxE,KAAK,EAAEyC,GAAG,EAAE;MAChChC,GAAGA,CAAA,EAAG;QACJ,OAAOoB,OAAO;MAChB,CAAC;MACDhB,GAAGA,CAAA,EAAG;QACJjB,MAAM,CAAC6E,IAAI,CACT,yBAAyBhC,GAAG;AACtC;AACA,kBACQ,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFtC,MAAM,CAACuE,iBAAiB,CAAC1E,KAAK,CAAC;AACjC;AAEA,OAAO,SAAS2E,+BAA+BA,CAC7C3E,KAAQ,EACa;EACrB,SAAS;;EACT,IAAIH,iBAAiB,EAAE;IACrB;IACA;IACA,OAAOG,KAAK;EACd;EACA;EACA,SAAS4E,cAAcA,CAAC5E,KAAQ,EAAuB;IACrD,IACG,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAC5C,OAAOA,KAAK,KAAK,UAAU,EAC3B;MACA,IAAID,YAAY,CAACC,KAAK,CAAC,EAAE;QACvB;QACA;QACA,OAAO0D,MAAM,CAACmB,mBAAmB,CAC/B7E,KAAK,EACLoB,SACF,CAAC;MACH;MACA,IAAI8C,gBAAgB,CAAIlE,KAAK,CAAC,EAAE;QAC9B;QACA;QACA;QACA,OAAOA,KAAK,CAACmE,gBAAgB;MAC/B;MACA,IAAIzC,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;QACxB,OAAO0D,MAAM,CAACmB,mBAAmB,CAC/B7E,KAAK,CAAC4B,GAAG,CAACgD,cAAc,CAAC,EACzBxD,SACF,CAAC;MACH;MACA,MAAMK,OAA4C,GAAG,CAAC,CAAC;MACvD,KAAK,MAAM,CAACgB,GAAG,EAAEZ,OAAO,CAAC,IAAI1B,MAAM,CAACuC,OAAO,CAAC1C,KAAK,CAAC,EAAE;QAClDyB,OAAO,CAACgB,GAAG,CAAC,GAAGmC,cAAc,CAAC/C,OAAO,CAAC;MACxC;MACA,OAAO6B,MAAM,CAACmB,mBAAmB,CAACpD,OAAO,EAAEzB,KAAK,CAAC;IACnD;IACA,OAAO0D,MAAM,CAACmB,mBAAmB,CAAC7E,KAAK,EAAEoB,SAAS,CAAC;EACrD;EACA,OAAOwD,cAAc,CAAC5E,KAAK,CAAC;AAC9B;AAEA,SAAS8E,eAAeA,CAAmB9E,KAAQ,EAAK;EACtD,OAAOA,KAAK;AACd;AAEA,SAAS+E,mBAAmBA,CAAmB/E,KAAQ,EAAK;EAC1D,IAAIN,qBAAqB,CAACe,GAAG,CAACT,KAAK,CAAC,EAAE;IACpC,OAAOA,KAAK;EACd;EACA,MAAMiC,MAAM,GAAGhB,2BAA2B,CAAC;IACzCV,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOP,KAAK;IACd;EACF,CAAC,CAAC;EACFN,qBAAqB,CAACmB,GAAG,CAACb,KAAK,EAAEiC,MAAM,CAAC;EACxC,OAAOjC,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgF,aAAa,GAAGnF,iBAAiB,GAC1CiF,eAAe,GACfC,mBAAmB", "ignoreList": []}