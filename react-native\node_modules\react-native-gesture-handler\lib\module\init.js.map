{"version": 3, "sources": ["init.ts"], "names": ["startListening", "RNGestureHandlerModule", "isF<PERSON><PERSON>", "fabricInitialized", "initialize", "maybeInitializeFabric", "install"], "mappings": "AAAA,SAASA,cAAT,QAA+B,mCAA/B;AACA,OAAOC,sBAAP,MAAmC,0BAAnC;AACA,SAASC,QAAT,QAAyB,SAAzB;AAEA,IAAIC,iBAAiB,GAAG,KAAxB;AAEA,OAAO,SAASC,UAAT,GAAsB;AAC3BJ,EAAAA,cAAc;AACf,C,CAED;AACA;;AACA,OAAO,SAASK,qBAAT,GAAiC;AACtC,MAAIH,QAAQ,MAAM,CAACC,iBAAnB,EAAsC;AACpCF,IAAAA,sBAAsB,CAACK,OAAvB;AACAH,IAAAA,iBAAiB,GAAG,IAApB;AACD;AACF", "sourcesContent": ["import { startListening } from './handlers/gestures/eventReceiver';\nimport RNGestureHandlerModule from './RNGestureHandlerModule';\nimport { isFabric } from './utils';\n\nlet fabricInitialized = false;\n\nexport function initialize() {\n  startListening();\n}\n\n// Since isFabric() may give wrong results before the first render, we call this\n// method during render of GestureHandlerRootView\nexport function maybeInitializeFabric() {\n  if (isFabric() && !fabricInitialized) {\n    RNGestureHandlerModule.install();\n    fabricInitialized = true;\n  }\n}\n"]}