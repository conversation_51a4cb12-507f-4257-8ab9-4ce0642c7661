@echo off
echo 🚀 Démarrage du serveur API Scanner QR - AquaTrack
echo.

echo 📁 Vérification du dossier backend...
if not exist "backend" (
    echo ❌ Erreur: Le dossier 'backend' n'existe pas
    echo Assurez-vous d'être dans le bon répertoire
    pause
    exit /b 1
)

cd backend

echo 📦 Vérification des dépendances...
if not exist "node_modules" (
    echo 📥 Installation des dépendances...
    npm install
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
) else (
    echo ✅ Dépendances déjà installées
)

echo.
echo 🔧 Configuration requise:
echo - PostgreSQL doit être démarré
echo - Base de données 'Facutration' doit exister
echo - Modifier les paramètres de connexion dans api/scanner.js si nécessaire
echo.

echo 🚀 Démarrage du serveur sur http://localhost:5000...
echo.
echo 📡 Endpoints disponibles:
echo   - GET /api/scan/:qrCode     - Scanner un QR code
echo   - GET /api/test-db          - Tester la connexion DB
echo   - GET /api/qr-codes         - Lister les QR codes
echo.
echo 💡 Pour arrêter le serveur: Ctrl+C
echo.

npm start

pause
