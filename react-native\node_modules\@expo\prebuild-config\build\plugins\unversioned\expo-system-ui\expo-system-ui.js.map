{"version": 3, "file": "expo-system-ui.js", "names": ["_withAndroidRootViewBackgroundColor", "data", "require", "_withAndroidUserInterfaceStyle", "_withIosRootViewBackgroundColor", "_withIosUserInterfaceStyle", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "withAndroidRootViewBackgroundColor", "withIosRootViewBackgroundColor", "withAndroidUserInterfaceStyle", "withIosUserInterfaceStyle"], "sources": ["../../../../src/plugins/unversioned/expo-system-ui/expo-system-ui.ts"], "sourcesContent": ["import { withAndroidRootViewBackgroundColor } from './withAndroidRootViewBackgroundColor';\nimport { withAndroidUserInterfaceStyle } from './withAndroidUserInterfaceStyle';\nimport { withIosRootViewBackgroundColor } from './withIosRootViewBackgroundColor';\nimport { withIosUserInterfaceStyle } from './withIosUserInterfaceStyle';\nimport { createLegacyPlugin } from '../createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-system-ui',\n  fallback: [\n    withAndroidRootViewBackgroundColor,\n    withIosRootViewBackgroundColor,\n    withAndroidUserInterfaceStyle,\n    withIosUserInterfaceStyle,\n  ],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,oCAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,mCAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,+BAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,8BAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,gCAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,+BAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,2BAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,0BAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,oBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,mBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2D,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE5C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CACRC,wEAAkC,EAClCC,gEAA8B,EAC9BC,8DAA6B,EAC7BC,sDAAyB;AAE7B,CAAC,CAAC", "ignoreList": []}