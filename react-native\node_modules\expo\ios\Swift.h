// Copyright 2018-present 650 Industries. All rights reserved.

// The generated swift header may depend on some Objective-C declarations,
// adding dependency imports here to prevent declarations not found errors.
#import <React/RCTHTTPRequestHandler.h>

// When `use_frameworks!` is used, the generated Swift header is inside ExpoModulesCore module.
// Otherwise, it's available only locally with double-quoted imports.
#if __has_include(<Expo/Expo-Swift.h>)
#import <Expo/Expo-Swift.h>
#else
#import "Expo-Swift.h"
#endif
