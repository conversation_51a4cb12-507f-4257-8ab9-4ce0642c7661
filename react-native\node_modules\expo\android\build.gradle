apply plugin: 'com.android.library'
apply plugin: 'expo-module-gradle-plugin'

import expo.modules.plugin.gradle.ExpoModuleExtension
import expo.modules.plugin.Version

// TODO(@lukmccall): Remove when we drop support for SDK 52
def useLegacyAutolinking = false
try {
  apply plugin: "expo-autolinking"
} catch (e) {
  useLegacyAutolinking = true
}

if (useLegacyAutolinking) {
  // Import autolinking script
  apply from: "../scripts/autolinking.gradle"
}

if (useLegacyAutolinking) {
  ensureDependeciesWereEvaluated(project)
}


buildscript {
  // Simple helper that allows the root project to override versions declared by this library.
  ext.safeExtGet = { prop, fallback ->
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
  }
}

def reactNativeVersion = project.extensions.getByType(ExpoModuleExtension).reactNativeVersion

group = 'host.exp.exponent'
version = '53.0.18'

expoModule {
  // We can't prebuild the module because it depends on the generated files.
  canBePublished false
}

android {
  namespace "expo.core"
  defaultConfig {
    versionCode 1
    versionName "53.0.18"
    consumerProguardFiles("proguard-rules.pro")
  }
  testOptions {
    unitTests.includeAndroidResources = true
  }

  if (useLegacyAutolinking) {
    sourceSets {
      main {
        java {
          srcDirs += new File(project.buildDir, generatedFilesSrcDir)
        }
      }
    }
  }

  sourceSets.main.java {
    if (reactNativeVersion.isAtLeast(new Version(0, 78, 0))) {
      srcDirs += 'src/rn78/main'
    } else if (reactNativeVersion.isAtLeast(new Version(0, 74, 0))) {
      // TODO(kudo,20241217): Remove this when we drop react-native 0.77 support
      srcDirs += 'src/rn74/main'
    }
  }
}

dependencies { dependencyHandler ->
  implementation 'com.facebook.react:react-android'

  testImplementation 'junit:junit:4.13.2'
  testImplementation 'androidx.test:core:1.5.0'
  testImplementation "com.google.truth:truth:1.1.2"
  testImplementation 'io.mockk:mockk:1.13.5'
  testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.2'
  testImplementation 'org.robolectric:robolectric:4.15.1'

  if (useLegacyAutolinking) {
    // Link expo modules as dependencies of the adapter. It uses `api` configuration so they all will be visible for the app as well.
    // A collection of the dependencies depends on the options passed to `useExpoModules` in your project's `settings.gradle`.
    addExpoModulesDependencies(dependencyHandler, project)
  }
}

if (useLegacyAutolinking) {
  // A task generating a package list of expo modules.
  task generateExpoModulesPackageListTask {
    def modulesConfig = getModulesConfig()
    def outputPath = getGenerateExpoModulesPackagesListPath()
    if (modulesConfig) {
      outputs.file(file(outputPath))
      inputs.property("modulesConfig", modulesConfig)
    }

    // TOOD(@lukmccall): fix not working with configuration cache enabled
    doLast {
      generateExpoModulesPackageList()
    }
  }

// Run that task during prebuilding phase.
  preBuild.dependsOn "generateExpoModulesPackageListTask"
}
