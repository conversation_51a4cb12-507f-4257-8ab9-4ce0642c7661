import*as e from"../../core/root/root.js";import*as t from"../../services/puppeteer/puppeteer.js";import"../../third_party/lighthouse/lighthouse-dt-bundle.js";class s{sessionId;onMessage;onDisconnect;constructor(e){this.sessionId=e,this.onMessage=null,this.onDisconnect=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.onDisconnect=e}getOnDisconnect(){return this.onDisconnect}getSessionId(){return this.sessionId}sendRawMessage(e){i("sendProtocolMessage",{message:e})}async disconnect(){this.onDisconnect?.("force disconnect"),this.onDisconnect=null,this.onMessage=null}}let n,o;async function a(a,r){let c;e.Runtime.Runtime.queryParam("isUnderTest")&&(console.log=()=>{},r.flags.maxWaitForLoad=2e3),self.listenForStatus((e=>{i("statusUpdate",{message:e[1]})}));try{if("endTimespan"===a){if(!o)throw new Error("Cannot end a timespan before starting one");const e=await o();return o=void 0,e}const i=await async function(t){const s=self.lookupLocale(t);if("en-US"===s||"en"===s)return;try{const t=e.Runtime.getRemoteBase();let n;n=t&&t.base?`${t.base}third_party/lighthouse/locales/${s}.json`:new URL(`../../third_party/lighthouse/locales/${s}.json`,import.meta.url).toString();const o=new Promise(((e,t)=>setTimeout((()=>t(new Error("timed out fetching locale"))),5e3))),a=await Promise.race([o,fetch(n).then((e=>e.json()))]);return self.registerLocaleData(s,a),s}catch(e){console.error(e)}return}(r.locales),l=r.flags;l.logLevel=l.logLevel||"info",l.channel="devtools",l.locale=i;const g=r.config||self.createConfig(r.categoryIDs,l.formFactor),p=r.url,{rootTargetId:f,mainSessionId:u}=r;n=new s(u),c=await t.PuppeteerConnection.PuppeteerConnectionHelper.connectPuppeteerToConnectionViaTab({connection:n,rootTargetId:f,isPageTargetCallback:e=>"page"===e.type});const{page:d}=c;if(!d)throw new Error("Could not create page handle for the target page");if("snapshot"===a)return await self.snapshot(d,{config:g,flags:l});if("startTimespan"===a){const e=await self.startTimespan(d,{config:g,flags:l});return void(o=e.endTimespan)}return await self.navigation(d,p,{config:g,flags:l})}catch(e){return{fatal:!0,message:e.message,stack:e.stack}}finally{"startTimespan"!==a&&await(c?.browser.disconnect())}}function i(e,t){self.postMessage({action:e,args:t})}self.onmessage=async function(e){const t=e.data;switch(t.action){case"startTimespan":case"endTimespan":case"snapshot":case"navigation":{const e=await a(t.action,t.args);e&&"object"==typeof e&&("report"in e&&delete e.report,"artifacts"in e&&(e.artifacts.Timing=JSON.parse(JSON.stringify(e.artifacts.Timing)))),self.postMessage({id:t.id,result:e});break}case"dispatchProtocolMessage":n?.onMessage?.(t.args.message);break;default:throw new Error(`Unknown event: ${e.data}`)}},globalThis.global=self,globalThis.global.isVinn=!0,globalThis.global.document={},globalThis.global.document.documentElement={},globalThis.global.document.documentElement.style={WebkitAppearance:"WebkitAppearance"},self.postMessage("workerReady");
