import * as React from 'react';
import type { NativeSafeAreaViewProps } from './SafeArea.types';
export type SafeAreaViewProps = NativeSafeAreaViewProps;
export declare const SafeAreaView: React.ForwardRefExoticComponent<NativeSafeAreaViewProps & React.RefAttributes<React.Component<import("./specs/NativeSafeAreaView").NativeProps, {}, any> & Readonly<import("react-native").NativeMethods>>>;
//# sourceMappingURL=SafeAreaView.d.ts.map