{"version": 3, "names": ["useEffect", "useRef", "FrameCallbackRegistryJS", "frameCallbackRegistry", "useFrameCallback", "callback", "autostart", "ref", "setActive", "isActive", "manageStateFrameCallback", "current", "callbackId", "registerFrameCallback", "memoizedFrameCallback", "unregisterFrameCallback"], "sourceRoot": "../../../src", "sources": ["hook/useFrameCallback.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,uBAAuB,MAAM,6CAA0C;;AAG9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,MAAMC,qBAAqB,GAAG,IAAID,uBAAuB,CAAC,CAAC;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAC9BC,QAAwC,EACxCC,SAAS,GAAG,IAAI,EACD;EACf,MAAMC,GAAG,GAAGN,MAAM,CAAgB;IAChCO,SAAS,EAAGC,QAAiB,IAAK;MAChCN,qBAAqB,CAACO,wBAAwB,CAC5CH,GAAG,CAACI,OAAO,CAACC,UAAU,EACtBH,QACF,CAAC;MACDF,GAAG,CAACI,OAAO,CAACF,QAAQ,GAAGA,QAAQ;IACjC,CAAC;IACDA,QAAQ,EAAEH,SAAS;IACnBM,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;EAEFZ,SAAS,CAAC,MAAM;IACdO,GAAG,CAACI,OAAO,CAACC,UAAU,GACpBT,qBAAqB,CAACU,qBAAqB,CAACR,QAAQ,CAAC;IACvD,MAAMS,qBAAqB,GAAGP,GAAG,CAACI,OAAO;IACzCJ,GAAG,CAACI,OAAO,CAACH,SAAS,CAACD,GAAG,CAACI,OAAO,CAACF,QAAQ,CAAC;IAE3C,OAAO,MAAM;MACXN,qBAAqB,CAACY,uBAAuB,CAC3CD,qBAAqB,CAACF,UACxB,CAAC;MACDE,qBAAqB,CAACF,UAAU,GAAG,CAAC,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACP,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAEzB,OAAOC,GAAG,CAACI,OAAO;AACpB", "ignoreList": []}