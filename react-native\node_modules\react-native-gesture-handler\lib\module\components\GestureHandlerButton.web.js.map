{"version": 3, "sources": ["GestureHandlerButton.web.tsx"], "names": ["React", "View", "forwardRef", "props", "ref"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,IAAT,QAAqB,cAArB;AAEA,4BAAeD,KAAK,CAACE,UAAN,CAAuB,CAACC,KAAD,EAAQC,GAAR,kBACpC,oBAAC,IAAD;AAAM,EAAA,GAAG,EAAEA,GAAX;AAAgB,EAAA,iBAAiB,EAAC;AAAlC,GAA+CD,KAA/C,EADa,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { View } from 'react-native';\n\nexport default React.forwardRef<View>((props, ref) => (\n  <View ref={ref} accessibilityRole=\"button\" {...props} />\n));\n"]}