<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/keep-awake/">
    <img
      src="../../.github/resources/expo-keep-awake.svg"
      alt="expo-keep-awake"
      height="64" />
  </a>
</p>

Provides a React component that prevents the screen sleeping when rendered. It also exposes static methods to control the behavior imperatively.

# API documentation

- [Documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/keep-awake/)
- [Documentation for the main branch](https://docs.expo.dev/versions/unversioned/sdk/keep-awake/)

# Installation in managed Expo projects

For [managed](https://docs.expo.dev/archive/managed-vs-bare/) Expo projects, please follow the installation instructions in the [API documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/keep-awake/).

# Installation in bare React Native projects

For bare React Native projects, you must ensure that you have [installed and configured the `expo` package](https://docs.expo.dev/bare/installing-expo-modules/) before continuing.

### Add the package to your npm dependencies

```
npx expo install expo-keep-awake
```

### Configure for Android

No additional set up necessary.

### Configure for iOS

Run `npx pod-install` after installing the npm package.

# Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide](https://github.com/expo/expo#contributing).
