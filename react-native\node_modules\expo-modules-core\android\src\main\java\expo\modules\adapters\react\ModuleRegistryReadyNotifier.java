package expo.modules.adapters.react;

import com.facebook.react.bridge.BaseJavaModule;
import com.facebook.react.bridge.NativeModule;

import expo.modules.core.ModuleRegistry;

/**
 * {@link ModuleRegistryReadyNotifier} is exported as a native module
 * to React Native and when {@link com.facebook.react.ReactInstanceManager}
 * notifies {@link com.facebook.react.bridge.NativeModule} of being ready
 * ({@link NativeModule#initialize()}) it delegates the call to {@link ModuleRegistry}.
 */
public class ModuleRegistryReadyNotifier extends BaseJavaModule {
  private ModuleRegistry mModuleRegistry;

  public ModuleRegistryReadyNotifier(ModuleRegistry moduleRegistry) {
    mModuleRegistry = moduleRegistry;
  }

  @Override
  public String getName() {
    return "ModuleRegistryReadyNotifier";
  }

  @Override
  public void initialize() {
    mModuleRegistry.ensureIsInitialized();
  }
}
