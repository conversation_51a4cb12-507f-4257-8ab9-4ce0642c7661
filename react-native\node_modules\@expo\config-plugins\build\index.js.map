{"version": 3, "file": "index.js", "names": ["AndroidConfig", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "IOSConfig", "_createBaseMod", "_withAndroidBaseMods", "_withIosBaseMods", "XML", "History", "WarningAggregator", "_Updates", "_Plugin", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "_withPlugins", "_withRunOnce", "_withDangerousMod", "_withFinalizedMod", "_withMod", "_iosPlugins", "_androidPlugins", "_withStaticPlugin", "_modCompiler", "_errors", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "i", "set", "BaseMods", "withGeneratedBaseMods", "provider", "withAndroidBaseMods", "getAndroidModFileProviders", "withIosBaseMods", "getIosModFileProviders"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * For internal use in Expo CLI\n */\nimport * as AndroidConfig from './android';\nimport * as IOSConfig from './ios';\nimport { provider, withGeneratedBaseMods } from './plugins/createBaseMod';\nimport { getAndroidModFileProviders, withAndroidBaseMods } from './plugins/withAndroidBaseMods';\nimport { getIosModFileProviders, withIosBaseMods } from './plugins/withIosBaseMods';\nimport * as XML from './utils/XML';\nimport * as History from './utils/history';\nimport * as WarningAggregator from './utils/warnings';\n\n// TODO: Remove\nexport * as Updates from './utils/Updates';\n\nexport { IOSConfig, AndroidConfig };\n\nexport { WarningAggregator, History, XML };\n\n/**\n * These are the \"config-plugins\"\n */\n\nexport * from './Plugin.types';\n\nexport { withPlugins } from './plugins/withPlugins';\n\nexport { withRunOnce, createRunOncePlugin } from './plugins/withRunOnce';\n\nexport { withDangerousMod } from './plugins/withDangerousMod';\nexport { withFinalizedMod } from './plugins/withFinalizedMod';\nexport { withMod, withBaseMod } from './plugins/withMod';\n\nexport {\n  withAppDelegate,\n  withInfoPlist,\n  withEntitlementsPlist,\n  withExpoPlist,\n  withXcodeProject,\n  withPodfile,\n  withPodfileProperties,\n} from './plugins/ios-plugins';\n\nexport {\n  withAndroidManifest,\n  withStringsXml,\n  withAndroidColors,\n  withAndroidColorsNight,\n  withAndroidStyles,\n  withMainActivity,\n  withMainApplication,\n  withProjectBuildGradle,\n  withAppBuildGradle,\n  withSettingsGradle,\n  withGradleProperties,\n} from './plugins/android-plugins';\n\nexport { withStaticPlugin } from './plugins/withStaticPlugin';\n\nexport { compileModsAsync, withDefaultBaseMods, evalModsAsync } from './plugins/mod-compiler';\n\nexport { PluginError } from './utils/errors';\n\nexport const BaseMods = {\n  withGeneratedBaseMods,\n  provider,\n  withAndroidBaseMods,\n  getAndroidModFileProviders,\n  withIosBaseMods,\n  getIosModFileProviders,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAAA,cAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,aAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,aAAA;EAAA;AAAA;AAC3C,SAAAS,UAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,SAAA;EAAA;AAAA;AACnC,SAAAC,eAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,cAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,qBAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,oBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,iBAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,gBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,IAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,GAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,GAAA;EAAA;AAAA;AACnC,SAAAC,QAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,OAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,OAAA;EAAA;AAAA;AAC3C,SAAAC,kBAAA;EAAA,MAAAd,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAY,iBAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAO,iBAAA;EAAA;AAAA;AAAA,SAAAC,SAAA;EAAA,MAAAf,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAa,QAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAAG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAQ,QAAA;EAAA;AAAA;AAatD,IAAAC,OAAA,GAAAd,OAAA;AAAAC,MAAA,CAAAc,IAAA,CAAAD,OAAA,EAAAE,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAhB,MAAA,CAAAiB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAd,OAAA,IAAAA,OAAA,CAAAc,GAAA,MAAAH,OAAA,CAAAG,GAAA;EAAAhB,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAc,GAAA;IAAAb,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAS,OAAA,CAAAG,GAAA;IAAA;EAAA;AAAA;AAEA,SAAAK,aAAA;EAAA,MAAAxB,IAAA,GAAAE,OAAA;EAAAsB,YAAA,YAAAA,CAAA;IAAA,OAAAxB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAyB,aAAA;EAAA,MAAAzB,IAAA,GAAAE,OAAA;EAAAuB,YAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAA0B,kBAAA;EAAA,MAAA1B,IAAA,GAAAE,OAAA;EAAAwB,iBAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAA2B,kBAAA;EAAA,MAAA3B,IAAA,GAAAE,OAAA;EAAAyB,iBAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAA4B,SAAA;EAAA,MAAA5B,IAAA,GAAAE,OAAA;EAAA0B,QAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAA6B,YAAA;EAAA,MAAA7B,IAAA,GAAAE,OAAA;EAAA2B,WAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUA,SAAA8B,gBAAA;EAAA,MAAA9B,IAAA,GAAAE,OAAA;EAAA4B,eAAA,YAAAA,CAAA;IAAA,OAAA9B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAcA,SAAA+B,kBAAA;EAAA,MAAA/B,IAAA,GAAAE,OAAA;EAAA6B,iBAAA,YAAAA,CAAA;IAAA,OAAA/B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAgC,aAAA;EAAA,MAAAhC,IAAA,GAAAE,OAAA;EAAA8B,YAAA,YAAAA,CAAA;IAAA,OAAAhC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAiC,QAAA;EAAA,MAAAjC,IAAA,GAAAE,OAAA;EAAA+B,OAAA,YAAAA,CAAA;IAAA,OAAAjC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6C,SAAAkC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAlC,wBAAAkC,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAA/B,GAAA,CAAA4B,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAzC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAA0C,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAzB,cAAA,CAAAC,IAAA,CAAAa,CAAA,EAAAW,CAAA,SAAAC,CAAA,GAAAH,CAAA,GAAAzC,MAAA,CAAA0C,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAC,CAAA,KAAAA,CAAA,CAAAxC,GAAA,IAAAwC,CAAA,CAAAC,GAAA,IAAA7C,MAAA,CAAAC,cAAA,CAAAsC,CAAA,EAAAI,CAAA,EAAAC,CAAA,IAAAL,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAF,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAU,GAAA,CAAAb,CAAA,EAAAO,CAAA,GAAAA,CAAA;AA7D7C;AACA;AACA;;AAUA;;AAOA;AACA;AACA;;AA0CO,MAAMO,QAAQ,GAAA5C,OAAA,CAAA4C,QAAA,GAAG;EACtBC,qBAAqB,EAArBA,sCAAqB;EACrBC,QAAQ,EAARA,yBAAQ;EACRC,mBAAmB,EAAnBA,0CAAmB;EACnBC,0BAA0B,EAA1BA,iDAA0B;EAC1BC,eAAe,EAAfA,kCAAe;EACfC,sBAAsB,EAAtBA;AACF,CAAC", "ignoreList": []}