{"name": "expo-location", "version": "18.0.10", "description": "Allows reading geolocation information from the device. Your app can poll for the current location or subscribe to location update events.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "location", "geolocation", "coords", "geocoding", "compass", "heading"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-location"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/location/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.0.4"}, "peerDependencies": {"expo": "*"}, "gitHead": "b08a0bd52965f85871c12c31da16a45e2cd26c4c"}