{"version": 3, "sources": ["../../../src/utils/expoUpdatesCli.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport resolveFrom, { silent as silentResolveFrom } from 'resolve-from';\n\nexport class ExpoUpdatesCLIModuleNotFoundError extends Error {}\nexport class ExpoUpdatesCLIInvalidCommandError extends Error {}\nexport class ExpoUpdatesCLICommandFailedError extends <PERSON>rror {}\n\nexport async function expoUpdatesCommandAsync(projectDir: string, args: string[]): Promise<string> {\n  let expoUpdatesCli;\n  try {\n    expoUpdatesCli =\n      silentResolveFrom(projectDir, 'expo-updates/bin/cli') ??\n      resolveFrom(projectDir, 'expo-updates/bin/cli.js');\n  } catch (e: any) {\n    if (e.code === 'MODULE_NOT_FOUND') {\n      throw new ExpoUpdatesCLIModuleNotFoundError(`The \\`expo-updates\\` package was not found. `);\n    }\n    throw e;\n  }\n\n  try {\n    return (\n      await spawnAsync(expoUpdatesCli, args, {\n        stdio: 'pipe',\n        env: { ...process.env },\n      })\n    ).stdout;\n  } catch (e: any) {\n    if (e.stderr && typeof e.stderr === 'string') {\n      if (e.stderr.includes('Invalid command')) {\n        throw new ExpoUpdatesCLIInvalidCommandError(\n          `The command specified by ${args} was not valid in the \\`expo-updates\\` CLI.`\n        );\n      } else {\n        throw new ExpoUpdatesCLICommandFailedError(e.stderr);\n      }\n    }\n\n    throw e;\n  }\n}\n"], "names": ["ExpoUpdatesCLICommandFailedError", "ExpoUpdatesCLIInvalidCommandError", "ExpoUpdatesCLIModuleNotFoundError", "expoUpdatesCommandAsync", "Error", "projectDir", "args", "expoUpdatesCli", "silentResolveFrom", "resolveFrom", "e", "code", "spawnAsync", "stdio", "env", "process", "stdout", "stderr", "includes"], "mappings": ";;;;;;;;;;;IAKaA,gCAAgC;eAAhCA;;IADAC,iCAAiC;eAAjCA;;IADAC,iCAAiC;eAAjCA;;IAISC,uBAAuB;eAAvBA;;;;gEAPC;;;;;;;iEACkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,MAAMD,0CAA0CE;AAAO;AACvD,MAAMH,0CAA0CG;AAAO;AACvD,MAAMJ,yCAAyCI;AAAO;AAEtD,eAAeD,wBAAwBE,UAAkB,EAAEC,IAAc;IAC9E,IAAIC;IACJ,IAAI;QACFA,iBACEC,IAAAA,qBAAiB,EAACH,YAAY,2BAC9BI,IAAAA,sBAAW,EAACJ,YAAY;IAC5B,EAAE,OAAOK,GAAQ;QACf,IAAIA,EAAEC,IAAI,KAAK,oBAAoB;YACjC,MAAM,IAAIT,kCAAkC,CAAC,4CAA4C,CAAC;QAC5F;QACA,MAAMQ;IACR;IAEA,IAAI;QACF,OAAO,AACL,CAAA,MAAME,IAAAA,qBAAU,EAACL,gBAAgBD,MAAM;YACrCO,OAAO;YACPC,KAAK;gBAAE,GAAGC,QAAQD,GAAG;YAAC;QACxB,EAAC,EACDE,MAAM;IACV,EAAE,OAAON,GAAQ;QACf,IAAIA,EAAEO,MAAM,IAAI,OAAOP,EAAEO,MAAM,KAAK,UAAU;YAC5C,IAAIP,EAAEO,MAAM,CAACC,QAAQ,CAAC,oBAAoB;gBACxC,MAAM,IAAIjB,kCACR,CAAC,yBAAyB,EAAEK,KAAK,2CAA2C,CAAC;YAEjF,OAAO;gBACL,MAAM,IAAIN,iCAAiCU,EAAEO,MAAM;YACrD;QACF;QAEA,MAAMP;IACR;AACF"}