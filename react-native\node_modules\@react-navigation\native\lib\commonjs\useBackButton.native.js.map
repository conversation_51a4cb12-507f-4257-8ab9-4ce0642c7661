{"version": 3, "names": ["useBackButton", "ref", "React", "useEffect", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "navigation", "current", "canGoBack", "goBack", "remove"], "sourceRoot": "../../src", "sources": ["useBackButton.native.tsx"], "mappings": ";;;;;;AAIA;AACA;AAA2C;AAAA;AAE5B,SAASA,aAAa,CACnCC,GAA2D,EAC3D;EACAC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGC,wBAAW,CAACC,gBAAgB,CAC/C,mBAAmB,EACnB,MAAM;MACJ,MAAMC,UAAU,GAAGN,GAAG,CAACO,OAAO;MAE9B,IAAID,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,KAAK;MACd;MAEA,IAAIA,UAAU,CAACE,SAAS,EAAE,EAAE;QAC1BF,UAAU,CAACG,MAAM,EAAE;QAEnB,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CACF;IAED,OAAO,MAAMN,YAAY,CAACO,MAAM,EAAE;EACpC,CAAC,EAAE,CAACV,GAAG,CAAC,CAAC;AACX"}