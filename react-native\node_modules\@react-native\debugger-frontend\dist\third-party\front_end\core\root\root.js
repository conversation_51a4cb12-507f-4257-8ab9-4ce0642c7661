import*as e from"../platform/platform.js";const t=new URLSearchParams(location.search);let n,r="";class s{constructor(){}static instance(e={forceNew:null}){const{forceNew:t}=e;return n&&!t||(n=new s),n}static removeInstance(){n=void 0}static queryParam(e){return t.get(e)}static setQueryParamForTesting(e,n){t.set(e,n)}static experimentsSetting(){try{return e.StringUtilities.toKebabCaseKeys(JSON.parse(self.localStorage&&self.localStorage.experiments?self.localStorage.experiments:"{}"))}catch(e){return console.error("Failed to parse localStorage['experiments']"),{}}}static setPlatform(e){r=e}static platform(){return r}static isDescriptorEnabled(e,t){const{experiment:n}=e;if("*"===n)return!0;if(n&&n.startsWith("!")&&o.isEnabled(n.substring(1)))return!1;if(n&&!n.startsWith("!")&&!o.isEnabled(n))return!1;const{condition:r}=e;return!r||r(t)}loadLegacyModule(e){return import(`../../${e}`)}}class a{#e;#t;#n;#r;#s;constructor(){this.#e=[],this.#t=new Set,this.#n=new Set,this.#r=new Set,this.#s=new Set}allConfigurableExperiments(){const e=[];for(const t of this.#e)this.#n.has(t.name)||e.push(t);return e}setExperimentsSetting(e){self.localStorage&&(self.localStorage.experiments=JSON.stringify(e))}register(t,n,r,s,a){if(this.#t.has(t))throw new Error(`Duplicate registraction of experiment '${t}'`);this.#t.add(t),this.#e.push(new i(this,t,n,Boolean(r),s??e.DevToolsPath.EmptyUrlString,a??e.DevToolsPath.EmptyUrlString))}isEnabled(e){return this.checkExperiment(e),!1!==s.experimentsSetting()[e]&&(!(!this.#n.has(e)&&!this.#r.has(e))||(!!this.#s.has(e)||Boolean(s.experimentsSetting()[e])))}setEnabled(e,t){this.checkExperiment(e);const n=s.experimentsSetting();n[e]=t,this.setExperimentsSetting(n)}enableExperimentsTransiently(e){for(const t of e)this.checkExperiment(t),this.#n.add(t)}enableExperimentsByDefault(e){for(const t of e)this.checkExperiment(t),this.#r.add(t)}setServerEnabledExperiments(e){for(const t of e)this.checkExperiment(t),this.#s.add(t)}enableForTest(e){this.checkExperiment(e),this.#n.add(e)}disableForTest(e){this.checkExperiment(e),this.#n.delete(e)}clearForTest(){this.#e=[],this.#t.clear(),this.#n.clear(),this.#r.clear(),this.#s.clear()}cleanUpStaleExperiments(){const e=s.experimentsSetting(),t={};for(const{name:n}of this.#e)if(e.hasOwnProperty(n)){const r=e[n];(r||this.#r.has(n))&&(t[n]=r)}this.setExperimentsSetting(t)}checkExperiment(e){if(!this.#t.has(e))throw new Error(`Unknown experiment '${e}'`)}}class i{name;title;unstable;docLink;feedbackLink;#e;constructor(e,t,n,r,s,a){this.name=t,this.title=n,this.unstable=r,this.docLink=s,this.feedbackLink=a,this.#e=e}isEnabled(){return this.#e.isEnabled(this.name)}setEnabled(e){this.#e.setEnabled(this.name,e)}}const o=new a;var l,c;!function(e){e.REACT_NATIVE_SPECIFIC_UI="react-native-specific-ui",e.JS_HEAP_PROFILER_ENABLE="js-heap-profiler-enable",e.ENABLE_PERFORMANCE_PANEL="enable-performance-panel",e.ENABLE_NETWORK_PANEL="enable-network-panel"}(l||(l={})),function(e){e.CAN_DOCK="can_dock",e.NOT_SOURCES_HIDE_ADD_FOLDER="!sources.hide_add_folder",e.REACT_NATIVE_UNSTABLE_NETWORK_PANEL="unstable_enableNetworkPanel"}(c||(c={}));const m={canDock:()=>Boolean(s.queryParam("can_dock")),notSourcesHideAddFolder:()=>Boolean(s.queryParam(c.NOT_SOURCES_HIDE_ADD_FOLDER)),reactNativeUnstableNetworkPanel:()=>Boolean(s.queryParam(c.REACT_NATIVE_UNSTABLE_NETWORK_PANEL))||o.isEnabled("enable-network-panel")};var h=Object.freeze({__proto__:null,getRemoteBase:function(e=self.location.toString()){const t=new URL(e).searchParams.get("remoteBase");if(!t)return null;const n=/\/serve_file\/(@[0-9a-zA-Z]+)\/?$/.exec(t);return n?{base:`devtools://devtools/remote/serve_file/${n[1]}/`,version:n[1]}:null},getPathName:function(){return window.location.pathname},Runtime:s,ExperimentsSupport:a,Experiment:i,experiments:o,get RNExperimentName(){return l},get ConditionName(){return c},conditions:m});export{h as Runtime};
