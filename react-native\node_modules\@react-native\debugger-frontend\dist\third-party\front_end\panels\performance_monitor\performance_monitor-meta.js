import*as o from"../../core/i18n/i18n.js";import*as r from"../../ui/legacy/legacy.js";const e={performanceMonitor:"Performance monitor",performance:"performance",systemMonitor:"system monitor",monitor:"monitor",activity:"activity",metrics:"metrics",showPerformanceMonitor:"Show Performance monitor"},i=o.i18n.registerUIStrings("panels/performance_monitor/performance_monitor-meta.ts",e),n=o.i18n.getLazilyComputedLocalizedString.bind(void 0,i);let t;r.ViewManager.registerViewExtension({location:"drawer-view",id:"performance.monitor",title:n(e.performanceMonitor),commandPrompt:n(e.showPerformanceMonitor),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return t||(t=await import("./performance_monitor.js")),t}()).PerformanceMonitor.PerformanceMonitorImpl),tags:[n(e.performance),n(e.systemMonitor),n(e.monitor),n(e.activity),n(e.metrics)]});
