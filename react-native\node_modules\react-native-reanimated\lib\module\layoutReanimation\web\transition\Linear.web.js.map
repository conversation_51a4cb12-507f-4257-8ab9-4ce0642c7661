{"version": 3, "names": ["LinearTransition", "name", "transitionData", "translateX", "translateY", "scaleX", "scaleY", "linearTransition", "style", "transform", "scale", "duration"], "sourceRoot": "../../../../../src", "sources": ["layoutReanimation/web/transition/Linear.web.ts"], "mappings": "AAAA,YAAY;;AAGZ,OAAO,SAASA,gBAAgBA,CAACC,IAAY,EAAEC,cAA8B,EAAE;EAC7E,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGJ,cAAc;EAEjE,MAAMK,gBAAgB,GAAG;IACvBN,IAAI;IACJO,KAAK,EAAE;MACL,CAAC,EAAE;QACDC,SAAS,EAAE,CACT;UACEN,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BC,UAAU,EAAE,GAAGA,UAAU,IAAI;UAC7BM,KAAK,EAAE,GAAGL,MAAM,IAAIC,MAAM;QAC5B,CAAC;MAEL;IACF,CAAC;IACDK,QAAQ,EAAE;EACZ,CAAC;EAED,OAAOJ,gBAAgB;AACzB", "ignoreList": []}