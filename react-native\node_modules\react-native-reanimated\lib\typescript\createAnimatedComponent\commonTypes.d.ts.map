{"version": 3, "file": "commonTypes.d.ts", "sourceRoot": "", "sources": ["../../../src/createAnimatedComponent/commonTypes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAC5C,OAAO,KAAK,EACV,iBAAiB,EACjB,WAAW,EACX,UAAU,EACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAClD,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,KAAK,EACV,oBAAoB,EACpB,0BAA0B,EAC1B,uBAAuB,EACvB,gBAAgB,EACjB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AAE9E,MAAM,WAAW,aAAc,SAAQ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAC5D,eAAe,CAAC,EAAE,kBAAkB,CAAC;IACrC,OAAO,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,QAAQ;IACvB,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC;IACrC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAC5C,UAAU,EAAE,UAAU,CAAC;CACxB;AAED,MAAM,WAAW,kBAAkB;IACjC,iBAAiB,CACf,iBAAiB,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,EACpD,QAAQ,EAAE,QAAQ,GACjB,IAAI,CAAC;IACR,iBAAiB,IAAI,IAAI,CAAC;CAC3B;AAED,MAAM,WAAW,YAAY;IAC3B,sBAAsB,EAAE,CACtB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,0BAA0B,KACtE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAC9B;AAED,MAAM,WAAW,eAAe;IAC9B,0BAA0B,CACxB,iBAAiB,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,GAClD,0BAA0B,GAC3B,IAAI,CAAC;IACR,6BAA6B,CAC3B,iBAAiB,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,GAClD,0BAA0B,GAC3B,IAAI,CAAC;CACT;AAED,MAAM,WAAW,oBAAoB;IACnC,YAAY,IAAI,IAAI,CAAC;IACrB,YAAY,IAAI,IAAI,CAAC;IACrB,YAAY,CAAC,SAAS,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC;CAC9E;AAED,MAAM,MAAM,4BAA4B,GAAG;IACzC,UAAU,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,sBAAsB,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;IAC1E,YAAY,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;IAC9B,KAAK,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;IAChC,aAAa,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC;IAC/D,aAAa,CAAC,EAAE,UAAU,CAAC;IAC3B,MAAM,CAAC,EAAE,CACL,oBAAoB,GACpB,uBAAuB,GACvB,OAAO,oBAAoB,CAC9B,GACC,4BAA4B,CAAC;IAC/B,QAAQ,CAAC,EAAE,CACP,oBAAoB,GACpB,OAAO,oBAAoB,GAC3B,0BAA0B,GAC1B,QAAQ,CACX,GACC,4BAA4B,CAAC;IAC/B,OAAO,CAAC,EAAE,CACN,oBAAoB,GACpB,OAAO,oBAAoB,GAC3B,0BAA0B,GAC1B,QAAQ,CACX,GACC,4BAA4B,CAAC;IAC/B,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,qBAAqB,CAAC,EAAE,gBAAgB,CAAC;CAC1C,CAAC;AAEF,MAAM,WAAW,oBAAqB,SAAQ,SAAS;IACrD,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;IAC1D,iBAAiB,CAAC,EAAE,MAAM,oBAAoB,CAAC;IAC/C,gBAAgB,CAAC,EAAE,MAAM,oBAAoB,CAAC;CAC/C;AAED,MAAM,WAAW,0BAA0B;IACzC,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;IAC7B,cAAc,CAAC,EAAE,OAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC;IAChE,cAAc,EAAE,OAAO,CAAC;IACxB,eAAe,EAAE,WAAW,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;IACrD,iBAAiB,EAAE;QAAE,KAAK,EAAE,UAAU,CAAA;KAAE,CAAC;IACzC,aAAa,EAAE,oBAAoB,GAAG,WAAW,GAAG,IAAI,CAAC;IACzD,wBAAwB,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAClD,eAAe,EAAE,eAAe,CAAC;IACjC,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,YAAY,EAAE,YAAY,CAAC;IAC3B,4BAA4B;IAC5B,oBAAoB,CAAC,EAAE,oBAAoB,CAAC;IAC5C,SAAS,CAAC,EAAE,QAAQ,CAAC;IACrB,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,mBAAmB,CAAC,CAAC;IACvD;;;OAGG;IACH,mBAAmB,EAAE,MAAM,MAAM,CAAC;CACnC;AAED,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;AAElD,MAAM,WAAW,qBAAsB,SAAQ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IACpE,GAAG,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;IACrB,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB"}