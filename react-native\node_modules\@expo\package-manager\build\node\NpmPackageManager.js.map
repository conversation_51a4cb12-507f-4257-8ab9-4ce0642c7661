{"version": 3, "file": "NpmPackageManager.js", "sourceRoot": "", "sources": ["../../src/node/NpmPackageManager.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAuC;AACvC,oEAA6D;AAC7D,sEAA4C;AAC5C,gDAAwB;AAExB,6DAA0D;AAC1D,wDAA4E;AAC5E,0CAAyD;AAEzD,MAAa,iBAAkB,SAAQ,uCAAkB;IAC9C,IAAI,GAAG,KAAK,CAAC;IACb,GAAG,GAAG,KAAK,CAAC;IACZ,QAAQ,GAAG,4BAAa,CAAC;IAElC,aAAa;QACX,MAAM,IAAI,GAAG,IAAA,mCAAoB,EAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC;QAC1E,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,IAAI,iBAAiB,CAAC;gBAC3B,GAAG,IAAI,CAAC,OAAO;gBACf,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,GAAG,EAAE,IAAI;aACV,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,eAAyB,EAAE;QAClC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAE/E,OAAO,IAAA,+BAAuB,EAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,cAAc,CAAC,EAC5D,GAAG,EAAE,CACH,CAAC,WAAW,CAAC,MAAM;YACjB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7F,CAAC;IACJ,CAAC;IAED,WAAW,CAAC,eAAyB,EAAE;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAE/E,OAAO,IAAA,+BAAuB,EAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,iBAAiB,CAAC,EAC/D,GAAG,EAAE,CACH,CAAC,WAAW,CAAC,MAAM;YACjB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACZ,SAAS;gBACT,YAAY;gBACZ,GAAG,KAAK;gBACR,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;aACvC,CAAC,CACT,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,eAAyB,EAAE;QACxC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,WAAW,CAAC,YAAsB;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,cAAc,CAAC,YAAsB;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,iBAAiB,CAAC,YAAsB;QACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,WAAW,CAAC,OAAiB,EAAE,UAAwB,EAAE;QACvD,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,OAAO,IAAA,qBAAU,EAAC,KAAK,EAAE,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACK,iBAAiB,CAAC,YAAsB;QAC9C,MAAM,MAAM,GAIR,EAAE,KAAK,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;QAElD,YAAY;aACT,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACZ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAA,yBAAa,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC;aACD,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAChB,uFAAuF;YACvF,yGAAyG;YACzG,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,CAAC;YAC3E,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAChD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;iBAAM,IAAI,IAAI,EAAE,CAAC;gBAChB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;QAEL,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,sBAAsB,CAClC,YAAoC,EACpC,WAA+C;QAE/C,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,GAAG,EAAE,cAAc,CAAC,CAAC;QAC/E,MAAM,GAAG,GACP,MAAM,mBAAQ,CAAC,SAAS,CAA4D,OAAO,CAAC,CAAC;QAE/F,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC1C,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,MAAM,mBAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5D,CAAC;CACF;AA3ID,8CA2IC"}