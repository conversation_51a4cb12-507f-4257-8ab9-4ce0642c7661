const e="-".charCodeAt(0),n="0".charCodeAt(0),t="9".charCodeAt(0),r="A".charCodeAt(0),i="Z".charCodeAt(0),a="a".charCodeAt(0),u="z".charCodeAt(0),o='"'.charCodeAt(0),d=":".charCodeAt(0),s="?".charCodeAt(0),c="*".charCodeAt(0),k="_".charCodeAt(0),f=".".charCodeAt(0),l="\\".charCodeAt(0),h="/".charCodeAt(0),v="+".charCodeAt(0),p="=".charCodeAt(0),C="!".charCodeAt(0),m="#".charCodeAt(0),A="$".charCodeAt(0),g="%".charCodeAt(0),S="&".charCodeAt(0),E="'".charCodeAt(0),P="^".charCodeAt(0),b="`".charCodeAt(0),_="|".charCodeAt(0),w="~".charCodeAt(0),z=32,O=126;function j(e){return void 0!==e&&(e>=n&&e<=t)}function I(e){return void 0!==e&&(e>=r&&e<=i||e>=a&&e<=u)}function L(e){return void 0!==e&&(e>=a&&e<=u)}function M(n){if(void 0===n)return!1;if(j(n)||I(n))return!0;switch(n){case C:case m:case A:case g:case S:case E:case c:case v:case e:case f:case P:case k:case b:case _:case w:return!0;default:return!1}}class N{data;pos;constructor(e){this.data=e,this.pos=0,this.skipSP()}peek(){return this.data[this.pos]}peekCharCode(){return this.pos<this.data.length?this.data.charCodeAt(this.pos):void 0}eat(){++this.pos}skipSP(){for(;" "===this.data[this.pos];)++this.pos}skipOWS(){for(;" "===this.data[this.pos]||"\t"===this.data[this.pos];)++this.pos}atEnd(){return this.pos===this.data.length}allParsed(){return this.skipSP(),this.pos===this.data.length}}function W(e){return"("===e.peek()?function(e){if("("!==e.peek())return{kind:0};e.eat();const n=[];for(;!e.atEnd();){if(e.skipSP(),")"===e.peek()){e.eat();const t=U(e);return 0===t.kind?t:{kind:12,items:n,parameters:t}}const t=x(e);if(0===t.kind)return t;if(n.push(t)," "!==e.peek()&&")"!==e.peek())return{kind:0}}return{kind:0}}(e):x(e)}function x(e){const n=B(e);if(0===n.kind)return n;const t=U(e);return 0===t.kind?t:{kind:4,value:n,parameters:t}}function B(n){const t=n.peekCharCode();return t===e||j(t)?function(e){let n=5,t=1,r="";"-"===e.peek()&&(e.eat(),t=-1);if(!j(e.peekCharCode()))return{kind:0};for(;!e.atEnd();){const t=e.peekCharCode();if(void 0!==t&&j(t))e.eat(),r+=String.fromCodePoint(t);else{if(t!==f||5!==n)break;if(e.eat(),r.length>12)return{kind:0};r+=".",n=6}if(5===n&&r.length>15)return{kind:0};if(6===n&&r.length>16)return{kind:0}}if(5===n){const e=t*Number.parseInt(r,10);return e<-999999999999999||e>999999999999999?{kind:0}:{kind:5,value:e}}const i=r.length-1-r.indexOf(".");if(i>3||0===i)return{kind:0};return{kind:6,value:t*Number.parseFloat(r)}}(n):t===o?function(e){let n="";if('"'!==e.peek())return{kind:0};e.eat();for(;!e.atEnd();){const t=e.peekCharCode();if(void 0===t)return{kind:0};if(e.eat(),t===l){if(e.atEnd())return{kind:0};const t=e.peekCharCode();if(e.eat(),t!==l&&t!==o)return{kind:0};n+=String.fromCodePoint(t)}else{if(t===o)return{kind:7,value:n};if(t<z||t>O)return{kind:0};n+=String.fromCodePoint(t)}}return{kind:0}}(n):t===d?function(e){let n="";if(":"!==e.peek())return{kind:0};e.eat();for(;!e.atEnd();){const t=e.peekCharCode();if(void 0===t)return{kind:0};if(e.eat(),t===d)return{kind:9,value:n};if(!j(t)&&!I(t)&&t!==v&&t!==h&&t!==p)return{kind:0};n+=String.fromCodePoint(t)}return{kind:0}}(n):t===s?function(e){if("?"!==e.peek())return{kind:0};if(e.eat(),"0"===e.peek())return e.eat(),{kind:10,value:!1};if("1"===e.peek())return e.eat(),{kind:10,value:!0};return{kind:0}}(n):t===c||I(t)?function(e){const n=e.peekCharCode();if(n!==c&&!I(n))return{kind:0};let t="";for(;!e.atEnd();){const n=e.peekCharCode();if(void 0===n||!M(n)&&n!==d&&n!==h)break;e.eat(),t+=String.fromCodePoint(n)}return{kind:8,value:t}}(n):{kind:0}}function U(e){const n=new Map;for(;!e.atEnd()&&";"===e.peek();){e.eat(),e.skipSP();const t=F(e);if(0===t.kind)return t;let r={kind:10,value:!0};if("="===e.peek()){e.eat();const n=B(e);if(0===n.kind)return n;r=n}n.has(t.value)&&n.delete(t.value),n.set(t.value,{kind:2,name:t,value:r})}return{kind:3,items:[...n.values()]}}function F(n){let t="";const r=n.peekCharCode();if(r!==c&&!L(r))return{kind:0};for(;!n.atEnd();){const r=n.peekCharCode();if(!L(r)&&!j(r)&&r!==k&&r!==e&&r!==f&&r!==c)break;t+=n.peek(),n.eat()}return{kind:1,value:t}}function H(e){return function(e){const n={kind:11,items:[]};for(;!e.atEnd();){const t=W(e);if(0===t.kind)return t;if(n.items.push(t),e.skipOWS(),e.atEnd())return n;if(","!==e.peek())return{kind:0};if(e.eat(),e.skipOWS(),e.atEnd())return{kind:0}}return n}(new N(e))}function Z(e){const n=G(e.value);if(0===n.kind)return n;const t=y(e.parameters);return 0===t.kind?t:{kind:13,value:n.value+t.value}}function $(e){const n=[];for(let t=0;t<e.items.length;++t){const r=e.items[t];if(12===r.kind){const e=q(r);if(0===e.kind)return e;n.push(e.value)}else{const e=Z(r);if(0===e.kind)return e;n.push(e.value)}}return{kind:13,value:n.join(", ")}}function q(e){const n=[];for(let t=0;t<e.items.length;++t){const r=Z(e.items[t]);if(0===r.kind)return r;n.push(r.value)}let t="("+n.join(" ")+")";const r=y(e.parameters);return 0===r.kind?r:(t+=r.value,{kind:13,value:t})}function y(e){let n="";for(const t of e.items){n+=";";const e=D(t.name);if(0===e.kind)return e;n+=e.value;const r=t.value;if(10!==r.kind||!r.value){n+="=";const e=G(r);if(0===e.kind)return e;n+=e.value}}return{kind:13,value:n}}function D(n){if(0===n.value.length)return{kind:0};const t=n.value.charCodeAt(0);if(!L(t)&&t!==c)return{kind:0};for(let t=1;t<n.value.length;++t){const r=n.value.charCodeAt(t);if(!L(r)&&!j(r)&&r!==k&&r!==e&&r!==f&&r!==c)return{kind:0}}return{kind:13,value:n.value}}function G(e){return 5===e.kind?function(e){if(e.value<-999999999999999||e.value>999999999999999||!Number.isInteger(e.value))return{kind:0};return{kind:13,value:e.value.toString(10)}}(e):6===e.kind?function(e){throw"Unimplemented"}():7===e.kind?function(e){for(let n=0;n<e.value.length;++n){const t=e.value.charCodeAt(n);if(t<z||t>O)return{kind:0}}let n='"';for(let t=0;t<e.value.length;++t){const r=e.value[t];'"'!==r&&"\\"!==r||(n+="\\"),n+=r}return n+='"',{kind:13,value:n}}(e):8===e.kind?function(e){if(0===e.value.length)return{kind:0};const n=e.value.charCodeAt(0);if(!I(n)&&n!==c)return{kind:0};for(let n=1;n<e.value.length;++n){const t=e.value.charCodeAt(n);if(!M(t)&&t!==d&&t!==h)return{kind:0}}return{kind:13,value:e.value}}(e):10===e.kind?function(e){return{kind:13,value:e.value?"?1":"?0"}}(e):9===e.kind?function(e){throw"Unimplemented"}():{kind:0}}var J=Object.freeze({__proto__:null,parseItem:function(e){const n=new N(e),t=x(n);return n.allParsed()?t:{kind:0}},parseList:H,serializeItem:Z,serializeList:$});var K=Object.freeze({__proto__:null,parseBrandsList:function(e,n,t){const r=[],i=H(e);if(0===i.kind)return n;for(const e of i.items){if(4!==e.kind)return t;const n=e.value;if(7!==n.kind)return t;if(1!==e.parameters.items.length)return t;const i=e.parameters.items[0];if("v"!==i.name.value)return t;const a=i.value;if(7!==a.kind)return t;r.push({brand:n.value,version:a.value})}return r},serializeBrandsList:function(e){const n={kind:11,items:[]},t={kind:1,value:"v"};for(const r of e){const e={kind:4,value:{kind:7,value:r.brand},parameters:{kind:3,items:[{kind:2,name:t,value:{kind:7,value:r.version}}]}};n.items.push(e)}const r=$(n);return 0===r.kind?"":r.value},validateAsStructuredHeadersString:function(e,n){return 0===Z({kind:4,value:{kind:7,value:e},parameters:{kind:3,items:[]}}).kind?{valid:!1,errorMessage:n}:{valid:!0,errorMessage:void 0}}});export{J as StructuredHeaders,K as UserAgentMetadata};
