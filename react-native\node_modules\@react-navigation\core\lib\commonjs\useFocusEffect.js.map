{"version": 3, "names": ["useFocusEffect", "effect", "navigation", "useNavigation", "arguments", "undefined", "message", "console", "error", "React", "useEffect", "isFocused", "cleanup", "callback", "destroy", "process", "env", "NODE_ENV", "then", "JSON", "stringify", "unsubscribeFocus", "addListener", "unsubscribeBlur"], "sourceRoot": "../../src", "sources": ["useFocusEffect.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAA4C;AAAA;AAAA;AAI5C;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,cAAc,CAACC,MAAsB,EAAE;EAC7D,MAAMC,UAAU,GAAG,IAAAC,sBAAa,GAAE;EAElC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IAC9B,MAAMC,OAAO,GACX,sFAAsF,GACtF,8EAA8E,GAC9E,mBAAmB,GACnB,+BAA+B,GAC/B,yBAAyB,GACzB,sBAAsB,GACtB,QAAQ,GACR,oEAAoE;IAEtEC,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;EACxB;EAEAG,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,OAAwC;IAE5C,MAAMC,QAAQ,GAAG,MAAM;MACrB,MAAMC,OAAO,GAAGb,MAAM,EAAE;MAExB,IAAIa,OAAO,KAAKT,SAAS,IAAI,OAAOS,OAAO,KAAK,UAAU,EAAE;QAC1D,OAAOA,OAAO;MAChB;MAEA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIX,OAAO,GACT,6FAA6F;QAE/F,IAAIQ,OAAO,KAAK,IAAI,EAAE;UACpBR,OAAO,IACL,kGAAkG;QACtG,CAAC,MAAM,IAAI,OAAQQ,OAAO,CAASI,IAAI,KAAK,UAAU,EAAE;UACtDZ,OAAO,IACL,uFAAuF,GACvF,uDAAuD,GACvD,8BAA8B,GAC9B,mBAAmB,GACnB,+BAA+B,GAC/B,oCAAoC,GACpC,+BAA+B,GAC/B,uDAAuD,GACvD,gBAAgB,GAChB,WAAW,GACX,oBAAoB,GACpB,kBAAkB,GAClB,QAAQ,GACR,oEAAoE;QACxE,CAAC,MAAM;UACLA,OAAO,IAAK,kBAAiBa,IAAI,CAACC,SAAS,CAACN,OAAO,CAAE,IAAG;QAC1D;QAEAP,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;MACxB;IACF,CAAC;;IAED;IACA,IAAIJ,UAAU,CAACS,SAAS,EAAE,EAAE;MAC1BC,OAAO,GAAGC,QAAQ,EAAE;MACpBF,SAAS,GAAG,IAAI;IAClB;IAEA,MAAMU,gBAAgB,GAAGnB,UAAU,CAACoB,WAAW,CAAC,OAAO,EAAE,MAAM;MAC7D;MACA;MACA,IAAIX,SAAS,EAAE;QACb;MACF;MAEA,IAAIC,OAAO,KAAKP,SAAS,EAAE;QACzBO,OAAO,EAAE;MACX;MAEAA,OAAO,GAAGC,QAAQ,EAAE;MACpBF,SAAS,GAAG,IAAI;IAClB,CAAC,CAAC;IAEF,MAAMY,eAAe,GAAGrB,UAAU,CAACoB,WAAW,CAAC,MAAM,EAAE,MAAM;MAC3D,IAAIV,OAAO,KAAKP,SAAS,EAAE;QACzBO,OAAO,EAAE;MACX;MAEAA,OAAO,GAAGP,SAAS;MACnBM,SAAS,GAAG,KAAK;IACnB,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAIC,OAAO,KAAKP,SAAS,EAAE;QACzBO,OAAO,EAAE;MACX;MAEAS,gBAAgB,EAAE;MAClBE,eAAe,EAAE;IACnB,CAAC;EACH,CAAC,EAAE,CAACtB,MAAM,EAAEC,UAAU,CAAC,CAAC;AAC1B"}