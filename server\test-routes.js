// Script de test pour vérifier toutes les routes de l'architecture modulaire
const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

// Fonction utilitaire pour tester une route
async function testRoute(method, endpoint, data = null, description = '') {
  try {
    console.log(`\n🧪 Test: ${description || `${method} ${endpoint}`}`);
    
    let response;
    if (method === 'GET') {
      response = await axios.get(`${BASE_URL}${endpoint}`);
    } else if (method === 'POST') {
      response = await axios.post(`${BASE_URL}${endpoint}`, data);
    } else if (method === 'PUT') {
      response = await axios.put(`${BASE_URL}${endpoint}`, data);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${BASE_URL}${endpoint}`);
    }
    
    console.log(`✅ Succès: ${response.status} - ${response.data.message || 'OK'}`);
    if (response.data.count !== undefined) {
      console.log(`📊 Données: ${response.data.count} enregistrement(s)`);
    }
    return response.data;
  } catch (error) {
    console.log(`❌ Erreur: ${error.response?.status || 'Network'} - ${error.response?.data?.message || error.message}`);
    return null;
  }
}

// Fonction principale de test
async function runTests() {
  console.log('🚀 Démarrage des tests de l\'architecture modulaire\n');
  console.log('=' .repeat(60));

  // Test de la route principale
  await testRoute('GET', '/', null, 'Route principale - Test de fonctionnement');

  // Tests des routes génériques
  console.log('\n📋 ROUTES GÉNÉRIQUES');
  console.log('-'.repeat(40));
  await testRoute('GET', '/api/database/schema', null, 'Schéma de la base de données');
  await testRoute('GET', '/api/dashboard/stats', null, 'Statistiques du dashboard');

  // Tests du module clients
  console.log('\n👥 MODULE CLIENTS');
  console.log('-'.repeat(40));
  await testRoute('GET', '/api/clients', null, 'Liste des clients');

  // Tests du module consommation
  console.log('\n💧 MODULE CONSOMMATION');
  console.log('-'.repeat(40));
  await testRoute('GET', '/api/consommation', null, 'Liste des consommations');
  await testRoute('GET', '/api/contracts', null, 'Liste des contrats');
  await testRoute('GET', '/api/last-consommation-global', null, 'Dernière consommation globale');

  // Tests du module factures
  console.log('\n🧾 MODULE FACTURES');
  console.log('-'.repeat(40));
  await testRoute('GET', '/api/factures', null, 'Liste des factures');
  await testRoute('GET', '/api/factures/stats/summary', null, 'Statistiques des factures');
  await testRoute('GET', '/api/factures/status/toutes', null, 'Factures par statut');

  // Tests du module QR
  console.log('\n📱 MODULE QR CODE');
  console.log('-'.repeat(40));
  await testRoute('POST', '/api/validate-qr', { qrCode: '1' }, 'Validation QR code');

  // Tests du module auth
  console.log('\n🔐 MODULE AUTHENTIFICATION');
  console.log('-'.repeat(40));
  await testRoute('GET', '/api/users', null, 'Liste des utilisateurs');

  // Test de connexion
  await testRoute('POST', '/login', {
    email: '<EMAIL>',
    motDepass: 'Tech123'
  }, 'Test de connexion technicien');

  console.log('\n' + '='.repeat(60));
  console.log('🎉 Tests terminés !');
  console.log('\n💡 Vérifiez les résultats ci-dessus pour identifier d\'éventuels problèmes.');
  console.log('✅ Les routes qui retournent un statut 200 fonctionnent correctement.');
  console.log('❌ Les routes en erreur nécessitent une vérification.');
}

// Exécuter les tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testRoute, runTests };
