{"version": 3, "file": "StatusBar.d.ts", "sourceRoot": "", "sources": ["../src/StatusBar.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAS1B,MAAM,MAAM,cAAc,GAAG,MAAM,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,CAAC;AAGpE,MAAM,MAAM,kBAAkB,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;AAG3D,MAAM,MAAM,cAAc,GAAG;IAC3B;;;;;OAKG;IACH,KAAK,CAAC,EAAE,cAAc,CAAC;IAEvB;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;OAKG;IACH,uBAAuB,CAAC,EAAE,kBAAkB,CAAC;IAE7C;;;OAGG;IACH,+BAA+B,CAAC,EAAE,OAAO,CAAC;IAE1C;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;;;OAKG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB,CAAC;AAEF;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,EACxB,KAAK,EACL,uBAAuB,EACvB,WAAkB,EAClB,eAAe,EAAE,mBAAmB,EACpC,GAAG,KAAK,EACT,EAAE,cAAc,qBAsBhB;AAGD;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,CAAC,EAAE,OAAO,QAE1E;AAGD;;;;GAIG;AACH,eAAO,MAAM,kBAAkB,8FAA4B,CAAC;AAG5D;;;;;GAKG;AACH,eAAO,MAAM,2BAA2B,oFAAqC,CAAC;AAG9E;;;;GAIG;AACH,eAAO,MAAM,2CAA2C,4BACJ,CAAC;AAGrD;;;;;GAKG;AACH,eAAO,MAAM,uBAAuB,gCAAiC,CAAC"}