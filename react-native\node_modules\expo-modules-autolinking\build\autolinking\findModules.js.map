{"version": 3, "file": "findModules.js", "sourceRoot": "", "sources": ["../../src/autolinking/findModules.ts"], "names": [], "mappings": ";;;;;AAiBA,4CAuEC;AAxFD,kDAA0B;AAC1B,4CAAoB;AACpB,+BAA4B;AAC5B,mCAAuC;AACvC,gDAAwB;AAExB,+DAAiG;AACjG,mCAAiD;AACjD,0DAAwE;AAGxE,8DAA8D;AAC9D,MAAM,4BAA4B,GAAG,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,CAAC;AAEnF;;GAEG;AACI,KAAK,UAAU,gBAAgB,CAAC,eAA8B;IACnE,MAAM,OAAO,GAAG,MAAM,IAAA,8CAAwB,EAAC,eAAe,CAAC,CAAC;IAChE,MAAM,OAAO,GAAiC,IAAI,GAAG,EAAE,CAAC;IAExD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE5C,yFAAyF;IACzF,MAAM,WAAW,GAAG,IAAI,GAAG,CACzB,OAAO,CAAC,gBAAgB,IAAI,YAAE,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC;QACjE,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC;QACpD,CAAC,CAAC,OAAO,CAAC,WAAW,CACxB,CAAC;IAEF,sGAAsG;IACtG,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACrC,MAAM,kBAAkB,GAAG,UAAU,KAAK,OAAO,CAAC,gBAAgB,CAAC;QAEnE,MAAM,kBAAkB,GAAG,MAAM,4BAA4B,CAAC,UAAU,CAAC,CAAC;QAE1E,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;YACnD,MAAM,WAAW,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAC5C,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CACvD,CAAC;YACF,MAAM,gBAAgB,GAAG,IAAA,oDAAiC,EACxD,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CACzD,CAAC;YAEF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,4BAA4B,CAAC,WAAW,EAAE;gBAClE,iBAAiB,EAAE,kBAAkB;aACtC,CAAC,CAAC;YAEH,MAAM,wBAAwB,GAAG,IAAA,8BAAsB,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC3E,IAAI,wBAAwB,EAAE,CAAC;gBAC7B,WAAW,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5F,SAAS;YACX,CAAC;YAED,0CAA0C;YAC1C,MAAM,eAAe,GAAoB;gBACvC,IAAI,EAAE,WAAW;gBACjB,OAAO;gBACP,MAAM,EAAE,gBAAgB;aACzB,CAAC;YACF,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YAErD,mFAAmF;YACnF,IAAI,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvD,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,aAAa,GAAkB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IAE3E,gFAAgF;IAChF,yCAAyC;IACzC,6EAA6E;IAC7E,6CAA6C;IAC7C,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;QACzE,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,OAAO,MAAM,gCAAgC,CAAC,aAAa,EAAE;QAC3D,GAAG,eAAe;QAClB,6CAA6C;QAC7C,2DAA2D;QAC3D,2BAA2B,EAAE,iBAAiB;KAC/C,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,QAAgB;IACtC,OAAO,4BAA4B,CAAC,OAAO,CAAC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,oBAAoB,CAC3B,OAAqC,EACrC,IAAY,EACZ,QAAyB;IAEzB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACvB,0DAA0D;QAC1D,8DAA8D;QAC9D,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;YAChB,GAAG,QAAQ;YACX,UAAU,EAAE,EAAE;SACf,CAAC,CAAC;IACL,CAAC;SAAM,IACL,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,KAAK,QAAQ,CAAC,IAAI;QACzC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,EAC1E,CAAC;QACD,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc,EAAE,GAAG,QAAQ,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,KAAK,UAAU,4BAA4B,CAAC,UAAkB;IAC5D,MAAM,eAAe,GAAG,GAAG,GAAG,4BAA4B,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC3E,MAAM,KAAK,GAAG,MAAM,IAAA,WAAI,EACtB,CAAC,KAAK,eAAe,EAAE,EAAE,QAAQ,eAAe,EAAE,EAAE,KAAK,eAAe,EAAE,CAAC,EAC3E;QACE,GAAG,EAAE,UAAU;KAChB,CACF,CAAC;IAEF,uHAAuH;IACvH,+DAA+D;IAC/D,OAAO,MAAM,CAAC,MAAM,CAClB,KAAK,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;QACvD,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEzC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YAC/E,GAAG,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC;QAC5B,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CACP,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,4BAA4B,CACnC,WAAmB,EACnB,EAAE,iBAAiB,KAAsC,EAAE;IAE3D,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;QAC1E,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,CAAC;IACrD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,iBAAiB,EAAE,CAAC;YACtB,uEAAuE;YACvE,OAAO;gBACL,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAChC,OAAO,EAAE,aAAa;aACvB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gCAAgC,CAC7C,OAAsB,EACtB,OAEC;IAED,MAAM,eAAe,GAAkB,EAAE,CAAC;IAC1C,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IAE1C,qFAAqF;IACrF,+BAA+B;IAC/B,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,2BAA2B,IAAI,EAAE,EAAE,CAAC;QAC7D,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,eAAe,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,kDAAkD;IAClD,SAAS,YAAY,CAAC,eAAuB;QAC3C,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;QAE7C,2CAA2C;QAC3C,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QACD,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEtC,4DAA4D;QAC5D,KAAK,MAAM,cAAc,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YACtD,MAAM,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;YAEjD,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,IAAI,yBAAiC,CAAC;gBAEtC,IAAI,gBAAgB,EAAE,CAAC;oBACrB,eAAe,CAAC,cAAc,CAAC,GAAG,gBAAgB,CAAC;oBACnD,yBAAyB,GAAG,cAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBAC/E,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH;;;2BAGG;wBACH,MAAM,cAAc,GAAG,IAAA,sBAAa,EAAC,eAAe,CAAC,CAAC;wBACtD,yBAAyB,GAAG,cAAc,CAAC,OAAO,CAAC,GAAG,cAAc,eAAe,CAAC,CAAC;oBACvF,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,mEAAmE;wBACnE,wFAAwF;wBACxF,2EAA2E;wBAC3E,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;4BACtE,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CAAC,mCAAmC,cAAc,YAAY,CAAC,CAC5E,CAAC;wBACJ,CAAC;wBACD,SAAS;oBACX,CAAC;gBACH,CAAC;gBAED,gCAAgC;gBAChC,YAAY,CAAC,yBAAyB,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,MAAM,sBAAsB,GAAG,MAAM,IAAA,oDAA8B,EAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACzF,YAAY,CAAC,sBAAsB,CAAC,CAAC;IAErC,OAAO,eAAe,CAAC;AACzB,CAAC", "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport { glob } from 'glob';\nimport { createRequire } from 'module';\nimport path from 'path';\n\nimport { getProjectPackageJsonPathAsync, mergeLinkingOptionsAsync } from './mergeLinkingOptions';\nimport { getIsolatedModulesPath } from './utils';\nimport { requireAndResolveExpoModuleConfig } from '../ExpoModuleConfig';\nimport { PackageRevision, SearchOptions, SearchResults } from '../types';\n\n// Names of the config files. From lowest to highest priority.\nconst EXPO_MODULE_CONFIG_FILENAMES = ['unimodule.json', 'expo-module.config.json'];\n\n/**\n * Searches for modules to link based on given config.\n */\nexport async function findModulesAsync(providedOptions: SearchOptions): Promise<SearchResults> {\n  const options = await mergeLinkingOptionsAsync(providedOptions);\n  const results: Map<string, PackageRevision> = new Map();\n\n  const nativeModuleNames = new Set<string>();\n\n  // custom native modules should be resolved first so that they can override other modules\n  const searchPaths = new Set(\n    options.nativeModulesDir && fs.existsSync(options.nativeModulesDir)\n      ? [options.nativeModulesDir, ...options.searchPaths]\n      : options.searchPaths\n  );\n\n  // `searchPaths` can be mutated to discover all \"isolated modules groups\", when using isolated modules\n  for (const searchPath of searchPaths) {\n    const isNativeModulesDir = searchPath === options.nativeModulesDir;\n\n    const packageConfigPaths = await findPackagesConfigPathsAsync(searchPath);\n\n    for (const packageConfigPath of packageConfigPaths) {\n      const packagePath = await fs.promises.realpath(\n        path.join(searchPath, path.dirname(packageConfigPath))\n      );\n      const expoModuleConfig = requireAndResolveExpoModuleConfig(\n        path.join(packagePath, path.basename(packageConfigPath))\n      );\n\n      const { name, version } = resolvePackageNameAndVersion(packagePath, {\n        fallbackToDirName: isNativeModulesDir,\n      });\n\n      const maybeIsolatedModulesPath = getIsolatedModulesPath(packagePath, name);\n      if (maybeIsolatedModulesPath) {\n        searchPaths.add(maybeIsolatedModulesPath);\n      }\n\n      if (options.exclude?.includes(name) || !expoModuleConfig.supportsPlatform(options.platform)) {\n        continue;\n      }\n\n      // add the current revision to the results\n      const currentRevision: PackageRevision = {\n        path: packagePath,\n        version,\n        config: expoModuleConfig,\n      };\n      addRevisionToResults(results, name, currentRevision);\n\n      // if the module is a native module, we need to add it to the nativeModuleNames set\n      if (isNativeModulesDir && !nativeModuleNames.has(name)) {\n        nativeModuleNames.add(name);\n      }\n    }\n  }\n\n  const searchResults: SearchResults = Object.fromEntries(results.entries());\n\n  // It doesn't make much sense to strip modules if there is only one search path.\n  // (excluding custom native modules path)\n  // Workspace root usually doesn't specify all its dependencies (see Expo Go),\n  // so in this case we should link everything.\n  if (options.searchPaths.length <= 1 || options.onlyProjectDeps === false) {\n    return searchResults;\n  }\n\n  return await filterToProjectDependenciesAsync(searchResults, {\n    ...providedOptions,\n    // Custom native modules are not filtered out\n    // when they're not specified in package.json dependencies.\n    alwaysIncludedPackagesNames: nativeModuleNames,\n  });\n}\n\n/**\n * Returns the priority of the config at given path. Higher number means higher priority.\n */\nfunction configPriority(fullpath: string): number {\n  return EXPO_MODULE_CONFIG_FILENAMES.indexOf(path.basename(fullpath));\n}\n\n/**\n * Adds {@link revision} to the {@link results} map\n * or to package duplicates if it already exists.\n * @param results [mutable] yet resolved packages map\n * @param name resolved package name\n * @param revision resolved package revision\n */\nfunction addRevisionToResults(\n  results: Map<string, PackageRevision>,\n  name: string,\n  revision: PackageRevision\n): void {\n  if (!results.has(name)) {\n    // The revision that was found first will be the main one.\n    // An array of duplicates and the config are needed only here.\n    results.set(name, {\n      ...revision,\n      duplicates: [],\n    });\n  } else if (\n    results.get(name)?.path !== revision.path &&\n    results.get(name)?.duplicates?.every(({ path }) => path !== revision.path)\n  ) {\n    const { config, duplicates, ...duplicateEntry } = revision;\n    results.get(name)?.duplicates?.push(duplicateEntry);\n  }\n}\n\n/**\n * Returns paths to the highest priority config files, relative to the {@link searchPath}.\n * @example\n * ```\n * // Given the following file exists: /foo/myapp/modules/mymodule/expo-module.config.json\n * await findPackagesConfigPathsAsync('/foo/myapp/modules');\n * // returns ['mymodule/expo-module.config.json']\n *\n * await findPackagesConfigPathsAsync('/foo/myapp/modules/mymodule');\n * // returns ['expo-module.config.json']\n * ```\n */\nasync function findPackagesConfigPathsAsync(searchPath: string): Promise<string[]> {\n  const bracedFilenames = '{' + EXPO_MODULE_CONFIG_FILENAMES.join(',') + '}';\n  const paths = await glob(\n    [`*/${bracedFilenames}`, `@*/*/${bracedFilenames}`, `./${bracedFilenames}`],\n    {\n      cwd: searchPath,\n    }\n  );\n\n  // If the package has multiple configs (e.g. `unimodule.json` and `expo-module.config.json` during the transition time)\n  // then we want to give `expo-module.config.json` the priority.\n  return Object.values(\n    paths.reduce<Record<string, string>>((acc, configPath) => {\n      const dirname = path.dirname(configPath);\n\n      if (!acc[dirname] || configPriority(configPath) > configPriority(acc[dirname])) {\n        acc[dirname] = configPath;\n      }\n      return acc;\n    }, {})\n  );\n}\n\n/**\n * Resolves package name and version for the given {@link packagePath} from its `package.json`.\n * if {@link fallbackToDirName} is true, it returns the dir name when `package.json` doesn't exist.\n * @returns object with `name` and `version` properties. `version` falls back to `UNVERSIONED` if cannot be resolved.\n */\nfunction resolvePackageNameAndVersion(\n  packagePath: string,\n  { fallbackToDirName }: { fallbackToDirName?: boolean } = {}\n): { name: string; version: string } {\n  try {\n    const { name, version } = require(path.join(packagePath, 'package.json'));\n    return { name, version: version || 'UNVERSIONED' };\n  } catch (e) {\n    if (fallbackToDirName) {\n      // we don't have the package.json name, so we'll use the directory name\n      return {\n        name: path.basename(packagePath),\n        version: 'UNVERSIONED',\n      };\n    } else {\n      throw e;\n    }\n  }\n}\n\n/**\n * Filters out packages that are not the dependencies of the project.\n */\nasync function filterToProjectDependenciesAsync(\n  results: SearchResults,\n  options: Pick<SearchOptions, 'projectRoot' | 'silent'> & {\n    alwaysIncludedPackagesNames?: Set<string>;\n  }\n): Promise<SearchResults> {\n  const filteredResults: SearchResults = {};\n  const visitedPackages = new Set<string>();\n\n  // iterate through always included package names and add them to the visited packages\n  // if the results contains them\n  for (const name of options.alwaysIncludedPackagesNames ?? []) {\n    if (results[name] && !visitedPackages.has(name)) {\n      filteredResults[name] = results[name];\n      visitedPackages.add(name);\n    }\n  }\n\n  // Helper for traversing the dependency hierarchy.\n  function visitPackage(packageJsonPath: string) {\n    const packageJson = require(packageJsonPath);\n\n    // Prevent getting into the recursive loop.\n    if (visitedPackages.has(packageJson.name)) {\n      return;\n    }\n    visitedPackages.add(packageJson.name);\n\n    // Iterate over the dependencies to find transitive modules.\n    for (const dependencyName in packageJson.dependencies) {\n      const dependencyResult = results[dependencyName];\n\n      if (!filteredResults[dependencyName]) {\n        let dependencyPackageJsonPath: string;\n\n        if (dependencyResult) {\n          filteredResults[dependencyName] = dependencyResult;\n          dependencyPackageJsonPath = path.join(dependencyResult.path, 'package.json');\n        } else {\n          try {\n            /**\n             * Custom `require` that resolves from the current working dir instead of this script path.\n             * **Requires Node v12.2.0**\n             */\n            const projectRequire = createRequire(packageJsonPath);\n            dependencyPackageJsonPath = projectRequire.resolve(`${dependencyName}/package.json`);\n          } catch (error: any) {\n            // Some packages don't include package.json in its `exports` field,\n            // but none of our packages do that, so it seems fine to just ignore that type of error.\n            // Related issue: https://github.com/react-native-community/cli/issues/1168\n            if (!options.silent && error.code !== 'ERR_PACKAGE_PATH_NOT_EXPORTED') {\n              console.warn(\n                chalk.yellow(`⚠️  Cannot resolve the path to \"${dependencyName}\" package.`)\n              );\n            }\n            continue;\n          }\n        }\n\n        // Visit the dependency package.\n        visitPackage(dependencyPackageJsonPath);\n      }\n    }\n  }\n\n  // Visit project's package.\n  const projectPackageJsonPath = await getProjectPackageJsonPathAsync(options.projectRoot);\n  visitPackage(projectPackageJsonPath);\n\n  return filteredResults;\n}\n"]}