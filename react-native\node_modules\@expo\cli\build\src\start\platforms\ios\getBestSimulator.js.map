{"version": 3, "sources": ["../../../../../src/start/platforms/ios/getBestSimulator.ts"], "sourcesContent": ["import { execSync } from 'child_process';\n\nimport * as SimControl from './simctl';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:ios:getBestSimulator') as typeof console.log;\n\ntype DeviceContext = Partial<Pick<SimControl.Device, 'osType'>>;\n\n/**\n * Returns the default device stored in the Simulator.app settings.\n * This helps us to get the device that the user opened most recently regardless of which tool they used.\n */\nfunction getDefaultSimulatorDeviceUDID() {\n  try {\n    const defaultDeviceUDID = execSync(\n      `defaults read com.apple.iphonesimulator CurrentDeviceUDID`,\n      { stdio: 'pipe' }\n    ).toString();\n    return defaultDeviceUDID.trim();\n  } catch {\n    return null;\n  }\n}\n\nexport async function getBestBootedSimulatorAsync({\n  osType,\n}: DeviceContext = {}): Promise<SimControl.Device | null> {\n  const [simulatorOpenedByApp] = await SimControl.getBootedSimulatorsAsync();\n  // This should prevent opening a second simulator in the chance that default\n  // simulator doesn't match what the Simulator app would open by default.\n  if (\n    simulatorOpenedByApp?.udid &&\n    (!osType || (osType && simulatorOpenedByApp.osType === osType))\n  ) {\n    debug(`First booted simulator: ${simulatorOpenedByApp?.windowName}`);\n    return simulatorOpenedByApp;\n  }\n\n  debug(`No booted simulator matching requirements (osType: ${osType}).`);\n  return null;\n}\n\n/**\n * Returns the most preferred simulator UDID without booting anything.\n *\n * 1. If the simulator app defines a default simulator and the osType is not defined.\n * 2. If the osType is defined, then check if the default udid matches the osType.\n * 3. If all else fails, return the first found simulator.\n */\nexport async function getBestUnbootedSimulatorAsync({ osType }: DeviceContext = {}): Promise<\n  string | null\n> {\n  const defaultId = getDefaultSimulatorDeviceUDID();\n  debug(`Default simulator ID: ${defaultId}`);\n\n  if (defaultId && !osType) {\n    return defaultId;\n  }\n\n  const simulators = await getSelectableSimulatorsAsync({ osType });\n\n  if (!simulators.length) {\n    // TODO: Prompt to install the simulators\n    throw new CommandError(\n      'UNSUPPORTED_OS_TYPE',\n      `No ${osType || 'iOS'} devices available in Simulator.app`\n    );\n  }\n\n  // If the default udid is defined, then check to ensure its osType matches the required os.\n  if (defaultId) {\n    const defaultSimulator = simulators.find((device) => device.udid === defaultId);\n    if (defaultSimulator?.osType === osType) {\n      return defaultId;\n    }\n  }\n\n  // Return first selectable device.\n  return simulators[0]?.udid ?? null;\n}\n\n/**\n * Get all simulators supported by Expo Go (iOS only).\n */\nexport async function getSelectableSimulatorsAsync({ osType = 'iOS' }: DeviceContext = {}): Promise<\n  SimControl.Device[]\n> {\n  const simulators = await SimControl.getDevicesAsync();\n  return simulators.filter((device) => device.isAvailable && device.osType === osType);\n}\n\n/**\n * Get 'best' simulator for the user based on:\n * 1. Currently booted simulator.\n * 2. Last simulator that was opened.\n * 3. First simulator that was opened.\n */\nexport async function getBestSimulatorAsync({ osType }: DeviceContext): Promise<string | null> {\n  const simulatorOpenedByApp = await getBestBootedSimulatorAsync({ osType });\n\n  if (simulatorOpenedByApp) {\n    return simulatorOpenedByApp.udid;\n  }\n\n  return await getBestUnbootedSimulatorAsync({ osType });\n}\n"], "names": ["getBestBootedSimulatorAsync", "getBestSimulatorAsync", "getBestUnbootedSimulatorAsync", "getSelectableSimulatorsAsync", "debug", "require", "getDefaultSimulatorDeviceUDID", "defaultDeviceUDID", "execSync", "stdio", "toString", "trim", "osType", "simulatorOpenedByApp", "SimControl", "getBootedSimulatorsAsync", "udid", "windowName", "simulators", "defaultId", "length", "CommandError", "defaultSimulator", "find", "device", "getDevicesAsync", "filter", "isAvailable"], "mappings": ";;;;;;;;;;;IAyBsBA,2BAA2B;eAA3BA;;IAyEAC,qBAAqB;eAArBA;;IAhDAC,6BAA6B;eAA7BA;;IAmCAC,4BAA4B;eAA5BA;;;;yBArFG;;;;;;gEAEG;wBACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SAAS;AAI/B;;;CAGC,GACD,SAASC;IACP,IAAI;QACF,MAAMC,oBAAoBC,IAAAA,yBAAQ,EAChC,CAAC,yDAAyD,CAAC,EAC3D;YAAEC,OAAO;QAAO,GAChBC,QAAQ;QACV,OAAOH,kBAAkBI,IAAI;IAC/B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,eAAeX,4BAA4B,EAChDY,MAAM,EACQ,GAAG,CAAC,CAAC;IACnB,MAAM,CAACC,qBAAqB,GAAG,MAAMC,QAAWC,wBAAwB;IACxE,4EAA4E;IAC5E,wEAAwE;IACxE,IACEF,CAAAA,wCAAAA,qBAAsBG,IAAI,KACzB,CAAA,CAACJ,UAAWA,UAAUC,qBAAqBD,MAAM,KAAKA,MAAM,GAC7D;QACAR,MAAM,CAAC,wBAAwB,EAAES,wCAAAA,qBAAsBI,UAAU,EAAE;QACnE,OAAOJ;IACT;IAEAT,MAAM,CAAC,mDAAmD,EAAEQ,OAAO,EAAE,CAAC;IACtE,OAAO;AACT;AASO,eAAeV,8BAA8B,EAAEU,MAAM,EAAiB,GAAG,CAAC,CAAC;QA6BzEM;IA1BP,MAAMC,YAAYb;IAClBF,MAAM,CAAC,sBAAsB,EAAEe,WAAW;IAE1C,IAAIA,aAAa,CAACP,QAAQ;QACxB,OAAOO;IACT;IAEA,MAAMD,aAAa,MAAMf,6BAA6B;QAAES;IAAO;IAE/D,IAAI,CAACM,WAAWE,MAAM,EAAE;QACtB,yCAAyC;QACzC,MAAM,IAAIC,oBAAY,CACpB,uBACA,CAAC,GAAG,EAAET,UAAU,MAAM,mCAAmC,CAAC;IAE9D;IAEA,2FAA2F;IAC3F,IAAIO,WAAW;QACb,MAAMG,mBAAmBJ,WAAWK,IAAI,CAAC,CAACC,SAAWA,OAAOR,IAAI,KAAKG;QACrE,IAAIG,CAAAA,oCAAAA,iBAAkBV,MAAM,MAAKA,QAAQ;YACvC,OAAOO;QACT;IACF;IAEA,kCAAkC;IAClC,OAAOD,EAAAA,eAAAA,UAAU,CAAC,EAAE,qBAAbA,aAAeF,IAAI,KAAI;AAChC;AAKO,eAAeb,6BAA6B,EAAES,SAAS,KAAK,EAAiB,GAAG,CAAC,CAAC;IAGvF,MAAMM,aAAa,MAAMJ,QAAWW,eAAe;IACnD,OAAOP,WAAWQ,MAAM,CAAC,CAACF,SAAWA,OAAOG,WAAW,IAAIH,OAAOZ,MAAM,KAAKA;AAC/E;AAQO,eAAeX,sBAAsB,EAAEW,MAAM,EAAiB;IACnE,MAAMC,uBAAuB,MAAMb,4BAA4B;QAAEY;IAAO;IAExE,IAAIC,sBAAsB;QACxB,OAAOA,qBAAqBG,IAAI;IAClC;IAEA,OAAO,MAAMd,8BAA8B;QAAEU;IAAO;AACtD"}