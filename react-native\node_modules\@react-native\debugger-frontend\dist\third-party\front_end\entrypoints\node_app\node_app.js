import"../shell/shell.js";import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as n from"../../ui/legacy/legacy.js";import*as o from"../../core/root/root.js";import*as i from"../main/main.js";import*as s from"../../core/host/host.js";import"../../ui/components/buttons/buttons.js";import*as r from"../../ui/legacy/components/utils/utils.js";import*as a from"../../core/sdk/sdk.js";const c={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},d=t.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",c),l=t.i18n.getLazilyComputedLocalizedString.bind(void 0,d);let g;async function h(){return g||(g=await import("../../panels/mobile_throttling/mobile_throttling.js")),g}n.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:l(c.throttling),commandPrompt:l(c.showThrottling),order:35,loadView:async()=>new((await h()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:l(c.goOffline),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:l(c.enableSlowGThrottling),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:l(c.enableFastGThrottling),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),n.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:l(c.goOnline),loadActionDelegate:async()=>new((await h()).ThrottlingManager.ActionDelegate),tags:[l(c.device),l(c.throttlingTag)]}),e.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const p={performance:"Performance",showPerformance:"Show Performance",showRecentTimelineSessions:"Show recent timeline sessions",record:"Record",stop:"Stop",recordAndReload:"Record and reload"},w=t.i18n.registerUIStrings("panels/js_timeline/js_timeline-meta.ts",p),m=t.i18n.getLazilyComputedLocalizedString.bind(void 0,w);let v;async function f(){return v||(v=await import("../../panels/timeline/timeline.js")),v}function u(e){return void 0===v?[]:e(v)}n.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:m(p.performance),commandPrompt:m(p.showPerformance),order:66,hasToolbar:!1,isPreviewFeature:!0,loadView:async()=>(await f()).TimelinePanel.TimelinePanel.instance({forceNew:null,isNode:!0})}),n.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await f()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:m(p.showRecentTimelineSessions),contextTypes:()=>u((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>u((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await f()).TimelinePanel.ActionDelegate),options:[{value:!0,title:m(p.record)},{value:!1,title:m(p.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>u((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:m(p.recordAndReload),loadActionDelegate:async()=>new((await f()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]});const y=new CSSStyleSheet;y.replaceSync(".add-network-target-button{margin:10px 25px;align-self:center}.network-discovery-list{flex:none;max-width:600px;max-height:202px;margin:20px 0 5px}.network-discovery-list-empty{flex:auto;height:30px;display:flex;align-items:center;justify-content:center}.network-discovery-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.network-discovery-value{flex:3 1 0}.list-item .network-discovery-value{white-space:nowrap;text-overflow:ellipsis;user-select:none;color:var(--sys-color-on-surface);overflow:hidden}.network-discovery-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px;align-items:center}.network-discovery-edit-row input{width:100%;text-align:inherit}.network-discovery-footer{margin:0;overflow:hidden;max-width:500px;padding:3px}.network-discovery-footer > *{white-space:pre-wrap}.node-panel{align-items:center;justify-content:flex-start;overflow-y:auto}.network-discovery-view{min-width:400px;text-align:left}:host-context(.node-frontend) .network-discovery-list-empty{height:40px}:host-context(.node-frontend) .network-discovery-list-item{padding:3px 15px;height:40px}.node-panel-center{max-width:600px;padding-top:50px;text-align:center}.node-panel-logo{width:400px;margin-bottom:50px}:host-context(.node-frontend) .network-discovery-edit-row input{height:30px;padding-left:5px}:host-context(.node-frontend) .network-discovery-edit-row{margin:6px 9px}\n/*# sourceURL=nodeConnectionsPanel.css */\n");const C={nodejsDebuggingGuide:"Node.js debugging guide",specifyNetworkEndpointAnd:"Specify network endpoint and DevTools will connect to it automatically. Read {PH1} to learn more.",noConnectionsSpecified:"No connections specified",addConnection:"Add connection",networkAddressEgLocalhost:"Network address (e.g. localhost:9229)"},T=t.i18n.registerUIStrings("entrypoints/node_app/NodeConnectionsPanel.ts",C),k=t.i18n.getLocalizedString.bind(void 0,T);class x extends n.Panel.Panel{#e;#t;constructor(){super("node-connection"),this.contentElement.classList.add("node-panel");const e=this.contentElement.createChild("div","node-panel-center");e.createChild("img","node-panel-logo").src="https://nodejs.org/static/images/logos/nodejs-new-pantone-black.svg",s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#n,this),this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!0),this.#t=new I((e=>{this.#e.networkDiscoveryConfig=e,s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesDiscoveryConfig(this.#e)})),this.#t.show(e)}#n({data:e}){this.#e=e,this.#t.discoveryConfigChanged(this.#e.networkDiscoveryConfig)}wasShown(){super.wasShown(),this.registerCSSFiles([y])}}class I extends n.Widget.VBox{#o;#i;#s;#r;constructor(e){super(),this.#o=e,this.element.classList.add("network-discovery-view");const o=this.element.createChild("div","network-discovery-footer"),i=n.XLink.XLink.create("https://nodejs.org/en/docs/inspector/",k(C.nodejsDebuggingGuide),void 0,void 0,"node-js-debugging");o.appendChild(t.i18n.getFormatLocalizedString(T,C.specifyNetworkEndpointAnd,{PH1:i})),this.#i=new n.ListWidget.ListWidget(this),this.#i.element.classList.add("network-discovery-list");const s=document.createElement("div");s.classList.add("network-discovery-list-empty"),s.textContent=k(C.noConnectionsSpecified),this.#i.setEmptyPlaceholder(s),this.#i.show(this.element),this.#s=null;const r=n.UIUtils.createTextButton(k(C.addConnection),this.#a.bind(this),{className:"add-network-target-button",variant:"primary"});this.element.appendChild(r),this.#r=[],this.element.classList.add("node-frontend")}#c(){const e=this.#r.map((e=>e.address));this.#o.call(null,e)}#a(){this.#i.addNewItem(this.#r.length,{address:"",port:""})}discoveryConfigChanged(e){this.#r=[],this.#i.clear();for(const t of e){const e={address:t,port:""};this.#r.push(e),this.#i.appendItem(e,!0)}}renderItem(e,t){const n=document.createElement("div");return n.classList.add("network-discovery-list-item"),n.createChild("div","network-discovery-value network-discovery-address").textContent=e.address,n}removeItemRequested(e,t){this.#r.splice(t,1),this.#i.removeItem(t),this.#c()}commitEdit(e,t,n){e.address=t.control("address").value.trim(),n&&this.#r.push(e),this.#c()}beginEdit(e){const t=this.#d();return t.control("address").value=e.address,t}#d(){if(this.#s)return this.#s;const e=new n.ListWidget.Editor;this.#s=e;const t=e.contentElement().createChild("div","network-discovery-edit-row"),o=e.createInput("address","text",k(C.networkAddressEgLocalhost),(function(e,t,n){const o=n.value.trim().match(/^([a-zA-Z0-9\.\-_]+):(\d+)$/);if(!o)return{valid:!1,errorMessage:void 0};return{valid:parseInt(o[2],10)<=65535,errorMessage:void 0}}));return t.createChild("div","network-discovery-value network-discovery-address").appendChild(o),e}wasShown(){super.wasShown(),this.#i.registerCSSFiles([y])}}const D={main:"Main",nodejsS:"Node.js: {PH1}"},E=t.i18n.registerUIStrings("entrypoints/node_app/NodeMain.ts",D),A=t.i18n.getLocalizedString.bind(void 0,E);let S;class b{static instance(e={forceNew:null}){const{forceNew:t}=e;return S&&!t||(S=new b),S}async run(){s.userMetrics.actionTaken(s.UserMetrics.Action.ConnectToNodeJSFromFrontend),a.Connections.initMainConnection((async()=>{a.TargetManager.TargetManager.instance().createTarget("main",A(D.main),a.Target.Type.Browser,null).setInspectedURL("Node.js")}),r.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost)}}class M extends a.SDKModel.SDKModel{#l;#g;#h;#p;#w;constructor(e){super(e),this.#l=e.targetManager(),this.#g=e,this.#h=e.targetAgent(),this.#p=new Map,this.#w=new Map,e.registerTargetDispatcher(this),this.#h.invoke_setDiscoverTargets({discover:!0}),s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#n,this),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.setDevicesUpdatesEnabled(!0)}#n({data:e}){const t=[];for(const n of e.networkDiscoveryConfig){const e=n.split(":"),o=parseInt(e[1],10);e[0]&&o&&t.push({host:e[0],port:o})}this.#h.invoke_setRemoteLocations({locations:t})}dispose(){s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.DevicesDiscoveryConfigChanged,this.#n,this);for(const e of this.#p.keys())this.detachedFromTarget({sessionId:e})}targetCreated({targetInfo:e}){"node"!==e.type||e.attached||this.#h.invoke_attachToTarget({targetId:e.targetId,flatten:!1})}targetInfoChanged(e){}targetDestroyed(e){}attachedToTarget({sessionId:e,targetInfo:t}){const n=A(D.nodejsS,{PH1:t.url}),o=new F(this.#h,e);this.#w.set(e,o);const i=this.#l.createTarget(t.targetId,n,a.Target.Type.Node,this.#g,void 0,void 0,o);this.#p.set(e,i),i.runtimeAgent().invoke_runIfWaitingForDebugger()}detachedFromTarget({sessionId:e}){const t=this.#p.get(e);t&&t.dispose("target terminated"),this.#p.delete(e),this.#w.delete(e)}receivedMessageFromTarget({sessionId:e,message:t}){const n=this.#w.get(e),o=n?n.onMessage:null;o&&o.call(null,t)}targetCrashed(e){}}class F{#h;#m;onMessage;#v;constructor(e,t){this.#h=e,this.#m=t,this.onMessage=null,this.#v=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#v=e}sendRawMessage(e){this.#h.invoke_sendMessageToTarget({message:e,sessionId:this.#m})}async disconnect(){this.#v&&this.#v.call(null,"force disconnect"),this.#v=null,this.onMessage=null,await this.#h.invoke_detachFromTarget({sessionId:this.#m})}}a.SDKModel.SDKModel.register(M,{capabilities:32,autostart:!0});const N={connection:"Connection",node:"node",showConnection:"Show Connection",networkTitle:"Node",showNode:"Show Node"},P=t.i18n.registerUIStrings("entrypoints/node_app/node_app.ts",N),R=t.i18n.getLazilyComputedLocalizedString.bind(void 0,P);let L;n.ViewManager.registerViewExtension({location:"panel",id:"node-connection",title:R(N.connection),commandPrompt:R(N.showConnection),order:0,loadView:async()=>new x,tags:[R(N.node)]}),n.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:R(N.networkTitle),commandPrompt:R(N.showNode),order:2,persistence:"permanent",loadView:async()=>(await async function(){return L||(L=await import("../../panels/sources/sources.js")),L}()).SourcesNavigator.NetworkNavigatorView.instance()}),self.runtime=o.Runtime.Runtime.instance({forceNew:!0}),e.Runnable.registerEarlyInitializationRunnable(b.instance),new i.MainImpl.MainImpl;
