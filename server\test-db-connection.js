const { Pool } = require('pg');

console.log('🔄 Test de connexion à la base de données Facutration...');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

async function testConnection() {
  try {
    console.log('📡 Tentative de connexion...');
    
    const client = await pool.connect();
    console.log('✅ Connexion réussie à PostgreSQL');
    
    // Test de la table Client
    console.log('📊 Test de la table Client...');
    const result = await client.query('SELECT COUNT(*) FROM client');
    console.log(`✅ ${result.rows[0].count} clients trouvés dans la table Client`);
    
    // Récupérer quelques clients pour test
    const clientsResult = await client.query('SELECT idclient, nom, prenom, ville FROM client LIMIT 5');
    console.log('📋 Premiers clients:');
    clientsResult.rows.forEach((client, index) => {
      console.log(`   ${index + 1}. ID:${client.idclient} - ${client.nom} ${client.prenom} (${client.ville})`);
    });
    
    client.release();
    console.log('✅ Test de connexion terminé avec succès');
    
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    console.error('💡 Vérifiez que PostgreSQL est démarré et accessible');
    return false;
  }
}

// Exécuter le test
testConnection().then((success) => {
  if (success) {
    console.log('\n🎉 La base de données est prête pour le serveur !');
  } else {
    console.log('\n❌ Problème de connexion à résoudre');
  }
  process.exit(success ? 0 : 1);
});
